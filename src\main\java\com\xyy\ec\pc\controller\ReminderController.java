package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.exception.XyyEcOrderBizNoneCheckRTException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.api.NoticeDetailBuineseApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.api.pay.PingAnAccountApi;
import com.xyy.ec.order.business.api.OrderAdjustBusinessApi;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderExtendBusinessApi;
import com.xyy.ec.order.business.dto.*;
import com.xyy.ec.order.business.dto.shippingReminder.OrderShippingReminderHistoryVo;
import com.xyy.ec.order.business.enums.pay.PayChannelEnum;
import com.xyy.ec.order.core.enums.OrderAdjustTypeEnum;
import com.xyy.ec.order.dto.pay.TradeCertificateResultDto;
import com.xyy.ec.order.enums.TransactionTypeEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.OrderDetailEnum;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.rpc.ReminderServiceRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.DateUtil;
import com.xyy.ec.pc.util.DateUtils;
import com.xyy.ec.product.business.api.ecp.skucategory.EcpCategoryRelationBusinessApi;
import com.xyy.ec.product.business.dto.SkuCategoryRelationBusinessDTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


@Controller
@RequestMapping("/merchant/center/order/reminder")
public class ReminderController extends BaseController {


    private static final Logger logger = LoggerFactory.getLogger(ReminderController.class);

    @Autowired
    ReminderServiceRpc reminderServiceRpc;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;


    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private NoticeDetailBuineseApi noticeDetailBuineseApi;
    @Value("${reminder_days:商家将在2个工作日内发货，如超时未处理，将自动为您申请退款并赔付}")
    private String reminderDays;
    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;
    @Reference(version = "1.0.0")
    EcpCategoryRelationBusinessApi categoryRelationBusinessApi;
    @Autowired
    private AppProperties appProperties;
    @Reference(version = "1.0.0")
    private OrderExtendBusinessApi orderExtendBusinessApi;
    @Reference(version = "1.0.0")
    private OrderAdjustBusinessApi orderAdjustBusinessApi;
    @Reference(version = "1.0.0")
    private PingAnAccountApi pingAnAccountApi;

    @Value("${noneShowJdCreditCertificateTime:2025-06-19 00:00:00}")
    public String noneShowJdCreditCertificateTime;

    @RequestMapping("/submit")
    @ResponseBody
    public XyyJsonResult submit(String orderNo) throws Exception {
        try {
            MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            reminderServiceRpc.submit(orderNo, merchantBussinessDto.getRealName());
            return XyyJsonResult.createSuccess().msg(reminderDays);
        }catch (XyyEcOrderBizNoneCheckRTException xyyError){
            return XyyJsonResult.createFailure().msg(xyyError.getMessage());
        }catch (Exception e) {
            logger.error("发起催发货失败", e);
            return XyyJsonResult.createFailure().msg(e.getMessage());
        }
    }

    @RequestMapping("/history")
    @ResponseBody
    public XyyJsonResult history(String orderNo) throws Exception {
        try {
            OrderShippingReminderHistoryVo reminderHistoryVo = reminderServiceRpc.queryHistory(orderNo);
            return XyyJsonResult.createSuccess().addResult("list",reminderHistoryVo);
        } catch (Exception e) {
            logger.error("发起催发货失败", e);
            return XyyJsonResult.createFailure().msg(e.getMessage());
        }
    }

    @RequestMapping("/cancel")
    @ResponseBody
    public XyyJsonResult cancel(String orderNo,String reminderId) throws Exception {
        try {
            MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            reminderServiceRpc.cancel(orderNo,reminderId,merchantBussinessDto.getRealName());
            return XyyJsonResult.createSuccess().msg("撤销催发货成功！");
        }catch (XyyEcOrderBizNoneCheckRTException xyyError){
            return XyyJsonResult.createFailure().msg(xyyError.getMessage());
        }catch (Exception e) {
            logger.error("撤销催发货失败", e);
            return XyyJsonResult.createFailure().msg(e.getMessage());
        }
    }

    @RequestMapping("/detail/{id}.htm")
    public String detail(@PathVariable Long id, ModelMap modelMap) {
        MyOrderInfoBusinessDto order = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            modelMap.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            order = orderBusinessApi.selectOrderDetail(merchant.getId(), id, OrderDetailEnum.DELIVERING.getCode());
            calcOrderAdjust(order); //计算订单优惠金额
            String phone = null;
            //pop订单获取商家电话
            if(order.getIsThirdCompany() == Constants.IS1){
                OrgInfoDto orgInfo = orderBusinessApi.getOrgInfo(order.getOrgId());
                if(orgInfo != null){
                    phone  = orgInfo.getPhone();
                }
            }
            modelMap.put("merchant", merchant);
            modelMap.put("phone", phone);
            List<OrderDetailBusinessDto> orderDetailBusinessDtos = order.getDetailList();
            List<Long> skuIds = orderDetailBusinessDtos.stream().map(OrderDetailBusinessDto::getSkuId).collect(Collectors.toList());
            List<SkuCategoryRelationBusinessDTO> skuCategoryRelationBusinessDTOS = categoryRelationBusinessApi.findSkuCategoryRelationBySkuIdList(skuIds);
            if(CollectionUtils.isNotEmpty(skuCategoryRelationBusinessDTOS)){
                Map<Long, List<SkuCategoryRelationBusinessDTO>> skuCategoryRelationMap = skuCategoryRelationBusinessDTOS.stream().collect(Collectors.groupingBy(SkuCategoryRelationBusinessDTO::getSkuId));
                orderDetailBusinessDtos.forEach(orderDetailBusinessDto -> {
                    List<SkuCategoryRelationBusinessDTO> skuCategoryRelations = skuCategoryRelationMap.get(orderDetailBusinessDto.getSkuId());
                    if(CollectionUtils.isNotEmpty(skuCategoryRelations)){
                        // 一般一个商品对应一个分类关系，但线上有大量商品对应多条一样的分类关系，所以这里取最新一条关系作为商品的分类
                        SkuCategoryRelationBusinessDTO skuCategoryRelationBusinessDTO = skuCategoryRelationBusinessDTOS.get(skuCategoryRelationBusinessDTOS.size() - 1);
                        orderDetailBusinessDto.setCategoryFirstId(skuCategoryRelationBusinessDTO.getCategoryFirstId());
                        orderDetailBusinessDto.setCategorySecondId(skuCategoryRelationBusinessDTO.getCategorySecondId());
                        orderDetailBusinessDto.setCategoryThirdId(skuCategoryRelationBusinessDTO.getCategoryThirdId());
                    }
                });
            }
            final List<TradeCertificateResultDto> tradeCertificateResultDtos = queryTradeCertificate(order);
            if (CollectionUtils.isNotEmpty(tradeCertificateResultDtos)) {
                final List<TradeCertificateResultDto> list = tradeCertificateResultDtos.stream().filter(x -> TransactionTypeEnum.PAY.getCode().equals(x.getTradeType())).collect(Collectors.toList());
                modelMap.put("payCertificateResult", list);
                modelMap.put("refundCertificateResult", tradeCertificateResultDtos.stream().filter(x->TransactionTypeEnum.REFUND.getCode().equals(x.getTradeType())).collect(Collectors.toList()));

            }
            Set<String> specifyPopShopLicenseOrgIds = appProperties.getSpecifyPopShopLicenseOrgIds();
            boolean isSpecifyPopShop = CollectionUtils.isNotEmpty(specifyPopShopLicenseOrgIds) && specifyPopShopLicenseOrgIds.contains(order.getOrgId());
            if (isSpecifyPopShop) {
                order.setCompanyName(null);
            }
        } catch (Exception e) {
            logger.error("查看订单异常", e);
        }
        modelMap.put("order", order);
        modelMap.put("center_menu", "order");
        return "/order/expeditedShipments.ftl";
    }

    /**
     * 计算订单的优惠金额
     *
     * @param tempOrder void
     * @Title: calcOrderAdjust
     */
    private void calcOrderAdjust(MyOrderInfoBusinessDto tempOrder) {
        //满减优惠
        BigDecimal fullDivPrice = new BigDecimal(0);

        BigDecimal voucherDivPrice = new BigDecimal(0);
        //订单优惠金额
        BigDecimal rebate = tempOrder.getDiscount();
        if (rebate != null && rebate.doubleValue() != 0.00) {
            OrderAdjustBusinessDto paramOrderAdjust = new OrderAdjustBusinessDto();
            //设置订单id
            paramOrderAdjust.setOrderNo(tempOrder.getOrderNo());
            //查询订单优惠券列表
            List<OrderAdjustBusinessDto> orderAdjustList = orderAdjustBusinessApi.selectList(paramOrderAdjust);

            for (OrderAdjustBusinessDto orderAdjust : orderAdjustList) {
                //判断优惠券的类型
                if (orderAdjust.getAdjustType() == OrderAdjustTypeEnum.ADJUST_TYPE_BUY_REWARD.getId()) {
                    //满减优惠
                    fullDivPrice = fullDivPrice.add(orderAdjust.getAmount());
                }
                if (orderAdjust.getAdjustType() == OrderAdjustTypeEnum.ADJUST_TYPE_VOUCHER.getId()) {
                    //使用优惠券记录
                    voucherDivPrice = voucherDivPrice.add(orderAdjust.getAmount());
                }
                // 返券的不需记录到rebate中
                if (orderAdjust.getAdjustType() == OrderAdjustTypeEnum.ADJUST_TYPE_RETURN_VOUCHER.getId()) {
                    continue;
                }
                //订单优惠金额-优惠券的数量
                rebate = rebate.subtract(orderAdjust.getAmount());
            }
        }
        //返利减去余额金额(因为订单拓展表的余额会减少,所以直接取最原始余额)
        OrderExtendBusinessDto orderExtendBusinessDto = orderExtendBusinessApi.selectByOrderNo(tempOrder.getOrderNo());
        if(orderExtendBusinessDto!=null){
            Double balanceUse = orderExtendBusinessDto.getSourceBalanceUse().doubleValue();
            if (balanceUse != null) {
                tempOrder.setBalanceAmount(new BigDecimal(balanceUse).setScale(2, RoundingMode.HALF_UP));
                rebate = rebate.subtract(new BigDecimal(balanceUse).setScale(2, RoundingMode.HALF_UP));
            }
        }
        tempOrder.setFullDivPrice(fullDivPrice);
        tempOrder.setVoucherDivPrice(voucherDivPrice);
        tempOrder.setRebate(rebate);

        if(tempOrder.getIsThirdCompany() == 0){
            //扣除掉运费
            BigDecimal freightAmount = tempOrder.getFreightAmount();
            if (freightAmount == null){
                freightAmount = BigDecimal.ZERO;
            }
            tempOrder.setTotalAmount(tempOrder.getTotalAmount().subtract(freightAmount).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private Date getNoneShowJdCreditCertificateDate(String noneShowJdCreditCertificateTime) {
        try {
            return cn.hutool.core.date.DateUtil.parse(noneShowJdCreditCertificateTime, DateUtil.PATTERN_STANDARD);
        } catch (Exception e) {
            logger.error("getNoneShowJdCreditCertificateDate error, {}", noneShowJdCreditCertificateTime, e);
        }
        return null;
    }

    private List<TradeCertificateResultDto> queryTradeCertificate(MyOrderInfoBusinessDto order) {
        try {
            logger.info("queryTradeCertificate start {} PayTime:{}",order.getOrderNo(),order.getPayTime());

            if (Objects.equals(order.getPayChannel(), PayChannelEnum.JD_CREDIT.getValue())) {
                Date noneShowJdCreditCertificateDate = getNoneShowJdCreditCertificateDate(noneShowJdCreditCertificateTime);
                if (noneShowJdCreditCertificateDate != null && order.getPayTime() != null && order.getPayTime().before(noneShowJdCreditCertificateDate)) {
                    order.setShowCertificate(Boolean.FALSE);
                    return Lists.newArrayList();
                }
            }

            boolean orderPayChannelCondition = Objects.equals(order.getPayChannel(), PayChannelEnum.PINGAN_LOAD.getValue()) ||
                    Objects.equals(order.getPayChannel(), PayChannelEnum.XYD_LOAN.getValue()) ||
                    Objects.equals(order.getPayChannel(), PayChannelEnum.KING_DEE_LOAN.getValue()) ||
                    Objects.equals(order.getPayChannel(), PayChannelEnum.JD_CREDIT.getValue());
            if (order.getPayTime() != null && orderPayChannelCondition) {
                //当前时间>订单支付时间+2工作日
                Date beforeDay = DateUtils.getFrontDay(new Date(), 2);
                if (beforeDay.compareTo(order.getPayTime()) >0) {

                    final ApiRPCResult<List<TradeCertificateResultDto>> result = pingAnAccountApi.queryTradeCertificateV2(order.getOrderNo(), order.getPayChannel());
                    return result.getData();
                } else {
                    logger.warn("queryTradeCertificate 当前时间-支付时间<2day {}",order.getOrderNo());
                }
            }

        } catch (Exception e) {
            logger.error("queryTradeCertificate error {}",order.getOrderNo(),e);
        }
        return Lists.newArrayList();
    }

}
