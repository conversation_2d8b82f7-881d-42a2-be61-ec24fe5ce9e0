package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/3/19
 * @Description 首营资料药检报告枚举
 */
public enum ReportRiTypeEnum {
    /** 药检报告 */
    DRUG_INSPECTION_REPORT(1,"药检报告"),
    /** 首营资料 */
    FIRST_CAMP_INFOMATION(2,"首营资料");
    private int type;
    private String text;

    ReportRiTypeEnum(int type, String text) {
        this.type = type;
        this.text = text;
    }
    private static Map<Integer, ReportRiTypeEnum> eMaps = new HashMap<>();
    static {
        for(ReportRiTypeEnum riTypeEnum : ReportRiTypeEnum.values()) {
            eMaps.put(riTypeEnum.getType(), riTypeEnum);
        }
    }

    public static ReportRiTypeEnum getByType(int type){
        return eMaps.get(type);
    }

    public int getType() {
        return type;
    }

    public String getText() {
        return text;
    }
}
