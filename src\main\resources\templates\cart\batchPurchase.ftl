<html>
<head>
    <#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>
        <#if (title?? && title!='')>
            ${title}
        <#else>
            药帮忙
        </#if>
    </title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="/static/css/search.css?t=${t_v}" />
    <link rel="stylesheet" href="/static/css/lib.css" />
    <#if errorMsg>
        <script type="text/javascript">
            $(function() {
                $.alert({"title":"提示：","body":"${errorMsg}"});
            });
        </script>
    </#if>
</head>
<style type="text/css">
    body {margin-left: 0px;margin-top: 0px;margin-right: 0px;margin-bottom: 0px;}
    /*隐藏搜索框*/
    .searcherBox{display: none;}
    .newwarp{display: none;}
    #intelligent-frame{
        border: none;
    }

</style>
<body>
<div class="container">
    <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
    <input type="hidden" id="merchant" name="merchant" value="${merchant}"/>

    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
      <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->

    <!--主体部分开始-->
    <iframe id="intelligent-frame" src="/newstatic/#/intelligentProcurement/index" width="100%" height="100%"></iframe>
    <!--主体部分结束-->

    <!--底部导航区域开始-->
<#--    <div class="footer" id="footer">-->
<#--      <#include "/common/footer.ftl" />-->
<#--    </div>-->
    <!--底部导航区域结束-->
</div>
</body>
<script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>

<script type="text/javascript">
function setIframeHeight(iframe) {
    if (iframe) {
        // var iframeWin = iframe.contentWindow || iframe.contentDocument.parentWindow;
        // if (iframeWin.document.body) {
        //     iframe.height = iframeWin.document.documentElement.scrollHeight || iframeWin.document.body.scrollHeight;
        // }

        window.addEventListener('message', function(e){
            if (e.data.message === 'getCrossHeight') {
                var crossHeight = e.data.height;
                var ifr = document.querySelector('#intelligent-frame');
                ifr.style.height = crossHeight + 'px';
                console.log('iframe-height', crossHeight)
            }
        }, false);

    }
};

window.onload = function () {
    setIframeHeight(document.getElementById('intelligent-frame'));
};

</script>
</html>