package com.xyy.ec.pc.newfront.service.impl;

import com.xyy.ec.merchant.bussiness.dto.LoginAgreementDto;
import com.xyy.ec.merchant.bussiness.dto.LoginAgreementInfoDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.params.LoginAgreementLogParam;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.model.AddLoginAgreementLogDto;
import com.xyy.ec.pc.newfront.service.LoginAgreementService;
import com.xyy.ec.pc.remote.LoginAgreementBussinessRemoteService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.ipip.IPUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LoginAgreementServiceImpl implements LoginAgreementService {

    @Autowired
    private LoginAgreementBussinessRemoteService loginAgreementBussinessRemoteService;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Autowired
    private AppProperties appProperties;
    @Override
    public AjaxResult<Object> getLoginAgreement(String lang) {
        try{
            LoginAgreementInfoDto pc = loginAgreementBussinessRemoteService.getLoginAgreement("pc", lang);
            if (pc == null){
                return AjaxResult.errResult("获取登录协议失败");
            }else {
                LoginAgreementDto userServiceAgreement = pc.getUserServiceAgreement();
                LoginAgreementDto privacyPolicy = pc.getPrivacyPolicy();

                if (userServiceAgreement != null){
                    userServiceAgreement.setUrl(appProperties.getBasePathUrl() + userServiceAgreement.getUrl());
                }else{
                    userServiceAgreement = new LoginAgreementDto();
                    userServiceAgreement.setUrl(appProperties.getBasePathUrl() + "/login/agreement.htm");
                }
                if (privacyPolicy != null){
                    privacyPolicy.setUrl(appProperties.getBasePathUrl() + privacyPolicy.getUrl());
                }else {
                    privacyPolicy = new LoginAgreementDto();
                    privacyPolicy.setUrl(appProperties.getBasePathUrl() + "/helpCenter/privacy.htm");
                }

            }
            return AjaxResult.successResult(pc);
        }catch (Exception e){
            log.error("获取登录协议失败",e);
            return AjaxResult.errResult("获取登录协议失败");
        }
    }

    @Override
    public AjaxResult<Object> addLog(AddLoginAgreementLogDto param, HttpServletRequest request) {
        try {
            if (CollectionUtils.isEmpty(param.getAgreements()) || ObjectUtils.isEmpty(param.getCheckTime())){
                return AjaxResult.errResult("params is empty");
            }
            if (param.getAgreements().size() > 5){
                return AjaxResult.errResult("params size is too large");
            }
            Date date = new Date();
            MerchantBussinessDto merchant = (MerchantBussinessDto)xyyIndentityValidator.currentPrincipal();
            String ip = IPUtils.getClientIP(request);
            Date checkTime = param.getCheckTime();
            String mobile = param.getMobile();
            Integer operateType = param.getOperateType();
            List<LoginAgreementLogParam> agreementLogParamList = param.getAgreements().stream().map(item -> {
                item.setClientType("pc");
                item.setAccountId(merchant != null?merchant.getAccountId():0);
                item.setLoginTime(date);
                item.setIp(ip);
                item.setOperateType(operateType);
                item.setCheckTime(checkTime);
                item.setMobile(mobile);
                return item;
            }).collect(Collectors.toList());
            loginAgreementBussinessRemoteService.insertLoginAgreementLog(agreementLogParamList);
            return AjaxResult.successResult("保存勾选协议日志成功");
        }catch (Exception e){
            log.error("保存勾选协议日志失败",e);
            return AjaxResult.errResult("保存勾选协议日志失败");
        }
    }
}
