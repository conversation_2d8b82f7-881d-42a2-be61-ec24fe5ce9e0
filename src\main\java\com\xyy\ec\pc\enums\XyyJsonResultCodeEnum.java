package com.xyy.ec.pc.enums;

import lombok.Getter;

/**
 * 信息错误码枚举
 *
 * <AUTHOR>
 */
@Getter
public enum XyyJsonResultCodeEnum {
    SUCCESS(1000, "成功"),
    FAIL(true, 9999, "失败"),
    //201 ~300 区间通用异常
    NETWORK_ERROR(201, "网络异常请稍后再试"),
    PARAMETER_ERROR(202, "{0}入参错误"),
    AUTHORITY_ERROR(203, "鉴权异常"),

    //301~ 9998 业务异常
    GET_DEFAULT_VISIBLE_BY_SHOP_CODE_ERROR(true, 301, "获取店铺默认可见性异常"),
    CHECK_SHOP_BY_BUYER_CODE_ERROR(true, 302, "验证买家对店铺是否可见异常"),
    QUERY_SHOP_BY_SHOP_CODE_ERROR(true, 303, "根据店铺编码查询店铺基本信息异常"),
    CHECK_IS_NOT_FILTER_PRODUCT_ATTR_ERROR(true, 304, "校验是否过滤商品属性异常"),
    QUERY_SHOP_CATEGORY_BY_SHOP_CODE_ERROR(true, 305, "根据店铺编码查询店铺品类信息异常"),

    LIST_SHOP_IMAGES_ERROR(true, 313,"查询店铺图片列表失败"),
    LIST_SHOP_PRODUCTS_ERROR(true, 313,"查询店铺商品列表失败"),

    PAGING_USING_EXHIBITION_PRODUCTS_ERROR(true, 301, "分页查询启用中的展示商品列表异常"),
    USING_PRODUCTS_ERROR(true, 302, "查询商品列表异常"),
    /**
     * cms优惠券查询异常
     */
    CMS_COUPON_QUERY_ERROR(true, 303, "cms优惠券查询异常"),

    /**
     * 校验用户是否不能看见易碎品失败
     */
    CHECK_IS_NOT_WATCH_FRAGILE_GOODS_ERROR(true, 320, "校验用户是否不能看见易碎品失败"),

    /**
     * 根据会员ID获取会员信息失败
     */
    GET_MERCHANT_BY_ID_ERROR(true, 321, "根据会员ID获取会员信息失败"),

    /**
     * 分页获取为你推荐商品列表失败
     */
    LIST_RECOMMEND_TO_YOU_SKU_IDS_BY_SEARCH_ERROR(true, 330, "获取为你推荐商品ID列表失败"),

    /**
     * 获取在售商品信息（cms）失败
     */
    FIND_CSU_LIST_ONLY_SALE_FOR_CMS_ERROR(true, 331, "获取在售商品信息（cms）失败"),

    /**
     * 根据店铺编码批量查询店铺信息失败
     */
    QUERY_SHOP_INFOS_BY_SHOP_CODES_ERROR(true, 332, "根据店铺编码批量查询店铺信息失败"),

    
    MERCHANT_CURRENT_LIMIT_ERROR(true, 555, "系统访问人数过多，请稍后再试"),
    ;

    XyyJsonResultCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    XyyJsonResultCodeEnum(boolean warn, int code, String msg) {
        this.warn = warn;
        this.code = code;
        this.msg = msg;
    }

    /**
     * 是否告警
     */
    private boolean warn;
    /**
     * 应用名
     */
    private String appName = "YbmPc";
    /**
     * 业务错误码
     */
    private int code;
    /**
     * 描述
     */
    private String msg;
}
