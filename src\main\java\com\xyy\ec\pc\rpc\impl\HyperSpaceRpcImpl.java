package com.xyy.ec.pc.rpc.impl;

import com.alibaba.csp.sentinel.slots.block.SentinelRpcException;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.client.api.MarketingQueryApi;
import com.xyy.ec.marketing.client.api.SuiXinPinQueryApi;
import com.xyy.ec.marketing.client.dto.ActInfoDTO;
import com.xyy.ec.marketing.client.dto.SuiXinPinSkuDTO;
import com.xyy.ec.marketing.client.dto.param.ActInfoQueryDTO;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.CouponApi;
import com.xyy.ec.marketing.hyperspace.api.GroupBuyingBusinessApi;
import com.xyy.ec.marketing.hyperspace.api.MarketingRebateApi;
import com.xyy.ec.marketing.hyperspace.api.PromoSkuQueryService;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.ActCardInfoParamDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.OneClickRestockCouponDTO;
import com.xyy.ec.marketing.hyperspace.api.dto.OneClickRestockCouponTemplateDTO;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateConsumptionBaseResult;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateConsumptionReturnParam;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateConsumptionReturnResult;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateVoucherParam;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponCenterDto;
import com.xyy.ec.marketing.hyperspace.api.dto.fullGive.PromoSkuPoolDTO;
import com.xyy.ec.marketing.hyperspace.api.dto.fullGive.PromoWithMainProductDTO;
import com.xyy.ec.pc.rpc.HyperSpaceRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
public class HyperSpaceRpcImpl implements HyperSpaceRpc {


    @Reference(version = "1.0.0")
    private GroupBuyingBusinessApi groupBuyingBusinessApi;
    @Reference(version = "1.0.0")
    private MarketingQueryApi marketingQueryApi;

    @Reference(version = "1.0.0")
    private MarketingRebateApi marketingRebateApi;
    @Reference(version = "1.0.0")
    private PromoSkuQueryService promoSkuQueryService;

    @Reference(version = "1.0.0")
    private SuiXinPinQueryApi suiXinPinQueryApi;

    @Reference(version = "1.0.0")
    private CouponApi couponApi;
    private static final List<Integer> ORDER_STATUS_LIST = Lists.newArrayList(1, 2, 3, 7);

    @Override
    public List<MarketingEnum> getFloorList(Long merchantId, Integer bizType, String bizCode) {
        try {
            log.info("查询楼层信息开始, merchantId:{}, bizType:{}, bizCode:{}", merchantId, bizType, bizCode);
            ApiRPCResult<List<MarketingEnum>> result = groupBuyingBusinessApi.getFloorList(merchantId, bizType, bizCode);
            if (result.isFail()) {
                log.error("查询楼层信息 业务异常, merchantId:{}, bizType:{}, bizCode:{}, result:{}", merchantId, bizType, bizCode, JSONObject.toJSONString(result));
                throw new RuntimeException("查询楼层信息业务异常");
            }
            log.info("查询楼层信息成功, merchantId:{}, bizType:{}, bizCode:{}, result:{}", merchantId, bizType, bizCode, JSONObject.toJSONString(result));
            return result.getData();
        } catch (Exception e) {
            log.error("查询楼层信息异常, merchantId:{}, bizType:{}, bizCode:{}, msg:{}", merchantId, bizType,bizCode, e.getMessage());
            throw e;
        }
    }

    int pageSize = 200;
    @Override
    public List<GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdList(List<Long> skuIdList, Long merchantId, List<Integer> statusList) {
        if (CollectionUtils.isEmpty(skuIdList)) {
            return Lists.newArrayList();
        }
        if (skuIdList.size() > 1000) {
            log.error("HyperSpaceRpc.getGroupBuyingInfoBySkuIdList continue, skuIdList is too long, skuIdList:{}, merchantId:{}, statusList:{}", JSONObject.toJSONString(skuIdList), merchantId, JSONObject.toJSONString(statusList));
            return Lists.newArrayList();
        }
        List<GroupBuyingInfoDto> retList = Lists.newArrayList();
        List<List<Long>> list = Lists.partition(skuIdList, pageSize);
        for(List<Long> subList:list){
            //分页查询
            try {
                log.info("HyperSpaceRpc.getGroupBuyingInfoBySkuIdList start, skuIdList:{}, merchantId:{}, statusList:{}", JSONObject.toJSONString(subList), merchantId, JSONObject.toJSONString(statusList));
                ApiRPCResult<List<GroupBuyingInfoDto>> result = groupBuyingBusinessApi.getGroupBuyingInfoBySkuIdList(subList, merchantId, statusList);
                if (result.isFail()) {
                    log.error("HyperSpaceRpc.getGroupBuyingInfoBySkuIdList 业务异常, skuIdList:{}, merchantId:{},statusList{}, result:{}", JSONObject.toJSONString(subList), merchantId, JSONObject.toJSONString(statusList), JSONObject.toJSONString(result));
                    throw new RuntimeException("查询拼团信息业务异常");
                }
                log.info("HyperSpaceRpc.getGroupBuyingInfoBySkuIdList success, skuIdList:{}, merchantId:{}, statusList{}, result:{}", JSONObject.toJSONString(subList), merchantId, JSONObject.toJSONString(statusList), JSONObject.toJSONString(result));
                if(CollectionUtils.isNotEmpty(result.getData())){
                    retList.addAll(result.getData());
                }
            } catch (Exception e) {
                log.error("HyperSpaceRpc.getGroupBuyingInfoBySkuIdList error, skuIdList:{}, merchantId:{}, statusList{}, msg:{}", JSONObject.toJSONString(subList), merchantId, JSONObject.toJSONString(statusList), e.getMessage());
                throw e;
            }
        }
        return retList;
    }

    @Override
    public List<SuiXinPinSkuDTO> getSuiXinPinSkuDiscountBySkuIds(Long merchantId, List<Long> skuIdList) {
        try{
            ApiRPCResult<List<SuiXinPinSkuDTO>> apiRpcResult = suiXinPinQueryApi.getGroupBuyingInfoBySkuIdList(merchantId, skuIdList);
            if (null == apiRpcResult || !apiRpcResult.isSuccess() || null == apiRpcResult.getData()){
                log.error("getSuiXinPinSkuDiscountBySkuIds_查询随心拼价格信息接口返回数据有问题_merchantId={},skuIdList={},apiRPCResult={}", merchantId, JSON.toJSONString(skuIdList), JSON.toJSONString(apiRpcResult));
                return Lists.newArrayList();
            }
            return apiRpcResult.getData();
        }catch (Exception e){
            log.error("getSuiXinPinSkuDiscountBySkuIds_查询随心拼价格信息接口异常_merchantId={},skuIdList={}",merchantId, JSON.toJSONString(skuIdList), e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdListForCms(List<Long> skuIdList, List<Integer> statusList) {
        try {
            log.info("HyperSpaceRpc.getGroupBuyingInfoBySkuIdListForCms start, skuIdList:{}, statusList:{}", JSONObject.toJSONString(skuIdList), JSONObject.toJSONString(statusList));
            ApiRPCResult<List<GroupBuyingInfoDto>> result = groupBuyingBusinessApi.getGroupBuyingInfoBySkuIdListForCms(skuIdList, statusList);
            if (result.isFail()) {
                log.error("HyperSpaceRpc.getGroupBuyingInfoBySkuIdListForCms 业务异常, skuIdList:{}, statusList{}, result:{}", JSONObject.toJSONString(skuIdList), JSONObject.toJSONString(statusList), JSONObject.toJSONString(result));
                throw new RuntimeException("查询拼团信息业务异常");
            }
            log.info("HyperSpaceRpc.getGroupBuyingInfoBySkuIdListForCms success, skuIdList:{}, statusList{}, result:{}", JSONObject.toJSONString(skuIdList), JSONObject.toJSONString(statusList), JSONObject.toJSONString(result));
            return result.getData();
        } catch (Exception e) {
            log.error("HyperSpaceRpc.getGroupBuyingInfoBySkuIdListForCms error, skuIdList:{}, statusList{}, 异常信息:", JSONObject.toJSONString(skuIdList), JSONObject.toJSONString(statusList), e);
            throw e;
        }
    }

    @Override
    public List<GroupBuyingInfoDto> getActCardInfoBySkuIdList(ActCardInfoParamDto actCardInfoParamDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("HyperSpaceRpc.getActCardInfoBySkuIdList start，actCardInfoParamDto：{}", JSONObject.toJSONString(actCardInfoParamDto));
            }
            ApiRPCResult<List<GroupBuyingInfoDto>> result = groupBuyingBusinessApi.getActCardInfoBySkuIdList(actCardInfoParamDto);
            if (result.isFail()) {
                log.error("HyperSpaceRpc.getActCardInfoBySkuIdList 出现异常，actCardInfoParamDto：{}，result：{}", JSONObject.toJSONString(actCardInfoParamDto), JSONObject.toJSONString(result));
                throw new RuntimeException("查询活动卡片信息出现异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("HyperSpaceRpc.getActCardInfoBySkuIdList success，actCardInfoParamDto：{}，result：{}", JSONObject.toJSONString(actCardInfoParamDto), JSONObject.toJSONString(result));
            }
            return result.getData();
        } catch (Exception e) {
            log.error("HyperSpaceRpc.getActCardInfoBySkuIdList error，actCardInfoParamDto：{}", JSONObject.toJSONString(actCardInfoParamDto), e);
            throw e;
        }
    }

    @Override
    public List<GroupBuyingInfoDto> getActCardInfoBySkuIdListForCms(ActCardInfoParamDto actCardInfoParamDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("HyperSpaceRpc.getActCardInfoBySkuIdListForCms start，actCardInfoParamDto：{}", JSONObject.toJSONString(actCardInfoParamDto));
            }
            ApiRPCResult<List<GroupBuyingInfoDto>> result = groupBuyingBusinessApi.getActCardInfoBySkuIdListForCms(actCardInfoParamDto);
            if (result.isFail()) {
                log.error("HyperSpaceRpc.getActCardInfoBySkuIdListForCms 出现异常，actCardInfoParamDto：{}，result：{}", JSONObject.toJSONString(actCardInfoParamDto), JSONObject.toJSONString(result));
                throw new RuntimeException("查询活动卡片信息出现异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("HyperSpaceRpc.getActCardInfoBySkuIdListForCms success，actCardInfoParamDto：{}，result：{}", JSONObject.toJSONString(actCardInfoParamDto), JSONObject.toJSONString(result));
            }
            return result.getData();
        } catch (Exception e) {
            log.error("HyperSpaceRpc.getActCardInfoBySkuIdListForCms error，actCardInfoParamDto：{}", JSONObject.toJSONString(actCardInfoParamDto), e);
            throw e;
        }
    }

    @Override
    public Map<Long, List<ActInfoDTO>> getMarketingInfoBySkuIdList(List<Long> skuIdList, Long merchantId, MarketingEnum activityTypeEnum) {
        Map<Long, List<ActInfoDTO>> retMap = new HashMap<>();
        if(CollectionUtils.isEmpty(skuIdList)){
            return retMap;
        }
        List<List<Long>> partition = Lists.partition(skuIdList, 200);
        for(List<Long> subList : partition){
            ActInfoQueryDTO actInfoQueryDTO = new ActInfoQueryDTO();
            actInfoQueryDTO.setCsuIds(subList);
            actInfoQueryDTO.setCid(merchantId);
            Set<MarketingEnum> actSet = new HashSet<>();
            actSet.add(activityTypeEnum);
            actInfoQueryDTO.setActTypeSet(actSet);
            actInfoQueryDTO.setStatus(Arrays.asList(2));
            try {
                log.info("HyperSpaceRpc.getMarketingInfoBySkuIdList start, actInfoQueryDTO:{}", JSONObject.toJSONString(actInfoQueryDTO));
                ApiRPCResult<Map<Long, List<ActInfoDTO>>> result = marketingQueryApi.queryActInfo(actInfoQueryDTO);
                if (result.isFail()) {
                    log.error("HyperSpaceRpc.getMarketingInfoBySkuIdList 业务异常, actInfoQueryDTO:{}, result:{}", JSONObject.toJSONString(actInfoQueryDTO), JSONObject.toJSONString(result));
                    throw new RuntimeException("查询活动信息业务异常");
                }
                log.info("HyperSpaceRpc.getMarketingInfoBySkuIdList success, actInfoQueryDTO:{}, result:{}", JSONObject.toJSONString(actInfoQueryDTO), JSONObject.toJSONString(result));
                if(Objects.nonNull(result.getData())) {
                    retMap.putAll(result.getData());
                }
            } catch (Exception e) {
                log.error("HyperSpaceRpc.getMarketingInfoBySkuIdList error, actInfoQueryDTO:{}, msg:{}", JSONObject.toJSONString(actInfoQueryDTO), e.getMessage());
                throw e;
            }
        }
        return retMap;

    }

    @Override
    public Map<Long, ActInfoDTO> batchGetCsuJoiningActInfo(Long merchantId, MarketingEnum activityTypeEnum, List<Long> csuIds) {
        if (Objects.isNull(merchantId) || merchantId <= 0L || Objects.isNull(activityTypeEnum) || CollectionUtils.isEmpty(csuIds)) {
            return Maps.newHashMap();
        }
        // 无效入参过滤处理
        List<Long> legalCsuIds = csuIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(legalCsuIds)) {
            return Maps.newHashMap();
        }
        Map<Long, ActInfoDTO> csuIdToActInfoMap = Maps.newHashMapWithExpectedSize(legalCsuIds.size());
        List<List<Long>> legalCsuIdsLists = Lists.partition(legalCsuIds, 200);
        ActInfoQueryDTO actInfoQueryDTO = new ActInfoQueryDTO();
        actInfoQueryDTO.setCid(merchantId);
        actInfoQueryDTO.setActTypeSet(Sets.newHashSet(activityTypeEnum));
        actInfoQueryDTO.setStatus(Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()));
        actInfoQueryDTO.setCsuIds(Lists.newArrayList());
        actInfoQueryDTO.setIsNeedActSkuInfo(true);
        actInfoQueryDTO.setIsNeedActSkuLimit(true);
        Map<Long, List<ActInfoDTO>> tempCsuIdToActInfosMap;
        for (List<Long> legalCsuIdsList : legalCsuIdsLists) {
            try {
                actInfoQueryDTO.setCsuIds(legalCsuIdsList);
                ApiRPCResult<Map<Long, List<ActInfoDTO>>> result = marketingQueryApi.queryActInfo(actInfoQueryDTO);
                if (log.isDebugEnabled()) {
                    log.debug("查询商品正在参加的活动，merchantId：{}，activityTypeEnum：{}，legalCsuIdsList：{}，调用MarketingQueryApi.queryActInfo，actInfoQueryDTO：{}，result：{}",
                            merchantId, activityTypeEnum, JSONArray.toJSONString(legalCsuIdsList),
                            JSONObject.toJSONString(actInfoQueryDTO), JSONObject.toJSONString(result));
                }
                if (Objects.isNull(result) || BooleanUtils.isNotTrue(result.isSuccess())) {
                    log.error("查询商品正在参加的活动，调用MarketingQueryApi.queryActInfo失败，merchantId：{}，activityTypeEnum：{}，legalCsuIdsList：{}，actInfoQueryDTO：{}，result：{}",
                            merchantId, activityTypeEnum, JSONArray.toJSONString(legalCsuIdsList),
                            JSONObject.toJSONString(actInfoQueryDTO), JSONObject.toJSONString(result));
                    throw new RuntimeException("查询商品正在参加的活动失败");
                }
                tempCsuIdToActInfosMap = result.getData();
                if (MapUtils.isNotEmpty(tempCsuIdToActInfosMap)) {
                    for (Map.Entry<Long, List<ActInfoDTO>> entry : tempCsuIdToActInfosMap.entrySet()) {
                        Long tempCsuId = entry.getKey();
                        List<ActInfoDTO> tempActInfos = entry.getValue();
                        if (Objects.nonNull(tempCsuId) && CollectionUtils.isNotEmpty(tempActInfos)) {
                            // 排序：同状态中按活动开始时间升序、活动ID降序排序
                            tempActInfos = tempActInfos.stream().sorted(Comparator.comparing(ActInfoDTO::getStime)
                                    .thenComparing((a, b) -> b.getMarketingId().compareTo(a.getMarketingId())))
                                    .collect(Collectors.toList());
                            // 理论上同一个商品同一个时间只能参加一个进行中活动
                            csuIdToActInfoMap.putIfAbsent(tempCsuId, tempActInfos.get(0));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询商品正在参加的活动异常，merchantId：{}，activityTypeEnum：{}，csuIds：{}，异常信息：",
                        merchantId, activityTypeEnum, JSONArray.toJSONString(csuIds), e);
                throw new RuntimeException("查询商品正在参加的活动异常", e);
            }
        }
        return csuIdToActInfoMap;
    }

    @Override
    public Map<Long, List<ActInfoDTO>> batchGetCsuPreheatingActInfos(Long merchantId, MarketingEnum activityTypeEnum, List<Long> csuIds) {
        if (Objects.isNull(merchantId) || merchantId <= 0L || Objects.isNull(activityTypeEnum) || CollectionUtils.isEmpty(csuIds)) {
            return Maps.newHashMap();
        }
        // 无效入参过滤处理
        List<Long> legalCsuIds = csuIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(legalCsuIds)) {
            return Maps.newHashMap();
        }
        Map<Long, List<ActInfoDTO>> csuIdToActInfoMap = Maps.newHashMapWithExpectedSize(legalCsuIds.size());
        List<List<Long>> legalCsuIdsLists = Lists.partition(legalCsuIds, 200);
        ActInfoQueryDTO actInfoQueryDTO = new ActInfoQueryDTO();
        actInfoQueryDTO.setCid(merchantId);
        actInfoQueryDTO.setActTypeSet(Sets.newHashSet(activityTypeEnum));
        actInfoQueryDTO.setStatus(Lists.newArrayList(MarketingQueryStatusEnum.UN_START.getType()));
        actInfoQueryDTO.setCsuIds(Lists.newArrayList());
        actInfoQueryDTO.setIsNeedActSkuInfo(true);
        actInfoQueryDTO.setIsNeedActSkuLimit(true);
        Map<Long, List<ActInfoDTO>> tempCsuIdToActInfosMap;
        for (List<Long> legalCsuIdsList : legalCsuIdsLists) {
            try {
                actInfoQueryDTO.setCsuIds(legalCsuIdsList);
                ApiRPCResult<Map<Long, List<ActInfoDTO>>> result = marketingQueryApi.queryActInfo(actInfoQueryDTO);
                if (log.isDebugEnabled()) {
                    log.debug("查询商品预热中的活动，merchantId：{}，activityTypeEnum：{}，legalCsuIdsList：{}，调用MarketingQueryApi.queryActInfo，actInfoQueryDTO：{}，result：{}",
                            merchantId, activityTypeEnum, JSONArray.toJSONString(legalCsuIdsList),
                            JSONObject.toJSONString(actInfoQueryDTO), JSONObject.toJSONString(result));
                }
                if (Objects.isNull(result) || BooleanUtils.isNotTrue(result.isSuccess())) {
                    log.error("查询商品预热中的活动，调用MarketingQueryApi.queryActInfo失败，merchantId：{}，activityTypeEnum：{}，legalCsuIdsList：{}，actInfoQueryDTO：{}，result：{}",
                            merchantId, activityTypeEnum, JSONArray.toJSONString(legalCsuIdsList),
                            JSONObject.toJSONString(actInfoQueryDTO), JSONObject.toJSONString(result));
                    throw new RuntimeException("查询商品预热中的活动失败");
                }
                tempCsuIdToActInfosMap = result.getData();
                if (MapUtils.isNotEmpty(tempCsuIdToActInfosMap)) {
                    for (Map.Entry<Long, List<ActInfoDTO>> entry : tempCsuIdToActInfosMap.entrySet()) {
                        Long tempCsuId = entry.getKey();
                        List<ActInfoDTO> tempActInfos = entry.getValue();
                        if (Objects.nonNull(tempCsuId) && CollectionUtils.isNotEmpty(tempActInfos)) {
                            // 排序：同状态中按活动开始时间升序、活动ID降序排序
                            tempActInfos = tempActInfos.stream().sorted(Comparator.comparing(ActInfoDTO::getStime)
                                    .thenComparing((a, b) -> b.getMarketingId().compareTo(a.getMarketingId())))
                                    .collect(Collectors.toList());
                            csuIdToActInfoMap.putIfAbsent(tempCsuId, tempActInfos);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询商品预热中的活动异常，merchantId：{}，activityTypeEnum：{}，csuIds：{}，异常信息：",
                        merchantId, activityTypeEnum, JSONArray.toJSONString(csuIds), e);
                throw new RuntimeException("查询商品预热中的活动异常", e);
            }
        }
        return csuIdToActInfoMap;
    }

    @Override
    public Map<Long, List<ActInfoDTO>> batchGetCsuSomeStatusActInfos(Long merchantId, MarketingEnum activityTypeEnum,
                                                                     Integer status, List<Long> csuIds) {
        if (Objects.isNull(merchantId) || merchantId <= 0L || Objects.isNull(activityTypeEnum) || Objects.isNull(status)
                || CollectionUtils.isEmpty(csuIds)) {
            return Maps.newHashMap();
        }
        // 状态是否合法
        boolean statusIsLegal = false;
        for (MarketingQueryStatusEnum marketingQueryStatusEnum : MarketingQueryStatusEnum.values()) {
            if (Objects.equals(marketingQueryStatusEnum.getType(), status)) {
                statusIsLegal = true;
                break;
            }
        }
        if (BooleanUtils.isNotTrue(statusIsLegal)) {
            return Maps.newHashMap();
        }
        // 无效入参过滤处理
        List<Long> legalCsuIds = csuIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(legalCsuIds)) {
            return Maps.newHashMap();
        }
        Map<Long, List<ActInfoDTO>> csuIdToActInfoMap = Maps.newHashMapWithExpectedSize(legalCsuIds.size());
        List<List<Long>> legalCsuIdsLists = Lists.partition(legalCsuIds, 200);
        ActInfoQueryDTO actInfoQueryDTO = new ActInfoQueryDTO();
        actInfoQueryDTO.setCid(merchantId);
        actInfoQueryDTO.setActTypeSet(Sets.newHashSet(activityTypeEnum));
        actInfoQueryDTO.setStatus(Lists.newArrayList(status));
        actInfoQueryDTO.setCsuIds(Lists.newArrayList());
        actInfoQueryDTO.setIsNeedActSkuInfo(true);
        actInfoQueryDTO.setIsNeedActSkuLimit(true);
        Map<Long, List<ActInfoDTO>> tempCsuIdToActInfosMap;
        for (List<Long> legalCsuIdsList : legalCsuIdsLists) {
            try {
                actInfoQueryDTO.setCsuIds(legalCsuIdsList);
                ApiRPCResult<Map<Long, List<ActInfoDTO>>> result = marketingQueryApi.queryActInfo(actInfoQueryDTO);
                if (log.isDebugEnabled()) {
                    log.debug("查询商品某个状态的活动，merchantId：{}，activityTypeEnum：{}，status：{}，legalCsuIdsList：{}，" +
                                    "调用MarketingQueryApi.queryActInfo，actInfoQueryDTO：{}，result：{}",
                            merchantId, activityTypeEnum, status, JSONArray.toJSONString(legalCsuIdsList),
                            JSONObject.toJSONString(actInfoQueryDTO), JSONObject.toJSONString(result));
                }
                if (Objects.isNull(result) || BooleanUtils.isNotTrue(result.isSuccess())) {
                    log.error("查询商品某个状态的活动，调用MarketingQueryApi.queryActInfo失败，merchantId：{}，activityTypeEnum：{}，status：{}，" +
                                    "legalCsuIdsList：{}，actInfoQueryDTO：{}，result：{}",
                            merchantId, activityTypeEnum, status, JSONArray.toJSONString(legalCsuIdsList),
                            JSONObject.toJSONString(actInfoQueryDTO), JSONObject.toJSONString(result));
                    throw new RuntimeException("查询商品某个状态的活动失败");
                }
                tempCsuIdToActInfosMap = result.getData();
                if (MapUtils.isNotEmpty(tempCsuIdToActInfosMap)) {
                    for (Map.Entry<Long, List<ActInfoDTO>> entry : tempCsuIdToActInfosMap.entrySet()) {
                        Long tempCsuId = entry.getKey();
                        List<ActInfoDTO> tempActInfos = entry.getValue();
                        if (Objects.nonNull(tempCsuId) && CollectionUtils.isNotEmpty(tempActInfos)) {
                            // 排序：同状态中按活动开始时间升序、活动ID降序排序
                            tempActInfos = tempActInfos.stream().sorted(Comparator.comparing(ActInfoDTO::getStime)
                                    .thenComparing((a, b) -> b.getMarketingId().compareTo(a.getMarketingId())))
                                    .collect(Collectors.toList());
                            csuIdToActInfoMap.putIfAbsent(tempCsuId, tempActInfos);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询商品某个状态的活动异常，merchantId：{}，activityTypeEnum：{}，status：{}，csuIds：{}，异常信息：",
                        merchantId, activityTypeEnum, status, JSONArray.toJSONString(csuIds), e);
                throw new RuntimeException("查询商品某个状态的活动异常", e);
            }
        }
        return csuIdToActInfoMap;
    }


    @Override
    public GroupBuyingInfoDto getGroupBuyingInfoById(Long marketingId) {
        try {
            log.info("HyperSpaceRpc.getGroupBuyingInfoById start, marketingId:{}", marketingId);
            ApiRPCResult<GroupBuyingInfoDto> result = groupBuyingBusinessApi.getGroupBuyingInfoById(marketingId);
            if (result.isFail()) {
                log.error("HyperSpaceRpc.getGroupBuyingInfoById 业务异常, marketingId:{}, result:{}", marketingId, JSONObject.toJSONString(result));
                throw new RuntimeException("查询拼团信息业务异常");
            }
            log.info("HyperSpaceRpc.getGroupBuyingInfoById success, marketingId:{}, result:{}", marketingId, JSONObject.toJSONString(result));
            return result.getData();
        } catch (Exception e) {
            log.error("HyperSpaceRpc.getGroupBuyingInfoById error, marketingId:{}, msg:{}", marketingId, e.getMessage());
            throw e;
        }
    }

    @Override
    public List<CouponCenterDto> getMarketingRebateVoucherTemplateList(String orderNo, Long cid, Integer scene) {
        try {
            log.info("HyperSpaceRpc.getMarketingRebateVoucherTemplateList start, orderNo:{}， cid:{}", orderNo, cid);
            MarketingRebateVoucherParam paramDto = new MarketingRebateVoucherParam();
            paramDto.setCId(cid);
            paramDto.setOrderNo(orderNo);
            paramDto.setScene(scene);
            ApiRPCResult<List<CouponCenterDto>> result = marketingRebateApi.getMarketingRebateVoucherList(paramDto);
            if (result.isFail()) {
                log.error("HyperSpaceRpc.getMarketingRebateVoucherTemplateList 业务异常,  orderNo:{}， cid:{}, result:{}", orderNo, cid, JSONObject.toJSONString(result));
                throw new RuntimeException(result.getMsg());
            }
            log.info("HyperSpaceRpc.getMarketingRebateVoucherTemplateList success,  orderNo:{}， cid:{}, result:{}", orderNo, cid, JSONObject.toJSONString(result));
            return result.getData();
        } catch (Exception e) {
            log.error("HyperSpaceRpc.getMarketingRebateVoucherTemplateList error,  orderNo:{}， cid:{}, msg:{}", orderNo, cid, e.getMessage());
            throw e;
        }
    }
    @Override
    public List<PromoSkuPoolDTO> queryPromoSkuPool(Long promoId) {
        try {
            ApiRPCResult<List<PromoSkuPoolDTO>> querySkuPoolResult = promoSkuQueryService.getPromoSkuPool(promoId);
            if (null == querySkuPoolResult || !querySkuPoolResult.isSuccess()) {
                log.info("queryPromoSkuPool_接口异常_promoId={},querySkuPoolResult={}", promoId, JSON.toJSONString(querySkuPoolResult));
                return Lists.newArrayList();
            }
            return querySkuPoolResult.getData();
        } catch (Exception e) {
            log.error("查询赠品池异常", e);
            return Lists.newArrayList();
        }
    }
    @Override
    public PromoWithMainProductDTO queryPromoMainProductList(Long promoId) {
        PromoWithMainProductDTO promoWithMainProductDTO = null;
        try {
            ApiRPCResult<PromoWithMainProductDTO> result = promoSkuQueryService.getPromoMainProductList(promoId);
            if (null == result || !result.isSuccess()) {
                log.info("queryPromoMainProductList接口异常_promoId={},querySkuPoolResult={}", promoId, JSON.toJSONString(result));
                return promoWithMainProductDTO;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("查询赠品池异常", e);
            return promoWithMainProductDTO;
        }
    }

    @Override
    public List<Long> querySuiXinPinTopSku(Long merchantId, String shopCode) {
        ApiRPCResult<List<Long>> listApiRpcResult = marketingQueryApi.querySuiXinPinTopSku(merchantId, shopCode);
        if (null == listApiRpcResult || !listApiRpcResult.isSuccess()){
            log.info("querySuiXinPinTopSku_接口异常_merchantId={},shopCode={},listApiRpcResult={}", merchantId, shopCode, JSON.toJSONString(listApiRpcResult));
            return Lists.newArrayList();
        }
        if (null == listApiRpcResult.getData()){
            return Lists.newArrayList();
        }
        return listApiRpcResult.getData();
    }

    @Override
    public MarketingRebateConsumptionBaseResult getMarketingRebateConsumptionBaseInfo(Long cid) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("HyperSpaceRpc.getMarketingRebateConsumptionBaseInfo start, cid:{}", cid);
            }
            ApiRPCResult<MarketingRebateConsumptionBaseResult> result = marketingRebateApi.getMarketingRebateConsumptionBaseInfo(cid);
            if (result.isFail()) {
                log.error("HyperSpaceRpc.getMarketingRebateConsumptionBaseInfo 业务异常,  cid:{}， result:{}", cid, JSONObject.toJSONString(result));
                throw new RuntimeException(result.getMsg());
            }
            if (log.isDebugEnabled()) {
                log.debug("HyperSpaceRpc.getMarketingRebateConsumptionBaseInfo success,  cid:{}， result:{}", cid, JSONObject.toJSONString(result));
            }
            return result.getData();
        }catch (SentinelRpcException e){
            log.error("HyperSpaceRpc.getMarketingRebateConsumptionBaseInfo error(降级),  cid:{}， msg:{}", cid, e.getMessage());
            return null;
        }catch (Exception e) {
            log.error("HyperSpaceRpc.getMarketingRebateConsumptionBaseInfo error,  cid:{}， msg:{}", cid, e.getMessage());
            throw e;
        }
    }

    @Override
    public MarketingRebateConsumptionReturnResult getMarketingRebateConsumptionReturnInfo(Long cid, BigDecimal tempAmount) {
        MarketingRebateConsumptionReturnParam paramDto = new MarketingRebateConsumptionReturnParam();
        paramDto.setCid(cid);
        paramDto.setOrderStatusList(ORDER_STATUS_LIST);
        paramDto.setIsNeedLastMonthAmount(false);
        paramDto.setTempAmount(tempAmount);
        return getMarketingRebateConsumptionReturnInfo(paramDto);
    }

    @Override
    public MarketingRebateConsumptionReturnResult getMarketingRebateConsumptionReturnInfo(Long cid) {
        MarketingRebateConsumptionReturnParam paramDto = new MarketingRebateConsumptionReturnParam();
        paramDto.setCid(cid);
        paramDto.setOrderStatusList(ORDER_STATUS_LIST);
        paramDto.setIsNeedLastMonthAmount( true);
        return getMarketingRebateConsumptionReturnInfo(paramDto);
    }

    public MarketingRebateConsumptionReturnResult getMarketingRebateConsumptionReturnInfo(MarketingRebateConsumptionReturnParam paramDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("HyperSpaceRpc.getMarketingRebateConsumptionReturnInfo start, paramDto:{}", JSONObject.toJSONString(paramDto));
            }
            ApiRPCResult<MarketingRebateConsumptionReturnResult> result = marketingRebateApi.getMarketingRebateConsumptionReturnInfo(paramDto);
            if (result.isFail()) {
                log.error("HyperSpaceRpc.getMarketingRebateConsumptionReturnInfo 业务异常,  paramDto:{}， result:{}", JSONObject.toJSONString(paramDto), JSONObject.toJSONString(result));
                throw new RuntimeException(result.getMsg());
            }
            if (log.isDebugEnabled()) {
                log.debug("HyperSpaceRpc.getMarketingRebateConsumptionReturnInfo success,  paramDto:{}， result:{}", JSONObject.toJSONString(paramDto), JSONObject.toJSONString(result));
            }
            return result.getData();
        }catch (SentinelRpcException e){
            log.error("HyperSpaceRpc.getMarketingRebateConsumptionReturnInfo error(降级),  cid:{}， msg:{}", paramDto.getCid(), e.getMessage());
            return null;
        }catch (Exception e) {
            log.error("HyperSpaceRpc.getMarketingRebateConsumptionReturnInfo error,  cid:{}， msg:{}", paramDto.getCid(), e.getMessage());
            throw e;
        }
    }

    @Override
    public OneClickRestockCouponDTO getOneClickRestockCoupon(Long merchantId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("HyperSpaceRpc.getMarketingRebateConsumptionReturnInfo start, merchantId:{}", merchantId);
            }
            ApiRPCResult<OneClickRestockCouponDTO> rpcResult = couponApi.getOneClickRestockCoupon(merchantId);
            if (rpcResult.isFail()) {
                log.error("HyperSpaceRpcImpl#getOneClickRestockCoupon 业务异常,  merchantId:{}， result:{}", merchantId, JSONObject.toJSONString(rpcResult));
                throw new RuntimeException(rpcResult.getMsg());
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("HyperSpaceRpcImpl#getOneClickRestockCoupon error, cid:{}， msg:{}", merchantId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<OneClickRestockCouponTemplateDTO> getOrderReturnCoupon(Long merchantId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("HyperSpaceRpc.getOrderReturnCoupon start, merchantId:{}", merchantId);
            }
            ApiRPCResult<List<OneClickRestockCouponTemplateDTO>> rpcResult = couponApi.getOrderReturnCoupon(merchantId);
            if (rpcResult.isFail()) {
                log.error("HyperSpaceRpcImpl#getOrderReturnCoupon 业务异常,  merchantId:{}， result:{}", merchantId, JSONObject.toJSONString(rpcResult));
                throw new RuntimeException(rpcResult.getMsg());
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("HyperSpaceRpcImpl#getOrderReturnCoupon error, cid:{}， msg:{}", merchantId, e.getMessage(), e);
            return null;
        }
    }

}
