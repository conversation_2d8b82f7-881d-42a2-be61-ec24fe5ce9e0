package com.xyy.ec.pc.cms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsGroupBuyingSubjectHeadImageDailyPeriodDTO implements Serializable {

    /**
     * 每天的开始时间，小时
     */
    private Integer startTimeDailyHours;

    /**
     * 每天的开始时间，分钟
     */
    private Integer startTimeDailyMinutes;

    /**
     * 每天的开始时间，秒
     */
    private Integer startTimeDailySeconds;

    /**
     * 每天的开始时间，小时
     */
    private Integer endTimeDailyHours;

    /**
     * 每天的开始时间，分钟
     */
    private Integer endTimeDailyMinutes;

    /**
     * 每天的开始时间，秒
     */
    private Integer endTimeDailySeconds;

    /**
     * 图片地址
     */
    private String imageUrl;

    public Boolean isInThisDailyPeriod(Integer hours, Integer minutes, Integer seconds) {
        if (Objects.isNull(hours) || Objects.isNull(minutes) || Objects.isNull(seconds)) {
            return false;
        }
        if (Objects.isNull(startTimeDailyHours) || Objects.isNull(startTimeDailyMinutes) || Objects.isNull(startTimeDailySeconds)
                || Objects.isNull(endTimeDailyHours) || Objects.isNull(endTimeDailyHours) || Objects.isNull(endTimeDailyHours)) {
            return false;
        }
        // 换算成秒比对
        int currentSeconds = hours * 3600 + minutes * 60 + seconds;
        int startTimeSeconds = startTimeDailyHours * 3600 + startTimeDailyMinutes * 60 + startTimeDailySeconds;
        int endTimeSeconds = endTimeDailyHours * 3600 + endTimeDailyMinutes * 60 + endTimeDailySeconds;
        return startTimeSeconds <= currentSeconds && currentSeconds <= endTimeSeconds;
    }

}
