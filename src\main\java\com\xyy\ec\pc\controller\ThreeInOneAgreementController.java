package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.bussiness.api.AgreementInfBussinessApi;
import com.xyy.ec.merchant.bussiness.api.AgreementRuleInfBussinessApi;
import com.xyy.ec.merchant.bussiness.api.AgreementSkuInfBussinessApi;
import com.xyy.ec.merchant.bussiness.api.ecp.ecptrinityagreement.EcpTrinityAgreementBusinessApi;
import com.xyy.ec.merchant.bussiness.base.ResultMessage;
import com.xyy.ec.merchant.bussiness.dto.AgreementForPcAppBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.AgreementForPcAppStatisticsDto;
import com.xyy.ec.merchant.bussiness.dto.AgreementMerchantDetailBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.ListProductBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.model.ThreeInOneAgreementDto;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.PcVersionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Objects;

/**
 * @program: xyy-ec-pc
 * @description: ${description}
 * @author: Mr.MI
 * @create: 2019-04-30 19:36
 **/
@RequestMapping("/merchant/center/threeInOneAgreement")
@Controller
public class ThreeInOneAgreementController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ThreeInOneAgreementController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private AgreementInfBussinessApi agreementInfBussinessApi;

    @Reference(version = "1.0.0")
    private AgreementRuleInfBussinessApi agreementRuleInfBussinessApi;

    @Reference(version = "1.0.0")
    private AgreementSkuInfBussinessApi agreementSkuInfBussinessApi;

    @Reference(version = "1.0.0")
    private EcpTrinityAgreementBusinessApi ecpTrinityAgreementBusinessApi;

    @Autowired
    private PcVersionUtils pcVersionUtils;

    /**
     *三合一协议页面初始化默认要查出协议统计内容和用户已签署协议列表
     * @param threeInOneAgreementDto
     * @param page
     * @param request
     * @return
     */
    @RequestMapping("/index.htm")
    public ModelAndView findAllThreeInOneAgreementInfo(ThreeInOneAgreementDto threeInOneAgreementDto, Page page, HttpServletRequest request, Model model) {
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (page.getOffset()<=0){
                page.setOffset(1);
            }
            page.setLimit(10);
            Long merchantId = merchant.getId();
            if (threeInOneAgreementDto.getSignStatus() == null){
                //默认查询已签署协议列表
                threeInOneAgreementDto.setSignStatus(1);
            }
            //用户协议简单统计
            AgreementForPcAppStatisticsDto agreementForPcAppStatisticsDto =  agreementInfBussinessApi.statisticsMethod(merchantId,merchant.getRegisterCode());
            //查询三合一协议已签协议列表
            Page<AgreementForPcAppBussinessDto> agreementMerchantListByPage = new Page<>();
            PageInfo<AgreementForPcAppBussinessDto> pcAppBussinessDtoPageInfo = new PageInfo<>();
            //查询已签协议列表
            if (threeInOneAgreementDto.getSignStatus() == 1){
                pcAppBussinessDtoPageInfo = agreementInfBussinessApi.selectPageListSigned(threeInOneAgreementDto.getAgreementName(),threeInOneAgreementDto.getStartTime()
                        ,threeInOneAgreementDto.getEndTime(),threeInOneAgreementDto.getStates(),threeInOneAgreementDto.getAgreementType(),merchantId,page.getOffset(),page.getLimit());
                //查询未签协议列表
            }else if (threeInOneAgreementDto.getSignStatus() == 0){
                pcAppBussinessDtoPageInfo = agreementInfBussinessApi.selectPageListUnSigned(threeInOneAgreementDto.getAgreementName(),threeInOneAgreementDto.getStartTime(),
                        threeInOneAgreementDto.getEndTime(),threeInOneAgreementDto.getStates(),threeInOneAgreementDto.getAgreementType(),merchant.getRegisterCode(),merchantId,page.getOffset(),page.getLimit());
            }
            if (pcAppBussinessDtoPageInfo == null){
                pcAppBussinessDtoPageInfo = new PageInfo<>();
                pcAppBussinessDtoPageInfo.setPageSize(10);
                pcAppBussinessDtoPageInfo.setPageNum(1);
                pcAppBussinessDtoPageInfo.setTotal(0);
            }
            setResultPage(agreementMerchantListByPage,pcAppBussinessDtoPageInfo);
            agreementMerchantListByPage.setRequestUrl(this.getRequestUrl(request));
            model.addAttribute("statisticDataResultMap", agreementForPcAppStatisticsDto);
            model.addAttribute("agreementMerchantListByPage", agreementMerchantListByPage);
            model.addAttribute("tab", threeInOneAgreementDto.getSignStatus());
            model.addAttribute("merchant", merchant);
            model.addAttribute("requestDTO", threeInOneAgreementDto);
            model.addAttribute("center_menu", "threeInOneAgreement");
            model.addAttribute("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            return new ModelAndView("/threeInOneAgreement/threeInOneAgreement.ftl");
        } catch (Exception e) {
            LOGGER.error("查询三合一协议信息失败", e);
            return new ModelAndView("/threeInOneAgreement/threeInOneAgreement.ftl");
        }
    }

    /**
     * 协议详情页面
     * @param page
     * @param agreementId
     * @param signStatus
     * @param request
     * @param model
     * @return
     */
    @RequestMapping("/detail.htm")
    public ModelAndView toAgreementMerchantDetailPage(Page page, Long agreementId,Integer signStatus,HttpServletRequest request, Model model){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            if (page.getOffset()<=0){
                page.setOffset(1);
            }
            page.setLimit(10);
            Long merchantId = merchant.getId();
            String branchCode = merchant.getRegisterCode();
            if(null == agreementId || StringUtils.isBlank(branchCode)){
                return new ModelAndView(new RedirectView("/threeInOneAgreement/threeInOneAgreementDetail.ftl",true,false));
            }
            AgreementMerchantDetailBussinessDto agreementMerchantDetailBussinessDto = agreementRuleInfBussinessApi.getAgreementMerchantDetail(agreementId,merchantId,branchCode);
            if (agreementMerchantDetailBussinessDto == null){
                return new ModelAndView(new RedirectView("/threeInOneAgreement/threeInOneAgreementDetail.ftl",true,false));
            }
            //获取协议专区用户协议产品分页信息
            Page<ListProductBussinessDto> agreementMerchantSkuPage = new Page<>();
            /**
             * TODO 改调用新的API
             */
//            PageInfo<ListProductBussinessDto> pageForDetail = agreementSkuInfBussinessApi.selectSkuPageList(page.getOffset(),page.getLimit(),merchant.getRegisterCode(),agreementId);
            PageInfo<ListProductBussinessDto> pageForDetail = ecpTrinityAgreementBusinessApi.selectSkuPageListEcp(page.getOffset(),page.getLimit(),merchant.getRegisterCode(),agreementId,merchantId);
            if(pageForDetail == null && pageForDetail.getList() != null){
                for(ListProductBussinessDto product : pageForDetail.getList()){
                    product.setFob(merchant.getFob() == null ? product.getFob() : merchant.getFob());
                }
            }
            setResultPage(agreementMerchantSkuPage,pageForDetail);
            agreementMerchantSkuPage.setPageCount(pageForDetail.getPages());
            agreementMerchantSkuPage.setRequestUrl(getRequestUrl(request));
            Date datetime= new Date();
            if (datetime.compareTo(agreementMerchantDetailBussinessDto.getEndTime()) > 0){
                model.addAttribute("expireStatus",1);
            }
            //判断一下是生效
            if (agreementMerchantDetailBussinessDto.getFreezeStates() != null && agreementMerchantDetailBussinessDto.getFreezeStates().byteValue() != 1 && datetime.getTime()>agreementMerchantDetailBussinessDto.getStartTime().getTime()  && datetime.getTime()<agreementMerchantDetailBussinessDto.getEndTime().getTime() ){
                model.addAttribute("showStatus",1);
            }
            //签约状态后台判断
            if (agreementMerchantDetailBussinessDto.getAuditStates() != null && agreementMerchantDetailBussinessDto.getAuditStates().byteValue() == 3){
                signStatus = 1;
            }else {
                signStatus = 0;
            }
            model.addAttribute("merchant", merchant);
            model.addAttribute("agreementMerchantSkuPage", agreementMerchantSkuPage);
            model.addAttribute("signStatus",signStatus);
            model.addAttribute("agreementMerchant", agreementMerchantDetailBussinessDto);
            model.addAttribute("center_menu", "threeInOneAgreement");
            model.addAttribute("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            return new ModelAndView("/threeInOneAgreement/threeInOneAgreementDetail.ftl");
        } catch (Exception e) {
            LOGGER.error("跳转协议详情页面异常,e="+ExceptionUtils.getStackTrace(e));
            return new ModelAndView("/threeInOneAgreement/threeInOneAgreementDetail.ftl");
        }
    }

    /**
     * 签署协议
     * @param agreementId
     * @return
     */
    @RequestMapping("/signAgreement")
    @ResponseBody
    public Object signAgreement(Long agreementId) {
        try {
            if (null == agreementId) {
                return this.addError("必要的参数不能为空");
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ResultMessage<Object> resultMessage = agreementInfBussinessApi.agreementSigned(merchant.getId(), agreementId,merchant.getRegisterCode());
            if (resultMessage.getCode() == 1) {
                return this.addResult("data", true);
            }
            return this.addError(resultMessage.getMsg());
        } catch (Exception e) {
            return this.addError("签约协议失败");
        }
    }

    /**
     * 协议申请
     * @param agreementId
     * @return
     */
    @RequestMapping("/applyAgreement")
    @ResponseBody
    public Object applyAgreement(Long agreementId) {
        try {
            if (null == agreementId) {
                return this.addError("必要的参数不能为空");
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ResultMessage<Object> resultMessage = agreementInfBussinessApi.agreementApply(merchant.getId(), agreementId,merchant.getRegisterCode());
            if (resultMessage.getCode() == 1) {
                return this.addResult("data", true);
            }
            return this.addError(resultMessage.getMsg());
        } catch (Exception e) {
            return this.addError("申请协议失败");
        }
    }
}
