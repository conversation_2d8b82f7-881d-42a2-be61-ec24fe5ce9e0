package com.xyy.ec.pc.cms.service.complement.dto;

import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * CMS：补足查询上下文，秒杀商品列表
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsSeckillProductListComplementContext implements Serializable {

    /**
     * 商品ID - 秒杀活动信息 Map
     */
    private Map<Long, MarketingSeckillActivityInfoDTO> csuIdToSeckillActivityInfoMap;

    /**
     * 商品ID - 商品信息 Map
     */
    private Map<Long, ListProduct> csuIdToProductInfoMap;

}
