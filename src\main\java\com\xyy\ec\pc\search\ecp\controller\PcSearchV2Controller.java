package com.xyy.ec.pc.search.ecp.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.common.enums.AppEventTrackingPropertyKeyEnum;
import com.xyy.ec.pc.common.enums.AppEventTrackingSpTypeEnum;
import com.xyy.ec.pc.common.params.AppEventTrackingSpIdGenerateParam;
import com.xyy.ec.pc.common.utils.AppEventTrackingUtils;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.search.config.SearchProperties;
import com.xyy.ec.pc.search.dto.QtListDataDTO;
import com.xyy.ec.pc.search.ecp.helpers.*;
import com.xyy.ec.pc.search.ecp.params.PcSearchQueryParam;
import com.xyy.ec.pc.search.ecp.params.PcSearchRecPurchaseQueryParam;
import com.xyy.ec.pc.search.ecp.service.EcpPcSearchService;
import com.xyy.ec.pc.search.ecp.service.PcSearchAggregateDataService;
import com.xyy.ec.pc.search.ecp.vo.PcSearchCardVO;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ec.search.engine.ecp.api.EcpSearchApi;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.*;
import com.xyy.ec.search.engine.ecp.params.EcpSearchQueryParam;
import com.xyy.ec.search.engine.ecp.params.EcpSearchRecPurchaseQueryParam;
import com.xyy.ec.search.engine.ecp.result.EcpSearchAggregateResult;
import com.xyy.ec.search.engine.ecp.result.EcpSearchBaseCardDTO;
import com.xyy.ec.search.engine.ecp.result.EcpSearchResult;
import com.xyy.ec.search.engine.enums.SearchType;
import com.xyy.ec.search.engine.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@RequestMapping("/pc/search/v2")
@RestController
public class PcSearchV2Controller extends BaseController {

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private EcpSearchApi ecpSearchApi;

    @Autowired
    private PcSearchAggregateDataService pcSearchAggregateDataService;

    @Autowired
    private EcpPcSearchService ecpPcSearchService;

    @Autowired
    private PcVersionUtils pcVersionUtils;

    @Autowired
    private SearchProperties searchProperties;

    /**
     * 搜索推荐购买
     *
     * @param searchRecPurchaseQueryParam
     * @param httpServletRequest
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/searchRecPurchase", method = RequestMethod.POST)
    public XyyJsonResult searchRecPurchase(PcSearchRecPurchaseQueryParam searchRecPurchaseQueryParam, HttpServletRequest httpServletRequest) {
        Long accountId = null;
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                // 未登录 提示鉴权异常
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }
            // 参数校验
            boolean validate = PcSearchRecPurchaseQueryParamHelper.validate(searchRecPurchaseQueryParam);
            if (BooleanUtils.isNotTrue(validate)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "查询参数非法");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getId();
            String branchCode = merchant.getRegisterCode();
            if (log.isDebugEnabled()) {
                log.debug("搜索推荐购买，accountId：{}，merchantId：{}，searchRecPurchaseQueryParam：{}",
                        accountId, merchantId, JSONObject.toJSONString(searchRecPurchaseQueryParam));
            }
            /* 埋点 */
            searchRecPurchaseQueryParam.setScmId(RandomStringUtils.randomAlphanumeric(8));
            /* 搜索 */
            /* 资质处理/价格处理 */
            pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            Boolean priceDisplayFlag = merchant.getPriceDisplayFlag();
            // 显示价格
            PcSearchCardVO searchRecPurchaseCardVO = null;
            if (BooleanUtils.isTrue(priceDisplayFlag)) {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                EcpSearchRecPurchaseQueryParam ecpSearchRecPurchaseQueryParam = EcpSearchRecPurchaseQueryParamHelper.create(
                        searchRecPurchaseQueryParam, accountId, merchantId);
                // 组合购是否放开批购包邮
                ecpSearchRecPurchaseQueryParam.setIsSupportWholesaleSearchRecPurchase(true);
                ApiRPCResult<EcpSearchBaseCardDTO> apiRPCResult = ecpSearchApi.searchRecPurchase(ecpSearchRecPurchaseQueryParam);
                if (log.isDebugEnabled()) {
                    log.debug("搜索推荐购买，accountId：{}，merchantId：{}，ecpSearchRecPurchaseQueryParam：{}，apiRPCResult：{}",
                            accountId, merchantId, JSONObject.toJSONString(ecpSearchRecPurchaseQueryParam), JSONObject.toJSONString(apiRPCResult));
                }
                stopWatch.stop();
                if (apiRPCResult.isFail()) {
                    log.error("搜索推荐购买失败，accountId：{}，merchantId：{}，ecpSearchRecPurchaseQueryParam：{}，apiRPCResult：{}",
                            accountId, merchantId, JSONObject.toJSONString(ecpSearchRecPurchaseQueryParam), JSONObject.toJSONString(apiRPCResult));
                    return XyyJsonResult.createFailure().code(apiRPCResult.getCode()).msg(apiRPCResult.getMsg());
                }
                EcpSearchBaseCardDTO cardInfo = apiRPCResult.getData();
                if (Objects.nonNull(cardInfo)) {
                    /* 数据结构转换和填充相关信息 */
                    List<PcSearchCardVO> rows = ecpPcSearchService.convertAndFillSearchCards(null, merchantId, branchCode,
                            priceDisplayFlag, Lists.newArrayList(cardInfo));
                    if (log.isDebugEnabled()) {
                        log.debug("搜索推荐购买，accountId：{}，merchantId：{}，branchCode：{}，priceDisplayFlag：{}，rows：{}",
                                accountId, merchantId, branchCode, priceDisplayFlag, JSONArray.toJSONString(rows));
                    }
                    QuickTrackingDataHelper.handleQuickTrackingData(rows, 1L, 20L);
                    if (CollectionUtils.isNotEmpty(rows)) {
                        searchRecPurchaseCardVO = rows.get(0);
                    }
                }
            }
            XyyJsonResult xyyJsonResult = XyyJsonResult.createSuccess()
                    .addResult("cardInfo", searchRecPurchaseCardVO)
                    .addResult(AppEventTrackingPropertyKeyEnum.SCMID.getPropertyKey(), searchRecPurchaseQueryParam.getScmId());
            return xyyJsonResult;
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("搜索推荐购买失败，accountId：{}，merchantId：{}，searchRecPurchaseQueryParam：{}，msg：{}，异常信息：",
                        accountId, merchantId, JSONObject.toJSONString(searchRecPurchaseQueryParam), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("搜索推荐购买失败，accountId：{}，merchantId：{}，searchRecPurchaseQueryParam：{}，异常信息：",
                    accountId, merchantId, JSONObject.toJSONString(searchRecPurchaseQueryParam), e);
            return XyyJsonResult.createFailure().msg("搜索推荐购买失败，请稍后重试");
        }
    }

    /**
     * 搜索
     *
     * @param searchQueryParam
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/listProducts", method = RequestMethod.POST)
    public XyyJsonResult listProducts(PcSearchQueryParam searchQueryParam, HttpServletRequest httpServletRequest) {
        Long accountId = null;
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                // 未登录 提示鉴权异常
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }
            // 默认值处理
            if (Objects.nonNull(searchQueryParam)) {
                searchQueryParam.setType(Optional.ofNullable(searchQueryParam.getType()).orElse(SearchType.NORMAL_SEARCH.getType()));
                searchQueryParam.setPageNum(Optional.ofNullable(searchQueryParam.getPageNum()).orElse(1));
                searchQueryParam.setPageSize(Optional.ofNullable(searchQueryParam.getPageSize()).orElse(20));
            }
            // 参数校验
            boolean validate = PcSearchQueryParamHelper.validate(searchQueryParam);
            if (BooleanUtils.isNotTrue(validate)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "查询参数非法");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getId();
            String branchCode = merchant.getRegisterCode();
            if (log.isDebugEnabled()) {
                log.debug("搜索（V2），accountId：{}，merchantId：{}，searchQueryParam：{}", accountId, merchantId, JSONObject.toJSONString(searchQueryParam));
            }
            if (!Objects.equals(searchQueryParam.getIsNextPage(), EcpSearchYesNoEnum.YES.getValue()) || StringUtils.isEmpty(searchQueryParam.getSid())) {
                searchQueryParam.setSid(SearchUtils.generateNewSidData(merchantId, EcpTerminalTypeEnum.PC.getType()));
            }
            if (!Objects.equals(searchQueryParam.getIsNextPage(), EcpSearchYesNoEnum.YES.getValue()) || StringUtils.isEmpty(searchQueryParam.getScmId())) {
                searchQueryParam.setScmId(RandomStringUtils.randomAlphanumeric(8));
            }
            EcpSearchSceneEnum searchScene = EcpSearchSceneEnum.valueOfCustom(searchQueryParam.getSearchScene());
            EcpSearchQueryParam ecpSearchQueryParam = EcpSearchQueryParamHelper.create(searchQueryParam, merchantId, accountId);
            ecpSearchQueryParam.setIsNotGetDynamicLabelData(true);
            ecpSearchQueryParam.setIsGetIntervalDayDeliveryData(true);
            // 发版过程中版本兼容：组合购/加价购
            ecpSearchQueryParam.setIsSupportSearchRecPurchase(searchProperties.getIsSupportSearchRecPurchase());
            // 组合购是否放开批购包邮
            ecpSearchQueryParam.setIsSupportWholesaleSearchRecPurchase(true);
            // 热搜词
            List<String> hotQueryWords = ecpPcSearchService.listHotQueryWords(searchQueryParam.getType(), merchantId, branchCode, EcpTerminalTypeEnum.PC.getType());
            /* 搜索 */
            // 保存搜索词历史
            ecpPcSearchService.saveQueryWordHistory(searchScene, merchantId, searchQueryParam.getQueryWord(), ecpSearchQueryParam.getShopCodes());
            EcpSearchResult searchResult = null;
            if (Objects.equals(searchQueryParam.getType(), SearchType.NORMAL_SEARCH.getType())) {
                ApiRPCResult<EcpSearchResult> apiRPCResult = ecpSearchApi.search(ecpSearchQueryParam);
                if (log.isDebugEnabled()) {
                    log.debug("搜索（V2），accountId：{}，merchantId：{}，ecpSearchQueryParam：{}，apiRPCResult：{}", accountId, merchantId, JSONObject.toJSONString(ecpSearchQueryParam), JSONObject.toJSONString(apiRPCResult));
                }
                if (apiRPCResult.isFail()) {
                    log.error("搜索（V2）失败，accountId：{}，merchantId：{}，searchQueryParam：{}，api异常信息：{}",
                            accountId, merchantId, JSONObject.toJSONString(searchQueryParam), JSONObject.toJSONString(apiRPCResult));
                    return XyyJsonResult.createFailure().code(apiRPCResult.getCode()).msg(apiRPCResult.getMsg());
                }
                searchResult = apiRPCResult.getData();
                if (Objects.equals(ecpSearchQueryParam.getPageNum(), 1) && searchResult.getCardPage().getTotalCount() == 0L) {
                    // 第一页请求且没有查到数据，则进入无结果热销精选的搜索场景
                    searchQueryParam.setType(SearchType.RECOMMENDATION.getType());
                }
            }
            // 是否是无结果热销精选的搜索场景
            boolean isNoResultHotSaleSearchScene = Objects.equals(searchQueryParam.getType(), SearchType.RECOMMENDATION.getType());
            if (isNoResultHotSaleSearchScene) {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                EcpSearchQueryParam ecpSearchNoResultHotSaleQueryParam = EcpSearchQueryParam.builder()
                        .merchantId(merchantId)
                        .sortStrategy(EcpSearchSortStrategyEnum.SALES_DESC)
                        .terminalType(EcpTerminalTypeEnum.PC)
                        .searchScene(EcpSearchSceneEnum.NO_RESULT_HOT_SALE)
                        .dynamicLabelConfig(searchQueryParam.getDynamicLabelConfig())
                        .pageNum(searchQueryParam.getPageNum())
                        .pageSize(searchQueryParam.getPageSize())
                        .build();
                ApiRPCResult<EcpSearchResult> apiRPCResult = ecpSearchApi.search(ecpSearchNoResultHotSaleQueryParam);
                if (log.isDebugEnabled()) {
                    log.debug("搜索（V2），无结果热销精选，accountId：{}，merchantId：{}，ecpSearchQueryParam：{}，apiRPCResult：{}", accountId, merchantId, JSONObject.toJSONString(ecpSearchQueryParam), JSONObject.toJSONString(apiRPCResult));
                }
                stopWatch.stop();
                log.info("搜索（V2），无结果热销精选，accountId：{}，merchantId：{}，ecpSearchQueryParam：{}，耗时：{}", accountId, merchantId, JSONObject.toJSONString(ecpSearchQueryParam), stopWatch);
                if (apiRPCResult.isFail()) {
                    log.error("搜索（V2）失败，accountId：{}，merchantId：{}，searchQueryParam：{}，api异常信息：{}",
                            accountId, merchantId, JSONObject.toJSONString(searchQueryParam), JSONObject.toJSONString(apiRPCResult));
                    return XyyJsonResult.createFailure().code(apiRPCResult.getCode()).msg(apiRPCResult.getMsg());
                }
                searchResult = apiRPCResult.getData();
            }
            // 数据结构转换
            IPage<EcpSearchBaseCardDTO> cardPage = searchResult.getCardPage();
            boolean isEnd = searchQueryParam.getPageNum() * searchQueryParam.getPageSize() >= cardPage.getTotalCount();
            /* 资质处理 */
            pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            Integer licenseStatus = merchant.getLicenseStatus();
            Boolean priceDisplayFlag = merchant.getPriceDisplayFlag();
            /* 数据结构转换和填充相关信息 */
            List<PcSearchCardVO> rows = ecpPcSearchService.convertAndFillSearchCards(searchScene, merchantId, branchCode, priceDisplayFlag, cardPage.getRecordList(), BooleanUtils.isTrue(searchResult.getIsShowNextDayDeliveryTag()));
            QuickTrackingDataHelper.handleQuickTrackingData(rows, cardPage.getPageNo(), cardPage.getPageSize());
            if (log.isDebugEnabled()) {
                log.debug("搜索（V2），accountId：{}，merchantId：{}，branchCode：{}，priceDisplayFlag：{}，rows：{}", accountId, merchantId, branchCode, priceDisplayFlag, JSONArray.toJSONString(rows));
            }
            ecpPcSearchService.sendSearchResultCsuInfoMq(accountId, merchantId, EcpTerminalTypeEnum.PC.getType(), null, IPUtils.getClientIP(httpServletRequest), rows);
            /* 标签 */
            searchQueryParam.setDynamicLabelConfig(null);
            /* 埋点 */
            AppEventTrackingSpTypeEnum spTypeEnum;
            if (Objects.equals(searchQueryParam.getType(), SearchType.RECOMMENDATION.getType())) {
                spTypeEnum = AppEventTrackingSpTypeEnum.SEARCH_NO_RESULT_HOT_SALE;
            } else {
                spTypeEnum = AppEventTrackingSpTypeEnum.SEARCH;
            }

            searchQueryParam.setSptype(spTypeEnum.getSpType());

            if (StringUtils.isEmpty(searchQueryParam.getSpid())) {
                searchQueryParam.setSpid(AppEventTrackingUtils.generateSpId(AppEventTrackingSpIdGenerateParam.builder().spTypeEnum(spTypeEnum)
                        .searchScene(searchScene).queryWord(searchQueryParam.getQueryWord()).build()));
            }
            // 全局nsid
            if (StringUtils.isEmpty(searchQueryParam.getNsid())) {
                searchQueryParam.setNsid(SearchUtils.generateNewSidData(merchantId, EcpTerminalTypeEnum.PC.getType()));
            }
            // 列表偏移量
            // 处理已返回总条数
            Integer currentCount = Optional.ofNullable(searchQueryParam.getListoffset()).orElse(0);
            int listSize = CollectionUtils.isEmpty(rows) ? 0 : rows.size();
            int nowCurrentCount = currentCount + listSize;
            searchQueryParam.setListoffset(nowCurrentCount);
            /* feed流 */
            searchQueryParam.setPageNum(searchQueryParam.getPageNum() + 1);
            //埋点数据
            QtListDataDTO qtListDataDTO = QtListDataDTO.builder()
                    .result_cnt(cardPage.getTotalCount())
                    .page_no(cardPage.getPageNo())
                    .page_size(cardPage.getPageSize())
                    .total_page(cardPage.getPages())
                    .key_word(searchQueryParam.getQueryWord())
                    .search_sort_strategy_id(searchResult.getSearchPreciseSortStrategyCode())
                    .build();
            /* 组装响应数据 */
            return XyyJsonResult.createSuccess()
                    .addResult("type", searchQueryParam.getType())
                    .addResult("wordList", hotQueryWords)
                    .addResult("searchSortStrategyId", searchResult.getSearchPreciseSortStrategyCode())
//                    .addResult("isShowOnlyTraditionalChineseMedicine", searchResult.getIsShowOnlyTraditionalChineseMedicine())
//                    .addResult("isShowSameProvince", searchProperties.getIsShowSameProvince())
                    .addResult("rows", rows)
                    .addResult("requestParam", searchQueryParam)
                    .addResult("licenseStatus", licenseStatus)
                    .addResult("isEnd", isEnd)
                    .addResult("pageNo", cardPage.getPageNo())
                    .addResult("pageSize", cardPage.getPageSize())
                    .addResult("totalPage", cardPage.getPages())
                    .addResult("totalCount", cardPage.getTotalCount())
                    .addResult("qtListData", JSONObject.toJSONString(qtListDataDTO))
                    .addResult(AppEventTrackingPropertyKeyEnum.SPTYPE.getPropertyKey(), searchQueryParam.getSptype())
                    .addResult(AppEventTrackingPropertyKeyEnum.SPID.getPropertyKey(), searchQueryParam.getSpid())
                    .addResult(AppEventTrackingPropertyKeyEnum.SID.getPropertyKey(), searchQueryParam.getSid())
                    .addResult(AppEventTrackingPropertyKeyEnum.JGSPID.getPropertyKey(), searchQueryParam.getJgspid())
                    .addResult(AppEventTrackingPropertyKeyEnum.SCMID.getPropertyKey(), searchQueryParam.getScmId());
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("搜索（V2）失败，accountId：{}，merchantId：{}，searchQueryParam：{}，msg：{}，异常信息：",
                        accountId, merchantId, JSONObject.toJSONString(searchQueryParam), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("搜索（V2）失败，accountId：{}，merchantId：{}，searchQueryParam：{}，异常信息：",
                    accountId, merchantId, JSONObject.toJSONString(searchQueryParam), e);
            return XyyJsonResult.createFailure().msg("搜索商品失败，请稍后重试");
        }
    }

    @PostMapping("/aggs")
    @ResponseBody
    public XyyJsonResult searchAggs(PcSearchQueryParam searchQueryParam) {
        Long accountId = null;
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                // 未登录 提示鉴权异常
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }
            // 参数校验
            boolean validate = PcSearchQueryParamHelper.validateForAggregation(searchQueryParam);
            if (BooleanUtils.isNotTrue(validate)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "查询参数非法");
            }
            /* 埋点 */
            if (StringUtils.isEmpty(searchQueryParam.getScmId())) {
                searchQueryParam.setScmId(RandomStringUtils.randomAlphanumeric(8));
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getId();
            // 参数转换
            EcpSearchQueryParam ecpSearchQueryParam = EcpSearchQueryParamHelper.create(searchQueryParam, merchantId, accountId);
            List<EcpSearchAggregateEnum> ecpSearchAggregateEnums = Lists.newArrayListWithExpectedSize(5);
            ecpSearchAggregateEnums.add(EcpSearchAggregateEnum.SHOP_AGG);
            ecpSearchAggregateEnums.add(EcpSearchAggregateEnum.SPEC_AGG);
            ecpSearchAggregateEnums.add(EcpSearchAggregateEnum.MANUFACTURER_AGG);
            ecpSearchAggregateEnums.add(EcpSearchAggregateEnum.CAT_AGG);
            ecpSearchAggregateEnums.add(EcpSearchAggregateEnum.DYNAMIC_LABEL);
            ecpSearchQueryParam.setEcpSearchAggregateEnums(ecpSearchAggregateEnums);
            ecpSearchQueryParam.setIsGetIntervalDayDeliveryData(true);
            // 搜索
            ApiRPCResult<EcpSearchAggregateResult> ecpSearchAggregateResultApi = ecpSearchApi.aggregate(ecpSearchQueryParam);
            if (log.isDebugEnabled()) {
                log.debug("聚合（V2），accountId：{}，merchantId：{}，ecpSearchQueryParam：{}，apiRPCResult：{}", accountId, merchantId, JSONObject.toJSONString(ecpSearchQueryParam), JSONObject.toJSONString(ecpSearchAggregateResultApi));
            }
            if (ecpSearchAggregateResultApi.isFail()) {
                log.error("聚合（V2）失败，accountId：{}，merchantId：{}，searchQueryParam：{}，api异常信息：{}",
                        accountId, merchantId, JSONObject.toJSONString(searchQueryParam), JSONObject.toJSONString(ecpSearchAggregateResultApi));
                return XyyJsonResult.createFailure().code(ecpSearchAggregateResultApi.getCode()).msg(ecpSearchAggregateResultApi.getMsg());
            }
            EcpSearchAggregateResult ecpSearchAggregateResult = ecpSearchAggregateResultApi.getData();
            Map<String, Object> aggregationsMap = Maps.newHashMapWithExpectedSize(5);
            aggregationsMap.put("specStats", pcSearchAggregateDataService.buildAggregateResultVOs(ecpSearchAggregateResult.getSpecs()));
            aggregationsMap.put("shopStats", pcSearchAggregateDataService.buildShopAggregateResultVOs(ecpSearchAggregateResult.getShops()));
            aggregationsMap.put("manufacturerStats", pcSearchAggregateDataService.buildManufacturersAggregateResultVOs(ecpSearchAggregateResult.getManufacturers()));
            aggregationsMap.put("catStats", pcSearchAggregateDataService.buildCsuCategoryAggregateResultVOs(ecpSearchAggregateResult.getCsuCategories()));
            aggregationsMap.put("dynamicLabelConfig", Objects.nonNull(ecpSearchAggregateResult.getSearchDynamicLabelDTO())
                    ? ecpSearchAggregateResult.getSearchDynamicLabelDTO().getSearchDynamicLabels() : Lists.newArrayList());
            return XyyJsonResult.createSuccess()
                    .addResult("aggregations", aggregationsMap)
                    .addResult(AppEventTrackingPropertyKeyEnum.SPTYPE.getPropertyKey(), searchQueryParam.getSptype())
                    .addResult(AppEventTrackingPropertyKeyEnum.SPID.getPropertyKey(), searchQueryParam.getSpid())
                    .addResult(AppEventTrackingPropertyKeyEnum.SID.getPropertyKey(), searchQueryParam.getSid())
                    .addResult(AppEventTrackingPropertyKeyEnum.JGSPID.getPropertyKey(), searchQueryParam.getJgspid())
                    .addResult(AppEventTrackingPropertyKeyEnum.SCMID.getPropertyKey(), searchQueryParam.getScmId());
        } catch (Exception e) {
            log.error("/pc/search/v2/aggs, 请求参数 : {}, error : ", JSONObject.toJSONString(searchQueryParam), e);
            return XyyJsonResult.createFailure();
        }
    }
}
