package com.xyy.ec.pc.newfront.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * @description: TODO
 * @date: 2025/5/27 12:44
 * @author: <EMAIL>
 * @version: 1.0
 */

@Getter
@Setter
@ToString
public class RegisterRespVO {

    String merchantId;

    String mobile;

    String registerSource;

    Boolean status;

    String msg;

    String mobilepwd;

    Integer sourceType;


    Integer licenseStatus;

    String realName;

    String province;

    String provinceCode;

    String city;

    String cityCode;

    String district;

    String areaCode;

    Date activeTime;

    String channelCode;

}
