package com.xyy.ec.pc.newfront.dto;

import com.xyy.ec.pc.newfront.vo.discountedPriceVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchCardVO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class SearchRespVO {

    // 搜索元数据
    private Integer type;
    //热搜词
    private List<String> wordList;
    private String searchSortStrategyId;
    private Integer licenseStatus;

    // 分页信息
    private Long pageNo;
    private Long pageSize;
    private Long totalPage;
    private Long totalCount;
    private Boolean isEnd;


    // 商品结果
    private List<SearchCardRespVO>  rows;

    private List<discountedPriceVO>  discountedPriceList;



    // 事件追踪属性
    private String spType;
    private String spId;
    private String sid;
    private String jgspId;
    private String scmId;



    public SearchRespVO(){

    }


}
