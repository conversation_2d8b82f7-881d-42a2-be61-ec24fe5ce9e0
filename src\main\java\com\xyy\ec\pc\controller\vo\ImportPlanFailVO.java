package com.xyy.ec.pc.controller.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 导入失败的VO
 * <AUTHOR>
 */
@Data
public class ImportPlanFailVO  implements Serializable {
    @Excel(name = "批准文号", width = 20)
    private String excelApprovalNumber;

    @Excel(name = "商品名称", width = 20)
    private String excelCommonName;

    @Excel(name = "规格", width = 20)
    private String excelSpec;

    @Excel(name = "生产厂家", width = 20)
    private String excelManufacturer;

    @Excel(name = "条形码", width = 20)
    private String excelCode;

    @Excel(name = "数量", width = 20)
    private Integer excelBuyNum;

    @Excel(name = "数据来源", width = 20)
    private String datasource;

    @Excel(name = "商家", width = 20)
    private String shopName;

    @Excel(name = "药帮忙价/连锁指导价", width = 20)
    private BigDecimal fob;

    @Excel(name = "库存", width = 20)
    private Integer availableQty;

    @Excel(name = "采购数量", width = 20)
    private Integer purchaseNum;

    @Excel(name = "生产日期", width = 20)
    private String productionDate;

    @Excel(name = "有效期", width = 20)
    private String effectiveDate;

    @Excel(name = "药帮忙批准文号", width = 20)
    private String approvalNumber;

    @Excel(name = "促销类型", width = 20)
    private String discountDesc;

    @Excel(name = "不可购买原因", width = 40)
    private String noBuyMsg;

    //商品ID 商品编码
    @Excel(name = "商家编码", width = 20)
    private String merchantCode;

    @Excel(name = "商品ID", width = 20)
    private String csuId;
    /**
     * 错误信息
     */
    @Excel(name = "失败原因", width = 40)
    private String errorMsg;
}
