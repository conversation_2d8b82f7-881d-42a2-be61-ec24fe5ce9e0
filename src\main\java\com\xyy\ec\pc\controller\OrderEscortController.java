package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.api.OrderDeliveryBusinessApi;
import com.xyy.ec.order.business.api.OrderEscortBusinessApi;
import com.xyy.ec.order.business.dto.MyOrderBusinessDto;
import com.xyy.ec.order.business.dto.OrderBusinessDto;
import com.xyy.ec.order.business.dto.OrderDeliveryBusinessDto;
import com.xyy.ec.order.business.dto.OrderEscortBusinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.base.Sort;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.system.business.api.CodeitemBusinessApi;
import com.xyy.ec.system.business.dto.CodeitemBusinessDto;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/merchant/center/orderEscort")
public class OrderEscortController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(OrderEscortController.class);

    @Reference(version = "1.0.0")
    private OrderEscortBusinessApi orderEscortBusinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private CodeitemBusinessApi codeitemBusinessApi;

    @Autowired
    private Config config;

    @RequestMapping("/index.htm")
    public String toIndex(OrderEscortBusinessDto orderEscortBusinessDto, Page page, ModelMap modelMap, HttpServletRequest request){

        try {
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageNum(page.getOffset());
            pageInfo.setPageSize(page.getLimit());
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            orderEscortBusinessDto.setMerchantId(merchant.getId());
            PageInfo<OrderEscortBusinessDto> modelPageInfo = orderEscortBusinessApi.selectOrderEscortList(pageInfo, orderEscortBusinessDto);

            page.setRows(modelPageInfo.getList());
            page.setTotal(modelPageInfo.getTotal());
            page.setPageCount(new Long(modelPageInfo.getTotal()).intValue()/page.getLimit() +1);
            String requestUrl = this.getRequestUrl(request);
            page.setRequestUrl(requestUrl);
            page.setOffset(page.getOffset());
            modelMap.put("pager", page);
            modelMap.put("productImageUrl", config.getProductImagePathUrl());
            modelMap.put("center_menu", "orderEscort");
        } catch (Exception e) {
            logger.error("查询保价护航提交列表异常", e);
        }

        return "/orderEscort/index.ftl";
    }

    @RequestMapping("/orderEscortInfo.htm/{id}")
    public String toOrderEscortInfo(@PathVariable("id") Long id, ModelMap modelMap){
        OrderEscortBusinessDto orderEscortInfo = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            orderEscortInfo = orderEscortBusinessApi.getOrderEscortInfo(id);
            if (orderEscortInfo == null || !orderEscortInfo.getMerchantId().equals(merchant.getId())){
                orderEscortInfo = null;
            }
        } catch (Exception e) {
            logger.error("查询保价护航详情异常", e);
        }

        modelMap.put("orderEscortInfo",orderEscortInfo);
        modelMap.put("productImageUrl", config.getProductImagePathUrl());
        modelMap.put("center_menu", "orderEscort");
        return "/orderEscort/orderEscortInfo.ftl";
    }

    @RequestMapping("/getOrderEscortSwitch")
    @ResponseBody
    public Object getOrderEscortSwitch(){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();


            List<CodeitemBusinessDto> codeitemBusinessDtoList = codeitemBusinessApi.selectByCodemapRTList("ORDER_ESCORT_SWITCH", merchant.getRegisterCode());
            Map<String, String> stringMap = null;
            if (CollectionUtils.isNotEmpty(codeitemBusinessDtoList)) {
                stringMap = codeitemBusinessDtoList.stream().collect(Collectors.toMap(CodeitemBusinessDto :: getCode, CodeitemBusinessDto :: getName));
            }

            if (stringMap != null && "1".equals(stringMap.get("ORDER_ESCORT"))){
                return "1";//订单保驾护航开
            }else{
                return "0";//订单保驾护航关
            }
        } catch (Exception e) {
            logger.error("查询保价护航提交开关异常", e);
        }

        return "0";
    }

}


