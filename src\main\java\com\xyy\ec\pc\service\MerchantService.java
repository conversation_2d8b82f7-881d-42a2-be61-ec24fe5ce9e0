package com.xyy.ec.pc.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.bussiness.dto.MerchantBalanceJournalBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.params.*;
import com.xyy.ec.merchant.bussiness.result.MerchantRelateShopResult;
import com.xyy.ec.pc.controller.vo.merchant.MerchantAccountRelatedMerchantInfoVO;
import com.xyy.ec.pc.controller.vo.merchant.MerchantClerkInfoVO;
import com.xyy.ec.pc.controller.vo.merchant.MerchantClerkLicenseAuditInfoVO;
import com.xyy.ec.pc.param.VirtualGoldReqDto;
import com.xyy.ec.pc.vo.VirtualGoldLogVO;
import com.xyy.ec.pc.base.Page;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: z<PERSON><PERSON>
 * @Date: 2018/8/28 10:19
 * @Description: 用户服务类
 */
public interface MerchantService {

    /**
     * 获取用户信息
     * @param merchantId
     * @return
     */
    MerchantBussinessDto getMerchant(Long merchantId);

    /**
     * 查询余额
     * @param orderNo
     * @return
     */
    MerchantBalanceJournalBussinessDto getSourceUseBanalce(String orderNo);

    /**
     * 根据同步码查询商户信息
     * @param syncNO
     * @return
     */
    MerchantBussinessDto getMerchantBySync(String syncNO) throws Exception;

    /**
     * 根据手机号查询用户信息
     * @param mobile
     * @return
     * @throws Exception
     */
    MerchantBussinessDto findMerchantByMobile(String mobile) throws Exception;

    /**
     * 调用通行证
     * @param tgc 票据
     */
//    Long doPostOa(String tgc);

    /**
     * 关联店铺
     *
     * @param param
     * @return
     */
    MerchantRelateShopResult relateShop(MerchantRelateShopParam param);

    /**
     * 取消店铺关联
     *
     * @param param
     */
    void cancelShopRelation(MerchantCancelShopRelationParam param);

    /**
     * 添加店员资质审核信息
     *
     * @param param
     */
    void addClerkLicenseAudit(MerchantAddClerkLicenseAuditParam param);

    /**
     * 获取店铺资质审核信息
     *
     * @param param
     * @return
     */
    MerchantClerkLicenseAuditInfoVO getClerkLicenseAuditInfo(MerchantGetClerkLicenseAuditInfoParam param);

    /**
     * 分页查询账号关联的店铺
     *
     * @param queryParam
     * @return
     */
    PageInfo<MerchantAccountRelatedMerchantInfoVO> listAccountRelatedMerchants(MerchantListAccountRelatedMerchantsQueryParam queryParam);

    /**
     * 分页查询店铺的店员信息
     *
     * @param queryParam
     * @return
     */
    PageInfo<MerchantClerkInfoVO> listMerchantClerks(MerchantListMerchantClerksQueryParam queryParam);

    /**
     * saas查询店铺信息
     * @param accountId
     * @return
     */
    List<MerchantBussinessDto> findMerchantForSaasByAccountId(Long accountId);


    BigDecimal queryVirtualGold(Long merchantId);

    Page<VirtualGoldLogVO> queryVirtualGoldLogList(VirtualGoldReqDto reqDto);


}
