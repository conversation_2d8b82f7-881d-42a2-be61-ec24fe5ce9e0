package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.merchant.bussiness.api.ShippingAddressBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.AddressMapBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.ShippingAddressBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.ShippingAddressBussinessExtensionDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.model.ShippingAddress;
import com.xyy.ec.pc.util.HttpClientUtil;
import com.xyy.ec.pc.util.JsonUtil;
import com.xyy.ec.pc.util.MobileValidateUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:
 * @Author: WanKp
 * @Date: 2018/8/26 09:31
 **/
@Controller
@RequestMapping("/merchant/center/shippingAddress")
public class ShippingAddressController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShippingAddressController.class);

    @Reference(version = "1.0.0")
    private ShippingAddressBussinessApi shippingAddressBussinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    /**
     * 中台url
     */
    @Value("${zt_url}")
    private String ztUrl;

    /**
     * 中台-判断手机号是否是销售
     */
    @Value("${zt_validateUserSaleByMobile}")
    private String ztValidateUserSaleByMobile;

    /**
     * 跳转到收货地址页面
     * @Title: myaddress
     * @param modelMap
     * @return
     * String
     * <AUTHOR>
     * @date 2016-12-25 下午1:19:09
     */
    @RequestMapping("/myaddress.htm")
    public String myaddress(ModelMap modelMap) {
        ShippingAddressBussinessDto shippingAddressBussinessDto = new ShippingAddressBussinessDto();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            shippingAddressBussinessDto.setMerchantId(merchant.getId());

            AddressMapBussinessDto  addressMapBussinessDto = shippingAddressBussinessApi.initAddress(shippingAddressBussinessDto);

            modelMap.put("addressList", addressMapBussinessDto.getAddressList());
            modelMap.put("gspAddress", addressMapBussinessDto.getDto());
            modelMap.put("center_menu", "address");
            modelMap.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            modelMap.put("merchant", merchant);
        } catch (Exception e) {
            LOGGER.error("跳转收货地址异常",e);
        }
        return "/merchantCenter/myaddress.ftl";
    }


    /**
     * 修改收货地址
     * @Title: updateShippingAddress
     * @param shippingAddress
     * @return
     * Object
     * <AUTHOR>
     * @date 2016-12-23 上午9:53:36
     */
    @RequestMapping("/updateShippingAddress.json")
    @ResponseBody
    public Object updateShippingAddress(ShippingAddressBussinessDto shippingAddress) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            shippingAddress.setMerchantId(merchant.getId());
            shippingAddressBussinessApi.updateShippingAddress(shippingAddress);
        } catch (Exception e) {
            LOGGER.error("更新地址异常",e);
            return this.addError("服务器异常!");
        }
        return this.addResult();
    }

    /**
     * 新版地址管理-修改商户地址
     */
    @RequestMapping("updateGSPAddress.json")
    @ResponseBody
    public Object updateGSPAddress(ShippingAddress shippingAddress) {

        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ShippingAddressBussinessDto addressBussinessDto = shippingAddressBussinessApi.selectByPrimaryKey(shippingAddress.getId());
            if(!addressBussinessDto.getMerchantId().equals(merchant.getId())){
                return this.addError("非法操作！");
            }
            if (!MobileValidateUtil.isTelAndMobile(shippingAddress.getMobile())){
                return this.addError("手机号格式有误，请确认后重新填写！");
            }
            if (MobileValidateUtil.isPass(shippingAddress.getMobile())) {
                //判断手机号是否为销售手机号
                LOGGER.info("调用中台地址为:{}",ztUrl+ztValidateUserSaleByMobile+shippingAddress.getMobile());
                String resultString = HttpClientUtil.doGet(ztUrl+ztValidateUserSaleByMobile+shippingAddress.getMobile());
                LOGGER.info("中台返回根据手机号返回信息：{}",JSON.toJSONString(resultString));
                Map<String, Object> httpRes = (Map<String, Object>) JSON.parse(resultString);
                if (!(Integer.valueOf(httpRes.get("code").toString()) == 200)) {
                    return this.addError(httpRes.get("msg").toString());
                }
                if (Boolean.valueOf(httpRes.get("result").toString())) {
                    return this.addError("内部员工号码无法添加");
                }
            } else {
                if (!MobileValidateUtil.isTelPass(shippingAddress.getMobile())) {
                    return this.addError("手机号格式有误，请确认后重新填写！");
                }
            }
            shippingAddress.setMerchantId(merchant.getId());
            shippingAddress.setRemark(null);
            ShippingAddressBussinessExtensionDto dto = new ShippingAddressBussinessExtensionDto();
            BeanUtils.copyProperties(shippingAddress,dto);
            shippingAddressBussinessApi.updateGSPAddressNew(dto);
            return this.addResult("shippingAddress", shippingAddress);
        } catch (Exception e) {
            LOGGER.error("地址修改异常", e);
            return this.addError("地址修改异常");
        }
    }

    /**
     * 临时获取市区编码做地区控销
     * @param merchantId
     * @return
     */
    @RequestMapping("/getCityCode")
    @ResponseBody
    public Object selectCityCode(@RequestParam("merchantId") Long merchantId) {
        try {
            ShippingAddressBussinessDto shippingAddressBussinessDto= shippingAddressBussinessApi.getDefaultAddressByMerchantId(merchantId);
            return this.addResult("data", shippingAddressBussinessDto);
        } catch (Exception e) {
            LOGGER.error("查询市区异常,e="+ExceptionUtils.getStackTrace(e));
            return this.addError("查询市区异常");
        }
    }

    /**
     * @Data 2019.7.14
     * 设置默认地址-修改商户地址
     */
    @RequestMapping("updateGSPAddressDefault.json")
    @ResponseBody
    public Object updateGSPAddressDefault(ShippingAddress shippingAddress) {

        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ShippingAddressBussinessDto addressBussinessDto = shippingAddressBussinessApi.selectByPrimaryKey(shippingAddress.getId());
            if(!addressBussinessDto.getMerchantId().equals(merchant.getId())){
                return this.addError("非法操作！");
            }
            shippingAddress.setMerchantId(merchant.getId());
            shippingAddress.setUpdatetime(new Date());
            ShippingAddressBussinessExtensionDto dto = new ShippingAddressBussinessExtensionDto();
            BeanUtils.copyProperties(shippingAddress,dto);
            shippingAddressBussinessApi.updateGSPAddressNew(dto);
            return this.addResult("shippingAddress", shippingAddress);

        } catch (Exception e) {
            LOGGER.error("服务器异常", e);
            return this.addError("服务器异常");
        }
    }

}
