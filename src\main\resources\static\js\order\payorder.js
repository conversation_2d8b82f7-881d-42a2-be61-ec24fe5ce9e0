$(function() {
    var myCards = [];
    var showMore = false;
    var loading = false;
    $('#dialog').hide();
    if(! $(".zffs .cur").length){     
        $("ul").each(function() {
            $(this).children('[apply-code="2"]').addClass("cur");
        });
    }
   
 

    /*选择支付方式*/
    $('.zffs ul li').click(function() {
        $(this).addClass("cur").siblings().removeClass("cur");
        $('input:checkbox').each(function() {
            $(this).prop('checked', false);
        });
        $("#bankCardPayment").removeClass('onBorder');
    });
    /*更多支付方式*/
    $('.zffs ul li').each(function() {
        var isHide = $(this).attr("data-hide");
        if (isHide) {
            $(this).hide();
        }
    });
    $(".zhifu-more").click(function() {
        $('.zffs ul li').each(function() {
            $(this).show();
        });
        $(".zhifu-more").hide();
    })

    /*显示支付后提示弹窗$("#payModal").modal('show');*/
    $(".close-btn").click(function() {
        $("#payModal").modal('hide');
    })

    $("#pay_btn").click(function() {
        try{
            payBtnClickQt();
        }catch(e){}
        var payType = $(".zffs .cur").attr("apply-code");
        var selectedCheckboxVal = $('input[type="checkbox"]:checked').val();
        if (typeof(payType) == "undefined" || payType == '') {
            //  && !selectedCheckboxVal
            if (myCards.length && !selectedCheckboxVal) {
                $.alert("请选择支付方式！");
                return false;
            } else if ($('#useVirtualGold').val() == 'true') {
                payType = 11;
                var token = $('#token').val();
                sureGoPay(token)
                    //newWindow.location.href = '/shop/payment?paycode=' +payType + '&orderId=' + $('#orderId').val()+'&isList='+$('#isList').val()+"&tranNo="+$('#tranNo').val()+"&token="+$('#token').val();
                return;
            }
        }

        if ((typeof(payType) == "undefined" || payType == '') && (myCards.length && selectedCheckboxVal) && $('#useVirtualGold').val() != 'true') {
            $('#dialog').show();
            return false;
        }

        //支付前，增加实名认证判定
        let authenticationDate={
            orderId: $('#orderId').val()
        }
        $.ajax({
            url: "/merchant/center/order/ra/authentication.json",
            "data":authenticationDate,
            type: "GET",
            dataType: "JSON",
            async: false,
            /*设置成同步*/
            success: function(data) {
                if (data.status === "success" && data.data.code == '40000') {
                    //该IP需要认证弹出认证窗口
                    $(".vertifyModalTitle").text(data.data.msg);

                    var initData = $(".datasub1").text();

                    if (120 == initData) {
                        $(".code-btn1").css('display', 'none');
                        $('.yzmbox-repe1').css('display', 'inline-block');

                        var storeData = initData;
                        var sh = setInterval(function() {
                            if (initData > 0) {
                                $(".datasub1").text(--initData);
                            } else {
                                clearInterval(sh);
                                $(".code-btn1").css('display', 'inline-block');
                                $('.yzmbox-repe1').css('display', 'none');
                                initData = storeData;
                                $(".datasub1").text(initData);
                            }
                        }, 1000);
                    }
                    $("#vertifyModal").modal("show");
                } else {
                    //该IP不需要认证，直接跳转支付
                    if(payType=='12' || payType=='14' || payType == '15'){

                    }else{
                        $("#payModal").modal('show');
                    }
                  

                    if (payType == "4" || payType == "12" || payType == "14" || payType == '15') {
                        var strGWJ = '/shop/payment?paycode=' + payType + "&rechargeType=" + $('#rechargeType').val() + '&amount=' + $('#cashPayAmount').val() + '&reqScene=pccashier';
                        var strOrder = '/shop/payment?paycode=' + payType + '&orderId=' + $('#orderId').val() + '&isList=' + $('#isList').val() + "&rechargeType=" + $('#rechargeType').val()
                        let payMentUrl = $('#rechargeType').val() == 2 ? strGWJ : strOrder;
                        if(window.scmAndSpm){
                            payMentUrl=payMentUrl+'&qtdata='+encodeURIComponent(JSON.stringify(window.scmAndSpm));
                        }
                        $.ajax({
                            type: "POST",
                            url: payMentUrl,
                            data: {},
                            dataType: "json",
                            success: function(data) {
                                if (data.status == 'success') {
                                    $("#payModal").modal('show');
                                    if (payType == "12") {
                                    $('#scenceForm').attr('action',data.data.openUrl);
                                    $("#jrgw-user-id-type").val("0")
                                    $("#gw-encrypt-type").val(data.data.gwEncryptType)
                                    $("#gw-sign-type").val(data.data.gwSignType)
                                    $("#encrypt").val(data.data.encrypt)
                                    $("#gw-sign").val(data.data.sign)
                                    $("#jrgw-request-time").val(data.data.time)
                                    $("#jrgw-enterprise-user-id").val(data.data.userId)
                                    setTimeout(()=>{
                                        $("#submitForm").click()
                                    },200)
                                    } else {
                                        var newWindow = window.open();
                                        newWindow.location.href = data.data;
                                    }
                                } else {
                                    $.alert(data.errorMsg);
                                }

                            }
                        });
                    } else {
                        var strGWJ = '/shop/payment?paycode=' + payType + "&rechargeType=" + $('#rechargeType').val() + '&amount=' + $('#cashPayAmount').val() + '&reqScene=pccashier';
                        var strOrder = '/shop/payment?paycode=' + payType + '&orderId=' + $('#orderId').val() + '&isList=' + $('#isList').val() + "&rechargeType=" + $('#rechargeType').val();
                        var paymentUrl = $('#rechargeType').val() == 2 ? strGWJ : strOrder;
                        if(window.scmAndSpm){
                            paymentUrl=paymentUrl+'&qtdata='+encodeURIComponent(JSON.stringify(window.scmAndSpm));
                        }
                        var newWindow = window.open();
                        newWindow.location.href = paymentUrl;
                    }
                }
            }
        });
    });

    $("#payfin_btn").click(function() {
        $("#payModal").modal('hide');
        var payType = $(".zffs .cur").attr("apply-code");
        $.ajax({
            type: "POST",
            url: "/shop/queryPay",
            data: { "paycode": payType, "orderId": $('#orderId').val() },
            dataType: "json",
            success: function(data) {}
        });
        var rechargeType = $('#rechargeType').val();
        if (rechargeType == 2) {
            window.location.href = "/pc/virtual/gold/virtualGold/index"
        }else {
            window.location.href = "/merchant/center/order/index.htm";
        }
    });
    $("#payagain_btn").click(function() {
        $("#payModal").modal('hide');
        $.ajax({
            type: "POST",
            url: "/shop/queryPay",
            data: { "paycode": payType, "orderId": $('#orderId').val() },
            dataType: "json",
            success: function(data) {}
        });
        window.location.href = "/merchant/center/order/confirmOrder.htm?id=" + $('#orderId').val();
    });
    timer('timer');



    $(".sui-modal-backdrop").click(function(e) {
        e.stopPropagation();
    })
    $("#modifyPhone").click(function() {
        $("#phoneModal").modal("show")
    })

    /*实名认证，重新发送短信验证码*/
    $(".code-btn1").click(function() {
        $.ajax({
            url: "/merchant/center/order/ra/authenticationRepeat.json",
            "data": { orderId: $('#orderId').val() },
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                if (data.status === "success") {
                    if (data.data.code == '10000') {
                        $(".code-btn1").css('display', 'none');
                        $('.yzmbox-repe1').css('display', 'inline-block');
                        var initData = $(".datasub1").text();
                        var storeData = initData;
                        var sh = setInterval(function() {
                            if (initData > 0) {
                                $(".datasub1").text(--initData);
                            } else {
                                clearInterval(sh);
                                $(".code-btn1").css('display', 'inline-block');
                                $('.yzmbox-repe1').css('display', 'none');
                                initData = storeData;
                                $(".datasub1").text(initData);
                            }
                        }, 1000);
                    } else {
                        $.alert(data.data.msg);
                    }
                } else {
                    $.alert(data.errorMsg);
                }
            }
        });
    });

    /* 实名认证，验证短信验证码 */
    $("#smsAuthentication").click(function() {
        $("#vertifyModal").modal('show');
        if ($("#codeNum").val() == '') {
            $.alert("验证码不能为空!");
            return false;
        }

        $.ajax({
            url: "/merchant/center/order/ra/smsAuthentication.json",
            "data": { orderId: $('#orderId').val(), smsCode: $("#codeNum").val() },
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                if (data.status === "success") {
                    if (data.data.code == '10000') {
                        $("#vertifyModal").modal('hide');
                        $.alert("验证成功");
                    } else {
                        $.alert(data.data.msg);
                    }
                } else {
                    $.alert(data.errorMsg);
                }
            }
        });
    });

    /*实名认证->更换认证手机号，发送短信*/
    $(".code-btn").click(function() {
        var password = $("#password").val();
        if (password == null || password == "") {
            $.alert("登录密码不能为空");
            return false;
        }

        var phoneNum = $("#phoneNum").val();

        if (phoneNum == null || phoneNum == "") {
            $.alert("新手机号不能为空");
            return false;
        }
        if (!isMobile(phoneNum)) {
            $.alert("手机号不规范，请核实");
            return false;
        }

        //调用发送验证码接口
        $.ajax({
            url: "/merchant/center/order/ra/changeMobileSmsSend.json",
            "data": { orderId: $('#orderId').val(), password: password, phoneNum: phoneNum },
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                if (data.status === "success") {
                    if (data.data.code == '10000') {
                        $(".code-btn").css('display', 'none');
                        $('.yzmbox-repe').css('display', 'inline-block');
                        var initData = $(".datasub").text();
                        var storeData = initData;
                        var sh = setInterval(function() {
                            if (initData > 0) {
                                $(".datasub").text(--initData);
                            } else {
                                clearInterval(sh);
                                $(".code-btn").css('display', 'inline-block');
                                $('.yzmbox-repe').css('display', 'none');
                                initData = storeData;
                                $(".datasub").text(initData);
                            }
                        }, 1000);
                        $("#phoneModal").modal('show');
                    } else {
                        $.alert(data.data.msg);
                    }
                } else {
                    $.alert(data.errorMsg);
                }
            }
        });
    });

    /* 实名认证->5. 更换认证手机号，提交更换 */
    $("#changeAuthenticationMobile").click(function() {
        var password = $("#password").val();
        if (password == null || password == "") {
            $.alert("登录密码不能为空");
            return false;
        }

        var phoneNum = $("#phoneNum").val();

        if (phoneNum == null || phoneNum == "") {
            $.alert("新手机号不能为空");
            return false;
        }
        if (!isMobile(phoneNum)) {
            $.alert("手机号不规范，请核实");
            return false;
        }

        var code = $("#code").val();

        if (code == null || code == "") {
            $.alert("验证码不能为空");
            return false;
        }

        $.ajax({
            url: "/merchant/center/order/ra/changeAuthenticationMobile.json",
            "data": { orderId: $('#orderId').val(), password: password, phoneNum: phoneNum, smsCode: code },
            type: "GET",
            dataType: "JSON",
            success: function(data) {
                if (data.status === "success") {
                    if (data.data.code == '10000') {
                        $("#phoneModal").modal('hide');
                        //跳转到委托人信息确认页
                    } else {
                        $.alert(data.data.msg);
                    }
                } else {
                    $.alert(data.errorMsg);
                }
            }
        });
    });

    /* 密码显示隐藏 */
    $("#togglePassword").click(function() {
        var hClass = $("#togglePassword").hasClass("icon-yanjing");
        if (hClass == true) {
            $("#togglePassword").removeClass("icon-yanjing");
            $("#togglePassword").addClass("icon-biyanjing");
            $("#password").attr("type", "password");
        } else if (hClass == false) {
            $("#togglePassword").addClass("icon-yanjing");
            $("#togglePassword").removeClass("icon-biyanjing");
            $("#password").attr("type", "text");
        }
    })
    function getDefaultCard(myCards){
        let isHaveCard=false
        let id=$("#defaultCard").val()
        myCards.forEach(element => {
            if(element.cardId==id){
                isHaveCard=true
            }
        
      });
      return isHaveCard
    }
    $.ajax({
        type: "post",
        url: '/merchant/center/getMyCards',
        data: {},
        cache: false,
        async: false,
        dataType: "json",
        success: function(res) {
            if (res.data && res.data.length) {
                $("#bankCardPayment").css('visibility', 'visible');
                //有默认银行卡
                if(getDefaultCard(res.data)){
                    $('.zffs ul li').each(function() {
                        $(this).removeClass("cur");
                    })
                }            
                // $("#bankCardPayment").addClass('onBorder');
                myCards = res.data;
                renderCardsList(myCards);
            } else {
                $("#bankCardPayment").hide();
            }
        }
    });

    function renderCardsList() {
        var tl = '<div class="list_item">';
        tl += '<div class="showMoreDiv" style="height: ' + (myCards.length > 2 ? '92px' : 'auto') + '">';
        for (var i = 0; i < myCards.length; i++) {
            var item = myCards[i];
            var str = '<div class="list_item_one">'
            if(item.cardId ==$("#defaultCard").val()){
                $("#bankCardPayment").addClass('onBorder');
                str += '<input value="' + item.cardId + '" type="checkbox" checked class="cardCheckbox"/>'
            }else{
                str += '<input value="' + item.cardId + '" type="checkbox" class="cardCheckbox"/>'
            }        
            str += '<img src="' + item.bankLogo + '" />'
            str += '<p>' + item.bankShortName + '</p>'
            str += '<div class="cardType">' + item.cardType + '（' + item.cardNo + '）</div>'
            str += '</div>'
            tl += str;
        }
        tl += '</div>';
        if (myCards.length > 2) {
            tl += '<div class="showMore">更多银行卡<i class="showMoreIcon down"></i></div>'
        }
        tl += '</div>';
        $('#vueRender').html(tl);
        // $('input:checkbox:first').prop('checked', true);
    }

    $(document).on('click', '.showMore', function() {
        showMore = !showMore;
        if (showMore) {
            $('.showMoreDiv').css('height', 'auto');
            $('.showMoreIcon').removeClass("down");
            $('.showMoreIcon').addClass("up");
        } else {
            $('.showMoreDiv').css('height', '92px');
            $('.showMoreIcon').removeClass("up");
            $('.showMoreIcon').addClass("down");
        }
    });

    $(document).on('click', '.list_item_one', function() {
        $('.zffs ul li').each(function() {
            $(this).removeClass("cur");
        })
        $("#bankCardPayment").addClass('onBorder');
        $('input:checkbox').each(function() {
            $(this).prop('checked', false);
        });
        $(this).find('input[type="checkbox"]').each(function() {
            $(this).prop('checked', true);
        })
    });

    $('#showTips').click(function() {
        $.alert("如忘记密码请在APP找回密码");
        // $.toast({
        // 	title: '如忘记密码请在APP找回密码'
        // });
    });

    $('#goPay').click(function() {
        if (loading) return false;
        var pass = $('#passInput').val();
        if (!pass) {
            $.alert("请输入支付密码");
            // $.toast({
            // 		title: '请输入支付密码'
            // });
            return false;
        }
        loading = true;
        var key = CryptoJS.enc.Utf8.parse("FmpHxGc9no95cvd4"); //十六位十六进制数作为密钥
        var srcs = CryptoJS.enc.Utf8.parse(pass);
        var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7, iv: key });
        pass = encrypted.toString();
        $('#passInput').val(pass);
        $.ajax({
            type: "post",
            url: '/merchant/center/checkPayPwd',
            data: {
                pwd: pass,
                orderId: $('#orderId').val(),
                scence: 1,
            },
            cache: false,
            async: false,
            dataType: "json",
            success: function(res) {
                if (res.code === 1000) {
                    var data = res.data;
                    if (data.status === 1) {
                        sureGoPay(data.token);
                    } else {
                        loading = false;
                        $('#tips').html(data.msg);
                        $('#passInput').val('');
                    }
                }
            }
        });
    });

    function sureGoPay(token) {
        var selectedCheckboxVal = $('input[type="checkbox"]:checked').val();
        var currentCard = '';
        var bankShowName = '';
        for (var i = 0; i < myCards.length; i++) {
            if (myCards[i].cardId == selectedCheckboxVal) {
                currentCard = myCards[i];
            }
        }
        if (currentCard) {
            bankShowName = '' + currentCard.bankShortName + '' + currentCard.cardType + '（' + currentCard.cardNo + '）';
        }
        var newWindow = window.open();
        var strGWJ = '/shop/payment?paycode=' + 11 + "&rechargeType=" + $('#rechargeType').val() + '&amount=' + $('#cashPayAmount').val() + '&reqScene=pccashier' + '&cardId=' + selectedCheckboxVal + '&bankCardInfo=' + encodeURIComponent(bankShowName) + '&token=' + token;
        var strOrder = '/shop/payment?paycode=' + 11 + '&orderId=' + $('#orderId').val() + '&isList=' + $('#isList').val() + '&token=' + token + '&cardId=' + selectedCheckboxVal + '&bankCardInfo=' + encodeURIComponent(bankShowName) + '&tranNo=' + $('#tranNo').val() + "&rechargeType=" + $('#rechargeType').val();
        newWindow.location.href = $('#rechargeType').val() == 2 ? strGWJ : strOrder;
        $('#dialog').hide();
        loading = false;
        $('#tips').html('');
        $('#passInput').val('');
    };

    $('#closeDialog').click(function() {
        $('#dialog').hide();
        $('#tips').html('');
        $('#passInput').val('');
    });
});

function timer(eleId) {
    var element = document.getElementById(eleId);
    if (element) {
        endTimer = element.getAttribute('data-timer');
        var endTime = new Date(parseInt(endTimer.substr(0, 4)), parseInt(endTimer.substr(4, 2)), parseInt(endTimer.substr(6, 2)), parseInt(endTimer.substr(8, 2)), parseInt(endTimer.substr(10, 2)), parseInt(endTimer.substr(12, 2)));
        var endTimeMonth = endTime.getMonth() - 1;
        endTime.setMonth(endTimeMonth);
        var ts = endTime - new Date();
        if (ts > 0) {
            var dd = parseInt(ts / 1000 / 60 / 60 / 24, 10);
            var hh = parseInt(ts / 1000 / 60 / 60 % 24, 10);
            var mm = parseInt(ts / 1000 / 60 % 60, 10);
            var ss = parseInt(ts / 1000 % 60, 10);
            dd = dd < 10 ? ("0" + dd) : dd; //天
            hh = hh < 10 ? ("0" + hh) : hh; //时
            mm = mm < 10 ? ("0" + mm) : mm; //分
            ss = ss < 10 ? ("0" + ss) : ss; //秒
            document.getElementById("timer_d").innerHTML = dd;
            document.getElementById("timer_h").innerHTML = hh;
            document.getElementById("timer_m").innerHTML = mm;
            document.getElementById("timer_s").innerHTML = ss;
            setTimeout(function() {
                timer(eleId);
            }, 1000);
        } else {
            document.getElementById("timer_d").innerHTML = 0;
            document.getElementById("timer_h").innerHTML = 0;
            document.getElementById("timer_m").innerHTML = 0;
            document.getElementById("timer_s").innerHTML = 0;
        }
    }
}