package com.xyy.ec.pc.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.remote.ProductForPromotionRemoteService;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.product.business.ecp.out.promotion.api.ProductForPromotionApi;
import com.xyy.ec.product.business.ecp.out.promotion.dto.SkuSimpleDTO;
import com.xyy.ec.product.business.ecp.out.search.api.ProductForSearchApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * {@link ProductForPromotionApi} RPC调用Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductForPromotionRemoteServiceImpl implements ProductForPromotionRemoteService {

    @Reference(version = "1.0.0")
    private ProductForPromotionApi productForPromotionApi;

    @Reference(version = "1.0.0")
    private ProductForSearchApi productForSearchApi;

    @Override
    public List<SkuSimpleDTO> findProductSimpleInfoBySkuIdList(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Lists.newArrayList();
        }
        // PS：由于商品这个接口是基础且重要接口，故而若出错向上传递异常，由上游自行决定如何处理。
        List<SkuSimpleDTO> result = Lists.newArrayListWithExpectedSize(16);
        List<List<Long>> skuIdsLists = Lists.partition(skuIds, 100);
        ApiRPCResult<List<SkuSimpleDTO>> apiRPCResult;
        List<SkuSimpleDTO> tempSkuSimpleDTOS;
        for (List<Long> skuIdsList : skuIdsLists) {
            try {
                apiRPCResult = productForPromotionApi.findProductSimpleInfoBySkuIdList(skuIdsList);
                if (log.isDebugEnabled()) {
                    log.debug("根据商品ID获取商品简易信息，入参：{}，出参：{}", JSONArray.toJSONString(skuIdsList),
                            JSONObject.toJSONString(apiRPCResult));
                }
                if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
                    log.error("根据商品ID获取商品简易信息失败，入参：{}，出参：{}", JSONArray.toJSONString(skuIdsList),
                            JSONObject.toJSONString(apiRPCResult));
                    String msg = Objects.isNull(apiRPCResult) ? "无响应" : apiRPCResult.getMsg();
                    throw new AppException(XyyJsonResultCodeEnum.FAIL, "根据商品ID获取商品简易信息失败：" + msg);
                }
                tempSkuSimpleDTOS = apiRPCResult.getData();
                if (CollectionUtils.isEmpty(tempSkuSimpleDTOS)) {
                    continue;
                }
                result.addAll(tempSkuSimpleDTOS);
            } catch (Exception e) {
                log.error("根据商品ID获取商品简易信息出现异常，入参：{}", JSONArray.toJSONString(skuIdsList), e);
                throw new AppException(XyyJsonResultCodeEnum.FAIL, "根据商品ID获取商品简易信息出现异常：" + e.getMessage());
            }
        }
        return result;
    }
    @Override
    public List<ProductDTO> queryCsuSimpleInfoCsuIdList(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Lists.newArrayList();
        }
        // PS：由于商品这个接口是基础且重要接口，故而若出错向上传递异常，由上游自行决定如何处理。
        List<ProductDTO> result = Lists.newArrayListWithExpectedSize(16);
        List<List<Long>> skuIdsLists = Lists.partition(skuIds, 100);
        ApiRPCResult<List<ProductDTO>> apiRPCResult;
        List<ProductDTO> tempSkuSimpleDTOS;
        for (List<Long> skuIdsList : skuIdsLists) {
            try {
                apiRPCResult = productForSearchApi.queryProductSimpleInfoBySkuIdList(skuIdsList);
                if (log.isDebugEnabled()) {
                    log.debug("根据商品ID获取商品简易信息，入参：{}，出参：{}", JSONArray.toJSONString(skuIdsList),
                            JSONObject.toJSONString(apiRPCResult));
                }
                if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
                    log.error("根据商品ID获取商品简易信息失败，入参：{}，出参：{}", JSONArray.toJSONString(skuIdsList),
                            JSONObject.toJSONString(apiRPCResult));
                    String msg = Objects.isNull(apiRPCResult) ? "无响应" : apiRPCResult.getMsg();
                    throw new AppException(XyyJsonResultCodeEnum.FAIL, "根据商品ID获取商品简易信息失败：" + msg);
                }
                tempSkuSimpleDTOS = apiRPCResult.getData();
                if (CollectionUtils.isEmpty(tempSkuSimpleDTOS)) {
                    continue;
                }
                result.addAll(tempSkuSimpleDTOS);
            } catch (Exception e) {
                log.error("根据商品ID获取商品简易信息出现异常，入参：{}", JSONArray.toJSONString(skuIdsList), e);
                throw new AppException(XyyJsonResultCodeEnum.FAIL, "根据商品ID获取商品简易信息出现异常：" + e.getMessage());
            }
        }
        return result;
    }
}
