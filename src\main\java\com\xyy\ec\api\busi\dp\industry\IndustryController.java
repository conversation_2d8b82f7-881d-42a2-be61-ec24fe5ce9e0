package com.xyy.ec.api.busi.dp.industry;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.chaos.model.ShopBaseInfo;
import com.xyy.chaos.model.ShopDetailInfo;
import com.xyy.chaos.model.enums.ShopStatusEnum;
import com.xyy.ec.api.busi.base.WebBaseController;
import com.xyy.ec.api.rpc.gdd.GongDuoDuoRpcService;
import com.xyy.ec.api.rpc.product.ProductRpcService;
import com.xyy.ec.api.service.IndustryService;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.enums.LayoutMerchantStatusEnum;
import com.xyy.ec.pc.model.dto.XyyIpAddressInfoDTO;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.XyyIpAddressService;
import com.xyy.ec.pc.service.layout.LayoutBaseService;
import com.xyy.ec.pc.shop.service.ShopImageService;
import com.xyy.ec.pc.shop.service.ShopService;
import com.xyy.ec.pc.shop.vo.ShopImageVO;
import com.xyy.ec.pc.shop.vo.ShopInfoVO;
import com.xyy.ec.pc.util.EncodeUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.shop.server.business.enums.ShopProductQueryOrderColumnEnum;
import com.xyy.ec.shop.server.business.params.ShopProductQueryOrderItemParam;
import com.xyy.ec.shop.server.business.params.ShopProductQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther:
 * @Date: 2020/5/12 10:48
 * @Description:copy
 */
@Slf4j
@RestController
@RequestMapping("/industry")
public class IndustryController extends WebBaseController {
    @Autowired
    GongDuoDuoRpcService gongDuoDuoRpcService;
    @Autowired
    IndustryService industryService;
    @Autowired
    ProductRpcService productRpcService;
    @Autowired
    private ShopService shopService;

    @Autowired
    private ShopImageService shopImageService;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private XyyIpAddressService xyyIpAddressService;

    @Autowired
    private LayoutBaseService layoutBaseService;

    @RequestMapping("/dplist.htm")
    public ModelAndView productList(HttpServletRequest request) throws Exception {
        ModelAndView modelAndView = new ModelAndView("/flagshipShop/list.ftl");
        String branchCode = this.getBranchCode();
        List<ShopBaseInfo>  list = gongDuoDuoRpcService.getShopList(branchCode);
        List resu = new ArrayList();
        Long merchantId = this.getUserId();
        if (!CollectionUtils.isEmpty(list)){
            Map<String, Set<String>>  maps = new HashMap<>();
            List<String>  subGoods = new ArrayList();
            for(ShopBaseInfo e:list){
                List<String> goods = e.getGoodsCodeList();
                Set<String> set = new HashSet<>();
                for (int i=0;i<goods.size() && i<200;i++){
                    subGoods.add(goods.get(i));
                    set.add(goods.get(i));
                }
                maps.put(e.getShopId(),set);
            }
            List<ProductDTO> dtos = getPage(merchantId ,branchCode,subGoods);
            for(ShopBaseInfo e:list){
                JSONObject jt = JSONObject.parseObject(JSONObject.toJSONString(e));
                Page<ProductDTO> skuPOJOPage = new Page<>();
                List<ProductDTO> lists = dtos.stream().filter(pdto->maps.get(e.getShopId()).contains(pdto.getBarcode())).collect(Collectors.toList());
                if (lists!=null){
                    lists = lists.subList(0,lists.size()>=5?5:lists.size());
                }
                skuPOJOPage.setRows(lists);
                jt.put("pager",skuPOJOPage);
                resu.add(jt);
            }
        }
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        modelAndView.addObject("merchant",merchant);
        modelAndView.addObject("merchantId", merchantId);
        Integer licenseStatus = null;
        if (merchant != null) {
            merchantId = merchant.getId();
            branchCode = merchant.getRegisterCode();
            licenseStatus = merchant.getLicenseStatus();
        }
        modelAndView.addObject("licenseStatus", licenseStatus);
        modelAndView.addObject("dpList",resu);
        return modelAndView;
    }

    public List<ProductDTO> getPage(Long merchantId,String branchCode,List<String>  subGoods){
        ShopProductQueryParam shopProductQueryParam = ShopProductQueryParam.builder()
                .merchantId(merchantId).branchCode(branchCode).build();
        PageInfo<ProductDTO> pageInfo = industryService.pagingShopProducts(shopProductQueryParam, 1, 1000,subGoods);
        List<ProductDTO> list = pageInfo.getList();
        LayoutMerchantStatusEnum layoutMerchantStatusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchantId);
        if (LayoutMerchantStatusEnum.MARCHANT_LICENSE_NO_PASS.equals(layoutMerchantStatusEnum)) {
            list = layoutBaseService.filterProductAttrsForShop(list);
            pageInfo.setList(list);
        }
        if (CollectionUtils.isEmpty(pageInfo.getList())){
            return list;
        }else{
            return pageInfo.getList();
        }
    }

    /**
     * 店铺活动
     * @return
     */
    @RequestMapping("/industry_dp.htm")
    public ModelAndView activity(IndustryReq actReq, HttpServletRequest request) throws Exception {
        ModelAndView modelAndView = null;
        actReq.setBranchCode(this.getBranchCode());
        actReq.setUserId(this.getUserId());
        ShopDetailInfo shopDetailInfo = gongDuoDuoRpcService.getShopDetailInfo(actReq.getShopCode());
        if (shopDetailInfo==null || shopDetailInfo.getShopStatus().getCode().intValue() != ShopStatusEnum.ONLINE.getCode().intValue()){
            modelAndView = new ModelAndView("/flagshipShop/underline.ftl");
        }else{
            modelAndView = new ModelAndView("/flagshipShop/index.ftl");
        }
        excuteAct(modelAndView,actReq,request,shopDetailInfo);
        return modelAndView;
    }

    private void excuteAct(ModelAndView modelAndView,IndustryReq actReq,HttpServletRequest request,ShopDetailInfo shopDetailInfo) throws Exception {
        String shopCode = actReq.getShopCode();
        Integer modelType = actReq.getModelType();
        Integer hasStock = actReq.getHasStock();
        Integer totalSalesVolumeType = actReq.getTotalSalesVolumeType();
        Integer tagType = actReq.getTagType();
        Integer offset = actReq.getOffset();
        Integer limit = actReq.getLimit();
        Long merchantId = null;

        // 回显查询条件
        modelAndView.addObject("shopCode", shopCode);
        modelAndView.addObject("modelType", modelType);
        modelAndView.addObject("hasStock", hasStock);
        modelAndView.addObject("totalSalesVolumeType", totalSalesVolumeType);
        modelAndView.addObject("tagType", tagType);
        modelAndView.addObject("offset", offset);
        modelAndView.addObject("limit", limit);
        // 分页要用，将url传到前台
        String url = getRequestUrl(request);
        // 初始化 & 转换
        boolean totalSalesVolume = false;
        boolean totalSalesVolumeIsAsc = false;
        if (totalSalesVolumeType != null) {
            if (totalSalesVolumeType.equals(1)) {
                totalSalesVolume = true;
                totalSalesVolumeIsAsc = false;
            } else if (totalSalesVolumeType.equals(2)) {
                totalSalesVolume = true;
                totalSalesVolumeIsAsc = true;
            }
        }
        Integer realHasStock = hasStock;
        if (realHasStock != null && !realHasStock.equals(1)) {
            realHasStock = null;
        }
        int realPageNum = offset == null ? 1 : offset;
        int realPageSize = limit == null ? 20 : limit;

        String branchCode;
        int provinceCode = 0;
        Integer licenseStatus = null;
        // 取出当前登录会员ID
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        if (merchant != null) {
            merchantId = merchant.getId();
            branchCode = merchant.getRegisterCode();
            licenseStatus = merchant.getLicenseStatus();
        } else {
            // 未登录从ip中定位获取
            XyyIpAddressInfoDTO xyyIpAddressInfo = xyyIpAddressService.getXyyIpAddressInfo(request);
            branchCode = xyyIpAddressInfo.getBranchCode();
            provinceCode = xyyIpAddressInfo.getProvinceCode();
        }
        if (log.isDebugEnabled()) {
            log.debug("取出当前的区域相关信息，merchantId：{}，branchCode：{}，provinceCode：{}",
                    merchantId, branchCode, provinceCode);
        }
        ShopInfoVO shopInfoVO = new ShopInfoVO();
        List<ShopImageVO> shopSlideshowImageVOS = Lists.newArrayList();
        List<ShopImageVO> shopFloorImageVOS = Lists.newArrayList();
        modelAndView.addObject("merchant", merchant);
        modelAndView.addObject("merchantId", merchantId);
        modelAndView.addObject("licenseStatus", licenseStatus);
        modelAndView.addObject("isVisible", 1);
        modelAndView.addObject("shopInfo", shopInfoVO);
        modelAndView.addObject("shopSlideshowImages", shopSlideshowImageVOS);
        modelAndView.addObject("shopFloorImages", shopFloorImageVOS);
        if (shopDetailInfo==null || shopDetailInfo.getShopStatus().getCode().intValue() != ShopStatusEnum.ONLINE.getCode().intValue()){
            return;
        }
        shopInfoVO.setBranchCode(branchCode);
//        shopInfoVO.setShopTags(shopDetailInfo.getShopTypeName());
        shopInfoVO.setShopCode(shopCode);
        shopInfoVO.setName(shopDetailInfo.getShopName());
        shopInfoVO.setShowName(shopDetailInfo.getShopName());
        shopInfoVO.setPcLogoUrl(shopDetailInfo.getLogoUrl());
        shopInfoVO.setShopPropertyCode("self");
        shopInfoVO.setPcLink(request.getRequestURI());
        shopInfoVO.setShopPropertyName("自营");
        PageInfo<ProductDTO> pageInfo = new PageInfo<>(Lists.newArrayList());
        pageInfo.setPageNum(realPageNum);
        pageInfo.setPageSize(realPageSize);
// 店铺商品列表
        List<ShopProductQueryOrderItemParam> orderItems = Lists.newArrayListWithCapacity(1);
        // 默认按照销量从高到底排序
        String salesVolumeColumn = ShopProductQueryOrderColumnEnum.THIRTY_DAYS_SALES_VOLUME.getColumn();
        boolean salesVolumeIsAsc = false;
        if (totalSalesVolume) {
            salesVolumeColumn = ShopProductQueryOrderColumnEnum.TOTAL_SALES_VOLUME.getColumn();
            salesVolumeIsAsc = totalSalesVolumeIsAsc;
        }
        ShopProductQueryOrderItemParam orderItemParam = ShopProductQueryOrderItemParam.builder()
                .column(salesVolumeColumn)
                .asc(salesVolumeIsAsc)
                .build();
        orderItems.add(orderItemParam);
        ShopProductQueryParam shopProductQueryParam = ShopProductQueryParam.builder()
                .shopCode(shopCode).merchantId(merchantId).branchCode(branchCode).hasStock(realHasStock).orderItems(orderItems)
                .build();
        pageInfo = industryService.pagingShopProducts(shopProductQueryParam, realPageNum, realPageSize,shopDetailInfo.getGoodsCodeList());
//                pageInfo = shopProductService.pagingShopProducts(shopProductQueryParam, realPageNum, realPageSize);

        // 商品信息根据资质状态过滤
        List<ProductDTO> list = pageInfo.getList();
        LayoutMerchantStatusEnum layoutMerchantStatusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
        if (LayoutMerchantStatusEnum.MARCHANT_LICENSE_NO_PASS.equals(layoutMerchantStatusEnum)) {
            list = layoutBaseService.filterProductAttrsForShop(list);
            pageInfo.setList(list);
        }
        Page<ProductDTO> skuPOJOPage = new Page<>();
        skuPOJOPage.setOffset(pageInfo.getPageNum());
        skuPOJOPage.setLimit(pageInfo.getPageSize());
        if (pageInfo.getTotal()>0){//bug...
            skuPOJOPage.setTotal(pageInfo.getTotal());
        }
        skuPOJOPage.setRows(pageInfo.getList());
        skuPOJOPage.setRequestUrl(url);
        modelAndView.addObject("pager", skuPOJOPage);
    }


    /**
     * 拼接页面参数
     *
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    protected String getRequestUrl(HttpServletRequest request){
        String url = "";
        String requestUri = request.getRequestURI();
        String queryString = request.getQueryString();
        String qs = StringUtil.removeParameter(queryString, "offset");
        if (requestUri.contains("/xyy-ec-pc/")) {
            requestUri = requestUri.replace("/xyy-ex-pc/", "/");
        }
        if (StringUtil.isNotEmpty(qs)) {
            url = requestUri + "?" + EncodeUtil.urlDecode(qs,"UTF-8");
        } else {
            url = requestUri;
        }
        return url;
    }
}

