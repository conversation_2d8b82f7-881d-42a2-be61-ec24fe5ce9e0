package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Maps;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.DateUtils;
import com.xyy.ec.system.business.api.CodeitemBusinessApi;
import com.xyy.ec.system.business.dto.CodeitemBusinessDto;
import com.xyy.ms.promotion.business.api.pc.VoucherForPcBusinessApi;
import com.xyy.ms.promotion.business.common.ErrorCodeEum;
import com.xyy.ms.promotion.business.common.ResultDTO;
import com.xyy.ms.promotion.business.dto.voucher.VoucherSignPromoExtendDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Title:
 * @Description: 签到领券活动
 * @date 2018/9/20 16:35
 */
@RequestMapping("/voucherSing")
@Controller
public class VocherSignPromoController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VocherSignPromoController.class);

    @Reference(version = "1.0.0")
    private VoucherForPcBusinessApi voucherBusinessApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Reference(version = "1.0.0")
    private CodeitemBusinessApi codeitemBusinessApi;


    /**
     * @return java.lang.Object
     * @throws
     * @Description:查询签到活动详细信息
     * <AUTHOR>
     * @date 2018/9/20 16:39
     */
    @RequestMapping("/getSignVoucherDetil")
    @ResponseBody
    public Object getSignVoucherDetil() {
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (null == merchant) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            ResultDTO<VoucherSignPromoExtendDTO> voucherSignPromoExtendDTOResultDTO = voucherBusinessApi.getSignVoucherDetailForPC(merchantId);

            Map<String, Object> map = this.addResult("data", null);
            if (null != voucherSignPromoExtendDTOResultDTO && voucherSignPromoExtendDTOResultDTO.getErrorCode() == ErrorCodeEum.SUCCESS.getErrorCode()) {
                map = this.addResult("data", voucherSignPromoExtendDTOResultDTO.getData());
            }

            /** 获取签到优惠券倒计时相关 */
            String branchCode = merchant.getRegisterCode();
            List<CodeitemBusinessDto> codeitemBusinessDtoList = codeitemBusinessApi.selectByCodemapRTList("VOUCHER_SIGN", branchCode);
            Map<String, String> codeMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(codeitemBusinessDtoList)) {
                codeMap = codeitemBusinessDtoList.stream().collect(Collectors.toMap(codeitemBusinessDto -> codeitemBusinessDto.getCode(), CodeitemBusinessDto::getName));
            }
            if (StringUtils.equalsIgnoreCase("TRUE", codeMap.get("IS_ON"))) {
                long now = new Date().getTime();
                long startTime = DateUtils.parserDate(codeMap.get("START_TIME"), DateUtils.LONG_DATE_FORMAT_STR).getTime();
                long endTime = DateUtils.parserDate(codeMap.get("END_TIME"), DateUtils.LONG_DATE_FORMAT_STR).getTime();
                long starttmp = (startTime - now) / 1000;
                long endTmp = (endTime - now) / 1000;
                map.put("startTime", starttmp <= 0 ? 0 : starttmp);
                map.put("endTime", endTmp <= 0 ? 0 : endTmp);

            }
            return map;
        } catch (Exception e) {
            LOGGER.error("查询签到活动详细信息：", e);
        }
        return this.addError("");
    }
}
