package com.xyy.ec.pc.service.order.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.backend.order.api.OrderStatisticsApi;

import com.xyy.ec.order.backend.order.dto.OrderFeeStatisticsV2QueryDto;
import com.xyy.ec.order.backend.order.dto.StatisticPcByMerchantV2Dto;
import com.xyy.ec.order.search.api.remote.dto.OrderFeeStatisticsQueryDto;
import com.xyy.ec.order.search.api.remote.result.StatisticPcByMerchantDto;
import com.xyy.ec.pc.service.order.OrderPurchaseStatisticV2Service;
import com.xyy.ec.pc.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderPurchaseStatisticV2ServiceImpl implements OrderPurchaseStatisticV2Service {

    @Reference(version = "1.0.0")
    private OrderStatisticsApi orderStatisticsApi;

    @Override
    public StatisticPcByMerchantDto orderFeeStatisticPcByMerchantV2(OrderFeeStatisticsQueryDto param) {
        try {
            OrderFeeStatisticsV2QueryDto paramV2 = new OrderFeeStatisticsV2QueryDto();
            BeanUtils.copyProperties(param, paramV2);
            ApiRPCResult<StatisticPcByMerchantV2Dto> statisticPcByMerchantDtoApiRPCResult = orderStatisticsApi.orderFeeStatisticPcByMerchant(paramV2);
            if (statisticPcByMerchantDtoApiRPCResult.getCode() == ApiResultCodeEum.SUCCESS.getCode()) {
                StatisticPcByMerchantV2Dto resultV2 = statisticPcByMerchantDtoApiRPCResult.getData();
                StatisticPcByMerchantDto result = new StatisticPcByMerchantDto();
                BeanUtils.copyProperties(resultV2, result);
                return result;
            }

        } catch (Exception e) {
            log.error("orderSearchApi#orderFeeStatisticPcByMerchant  查询异常", e);
        }
        return new StatisticPcByMerchantDto();
    }
}

