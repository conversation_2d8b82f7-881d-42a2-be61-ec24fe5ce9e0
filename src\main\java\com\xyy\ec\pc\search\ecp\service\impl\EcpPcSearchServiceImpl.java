package com.xyy.ec.pc.search.ecp.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.config.AppRocketMqFactory;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.TagTypeEnum;
import com.xyy.ec.pc.remote.OrderBackendService;
import com.xyy.ec.pc.remote.ProductForSearchRemoteService;
import com.xyy.ec.pc.remote.ShopQueryRemoteService;
import com.xyy.ec.pc.rpc.EcSearchServiceRpc;
import com.xyy.ec.pc.rpc.ProductServiceRpc;
import com.xyy.ec.pc.search.config.SearchProperties;
import com.xyy.ec.pc.search.dto.SearchCsuDataTagCsuDTO;
import com.xyy.ec.pc.search.ecp.helpers.PcSearchCardVOHelper;
import com.xyy.ec.pc.search.ecp.helpers.PcSearchProductVOHelper;
import com.xyy.ec.pc.search.ecp.service.EcpPcSearchService;
import com.xyy.ec.pc.search.ecp.vo.*;
import com.xyy.ec.pc.search.helpers.SearchCsuDataTagCsuDTOHelper;
import com.xyy.ec.pc.search.params.SearchCsuDataTagQueryParam;
import com.xyy.ec.pc.search.service.DataService;
import com.xyy.ec.pc.search.vo.PcSearchProductInfoVo;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.pc.util.NumberUtil;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.product.business.ecp.csutag.dto.ProductActivityTagDTO;
import com.xyy.ec.product.business.ecp.csutag.dto.TagDTO;
import com.xyy.ec.search.engine.api.EcHotWordsApi;
import com.xyy.ec.search.engine.dto.HotWordsDTO;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchCardTypeEnum;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchSceneEnum;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchYesNoEnum;
import com.xyy.ec.search.engine.ecp.result.*;
import com.xyy.ec.search.engine.enums.HistoryType;
import com.xyy.ec.search.engine.enums.SearchType;
import com.xyy.ec.shop.server.business.api.ShopTagApi;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchRecPurchaseTypeEnum;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EcpPcSearchServiceImpl implements EcpPcSearchService {

    @Reference(version = "1.0.0")
    private EcHotWordsApi hotWordsApi;

    @Autowired
    private ProductForSearchRemoteService productForSearchRemoteService;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private ShopQueryRemoteService shopQueryRemoteService;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private DataService dataService;

    @Autowired
    private ProductServiceRpc productServiceRpc;

    @Reference(version = "1.0.0")
    private ShopTagApi shopTagApi;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private AppRocketMqFactory appRocketMqFactory;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Autowired
    EcSearchServiceRpc ecSearchServiceRpc;

    @Autowired
    private OrderBackendService orderBackendService;
    @Override
    public void sendSearchResultCsuInfoMq(Long accountId, Long merchantId, Integer terminalType, Integer terminalVersion, String ip, List<PcSearchCardVO> searchCardVOS) {
        if (BooleanUtils.isNotTrue(searchProperties.getIsOpenSendSearchResultCsuInfoMq()) || CollectionUtils.isEmpty(searchCardVOS)) {
            return;
        }
        try {
            List<JSONObject> mqs = Lists.newArrayListWithExpectedSize(16);
            for (PcSearchCardVO searchCardVO : searchCardVOS) {
                if (Objects.equals(searchCardVO.getCardType(), EcpSearchCardTypeEnum.PRODUCT_CARD.getType()) && Objects.nonNull(searchCardVO.getProductInfo())) {
                    // 商品卡片
                    JSONObject mq = this.createSearchResultCsuInfoMq(accountId, merchantId, terminalType, terminalVersion, ip, searchCardVO.getProductInfo());
                    if (Objects.nonNull(mq)) {
                        mqs.add(mq);
                    }
                } else if (Objects.equals(searchCardVO.getCardType(), EcpSearchCardTypeEnum.OPERATION_CARD.getType()) && Objects.nonNull(searchCardVO.getOperationInfo())
                        && CollectionUtils.isNotEmpty(searchCardVO.getOperationInfo().getProducts())) {
                    // 运营位卡片
                    searchCardVO.getOperationInfo().getProducts()
                            .stream().map(product -> this.createSearchResultCsuInfoMq(accountId, merchantId, terminalType, terminalVersion, ip, product))
                            .filter(Objects::nonNull)
                            .forEach(mq -> mqs.add(mq));
                }
            }
            Lists.partition(mqs, 20).forEach(partition -> appRocketMqFactory.sendSearchResultCsuInfoMq(partition));
        } catch (Exception e) {
            log.error("发送搜索结果商品信息mq失败，消息：{}，异常：", JSONArray.toJSONString(searchCardVOS), e);
        }
    }

    private JSONObject createSearchResultCsuInfoMq(Long accountId, Long merchantId, Integer terminalType, Integer terminalVersion, String ip, PcSearchProductVO pcSearchProductVO) {
        if (Objects.isNull(pcSearchProductVO)) {
            return null;
        }
        // accountId - merchantId - terminalType - terminalVersion - ip - csuId - masterStandardProductId - price - status - searchTime
        BigDecimal price = null;
        if (Objects.nonNull(pcSearchProductVO.getActSk())) {
            // 秒杀
            price = pcSearchProductVO.getActSk().getSkPrice();
        } else if (Objects.nonNull(pcSearchProductVO.getActPt())) {
            // 拼团
            price = pcSearchProductVO.getActPt().getAssemblePrice();
        } else if (Objects.nonNull(pcSearchProductVO.getActPgby())) {
            // 批购包邮
            price = pcSearchProductVO.getActPgby().getAssemblePrice();
        } else {
            if (Objects.nonNull(pcSearchProductVO.getFob())) {
                price = pcSearchProductVO.getFob();
            }
        }
        JSONObject mq = new JSONObject();
        mq.put("accountId", accountId);
        mq.put("merchantId", merchantId);
        mq.put("terminalType", terminalType);
        mq.put("terminalVersion", terminalVersion);
        mq.put("ip", ip);
        mq.put("csuId", pcSearchProductVO.getId());
        mq.put("masterStandardProductId", pcSearchProductVO.getMasterStandardProductId());
        mq.put("price", price);
        mq.put("status", pcSearchProductVO.getStatus());
        mq.put("searchTime", new Date());
        return mq;
    }

    @Override
    public List<PcSearchCardVO> convertAndFillSearchCards(EcpSearchSceneEnum searchScene, Long merchantId, String branchCode, Boolean priceDisplayFlag,
                                                          List<EcpSearchBaseCardDTO> cardDTOS) {
        return getPcSearchCardVOS(searchScene, merchantId, branchCode, priceDisplayFlag, cardDTOS, 1, null);
    }

    @Override
    public List<PcSearchCardVO> convertAndFillSearchCards(EcpSearchSceneEnum searchScene, Long merchantId, String branchCode, Boolean priceDisplayFlag,
                                                          List<EcpSearchBaseCardDTO> cardDTOS, Boolean isShowNextDayDeliveryTag) {
        return getPcSearchCardVOS(searchScene, merchantId, branchCode, priceDisplayFlag, cardDTOS, 2, isShowNextDayDeliveryTag);
    }

    private List<PcSearchCardVO> getPcSearchCardVOS(EcpSearchSceneEnum searchScene, Long merchantId, String branchCode, Boolean priceDisplayFlag,
                                                    List<EcpSearchBaseCardDTO> cardDTOS, Integer type, Boolean isShowNextDayDeliveryTag) {
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，cardDTOS：{}", merchantId, JSONArray.toJSONString(cardDTOS));
        }
        if (Objects.isNull(merchantId) || merchantId <= 0 || CollectionUtils.isEmpty(cardDTOS)) {
            return Lists.newArrayList();
        }
        Boolean isQueryShopDataTags = null;
        Boolean isShowSimilarGoodsJump = null;
        if (Objects.equals(searchScene, EcpSearchSceneEnum.MAIN)) {
            isQueryShopDataTags = true;
            // 强制不显示店铺同款
            isShowSimilarGoodsJump = false;
        }
        // 获取商品id列表
        List<Long> csuIds = this.getSearchCardCsuIds(cardDTOS);
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，csuIds：{}", merchantId, JSONArray.toJSONString(csuIds));
        }
        if (CollectionUtils.isEmpty(csuIds)) {
            return Lists.newArrayList();
        }
        // 查询商品信息
        List<ProductDTO> productDTOS;
        if (Objects.equals(2, type)) {
            productDTOS = productForSearchRemoteService.fillProductInfoNew(csuIds, merchantId, branchCode, isShowNextDayDeliveryTag);
        } else {
            productDTOS = productForSearchRemoteService.fillProductInfo(csuIds, merchantId, branchCode);
        }
        if (log.isDebugEnabled()) {
            log.debug("搜索（V2）转换和填充搜索结果卡片信息，csuIds：{}，merchantId：{}，branchCode：{}，productDTOS：{}", JSONArray.toJSONString(csuIds), merchantId, branchCode, JSONArray.toJSONString(productDTOS));
        }
        if (CollectionUtils.isEmpty(productDTOS)) {
            return Lists.newArrayList();
        }
        List<List<TagDTO>> tagLists = productDTOS.stream().map(ProductDTO::getTagList).collect(Collectors.toList());
        handleXianShiJiaBuTag(tagLists);

        List<PcSearchProductVO> productVOS = PcSearchProductVOHelper.creates(productDTOS);
        // 隐藏规格：将规格设置为空串
        productVOS.forEach(product -> product.setSpec(""));
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，productVOS：{}", merchantId, JSONArray.toJSONString(productVOS));
        }
        // 填充店铺信息
        this.fillProductShopInfo(productVOS);
        // 填充活动信息
        List<Long> marketingActivityCsuIds = productVOS.stream()
                .filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.PROMOTION_SKU_TYPE.getId())
                        || Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.WHOLESALE_TYPE.getId()))
                .map(PcSearchBaseProductVO::getId).collect(Collectors.toList());

        Set<Long> gaoMaoSkuIdSet = productVOS.stream().filter(productDto -> {
            if (null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                    || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))) {
                return true;
            }
            return false;
        }).map(PcSearchProductVO::getId).collect(Collectors.toSet());
        Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoDtoMap = marketingService.getActCardInfoBySkuIdListForSearch(marketingActivityCsuIds, merchantId,
                Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
        this.fillProductMarketingActivityInfo(productVOS, csuIdToGroupBuyingInfoDtoMap);
        List<Long> skIdList = productVOS.stream()
                .filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.SECKILL_SKU_TYPE.getId()))
                .map(PcSearchBaseProductVO::getId).collect(Collectors.toList());
        Map<Long, MarketingSeckillActivityInfoDTO> csuIdToSeckillActivityInfoDTOMap = marketingService.getShowingSeckillActivityInfoByCsuIdsForSearch(merchantId, skIdList);
        productVOS.stream().forEach(item -> item.setActSk(csuIdToSeckillActivityInfoDTOMap.get(item.getId())));
        // 填充数据标签信息
        SearchCsuDataTagQueryParam queryParam = SearchCsuDataTagQueryParam.builder()
                .merchantId(merchantId).csuList(SearchCsuDataTagCsuDTOHelper.createsProductDTO(productVOS))
                .isQueryShopDataTags(isQueryShopDataTags).build();
        Map<Long, Map<String, Object>> dataTagMaps = dataService.batchGetCsuDataTags(queryParam);
        productVOS.stream().forEach(product -> PcSearchProductVOHelper.setTagInfo(product, dataTagMaps.get(product.getId())));
        // 填充库存信息
        productServiceRpc.fillProductDTOActTotalSurplusQtyToAvailableQty(productVOS);
        // 不显示价格时隐藏价格
        if (BooleanUtils.isFalse(priceDisplayFlag)) {
            this.resetProductPrice(productVOS);
        }
        // 转数据结构
        Map<Long, PcSearchProductVO> csuIdToInfoMap = productVOS.stream().filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId()))
                .collect(Collectors.toMap(PcSearchProductVO::getId, Function.identity(), (f, s) -> f));
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，csuIdToInfoMap：{}", merchantId, JSONObject.toJSONString(csuIdToInfoMap));
        }
        List<PcSearchCardVO> searchCardVOS = PcSearchCardVOHelper.creates(cardDTOS, csuIdToInfoMap, isShowSimilarGoodsJump, searchProperties);
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，searchCardVOS：{}", merchantId, JSONArray.toJSONString(searchCardVOS));
        }
        //处理组合购营销标语
        if (BooleanUtils.isTrue(searchProperties.getIsOpenMarketingSlogan())) {
            Map<Long, String> productCommonNameMap = productDTOS.stream().filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId()) && StringUtils.isNotEmpty(item.getCommonName()))
                    .collect(Collectors.toMap(ProductDTO::getId, ProductDTO::getCommonName, (k1, k2) -> k2));
            this.handleMarketingSlogan(searchCardVOS, productCommonNameMap);
        }
        // 执行搜索卡片展示逻辑
        this.doSearchCardShowLogic(searchCardVOS);
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，最终的searchCardVOS：{}", merchantId, JSONArray.toJSONString(searchCardVOS));
        }
        return searchCardVOS;
    }

    private void handleXianShiJiaBuTag(List<List<TagDTO>> tagLists) {
        for (List<TagDTO> tagDTOS : tagLists) {
            if (CollectionUtils.isEmpty(tagDTOS)) {
                continue;
            }
            tagDTOS.removeIf(this::haveXianShiJiaBuTagName);
        }
    }

    private boolean haveXianShiJiaBuTagName(TagDTO tagDTO) {
        return tagDTO.getText().equals("限时加补");
    }

    @Override
    public void saveQueryWordHistory(EcpSearchSceneEnum searchSceneEnum, Long merchantId, String queryWord, List<String> shopCodes) {
        if (StringUtils.isEmpty(queryWord)) {
            return;
        }
        if (Objects.isNull(searchSceneEnum)) {
            log.error("保存query词历史失败，searchSceneEnum参数非法，merchantId：{}，queryWord：{}，searchSceneEnum：{}，shopCodes：{}", merchantId, queryWord, searchSceneEnum, JSONArray.toJSONString(shopCodes));
            return;
        }
        if (Objects.isNull(merchantId) || merchantId <= 0L) {
            log.error("保存query词历史失败，merchantId参数非法，merchantId：{}，queryWord：{}，searchSceneEnum：{}，shopCodes：{}", merchantId, queryWord, searchSceneEnum, JSONArray.toJSONString(shopCodes));
            return;
        }
        try {
            if (Objects.equals(searchSceneEnum, EcpSearchSceneEnum.MAIN)) {
                // 主搜
                CompletableFuture.runAsync(() -> hotWordsApi.saveHistoryWord(HistoryType.SEARCH.getValue(), merchantId, queryWord, null));
            } else if (Objects.equals(searchSceneEnum, EcpSearchSceneEnum.SHOP)) {
                // 店铺
                if (CollectionUtils.isEmpty(shopCodes) || shopCodes.size() > 1) {
                    log.error("保存query词历史失败，当前为店铺搜索，shopCodes参数非法，merchantId：{}，queryWord：{}，searchSceneEnum：{}，shopCodes：{}", merchantId, queryWord, searchSceneEnum, JSONArray.toJSONString(shopCodes));
                    return;
                }
                CompletableFuture.runAsync(() -> hotWordsApi.saveHistoryWord(HistoryType.SHOP_SEARCH.getValue(), merchantId, queryWord, shopCodes.get(0)));
            }
        } catch (Exception e) {
            log.error("保存query词历史失败，merchantId：{}，queryWord：{}，searchSceneEnum：{}，shopCodes：{}，异常信息：", merchantId, queryWord, searchSceneEnum, JSONArray.toJSONString(shopCodes), e);
        }
    }

    @Override
    public List<String> listHotQueryWords(Integer type, Long merchantId, String branchCode, Integer terminalType) {
        if (Objects.isNull(type)) {
            log.error("获取热搜词列表失败，type参数非法，type：{}，merchantId：{}，branchCode：{}，terminalType：{}", type, merchantId, branchCode, terminalType);
            return Lists.newArrayList();
        }
        if (Objects.isNull(merchantId) || merchantId <= 0L) {
            log.error("获取热搜词列表失败，merchantId参数非法，type：{}，merchantId：{}，branchCode：{}，terminalType：{}", type, merchantId, branchCode, terminalType);
            return Lists.newArrayList();
        }
        if (StringUtils.isEmpty(branchCode)) {
            log.error("获取热搜词列表失败，branchCode参数非法，type：{}，merchantId：{}，branchCode：{}，terminalType：{}", type, merchantId, branchCode, terminalType);
            return Lists.newArrayList();
        }
        try {
            if (Objects.equals(type, SearchType.RECOMMENDATION.getType())) {
                List<HotWordsDTO> hotWordsDTOS = hotWordsApi.finhotSearchlist(merchantId, terminalType, branchCode);
                if (CollectionUtils.isNotEmpty(hotWordsDTOS)) {
                    return hotWordsDTOS.stream().filter(item -> !Objects.equals(item.getType(), 3))
                            .limit(Constants.MAX_HOT_WORD_SIZE).map(HotWordsDTO::getKeyword).filter(Objects::nonNull).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            log.error("获取热搜词列表失败，type：{}，merchantId：{}，branchCode：{}，terminalType：{}，异常信息：", type, merchantId, branchCode, terminalType, e);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<PcSearchProductVO> resetProductPrice(List<PcSearchProductVO> products) {
        if (CollectionUtils.isEmpty(products)) {
            return products;
        }
        products.stream().filter(item -> item != null).forEach(item -> {
            //商品标签
            ProductActivityTagDTO activityTag = new ProductActivityTagDTO();
            activityTag.setTagUrl("");
            activityTag.setTagNoteBackGroupUrl("");
            activityTag.setSkuTagNotes(new ArrayList<>());
            item.setFob(BigDecimal.ZERO);
            item.setUnitPrice(null);
            Map<String, Object> tags = item.getTags();
            if (MapUtils.isNotEmpty(tags)) {
                tags.remove(TagTypeEnum.UNIT_PRICE_TAG.getName());
            }
            item.setActivityTag(activityTag);
            //毛利
            item.setGrossMargin("");
            //建议零售价
            item.setSuggestPrice(BigDecimal.ZERO);
            //控销零售价
            item.setUniformPrice(BigDecimal.ZERO);
            // 对比价
            item.setRetailPrice(BigDecimal.ZERO);
            // 拼团 批购包邮 秒杀
            if (Objects.nonNull(item.getActPt())) {
                item.getActPt().setAssemblePrice(BigDecimal.ZERO);
            }
            if (Objects.nonNull(item.getActPgby())) {
                item.getActPgby().setAssemblePrice(BigDecimal.ZERO);
            }
            if (Objects.nonNull(item.getActSk())) {
                item.getActSk().setSkPrice(BigDecimal.ZERO);
            }
        });
        return products;
    }

    @Override
    public List<PcSearchProductVO> fillProductShopInfo(List<PcSearchProductVO> products) {
        if (CollectionUtils.isEmpty(products)) {
            return products;
        }
        // 填充商品的店铺信息
        Set<String> shopCodes = products.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShopCode()))
                .map(PcSearchProductVO::getShopCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(shopCodes)) {
            return products;
        }
        List<ShopInfoDTO> shopInfoDTOS = shopQueryRemoteService.queryShopInfosByShopCodes(Lists.newArrayList(shopCodes));
        if (CollectionUtils.isEmpty(shopInfoDTOS)) {
            return products;
        }
        Map<String, ShopInfoDTO> shopCodeToShopInfoDTOMap = shopInfoDTOS.stream().filter(item -> item != null && item.getShopCode() != null)
                .collect(Collectors.toMap(ShopInfoDTO::getShopCode, Function.identity(), (a, b) -> a));
        if (MapUtils.isEmpty(shopCodeToShopInfoDTOMap)) {
            return products;
        }
        products.stream().filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShopCode()))
                .forEach(item -> {
                    ShopInfoDTO shopInfoDTO = shopCodeToShopInfoDTOMap.get(item.getShopCode());
                    if (Objects.nonNull(shopInfoDTO)) {
                        item.setShopName(shopInfoDTO.getShowName());
                        item.setShopUrl(shopInfoDTO.getPcLink());
                    }
                });
        return products;
    }

    @Override
    public List<PcSearchProductVO> fillProductMarketingActivityInfo(List<PcSearchProductVO> products, Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap) {
        if (CollectionUtils.isEmpty(products) || MapUtils.isEmpty(csuIdToGroupBuyingInfoMap)) {
            return products;
        }
        Long id;
        GroupBuyingInfoDto groupBuyingInfoDto;
        for (PcSearchProductVO product : products) {
            id = product.getId();
            groupBuyingInfoDto = csuIdToGroupBuyingInfoMap.get(id);
            if (Objects.nonNull(groupBuyingInfoDto)) {
                if (Objects.equals(groupBuyingInfoDto.getActivityType(), MarketingEnum.PING_TUAN.getCode())) {
                    PcSearchProductVOHelper.setActPt(product, groupBuyingInfoDto);
                } else if (Objects.equals(groupBuyingInfoDto.getActivityType(), MarketingEnum.PI_GOU_BAO_YOU.getCode())) {
                    PcSearchProductVOHelper.setActPgby(product, groupBuyingInfoDto);
                }
            }
            if (Objects.nonNull(product.getActPt())) {
                product.setPrice(product.getActPt().getAssemblePrice());
            }
            if (Objects.nonNull(product.getActPgby())) {
                product.setPrice(product.getActPgby().getAssemblePrice());
            }
        }
        return products;
    }

    @Override
    public List<PcSearchProductVO> getProductInfoForSxp(List<Long> csuIds, Long merchantId) {

        Boolean isQueryShopDataTags = null;
        String branchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchantId);

        // 获取商品id列表
        if (CollectionUtils.isEmpty(csuIds)) {
            return Lists.newArrayList();
        }
        // 查询商品信息
        List<ProductDTO> productDTOS = productForSearchRemoteService.fillProductInfo(csuIds, merchantId, branchCode);
        if (CollectionUtils.isEmpty(productDTOS)) {
            return Lists.newArrayList();
        }
        List<PcSearchProductVO> productVOS = PcSearchProductVOHelper.creates(productDTOS);

        // 填充店铺信息
        this.fillProductShopInfo(productVOS);
        // 填充活动信息
        List<Long> marketingActivityCsuIds = productVOS.stream()
                .filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.PROMOTION_SKU_TYPE.getId())
                        || Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.WHOLESALE_TYPE.getId()))
                .map(PcSearchProductVO::getId).collect(Collectors.toList());
        Set<Long> gaoMaoSkuIdSet = productVOS.stream().filter(productDto -> {
            if (null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                    || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))) {
                return true;
            }
            return false;
        }).map(PcSearchProductVO::getId).collect(Collectors.toSet());
        Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoDtoMap = marketingService.getActCardInfoBySkuIdListForSearch(marketingActivityCsuIds, merchantId,
                Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
        this.fillProductMarketingActivityInfo(productVOS, csuIdToGroupBuyingInfoDtoMap);
        List<Long> skIdList = productVOS.stream()
                .filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.SECKILL_SKU_TYPE.getId()))
                .map(PcSearchProductVO::getId).collect(Collectors.toList());
        Map<Long, MarketingSeckillActivityInfoDTO> csuIdToSeckillActivityInfoDTOMap = marketingService.getShowingSeckillActivityInfoByCsuIdsForSearch(merchantId, skIdList);
        productVOS.stream().forEach(item -> item.setActSk(csuIdToSeckillActivityInfoDTOMap.get(item.getId())));
        // 填充数据标签信息
        SearchCsuDataTagQueryParam queryParam = SearchCsuDataTagQueryParam.builder()
                .merchantId(merchantId).csuList(SearchCsuDataTagCsuDTOHelper.creates2(productVOS))
                .isQueryShopDataTags(isQueryShopDataTags).build();
        Map<Long, Map<String, Object>> dataTagMaps = dataService.batchGetCsuDataTags(queryParam);
        productVOS.stream().forEach(product -> PcSearchProductVOHelper.setTagInfo(product, dataTagMaps.get(product.getId())));
        // 填充库存信息
        productServiceRpc.fillProductDTOActTotalSurplusQtyToAvailableQty(productVOS);

        fillSxpExtra(productVOS);
        return productVOS.stream().filter(x -> x.getControlType() == null || x.getControlType() != 2).collect(Collectors.toList());
    }

    private void fillSxpExtra(List<PcSearchProductVO> productVOS) {
        if (productVOS == null || productVOS.isEmpty()) {
            return;
        }

        List<Long> skuIds = productVOS.stream().map(s -> s.getId()).collect(Collectors.toList());
        Map<Long, String> skuId2promoTag = orderBackendService.queryPromoTag(skuIds);

        // 填充promoTag
        for (PcSearchProductVO infoVo : productVOS) {
            infoVo.setPromoTag(skuId2promoTag.get(infoVo.getId()));

            // ...填充其他字段
        }

    }

    private List<SearchCsuDataTagCsuDTO> creates(List<PcSearchProductVO> products) {
        if (CollectionUtils.isEmpty(products)) {
            return Lists.newArrayList();
        }
        return products.stream().map(SearchCsuDataTagCsuDTOHelper::create).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<Long> getSearchCardCsuIds(List<EcpSearchBaseCardDTO> cardDTOS) {
        List<Long> csuIds = Lists.newArrayListWithExpectedSize(16);
        cardDTOS.stream().forEach(searchBaseCardDTO -> {
            if (searchBaseCardDTO instanceof EcpSearchProductCardDTO) {
                EcpSearchProductCardDTO ecpSearchProductCardDTO = (EcpSearchProductCardDTO) searchBaseCardDTO;
                if (Objects.nonNull(ecpSearchProductCardDTO.getProduct())) {
                    csuIds.add(ecpSearchProductCardDTO.getProduct().getId());
                }
            } else if (searchBaseCardDTO instanceof EcpSearchOperationCardDTO) {
                EcpSearchOperationCardDTO ecpSearchOperationCardDTO = (EcpSearchOperationCardDTO) searchBaseCardDTO;
                if (CollectionUtils.isNotEmpty(ecpSearchOperationCardDTO.getProducts())) {
                    ecpSearchOperationCardDTO.getProducts().stream().forEach(product -> csuIds.add(product.getId()));
                }
            } else if (searchBaseCardDTO instanceof EcpSearchGroupPurchaseCardDTO) {
                EcpSearchGroupPurchaseCardDTO ecpSearchGroupPurchaseCardDTO = (EcpSearchGroupPurchaseCardDTO) searchBaseCardDTO;
                if (Objects.nonNull(ecpSearchGroupPurchaseCardDTO.getMainProduct())) {
                    csuIds.add(ecpSearchGroupPurchaseCardDTO.getMainProduct().getId());
                }
                if (CollectionUtils.isNotEmpty(ecpSearchGroupPurchaseCardDTO.getSubProducts())) {
                    ecpSearchGroupPurchaseCardDTO.getSubProducts().stream().forEach(product -> csuIds.add(product.getId()));
                }
            }
        });
        return csuIds;
    }

    /**
     * 处理组合购营销标语
     *
     * @param searchCardVOS
     * @return
     */
    private void handleMarketingSlogan(List<PcSearchCardVO> searchCardVOS, Map<Long, String> productCommonNameMap) {
        if (CollectionUtils.isEmpty(searchCardVOS)) {
            return;
        }

        Set<Long> productSpuIds = Sets.newHashSetWithExpectedSize(16);
        Set<String> productCommonNames = Sets.newHashSetWithExpectedSize(16);
        List<PcSearchProductVO> productLists = Lists.newArrayListWithExpectedSize(16);

        for (PcSearchCardVO searchCardVO : searchCardVOS) {
            if (Objects.equals(searchCardVO.getCardType(), EcpSearchCardTypeEnum.GROUP_PURCHASE_CARD.getType())) {
                //主品
                if (Objects.nonNull(searchCardVO.getGroupPurchaseInfo()) && Objects.nonNull(searchCardVO.getGroupPurchaseInfo().getMainProduct())) {
                    PcSearchProductVO mainProduct = searchCardVO.getGroupPurchaseInfo().getMainProduct();
                    productLists.add(mainProduct);
                    productSpuIds.add(mainProduct.getSpuId());
                    productCommonNames.add(productCommonNameMap.getOrDefault(mainProduct.getId(), ""));
                }
                //副品
                List<PcSearchProductVO> subProducts = Objects.nonNull(searchCardVO.getGroupPurchaseInfo()) ? searchCardVO.getGroupPurchaseInfo().getSubProducts() : Lists.newArrayList();
                if (CollectionUtils.isEmpty(subProducts)) {
                    continue;
                }
                productLists.addAll(subProducts);
                List<Long> spuIds = subProducts.stream().map(PcSearchProductVO::getSpuId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(spuIds)) {
                    productSpuIds.addAll(spuIds);
                }
                List<String> commonNames = subProducts.stream().map(item -> productCommonNameMap.get(item.getId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(commonNames)) {
                    productCommonNames.addAll(commonNames);
                }
            }
        }

        // SpuId客户购买数
        List<RecSpuPurchasedMerchantNum30dDTO> spuPurchasedMerchantNums = ecSearchServiceRpc.selectMerchantNumBySpuIds(Lists.newArrayList(productSpuIds));
        Map<Long, Integer> spuPurchasedMerchantNumMap = spuPurchasedMerchantNums.stream().collect(Collectors.toMap(RecSpuPurchasedMerchantNum30dDTO::getSpuId, RecSpuPurchasedMerchantNum30dDTO::getMerchantNum, (k1, k2) -> k1));

        // 通用名对应营销标语
        List<CommonNameMarketingSloganRelationDTO> marketingSloganByCommonNames = ecSearchServiceRpc.selectMarketingSloganByCommonNames(Lists.newArrayList(productCommonNames));
        Map<String, CommonNameMarketingSloganRelationDTO> marketingSloganByCommonNameMap = marketingSloganByCommonNames.stream().collect(Collectors.toMap(CommonNameMarketingSloganRelationDTO::getCommonName, Function.identity(), (k1, k2) -> k1));

        for (PcSearchProductVO searchProduct : productLists) {
            String commonName = productCommonNameMap.get(searchProduct.getId());
            if (StringUtils.isNotEmpty(commonName) && marketingSloganByCommonNameMap.containsKey(commonName)) {
                CommonNameMarketingSloganRelationDTO commonNameMarketingSloganRelation = marketingSloganByCommonNameMap.get(commonName);
                searchProduct.setMarketingSlogan(commonNameMarketingSloganRelation.getMarketingSlogan());
            } else {
                String marketingSlogan = buildMarketingSloganBySpuId(searchProduct.getSpuId(), spuPurchasedMerchantNumMap);
                searchProduct.setMarketingSlogan(marketingSlogan);
            }
            searchProduct.setSpuId(null);
        }
    }

    private String buildMarketingSloganBySpuId(Long spuId, Map<Long, Integer> spuPurchasedMerchantNumMap) {
        if (!spuPurchasedMerchantNumMap.containsKey(spuId)) {
            return "";
        }
        //spuId客户购买数
        Integer purchasedMerchantNum = spuPurchasedMerchantNumMap.get(spuId);
        if (Objects.isNull(purchasedMerchantNum)) {
            return "";
        }
        if (purchasedMerchantNum < 50) {
            return "";
        }
        String purchasedMerchantNumStr;
        if (purchasedMerchantNum < 100) {
            return purchasedMerchantNum + "人一起买";
        } else if (purchasedMerchantNum < 100000) {
            purchasedMerchantNumStr = String.valueOf(NumberUtil.formatNumber(purchasedMerchantNum));
        } else {
            purchasedMerchantNumStr = "10万";
        }
        return purchasedMerchantNumStr + "+人一起买";
    }


    /**
     * 执行搜索卡片展示逻辑
     *
     * @param searchCardVOS
     * @return
     */
    private List<PcSearchCardVO> doSearchCardShowLogic(List<PcSearchCardVO> searchCardVOS) {
        if (CollectionUtils.isEmpty(searchCardVOS)) {
            return searchCardVOS;
        }
        for (int i = 0; i < searchCardVOS.size(); i++) {
            PcSearchCardVO searchCardVO = searchCardVOS.get(i);
            if (Objects.equals(searchCardVO.getCardType(), EcpSearchCardTypeEnum.OPERATION_CARD.getType())) {
                PcSearchOperationVO operationInfo = searchCardVO.getOperationInfo();
                // 若活动主标题没有，则将活动相关信息清空
                if (StringUtils.isEmpty(operationInfo.getTitle())) {
                    operationInfo.setSubTile(null);
                    operationInfo.setJumpUrl(null);
                }
            } else if (Objects.equals(searchCardVO.getCardType(), EcpSearchCardTypeEnum.PRODUCT_CARD.getType())) {
                PcSearchProductVO productInfo = searchCardVO.getProductInfo();
                if (isEnterProductDetailIsShowRecPurchase(productInfo) && BooleanUtils.isTrue(searchProperties.getIsGroupBuyingOrWholesaleAllowSearchRecPurchase())) {
                    Integer showRecPurchase = EcpSearchRecPurchaseTypeEnum.GROUP_PURCHASE.getType();
                    productInfo.setEnterProductDetailIsShowRecPurchase(EcpSearchYesNoEnum.YES.getValue());
                    productInfo.setEnterProductDetailShowRecPurchaseType(showRecPurchase);
                }
            } else if (Objects.equals(searchCardVO.getCardType(), EcpSearchCardTypeEnum.GROUP_PURCHASE_CARD.getType())) {
                // 搜索结果中，最多有一个组合购卡片或者加价购卡片，主品同组合购同页
                PcSearchGroupPurchaseVO groupPurchaseInfo = searchCardVO.getGroupPurchaseInfo();
                PcSearchProductVO mainProduct = groupPurchaseInfo.getMainProduct();
                Long mainCsuId = mainProduct.getId();
                Integer showRecPurchase = EcpSearchRecPurchaseTypeEnum.GROUP_PURCHASE.getType();
                // 找出主品 调整进入商品详情数据
                Optional<PcSearchCardVO> optionalPcSearchCardVO = searchCardVOS.stream().filter(c -> Objects.equals(c.getId(), mainCsuId)).findFirst();
                optionalPcSearchCardVO.ifPresent(pcSearchCardVO -> this.setSearchRecPurchaseMainProductEnterProductDetailRelatedInfo(pcSearchCardVO, mainCsuId, showRecPurchase));
                // 设置本卡片商品的进入商详相关数据
                mainProduct.setEnterProductDetailIsShowRecPurchase(EcpSearchYesNoEnum.YES.getValue());
                mainProduct.setEnterProductDetailShowRecPurchaseType(showRecPurchase);
            }
        }
        return searchCardVOS;
    }

    /**
     * 设置搜索推荐购买的主品进入商详相关信息
     *
     * @param searchCardVO
     * @param mainProductId
     * @param showRecPurchase
     */
    private void setSearchRecPurchaseMainProductEnterProductDetailRelatedInfo(PcSearchCardVO searchCardVO, Long mainProductId, Integer showRecPurchase) {
        if (Objects.equals(searchCardVO.getCardType(), EcpSearchCardTypeEnum.PRODUCT_CARD.getType())) {
            PcSearchProductVO productInfo = searchCardVO.getProductInfo();
            if (Objects.equals(productInfo.getId(), mainProductId)) {
                productInfo.setEnterProductDetailIsShowRecPurchase(EcpSearchYesNoEnum.YES.getValue());
                productInfo.setEnterProductDetailShowRecPurchaseType(showRecPurchase);
            }
        } else if (Objects.equals(searchCardVO.getCardType(), EcpSearchCardTypeEnum.OPERATION_CARD.getType())) {
            PcSearchOperationVO operationInfo = searchCardVO.getOperationInfo();
            List<PcSearchProductVO> products = operationInfo.getProducts();
            products.stream().filter(productInfo -> Objects.equals(productInfo.getId(), mainProductId))
                    .forEach(productInfo -> {
                        productInfo.setEnterProductDetailIsShowRecPurchase(EcpSearchYesNoEnum.YES.getValue());
                        productInfo.setEnterProductDetailShowRecPurchaseType(showRecPurchase);
                    });
        }
    }

    private boolean isEnterProductDetailIsShowRecPurchase(PcSearchProductVO product) {
        if (product == null || product.getControlType() != null || !Objects.equals(product.getStatus(), ProductEnumDTO.SkuStatusEnum.ONSALE.getId())) {
            return false;
        }

        return Objects.nonNull(product.getActPt()) || Objects.nonNull(product.getActPgby());
    }
}
