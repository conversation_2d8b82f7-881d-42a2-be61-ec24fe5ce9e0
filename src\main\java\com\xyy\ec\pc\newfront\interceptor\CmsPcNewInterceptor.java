package com.xyy.ec.pc.newfront.interceptor;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.interceptor.AuthInterceptor;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.authentication.utils.ServletUtils;
import com.xyy.ec.pc.interceptor.helper.DifferentPlacesLoginHelper;
import com.xyy.ec.pc.interceptor.helper.SpiderHelper;
import com.xyy.ec.pc.newfront.service.IndexNewService;
import com.xyy.ec.pc.rest.AjaxErrorEnum;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.IdentityValidator;
import com.xyy.ec.pc.service.layout.LayoutBaseService;
import com.xyy.ec.pc.util.CookieUtils;
import com.xyy.ec.pc.util.ipip.IPUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

@Slf4j
@RequiredArgsConstructor
@Component
public class CmsPcNewInterceptor implements HandlerInterceptor {

    private final LayoutBaseService layoutBaseService;
    private final AuthInterceptor authInterceptor;
    private final IdentityValidator identityValidator;
    private final DifferentPlacesLoginHelper differentPlacesLoginHelper;
    private final TokenService tokenService;
    private final SpiderHelper spiderHelper;
    private final IndexNewService indexNewService;

    private final String XYY_TOKEN = "xyy_token";

    @Value("${new.index.open:false}")
    private boolean openNewIndex;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (ArrayUtil.isNotEmpty(request.getCookies()) && Arrays.stream(request.getCookies()).anyMatch(cookie -> XYY_TOKEN.equals(cookie.getName()))) {
            // 如果有xyy_token走正常拦截器
            return this.valid(request, response);
        }
        if (layoutBaseService.judgeRequestIsFromNewCmsAdmin(request)) {
            // 如果是cms admin就直接通过
            log.info("cms admin request:{}", request.getRequestURI());
            return true;
        }
        ServletUtil.write(response, JSONObject.toJSONString(AjaxResult.errResult(AjaxErrorEnum.UN_LOGIN)), ContentType.APPLICATION_JSON.toString());
        ServletUtils.clearCookie();
        return false;
    }

    private boolean valid(HttpServletRequest request, HttpServletResponse response) throws Exception {

        JwtPrincipal jwtPrincipal = (JwtPrincipal) identityValidator.currentPrincipal();
        // 用户未登录
        if (jwtPrincipal == null) {
            ServletUtil.write(response, JSONObject.toJSONString(AjaxResult.errResult(AjaxErrorEnum.UN_LOGIN)), ContentType.APPLICATION_JSON.toString());
            // 未登录也清一下, 避免是因为token有问题导致误判
            identityValidator.logout();
            return false;
        }

        if ("/new-front/index/header-data".equals(request.getRequestURI())) {
            if (jwtPrincipal.getMerchantId() == null || jwtPrincipal.getMerchantId() <= 0) {
                ServletUtil.write(response, JSONObject.toJSONString(AjaxResult.errResult(AjaxErrorEnum.NO_STORE_SELECTED)), ContentType.APPLICATION_JSON.toString());
                return false;
            }
            // 后临时加的功能, 新首页只能给对应用户看
            if (openNewIndex) {
                String referer = request.getHeader("referer");
                if (StringUtils.isNotBlank(referer) && (referer.startsWith("https://www.ybm100.com/new") || referer.startsWith("https://www-new.stage.ybm100.com/new") || referer.startsWith("https://new-www.test.ybm100.com/new"))) {
                    if (!indexNewService.isNewIndex(jwtPrincipal.getMerchantId())) {
                        ServletUtil.write(response, JSONObject.toJSONString(AjaxResult.errResult(AjaxErrorEnum.OLD_INDEX_USER)), ContentType.APPLICATION_JSON.toString());
                        return false;
                    }
                }
            }
        }

        // 异地登录未验证拦截
        if (differentPlacesLoginHelper.isDifferentPlacesLogin(jwtPrincipal)) {
            ServletUtil.write(response, JSONObject.toJSONString(AjaxResult.errResult(AjaxErrorEnum.REMOTE_LOGIN)), ContentType.APPLICATION_JSON.toString());
            identityValidator.logout();
            return false;
        }
        // 爬虫拦截
        if (!this.spiderCheck()) {
            ServletUtil.write(response, JSONObject.toJSONString(AjaxResult.errResult(AjaxErrorEnum.SPIDER_ACCOUNT)), ContentType.APPLICATION_JSON.toString());
            identityValidator.logout();
            return false;
        }
        // 验证刷新token
        tokenService.verifyToken(jwtPrincipal);
        // 判断qt session是否需要重新生成
        authInterceptor.refreshQtSession();
        return true;
    }


    private boolean spiderCheck() {
        // 是否拦截
        if (!spiderHelper.getNewSpiderInterceptionOpen()) {
            return true;
        }
        // 白名单IP
        if (spiderHelper.isSpiderWhiteIp()) {
            return true;
        }
        // 白名单账号
        if (spiderHelper.isSpiderWhiteAccount()) {
            return true;
        }
        // 黑名单IP
        if (spiderHelper.isSpiderBlackIp()) {
            log.error("爬虫黑名单IP[{}], ACCOUNT ID[{}], 禁止访问", IPUtils.getIp(), CookieUtils.getAccountId());
            return false;
        }
        // 黑名单账号
        if (spiderHelper.isSpiderBlackAccount()) {
            log.error("爬虫黑名单账号[{}], IP[{}], 禁止访问", CookieUtils.getAccountId(), IPUtils.getIp());
            return false;
        }
        // 爬虫会话 && IP近期没有做过短信验证
        if (spiderHelper.isSpiderToken() && !spiderHelper.isValidIp()) {
            log.error("爬虫会话[{}], ACCOUNT ID[{}], IP[{}], 禁止访问", CookieUtils.getToken(), CookieUtils.getAccountId(), IPUtils.getIp());
            return false;
        }
        // 爬虫账号 && IP近期没有做过短信验证
        if (spiderHelper.isSpiderAccount() && !spiderHelper.isValidIp()) {
            log.error("爬虫账号[{}], IP[{}], 禁止访问", CookieUtils.getAccountId(), IPUtils.getIp());
            return false;
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
