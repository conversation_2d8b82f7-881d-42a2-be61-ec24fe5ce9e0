package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.service.MessageCenterService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.system.business.dto.MessageCenterBusinessDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 消息中心控制器
 */

@CustomizeCmsResponse
@RestController
@RequestMapping("/new-front/message-center")
@Slf4j
public class MessageCenterController extends BaseController {


    @Resource
    private MessageCenterService messageCenterService;


    /**
     * App获取未读消息数量
     *
     * @param messageCenter 消息中心
     * @return MessageVO   未读消息数量
     */
    @PostMapping("/find-un-read-count")
    public AjaxResult<Integer> findUnReadCount(@RequestBody MessageCenterBusinessDto messageCenter) {
        Integer count;
        try {
            count = messageCenterService.findUnReadCount(messageCenter);
        } catch (Exception e) {
            log.error("未读数量查询异常", e);
            return AjaxResult.errResult("未读数量查询异常");
        }
        return AjaxResult.successResult(count);
    }


}
