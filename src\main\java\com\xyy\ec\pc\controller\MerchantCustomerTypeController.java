package com.xyy.ec.pc.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.api.MerchantCustomerTypeBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantCustomerTypeBusinessDto;
import com.xyy.ec.pc.base.BaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping("/merchantCustomerType")
public class MerchantCustomerTypeController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantCustomerTypeController.class);

    @Reference(version = "1.0.0")
    private MerchantCustomerTypeBusinessApi merchantCustomerTypeBusinessApi;

    @RequestMapping("/getAll")
    public Object getAll(){
        try {
            List<MerchantCustomerTypeBusinessDto> allList = merchantCustomerTypeBusinessApi.getAll();
            return this.addResult("list", allList);
        } catch (Exception e) {
            LOGGER.error("用户类型列表查询异常",e);
            return this.addError("用户类型列表查询异常");
        }
    }
    @RequestMapping("/getCustomerType")
    @ResponseBody
    public Object getCustomerType(){
        try {
            List<MerchantCustomerTypeBusinessDto> allList = merchantCustomerTypeBusinessApi.getAll();
            return this.addResult("list", allList);
        } catch (Exception e) {
            LOGGER.error("用户类型列表查询异常",e);
            return this.addError("用户类型列表查询异常");
        }
    }


    @RequestMapping("/getById")
    public Object getById(Integer id){
        if(id == null || id.intValue()< 0){
            return this.addError("参数异常");
        }
        try {
            MerchantCustomerTypeBusinessDto businessDto = merchantCustomerTypeBusinessApi.selectByPrimaryKey(id);
            return this.addResult("customerType", businessDto);
        } catch (Exception e) {
            LOGGER.error("用户类型查询异常",e);
            return this.addError("用户类型查询异常");
        }
    }
}
