package com.xyy.ec.pc.vo;

import com.xyy.ec.merchant.server.dto.SubAccountDto;
import com.xyy.ec.merchant.server.enums.SubAccountEnabledStatusEnum;
import com.xyy.ec.pc.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SubAccountVo implements Serializable {
    private Long relId;

    private String subAccountName;

    private String mobile;

    private String creator;

    private String createDate;

    private Integer enabledStatus;
    private String enabledStatusText;

    public static SubAccountVo build(SubAccountDto subAccountDto){
        if (subAccountDto == null){
            return null;
        }
        SubAccountVo subAccountVo = new SubAccountVo();
        subAccountVo.setRelId(subAccountDto.getRelId());
        subAccountVo.setSubAccountName(subAccountDto.getSubAccountName());
        subAccountVo.setMobile(subAccountDto.getMobile());
        subAccountVo.setCreator(subAccountDto.getCreator());
        subAccountVo.setCreateDate(DateUtil.date2String(subAccountDto.getCreateTime(), DateUtil.PATTERN_STANDARD));
        subAccountVo.setEnabledStatus(subAccountDto.getEnabledStatus());
        subAccountVo.setEnabledStatusText(SubAccountEnabledStatusEnum.getText(subAccountDto.getEnabledStatus().byteValue()));
        return subAccountVo;
    }
}
