.main {
	background-color: #f8f8f8;
	padding-bottom: 1px;
	min-width: 1200px;
}
.myqual{
	margin-bottom: 40px;
}
.sui-breadcrumb a{
	color: #666;
}
.sui-breadcrumb > .active {
	color: #00dc82;
}
.sui-breadcrumb>li+li:before {
	content: " ";
	padding: 0;
}
.side-left {
	width: 198px;
	border: 1px solid #e8e8e8;
	background: #fff;
}
.main-right{
	width: 978px;
}
.myqual .main-right{
	border: 1px solid #e8e8e8;
}
.side-left .title{
	width: 173px;
	height: 40px;
	line-height: 40px;
	color: #fff;
	font-size: 18px;
	border-bottom: 1px solid #e8e8e8;
	background: #00dc82;
	padding-left: 25px;
}
.side-left .list .item{
	display: block;
	width: 173px;
	height: 40px;
	line-height: 40px;
	text-align: left;
	font-size: 16px;
	color: #666;
	padding-left: 25px;
	position: relative;
}
.side-left .uicon{
	display: inline-block;
	width: 20px;
	height: 100%;
	background: url(/static/images/user/uicon.png) no-repeat;
	background-size: 100%;
	float: left;
	margin-right: 10px;
}
.side-left .uicon1{
	background-position: 0 10px
}
.side-left .list .item .uicon2{
	background-position: 0 -25px
}
.side-left .list .item .uicon3{
	background-position: 0 -55px
}
.side-left .list .item .uicon4{
	background-position: 0 -87px
}
.side-left .list .item .uicon5{
	background-position: 0 -117px
}
.side-left .list .item .uicon6{
	background-position: 0 -150px
}
.side-left .list .item .uicon7{
	background-position: 0 -182px
}
.side-left .list .item .uicon8{
	background-position: 0 -219px
}
.side-left .list .item .uicon9{
	background-position: 0 -254px
}
.side-left .list .item .uicon10{
	background-position: 0 -290px
}
.side-left .list .item:hover,.side-left .list .item.active{
	background: #4B4B4B;
	color: #fff;
}

.page-title{
	height: 40px;
	line-height: 40px;
	border-left: 10px solid #00dc82;
	color: #333;
	font-size: 16px;
	padding-left: 20px;
	float: left;
	width: 80px;
}
.page-title-license{
	height: 40px;
	line-height: 40px;
	border-left: 10px solid #00dc82;
	color: #333;
	font-size: 16px;
	padding-left: 20px;
	float: left;
	width: 950px;
}
.page-tips{
	width: 900px;
	margin: 0 auto;
	border-bottom: 1px solid #e8e8e8;
	padding-bottom: 10px;
}
.page-tips h4{
	font-size: 18px;
	color: #f39800;
	font-weight: bold;
	padding: 5px 0;
}
.page-tips p{
	padding: 5px 0;
	color: #f39800;
}
/*我的资质*/
.qual-list{
	width: 978px;

}
.qual-item{
	width: 200px;
	height: 220px;
	float: left;
	margin: 15px;
	position: relative;
	overflow: hidden;
}
.qual-item .name{
	width: 198px;
	height: 30px;
	text-align: center;
	color: #333;
	font-size: 16px;
}
.qual-item .photo{
	width: 180px;
	height: 120px;
	padding: 9px;
	border: 1px solid #e8e8e8;
	overflow: hidden;
}
.qual-item .photo img{
	display: block;
	width: 180px;
	height: 120px;
	opacity: 0.6;
}
.state-icon{
	display: inline-block;
	width: 45px;
	height: 45px;
	position: absolute;
	top: 85px;
	left: 76px;
}
.state-icon1{
	background: url(/static/images/user/qualicon1.png) no-repeat 0 0;
}
.state-icon2{
	background: url(/static/images/user/qualicon2.png) no-repeat 0 0;
}
.state-icon3{
	background: url(/static/images/user/qualicon3.png) no-repeat 0 0;
}
.qual-item .bot{
	height: 28px;
	margin-top: 10px;
}
.qual-item .state{
	display: inline-block;
	width: 50%;
}

.no-qual .updata-btn{
	text-align: center;
	display: inline-block;
	position: absolute;
	top: 93px;
	left: 52px;
	text-decoration: underline;
	cursor: pointer;
}
.no-qual .uphoto{
	display: block;
	width: 198px;
	height: 220px;
	position: absolute;
	top: 0;
	left: 0;
	color: #fff;
	opacity: 0;
	filter:alpha(opacity=0);
}
.no-qual .action-btn{
	display: inline-block;
	width: 78px;
	height: 28px;
	text-align: center;
	line-height: 28px;
	border: 1px solid #e8e8e8;
	border-radius: 3px;
	color: #666;
}
.data-qual .uphoto{
	display: inline-block;
	width: 50%;
	height: 30px;
	color: #fff;
	opacity: 0;
	position: absolute;
	top: 180px;
	right: 0;
	z-index: 9;
	filter:alpha(opacity=0);
}
.data-qual .action-btn{
	display: inline-block;
	width: 78px;
	height: 28px;
	text-align: center;
	line-height: 28px;
	border: 1px solid #e8e8e8;
	border-radius: 3px;
	color: #666;
	position: absolute;
	top: 180px;
	right: 0;
}
.wait-qual .photo{
	border: 1px solid #f39800;
}
.wait-qual .state{
	color: #f39800;
	line-height: 30px;
}

.notpass-qual .photo{
	border: 1px solid #ff2400;
}
.notpass-qual .state{
	color: #ff2400;
	line-height: 30px;
}

.pass-qual .photo{
	border: 1px solid #00dc82;
}
.pass-qual .state{
	color: #00dc82;
	line-height: 30px;
}
.sub-qual-btn{
	display: block;
	width: 120px;
	height: 30px;
	text-align: center;
	line-height: 30px;
	color: #fff;
	border-radius: 3px;
	background: #00dc82;
	margin: 40px auto;
}
.sub-qual-btn:hover{
	color: #fff;
	opacity: 0.8;
}
/*我的订单*/
.myorder{
	margin-bottom: 40px;
}
.sui-nav.nav-tabs>li>a{
	line-height: 38px;
}
.sui-nav.nav-tabs>.active>a{
	border: none;
	color: #000;
	font-size: 16px;
	border-bottom: 1px solid #00dc82;
}
.myorder-nav-tabs{

}
.myorder-nav-tabs li{
	border: none;
	line-height: 38px;
	width: 90px;
	text-align: center;
}
.myorder-nav-tabs li a{
	display: block;
	width: 90px;
	height: 38px;
	line-height: 38px;
	padding: 0;
	border:none;
}
.myorder-nav-tabs li a i{
	font-style: normal;
	color: #f39800;
}
.myorder-search {
	color: #999;
}
.myorder-search .search-item{
	margin-bottom: 15px;
	height: 30px;
}
.myorder-search .inp-sel{
	width: 105px;
	height: 28px;
	line-height: 28px;
	border: 1px solid #ddd;
}
.myorder-search .padding-left{
	padding-left: 30px;
}
.myorder-search .inp-num{
	width: 158px;
	height: 28px;
	line-height: 28px;
	border: 1px solid #ddd;
	padding: 0 5px;
}
.myorder-search .sui-form input[type="text"]{
	height: 24px;
	line-height: 24px;
}
.myorder-search .sui-form{
	width: 340px;
	float: left;
}
.myorder-search .query-btn{
	display: block;
	color: #fff;
	width: 100px;
	height: 30px;
	background: #00dc82;
	text-align: center;
	line-height: 30px;
	float: left;
}
.myorder-search .clear-btn{
	display: block;
	color: #fff;
	width: 130px;
	height: 30px;
	background: #00dc82;
	text-align: center;
	line-height: 30px;
	float: left;
	margin-left: 20px;
}

.myorder-tab-content .oper-btn{
	background: #00dc82;
	padding: 2px 4px;
	margin-right: 10px;
	color: #fff;
}
.myorder-tab-content .opt-btn{
	padding: 2px 4px;
	margin-right: 10px;
	color: #666;
}
.myorder-tab-content .opt-btn:hover{
	text-decoration: underline;
	color: #00dc82;
}
/*收获地址*/
.myaddress{
	margin-bottom: 40px;
}
.add-address{
	height: 40px;
	padding: 0 30px;
	float: left;
	width: 300px;
}

.add-address-btn{
	display: block;
	width: 100px;
	height: 23px;
	text-align: center;
	line-height: 23px;
	border: 1px solid #00dc82;
	color: #00dc82;
	margin-top: 10px;
	border-radius: 3px;
}
.add-address-btn:hover{
	color: #00dc82;
	text-decoration: none;
	opacity: 0.8;
}

.address-list{
	width: 918px;
	padding: 0 30px 40px 30px;
}
.address-list th,.address-list td{
	line-height: 40px;
}
.address-list .res-btn{
	color: #00dc82;
	padding: 0 5px;
}
.address-list .res-btn:hover{
	color: #f39800;
}
.address-list .opt-btn{
	color: #f39800;
	padding: 0 5px;
}

.reg-address-list{
	width: 918px;
	padding: 40px 30px;
}
.reg-address-list th,.reg-address-list td{
	line-height: 40px;
}

.myaddress .msg-question{
	color: #f39800;
	width: 918px;
	margin-left: 30px;
	background: rgb(255, 229, 219);
	border: 1px solid #f39800;

	border-radius: 5px;

}
.myaddress .msg-question .msg-con{
	color: #f39800;
}
.myaddress .msg-question p{
	color: #f39800;
}
.myaddress .msg-question .sui-icon{
	font-size: 24px;
	float: left;
	padding-right: 8px;
}

#addModal,#editModal{
	width: 700px;
}
.add-address-modal .sui-form.form-horizontal .control-label{
	display: inline-block;
}
.add-address-modal .sui-form input[type="text"]{
	height: 24px;
	line-height: 24px;
}
.add-address-modal .detailinp{
	margin-top: 10px;
	width: 395px;
}
.add-address-modal .prov,.add-address-modal .city,.add-address-modal .dist{
	width: 110px;
	height: 28px;
	border: 1px solid #e8e8e8;
	margin-right: 5px;
}
.add-address-modal .modal-bot{
	padding-left: 100px;
}
.add-address-modal .modal-bot button{
	width: 98px;
	height: 30px;
	margin-right: 10px;
}
.add-address-modal .btn-primary {
	background: #00dc82;
	border: 1px solid #00dc82;
}
.sui-modal .modal-header .modal-title{
	font-size: 20px;
	color: #333;
	font-weight: bold;
}
#city_4{
	text-align: left;
}
#fullAddress{
	width:400px;
	line-height:1.2;
	table-layout: fixed;
	word-break: break-all;
}
#my-address-p{
	line-height:1.2;
	table-layout: fixed;
	word-break: break-all;
}

/*wsh add 20170627 begin*/
#addModal,#editModal{  width: 700px;  }
.add-address-modal .sui-form.form-horizontal .control-label{display: inline-block;  }
.add-address-modal .sui-form input[type="text"]{  height: 24px;  line-height: 24px;  }
.add-address-modal .detailinp{  width: 395px;}
.add-address-modal .prov,.add-address-modal .city,.add-address-modal .dist{  width: 110px;  height: 28px;  border: 1px solid #e8e8e8;  margin-right: 5px;}
.add-address-modal .modal-bot{ text-align: right; margin-top: 10px;border-top: 1px solid #eee; padding-top: 20px; }
.add-address-modal .modal-bot button{  width: 98px;  height: 30px;  margin-right: 10px;  }
.add-address-modal .btn-primary {  background: #00dc82;  border: 1px solid #00dc82;}
.sui-modal .modal-header .modal-title{  font-size: 20px;  color: #333;  font-weight: bold;  }
#editModal textarea{height: 67px;width: 353px;}
#editModal .spe{position: relative;top:-25px;}
img.errorimg{position: absolute;left: 3px;top: 4px;}
/*wsh add 20170627 end*/

/*个人中心*/
.myuser{
	margin-bottom: 40px;
}
.myuser-left{
	width: 978px;
}
.myuser-right{
	width: 240px;
	margin-right: 10px;
}

.myuser-left .uname .text{
	font-size: 24px;
	color: #333;
	padding-left: 30px;
	display: inline-block;
	float: left;
	margin-top: 10px;
}
.myuser-left .uname .icon-cation{
	display: inline-block;
	width: 48px;
	height: 38px;
	background: url(/static/images/user/ren-icon.png) no-repeat;
	float: left;
	margin-left: 15px;
}
.assets{
	margin-top: 20px;
}

.mymod-title{
	border-bottom: 4px solid #00dc82;
	height: 30px;

}
.mymod-title h4{
	width: 105px;
	height: 30px;
	background: url(/static/images/user/mymodtitle.png) no-repeat;
	line-height: 30px;
	text-align: center;
	color: #fff;
	font-size: 16px;
}
.assets-info{
	background: #fff;
	border-radius: 6px;
	height: 80px;
	margin-bottom: 10px;
}
.assets-info .link{
	padding: 0 40px 0 10px;
	color: #0066cc;
}

.myorder-box-item{
	display: inline-block;
	width: 25%;
	text-align: center;
	float: left;
	margin-top: 50px;
}
.myorder-box-item .icon-or{
	display: inline-block;
	width: 100%;
	height: 80px;
	width: 80px;
	background-position: center top;
	background-repeat: no-repeat;
	background-size: 100%;
}
.myorder-box-item p{
	padding-top: 5px;
}
.myorder-box-item p .origan{
	color: #f39801;
	font-weight: 600;
}
.myorder-box-item .icon-or1{
	background-image: url(/static/images/user/or1.png)
}
.myorder-box-item .icon-or2{
	background-image: url(/static/images/user/or2.png)
}
.myorder-box-item .icon-or3{
	background-image: url(/static/images/user/or3.png)
}
.myorder-box-item .icon-or4{
	background-image: url(/static/images/user/or4.png)
}
.myorder-box-item:hover,.myorder-box-item:link,.myorder-box-item:active{
	text-decoration: none;
}
.checkin{margin: auto auto auto auto; width: 240px; padding: 5px;border: 1px solid #00dc82; border-radius: 3px; float: left; }

.checkin-title{height: 36px;line-height: 36px;font-size: 16px;margin-bottom: 10px;}

.checkin-title p {
	float: left;
	width: 93px;
	height: 36px;
	line-height: 36px;
	font-size: 12px;
	padding-left: 5px;
}
.checkin-title .sign-days-month{
	display: inline-block;
	width: 77px;
	float: left;
	font-size: 12px;
}

.checkin-title a {
	display: inline-block;
	width: 55px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	background: #00dc82;
	border-radius: 5px;
	color: #fff;
	text-decoration: none;
	font-size: 14px;
}
.checkin li{background: #fff; float: left;padding: 10px;text-align: center;}
.checkin-title .signed{
	background: #f39800;
	cursor: default;
}

li.able-qiandao{ color: #f39800!important; }

li.checked {
	background: #fff url(/static/images/user/singicon.png) no-repeat center;
	background-size: 85%;
}
.checkinmask{ width: 100%;height: 105%;position: absolute;top: 0;left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.55);visibility: hidden;transition: all 0.25s ease}
.checkinmodal{background:#fff;width: 450px;height: 400px;border-radius: 10px;position: absolute;margin-top: -200px;margin-left:-225px;left: 50%;top: 50%;border:5px solid #00dc82;box-sizing:border-box;overflow: hidden;transform: translateY(-200%);transition: all 0.25s ease}
a.closeBtn{display: block;position: absolute;right: 10px;top: 5px;font-family: 'simsun';font-size: 36px;text-decoration: none;font-weight: bolder;color: #333}
.title_h1{text-align: center;font-size: 40px;font-weight: normal;padding-top: 80px;display: block;width: 100%}
.title_h1 span{display: inline-block;width: 40px;height: 40px;position: relative;float: left;margin-left: 30%;margin-top: 7px;}
.title_h1 em{display: inline-block;font-size: 30px;float: left;margin-left: 10px; font-style: normal; margin-top: 9px;}
.title_h1 i{display: inline-block;font-size: 16px;float: left;margin: 14px 0 0 10px;}
.title_h2{text-align: center;font-size: 16px;display: block;padding-top: 20px;}
.title_h2 span{font-size: 36px;color: #b25d06;}
.title_h3{
	text-align: center;
	font-size: 20px;
	padding: 10px 0;
}
.trf{visibility: visible;}
.trf .checkinmodal{transform: translateY(0);}
.calendarList{
	border-left: 1px solid #e8e8e8;
	margin-right: -1px;
}
/*修改密码*/
.mypassword{
	margin-bottom: 40px;
}
.mypassword-cont{
	padding-left: 100px;
	padding-top: 50px;
}
.mypassword .control-label{
	font-size: 16px;
	color: #666;
}
.mypassword .sui-form input[type="password"]{
	width: 300px;
	height: 30px;
}
.changpassword-btn{
	width: 95px;
	height: 35px;
	text-align: center;
	line-height: 35px;
	background: #00dc82;
	border: none;
	font-size: 14px;
}
.changpassword-btn:hover{
	opacity: 0.8;
	background: #00dc82;
	border: none;
}
.mypassword  .error-text{
	color: #f00;
}
/*我的优惠券*/
.mycoupon{
	margin-bottom: 40px;
}
.mycoupon-cont .sui-nav.nav-tabs>li{
	width: 120px;
	text-align: center;
}
.mycoupon-cont .sui-nav.nav-tabs>li>a{
	height: 40px;
	line-height: 40px;
}
.mycoupon-cont .sui-nav.nav-tabs{
	padding-left: 0;
	height: 44px;
}
.mycoupon-cont .sui-nav.nav-tabs>.active>a{
	border-bottom:2px solid #00dc82;
	color: #00dc82;
}
.mycoupon-cont .coupon-list{
	padding: 25px 30px;
	width: 918px;
}
.mycoupon-cont .coupon-item{
	display: block;
	width: 280px;
	overflow: hidden;
	float: left;
	margin: 10px;
	padding: 9px 0;
	border-radius: 3px;
}
.coupon-notuse-list .coupon-item{
	background: #00dc82 url(/static/images/user/coupon-itembg1.png) center 9px no-repeat;
}
.coupon-overdue-list .coupon-item{
	background: #ccc url(/static/images/user/coupon-itembg2.png) center 9px no-repeat;
}
.coupon-item-left {
	display: inline-block;
	width: 100px;
	height: 82px;
	color: #00dc82;
	font-size: 40px;
	line-height: 82px;
	float: left;
	overflow: hidden;
	padding-left: 20px;
}
.coupon-item-left .rmb-icon{
	font-size: 24px;
}
.coupon-item-right{
	width: 160px;
	height: 82px;
	float: left;
	display: inline-block;
	overflow: hidden;
	text-align: center;
}
.coupon-item-right .range{
	margin-top: 15px;
}
.coupon-item-right .condition{
	width: 110px;
	height: 16px;
	line-height: 16px;
	overflow: hidden;
	color: #fff;
	background: #00dc82;
	border-radius: 10px;
	margin: 0 auto;
	margin-top: 5px;
	font-size: 12px;
}
.coupon-item-right .limit{
	margin-top: 5px;
	font-size: 12px;
}

.coupon-overdue-list .coupon-item-left{
	color: #ccc;
}
.coupon-overdue-list .condition{
	background: #ccc;
}
/*心愿单*/
.mywish{
	margin-bottom: 40px;
}
.mywish .main-right{
	border: 1px solid #e8e8e8;
	width: 978px;
}
.mywish-cont{
	padding: 30px;
	width: 918px;
}
.mywish-cont .sui-nav.nav-tabs>li>a{
	height: 40px;
	line-height: 40px;
}
.mywish-cont .sui-nav.nav-tabs{
	padding-left: 0;
	height: 44px;
}
.mywish-cont .sui-nav.nav-tabs>.active>a{
	border-bottom:2px solid #00dc82;
}

.mywish-tab-cont{
	padding: 20px;
	width: 878px;
}
.control-wish-label{
	display: inline-block;
	width: 65px;
	text-align: right;
	padding-right: 10px;
}
.mywish-cont .msg-tips{
	margin: 30px 0 20px 0;
}
.mywish-cont .sui-form input[type="text"].inp{
	height: 24px;
	line-height: 24px;
}
.mywish-cont .sui-form input[type="text"].inp-pro{
	width: 290px;
}
.mywish-cont .sui-form input[type="text"].inp-com{
	width: 290px;
}
.mywish-cont .sui-form input[type="text"].inp-spec{
	width: 290px;
}
.mywish-cont .sui-form input[type="text"].inp-mark{
	width: 500px;
}
.mywish-cont .sui-form input[type="number"].inp-com {
	width: 290px;
	height: 24px;
	line-height: 24px;
}
.mywish-cont .sui-form input[type="text"].inp-com {
	width: 290px;
	height: 24px;
	line-height: 24px;
}
.mywish-cont  .sui-wish-btn{
	width: 100px;
	height: 30px;
	margin-right: 15px;
}
.mywish-cont  .sui-wish-btn.btn-primary{
	background: #00dc82;
	border: none;
}
.addwishphoto{
	margin-top: 10px;
	height: 112px;
}
.addwishphoto-item{
	width: 100px;
	height: 100px;
	overflow: hidden;
	position: relative;
	float: left;
	margin-right: 15px;
	border: #e8e8e8 solid 1px;
	border-radius: 4px;
	padding: 5px;
}

.addwishphoto .noadd .addphoto-btn{
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 9;
	opacity: 0;
	filter:alpha(opacity=0);
}
.addwishphoto .hasphoto img{
	display: block;
	width: 100px;
	height: 100px;
}
.addwishphoto .hasphoto .wishshade{
	position: absolute;
	width: 100%;
	height: 100%;
	background: rgba(0,0,0,0.55);
	top: 0;
	left: 0;
	bottom: 0;
	z-index: 9;
	display: none;
}
.addwishphoto .hasphoto .del-btn{
	width: 20px;
	height: 20px;
	background: #fff;
	color: #666;
	font-size: 20px;
	position: absolute;
	top: 5px;
	right: 5px;
	z-index: 10;
	text-align: center;
	line-height: 20px;
	border-radius: 4px;
	cursor: pointer;
	display: none;
}
.addwishphoto-tips{
	margin: 5px 0;
	color: #999;
}
.wishpro-item{
	display: inline-block;
	width: 208px;
	height: 368px;
	position: relative;
	border: 1px solid #e8e8e8;
	float: left;
	padding: 15px 10px;
	margin: 20px 30px;
}
.wishpro-item .wishpro-photo{
	display: block;
	width: 208px;
	height: 208px;
	overflow: hidden;
}
.wishpro-item .wishpro-photo img{
	display: block;
	width: 208px;
}
.wishpro-item .wishpro-price{
	text-align: center;
	height: 50px;
	line-height: 50px;
	color: #ff2400;
	font-size: 22px;
}
.wishpro-item .wishpro-info-row{
	height: 24px;
}
.wishpro-item .wishpro-info-row .name{
	color: #999;
	display: inline-block;
	width: 44px;
	float: left;
}
.wishpro-item .wishpro-info-row .text{
	color: #333;
	width: 164px;
	float: left;
	display: inline-block;
}
.wishpro-cannel{
	height: 32px;
	margin-top: 6px;
}
.wishpro-cannel .min{
	display: inline-block;
	width: 30px;
	height: 30px;
	border: 1px solid #e8e8e8;
	float: left;
	line-height: 30px;
	text-align: center;
	font-size: 20px;
	color: #666;
	cursor: pointer;
}
.wishpro-cannel .add{
	display: inline-block;
	width: 30px;
	height: 30px;
	border: 1px solid #e8e8e8;
	float: left;
	line-height: 30px;
	text-align: center;
	font-size: 20px;
	color: #666;
	cursor: pointer;
}
.wishpro-cannel .min-gray{
	display: inline-block;
	width: 30px;
	height: 30px;
	border: 1px solid #e8e8e8;
	float: left;
	line-height: 30px;
	text-align: center;
	font-size: 20px;
	color: #666;
	background: #e0e0e0;
}
.wishpro-cannel .add-gray{
	display: inline-block;
	width: 30px;
	height: 30px;
	border: 1px solid #e8e8e8;
	float: left;
	line-height: 30px;
	text-align: center;
	font-size: 20px;
	color: #666;
	background: #e0e0e0;
}
.wishpro-cannel .amount-inp{
	display: inline-block;
	width: 52px;
	height: 30px;
	border: none;
	border-top: 1px solid #e8e8e8;
	border-bottom: 1px solid #e8e8e8;
	line-height: 30px;
	text-align: center;
	float: left;
}
.wishpro-cannel .amount-gray-inp{
	display: inline-block;
	width: 52px;
	height: 30px;
	border: none;
	border-top: 1px solid #e8e8e8;
	border-bottom: 1px solid #e8e8e8;
	line-height: 30px;
	text-align: center;
	float: left;
	background: #e0e0e0;
}
.wishpro-cannel .join-btn{
	display: inline-block;
	width: 90px;
	height: 32px;
	line-height: 32px;
	text-align: center;
	color: #fff;
	background: #00dc82;
	float: left;
}
.wishpro-cannel .join-gray-btn{
	display: inline-block;
	width: 90px;
	height: 32px;
	line-height: 32px;
	text-align: center;
	color: #666;
	background: #e0e0e0;
	float: left;
}
.wishpro-item .icon-been{
	display: block;
	width: 70px;
	height: 36px;
	position: absolute;
	top: -5px;
	left: 10px;
}
.wishpro-item .icon-has{
	background: url(/static/images/user/been2.png) no-repeat 0 0;
}
.wishpro-item .icon-not{
	background: url(/static/images/user/been1.png) no-repeat 0 0;
}
.wishpro-item .icon-notcomplete{
	background: url(/static/images/user/been3.png) no-repeat 0 0;
}
.wishpro-item .sui-tooltip{
	bottom: 50px;
	left: 10px;
	display:none;
}
/*我的收藏*/
.mycolle{
	margin-bottom: 40px;
}
.mycolle-cont .sui-nav.nav-tabs>li>a{
	height: 40px;
	line-height: 40px;
	font-weight: 700;
}

.mycolle-cont .sui-nav.nav-tabs > li > a > span {
	margin-left: 3px
}
.mycolle-cont .sui-nav.nav-tabs{
	padding-left: 0;
	height: 44px;
}
.mycolle-cont .sui-nav.nav-tabs>.active>a{
	border-bottom:2px solid #00dc82;
}

.mycolle-cont .sui-nav.nav-tabs > li > a:hover, .mycolle-cont .sui-nav.nav-tabs > li > a:focus {
	color: #00C675;
}
.main .mycolle-cont .sui-nav .seach-box .icon-search{
	color:#D9D9D9;
	left:170px;
	top:5px;
}
.mycolle-cont .sui-nav.nav-tabs > .active > a {
	color: #00C675;
}
.mycolle-cont .sui-nav.nav-tabs>li.seach-box{
	float: right;
	width: 285px;
	position: relative;
}
.mycolle-cont .sui-nav.nav-tabs>li.seach-box>input{
	padding-right: 25px;
}
.mycolle-cont .seach-box .inp-seach{
	width: 160px;
	height: 28px;
	border: 1px solid #cacaca;
	float: left;
	margin-right: 10px;
	border-radius: 5px;
	padding-left: 5px;
}

.mycolle-cont .seach-box .seach-btn {
	display: inline-block;
	height: 30px;
	width: 65px;
	text-align: center;
	cursor: pointer;
	float: left;
	line-height: 30px;
	background: #00C675;
	border-radius: 4px;
	font-size: 14px;
	color: #FFFFFF;
	position:relative;
}

.mycolle-cont .sui-nav .seach-box .icon-search {
	color: #fff;
	font-size: 16px;
	position: absolute;
	top: 0;
	left: 25px;
}
.mycolle-cont .cantorle{
	padding: 0 15px;
	width: 948px;
	height: 30px;
}
.mycolle-cont .cantorle .del-ced-btn{
	margin-left: 18px;
	color: #666;
}
.mycolle-cont .cantorle .join-ced-btn{
	margin-left: 18px;
	color: #666;
}
.mycolle-cont .cantorle .item-type-btn1{
	margin-left: 18px;
	color: #666;
}
.mycolle-cont .cantorle .item-type-btn2{
	margin-left: 18px;
	color: #666;
}
.mycolle-cont .cantorle .item-type-btn-cur{
	color: #f39800;
}
.cantorle-right{
	width: 190px;
}


/*每日特惠列表*/
ul.mrth{width: 978px;margin: 0 auto;overflow: hidden; display: none;}
.mrth li{width: 228px;height: 420px;border: 1px solid #e0e0e0; float:left;margin-right: 17px;margin-right:12px\9;margin-bottom: 15px;position: relative;z-index: 1;}
.mrth li:nth-child(4n){margin-right: 0;}
.mrth li:hover{border: 1px solid #f56f26;}

.mrth .row1{ width: 228px; height: 202px; position: relative;}
.mrth .row1 .checkbox-pretty{
	display: block;
	height: 20px;
	margin: 5px;
}
.mrth .row1 img.photo{
	display: block;
	width: 180px;
	height: 180px;
	margin: 0 auto;
}
.mrth .row1 img.markIcon{
	display: block;
	width: 58px;
	height: 58px;
	position: absolute;
	top: -5px;
	right: 0;
}
.mrth .row1 .tag-box{
	position: absolute;
	left: 0;
	bottom: 0;
	width: 228px;
}
.mrth .row1 .tag-box .tag{
	display: inline-block;
	padding: 2px 5px;
	margin-top: 5px;
	border-radius: 0 15px 15px 0;
	font-size: 12px;
	color: #fff;
}
.mrth .row1 .tag-box .tag-kong{
	background: #00a4ff;
}
.mrth .row1 .tag-box .tag-xian{
	background: #f39800;
}
.mrth .row1 .tag-box .tag-man{
	background: #ff0000;
}

.mrth .row2{text-align: center;font-size: 18px;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;padding: 0 27px;margin-top: 40px;}
.mrth .row2 a{color: #666666;}
.mrth .row2 a:hover{color: #f39800;}
.mrth .row3{text-align: center;font-size: 20px;margin-top: 12px;}
.mrth .row3 .price{color: #ff2400;}
.mrth .row3 .login_show{color: #f39800;}
.mrth .row3 .noPermission{color: #ff2400;}
.mrth .row4{text-align: center;color: #666666;margin-top: 8px;}
.mrth .row5{text-align: center;color: #666666;margin-top: 8px;margin-bottom: 30px;}
.mrth .row6{width: 206px;height: 32px;margin: 0 auto; position: relative;}
.mrth .row6 a.sub{display:block;width: 30px;height: 32px;line-height: 32px;border-right: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.mrth .row6 input{display:block;width: 54px;height: 32px;line-height: 32px;margin: 0;padding: 0;border: none;text-align: center;}
.mrth .row6 a.add{display:block;width: 30px;height: 32px;line-height: 32px;border-left: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.mrth .row6 a.buy{display:block;width: 90px;height: 32px;line-height: 32px;color:#fff;background: #00dc82;text-align: center;}
.mrth .row6 a.gary{display:block;width: 90px;height: 32px;line-height: 32px;color:#999999;background: #e0e0e0;text-align: center;}
.mrth .soucang{display:block;position: absolute;top:10px;left: 10px;width: 80px;height: 32px;line-height: 32px;border: 1px solid #f39800;color: #f39800;z-index: 4;text-align: center;}
.mrth .soucang_gary{display:block;position: absolute;top:10px;left: 10px;width: 80px;height: 32px;line-height: 32px;border: 1px solid #e0e0e0;color: #999;z-index: 4;text-align: center;}
.mrth .soucang i{margin-right: 5px;}
.mrth .showorno{display: none;}

.mrth .row6 .min {
	display: inline-block;
	width: 30px;
	height: 30px;
	border: 1px solid #e8e8e8;
	float: left;
	line-height: 30px;
	text-align: center;
	font-size: 20px;
	color: #666;
	cursor: pointer;
}
.mrth .row6 .amount-inp {
	display: inline-block;
	width: 52px;
	height: 30px;
	border: none;
	border-top: 1px solid #e8e8e8;
	border-bottom: 1px solid #e8e8e8;
	line-height: 30px;
	text-align: center;
	float: left;
}

.mrth .row6 .add {
	display: inline-block;
	width: 30px;
	height: 30px;
	border: 1px solid #e8e8e8;
	float: left;
	line-height: 30px;
	text-align: center;
	font-size: 20px;
	color: #666;
	cursor: pointer;
}
.mrth .row6 .sui-tooltip {
	top: -38px;
	left: 0;
	display:none;
}

/*列表模式*/
.listmode{width:976px;border: 1px solid #e0e0e0;display: none;}
/*表头*/
.listmode .headbox{overflow: hidden;background: #f5f5f5;}
.listmode .headbox  ul{}
.listmode .headbox  li{float:left;height: 50px;line-height: 50px;text-align: center;}
.listmode .headbox .li1{width: 290px;text-align: left;padding-left: 50px;}
.listmode .headbox .li2{width: 200px;}
.listmode .headbox .li3{width: 90px;}
.listmode .headbox .li4{width: 75px;}
.listmode .headbox .li5{width: 140px;}
.listmode .headbox .li6{width: 130px;}
/*表体*/
.listmode .bodybox{overflow: hidden;border-bottom: 1px solid #e0e0e0;}
.listmode .bodybox  ul{}
.listmode .bodybox  li{float:left;height: 134px;}
.listmode .bodybox .lib1{width: 340px;}
.listmode .bodybox .lib2{width: 200px;}
.listmode .bodybox .lib3{width: 90px;}
.listmode .bodybox .lib4{width: 75px; margin-top: 30px;}
.listmode .bodybox .lib5{width: 140px; position: relative;}
.listmode .bodybox .lib6{width: 130px;}
.lib1 .checkb {
	width: 10px;
	margin-top: 25px;
	padding-left: 10px;
}

.lib1 .l-box {
	width: 90px;
	height: 90px;
	border: 1px solid #e0e0e0;
	overflow: hidden;
	margin-left: 20px;
	margin-top: 10px;
	padding: 2px;
	display: inline-block;
	position: relative;
}
.lib1 .l-box img.photo{width: 110px;height: 110px; display: block;}
.lib1 .l-box img.markIcon{width: 30px;height: 30px; display: block; position: absolute;left: 0;top: 0;}
.lib1 .r-box {width: 190px;padding-left: 10px;padding-top: 20px;}
.lib1 .r-box span{font-size: 12px;color: #333333;}
.lib1 .r-box span.title{color: #999;}
.lib1 .r-box span.info{color: #666;}
.lib1 .r-box .lib1-row1{display: inline-block;margin-top: 5px;}
.lib1 .r-box span a{color: #333;}
.lib1 .r-box span a:hover{color:#f39800;}
.lib1 .r-box .lib1-row2{margin-top: 2px; width: 190px;}
.lib1 .r-box .lib1-row3{margin-top: 2px;width: 190px;}
.lib1 .r-box .lib1-row4{margin-top: 2px;width: 190px;}
.lib1 .r-box .lib1-row5{margin-top: 2px;width: 220px;}

.lib2 span{font-size: 12px;}
.lib2 .huodong{margin-top: 30px;padding-left: 10px;}
.lib2 .huodong span{display: inline-block; border: 1px solid #ff2400; color: #ff2400;padding: 2px 7px; }
.lib2 .time{margin-top: 15px;padding-left: 10px;}
.lib2 .time span{color: #ff2400;}

.lib3{text-align: center;}
.lib3 .zkj{color: #ff2400;margin-top: 30px;}
.lib3 .sjj{margin-top: 15px;}
.lib3 .sjj span{text-decoration: line-through;color: #999;}
.lib3 .loginshow{color: #f39800;margin-top: 20px;}
.lib3 .notbug{color: #ff2400;margin-top: 20px;}
.lib4{text-align: center;padding-top: 20px;}
.lib5{padding-top: 50px;}
.lib5 .suliang{width:120px;height: 32px;margin: 0 auto;}
.lib5 .suliang .cannel .min{
	display: inline-block;
	width: 30px;
	height: 30px;
	border: 1px solid #e8e8e8;
	float: left;
	line-height: 30px;
	text-align: center;
	font-size: 20px;
	color: #666;
	cursor: pointer;
}
.lib5 .suliang .cannel .add{
	display: inline-block;
	width: 30px;
	height: 30px;
	border: 1px solid #e8e8e8;
	float: left;
	line-height: 30px;
	text-align: center;
	font-size: 20px;
	color: #666;
	cursor: pointer;
}
.lib5 .suliang .cannel .amount-inp{
	display: inline-block;
	width: 52px;
	height: 30px;
	border: none;
	border-top: 1px solid #e8e8e8;
	border-bottom: 1px solid #e8e8e8;
	line-height: 30px;
	text-align: center;
	float: left;
}
.lib5 .sui-tooltip {
	top: 10px;
	left: 10px;
	display:none;
}
.lib6 a{display:block;margin:0 auto;width: 120px;height: 30px;line-height: 30px;text-align: center;border-radius: 3px;}
.lib6 a.addbuy{color: #fff;background: #00dc82;border: 1px solid #00dc82;margin-top: 40px;}
.lib6 a.souc{color: #666666;border: 1px solid #e0e0e0;margin-top: 8px;}
.lib6 a.active{border: 1px solid #f39800;color: #f39800;background: #fff;margin-top: 8px;}

.page{width: 1200px;margin: 0 auto;text-align: center;margin-top: 30px;margin-bottom: 30px;}
.pagination-small ul>li>a, .pagination-small ul>li>span{font-size: 12px;padding: 5px 10px;color: #666;}
.sui-pagination ul>.active>a, .sui-pagination ul>.active>span{background: #00dc82;border-color:#00dc82;}
.sui-pagination ul li a:hover,.sui-pagination ul li a:focus{background: #00dc82;border-color:#00dc82;}
.sui-pagination ul li a:active{background: #00dc82;border-color:#00dc82;}
.sui-pagination ul>.active a:hover{background: #00dc82;border-color:#00dc82;}
.sui-pagination ul>.active a:active,:visited{background: #00dc82;border-color:#00dc82;}
.sui-pagination div .page-num:focus{border: 1px solid #00dc82;}

.pagination-small div .page-num+.page-confirm, .pagination-small div .page-num+.page-confirm{background: #00dc82;border-color:#00dc82;}
.pagination-small div .page-num+.page-confirm:hover, .pagination-small div .page-num+.page-confirm:focus{background: #00dc82;border-color:#00dc82;}
/*申请退款*/
.myreorder{
	margin-bottom: 40px;
}
.myreorder-tit{
	height: 40px;
	line-height: 40px;
}
.myreorder-table table{
	width: 100%;
	border: 1px solid #e8e8e8;
}
.myreorder-table table thead{
	height: 40px;
	background: #f5f5f5;
	line-height: 40px;
}
.myreorder-table table thead th,.myreorder-table table tbody td{
	text-align: center;
}
.myreorder-table table .tb1{
	width: 110px;
}
.myreorder-table table .tb2{
	width: 440px;
}
.myreorder-table table .tb3{
	width: 160px;
}
.myreorder-table table .tb4{
	width: 100px;
}
.myreorder-table table .tb5{
	width: 160px;
}
.myreorder-table table .tr-row{
	border-top: 1px solid #e8e8e8;
}
.myreorder-table table tbody tr{
	height: 135px;
}
.myreorder-table .product-photo{
	display: inline-block;
	width: 90px;
	height: 90px;
	padding: 5px;
	border: 1px solid #e8e8e8;
	border-radius: 2px;
	float: left;
}
.myreorder-table  .product-info{
	width: 320px;
	text-align: left;
	margin-left: 20px;
	float: left;
}
.myreorder-table  .product-info .product-info-title{
	width: 100%;
	height: 30px;
	padding-top: 10px;
	color: #000;
}
.myreorder-table  .product-info .product-info-title a{color: #000;}
.myreorder-table  .product-info .product-info-title a:hover{color: #f39800;}
.myreorder-table  .product-info .product-info-row{
	width: 100%;
	height: 20px;

}
.myreorder-table  .product-info .product-info-row .name{
	color: #999;
}
.myreorder-table .product-price{
	font-size: 18px;
	color: #ff2400;
}
.myreorder-table .product-oldprice{
	padding-top: 10px;
	text-decoration:line-through;
	color: #999;
}
.myreorder-table .total-price{
	font-size: 18px;
}
.myreorder-table .fav-price{
	color: #999;
	padding-top: 10px;
}
.myreorder .more-list-btn{
	display: block;
	height: 30px;
	width: 976px;
	border: 1px solid #e8e8e8;
	border-top: none;
	text-align: center;
	line-height: 30px;
	color: #666;
}
.myreorder .more-list-btn:hover{
	color: #00dc82;
}
.myreorder-loading{
	display: block;
	height: 30px;
	width: 976px;
	border: 1px solid #e8e8e8;
	border-top: none;
	text-align: center;
	line-height: 30px;
	color: #666;
}
.myreorder-bot{
	background: #f5f5f5;
	width: 978px;
	overflow: hidden;
}
.myreorder-bot .total-num {
	float: left;
	padding: 20px 0 0 30px;
	width: 270px;
	display: inline-block;
}
.myreorder-bot .total-num .num{
	color: #f39800;
}
.myreorder-bot .cancol-box {
	float: left;
	text-align: right;
	width: 350px;
	display: inline-block;
}
.myreorder-bot .cancol-box .cancol-box-top{
	font-size: 18px;
	padding-top: 10px;
}
.myreorder-bot .cancol-box .cancol-box-top .total-price-num{
	color: #ff2400;
	width: 120px;
	display: inline-block;
	text-align: left;
}
.myreorder-bot .cancol-box .cancol-box-bot{
	padding-top: 10px;
}
.myreorder-bot .cancol-box .cancol-box-bot p{
	font-size: 12px;
}
.myreorder-subtn{
	display: inline-block;
	width: 126px;
	height: 38px;
	overflow: hidden;
	float: left;
	text-align: center;
	line-height: 38px;
	border: 1px solid #ff2400;
	color: #ff2400;
	margin: 25px 0 0 25px;
	border-radius: 4px;
}
.again-buy-btn{
	display: inline-block;
	width: 126px;
	height: 38px;
	overflow: hidden;
	float: left;
	text-align: center;
	line-height: 38px;
	border: 1px solid #ff2400;
	color: #ff2400;
	margin: 25px 0 0 25px;
	border-radius: 4px;
}
/*退款原因*/
.myreordereason{
	margin-bottom: 40px;
}
.myreordereason-main .msg-info{
	color: #ff9948;
	background: #fff0e4;
	border: 1px solid #ff9948;
	border-radius: 4px;
	width: 770px;
	padding: 10px 15px;
}
.myreordereason-main .sui-msg.msg-info>.msg-con{
	background: #fff0e4;
	border:none;
	color: #ff9948;
}
.myreordereason-main .sui-msg>.msg-icon{
	top: 12px;
	left: 12px;
}
.myreordereasonphoto{
	height: 112px;
}
.myreordereasonphoto-item{
	width: 100px;
	height: 100px;
	overflow: hidden;
	position: relative;
	float: left;
	margin-right: 15px;
	border: #e8e8e8 solid 1px;
	border-radius: 4px;
	padding: 5px;
}
.myreordereasonphoto-itemSpec{
	width: 100px;
	height: 100px;
	position: relative;
	margin-right: 15px;
	border: #e8e8e8 solid 1px;
	border-radius: 4px;
	padding: 5px;
}
.myreordereasonphoto .noadd .addphoto-btn{
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 9;
	opacity: 0;
}
.myreordereasonphoto .hasphoto img{
	display: block;
	width: 100px;
	height: 100px;
}
.myreordereasonphoto .hasphoto .wishshade{
	position: absolute;
	width: 100%;
	height: 100%;
	background: rgba(0,0,0,0.55);
	top: 0;
	left: 0;
	bottom: 0;
	z-index: 9;
	display: none;
}
.myreordereasonphoto .hasphoto .del-btn{
	width: 20px;
	height: 20px;
	background: #fff;
	color: #666;
	font-size: 20px;
	position: absolute;
	top: 5px;
	right: 5px;
	z-index: 10;
	text-align: center;
	line-height: 20px;
	border-radius: 4px;
	cursor: pointer;
	display: none;
}
.reordereason-subtn{
	display: block;
	width: 125px;
	height: 35px;
	text-align: center;
	line-height: 35px;
	background: #00dc82;
	color: #fff;
	border-radius: 4px;
	margin-top: 20px;
}
.reordereason-subtn:hover{
	color: #fff;
	opacity: 0.8;
}
.left-ubox{display: inline-block;width: 300px;}
.right-ubox{display: inline-block;width: 300px;}
.myreordereason-text-title{
	padding-top: 10px;
	padding-bottom: 5px;
}
.myreordereason-amount-num{
	color: #ff2400;
	font-size: 20px;
}
.reason-select{
	width: 200px;
	height: 30px;
	border-radius: 3px;
	border: 1px solid #e8e8e8;
}
.mark-textarea{
	line-height: 24px;
	padding: 5px;
	width: 470px;
	height: 80px;
	border-radius: 4px;
	border: 1px solid #e8e8e8;
}

/*退款详情*/

.myreorderdetail-left .sui-msg{
	margin-bottom: 20px;
}
.myreorderdetail-left p{
	padding: 4px 0;
}
.myreorderdetail-left p.tips-text{
	color: #00dc82;
}
.myreorderdetail-left .mark-text-title{
	display: inline-block;
	width: 70px;
	float: left;
}
.myreorderdetail-left .mark-text-detail{
	display: inline-block;
	width: 270px;
	float: left;
}
.myreorderdetail-right{
	width: 450px;
	float: left;
	margin-top: 30px;
}
.myreorderdetail-right .pinze-title{
	display: inline-block;
	width: 75px;
	float: left;
}
.myreorderdetail-right .pinze-photo{
	display: inline-block;
	width: 100px;
	height: 100px;
	padding: 5px;
	margin-left: 10px;
	border: 1px solid #e8e8e8;
	float: left;
}
.myreorderdetail .myreorder-bot .cancol-box{
	width: 620px;
}
/*订单详情*/


.qual-item .inp-name-text{
	height: 20px;
	width: 140px;
	color: #333;
	line-height: 0;
	border: 1px solid #fff;
}
.qual-item .inp-name-text-border{
	border: 1px solid #e8e8e8;
}
.qual-item .save-name-btn{
	display: inline-block;
	width: 48px;
	height: 20px;
	border: 1px solid #e8e8e8;
	font-size: 12px;
	float: right;
	cursor: pointer;
}
.qual-item .zi-uphoto{
	display: block;
	width: 198px;
	height: 190px;
	position: absolute;
	top: 30px;
	left: 0;
	color: #fff;
	opacity: 0;
	filter:alpha(opacity=0);
}

/* 订单详情——入库价 */
.dd-ddjl {
	border-radius: 6px;
	overflow: hidden;
}

.dd-xian {
	border-left: 3px solid #353A42;
	margin-top: 0px;
	height: 18px;
	padding: 0px 0 0 6px;
	margin-bottom: 20px;
}

.dd-xian .bl-info {
	font-size: 14px;
	color: #333;
	font-weight: bold;
}

.dd-xian .dd-dayin {
	font-size: 12px;
	color: #6493D4;
}

.dd-dayin .dd-daochu {
	margin-right: 15px;
}

.dd-dayin span {
	cursor: pointer;
}

.dd-biao thead tr {
	background: #353A42;
	height: 50px;
	text-align: center;
}

.dd-biao  tr th{ text-align: left;color: #fff;padding: 0 5px;}
.dd-biao  tr td{ text-align: left;padding: 0 5px;}
.dd-biao  tr  .th-row1{text-align: center;width: 50px;
	box-sizing: border-box;}
.dd-ddjl .dd-biao .th-row2{width: 100px;}
.dd-ddjl .dd-biao .th-row3{width: 100px;
	box-sizing: border-box;}
.dd-ddjl .dd-biao .th-row4{width: 100px;}

.dd-biao tbody tr {
	height: 60px;
}

.dd-biao tbody tr:nth-of-type(2n) {
	background-color: #f8f8f8;
}

.dd-biao tbody tr:nth-of-type(2n-1) {
	background-color: #fff;
}

.red {
	color: #FF0000;
}

.dd-ddjl .allprice {
	height: 50px;
	background: #EBEBEB;
	text-align: right;
}

.dd-ddjl .allprice .allprice-in {
	margin-right: 30px;
	margin-top: 8px;
	font-weight: 700;
}

/*没有心愿单*/
.page-title{float: none;}
.nowish{width: 518px;height: 149px;margin: 0 auto;padding-top: 70px;padding-bottom: 300px;}
.noshow{display: none;}

/*没有购买记录*/
.nobug-box{width: 518px;height: 149px;margin: 0 auto;padding-top: 70px;padding-bottom: 300px;}

/*总计*/
.totalbox{width: 938px; padding: 20px;}
.totalbox .rbox-total{}
.totalbox .rbox-total .spzjbox{text-align: right;font-size: 14px;color: #333333;margin-bottom: 20px;font-family: "宋体";}
.totalbox .rbox-total .spzjbox span{font-size: 14px;color: #333333;font-family: "宋体";}
.totalbox .rbox-total .spzjbox span.zhongshu{padding: 0 5px;}
.totalbox .rbox-total .spzjbox span.zongjianshu{padding: 0 25px 0 5px;}

.totalbox .rbox-total .fd-warp{overflow: hidden;}
.totalbox .rbox-total .fd-warp .left-fd-box{float: left;padding-right: 40px;}
.totalbox .rbox-total .fd-warp .mid-line{float: left;height: 140px;border-right: 1px solid #ececec;margin-top: 5px;}
.totalbox .rbox-total .fd-warp .right-fd-box{float: right;}
.fd-warp .list-item{margin-bottom: 14px;}
.fd-warp .list-item span{display: inline-block;font-size: 12px;color: #333333;}
.fd-warp .list-item span.spec{color: #9b9999;width: 90px;text-align: right;margin-right: 15px;color: #9b9999;}
.fd-warp .list-item span.red{color: #e73734;}

/*总计栏*/
.orderfix{position: fixed;margin:auto;left:0; right:0; bottom:0;background: #fff;}
.applybox{width: 978px;height:58px;line-height:58px;overflow:hidden;margin:0 auto;border: 0px solid #e0e0e0;box-shadow:-5px -5px 10px #f5f5f5;}
.applybox .acol5{margin-right: 30px;}
.applybox .acol5 .tinfo{font-size: 18px;font-weight: bold;position: relative; top: -5px;}
.applybox .acol5 .money{color: #666;font-size: 30px;text-align: right;font-weight: bold;}
.applybox .acol6 a{display: block;width: 136px;height: 58px;line-height: 58px;text-align: center;background: #ff2400;color: #fff;font-size: 24px;font-weight: bold;text-decoration: none;}
/*结算页面 金额对其*/

.cancol-box-bot-num{
	display: inline-block;
	width: 120px;
	text-align: left;
}
/*pc 2.1.1*/
.user-avatar{
	display: inline-block;
	width: 145px;
	height: 145px;
	float: left;
	position: relative;
	margin: 45px 10px 0 0;
}
.avatar-marker{
	width: 145px;
	height: 145px;
	position: absolute;
	background: url(/static/images/user/avatar-box.png) no-repeat;
	background-size: 100%;
	top: 0;
	left: 0;

}
.avatar-photo{
	display: inline-block;
	width: 125px;
	height: 125px;
	position: absolute;
	top: 10px;
	left: 10px;
}
.user-info{
	width: 470px;
	float: left;
	margin:  20px;
}
.user-info-amount{
	height: 80px;
}
.user-info-amount .amount-item{
	width: 150px;
	display: inline-block;
	float: left;
	height: 80px;
	text-align: center;
	overflow: hidden;
}
.user-info-amount .amount-item-boder{
	border-left: 1px solid #d6d6d6;
	border-right: 1px solid #d6d6d6;
}
.user-info-amount .amount-item .amount-num{
	color: #666;
	font-size: 30px;
	margin-top: 5px;
}
.user-info-amount .amount-item .amount-num .unit{
	font-size: 14px;
}
.user-info-amount .amount-item .us-btn{
	display: inline-block;
	width: 95px;
	height: 26px;
	line-height: 26px;
	text-align: center;
	border: 1px solid #f39800;
	border-radius: 15px;
	color: #f39800;
	margin-top: 15px;
}
.user-info-remaker{
	margin-top: 30px;
}
.user-info-remaker p{
	font-size: 12px;
	padding-top: 5px;
}

.user-gaomao{
	margin-top: 20px;
}
.user-gaomao-title{
	width: 978px;
	height: 40px;
	overflow: hidden;
	background: #f5f5f5;
}
.user-gaomao-title h4{
	width: 200px;
	float: left;
	padding-left: 20px;
	font-size: 16px;
	color: #f39800;
	line-height: 40px;
}
.user-gaomao-title .ref-btn{
	float: right;
	color: #333;
	line-height: 40px;
	padding-right: 20px;
	cursor: pointer;
}
.user-gaomao-title .ref-btn .icon-refresh{
	padding-right: 6px;
}
.user-gaomao-list{
	padding: 5px 0;
}
.user-gaomao-list .user-gaomao-item{
	display: inline-block;
	width: 218px;
	height: 108px;
	overflow: hidden;
	border:1px solid #e8e8e8;
	float: left;
	padding: 5px;
	margin: 7px;
}
.user-gaomao-list .user-gaomao-item .pro-photo{
	display: inline-block;
	width: 90px;
	height: 90px;
	float: left;
	margin-top: 15px;
}
.user-gaomao-list .user-gaomao-item .pro-photo img{
	display: inline-block;
	width: 90px;
}
.user-gaomao-list .user-gaomao-item .pro-info{
	float: right;
	width: 110px;
}
.user-gaomao-list .user-gaomao-item .pro-info .pro-title{
	color: #000;
	margin-top: 15px;
}
.user-gaomao-list .user-gaomao-item .pro-info .pro-spec{
	color: #999;
	font-size: 12px;
}
.user-gaomao-list .user-gaomao-item .pro-info .pro-price{
	color: #ff2400;
	font-size: 16px;
	margin-top: 18px;
}

.order-tab-title{
	height: 100px;
	width: 978px;
	overflow: hidden;
	margin-top: 10px;
}
.order-tab-title-item{
	width: 160px;
	text-align: center;
	float: left;
}
.order-tab-title-item .order-icon{
	display: inline-block;
	width: 73px;
	height: 73px;
	-webkit-transition: background-position linear .15s;
	-moz-transition: background-position linear .15s;
	transition: background-position linear .15s;
}
.order-tab-title-item .order-tab-title-name{
	margin-top: 5px;
	width: 160px;
	display: inline-block;
	color: #666;
}
.order-tab-title-item .order-tab-title-name .origan{
	color: #f39801;
	font-weight: 600;
	font-style: normal;
}
.order-tab-title-item .order-icon1{
	background: url(/static/images/user/mo-icon1.png) no-repeat 0 -73px;
}
.order-tab-title-item .order-icon2{
	background: url(/static/images/user/mo-icon2.png) no-repeat 0 -73px;
}
.order-tab-title-item .order-icon3{
	background: url(/static/images/user/mo-icon3.png) no-repeat 0 -73px;
}
.order-tab-title-item .order-icon4{
	background: url(/static/images/user/mo-icon4.png) no-repeat 0 -73px;
}
.order-tab-title-item .order-icon5{
	background: url(/static/images/user/mo-icon5.png) no-repeat 0 -73px;
}
.order-tab-title-item .order-icon6{
	background: url(/static/images/user/mo-icon6.png) no-repeat 0 -73px;
}
.order-tab-title-item-cur .order-icon{
	background-position: 0 0;
}
.order-tab-title-item .order-icon:hover{
	background-position: 0 0;
}

.myneworder-search{
	margin-top: 25px;
}
.myneworder-search .padding-left{
	padding-left: 15px;
}
.myneworder-search label{
	color: #9a9a9a;
}
.myneworder-search .inp-sel {
	width: 105px;
	height: 28px;
	line-height: 28px;
	border: 1px solid #ddd;
	border-radius: 0;
}
.myneworder-search .sui-form input[type="text"] {
	height: 24px;
	line-height: 24px;
}
.myneworder-search .inp-num {
	width: 134px;
	height: 28px;
	line-height: 28px;
	border: 1px solid #ddd;
	padding: 0 5px;
}
.myneworder-search .query-btn {
	display: block;
	color: #fff;
	width: 100px;
	height: 30px;
	background: #00dc82;
	text-align: center;
	line-height: 30px;
	/*margin-left: 25px;*/
}

.myneworder-search .export-btn {
	display: block;
	color: #fff;
	width: 100px;
	height: 30px;
	background: #00dc82;
	text-align: center;
	line-height: 30px;
	margin-left: 25px;
}
.myneworder-search .detail-export-btn {
	display: block;
	color: #fff;
	width: 100px;
	height: 30px;
	background: #00dc82;
	text-align: center;
	line-height: 30px;
	margin-left: 25px;
}
.myneworder-search .invoice-btn {
	display: block;
	color: #fff;
	width: 100px;
	height: 30px;
	background: #00dc82;
	text-align: center;
	line-height: 30px;
	margin-left: 25px;
}
.myneworder-search #searchShopUl {
	display: none;
}
.myneworder-search .searchShopBox {
	position: relative;
}
.myneworder-search #searchShopUl {
	position: absolute;
	top: 28px;
	right: 0;
	background: #fff;
	width: 210px;
	border: 1px solid #ddd;
}
.myneworder-search #searchShopUl li {
	cursor: pointer;
	padding: 8px 5px;
}
.myneworder-search #searchShopUl li:hover {
	background: #ddd;
}
.myorder-tab-content .table-head{
	width: 978px;
	height: 35px;
	line-height: 35px;
	overflow: hidden;
	background: #f5f5f5;
	margin-bottom: 20px;
}
.myorder-tab-content .table-head-row{
	display: inline-block;
	float: left;
	font-weight: 600;
}
.myorder-tab-content .table-head-row1{
	width: 85px;
	padding-left: 15px;
}
.myorder-tab-content .table-head-row2{
	width:260px;
	text-align: center;
}
.myorder-tab-content .table-head-row3{
	width:170px;
	text-align: center;
}
.myorder-tab-content .table-head-row4{
	width:120px;
	text-align: center;
}
.myorder-tab-content .table-head-row5{
	width:260px;
	text-align: center;
}
.myorder-tab-content .table-head-row6{
	width:60px;
	text-align: center;
}
.myorder-tab-content .table-body{
	margin-top: 10px;
}
.myorder-tab-content .table-body .table-item{
	width: 976px;
	border: 1px solid #e8e8e8;
	overflow: hidden;
	margin-bottom: 10px;
}
.myorder-tab-content .table-body .table-item .table-item-head{
	height: 35px;
	line-height: 35px;
	padding-left: 15px;
	background: #f5f5f5;
}
.myorder-tab-content .table-body .table-item-body .table-body-row{
	display: inline-block;
	float: left;
}
.myorder-tab-content .table-body .table-item-body .table-body-row1{
	width: 70px;
	height: 70px;
	border: 1px solid #e8e8e8;
	padding: 5px;
	margin: 10px 30px;
}
.myorder-tab-content .table-body .table-item-body .table-body-row2{
	width: 220px;
	color: #999;
	padding-top: 20px;
}
.myorder-tab-content .table-body .table-item-body .table-body-row2 div{
	font-size: 13px;
	color: #999;
}
.myorder-tab-content .table-body .table-item-body .table-body-row3{
	width: 170px;
	color: #000;
	text-align: center;
	padding-top: 25px;
}
.myorder-tab-content .table-body .table-item-body .table-body-row3 .wait-pay{
	color: #f39800;
}
.myorder-tab-content .table-body .table-item-body .table-body-row3 div{
	padding-bottom: 10px;
	color: #000;
}
.myorder-tab-content .table-body .table-item-body .table-body-row4{
	width: 125px;
	color: #999;
	text-align: center;
	padding-top: 25px;
}
.myorder-tab-content .table-body .table-item-body .table-body-row4 div{
	padding-bottom: 10px;
	color: #999;
}
.myorder-tab-content .table-body .table-item-body .table-body-row5{
	width: 135px;
	color: #000;
	text-align: center;
	padding-top: 20px;
}
.myorder-tab-content .table-body .table-item-body .table-body-row5 a{
	padding-bottom: 10px;
	display: inline-block;
	width: 165px;
	color: #000;
}
.myorder-tab-content .table-body .table-item-body .table-body-row7{
	width: 60px;
	color: #999;
	padding-top: 25px;
}
.myorder-tab-content .table-body .table-item-body .table-body-row7 div{
	text-align: center;
}
.myorder-tab-content .table-body .table-item-body .table-body-row5 .pay-order-btn{
	width: 75px;
	height: 25px;
	border: 1px solid #00dc82;
	border-radius: 4px;
	line-height: 25px;
	text-align: center;
	color: #00dc82;
	padding: 0;
}
.myorder-tab-content .table-body .table-item-body .table-body-row6{
	width: 122px;
	color: #000;
	text-align: center;
	padding-top: 20px;
}
.myorder-tab-content .table-body .table-item-body .table-body-row6 a{
	padding-bottom: 10px;
	display: inline-block;
	width: 122px;
	color: #000;
}
.red-circle{
	display: inline-block;
	width: 8px;
	height: 8px;
	border-radius: 8px;
	background: #f00;
}
.white-circle{
	display: inline-block;
	width: 8px;
	height: 8px;
	border-radius: 8px;
	background: #fff;
}
.myorder-tab-content .table-bot{
	width: 963px;
	height: 40px;
	margin-top: 20px;
	padding-left: 15px;
}
.myorder-tab-content .table-bot .del-order-btn{
	padding-left: 30px;
	color: #666;
}
.myorder-tab-content .tab-cont{
	display: none;
}
#successModel{
	padding: 30px 20px;
	background: #fff;
	border-radius: 5px;
	font-size: 24px;
	text-align: center;
}
/*pc2.2.1 查看退款*/
.tophead{overflow: hidden;background: #f5f5f5;margin-bottom: 10px;}
.tophead  ul{}
.tophead  li{float:left;height: 38px;line-height: 38px;text-align: center;font-weight: bold;}
.tophead .li1{width: 330px;padding-left: 100px;text-align: left;}
.tophead .li5{width: 70px;}
.tophead .li3{width: 204px;}
.tophead .li4{width: 270px;}

/*套餐退款详情*/
.taocan-box{margin-bottom: 0px;border: 1px solid #ededed;}
.taocan-box .tc-tit{padding: 10px 20px;border-bottom: 1px solid #ededed;font-weight: bold;}
.taocan-box .listmode-tk{margin-bottom: 0px;}
.down .tc-tit{display: block;}
.up .tc-tit{display: none;}

.tuikuanbox{}
/*列表模式*/
.listmode-tk{width:976px;margin: 0 auto; display: block;border: 1px solid #ededed;overflow: hidden;margin-bottom: 10px;}
/*表头*/
.listmode-tk .headbox{overflow: hidden;background: #f5f5f5;height: 36px;line-height: 36px;cursor: pointer;}
.listmode-tk .headbox img{padding-left: 20px;padding-right: 10px;}
.listmode-tk .headbox span{}
.listmode-tk .headbox span.tktime{padding-left: 40px;}
.down .bodybox{display: block;}
.down .tk-why{display: block;}
.up .bodybox{display: none;}
.up .tk-why{display: none;}
.down .zhan-tk{display: block;}
.down .shou-tk{display: none;}
.up .zhan-tk{display: none;}
.up .shou-tk{display: block;}

/*表体*/
.listmode-tklistmode-tk .bodybox{overflow: hidden;}
/*满减*/
.listmode-tk  .manjianbox{height: 36px;line-height: 36px;border-bottom: 1px solid #e0e0e0;}
.listmode-tk  .manjianbox .title{font-size: 14px;display: inline-block;background: #ff2400;color: #fff;height: 17px;line-height: 17px;padding: 2px 5px;margin-left: 20px;margin-right: 20px;}
.listmode-tk  .manjianbox .info{font-size: 12px;color: #ff2400;}
/*列表*/
.listmode-tk .bodybox  ul{overflow: hidden;}
.listmode-tk .bodybox  li{float:left;overflow: hidden;}
.listmode-tk .bodybox .lib1{width: 430px;}
/*.listmode .bodybox .lib2{!*width: 240px;*!width: 230px;text-align: center}*/
.listmode-tk .bodybox .lib5{width: 70px;}
.listmode-tk .bodybox .lib3{width: 204px;}
.listmode-tk .bodybox .lib4{width: 270px;}

/*.listmode .bodybox .lib6{!*width: 168px;*!width: 148px;text-align: left;}*/
/*.lib1 .checkb{width:10px;margin-top: 25px; padding-left: 20px;}*/
.listmode-tk .lib1 .l-box{width: 95px;height: 95px;border: 1px solid #e0e0e0;overflow: hidden;margin-left: 20px;margin-bottom: 10px;margin-top: 10px;}
.listmode-tk .lib1 .l-box img{width: 90px;height: 90px;vertical-align: top;}
.listmode-tk .lib1 .r-box {width: 290px;padding-top: 0px;}
.listmode-tk .lib1 .r-box span{font-size: 12px;}
.listmode-tk .lib1 .r-box span.title{color: #999;}
.listmode-tk .lib1 .r-box span.info{color: #666;}
.listmode-tk .lib1 .r-box .lib1-row1 a{color: #333;font-weight: bold;}
.listmode-tk .lib1 .r-box .lib1-row1 a:hover{color: #f39800;}
.listmode-tk .lib1 .r-box .lib1-row1 span{color: #333;font-weight: bold;}
.listmode-tk .lib1 .r-box .lib1-row2{margin-top: 20px;}
.listmode-tk .lib1 .r-box .lib1-row3{margin-top: 2px;}
.listmode-tk .lib1 .r-box .lib1-row4{margin-top: 2px;}

.listmode-tk .lib2 span{font-size: 12px;}
.listmode-tk .lib2 .huodong{margin-top: 20px;padding-left: 25px;}
.listmode-tk .lib2 .huodong span{display: inline-block;background: #ff2400;color: #fff;padding: 2px 7px; }
.listmode-tk .lib2 .time{margin-top: 15px;padding-left: 25px;}
.listmode-tk .lib2 .time span{color: #ff2400;}

.listmode-tk .lib3{text-align: center;}
.listmode-tk .lib3 .tkje{color: #666666;margin-top: 15px;font-size: 12px;}
.listmode-tk .lib3 .tkye{margin-top: 5px;font-size: 12px;}



.listmode-tk .lib4{text-align: center;margin-top: 0px;padding-top: 20px;}
.listmode-tk .lib4 .tkshz{color: #f39800;}
.listmode-tk .lib4 .tksuccess{color: #00dc82;}
.listmode-tk .lib4 .tkfail{color:#ff2400;}

.listmode-tk .lib5{padding-top: 30px;text-align: center;}
.listmode-tk .lib5 .suliang{border: 1px solid #e0e0e0;width:120px;height: 32px;overflow: hidden;margin: 0 auto;}
.listmode-tk .lib5 .suliang a.sub{display:block;width: 30px;height: 32px;line-height: 32px;border-right: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.listmode-tk .lib5 .suliang input{display:block;width: 54px;height: 32px;line-height: 32px;margin: 0;padding: 0;border: none;text-align: center;}
.listmode-tk .lib5 .suliang a.add{display:block;width: 30px;height: 32px;line-height: 32px;border-left: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.listmode-tk .lib6{}
.listmode-tk .lib6 a{display:block;margin:0 auto;width: 120px;height: 30px;line-height: 30px;text-align: center;border-radius: 3px;color: #666666;}
.listmode-tk .lib6 a.addbuy{color: #666666;margin-top: 20px;}
.listmode-tk .lib6 a.souc{color: #666666;margin-top: 8px;}

.noshow{display: none;}
/*退款原因说明凭证*/
.tk-why{margin-left:10px;margin-right:10px;padding:10px;overflow: hidden;border-top: 1px dashed #cccccc;}
.tk-why .leftbox{float: left;width: 210px;padding-left: 10px;}
.tk-why .leftbox .sub-l-box{float: left;width: 70px;font-size: 12px;color: #666666;}
.tk-why .leftbox .sub-r-box{float: left;width: 140px;font-size: 12px;color: #999999;}
.tk-why .middlebox{float: left;width: 250px;}
.tk-why .middlebox .sub-l-box{float: left;width: 70px;font-size: 12px;color: #666666;}
.tk-why .middlebox .sub-r-box{float: left;width: 180px;font-size: 12px;color: #999999;}
.tk-why .rightbox{float: left;width: 358px;padding-left: 100px;}
.tk-why .rightbox .sub-l-box{float: left;width: 70px;font-size: 12px;color: #666666;}
.tk-why .rightbox .sub-r-box{float: left;width: 288px;font-size: 12px;color: #999999;}

.tk-why .rightbox .sub-r-box a {
	width: 76px;
	height: 76px;
	display: inline-block;
	border: 1px solid #e0e0e0;
	text-align: center;
	margin: 5px;
	background: url("/static/images/fdj.png") no-repeat 61px 61px;
}
.tk-why .rightbox .sub-r-box a img{width: 44px;height: 66px;margin-top: 5px;}

/*温馨提示*/
.b-infobox{width:810px;margin: 0 auto;line-height:30px;border: 1px solid #ff9948;margin-top: 50px;background: #fff0e4;color: #ff9948;font-size: 15px;border-radius: 3px;margin-left: 300px;padding-top: 10px;padding-bottom: 10px;margin-bottom: 25px;}
.b-infobox p{font-size: 12px;color: #ff9948;padding-left: 40px;}
.b-infobox i{font-size: 24px;margin-left: 15px;position: relative;top:5px;margin-right: 15px;}
.b-infobox span{font-size: 16px;color: #ff9948;font-weight: bold;}

/*订单详情*/
/*套餐商品*/
.taocanbox{border-bottom: 1px solid #e0e0e0;}
.taocanbox .taocantitle{height: 35px;line-height: 35px;border-bottom: 1px solid #e0e0e0;padding-left: 60px;}
.taocanbox .taocanmain{position: relative;margin-bottom: 35px;}
.taocanbox .tcprice{position: absolute;top:50%;right: 56px;font-size: 14px;color: #ff2400;}
/*列表模式*/
.listmode-tc{width:976px;margin: 0 auto;border: 1px solid #e0e0e0; display: block;}
/*表头*/
.listmode-tc .headbox{overflow: hidden;background: #f5f5f5;border-bottom: 1px solid #e0e0e0;}
.listmode-tc .headbox  ul{}
.listmode-tc .headbox  li{float:left;height: 50px;line-height: 50px;text-align: center;}
.listmode-tc .headbox .li0{width: 280px;text-align: left;padding-left: 30px;}
.listmode-tc .headbox .li1{width: 240px;text-align: left;}
/*.listmode .headbox .li2{width: 240px;}*/
.listmode-tc .headbox .li3{width: 160px;}
.listmode-tc .headbox .li4{width: 160px;}
.listmode-tc .headbox .li5{width: 100px;}
/*.listmode .headbox .li6{width: 168px;}*/
/*表体*/
.listmode-tc .bodybox{overflow: hidden;border-bottom: 1px solid #e0e0e0;/*border-left: 1px solid #e0e0e0;border-right: 1px solid #e0e0e0;*/}
/*满减*/
.listmode-tc  .manjianbox{height: 36px;line-height: 36px;border-bottom: 1px solid #e0e0e0;}
.listmode-tc  .manjianbox .title{font-size: 14px;display: inline-block;background: #ff2400;color: #fff;height: 17px;line-height: 17px;padding: 2px 5px;margin-left: 20px;margin-right: 20px;}
.listmode-tc  .manjianbox .info{font-size: 12px;color: #ff2400;}
/*列表*/
.listmode-tc .bodybox  ul{}
.listmode-tc .bodybox  li{float:left;height: 110px;padding-top: 10px;padding-bottom: 10px;}
.listmode-tc .bodybox .lib0{width: 80px;text-align: left;padding-left: 30px;}
.listmode-tc .bodybox .lib1{width: 440px;text-align: left;overflow: hidden;}
/*.listmode .bodybox .lib2{!*width: 240px;*!width: 230px;text-align: center}*/
.listmode-tc .bodybox .lib3{width: 160px;}
.listmode-tc .bodybox .lib4{width: 160px;}
.listmode-tc .bodybox .lib5{width: 100px;}
/*.listmode .bodybox .lib6{!*width: 168px;*!width: 148px;text-align: left;}*/
/*.lib1 .checkb{width:10px;margin-top: 25px; padding-left: 20px;}*/
.listmode-tc .lib1 .l-box{width: 95px;height: 95px;border: 1px solid #e0e0e0;overflow: hidden;margin-left: 20px;padding-top: 0px;padding-bottom: 0px;margin: 0;}
.listmode-tc .lib1 .l-box img{width: 90px;height: 90px;vertical-align: top;}
.listmode-tc .lib1 .r-box {width: 260px;padding-top: 2px;padding-left: 20px;}
.listmode-tc .lib1 .r-box span{font-size: 12px;}
.listmode-tc .lib1 .r-box span.title{color: #999;}
.listmode-tc .lib1 .r-box span.info{color: #666;}
.listmode-tc .lib1 .r-box .lib1-row1 a{color: #333;font-weight: bold;font-size: 12px;}
.listmode-tc .lib1 .r-box .lib1-row1 a:hover{color: #f39800;}
.listmode-tc .lib1 .r-box .lib1-row1 span{color: #333;font-weight: bold;}
.listmode-tc .lib1 .r-box .lib1-row2{margin-top: 10px;}
.listmode-tc .lib1 .r-box .lib1-row3{margin-top: 2px;}
.listmode-tc .lib1 .r-box .lib1-row4{margin-top: 2px;}

.listmode-tc .lib2 span{font-size: 12px;}
.listmode-tc .lib2 .huodong{margin-top: 20px;padding-left: 25px;}
.listmode-tc .lib2 .huodong span{display: inline-block;background: #ff2400;color: #fff;padding: 2px 7px; }
.listmode-tc .lib2 .time{margin-top: 15px;padding-left: 25px;}
.listmode-tc .lib2 .time span{color: #ff2400;}

.listmode-tc .lib3{text-align: center;}
.listmode-tc .lib3 .zkj{color: #ff2400;margin-top: 20px;}
.listmode-tc .lib3 .sjj{margin-top: 15px;}
.listmode-tc .lib3 .sjj span{text-decoration: line-through;color: #999;}
.listmode-tc .lib3 .loginshow{color: #f39800;margin-top: 20px;}
.listmode-tc .lib3 .notbug{color: #ff2400;margin-top: 20px;}

.listmode-tc .lib4{text-align: center;padding-top: 0;}
.listmode-tc .lib4 .zkj{color: #ff2400;padding-bottom: 15px;margin-top: 20px;}
.listmode-tc .lib4 .sjj{color: #999;}
.listmode-tc .lib4 .sjj span{font-size: 12px;}

.listmode-tc .listmode .lib4{padding-top: 0;}
.listmode-tc .listmode .lib5{padding-top: 0;}

.listmode-tc .lib5{text-align: center;padding-top: 0;}
.listmode-tc .lib5 span{margin-top: 30px;display: inline-block;}
.listmode-tc .lib5 .suliang{border: 1px solid #e0e0e0;width:120px;height: 32px;overflow: hidden;margin: 0 auto;}
.listmode-tc .lib5 .suliang a.sub{display:block;width: 30px;height: 32px;line-height: 32px;border-right: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.listmode-tc .lib5 .suliang input{display:block;width: 54px;height: 32px;line-height: 32px;margin: 0;padding: 0;border: none;text-align: center;}
.listmode-tc .lib5 .suliang a.add{display:block;width: 30px;height: 32px;line-height: 32px;border-left: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.listmode-tc .lib6{}
.listmode-tc .lib6 a{display:block;margin:0 auto;width: 120px;height: 30px;line-height: 30px;text-align: center;border-radius: 3px;color: #666666;}
.listmode-tc .lib6 a.addbuy{color: #666666;margin-top: 20px;}
.listmode-tc .lib6 a.souc{color: #666666;margin-top: 8px;}

.listmode-tc .bodybox .lib1 .l-box{position: relative;}
.listmode-tc .bodybox .lib1 .l-box  .bq-box{position: absolute;top:20px;left: 20px;text-align: center;}
.listmode-tc .bodybox .lib1 .l-box  .bq-box img{width: 50px;height: 50px;}

/*我的收藏页面*/
ul.shoucang{width: 956px;display: none;}
.checkbox-warp{position: absolute;top:3px;right: 0;z-index: 999;}
.bodybox1 .lib5{padding-top: 40px;}


/*我的收藏 中包装*/
.colle-zbz{}
.colle-zbz .colle-zbz-top{font-size: 12px;  color: #666666;text-align: center;margin-bottom: 5px;margin-top: 5px;}
.colle-zbz .colle-zbz-foot{width:82px;margin: 0 auto; font-size: 12px;  color: #ffffff;text-align: center;background: #FF6D6D;  border-radius: 15px;}

/*发票选择弹窗*/
#fpxzTc{width: 712px;border-radius: 5px;}
#fpxzTc .modal-body{line-height: 17px;padding: 0;padding-bottom: 20px;}
#fpxzTc .modal-header{border-radius: 5px;}
.sui-modal .modal-body{text-align: left;}
#fpxzTc .xzmain {padding: 20px 20px 20px 20px;}
#fpxzTc .dbinfo{}
#fpxzTc .dbinfo span{font-size: 12px;color: rgba(102,102,102,0.75);}
#fpxzTc .dbinfo span.jg{padding-left: 40px;}

.fptable{width: 100%;margin-top: 17px;}
.fptable tr{}
.fptable tr.head{background: #E8E8E8;}
.fptable tr.odd{background: #F7F7F7;}
.fptable tr.even{background: #E8E8E8;}
.fptable tr td{width: 15%;text-align: center;height: 40px;line-height: 40px; color: #151515;}
.fptable tr td span.wuxiao{color: #ff0e02;}
.fptable tr td span.youxiao{color: #151515;}
.fptable tr td a{text-decoration:underline;color: #6493d4; }



/*收货人信息*/
.receiver_info{ width: 404px; height: 203px;border: 2px solid #F5F5F5;padding:0 30px;margin-bottom: 20px;}
.receiver_info .receiver_info_tit{margin-top: 30px;font-weight: bold;}
.receiver_info .receiver_info_nane{margin-top: 20px;}
.receiver_info .receiver_info_tel{margin-top: 10px;}
.receiver_info .receiver_info_add{margin-top: 10px;line-height: 22px;}
.receiver_info span{display: inline-block;font-size: 14px;vertical-align: top;}
.receiver_info span.l_tit{width: 75px;}
.receiver_info span.r_info{width: 320px;}

/*新版订单详情*/
/*列表模式*/
.listmode-dd {
	width: 976px;
	margin: 0 auto;
	border: 1px solid #e0e0e0;
	display: block;
	border-radius: 6px 6px 0 0;
}
/*表头*/
.listmode-dd .headbox {
	overflow: hidden;
	background: #E3E3E3;
	border-bottom: 1px solid #e0e0e0;
}
.listmode-dd .headbox  ul{}
.listmode-dd .headbox  li{float:left;height: 50px;line-height: 50px;text-align: center;}
/*.listmode-dd .headbox .li0{width: 280px;text-align: left}*/
.listmode-dd .headbox .li1{width: 290px;text-align: left;padding-left: 60px;}
.listmode-dd  .li_mingxi{width: 280px;}
/*.listmode .headbox .li2{width: 240px;}*/
.listmode-dd .headbox .li3{width: 150px;}
.listmode-dd .headbox .li4{width: 100px;}
.listmode-dd .headbox .li5{width: 80px;}
/*.listmode .headbox .li6{width: 168px;}*/
/*表体*/
.listmode-dd .bodybox{overflow: hidden;padding-bottom:20px;border-bottom: 1px solid #e0e0e0;/*border-left: 1px solid #e0e0e0;border-right: 1px solid #e0e0e0;*/}
/*满减*/
.listmode-dd  .manjianbox{height: 36px;line-height: 36px;border-bottom: 1px solid #e0e0e0;}
.listmode-dd  .manjianbox .title{font-size: 14px;display: inline-block;background: #ff2400;color: #fff;height: 17px;line-height: 17px;padding: 2px 5px;margin-left: 20px;margin-right: 20px;}
.listmode-dd  .manjianbox .info{font-size: 12px;color: #ff2400;}
/*列表*/
.listmode-dd .bodybox  ul{}
.listmode-dd .bodybox  li{float:left;height: 110px;padding-top: 10px;padding-bottom: 10px;}
/*.listmode-dd .bodybox .lib0{width: 80px;text-align: left;padding-left: 30px;}*/
.listmode-dd .bodybox .lib1{width: 350px;text-align: left;overflow: hidden;}
/*.listmode .bodybox .lib2{!*width: 240px;*!width: 230px;text-align: center}*/
.listmode-dd .bodybox .lib3{width: 150px;}
.listmode-dd .bodybox .lib4{width: 100px;}
.listmode-dd .bodybox .lib5{width: 80px;}
/*.listmode .bodybox .lib6{!*width: 168px;*!width: 148px;text-align: left;}*/
/*.lib1 .checkb{width:10px;margin-top: 25px; padding-left: 20px;}*/
.listmode-dd .lib1 .l-box{width: 95px;height: 95px;border: 1px solid #e0e0e0;overflow: hidden;margin-left: 20px;padding-top: 0px;padding-bottom: 0px;}
.listmode-dd .lib1 .l-box img{width: 90px;height: 90px;vertical-align: top;}
.listmode-dd .lib1 .r-box {width: 168px;padding-top: 2px;/*padding-left: 20px;*/}
.listmode-dd .lib1 .r-box span{font-size: 12px;}
.listmode-dd .lib1 .r-box span.title{color: #999;}
.listmode-dd .lib1 .r-box span.info{color: #666;}
.listmode-dd .lib1 .r-box .lib1-row1{display: block;}
.listmode-dd .lib1 .r-box .lib1-row1 a{color: #333;font-weight: bold;font-size: 12px;}
.listmode-dd .lib1 .r-box .lib1-row1 a:hover{color: #f39800;}
.listmode-dd .lib1 .r-box .lib1-row1 span{color: #333;font-weight: bold;}
.listmode-dd .lib1 .r-box .lib1-row2{margin-top: 2px;}
.listmode-dd .lib1 .r-box .lib1-row3{margin-top: 2px;}
.listmode-dd .lib1 .r-box .lib1-row4{margin-top: 2px;}

.listmode-dd .lib2 span{font-size: 12px;}
.listmode-dd .lib2 .huodong{margin-top: 20px;padding-left: 25px;}
.listmode-dd .lib2 .huodong span{display: inline-block;background: #ff2400;color: #fff;padding: 2px 7px; }
.listmode-dd .lib2 .time{margin-top: 15px;padding-left: 25px;}
.listmode-dd .lib2 .time span{color: #ff2400;}

.listmode-dd .lib3{text-align: center;}
.listmode-dd .lib3 .zkj{color: #ff2400;margin-top: 20px;}
.listmode-dd .lib3 .sjj{margin-top: 15px;}
.listmode-dd .lib3 .sjj span{text-decoration: line-through;color: #999;}
.listmode-dd .lib3 .loginshow{color: #f39800;margin-top: 20px;}
.listmode-dd .lib3 .notbug{color: #ff2400;margin-top: 20px;}

.listmode-dd .lib4{text-align: center;padding-top: 0;}
.listmode-dd .lib4 .zkj{color: #ff2400;padding-bottom: 15px;margin-top: 30px;}
.listmode-dd .lib4 .sjj{color: #999;}
.listmode-dd .lib4 .sjj span{font-size: 12px;}

.listmode-dd .listmode .lib4{padding-top: 0;}
.listmode-dd .listmode .lib5{padding-top: 0;}

.listmode-dd .lib5{text-align: center;padding-top: 0;}
.listmode-dd .lib5 span{margin-top: 30px;display: inline-block;}
.listmode-dd .lib5 .suliang{border: 1px solid #e0e0e0;width:120px;height: 32px;overflow: hidden;margin: 0 auto;}
.listmode-dd .lib5 .suliang a.sub{display:block;width: 30px;height: 32px;line-height: 32px;border-right: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.listmode-dd .lib5 .suliang input{display:block;width: 54px;height: 32px;line-height: 32px;margin: 0;padding: 0;border: none;text-align: center;}
.listmode-dd .lib5 .suliang a.add{display:block;width: 30px;height: 32px;line-height: 32px;border-left: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.listmode-dd .lib6{}
.listmode-dd .lib6 a{display:block;margin:0 auto;width: 120px;height: 30px;line-height: 30px;text-align: center;border-radius: 3px;color: #666666;}
.listmode-dd .lib6 a.addbuy{color: #666666;margin-top: 20px;}
.listmode-dd .lib6 a.souc{color: #666666;margin-top: 8px;}

.listmode-dd .bodybox .lib1 .l-box{position: relative;}
.listmode-dd .bodybox .lib1 .l-box  .bq-box{position: absolute;top:20px;left: 20px;text-align: center;}
.listmode-dd .bodybox .lib1 .l-box  .bq-box img{width: 50px;height: 50px;}

.listmode-dd .taocanbox .tcprice{position: absolute;top:50%;right: 34px;font-size: 14px;color: #ff2400;}

/*商品明细*/
.li_mingxi .mxrow1{margin-top: 20px;}

/* .li_mingxi .mxrow2{margin-top: 10px;} */
.li_mingxi span{font-size: 12px;display: inline-block;}

.li_mingxi .mxrow_tit {
	width: 150px;
	text-align: right;
}
.li_mingxi .mxrow_info{width: 70px;}
.li_mingxi .head-tit{font-size: 14px;padding-left: 0px;}

/*我的订单提示*/
.orderwait{height: 35px;line-height: 35px;margin-bottom: 20px;color: #f39800;display: block;}

/* 字体图标样式 */
.side-left .list .item span {
	margin-right: 10px;
	font-size: 18px;
	color: #CFCFCF;
}

.side-left .list .item .icon-shouhouguize, .side-left .list .item .icon-xinyuandan {
	font-size: 16px;
}
/* 自营企业 */
.main-right .tab-cont .ziying{
	background-color: #00DC82;
	border-radius: 2px;
	color: #ffffff;
	font-size: 12px;
	padding: 2px 4px;
	margin-right: 5px;
}
.main-right .tab-cont .cgd-qy{
	height: 40px;
	line-height: 40px;
	background: #fff;
	border-bottom: 1px solid #e0e0e0;
	padding-left: 15px;
	display: flex;
	padding-right: 90px;
}
.main-right .tab-cont .cgd-qy .opt-refund-btn {
	color: #00dc82;
	text-decoration: none;
}
.main-right .tab-cont .cgd-qy .qy-title{
	font-size: 16px;
	color: #666666;
}

.side-left .list .item i{
	position: absolute;
	top: 10px;
	left: 133px;
	width: 5px;
	height: 5px;
	border-radius: 50%;
	background-color: #FF3B2F;
}
.orderBtnStyle {
	border-radius: 4px;
	border: 1px solid red;
	height: 30px;
	line-height: 30px;
	color: red;
	text-align: center;
	padding: 0 10px;
	font-size: 14px;
	margin-right: 10px;
}
.green {
	border: 1px solid #00dc82;
	color: #00dc82;
}