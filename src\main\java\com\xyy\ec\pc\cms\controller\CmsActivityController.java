package com.xyy.ec.pc.cms.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.ecp.api.RuleExhibitionGroupBusinessApi;
import com.xyy.ec.layout.buinese.ecp.enums.CmsEventTrackingSpTypeEnum;
import com.xyy.ec.layout.buinese.ecp.enums.exhibition.ExhibitionSortStrategyEnum;
import com.xyy.ec.layout.buinese.ecp.params.CmsEventTrackingSpIdGenerateParam;
import com.xyy.ec.layout.buinese.ecp.params.RuleExhibitionGroupSimpleAggregateQueryParam;
import com.xyy.ec.layout.buinese.ecp.params.RuleExhibitionQueryParam;
import com.xyy.ec.layout.buinese.ecp.results.RuleExhibitionGroupSimpleAggregateInfoDTO;
import com.xyy.ec.layout.buinese.ecp.utils.CmsEventTrackingUtils;
import com.xyy.ec.layout.buinese.ecp.utils.RuleExhibitionUtil;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingSkuDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.enums.MerchantLicenseEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.cms.constants.CmsConstants;
import com.xyy.ec.pc.cms.dto.CmsGroupBuyingSubjectHeadImageDailyPeriodDTO;
import com.xyy.ec.pc.cms.dto.CmsListProductDto;
import com.xyy.ec.pc.cms.dto.CmsRequestDTO;
import com.xyy.ec.pc.cms.dto.GroupBuyingProductCategoryDTO;
import com.xyy.ec.pc.cms.enums.CmsCategoryFlowSortTypeEnum;
import com.xyy.ec.pc.cms.enums.CmsGroupBuyingQueryTypeEnum;
import com.xyy.ec.pc.cms.helpers.*;
import com.xyy.ec.pc.cms.param.CmsGroupBuyingProductQueryParam;
import com.xyy.ec.pc.cms.param.CmsProductQueryParam;
import com.xyy.ec.pc.cms.param.CmsRequestParam;
import com.xyy.ec.pc.cms.param.CmsSpecifiedProductQueryParam;
import com.xyy.ec.pc.cms.service.AppLayoutService;
import com.xyy.ec.pc.cms.service.CmsSkuService;
import com.xyy.ec.pc.cms.utils.FeedParamUtils;
import com.xyy.ec.pc.cms.utils.LayoutBranchUtils;
import com.xyy.ec.pc.cms.utils.LayoutProductUtils;
import com.xyy.ec.pc.cms.vo.*;
import com.xyy.ec.pc.common.enums.AppEventTrackingPropertyKeyEnum;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.enums.LayoutMerchantStatusEnum;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.helper.ProductDataFilterHelper;
import com.xyy.ec.pc.helper.TrackDataHelper;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.remote.MerchantBusinessRemoteService;
import com.xyy.ec.pc.remote.ProductExhibitionGroupBusinessAdminRemoteService;
import com.xyy.ec.pc.remote.ProductForLayoutRemoteService;
import com.xyy.ec.pc.remote.ShopQueryRemoteService;
import com.xyy.ec.pc.rpc.ProductServiceRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.layout.LayoutBaseService;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.pc.util.ProductMangeUtils;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.search.engine.api.EcSearchApi;
import com.xyy.ec.search.engine.api.EcSearchForLayoutApi;
import com.xyy.ec.search.engine.dto.SearchCsuDTO;
import com.xyy.ec.search.engine.dto.layout.EcSearchSimpleAggregateInfoDTO;
import com.xyy.ec.search.engine.entity.CsuInfo;
import com.xyy.ec.search.engine.entity.EcProductVo;
import com.xyy.ec.search.engine.enums.CsuOrder;
import com.xyy.ec.search.engine.enums.EcRankType;
import com.xyy.ec.search.engine.enums.SortOrder;
import com.xyy.ec.search.engine.metadata.IPage;
import com.xyy.ec.search.engine.pagination.Page;
import com.xyy.ec.search.engine.params.layout.EcSearchSimpleAggregateLayoutQueryParam;
import com.xyy.track.dto.TrackDataEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.net.URL;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cms/activity/")
@Slf4j
public class CmsActivityController extends BaseController {

    @Autowired
    private CmsSkuService cmsSkuService;

    @Autowired
    private LayoutBaseService layoutBaseService;

    @Autowired
    private LayoutBranchUtils layoutBranchUtils;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private CmsAppProperties cmsAppProperties;

    @Autowired
    private MerchantBusinessRemoteService merchantBusinessRemoteService;

    @Reference(version = "1.0.0")
    private EcSearchApi searchApi;

    @Autowired
    private ProductForLayoutRemoteService productForLayoutRemoteService;

    @Autowired
    private ShopQueryRemoteService shopQueryRemoteService;

    @Autowired
    private PcVersionUtils pcVersionUtils;

    @Autowired
    private ProductExhibitionGroupBusinessAdminRemoteService productExhibitionGroupBusinessAdminRemoteService;

    @Autowired
    private ProductServiceRpc productServiceRpc;

    @Autowired
    private FeedParamUtils feedParamUtils;

    @Reference(version = "1.0.0")
    private RuleExhibitionGroupBusinessApi ruleExhibitionGroupBusinessApi;

    @Reference(version = "1.0.0")
    private EcSearchForLayoutApi ecSearchForLayoutApi;

    @Reference(version = "1.0.0")
    private EcSearchApi ecSearchApi;

    @Autowired
    private AppLayoutService appLayoutService;

    @Autowired
    private AppProperties appProperties;

    /**
     * 获取商品流聚合额外信息：分类列表、商家列表。
     *
     * @param terminalType
     * @param version
     * @param cmsRequestParam
     * @param httpServletRequest
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getProductFlowAggExtraInfo", method = RequestMethod.POST)
    public XyyJsonResult getProductFlowAggExtraInfo(@RequestHeader(value = "terminalType", required = false) Integer terminalType,
                                                    @RequestHeader(value = "version", required = false) Integer version,
                                                    CmsRequestParam cmsRequestParam, HttpServletRequest httpServletRequest) {
        Long merchantId = null;
        try {
            boolean isAdmin = layoutBaseService.judgeRequestIsFromCmsAdmin(httpServletRequest);
            if (BooleanUtils.isTrue(isAdmin)) {
                // 管理侧
                merchantId = appLayoutService.getNoLoginLogicMerchantId(merchantId);
            } else {
                MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
                if (merchant == null) {
                    return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR).msg("请登录");
                }
                merchantId = merchant.getId();
            }
            CmsRequestDTO cmsRequestDTO = CmsRequestHelper.createDTO(cmsRequestParam);
            if (cmsRequestDTO == null) {
                cmsRequestDTO = new CmsRequestDTO();
            }
            String exhibitionId = cmsRequestDTO.getExhibitionId();
            /* 查询 */
            List<CmsProductFlowAggCategoryVO> categoryRows = null;
            List<CmsProductFlowAggShopVO> shopRows = null;
            if (BooleanUtils.isTrue(RuleExhibitionUtil.isRuleExhibition(exhibitionId)) && cmsAppProperties.getUseRuleExhibitionSwitch()) {
                RuleExhibitionGroupSimpleAggregateQueryParam queryParam = RuleExhibitionGroupSimpleAggregateQueryParam.builder()
                        .merchantId(merchantId).exhibitionIds(Lists.newArrayList(exhibitionId)).build();
                ApiRPCResult<RuleExhibitionGroupSimpleAggregateInfoDTO> apiRPCResult = ruleExhibitionGroupBusinessApi.simpleAggregateSearch(queryParam);
                if (log.isDebugEnabled()) {
                    log.debug("获取商品流聚合额外信息，调用ruleExhibitionGroupBusinessApi.simpleAggregateSearch，queryParam：{}，apiRPCResult：{}",
                            JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
                }
                if (apiRPCResult.isFail()) {
                    log.error("获取商品流聚合额外信息失败，调用ruleExhibitionGroupBusinessApi.simpleAggregateSearch失败，queryParam：{}，apiRPCResult：{}",
                            JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
                    return XyyJsonResult.createFailure().msg("获取导航分类和商家信息失败，请稍后重试");
                }
                RuleExhibitionGroupSimpleAggregateInfoDTO data = apiRPCResult.getData();
                if (Objects.nonNull(data)) {
                    categoryRows = CmsProductFlowAggCategoryVOHelper.createsForLayout(data.getCsuCategories());
                    shopRows = CmsProductFlowAggShopVOHelper.createsForLayout(data.getShops());
                }
            } else {
                EcSearchSimpleAggregateLayoutQueryParam queryParam = EcSearchSimpleAggregateLayoutQueryParam.builder()
                        .merchantId(merchantId).tagList(Lists.newArrayList(exhibitionId)).build();
                ApiRPCResult<EcSearchSimpleAggregateInfoDTO> apiRPCResult = ecSearchForLayoutApi.simpleAggregateSearch(queryParam);
                if (log.isDebugEnabled()) {
                    log.debug("获取商品流聚合额外信息，调用ecSearchForLayoutApi.simpleAggregateSearch，queryParam：{}，apiRPCResult：{}",
                            JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
                }
                if (apiRPCResult.isFail()) {
                    log.error("获取商品流聚合额外信息失败，调用ecSearchForLayoutApi.simpleAggregateSearch失败，queryParam：{}，apiRPCResult：{}",
                            JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
                    return XyyJsonResult.createFailure().msg("获取导航分类和商家信息失败，请稍后重试");
                }
                // 商品组探活埋点
                productExhibitionGroupBusinessAdminRemoteService.asyncSendExhibitionLiveEventMQForSearch(exhibitionId);
                EcSearchSimpleAggregateInfoDTO data = apiRPCResult.getData();
                if (Objects.nonNull(data)) {
                    categoryRows = CmsProductFlowAggCategoryVOHelper.createsForSearch(data.getCsuCategories());
                    shopRows = CmsProductFlowAggShopVOHelper.createsForSearch(data.getShops());
                }
            }
            /* 组装响应数据 */
            return XyyJsonResult.createSuccess()
                    .addResult("categoryRows", categoryRows)
                    .addResult("shopRows", shopRows);
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("获取商品流聚合额外信息失败，merchantId：{}，terminalType：{}，version：{}，cmsRequestParam：{}，msg：{}，异常信息：",
                        merchantId, terminalType, version, JSONObject.toJSONString(cmsRequestParam), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("获取商品流聚合额外信息失败，merchantId：{}，terminalType：{}，version：{}，cmsRequestParam：{}，异常信息：",
                    merchantId, terminalType, version, JSONObject.toJSONString(cmsRequestParam), e);
            return XyyJsonResult.createFailure().msg("获取导航分类和商家信息失败，请稍后重试");
        }
    }

    /**
     * 获取品类商品流商品信息列表
     *
     * @param terminalType
     * @param version
     * @param cmsRequestParam
     * @param httpServletRequest
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/listCategoryFlowProductInfos", method = RequestMethod.POST)
    public XyyJsonResult listCategoryFlowProductInfos(@RequestHeader(value = "terminalType", required = false) Integer terminalType,
                                                      @RequestHeader(value = "version", required = false) Integer version,
                                                      CmsRequestParam cmsRequestParam, HttpServletRequest httpServletRequest) {
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant;
            boolean isAdmin = layoutBaseService.judgeRequestIsFromCmsAdmin(httpServletRequest);
            if (BooleanUtils.isTrue(isAdmin)) {
                // 管理侧
                merchantId = appLayoutService.getNoLoginLogicMerchantId(merchantId);
                merchant = merchantBusinessRemoteService.getMerchantById(merchantId);
            } else {
                merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            }
            if (merchant == null) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR).msg("请登录");
            }
            merchantId = merchant.getId();
            CmsRequestDTO cmsRequestDTO = CmsRequestHelper.createDTO(cmsRequestParam);
            if (cmsRequestDTO == null) {
                cmsRequestDTO = new CmsRequestDTO();
            }
            Integer pageNum = Optional.ofNullable(cmsRequestDTO.getPageNum()).orElse(1);
            Integer pageSize = Optional.ofNullable(cmsRequestDTO.getPageSize()).orElse(10);
            cmsRequestDTO.setPageNum(pageNum);
            cmsRequestDTO.setPageSize(pageSize);
            String exhibitionId = cmsRequestDTO.getExhibitionId();
            Long categoryId = cmsRequestDTO.getCategoryId();
            String shopCodes = cmsRequestDTO.getShopCodes();
            String anchorCsuIds = cmsRequestDTO.getAnchorCsuIds();
            Integer sortType = cmsRequestDTO.getSortType();
            sortType = Optional.ofNullable(sortType).orElse(CmsCategoryFlowSortTypeEnum.CHOICENESS.getType());
            cmsRequestDTO.setSortType(sortType);
            if (Objects.equals(categoryId, CmsConstants.CATEGORY_FLOW_CATEGORY_ID_ALL)) {
                categoryId = null;
            }
            // 支持店铺搜索
            Set<String> shopCodesSet = null;
            if (StringUtils.isNotEmpty(shopCodes)) {
                shopCodesSet = Sets.newHashSet(shopCodes.split(","));
            }
            // 锚点商品
            Set<Long> anchorCsuIdsSet = null;
            if (StringUtils.isNotEmpty(anchorCsuIds)) {
                anchorCsuIdsSet = Sets.newHashSet(anchorCsuIds.split(","))
                        .stream().map(item -> {
                            if (StringUtils.isEmpty(item)) {
                                return null;
                            }
                            try {
                                return Long.parseLong(item.trim());
                            } catch (Exception e) {
                                return null;
                            }
                        }).filter(Objects::nonNull).collect(Collectors.toSet());
            }
            /* 查询 */
            PageInfo<Long> pageInfo = new PageInfo<>();
            pageInfo.setPageNum(pageNum);
            pageInfo.setPageSize(pageSize);
            pageInfo.setPages(0);
            pageInfo.setTotal(0L);
            pageInfo.setList(Lists.newArrayList());
            if (BooleanUtils.isTrue(RuleExhibitionUtil.isRuleExhibition(exhibitionId)) && cmsAppProperties.getUseRuleExhibitionSwitch()) {
                Integer sortStrategy;
                if (Objects.equals(sortType, CmsCategoryFlowSortTypeEnum.HOT.getType())) {
                    sortStrategy = ExhibitionSortStrategyEnum.ORDER_USER_SORT_FIRST.getSortType();
                } else {
                    sortStrategy = ExhibitionSortStrategyEnum.NORMAL.getSortType();
                }
                List<Long> excludeSkuIdList = null;
                if (CollectionUtils.isNotEmpty(anchorCsuIdsSet)) {
                    excludeSkuIdList = Lists.newArrayList(anchorCsuIdsSet);
                }
                RuleExhibitionQueryParam param = RuleExhibitionQueryParam.builder().merchantId(merchantId)
                        .exhibitionIdList(Lists.newArrayList(exhibitionId)).categoryFirstId(categoryId)
                        .excludeSkuIdList(excludeSkuIdList).sortStrategy(sortStrategy).pageNum(pageNum).pageSize(pageSize).build();
                ApiRPCResult<PageInfo<Long>> apiRPCResult = ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProductIds(param);
                if (log.isDebugEnabled()) {
                    log.debug("获取品类商品流商品信息列表，调用ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProductIds，入参：{}，apiRPCResult：{}", JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
                }
                if (apiRPCResult.isFail()) {
                    log.error("获取品类商品流商品信息列表，调用ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProductIds失败，入参：{}，apiRPCResult：{}", JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
                    return XyyJsonResult.createFailure().msg("获取商品信息列表失败，请稍后重试");
                }
                PageInfo<Long> data = apiRPCResult.getData();
                if (Objects.nonNull(data)) {
                    pageInfo.setPageNum(data.getPageNum());
                    pageInfo.setPageSize(data.getPageSize());
                    pageInfo.setPages(data.getPages());
                    pageInfo.setTotal(data.getTotal());
                    pageInfo.setList(Optional.ofNullable(data.getList()).orElse(Lists.newArrayList()));
                }
            } else {
                SearchCsuDTO searchCsuDTO = new SearchCsuDTO();
                searchCsuDTO.setMerchantId(merchantId);
                searchCsuDTO.setBranchCode(merchant.getRegisterCode());
                //判断是否KA用户，如果是KA用户则只查询有连锁指导价的商品
                if (merchant != null && merchant.getIsKa()) {
                    searchCsuDTO.setHasGuidePrice(true);
                }
                searchCsuDTO.setTagList(exhibitionId);
                if (CollectionUtils.isNotEmpty(shopCodesSet)) {
                    searchCsuDTO.setShopCodes(Lists.newArrayList(shopCodesSet));
                }
                if (CollectionUtils.isNotEmpty(anchorCsuIdsSet)) {
                    searchCsuDTO.setExcludeIds(anchorCsuIdsSet.stream().map(String::valueOf).collect(Collectors.toList()));
                }
                searchCsuDTO.setCategoryId(categoryId);
                searchCsuDTO.setIsNotApplyNoResultSecondRecall(true);
                searchCsuDTO.setRankType(com.xyy.ec.data.enums.EcRankType.EC_RANK.getValue());
                if (Objects.equals(sortType, CmsCategoryFlowSortTypeEnum.HOT.getType())) {
                    searchCsuDTO.setCsuOrder(CsuOrder.SALE);
                } else {
                    searchCsuDTO.setCsuOrder(CsuOrder.DEFAULT);
                }
                searchCsuDTO.setSortOrder(SortOrder.DESC);
                com.xyy.ec.search.engine.pagination.Page page = new com.xyy.ec.search.engine.pagination.Page(pageNum, pageSize);
                searchCsuDTO.setPageNum(pageNum);
                searchCsuDTO.setPageSize(pageSize);
                ApiRPCResult<IPage<EcProductVo>> apiRPCResult = ecSearchApi.searchListForPC(page, searchCsuDTO);
                if (log.isDebugEnabled()) {
                    log.debug("获取品类商品流商品信息列表，调用ecSearchApi.searchListForApp，入参：{}，page：{}，apiRPCResult：{}", JSONObject.toJSONString(searchCsuDTO), JSONObject.toJSONString(page), JSONObject.toJSONString(apiRPCResult));
                }
                if (apiRPCResult.isFail()) {
                    log.error("获取品类商品流商品信息列表，调用ecSearchApi.searchListForApp失败，入参：{}，page：{}，apiRPCResult：{}", JSONObject.toJSONString(searchCsuDTO), JSONObject.toJSONString(page), JSONObject.toJSONString(apiRPCResult));
                    return XyyJsonResult.createFailure().msg("获取商品信息列表失败，请稍后重试");
                }
                IPage<EcProductVo> data = apiRPCResult.getData();
                if (Objects.nonNull(data)) {
                    pageInfo.setPageNum((int) data.getPageNo());
                    pageInfo.setPageSize((int) data.getPageSize());
                    pageInfo.setPages((int) data.getPages());
                    pageInfo.setTotal(data.getTotalCount());
                    pageInfo.setList(Optional.ofNullable(data.getRecordList()).orElse(Lists.newArrayList()).stream().map(EcProductVo::getId).collect(Collectors.toList()));
                }
                // 商品组探活埋点
                productExhibitionGroupBusinessAdminRemoteService.asyncSendExhibitionLiveEventMQForSearch(searchCsuDTO);
            }
            Long count = pageInfo.getTotal();
            boolean isEnd = pageNum * pageSize >= count;
            // 填充商品信息
            boolean isNotWatchFragileGoods = merchantBusinessRemoteService.checkIsNotWatchFragileGoods(merchantId);
            List<ListProduct> products = productForLayoutRemoteService.fillProductInfo(pageInfo.getList(), merchantId, isNotWatchFragileGoods, null);
            products = products.stream().filter(item -> Objects.nonNull(item) && LayoutProductUtils.isOnSale(item.getStatus()) && LayoutProductUtils.isNotProductControl(item.getControlType()))
                    .collect(Collectors.toList());

            /* 资质处理 */
            Integer licenseStatus = layoutBaseService.getLicenseStatus(merchant);
            LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
            if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                products = layoutBaseService.setProductProperties(products, statusEnum, httpServletRequest);
            }
            /* 填充其他信息 */
            List<CmsListProductVO> cmsListProductVOS = CmsListProductVOHelper.creates(products);
            if (CollectionUtils.isNotEmpty(cmsListProductVOS)) {
                // 获取拼团或批购包邮活动信息
                List<Long> csuIds = cmsListProductVOS.stream().filter(Objects::nonNull).map(CmsListProductVO::getId).filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(csuIds)) {
                    Set<Long> gaoMaoSkuIdSet = Sets.newHashSet();
                    gaoMaoSkuIdSet = cmsListProductVOS.stream().filter(productDto -> {
                        if(null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                                || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))){
                            return true;
                        }
                        return false;
                    }).map(CmsListProductVO :: getId).collect(Collectors.toSet());

                    Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap = cmsSkuService.getMarketingActivityInfoBySkuIdList(csuIds,
                            Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()), merchantId,
                            Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
                    // 填充拼团或批购包邮活动信息
                    cmsListProductVOS = cmsSkuService.fillListProductsMarketingActivityInfo(cmsListProductVOS, csuIdToGroupBuyingInfoMap);
                }
                cmsListProductVOS = cmsSkuService.fillListProductsShopInfo(cmsListProductVOS);
                cmsListProductVOS = cmsSkuService.fillListProductsTagInfo(merchantId, cmsListProductVOS, true);
                //填充库存信息
                cmsListProductVOS = productServiceRpc.fillCmsActTotalSurplusQtyToAvailableQty(cmsListProductVOS);
            }
            /* 埋点处理 */
            cmsRequestDTO = feedParamUtils.handleCmsRequestParam(cmsRequestDTO);
            CmsEventTrackingSpTypeEnum spTypeEnum = CmsEventTrackingSpTypeEnum.H5_SUBJECT;
            String spType = spTypeEnum.getSpType();
            if (StringUtils.isEmpty(cmsRequestDTO.getSptype())) {
                cmsRequestDTO.setSptype(spType);
            }
            if (StringUtils.isEmpty(cmsRequestDTO.getSpid())) {
                String spId = CmsEventTrackingUtils.generateSpId(CmsEventTrackingSpIdGenerateParam.builder()
                        .spTypeEnum(spTypeEnum).pageId(cmsRequestDTO.getPageId())
                        .pageTitle(cmsRequestDTO.getPageTitle()).build());
                cmsRequestDTO.setSpid(spId);
            }
            if (StringUtils.isEmpty(cmsRequestDTO.getSid())) {
                String sid = SearchUtils.generateNewSidData(merchantId, terminalType);
                cmsRequestDTO.setSid(sid);
            }

            //qt 埋点
            if(CollectionUtils.isNotEmpty(cmsListProductVOS)){
                TrackDataEntity trackData = TrackDataHelper.buildScmETrackData(cmsRequestParam.getScmE());
                cmsListProductVOS.forEach(item -> {
                    item.setTrackData(trackData);
                });
                //首次请求，将scmE埋点赋值给请求参数
                if(cmsRequestDTO.getScmE() == null){
                    cmsRequestDTO.setScmE(trackData.getScmEntity().getScmE());
                }
            }

            CmsRequestParam resultCmsRequestParam = CmsRequestHelper.createParam(cmsRequestDTO);

            /* 组装响应数据 */
            return XyyJsonResult.createSuccess()
                    .addResult("rows", cmsListProductVOS)
                    .addResult("cmsPageParam", resultCmsRequestParam)
                    .addResult("licenseStatus", licenseStatus)
                    .addResult("isEnd", isEnd)
                    .addResult("pageNo", pageInfo.getPageNum())
                    .addResult("pageSize", pageInfo.getPageSize())
                    .addResult("totalPage", pageInfo.getPages())
                    .addResult("totalCount", pageInfo.getTotal())
                    .addResult(AppEventTrackingPropertyKeyEnum.SPTYPE.getPropertyKey(), resultCmsRequestParam.getSptype())
                    .addResult(AppEventTrackingPropertyKeyEnum.SPID.getPropertyKey(), resultCmsRequestParam.getSpid())
                    .addResult(AppEventTrackingPropertyKeyEnum.SID.getPropertyKey(), resultCmsRequestParam.getSid());
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("获取品类商品流商品信息列表失败，merchantId：{}，terminalType：{}，version：{}，cmsRequestParam：{}，msg：{}，异常信息：",
                        merchantId, terminalType, version, JSONObject.toJSONString(cmsRequestParam), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("获取品类商品流商品信息列表失败，merchantId：{}，terminalType：{}，version：{}，cmsRequestParam：{}，异常信息：",
                    merchantId, terminalType, version, JSONObject.toJSONString(cmsRequestParam), e);
            return XyyJsonResult.createFailure().msg("获取商品信息列表失败，请稍后重试");
        }
    }

    /**
     * 拼团专题页
     *
     * @param cmsGroupBuyingProductQueryParam
     * @param request
     * @return
     */
    @RequestMapping(value = "/groupBuyingActivity.htm", method = {RequestMethod.GET, RequestMethod.POST})
    public ModelAndView groupBuyingActivity(CmsGroupBuyingProductQueryParam cmsGroupBuyingProductQueryParam, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (log.isDebugEnabled()) {
                log.debug("【拼团专题页】当前登录用户信息，merchant：{}", JSONObject.toJSONString(merchant));
            }
            if (merchant == null) {
                // 拼团专题页需要登录
                log.info("【拼团专题页】用户未登录，跳转至登录页面，引导用户登录");
                return new ModelAndView(new RedirectView("/login/login.htm", true, false));
            }
            Long merchantId = merchant.getId();
            ModelAndView modelAndView = new ModelAndView("activityEvent/pinActivity.ftl");
            // 查询参数
            if (cmsGroupBuyingProductQueryParam == null) {
                cmsGroupBuyingProductQueryParam = new CmsGroupBuyingProductQueryParam();
            }
            Integer type = Optional.ofNullable(cmsGroupBuyingProductQueryParam.getType()).orElse(CmsGroupBuyingQueryTypeEnum.IN_PROGRESS.getType());
            Long categoryId = Optional.ofNullable(cmsGroupBuyingProductQueryParam.getCategoryId()).orElse(CmsConstants.GROUP_BUYING_PRODUCT_CATEGORY_ID_ALL);
            Integer pageNum = Optional.ofNullable(cmsGroupBuyingProductQueryParam.getOffset()).orElse(1);
            Integer pageSize = Optional.ofNullable(cmsGroupBuyingProductQueryParam.getLimit()).orElse(20);
            cmsGroupBuyingProductQueryParam.setType(type);
            cmsGroupBuyingProductQueryParam.setCategoryId(categoryId);
            cmsGroupBuyingProductQueryParam.setOffset(pageNum);
            cmsGroupBuyingProductQueryParam.setLimit(pageSize);
            // 商品分类数据
            List<GroupBuyingProductCategoryDTO> groupBuyingProductCategoryList = cmsAppProperties.getGroupBuyingProductCategoryList();
            // 商品数据
            // 分页要用，将url传到前台
            String url = getRequestUrl(request);
            com.xyy.ec.pc.base.Page<CmsGroupBuyingProductVO> blankPageInfo = new com.xyy.ec.pc.base.Page<>();
            blankPageInfo.setOffset(pageNum);
            blankPageInfo.setLimit(pageSize);
            blankPageInfo.setRows(Lists.newArrayList());
            blankPageInfo.setTotal(0L);
            blankPageInfo.setRequestUrl(url);
            String cmsGroupBuyingSubjectHeadImageUrl = this.getCmsGroupBuyingSubjectHeadImageUrl();
            if (log.isDebugEnabled()) {
                log.debug("PC拼团专题页头图：{}", cmsGroupBuyingSubjectHeadImageUrl);
            }
            modelAndView.addObject("merchant", merchant);
            modelAndView.addObject("merchantId", merchantId);
            modelAndView.addObject("type", type);
            modelAndView.addObject("categoryId", categoryId);
            modelAndView.addObject("offset", pageNum);
            modelAndView.addObject("limit", pageSize);
            modelAndView.addObject("categoryList", groupBuyingProductCategoryList);
            modelAndView.addObject("pager", blankPageInfo);
            modelAndView.addObject("headImageUrl", cmsGroupBuyingSubjectHeadImageUrl);
            // 资质状态
            MerchantBussinessDto merchantBussinessDto = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            boolean priceDisplayFlag = merchantBussinessDto.getPriceDisplayFlag();
            if (BooleanUtils.isNotTrue(priceDisplayFlag)) {
                log.info("【拼团专题页】此会员ID资质未认证，直接返回空集合。参数，merchantId：{}", merchantId);
                return modelAndView;
            }
            Long productCategoryId = cmsGroupBuyingProductQueryParam.getCategoryId();
            boolean isNotWatchFragileGoods = merchantBusinessRemoteService.checkIsNotWatchFragileGoods(merchantId);
            Integer isShowFragileGoods;
            if (BooleanUtils.isTrue(isNotWatchFragileGoods)) {
                isShowFragileGoods = 0;
            } else {
                isShowFragileGoods = 1;
            }
            // 默认展示进行中
            String exhibitionId = "";
            if (Objects.equals(type, CmsGroupBuyingQueryTypeEnum.IN_PROGRESS.getType())) {
                exhibitionId = CmsConstants.EXHIBITION_ID_STR_GROUP_BUYING_IN_PROGRESS;
            } else {
                exhibitionId = CmsConstants.EXHIBITION_ID_STR_GROUP_BUYING_NOT_START;
            }
            SearchCsuDTO searchCsuDTO = new SearchCsuDTO();
            searchCsuDTO.setMerchantId(merchantId);
            searchCsuDTO.setIsShowFragileGoods(isShowFragileGoods);
            searchCsuDTO.setTagList(exhibitionId);
            if (!Objects.equals(productCategoryId, CmsConstants.GROUP_BUYING_PRODUCT_CATEGORY_ID_ALL)) {
                Map<Long, GroupBuyingProductCategoryDTO> logicCategoryIdToGroupBuyingProductCategoryMap =
                        cmsAppProperties.getLogicCategoryIdToGroupBuyingProductCategoryMap();
                GroupBuyingProductCategoryDTO groupBuyingProductCategoryDTO = logicCategoryIdToGroupBuyingProductCategoryMap.get(productCategoryId);
                if (groupBuyingProductCategoryDTO == null || CollectionUtils.isEmpty(groupBuyingProductCategoryDTO.getCategoryIds())) {
                    log.info("【拼团专题页】没有商品分类，直接返回空集合。参数，merchantId：{}，productCategoryId：{}", merchantId, productCategoryId);
                    return modelAndView;
                }
                // 截断，最多200个。
                List<Long> categoryIds = groupBuyingProductCategoryDTO.getCategoryIds();
                if (categoryIds.size() > 200) {
                    categoryIds = categoryIds.subList(0, 200);
                }
                searchCsuDTO.setCategoryIdList(categoryIds);
            }
            searchCsuDTO.setCsuOrder(CsuOrder.PT_DEFAULT);
            searchCsuDTO.setSortOrder(SortOrder.DESC);
            searchCsuDTO.setRankType(EcRankType.EC_PT_RANK.getValue());
            searchCsuDTO.setIsNoResultSearch(false);
            searchCsuDTO.setIsShopSearch(false);
            Page pageParam = new Page(pageNum, pageSize);
            ApiRPCResult<Page<CsuInfo>> apiRPCResult = searchApi.searchForPC(pageParam, searchCsuDTO);
            if (log.isDebugEnabled()) {
                log.debug("【拼团专题页】调用SearchApi.searchForPC，入参：pageParam：{}，searchCsuDTO：{}，响应：{}",
                        JSONObject.toJSONString(pageParam), JSONObject.toJSONString(searchCsuDTO), JSONObject.toJSONString(apiRPCResult));
            }
            // 商品组探活埋点
            productExhibitionGroupBusinessAdminRemoteService.asyncSendExhibitionLiveEventMQForSearch(searchCsuDTO);
            if (!apiRPCResult.isSuccess()) {
                log.error("【拼团专题页】调用SearchApi.searchForPC失败，入参：pageParam：{}，searchCsuDTO：{}，响应：{}",
                        JSONObject.toJSONString(pageParam), JSONObject.toJSONString(searchCsuDTO), JSONObject.toJSONString(apiRPCResult));
                throw new AppException(XyyJsonResultCodeEnum.FAIL, "查询拼团信息失败，请稍后重试");
            }
            Page<CsuInfo> resultPage = apiRPCResult.getData();
            if (resultPage == null) {
                return modelAndView;
            }
            List<CsuInfo> csuInfoList = resultPage.getRecordList();
            if (CollectionUtils.isEmpty(csuInfoList)) {
                return modelAndView;
            }
            // 过滤掉非法数据
            csuInfoList = csuInfoList.stream().filter(item -> item != null && item.getId() != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(csuInfoList)) {
                return modelAndView;
            }
            //填充库存信息
            csuInfoList = productServiceRpc.fillSearchProductActTotalSurplusQtyToAvailableQty(csuInfoList);
            long pages = resultPage.getPages();
            long resultTotalCount = resultPage.getTotalCount();
            long pageNo = resultPage.getPageNo();
            Map<Long, CsuInfo> csuIdToCsuInfoMap = csuInfoList.stream().collect(Collectors.toMap(CsuInfo::getId, Function.identity(), (a, b) -> a));
            List<Long> resultCsuIds = csuInfoList.stream().map(CsuInfo::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(resultCsuIds)) {
                return modelAndView;
            }
            // 查询商品所属拼团信息
            // 优先级：进行中 > 未开始
            List<Integer> statusList = Lists.newArrayList();
            if (Objects.equals(CmsConstants.EXHIBITION_ID_STR_GROUP_BUYING_NOT_START, exhibitionId)) {
                statusList.add(MarketingQueryStatusEnum.UN_START.getType());
            } else if (Objects.equals(CmsConstants.EXHIBITION_ID_STR_GROUP_BUYING_IN_PROGRESS, exhibitionId)) {
                statusList.add(MarketingQueryStatusEnum.STARTING.getType());
            }
            Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoDtoMap = cmsSkuService.getGroupBuyingInfoBySkuIdList(resultCsuIds,
                    statusList, merchantId);
            // 按照查询的商品ID顺序调整。
            List<GroupBuyingInfoDto> groupBuyingInfoDtos = resultCsuIds.stream().map(item -> csuIdToGroupBuyingInfoDtoMap.get(item))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (log.isDebugEnabled()) {
                log.debug("【查询拼团信息列表V2】查询当前页的商品参与的拼团信息列表，当前页商品ID列表：{}，拼团信息列表：{}",
                        JSONArray.toJSONString(resultCsuIds), JSONArray.toJSONString(groupBuyingInfoDtos));
            }
            if (CollectionUtils.isEmpty(groupBuyingInfoDtos)) {
                return modelAndView;
            }
            // 过滤掉非法数据
            groupBuyingInfoDtos = groupBuyingInfoDtos.stream().filter(Objects::nonNull).filter(item -> {
                List<GroupBuyingSkuDto> groupBuyingSkuDtoList = item.getGroupBuyingSkuDtoList();
                if (CollectionUtils.isEmpty(groupBuyingSkuDtoList)) {
                    return false;
                }
                GroupBuyingSkuDto groupBuyingSkuDto = groupBuyingSkuDtoList.get(0);
                if (groupBuyingSkuDto == null) {
                    return false;
                }
                if (groupBuyingSkuDto.getSkuId() == null) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(groupBuyingInfoDtos)) {
                return modelAndView;
            }
            List<Long> csuIds = groupBuyingInfoDtos.stream().map(item -> {
                GroupBuyingSkuDto groupBuyingSkuDto = item.getGroupBuyingSkuDtoList().get(0);
                return groupBuyingSkuDto.getSkuId();
            }).collect(Collectors.toList());
            // 商品的拼团信息
            Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap = groupBuyingInfoDtos.stream()
                    .collect(Collectors.toMap(item -> item.getGroupBuyingSkuDtoList().get(0).getSkuId(),
                            Function.identity(), (a, b) -> a));
            // 转换
            List<CmsGroupBuyingProductVO> cmsGroupBuyingProductVOS = csuIds.stream().map(item -> {
                CsuInfo csuInfo = csuIdToCsuInfoMap.get(item);
                if (csuInfo == null) {
                    return null;
                }
                GroupBuyingInfoDto groupBuyingInfoDto = csuIdToGroupBuyingInfoMap.get(item);
                if (groupBuyingInfoDto == null) {
                    return null;
                }
                // 去掉已结束的
                if (groupBuyingInfoDto.getStatus() == null || Objects.equals(groupBuyingInfoDto.getStatus(), 3)) {
                    return null;
                }
                CmsGroupBuyingProductVO cmsGroupBuyingProductVO = new CmsGroupBuyingProductVO();
                // 商品基本信息
                BeanUtils.copyProperties(csuInfo, cmsGroupBuyingProductVO);
                // 活动信息
                GroupBuyingSkuDto groupBuyingSkuDto = groupBuyingInfoDto.getGroupBuyingSkuDtoList().get(0);
                Long surplusTime = 0L;
                if (Objects.equals(groupBuyingInfoDto.getStatus(), 2)) {
                    surplusTime = groupBuyingInfoDto.getEndTime().getTime() - System.currentTimeMillis();
                    if (surplusTime <= 0L) {
                        surplusTime = 0L;
                    }
                    surplusTime = surplusTime / 1000L;
                }
                BigDecimal percentage = groupBuyingInfoDto.getPercentage().multiply(BigDecimal.valueOf(100));
                // 将活动的拼团状态转为前端拼团状态
                /** 拼团活动状态 1.未开始 ，2.拼团中，3.已结束 兼容老逻辑前端拼团活动状态 0-未开始 1-进行中 2-结束 **/
                Integer assembleStatus = groupBuyingInfoDto.getStatus() == null ? 0 : groupBuyingInfoDto.getStatus() - 1;
                CmsGroupBuyingActInfoVO groupBuyingActInfo = CmsGroupBuyingActInfoVO.builder()
                        .goodId(csuInfo.getId())
                        .assemblePrice(groupBuyingSkuDto.getSkuPrice())
                        .surplusTime(surplusTime)
                        .percentage(percentage)
                        .skuStartNum(groupBuyingSkuDto.getSkuStartNum())
                        .orderNum(groupBuyingInfoDto.getOrderNum())
                        .assembleStartTime(groupBuyingInfoDto.getStartTime())
                        .assembleEndTime(groupBuyingInfoDto.getEndTime())
                        .marketingId(groupBuyingInfoDto.getMarketingId())
                        .assembleStatus(assembleStatus)
                        .preheatShowPrice(groupBuyingInfoDto.getPreheatShowPrice())
                        // 拼团多阶梯价信息
                        .stepPriceStatus(groupBuyingInfoDto.getStepPriceStatus())
                        .minSkuPrice(groupBuyingInfoDto.getMinSkuPrice())
                        .maxSkuPrice(groupBuyingInfoDto.getMaxSkuPrice())
                        .startingPriceShowText(groupBuyingInfoDto.getStartingPriceShowText())
                        .rangePriceShowText(groupBuyingInfoDto.getRangePriceShowText())
                        .stepPriceShowTexts(groupBuyingInfoDto.generateStepPriceShowTexts(cmsGroupBuyingProductVO.getProductUnit()))
                        .build();
                cmsGroupBuyingProductVO.setActPt(groupBuyingActInfo);
                // 重置商品名称
                String productUnit = Optional.ofNullable(cmsGroupBuyingProductVO.getProductUnit()).orElse("");
                String productSpec = Optional.ofNullable(cmsGroupBuyingProductVO.getSpec()).orElse("");
                String skuNamePrefix = "";
                if (StringUtils.isNotEmpty(groupBuyingInfoDto.getTopicPrefix())) {
                    skuNamePrefix = groupBuyingInfoDto.getTopicPrefix();
                }
                String skuName = skuNamePrefix + groupBuyingSkuDto.getSkuStartNum() + productUnit + "包邮"
                        + cmsGroupBuyingProductVO.getShowName() + "/" + productSpec;
                cmsGroupBuyingProductVO.setShowName(skuName);
                return cmsGroupBuyingProductVO;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            com.xyy.ec.pc.base.Page<CmsGroupBuyingProductVO> pageInfo = new com.xyy.ec.pc.base.Page<>();
            pageInfo.setOffset(pageNum);
            pageInfo.setLimit(pageSize);
            pageInfo.setRows(cmsGroupBuyingProductVOS);
            pageInfo.setPageCount((int) pages);
            pageInfo.setTotal(resultTotalCount);
            pageInfo.setRequestUrl(url);
            modelAndView.addObject("pager", pageInfo);
            return modelAndView;
        } catch (Exception e) {
            log.error("【拼团专题页】加载页面失败，参数：{}", JSONObject.toJSONString(cmsGroupBuyingProductQueryParam), e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    private String getCmsGroupBuyingSubjectHeadImageUrl() {
        Map<Integer, List<CmsGroupBuyingSubjectHeadImageDailyPeriodDTO>> cmsGroupBuyingSubjectHeadImageMap =
                cmsAppProperties.getCmsGroupBuyingSubjectHeadImageMap();
        if (MapUtils.isEmpty(cmsGroupBuyingSubjectHeadImageMap)) {
            return null;
        }
        Calendar instance = Calendar.getInstance();
        int week = instance.get(Calendar.DAY_OF_WEEK);
        if (Objects.equals(week, Calendar.SUNDAY)) {
            week = 7;
        } else {
            week = week - 1;
        }
        int hours = instance.get(Calendar.HOUR_OF_DAY);
        int minutes = instance.get(Calendar.MINUTE);
        int seconds = instance.get(Calendar.SECOND);
        if (log.isDebugEnabled()) {
            log.debug("获取PC拼团专题页头图，星期{}，时间：{}:{}:{}", week, hours, minutes, seconds);
        }
        List<CmsGroupBuyingSubjectHeadImageDailyPeriodDTO> dailyPeriods = cmsGroupBuyingSubjectHeadImageMap.get(week);
        if (CollectionUtils.isEmpty(dailyPeriods)) {
            return null;
        }
        for (CmsGroupBuyingSubjectHeadImageDailyPeriodDTO dailyPeriod : dailyPeriods) {
            if (dailyPeriod.isInThisDailyPeriod(hours, minutes, seconds)) {
                return dailyPeriod.getImageUrl();
            }
        }
        return null;
    }

    /**
     * 商品组数据请求
     *
     * @param isAdmin
     * @param exhibitionId
     * @param pageNum
     * @param pageSize
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/listProducts", method = {RequestMethod.GET, RequestMethod.POST})
    public XyyJsonResult listProducts(@RequestHeader(name = "isAdmin", required = false) Boolean isAdmin,
                                      @RequestHeader(name = "terminalType", required = false) Integer terminalType,
                                      @RequestParam("exhibitionId") String exhibitionId,
                                      @RequestParam("branchCode") String branchCode,
                                      @RequestParam(value = "moduleSourceType", required = false) Integer moduleSourceType,
                                      @RequestParam(value = "isRecommend", required = false) Boolean isRecommend,
                                      @RequestParam(value = "scmE", required = false) String scmE,
                                      int pageNum, int pageSize, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = null;
            if (Objects.nonNull(merchant)) {
                merchantId = merchant.getId();
            }
            String realBranchCode;
            if (!BooleanUtils.isTrue(isAdmin)) {
                realBranchCode = getBranchCodeByMerchantId(request, merchantId);
            } else {
                realBranchCode = branchCode;
            }

            CmsProductQueryParam cmsProductQueryParam = CmsProductQueryParam.builder()
                    .isAdmin(isAdmin).merchantId(merchantId).exhibitionId(exhibitionId).branchCode(realBranchCode)
                    .moduleSourceType(moduleSourceType).isRecommend(isRecommend).terminalType(terminalType).build();
            CmsListProductDto cmsListProductDto = cmsSkuService.listProducts(cmsProductQueryParam, pageNum, pageSize);
            PageInfo pageInfo = cmsListProductDto.getPageInfo();
            // 商品信息根据资质状态过滤
            Integer licenseStatus;
            List<ListProduct> products = pageInfo.getList();
            if (!BooleanUtils.isTrue(isAdmin)) {
                LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
                licenseStatus = layoutBaseService.getLicenseStatus(merchant);
                if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                    products = layoutBaseService.setProductProperties(products, statusEnum, request);
                    pageInfo.setList(products);
                }
            } else {
                licenseStatus = MerchantLicenseEnum.sy_passed.getId();
            }
            //推荐商品或者控销商品符合下列条件之一，则为最后一页
            Boolean resultIsRecommend = cmsListProductDto.getIsRecommend();

            List<CmsListProductVO> cmsListProductVOS = CmsListProductVOHelper.creates(products);
            if (CollectionUtils.isNotEmpty(cmsListProductVOS)) {
                if (BooleanUtils.isTrue(isAdmin) || Objects.nonNull(merchantId)) {
                    // 获取拼团或批购包邮活动信息
                    List<Long> csuIds = cmsListProductVOS.stream().filter(Objects::nonNull).map(CmsListProductVO::getId).filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(csuIds)) {
                        Set<Long> gaoMaoSkuIdSet = Sets.newHashSet();
                        gaoMaoSkuIdSet = cmsListProductVOS.stream().filter(productDto -> {
                            if(null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                                    || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))){
                                return true;
                            }
                            return false;
                        }).map(CmsListProductVO :: getId).collect(Collectors.toSet());
                        Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap = cmsSkuService.getMarketingActivityInfoBySkuIdList(csuIds,
                                Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()), merchantId,
                                Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
                        // 填充拼团或批购包邮活动信息
                        cmsListProductVOS = cmsSkuService.fillListProductsMarketingActivityInfo(cmsListProductVOS, csuIdToGroupBuyingInfoMap);
                    }
                }
                cmsListProductVOS = cmsSkuService.fillListProductsShopInfo(cmsListProductVOS);
                cmsListProductVOS = cmsSkuService.fillListProductsTagInfo(merchantId, cmsListProductVOS, true);
                //填充库存信息
                cmsListProductVOS = productServiceRpc.fillCmsActTotalSurplusQtyToAvailableQty(cmsListProductVOS);
                //受托生产厂家处理
                for (CmsListProductVO productVO : cmsListProductVOS) {
                    if(StringUtils.isNotBlank(productVO.getEntrustedManufacturer())){
                        productVO.setManufacturer(ProductMangeUtils.getManufacturer(productVO.getMarketAuthor(),productVO.getManufacturer(),productVO.getEntrustedManufacturer()));
                    }
                }
            }

            //qt埋点
            if(CollectionUtils.isNotEmpty(cmsListProductVOS)){
                TrackDataEntity trackData =  TrackDataHelper.buildScmETrackData(scmE);
                cmsListProductVOS.forEach(productVO -> {
                    productVO.setTrackData(trackData);
                });
            }
            ProductDataFilterHelper.fillBlankCms(cmsListProductVOS);
            XyyJsonResult xyyJsonResult = XyyJsonResult.createSuccess().addResult("pageNo", pageInfo.getPageNum())
                    .addResult("pageSize", pageInfo.getPageSize())
                    .addResult("totalPage", pageInfo.getPages())
                    .addResult("totalCount", pageInfo.getTotal())
                    .addResult("licenseStatus", licenseStatus)
                    .addResult("products", cmsListProductVOS)
                    .addResult("merchantId", merchant == null ? null : merchant.getId())
                    .addResult("recommend", resultIsRecommend)
                    .addResult("tip", cmsListProductDto.getTip());
            if (BooleanUtils.isTrue(isAdmin)) {
                xyyJsonResult.addResult("isAdmin", isAdmin);
            }
            return xyyJsonResult;
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("普通商品查询根据会员ID、商品展示组ID查询商品列表（带翻页功能）失败，isAdmin：{}，exhibitionId：{}",
                        isAdmin, exhibitionId, e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("普通商品查询根据会员ID、商品展示组ID查询商品列表（带翻页功能）失败，isAdmin：{}，exhibitionId：{}",
                    isAdmin, exhibitionId, e);
            return XyyJsonResult.createFailure();
        }
    }

    /**
     * 单品请求数据
     */
    @Deprecated
    @ResponseBody
    @RequestMapping(value = "/selectSKUsByOrderId", method = {RequestMethod.GET, RequestMethod.POST})
    public XyyJsonResult selectSKUsByOrderId(@RequestHeader(name = "isAdmin", required = false) Boolean isAdmin,
                                             @RequestBody CmsSpecifiedProductQueryParam cmsSpecifiedProductQueryParam, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = null;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            Boolean validate = CmsSpecifiedProductQueryParamHelper.validate(cmsSpecifiedProductQueryParam);
            if (!BooleanUtils.isTrue(validate)) {
                String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "商品查询");
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
            }
            String realBranchCode;
            if (!BooleanUtils.isTrue(isAdmin)) {
                realBranchCode = getBranchCodeByMerchantId(request, merchantId);
            } else {
                realBranchCode = cmsSpecifiedProductQueryParam.getBranchCode();
            }
            List<Long> skuIdList = cmsSpecifiedProductQueryParam.getSkuIdList();
            List<ListProduct> list = cmsSkuService.pagingUsingProducts(isAdmin, realBranchCode, skuIdList, merchantId);
            List<CmsListProductVO> cmsListProductVOS = CmsListProductVOHelper.creates(list);
            cmsListProductVOS = cmsSkuService.fillListProductsShopInfo(cmsListProductVOS);
            cmsListProductVOS = cmsSkuService.fillListProductsTagInfo(merchantId, cmsListProductVOS, true);
            // 商品信息根据资质状态过滤
            Integer licenseStatus = null;
            if (!BooleanUtils.isTrue(isAdmin)) {
                LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
                licenseStatus = layoutBaseService.getLicenseStatus(merchant);
                if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                    list = layoutBaseService.setProductProperties(list, statusEnum, request);
                }
            } else {
                licenseStatus = MerchantLicenseEnum.sy_passed.getId();
            }
            list = layoutBaseService.setProductShopName(list);
            XyyJsonResult xyyJsonResult = XyyJsonResult.createSuccess()
                    .addResult("licenseStatus", licenseStatus)
                    .addResult("products", cmsListProductVOS)
                    .addResult("merchantId", merchant == null ? null : merchant.getId());
            if (BooleanUtils.isTrue(isAdmin)) {
                xyyJsonResult.addResult("isAdmin", isAdmin);
            }
            return xyyJsonResult;
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("普通商品查询根据会员ID、商品ID查询商品列表失败，isAdmin：{}，cmsProductQueryParam：{}",
                        isAdmin, JSONObject.toJSON(cmsSpecifiedProductQueryParam), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("普通商品查询根据会员ID、商品ID查询商品列表失败，isAdmin：{}，cmsProductQueryParam：{}",
                    isAdmin, JSONObject.toJSON(cmsSpecifiedProductQueryParam), e);
            return XyyJsonResult.createFailure();
        }
    }

    //https://www.test.ybm.com/cms/activity/2020/7/935.htm
    @ResponseBody
    @RequestMapping(value = "/{year}/{month}/{pageId}.htm", method = {RequestMethod.GET, RequestMethod.POST})
    public Object getActivityPageInfo(@PathVariable("year") Integer year, @PathVariable("month") Integer month,
                                      @PathVariable("pageId") String pageId) {
        try {
            ModelMap modelMap = new ModelMap();
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String url = StringUtils.join(cmsAppProperties.getCmsAssetsDomain(), year, "/", month, "/", pageId, ".html");
            String cmHtml = IOUtils.toString(new URL(url), "UTF-8");
            String body = this.getBody(cmHtml);
            //String cssLink = this.getCssLink(cmHtml);
            String cmsIndexHtmlPcMainCssName = this.getCmsIndexHtmlPcMainCssName(cmHtml);
            modelMap.put("cmsIndexHtmlPcMainCssName", cmsIndexHtmlPcMainCssName);
            modelMap.put("areaInfo", body);
            modelMap.put("cssList", null);
            modelMap.put("merchant", merchant);
            modelMap.put("merchantId", merchantId);
            modelMap.put("assetsDomain", cmsAppProperties.getCmsAssetsDomain());
            return new ModelAndView("/cms/cmsActivity.ftl", modelMap);
        } catch (Exception e) {
            log.error("访问cms静态页失败", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    private String getCmsIndexHtmlPcMainCssName(String cmsIndexHtml) {
        if (StringUtils.isEmpty(cmsIndexHtml)) {
            return null;
        }
        Pattern compile = Pattern.compile("pc-main\\.(.+)\\.css");
        Matcher matcher = compile.matcher(cmsIndexHtml);
        while (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private String getBody(String cmHtml) {
        int startIndex = cmHtml.indexOf(">", cmHtml.indexOf("<body"));
        int endIndex = cmHtml.lastIndexOf("</body>");
        return cmHtml.substring(startIndex + 1, endIndex);
    }

}
