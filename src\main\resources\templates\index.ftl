<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description"
          content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>药帮忙官网-便宜买好药，当然药帮忙</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link href="/favicon.ico" rel="shortcut icon">
    <link rel="stylesheet" type="text/css" href="/static/css/sui.min.css"/>
    <link rel="stylesheet" type="text/css" href="/static/css/sui-append.min.css"/>
    <link rel="stylesheet" type="text/css" href="/static/css/reset.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/headerAndFooter.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/common.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/lib.css?t=${t_v}"/>
    <link rel="stylesheet" href="/static/css/index.css?t=${t_v}"/>

    <script type="text/javascript" src="/static/js/jquery-1.11.3.min.js"></script>
    <script type="text/javascript" src="/static/js/sui.min.js"></script>
    <script type="text/javascript" src="/static/js/common.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/plugins/layer/layer.js"></script>
    <script type="text/javascript" src="/static/js/lib.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/util.js?t=${t_v}"></script>
    <script src="/static/js/plugins/jquery.md5.js" type="text/javascript"></script>
    <script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/index.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/zhuge/zhugeio.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>

    <style>
        .bannerbox2.con-618 {
            margin-top: 15px;
        }

        .bannerbox2.con-618 a {
            width: 100%;
            max-width: 100%;
        }

        .bannerbox2.con-618 a img {
            width: 100%;
            max-width: 100%;
            height: auto;
        }

        .bannerbox2.con-618 a .money_lev img {
            width: 20px;
        }

        .jd_con {
            position: absolute;
            left: 2px;
            top: 6px;
            z-index: 1;
            height: 16px;
            width: 20%;
            max-width: 99.5%;
            border-radius: 7px;
            background: -webkit-linear-gradient(90deg, #ff8d00, #ffe800); /* Safari 5.1 - 6.0 */
            background: -o-linear-gradient(90deg, #ff8d00, #ffe800); /* Opera 11.1 - 12.0 */
            background: -moz-linear-gradient(90deg, #ff8d00, #ffe800); /* Firefox 3.6 - 15 */
            background: linear-gradient(90deg, #ff8d00, #ffe800); /* 标准的语法 */
        }

        .jd_con.ie_css {
            background: #ff8d00;
        }

        .money_lev {

        }

        .money_lev img {
            vertical-align: middle;
        }

        .money_lev span {
            color: #fc2137;
            font-size: 25px;
            font-weight: 600;
            vertical-align: middle;
        }

        /*.money_lev::after {*/
        /*    display: block;*/
        /*    content: '';*/
        /*    width: 0;*/
        /*    height: 0;*/
        /*    z-index: -1;*/
        /*    left: 44%;*/
        /*    top: 28px;*/
        /*    border: 16px solid #e02a11;*/
        /*    border-radius: 5px;*/
        /*    border-right-width: 0px;*/
        /*    border-bottom-width: 0px;*/
        /*    position: absolute;*/
        /*    transform: rotate(45deg);*/
        /*}*/

        .money_lev_after {
            display: block;
            content: '';
            width: 0;
            height: 0;
            z-index: -1;
            left: 44%;
            top: 28px;
            border: 16px solid #e02a11;
            border-radius: 5px;
            border-right-width: 0px;
            border-bottom-width: 0px;
            position: absolute;
            transform: rotate(45deg);
        }

        /*.money_lev_r::after{*/
        /*    display: block;*/
        /*    content: '';*/
        /*    width: 0;*/
        /*    height: 0;*/
        /*    z-index: -1;*/
        /*    left: 44%;*/
        /*    top: 28px;*/
        /*    border: 16px solid #df5602;*/
        /*    border-radius: 5px;*/
        /*    border-right-width: 0px;*/
        /*    border-bottom-width: 0px;*/
        /*    position: absolute;*/
        /*    transform: rotate(45deg);*/
        /*}*/

        .money_lev_r_after {
            display: block;
            content: '';
            width: 0;
            height: 0;
            z-index: -1;
            left: 44%;
            top: 28px;
            border: 16px solid #df5602;
            border-radius: 5px;
            border-right-width: 0px;
            border-bottom-width: 0px;
            position: absolute;
            transform: rotate(45deg);
        }

        .remark_con {
            width: 60%;
            max-width: 60%;
            height: 36px;
            margin-left: 0%;
        }

        .remark_con div {
            line-height: 36px;
            border-radius: 20px;
            font-size: 20px;
            font-weight: 600;
            background: -webkit-linear-gradient(90deg, #ff1a18, #ff8900); /* Safari 5.1 - 6.0 */
            background: -o-linear-gradient(90deg, #ff1a18, #ff8900); /* Opera 11.1 - 12.0 */
            background: -moz-linear-gradient(90deg, #ff1a18, #ff8900); /* Firefox 3.6 - 15 */
            background: linear-gradient(90deg, #ff1a18, #ff8900); /* 标准的语法 */
        }

        .remark_con div.iecss {
            background: #ff8900;
        }

        /*.remark_con::before {*/
        /*    display: block;*/
        /*    content: '';*/
        /*    width: 0;*/
        /*    height: 0;*/
        /*    z-index: -1;*/
        /*    left: 10%;*/
        /*    top: 58px;*/
        /*    border: 16px solid #ff1d18;*/
        /*    border-radius: 5px;*/
        /*    border-right-width: 0px;*/
        /*    border-bottom-width: 0px;*/
        /*    position: absolute;*/
        /*    transform: rotate(45deg);*/
        /*}*/


        .remark_con_before {
            display: block;
            content: '';
            width: 0;
            height: 0;
            z-index: -1;
            left: 10%;
            top: 58px;
            border: 16px solid #ff1d18;
            border-radius: 5px;
            border-right-width: 0px;
            border-bottom-width: 0px;
            position: absolute;
            transform: rotate(45deg);
        }


        .remark_con_before.remark_con_r {
            border: 16px solid #ff7c03;
        }

        .remark_1 {
            color: #fff;
        }

        .remark_2 {
            color: #ffe800;
        }

        .hd_618 a, .hd_618 a img {
            width: 100%;
            height: auto;
        }
        .site-gray, .site-gray *{
            filter: gray !important;
            filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
            filter: grayscale(100%);
            -webkit-filter: grayscale(100%);
            -moz-filter: grayscale(100%);
            -ms-filter: grayscale(100%);
            -o-filter: grayscale(100%);
        }
    </style>

    <!--[if lte IE 9]>

    <style>

        .remark_con div {
            background: #ff4d0d !important;
        }

        .jd_con {
            background: #ffbb00 !important;
        }
    </style>

    <![endif]-->
    <!--推送弹框css-->
    <style>
        .push-modal.sui-modal{
            width:500px;
            margin-left:-250px;
            background:none;
            box-shadow: none;
        }
        .push-modal .modal-body{
            padding:0;
            border-radius: 4px 4px 0 0;
        }

        .push-modal .modal-footer{
            position:relative;
            padding:0;
            height:53px;
            box-shadow:none;
        }
        .push-modal .modal-footer i.del-icon{
            display:inline-block;
            background:url('../static/images/modal-del.png') 0 0 no-repeat;
            width:38px;
            height:38px;
            position:absolute;
            left:50%;
            margin-left:-19px;
            top:15px;
            cursor:pointer;
        }

        .push-modal .modal-body .normal-push{
            box-sizing: border-box;
            padding:10px 10px 20px;
            background:none;
            position:relative;
        }

        .push-modal .modal-body .normal-push i{
            position:absolute;
            display:none;
            width:64px;
            height:20px;
            left:50%;
            margin-left:-32px;
            cursor:pointer;
        }

        .push-modal .modal-body .normal-push i.top-icon{
            background:url('../static/images/modal-top-icon.png') 0 0;
            top:10px;
        }

        .push-modal .modal-body .normal-push i.bottom-icon{
            background:url('../static/images/modal-bottom-icon.png') 0 0;
            bottom:20px;
        }

        .push-modal .modal-body .normal-push .push-box{
            height:420px;
            overflow-y:auto;
        }

        .push-modal .modal-body .normal-push .push-box::-webkit-scrollbar {
            width: 5px;
            height: 5px;
        }
        /* 滚动槽 */
        .push-modal .modal-body .normal-push .push-box::-webkit-scrollbar-track {
            box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }
        /* 滚动条滑块 */
        .push-modal .modal-body .normal-push .push-box::-webkit-scrollbar-thumb {
            border-radius: 5px;
            background: #bbb;
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
        }
        ::-webkit-scrollbar-thumb:window-inactive {
            background: #ccc;
        }

        .push-modal .modal-body .normal-push .push-box a{
            width:100%;
            display:block;
        }

        .push-modal .modal-body .normal-push .push-box a.last{
            margin-bottom:0;
        }

        .push-modal .modal-body .normal-push .push-box a img{
            width:100%;
            vertical-align: bottom;
        }

        .push-modal .modal-body .coupon-push{
            background:#FF4244;
        }

        .push-modal .modal-body .coupon-push .ad-box{
            height:150px;
        }

        .push-modal .modal-body .coupon-push .ad-box a{
            width:100%;
            height:100%;
        }

        .push-modal .modal-body .coupon-push .ad-box a img{
            width:100%;
            height:100%;
        }

        .push-modal .modal-body .coupon-push .coupon-box{
            box-sizing: border-box;
            padding:10px 10px 15px;
        }

        .push-modal .modal-body .coupon-push .coupon-box li{
            margin-bottom:10px;
            height:100px;
            overflow:hidden;
            border-radius: 4px;
            background:url('../static/images/coupon-bg.png') 0 0 no-repeat;
        }

        .push-modal .modal-body .coupon-push .coupon-box li.last{
            margin-bottom:0;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div{
            float:left;
            height:100%;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div.left-box{
            width:138px;
            padding:25px 0;
            box-sizing: border-box;
        }
        .push-modal .modal-body .coupon-push .coupon-box li > div.left-box p{
            text-align: center;
            color:#FF2121;
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            margin:5px 0;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div.left-box p span{
            font-size:40px;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div.right-box{
            width:calc(100% - 138px);
            box-sizing: border-box;
            padding:10px 30px 10px 10px;
            position:relative;
        }
        .push-modal .modal-body .coupon-push .coupon-box li > div.right-box p{
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            color: #292933;
            overflow: hidden;
            line-height: 20px;
        }
        .push-modal .modal-body .coupon-push .coupon-box li > div.right-box p i{
            float:left;
            padding:0 10px;
            height:20px;
            border-radius: 12px;
            background:#FF4244;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: center;
            color: #ffffff;
            font-style: normal;
            margin-right:10px;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div.right-box p.time-text{
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: left;
            color: #999999;
            position: absolute;
            bottom:10px;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div.right-box button{
            position:absolute;
            padding:0 10px;
            line-height: 32px;
            height: 32px;
            background: linear-gradient(90deg,#ff6025, #ff4244);
            border:none;
            border-radius: 18px;
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: center;
            color:#fff;
            right:30px;
            bottom:32px;
            outline: none;
        }

        .push-modal .modal-body .coupon-push .more-box{
            height:55px;
            box-sizing: border-box;
        }

        .push-modal .modal-body .coupon-push .more-box button{
            width:280px;
            height: 40px;
            background: linear-gradient(180deg,#fff6dd, #ffc362 92%, #ffc362 92%);
            border-radius: 32px;
            font-size: 20px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            text-align: center;
            color: #ff4244;
            border:none;
            outline:none;
        }

    </style>
</head>
<prop key="classic_compatible"  style="display: none;">true</prop>
<body>
<div class="container">
    <#if alertFlag ==2>
        <!--被委托人认证弹窗-->
        <!--$('#authModal').modal('show');-->
        <div id="authModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade" data-show="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 id="myModalLabel" class="modal-title">温馨提示</h4>
                    </div>
                    <div class="modal-body" style="text-align: left;font-size: 14px;line-height:20px;">为了确保配送药品的安全，邀请您进行企业被委托人身份信息认证</div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">暂不认证</button>
                        <a type="button" class="sui-btn btn-primary btn-large" href="/authentication/indexAuthentication.htm?reSubmit=1">去认证</a>
                    </div>
                </div>
            </div>
        </div>
    </#if>
    <#if alertFlag1==true >
        <!--被委托人信息变更弹窗-->
        <div id="modifyModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade" data-backdrop="static" style="display: block;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 id="myModalLabel" class="modal-title">温馨提示</h4>
                    </div>
                    <div class="modal-body" style="text-align: left;font-size: 14px;line-height: 20px;">企业被委托人信息已变更，为了确保配送药品的安全，邀请您重新进行企业被委托人身份信息认证</div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">暂不认证</button>
                        <a type="button" class="sui-btn btn-primary btn-large" href="/authentication/indexAuthentication.htm">去认证</a>
                    </div>
                </div>
            </div>
        </div>
    </#if>
    <#if count!=null && count gt 0 >
    <div id="myModal" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" data-bgcolor="rgba(0, 0, 0, 0.7411764705882353)"  data-show="true" class="sui-modal hide">
        <div class="modal-dialog">
            <div class="modal-content">

                <div class="modal-body" style="line-height: 25px;font-size:18;color:#333;font-weight:600;padding:25px 0px;">
                    您的资质即将过期/已过期
                    <br/>
                    为避免影响您的正常采购，请及时联系工作人员</div>
                <div class="modal-footer" style="text-align: center;">
                    <a href="/merchant/center/license/findLicenseCategoryInfo.htm" class="sui-btn btn-primary btn-large">去查看</a>
                </div>
            </div>
        </div>
    </div>
    </#if>
    <script>
        $(function(){
            if($('#myModal') && $('#myModal').length){
                $('#myModal').modal({
                    show:true,
                    backdrop:"static"
                })
            }
        })
    </script>
    <input type="hidden" id="branchCode" name="branchCode" value="${branchCode}"/>
    <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
    <input type="hidden" id="pcVersion" name="pcVersion" value="${pcVersion}"/>
    <#list appModuleManageBuineseDtoList as appModuleCategory>
        <#if appModuleCategory.alias == 1021 && appModuleCategory.style == 100>
            <!--806顶部广告图片-->
            <div class="topbanner-new">
                <#if appModuleCategory.items?? && (appModuleCategory.items?size > 0)>
                    <#list appModuleCategory.items as item>
                        <#if item_index == 0>
                            <#if (item.action?? && item.action!='')>
                                <a href="${item.action}">
                                    <img src="${productImageUrl}${item.imgUrl}" alt=""/>
                                </a>
                            <#else>
                                <a href="javascript:void(0);">
                                <#--<a href="javascript:void(0);" onclick="receiveTemplate(${merchantId},120)">-->
                                    <img src="${productImageUrl}${item.imgUrl}" alt=""/>

                                </a>
                            </#if>
                        </#if>
                    </#list>
                </#if>
            </div>
        <#elseif (merchantId > 0)>
            <#if appModuleCategory.alias == 1021>
                <!--顶部广告图片-->
                <div class="topbanner-new">
                    <#if (appModuleCategory.action?? && appModuleCategory.action!='')>
                        <a href="${appModuleCategory.action}">
                            <img src="${productImageUrl}${appModuleCategory.bgRes}" alt=""/>
                        </a>
                    <#else>
                        <a href="javascript:void(0);" onclick="openGetQuan()">
                        <#--<a href="javascript:void(0);" onclick="receiveTemplate(${merchantId},120)">-->
                            <img src="${productImageUrl}${appModuleCategory.bgRes}" alt=""/>
                        </a>
                    </#if>
                </div>

                <!--领券弹窗-->
                <div id="getQuan" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static"
                     class="sui-modal hide fade spe-tc">
                    <div class="fltcbox">
                        <a href="javascript:void(0);" class="close-btn"><img
                                src="http://upload.ybm100.com/ybm/app/layout/20170531/fltcolse.png" alt=""></a>
                        <a href="javascript:void(0);" onclick="receiveTemplate(${merchantId},${appModuleCategory.api})"
                           class="fl-detail" target="_blank">
                            <img src="http://upload.ybm100.com/ybm/app/layout/20170531/linquanbg.png" alt="">
                        </a>
                    </div>
                </div>
            </#if>
        <#elseif appModuleCategory.alias == 1021 && appModuleCategory.style == 1>
            <div class="topbanner-new">
                <#if (appModuleCategory.action?? && appModuleCategory.action!='')>
                    <a href="${appModuleCategory.action}">
                        <img src="${productImageUrl}${appModuleCategory.bgRes}" alt=""/>
                    </a>
                <#else>
                    <a href="javascript:void(0);" onclick="openGetQuan()">
                    <#--<a href="javascript:void(0);" onclick="receiveTemplate(${merchantId},120)">-->
                        <img src="${productImageUrl}${appModuleCategory.bgRes}" alt=""/>
                    </a>
                </#if>
            </div>
        </#if>
    </#list>
    <!--置顶搜索栏-->
    <div class="topsearcher">
        <div class="w1200">
            <a href="/" class="sep_seach fl"><img src="/static/images/logo-zhiding.png" alt=""/></a>
            <div class="search fl">
                <div class="inputbox fl">
                    <input type="text" id="search-top" placeholder="药品名称/厂家名称/助记码" value="${keywordSearch}"/>
                </div>
                <div class="ss fl">
                    <a href="#" id="searchproductTop" onclick="searchProductTop();">搜索</a>
                </div>
            </div>
        <#--<div class="cgoubox fl">-->
        <#--<div class="caigou">-->
        <#--<a href="/merchant/center/cart/index.htm" id="myCart"><i class="sui-icon  icon-tb-cart"></i>我的采购单</a>-->
        <#--</div>-->
        <#--<div id="firstCartNumberDiv" class=""></div>-->
        <#--</div>-->
            <!--搜索下拉弹窗-->
            <ul class="searchUl-top" id="searchUl-top">
            </ul>
        </div>
    </div>


    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
        <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->

    <!--主体部分开始-->
    <div class="main"
            <#if index_bg??>
                style="background-image: url('${productImageUrl}${index_bg.titleRes}');
                <#if index_bg.bgRes?default("")?trim?length lt 1>
                    background-color:#fcf3f2;
                <#else >
                    background-color:${index_bg.bgRes};
                </#if>
                "
            </#if>
    >
        <!--商品分类-->
        <div class="goodsmain-index">
            <#if index_bg??>
                <div class="main_left" onclick="window.location.href='${index_bg.action}'">
                </div>
            </#if>
            <div class="hdpbox ">
                <!--幻灯片-->
                <div class="l-box">
                    <div id="myCarousel2" class="sui-carousel slide">
                        <ol class="carousel-indicators">
                            <#list moduleBanner as item>
                                <#if (item_index==0)>
                                    <li data-target="#myCarousel2" data-slide-to="0" class="active"></li>
                                <#else>
                                    <li data-target="#myCarousel2" data-slide-to="${item_index}"></li>
                                </#if>
                            </#list>
                        </ol>
                        <div class="carousel-inner">
                            <#list moduleBanner as item>
                                <#if (item_index==0)>
                                    <div class="active item">
                                        <a href="${item.action}" target="_blank"><img
                                                src="${productImageUrl}${item.imgUrl}"></a>
                                    </div>
                                <#else>
                                    <div class="item">
                                        <a href="${item.action}" target="_blank"><img
                                                src="${productImageUrl}${item.imgUrl}"></a>
                                    </div>
                                </#if>
                            </#list>
                        </div>
                        <div class="jiantou">
                            <a href="#myCarousel2" data-slide="prev" class="carousel-control left">‹</a>
                            <a href="#myCarousel2" data-slide="next" class="carousel-control right">›</a>
                        </div>
                    </div>
                </div>
            </div>
            <!--右侧内容-->
            <div class="yjxdbox">
                <div class="model1">
                <#--<a href="/merchant/center/collection/findAttention.htm?tab=2" class="yjcgimg"><img src="/static/images/yijiancaigou.png"  alt=""></a>-->
                <#--<div class="tuxiangbox"><img src="http://upload.ybm100.com/ybm/app/layout/newpc/touxiang.png" alt=""></div>-->
                    <div class="huanying">Hi，欢迎来到药帮忙</div>
                    <#if (merchantId > 0)>
                    <#--<!--已登录&ndash;&gt;-->
                    <#--<div class="loginin" align="center">-->
                    <#--<a class="noDownLine text-overflow" href="/merchant/center/index.htm" target="_blank">-->
                    <#--${merchant.realName}！欢迎您！-->
                    <#--</a>-->
                    <#--</div>-->
                        <!--已登录-->
                        <div class="loginin">
                            ${merchant.realName}！欢迎您！
                        </div>
                    <#else>
                        <!--未登录-->
                        <div class="loginout ">
                            <a href="/login/login.htm" target="_self" class="login-new">登录</a>
                            <a href="/newstatic/#/register/index" class="reg-new">注册</a>
<#--                            <a href="/login/register.htm" target="_self" class="reg-new">注册</a>-->
                        </div>
                    </#if>
                </div>
                <!--公告-->
                <div class="gg-new">
                    <div class="gg-new-tit">
                        <span class="fl g-gao">公告</span>
                        <a href="/notice/noticeList" target="_blank" class="fr g-more">更多</a>
                    </div>
                    <ul class="gg-new-list">
                        <#list noticeDetails as notice>
                            <li><a class="noDownLine text-overflow" href="/notice/noticeDetail?id=${notice.id}"
                                   target="_blank">${notice.noticeTitle}</a></li>
                        </#list>
                    </ul>
                </div>
                <#--  <div class="jyzzbox">
                    <img src="/static/images/${branchCode}_zgz.png" alt="小药药资格证---${merchantId}">
                    <img src="/static/images/${branchCode}_zgz.png" alt="小药药资格证---${merchantId}">
                </div>  -->
            </div>

            <#if index_bg??>
                <div class="main_right" onclick="window.location.href='${index_bg.action}'">
                </div>
            </#if>
        </div>


        <#list appModuleManageBuineseDtoList as appModuleCategory>

            <#if appModuleCategory.alias == 1028>
        <!--三张banner图-->
                <#if (appModuleCategory.bgRes ?? && appModuleCategory.bgRes != '')>
            <div class="three-bannerbox clearfix" style="background-color: ${appModuleCategory.bgRes}">
                <#else>
            <div class="three-bannerbox clearfix" >
                </#if>
                <div class="three-innerbox"><#list appModuleCategory.items as item>
                    <#if (item_index > 0)>
                            <a href="${item.action}" class="fl a-jg" target="_blank" style="height: ${appModuleCategory.height}px;margin:${appModuleCategory.marginText};margin-left: 10px;margin-right:0">
                                <img src="${productImageUrl}${item.imgUrl}" alt="" style="height: ${appModuleCategory.height}px"/>
                            </a>
                    <#else>
                            <a href="${item.action}" class="fl" target="_blank" style="height: ${appModuleCategory.height}px;margin:${appModuleCategory.marginText};margin-left: 0;margin-right:0">
                                <img src="${productImageUrl}${item.imgUrl}" alt="" style="height: ${appModuleCategory.height}px"/>
                            </a>
                    </#if>
                </#list></div>
                </div>
            </#if>

            <#if appModuleCategory.alias == 1051>
            <!--新热门推荐 四张图并排显示-->
                <#if (appModuleCategory.bgRes ?? && appModuleCategory.bgRes != '')>
            <div class="bannerbox2" style="background-color: ${appModuleCategory.bgRes}">
                <#else>
                <div class="bannerbox2">
                </#if>
                <#list appModuleCategory.items as item>
                    <#if (item_index > 0)>
                            <a href="${item.action}" class="fl a-jg" target="_blank">
                                <img src="${productImageUrl}${item.imgUrl}" alt=""/>
                            </a>
                    <#else>
                            <a href="${item.action}" class="fl" target="_blank">
                                <img src="${productImageUrl}${item.imgUrl}" alt=""/>
                            </a>
                    </#if>
                </#list>
                </div>
            </#if>
            <#if appModuleCategory.alias == 1035>
            <!--五张banner图-->
                <#if (appModuleCategory.bgRes ?? && appModuleCategory.bgRes != '')>
                <div class="five-bannerbox" style="background-color: ${appModuleCategory.bgRes}">
                <#else>
                <div class="five-bannerbox">
                </#if>
                <div class="five-innerbox"><#list appModuleCategory.items as item>
                    <#if (item_index > 0)>
                            <a href="${item.action}" class="fl a-jg" target="_blank" style="height: ${appModuleCategory.height}px;margin:${appModuleCategory.marginText};margin-left: 10px;margin-right:0">
                                <img src="${productImageUrl}${item.imgUrl}" alt="" style="height: ${appModuleCategory.height}px"/>
                            </a>
                    <#else>
                            <a href="${item.action}" class="fl" target="_blank" style="height: ${appModuleCategory.height}px;margin:${appModuleCategory.marginText};margin-left: 0;margin-right:0">
                                <img src="${productImageUrl}${item.imgUrl}" alt="" style="height: ${appModuleCategory.height}px"/>
                            </a>
                    </#if>
                </#list></div>
                </div>
            </#if>
        <!--纯商品列表-->
            <#if appModuleCategory.alias == 1036>
                <#if (appModuleCategory.bgRes?? && appModuleCategory.bgRes != '')>
            <div class="discount-area" style="background-color: ${appModuleCategory.bgRes}">
                <#else>
            <div class="discount-area" >
                </#if>
                <div class="da-zone da-zone-new">
                    <div class="da-content">
                        <!--推荐列表-->
                        <ul class="mrth-new clearfix">
                                <#if appModuleCategory.productList??>
                                    <#list appModuleCategory.productList as skuVO>
                                        <#import "/common/skuVOIndex.ftl" as pr>
                                        <@pr.skuVO skuVO/>
                                    </#list>
                                </#if>
                        </ul>
                    </div>
                </div>
            </div>
            </#if>

            <#if appModuleCategory.alias == 618>
                <#if (merchantId > 0)>
                    <#list appModuleCategory.items as item>
                        <#if index_618_div_data?? && index_618_div_data.state>
                                <div class="bannerbox2 con-618">
                                    <#if (item.action?? && item.action!='')>
                                    <a href="${item.action}" target="_blank">
                                    </#if>
                                    <div>
                                        <img src="/static/images/2019618/1.png"/>
                                    </div>
                                    <div style="position: relative;">
                                        <img src="/static/images/2019618/2.png"/>
                                        <div style="position: absolute;top:0px;left:35px;">
                                            <div style="float: left;margin-top:20px;margin-right:10px;">
                                                <img src="/static/images/2019618/pc15.png"/>
                                            </div>
                                            <div style="float: left;position: relative;z-index:2;">
                                                <div>
                                                    <div style="position: relative;height: 30px;">
                                                        <div style="position: absolute;left:15%;line-height: 22px;"
                                                             class="money_lev">
                                                            <img src="/static/images/2019618/pc07.png"/>
                                                            <span>${index_618_div_data.level1Amount} 元</span>
                                                            <span class="money_lev_after"></span>
                                                        </div>
                                                        <div style="position: absolute;left:73.5%;line-height: 22px;"
                                                             class="money_lev money_lev_r">
                                                            <img src="/static/images/2019618/pc09.png"/>
                                                            <span>${index_618_div_data.level2Amount} 元</span>
                                                            <span class="money_lev_r_after"></span>
                                                        </div>
                                                    </div>
                                                    <div style="position: relative;">
                                                        <img src="/static/images/2019618/pc20.png"/>

                                                            <#if (index_618_div_data.orderAmount <= index_618_div_data.level1Amount)>
                                                                <div class="jd_con"
                                                                     style="width:${(index_618_div_data.orderAmount / index_618_div_data.level1Amount) * 0.20 *100 }%;"></div>
                                                            </#if>

                                                            <#if (index_618_div_data.orderAmount > index_618_div_data.level1Amount && index_618_div_data.orderAmount < index_618_div_data.level2Amount)>
                                                                <div class="jd_con"
                                                                     style="width:${(0.2 + (((index_618_div_data.orderAmount-index_618_div_data.level1Amount) / (index_618_div_data.level2Amount-index_618_div_data.level1Amount)) * 0.6))*100 }%;"></div>
                                                            </#if>

                                                            <#if (index_618_div_data.orderAmount >= index_618_div_data.level2Amount)>
                                                                <div class="jd_con"
                                                                     style="width:${(((0.8 + ((index_618_div_data.orderAmount-index_618_div_data.level2Amount) / 10000 * 0.2))*100)>=100)?string("100","" +((0.8 + ((index_618_div_data.orderAmount-index_618_div_data.level2Amount) / 10000 * 0.2))*100)) }%;"></div>
                                                            </#if>
                                                    <#--                                                            <div class="jd_con"-->
                                                    <#--                                                                 style="width:${  index_618_div_data.amountPercent}%;"></div>-->
                                                    </div>
                                                </div>



                                                    <#if (index_618_div_data.orderAmount <= index_618_div_data.level1Amount)>
                                                        <span class="remark_con_before"
                                                              style="left:${(((index_618_div_data.orderAmount / index_618_div_data.level1Amount) * 0.20 *100)<=0)?string("2",""+ (((index_618_div_data.orderAmount / index_618_div_data.level1Amount) * 0.20 *100)-1)) }%;"></span>
                                                    </#if>

                                                    <#if (index_618_div_data.orderAmount > index_618_div_data.level1Amount && index_618_div_data.orderAmount < index_618_div_data.level2Amount) >
                                                        <span class="remark_con_before"
                                                              style="left:${(0.2 + (((index_618_div_data.orderAmount-index_618_div_data.level1Amount) / (index_618_div_data.level2Amount-index_618_div_data.level1Amount)) * 0.6))*100-1 }%;"></span>
                                                    </#if>

                                                    <#if (index_618_div_data.orderAmount >= index_618_div_data.level2Amount)>
                                                        <span class="remark_con_before"
                                                              style="left:${(((0.8 + ((index_618_div_data.orderAmount-index_618_div_data.level2Amount) / 10000 * 0.2))*100)>=100)?string("97","" +(((0.8 + ((index_618_div_data.orderAmount-index_618_div_data.level2Amount) / 10000 * 0.2))*100)-1)) }%;"></span>
                                                    </#if>
                                            <#--                                                    <span class="remark_con_before"-->
                                            <#--                                                          style="left:${((index_618_div_data.amountPercent-1)<=0)?string("3%",""+(((index_618_div_data.amountPercent-1) >=97) ?string('97',""+(index_618_div_data.amountPercent-1))+"%"))};"></span>-->
                                                <div style="margin-top:10px;margin-left:${(index_618_div_data.amountPercent-1>=50)?string("40%","0%")};"
                                                     class="remark_con">
                                                <#--  未满足门槛 -->
                                                        <#if (index_618_div_data.orderAmount >=0 && index_618_div_data.orderAmount < index_618_div_data.level1Amount) >
                                                            <div>
                                                                <span class="remark_1">还差</span>
                                                                <span class="remark_2">${index_618_div_data.differenceAmount } 元</span>
                                                                <span class="remark_1"> ，超级返商品额外返</span>
                                                                <span class="remark_2">${index_618_div_data.rebateRate}%</span>
                                                            </div>
                                                        <#--                                                        </#if>-->
                                                        <#--已满足一个门槛-->
                                                        <#elseif (index_618_div_data.orderAmount >= index_618_div_data.level1Amount && index_618_div_data.orderAmount < index_618_div_data.level2Amount) >
                                                            <div>
                                                                <span class="remark_1">预计可得</span>
                                                                <span class="remark_2">${index_618_div_data.rebate} 元</span>
                                                                <span class="remark_1">，还</span>
                                                                <span class="remark_2">差${index_618_div_data.differenceAmount } 元</span>
                                                                <span class="remark_1">可额外返</span>
                                                                <span class="remark_2"> ${index_618_div_data.rebateRate}%</span>
                                                            </div>
                                                        <#--                                                         </#if>-->
                                                        <#--已满足最高门槛-->
                                                        <#elseif (index_618_div_data.orderAmount >= index_618_div_data.level2Amount)>
                                                            <div>
                                                                <#if (index_618_div_data.rebate >0)>
                                                                   <span  class="remark_1">恭喜您，</span>
                                                                </#if>
                                                                <span class="remark_1">可获得超级返商品额外返利</span>
                                                                <span class="remark_2">${index_618_div_data.rebate} 元</span>
                                                            </div>
                                                        <#else>

                                                        </#if>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="clear:both;"></div>
                                        <#if (item.action?? && item.action!='')>
                                    </a>
                                        </#if>
                                </div>
                        <#else>
                            <#if (item.action?? && item.action!='')>
                                <a href="${item.action}">
                            </#if>
                            <#if (branchCode =="XS430000"|| branchCode =="XS500000")>
                                    <div class="bannerbox2 con-618">
                                        <img src="/static/images/2019618/nodata/2000-8000/pc_03.png"/>
                                        <img src="/static/images/2019618/nodata/2000-8000/pc_03-02.png"/>
                                    </div>
                            <#--                                </#if>-->

                            <#elseif (branchCode =="XS420000")>
                                    <div class="bannerbox2 con-618">
                                        <img src="/static/images/2019618/nodata/5000-20000/pc_03.png"/>
                                        <img src="/static/images/2019618/nodata/5000-20000/pc_03-02.png"/>
                                    </div>
                            <#--                                </#if>-->

                            <#elseif (branchCode =="XS350000")>
                                    <div class="bannerbox2 con-618">
                                        <img src="/static/images/2019618/nodata/1000-6000/pc_03.png"/>
                                        <img src="/static/images/2019618/nodata/1000-6000/pc_03-02.png"/>
                                    </div>
                            <#else>
                            </#if>
                            <#if (item.action?? && item.action!='')>
                                    </a>
                            </#if>
                        </#if>
                    </#list>
                <#else>
                        <div class="bannerbox2 con-618" >
                            <a href="/login/login.htm" target="_self">
                                <img src="/static/images/2019618/22.png"/>
                                <img src="/static/images/2019618/23.png"/>
                            </a>
                        </div>
                </#if>
            </#if>
            <#if appModuleCategory.alias == 1031>
                <!--top推荐-->
                <#if (appModuleCategory.bgRes?? && appModuleCategory.bgRes != '')>
                <div class="toptj-box mkkc clearfix" style="background-color: ${appModuleCategory.bgRes}">
                <#else>
                    <div class="toptj-box mkkc clearfix">
                </#if>
                        <div class="top-tit-box">
                            <div class="l-box fl">
                                <span class="top-span1">${appModuleCategory.titleRes}</span>
                                <span class="top-span2">${appModuleCategory.title}</span>
                            </div>
                            <div class="r-box fr"><a href="${appModuleCategory.action}" class="more">更多></a></div>
                        </div>
                        <!--top推荐列表-->
                        <ul class="mrth-new clearfix">
                            <#if appModuleCategory.items>
                                <#list appModuleCategory.items as item >
                                    <li class="litupian"><a href="${item.action}"><img
                                            src="${productImageUrl}${item.imgUrl}" alt=""></a></li>
                                </#list>
                            <#else>
                                <li class="litupian"><a href="${appModuleCategory.action}"><img
                                        src="${productImageUrl}${appModuleCategory.bgRes}" alt=""></a></li>
                            </#if>

                            <#if appModuleCategory.productList??>
                                <#list appModuleCategory.productList as skuVO>
                                    <#import "/common/skuVOIndex.ftl" as pr>
                                    <@pr.skuVO skuVO/>
                                </#list>
                            </#if>
                        </ul>
                    </div>
            </#if>


            <#if appModuleCategory.alias == 1029>
            <!--2张banner图-->
                <#if (appModuleCategory.bgRes?? && appModuleCategory.bgRes != '')>
            <div class="two-bannerbox clearfix" style="background-color: ${appModuleCategory.bgRes}">
                <#else>
            <div class="two-bannerbox clearfix" >
                </#if>
                <div class="two-innerbox">
                <#list appModuleCategory.items as item>
                    <#if (item.sort == 1)>
                    <a href="${item.action}" class="fl" style="height: ${appModuleCategory.height}px;margin:${appModuleCategory.marginText};"><img src="${productImageUrl}${item.imgUrl}" alt="" style="height: ${appModuleCategory.height}px;"></a>

                    <#else>
                    <a href="${item.action}" class="fr" style="height: ${appModuleCategory.height}px;margin:${appModuleCategory.marginText};"><img src="${productImageUrl}${item.imgUrl}" alt="" style="height: ${appModuleCategory.height}px;"></a>
                    </#if>
                </#list>
                </div>

        </div>
            </#if>

            <#if appModuleCategory.alias == 1030>
                        <!--中间包围ddddd-->
                <#if (appModuleCategory.bgRes?? && appModuleCategory.bgRes != '')>
                        <div class="toptj-box xpmj clearfix" style="background-color: ${appModuleCategory.bgRes}">
                <#else>
                            <div class="toptj-box xpmj clearfix">
                </#if>
                                <div class="top-tit-box">
                                    <div class="l-box fl">
                                        <span class="top-span1">${appModuleCategory.titleRes}</span>
                                        <span class="top-span2">${appModuleCategory.title}</span>
                                    </div>
                                    <div class="r-box fr"><a href="${appModuleCategory.action}" class="more">更多></a>
                                    </div>
                                </div>
                                <!--推荐列表-->
                                <ul class="mrth-new clearfix">
                                    <#if appModuleCategory.productList??>
                                        <#list appModuleCategory.productList as skuVO>
                                            <#import "/common/skuVOIndex.ftl" as pr>
                                            <@pr.skuVO skuVO/>
                                            <#if (skuVO_index ==0)>
                                                <#if appModuleCategory.items>
                                                    <#list appModuleCategory.items as item >
                                                        <li class="litupian3">
                                                            <a href="${item.action}">
                                                                <img src="${productImageUrl}${item.imgUrl}" alt=""/>
                                                            </a>
                                                        </li>
                                                    </#list>
                                                <#else>
                                                    <li class="litupian3">
                                                        <a href="${appModuleCategory.action}">
                                                            <img src="${productImageUrl}${appModuleCategory.bgRes}"
                                                                 alt=""/>
                                                        </a>
                                                    </li>
                                                </#if>
                                            </#if>
                                        </#list>
                                    </#if>
                                </ul>
                            </div>
            </#if>

            <#if appModuleCategory.alias == 1032>
            <!--1张banner图-->
                <#list appModuleCategory.items as item>
                    <#if (appModuleCategory.bgRes?? && appModuleCategory.bgRes != '')>
                <div class="one-bannerbox" style="background-color: ${appModuleCategory.bgRes}">
                    <#else>
                <div class="one-bannerbox" >
                    </#if>
                    <#--<div class="one-innerbox">-->
                        <#if (item.action?? && item.action!='')>
                            <a href="${item.action}" target="_blank" style="height: ${appModuleCategory.height}px;margin:${appModuleCategory.marginText};">
                                <img src="${productImageUrl}${item.imgUrl}" alt="" style="height: ${appModuleCategory.height}px;"/>
                            </a>
                        <#else>
                            <a target="_blank" style="height: ${appModuleCategory.height}px;margin:${appModuleCategory.marginText};">
                                <img src="${productImageUrl}${item.imgUrl}" alt="" style="height: ${appModuleCategory.height}px;"/>
                            </a>
                        </#if>
                    <#--</div>-->
            </div>
                </#list>
            </#if>

            <#if appModuleCategory.alias == 1022>
                                    <!--返利弹窗-->
                                    <div id="getModel" tabindex="-1" role="dialog" data-hasfoot="false"
                                         data-backdrop="static" class="sui-modal hide fade spe-tc">
                                        <div class="fltcbox">
                                            <a href="javaScript:void(0);" class="close-btn"><img
                                                    src="${productImageUrl}/ybm/app/layout/20170401/4月PC相关/fltcolse.png"
                                                    alt=""></a>
                                            <a href="${appModuleCategory.action}" class="fl-detail" target="_blank">
                                                <img src="${productImageUrl}${appModuleCategory.titleRes}" alt="">
                                            </a>
                                        </div>
                                    </div>
            </#if>

            <#if appModuleCategory.alias == 1034 && (merchantId < 1)>
                                    <!-- 药采节弹窗 -->
                                    <div id="yaocai" tabindex="-1" role="dialog" data-hasfoot="false"
                                         data-backdrop="static" class="sui-modal hide fade spe-yaocai">
                                        <div class="fltcbox">
                                            <#list appModuleCategory.items as item>
                                                <#if item_index ==0>
                                                    <a href="javaScript:void(0);" class="close-btn"><img
                                                            src="${productImageUrl}${item.imgUrl}" alt=""></a>
                                                <#elseif  item_index ==1>
                                                    <div class="fl-detail">
                                                        <img src="${productImageUrl}${item.imgUrl}" alt="">
                                                    </div>
                                                <#elseif  item_index ==2>
                                                    <a href="${item.action}" target="_blank"
                                                       class="yaocai-link"><img
                                                            src="${productImageUrl}${item.imgUrl}" alt=""></a>
                                                </#if>
                                            </#list>
                                        </div>
                                    </div>
            </#if>

            <#if appModuleCategory.alias == 1033>
                                    <!--品牌推荐 6张图 -->
                                    <div class="toptj-box pptj clearfix">
                                        <div class="top-tit-box">
                                            <div class="l-box fl">
                                                <span class="top-span1">${appModuleCategory.titleRes}</span>
                                                <span class="top-span2"></span>
                                                <span class="top-span3"></span>
                                            </div>
                                            <div class="r-box fr"><a href="${appModuleCategory.action}"
                                                                     class="more">更多></a></div>
                                        </div>
                                        <div class="pptj-warp">
                                            <div class="dpimgbox clearfix"
                                                 <#if appModuleCategory.bgRes?? && appModuleCategory.bgRes!=''>style="background: url('${appModuleCategory.bgRes}')"</#if>>
                                                <#list appModuleCategory.items as item>
                                                    <#if (item.action?? && item.action!='')>
                                                        <a href="${item.action}" target="_blank"><img
                                                                src="${productImageUrl}${item.imgUrl}"
                                                                alt=""></a>
                                                    <#else>
                                                        <a href="javascript:void(0);"><img
                                                                src="${productImageUrl}${item.imgUrl}"
                                                                alt=""></a>
                                                    </#if>
                                                </#list>
                                            </div>
                                        </div>
                                    </div>
            </#if>

            <#if appModuleCategory.alias == 2004>
                                    <!--品牌推荐-->
                                    <div class="is-yure <#if (.now?date lt "2018-03-19 00:00:00"?date("yyyy-MM-dd HH:mm:ss"))>yure</#if>">
                                        <div class="zq-common pinpai">
                                            <div class="zq-common-tit">
                                                <a class="genduo" href="${appModuleCategory.action}">
                                                    <span class="genduo-info">更多</span><img
                                                        src="http://upload.ybm100.com/ybm/pc/activitys/img/events/319/jiantou319.png">
                                                </a>
                                            </div>
                                            <div class="pptj319">
                                                <#list appModuleCategory.items as item>
                                                    <a href="${item.action}" target="_blank"
                                                            <#if (item_index == 1 || item_index == 4)>
                                                                class="spe"
                                                            </#if>
                                                    ><img src="${productImageUrl}${item.imgUrl}" alt=""></a>
                                                </#list>
                                            </div>
                                        </div>
                                    </div>
            </#if>

            <#if appModuleCategory.alias == 2006>
                                    <!--春季热销-->
                                    <div class="springbox">
                                        <#list appModuleCategory.items as item>
                                            <div class="item<#if (item_index == 0)> first </#if>">
                                                <div class="zq-common-tit">
                                                    <div class="spring-info">${item.text}</div>
                                                    <a class="genduo" href="${item.action}">
                                                        <span class="genduo-info">更多</span><img
                                                            src="http://upload.ybm100.com/ybm/pc/activitys/img/events/319/jiantou319.png">
                                                    </a>
                                                </div>
                                                <ul class="mrth-new events clearfix">
                                                    <#list item.skuVOList as skuVO>
                                                        <#import "/common/skuVOIndex.ftl" as pr>
                                                        <@pr.skuVO skuVO/>
                                                    </#list>
                                                </ul>
                                            </div>
                                        </#list>
                                    </div>
            </#if>

            <#if appModuleCategory.alias == 2007>
                                    <div class="is-yure <#if (.now?date lt "2018-03-19 00:00:00"?date("yyyy-MM-dd HH:mm:ss"))>yure</#if>">
                                        <!--7折专区-->
                                        <div class="zq-common qizhe">
                                            <div class="zq-common-tit">
                                                <a class="genduo" href="${appModuleCategory.action}">
                                                    <span class="genduo-info">更多</span><img
                                                        src="http://upload.ybm100.com/ybm/pc/activitys/img/events/319/jiantou319.png">
                                                </a>
                                            </div>
                                            <ul class="mrth-new events clearfix">
                                                <#if appModuleCategory.productList??>
                                                    <#list appModuleCategory.productList as skuVO>
                                                        <#import "/common/skuVOIndex.ftl" as pr>
                                                        <@pr.skuVO skuVO/>
                                                        <#if (skuVO_index ==0)>
                                                            <#if appModuleCategory.items>
                                                                <#list appModuleCategory.items as item >
                                                                    <li class="litupian3">
                                                                        <a href="${item.action}"><img
                                                                                src="${productImageUrl}${item.imgUrl}"
                                                                                alt="">
                                                                        </a>
                                                                    </li>
                                                                </#list>
                                                            <#else>
                                                                <li class="litupian3">
                                                                    <a href="${appModuleCategory.action}">
                                                                        <img src="${productImageUrl}${appModuleCategory.bgRes}"
                                                                             alt=""/>
                                                                    </a>
                                                                </li>
                                                            </#if>
                                                        </#if>
                                                    </#list>
                                                </#if>
                                            </ul>
                                        </div>
                                    </div>
            </#if>
            <#if appModuleCategory.alias == 2008>
                                    <div class="is-yure <#if (.now?date lt "2018-03-19 00:00:00"?date("yyyy-MM-dd HH:mm:ss"))>yure</#if>">
                                        <!--8折专区-->
                                        <div class="zq-common bazhe">
                                            <div class="zq-common-tit">
                                                <a class="genduo" href="${appModuleCategory.action}">
                                                    <span class="genduo-info">更多</span><img
                                                        src="http://upload.ybm100.com/ybm/pc/activitys/img/events/319/jiantou319.png">
                                                </a>
                                            </div>
                                            <ul class="mrth-new events clearfix">
                                                <#if appModuleCategory.productList??>
                                                    <#list appModuleCategory.productList as skuVO>
                                                        <#import "/common/skuVOIndex.ftl" as pr>
                                                        <@pr.skuVO skuVO/>
                                                        <#if (skuVO_index ==0)>
                                                            <#if appModuleCategory.items>
                                                                <#list appModuleCategory.items as item >
                                                                    <li class="litupian3">
                                                                        <a href="${item.action}"><img
                                                                                src="${productImageUrl}${item.imgUrl}"
                                                                                alt="">
                                                                        </a>
                                                                    </li>
                                                                </#list>
                                                            <#else>
                                                                <li class="litupian3">
                                                                    <a href="${appModuleCategory.action}">
                                                                        <img src="${productImageUrl}${appModuleCategory.bgRes}"
                                                                             alt=""/>
                                                                    </a>
                                                                </li>
                                                            </#if>
                                                        </#if>
                                                    </#list>
                                                </#if>
                                            </ul>
                                        </div>
                                    </div>
            </#if>
            <#if appModuleCategory.alias == 2009>
                                    <div class="is-yure <#if (.now?date lt "2018-03-19 00:00:00"?date("yyyy-MM-dd HH:mm:ss"))>yure</#if>">
                                        <!--9折专区-->
                                        <div class="zq-common jiuzhe">
                                            <div class="zq-common-tit">
                                                <a class="genduo" href="${appModuleCategory.action}">
                                                    <span class="genduo-info">更多</span><img
                                                        src="http://upload.ybm100.com/ybm/pc/activitys/img/events/319/jiantou319.png">
                                                </a>
                                            </div>
                                            <ul class="mrth-new events clearfix">
                                                <#if appModuleCategory.productList??>
                                                    <#list appModuleCategory.productList as skuVO>
                                                        <#import "/common/skuVOIndex.ftl" as pr>
                                                        <@pr.skuVO skuVO/>
                                                        <#if (skuVO_index == 0)>
                                                            <#if appModuleCategory.items>
                                                                <#list appModuleCategory.items as item >
                                                                    <li class="litupian3">
                                                                        <a href="${item.action}"><img
                                                                                src="${productImageUrl}${item.imgUrl}"
                                                                                alt="">
                                                                        </a>
                                                                    </li>
                                                                </#list>
                                                            <#else>
                                                                <li class="litupian3">
                                                                    <a href="${appModuleCategory.action}">
                                                                        <img src="${productImageUrl}${appModuleCategory.bgRes}"
                                                                             alt=""/>
                                                                    </a>
                                                                </li>
                                                            </#if>
                                                        </#if>
                                                    </#list>
                                                </#if>
                                            </ul>
                                        </div>
                                    </div>
            </#if>

            <!-- 秒杀活动组件start -->
            <#if appModuleCategory.alias == 10191>
                <#if (seckillProductInfos?? && seckillProductInfos?size>0) >
                    <div class="msBox">
                        <div class="msTitle">秒杀活动</div>
                        <div class="msContent">
                            <!--列表-->
                            <ul class="mrth-new" id="aa">
                                <#list seckillProductInfos as skuVO>
                                    <#import "/common/skuSkVOIndex.ftl" as pr>
                                    <@pr.skuVO skuVO/>
                                </#list>
                            </ul>
                        </div>
                        <div class="btnToLeft" onclick="msToLastOrNext('last');">
                            <img src="/static/images/d-left.png" />
                        </div>
                        <div class="btnToRight" onclick="msToLastOrNext('next');">
                            <img src="/static/images/d-right.png" />
                        </div>
                    </div>
                </#if>
            </#if>
            <!-- 秒杀活动组件end -->

            <#if appModuleCategory.alias == 12000>
                <#if (appModuleCategory.productList ? exists)>
                                                                    <div class="ms-warp">
                                                                        <!--秒杀标题-->
                                                                        <div class="miaosha">
                                                                            <!--倒计时初始值设置data-timer="20161215000000"-->
                                                                            <img src="http://upload.ybm100.com/ybm/app/layout/20170619/ms-time.png"/>
                                                                            <span class="zdms">限时抢购</span>

                                                                            <span class="only"><#if appModuleCategory.isPreHeat == 0>距开始 <#else>距结束</#if></span>

                                                                            <span class="f24"
                                                                                  id="limit_special_timer_d">0</span>
                                                                            <span class="f18"
                                                                                  id="limit_special_timer_d_maohao">:</span>
                                                                            <span class="f24"
                                                                                  id="limit_special_timer_h">0</span>
                                                                            <span class="f18">:</span>
                                                                            <span class="f24"
                                                                                  id="limit_special_timer_m">0</span>
                                                                            <span class="f18">:</span>
                                                                            <span class="f24"
                                                                                  id="limit_special_timer_s">0</span>
                                                                            <a class="fr"
                                                                               href="${appModuleCategory.action}">更多</a>
                                                                        </div>

                                                                        <ul class="mrth-new clearfix">
                                                                            <#list appModuleCategory.productList as skuVO>
                                                                                <#if skuVO_index == 0>
                                                                                    <#if appModuleCategory.isPreHeat == 0>
                                                                            <div id="limitSpecialTimer"
                                                                                 data-timer="#{appModuleCategory.promotionTimeStamp}">
                                                                                    <#else>
                                                                                <div id="limitSpecialTimer"
                                                                                     data-timer="#{appModuleCategory.promotionTimeStamp}">
                                                                                    </#if>
                                                                                </div>
                                                                                </#if>
                                                                                <#import "/common/skuVOIndex.ftl" as pr>
                                                                                <@pr.skuVO skuVO/>
                                                                            </#list>
                                                                        </ul>
                                                                    </div>
                </#if>
            </#if>
            <#if appModuleCategory.alias == 13000>
                                                            <!--中间包围-->
                <#if (appModuleCategory.bgRes?? && appModuleCategory.bgRes != '')>
                                                            <div class="toptj-box-augest xpmj clearfix ${appModuleCategory.bgRes}">
                <#else>
                                                                <div class="toptj-box-augest xpmj clearfix">
                </#if>
                                                                    <div class="top-tit-box"
                                                                         style="background: url('${productImageUrl}${appModuleCategory.titleRes}')">
                                                                        <div class="r-box fr"><a
                                                                                href="${appModuleCategory.action}"
                                                                                class="more">更多></a></div>
                                                                    </div>
                                                                    <!--推荐列表-->
                                                                    <ul class="mrth-new clearfix">
                                                                        <#if appModuleCategory.productList??>
                                                                            <#list appModuleCategory.productList as skuVO>
                                                                                <#import "/common/skuVOIndex.ftl" as pr>
                                                                                <@pr.skuVO skuVO/>
                                                                                <#if (skuVO_index ==0)>
                                                                                    <#if appModuleCategory.items>
                                                                                        <#list appModuleCategory.items as item >
                                                                                            <li class="litupian3">
                                                                                                <a href="${item.action}">
                                                                                                    <img src="${productImageUrl}${item.imgUrl}"
                                                                                                         alt=""/>
                                                                                                </a>
                                                                                            </li>
                                                                                        </#list>
                                                                                    <#else>
                                                                                        <li class="litupian3">
                                                                                            <a href="${appModuleCategory.action}">
                                                                                                <img src="${productImageUrl}${appModuleCategory.bgRes}"
                                                                                                     alt=""/>
                                                                                            </a>
                                                                                        </li>
                                                                                    </#if>
                                                                                </#if>
                                                                            </#list>
                                                                        </#if>
                                                                    </ul>
                                                                </div>
            </#if>

            <#if appModuleCategory.alias == 14000>
                                                                    <div class="brand-area"
                                                                         style="background-image: url('${productImageUrl}${appModuleCategory.titleRes}')">
                                                                        <div class="ca-title">
                                                                            <a href="${appModuleCategory.action}"
                                                                               class="da-more">
                                                                            </a>
                                                                        </div>
                                                                        <div class="ba-content">
                                                                            <ul>
                                                                                <#list appModuleCategory.items as item>
                                                                                    <li><a href="${item.action}">
                                                                                        <div class="ba-zone">
                                                                                            <div class="ba-top">
                                                                                                <img src="${productImageUrl}${item.imgUrl}"
                                                                                                     alt="">
                                                                                            </div>
                                                                                            <div class="ba-bottom"
                                                                                                 style="background-image: url('${productImageUrl}${appModuleCategory.titleColor}')">
                                                                                                ${item.remark}
                                                                                            </div>
                                                                                        </div>
                                                                                    </a>
                                                                                    </li>
                                                                                </#list>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
            </#if>


            <#if appModuleCategory.alias == 14002>
                                                                    <div class="brand-area"
                                                                         style="background: url('${productImageUrl}${appModuleCategory.titleRes}') no-repeat top center;">
                                                                        <div class="ca-title">
                                                                            <a href="${appModuleCategory.action}">
                                                                            </a>
                                                                        </div>
                                                                        <#list appModuleCategory.items as item>
                                                                            <div class="ba-content">
                                                                                <a href="${item.action}">
                                                                                    <img src="${productImageUrl}${item.imgUrl}"
                                                                                         alt="">
                                                                                </a>
                                                                            </div>
                                                                        </#list>
                                                                    </div>
            </#if>

        <#--品牌专区1-->
            <#if appModuleCategory.alias == 15000>
                                                                    <div class="category-area" style="<#if appModuleCategory.style?? && appModuleCategory.style!="" && appModuleCategory.style!=null>margin-top:${appModuleCategory.style}px;</#if>">
                                                                        <#if appModuleCategory.titleRes?? && appModuleCategory.titleRes!="" && appModuleCategory.titleRes!=null >
                                                                            <div class="ca-title"
                                                                                 style="background-image: url('${productImageUrl}${appModuleCategory.titleRes}')">
                                                                            </div>
                                                                        </#if>

                                                                        <#list appModuleCategory.items as item>
                                                                            <div class="ca-content"
                                                                                 style="background-image: url('${productImageUrl}${item.imgUrl}');<#if appModuleCategory.marginText?? && appModuleCategory.marginText!="" && appModuleCategory.marginText!=null >${appModuleCategory.marginText}</#if>">
                                                                                <div class="con-title">
                                                                                    <a href="${item.action}" class="da-more" style="display:inline-block;width:100%;height:100%;">
                                                                                        <h2></h2>
                                                                                        <p>${item.remark}</p>
                                                                                        <div class="ca-hdxq">
                                                                                            <a href="${item.action}"
                                                                                               class="da-more">
                                                                                            </a>
                                                                                        </div>
                                                                                    </a>
                                                                                </div>
                                                                                <ul class="mrth-new events clearfix">
                                                                                    <#list item.skuVOList as skuVO>
                                                                                        <#import "/common/skuVO.ftl" as pr>
                                                                                        <@pr.skuVO skuVO/>
                                                                                    </#list>
                                                                                </ul>
                                                                            </div>
                                                                        </#list>
                                                                    </div>
            </#if>

        <#--品牌专区2-->
            <#if appModuleCategory.alias == 15002>
                                                                    <div class="category-area" style="<#if appModuleCategory.style?? && appModuleCategory.style!="" && appModuleCategory.style!=null>margin-top:${appModuleCategory.style}px;</#if>">
                                                                        <#if appModuleCategory.titleRes?? && appModuleCategory.titleRes!="" && appModuleCategory.titleRes!=null >
                                                                             <div class="ca-title"
                                                                                  style="background-image: url('${productImageUrl}${appModuleCategory.titleRes}')">
                                                                             </div>
                                                                        </#if>

                                                                        <#list appModuleCategory.items as item>
                                                                            <div class="ca-content"
                                                                                 style="background-image: url('${productImageUrl}${item.imgUrl}');<#if appModuleCategory.marginText?? && appModuleCategory.marginText!="" && appModuleCategory.marginText!=null >${appModuleCategory.marginText}</#if>">
                                                                                <div class="con-title"
                                                                                     onclick="window.location.href='${item.action}'">
                                                                                    <div class="ca-hdxq">
                                                                                    </div>
                                                                                </div>
                                                                                <ul class="mrth-new events clearfix">
                                                                                    <#list item.skuVOList as skuVO>
                                                                                        <#import "/common/skuVO.ftl" as pr>
                                                                                        <@pr.skuVO skuVO/>
                                                                                    </#list>
                                                                                </ul>
                                                                            </div>
                                                                        </#list>
                                                                    </div>
            </#if>
        <#--品牌专区3 双十-->
            <#if appModuleCategory.alias == 15004>
                                                                    <div class="category-area" style="<#if appModuleCategory.style?? && appModuleCategory.style!="" && appModuleCategory.style!=null>margin-top:${appModuleCategory.style}px;</#if>">
                                                                        <#if appModuleCategory.titleRes?? && appModuleCategory.titleRes!="" && appModuleCategory.titleRes!=null >
                                                                            <div class="ca-title"
                                                                                 style="background-image: url('${productImageUrl}${appModuleCategory.titleRes}')">
                                                                            </div>
                                                                        </#if>

                                                                        <#list appModuleCategory.items as item>
                                                                            <div class="ca-content-new"
                                                                                 style="background-image: url('${productImageUrl}${item.imgUrl}');<#if appModuleCategory.marginText?? && appModuleCategory.marginText!="" && appModuleCategory.marginText!=null >${appModuleCategory.marginText}</#if>">
                                                                                <div class="con-title">
                                                                                    <a href="${item.action}" class="da-more" style="display:inline-block;width:100%;height:100%;">
                                                                                        <h2></h2>
                                                                                        <p>${item.remark}</p>
                                                                                        <div class="ca-hdxq">
                                                                                            <a href="${item.action}"
                                                                                               class="da-more">
                                                                                            </a>
                                                                                        </div>
                                                                                    </a>
                                                                                </div>
                                                                                <ul class="mrth-new events clearfix">
                                                                                    <#list item.skuVOList as skuVO>
                                                                                        <#import "/common/skuVO.ftl" as pr>
                                                                                        <@pr.skuVO skuVO/>
                                                                                    </#list>
                                                                                </ul>
                                                                            </div>
                                                                        </#list>
                                                                    </div>
            </#if>

            <#if appModuleCategory.alias == 2018101001>
                                                                    <div class="discount-area">
                                                                        <div class="da-zone">
                                                                            <div class="da-title"
                                                                                 style="background-image: url('${productImageUrl}${appModuleCategory.titleRes}')">
                                                                                <!-- <span>5折专区 抢先看！</span> -->
                                                                                <a href="${appModuleCategory.action}"
                                                                                   class="da-more">
                                                                                </a>
                                                                            </div>
                                                                            <div class="da-content"
                                                                                 style="background-image: url('${productImageUrl}${appModuleCategory.titleColor}')">
                                                                                <!--推荐列表-->
                                                                                <ul class="mrth-new clearfix">
                                                                                    <#if appModuleCategory.productList??>
                                                                                        <#list appModuleCategory.productList as skuVO>
                                                                                            <#import "/common/skuVOIndex.ftl" as pr>
                                                                                            <@pr.skuVO skuVO/>
                                                                                        </#list>
                                                                                    </#if>
                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </div>
            </#if>

        </#list>

    <#--底部悬浮层-->
                                                                <#if index_footer_div??>
          <div class="main_bottom" style="background:url('${productImageUrl}${index_footer_div.titleRes}') center center no-repeat;background-size: 100%;" onclick="window.location.href='${index_footer_div.action}'">
              <img src="${productImageUrl}${index_footer_div.titleRes}" alt="" style="opacity:0;width:100%;">
              <div class="main_img">
              <#list index_footer_div.items as item>

                  <a href="${item.action}">
                      <img src="${productImageUrl}${item.imgUrl}" alt="" style="width:130px;">
                  </a>
              </#list>
              </div>
          </div>
          <script type="text/javascript">
              if ($('.main_bottom').length > 0) {
                  $.fn.scrollEnd = function (callback, timeout) {
                      $(this).scroll(function () {
                          $('.main_bottom').show();
                          var $this = $(this);
                          if ($this.data('scrollTimeout')) {
                              clearTimeout($this.data('scrollTimeout'));
                          }
                          $this.data('scrollTimeout', setTimeout(callback, timeout));
                      });
                  };

                  //with a 1000ms timeout
                  $(window).scrollEnd(function () {
                      $('.main_bottom').hide();
                  }, 10000);
                  $(window).scroll(function () {
                      $(".main_bottom").css(" display", "block");
                  });


              }
          </script>
                                                                </#if>
        <div class="main-footer">

        </div>

    </div>
        <!--主体部分结束-->
    </div>
        <!--底部导航区域开始-->
        <div class="footer" id="footer">
                                                            <#include "/common/footer.ftl" />
        </div>
        <!--底部导航区域结束-->

        <!--秒杀活动未开始浮层 1S消失-->
        <div id="msfc" tabindex="-1" role="dialog"
             data-hasfoot="false"
             data-backdrop="static" class="sui-modal hide fade">
            <span>活动尚未开始</span>
        </div>

        <!--319弹窗-->
        <#--<div id="319Frame" tabindex="-1" role="dialog"
             style="display: none;margin-left: -220px; background: none; box-shadow: none;"
             data-hasfoot="false"
             data-backdrop="static"
             class="sui-modal hide fade spe-tc">
            <div style="text-align: right;"
                 onclick="colse319Frame()"><span
                    class="sui-icon icon-tb-roundclose"
                    style="font-size: 35px;color: #fff;">
            </span></div>

        &lt;#&ndash;"sui-modal hide fade spe-tc"&ndash;&gt;
            <a href="#" class="close-btn" id="319Frame_A">
                <img src="" alt="" id="319Frame_img"></a>
        </div>-->

        <!-- 大礼包 -->
        <div class="yin_box" id="giftBag" style="display: none;">
            <div class="img_yin">
                <a href="giftBag/findGiftBag.htm">
                    <img src="static/images/giftbag/tanchuang.png"
                         alt="">
                </a>
                <span id="giftBagTime"></span>
                <i class="sui-icon icon-tb-roundclose"></i>
            </div>
        </div>
        <!--客服入口开始-->
        <div id="kefuFloat" class="kefu-box" style="position: fixed;bottom:25px;right:40px;z-index:1000;">
            <a href="javaScript:callKf('','${merchant.id}');">
                <img src="/static/images/kefu-online.png" alt="" width="80px">
            </a>
        </div>
        <!--客服入口结束-->
        <!--首页推送弹框-->
        <div id="pushModal" tabindex="-1" role="dialog" class="push-modal sui-modal fade hide" data-backdrop="static" data-show="false">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body"></div>
                    <div class="modal-footer">
                        <i class="del-icon" onclick="closePush();"></i>
                    </div>
                </div>
            </div>
        </div>

</body>
<script type="text/javascript">
    $(document).ready(function () {
        // 未登录隐藏在线客服入口
        if ($('#merchantId').val() == '0') {
            $('#kefuFloat').hide();
        }
        // if (window.ybmJsBridge && window.ybmJsBridge.setUserId) {
        //     var bridgeLoaclStorage = localStorage.getItem('bridgeLoaclStorage')
        //     var merchantId = document.getElementById('merchantId').value
        //     if (!bridgeLoaclStorage && merchantId != '0') {
        //         localStorage.setItem('bridgeLoaclStorage', 'has');
        //         window.ybmJsBridge.setUserId(merchantId)
        //     }
        // }
        $("#authModal").modal('show')
        $("#modifyModal").modal('show')
        /*         var int = 0;
                //搜索弹窗响应键盘事件

                $("#search-top").unbind("keyup");
                $("#search-top").keyup(function (event) {
                    var e = event || window.event;
                    var k = e.keyCode || e.which;
                    if(k == 38 || k == 37 || k == 39 || k == 40){
                    }else{
                        var name = $("#search-top").val();
                        if(name == null || name ==""){
        //                    name="阿莫西林胶囊";
                        }else{
                            $("#search-top").val(name);
                        }
                        loadProductNameByAutoCompleteTop(name);
                    }

                    switch (k) {
                        case 13:
        //                    searchProductTopName();
                            break;

                    }
                }); */


        /*         $("#search").unbind("keyup");
                $("#search").keyup(function (event) {
                    var e = event || window.event;
                    var k = e.keyCode || e.which;
                    if(k == 38 || k == 37 || k == 39 || k == 40){
                    }else{
                        var name = $("#search").val();
                        if(name == null || name ==""){
        //                    name="阿莫西林胶囊";
                        }else{
                            $("#search").val(name);
                        }
                        loadProductNameByAutoComplete(name);
                    }

                    switch (k) {
                        case 13:
        //                    searchProductName();
                            break;

                    }
                }); */
    });

    /*     function searchProductName(){

            var name = $("#search").val();
            if(name == null || name ==""){
    //            name="阿莫西林胶囊";
            }else{
                var links ="/search/skuVO.htm?keyword="+name;
                //links = encodeURI(links);
                location.href= (links);
            }


        }
     */
    /*     function searchProductTopName(){

            var topName = $("#search-top").val();
            if(topName == null || topName ==""){
    //            topName="阿莫西林胶囊";
            }else{
                var links ="/search/skuVO.htm?keyword="+topName;
                //links = encodeURI(links);
                location.href= (links);
            }


        } */

    function changeList(divId, obj, id, time, state, changci) {
        if (state == 1) {
            $("#only").html("距开始");
        } else {
            $("#only").html("距结束");
        }
        $("#changci").html(changci + "点场");
        $("#skill_timer").attr("data-timer", time);
        $(".skillProductList").hide();
        $(".div_left_right").hide();
        $("#" + divId).show();
        $(".timebox li").attr("class", "");
        $("#" + obj.id).attr("class", "cur");
    }

    function left_move(id) {
        var tempLength = eval($('#tempLength_' + id).val()); //临时变量,当前移动的长度
        var moveNum = 1; //每次移动的数量
        var moveTime = 300; //移动速度,毫秒
        var scrollDiv = $("#miaosha_" + id + ""); //进行移动动画的容器
        var scrollItems = $("#miaosha_" + id + " li"); //移动容器里的集合
        var moveLength = (scrollItems.eq(0).outerWidth(true)) * moveNum; //计算每次移动的长度
        //上一张
        if (tempLength > 0) {
            if (tempLength > moveLength) {
                scrollDiv.animate({left: "+=" + moveLength + "px"}, moveTime);
                tempLength -= moveLength;
            } else {
                scrollDiv.animate({left: "+=" + tempLength + "px"}, moveTime);
                tempLength = 0;
            }
        }
        $('#tempLength_' + id).val(tempLength);

    }

    function right_move(id) {
        var tempLength = eval($('#tempLength_' + id).val()); //临时变量,当前移动的长度
        var viewNum = 5; //设置每次显示图片的个数量
        var moveNum = 1; //每次移动的数量
        var moveTime = 300; //移动速度,毫秒
        var scrollDiv = $("#miaosha_" + id + ""); //进行移动动画的容器
        var scrollItems = $("#miaosha_" + id + " li"); //移动容器里的集合
        var moveLength = (scrollItems.eq(0).outerWidth(true)) * moveNum; //计算每次移动的长度
        var countLength = (scrollItems.length - viewNum) * scrollItems.eq(0).outerWidth(true); //计算总长度,总个数*单个长度
        //下一张
        if (tempLength < countLength) {
            if ((countLength - tempLength) > moveLength) {
                scrollDiv.animate({left: "-=" + moveLength + "px"}, moveTime);
                tempLength += moveLength;
            } else {
                scrollDiv.animate({left: "-=" + (countLength - tempLength) + "px"}, moveTime);
                tempLength += (countLength - tempLength);
            }
        }
        $('#tempLength_' + id).val(tempLength);
    }
</script>
<script type="text/javascript">

    $(".addList").click(function () {
        var step = 10;
        var me = $(this),
                txt = me.prev(":text");
        var val = parseFloat(txt.val());
        var isSplit = txt.attr("isSplit");
        var middpacking = txt.attr("middpacking");
        step = parseFloat(middpacking);
        var num = 0;
        if (!isNaN(val)) {
            num = val;
        }
        txt.val(num + step);
    });

    $(".subList").click(function () {
        var step = 1;
        var me = $(this),
                txt = me.next(":text");
        var val = parseFloat(txt.val());
        var isSplit = txt.attr("isSplit");
        var middpacking = txt.attr("middpacking");
        if (isSplit == 0) {
            step = parseFloat(middpacking);
        }
        var num = 0;
        if (!isNaN(val)) {
            num = val;
        }
        if (num <= step) {
            txt.val(0);
        } else {
            txt.val(num - step);
        }
    });

    /*秒杀加1操作*/
    $(".addOne").click(function () {
        var step = 10;
        var me = $(this),
                txt = me.prev(":text");
        var val = parseFloat(txt.val());
        var isSplit = txt.attr("isSplit");
        var middpacking = txt.attr("middpacking");

        step = parseFloat(middpacking);

        var num = 0;
        if (!isNaN(val)) {
            num = val;
        }
        txt.val(num + step);
    });


    /*减操作*/
    $(".subOne").click(function () {
        var step = 1;
        var me = $(this),
                txt = me.next(":text");
        var val = parseFloat(txt.val());
        var isSplit = txt.attr("isSplit");
        var middpacking = txt.attr("middpacking");
        if (isSplit == 0) {
            step = parseFloat(middpacking);
        }
        var num = 0;
        if (!isNaN(val)) {
            num = val;
        }
        if (num <= step) {
            txt.val(0);
        } else {
            txt.val(num - step);
        }
    });

    //
    function openGetQuan() {
        $("#getQuan").modal('show');
    }

    // 領取優惠券
    function receiveTemplate(merchantId, receiveTemplateId) {
        if (merchantId && merchantId > 0) {
            $.ajax({
                url: "/merchant/center/voucher/receiveVoucher",
                type: "POST",
                dataType: "json",
                data: {
                    merchantId: merchantId,
                    voucherTemplateId: receiveTemplateId
                },
                success: function (result) {
                    $("#getQuan").modal('hide');
                    if (result.status == "success") {
                        $.alert({
                            title: '提示',
                            body: result.msg
                        });
                    } else {
                        $.alert({
                            title: '提示',
                            body: result.errorMsg
                        });
                    }
                },
                error: function () {
                    $("#getQuan").modal('hide');
                    $.alert({
                        title: '提示',
                        body: '因为某些原因导致优惠券领取异常哟!'
                    });
                }
            });
        } else {
            $.alert({
                title: '提示',
                body: '您还没有登录，请先登录!',
                okHidden: function (e) {
                    window.location.href = "/login/login.htm?redirectUrl=/";
                }
            });
        }
    }

</script>
<!--首页推送js-->
<script type="text/javascript">
    $(function(){
        $.post('/layout/frame/getNewCmsDialog',{sceneType:1},function(res){
            if (res.code == 5000) {
                alert(res.msg)
            } else if(!$.isEmptyObject(res.data)){
                var data = res.data.detail
                var type = data.style
                var datas = type==='60' ? data.imageDtos : data.couponDtos
                initPushDialog(type,datas,data.imageDtos)
                $('#pushModal').modal('show')
                $('#pushModal').on('shown.bs.modal', function () {
                    if(type==='60'){
                        var $box = $('#pushModal .push-box')
                        if($box.length){
                            if($box.scrollTop()>0){
                                $box.siblings('.top-icon').show()
                            }else{
                                $box.siblings('.top-icon').hide()
                            }
                            if($box[0].scrollHeight-$box.height()>$box.scrollTop()){
                                $box.siblings('.bottom-icon').show()
                            }else{
                                $box.siblings('.bottom-icon').hide()
                            }

                            $box.off('scroll').on('scroll',function(){
                                if($(this).scrollTop()>0){
                                    $(this).siblings('.top-icon').show()
                                }else{
                                    $(this).siblings('.top-icon').hide()
                                }
                                if($(this)[0].scrollHeight-$(this).height()>$(this).scrollTop()){
                                    $(this).siblings('.bottom-icon').show()
                                }else{
                                    $(this).siblings('.bottom-icon').hide()
                                }
                            })
                            var $up = $('#pushModal .top-icon')
                            var $down = $('#pushModal .bottom-icon')
                            // 滚动高度
                            var step = 10
                            if($up.length){
                                $up.off('click').on('click',function(){
                                    if($box.scrollTop()>0){
                                        $box.scrollTop($box.scrollTop()-step<0?0:$box.scrollTop()-step)
                                    }
                                })
                            }
                            if($down.length){
                                $down.off('click').on('click',function(){
                                    var max = $box[0].scrollHeight-$box.height()
                                    if(max > $box.scrollTop()){
                                        $box.scrollTop($box.scrollTop()+step>max?max:$box.scrollTop()+step)
                                    }
                                })
                            }
                        }
                    }
                })
            }
        },'json')
    });

    (function($){
        $.couponDatas = []
        $.imgDatas = []
    })(jQuery)


    function closePush(){
        $('#pushModal').modal('hide')
    }

    /**
     * 初始化推送弹框
     * @param type 弹框类型
     * @param datas 弹框展示数据
     */
    function initPushDialog(type,datas,imgs){
        var html = ''
        if(type==='60'){
            html = '<div class="normal-push">' +
                '       <div class="push-box">'
            for(var i=0;i<datas.length;i++){
                var classHtml = ''
                if(i===datas.length-1){
                    classHtml = 'class="last"'
                }
                var hrefHtml = datas[i].action?'href="'+datas[i].action+'"':''
                html+= '<a '+classHtml+' '+hrefHtml+'>' +
                    '       <img src="'+datas[i].imgUrl+'">' +
                    '   </a>'
            }
            html += '</div>' +
                '   <i class="top-icon"></i>' +
                '   <i class="bottom-icon"></i>' +
                '</div>'
        }else{
            $.couponDatas = datas
            $.imgDatas = imgs
            var moreHtml = ''
            var len = datas.length>3 ? 3 : datas.length
            if(datas.length>=3){
                moreHtml = '<div class="more-box">' +
                    '         <button onclick="toCouponCenter()">点击领取更多</button>' +
                    '</div>'
            }
            var imgHtml = ''
            if(imgs && imgs.length){
                var hrefHtml = datas[0].action?'href="'+datas[0].action+'"':''
                imgHtml = '<a '+hrefHtml+' ><img src="'+imgs[0].imgUrl+'"></a>'
            }
            html = '<div class="coupon-push">' +
                '       <div class="ad-box">'+imgHtml+'</div>' +
                '           <ul class="coupon-box">'
            for(var i=0;i<len;i++){
                var priceHtml = ''
                if(datas[i].voucherState===0){
                    priceHtml = '￥'+ datas[i].moneyInVoucher
                }else{
                    priceHtml = datas[i].discount+'折'
                }
                var classHtml = ''
                if(i===len-1){
                    classHtml = 'class="last"'
                }
                var timeHtml = ''
                if(datas[i].expireDate){
                    timeHtml = '使用时间：'+formatDate(datas[i].expireDate)+'-'+formatDate(datas[i].validDate)
                }

                html += '<li '+classHtml+'>' +
                    '      <div class="left-box">' +
                    '          <p>'+priceHtml+'</p>' +
                    '          <p>'+datas[i].minMoneyToEnableDesc+'</p>' +
                    '      </div>' +
                    '      <div class="right-box">' +
                    '          <p><i>'+datas[i].voucherTypeDesc+'</i>'+datas[i].voucherTitle+'</p>' +
                    '          <p class="time-text">'+timeHtml+'</p>' +
                    '          <button onclick="toCoupon(\''+datas[i].pcUrl+'\','+datas[i].templateId+','+datas[i].state+');">'+(datas[i].state===1?'立即领取':'去使用')+'</button>' +
                    '      </div>' +
                    '   </li>'
            }
            html += '</ul>' + moreHtml +
                '</div>'
        }
        $('#pushModal .modal-body').html(html)
    }

    function formatDate(date){
        return date?new Date(date + 8 * 3600 * 1000)
            .toJSON()
            .substr(0, 19)
            .split('T')[0].replace(/-/g,'/'):''
    }

    /**
     * 领取优惠券
     * @param url 跳转地址
     */
    function toCoupon(url,id,type){
        if(Number(type)===1){
            // 立即领取
            $.post('/merchant/center/voucher/receiveVoucher',{voucherTemplateId:id,merchantId: $("#merchantId").val()},function(res){
                if(res.status==='success'){
                    for(var i=0;i<$.couponDatas.length;i++){
                        if($.couponDatas[i].templateId===id){
                            $.couponDatas[i].state=2
                            $.couponDatas[i].validDate = res.validDate
                            $.couponDatas[i].expireDate = res.expireDate
                            initPushDialog(70,$.couponDatas,$.imgDatas)
                        }
                    }
                }else{
                    $.alert({
                        title: '提示',
                        body: res.errorMsg || res.msg
                    });
                }
            },'json')
        }else{
            var a = document.createElement('a')
            // 去使用
            if(url!=='null'){
                a.href = url
            }else{
                a.href = '/voucher/centre/findVoucherSku.htm?voucherTemplateId='+id
            }
            a.click()
        }
    }

    /**
     * 去领券中心
     */
    function toCouponCenter(){
        var a = document.createElement('a')
        a.href = '/activity/voucherCenter.htm?isVoucherCenter=true'
        a.click()
    }
</script>
</html>

