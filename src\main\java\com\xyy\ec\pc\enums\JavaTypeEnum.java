package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;

public enum JavaTypeEnum {

	CHAR(8,"char"),
	INTEGER(32,"int"),
	LONG(64,"long"),
	DOUBLE(128,"double");
	
    private int id;
    private  String value;

    JavaTypeEnum(int id, String value){
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, JavaTypeEnum> enumMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(JavaTypeEnum e : JavaTypeEnum.values()) {
        	enumMaps.put(e.getId(), e);
            maps.put(e.getId(),e.getValue());
        }
    }

    public static String get(int id) {
        return enumMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
    
}
