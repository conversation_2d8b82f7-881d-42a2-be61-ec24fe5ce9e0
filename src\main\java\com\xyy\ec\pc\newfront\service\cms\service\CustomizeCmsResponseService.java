package com.xyy.ec.pc.newfront.service.cms.service;

import com.xyy.ec.pc.newfront.dto.CouponRespVO;
import com.xyy.ec.pc.newfront.dto.MerchantRespVO;
import com.xyy.ec.pc.newfront.vo.ProductGroupsVO;
import com.xyy.ec.pc.newfront.vo.ShotInfoVO;
import com.xyy.ec.pc.rest.AjaxResult;

import java.util.List;


/**
 * cms 预览模块
 */
public interface CustomizeCmsResponseService {

    ProductGroupsVO listExpectProducts(Object[] args);

    List<ShotInfoVO> queryShopList(Object[] args);

    MerchantRespVO selectUser();

    ProductGroupsVO listProducts(Object[] args);

    List<CouponRespVO> previewCouponsByTemplateIds(Object[] args);
}
