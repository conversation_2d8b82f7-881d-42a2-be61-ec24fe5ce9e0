
<#macro skuVO skuVO skuIndex='-1' shopIndex='-1'>
<#--  商品位置 模块位置   -->
<li class="sku-item-qt sku-item-qt-click" qtData="${skuIndex},${shopIndex},${skuVO.id},${skuVO.showName},${skuVO.scmId}">
    <input type="hidden" id="skuId" name="skuId" value="${skuVO.id}">
    <input type="hidden" id="skuName" name="skuName" value="${skuVO.showName}"/>
    <input type="hidden" id="barcode" name="barcode" value="${skuVO.barcode}"/>
    <input type="hidden" id="categoryId" name="categoryId" value="${skuVO.categoryId}"/>
    <input type="hidden" name="skuIndex" value="${skuIndex}"/>
    <input type="hidden" name="shopIndex" value="${shopIndex}"/>
<div class="row1">
    <#if sptype_2??>
    <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype_2}&spid=${spid_2}&sid=${sid_2}" target="_blank" title="${skuVO.commonName}">
    <#else>
    <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype}&spid=${spid}&sid=${sid}" target="_blank" title="${skuVO.commonName}">
    </#if>
        <!--特价标签优先promotionSkuImageUrl-->
	    <#if (merchant ? exists)>
	        <#if skuVO.promotionSkuImageUrl?? && skuVO.promotionSkuImageUrl != null>
	           <#if skuVO.isControl ==1 && !skuVO.isPurchase>
	           <#else>
	        	<div class="yaokuanghuan-pos ">
									<img src="${productImageUrl}/${skuVO.promotionSkuImageUrl}" class="jjks" alt="">
									<div class="tejia806">￥<span class="price806">${skuVO.promotionSkuPrice}</span></div>
			    </div>
			   </#if>
			</#if>
	    </#if>

        <#if skuVO.promotionSkuImageUrl == null && skuVO.markerUrl?? && skuVO.markerUrl!=''>
            <div class="yaokuanghuan-pos">
                <img src="${productImageUrl}/${skuVO.markerUrl}" alt="">
            </div>
        </#if>

        <img id="dt_${skuVO.id}" src="${productImageUrl}/ybm/product/min/${skuVO.imageUrl}" alt="" onerror="this.src='/static/images/default-middle.png'"/>
        <#if skuVO.activityTag != null && skuVO.activityTag.tagNoteBackGroupUrl != ''>
            <#if skuVO.activityTag.sourceType == 2 >
                <div class="shop-activity-tag w185">
                    <img src="${skuVO.activityTag.tagNoteBackGroupUrl}" alt="">
                    <span class="top-box">${skuVO.activityTag.customTopNote}</span>
                    <span class="time-box">${skuVO.activityTag.timeStr}</span>
                    <div class="price-box">
                        <#list skuVO.activityTag.skuTagNotes as skuTagNote>
                            <span>${skuTagNote.text}</span>
                        </#list>
                    </div>
                    <span class="bottom-box">${skuVO.activityTag.customBottomNote}</span>
                </div>
            <#else>
                <div class="yaokuanghuan-posnew">
                    <img src="${productImageUrl}/${skuVO.activityTag.tagNoteBackGroupUrl}" alt="">
                    <div class="acTime">
                        <span class="ast">${skuVO.activityTag.timeStr}</span>
                    </div>
                    <#if skuVO.activityTag.skuTagNotes?? && skuVO.activityTag.skuTagNotes?size != 0>
                        <div class="tejia806">
                            <#list skuVO.activityTag.skuTagNotes as skuTagNote>
                                <span class="price806" style="color: #${skuTagNote.textColor}">${skuTagNote.text}</span>
                            </#list>
                        </div>
                    </#if>
                </div>
            </#if>

        </#if>
       
    </a>
    <!--标签-->
    <div class="bq-box">
		<#if (skuVO.status == 1 && skuVO.availableQty == 0) || skuVO.status == 2 || ((skuVO.status == 3 || skuVO.status == 5)  && skuVO.promotionTotalQty == 0)  || (skuVO.isSplit == 0 && skuVO.availableQty - skuVO.mediumPackageNum lt 0)>
            <img src="/static/images/product/bq-shouqing.png" alt="">
		</#if>
		<#if skuVO.status == 4>
            <img src="/static/images/product/bq-xiajia.png" alt="">
		</#if>
    </div>
</div>
<div class="row2">
    <#if sptype_2??>
    <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype_2}&spid=${spid_2}&sid=${sid_2}" target="_blank" title="${skuVO.showName}">
    <#else>
    <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype}&spid=${spid}&sid=${sid}" target="_blank" title="${skuVO.showName}">
    </#if>
        <#if skuVO.isShow806 || skuVO.gift>
			<div class="bq806">
                <img src="/static/images/bq806.png" alt="">
            </div>
        </#if>
        <#if skuVO.activityTag != null && skuVO.activityTag.tagUrl != ''>
            <div class="bq806">
                <img src="${productImageUrl}/${skuVO.activityTag.tagUrl}" alt="">
            </div>
        </#if>
        <#if skuVO.agent == 1>
            <span class="dujia">独家</span>
        </#if>
        <#if skuVO.isUsableMedicalStr ?? && skuVO.isUsableMedicalStr == 1>
            <#--  <span class="yibao">国家医保</span>  -->
            <#if skuVO.medicalPayType ?? && skuVO.medicalPayType == 1>
                <span class="yibao">医保甲类</span>
            <#elseif skuVO.medicalPayType ?? && skuVO.medicalPayType == 2>
                <span class="yibao">医保乙类</span>
            <#elseif skuVO.medicalPayType ?? && skuVO.medicalPayType == 3>
                <span class="yibao">医保丙类</span>
            <#--  <#else>
                <span class="yibao">医保</span>  -->
            </#if>
        </#if>
        ${skuVO.showName}
    </a>
</div>
<div class="row4 text-overflow">
${skuVO.spec}
</div>
<div class="row5 text-overflow">
${skuVO.manufacturer}
</div>
<div class="row3">
	<#if (merchant ? exists)>
<#--        <#if skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0' >-->
<#--            <span class="noPermission">价格签署协议可见</span>-->
<#--        <#elseif skuVO.showAgree =='0'>-->
<#--            <span class="noPermission">价格签署协议可见</span>-->
        <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
            <span class="noPermission">含税价认证资质后可见</span>
        <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
            <span class="noPermission">含税价认证资质后可见</span>
        <#elseif skuVO.agreementEffective?? && skuVO.agreementEffective =='3'>
            <span class="noPermission">协议已冻结，价格解冻后可见</span>
        <#elseif skuVO.isControl ==1 >
			<#if skuVO.isPurchase>
                <#if skuVO.levelPriceDTO ??>
                    <span class="meiyuan">￥</span><span class="price">${skuVO.levelPriceDTO.rangePriceShowText}</span>
                    <input type="hidden" id="level-price-${skuVO.id}" value="${skuVO.levelPriceDTO.rangePriceShowText}"/>
                    <span class="zhehou-price zhehou-price-${skuVO.id}"></span>
				<#elseif skuVO.priceType==1>
                    <span class="meiyuan">￥</span><span class="price">${skuVO.fob}</span>
                    <input type="hidden" id="price-${skuVO.id}" value="${skuVO.fob}"/>
                    <span class="zhehou-price zhehou-price-${skuVO.id}"></span>
				<#else >
					<#if skuVO.skuPriceRangeList ??>
                        <span class="meiyuan">￥</span><span class="price">
						<#list skuVO.skuPriceRangeList  as priceReange>
							<#if priceReange_index==0>
							${priceReange.price}
							</#if>
							<#if !priceReange_has_next>
                                ～${priceReange.price}
							</#if>
						</#list>
                                                        </span>
					</#if>
				</#if>
			<#else>
                <span class="noPermission">暂无购买权限</span>
			</#if>
		<#else>
            <#if skuVO.levelPriceDTO ??>
                <span class="price">${skuVO.levelPriceDTO.rangePriceShowText}</span>
                <input type="hidden" id="level-price-${skuVO.id}" value="${skuVO.levelPriceDTO.rangePriceShowText}"/>
			<#elseif skuVO.priceType==1 >
                <span class="price t">￥${skuVO.fob}</span>
                <input type="hidden" id="price-${skuVO.id}" value="${skuVO.fob}"/>
                <span class="zhehou-price zhehou-price-${skuVO.id}"></span>
			<#else >
				<#if skuVO.skuPriceRangeList ??>
                    <span class="price">￥
						<#list skuVO.skuPriceRangeList  as priceReange>
							<#if priceReange_index==0>
							${priceReange.price}
							</#if>
							<#if !priceReange_has_next>
                                ～${priceReange.price}
							</#if>
						</#list>
                                                   </span>
				</#if>
			</#if>
		</#if>
	<#else>
        <span class="login_show">价格登录可见</span>
	</#if>
    <!--正常显示价格样式
    <span class="meiyuan">￥</span><span class="price">22.50</span>-->
    <!--价格登录可见样式-->
    <!--<span class="login_show">价格登录可见</span>-->
    <!--暂无购买权限样式-->
    <!--<span class="noPermission">暂无购买权限</span>-->
    <#--  TODO:  -->
        <br>
    <#if skuVO.unitPrice?? && skuVO.unitPrice != '' && !(skuVO.isControl == 1 && !skuVO.isPurchase) >
      <span style="color: #FF3535;font-size: 10px;display: inline-block;background-color: transparent;border: 1px solid #FF3535;padding: 0px 1px;border-radius: 3px">${skuVO.unitPrice}</span>  
    </#if>
</div>

<div class="row-biaoqian">
    <#if (merchant ? exists)>
        <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
         <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
         <#elseif skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0' >
        <#elseif skuVO.agreementEffective?? && skuVO.agreementEffective =='3'>
         <#else >
             <#list skuVO.tagList as item >
                 <#if item_index < 3>
                     <#if (item.uiType == 4)>
                    <span class="default">${item.name}</span>
                     </#if>
                     <#if item.uiType == 1>
                    <span class="linqi">${item.name}</span>
                     </#if>
                     <#if item.uiType == 2>
                    <span class="quan">${item.name}</span>
                     </#if>
                     <#if (item.uiType == 3)>
                    <span class="manjian">${item.name}</span>
                     </#if>
                     <#--<#if (item.uiType == 5)>-->
                         <#--<span class="yibao">${item.name}</span>-->
                     <#--</#if>-->
                 </#if>
             </#list>
         </#if>
    </#if>


</div>
<div class="row-last">
	<#if (merchant ? exists)>
        <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
        <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
        <#elseif skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0' >
        <#elseif skuVO.agreementEffective?? && skuVO.agreementEffective =='3'>
<#--            <span class="noPermission">协议已冻结，价格解冻后可见</span>-->
        <#elseif skuVO.showAgree =='0'>
        <#elseif skuVO.isControl ==1 >
            <#if skuVO.isPurchase>
                <#if (skuVO.uniformPrice ?? ) && (skuVO.uniformPrice != '')>
                <div class="kongxiao-box">
                    <span class="s-kx">控销价</span><span class="jg">￥${skuVO.uniformPrice}</span>
                </div>
                </#if>
                <#if (skuVO.suggestPrice ?? ) && (skuVO.suggestPrice != '') && (skuVO.suggestPrice > 0)>
                <div class="kongxiao-box">
                    <span class="s-kx">零售价</span><span class="jg">￥${skuVO.suggestPrice}</span>
                </div>
                </#if>
                <#if (skuVO.grossMargin ??) && (skuVO.grossMargin != '')>
                <div class="maoli-box">
                    <span class="s-ml">毛利</span><span class="jg">${skuVO.grossMargin}</span>
                </div>
                </#if>
            </#if>
        <#else>
            <#if (skuVO.uniformPrice ?? ) && (skuVO.uniformPrice != '')>
            <div class="kongxiao-box">
                <span class="s-kx">控销价</span><span class="jg">￥${skuVO.uniformPrice}</span>
            </div>
            </#if>
            <#if (skuVO.suggestPrice ?? ) && (skuVO.suggestPrice != '') && (skuVO.suggestPrice > 0)>
            <div class="kongxiao-box">
                <span class="s-kx">零售价</span><span class="jg">￥${skuVO.suggestPrice}</span>
            </div>
            </#if>
            <#if (skuVO.grossMargin ??) && (skuVO.grossMargin != '')>
            <div class="maoli-box">
                <span class="s-ml">毛利</span><span class="jg">${skuVO.grossMargin}</span>
            </div>
            </#if>
        </#if>
	</#if>
</div>
    <div class="row7">
        <#if skuVO.isThirdCompany == 0 && (skuVO.titleTagList?? && skuVO.titleTagList?size > 0)><span class="ziying">${skuVO.titleTagList[0].text}</span></#if>
        <#if skuVO.shopName ??><a href="javascript:;" style="text-decoration: none;width: 132px;display: inline-block;overflow: hidden;vertical-align: middle;text-overflow: ellipsis;white-space: nowrap;">${skuVO.shopName}</a></#if>
    </div>
<div class="row6-box showorno">
    <!--中包装-->
    <div class="dt-zbz">
        <#if skuVO.validity?? && skuVO.validity != ''>
            <span class="sp1">效期</span>
            <span class="sp2">${skuVO.validity}</span>
        <#elseif ((skuVO.nearEffect?? && skuVO.nearEffect != '') && (skuVO.farEffect?? && skuVO.farEffect != ''))>
            <#if skuVO.nearEffect == skuVO.farEffect>
                <span class="sp1">效期</span>
                <span class="sp2">${skuVO.farEffect}</span>
            <#else>
                <span class="sp1">效期</span>
                <span class="sp2">${skuVO.nearEffect+"/"+skuVO.farEffect}</span>
            </#if>
        </#if>
<#--        <span class="sp1">中包装:${skuVO.mediumPackageNum}${skuVO.productUnit}</span>-->
<#--        <#if skuVO.isSplit == 0>-->
<#--            <span class="sp2">不可拆零</span>-->
<#--        </#if>-->
    </div>
<#--    <#if skuVO.isOEM?? && skuVO.isOEM == 'true' && (skuVO.signStatus == '0' || skuVO.agreementEffective =='0')>-->
    <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
        <div class="verifyBox">
            <a href="/merchant/center/license/findLicenseCategoryInfo.htm">资质认证</a>
        </div>
    <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
        <div class="verifyBox">
            <a href="/merchant/center/license/findLicenseCategoryInfo.htm">资质审核中</a>
        </div>
    <#elseif skuVO.agreementEffective?? && skuVO.agreementEffective =='3'>
<#--        <span class="noPermission">协议已冻结，价格解冻后可见</span>-->
    <#elseif skuVO.isOEM?? && skuVO.isOEM == 'true' && (skuVO.signStatus == '0' || skuVO.agreementEffective =='0')>
    <#else >
        <div class="row6">
            <a href="javascript:void(0);" class="sub fl" onclick="action_list_product_btn_click_skuVo(this,event,1,'减');">-</a>
            <input class="fl" type="text" value="${skuVO.cartProductNum}" id="buyNum_${skuVO.id}" name="buyNum"
                   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                   onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"
                   isSplit="${skuVO.isSplit}" middpacking="${skuVO.mediumPackageNum}" onclick="action_list_product_btn_click_skuVo(this,event,2,this.value);" />
            <a href="javascript:void(0);" class="add fl" onclick="action_list_product_btn_click_skuVo(this,event,3,'加');">+</a>
            <a href="javascript:void(0);" class="buy fl"
               onclick="action_list_product_btn_click_skuVo(this,event,4,'加入采购单');addListCart(${skuVO.id},${skuVO.mediumPackageNum},${skuVO.isSplit},event,this)"
               id="href_DT_${skuVO.id}">加入采购单</a>
            <!--灰色样式-->
            <!--<a href="javascript:void(0);" class="gary fl">加入采购单</a>-->
        </div>
    </#if>
</div>
<!--收藏 已收藏类名hasCollect  未收藏类名nopCollect-->
    <#--<#if skuVO.isOEM?? && skuVO.isOEM == 'true' && (skuVO.signStatus == '0' || skuVO.agreementEffective =='0')>-->
    <#--<#else >-->
        <#if skuVO.favoriteStatus == 1>
            <div class="w-collectZone hasCollect initial j-collectBtn" id="${skuVO.id}"
                 onclick="removeSC(${skuVO.id},event,this)">
                <div class="zone-1">
                    <div class="top top-1">
                        <span class="w-icon-normal icon-normal-collectEpt"></span>
                    </div>
                    <div class="top top-2">
                        <span class="w-icon-normal icon-normal-collectFull"></span>
                    </div>
                </div>
                <div class="zone-2">
                    <div class="bottom bottom-1">
                        <p class="textOne">收藏</p>
                    </div>
                    <div class="bottom bottom-2">
                        <p class="textTwo">已收藏</p>
                    </div>
                </div>
            </div>
        <#else>
            <div class="w-collectZone nopCollect initial j-collectBtn" id="${skuVO.id}"
                 onclick="addDTSX(${skuVO.id},event,this)">
                <div class="zone-1">
                    <div class="top top-1">
                        <span class="w-icon-normal icon-normal-collectEpt"></span>
                    </div>
                    <div class="top top-2">
                        <span class="w-icon-normal icon-normal-collectFull"></span>
                    </div>
                </div>
                <div class="zone-2">
                    <div class="bottom bottom-1">
                        <p class="textOne">收藏</p>
                    </div>
                    <div class="bottom bottom-2">
                        <p class="textTwo">已收藏</p>
                    </div>
                </div>
            </div>
        </#if>
    <#--</#if>-->

<!--角标begin-->
<!--药狂欢角标 默认隐藏 去掉noshow显示-->
	<#--<#if skuVO.markerUrl?? && skuVO.markerUrl!=''>-->
    <#--<div class="yaokuanghuan-pos">-->
        <#--<img src="${productImageUrl}/${skuVO.markerUrl}" alt="">-->
    <#--</div>-->
    <#--</#if>-->
<#--<div class="jiaobiaobox">-->
	<#--<#if skuVO.promotionTag?? && skuVO.promotionTag!=''>-->
        <#--<div class="xiangou-pos">-->
            <#--<span>${skuVO.promotionTag }</span>-->
        <#--</div>-->
	<#--</#if>-->

    <#--<!--近期角标 默认隐藏 去掉noshow显示&ndash;&gt;-->
	<#--<#if skuVO.validity?? && skuVO.validity != ''>-->
        <#--<div class="jingqi-pos ">-->
            <#--<span>临期特价</span>-->
        <#--</div>-->
	<#--</#if>-->

    <#--<!--满减角标 默认隐藏 去掉noshow显示&ndash;&gt;-->
	<#--<#if skuVO.isBuyReward==1>-->
        <#--<div class="manjian-pos">-->
            <#--<span>满减</span>-->
        <#--</div>-->
	<#--</#if>-->

	<#--<#if skuVO.status==5>-->
        <#--<div class="manjian-pos">-->
            <#--<span>秒杀</span>-->
        <#--</div>-->
	<#--</#if>-->
<#--</div>-->
    <!--建议零售价或者建议统一价-->
    <#--<#if (merchant ? exists)>-->
        <#--<#if skuVO.isControl ==1 >-->
            <#--<#if skuVO.isPurchase>-->
                <#--<#if (skuVO.suggestPrice ?? ) && (skuVO.suggestPrice != '')>-->
                    <#--<div class="jylsj">-->
                        <#--<span>建议零售价：</span><span>￥${skuVO.suggestPrice}</span>-->
                    <#--</div>-->
                <#--</#if>-->
            <#--</#if>-->
        <#--<#else>-->
            <#--<#if (skuVO.suggestPrice ?? ) && (skuVO.suggestPrice != '')>-->
                <#--<div class="jylsj">-->
                    <#--<span>建议零售价：</span><span>￥${skuVO.suggestPrice}</span>-->
                <#--</div>-->
            <#--</#if>-->
        <#--</#if>-->
    <#--</#if>-->
<!--不参与返点提示-->
	<#if (skuVO.blackProductText)!>
    <div class="nofd">
	${skuVO.blackProductText}
    </div>
	</#if>
<!--end-->
</li>
<script>
    /**获取折后价**/
    var merchantId = $("#merchantId").val();
    // var branchCode = $("#branchCode").val();
    function getPrice(){
        var productList = $(".mrth-new li");
        var idList = '';
        for(var i = 0;i < productList.length;i++){
            if(i < productList.length-1){
                idList += $(productList[i]).find("#skuId").val() + ',';
            }else{
                idList += $(productList[i]).find("#skuId").val() + '';
            }
        }
        if(idList){
            var idListArr = idList.split(",");
            if(idListArr && idListArr.length > 0){
                $.ajax({
                    url: "/marketing/discount/satisfactoryInHandPrice",
                    type: "POST",
                    dataType: "json",
                    data: {
                        merchantId: merchantId,
                        skuIds: idListArr[idListArr.length - 1]
                        // branchCode: branchCode
                    },
                    success: function(result){
                        if(result){
                            if(result.data){
                                var priceList = result.data;
                                if(priceList && priceList.length>0){
                                    priceList.forEach(function(item,index){
                                        var price = $('#level-price-' + item.skuId).val();
                                        //阶梯价取最小值
                                        price = price ? price.split("-")[0] : $("#price-" + item.skuId).val();
                                        idListArr.forEach(function(item1,index1){
                                            if(item.skuId == item1 && item.price && !isNaN(price) && Number(price) > Number(item.price.split("￥")[1])){
                                                var str = item.price
                                                $(".zhehou-price-"+item1).html(str);
                                            }
                                        })
                                    })
                                }
                            }
                        }else{
                            $.alert(result.errorMsg);
                        }
                    }
                })
            }

        }

    }
    getPrice();
</script>
<script>
//qt埋点
function action_list_product_btn_click_skuVo(el,event,index,name){
   try{
     if(window.companyShopBtnClick){
        window.companyShopBtnClick(el,event,index,name)
     }
   }  catch (e) {
     console.log(e)
   }
}
</script>
</#macro>