package com.xyy.ec.pc.authentication.consts;

/**
 * <AUTHOR>
 * @Date 2024/4/10 14:28
 * @File CacheConstants.class
 * @Software IntelliJ IDEA
 * @Description
 */
public class CacheConstants {

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    public static final String KICK_OUT_PC = "KICK_OUT_PC:";

    public static final String CRAWLER_IP = "CRAWLER_IP:";

    public static final String CRAWLER = "CRAWLER:";

    public static final String CRAWLER_CODE = "CRAWLER_CODE:";

    /** 登出用户短信验证key，PC端 **/
    public static final String KICK_OUT_VERIFY_PC = "KICK_OUT_VERIFY_PC:";
}