package com.xyy.ec.pc.newfront.rpc.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.iwhalecloud.xyy.cms.api.HotWordApi;
import com.iwhalecloud.xyy.cms.dto.CmsHotWordDto;
import com.iwhalecloud.xyy.cms.dto.param.HotWordShowListParam;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pc.newfront.rpc.CmsNewRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.util.List;
@Slf4j
@Service
public class CmsNewRpcServiceImpl implements CmsNewRpcService {

    @Reference(version = "1.0.0")
    HotWordApi hotWordApi;

    @Override
    public ApiRPCResult<List<CmsHotWordDto>> frontHotWords(HotWordShowListParam param) {
        try {
            ApiRPCResult<List<CmsHotWordDto>> result = hotWordApi.frontHotWords(param);
            if (result.isFail()){
                log.error("HotWordApi.frontHotWords 热词异常 error:{}",result.getMsg());
            }
           return result;
        }catch (Exception e){
            log.error("HotWordApi.frontHotWords 热词未知异常 error:{}",e.getMessage(),e);
            throw new RuntimeException("HotWordApi.frontHotWords 热词未知异常");
        }
    }
}
