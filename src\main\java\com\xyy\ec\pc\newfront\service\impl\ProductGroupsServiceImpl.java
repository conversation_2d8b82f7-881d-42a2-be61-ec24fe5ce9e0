package com.xyy.ec.pc.newfront.service.impl;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.enums.MerchantLicenseEnum;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.cms.constants.CmsConstants;
import com.xyy.ec.pc.cms.dto.CmsListProductDto;
import com.xyy.ec.pc.cms.dto.CmsRequestDTO;
import com.xyy.ec.pc.cms.enums.LayoutComponentEnum;
import com.xyy.ec.pc.cms.helpers.CmsListProductVOHelper;
import com.xyy.ec.pc.cms.helpers.CmsRequestHelper;
import com.xyy.ec.pc.cms.param.CmsExpectProductQueryParam;
import com.xyy.ec.pc.cms.param.CmsProductQueryParam;
import com.xyy.ec.pc.cms.param.CmsRequestParam;
import com.xyy.ec.pc.cms.service.CmsSkuService;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.enums.LayoutMerchantStatusEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.helper.ProductDataFilterHelper;
import com.xyy.ec.pc.model.dto.XyyIpAddressInfoDTO;
import com.xyy.ec.pc.newfront.service.ProductGroupsService;
import com.xyy.ec.pc.newfront.vo.AddPurchaseCalculationParam;
import com.xyy.ec.pc.newfront.vo.ProductGroupsVO;
import com.xyy.ec.pc.newfront.vo.ProductParam;
import com.xyy.ec.pc.remote.MerchantBusinessRemoteService;
import com.xyy.ec.pc.remote.ProductForLayoutRemoteService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.rpc.ProductServiceRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.XyyIpAddressService;
import com.xyy.ec.pc.service.layout.LayoutBaseService;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.pc.util.ProductMangeUtils;
import com.xyy.ec.pc.util.RandomUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.product.business.dto.common.CommonProductProperty;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ms.marketing.nine.chapters.api.estimation.EstimationDiscountPriceApi;
import com.xyy.ms.marketing.nine.chapters.api.estimation.dto.DiscountPrice;
import com.xyy.ms.marketing.nine.chapters.api.estimation.dto.DiscountPriceRequest;
import io.netty.util.internal.ConcurrentSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ProductGroupsServiceImpl implements ProductGroupsService {

    @Resource
    private CmsSkuService cmsSkuService;

    @Resource
    private LayoutBaseService layoutBaseService;

    @Resource
    private XyyIndentityValidator xyyIndentityValidator;

    @Resource
    private CmsAppProperties cmsAppProperties;

    @Resource
    private ProductServiceRpc productServiceRpc;

    @Resource
    private MarketingService marketingService;

    @Resource
    private AppProperties appProperties;

    @Resource
    private XyyIpAddressService xyyIpAddressService;

    @Reference(version = "1.0.0", timeout = 60000)
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0", timeout = 600)
    private EstimationDiscountPriceApi estimationDiscountPriceApi;


    @Autowired
    private MerchantBusinessRemoteService merchantBusinessRemoteService;

    @Autowired
    private ProductForLayoutRemoteService productForLayoutRemoteService;


    private static final long MAX_CALCULATE_TIME = 1000L;
    private static volatile ConcurrentSet<String> BLACK_LIST = new ConcurrentSet<>();

    @Value("${hand.price.black.list}")
    public void setDiscountPriceBlackList(String blackList) {
        doSetDiscountPriceBlackList(blackList);
    }

    private synchronized void doSetDiscountPriceBlackList(String blackList) {
        if (StringUtils.isBlank(blackList)) {
            BLACK_LIST = new ConcurrentSet<>();
        }
        try {
            String[] split = blackList.split(",");
            if (split.length > 0) {
                ConcurrentSet<String> blackSet = new ConcurrentSet<>();
                blackSet.addAll(Lists.newArrayList(split));
                BLACK_LIST = blackSet;
            }
        } catch (Exception e) {
            log.error("setDiscountPriceBlackList_blackList={}", blackList, e);
        }
        log.info("doSetDiscountPriceBlackList_blackList={},BLACK_LIST={}", blackList, JSON.toJSONString(BLACK_LIST));
    }

    @Override
    public AjaxResult<ProductGroupsVO> listExpectProducts(Integer terminalType, CmsRequestParam cmsRequestParam, HttpServletRequest request) {
        try {

            CmsRequestDTO cmsRequestDTO = CmsRequestHelper.createDTO(cmsRequestParam);
            if (cmsRequestDTO == null) {
                cmsRequestDTO = new CmsRequestDTO();
            }
            Integer expectedProductNum = cmsRequestDTO.getExpectedProductNum();
            String exhibitionIdStr = cmsRequestDTO.getExhibitionIdStr();
            Boolean isFillActPt = cmsRequestDTO.getIsFillActPt();
            Boolean isFillActPgby = cmsRequestDTO.getIsFillActPgby();
            if (Objects.isNull(expectedProductNum) || expectedProductNum <= 0 || StringUtils.isEmpty(exhibitionIdStr)) {
                return AjaxResult.errResult(CmsConstants.MSG_ERROR);
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = null;
            if (Objects.nonNull(merchant)) {
                merchantId = merchant.getId();
            }
            String realBranchCode = getBranchCodeByMerchantId(request, merchantId);

            CmsExpectProductQueryParam cmsExpectProductQueryParam = CmsExpectProductQueryParam.builder()
                    .merchantId(merchantId).exhibitionId(exhibitionIdStr).branchCode(realBranchCode)
                    .terminalType(terminalType).expectNum(expectedProductNum).build();
            List<ListProduct> products = cmsSkuService.listExpectProducts(cmsExpectProductQueryParam);
            // 商品信息根据资质状态过滤
            Integer licenseStatus;
            LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
            licenseStatus = layoutBaseService.getLicenseStatus(merchant);
            if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                products = layoutBaseService.setProductProperties(products, statusEnum, request);
            }
            // 处方药商品默认图处理
            if (log.isDebugEnabled()) {
                log.debug("【处方药商品默认图处理】merchantId：{}，原商品信息：{}", merchantId, JSONArray.toJSONString(products));
            }
            if ((Objects.isNull(cmsRequestParam.getModuleSourceType()) || Objects.equals(cmsRequestParam.getModuleSourceType(), LayoutComponentEnum.PC_INDEX_PRODUCT_FLOOR.getModuleSourceType().toString()))
                    && BooleanUtils.isTrue(cmsAppProperties.getIsOpenIndexProductDefaultImageFeature())) {
                String defaultImageUrl = cmsAppProperties.getProductDefaultImageUrl();
                products.stream().filter(item -> Objects.equals(item.getDrugClassification(), 3))
                        .forEach(item -> item.setImageUrl(defaultImageUrl));
                if (log.isDebugEnabled()) {
                    log.debug("【处方药商品默认图处理】merchantId：{}，处理后商品信息：{}", merchantId, JSONArray.toJSONString(products));
                }
            }
            List<CmsListProductVO> cmsListProductVOS = CmsListProductVOHelper.creates(products);
            cmsListProductVOS = cmsSkuService.fillListProductsShopInfo(cmsListProductVOS);
            cmsListProductVOS = cmsSkuService.fillListProductsTagInfo(merchantId, cmsListProductVOS, true);
            //填充库存信息
            cmsListProductVOS = productServiceRpc.fillCmsActTotalSurplusQtyToAvailableQty(cmsListProductVOS);

            if ((BooleanUtils.isTrue(isFillActPt) || BooleanUtils.isTrue(isFillActPgby)) && CollectionUtils.isNotEmpty(products)) {
                // 尝试填充拼团活动信息
                List<Long> skuIdList = products.stream().map(ListProduct::getId).distinct().collect(Collectors.toList());
                Set<Long> gaoMaoSkuIdSet = Sets.newHashSet();
                gaoMaoSkuIdSet = cmsListProductVOS.stream().filter(productDto -> null != productDto
                                && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                                || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode())))
                        .map(CmsListProductVO::getId).collect(Collectors.toSet());
                Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap = cmsSkuService.getMarketingActivityInfoBySkuIdList(skuIdList,
                        Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()), merchantId,
                        Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
                cmsListProductVOS = cmsSkuService.fillListProductsMarketingActivityInfo(cmsListProductVOS, csuIdToGroupBuyingInfoMap);
                if (Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NO_LOGIN)) {
                    cmsListProductVOS = cmsSkuService.hideListProductsMarketingGroupBuyingActivityPriceInfo(cmsListProductVOS);
                }
            }

            // 获取商品折扣信息
            Set<Long> skuId = cmsListProductVOS.stream().map(CommonProductProperty::getId).collect(Collectors.toSet());
            HashMap<Long, String> priceMap = satisfactoryInHandPrice(merchant, Lists.newArrayList(skuId), request);


            if (CollectionUtils.isNotEmpty(cmsListProductVOS)) {
                for (CmsListProductVO productVO : cmsListProductVOS) {
                    productVO.setPrice(priceMap.get(productVO.getId()));
                    // 移除价格
                    if (StrUtil.isNotEmpty(productVO.getControlTitle()) && productVO.getControlType() != 5) {
                        handleProductVO(productVO);
                    }
                    //受托生产厂家处理
                    if (StringUtils.isNotBlank(productVO.getEntrustedManufacturer())) {
                        productVO.setManufacturer(ProductMangeUtils.getManufacturer(productVO.getMarketAuthor(), productVO.getManufacturer(), productVO.getEntrustedManufacturer()));
                    }
                }
            }

            ProductDataFilterHelper.fillBlankCms(cmsListProductVOS);


            return AjaxResult.successResult(
                    ProductGroupsVO.builder()
                            .productVOList(cmsListProductVOS)
                            .licenseStatus(licenseStatus)
                            .merchantId(merchant == null ? null : merchant.getId())
                            .scmE(RandomUtil.nanoId(8))
                            .build());


        } catch (
                AppException e) {
            if (e.isWarn()) {
                log.error("获取期望数量的商品流失败，terminalType：{}，cmsRequestParam：{}，msg：{}",
                        terminalType, JSONObject.toJSONString(cmsRequestParam), e.getMsg());
            }
            return AjaxResult.errResult(e.getMsg());
        } catch (Exception e) {
            log.error("获取期望数量的商品流异常，terminalType：{}，cmsRequestParam：{}",
                    terminalType, JSONObject.toJSONString(cmsRequestParam), e);
            return AjaxResult.errResult(CmsConstants.MSG_ERROR);
        }
    }

    /**
     * 处理商品价格
     */
    @Override
    public void handleProductVO(CmsListProductVO productVO) {
        productVO.setFob(null);
        productVO.setRetailFob(null);
        productVO.setRetailPrice(null);
        productVO.setDefaultFob(null);
        productVO.setPrescriptionPrice(null);

        if (productVO.getActSk() != null) {
            productVO.getActSk().setSkPrice(null);
        }

        if (productVO.getActPt() != null) {
            productVO.getActPt().setMinSkuPrice(null);
            productVO.getActPt().setMaxSkuPrice(null);
            productVO.getActPt().setAssemblePrice(null);
        }

        if (productVO.getActPgby() != null) {
            productVO.getActPgby().setAssemblePrice(null);
        }

        if (productVO.getLevelPriceDTO() != null) {
            productVO.getLevelPriceDTO().setMaxSkuPrice(null);
            productVO.getLevelPriceDTO().setMinSkuPrice(null);
        }

    }


    /**
     * 通过用户id获取用户所在的域编码(Long类型) 未登录时按请求ip获取域编码
     */
    protected String getBranchCodeByMerchantId(HttpServletRequest request, Long merchantId) {
        if (merchantId == null || merchantId == 0L) {
            XyyIpAddressInfoDTO xyyIpAddressInfo = xyyIpAddressService.getXyyIpAddressInfo(request);
            return xyyIpAddressInfo.getBranchCode();
        } else {
            return getBranchCodeByMerchantId(merchantId);
        }
    }

    /**
     * 通过用户id获取用户所在的域编码
     */
    protected String getBranchCodeByMerchantId(Long merchantId) {
        String branchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchantId);
        if (StringUtil.isEmpty(branchCode)) {
            branchCode = BranchEnum.HUBEI_COUNTRY.getKey();
        }
        return branchCode;
    }

    @Override
    public HashMap<Long, String> satisfactoryInHandPrice(MerchantBussinessDto merchant, List<Long> skuIds, HttpServletRequest request) {
        HashMap<Long, String> priceMap = new HashMap<>();
        if (null == merchant) {
            return priceMap;
        }
        // 黑名单
        if (getIsInBlackList(request)) {
            return priceMap;
        }

        if (CollectionUtils.isNotEmpty(skuIds)) {
            List<DiscountPrice> discountPrices = Lists.newArrayList();
            long startTime = System.currentTimeMillis();
            if (log.isDebugEnabled()) {
                log.debug("satisfactoryInHandPrice_skuIdList={}", skuIds.size());
            }
            int counter = 0;
            for (List<Long> part : Lists.partition(skuIds, 20)) {
                counter++;
                if ((System.currentTimeMillis() - startTime) > MAX_CALCULATE_TIME) {
                    log.info("satisfactoryInHandPrice_计算未完成_skuIdList={},counter={}", skuIds.size(), counter);
                    break;
                }
                try {
                    DiscountPriceRequest discountPriceRequest = new DiscountPriceRequest();
                    discountPriceRequest.setBranchCode(merchant.getRegisterCode());
                    discountPriceRequest.setKa(merchant.getIsKa());
                    discountPriceRequest.setMerchantId(merchant.getId());
                    discountPriceRequest.setSkuIdList(part);
                    List<DiscountPrice> prices = estimationDiscountPriceApi.mgetDiscountPrice(discountPriceRequest);
                    discountPrices.addAll(prices);
                } catch (Exception e) {
                    log.error("satisfactoryInHandPrice_merchantId={},skuIds={}", merchant.getId(), skuIds, e);
                }
            }

            if (CollectionUtils.isNotEmpty(discountPrices)) {
                for (DiscountPrice price : discountPrices) {
                    if (null == price || null == price.getFob() || null == price.getPrice() || price.getFob().compareTo(price.getPrice()) <= 0) {
                        if (log.isDebugEnabled()) {
                            log.debug("satisfactoryInHandPrice_不展示_price={}", JSON.toJSONString(price));
                        }
                        continue;
                    }
                    if (price.getFob().compareTo(price.getPrice()) < 0) {
                        if (log.isDebugEnabled()) {
                            log.debug("satisfactoryInHandPrice_不展示_原价小于到手价_price={}", JSON.toJSONString(price));
                        }
                        continue;
                    }
                    priceMap.put(price.getSkuId(), "折后约￥" + price.getPrice().toString());
                }
            }
        }
        return priceMap;
    }


    private synchronized boolean getIsInBlackList(HttpServletRequest request) {
        if (null == request) {
            return false;
        }
        String userRealIp = IPUtils.getClientIP(request);
        boolean result = StringUtils.isNotBlank(userRealIp) && BLACK_LIST.contains(userRealIp);
        if (result) {
            log.info("getIsInBlackList_in_userRealIp={}", userRealIp);
        }
        return result;
    }


    @Override
    public AjaxResult<Integer> calculateTheNumOfShoppingCarts(AddPurchaseCalculationParam param) {
        return AjaxResult.successResult(cal(param.getLeastPurchaseNum(), param.getMediumPackageNum(), param.getSplit(), param.getCurrent(), param.getRule()));
    }


    @Override
    public AjaxResult<ProductGroupsVO> listProducts(ProductParam param, HttpServletRequest request) {
        String branchCode = param.getBranchCode();
        String exhibitionId = param.getExhibitionIdStr();
        Boolean isAdmin = param.getIsAdmin();
        Integer terminalType = param.getTerminalType();
        Integer moduleSourceType = param.getModuleSourceType();
        Boolean isRecommend = param.getIsRecommend();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        String anchorCsuIdsStr = param.getAnchorCsuIds();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = null;
            if (Objects.nonNull(merchant)) {
                merchantId = merchant.getId();
            }
            String realBranchCode;
            if (!BooleanUtils.isTrue(isAdmin)) {
                realBranchCode = getBranchCodeByMerchantId(request, merchantId);
            } else {
                realBranchCode = branchCode;
            }

            CmsProductQueryParam cmsProductQueryParam = CmsProductQueryParam.builder()
                    .isAdmin(isAdmin).merchantId(merchantId).exhibitionId(exhibitionId).branchCode(realBranchCode)
                    .moduleSourceType(moduleSourceType).isRecommend(isRecommend).terminalType(terminalType).build();
            CmsListProductDto cmsListProductDto = cmsSkuService.listProducts(cmsProductQueryParam, pageNum, pageSize);
            PageInfo<ListProduct> pageInfo = cmsListProductDto.getPageInfo();
            // 商品信息根据资质状态过滤
            Integer licenseStatus;
            List<ListProduct> products = pageInfo.getList();
            // 处理锚点数据
            products = handleAnchorGoods(anchorCsuIdsStr, pageNum, exhibitionId, merchantId, products);

            if (BooleanUtils.isFalse(isAdmin)) {
                LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
                licenseStatus = layoutBaseService.getLicenseStatus(merchant);
                if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                    products = layoutBaseService.setProductProperties(products, statusEnum, request);
                    pageInfo.setList(products);
                }
            } else {
                licenseStatus = MerchantLicenseEnum.sy_passed.getId();
            }
            //推荐商品或者控销商品符合下列条件之一，则为最后一页
            Boolean resultIsRecommend = cmsListProductDto.getIsRecommend();

            List<CmsListProductVO> cmsListProductVOS = CmsListProductVOHelper.creates(products);
            if (CollectionUtils.isNotEmpty(cmsListProductVOS)) {
                if (BooleanUtils.isTrue(isAdmin) || Objects.nonNull(merchantId)) {
                    // 获取拼团或批购包邮活动信息
                    List<Long> csuIds = cmsListProductVOS.stream().filter(Objects::nonNull).map(CmsListProductVO::getId).filter(Objects::nonNull).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(csuIds)) {
                        Set<Long> gaoMaoSkuIdSet = cmsListProductVOS.stream().filter(productDto -> null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross()) || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))).map(CmsListProductVO::getId).collect(Collectors.toSet());
                        Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap = cmsSkuService.getMarketingActivityInfoBySkuIdList(csuIds, Lists.newArrayList(MarketingQueryStatusEnum.UN_START.getType(), MarketingQueryStatusEnum.STARTING.getType()), merchantId, Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
                        // 填充拼团或批购包邮活动信息
                        cmsListProductVOS = cmsSkuService.fillListProductsMarketingActivityInfo(cmsListProductVOS, csuIdToGroupBuyingInfoMap);

                        List<Long> skIdList = cmsListProductVOS.stream().filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.SECKILL_SKU_TYPE.getId())).map(CmsListProductVO::getId).collect(Collectors.toList());
                        Map<Long, MarketingSeckillActivityInfoDTO> csuIdToSeckillActivityInfoDTOMap = marketingService.getShowingSeckillActivityInfoByCsuIdsForSearch(merchantId, skIdList);
                        cmsListProductVOS.forEach(item -> item.setActSk(csuIdToSeckillActivityInfoDTOMap.get(item.getId())));
                    }
                }
                cmsListProductVOS = cmsSkuService.fillListProductsShopInfo(cmsListProductVOS);
                cmsListProductVOS = cmsSkuService.fillListProductsTagInfo(merchantId, cmsListProductVOS, true);
                //填充库存信息
                cmsListProductVOS = productServiceRpc.fillCmsActTotalSurplusQtyToAvailableQty(cmsListProductVOS);

                // 获取商品折扣信息
                Set<Long> skuId = cmsListProductVOS.stream().map(CommonProductProperty::getId).collect(Collectors.toSet());
                HashMap<Long, String> priceMap = satisfactoryInHandPrice(merchant, Lists.newArrayList(skuId), request);


                for (CmsListProductVO productVO : cmsListProductVOS) {
                    productVO.setPrice(priceMap.get(productVO.getId()));
                    // 移除价格
                    if (StrUtil.isNotEmpty(productVO.getControlTitle()) && productVO.getControlType() != 5) {
                        handleProductVO(productVO);
                    }
                    if (productVO.getActPgby() != null) {
                        productVO.setLeastPurchaseNum(productVO.getActPgby().getSkuStartNum());
                    }

                    //受托生产厂家处理
                    if (StringUtils.isNotBlank(productVO.getEntrustedManufacturer())) {
                        productVO.setManufacturer(ProductMangeUtils.getManufacturer(productVO.getMarketAuthor(), productVO.getManufacturer(), productVO.getEntrustedManufacturer()));
                    }
                }
            }

            ProductDataFilterHelper.fillBlankCms(cmsListProductVOS);

            ProductGroupsVO groups = ProductGroupsVO.builder().pageNo(pageInfo.getPageNum())
                    .pageSize(pageInfo.getPageSize())
                    .totalPage(pageInfo.getPages())
                    .totalCount(pageInfo.getTotal())
                    .productVOList(cmsListProductVOS)
                    .licenseStatus(licenseStatus)
                    .merchantId(merchant == null ? null : merchant.getId())
                    .recommend(resultIsRecommend)
                    .scmE(StrUtil.isBlank(param.getScmE()) ? RandomUtil.nanoId(8) : param.getScmE())
                    .tip(cmsListProductDto.getTip()).build();

            if (BooleanUtils.isTrue(isAdmin)) {
                groups.setIsAdmin(isAdmin);
            }

            return AjaxResult.successResult(groups);
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("普通商品查询根据会员ID、商品展示组ID查询商品列表（带翻页功能）失败，isAdmin：{}，exhibitionId：{}",
                        isAdmin, exhibitionId, e);
            }
            throw new RuntimeException();
        } catch (Exception e) {
            log.error("普通商品查询根据会员ID、商品展示组ID查询商品列表（带翻页功能）失败，isAdmin：{}，exhibitionId：{}",
                    isAdmin, exhibitionId, e);
            throw new RuntimeException();
        }

    }

    private List<ListProduct> handleAnchorGoods(String anchorCsuIdsStr, Integer pageNum, String exhibitionId, Long merchantId, List<ListProduct> products) {
        // 锚点商品
        Set<Long> anchorCsuIdsSet = new HashSet<>();
        if (StringUtils.isNotEmpty(anchorCsuIdsStr)) {
            anchorCsuIdsSet = Sets.newHashSet(anchorCsuIdsStr.split(","))
                    .stream().map(item -> {
                        if (StringUtils.isEmpty(item)) {
                            return null;
                        }
                        try {
                            return Long.parseLong(item.trim());
                        } catch (Exception e) {
                            return null;
                        }
                    }).filter(Objects::nonNull).collect(Collectors.toSet());
        }

        List<Long> anchorCsuIds = new ArrayList<>(anchorCsuIdsSet);
        List<ListProduct> resultAnchorCsuInfo = new ArrayList<>();
        // 第一页 需要锚品数据
        if (CollectionUtils.isNotEmpty(anchorCsuIds) && pageNum == 1) {
            //判定商品是否在商品组中 锚品
            List<Long> anchorCsuIdsList = cmsSkuService.listUsingExhibitionProductIds(Lists.newArrayList(exhibitionId), anchorCsuIds);
            //如果不在 则不锚品
            if (CollectionUtils.isNotEmpty(anchorCsuIdsList)) {
                //判断商品是否符合 普通品、批购包邮、拼团 其中之一
                // 填充商品信息
                boolean isNotWatchFragileGoods = merchantBusinessRemoteService.checkIsNotWatchFragileGoods(merchantId);
                List<ListProduct> productList = productForLayoutRemoteService.fillProductInfo(anchorCsuIdsList, merchantId, isNotWatchFragileGoods, null);
                for (ListProduct listProduct : productList) {
                    //如果是 普通 或 拼团 或 批购包邮
                    if (ProductEnumDTO.ProductTypeEnum.NORMAL_SKU_TYPE.getId() == listProduct.getProductType()
                            || ProductEnumDTO.ProductTypeEnum.PROMOTION_SKU_TYPE.getId() == listProduct.getProductType()
                            || ProductEnumDTO.ProductTypeEnum.WHOLESALE_TYPE.getId() == listProduct.getProductType()) {
                        resultAnchorCsuInfo.add(listProduct);
                    }
                }
            }
        }
        //如果锚品符合
        if (CollectionUtils.isNotEmpty(resultAnchorCsuInfo)) {
            //剔除锚品信息
            products = products.stream().filter(x -> !anchorCsuIds.contains(x.getId())).collect(Collectors.toList());
            //头部循环增加锚品信息
            for (int i = 0; i < resultAnchorCsuInfo.size(); i++) {
                products.add(i, resultAnchorCsuInfo.get(i));
            }
        }
        return products;
    }

    /**
     * @param least         起购
     * @param mediumPackage 中包装
     * @param split         可拆零
     * @param current       当前值
     * @param rule          计算规则
     * @return 计算结果
     */
    public static int cal(int least, int mediumPackage, boolean split, int current, int rule) {
        int startNum = least;
        int add = 1;
        if (BooleanUtil.isFalse(split)) {
            // 不可拆零
            startNum = least % mediumPackage == 0 ? least : ((least / mediumPackage) + 1) * mediumPackage;
            add = mediumPackage;
        }
        // 想买的数量
        int buy = current;
        switch (rule) {
            case 1:
                buy += add;
                break;
            case 2:
                buy -= add;
                break;
        }

        if (buy <= 0) {
            return 0;
        }

        if (buy <= startNum) {
            return startNum;
        }

        if ((buy - startNum) % add == 0) {
            return buy;
        } else {
            return (((buy - startNum) / add) + 1) * add + startNum;
        }
    }


}
