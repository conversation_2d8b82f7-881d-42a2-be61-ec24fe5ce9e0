package com.xyy.ec.pc.cms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 拼团商品分类信息DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupBuyingProductCategoryDTO implements Serializable {

    /**
     * 逻辑分类ID
     */
    private Long id;

    /**
     * 逻辑分类名称
     */
    private String name;

    /**
     * 真实的分类ID列表
     */
    private List<Long> categoryIds;
}
