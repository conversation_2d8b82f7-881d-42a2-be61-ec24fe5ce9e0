package com.xyy.ec.pc.constants;

public interface AgreementStatusConstants {

    enum AgreementTypeEnum{
        MONEY_TYPE(1, "金额协议"), ONE_SKU_TYPE(2, "单品协议");
        private int type;
        private String typeText;

        AgreementTypeEnum(int type, String typeText){
            this.type = type;
            this.typeText = typeText;
        }

        public int getType() {
            return type;
        }

        public static AgreementTypeEnum getByKey(int type) {
            AgreementTypeEnum agreementTypeEnum = null;
            for (AgreementTypeEnum tempAgreementTypeEnum : AgreementTypeEnum.values()) {
                if (tempAgreementTypeEnum.getType() == type) {
                    agreementTypeEnum = tempAgreementTypeEnum;
                    break;
                }
            }
            return agreementTypeEnum;
        }
    }

    /**
     * 协议状态
     */
    enum AgreementStatusEnum{
        EFFECTIVE_NO(0, "未生效"), EFFECTIVE_DOING(1, "生效中"), EFFECTIVE_FROZEN(3, "冻结"), EFFECTIVE_EXPIRY(4, "已过期");

        private int status;
        private String statusText;

        AgreementStatusEnum(int status, String statusText){
            this.status = status;
            this.statusText = statusText;
        }

        public int getStatus() {
            return status;
        }

        public String getStatusText() {
            return statusText;
        }

        public static AgreementStatusEnum getByKey(int status) {
            AgreementStatusEnum agreementStatusEnum = null;
            for (AgreementStatusEnum tempAgreementStatusEnum : AgreementStatusEnum.values()) {
                if (tempAgreementStatusEnum.getStatus() == status) {
                    agreementStatusEnum = tempAgreementStatusEnum;
                    break;
                }
            }
            return agreementStatusEnum;
        }

        @Override
        public String toString() {
            return "AgreementStatusEnum{" +
                    "status='" + status + '\'' +
                    ", statusText='" + statusText + '\'' +
                    '}';
        }
    }

    /**
     * 协议返点金额领取状态
     */
    enum MerchantAgreementMoneyStatusEnum{
        ACTIVE_NO(0, "未领取"), ACTIVE_DONE(1, "已领取"), DISCARD(2, "作废");

        private int status;
        private String statusText;

        MerchantAgreementMoneyStatusEnum(int status, String statusText){
            this.status = status;
            this.statusText = statusText;
        }

        public int getStatus() {
            return status;
        }

        public String getStatusText() {
            return statusText;
        }

        public static MerchantAgreementMoneyStatusEnum getByKey(int status) {
            MerchantAgreementMoneyStatusEnum merchantAgreementMoneyStatusEnum = null;
            for (MerchantAgreementMoneyStatusEnum tempMerchantAgreementMoneyStatusEnum : MerchantAgreementMoneyStatusEnum.values()) {
                if (tempMerchantAgreementMoneyStatusEnum.getStatus() == status) {
                    merchantAgreementMoneyStatusEnum = tempMerchantAgreementMoneyStatusEnum;
                    break;
                }
            }
            return merchantAgreementMoneyStatusEnum;
        }

		public static MerchantAgreementMoneyStatusEnum findByValue(int value){
			switch(value){

				case 0: return ACTIVE_NO;
				case 1: return ACTIVE_DONE;
				case 2: return DISCARD;

				default: return null;

			}

		}

        @Override
        public String toString() {
            return "MerchantAgreementMoneyStatusEnum{" +
                    "status='" + status + '\'' +
                    ", statusText='" + statusText + '\'' +
                    '}';
        }
    }

    /**
     * 协议签署状态
     */
    enum AgreementSignStatusEnum{
        SIGN_NO(0, "未签署"), SIGN_DONE(1, "已签署");

        private int status;
        private String statusText;

        AgreementSignStatusEnum(int status, String statusText){
            this.status = status;
            this.statusText = statusText;
        }

        public int getStatus() {
            return status;
        }

        public String getStatusText() {
            return statusText;
        }

        public static AgreementSignStatusEnum getByKey(int status) {
            AgreementSignStatusEnum agreementSignStatusEnum = null;
            for (AgreementSignStatusEnum tempAgreementSignStatusEnum : AgreementSignStatusEnum.values()) {
                if (tempAgreementSignStatusEnum.getStatus() == status) {
                    agreementSignStatusEnum = tempAgreementSignStatusEnum;
                    break;
                }
            }
            return agreementSignStatusEnum;
        }

        @Override
        public String toString() {
            return "AgreementSignStatusEnum{" +
                    "status=" + status +
                    ", statusText='" + statusText + '\'' +
                    '}';
        }
    }

    enum AgreementSettleTypeEnum{
        MONTY_SETTLE(1, "月度协议"), QUARTER_SETTLE(2, "季度协议"), YEAR_SETTLE(3, "年度协议");
        private int settleType;
        private String settleTypeText;

        AgreementSettleTypeEnum(int settleType, String settleTypeText){
            this.settleType = settleType;
            this.settleTypeText = settleTypeText;
        }

        public static AgreementSettleTypeEnum getByKey(int settleType) {
            AgreementSettleTypeEnum agreementSettleTypeEnum = null;
            for (AgreementSettleTypeEnum tempAgreementSettleTypeEnum : AgreementSettleTypeEnum.values()) {
                if (tempAgreementSettleTypeEnum.getSettleType() == settleType) {
                    agreementSettleTypeEnum = tempAgreementSettleTypeEnum;
                    break;
                }
            }
            return agreementSettleTypeEnum;
        }

        public static AgreementSettleTypeEnum findByValue(int value) {
			switch(value){
				case 0: return MONTY_SETTLE;
				case 1: return QUARTER_SETTLE;
				case 2: return YEAR_SETTLE;
				default: return null;

			}

		}

        public int getSettleType() {
            return settleType;
        }

        public String getSettleTypeText() {
            return settleTypeText;
        }

    }

    /**
     * 协议用户范围
     */
    enum AgreementMerchantRangeEnum{
        ALL_MERCHANT(0, "所有用户"), SPECIAL_MERCHANT(1, "指定用户");
        private int rangeType;
        private String rangeTypeText;

        AgreementMerchantRangeEnum(int rangeType, String rangeTypeText) {
            this.rangeType = rangeType;
            this.rangeTypeText = rangeTypeText;
        }

        public static AgreementMerchantRangeEnum getByKey(int rangeType) {
            AgreementMerchantRangeEnum agreementMerchantRangeEnum = null;
            for (AgreementMerchantRangeEnum tempAgreementMerchantRangeEnum : AgreementMerchantRangeEnum.values()) {
                if (tempAgreementMerchantRangeEnum.getRangeType() == rangeType) {
                    agreementMerchantRangeEnum = tempAgreementMerchantRangeEnum;
                    break;
                }
            }
            return agreementMerchantRangeEnum;
        }

        public int getRangeType() {
            return rangeType;
        }

        public String getRangeTypeText() {
            return rangeTypeText;
        }
    }

    /**
     * 协议黑名单类型
     */
    enum AgreementMerchantBlackListTypeEnum{
        BLACK_TYPE(0, "黑名单"), WHITE_TYPE(1, "白名单");
        private Integer blackListType;
        private String blackListTypeText;

        AgreementMerchantBlackListTypeEnum(Integer blackListType, String blackListTypeText) {
            this.blackListType = blackListType;
            this.blackListTypeText = blackListTypeText;
        }

        public static AgreementMerchantBlackListTypeEnum getByKey(int blackListType) {
            AgreementMerchantBlackListTypeEnum agreementMerchantBlackListTypeEnum = null;
            for (AgreementMerchantBlackListTypeEnum tempAgreementMerchantBlackListTypeEnum : AgreementMerchantBlackListTypeEnum.values()) {
                if (tempAgreementMerchantBlackListTypeEnum.getBlackListType() == blackListType) {
                    agreementMerchantBlackListTypeEnum = tempAgreementMerchantBlackListTypeEnum;
                    break;
                }
            }
            return agreementMerchantBlackListTypeEnum;
        }

        public Integer getBlackListType() {
            return blackListType;
        }

        public String getBlackListTypeText() {
            return blackListTypeText;
        }
    }

    enum AgreementScopeTypeEnum {
        SPECIAL_USER(0, "指定用户"),
        SPECIAL_AREA(1, "指定地区"),;
        int type;
        String typeText;

        AgreementScopeTypeEnum(int type, String typeText) {
            this.type = type;
            this.typeText = typeText;
        }

        AgreementScopeTypeEnum getByType(int type) {
            for (AgreementScopeTypeEnum agreementScopeTypeEnum : values()) {
                if (agreementScopeTypeEnum.getType() == type) {
                    return agreementScopeTypeEnum;
                }
            }
            return null;
        }

        public int getType() {
            return type;
        }

        public String getTypeText() {
            return typeText;
        }
    }

    enum AgreementCategoryEnum {
        GENERAL(0, "普通协议"),
        OEM(1, "OEM协议");
        private int category;
        private String categoryText;

        AgreementCategoryEnum(int category, String categoryText) {
            this.category = category;
            this.categoryText = categoryText;
        }

        public int getCategory() {
            return category;
        }

        public String getCategoryText() {
            return categoryText;
        }
    }
}
