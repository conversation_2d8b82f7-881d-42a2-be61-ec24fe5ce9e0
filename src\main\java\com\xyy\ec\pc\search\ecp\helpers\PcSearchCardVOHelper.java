package com.xyy.ec.pc.search.ecp.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.pc.search.config.SearchProperties;
import com.xyy.ec.pc.search.ecp.vo.PcSearchCardVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchGroupPurchaseVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchOperationVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchProductVO;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchRecPurchaseTypeEnum;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchYesNoEnum;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.SearchProductCardPositionTypeEnum;
import com.xyy.ec.search.engine.ecp.result.EcpSearchBaseCardDTO;
import com.xyy.ec.search.engine.ecp.result.EcpSearchGroupPurchaseCardDTO;
import com.xyy.ec.search.engine.ecp.result.EcpSearchOperationCardDTO;
import com.xyy.ec.search.engine.ecp.result.EcpSearchProductCardDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * {@link PcSearchCardVO} 帮助类
 *
 * <AUTHOR>
 */
public class PcSearchCardVOHelper {

    /**
     * 组合购，副品，显示的最小数量
     */
    public static final int GROUP_PURCHASE_SUB_PRODUCT_MIN_SHOW_SIZE = 2;

    /**
     * 组合购，副品，显示的最小数量
     */
    public static final int GROUP_PURCHASE_SUB_PRODUCT_MAX_SHOW_SIZE = 5;

    /**
     * 创建
     *
     * @param searchBaseCardDTO
     * @param csuIdToInfoMap
     * @param isShowSimilarGoodsJump
     * @return
     */
    public static PcSearchCardVO create(EcpSearchBaseCardDTO searchBaseCardDTO, Map<Long, PcSearchProductVO> csuIdToInfoMap, Boolean isShowSimilarGoodsJump, SearchProperties searchProperties) {
        if (Objects.isNull(searchBaseCardDTO) || MapUtils.isEmpty(csuIdToInfoMap)) {
            return null;
        }

        String groupPurchaseTitle = searchProperties.getGroupPurchaseTitle();
        Boolean isOpenSearchRecPurchaseWholesale = searchProperties.getIsOpenSearchRecPurchaseWholesale();
        Integer searchRecPurchaseCarouselTime = searchProperties.getSearchRecPurchaseCarouselTime();
        if (searchBaseCardDTO instanceof EcpSearchProductCardDTO) {
            EcpSearchProductCardDTO ecpSearchProductCardDTO = (EcpSearchProductCardDTO) searchBaseCardDTO;
            if (Objects.isNull(ecpSearchProductCardDTO.getProduct())) {
                return null;
            }
            PcSearchProductVO productVO = csuIdToInfoMap.get(ecpSearchProductCardDTO.getProduct().getId());
            if (Objects.isNull(productVO)) {
                return null;
            }
            PcSearchCardVO appSearchCardVO = new PcSearchCardVO();
            appSearchCardVO.setId(productVO.getId());
            appSearchCardVO.setCardType(searchBaseCardDTO.getCardType());
            if (CollectionUtils.isNotEmpty(ecpSearchProductCardDTO.getCategorySupportExhibitionIds())) {
                productVO.setCategorySupportExhibitionIdsStr(String.join(",", ecpSearchProductCardDTO.getCategorySupportExhibitionIds()));
            }
            if (CollectionUtils.isNotEmpty(ecpSearchProductCardDTO.getCategorySupportIds())) {
                productVO.setCategorySupportIdsStr(String.join(",", ecpSearchProductCardDTO.getCategorySupportIds().stream().map(String::valueOf).collect(Collectors.toList())));
            }
            productVO.setHasSimilarGoods(BooleanUtils.isTrue(isShowSimilarGoodsJump) && Objects.nonNull(ecpSearchProductCardDTO.getProduct().getSimilarSkuNum())
                    ? EcpSearchYesNoEnum.YES.getValue() : EcpSearchYesNoEnum.NO.getValue());
            Integer positionType = ecpSearchProductCardDTO.getPositionType();
            String positionTypeName;
            SearchProductCardPositionTypeEnum searchProductCardPositionTypeEnum = SearchProductCardPositionTypeEnum.valueOfCustom(positionType);
            if (Objects.nonNull(searchProductCardPositionTypeEnum)) {
                positionTypeName = searchProductCardPositionTypeEnum.getName();
            } else {
                positionType = SearchProductCardPositionTypeEnum.NORMAL.getType();
                positionTypeName = SearchProductCardPositionTypeEnum.NORMAL.getName();
            }
            productVO.setPositionType(positionType);
            productVO.setPositionTypeName(positionTypeName);
            appSearchCardVO.setProductInfo(productVO);
            return appSearchCardVO;
        } else if (searchBaseCardDTO instanceof EcpSearchOperationCardDTO) {
            EcpSearchOperationCardDTO ecpSearchOperationCardDTO = (EcpSearchOperationCardDTO) searchBaseCardDTO;
            if (CollectionUtils.isEmpty(ecpSearchOperationCardDTO.getProducts())) {
                return null;
            }
            Integer positionType = ecpSearchOperationCardDTO.getPositionType();
            String positionTypeName;
            SearchProductCardPositionTypeEnum searchProductCardPositionTypeEnum = SearchProductCardPositionTypeEnum.valueOfCustom(positionType);
            if (Objects.nonNull(searchProductCardPositionTypeEnum)) {
                positionTypeName = searchProductCardPositionTypeEnum.getName();
            } else {
                positionType = SearchProductCardPositionTypeEnum.NORMAL.getType();
                positionTypeName = SearchProductCardPositionTypeEnum.NORMAL.getName();
            }
            Integer finalPositionType = positionType;
            String finalPositionTypeName = positionTypeName;
            List<PcSearchProductVO> productVOS = ecpSearchOperationCardDTO.getProducts().stream().map(product -> {
                PcSearchProductVO productVO = csuIdToInfoMap.get(product.getId());
                if (Objects.isNull(productVO)) {
                    return null;
                }
                productVO.setHasSimilarGoods(EcpSearchYesNoEnum.NO.getValue());
                productVO.setOperationExhibitionId(ecpSearchOperationCardDTO.getOperationExhibitionId());
                productVO.setOperationCustomerGroupId(ecpSearchOperationCardDTO.getOperationCustomerGroupId());
                productVO.setOperationId(ecpSearchOperationCardDTO.getOperationId());
                productVO.setPositionType(finalPositionType);
                productVO.setPositionTypeName(finalPositionTypeName);
                return productVO;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productVOS)) {
                return null;
            }
            PcSearchCardVO appSearchCardVO = new PcSearchCardVO();
            appSearchCardVO.setId(Objects.nonNull(productVOS.get(0)) ? productVOS.get(0).getId() : null);
            appSearchCardVO.setCardType(searchBaseCardDTO.getCardType());
            PcSearchOperationVO operationInfo = new PcSearchOperationVO();
            operationInfo.setShowType(ecpSearchOperationCardDTO.getShowType());
            operationInfo.setTitle(ecpSearchOperationCardDTO.getTitle());
            operationInfo.setSubTile(ecpSearchOperationCardDTO.getSubTile());
            operationInfo.setJumpUrl(ecpSearchOperationCardDTO.getJumpUrl());
            operationInfo.setActivityPageId(ecpSearchOperationCardDTO.getActivityPageId());
            operationInfo.setProducts(productVOS);
            appSearchCardVO.setOperationInfo(operationInfo);
            return appSearchCardVO;
        } else if (searchBaseCardDTO instanceof EcpSearchGroupPurchaseCardDTO) {
            EcpSearchGroupPurchaseCardDTO ecpSearchGroupPurchaseCardDTO = (EcpSearchGroupPurchaseCardDTO) searchBaseCardDTO;
            // app侧，组合购时，主品和副品必须同时存在
            if (Objects.isNull(ecpSearchGroupPurchaseCardDTO.getMainProduct()) || CollectionUtils.isEmpty(ecpSearchGroupPurchaseCardDTO.getSubProducts())) {
                return null;
            }
            Integer positionType = ecpSearchGroupPurchaseCardDTO.getPositionType();
            String positionTypeName;
            SearchProductCardPositionTypeEnum searchProductCardPositionTypeEnum = SearchProductCardPositionTypeEnum.valueOfCustom(positionType);
            if (Objects.nonNull(searchProductCardPositionTypeEnum)) {
                positionTypeName = searchProductCardPositionTypeEnum.getName();
            } else {
                positionType = SearchProductCardPositionTypeEnum.NORMAL.getType();
                positionTypeName = SearchProductCardPositionTypeEnum.NORMAL.getName();
            }
            Integer finalPositionType = positionType;
            String finalPositionTypeName = positionTypeName;
            PcSearchProductVO originalMainProductVO = csuIdToInfoMap.get(ecpSearchGroupPurchaseCardDTO.getMainProduct().getId());
            if (isProductInValid(originalMainProductVO, isOpenSearchRecPurchaseWholesale)) {
                return null;
            }
            PcSearchProductVO mainProductVO = new PcSearchProductVO();
            BeanUtils.copyProperties(originalMainProductVO, mainProductVO);
            mainProductVO.setHasSimilarGoods(EcpSearchYesNoEnum.NO.getValue());
            mainProductVO.setPositionType(finalPositionType);
            mainProductVO.setPositionTypeName(finalPositionTypeName);
            mainProductVO.setSpuId(ecpSearchGroupPurchaseCardDTO.getMainProduct().getSpuId());
            List<PcSearchProductVO> subProductVOS = ecpSearchGroupPurchaseCardDTO.getSubProducts().stream().map(product -> {
                PcSearchProductVO originalSubProductVO = csuIdToInfoMap.get(product.getId());
                if (isProductInValid(originalSubProductVO, isOpenSearchRecPurchaseWholesale)) {
                    return null;
                }
                PcSearchProductVO subProductVO = new PcSearchProductVO();
                BeanUtils.copyProperties(originalSubProductVO, subProductVO);
                subProductVO.setHasSimilarGoods(EcpSearchYesNoEnum.NO.getValue());
                subProductVO.setPositionType(finalPositionType);
                subProductVO.setPositionTypeName(finalPositionTypeName);
                subProductVO.setSpuId(product.getSpuId());
                return subProductVO;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subProductVOS) || subProductVOS.size() < GROUP_PURCHASE_SUB_PRODUCT_MIN_SHOW_SIZE) {
                return null;
            }
            subProductVOS = resortSearchRecPurchaseSubProducts(subProductVOS, GROUP_PURCHASE_SUB_PRODUCT_MAX_SHOW_SIZE);
            PcSearchCardVO appSearchCardVO = new PcSearchCardVO();
            appSearchCardVO.setCardType(searchBaseCardDTO.getCardType());
            PcSearchGroupPurchaseVO groupPurchaseInfo = new PcSearchGroupPurchaseVO();
            groupPurchaseInfo.setMainProduct(mainProductVO);
            groupPurchaseInfo.setSubProducts(subProductVOS);
            groupPurchaseInfo.setTitle(groupPurchaseTitle);
            groupPurchaseInfo.setCarouselTime(searchRecPurchaseCarouselTime);
            appSearchCardVO.setGroupPurchaseInfo(groupPurchaseInfo);
            return appSearchCardVO;
        }
        return null;
    }

    private static boolean isProductInValid(PcSearchProductVO product, Boolean isOpenSearchRecPurchaseWholesale) {
        if (product == null || product.getControlType() != null || !Objects.equals(product.getStatus(), ProductEnumDTO.SkuStatusEnum.ONSALE.getId())) {
            return true;
        }

        return BooleanUtils.isTrue(isOpenSearchRecPurchaseWholesale)
                ? Objects.isNull(product.getActPt()) && Objects.isNull(product.getActPgby())
                : Objects.isNull(product.getActPt());
    }

    /**
     * 重排副品：优先限时加补活动商品，最多取X个。
     *
     * @param subProductVOS
     * @param maxSize
     * @return
     */
    private static List<PcSearchProductVO> resortSearchRecPurchaseSubProducts(List<PcSearchProductVO> subProductVOS, int maxSize) {
        if (CollectionUtils.isEmpty(subProductVOS) || maxSize <= 0) {
            return subProductVOS;
        }
        return subProductVOS.stream().sorted((f, s) -> {
            Integer firstOrderNo = Objects.isNull(f.getLimitFullDiscountActInfo()) ? 0 : 1;
            Integer secondOrderNo = Objects.isNull(s.getLimitFullDiscountActInfo()) ? 0 : 1;
            // 降序
            return secondOrderNo.compareTo(firstOrderNo);
        }).limit(maxSize).collect(Collectors.toList());
    }

    /**
     * 创建
     *
     * @param searchBaseCardDTOS
     * @param csuIdToInfoMap
     * @param isShowSimilarGoodsJump
     * @return
     */
    public static List<PcSearchCardVO> creates(List<EcpSearchBaseCardDTO> searchBaseCardDTOS, Map<Long, PcSearchProductVO> csuIdToInfoMap, Boolean isShowSimilarGoodsJump, SearchProperties searchProperties) {
        if (CollectionUtils.isEmpty(searchBaseCardDTOS) || MapUtils.isEmpty(csuIdToInfoMap)) {
            return Lists.newArrayList();
        }
        return searchBaseCardDTOS.stream().map(item -> PcSearchCardVOHelper.create(item, csuIdToInfoMap, isShowSimilarGoodsJump, searchProperties))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

}
