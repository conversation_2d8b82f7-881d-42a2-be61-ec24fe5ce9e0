<!DOCTYPE HTML>
<html>

<head>
	<#include "common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>用户登录-药帮忙</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="/static/css/login-headerAndFooter.css"/>
    <link rel="stylesheet" href="/static/css/reg.css?t=${t_v}"/>
    <script type="text/javascript" src="/static/js/util.js"></script>
    <script src="/static/js/callback.js"></script>
    <style>
    .container{
        display:none;
    }
    </style>
</head>
<body>
	<div class="container" >

		<!--头部导航区域开始-->
		<div class="headerBox" id="headerBox">
            <div class="login-nav">
                <div class="p-lbox fl logoLeft">
                    <img src="/static/images/logo_login.png" alt="" />
                    <div class="title"></div>
                </div>
                <#--<div class="p-rbox fr">
                    <img src="/static/images/buzou4.png" alt="">
                </div>-->
            </div>
		</div>
		<!--头部导航区域结束-->


		<!--主体部分开始-->
		<div class="main">
			<div class="regbox">
				<div class="con-box">
					<form id="regForm" class="sui-form form-horizontal sui-validate" method="post">
						<input type="hidden" id="is_next_step" name="is_next_step"/>
						<div class="control-group">
							<label for="mobile" class="control-label">手机号码：</label>
							<div class="controls">
								<input type="text" id="mobile" name="mobile" placeholder="请输入您的手机号码"
									data-rules="required" name="mobile" oninput="mobileIdentify()" maxlength="11"">
							</div>
                            <div id="mobileIdentify" class="sui-msg msg-error help-inline" style="display: none;">
                                <div class="msg-con">
                                    <span>请填写正确的手机号码</span>
                                </div>   <img src="/static/images/cuowutishi.png" class="errorimg">
                            </div>
                            <div class="err-box" >
                                <div class="errinfo noshow" id="errorPhoneMsg">
                                    <img src="/static/images/cuowutishi.png" class="errorimg" style="position:unset"> <span class="wenantishi"></span>
                                </div>
                            </div>
						</div>
                        <!--手机验证码-->
                        <div class="control-group specont">
                            <label for="yzcode" class="control-label">短信验证码：</label>

                            <div class="controls yzmwarp">
                                <input type="text" id="code" placeholder="请输入短信验证码"  name="code" data-rules="required" value="${code!""}" onblur="hiddenCode()">
                                <a href="javascript:;" class="yzmbox">
                                    获取短信验证码
                                </a>
                                <a href="javascript:;" class="yzmbox-repe">
                                    重新发送（<span class="datasub">60</span>）
                                </a>
                            </div>
                            <div class="code-box" >
                                <div class="errinfo noshow" id="errorMsg">
                                    <img src="/static/images/cuowutishi.png" class="errorimg" style="position:unset"> <span class="codetishi"></span>
                                </div>
                            </div>
                           

                        </div>
						<div class="control-group">
							<label class="control-label"></label>
							<div class="controls">
								<button type="button" id="regNext" class="sui-btn btn-primary btn-large">确定</button>
							</div>
						</div>
					</form>
				</div>
			</div>


		</div>
		<!--主体部分结束-->
		<#include "/pharmacy.ftl" />
		<!--底部导航区域开始-->
		<div class="footer" id="footer">
			<#include "/login_footer.ftl" />
		</div>
		<!--底部导航区域结束-->


	</div>

</body>
<script>
    function mobileIdentify() {
		    var regMobile = /^1\d{10}$/;
		    var mobile = $("#mobile").val();
            if(mobile.length !=0 && !(regMobile.test(mobile))){
                $('#mobileIdentify').show();
            }else{
                $('#mobileIdentify').hide();
			}
        }
</script>
<style>
.title{
    margin: 10px 0 0 20px;
    padding-left: 20px;
    height: 26px;
    line-height:26px;
    font-size: 20px;
    color: #222;
    border-left: 1px solid #bbb;
}
.logoLeft{
    display: flex;
    align-items: center;
}
.regbox .con-box{
    padding-top:120px;
}
.regbox{height:635px;}
.code-box{padding-left: 10px;}
.code-box .errinfo{margin-top: 10px;color: #ea4a36;}
.code-box .errinfo-noshow{width: 120px;margin-top: 10px;background: #ffebeb;color: #ea4a36;padding: 2px 2px;display: none;}
</style>

</html>