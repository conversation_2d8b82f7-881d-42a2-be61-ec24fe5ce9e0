package com.xyy.ec.pc.recommend.dto;

import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.recommend.vo.PcRecommendCardVO;
import com.xyy.recommend.ecp.result.EcpRecommendResult;

import java.util.List;

public class RecommendProcessResult {
    private boolean success;
    private XyyJsonResult failureResult;
    private EcpRecommendResult recommendResult;
    private List<PcRecommendCardVO> rows;
    private Integer licenseStatus;

    private RecommendProcessResult(boolean success) {
        this.success = success;
    }

    public static RecommendProcessResult success(EcpRecommendResult recommendResult, List<PcRecommendCardVO> rows, Integer licenseStatus) {
        RecommendProcessResult result = new RecommendProcessResult(true);
        result.recommendResult = recommendResult;
        result.rows = rows;
        result.licenseStatus = licenseStatus;
        return result;
    }

    public static RecommendProcessResult failure(XyyJsonResult failureResult) {
        RecommendProcessResult result = new RecommendProcessResult(false);
        result.failureResult = failureResult;
        return result;
    }

    public boolean isSuccess() {
        return success;
    }

    public XyyJsonResult getFailureResult() {
        return failureResult;
    }

    public EcpRecommendResult getRecommendResult() {
        return recommendResult;
    }

    public List<PcRecommendCardVO> getRows() {
        return rows;
    }

    public Integer getLicenseStatus() {
        return licenseStatus;
    }
}
