package com.xyy.ec.pc.controller.wxwork;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.wxwork.WxWorkBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.controller.ActivityDataController;
import com.xyy.ec.pc.enums.LayoutMerchantStatusEnum;
import com.xyy.ec.pc.enums.wxwork.ChangeTypeEnum;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import com.xyy.electron.data.bussiness.data.enums.electron.template.ErpPubilcTemplateFieldEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping
public class WxWorkController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ActivityDataController.class);

    @Reference(version = "1.0.0")
    private WxWorkBusinessApi wxWorkBusinessApi;

    @GetMapping("/wechat-test/wxwork/callback")
    public String validEntWeChatURLTest(HttpServletRequest request) {
        return this.verifyURL(request);
    }


    @PostMapping("/wechat-test/wxwork/callback")
    public String callbackTest(HttpServletRequest request, @RequestBody(required = false) String body) {
        return this.wxWorkCallback(request, body);
    }

    @GetMapping("/wechat-stage/wxwork/callback")
    public String validEntWeChatURLStage(HttpServletRequest request) {
        return this.verifyURL(request);
    }


    @PostMapping("/wechat-stage/wxwork/callback")
    public String callbackState(HttpServletRequest request, @RequestBody(required = false) String body) {
        return this.wxWorkCallback(request, body);
    }

    @GetMapping("/wxwork/callback")
    public String validEntWeChatURL(HttpServletRequest request) {
        return this.verifyURL(request);
    }


    @PostMapping("/wxwork/callback")
    public String callback(HttpServletRequest request, @RequestBody(required = false) String body) {
        return this.wxWorkCallback(request, body);
    }

    private String wxWorkCallback(HttpServletRequest request, @RequestBody(required = false) String body) {
        //微信加签
        String msgSignature = request.getParameter("msg_signature");
        String timeStamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");
        logger.info("企业微信加密签名: {},时间戳: {},随机数: {}", msgSignature, timeStamp, nonce);
        ApiRPCResult<String> result = wxWorkBusinessApi.handleMessage(msgSignature, timeStamp, nonce, body);
        if (result.isFail()) {
            logger.error("微信回调失败：{}", result.getErrMsg());
            return "fail";
        }
        String data = result.getData();
        if (StringUtils.isEmpty(data)) {
            logger.error("微信回调数据为空");
            return "fail";
        }
        JSONObject jsonObject = JSONUtil.parseObj(data);
        logger.info("企业微信回调数据：{}", jsonObject);
        String changeType = jsonObject.getStr("ChangeType");
        String event = jsonObject.getStr("Event");
        if (StringUtils.isEmpty(changeType) || StringUtils.isEmpty(event)) {
            logger.error("微信回调数据不完整");
            return "fail";
        }
        ChangeTypeEnum changeTypeEnum = ChangeTypeEnum.getByCodeAndEvent(changeType, event);
        if (Objects.isNull(changeTypeEnum)){
            logger.info("微信回调事件类型不支持");
            return "success";
        }

        switch (changeTypeEnum) {
            case ADD_EXTERNAL_CONTACT:
                addExternalContact(jsonObject);
                break;
            default:
                break;
        }
        return "success";
    }


    private String verifyURL(HttpServletRequest request) {
        //微信加签
        String msgSignature = request.getParameter("msg_signature");
        String timeStamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");
        String echostr = request.getParameter("echostr");
        logger.info("企业微信加密签名: {},时间戳: {},随机数: {},加密的字符串: {}", msgSignature, timeStamp, nonce, echostr);
        ApiRPCResult<String> result = wxWorkBusinessApi.verifyURL(msgSignature, timeStamp, nonce, echostr);
        if (result.isFail()) {
            logger.error("微信验证失败：{}", result.getErrMsg());
        }
        return result.getData();
    }


    private void addExternalContact(JSONObject jsonObject) {
        String extUserId = jsonObject.getStr("ExternalUserID");
        String userId = jsonObject.getStr("UserID");
        String welcomeCode = jsonObject.getStr("WelcomeCode");

        ApiRPCResult<String> sendMessageResult = wxWorkBusinessApi.sendNewCustomerMessage(welcomeCode, userId, extUserId);
        if (sendMessageResult.isFail()) {
            logger.error("发送欢迎语失败：{}", sendMessageResult.getErrMsg());
        }

        ApiRPCResult<String> stringApiRPCResult = wxWorkBusinessApi.updateRemarkAndTag(userId, extUserId);
        if (stringApiRPCResult.isFail()) {
            logger.error("更新标签失败：{}", stringApiRPCResult.getErrMsg());
        }
    }
}
