package com.xyy.ec.pc.newfront.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.iwhalecloud.xyy.cms.dto.CmsHotWordDto;
import com.iwhalecloud.xyy.cms.dto.param.HotWordShowListParam;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.newfront.dto.SearchAgesRespVO;
import com.xyy.ec.pc.newfront.dto.SearchCardRespVO;
import com.xyy.ec.pc.newfront.dto.SearchProductRespVO;
import com.xyy.ec.pc.newfront.dto.SearchRespVO;
import com.xyy.ec.pc.newfront.rpc.CmsNewRpcService;
import com.xyy.ec.pc.newfront.service.SearchNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.search.config.SearchProperties;
import com.xyy.ec.pc.search.ecp.helpers.EcpSearchQueryParamHelper;
import com.xyy.ec.pc.search.ecp.helpers.PcSearchQueryParamHelper;
import com.xyy.ec.pc.search.ecp.helpers.QuickTrackingDataHelper;
import com.xyy.ec.pc.search.ecp.params.PcSearchQueryParam;
import com.xyy.ec.pc.search.ecp.service.EcpPcSearchService;
import com.xyy.ec.pc.search.ecp.service.PcSearchAggregateDataService;
import com.xyy.ec.pc.search.ecp.vo.PcSearchCardVO;
import com.xyy.ec.pc.search.service.impl.DataServiceImpl;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ec.search.engine.ecp.api.EcpSearchApi;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.*;
import com.xyy.ec.search.engine.ecp.params.EcpSearchQueryParam;
import com.xyy.ec.search.engine.ecp.result.EcpSearchAggregateResult;
import com.xyy.ec.search.engine.ecp.result.EcpSearchBaseCardDTO;
import com.xyy.ec.search.engine.ecp.result.EcpSearchResult;
import com.xyy.ec.search.engine.enums.SearchType;
import com.xyy.ec.search.engine.metadata.IPage;
import com.xyy.ms.marketing.nine.chapters.api.estimation.EstimationDiscountPriceApi;
import com.xyy.ms.marketing.nine.chapters.api.estimation.dto.DiscountPrice;
import com.xyy.ms.marketing.nine.chapters.api.estimation.dto.DiscountPriceRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;



@Service
@Slf4j
@RequiredArgsConstructor
public class SearchNewServiceImpl implements SearchNewService {


    private final XyyIndentityValidator xyyIndentityValidator;


    private final CmsNewRpcService cmsNewRpcService;
    private final AppProperties appProperties;

    @Reference(version = "1.0.0")
    private EcpSearchApi ecpSearchApi;

    @Reference(version = "1.0.0")
    private EstimationDiscountPriceApi estimationDiscountPriceApi;

    @Autowired
    private PcSearchAggregateDataService pcSearchAggregateDataService;

    @Autowired
    private EcpPcSearchService ecpPcSearchService;


    private final PcVersionUtils pcVersionUtils;


    private final SearchProperties searchProperties;
    @Autowired
    private DataServiceImpl dataServiceImpl;




    @Override
    public AjaxResult<SearchRespVO> listProducts(PcSearchQueryParam searchQueryParam, HttpServletRequest httpServletRequest) throws Exception {
        Long accountId = null;
        Long merchantId = null;
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                // 未登录 提示鉴权异常
                return AjaxResult.errResult("未登录");
            }
            // 默认值处理
            if (Objects.nonNull(searchQueryParam)) {
                searchQueryParam.setType(Optional.ofNullable(searchQueryParam.getType()).orElse(SearchType.NORMAL_SEARCH.getType()));
                searchQueryParam.setPageNum(Optional.ofNullable(searchQueryParam.getPageNum()).orElse(1));
                searchQueryParam.setPageSize(Optional.ofNullable(searchQueryParam.getPageSize()).orElse(20));
            }
            // 参数校验
            boolean validate = PcSearchQueryParamHelper.validate(searchQueryParam);
            if (BooleanUtils.isNotTrue(validate)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "查询参数非法");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getId();
            String branchCode = merchant.getRegisterCode();
            if (log.isDebugEnabled()) {
                log.debug("搜索（V2），accountId：{}，merchantId：{}，searchQueryParam：{}", accountId, merchantId, JSONObject.toJSONString(searchQueryParam));
            }
            if (!Objects.equals(searchQueryParam.getIsNextPage(), EcpSearchYesNoEnum.YES.getValue()) || StringUtils.isEmpty(searchQueryParam.getSid())) {
                searchQueryParam.setSid(SearchUtils.generateNewSidData(merchantId, EcpTerminalTypeEnum.PC.getType()));
            }
            if (!Objects.equals(searchQueryParam.getIsNextPage(), EcpSearchYesNoEnum.YES.getValue()) || StringUtils.isEmpty(searchQueryParam.getScmId())) {
                searchQueryParam.setScmId(RandomStringUtils.randomAlphanumeric(8));
            }
            EcpSearchSceneEnum searchScene = EcpSearchSceneEnum.valueOfCustom(searchQueryParam.getSearchScene());
            EcpSearchQueryParam ecpSearchQueryParam = EcpSearchQueryParamHelper.create(searchQueryParam, merchantId, accountId);
            ecpSearchQueryParam.setIsNotGetDynamicLabelData(true);
            // 发版过程中版本兼容：组合购/加价购
            ecpSearchQueryParam.setIsSupportSearchRecPurchase(searchProperties.getIsSupportSearchRecPurchase());
            // 热搜词
            List<String> hotQueryWords = ecpPcSearchService.listHotQueryWords(searchQueryParam.getType(), merchantId, branchCode, EcpTerminalTypeEnum.PC.getType());
            /* 搜索 */
            // 保存搜索词历史
            ecpPcSearchService.saveQueryWordHistory(searchScene, merchantId, searchQueryParam.getQueryWord(), ecpSearchQueryParam.getShopCodes());
            EcpSearchResult searchResult = null;
            if (Objects.equals(searchQueryParam.getType(), SearchType.NORMAL_SEARCH.getType())) {
                ApiRPCResult<EcpSearchResult> apiRPCResult = ecpSearchApi.search(ecpSearchQueryParam);
                if (log.isDebugEnabled()) {
                    log.debug("搜索（V2），accountId：{}，merchantId：{}，ecpSearchQueryParam：{}，apiRPCResult：{}", accountId, merchantId, JSONObject.toJSONString(ecpSearchQueryParam), JSONObject.toJSONString(apiRPCResult));
                }
                if (apiRPCResult.isFail()) {
                    log.error("搜索（V2）失败，accountId：{}，merchantId：{}，searchQueryParam：{}，api异常信息：{}",
                            accountId, merchantId, JSONObject.toJSONString(searchQueryParam), JSONObject.toJSONString(apiRPCResult));
                    return AjaxResult.errResult(apiRPCResult.getMsg());
                }
                searchResult = apiRPCResult.getData();
                if (Objects.equals(ecpSearchQueryParam.getPageNum(), 1) && searchResult.getCardPage().getTotalCount() == 0L) {
                    // 第一页请求且没有查到数据，则进入无结果热销精选的搜索场景
                    searchQueryParam.setType(SearchType.RECOMMENDATION.getType());
                }
            }
            // 是否是无结果热销精选的搜索场景
            boolean isNoResultHotSaleSearchScene = Objects.equals(searchQueryParam.getType(), SearchType.RECOMMENDATION.getType());
            if (isNoResultHotSaleSearchScene) {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                EcpSearchQueryParam ecpSearchNoResultHotSaleQueryParam = EcpSearchQueryParam.builder()
                        .merchantId(merchantId)
                        .sortStrategy(EcpSearchSortStrategyEnum.SALES_DESC)
                        .terminalType(EcpTerminalTypeEnum.PC)
                        .searchScene(EcpSearchSceneEnum.NO_RESULT_HOT_SALE)
                        .dynamicLabelConfig(searchQueryParam.getDynamicLabelConfig())
                        .pageNum(searchQueryParam.getPageNum())
                        .pageSize(searchQueryParam.getPageSize())
                        .build();
                ApiRPCResult<EcpSearchResult> apiRPCResult = ecpSearchApi.search(ecpSearchNoResultHotSaleQueryParam);
                if (log.isDebugEnabled()) {
                    log.debug("搜索（V2），无结果热销精选，accountId：{}，merchantId：{}，ecpSearchQueryParam：{}，apiRPCResult：{}", accountId, merchantId, JSONObject.toJSONString(ecpSearchQueryParam), JSONObject.toJSONString(apiRPCResult));
                }
                stopWatch.stop();
                log.info("搜索（V2），无结果热销精选，accountId：{}，merchantId：{}，ecpSearchQueryParam：{}，耗时：{}", accountId, merchantId, JSONObject.toJSONString(ecpSearchQueryParam), stopWatch);
                if (apiRPCResult.isFail()) {
                    log.error("搜索（V2）失败，accountId：{}，merchantId：{}，searchQueryParam：{}，api异常信息：{}",
                            accountId, merchantId, JSONObject.toJSONString(searchQueryParam), JSONObject.toJSONString(apiRPCResult));
                    return AjaxResult.errResult(apiRPCResult.getMsg());
                }
                searchResult = apiRPCResult.getData();
            }
            // 数据结构转换
            IPage<EcpSearchBaseCardDTO> cardPage = searchResult.getCardPage();
            boolean isEnd = searchQueryParam.getPageNum() * searchQueryParam.getPageSize() >= cardPage.getTotalCount();
            /* 资质处理 */
            pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            Integer licenseStatus = merchant.getLicenseStatus();
            Boolean priceDisplayFlag = merchant.getPriceDisplayFlag();
            /* 数据结构转换和填充相关信息 */
            List<PcSearchCardVO> rows = ecpPcSearchService.convertAndFillSearchCards(searchScene, merchantId, branchCode, priceDisplayFlag, cardPage.getRecordList(), BooleanUtils.isTrue(searchResult.getIsShowNextDayDeliveryTag()));
            QuickTrackingDataHelper.handleQuickTrackingData(rows, cardPage.getPageNo(), cardPage.getPageSize());
            if (log.isDebugEnabled()) {
                log.debug("搜索（V2），accountId：{}，merchantId：{}，branchCode：{}，priceDisplayFlag：{}，rows：{}", accountId, merchantId, branchCode, priceDisplayFlag, JSONArray.toJSONString(rows));
            }
            ecpPcSearchService.sendSearchResultCsuInfoMq(accountId, merchantId, EcpTerminalTypeEnum.PC.getType(), null, IPUtils.getClientIP(httpServletRequest), rows);

            // 处理已返回总条数
            Integer currentCount = Optional.ofNullable(searchQueryParam.getListoffset()).orElse(0);
            int listSize = CollectionUtils.isEmpty(rows) ? 0 : rows.size();
            int nowCurrentCount = currentCount + listSize;
            //折后价处理
            List<Long> skuIds = rows.stream().map(PcSearchCardVO::getId).collect(Collectors.toList());

            List<SearchCardRespVO> searchCardList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(skuIds)) {
                DiscountPriceRequest baseRequest = new DiscountPriceRequest();
                baseRequest.setBranchCode(merchant.getRegisterCode());
                baseRequest.setKa(merchant.getIsKa());
                baseRequest.setMerchantId(merchantId);
                baseRequest.setSkuIdList(skuIds);
                Map<Long, String> discountPricesMap = new ConcurrentHashMap<>(skuIds.size());
                List<DiscountPrice> discountPrices = estimationDiscountPriceApi.mgetDiscountPrice(baseRequest);
                 if (CollectionUtils.isNotEmpty(discountPrices)) {

                //todo 待优化性能
                    for (DiscountPrice price : discountPrices) {
                        if (null == price || null == price.getFob() || null == price.getPrice() || price.getFob().compareTo(price.getPrice()) <= 0) {
                            if (log.isDebugEnabled()) {
                                log.debug("satisfactoryInHandPrice_不展示_price={}", JSON.toJSONString(price));
                            }
                            continue;
                        }
                        if (price.getFob().compareTo(price.getPrice()) < 0) {
                            if (log.isDebugEnabled()) {
                                log.debug("satisfactoryInHandPrice_不展示_原价小于到手价_price={}", JSON.toJSONString(price));
                            }
                            continue;
                        }
                        discountPricesMap.put(price.getSkuId(), "折后约￥" + price.getPrice().toString());

                    }
                }
                //copy
                CollUtil.forEach(rows, (row, index) -> {
                    SearchCardRespVO vo = new SearchCardRespVO();
                    BeanUtil.copyProperties(row, vo);
                    searchCardList.add(vo);
                });
                for (SearchCardRespVO searchCardRespVO : searchCardList) {
                    if (null == searchCardRespVO) {
                        continue;
                    }
                    SearchProductRespVO productInfo = searchCardRespVO.getProductInfo();
                    if (null == productInfo) {
                        continue;
                    }
                    productInfo.setDiscountedPriceStr(discountPricesMap.get(productInfo.getId()));
                }

            }


            searchQueryParam.setListoffset(nowCurrentCount);
            /* feed流 */
            searchQueryParam.setPageNum(searchQueryParam.getPageNum() + 1);

            SearchRespVO searchRespVO = new SearchRespVO();
            searchRespVO.setType(searchQueryParam.getType());
            searchRespVO.setWordList(hotQueryWords);
            searchRespVO.setSearchSortStrategyId(searchResult.getSearchPreciseSortStrategyCode());
            searchRespVO.setRows(searchCardList);
            searchRespVO.setLicenseStatus(licenseStatus);
            searchRespVO.setIsEnd(isEnd);
            searchRespVO.setPageNo(cardPage.getPageNo());
            searchRespVO.setPageSize(cardPage.getPageSize());
            searchRespVO.setTotalPage(cardPage.getPages());
            searchRespVO.setTotalCount(cardPage.getTotalCount());
            /* 组装响应数据 */
            return AjaxResult.successResult(searchRespVO);
    }

    @Override
    public AjaxResult<SearchAgesRespVO> getSearchCategory(@RequestBody PcSearchQueryParam searchQueryParam) throws Exception {
        Long accountId = null;
        Long merchantId = null;

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                // 未登录 提示鉴权异常
                return AjaxResult.errResult("未登录");
            }
            // 参数校验
            boolean validate = PcSearchQueryParamHelper.validateForAggregation(searchQueryParam);
            if (BooleanUtils.isNotTrue(validate)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "查询参数非法");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getId();
            // 参数转换
            EcpSearchQueryParam ecpSearchQueryParam = EcpSearchQueryParamHelper.create(searchQueryParam, merchantId, accountId);
            List<EcpSearchAggregateEnum> ecpSearchAggregateEnums = Lists.newArrayListWithExpectedSize(5);
            ecpSearchAggregateEnums.add(EcpSearchAggregateEnum.SHOP_AGG);
            ecpSearchAggregateEnums.add(EcpSearchAggregateEnum.SPEC_AGG);
            ecpSearchAggregateEnums.add(EcpSearchAggregateEnum.MANUFACTURER_AGG);
            ecpSearchAggregateEnums.add(EcpSearchAggregateEnum.CAT_AGG);
            ecpSearchAggregateEnums.add(EcpSearchAggregateEnum.DYNAMIC_LABEL);
            ecpSearchQueryParam.setEcpSearchAggregateEnums(ecpSearchAggregateEnums);
            ecpSearchQueryParam.setIsGetIntervalDayDeliveryData(true);
            // 搜索
            ApiRPCResult<EcpSearchAggregateResult> ecpSearchAggregateResultApi = ecpSearchApi.aggregate(ecpSearchQueryParam);
            if (log.isDebugEnabled()) {
                log.debug("聚合（V2），accountId：{}，merchantId：{}，ecpSearchQueryParam：{}，apiRPCResult：{}", accountId, merchantId, JSONObject.toJSONString(ecpSearchQueryParam), JSONObject.toJSONString(ecpSearchAggregateResultApi));
            }
            if (ecpSearchAggregateResultApi.isFail()) {
                log.error("聚合（V2）失败，accountId：{}，merchantId：{}，searchQueryParam：{}，api异常信息：{}",
                        accountId, merchantId, JSONObject.toJSONString(searchQueryParam), JSONObject.toJSONString(ecpSearchAggregateResultApi));
                return AjaxResult.errResult(ecpSearchAggregateResultApi.getMsg());
            }
            EcpSearchAggregateResult ecpSearchAggregateResult = ecpSearchAggregateResultApi.getData();
            SearchAgesRespVO searchAgesRespVO = new SearchAgesRespVO();
            searchAgesRespVO.setCatStats(pcSearchAggregateDataService.buildCsuCategoryAggregateResultVOs(ecpSearchAggregateResult.getCsuCategories()));
            searchAgesRespVO.setSpecStats(pcSearchAggregateDataService.buildAggregateResultVOs(ecpSearchAggregateResult.getSpecs()));
            searchAgesRespVO.setShopStats(pcSearchAggregateDataService.buildShopAggregateResultVOs(ecpSearchAggregateResult.getShops()));
            searchAgesRespVO.setManufacturerStats(pcSearchAggregateDataService.buildManufacturersAggregateResultVOs(ecpSearchAggregateResult.getManufacturers()));
            searchAgesRespVO.setDynamicLabelConfig(Objects.nonNull(ecpSearchAggregateResult.getSearchDynamicLabelDTO())
                    ? ecpSearchAggregateResult.getSearchDynamicLabelDTO().getSearchDynamicLabels() : Lists.newArrayList());
            return AjaxResult.successResult(searchAgesRespVO);


    }

    @Override
    public AjaxResult<List<CmsHotWordDto>> getHotWords(HotWordShowListParam param) {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipalEaseEx();
        if (merchant.getMerchantId() == null){
            return AjaxResult.errResult("ID不能为空");
        }
        param.setMerchantId(merchant.getMerchantId());
        ApiRPCResult<List<CmsHotWordDto>> list =  cmsNewRpcService.frontHotWords(param);
        if (CollectionUtils.isNotEmpty(list.getData())){
         List<CmsHotWordDto> data =   list.getData();
            return AjaxResult.successResult(data);
        }
        return AjaxResult.successResult(new ArrayList<>());

    }


}
