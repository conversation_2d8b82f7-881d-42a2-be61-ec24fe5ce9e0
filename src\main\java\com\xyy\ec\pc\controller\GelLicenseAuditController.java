package com.xyy.ec.pc.controller;

import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.util.ImgUtils;
import com.xyy.ec.pc.util.PdfUtil;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;

/**
 * <AUTHOR>
 * @date ：Created in 2019/9/4 9:51
 * 单据上传,审核,修改相关功能
 */
@RequestMapping("/licenseAudit")
@Controller
public class GelLicenseAuditController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(GelLicenseAuditController.class);
    @Autowired
    private XyyConfig.CdnConfig cdnConfig;

    //小药药资质图片文件夹路径
    public static String xyyFilePath = "/ybm/delegate_template/";

    //小药药模板pdf
    public static String xyyPdfName = "小药药委托书模板.pdf";

    //小药药模板图片
    public static String xyyFileName = "ybm100_model.png";

    //小药药委托模板图片路径
    public static String xyyTemplateFilePath = "/xyy-ec-pc/src/main/resources/static/images/";

    public static String bb="D:\\";

    /**
     * 委托书模板下载
     *
     * @param res        请求返回
     * @param type       1.个人委托书模板 2.企业委托书模板
     * @param merchantId 会员id
     * @throws Exception
     */
    @RequestMapping(value = "download.json")
    public void download(HttpServletResponse res, int type, Long merchantId) throws Exception {
        String url = type == 1 ? xyyTemplateFilePath : xyyFilePath+merchantId+"/";
        String fileName = type == 1 ? xyyFileName : xyyPdfName;
        FTPClient ftpClient = new FTPClient();
        if (type == 2) {
            //链接ftp
            boolean flag = createFtpConnect(cdnConfig.getCdnHostname(),
                    Integer.parseInt(cdnConfig.getCdnPort()), cdnConfig.getCdnUsername(),
                    cdnConfig.getCdnPassword(),ftpClient);
            if(!flag){
                logger.info("委托书模板下载链接ftp失败");
                return;
            }

            //检查是否有生成模板pdf
            if (ftpClient.makeDirectory(url)) {
                ftpClient.enterLocalActiveMode();// 主动模式
                ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
                //获取委托书模板图片
                String urlOut = xyyTemplateFilePath + File.separator + xyyFileName;
                //进入小药药资质图片文件夹
                ftpClient.changeWorkingDirectory(xyyFilePath);
                //获取该文件下的图片模板流
                InputStream retrieveFileStream = ftpClient.retrieveFileStream(xyyFileName);
                if(retrieveFileStream != null){
                    retrieveFileStream.close();
                    ftpClient.completePendingCommand();
                }
                //进入指定会员资质图片文件夹
                ftpClient.changeWorkingDirectory(url);
                //判断指定会员信息委托书模板图片是否存在
                InputStream is = ftpClient.retrieveFileStream(new String(xyyFileName.getBytes("GBK"),ftpClient.DEFAULT_CONTROL_ENCODING));
                if(is == null || ftpClient.getReplyCode() == FTPReply.FILE_UNAVAILABLE){
                    //不存在则创建
                    boolean bol = ftpClient.storeFile(xyyFileName,retrieveFileStream);
                    if(!bol){
                        logger.info("ftpClient.storeFile异常");
                    }
                }else{
                    //存在则删除
                    is.close();
                    ftpClient.completePendingCommand();
                    ftpClient.deleteFile(xyyFileName);
                }
//                String urlIn = xyyFilePath +"/"+ xyyFileName;
                //向会员信息委托书模板图片写入会员相关信息
//                ImgUtils.doImage(urlOut, urlIn);
                //3.生成pdf
//                PdfUtil.imagesToPdf(ftpClient,xyyFilePath, url + File.separator + fileName);
            }else{
                ftpClient.changeWorkingDirectory(url);
                //获取该pdf模板
                InputStream retrievePdfStream = ftpClient.retrieveFileStream(new String(fileName.getBytes("gbk"), "ISO-8859-1"));
                if(retrievePdfStream == null || ftpClient.getReplyCode() == FTPReply.FILE_UNAVAILABLE){
                    //不存在则创建

                }
            }
        }
        String strFilePath = bb +File.separator+ fileName;
        BufferedOutputStream outStream;
        try {
            ftpClient.changeWorkingDirectory(url);
            outStream = new BufferedOutputStream(new FileOutputStream(
                    strFilePath));
            logger.info(fileName + "开始下载....");
            ftpClient.retrieveFile(fileName, outStream);
        } catch (IOException e) {
            logger.info("委托书模板下载异常:", e);
        } finally {
            closeFtpConnect(ftpClient);
        }
    }

    private static boolean createFtpConnect(String hostname, int port,
                                            String username, String password,FTPClient ftpClient) {
        boolean flag = false;
        ftpClient.setControlEncoding("UTF-8");
        try {
            // 连接FTP服务器
            ftpClient.connect(hostname, port);
            // 登录FTP服务器
            ftpClient.login(username, password);
            // 是否成功登录FTP服务器
            int replyCode = ftpClient.getReplyCode();

            if (!FTPReply.isPositiveCompletion(replyCode)) {
                return flag;
            }

            flag = true;
        } catch (Exception e) {
            logger.info("文件上传异常：",e);
        }
        return flag;
    }

    /**
     * @退出关闭服务器链接
     * */
    public void closeFtpConnect(FTPClient ftpClient) {
        if (null != ftpClient && ftpClient.isConnected()) {
            try {
                ftpClient.logout();
            }catch (IOException e) {
                logger.error("退出FTP服务器异常:",e);
            }finally {
                try {
                    ftpClient.disconnect();
                }catch (IOException e) {
                    logger.error("关闭FTP服务器的连接异常:",e);
                }
            }
        }
    }
}
