package com.xyy.ec.pc.newfront.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum TypeEnum {
    LICENSE("1", "营业执照"),
    MEDICAL_RUN_PERMIT("2", "药品经营许可证"),
    MEDICAL_ORG_RUN_PERMIT("3", "医疗机构执业许可证"),
    PROXY_STATEMENT ("4", "委托书"),
    ID_CARD("5", "被委托人身份证复印件"),
    FOOD_RUN_PERMIT("6", "食品经营许可证"),
    SECOND_MEDICAL_DEVICE_PERMIT("7", "第二类医疗器械经营备案凭证"),
    MEDICAL_DEVICE_PERMIT("8", "医疗器械经营许可证"),
    OFFICIAL_SEAL("9", "公章"),
    GENERAL_STRUCTURE("1000", "通用票证抽取"),
    ;

    private String type;
    private String desc;

    public static TypeEnum ofType(String type) {
        for (TypeEnum value : TypeEnum.values()) {
            if (StringUtils.equals(type, value.getType())) {
                return value;
            }
        }
        throw new IllegalArgumentException("nonexistent->"+type);
    }


}