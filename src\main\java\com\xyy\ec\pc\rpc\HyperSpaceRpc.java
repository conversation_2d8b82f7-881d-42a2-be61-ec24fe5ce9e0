package com.xyy.ec.pc.rpc;

import com.xyy.ec.marketing.client.dto.ActInfoDTO;
import com.xyy.ec.marketing.client.dto.SuiXinPinSkuDTO;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.ActCardInfoParamDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.OneClickRestockCouponDTO;
import com.xyy.ec.marketing.hyperspace.api.dto.OneClickRestockCouponTemplateDTO;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateConsumptionBaseResult;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateConsumptionReturnResult;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponCenterDto;
import com.xyy.ec.marketing.hyperspace.api.dto.fullGive.PromoSkuPoolDTO;
import com.xyy.ec.marketing.hyperspace.api.dto.fullGive.PromoWithMainProductDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * hyperspace rpc
 */
public interface HyperSpaceRpc {

    /**
     * 根据id获取楼层列表
     * @param merchantId
     * @param bizType
     * @param bizCode
     * @return
     */
    List<MarketingEnum> getFloorList(Long merchantId, Integer bizType, String bizCode);

    /**
     * 根据商品id查询拼团活动信息
     * @param skuIdList
     * @param merchantId
     * @return
     */
    List<GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdList(List<Long> skuIdList, Long merchantId, List<Integer> statusList);

    List<SuiXinPinSkuDTO> getSuiXinPinSkuDiscountBySkuIds(Long merchantId, List<Long> skuIdList);

    /**
     * 根据商品id查询拼团活动信息。
     * CMS后台专用。
     *
     * @param skuIdList
     * @param statusList
     * @return
     */
    List<GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdListForCms(List<Long> skuIdList, List<Integer> statusList);

    /**
     * 根据商品id查询拼团活动信息
     *
     * @param actCardInfoParamDto
     * @return
     */
    List<GroupBuyingInfoDto> getActCardInfoBySkuIdList(ActCardInfoParamDto actCardInfoParamDto);

    /**
     * 根据商品id查询拼团活动信息。
     * CMS后台专用。
     *
     * @param actCardInfoParamDto
     * @return
     */
    List<GroupBuyingInfoDto> getActCardInfoBySkuIdListForCms(ActCardInfoParamDto actCardInfoParamDto);


    /**
     * 根据商品id查询通用活动信息（走通用接口）
     * @param skuIdList
     * @param merchantId
     * @return
     */
    Map<Long, List<ActInfoDTO>> getMarketingInfoBySkuIdList(List<Long> skuIdList, Long merchantId, MarketingEnum activityTypeEnum);

    /**
     * 批量获取商品正在参加中的活动。PS：活动信息中是此活动与入参的交集信息，如活动的品、活动的限购信息。返回的数据中只留下参加进行中活动的商品。
     *
     * @param merchantId
     * @param activityTypeEnum
     * @param csuIds
     * @return 返回的数据中只留下参加进行中活动的商品。
     */
    Map<Long, ActInfoDTO> batchGetCsuJoiningActInfo(Long merchantId, MarketingEnum activityTypeEnum, List<Long> csuIds);

    /**
     * 批量获取商品预热中的活动。PS：活动信息中是此活动与入参的交集信息，如活动的品、活动的限购信息。返回的数据中只留下参加预热中活动的商品。<br/>
     * PS：某类活动预热中的，可以有多个时间段，同一个商品可以有多个预热中的活动。
     *
     * @param merchantId
     * @param activityTypeEnum
     * @param csuIds
     * @return 返回的数据中只留下参加预热中活动的商品。
     */
    Map<Long, List<ActInfoDTO>> batchGetCsuPreheatingActInfos(Long merchantId, MarketingEnum activityTypeEnum, List<Long> csuIds);

    /**
     * 批量获取商品预热中的活动。PS：活动信息中是此活动与入参的交集信息，如活动的品、活动的限购信息。返回的数据中只留下参加某个状态的活动的商品。<br/>
     * PS：某类活动预热中的，可以有多个时间段，同一个商品可以有多个预热中的活动。
     *
     * @param merchantId
     * @param activityTypeEnum
     * @param status           状态，{@link MarketingQueryStatusEnum}。
     * @param csuIds
     * @return 返回的数据中只留下参加某个状态的活动的商品。
     */
    Map<Long, List<ActInfoDTO>> batchGetCsuSomeStatusActInfos(Long merchantId, MarketingEnum activityTypeEnum,
                                                              Integer status, List<Long> csuIds);

    /**
     * 根据拼团活动id查询活动信息（落地页使用 不走选人）
     * @param marketingId
     * @return
     */
    GroupBuyingInfoDto getGroupBuyingInfoById(Long marketingId);

    List<CouponCenterDto> getMarketingRebateVoucherTemplateList(String orderNo, Long merchantId, Integer type);
    List<PromoSkuPoolDTO> queryPromoSkuPool(Long promoId);

    PromoWithMainProductDTO queryPromoMainProductList(Long promoId);

    List<Long> querySuiXinPinTopSku(Long merchantId, String shopCode);

    /**
     * 获取消费返活动信息 及参与状态
     */
    MarketingRebateConsumptionReturnResult getMarketingRebateConsumptionReturnInfo(Long cid, BigDecimal tempAmount);
    /**
     * 获取消费返活动基本信息
     * @param cid
     * @return
     */
    MarketingRebateConsumptionBaseResult getMarketingRebateConsumptionBaseInfo(Long cid);

    /**
     * 获取消费返活动信息 及参与状态
     * @param cid
     * @return
     */
    MarketingRebateConsumptionReturnResult getMarketingRebateConsumptionReturnInfo(Long cid);

    /**
     * 一键采购补货优惠劵
     * @param merchantId
     * @return
     */
    OneClickRestockCouponDTO getOneClickRestockCoupon(Long merchantId);

    /**
     * 下单返优惠劵
     * @param merchantId
     * @return
     */
    List<OneClickRestockCouponTemplateDTO> getOrderReturnCoupon(Long merchantId);

}
