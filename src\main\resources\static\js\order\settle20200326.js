var $doObj;
var sourceName = "";
$(function() {
    /***优惠券开始**/
    $('.xiala-all').click(function(e){
        var that = $(this);
        e.stopPropagation();
        if($(this).hasClass("double")){
            // $(".youhuiquan").find(".cg-yhq").slideUp();
            $(this).parent(".youhuiquan").find(".cg-yhq").slideUp();
            $(this).removeClass("double");
        }else{
            $(this).parent(".youhuiquan").find(".cg-yhq").slideDown();
            $(this).addClass("double");
            var skuInfoForPc = $(this).find("#skuInfoForPc").val();
            var shopPatternCode = $(this).find("#shopPatternCode").val();
            var selectVoucherIds = $(this).find("#selectVoucherIds").val();
            // if(shopPatternCode == 'ybm'){
            //     getVoucher(that,selectVoucherIds,0,skuInfoForPc,shopPatternCode);
            // }
            getVoucher(that,selectVoucherIds,0,skuInfoForPc,shopPatternCode);

        }
    });
    function addTamp(m) {
        return m < 10 ? "0" + m : m;
    }
    //时间戳处理
    function format_date(timestamp) {
        if (timestamp === "" || timestamp === 0) {
            return "";
        }
        var time = new Date(timestamp);
        var year = time.getFullYear();
        var month = time.getMonth() + 1;
        var date = time.getDate();
        return year + "/" + addTamp(month) + "/" + addTamp(date);
    }
    Array.prototype.remove = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
    //获取优惠券-弹窗
    function getVoucher(that,selectVoucherIds,needOptimal,skuInfoForPc,shopPatternCode){
        var shopCode =that.attr("shopCode");
        /*
         * @isUse  bool 是否可用0查询可用优惠券列表1查询不可用优惠券列表
         * @selectVoucherIds  String 选中的优惠券列表
         * @needOptimal bool 是否需要最优 1需要 0不需要 1代表true需要，其它都代表不需要
         * */
        //查询可用优惠券
        $.ajax({
            url: "/merchant/center/voucher/findVoucherInfo",
            type: "POST",
            dataType: "json",
            data:{
                // shopCode:shopCode,
                isUse:0,
                selectVoucherIds:selectVoucherIds,
                needOptimal:needOptimal,
                skuInfoForPc:skuInfoForPc,
                // shopPatternCode:shopPatternCode
            },
            success: function(data) {
                if(data.status=="success"){
                    //可用优惠券
                    if(data.data.voucherList){
                        var html = '',spHtml = '';
                        if(data.data.voucherList.length){
                            spHtml = '<div style="position: relative;height: 30px;"><span style="margin-left: 10px;width:200px;display: inline-block;margin-right: 80px;color:#FF0000;font-size:12px;" >'+data.data.voucherTip+'</span>'
                            if(!data.data.isOptimal){
                                spHtml += '<p class="tuijian">使用推荐方案</p>';
                            }
                            // that.parent(".yhq-box").find(".address-top-title1").html('<span class="yi-get">已领取优惠券</span>以下为已领取可使用优惠券');
                            data.data.voucherList.forEach(function(element, index){
                                var nextLine="";
                                var batr="";
                                if((index+1)%2 == 0){
                                    nextLine = "three-3n";
                                }
                                if(element.maxMoneyInVoucherDesc && element.maxMoneyInVoucherDesc!=null && element.maxMoneyInVoucherDesc!="" ){
                                    batr = '<div class="yhq-lb-foot">' + element.maxMoneyInVoucherDesc + '</div>'
                                }
                                var checked = element.isSelected ? "checked" : "disabled";
                                if(element.voucherType == 6){                //叠加券
                                    var classType = element.isSelected ? "cur" : "";
                                    var str = "";
                                    if(element.voucherState==1){
                                        str='<span class="price">'+element.discountRatio+'</span><span class="fuhao">折</span>'
                                    }else{
                                        str='<span class="fuhao">￥</span><span class="price">'+element.moneyInVoucher+'</span>'
                                    }
                                    spHtml += '</div><li class="'+classType+' '+nextLine+ '">' +
                                                '<input type="hidden" name="voucherMoney" value="'+element.moneyInVoucher+'"/>' +
                                                '<input type="hidden" name="voucherId" shopcode="'+ element.shopCode + '" shoppatterncode="'+ element.shopPatternCode + '" value="'+element.voucherId+'"/>' +
                                                ' <div class="yhq-lb">' +
                                                    '<div class="yhq-lb-top">' + str + '</div>' +
                                                    '<div class="yhq-lb-foot">'+ element.minMoneyToEnableDesc + '</div>' +
                                                    batr +
                                                '</div>'+
                                                '<div class="yhq-rb">'+
                                                    '<div class="yhq-rb-top">'+
                                                        '<span class="quan quan-die">'+ element.voucherTypeDesc + '</span>'+
                                                        '<span class="info">'+ element.voucherDesc + '</span>'+
                                                    '</div>'+
                                                    '<div style="height:30px;overflow:hidden;"></div>'+
                                                    '<div class="yhq-rb-foot" style="position:absolute;bottom:10px;">'+
                                                        '<span>'+ format_date(element.validDate) + '-' + format_date(element.expireDate) +'</span>'+
                                                    '</div>'+
                                                '</div>'+
                                                '<div class="yhq-checkb">'+
                                                    '<label class="checkbox-pretty inline ' + checked +'" canSelect="'+ element.canSelected +'">'+
                                                        '<input type="checkbox" ' + checked + '><span></span>'+
                                                    '</label>'+
                                                '</div>'+
                                            '</li>';
                                }else{                 //非叠加券
                                    var classType = element.isSelected ? "cur" : "";
                                    var classTypeUi = element.canSelected ? "" : "ygq";
                                    var checked = element.isSelected ? "checked" : "disabled";
                                    var shopName = element.shopName ? element.shopName : "";
                                    var quanType;
                                    var str = "";
                                    if(element.voucherState == 1){
                                        str=`<span class="price ${element.voucherType == 9 ? 'zp-price' : ''}">`+element.discountRatio+`</span><span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}">折</span>`
                                    }else if(element.voucherUsageWay == 1){
                                        str=`<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}">￥</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}">`+element.sourceMoneyInVoucher+'</span>'
                                    }else{
                                        str=`<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}">￥</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}">`+element.moneyInVoucher+'</span>'
                                    }
                                    if(element.voucherType == 2){
                                        quanType = '<span class="quan">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 1){
                                        quanType = '<span class="quan quan-tong">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 6){
                                        quanType = '<span class="quan quan-die">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 5){
                                        quanType = '<span class="quan quan-xin">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 7){
                                        quanType = '<span class="quan quan-shop">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 8){
                                        quanType = '<span class="quan quan-platform">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 9){
                                        quanType = '<span class="quan quan-zhuanpin">'+element.voucherTypeDesc+'</span>';
                                    } 
                                    spHtml += '</div><li class="'+classType+' '+classTypeUi+' '+nextLine+ '">' +
                                                '<input type="hidden" name="voucherMoney" value="'+element.moneyInVoucher+'"/>' +
                                               '<input type="hidden" name="voucherId" shopcode="'+ element.shopCode + '" shoppatterncode="'+ element.shopPatternCode + '" value="'+element.id+'"/>' +
                                                ' <div class="yhq-lb">' +
                                                    '<div class="yhq-lb-top">' + str + '</div>' +
                                                    `<div class="yhq-lb-foot ${element.voucherType == 9 ? 'yhq-lb-foot-zp' : ''}">`+ element.minMoneyToEnableDesc + '</div>' +
                                                    batr +
                                                '</div>'+
                                                '<div class="yhq-rb">'+
                                                    '<div class="yhq-rb-top">'+
                                                        quanType +
                                                        '<span class="info" title="'+ shopName +'">'+ shopName +'</span>'+
                                                    '</div>'+
                                                    '<div style="height:30px;overflow:hidden;line-height:30px;">'+ element.voucherDesc +'</div>'+
                                                    '<div class="yhq-rb-foot">'+
                                                        '<span>'+ element.voucherScope +'</span>'+
                                                    '</div>'+
                                                    '<div style="font-size:12px;color:#999999;">'+ format_date(element.validDate) + '-' + format_date(element.expireDate) +'</div>'+
                                                '</div>'+
                                                '<div class="yhq-checkb">'+
                                                        '<label class="checkbox-pretty inline ' + checked +'"  canSelect="'+ element.canSelected +'">'+
                                                            '<input type="checkbox" ' + checked + '><span></span>'+
                                                        '</label>'+
                                                    '</div>'+
                                                 '</div>'+
                                            '</li>';
                                }
                            });
                            that.parent(".youhuiquan-sum").find(".ky").html(spHtml);
                            getTotalGoodsMoney();
                        }
                    };
                }else{
                    $.alert(data.errorMsg);
                }
            },
            error: function(XMLHttpRequest, textStatus, errorThrown){
                //$.alert('网络异常');
                // that.parent(".youhuiquan").find(".ky").html('网络异常');
                // that.parent(".youhuiquan").find(".ky").css({
                //     "text-align": "center",
                //     "color": "red"
                // })
            }
        })
        //查询不可用优惠券
        $.ajax({
            url: "/merchant/center/voucher/findVoucherInfo",
            type: "POST",
            dataType: "json",
            data:{
                // shopCode:shopCode,
                isUse:1,
                selectVoucherIds:selectVoucherIds,
                needOptimal:needOptimal,
                skuInfoForPc:skuInfoForPc,
                // shopPatternCode:shopPatternCode
            },
            success: function(data) {
                if(data.status=="success"){
                    //不可用优惠券
                    if(data.data.voucherList){
                        var html = '',spHtml = '';
                        if(data.data.voucherList.length){
                            // that.parent(".yhq-box").find(".address-top-title1").html('<span class="yi-get">已领取优惠券</span>以下为已领取可使用优惠券');
                            data.data.voucherList.forEach(function(element, index){
                                var nextLine="";
                                var batr="";
                                if((index+1)%2 == 0){
                                    nextLine = "three-3n";
                                }
                                if(element.voucherType == 6){                //叠加券
                                    var classType = element.isSelected ? "cur" : "";
                                    var checked = element.isSelected ? "checked" : "disabled";
                                    var str = "";
                                    if(element.voucherState==1){
                                        str='<span class="price">'+element.discountRatio+'</span><span class="fuhao">折</span>'
                                    }else{
                                        str='<span class="fuhao">￥</span><span class="price">'+element.moneyInVoucher+'</span>'
                                    }
                                    if(element.maxMoneyInVoucherDesc && element.maxMoneyInVoucherDesc!=null && element.maxMoneyInVoucherDesc!="" ){
                                        batr = '<div class="yhq-lb-foot">' + element.maxMoneyInVoucherDesc + '</div>'
                                    }
                                    spHtml += '<li class="'+classType+' '+nextLine+ '">' +
                                                 '<input type="hidden" name="voucherMoney" value="'+element.moneyInVoucher+'"/>' +
                                                '<input type="hidden" name="voucherId" shopcode="'+ element.shopCode + '" shoppatterncode="'+ element.shopPatternCode + '" value="'+element.voucherId+'"/>' +
                                                 '<div class="yhq-lb">' +
                                                    '<div class="yhq-lb-top">' + str + '</div>' +
                                                    '<div class="yhq-lb-foot">'+ element.minMoneyToEnableDesc + '</div>' +
                                                    batr +
                                                '</div>'+
                                                '<div class="yhq-rb">'+
                                                    '<div class="yhq-rb-top">'+
                                                        '<span class="quan quan-die">'+ element.voucherTypeDesc + '</span>'+
                                                        '<span class="info">'+ element.voucherDesc + '</span>'+
                                                    '</div>'+
                                                    '<div style="height:30px;overflow:hidden;"></div>'+
                                                    '<div class="yhq-rb-foot" style="position:absolute;bottom:10px;">'+
                                                        '<span>'+ format_date(element.validDate) + '-' + format_date(element.expireDate) +'</span>'+
                                                    '</div>'+
                                                '</div>'+
                                            '</li>';
                                }else{                 //非叠加券
                                    var classType = element.isSelected ? "cur" : "";
                                    var classTypeUi = element.canSelected ? "" : "ygq";
                                    var checked = element.isSelected ? "checked" : "disabled";
                                    var shopName = element.shopName ? element.shopName : "";
                                    var quanType;
                                    var str = "";
                                    if(element.voucherState == 1){
                                        str=`<span class="price ${element.voucherType == 9 ? 'zp-price' : ''}">`+element.discountRatio+`</span><span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}">折</span>`
                                    }else if(element.voucherUsageWay == 1){
                                        str=`<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}">￥</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}">`+element.sourceMoneyInVoucher+'</span>'
                                    }else{
                                        str=`<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}">￥</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}">`+element.moneyInVoucher+'</span>'
                                    }
                                    if(element.voucherType == 2){
                                        quanType = '<span class="quan">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 1){
                                        quanType = '<span class="quan quan-tong">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 6){
                                        quanType = '<span class="quan quan-die">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 5){
                                        quanType = '<span class="quan quan-xin">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 7){
                                        quanType = '<span class="quan quan-shop">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 8){
                                        quanType = '<span class="quan quan-platform">'+element.voucherTypeDesc+'</span>';
                                    }else if(element.voucherType == 9){
                                        quanType = '<span class="quan quan-zhuanpin">'+element.voucherTypeDesc+'</span>';
                                    } 
                                    spHtml += '<li class="'+classType+' '+classTypeUi+' '+nextLine+ '">' +
                                                '<input type="hidden" name="voucherMoney" value="'+element.moneyInVoucher+'"/>' +
                                                '<input type="hidden" name="voucherId" shopcode="'+ element.shopCode + '" shoppatterncode="'+ element.shopPatternCode + '" value="'+element.voucherId+'"/>' +
                                                ' <div class="yhq-lb">' +
                                                    '<div class="yhq-lb-top">' + str + '</div>' +
                                                    `<div class="yhq-lb-foot ${element.voucherType == 9 ? 'yhq-lb-foot-zp' : ''}">`+ element.minMoneyToEnableDesc + '</div>' +
                                                    batr +
                                                '</div>'+
                                                '<div class="yhq-rb">'+
                                                    '<div class="yhq-rb-top">'+
                                                         quanType +
                                                        '<span class="info" title="'+ shopName +'">'+ shopName +'</span>'+
                                                    '</div>'+
                                                    '<div style="height:30px;overflow:hidden;line-height:30px;">'+ element.voucherDesc +'</div>'+
                                                    '<div class="yhq-rb-foot">'+
                                                        '<span>'+ element.voucherScope +'</span>'+
                                                    '</div>'+
                                                    '<div style="font-size:12px;color:#999999;">'+ format_date(element.validDate) + '-' + format_date(element.expireDate) +'</div>'+
                                                '</div>'+
                                            '</li>';
                                }

                            });
                            that.parent(".youhuiquan").find(".bky").html(spHtml);
                        }
                    };

                }else{
                    $.alert(data.errorMsg);
                }
            },
            error: function(XMLHttpRequest, textStatus, errorThrown){
                //$.alert('网络异常');
                // that.parent(".youhuiquan").find(".bky").html('网络异常');
                // that.parent(".youhuiquan").find(".bky").css({
                //     "text-align": "center",
                //     "color": "red"
                // })
            }
        })
    }
    //选中优惠
    $(".youhuiquan").on('click',function(e){
        // console.log(999)
        e.stopPropagation();
        var skuInfoForPc = $(this).find("#skuInfoForPc").val();
        var that = $(this).find(".xiala-all");
        if(e.target.parentNode.className.indexOf("checkbox-pretty") >= 0){
            var canSe = $(e.target.parentNode).attr('canSelect');
            if(canSe == 'false'){
                return
            }
            var voucherIds = [];
            //获取已经选中的优惠券
            if($(this).find("li.cur")){
                var voucherIdInput = $(this).find("li.cur").find("input[name='voucherId']");
                $.each(voucherIdInput, function(index, item){
                    voucherIds.push($(item).val());
                });
            }
            //处理新选中的优惠券
            if($(e.target.parentNode).hasClass("checked")){
                var selectId = $(e.target.parentNode).parent(".yhq-checkb").parent("li").find("input[name='voucherId']").val();
                //移除选中id
                voucherIds.remove(selectId)

            }else{
                var selectId = $(e.target.parentNode).parent(".yhq-checkb").parent("li").find("input[name='voucherId']").val();
                //添加选中id
                voucherIds.push(selectId);
            }
            if(voucherIds.length>0){
                var voucherStr = voucherIds.join(",");
                console.log(voucherStr)
                // return false;
                getVoucher(that,voucherStr,0,skuInfoForPc);
            }else{
                getVoucher(that,'',0,skuInfoForPc);
            }
            // getTotalGoodsMoney();	//调用后台计算金额
        }
        if(e.target.className.indexOf("tuijian") >= 0){
            getVoucher(that,"",1,skuInfoForPc)
        }
    })
    /*优惠券复选框点击事情*/
    $(".youhuiquan .checkbox-pretty").click(function(event){
        event.preventDefault();
        event.stopPropagation();
        var $checkbox = $(this).checkbox();
        var thisType = $(this).closest("li").attr("type");
        var thisCheckBox = $(this).find(":checkbox");
        if($(this).hasClass("checked")){
            // $checkbox.checkbox("uncheck");
            $(this).removeClass("checked");
            //其他置亮
            var otherLi = $(this).parent(".yhq-checkb").parent("li").parent("ul").find("li.ygq[type=" + thisType + "]");//$('.youhuiquan ul li.ygq[type=' + thisType + ']');
            $.each(otherLi, function (index, entry) {
                $(entry).removeClass("ygq");
                $(entry).find(":checkbox").removeAttr("disabled");
            });
        }else{
            $(this).addClass("checked");
            //其他置灰，不可选中
            var otherLi = $(this).parent(".yhq-checkb").parent("li").parent("ul").find("li[type=" + thisType + "]");//$('.youhuiquan ul li:not(.cur)[type=' + thisType + ']');
            $.each(otherLi, function (index, entry) {
                var $tempCheckBox = $(entry).find(":checkbox");
                if (!$tempCheckBox.is(thisCheckBox)) {
                    $(entry).addClass("ygq");
                    $tempCheckBox.attr("disabled", "");
                    $(entry).find(".checkbox-pretty").removeClass("checked");
                }else{
                    $(entry).removeClass("ygq");
                }
            });
        };
        setcolor();
        if($(this).hasClass('checked') && Number(thisType.split('-')[0])===6){
            var total = Number($('#ybmTotalAmount').val())
            var actDiscount = Number($('#ybmPromoTotalAmt').val())
            var $ul = $(this).parents("ul").eq(0)
            var $checkedList = $ul.find('.checkbox-pretty.checked')
            var price = 0
            $checkedList.each(function(i,el){
                price+=Number($(el).parents("li").eq(0).find('input[name="voucherMoney"]').val())
            })
            if(total-actDiscount-price<0){
                $('.xyy-confirm').show();
                $('.xyy-cover').show()
                $('#curCoupon').val($(this).parents("li").eq(0).find('input[name="voucherId"]').val())
            }else{
                getTotalGoodsMoney();	//调用后台计算金额
            }
        }else{
            getTotalGoodsMoney();	//调用后台计算金额
        }
    })
    /***优惠券结束**/
	$("#city_4").citySelect({
		prov:"420000",
		city:"420100",
		dist:"420112",
		nodata:"none"
	});
	$("#city_5").citySelect({
		prov:"420000",
		city:"420100",
		dist:"420112",
		nodata:"none"
	});
	/* 显示编辑和删除*/
	$(".address li").hover(
		function() {
			$(this).find(".opp").css("display", "block");
		},
		function() {
			$(this).find(".opp").css("display", "none");
		}
	);
    var JSclick=false
    var isTransactionSnapshotOk=false
    $('#declarationBox').on('change', function() {
       if($(this).prop('checked')){
        $(".transactionOk").removeClass("transactionNo");
       }else{
        $(".transactionOk").addClass("transactionNo");
       }
      });
     $(document).on("click",function(){
        $(".tooltip-zf").css("visibility","hidden")
        $(".tooltip-zf").css("opacity","0")
     })
    //支付方式前置校验
    $('.zffs ul li').click(function(event){
        //线下转账
        var that=$(this)
        var payType=$(".zffs .cur").attr("t");
        if($(this).attr("t") == 3&&!JSclick&&payType!=3&&$('#isHasPop').val()){ 
            event.stopImmediatePropagation();
            $("#declarationBox").prop("checked", false);//协议设置未选
            $(".transactionOk").addClass("transactionNo");
            $("#transactionSnapshotModal").modal('show');
            $('#transactionSnapshotModal').on('okHide', function (e) {

                if(!$("#declarationBox").prop('checked')){
                   setTimeout(() => {
                    $(".tooltip-zf").css("visibility","visible")
                    $(".tooltip-zf").css("opacity","1")
                }, 100);
                    return false;
                }
                isTransactionSnapshotOk=true
                setTimeout(() => {
                    $(that).trigger('click'); // 延迟触发，避免递归
                }, 0);
                JSclick=true;
            });   
        }else{
            JSclick=false;
        }
    });
	/*选择支付方式*/
	$('.zffs ul li').click(function(){
		$(this).addClass("cur").siblings().removeClass("cur");
		var text = $("#offlinePayTips").val();
		if($(this).attr("t") == 3 && text){
            $.alert({
                title: '提示',
                body: text,
                okBtn : '我知道了'
            });
        }
        var unpcaTips = $("#unpcaTips").val();
        if ($(this).attr("t") == 3){
            if(unpcaTips){
                $.alert({
                    title: '提示',
                    body: unpcaTips,
                    okBtn : '我知道了'
                });
            }
           
            $('.xxTip').show();
        }else{
            $('.xxTip').hide();
        }
		if ($(this).attr("t") != 1 && $(this).attr("t") != 5){
            $('.availVirtualGoldCon').hide();
        } else {
            $('.availVirtualGoldCon').show();
            $('.availRedPacketCon').show();
        }
	});
	/*选择发票类型*/
	$('.fplx ul li').click(function(){
		$(this).addClass("cur").siblings().removeClass("cur");
	});
	//选着支付方式
    $('.zffs ul li').click(function(){
        var isDisabled = $('#isGrayBtn').val();
        console.log('isDisabled', isDisabled)
        var payType=$(".zffs .cur").attr("t");	//支付类型
        if (isDisabled && payType == 5) {
            $(this).closest('li').removeClass("cur");
        } else {
            getTotalGoodsMoney();	//调用后台计算金
        }
    });

	//结算事件
	$("#settle_btn").click(function() {
        try{
            orderPendingConfirmation();
        }catch(e){
            console.log(e)
        }
        sourceName=""
        checkVoucherMonitor(true)
    });

    $("#group_settle_btn").click(function() {
        try{
            orderPendingConfirmation();
        }catch(e){
            console.log(e)
        }
        sourceName="group_settle_btn"
        var payType=$(".zffs .cur").attr("t");	//支付方式
        if ($("#virtualGoldInput:checked").val()&& payType != 3 && Number($('#virtualGoldInput:checked').val())>0) {
            findPayPassword();
        } else {
            submitOrder('', true)
        }
    });

	/*展开收起*/
    $(".more").click(function(){
        $(this).css("display","none");
        $(this).parent(".defaultbox").find(".list-default-box").removeClass("defaultH");
        $(this).next(".no-more").css("display","block");
    });
    $(".no-more").click(function(){
        $(this).css("display","none");
        $(this).parent(".defaultbox").find(".list-default-box").addClass("defaultH");
        $(this).prev(".more").css("display","block");
    });

	/*收货地址展开收起*/
	$(".add-more").click(function(){
		$(this).css("display","none");
		$(".add-no-more").css("display","block");
		$(".recebox .address").css("max-height","none");
	});
	$(".add-no-more").click(function(){
		$(this).css("display","none");
		$(".add-more").css("display","block");
		$(".recebox .address").css("max-height",123);
	});

	/*我的余额*/
    $('.my-balance :input').bind('input propertychange', function(){
        var inputprice=$(".inputprice").val();
        var maxBalance = parseFloat($("#hasBalanceAmount").val());
        if(inputprice>maxBalance){
            $(".inputprice").tooltip({
                "title":"抵扣金额已达上限"
            });
            $('.inputprice').tooltip('show');
            setTimeout(function(){$('.inputprice').tooltip('destroy');},2000);
            $(".inputprice").val(maxBalance);
        }
    });

	/*勾选协议*/
    $(".wrapbox .checkbox-pretty").click(function(event){
        event.preventDefault();
        var $checkbox = $(this).checkbox();
        if($(this).hasClass("checked")){
            $checkbox.checkbox("uncheck");
            $(".acol6 a.tjbtn").addClass("gray");
            $(".acol6 a.tjbtn").unbind("click");
        }else{
            $checkbox.checkbox("check");
            $(".acol6 a.tjbtn").removeClass("gray");
            $(".acol6 a.tjbtn").bind("click", checkVoucherMonitor);
        };
    });


    /*自定义confirm*/
    $('#xyyOk').click(function(e){
        $('.xyy-confirm').hide();
        $('.xyy-cover').hide()
		getTotalGoodsMoney();	//调用后台计算金额
    })

    $('#xyyCancel').click(function(e){
        $('.xyy-confirm').hide();
        $('.xyy-cover').hide()
        var id = $('#curCoupon').val()
        var $input = $('.cg-yhq').find('input[name="voucherId"][value="'+id+'"]')
        $input.parent('li').find('.checkbox-pretty').removeClass('checked')
        $('#curCoupon').val('')
        // getTotalGoodsMoney()
    })

    /*优惠券tab切换*/
    $(".ky-yhq").click(function(e){
        e.stopPropagation();
        $(this).addClass("cur");
        $(this).next(".bky-yhq").removeClass("cur");
        $(this).parent(".yhq-l-box").parent(".address-top-title").parent(".cg-yhq").find(".ky").css("display","block");
        $(this).parent(".yhq-l-box").parent(".address-top-title").parent(".cg-yhq").find(".bky").css("display","none");
        // $(".youhuiquan .ky").css("display","block");
        // $(".youhuiquan .bky").css("display","none");
    });
    $(".bky-yhq").click(function(e){
        e.stopPropagation();
        $(this).addClass("cur");
        $(this).prev(".ky-yhq").removeClass("cur");
        $(this).parent(".yhq-l-box").parent(".address-top-title").parent(".cg-yhq").find(".bky").css("display","block");
        $(this).parent(".yhq-l-box").parent(".address-top-title").parent(".cg-yhq").find(".ky").css("display","none");
        // $(".ky-yhq").removeClass("cur");
        // $(".youhuiquan .bky").css("display","block");
        // $(".youhuiquan .ky").css("display","none");
    })
    /*查看更多优惠券*/
    var hei=$('.youhuiquan').outerHeight();
    // console.log(hei);
    $(".xiala").click(function(){
        $(".xiala").css("display","none");
        $(".shouqi").css("display","inline-block");
        $('.youhuiquan').css("max-height","100%");
    })
    $(".shouqi").click(function(){
        $(".shouqi").css("display","none");
        $(".xiala").css("display","inline-block");
        $('.youhuiquan').css("max-height","270px");
    })


    /*收货弹框姓名检测*/
    var minlength_xm=function(value, element, param){return trim(value).length>=param};
    Validate.setRule("minlength_xm",minlength_xm,"姓名不能少于两个字哦");

    var hanzi = function(value, element, param) { return (/^[\u4e00-\u9fa5]+$/).test(trim(value)); };
    Validate.setRule("hanzi", hanzi, '咱都是中国人，请使用汉字哦');

    var maxlength_xm=function (value, element, param) {return trim(value).length<=param};
    Validate.setRule("maxlength_xm",maxlength_xm,"姓名不能大于八个字哦");


    //随货同行label选择事件
    $("#peerTypeLab").on("click",function () {
        if($("#peerType").is(":checked") == true){
            $("#peerType").attr("checked",true);
        }else {
            $("#peerType").attr("checked",false);
        }
    })


    $(".shi_qu").click(function(){
        $(".ti_box").css("display","none")
    })
    $(".shi_qu_v").click(function(){
        $(".ti_box_v").css("display","none")
        submitOrder();
    })
    $(".shi_que_v").click(function(){
        var url = window.location.href;
        if(url.indexOf("voucherMonitor")==-1){
            url += "&voucherMonitor=1";
        }
        window.location.href=url;
    });
    $(".icon-tb-close").click(function(){
        $(".ti_box").css("display","none")
    })
    $(".info_hui").click(function(){
        $(".ti_box").css("display","block");
        $("#bigPackage").val($(this).parent().attr("name"));
        var giftTotalAmount=$(this).parent().find(":input[name='giftTotalAmount']").val();
        $("#bigPackageTotalAmount").val(giftTotalAmount);
    })
    $(".shi_que").click(function(){
        var giftid=$("#bigPackage").val();
        $.ajax({
            type: "POST",
            url: "/merchant/center/order/cancelBigGiftPackage.json",
            data: {"giftId":giftid},
            dataType: "json",
            success: function (data) {
                if (data.status === "success") {
                    var selectVal=$("input[name='giftId']:checked").val();
                    $(".manjianbox[name='"+giftid+"']").remove();
                    $(".bigGift"+giftid).remove();
                    if(selectVal==giftid) {
                        $("input[name='giftId']").first().prop("checked",true);
                        $("input[name='giftIdmore']").first().prop("checked",true);
                    }
                    getTotalGoodsMoney();	//调用后台计算金
                    $(".ti_box").css("display","none");
                }else{
                    $.alert(data.errorMsg);
                }
            }
        });
    })
    $("input[name='giftId']").click(function(){
        var selectGiftIdVal=$(this).val();
        var that =$(this);
        if(that.attr("data-mutex-check")=="true"){
            that.removeAttr('checked');
            that.attr("data-mutex-check","false");
        }else{
            that.attr("data-mutex-check","true");
        }
         $("input[name='giftId']").each(function(){
            if($(this).val()!=selectGiftIdVal){
                $(this).removeAttr('checked');
                $(this).attr("data-mutex-check","false");
            }
        });
        $("input[name='giftIdmore']").each(function(){
            if($(this).val()==selectGiftIdVal&&that.attr("data-mutex-check")=="true"){
                $(this).prop('checked',true);
                $(this).attr("data-mutex-check","true");
            }else{
                $(this).removeAttr('checked');
                $(this).attr("data-mutex-check","false");
            }
        });
        getTotalGoodsMoney();
    });
    $("input[name='giftIdmore']").click(function(){
        var selectGiftIdmoreVal=$(this).val();
        var that =$(this);
        if(that.attr("data-mutex-check")=="true"){
            that.removeAttr('checked');
            that.attr("data-mutex-check","false");
        }else{
            that.attr("data-mutex-check","true");
        }
        $("input[name='giftIdmore']").each(function(){
            if($(this).val()!=selectGiftIdmoreVal){
                $(this).removeAttr('checked');
                $(this).attr("data-mutex-check","false");
            }
        });
        $("input[name='giftId']").each(function(){
            if($(this).val()==selectGiftIdmoreVal&&that.attr("data-mutex-check")=="true"){
                $(this).prop('checked',true);
                $(this).attr("data-mutex-check","true");
            }else{
                $(this).removeAttr('checked');
                $(this).attr("data-mutex-check","false");
            }
        });
        getTotalGoodsMoney();
    });

    function isShowAvailVirtualGold(){
        var virtualGoldInput = $('#virtualGoldInput').val()
        if (virtualGoldInput > 0 || $(".hasGoldTips").length) {
            $('.availVirtualGoldCon').show()
        } else {
            $('.availVirtualGoldCon').hide()
        }
    }
    isShowAvailVirtualGold()

    var virtualGoldInput = $('input[id="virtualGoldInput"]')
    $(virtualGoldInput).change(function (){
        getTotalGoodsMoney()
    })

    function isShowAvailRedPacket(){
        var redPacketInput = $('#redPacketInput').val()
        if (redPacketInput > 0) {
            $('.availRedPacketCon').show()
        } else {
            $('.availRedPacketCon').hide()
        }
    }
    isShowAvailRedPacket()

    var redPacketInput = $('input[id="redPacketInput"]')
    $(redPacketInput).change(function (){
        getTotalGoodsMoney()
    })
    inputInit();
    getActivityData()
    $('#rewardbanner').click(function(e){
        $('#consumerRebate').show()
    })
});

function getActivityData(){
     $.ajax({
            url: '/merchant/center/order/queryConsumeRebateDetail',
            type: 'post',
            data: {
              money:$("#orderMoney").val()
            },
            success(data) {
                if(data.status=="success"){
                    if(data.data.actResultList!=null||data.data.actResultList.length>0){
                        if(data.data.levelScene == 2){
                            $('#levelScene2').show()
                            $('#levelScene3').hide()
                            $('#levelScene4').hide()
                            $('#levelScene5').hide()
                            $('#referralPercentage').text(data.data.nextLevelRate)
                            $('#differenceValue').text(data.data.nextLevelShortAmount+'元')
                        }
                        if(data.data.levelScene == 3&&data.data.nextLevelShortAmount!=0){
                            $('#levelScene3').show()
                            $('#levelScene2').hide()
                            $('#levelScene4').hide()
                            $('#levelScene5').hide()
                            $('#moneyVal').text(data.data.realAmount)
                            $('#differValue').text(data.data.nextLevelShortAmount+'元')
                            $('#redPacketVal').text(data.data.nextLevelRedPacketAmount+'元')
                        }
                        if(data.data.levelScene == 3&&data.data.nextLevelShortAmount==0){
                            $('#levelScene5').show()
                            $('#levelScene2').hide()
                            $('#levelScene3').hide()
                            $('#levelScene4').hide()
                            $('#moneyZeroVal').text(data.data.realAmount)
                        }
                        if(data.data.levelScene == 4){
                            $('#levelScene4').show()
                            $('#levelScene2').hide()
                            $('#levelScene3').hide()
                            $('#levelScene5').hide()
                            $('#amountVal').text(data.data.realAmount)
                            $('#maxAmount').text(data.data.maxReturnRedPackageAmount+'元')
                        }
                        var row = ""
                        $("#data-table tbody").empty();
                         startCountdown(data.data.toEtimeSeconds); 
                         $.each(data.data.actResultList, function(_, item) {
                           // 拼接金额范围
                            let amountRange = item.amount;
                            if (item.nextLevelAmount) {
                                amountRange += ' - ' + item.nextLevelAmount+'元';
                            } else {
                                amountRange += '元';
                            }
                            // 构建费率单元格
                            let rateCell = $('<td class="rate-cell">').append(
                                $('<span>').text(item.rate+'%')
                            );
                            // 条件添加额外补贴
                            if (item.extraSubsidyAmountRate) {
                                rateCell.append(
                                    $('<div class="add-ratio">').text('+'+item.extraSubsidyAmountRate+'%')
                                );
                            }
                            if (item.extraSubsidyAmountRate) {
                                rateCell.append(
                                    $('<div class="limited-time-offer"><div class="limited-label">限时加补' + item.extraSubsidyAmountRate + '%,仅剩<span class="countdownEnd"></span></div></div>')
                                );
                            }
                            // 创建表格行
                            row = $('<tr>').append(
                                $('<td>').text(amountRange),
                                rateCell,
                                $('<td>').text(item.maxAmount+'元')
                            );
                            $("#data-table tbody").append(row);
                        });
                       
                        var str = "";
                        if(data.data.levelScene == 1){
                            str = '<div class="state-color">参与下单返活动，预估月均可返红包 <span>'+data.data.lastMonthExpectedAmount+'元</span></div>'  
                        }else if(data.data.levelScene == 2){
                            str = '<div class="state-color">仅差<span>'+data.data.nextLevelShortAmount+'元</span>参与返利活动，返利<span>'+data.data.nextLevelRate+'%</span>起</div>'
                        }else if(data.data.levelScene == 3&&data.data.nextLevelShortAmount!=0){
                            str = '<div class="state-color">订单完成获得红包<span>'+data.data.realAmount+'元</span>仅差<span>'+data.data.nextLevelShortAmount+'元</span>可得<span>'+data.data.nextLevelRedPacketAmount+'元红包</span></div>'
                        }else if(data.data.levelScene == 3&&data.data.nextLevelShortAmount==0){
                            str = '<div class="state-color">订单完成获得红包<span class="amount-color">'+data.data.realAmount+'元</span></div>'
                        }else{
                            str = '<div class="state-color"> 订单完成获得红包 <span>'+data.data.realAmount+'元,</span>多买多返最高返<span>'+data.data.maxReturnRedPackageAmount+'元</span></div>'
                        }
                        $("#tip-content").html(str)
                        $("#rewardbanner").show()
                    }
                }
            },
            error(err) {
                parent.layer.msg('网络异常');
            }
        })
}
    function startCountdown(seconds) {
        var endTime = seconds * 1000 + new Date().getTime(); // 结束时间
        var timer = setInterval(() => {
            var now = new Date().getTime(); // 当前时间
            var distance = endTime - now; // 剩余时间
            if (distance < 0) {
                clearInterval(timer); // 如果时间已经结束，停止计时器
                document.getElementById('countdown').innerHTML = '';
                document.getElementsByClassName('countdownEnd').innerHTML = '';
                return;
            }
            var days = Math.floor(distance / (1000 * 60 * 60 * 24));
            var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((distance % (1000 * 60)) / 1000);
            // 补零处理
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            seconds = seconds < 10 ? '0' + seconds : seconds;
            if(distance < 86400000){
                    document.getElementById('countdown').innerHTML =   hours + ":" + minutes + ":" + seconds;
                    let els = document.getElementsByClassName('countdownEnd');
                    for (let i = 0; i < els.length; i++) {
                        els[i].innerHTML = hours + ":" + minutes + ":" + seconds;
                    }
            }else{
                document.getElementById('countdown').innerHTML = days + "天 " + hours + ":" + minutes + ":" + seconds;
                let els = document.getElementsByClassName('countdownEnd');
                for (let i = 0; i < els.length; i++) {
                    els[i].innerHTML = days + "天 " + hours + ":" + minutes + ":" + seconds;
                }
            }
  
           
        }, 1000);
    }
 


function addScmeV3(type) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    //14位
    if(type==1){
        for (let i = 0; i < 14; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    //6位
    if(type==2){
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
       return result;
    }
    //8位
    if(type==3){
        for (let i = 0; i < 8; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
       return result;
    }
}
//qt埋点三期
//PC待确认订单页-底部提交支付按钮点击
function orderPendingConfirmation(){
    try{
        // console.log(element,'qiyu')
        // if(!element.hasAttribute('data-qt')){
            // element.setAttribute('data-qt', '1');
            let spm_cnt = "1_4." + 'orderPendingConfirmation_0-0_0'+".submitOrder@Z." +  "btn@1" + '.' +window.getSpmE(); //订单列表再次购买点击埋点
            let scm_cnt = "pcFE.0.all_0." + 'text-提交订单.' +  sessionStorage.getItem("orderPendingConfirmationClick") +  window.addScmeV3(2);
            window.orderPendingConfirmationQtSpm = spm_cnt;
            window.orderPendingConfirmationQtScm = scm_cnt;
            aplus_queue.push({
                'action': 'aplus.record',
                'arguments': ['action_sub_module_click', 'CLK',
                {
                    "spm_cnt":spm_cnt,
                    "scm_cnt": scm_cnt
                }
                ]
            });
        // }
    }catch(err){
        console.log(err,'qiyu1');
    }
}
$(document).ready(function(){
    //qt埋点三期
	//PC待确认订单页-页面曝光
    try{
        sessionStorage.setItem("orderPendingConfirmationClick", addScmeV3(3));
        let spm_cnt="1_4."+"orderPendingConfirmation_0-0_0.0.0." + window.getSpmE(); //订单页面曝光
        aplus_queue.push({
            'action': 'aplus.record',
            'arguments': ['page_exposure', 'EXP', {
            "spm_cnt":spm_cnt,
            }]
        });
    }catch(err){
        console.log(err);
    }
})
let giftList = [];
let abandonStatus = false;
/**
 * 点击加号和减号
 * @param { string } id 
 * @param { 'add' | 'delete' } type 
 */
function fullNumChange(id, type) {
    if (abandonStatus) return;
    const list = giftList.filter(item => item.id == id);
    console.log(list[0]);
    
    let val = Number($('#num-' + id).val());
    val = Number.isNaN(val) ? 0 : val;
    val = type == 'add' ? val + 1 : val - 1;
    /* val = Number.isNaN(val) ? list[0].orderGiveMinQty : val < list[0].orderGiveMinQty ? list[0].orderGiveMinQty : val; */
    if (list[0].orderGiveMinQty) {
        //有赠送下限
        val = val < list[0].orderGiveMinQty ? list[0].orderGiveMinQty : val;
    } else {
        val = val < 0 ? 0 : val;
    }
    if (list[0].orderGiveMaxQty && list[0].orderGiveMaxQty != -1) {
        val = val > list[0].orderGiveMaxQty ? list[0].orderGiveMaxQty : val;
    }
    const canSelect = $('#canSelectNum').text();
    const selected = $('#selectedNum').text();
    if (Number(selected) + (type == 'add' ? 1 : -1) > Number(canSelect)) {
        //超出限制
        parent.layer.msg(`还可选择${canSelect - selected}件商品，请重新选择`);
        return;
    }
    if (!list[0].isSelected) {
        $.ajax({
            url: '/app/selectItemGiftPool',
            type: 'get',
            data: {
                bizSource: $('#bizSource').val(),
                promoId: $('#promoId').val(),
                amount: val,
                skuId: id,
                merchantId: $('#merchantId').val()
            },
            success(data) {
                if (data.status == 'success') {
                    document.getElementById('check-' + id).checked = true;
                } else {
                    parent.layer.msg('添加失败');
                    fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
                }
            },
            error(err) {
                fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
                parent.layer.msg('网络异常');
            }
        })
    }
    $.ajax({
        url: '/app/changeGiftPool',
        type: 'get',
        data: {
            bizSource: $('#bizSource').val(),
            promoId: $('#promoId').val(),
            amount: val,
            skuId: id,
            totalSelectedNum: $('#canSelectNum').text(),
            merchantId: $('#merchantId').val()
        },
        success(data) {
            if (data.status == 'success') {
            } else {
                parent.layer.msg('添加失败');
            }
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
        },
        error(err) {
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
            parent.layer.msg('网络异常');
        }
    })
}
//放弃赠品
function abandonGift() {
    document.getElementById('abandon').checked
    let url = abandonStatus ? '/app/deleteAutoGiveUpActFlag' : '/app/addAutoGiveUpActFlag'
    
    $.ajax({
        url: url,
        type: 'get',
        data: {
            bizSource: $('#bizSource').val(),
            promoId: $('#promoId').val(),
            merchantId: $('#merchantId').val()
        },
        success(data) {
            if (data.status == 'success') {
            } else {
                parent.layer.msg('添加失败');
            }
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
        },
        error(err) {
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
            parent.layer.msg('网络异常');
        }
    })
}
//修改数量
function fullNumInput(id) {
    if (abandonStatus) return;
    const canSelect = $('#canSelectNum').text();
    const selected = $('#selectedNum').text();
    const list = giftList.filter(item => item.id == id);
    const curSelected = list[0].selectNum ? list[0].selectNum : 0;
    const target = document.getElementById('num-' + id)
    if (!(/^([0]|[1-9][0-9]*)?$/.test(target.value))) {
        target.value = list[0].selectNum;
    }
    if (!target.value) return
    if (list[0].orderGiveMinQty && target.value  < list[0].orderGiveMinQty) {
        //有赠送下限
        parent.layer.msg(`当前赠品至少需选择${list[0].orderGiveMinQty}个`);
        target.value = curSelected;
        return
        
    }
    if (list[0].orderGiveMaxQty && list[0].orderGiveMaxQty != -1 && target.value  > list[0].orderGiveMaxQty) {
        parent.layer.msg(`赠品最多可选择${list[0].orderGiveMaxQty}个`);
        target.value = curSelected;
        return
    }
    if (target.value - curSelected + Number(selected) > Number(canSelect)) {
        target.value = curSelected;
        //超出限制
        parent.layer.msg(`还可选择${canSelect - selected}件商品，请重新选择`);
        return
    }
    if (!list[0].isSelected) {
        $.ajax({
            url: '/app/selectItemGiftPool',
            type: 'get',
            data: {
                bizSource: $('#bizSource').val(),
                promoId: $('#promoId').val(),
                amount: target.value,
                skuId: id,
                merchantId: $('#merchantId').val()
            },
            success(data) {
                if (data.status == 'success') {
                    document.getElementById('check-' + id).checked = true;
                } else {
                    parent.layer.msg('添加失败');
                    fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
                }
            },
            error(err) {
                fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
                parent.layer.msg('网络异常');
            }
        })
    }
    $.ajax({
        url: '/app/changeGiftPool',
        type: 'get',
        data: {
            bizSource: $('#bizSource').val(),
            promoId: $('#promoId').val(),
            amount: target.value,
            totalSelectedNum: $('#canSelectNum').text(),
            skuId: id,
            merchantId: $('#merchantId').val()
        },
        success(data) {
            if (data.status == 'success') {

            } else {
                parent.layer.msg('添加失败');
            }
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
        },
        error(err) {
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
            parent.layer.msg('网络异常');
        }
    })
}
//满赠弹框取消
function fullGiveClose() {
    console.log('fullGiveClose');
    window.location.reload();
}
//满赠弹框提交
function fullGiveSubmit() {
    console.log('fullGiveSubmit');
    
    window.location.reload();
}
//选择商品
function fullGiveClick(id) {
    //
    if (abandonStatus) return;
    const list = giftList.filter(item => item.id == id);
    const target = document.getElementById('check-' + id);
    const value = document.getElementById('num-' + id);
    value.value = value.value ? value.value : list[0].orderGiveMinQty ? list[0].orderGiveMinQty : 1;
    if (Number(value.value) + Number($('#selectedNum').text()) > $('#canSelectNum').text() && !list[0].isSelected) {
        parent.layer.msg(`最多可选择${$('#canSelectNum').text()}件赠品`);
        target.checked = false;
        return
    }
    target.checked = false;
    let url = list[0].isSelected ? '/app/cancelItemGiftPool' : '/app/selectItemGiftPool'
    $.ajax({
        url: url,
        type: 'get',
        data: {
            bizSource: $('#bizSource').val(),
            promoId: $('#promoId').val(),
            amount: list[0].isSelected ? 0 : value.value,
            skuId: id,
            merchantId: $('#merchantId').val()
        },
        success(data) {
            if (data.status == 'success') {

            } else {
                parent.layer.msg('添加失败');
            }
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
        },
        error(err) {
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
            parent.layer.msg('网络异常');
        }
    })
}
function fullNumSelectGive(bizSource, giftPoolActHasSelectedNum, giftPoolActTotalSelectedNum, promoId, noRender) {
    //id: fullGiveModal      弹框
    //id: abandon   放弃按钮
    //id: canSelectNum  总共可选
    //id: selectedNum   当前已选
    document.body.style.overflow = 'hidden';
    $('#canSelectNum').text(giftPoolActTotalSelectedNum);
    $('#promoId').val(promoId);
    $('.i-actList-active').attr('class', '');
    $('#act-' + promoId).attr('class', 'i-actList-active')
    let selected = 0;
    $.ajax({
        url: '/promotion/promo/getGiftSkuPool',
        type: 'post',
        dataType: "json",
        data: {
            bizSource: $('#bizSource').val(),
            promoId: promoId,
            merchantId: $('#merchantId').val(),
        },
        success(data) {
            if (data.status === "success") {
                //是否放弃赠品
                document.getElementById('abandon').checked = data.data.isGiveUpGift ? true : false;
                abandonStatus = data.data.isGiveUpGift ? true : false;
                giftList = data.data.productList;
                giftList.forEach(item => {
                    selected += item.isSelected ? item.selectNum ? item.selectNum : 0 : 0;
                })
                $('#selectedNum').text(abandonStatus ? 0 : selected)
                if (Number(giftPoolActTotalSelectedNum) == Number(selected)) {
                    $('#actStatus-' + promoId).attr('style', 'background: #26bb53;')
                    $('#actStatus-' + promoId).text('已选')
                } else if (abandonStatus || Number(selected) == 0) {
                    $('#actStatus-' + promoId).attr('style', 'background: #9f9f9f;')
                    $('#actStatus-' + promoId).text('未选')
                } else {
                    $('#actStatus-' + promoId).text('部分选')
                    $('#actStatus-' + promoId).attr('style', 'background: #FE8535;')
                }
                if (!noRender) {
                    renderGift(giftList);
                }
            }
        },
        error(err) {
            parent.layer.msg('网络异常');
        }
    })
}
function renderGift(productList) {
    //渲染选择赠品弹框
    const str = productList.map(item => {
        let limit = '<div>'
        if (item.orderGiveMinQty && item.orderGiveMaxQty && item.orderGiveMinQty != -1 && item.orderGiveMaxQty != -1) {
            limit += item.orderGiveMinQty + '盒起送,限' + item.orderGiveMaxQty + '盒</div>'
        } else if (item.orderGiveMinQty && item.orderGiveMinQty != -1) {
            limit += item.orderGiveMinQty + '盒起送</div>'
        } else if (item.orderGiveMaxQty && item.orderGiveMaxQty != -1) {
            limit += '限' + item.orderGiveMaxQty + '盒</div>'
        } else {
            limit = '';
        }
        return `
            <div class="i-item">
                <div class="checkbox">
                    <input id="check-${item.id}" ${abandonStatus || item.status != 1 ? 'disabled' : ''} class="${abandonStatus || item.status != 1 ? 'i-disabled' : ''}" onclick="fullGiveClick('${item.id}')" style="transform: translateY(2px);" type="checkbox" name="abandon" ${item.isSelected ? 'checked' : ''}>
                </div>
                <div class="i-info">
                    <div class="i-img">
                        ${item.status == 1 ? '' : '<div style="position: absolute;border-radius: 50%;width: 70%;height: 70%;background: #000000a8;display: flex;justify-content: center;align-items: center;color: white;top: 50%;left: 50%;transform: translate(-50%, -50%);">已售罄</div>'}
                        <img style="width: 100%;height: 100%;" src="${item.imageUrl}" alt="">
                    </div>
                    <div style="width: 0;flex-grow: 1;display: flex;flex-direction: column;">
                        <p style="font-weight: 600;">${item.showName}  ${item.spec}</p>
                        <p style="font-size: 12px;flex-grow: 1;height: 0;color: #929292;">效期：${item.farEffect}</p>
                        <p style="font-size: 12px;">
                            <span style="color: red;font-size:14px;">￥${item.actPrice}元</span>
                            <span>/${item.productUnit}</span>
                            <span style="margin-left: 20px; color: #929292;">原价: ￥${item.fob}</span>
                        </p>
                    </div>
                </div>
                <div style="display: ${item.status == 1 ? 'auto' : 'none'}">
                    <div class="i-numBtn ${abandonStatus ? 'i-disabled' : ''}" >
                        <div onclick="fullNumChange('${item.id}', 'delete')">
                            <span style="font-size: 20px;;line-height: 30px;">-</span>
                        </div>
                        <input id="num-${item.id}" class="${abandonStatus ? 'i-disabled' : ''}" ${abandonStatus ? 'disabled' : ''} type="text" value="${item.selectNum ? item.selectNum : ''}" onblur="fullNumInput('${item.id}')">
                        <div onclick="fullNumChange('${item.id}', 'add')">
                            <span style="font-size: 20px;line-height: 30px;">+</span>
                        </div>
                    </div>
                    ${ limit }
                </div>
                ${item.status == 1 ? '' : '<div style="padding: 0 15px;font-size:16px;color:#8e8e8e;">商品已售罄</div>'}
            </div>
        `
    }).join('');
    $('#productList').html(str);
    giftList.forEach(item => {
        document.getElementById('check-' + item.id).checked = item.isSelected;
    })
    $('#fullGiveModal').show();
}
//wcwanga add voucher monitor
function checkVoucherMonitor(target) {
    const giveNum = $('.giftPoolActCanSelectedNum')
    if (giveNum.length > 0 && target) {
        if (giveNum.length == 1) {
            $('#mz-title').show();
            $('#mz-actList').hide();
        } else {
            const giveGifts = $('.giftPoolActTitle');
            for (let i = giveGifts.length - 1; i >= 0; i--) {
                giveGifts[i].click();
            }
            $('#mz-actList').show();
            $('#mz-title').hide();
        }
        $('#mz-normal').hide();
        $('#mz-submit').show();
        giveNum[0].click();
        return
    }
    var voucherIds = getSelectVoucherIds();

    var payType=$(".zffs .cur").attr("t");	//支付类型
    var billType=$(".fplx .cur").attr("t");	//支付类型
    var balanceAmount = $('input[name=balanceAmount]').val();
    var more=$("#divmore").val();
    var giftId;
    if(more==0){
        giftId=$("input[name='giftId']:checked").val();
    }else{
        giftId=$("input[name='giftIdmore']:checked").val();
    }
    if(undefined==giftId){
        giftId="-1";
    }
    if(!payType) {
        $.alert({"title":"提示","body":"请选择支付类型！"});
        return;
    }
    $.ajax({
        type: "POST",
        url: "/merchant/center/order/voucherMonitor.json",
        data: {
            "voucherIds":JSON.stringify(voucherIds),
            "payType":payType,
            "billType":billType,
            "balanceAmount":balanceAmount,
            "giftIds":giftId,
        },
        dataType: "json",
        success: function (data) {
            if(data.success){
                verifyOrder();
            }else {
                $(".shi_xia_v p").text(data.msg);
                $(".ti_box_v").css("display","block");
                return false;
            }
        }
    });
}
function verifyOrder() {
	var billType=$(".fplx .cur").attr("t");	//发票类型
    /*校验发票类型是否选中*/
    if( billType==undefined){
      $.alert({"title":"提示","body":"请选择一个发票类型！"});
      return;
    }
	var payType=$(".zffs .cur").attr("t");	//支付方式
    if(!payType) {
        $.alert({"title":"提示","body":"请选择支付类型！"});
        return;
    }
	var addressId=$(".gspadres:has(label.checked)").find("input[hi='id']").val();	//收货地址编号
    if(!addressId){
        $.alert({"title":"提示","body":"离胜利就差一步了,选个收货地址呗!"});
        return;
    }
	if (payType == 5) {
        $.confirm({
            title: '温馨提示',
            body: '您当前所选支付方式为【自有账期】，提交订单后将会自动支付并出库，请确认是否继续提交？',
            okHidden: function () {
                if ($("#virtualGoldInput:checked").val() && Number($('#virtualGoldInput:checked').val())>0) {
                    findPayPassword();
                } else {
                    submitOrder();
                }
            },
            cancelHidden:function(e){
                $("#settle_btn").removeClass("tjbtn-gray");
                $("#settle_btn").addClass("tjbtn");
                $("#settle_btn").bind('click', checkVoucherMonitor);
                // return false;
            }
        });
    } else if ($("#virtualGoldInput:checked").val() && payType != 3 && Number($('#virtualGoldInput:checked').val())>0) {
        findPayPassword();
    } else {
        // 提交表单
        submitOrder();
    }
}
function findPayPassword() {
	$.ajax({
		type: "POST",
		url: "/merchant/center/payPwd/queryState",
		success: function (data) {
			if (data.data.state == 1) {
				//有支付密码
				const pay = document.getElementById('xx-pay');
				pay.style.zIndex = 'auto';
				pay.style.opacity = 1;
			} else {
				//没有支付密码
				$.confirm({
					body: "为了您的资金安全，请先设置支付密码", 
					title: '支付密码设置提醒', 
					okBtn: '去设置',
					cancelBtn: '关闭',
					okHidden: function() {
						//跳转设置密码页面
                        window.open("/merchant/center/setPayPwd/index");
                    },
                    cancelHidden: function() {
                        //关闭
						console.log(666)
					}
				})
			}
		}
	 });
}
function getSuiXinPinSkus() {
    // 顺手买一件
    var suiXinPinSkusUpload = []
    var suiXinPinSkusSource = JSON.parse(sessionStorage.getItem("suiXinPinSkus") || "[]");
    if(suiXinPinSkusSource.length > 0) {
        for (let index = 0; index < suiXinPinSkusSource.length; index++) {
            const sku = suiXinPinSkusSource[index];
            if(sku.quantity > 0) {
                suiXinPinSkusUpload.push({skuId:sku.skuId,quantity:sku.quantity,type: sku.type});
            }
        }
    }
    return suiXinPinSkusUpload;
}
function submitOrder(token, target) {
    const giveNum = $('.giftPoolActCanSelectedNum')
    if (giveNum.length > 0 && target) {
        if (giveNum.length == 1) {
            $('#mz-title').show();
            $('#mz-actList').hide();
        } else {
            const giveGifts = $('.giftPoolActTitle');
            for (let i = giveGifts.length - 1; i >= 0; i--) {
                giveGifts[i].click();
            }
            $('#mz-actList').show();
            $('#mz-title').hide();
        }
        $('#mz-normal').hide();
        $('#mz-submit').show();
        giveNum[0].click();
        return
    }
    $("#settle_btn").removeClass("tjbtn");
    $("#settle_btn").addClass("tjbtn-gray");
    $("#settle_btn").unbind();
    var billType=$(".fplx .cur").attr("t");	//发票类型
    /*校验发票类型是否选中*/
    /* if( billType==undefined){
      $.alert({"title":"提示","body":"请选择一个发票类型！"});
      return;
    } */
    var peerType;
    if(billType == 1 ){
        if($("#peerType").prop("checked")){
            peerType = $("#peerType").val();
        }
    }
    var payType=$(".zffs .cur").attr("t");	//支付方式
    /* if(!payType) {
        $.alert({"title":"提示","body":"请选择支付类型！"});
        return;
    } */
    var deliveryTime=$(".zffs #deliveryTime").html();	//发货时间
    var remark=getAllRemarks();	//备注
    var voucherIds = getSelectVoucherIds();	///优惠券编号
	var tranNo = $("#tranNo").val();
    var money="";		//订单实付金额
    var addressId=$(".gspadres:has(label.checked)").find("input[hi='id']").val();	//收货地址编号
    /* if(!addressId){
        $.alert({"title":"提示","body":"离胜利就差一步了,选个收货地址呗!"});
        return;
    } */

    var balanceAmount = $('input[name=balanceAmount]').val();
    if (!balanceAmount) {
        balanceAmount = 0;
    }


    var form = $('<form></form>');
    $("body").append(form);

    // 设置属性
    form.attr('action', "/merchant/center/order/confirmOrder.htm");
    form.attr('method', 'post');
    form.attr('target', '_self');

    //勾选的商品资质

    //productCredential = [{"skuId":12,"productCredential":"1,2"}]

    // $('input[name="skuCredential"]').each(function() {
        // do something
        // var skuId = $(this).attr("alert-skuId")
        // var productCredential = $(this).attr("alert-credential")
        //TODO 怎么赋值给productCredential
    // });

    var productCredential = [];
    let skuids = Object.keys(checkObj);
    if(skuids.length > 0){
        for (let index = 0; index < skuids.length; index++) {
            const key = skuids[index];
            // productCredentialStr += `{'skuId':${key}, 'productCredential':'${checkObj[key]}'},`
            productCredential.push({skuId:key,productCredential:checkObj[key]});
        }
        // productCredential = `[${productCredentialStr}]`
        var proCredential = $('<input type="hidden" name="productCredential" />');
        proCredential.attr('value', JSON.stringify(productCredential));
        form.append(proCredential);
    }

    //勾选的企业资质
    var corpCredential = [];
    if(Object.keys(tagCheckeds).length > 0) {
        let tagCheckedsKeys = Object.keys(tagCheckeds);
        // let corpCredentialStr = ''
        for (let index = 0; index < tagCheckedsKeys.length; index++) {
            const key = tagCheckedsKeys[index];
            const val = tagCheckeds[key].join(',');
            corpCredential.push({orgId:key,enterpriseCredential:val});
            // corpCredentialStr += `{'orgId':${key}, 'enterpriseCredential':'${val}'},`
        }
        // corpCredential = `[${corpCredentialStr}]`
        var corpCtl = $('<input type="hidden" name="corpCredential" />');
        corpCtl.attr('value', JSON.stringify(corpCredential));
        form.append(corpCtl);
    }
    // 顺手买一件
    const suiXinPinSkusUpload = getSuiXinPinSkus();
    if(suiXinPinSkusUpload.length > 0) {
        var suiXinPinSkus = $('<input type="hidden" name="suiXinPinSkus" />');
        suiXinPinSkus.attr('value', JSON.stringify(suiXinPinSkusUpload));
        form.append(suiXinPinSkus);
    }

    //[{"orgId":"xyyjh12","enterpriseCredential":"1,2"}]
    // $('input[name="skuCorpCredential"]').each(function() {
        // do something

    // });
    // 创建Input
    var billTypeInput = $('<input type="hidden" name="billType" />');
    billTypeInput.attr('value', billType);
    var payTypeInput = $('<input type="hidden" name="payType" />');
    payTypeInput.attr('value', payType);
    var voucherIdInput = $('<input type="hidden" name="voucherIds" />');
    voucherIdInput.attr('value', JSON.stringify(voucherIds));
    var remarkInput = $('<input type="hidden" name="remark" />');
    remarkInput.attr('value', JSON.stringify(remark));
    var addressIdInput = $('<input type="hidden" name="addressId" />');
    addressIdInput.attr('value', addressId);
    var moneyInput = $('<input type="hidden" name="money" />');
    moneyInput.attr('value', money);
    var balanceAmountInput = $('<input type="hidden" name="balanceAmount" />');
    balanceAmountInput.attr('value', balanceAmount);

    var storeStatusInput = $('<input type="hidden" name="storeStatus" />');
    storeStatusInput.attr('value', true);

    var tranNoInput = $('<input type="hidden" name="tranNo" />');
    tranNoInput.attr('value', tranNo);
    var shoppingCartImg = $('<input type="hidden" name="shoppingCartImgUUID" />');
    shoppingCartImg.attr('value', shoppingCartImgUUID);

    var productNum = $('<input type="hidden" name="productNum" />');
    productNum.attr('value', $('#groupProductNum').val());

    var skuId = $('<input type="hidden" name="skuId" />');
    skuId.attr('value', $('#groupSkuId').val());

    var bizSource = $('<input type="hidden" name="bizSource" />');
    bizSource.attr('value', $('#bizSource').val());
    var tokenInput;
    if (token) {
        tokenInput = $('<input type="hidden" name="token" />');
        tokenInput.attr('value', token ? token : '');
    }
    var notSubmitOrderOrgIds = $('<input type="hidden" name="notSubmitOrderOrgIds" />');
    notSubmitOrderOrgIds.attr('value', $('#notSubmitOrderOrgIds').val());


    var virtualGold = $('<input type="hidden" name="virtualGold" />');
    var virtualGoldBalance = $('#virtualGoldInput:checked').val()
    virtualGold.attr('value', virtualGoldBalance);
    
    let jgInfo = sessionStorage.getItem("jgInfo") || null;

    /* var mddata = $('<input type="hidden" name="mddata" />');
    mddata.attr('value', jgInfo); */
    /*获取URL参数值*/
    function GetQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return decodeURIComponent(r[2]); 
        return null;
    }
    if(window.orderPendingConfirmationQtSpm){
        var qtdata = $('<input type="hidden" name="qtdata" />');
        qtdata.attr('value', JSON.stringify({spm_cnt:orderPendingConfirmationQtSpm,scm_cnt:orderPendingConfirmationQtScm}));
        form.append(qtdata);
    }


    var isUseVirtualGold = true
    if (!virtualGoldBalance) {
        isUseVirtualGold = false
    }
    var useVirtualGold = $('<input type="hidden" name="useVirtualGold" />');
    useVirtualGold.attr('value', isUseVirtualGold);

    var useRedPacketDiscount = $('#redPacketInput:checked').val()
    var isUseRedPacket = true
    if (!useRedPacketDiscount) {
        isUseRedPacket = false
    }
    var useRedPacket = $('<input type="hidden" name="useRedPacket" />');
    useRedPacket.attr('value', isUseRedPacket);

    var peerTypeInput;
    if(peerType && peerType.length>0){
        var peerTypeInput = $('<input type="hidden" name="peerType" />');
        peerTypeInput.attr('value', peerType);
    }

    $("input[name=cartIds]").each(function(){
        var cartIdsInput = $('<input type="hidden" name="cartIds" />');
        cartIdsInput.attr('value', $(this).val());
        form.append(cartIdsInput);
    });

    var giftIdsInput = $('<input type="hidden" name="giftIds" />');

    var more=$("#divmore").val();
    var giftId;
    if(more==0){
        giftId=$("input[name='giftId']:checked").val();
    }else{
        giftId=$("input[name='giftIdmore']:checked").val();
    }
    if(undefined==giftId){
        giftId="-1";
    }
    giftIdsInput.attr('value', giftId);

    // 附加到Form
    form.append(tranNoInput);
    form.append(storeStatusInput);
    form.append(billTypeInput);
    form.append(payTypeInput);
    form.append(voucherIdInput);
    form.append(remarkInput);
    form.append(addressIdInput);
    form.append(moneyInput);
    form.append(balanceAmountInput);
    form.append(shoppingCartImg);
    form.append(giftIdsInput);
    form.append(productNum);
    form.append(skuId);
    form.append(bizSource);
    form.append(bizScene);
    /**添加组合购信息到Form */
    /**对组合购单独处理 */
    let param = new URLSearchParams(window.location.search);
    let bizProductsInput = null;
    let bizSceneInput = null
    if(param.get('bizProducts')) {
        let bizProducts = param.get('bizProducts');
        bizProductsInput = $('<input type="hidden" name="bizProducts" />');
        bizProductsInput.attr('value', bizProducts);
    }
    if(param.get('bizScene')) {
        let bizScene = param.get('bizScene');
        bizSceneInput = $('<input type="hidden" name="bizScene" />');
        bizSceneInput.attr('value', bizScene);
    }
    if(bizSceneInput){
        form.append(bizSceneInput);
    }
    if(bizProductsInput){
        form.append(bizProductsInput);
    }
    if (token) {
        form.append(tokenInput);
    }
    //form.append(bizScene);
    form.append(virtualGold);
    form.append(useVirtualGold);
    form.append(useRedPacket);
    /* form.append(mddata); */

    
    if(peerTypeInput && peerTypeInput.length>0){
        form.append(peerTypeInput);
    }
    form.append(notSubmitOrderOrgIds);
    // var text = $("#offlinePayTips").val();
    // if(payType == 3 && text){
    //     $.confirm({
    //         title: '提示',
    //         body: text,
    //         okHidden: function () {
    //             form.submit();
    //         }
    //     });
    // }else{
    //     form.submit();
    // }
    var isPayMergeInput = $('<input type="hidden" name="isPayMerge" />');
    isPayMergeInput.attr('value', 1);
    form.append(isPayMergeInput);
    form.submit();
    /* if (payType == 5) {
        $.confirm({
            title: '温馨提示',
            body: '您当前所选支付方式为【自有账期】，提交订单后将会自动支付并出库，请确认是否继续提交？',
            okHidden: function () {
                form.submit();
            },
            cancelHidden:function(e){
                $("#settle_btn").removeClass("tjbtn-gray");
                $("#settle_btn").addClass("tjbtn");
                $("#settle_btn").bind('click', checkVoucherMonitor);
                // return false;
            }
        });
    } else {
        // 提交表单
        form.submit();
    } */

}

/*初始化判断位置*/
function initpositon(){
    var a = document.getElementById("footer").offsetTop;
    if(a >= $(window).scrollTop() && a < ($(window).scrollTop() + $(window).height())) {
        $(".applybox").removeClass("orderfix");
    } else {
        $(".applybox").addClass("orderfix");
    }
}
/**
 * 跳转到编辑收货地址
 */
function editAddress(obj){
	$doObj = $(obj);
	$doObj.attr("data-target","#editModal");
    var parentObj = $doObj.parents(".gspadres");

    $("#editModal [name='id']").val(parentObj.find("input[hi='id']").val());
    $("#editModal [name='contactor']").val(parentObj.find("span[data-id='contactor']").text());
    $("#editModal [name='mobile']").val(parentObj.find("span.mobile").text());
    $("#editModal [name='fullAddress']").html(parentObj.find("p[data-id='fullAddress']").text());
    $("#editModal [name='remark']").html(parentObj.find("input[hi='remark']").val());
    var $checkbox = $("#editModal [name='isdefault']").parent("#isdefault").checkbox();
    var isdefault =parentObj.find("input[hi='isdefault']").val();
    if(isdefault && isdefault == 'true'){
        $(".is_default").hide();
        $(".default_address").show();
        $checkbox.checkbox("check");
        $checkbox.checkbox("disable");
    }else {
        $checkbox.checkbox("enable");
        $checkbox.checkbox("uncheck");
        $(".is_default").show();
        $(".default_address").hide();
    }
    var auditState = parentObj.find("input[hi='auditState']").val();
    var remark = parentObj.find("input[hi='remark']").val();
    if (auditState != 2 && auditState != 3) {
        $("#editModal .detailaddress").html("<input type=\"text\" maxlength=\"50\" class=\"detailinp\" name=\"remark\" value='" + remark + "' placeholder=\"请输入地址备注信息\">");
        $(".addressTips").html(
            "<div class=\"sui-msg msg-default msg-notice\" style=\"position: relative;margin-top:10px;\">\n" +
                "<div class=\"msg-con\" style=\"padding:10px 0px;color: #F5A623;border-width:0px;\">请确保地址能被快递员识别，如不能识别，请在输入框内加以辅助描述，限50字。地址备注信息不可重复修改，请认真填写；</div>\n" +
            " </div>"
        );
    } else {
        $("#editModal .detailaddress").html(remark+"<input type=\"hidden\" name=\"remark\" value="+remark+">");
        $(".addressTips").empty();
    }
}
/**
 * 编辑收货地址
 */
function doEditAddress(){
	var index=layer.load(2);

    var address = $("#editModal [name='remark']").val();
    if(address && (address.length<2 || address.length>50)){
        layer.close(index);
        $.alert("地址备注不能大于50个字哦");
        return false;
    }

    var id=$("#editModal [name='id']").val();

    var contactor=$("#editModal [name='contactor']").val();

    if(!contactor){
    	layer.close(index);
    	$.alert("姓名不能为空哦");
    	return false;
    }

    if(contactor.length > 8){
    	layer.close(index);
    	$.alert("姓名不能大于八个字哦");
    	return false;
    }
    var mobile=$("#editModal [name='mobile']").val();

    // if(mobile.length != 11) {
    //     layer.close(index);
    //     $.alert("手机号必须为11位哦");
    //     return false;
    // }

    if(!mobile){
        layer.close(index);
        $.alert("请填写电话号码");
        return false;
    }
    var xx = /^[0-9]*$/;
    if(!xx.test(mobile)){
        layer.close(index);
        $.alert("请填写正确的电话号码");
        return false;
	}

    var isdefault = $("#editModal [name='isdefault']").is(":checked");
    if(contactor && mobile){

        $.ajax({
           type: "POST",
           url: "/merchant/center/shippingAddress/updateGSPAddress.json",
            data: {"id":id,"contactor":contactor,"mobile":mobile,"remark":address, "isdefault":isdefault},
            dataType: "json",
            success: function (data) {
            	if (data.status === "success") {
                    var address = data.shippingAddress;
                    var addressNode = $('input[hi=id][value=' + id +']').parent();
                    addressNode.find("span[data-id='contactor']").text(address.contactor);
                    addressNode.find("span.mobile").text(address.mobile);
                    addressNode.find("input[hi='remark']").val(address.remark);
                    addressNode.find("input[hi='auditState']").val(address.auditState);

                    if(isdefault){
                        //设置单选框
                        $(".default-show").find("[name='radio']").parent().checkbox().checkbox('uncheck');
                        $(".default-show").find("[name='radio']").removeAttr("checked");
                        addressNode.find("[name='radio']").parent().checkbox().checkbox('check');
                        addressNode.find("[name='radio']").attr("checked", "checked");
                        //设置默认标签
                        $(".default-show").find("input.isdefault").prop("value",false);
                        addressNode.find("input[hi='isdefault']").val(isdefault);
                        $(".default-show").find("span.default").remove();
                        addressNode.find("span[data-id='mobile']").append("<span class='def-biaoqian'> <span class='default'>默认</span> </span>");
                        var parent = addressNode.parent();
                        $(parent).prepend(addressNode);
                    }

	        		$("#editModal .sui-close").click();
	            }else{
	            	$.alert(data.errorMsg);
	            }
	        	layer.close(index);
            }
        });
    }
    layer.close(index);
    return false;
}
/**
 * 删除收货地址
 */
function doDeleteAddress(obj){
	var id=$(obj).parents("li").find("input[hi='id']").val();
    $('#delModal').on('okHide', function (e) {
    	var index=layer.load(2);
        $.ajax({
            type: "POST",
            url: "/merchant/center/shippingAddress/deleteShippingAddress.json",
            data: {"id":id},
            dataType: "json",
            success: function (data) {
                if (data.status === "success") {
                    $(obj).parents("li").remove();
                    var liLength=$(".address li").length;
                    if(liLength==3){
                		$(".default-show .add-more-box").remove();
                	}
                }else{
                    $.alert(data.errorMsg);
                }
                layer.close(index);
            }
        });
    });

    return false;
}
/**
 * 添加地址
 * @param obj
 */
function addAddress(obj){
	$("#addModal input").val("");
	$("#city_4").citySelect({
		prov:"420000",
		city:"420100",
		dist:"420112",
		nodata:"none"
	});
	$("#addModal input[type='checkbox']").prop("checked",false);
	$(obj).attr("data-target","#addModal");
}
/**
 * 添加收货地址
 */
function doAddAddress(){
    var address = $("#addModal [name='address']").val();
    if(address && (address.length<2 || address.length>100)){
        return;
    }
    var contactor=$("#addModal [name='contactor']").val();
    var mobile=$("#addModal [name='mobile']").val();
    var provinceCode=$("#addModal [name='province']").val();
    var cityCode=$("#addModal [name='city']").val();
    var areaCode=$("#addModal [name='district']").val();
    var address=$("#addModal [name='address']").val();
    var isdefault=$("#addModal [name='isdefault']").prop("checked");
    if(contactor && mobile && isMobile(mobile) && provinceCode && cityCode && areaCode && address){
    	var index=layer.load(2);
    	var province=$("#addModal [name='province'] :selected").text();
        var city=$("#addModal [name='city'] :selected").text();
        var district=$("#addModal [name='district'] :selected").text();
    	$.ajax({
	        type: "POST",
	        url: "/merchant/center/shippingAddress/addShippingAddress.json",
	        data: {"contactor":contactor,"mobile":mobile,"province":province,"city":city,"district":district,"provinceCode":provinceCode,"cityCode":cityCode,"areaCode":areaCode,"address":address,"isdefault":isdefault},
	        dataType: "json",
	        success: function (data) {
	        	if (data.status === "success") {
	        		var html ="<li>"
		        		+ "<input type='hidden' hi='id' value='"+data.id+"'/>"
		        		+ "<input type='hidden' hi='province' value='"+provinceCode+"'/>"
		        		+ "<input type='hidden' hi='city' value='"+cityCode+"'/>"
		        		+ "<input type='hidden' hi='district' value='"+areaCode+"'/>"
		        		+ "<input type='hidden' hi='address' value='"+address+"'/>"
		        		+ "<div class='a-col1 fl contactor'>"+contactor+"</div>"
		        		+ "<div class='a-col2 fl'>"+(province+city+district+address)+"</div>"
		        		+ "<div class='a-col3 fl mobile'>"+mobile+"</div>"
		        		+ "<div class='a-col4 fl'><a href='javascript:void(0);' data-toggle='modal' onclick='editAddress(this)' data-keyboard='true'>编辑</a></div>"
		        		+ "<div class='a-col5 fl'><a href='javascript:void(0);' data-toggle='modal' data-target='#delModal' data-keyboard='false' onclick='doDeleteAddress(this)'>删除</a></div>"
		        		+ "<div class='a-col6 fl'>";
	        		if(isdefault){
	        			var oldDefaultObj=$(".address").find("div.default-add");
	        			oldDefaultObj.html("<a href='javascript:void(0);'  data-toggle='modal' onclick='doIsDefault(this)'>设为默认收货地址</a>");
	        			oldDefaultObj.attr("class","set-default-add");
	        		 	html += "<div class='default-add '><span class='sui-icon icon-tb-roundcheckfill'></span>默认地址</div>";
	        				 + "</div></li>";
	    				 if($(".address li").length==0){
	    					 $(".address").append(html);
	    				 }else{
	    					 $(".address li:eq(0)").before(html);
	    				 }
	        		}else{
						html += "<div class='set-default-add '><a href='javascript:void(0);' data-toggle='modal' onclick='doIsDefault(this)'>设为默认收货地址</a></div></li>";
						$(".address").append(html);
	        		}
	        		var liLength=$(".address li").length;
	        		if(liLength==4){
	        			var html="<div class='add-more-box'>"
	        				+ "<a href='javascript:void(0);' class='add-more'>展开查看更多地址 <i class='sui-icon icon-tb-unfold'></i></a>"
	        				+ "<a href='javascript:void(0);' class='add-no-more'>点击收起 <i class='sui-icon  icon-tb-fold'></i> </a>"
	        				+ "</div>";
	        			$(".address").after(html);
	        			var height=$(".recebox .address").css("max-height");
	        			if(height=="123px"){
	        				$(".add-more").css("display","block");
	        				$(".add-no-more").css("display","none");
	        			}else{
	        				$(".add-more").css("display","none");
	        				$(".add-no-more").css("display","block");
	        			}
	        			/*收货地址展开收起*/
	        			$(".add-more").click(function(){
	        				$(this).css("display","none");
	        				$(".add-no-more").css("display","block");
	        				$(".recebox .address").css("max-height","none");
	        			});
	        			$(".add-no-more").click(function(){
	        				$(this).css("display","none");
	        				$(".add-more").css("display","block");
	        				$(".recebox .address").css("max-height",123);
	        			});
	        		}
	        		$("#addModal .sui-close").click();

	            }else{
	            	$.alert(result.errorMsg);
	            }
	        	layer.close(index);
	        }
	    });
    }
    return false;
}
/**
 * 获取商品总金额
 */
function getTotalGoodsMoney(){

    var voucherIds = getSelectVoucherIds();

	var payType=$(".zffs .cur").attr("t");	//支付类型
	var billType=$(".fplx .cur").attr("t");	//支付类型
	var balanceAmount = $('input[name=balanceAmount]').val();
	var more=$("#divmore").val();
    var giftId;
	if(more==0){
        giftId=$("input[name='giftId']:checked").val();
    }else{
        giftId=$("input[name='giftIdmore']:checked").val();
    }
    if(undefined==giftId){
        giftId="-1";
    }
    var bizSource = $('#bizSource').val();
    //var bizScene = $('#bizScene').val();
    var groupSkuId = $('#groupSkuId').val();
    var groupProductNum = $('#groupProductNum').val();

    var virtualGoldBalance = $('#virtualGoldInput:checked').val()
    console.log(virtualGoldBalance)
    var useVirtualGold = true
    if (!virtualGoldBalance) {
        useVirtualGold = false
    }
    console.log(useVirtualGold)

    var redPacketBalance = $('#redPacketInput:checked').val()
    console.log(redPacketBalance)
    var useRedPacket = true
    if (!redPacketBalance) {
        useRedPacket = false
    }

    var notSubmitOrderOrgIds = $('#notSubmitOrderOrgIds').val()

    let param = new URLSearchParams(window.location.search);
    let bizProducts = null;
    if(param.get('bizProducts')) {
        bizProducts = param.get('bizProducts');
    }
    let bizScene = null;
    if(param.get('bizScene')) {
        bizScene = param.get('bizScene');
    }
    const suiXinPinSkusUpload = getSuiXinPinSkus();
	//异步请求金额计算方法
	$.ajax({
		type: "POST",
		url: "/merchant/center/order/calcSettleAmount.json",
        data: {
            "voucherIds":JSON.stringify(voucherIds),
            "payType":payType,
            "billType":billType,
            "balanceAmount":balanceAmount,
             giftIds:giftId,
             voucherMonitor:"1",
             bizSource:bizSource,
             bizScene:bizScene,
             skuId:groupSkuId,
             productNum:groupProductNum,
             useVirtualGold:useVirtualGold,
             virtualGoldBalance:virtualGoldBalance,
             notSubmitOrderOrgIds:notSubmitOrderOrgIds,
             useRedPacket:useRedPacket,
             bizProducts:bizProducts,
             suiXinPinSkus: JSON.stringify(suiXinPinSkusUpload)
            },
        dataType: "json",
        success: function (data) {
        	if(data.status=="success"){
                $.ajax({
                    url: '/merchant/center/order/queryConsumeRebateDetail',
                    type: 'post',
                    data: {
                    money:data.money
                    },
                    success(data) {
                       if(data.status=="success"){
                            if(data.data.actResultList!=null||data.data.actResultList.length>0){
                                if(data.data.levelScene == 2){
                                    $('#levelScene2').show()
                                    $('#levelScene3').hide()
                                    $('#levelScene4').hide()
                                    $('#levelScene5').hide()
                                    $('#referralPercentage').text(data.data.nextLevelRate)
                                    $('#differenceValue').text(data.data.nextLevelShortAmount+'元')
                                }
                                if(data.data.levelScene == 3&&data.data.nextLevelShortAmount!=0){
                                    $('#levelScene3').show()
                                    $('#levelScene2').hide()
                                    $('#levelScene4').hide()
                                    $('#levelScene5').hide()
                                    $('#moneyVal').text(data.data.realAmount)
                                    $('#differValue').text(data.data.nextLevelShortAmount+'元')
                                    $('#redPacketVal').text(data.data.nextLevelRedPacketAmount+'元')
                                }
                                if(data.data.levelScene == 3&&data.data.nextLevelShortAmount==0){
                                    $('#levelScene5').show()
                                    $('#levelScene2').hide()
                                    $('#levelScene3').hide()
                                    $('#levelScene4').hide()
                                    $('#moneyZeroVal').text(data.data.realAmount)
                                }
                                if(data.data.levelScene == 4){
                                    $('#levelScene4').show()
                                    $('#levelScene2').hide()
                                    $('#levelScene3').hide()
                                    $('#levelScene5').hide()
                                    $('#amountVal').text(data.data.realAmount)
                                    $('#maxAmount').text(data.data.maxReturnRedPackageAmount+'元')
                                }
                            startCountdown(data.data.toEtimeSeconds); 
                            var row = ""
                            $("#data-table tbody").empty();
                            $.each(data.data.actResultList, function(_, item) {
                            // 拼接金额范围
                                let amountRange = item.amount;
                                if (item.nextLevelAmount) {
                                    amountRange += ' - ' + item.nextLevelAmount+'元';
                                } else {
                                    amountRange += '元';
                                }
                                // 构建费率单元格
                                let rateCell = $('<td class="rate-cell">').append(
                                    $('<span>').text(item.rate+'%')
                                );
                                // 条件添加额外补贴
                                if (item.extraSubsidyAmountRate) {
                                    rateCell.append(
                                        $('<div class="add-ratio">').text('+'+item.extraSubsidyAmountRate+'%')
                                    );
                                }
                                if (item.extraSubsidyAmountRate) {
                                    rateCell.append(
                                        $('<div class="limited-time-offer"><div class="limited-label">限时加补' + item.extraSubsidyAmountRate + '%,仅剩<span class="countdownEnd"></span></div></div>')
                                    );
                                }
                                // 创建表格行
                                row = $('<tr>').append(
                                    $('<td>').text(amountRange),
                                    rateCell,
                                    $('<td>').text(item.maxAmount+'元')
                                );
                                $("#data-table tbody").append(row);
                            });
                       
                                var str = "";
                                if(data.data.levelScene == 1){
                                    str = '<div class="state-color">参与下单返活动，预估月均可返红包 <span>'+data.data.lastMonthExpectedAmount+'元</span></div>'  
                                }else if(data.data.levelScene == 2){
                                    str = '<div class="state-color">仅差<span>'+data.data.nextLevelShortAmount+'元</span>参与返利活动，返利<span>'+data.data.nextLevelRate+'%</span>起</div>'
                                }else if(data.data.levelScene == 3&&data.data.nextLevelShortAmount!=0){
                                    str = '<div class="state-color">订单完成获得红包<span>'+data.data.realAmount+'元</span>仅差<span>'+data.data.nextLevelShortAmount+'元</span>可得<span>'+data.data.nextLevelRedPacketAmount+'元红包</span></div>'
                                }else if(data.data.levelScene == 3&&data.data.nextLevelShortAmount==0){
                                    str = '<div class="state-color">订单完成获得红包<span class="amount-color">'+data.data.realAmount+'元</span></div>'
                                }else{
                                    str = '<div class="state-color"> 订单完成获得红包 <span>'+data.data.realAmount+'元,</span>多买多返最高返<span>'+data.data.maxReturnRedPackageAmount+'元</span></div>'
                                }
                                $("#tip-content").html(str)
                                $("#rewardbanner").show()
                            }
                        }
                    },
                    error(err) {
                        parent.layer.msg('网络异常');
                    }
                })
        	    // 刷新商品各种金额
                if (data.detailList) {
                    $.each(data.detailList, function(i, row){
                        $("#" + row.uniqueKey + "realPayAmount").text("￥" + row.realPayAmount);
                        $("#" + row.uniqueKey + "discountAmount").text("￥" + row.discountAmount);
                        $("#" + row.uniqueKey + "useBalanceAmount").text("￥" + row.useBalanceAmount);
                        $("#" + row.uniqueKey + "balanceAmount").text("￥" + row.balanceAmount);

                        $("#" + row.uniqueKey + "realPayAmountK").text("￥" + row.realPayAmount);
                        $("#" + row.uniqueKey + "discountAmountK").text("￥" + row.discountAmount);
                        $("#" + row.uniqueKey + "useBalanceAmountK").text("￥" + row.useBalanceAmount);
                        $("#" + row.uniqueKey + "balanceAmountK").text("￥" + row.balanceAmount);

                        //实付价  实付价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）-余额抵扣；
                        $("." + row.uniqueKey + "purchasePrice").text("￥" + returnFloat(row.purchasePrice));
                        //成本价  成本价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）-返利；
                        $("." + row.uniqueKey + "costPrice").text("￥" + returnFloat(row.costPrice));

                        $("." + row.uniqueKey + "purchasePriceK").text("￥" + returnFloat(row.purchasePrice));
                        $("." + row.uniqueKey + "costPriceK").text("￥" + returnFloat(row.costPrice));
                        if (row.tcList) {
                            var tag = '';
                            $.each(row.tcList, function (j, value) {
                                if (j < 3) {
                                    var uiType = value.uiType;
                                    if (uiType == 1) {
                                        tag = tag + '<span class="linqi" >' + value.name + '</span> ';
                                    } else if (uiType == 2) {
                                        tag = tag + '<span class="quan" >' + value.name + '</span> ';
                                    } else if (uiType == 3) {
                                        tag = tag + '<span class="manjian" >' + value.name + '</span> ';
                                    } else if (uiType == 4) {
                                        tag = tag + '<span class="default" >' + value.name + '</span> ';
                                    } else if (uiType == 5) {
                                        tag = tag + '<span class="yibao" >' + value.name + '</span> ';
                                    }
                                }
                            });
                            $("#" + row.uniqueKey + "row-biaoqian").html(tag);
                        }
                    });
                }

                // 刷新店铺小计
                if (data.shopSubtotalList) {
                    $.each(data.shopSubtotalList, function(i, row){
                        $("#" + row.uniqueKey + "promoTotalAmt").text(returnFloat(row.promoTotalAmt));
                        $("#" + row.uniqueKey + "voucherTotalAmt").text(returnFloat(row.voucherTotalAmt));
                        $("#" + row.uniqueKey + "payAmount").text(returnFloat(row.payAmount));
                        $("#" + row.uniqueKey + "voucherTip").text(row.voucherTip);
                    });
                }

                // 刷新公司小计
                if (data.companySubtotalList) {
                    $.each(data.companySubtotalList, function(i, row){
                        $("#" + row.uniqueKey + "productVarietyNum").text(row.shops[0].productVarietyNum);
                        $("#" + row.uniqueKey + "productTotalNum").text(row.shops[0].productTotalNum);
                        $("#" + row.uniqueKey + "totalAmount").text(returnFloat(row.shops[0].totalAmount));
                        $("#" + row.uniqueKey + "freightTotalAmt").text(returnFloat(row.freightTotalAmt));
                        $("#" + row.uniqueKey + "promoTotalAmt").text(returnFloat(data.shopSubtotalList[i].promoTotalAmt));
                        $("#" + row.uniqueKey + "voucherTotalAmt").text(returnFloat(data.shopSubtotalList[i].voucherTotalAmt)); //
                        /* $("#" + row.uniqueKey + "payAmount").text(returnFloat(data.shopSubtotalList[i].payAmount)); // */

                        if(row.freightTipsShowStatus == 0){
                            $("#freightTipsP").hide();
                        }else if(row.freightTipsShowStatus == 1){
                            $("#freightTipsP").html('实付金额满<span class="red">'+row.freeDeliveryAmount+'</span>元包邮，还需凑<span class="red">'+row.freeFreightDiffAmount+'元 <a href="/merchant/freight/list.htm?route=settle" style="color:#e73734;padding-left:10px;">'+row.freightUrlText+'></a>');
                            $("#freightTipsP").show();

                        }
                        if(row.freightIconShowStatus == 0){
                            $("#freightTipsA").hide();
                        }else if(row.freightIconShowStatus == 1){
                            $("#freightTipsA").show();
                        }
                    });
                }

                // 刷新总计
                $("#finalTotalAmount").text(returnFloat(data.finalTotalAmount));
                $("#finalPromoTotalAmt").text(returnFloat(data.finalPromoTotalAmt));
                $("#finalVoucherTotalAmt").text(returnFloat(data.finalVoucherTotalAmt));
                $("#finalFreightTotalAmt").text(returnFloat(data.finalFreightTotalAmt));
                $("#finalFixedPriceAmount").text(returnFloat(data.finalFixedPriceAmount));
                $("#finalPayAmount").text("￥"+returnFloat(data.finalPayAmount));

                $("#virtualGoldBalance").text(returnFloat(data.virtualGoldBalance));
                $("#availVirtualGold").text(returnFloat(data.availVirtualGold));
                $("#finalRedPacketAmount").text(returnFloat(data.redPacketAmount));
                if(data.finalFreightTotalAmt > 0){
                    $("#orderSettleFreightTotalAmt").text('(含运费¥'+data.finalFreightTotalAmt.toFixed(2)+')');
                    $("#orderSettleFreightTotalAmt").show();
                }else {
                    $("#orderSettleFreightTotalAmt").hide();
                }

        	}
        }
	});
}

/**
 * 设为默认地址
 * @param obj
 */
function doIsDefault(obj){
	$(obj).attr("data-target","#editDefaultModal");
	var id=$(obj).parents("li").find("input[hi='id']").val();
    $('#editDefaultModal').on('okHide', function (e) {
    	var index=layer.load(2);
        $.ajax({
            type: "POST",
            url: "/merchant/center/shippingAddress/updateShippingAddress.json",
            data: {"id":id,"isdefault":1},
            dataType: "json",
            success: function (data) {
                if (data.status === "success") {
                	var oldDefaultObj=$(".address").find("div.default-add");
        			oldDefaultObj.html("<a href='javascript:void(0);'  data-toggle='modal' onclick='doIsDefault(this)'>设为默认收货地址</a>");
        			oldDefaultObj.attr("class","set-default-add");
        			var newDefaultObj=$(obj).parents("li").find("div.set-default-add");
        			newDefaultObj.html("<span class='sui-icon icon-tb-roundcheckfill'></span>默认地址");
        			newDefaultObj.attr("class","default-add");
        			$(".address li:eq(0)").before(newDefaultObj.parents("li"));
                	$(obj).parents("li").remove();
                }else{
                    $.alert(data.errorMsg);
                }
                layer.close(index);
            }
        });
    });

    return false;
}

function clearNoNum(obj){
    obj.value = obj.value.replace(/^\./g,'');//必须保证第一个为数字而不是.
    obj.value = obj.value.replace(/[^\d.]/g,"");  //清除“数字”和“.”以外的字符
    obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
    if(obj.value.indexOf(".")< 0 && obj.value !=""){//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        obj.value= parseFloat(obj.value);
    }
    if(obj.value=='' || obj.value == 0){
        $(".resultprice").text(0);
       // obj.value = 0;
       // $(".yuedk").css("display","none");
    }else{
        $(".resultprice").text(obj.value);
       // $(".yuedk").css("display","block");
    }
}

/*控制选中后背景颜色*/
function setcolor(){
    $(".youhuiquan .checkbox-pretty").each(function(){
        if($(this).hasClass("checked")){
            $(this).closest('li').addClass("cur");
        }else{
            $(this).closest('li').removeClass("cur");
        }
    });
}

function getSelectVoucherIds() {
    var voucherIds = {"notVoucherIds":"-1"};
    var voucherIdInput = $('.youhuiquan ul .cur > input[name="voucherId"]');	///优惠券编号
    // console.log(888,voucherIdInput,$('.youhuiquan .cg-yhq ul.ky li '))
    $.each(voucherIdInput, function(index, entry){
        var voucherId = $(entry).val();
        if (voucherId == 1) {
            var djVoucherIdInput = $('#yhqModal ul input[name=voucherId]');	///优惠券编号
            $.each(djVoucherIdInput, function(index, entry2){
                voucherIds[$(entry2).val()] = $(entry2).attr("shopcode")+":"+$(entry2).attr("shoppatterncode");
            });
        } else {
            voucherIds[$(entry).val()] = $(entry).attr("shopcode")+":"+$(entry).attr("shoppatterncode");
        }
    });
    return voucherIds;
}

function getAllRemarks() {
    var remarks = {};
    $(".companyRemark").each(function (index,entry) {
        remarks[$(entry).attr("company")] = $(entry).val();
    })
    return remarks;
}

function showVoucherUnavaliable() {
    var oldSpan = $("#nopCollect").find("span");
    var oldText = oldSpan.html();
    var voucherUnavailableText = $("#voucherUnavailableDialog").html();
    oldSpan.html(voucherUnavailableText);
    $("#nopCollect").modal('show');
    setTimeout(function(){
            $("#nopCollect").modal('hide');
            oldSpan.html(oldText);
        }  ,2000)
}
//js保留两位小数，自动补充零
function returnFloat(value){

    if(isEmpty(value)){
        return "0.00";
    }

    var value=Math.round(parseFloat(value)*100)/100;
    var xsd=value.toString().split(".");
    if(xsd.length==1){
        value=value.toString()+".00";
        return value;
    }
    if(xsd.length>1){
        if(xsd[1].length<2){
            value=value.toString()+"0";
        }
        return value;
    }
}

function isEmpty(v) {
    switch (typeof v) {
        case 'undefined':
            return true;
        case 'string':
            if (v.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g, '').length == 0) return true;
            break;
        case 'boolean':
            if (!v) return true;
            break;
        case 'number':
            if (0 === v || isNaN(v)) return true;
            break;
        case 'object':
            if (null === v || v.length === 0) return true;
            for (var i in v) {
                return false;
            }
            return true;
    }
    return false;
}
const inputInit = () => {
	const inputBox = document.getElementById('inputBox');
	const inputList = inputBox.children;
	const passwordList = [];
	//输入框组件
	const pay = document.getElementById('xx-pay');
	//关闭按钮
	const closeBtn = document.getElementById('xx-pay-close');
	//忘记密码按钮
	const forgetBtn = document.getElementById('xx-pay-forget');
	//红字提示
	const msgEl = document.getElementById('xx-pay-msg');
	//提交按钮
	const submitBtn = document.getElementById('xx-pay-submit');
	for (let i = 0; i < inputList.length; i++) {
		passwordList[i] = '';
	}
	function setFocus(index) {
		const timer = setTimeout(() => {
			if (!inputList[index]) return ;
			inputList[index].focus();
			clearTimeout(timer);
		},0)
	} 
	function close() {
		for (let i = 0; i < inputList.length; i++) {
			passwordList[i] = '';
			inputList[i].style.borderColor = "";
			inputList[i].value = '';
		}
		msgEl.textContent = '';
		pay.style.zIndex = '-1';
		pay.style.opacity = 0;
	}

	function forget() {
        window.open("/merchant/center/setPayPwd/index");
	}
	function submit() {
		let target = true;
		passwordList.forEach((val,index) => {
			if(!val) {
				target = false;
				inputList[index].style.borderColor = 'red';
			}
		})
		if (!target) {
			msgEl.textContent = "支付密码未输入完整"
			return ;
		}
		var tranNo = $("#tranNo").val();
		//密码加密
		var key = CryptoJS.enc.Utf8.parse("FmpHxGc9no95cvd4");  //十六位十六进制数作为密钥
		var srcs = CryptoJS.enc.Utf8.parse(passwordList.join(''));
		var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7, iv: key });
		pass = encrypted.toString();
		//验证密码是否正确
		$.ajax({
			type: "POST",
			url: "/merchant/center/checkPayPwd",
			data: {
				pwd: pass,
				tranNo: tranNo,
				orderId: ''
			},
			dataType: "json",
			success: function (data) {
				if (data.data.status == 1) {
					close();
					//正确
                    if(sourceName=='group_settle_btn'){
                        submitOrder('', true)
                    }else{
                        submitOrder(data.data.token);
                    }
					
				} else {
					msgEl.textContent = data.data.msg;
				}
			}
		 });
	}
	closeBtn.addEventListener('click', close);
	inputBox.addEventListener("keydown", (e) => {
		const index = Number(e.target.getAttribute('sub'));
		if (isNaN(index)) return;
		if (e.key == 'Backspace') {
			inputList[index].style.borderColor = '';
			inputList[index].value = '';
			passwordList[index] = '';
			setFocus(index - 1);
		}
		if (!isNaN(e.key) && e.key != ' ') {
			inputList[index].value = String.fromCharCode(Math.random() *  (0x9FA5 - 0x4E00) + 0x4E00);
			inputList[index].style.borderColor = '#009ecf';
			passwordList[index] = e.key
			setFocus(index + 1);
		}
		switch(e.key) {
			case 'ArrowLeft':
				setFocus(index - 1);
				break;
			case 'ArrowRight':
				setFocus(index + 1);
				break;
		}
		/* console.log(e.target.getAttribute('sub')); */
	})
	submitBtn.addEventListener('click',submit)
	forgetBtn.addEventListener('click',forget)
	/* const aaa = document.getElementById('aaa'); */
	/* aaa.addEventListener("click", () => {
		let target = true;
		passwordList.forEach((val,index) => {
			if(!val) {
				target = false;
				inputList[index].style.borderColor = 'red';
			}
		})
		if (target) {
			alert(passwordList.join(''), '付款成功')
		}
	}) */
}