package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: MaiDianActionEnum
 * @Description:
 *      埋点操作页面
 * @author: denghp
 * @date: 2020/3/16
 */
public enum MaiDianActionEnum {
    /**
     * 搜索页面
     */
    SEARCH("search",1,0),
    /**
     * 分类页面
     */
    CATEGORY("category",2,0),
    /**
     * APP 首页推荐
     */
    APP_RECOMMEND("app_recommend",3,1),
    /**
     * APP 商品详情页推荐
     */
    APP_CSU_DETAIL_RECOMMEND("app_csu_detail_recommend",3,2),
    /**
     * PC 商品详情页推荐
     */
    PC_CSU_DETAIL_RECOMMEND("pc_csu_detail_recommend",3,3),
    /**
     * PC 搜索无结果推荐
     */
    PC_SEARCH_NO_RESULT_RECOMMEND("pc_search_no_result_recommend", 3, 4),
    /**
     * APP 搜索无结果推荐
     */
    APP_SEARCH_NO_RESULT_RECOMMEND("app_search_no_result_commend",3, 5),

    /**
     * APP 发现页为你推荐
     */
    APP_DISCOVER_RECOMMEND("app_discover_recommend", 3, 6),

    /**
     * 首页常购清单
     */
    APP_HOME_ALWAYS_BUY_LIST("always_buy_list", 6, 1),

    /**
     * 发现页常购清单
     */
    APP_DISCOVER_ALWAYS_BUY_LIST("discover_always_buy_list", 6, 2),
    /**
     * 批量采购
     */
    PC_BATCH_PURCHASE("pc_batch_purchase", 24, 1)
    ;
    private String name;
    private Integer spType;
    private Integer spFrom;

    private static Map<String, MaiDianActionEnum> enumMaps = new HashMap<>();

    static {
        for(MaiDianActionEnum e : MaiDianActionEnum.values()) {
            enumMaps.put(e.getName(), e);
        }
    }

    public String getName() {
        return name;
    }

    public Integer getSpType() {
        return spType;
    }

    public Integer getSpFrom() {
        return spFrom;
    }

    MaiDianActionEnum(String name, Integer spType, Integer spFrom) {
        this.name = name;
        this.spType = spType;
        this.spFrom = spFrom;
    }

    public static MaiDianActionEnum getEnumByName(String name) {
        return enumMaps.get(name);
    }


}
