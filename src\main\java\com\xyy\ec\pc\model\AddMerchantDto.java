package com.xyy.ec.pc.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 添加店铺dto
 * @author: liuyang
 * @date 2022年10月25日17:14:02
 **/
@Data
public class AddMerchantDto implements Serializable {

    private static final long serialVersionUID = 1808529049265795752L;

    /**
     * 店铺类型
     */
    private Integer customerType;
    /**
     * 账号ID
     */
    private Long accountId;
    /**
     * 营业执照号/医疗机构执业许可证编号
     */
    private String licenseNo;
    /**
     * 店铺名称
     */
    private String name;
    /**
     * 省份编码
     */
    private String provinceCode;
    /**
     * 市编码
     */
    private String cityCode;

    /**  区编码 */
    private String areaCode;
    /**
     * 省份名称
     */
    private String province;
    /**
     * 市名称
     */
    private String city;
    /**
     * 区名称
     */
    private String district;
    /**
     * 街道编码
     */
    private String streetCode;
    /** 街道名称*/
    private String street;
    /** 详细地址 */
    private String address;
    /**
     * 营业执照电子版url/医疗机构执业许可证电子版url
     */
    private String licenseImageUrl;

    /**
     * 注册时查询的天眼查的企业基础数据信息透传回来
     */
    private String tycBasicDataDto;

    /**
     * ocr识别结果
     */
    private String ocrParseDTO;

}
