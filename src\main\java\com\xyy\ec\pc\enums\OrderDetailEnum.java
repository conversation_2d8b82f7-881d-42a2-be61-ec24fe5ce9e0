package com.xyy.ec.pc.enums;


import java.util.HashMap;
import java.util.Map;

/**
 * 订单明细 枚举
 */
public enum OrderDetailEnum {

    NO_DELIVERY(0, "未配送"),
    DELIVERING(1, "配送中"),
    FINISHED(2, "已完成"),
    RETURNED_GOODS(3, "已退货");

    private Integer code;
    private String name;

    private OrderDetailEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    private static final Map<Integer,OrderDetailEnum> map = new HashMap<Integer,OrderDetailEnum>();

    static {
        for (OrderDetailEnum t : OrderDetailEnum.values()){
            map.put(t.getCode(),t);
        }
    }

    public static OrderDetailEnum idOf(Integer key){
        return map.get(key);
    }

}
