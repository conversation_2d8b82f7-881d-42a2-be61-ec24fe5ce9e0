package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponBaseDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.service.CmsCouponService;
import com.xyy.ec.pc.service.ShopConfuseService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pop.server.api.external.ec.EcPopCorporationApi;
import com.xyy.ec.pop.server.api.merchant.api.OneSkuAllAreaGrayScaleApi;
import com.xyy.ec.pop.server.api.seller.dto.PopCorporationDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/coupon")
public class CmsCouponController extends BaseController {

    @Autowired
    private CmsCouponService cmsCouponService;

    @Reference(version = "1.0.0")
    private EcPopCorporationApi popCorporationApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private OneSkuAllAreaGrayScaleApi oneSkuAllAreaGrayScaleApi;

    private final static String OrgPrefix = "XS";

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private ShopConfuseService shopConfuseService;

    @RequestMapping(value = "/getPopCouponList", method = RequestMethod.GET)
    @ResponseBody
    public XyyJsonResult listCouponByIds(HttpServletRequest request, String orgId){
        if (BooleanUtils.isTrue(shopConfuseService.isCantShowShop(orgId))) {
            log.warn("【cms】pop优惠券查询，不显示店铺，orgId：{}", orgId);
            return XyyJsonResult.createFailure().msg(appProperties.getShopConfuseNotShowShopTip());
        }
        Long merchantId = null;
        String shopCode = orgId;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant != null) {
                merchantId = merchant.getId();
            }

            if(StringUtils.isNotBlank(orgId) && orgId.startsWith(OrgPrefix)){
                ApiRPCResult<PopCorporationDto> info = popCorporationApi.getOrgInfo(orgId);
                if(info.isSuccess() && info.getData() != null){
                    shopCode = getShopCode(info.getData().getOrgId(),orgId);
                }
            }

            if(StringUtils.isBlank(shopCode)){
                return XyyJsonResult.createFailure().msg("店铺编码为空");
            }

            List<CouponBaseDto> couponBaseDtos = cmsCouponService.listCouponByPopOrgId(merchantId, shopCode);
            return XyyJsonResult.createSuccess().addResult("list", couponBaseDtos);
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("【cms】pop优惠券查询失败，merchantId：{}，shopCode:{},orgId：{}",
                        merchantId, shopCode, orgId, e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("【cms】pop优惠券查询失败，merchantId：{}，shopCode:{},orgId：{}",
                    merchantId, shopCode, orgId, e);
            return XyyJsonResult.createFailure();
        }
    }

    public String getShopCode(String orgId,String orgAreaId){

        //主区域ID
        String shopCode = orgAreaId;
        try {
            ApiRPCResult<Boolean> result = oneSkuAllAreaGrayScaleApi.inGray(orgId);
            if(result.isSuccess() && result.getData()!= null && result.getData()) {
                shopCode = orgId;
            }
        } catch (Exception e) {
            log.error("【pop优惠券】查询灰度开关异常", e);
        }
        return shopCode;
    }

}
