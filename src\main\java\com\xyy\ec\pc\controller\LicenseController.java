package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.*;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.merchant.bussiness.dto.LicenseAuditBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.LicenseBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.LicensePoolBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.licence.MerchantForLicense;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.rpc.CrmRpcService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.JsonUtil;
import com.xyy.ec.system.business.api.SysUserBusinessApi;
import com.xyy.ec.system.business.dto.SysUserBusinessDto;
import com.xyy.electron.data.bussiness.data.api.SubjectCompanyLetterBussinessApi;
import com.xyy.electron.data.bussiness.data.dto.LicensesDto;
import com.xyy.electron.data.bussiness.data.enums.SceneCodeEnum;
import com.xyy.electron.data.bussiness.data.tool.ErrorCode;
import com.xyy.electron.data.bussiness.data.tool.ResultUtil;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.*;

/**
 * 个人中心
 * 
 * <AUTHOR>
 *
 */
@RequestMapping("/merchant/center/license")
@Controller
public class LicenseController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(LicenseController.class);

	@Reference(version = "1.0.0")
	private LicenseBussinessApi licenseBussinessApi;

	@Reference(version = "1.0.0")
    private LicenseCategoryBussinessApi licenseCategoryBussinessApi;

	@Reference(version = "1.0.0")
	LicenseImgUrlBussinessApi licenseImgUrlBussinessApi;

	@Reference(version = "1.0.0")
	private LicensePoolBusinessApi licensePoolBusinessApi;

	@Autowired
	private XyyIndentityValidator xyyIndentityValidator;

	@Reference(version = "1.0.0")
	private MerchantBussinessApi merchantBussinessApi;

	@Reference(version = "1.0.0")
	private SysUserBusinessApi sysUserBusinessApi;

	@Reference(version = "1.0.0")
	private LicenseAuditBussinessApi licenseAuditBussinessApi;

	@Reference(version = "1.0.0")
	private SubjectCompanyLetterBussinessApi subjectCompanyLetterBussinessApi;

	@Reference(version = "1.0.0")
	private MerchantLicenseApi merchantLicenseApi;

	@Autowired
	private CrmRpcService crmRpcService;

	/**
	 * 我的资质页面入口
	 * @return
	 */
	@RequestMapping(value = "/findLicenseCategoryInfo.htm", method = RequestMethod.GET)
	public ModelAndView findLicenseCategoryInfo(){
		Map<String,Object> model = new HashMap<String,Object>();
		MerchantPrincipal merchant = null;
		MerchantBussinessDto merchantBussinessDto=null;
		try {
			merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			merchantBussinessDto=merchantBussinessApi.selectByPrimaryKey(merchant.getId());
			//判断用户是否在开放的区域
			String newbranchCodes = licenseAuditBussinessApi.getNewLicenseBranchCode();
			Boolean boo = false;
			if(newbranchCodes.contains(merchantBussinessDto.getRegisterCode()) || newbranchCodes.contains(BranchEnum.ALL_COUNTRY.getKey())){
				//判断用户是否已过一审
				boo = licenseAuditBussinessApi.getAuditStatusByMerchantId(merchantBussinessDto.getId());
				if(boo == null || !boo){
					//根据用户信息，判断用户是不是未提交，首营待审核
					if(merchantBussinessDto.getLicenseStatus() != 1 && merchantBussinessDto.getLicenseStatus() != 5){
						boo = true;
					}
				}
			}
			model.put("boo",boo);
		} catch (Exception e) {
			LOGGER.error("获取会员信息异常:",e);
		}
		if (merchant == null) {
			return new ModelAndView(new RedirectView("/login/login.htm",true,false));
		}
		model.put("customerType", merchant.getBusinessType());
		LicensePoolBusinessDto dto = new LicensePoolBusinessDto();
		dto.setMerchantId(merchant.getId());
		List<LicensePoolBusinessDto> licensePoolBusinessDtos = null;
		Date now = new Date();
		try {
			//查询开放的区域，如果未开放不展示 资质列表及相关按钮
			String branchCodes= licenseAuditBussinessApi.getLicenseBranchCode();
			LOGGER.info("获取可操作的区域：branchCodes:{}",branchCodes);
			if(!branchCodes.contains(merchant.getRegisterCode()) && !branchCodes.contains(BranchEnum.ALL_COUNTRY.getKey())){
				model.put("branchCodeOpen","false");
			}else {
				model.put("branchCodeOpen","true");
			}

			//查询开放的区域，如果未开放不展示 资质列表及相关按钮
			//获取可编辑的单据
			List<LicenseAuditBusinessDto> editorLicenseAuditList = licenseAuditBussinessApi.getEditerLicenseAudit(merchant.getId());
			if(CollectionUtil.isNotEmpty(editorLicenseAuditList)){
				model.put("firstLicenseType", editorLicenseAuditList.get(0).getFirstLicenseType());
				model.put("customerType", editorLicenseAuditList.get(0).getCustomerType());
			}
			licensePoolBusinessDtos = licensePoolBusinessApi.selectList(dto);
			licensePoolBusinessDtos.forEach(licensePoolBusinessDto -> {
				if(licensePoolBusinessDto.getValidateTime() == null){
					licensePoolBusinessDto.setStatus(0);
					return;
				}

				if(licensePoolBusinessDto.getValidateTime().getTime() - now.getTime() < 0){
					licensePoolBusinessDto.setStatus(1); //逾期
				}else if (licensePoolBusinessDto.getValidateTime().getTime() - now.getTime() > 0 && licensePoolBusinessDto.getValidateTime().getTime() - now.getTime() < (long)30*1000*60*60*24){
					licensePoolBusinessDto.setStatus(2); //临期
				}else {
					licensePoolBusinessDto.setStatus(0); //正常
				}
			});
		} catch (Exception e) {
			LOGGER.error("查询资质列表异常", ExceptionUtils.getStackTrace(e));
		}
		String phone = crmRpcService.getBdPhoneByMerchantId(merchant.getId());
		if (StringUtils.isNotBlank(phone)) {
			model.put("phone", phone);
		}
		model.put("licenseList",licensePoolBusinessDtos);
		model.put("merchantId", merchant.getId());
		model.put("center_menu", "license");
		model.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
		model.put("merchant", merchantBussinessDto);
		return new ModelAndView("/license/license.ftl",model);
	}


	/**
	 * 我的资质详情
	 * @return
	 */
	@RequestMapping(value = "/detial.htm", method = RequestMethod.GET)
	public ModelAndView detial(){
		Map<String,Object> model = new HashMap<String,Object>();
		MerchantPrincipal merchant = null;
		try {
			merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
		} catch (Exception e) {
			LOGGER.error("获取会员信息异常:",e);
		}
		if (merchant == null) {
			return new ModelAndView(new RedirectView("/login/login.htm",true,false));
		}
		List<String> licenseCategoryInfoForApp=null;
		try {
			licenseCategoryInfoForApp= licenseImgUrlBussinessApi.queryLicenseImgurlByMerchantId(merchant.getId());
		} catch (Exception e) {
			LOGGER.error("查看资质出现异常,e="+e);
		}

		try {
			if(merchant.getSysUserId() == null) model.put("phone", null);
			SysUserBusinessDto sysUserEntity = sysUserBusinessApi.selectByPrimaryKey(merchant.getSysUserId());
			if (sysUserEntity != null) model.put("phone", sysUserEntity.getPhone());
		} catch (Exception e) {
			LOGGER.error("查询关系销售异常",e);
		}

		model.put("licenseCategoryList", licenseCategoryInfoForApp);
		model.put("merchantId", merchant.getId());
		model.put("center_menu", "license");
		model.put("merchant", merchant);
		model.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
		return new ModelAndView("/license/detial.ftl",model);
	}

	/**
	 * 保存或者修改资质信息
	 * @param license
	 * @return
	 */
    @RequestMapping("/saveLicenseInfo.json")
    @ResponseBody
    public Object saveLicenseInfo(LicenseBussinessDto license){
        try{
			MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchant == null) {
				return new ModelAndView(new RedirectView("/login/login.htm",true,false));
			}
			license.setMerchantId(merchant.getId());
			Long id = licenseBussinessApi.saveOrUpdateLicenseInfo(license);
			return this.addResult("id", id);
        }catch(Exception e){
            LOGGER.error("保存资质出错,e="+e);
            return this.addError("保存失败，请稍后重试");
        }
    }


	/**
	 * 公司资质列表
	 * @return
	 */
	@RequestMapping("/licenseList.htm")
	public ModelAndView licenseList(){
		try{
			Map<String,Object> model = new HashMap<String,Object>();
			MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			//判断用户是否在开放的区域
			String newbranchCodes = licenseAuditBussinessApi.getNewLicenseBranchCode();
			if(!newbranchCodes.contains(merchant.getRegisterCode()) && !newbranchCodes.contains(BranchEnum.ALL_COUNTRY.getKey())){
				model.put("msg","此功能暂未开放");
				return new ModelAndView("/helpCenter/xyyLicense.ftl",model);
			}
			String branchCode = merchant.getRegisterCode();
			String sceneCode = SceneCodeEnum.ec_ybm_scene_code.getCode();
			ResultUtil<List<LicensesDto>> resultUtil = subjectCompanyLetterBussinessApi.getContracts(branchCode,sceneCode,merchant.getRealName(), merchant.getId());
			if(resultUtil.getCode() == ErrorCode.FAIL){
				LOGGER.info("药帮忙资质获取失败,resultUtil:{}", JSONObject.toJSONString(resultUtil));
				model.put("msg",resultUtil.getMsg());
				return new ModelAndView("/helpCenter/xyyLicense.ftl",model);
			}
			model.put("data",resultUtil.getDataInfo());
			return new ModelAndView("/helpCenter/xyyLicense.ftl",model);
		}catch(Exception e){
			LOGGER.error("药帮忙资质获取异常,e="+e);
			return new ModelAndView("/helpCenter/xyyLicense.ftl");
		}
	}

	/**
	 * 客户首营认证
	 * @param merchantForLicense
	 * @return
	 */
	@RequestMapping(value = "/authentication.json", method = RequestMethod.POST)
	@ResponseBody
	public Object getMerchantInfoToAuthentication(MerchantForLicense merchantForLicense) {
		try {
			LOGGER.info("用户认证,入参：merchantForLicense:{}", JSONObject.toJSONString(merchantForLicense));
			ApiRPCResult<MerchantForLicense> rpcResult = merchantLicenseApi.getMerchantInfoToAuthentication(merchantForLicense);
			LOGGER.info("用户认证接口返回值:{}", JSONObject.toJSONString(rpcResult));
			if (rpcResult.isFail()) {
				String errMsg = StringUtils.isEmpty(rpcResult.getErrMsg()) ? rpcResult.getMsg(): rpcResult.getErrMsg();
				return this.addError(errMsg);
			}
			return this.addResult(DATA, rpcResult.getData());
		} catch (Exception e) {
			LOGGER.error("用户认证失败:", e);
			return this.addError("用户认证失败");
		}
	}
}
