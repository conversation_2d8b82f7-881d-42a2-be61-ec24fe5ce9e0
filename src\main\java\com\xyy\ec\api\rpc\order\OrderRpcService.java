package com.xyy.ec.api.rpc.order;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.base.log.ApiLog;
import com.xyy.ec.order.business.api.ecp.cart.EcpShoppingCartBusinessApi;
import com.xyy.ec.order.business.api.ecp.finance.loan.gfbt.FinanceMerchantBusinessApi;
import com.xyy.ec.order.business.api.ecp.finance.loan.gfbt.FinanceWishBusinessApi;
import com.xyy.ec.order.business.api.ecp.invoice.BillWhitelistBusinessApi;
import com.xyy.ec.order.business.api.ecp.orderforpromotion.PromotionMsgBusinessApi;
import com.xyy.ec.order.business.dto.BillWhitelistBusinessDto;
import com.xyy.ec.order.business.dto.ecp.finance.loan.gfbt.FinanceWishBusinessDto;
import com.xyy.ec.order.business.dto.ecp.orderforpromotion.PromotionMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/9 16:11
 * @Description:订单接口
 */
@Component
@Slf4j
public class OrderRpcService {
    @Reference(version = "1.0.0")
    FinanceMerchantBusinessApi financeMerchantBusinessApi;
    @Reference(version = "1.0.0")
    FinanceWishBusinessApi financeWishBusinessApi;
    @Reference(version = "1.0.0")
    EcpShoppingCartBusinessApi ecpShoppingCartBusinessApi;
    @Reference(version = "1.0.0")
    BillWhitelistBusinessApi billWhitelistBusinessApi;

    @Reference(version = "1.0.0")
    PromotionMsgBusinessApi promotionMsgBusinessApi;

    public ApiRPCResult getMerchantCreditLine(Long userId){
        ApiRPCResult apiRPCResult =  financeMerchantBusinessApi.getMerchantCreditLine(userId);
        return apiRPCResult;
    }
    @ApiLog
    public ApiRPCResult saveFinanceWish(FinanceWishBusinessDto dto){
        ApiRPCResult apiRPCResult =  financeWishBusinessApi.saveFinanceWish(dto);
        return apiRPCResult;
    }
    public ApiRPCResult selectRturnVoucherInfo(String shopCode, Long merchantId, Long promoId){
        ApiRPCResult apiRPCResult =  ecpShoppingCartBusinessApi.selectReturnVoucherInfo(shopCode, merchantId, promoId);
        return apiRPCResult;
    }
    @ApiLog
    public int ptdzInvoincePeer(Long merchantId){
        BillWhitelistBusinessDto billWhitelistBusinessDto = billWhitelistBusinessApi.selectByMerchantId(merchantId);
        return billWhitelistBusinessDto!=null?1:0;
    }
    @ApiLog
    public ApiRPCResult<PromotionMsg> queryPromotionMsg(Long skuId, Long actId, Long merchantId){
        return promotionMsgBusinessApi.queryPromotionMsg(skuId,actId, merchantId);
    }

}
