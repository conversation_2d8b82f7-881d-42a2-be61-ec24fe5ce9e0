package com.xyy.ec.pc.common.utils;

import org.apache.commons.lang3.StringUtils;

public class MobileDesensitizationUtils {

    public static String desensitizeMobilePhoneNumber(String mobilePhoneNumber) {
        if (StringUtils.isEmpty(mobilePhoneNumber)) {
            return mobilePhoneNumber;
        }
        if (mobilePhoneNumber.length() != 11) {
            return mobilePhoneNumber;
        }
        mobilePhoneNumber = mobilePhoneNumber.replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2");
        return mobilePhoneNumber;
    }

}
