package com.xyy.ec.pc.out.controller.dto;

/**
 * SAAS接口认证请求对象
 * 用于封装token、signature、timestamp等认证参数
 */
public class SaasAuthRequest {
    
    /**
     * 访问令牌
     */
    private String token;
    
    /**
     * 签名
     */
    private String signature;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    public SaasAuthRequest() {
    }
    
    public SaasAuthRequest(String token, String signature, Long timestamp) {
        this.token = token;
        this.signature = signature;
        this.timestamp = timestamp;
    }
    
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public String getSignature() {
        return signature;
    }
    
    public void setSignature(String signature) {
        this.signature = signature;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    @Override
    public String toString() {
        return "SaasAuthRequest{" +
                "token='" + token + '\'' +
                ", signature='" + signature + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}