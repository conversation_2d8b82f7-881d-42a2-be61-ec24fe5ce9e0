package com.xyy.ec.pc.controller.marketing;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.service.MerchantService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ms.marketing.nine.chapters.api.estimation.EstimationDiscountPriceApi;
import com.xyy.ms.marketing.nine.chapters.api.estimation.dto.DiscountPrice;
import com.xyy.ms.marketing.nine.chapters.api.estimation.dto.DiscountPriceDetail;
import com.xyy.ms.marketing.nine.chapters.api.estimation.dto.DiscountPriceDetailGroup;
import com.xyy.ms.marketing.nine.chapters.api.estimation.dto.DiscountPriceRequest;
import io.netty.util.internal.ConcurrentSet;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/marketing/discount/")
public class DiscountPriceController {
    @Reference(version = "1.0.0", timeout = 600)
    private EstimationDiscountPriceApi estimationDiscountPriceApi;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    private static final long MAX_CALCULATE_TIME = 1000L;
    private static volatile ConcurrentSet<String> BLACK_LIST = new ConcurrentSet<>();



    @Value("${hand.price.black.list}")
    public void setDiscountPriceBlackList(String blackList){
        doSetDiscountPriceBlackList(blackList);
    }

    private synchronized void doSetDiscountPriceBlackList(String blackList){
        if (StringUtils.isBlank(blackList)){
            BLACK_LIST = new ConcurrentSet<>();
        }
        try{
            String[] split = blackList.split(",");
            if (split.length>0){
                ConcurrentSet<String> blackSet = new ConcurrentSet<>();
                blackSet.addAll(Lists.newArrayList(split));
                BLACK_LIST = blackSet;
            }
        }catch (Exception e){
            log.error("setDiscountPriceBlackList_blackList={}", blackList, e);
        }
        log.info("doSetDiscountPriceBlackList_blackList={},BLACK_LIST={}", blackList, JSON.toJSONString(BLACK_LIST));
    }

    private synchronized boolean getIsInBlackList(HttpServletRequest request, MerchantBussinessDto merchantBussinessDto){
        if (null == request){
            return false;
        }
        String userRealIp = IPUtils.getClientIP(request);
        boolean result = StringUtils.isNotBlank(userRealIp) && BLACK_LIST.contains(userRealIp);
        if (result){
            log.info("getIsInBlackList_in_userRealIp={}", userRealIp);
        }
        return result;
    }


    private JSONObject emptyListResponse(){
        JSONObject result = new JSONObject();
        result.put("code", 1000);
        result.put("data",  new JSONArray());
        result.put("status", "success");
        return result;
    }

    private JSONObject emptySingleObjectResponse(){
        JSONObject result = new JSONObject();
        result.put("code", 1000);
        result.put("data", new JSONObject());
        result.put("status", "success");
        return result;
    }

    @ResponseBody
    @RequestMapping(value = "satisfactoryInHandPrice", method = RequestMethod.POST)
    public JSONObject satisfactoryInHandPrice(@RequestParam(value = "merchantId", required = false) Long merchantId, String skuIds, HttpServletRequest request){
        MerchantBussinessDto merchantBussinessDto = null;
        try {
            merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            log.info("satisfactoryInHandPrice_merchantId={},skuIds={}", merchantId, skuIds, e);
        }
        if (null == merchantBussinessDto){
            return emptyListResponse();
        }

        if (null == merchantId){
            String s = request.getHeader("merchantId");
            log.info("satisfactoryInHandPrice_heads_s={}", s);
            if (NumberUtils.isNumber(s)){
                merchantId = NumberUtils.createLong(s);
            }
        }
        if (null == merchantId || merchantId <= 0 || !Objects.equals(merchantBussinessDto.getId(), merchantId)){
            log.info("有越权风险_merchantBussinessDto.getId()={},merchantId={}", merchantBussinessDto.getId(), merchantId);
            return emptyListResponse();
        }
        if (getIsInBlackList(request, merchantBussinessDto)){
            return emptyListResponse();
        }

        List<DiscountPrice> discountPrices = Lists.newArrayList();
        List<Long> skuIdList = null;
        try{
            skuIdList= JSONObject.parseArray("[" + skuIds + "]", Long.class);
        }catch (Exception e){
            log.error("satisfactoryInHandPrice_入参解析失败_skuIds={}", skuIds, e);
        }

        if (CollectionUtils.isNotEmpty(skuIdList)){
            long startTime = System.currentTimeMillis();
            if (log.isDebugEnabled()) {
                log.debug("satisfactoryInHandPrice_skuIdList={}", skuIdList.size());
            }
            int counter = 0;
            for (List<Long> part : Lists.partition(skuIdList, 20)){
                counter ++;
                if ( (System.currentTimeMillis() - startTime)  > MAX_CALCULATE_TIME){
                    log.info("satisfactoryInHandPrice_计算未完成_skuIdList={},counter={}", skuIdList.size(), counter);
                    break;
                }
                try{
                    MerchantBussinessDto merchant = merchantService.getMerchant(merchantId);
                    if (null == merchant){
                        return emptyListResponse();
                    }
                    DiscountPriceRequest discountPriceRequest = new DiscountPriceRequest();
                    discountPriceRequest.setBranchCode(merchant.getRegisterCode());
                    discountPriceRequest.setKa(merchant.getIsKa());
                    discountPriceRequest.setMerchantId(merchantId);
                    discountPriceRequest.setSkuIdList(part);
                    discountPrices.addAll(estimationDiscountPriceApi.mgetDiscountPrice(discountPriceRequest));
                }catch (Exception e){
                    log.error("satisfactoryInHandPrice_merchantId={},skuIds={}", merchantId, skuIds, e);
                }
            }
        }

        JSONArray data = new JSONArray();
        if (CollectionUtils.isNotEmpty(discountPrices)){
            for (DiscountPrice price : discountPrices){
                if (null == price || null == price.getFob() || null == price.getPrice() || price.getFob().compareTo(price.getPrice()) <= 0){
                    if (log.isDebugEnabled()) {
                        log.debug("satisfactoryInHandPrice_不展示_price={}", JSON.toJSONString(price));
                    }
                    continue;
                }
                if(price.getFob().compareTo(price.getPrice()) < 0){
                    if (log.isDebugEnabled()) {
                        log.debug("satisfactoryInHandPrice_不展示_原价小于到手价_price={}", JSON.toJSONString(price));
                    }
                    continue;
                }
                JSONObject item = new JSONObject();
                item.put("skuId", price.getSkuId());
                item.put("price", "折后约￥" + price.getPrice().toString());
                data.add(item);
            }
        }
        JSONObject result = new JSONObject();
        result.put("code", 1000);
        result.put("data", data);
        result.put("status", "success");
        return result;
    }


    @ResponseBody
    @RequestMapping(value = "satisfactoryInHandPriceWithDetail", method = RequestMethod.POST)
    public JSONObject satisfactoryInHandPriceWithDetail(@RequestParam(value = "merchantId", required = false) Long merchantId, @RequestParam("skuId")Long skuId, HttpServletRequest request) {
        MerchantBussinessDto merchantBussinessDto = null;
        try {
            merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            log.info("satisfactoryInHandPrice_merchantId={},skuIds={}", merchantId, skuId, e);
        }
        if (null == merchantBussinessDto){
            return emptySingleObjectResponse();
        }

        if (null == merchantId){
            String s = request.getHeader("merchantId");
            log.info("satisfactoryInHandPriceWithDetail_heads_s={}", s);
            if (NumberUtils.isNumber(s)){
                merchantId = NumberUtils.createLong(s);
            }
        }
        if (null == merchantId || merchantId<=0 || !Objects.equals(merchantBussinessDto.getId(), merchantId)){
            log.info("有越权风险_merchantBussinessDto.getId()={},merchantId={}", merchantBussinessDto.getId(), merchantId);
            return emptySingleObjectResponse();
        }
        if (getIsInBlackList(request, merchantBussinessDto)){
            return emptySingleObjectResponse();
        }
        DiscountPrice price = null;
        try {
            MerchantBussinessDto merchant = merchantService.getMerchant(merchantId);
            if (null == merchant){
                return emptySingleObjectResponse();
            }
            DiscountPriceRequest discountPriceRequest = new DiscountPriceRequest();
            discountPriceRequest.setBranchCode(merchant.getRegisterCode());
            discountPriceRequest.setKa(merchant.getIsKa());
            discountPriceRequest.setMerchantId(merchantId);
            discountPriceRequest.setSkuIdList(Lists.newArrayList(skuId));
            price = estimationDiscountPriceApi.getDiscountPrice(discountPriceRequest);
        }catch (Exception e){
            log.info("satisfactoryInHandPriceWithDetail_merchantId={},skuId={}", merchantId, skuId, e);
        }
        JSONObject data = new JSONObject();
        if (null != price && null != price.getFob() && null != price.getPrice() && price.getFob().compareTo(price.getPrice())>0) {
            boolean tipNotice = false;
            data.put("announcement", "折后价是系统根据当前商品的促销活动及可领、可用优惠券，预估出的较优买入量及对应可享的优惠后单价，最终实付价格以结算页为准。");
            data.put("skuId", price.getSkuId());
            data.put("fob", "¥" + price.getFob().toString());
            data.put("price", "¥" + price.getPrice().toString());
            data.put("buyQty", price.getBuyQty());
            data.put("productUnit", price.getProductUnit());
            if (null != price.getCouDanAmount() && price.getCouDanAmount().compareTo(BigDecimal.ZERO)>0){
                data.put("priceDetail", String.format("预估买入%d盒，再凑单%s元，可享折后价约¥%s", price.getBuyQty(), price.getCouDanAmount().toString(), price.getPrice().toString()));
                data.put("priceDetailPrefix", String.format("预估买入%d盒，再凑单%s元，可享折后价约", price.getBuyQty(), price.getCouDanAmount().toString()));
            }else{
                data.put("priceDetail", String.format("预估买入%d盒，可享折后价约¥%s", price.getBuyQty(), price.getPrice().toString()));
                data.put("priceDetailPrefix", String.format("预估买入%d盒，可享折后价约", price.getBuyQty()));
            }
            JSONArray discountGroup = new JSONArray();
            if (CollectionUtils.isNotEmpty(price.getDiscountGroups())){
                for (DiscountPriceDetailGroup group : price.getDiscountGroups()){
                    JSONObject dg = new JSONObject();
                    dg.put("title", group.getTitle());
                    dg.put("discountPrice", "-¥" + group.getDiscountPrice().toString());
                    JSONArray discountDetails = new JSONArray();
                    if (CollectionUtils.isNotEmpty(group.getDetails())){
                        for (DiscountPriceDetail detail : group.getDetails()){
                            JSONObject detailJson = new JSONObject();
                            detailJson.put("typeTitle", detail.getTitle());
                            detailJson.put("discountDesc", detail.getDiscountDesc());
                            detailJson.put("discountPrice", "-¥" +detail.getDiscountPrice());
                            if (null != detail.getStatus() && detail.getStatus().equals(1)){
                                tipNotice = true;
                            }
                            detailJson.put("status", detail.getStatus());
                            discountDetails.add(detailJson);
                        }
                    }else{
                        continue;
                    }
                    dg.put("discountDetails", discountDetails);
                    discountGroup.add(dg);
                }
            }
            data.put("discountGroup", discountGroup);
            data.put("notice", tipNotice ? "购买时，记得先领券哦" : "");
        }else {
            log.info("satisfactoryInHandPriceWithDetail_不展示_price={}", JSON.toJSONString(price));
        }
        JSONObject result = new JSONObject();
        result.put("code", 1000);
        result.put("data", data);
        result.put("status", "success");
        return result;
    }

}
