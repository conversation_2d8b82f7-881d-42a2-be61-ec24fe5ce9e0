package com.xyy.ec.api.busi.shop;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/18 19:08
 * @Description:
 */
@Data
public class ACTReq implements Serializable {
    private Long actId;//活动id
    @NotNull
    private String shopCode;//店铺code
    private Integer modelType = 1;
    private Integer hasStock = 0;
    private Integer totalSalesVolumeType = 1;
    private Integer tagType = 3;
    private Integer offset = 0;
    private Integer limit = 100;
    private String branchCode;
    private Long userId;
}
