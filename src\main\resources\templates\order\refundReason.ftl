<!DOCTYPE HTML>
<html>
	<head>
		<#include "/common/common.ftl" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<title>提交退款原因</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
		<link rel="stylesheet" href="/static/css/user.css?t=${t_v}" />
        <link rel="stylesheet" href="/static/css/my-reorder-reason.css?t=${t_v}"/>
        <script src="/static/js/vue.js?t=${t_v}"></script>
		<script type="text/javascript" src="/static/js/uploadPreview.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/order/refundReason.js?t=${t_v}"></script>
	</head>

	<body>
   	 <div class="container">

        <!--头部导航区域开始-->
        <div class="headerBox" id="headerBox">
				<#include "/common/header.ftl" />
        </div>
        <!--头部导航区域结束-->

        <!--主体部分开始-->
        <div class="main">

            <div class="myreordereason row">
                <!--面包屑-->
                <ul class="sui-breadcrumb">
                    <li><a href="${ctx}/">首页</a>></li>
                    <li><a href="${ctx }/merchant/center/index.htm">用户中心</a>></li>
                    <li><a href="${ctx }/merchant/center/order/index.htm">我的订单</a>></li>
                    <li><a href="javascript:history.go(-1)">申请退款</a>></li>
                    <li class="active">提交退款原因</li>
                </ul>
                <div class="myreordereason-content clear">
                    <div class="side-left fl">
							<#include "/common/merchantCenter/left.ftl" />
                    </div>

                    <div class="main-right fr">
                        <!--未填写信息的提示-->
                        <div class="tips" style="display: none;">
                            请填写相关信息!
                        </div>

                        <!--提示信息-->
                        <div class="sui-msg msg-large msg-tips">
<#--                            <div class="msg-con">温馨提示：1.如退款商品包含赠品，请将赠品同退款商品一并寄回；2.客服会在第一时间与您联系确认退货事宜，给您带来的不便，敬请谅解，药帮忙竭诚为您服务！-->
<#--                            </div>-->
                            <div class="msg-con">
                                <p style="color: red">温馨提示:</p>
                                <p style="color: red">1、如需退回实物，请与实际申请退货商品、数量保持一致；</p>
                                <p style="color: red">2、如退款商品包含赠品，请将赠品一并寄回；</p>
                                <p style="color: red">3、若订单为纸质发票，请将发票随退货一并寄回；</p>
                                <p style="color: red">4、邮寄药品注意打包，若药品影响二次销售则无法正常办退；</p>
                            </div>
                        </div>

                        <div class="about-money">
                            <div>
                                <p>
                                    退款金额：<span>（不含活动优惠总额）</span>
                                </p>

                                <p class="red">¥${money}</p>
                            </div>
                            <#if (refundBalance?? && refundBalance>0)>
                                <div>
                                    <p>
                                        退回余额：<span>（退款成功可退余额）</span>
                                    </p>

                                    <p class="red">¥${refundBalance}</p>
                                </div>
                            </#if>
                            <#if (refundVirtualGold?? && refundVirtualGold>0)>
                                <div>
                                    <p>
                                        退回购物金：<span>（退款成功可退购物金）</span>
                                    </p>

                                    <p class="red">¥${refundVirtualGold}</p>
                                </div>
                            </#if>
                        </div>

                        <form class="sui-form form-horizontal" id="subApplyRefund"
                              action="${ctx }/merchant/center/order/submitApplyRefund.htm" method="post"
                              enctype="multipart/form-data">
                            <input type="hidden" id="orderId" name="orderId" value="${orderId }"/>
                            <input type="hidden" name="orderNo" value="${orderNo }"/>
                            <input type="hidden" name="refundType" value="${refundType }"/>
                            <input type="hidden" name="str" value='${str }'/>
                            <input type="hidden" name="afterSalesType" value="${defaultRefundType!''}" id="afterSalesTypeInput"/>
                            <input type="hidden" id="myreordereason-amount-num" value="${money}"/>
                            <input type="hidden" id="invoice-type" value="${invoiceType}"/>
<#--                            <#if payType=="3">-->
<#--                                <div class="control-group">-->
<#--                                    <label class="control-label"><b style="color: #f00;">*</b>开户行及支行：</label>-->
<#--                                    <div class="controls">-->
<#--                                        <input type="text" name="bankName" class="input-large bank-msg" value="${orderRefundBankBusinessDto.bankName}" maxlength="30" required>-->
<#--                                </div>-->
<#--                            </div>-->
<#--                                <div class="control-group">-->
<#--                                    <label class="control-label"><b style="color: #f00;">*</b>银行卡号：</label>-->
<#--                                    <div class="controls">-->
<#--                                        <input type="number" name="bankCard" class="input-large" value="<#if orderRefundBankBusinessDto.bankCard??>${orderRefundBankBusinessDto.bankCard}</#if>" required>-->
<#--&lt;#&ndash;                                        <input type="number" name="bankCard" class="card-num" value="<#if orderRefundBankBusinessDto.bankCardArr??>${orderRefundBankBusinessDto.bankCardArr[1]}</#if>" required>&ndash;&gt;-->
<#--&lt;#&ndash;                                        <input type="number" name="bankCard" class="card-num" value="<#if orderRefundBankBusinessDto.bankCardArr??>${orderRefundBankBusinessDto.bankCardArr[2]}</#if>" required>&ndash;&gt;-->
<#--&lt;#&ndash;                                        <input type="number" name="bankCard" class="card-num" value="<#if orderRefundBankBusinessDto.bankCardArr??>${orderRefundBankBusinessDto.bankCardArr[3]}</#if>" required>&ndash;&gt;-->
<#--                                    </div>-->
<#--                            </div>-->
<#--                                <div class="control-group">-->
<#--                                    <label class="control-label"><b style="color: #f00;">*</b>开户人：</label>-->
<#--                                    <div class="controls">-->
<#--                                        <input name="owner" type="text" value="<#if orderRefundBankBusinessDto.owner??>${orderRefundBankBusinessDto.owner}<#else>${contactor}</#if>" class="input-large holder" maxlength="10"-->
<#--                                               required>-->
<#--                                    </div>-->
<#--                                </div>-->
<#--                                <div class="control-group">-->
<#--                                    <label class="control-label"><b style="color: #f00;">*</b>联系电话：</label>-->
<#--                                    <div class="controls">-->
<#--                                        <input name="cellphone" type="tel" value="<#if orderRefundBankBusinessDto.cellphone??>${orderRefundBankBusinessDto.cellphone}<#else>${mobile}</#if>"-->
<#--                                               class="input-large holder-phone"  minlength="5" maxlength="13" required>-->
<#--                                    </div>-->
<#--                                </div>-->
<#--                            </#if>-->
                            <div class="control-group">
                                <label class="control-label after-type">
                                    <span style="color: red;">*</span> 售后类型：
                                </label>
                                <div class="controls" style="width: 300px">
                                    <select 
                                        <#if isRefundTypeEditable?? && !isRefundTypeEditable>disabled</#if> 
                                        onchange="handleRefundTypeChange(this.value)" 
                                        style="width: 309px;height: 26px;"
                                        id="afterSalesType">
                                        <#if afterSalesType??>
                                            <option value="" selected disabled>请选择售后类型</option>
                                            <#list afterSalesType as refundTypeItem>
                                                <option value="${refundTypeItem}" 
                                                    <#if refundTypeItem == defaultRefundType>selected="selected"</#if>
                                                >${refundTypeItem}</option>
                                            </#list>
                                            <#else>
                                            <option value="${defaultRefundType}" selected="selected">${defaultRefundType}</option>
                                        </#if>
                                    </select>
                                </div>
                            </div>
                            <div class="control-group">
                                <label class="control-label v-top">
                                    <span style="color: red;">*</span> 退款原因：
                                </label>
                                <div class="controls" style="width: 300px">
<#--                                    <select class="select" name="refundReason">-->
<#--                                        <#list refundReasonList as refundReason>-->
<#--                                            <option value="${refundReason.showText}" >${refundReason.showText}</option>-->
<#--                                        </#list>-->
<#--                                    </select>-->
                                    <#if refundReason??>
                                        <input id="freightRefund" class="refundReason" type="text" name="refundReason" autocomplete="off" readonly="readonly" value="${refundReason}">
                                        <div class="downTag"></div>
                                    <#else>
                                        <input id="freightRefund" class="refundReason" type="text" name="refundReason" autocomplete="off" readonly="readonly">
                                        <div class="downTag"></div>
                                        <div class="refundReasonOption">
                                            <ul>
                                                <#list refundReasonList as refundReason>
                                                    <#if  (refundReason.children?size > 0) >
                                                        <li class="reasonLi"
                                                        value="${refundReason.showText}"
                                                        data-refundType="${refundReason.refundType!''}"
                                                        data-uploadPlusTips='${refundReason.uploadPlusTipsJSON!""}'>${refundReason.showText}
                                                    </#if>
                                                    <#if  (refundReason.children?size < 1) >
                                                        <li class="onlyOne"
                                                        value="${refundReason.showText}"
                                                        data-refundType="${refundReason.refundType!''}"
                                                        data-uploadPlusTips='${refundReason.uploadPlusTipsJSON!""}'>${refundReason.showText}
                                                    </#if>
                                                        <#if (refundReason.children?size > 0)>
                                                            <ul>
                                                                <#list refundReason.children as item>
                                                                    <li class="childReasonLi"
                                                                        value="${refundReason.showText}-${item.showText}"
                                                                        data-refundType="${item.refundType!''}"
                                                                        data-uploadPlusTips='${item.uploadPlusTipsJSON!""}'
                                                                        >${item.showText}</li>
                                                                </#list>
                                                            </ul>
                                                        </#if>
                                                    </li>
                                                </#list>
                                            </ul>
                                        </div>
                                    </#if>
                                </div>
                            </div>

                            <div class="control-group" id="positionLabel">
                                <label class="control-label refundTips">退款说明：</label>
                                <div class="controls">
                                    <textarea rows="3" class="input-block-level textarea"
                                              name="refundExplain"></textarea>
                                </div>
                            </div>

                            <div class="control-group" id="otherVoucher">
                                <label class="control-label" style="vertical-align: text-top">上传凭证：</label>
                                <input type="hidden" id="imgList" name="imgList" value="">
                                <div class="controls myreordereasonphoto">
                                    <div class="myreordereasonphoto-item noadd">
                                        <input class="addphoto-btn" type="file" name="evidence" value=""
                                               id="upload_evidence1">
                                        <img class="default-photo" src="/static/img/user/defaultwish.png">
                                        <span class="del-btn">x</span>
                                    </div>
                                    <div class="myreordereasonphoto-item noadd">
                                        <input class="addphoto-btn" type="file" name="evidence" value=""
                                               id="upload_evidence2">
                                        <img class="default-photo" src="/static/img/user/defaultwish.png">
                                        <span class="del-btn">x</span>
                                    </div>
                                    <div class="myreordereasonphoto-item noadd">
                                        <input class="addphoto-btn" type="file" name="evidence" value=""
                                               id="upload_evidence3">
                                        <img class="default-photo" src="/static/img/user/defaultwish.png">
                                        <span class="del-btn">x</span>
                                    </div>
                                    <div class="myreordereasonphoto-item noadd">
                                        <input class="addphoto-btn" type="file" name="evidence" value=""
                                               id="upload_evidence4">
                                        <img class="default-photo" src="/static/img/user/defaultwish.png">
                                        <span class="del-btn">x</span>
                                    </div>
                                    <div class="myreordereasonphoto-item noadd">
                                        <input class="addphoto-btn" type="file" name="evidence" value=""
                                               id="upload_evidence5">
                                        <img class="default-photo" src="/static/img/user/defaultwish.png">
                                        <span class="del-btn">x</span>
                                    </div>
                                    <div class="myreordereasonphoto-item noadd">
                                        <input class="addphoto-btn" type="file" name="evidence" value=""
                                               id="upload_evidence6">
                                        <img class="default-photo" src="/static/img/user/defaultwish.png">
                                        <span class="del-btn">x</span>
                                    </div>
                                    <div class="myreordereasonphoto-item noadd">
                                        <input class="addphoto-btn" type="file" name="evidence" value=""
                                               id="upload_evidence7">
                                        <img class="default-photo" src="/static/img/user/defaultwish.png">
                                        <span class="del-btn">x</span>
                                    </div>
                                    <div class="myreordereasonphoto-item noadd">
                                        <input class="addphoto-btn" type="file" name="evidence" value=""
                                               id="upload_evidence8">
                                        <img class="default-photo" src="/static/img/user/defaultwish.png">
                                        <span class="del-btn">x</span>
                                    </div>
                                    <div class="myreordereasonphoto-item noadd">
                                        <input class="addphoto-btn" type="file" name="evidence" value=""
                                               id="upload_evidence9">
                                        <img class="default-photo" src="/static/img/user/defaultwish.png">
                                        <span class="del-btn">x</span>
                                    </div>
                                </div>
                            </div>
                            <div style="color: red; margin-left: 36px;" class="refundReasonTip"></div>
                            <div class="btn-center">
                                <a href="javascript:;" class="sui-btn btn-primary btn-xlarge btn_style"
                                   id="btn">提交审核</a>
                            </div>
                        </form>
                        <div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <!--主体部分结束-->

        <!--底部导航区域开始-->
        <div class="footer" id="footer">
				<#include "/common/footer.ftl" />
        </div>
        <!--底部导航区域结束-->
    </div>
	</body>
</html>