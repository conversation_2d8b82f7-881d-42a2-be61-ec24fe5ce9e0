<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description"
          content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>${shopInfo.showName!"药帮忙"}</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link href="/favicon.ico" rel="shortcut icon">
    <link rel="stylesheet" type="text/css" href="/static/css/sui.min.css"/>
    <link rel="stylesheet" type="text/css" href="/static/css/sui-append.min.css"/>
    <link rel="stylesheet" type="text/css" href="/static/css/reset.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/headerAndFooter.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/common.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/lib.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/shop/shop.css?t=${t_v}"/>
    <link rel="stylesheet" href="/static/css/search.css?t=${t_v}" />

    <script type="text/javascript" src="/static/js/jquery-1.11.3.min.js"></script>
    <script type="text/javascript" src="/static/js/sui.min.js"></script>
    <script type="text/javascript" src="/static/js/common.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/plugins/layer/layer.js"></script>
    <script type="text/javascript" src="/static/js/lib.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/util.js?t=${t_v}"></script>
    <script src="/static/js/plugins/jquery.md5.js" type="text/javascript"></script>
    <script type="text/javascript" src="/static/js/sku_search.js?t=${t_v}"></script>

    <script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/toast.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/zhuge/zhugeio.js?t=${t_v}"></script>
    <script type="text/javascript">
        function searchByTagType(type) {
            var url = '${pager.requestUrl}';
            // 重置
            url = url.replace("tagType=1", "");
            url = url.replace("tagType=2", "");
            if (url.indexOf(".htm?") > 0) {
                if (url.substring(url.length - 1, url.length) == '&') {
                    url = url.substring(0, url.length - 1);
                }
                url += "&tagType=" + type;
            } else {
                url += "?tagType=" + type;
            }
            window.location.href = encodeURI(url);
        }

        function searchByModelType(type) {
            var url = '${pager.requestUrl}';
            // 重置
            url = url.replace("modelType=1", "");
            url = url.replace("modelType=2", "");
            if (url.indexOf(".htm?") > 0) {
                if (url.substring(url.length - 1, url.length) == '&') {
                    url = url.substring(0, url.length - 1);
                }
                url += "&modelType=" + type;
            } else {
                url += "?modelType=" + type;
            }
            window.location.href = encodeURI(url);
        }

        function searchByTotalSalesVolume(totalSalesVolumeType) {
            var url = '${pager.requestUrl}';
            // 重置
            url = url.replace("totalSalesVolumeType=1", "");
            url = url.replace("totalSalesVolumeType=2", "");
            if (url.indexOf(".htm?") > 0) {
                if (url.substring(url.length - 1, url.length) == '&') {
                    url = url.substring(0, url.length - 1);
                }
                url += "&totalSalesVolumeType=" + totalSalesVolumeType;
            } else {
                url += "?totalSalesVolumeType=" + totalSalesVolumeType;
            }
            window.location.href = encodeURI(url);
        }

        function searchByHasStock() {
            var url = '${pager.requestUrl}';
            // 重置
            url = url.replace("hasStock=1", "");
            var value = null;
            var obj = document.getElementsByName('hasStock');
            for (var i = 0; i < obj.length; i++) {
                if (obj[i].checked) {
                    value = 1;
                }
            }
            if (value != null) {
                if (url.indexOf(".htm?") > 0) {
                    if (url.substring(url.length - 1, url.length) == '&') {
                        url = url.substring(0, url.length - 1);
                    }
                    url += "&hasStock=" + value;
                } else {
                    url += "?hasStock=" + value;
                }
            }
            window.location.href = encodeURI(url);
        }

    </script>
    <style>
        .main{
            background: #ffffff;
            margin-top: 20px;
        }
        .ss-title a {
            width: 105px;
        }
        .stock{
            margin-left: 10px;
        }
    </style>
</head>
<div class="container">
    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
        <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->

    <!--店铺对用户不可见-->
    <#if (isVisible)>
    <!--主体部分开始-->
    <div class="main">
        <input type="hidden" id="shopCode" value="${shopCode}"/>
        <input type="hidden" id="modelType" value="${modelType}"/>
        <input type="hidden" id="hasStock" value="${hasStock}"/>
        <input type="hidden" id="totalSalesVolumeType" value="${totalSalesVolumeType}"/>
        <input type="hidden" id="offset" value="${offset}"/>
        <input type="hidden" id="limit" value="${limit}"/>
        <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>

        <!--店铺信息-->
        <div class="default">
            <div class="head-box">
                <div class="head-l-box fl">
                    <#if shopInfo.pcLogoUrl??  && shopInfo.pcLogoUrl != "">
                        <a href="#" class="shop-logo"><img src='${shopInfo.pcLogoUrl!""}' alt='${shopInfo.showName!"药帮忙"}'></a>
                    </#if>
                    <div class="shop-des-box">
                        <p>${shopInfo.showName}</p>
                        <#if shopInfo.shopPropertyCode == "self">
                            <span class="grn">${shopInfo.shopPropertyName}</span>
                        </#if>
                        <#if shopInfo.shopTags??  && shopInfo.shopTags != "">
                            <#list shopInfo.shopTags?split(",") as shopTag>
                                <span class="blu">${shopTag}</span>
                            </#list>
                        </#if>
                    </div>
                </div>
                <div class="head-r-box fr" style="position: relative;">
                    <a id="J_set" href="javascript:void(0)" data-placement="bottom" data-toggle="tooltip" data-trigger="hover" data-original-title="<p style=&quot;font-size:16px;text-align:center;margin:15px auto 50px;&quot;>请复制链接完成分享吧</p><div><input id=&quot;spUrl&quot; value=&quot;&quot; type=&quot;text&quot;><span class=&quot;btn&quot;>复制链接</span></div>">
                        <span class="grn">
                            <img src="/static/images/share-icon.png" alt="">分享店铺
                        </span>
                    </a>
                </div>
            </div>
        </div>
        <!--导航-->
        <div class="head-bar">
            <ul>
                <li class='tab-bar active'>
                    <a href="/shop/${shopCode}.htm" class="item">首页</a>
                </li>
                <li class='tab-bar shangpin-tab'>
                    <a href="/shop/productList.htm?shopCode=${shopCode}&categoryCode=${firstCategory}" class="item">所有商品</a>
                    <div class="shaixuan-box">
                        <ul>
                            <#list categorys as category>
                                <li class="${((category_index+1) % 3 == 0)?string("three-n" , "")}">
                                    <div class="shaixuan-yiji">
                                        <a href="/shop/productList.htm?shopCode=${shopCode}&categoryCode=${category.categoryCode}">${category.categoryName}</a>
                                        <i class="sui-icon icon-tb-right"></i>
                                    </div>
                                    <div class="shaixuan-erji">
                                        <#list category.subCategorys as subCategory>
                                        <#--                                    <a href="/shop/productList.htm?shopCode=${shopCode}&categoryCode=${subCategory.categoryCode}" class="${(subCategory.categoryCode == categoryCode)?string("active" , "")}">${subCategory.categoryName}</a>-->
                                            <a href="/shop/productList.htm?shopCode=${shopCode}&categoryCode=${subCategory.categoryCode}">${subCategory.categoryName}</a>
                                        </#list>
                                    </div>
                                </li>
                            <#if ((category_index+1)?number) % 3 ==0><li style="clear:both;"></li></#if>
                            </#list>
                        </ul>
                    </div>
                </li>
                 <#if (hasAct??)>
                    <li class='tab-bar'>
                        <a href="/actshop/shop_activity.htm?shopCode=${shopCode}" class="item">活动</a>
                    </li>
                 </#if>
                <#if (hasShopPackageActivity??)>
                    <li class='tab-bar'>
                        <a href="/actshop/shop/activityPackage.htm?shopCode=${shopCode}" class="item">套餐</a>
                    </li>
                </#if>
            </ul>
        </div>
        <#if !(tagType?? && tagType == 1)>
            <!--轮播-->
            <#if (shopSlideshowImages?? && shopSlideshowImages?size>0)>
                <div class="hdpbox ">
                    <!--幻灯片-->
                    <div class="l-box">
                        <div id="myCarousel2" class="sui-carousel slide">
                            <ol class="carousel-indicators">
                                <#list shopSlideshowImages as item>
                                    <#if (item_index==0)>
                                        <li data-target="#myCarousel2" data-slide-to="0" class="active"></li>
                                    <#else>
                                        <li data-target="#myCarousel2" data-slide-to="${item_index}"></li>
                                    </#if>
                                </#list>
                            </ol>
                            <div class="carousel-inner">
                                <#list shopSlideshowImages as item>
                                    <#if (item_index==0)>
                                        <div class="active item">
                                            <#if item.jumpurl?default("")?trim?length gt 1>
                                                <a href="${item.jumpurl}">
                                                    <img src="${item.url}">
                                                </a>
                                            <#else>
                                                <img src="${item.url}">
                                            </#if>
                                        </div>
                                    <#else>
                                        <div class="item">
                                            <#if item.jumpurl?default("")?trim?length gt 1>
                                                <a href="${item.jumpurl}">
                                                    <img src="${item.url}">
                                                </a>
                                            <#else>
                                                <img src="${item.url}">
                                            </#if>
                                        </div>
                                    </#if>
                                </#list>
                            </div>
                            <div class="jiantou">
                                <a href="#myCarousel2" data-slide="prev" class="carousel-control left">‹</a>
                                <a href="#myCarousel2" data-slide="next" class="carousel-control right">›</a>
                            </div>
                        </div>
                    </div>
                </div>
            </#if>
        </#if>
        <!--内容-->
        <div class="content-box">
            <#if !(tagType?? && tagType == 1)>
                <#if (shopFloorImages?? && shopFloorImages?size>0)>
                    <div class="banner-box">
                        <#list shopFloorImages as item>
                            <#if (item_index==0)>
                                <div class="active item">
                                    <#if item.jumpurl?default("")?trim?length gt 1>
                                        <a href="${item.jumpurl}">
                                            <img src="${item.url}">
                                        </a>
                                    <#else>
                                        <img src="${item.url}">
                                    </#if>
                                </div>
                            <#else>
                                <div class="item">
                                    <#if item.jumpurl?default("")?trim?length gt 1>
                                        <a href="${item.jumpurl}">
                                            <img src="${item.url}">
                                        </a>
                                    <#else>
                                        <img src="${item.url}">
                                    </#if>
                                </div>
                            </#if>
                        </#list>
                    </div>
                </#if>
            </#if>
            <!--搜索结果-->
            <div class="ss-title clearfix">
                <span class="jieguo">搜索结果：</span>

                <#if totalSalesVolumeType == 1>
                <a href="javascript:void(0);" class="paixu xiaoliang con-down" onclick="searchByTotalSalesVolume(2)">总销量<img src="/static/images/s-up.png" class="s-up"><img src="/static/images/s-down.png" class="s-down"></a>
                <#elseif totalSalesVolumeType == 2>
                <a href="javascript:void(0);" class="paixu xiaoliang con-up" onclick="searchByTotalSalesVolume(1)">总销量<img src="/static/images/s-up.png" class="s-up"><img src="/static/images/s-down.png" class="s-down"></a>
                <#else>
                <a href="javascript:void(0);" class="paixu xiaoliang" onclick="searchByTotalSalesVolume(1)">总销量<img src="/static/images/s-default.png" class="s-default"></a>
                </#if>

                <#if hasStock == 1>
                <label class="checkbox-pretty inline stock checked">
                    <input type="checkbox" name="hasStock" onclick="searchByHasStock()" checked="checked"><span>只看有货</span>
                </label>
                <#else >
                <label class="checkbox-pretty inline stock">
                    <input type="checkbox" name="hasStock" onclick="searchByHasStock()"><span>只看有货</span>
                </label>
                </#if>
                <#if (modelType ==2)>
                    <a href="javascript:void(0);" onclick="searchByModelType(2)" class="lbms fr lbfn active"><i class="sui-icon icon-th-list"></i>查看列表模式</a>
                    <a href="javascript:void(0);" onclick="searchByModelType(1)" class="lbms fr dtfn"><i class="sui-icon icon-th-large"></i>查看大图模式</a>
                <#else>
                    <a href="javascript:void(0);" onclick="searchByModelType(2)" class="lbms fr lbfn"><i class="sui-icon icon-th-list"></i>查看列表模式</a>
                    <a href="javascript:void(0);" onclick="searchByModelType(1)" class="lbms fr dtfn active"><i class="sui-icon icon-th-large"></i>查看大图模式</a>
                </#if>
            <#--<a href="javascript:void(0);" onclick="searchByModelType(2)" class="lbms fr lbfn"><i class="sui-icon icon-th-list"></i>列表模式</a>-->
            <#--<a href="javascript:void(0);" onclick="searchByModelType(1)" class="lbms fr dtfn active"><i class="sui-icon icon-th-large"></i>大图模式</a>-->
            </div>

            <#if pager.rows??&&(pager.rows?size>0)>
                <!--大图模式-->
                <#if (modelType == 2)>
                    <ul class="mrth-new clearfix" style="display:none;">
                <#else>
                    <ul class="mrth-new clearfix" style="display:block;">
                </#if>
                <#list pager.rows as skuVO>
                    <#import "/common/skuVO.ftl" as pr>
                    <@pr.skuVO skuVO/>
                </#list>
                    </ul>
                <!--列表模式-->
                <#if (modelType ==2)>
                <div class="listmode" style="display: block;">
                <#else>
                <div class="listmode" style="display: none;">
                </#if>
                    <!--表体-->
                <#list pager.rows as skuVO>
                    <#if skuVO_index % 2 == 0>
                    <div class="bodybox">
                    <#else >
                        <div class="bodybox odd">
                    </#if>
                            <ul>
                                <li class="lib1">
                                    <div class="l-box fl">
                                        <a href="/search/skuDetail/${skuVO.id}.htm" target="_blank"
                                           title="${skuVO.showName}">
                                            <img id="lb_${skuVO.id}"
                                                 src="${productImageUrl}/ybm/product/min/${skuVO.imageUrl}" alt=""
                                                 onerror="this.src='/static/images/default-small.png'"/>
                                            <!--药狂欢角标 默认隐藏 去掉noshow显示-->
                                            <#if skuVO.markerUrl?? && skuVO.markerUrl!=''>
                                                <div class="yaokuanghuan-pos">
                                                    <img src="${productImageUrl}/${skuVO.markerUrl}" alt="">
                                                </div>
                                            </#if>
                                            <!--标签-->
                                            <div class="bq-box">
                                                <#if (skuVO.status == 1 && skuVO.availableQty == 0) || skuVO.status == 2 || ((skuVO.status == 3 || skuVO.status == 5) && skuVO.promotionTotalQty == 0 ) || (skuVO.isSplit == 0 && skuVO.availableQty - skuVO.mediumPackageNum lt 0)>
                                                    <img src="/static/images/product/bq-shouqing.png" alt="">
                                                </#if>
                                                <#if skuVO.status == 4>
                                                    <img src="/static/images/product/bq-xiajia.png" alt="">
                                                </#if>
                                            </div>
                                        </a>
                                    </div>
                                    <div class="r-box fl">
                                        <div class="lib1-row1 text-overflow">
											<span>
												<a href="/search/skuDetail/${skuVO.id}.htm" target="_blank"
                                                   title="${skuVO.showName}">
													<#if skuVO.isShow806 || skuVO.gift>
                                                        <div class="bq806">
														<img src="/static/images/bq806.png" alt="">
														</div>
                                                    </#if>
                                                    <#if skuVO.activityTag != null && skuVO.activityTag.tagUrl != ''>
                                                        <div class="bq806">
														<img src="${productImageUrl}/${skuVO.activityTag.tagUrl}"
                                                             alt="">
														</div>
                                                    </#if>

                                                    <#if skuVO.agent == 1>
                                                        <span class="dujia">独家</span>
                                                    </#if>
                                                    <#if skuVO.isUsableMedicalStr ?? && skuVO.isUsableMedicalStr == 1>
                                                        <span class="yibao">医保</span>
                                                    </#if>
                                                    <#if skuVO.nearEffectiveFlag ?? && skuVO.nearEffectiveFlag == 2>
                                                        <span class="linqi">近效期</span>
                                                    </#if>
                                                <#--<#if skuVO.gift>-->
                                                <#--<img src="/static/images/xiangli_bg.png" class="xiangli">-->
                                                <#--</#if>-->
                                                    ${skuVO.showName}
                                                </a>
											</span>
                                        </div>
                                        <div class="row-biaoqian">
                                            <#if (merchant ? exists)>
                                                <#list skuVO.tagList as item >
                                                    <#if item_index < 3>
                                                        <#if (item.uiType == 4)>
                                                            <span class="default">${item.name}</span>
                                                        </#if>
                                                        <#if item.uiType == 1>
                                                            <span class="linqi">${item.name}</span>
                                                        </#if>
                                                        <#if item.uiType == 2>
                                                            <span class="quan">${item.name}</span>
                                                        </#if>
                                                        <#if (item.uiType == 3)>
                                                            <span class="manjian">${item.name}</span>
                                                        </#if>
                                                        <#if (item.uiType == 5)>
                                                            <span class="yibao">${item.name}</span>
                                                        </#if>
                                                    </#if>
                                                </#list>
                                            </#if>
                                        </div>
                                        <div class="row-last">
                                            <#if (merchant ? exists)>
                                            <#--                                                <#if skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0'>-->

                                                <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
                                                <#--                                                    <span class="noPermission">含税价认证资质后可见</span>-->
                                                <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
                                                <#--                                                    <span class="noPermission">含税价认证资质后可见</span>-->
                                                <#elseif skuVO.isControl ==1 >
                                                    <#if skuVO.isPurchase>
                                                        <#if (skuVO.uniformPrice ?? ) && (skuVO.uniformPrice != '')>
                                                            <div class="kongxiao-box">
                                                                <span class="s-kx">控销价</span><span
                                                                    class="jg">￥${skuVO.uniformPrice}</span>
                                                            </div>
                                                        </#if>
                                                        <#if (skuVO.suggestPrice ?? ) && (skuVO.suggestPrice != '')>
                                                            <div class="kongxiao-box">
                                                                <span class="s-kx">零售价</span><span
                                                                    class="jg">￥${skuVO.suggestPrice}</span>
                                                            </div>
                                                        </#if>
                                                        <#if (skuVO.grossMargin ??) && (skuVO.grossMargin != '')>
                                                            <div class="maoli-box">
                                                                <span class="s-ml">毛利</span><span
                                                                    class="jg">${skuVO.grossMargin}</span>
                                                            </div>
                                                        </#if>
                                                    </#if>
                                                <#else>
                                                    <#if (skuVO.uniformPrice ?? ) && (skuVO.uniformPrice != '')>
                                                        <div class="kongxiao-box">
                                                            <span class="s-kx">控销价</span><span
                                                                class="jg">￥${skuVO.uniformPrice}</span>
                                                        </div>
                                                    </#if>
                                                    <#if (skuVO.suggestPrice ?? ) && (skuVO.suggestPrice != '')>
                                                        <div class="kongxiao-box">
                                                            <span class="s-kx">零售价</span><span
                                                                class="jg">￥${skuVO.suggestPrice}</span>
                                                        </div>
                                                    </#if>
                                                    <#if (skuVO.grossMargin ??) && (skuVO.grossMargin != '')>
                                                        <div class="maoli-box">
                                                            <span class="s-ml">毛利</span><span
                                                                class="jg">${skuVO.grossMargin}</span>
                                                        </div>
                                                    </#if>
                                                </#if>
                                            </#if>
                                        </div>
                                        <div class="lib1-row2 text-overflow">
                                            <span class="title">规　　格：</span>
                                            <span class="info">${skuVO.spec}</span>
                                        </div>
                                        <div class="lib1-row4 text-overflow">
                                            <span class="title">生产厂家：</span>
                                            <span class="info">${skuVO.manufacturer}</span>
                                        </div>
                                    </div>
                                </li>
                                <li class="lib2">
                                    <#if merchant ?exists>
                                    <#--                                        <#if skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0'>-->
                                    <#--                                                <div class="noright">-->
                                    <#--                                                    <div>价格签署协议可见</div>-->
                                    <#--                                                &lt;#&ndash;<div class="two-row">原价：暂无购买权限</div>&ndash;&gt;-->
                                    <#--                                                </div>-->
                                    <#--                                        <#elseif skuVO.showAgree =='0'>-->
                                    <#--                                                <div class="noright">-->
                                    <#--                                                    <div>价格签署协议可见</div>-->
                                    <#--                                                &lt;#&ndash;<div class="two-row">原价：暂无购买权限</div>&ndash;&gt;-->
                                    <#--                                                </div>-->
                                    <#--										<#elseif skuVO.isControl ==1 >-->
                                        <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
                                            <div class="noright">
                                                <div>含税价认证资质后可见</div>
                                            </div>
                                        <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
                                            <div class="noright">
                                                <div>含税价认证资质后可见</div>
                                            </div>
                                        <#elseif skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0'>
                                            <div class="noright">
                                                <div>价格签署协议可见</div>
                                            </div>
                                        <#elseif skuVO.showAgree =='0'>
                                            <div class="noright">
                                                <div>价格签署协议可见</div>
                                            </div>
                                        <#elseif skuVO.isControl ==1 >
                                            <#if skuVO.isPurchase>
                                                <div class="normal ">
                                                    <div class="newpricebox">
                                                        <#if skuVO.priceType==1>
                                                            <span class="newprice">￥${skuVO.fob}</span>
                                                            <span class="zhehou-price zhehou-price-${skuVO.id}"></span>
                                                        <#else >
                                                            <#if skuVO.skuPriceRangeList ??>
                                                                <span class="newprice">￥
																<#list skuVO.skuPriceRangeList  as priceReange>
                                                                    <#if priceReange_index==0>
                                                                        ${priceReange.price}
                                                                    </#if>
                                                                    <#if !priceReange_has_next>
                                                                        -${priceReange.price}
                                                                    </#if>
                                                                </#list>
										                        </span>
                                                            </#if>
                                                        </#if>
                                                        <#if skuVO.promotionTag?? && skuVO.promotionTag!=''>
                                                            <span class="xiangou">
													            	<img src="/static/images/xgsanjiao.png"
                                                                         class="sanjiao">
                                                                ${skuVO.promotionTag }
												            	</span>
                                                        </#if>
                                                    </div>
                                                    <div class="oldpricebox">
                                                        <span class="oldprice-tit">原价：</span>
                                                        <span class="oldprice">￥${skuVO.retailPrice}</span>
                                                    </div>
                                                    <!--不参与返点提示-->
                                                    <#if (skuVO.blackProductText)!>
                                                        <div class="nofd">
                                                            ${skuVO.blackProductText}
                                                        </div>
                                                    </#if>
                                                    <div class="endtimebox">
                                                        <#if (skuVO.promotionEndTime?exists)>
                                                            <span>促销截止时间：</span>
                                                            <span>${skuVO.promotionEndTime?string("yyyy-MM-dd")}</span>
                                                        </#if>
                                                    </div>
                                                </div>
                                            <#else>
                                                <div class="noright">
                                                    <div>暂无购买权限</div>
                                                <#--<div class="two-row">原价：暂无购买权限</div>-->
                                                </div>
                                            </#if>
                                        <#else>
                                            <div class="normal ">
                                                <div class="newpricebox">
                                                    <#if skuVO.priceType==1 >
                                                        <span class="newprice">￥${skuVO.fob}</span>
                                                        <span class="zhehou-price zhehou-price-${skuVO.id}"></span>
                                                    <#else >
                                                        <#if skuVO.skuPriceRangeList ??>
                                                            <span class="newprice">￥
																	<#list skuVO.skuPriceRangeList  as priceReange>
                                                                        <#if priceReange_index==0>
                                                                            ${priceReange.price}
                                                                        </#if>
                                                                        <#if !priceReange_has_next>
                                                                            -${priceReange.price}
                                                                        </#if>
                                                                    </#list>
											                      </span>
                                                        </#if>
                                                    </#if>
                                                    <#if skuVO.promotionTag?? && skuVO.promotionTag!=''>
                                                        <span class="xiangou">
												                <img src="/static/images/xgsanjiao.png" class="sanjiao">
                                                            ${skuVO.promotionTag }
												            </span>
                                                    </#if>
                                                </div>
                                                <div class="oldpricebox">
                                                    <span class="oldprice-tit">原价：</span>
                                                    <span class="oldprice">￥${skuVO.retailPrice}</span>
                                                </div>
                                                <!--不参与返点提示-->
                                                <#if skuVO.FdType != 4>
                                                    <div class="nofd">
                                                        ${skuVO.FdType}
                                                    </div>
                                                </#if>
                                            <#--<div class="endtimebox">-->
                                            <#--<span>限时促销倒计时：</span><span>02:23:25</span>-->
                                            <#--</div>-->
                                                <div class="endtimebox">
                                                    <#if (skuVO.promotionEndTime?exists)>
                                                        <span>促销截止时间：</span>
                                                        <span>${skuVO.promotionEndTime?string("yyyy-MM-dd")}</span>
                                                    </#if>
                                                </div>
                                            </div>
                                        </#if>
                                    <#else>
                                        <div class="login-show">
                                            <div>价格登录可见</div>
                                        <#--<div class="two-row">原价：价格登录可见</div>-->
                                        </div>
                                    </#if>
                                </li>
                                <li class="lib_zbz">
                                    <!--中包装-->
                                    <div class="zbz-listbox">
                                        <div class="zbz-listbox-top">${skuVO.mediumPackageTitle}
                                            <#if skuVO.isSplit == 0>
                                                <span class="chailin">${skuVO.isSplitTitle}</span>
                                            </#if>
                                        </div>
                                        <div class="kucun"><span>库存：</span>
                                            <span>
												<#if skuVO.isSplit == 0 && skuVO.availableQty - skuVO.mediumPackageNum lt 0>
                                                    0
                                                <#else >
                                                    <#if (skuVO.availableQty > 100)>
                                                        大于100
                                                    <#else >
                                                        ${skuVO.availableQty}
                                                    </#if>
                                                </#if>
                                            </span>
                                        </div>
                                    </div>

                                </li>
                                <li class="lib5">
                                    <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
                                        <div class="verifyBox">
                                            <a href="/merchant/center/license/findLicenseCategoryInfo.htm">资质认证</a>
                                        </div>
                                    <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
                                        <div class="verifyBox">
                                            <a href="/merchant/center/license/findLicenseCategoryInfo.htm">资质审核中</a>
                                        </div>
                                    <#else >
                                        <div class="suliang">
                                            <a href="javascript:void(0);" class="sub fl">-</a>
                                            <input class="fl" type="text" value="${skuVO.cartProductNum}"
                                                   id="buyNumlb_${skuVO.id}" name="buyNumlb" isSplit="${skuVO.isSplit}"
                                                   middpacking="${skuVO.mediumPackageNum}"
                                                   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                                   onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"/>
                                            <a href="javascript:void(0);" class="add fl">+</a>
                                        </div>
                                        <a href="javascript:void(0);" class="addbuy fl"
                                           onclick="addCartLb(${skuVO.id},${skuVO.mediumPackageNum},${skuVO.isSplit},event)"
                                           id="href_LB_${skuVO.id}">加入采购单</a>
                                        <#if skuVO.favoriteStatus == 1>
                                            <div class="w-collectZone_list hasCollect initial j-collectBtn fl"
                                                 id="${skuVO.id}" onclick="rmCollectionLb(${skuVO.id},event,this)">
                                                <div class="zone-1">
                                                    <div class="top top-1">
                                                        <span class="w-icon-normal icon-normal-collectEpt"></span>
                                                    </div>
                                                    <div class="top top-2">
                                                        <span class="w-icon-normal icon-normal-collectFull"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        <#else>
                                            <div class="w-collectZone_list nopCollect initial j-collectBtn fl"
                                                 id="${skuVO.id}" onclick="addCollectionLb(${skuVO.id},event,this)">
                                                <div class="zone-1">
                                                    <div class="top top-1">
                                                        <span class="w-icon-normal icon-normal-collectEpt"></span>
                                                    </div>
                                                    <div class="top top-2">
                                                        <span class="w-icon-normal icon-normal-collectFull"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </#if>
                                    </#if>
                                </li>
                            </ul>
                        </div>
                </#list>
                </div>

                <!--分页器-->
                <div class="page">
                    <#import "/common/pager.ftl" as p>
                    <@p.pager currentPage=pager.currentPage limit=pager.limit total=pager.total pageCount=pager.pageCount toURL=pager.requestUrl method="get"/>
                </div>
            <#else>
                <!--没有搜索到商品-->
                <div class="nosearchgood">
                    <div class="tpbox" style="text-align: center;margin-bottom: 30px;">
                        <img src="${ctx}/static/images/nosearchresult.png" alt="" style="margin-top: 62px;width:290px;height:200px;">
                        <p style="color:#999;margin-top:38px;font-size:18px;">暂无商品</p>
                    </div>
                </div>
            </#if>
        </div>
        </div>
        </div>
    <!--主体部分结束-->
    <#else>
        <!--店铺对用户不可见-->
        <div class="main">
            <div class="nosearchgood" style="margin-bottom: 150px;">
                <div class="tpbox" style="margin-top:20px;text-align: center;">
                    <img src="${ctx}/static/images/nosearchresult.png" alt="" style="margin-top: 62px;width:290px;height:200px;">
                    <p style="color:#999;margin-top:38px;font-size:18px;">控销店铺暂不支持自由浏览</p>
                </div>
            </div>
        </div>
    </#if>
    <!--底部导航区域开始-->
    <div class="footer" id="footer">
        <#include "/common/footer.ftl" />
    </div>
    <!--底部导航区域结束-->
</div>
</div>
</body>
<script type="text/javascript">
    <!--复制链接-->
    $("#J_set").bind('mouseover',function(){
        var str = '<p style="font-size:16px;text-align:left;margin:15px auto 50px;">请复制链接完成分享吧</p><div><input id="spUrl" value="${shopInfo.pcLink}" type="text"><span onClick="copyUrl()" class="btn">复制链接</span></div>'
        $(".head-r-box #J_set").attr("data-original-title",str);
    })
    function copyUrl(){
        spUrl.select();
        document.execCommand("Copy");
        $.toast({
            title: '复制成功'
        });
    }

    $(function () {
        var tagType = '${tagType}';
        if (!(tagType === '1')) {
            /*启动幻灯片播放*/
            $("#myCarousel2").carousel('cycle');
        }
    });
</script>
</html>


