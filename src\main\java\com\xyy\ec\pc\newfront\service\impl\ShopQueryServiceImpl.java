package com.xyy.ec.pc.newfront.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.client.api.query.MarketingTagQueryService;
import com.xyy.ec.marketing.client.api.query.resp.ShopActivitiesTagView;
import com.xyy.ec.marketing.client.common.enums.ShopPatternEnum;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.newfront.service.ShopQueryService;
import com.xyy.ec.pc.newfront.vo.ShotInfoVO;
import com.xyy.ec.pc.newfront.vo.ShotQueryParam;
import com.xyy.ec.pc.popshop.helper.CompanyHelper;
import com.xyy.ec.pc.popshop.rpc.PopCorporationServiceRpc;
import com.xyy.ec.pc.popshop.rpc.ProductApiRpc;
import com.xyy.ec.pc.popshop.service.PopShopService;
import com.xyy.ec.pc.popshop.vo.CompanyShopFloorVo;
import com.xyy.ec.pc.rpc.ShopServiceRpc;
import com.xyy.ec.pc.service.ShopConfuseService;
import com.xyy.ec.pop.server.api.external.app.dto.AppFloorDto;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.shop.server.business.api.ShopQueryApi;
import com.xyy.ec.shop.server.business.api.ShopTagForAppApi;
import com.xyy.ec.shop.server.business.params.ShopTagQueryParam;
import com.xyy.ec.shop.server.business.results.ShopActivityInfoDTO;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.ec.shop.server.business.results.ShopTagForAppDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShopQueryServiceImpl implements ShopQueryService {

    @Reference(version = "1.0.0", timeout = 500, check = false)
    private ShopQueryApi shopQueryApi;

    @Reference(version = "1.0.0")
    private MarketingTagQueryService marketingTagQueryService;

    @Reference(version = "1.0.0")
    private ShopTagForAppApi shopTagForAppApi;

    @Resource
    private PopCorporationServiceRpc popCorporationServiceRpc;

    @Resource
    private ShopServiceRpc shopServiceRpc;

    @Resource
    private ProductApiRpc productApiRpc;

    @Resource
    private CmsAppProperties cmsAppProperties;

    @Resource
    private PopShopService popShopService;

    @Resource
    private ShopConfuseService shopConfuseService;

    @Override
    public List<ShotInfoVO> queryShopActivityInfoByShopCodes(ShotQueryParam param, long merchantId) {

        List<String> shopCodes = param.getShopCodes();

        // 补齐店铺code
        shopCodes = padShopCodes(shopCodes, param.getPageSize() == null ? 6 : param.getPageSize());

        List<ShotInfoVO> shotVOS = new ArrayList<>();

        // 获取用户可见性店铺
        ApiRPCResult<List<String>> shopsResult = shopQueryApi.getShopCodesByBuyerCode(String.valueOf(merchantId), shopCodes);

        if (null == shopsResult || !shopsResult.isSuccess()) {
            throw new RuntimeException("查询可见店铺服务异常");
        }

        ApiRPCResult<List<ShopInfoDTO>> res = shopQueryApi.queryShopInfoFullByShopCodes(shopsResult.getData(), merchantId);
        if (null == res || !res.isSuccess()) {
            throw new RuntimeException("查询店铺服务异常");
        }

        Map<String, List<ShopActivitiesTagView>> tempMap = null;
        try {
            tempMap = marketingTagQueryService.mgetShopActivitiesTagsForShopList(merchantId, shopCodes);
        } catch (Exception e) {
            log.error("远程服务调用 MarketingTagQueryService.mgetShopActivitiesTagsForShopList 异常，参数：merchantId：{}，shopCodeList：{}，异常信息：", merchantId, JSONArray.toJSONString(shopCodes), e);
        }
        List<ShopInfoDTO> shopInfoDTOS = res.getData();
        // 创建shopCode到索引的映射
        Map<String, Integer> shopCodeToIndex = new HashMap<>();
        for (int i = 0; i < shopCodes.size(); i++) {
            shopCodeToIndex.put(shopCodes.get(i), i);
        }
        // 根据shopCodes的顺序对店铺信息进行排序
        shopInfoDTOS.sort(Comparator.comparingInt(o -> shopCodeToIndex.getOrDefault(o.getShopCode(), Integer.MAX_VALUE)));
        for (ShopInfoDTO dto : shopInfoDTOS) {
            ShotInfoVO vo = ShotInfoVO.builder()
                    .id(dto.getId())
                    .shopCode(dto.getShopCode())
                    .name(dto.getName())
                    .showName(dto.getShowName())
                    .pcLogoUrl(dto.getPcLogoUrl())
                    .appLogoUrl(dto.getAppLogoUrl())
                    .freightTips(dto.getFreightTips())
                    .orgId(dto.getOrgId())
                    .isThirdCompany(dto.getShopTagsDto() == null ? null : dto.getShopTagsDto().getIsThirdCompany())
                    .build();

            if (MapUtils.isNotEmpty(tempMap)) {
                List<ShopActivitiesTagView> shopActivitiesTagViews = tempMap.get(dto.getShopCode());
                if (CollectionUtils.isNotEmpty(shopActivitiesTagViews)) {
                    List<ShopActivityInfoDTO> shopActivityInfoDTOList = shopActivitiesTagViews.stream().map(ShopActivityInfoDTO::cloneShopActivity).collect(Collectors.toList());
                    vo.setActivityInfo(shopActivityInfoDTOList);
                }
            }
            shotVOS.add(vo);
        }
        return shotVOS;
    }


    /**
     * 根据 pageSize 补齐 shopCodes 个数
     *
     * @param shopCodes 需要补齐的 shopCodes 列表
     * @param pageSize  页面大小
     * @return 补齐后的 shopCodes 列表
     */
    public List<String> padShopCodes(List<String> shopCodes, int pageSize) {
        if (shopCodes.size() >= pageSize) {
            shopCodes = shopCodes.subList(0, pageSize);
            return shopCodes;
        }
        List<String> allShopCodes = new ArrayList<>(shopCodes);
        handleShopCode(pageSize, findShopList(pageSize), allShopCodes);
        return allShopCodes;
    }

    /**
     * 处理shopCode
     *
     * @param pageSize     shop数量
     * @param shopList     待处理shopCode
     * @param allShopCodes 目标
     */
    private void handleShopCode(int pageSize, List<String> shopList, List<String> allShopCodes) {
        if (CollUtil.isEmpty(shopList)) {
            return;
        }
        for (String shopCode : shopList) {
            if (!allShopCodes.contains(shopCode)) {
                allShopCodes.add(shopCode);
                if (allShopCodes.size() == pageSize) {
                    break;
                }
            }
        }
    }


    /**
     * 查询精品店铺列表
     *
     * @param pageSize 商户id
     * @return 店铺列表
     */
    public List<String> findShopList(Integer pageSize) {
        ShopTagQueryParam shopTagQueryParam = new ShopTagQueryParam();
        shopTagQueryParam.setQuality(1);
        shopTagQueryParam.setPageNum(1);
        shopTagQueryParam.setPageSize(pageSize);
        ApiRPCResult<PageInfo<ShopTagForAppDTO>> result = shopTagForAppApi.queryShopByShopTag(shopTagQueryParam);
        if (null == result || !result.isSuccess()) {
            throw new RuntimeException("查询店铺服务异常");
        }
        List<ShopTagForAppDTO> shopTagForAppDTOS = result.getData().getList();
        if (CollUtil.isNotEmpty(shopTagForAppDTOS)) {
            return shopTagForAppDTOS.stream().map(ShopTagForAppDTO::getShopCode).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


    public List<CompanyShopFloorVo> companyFloor(String orgId, Long merchantId, String branchCode) {
        if (BooleanUtils.isTrue(shopConfuseService.isCantShowShop(orgId))) {
            log.warn("PopShopServiceImpl.shopIndexView，Pop店铺首页数据，不显示店铺，orgId：{}，merchantId：{}，branchCode：{}", orgId, merchantId, branchCode);
            return Lists.newArrayList();
        }

        List<CompanyShopFloorVo> floorVOS = new ArrayList<>();
        // 请求楼层商品信息
        Future<List<AppFloorDto>> floorFuture = CompletableFuture.supplyAsync(() -> popCorporationServiceRpc.listFloor(orgId, branchCode));
        //拿到楼层数据后，获取楼层商品信息
        List<AppFloorDto> popFloorInfos = getPopFloorInfoByFuture(floorFuture);
        if (popFloorInfos == null) {
            return Lists.newArrayList();
        }
        //楼层商品返回结果集合
        List<AppFloorDto> floorInfos = new ArrayList<>();
        ShopInfoDTO shopInfoDTO = shopServiceRpc.queryShopByOrgId(orgId);
        boolean isVirtual = shopInfoDTO != null && ShopPatternEnum.VIRTUAL.getCode().equalsIgnoreCase(shopInfoDTO.getShopPatternCode());
        //组装查询拼团和特价
        popShopService.getActivityFloor(orgId, merchantId, floorInfos, isVirtual);

        floorInfos.addAll(popFloorInfos);

        List<Long> skuIds = CompanyHelper.getPopFloorSkuIds(floorInfos);
        Map<Long, ListProduct> skuVOMap = productApiRpc.findOnSaleProductMapBySkuIdList(skuIds, merchantId, branchCode, isVirtual);
        log.info("PopShopServiceImpl.shopIndexView, skuIds:{}, merchantId:{}, branchCode:{}, skuVOMap:{}",
                JSON.toJSONString(skuIds), merchantId, branchCode, JSON.toJSONString(skuVOMap));
        // 处方药商品默认图处理
        if (log.isDebugEnabled()) {
            log.debug("【处方药商品默认图处理】merchantId：{}，orgId：{}，原商品信息：{}", merchantId, orgId, JSONObject.toJSONString(skuVOMap));
        }
        if (BooleanUtils.isTrue(cmsAppProperties.getIsOpenIndexProductDefaultImageFeature())) {
            String defaultImageUrl = cmsAppProperties.getProductDefaultImageUrl();
            skuVOMap.values().stream().filter(item -> Objects.nonNull(item) && Objects.equals(item.getDrugClassification(), 3))
                    .forEach(item -> item.setImageUrl(defaultImageUrl));
            if (log.isDebugEnabled()) {
                log.debug("【处方药商品默认图处理】merchantId：{}，orgId：{}，处理后商品信息：{}", merchantId, orgId, JSONObject.toJSONString(skuVOMap));
            }
        }

        // 组装楼层商品信息
        popShopService.setFloorGoods(floorVOS, floorInfos, skuVOMap);
        log.info("PopShopServiceImpl.setFloorGoods-after参数# floorVOS:{}", JSON.toJSONString(floorVOS));
        if (CollectionUtils.isNotEmpty(floorVOS)) {
            popShopService.popGroup(merchantId, floorVOS);
        }

        floorVOS.forEach(floorVO -> {
            if (CollectionUtils.isNotEmpty(floorVO.getListProductVos())) {
                String alphanumeric = RandomStringUtils.randomAlphanumeric(8);
                floorVO.getListProductVos().forEach(product -> {
                    product.setScmId(alphanumeric);
                });
            }
        });
        return floorVOS;
    }


    private List<AppFloorDto> getPopFloorInfoByFuture(Future<List<AppFloorDto>> future) {
        try {
            return future.get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("pop商家楼层信息查询", e);
        }
        return null;
    }


}
