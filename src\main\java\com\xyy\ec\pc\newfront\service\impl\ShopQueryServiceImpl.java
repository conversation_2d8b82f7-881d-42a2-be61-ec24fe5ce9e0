package com.xyy.ec.pc.newfront.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.client.api.query.MarketingTagQueryService;
import com.xyy.ec.marketing.client.api.query.resp.ShopActivitiesTagView;
import com.xyy.ec.pc.newfront.service.ShopQueryService;
import com.xyy.ec.pc.newfront.vo.ShotInfoVO;
import com.xyy.ec.pc.newfront.vo.ShotQueryParam;
import com.xyy.ec.pc.remote.ShopQueryRemoteService;
import com.xyy.ec.pc.rpc.OrderServerRpcService;
import com.xyy.ec.pc.service.order.MatchPriceService;
import com.xyy.ec.shop.server.business.api.ShopQueryApi;
import com.xyy.ec.shop.server.business.api.ShopTagForAppApi;
import com.xyy.ec.shop.server.business.params.ShopTagQueryParam;
import com.xyy.ec.shop.server.business.results.ShopActivityInfoDTO;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.ec.shop.server.business.results.ShopTagForAppDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShopQueryServiceImpl implements ShopQueryService {

    @Reference(version = "1.0.0", timeout = 500, check = false)
    private ShopQueryApi shopQueryApi;

    @Reference(version = "1.0.0")
    private MarketingTagQueryService marketingTagQueryService;

    @Reference(version = "1.0.0")
    private ShopTagForAppApi shopTagForAppApi;


    @Resource
    private MatchPriceService matchPriceService;

    @Resource
    private ShopQueryRemoteService shopQueryRemoteService;

    @Resource
    private OrderServerRpcService orderServerRpcService;

    @Override
    public List<ShotInfoVO> queryShopActivityInfoByShopCodes(ShotQueryParam param, long merchantId) {

        List<String> shopCodes = param.getShopCodes();

        // 补齐店铺code
        shopCodes = padShopCodes(shopCodes, param.getPageSize() == null ? 6 : param.getPageSize());

        List<ShotInfoVO> shotVOS = new ArrayList<>();

        // 获取用户可见性店铺
        ApiRPCResult<List<String>> shopsResult = shopQueryApi.getShopCodesByBuyerCode(String.valueOf(merchantId), shopCodes);

        if (null == shopsResult || !shopsResult.isSuccess()) {
            throw new RuntimeException("查询可见店铺服务异常");
        }

        ApiRPCResult<List<ShopInfoDTO>> res = shopQueryApi.queryShopInfoFullByShopCodes(shopsResult.getData(), merchantId);
        if (null == res || !res.isSuccess()) {
            throw new RuntimeException("查询店铺服务异常");
        }

        Map<String, List<ShopActivitiesTagView>> tempMap = null;
        try {
            tempMap = marketingTagQueryService.mgetShopActivitiesTagsForShopList(merchantId, shopCodes);
        } catch (Exception e) {
            log.error("远程服务调用 MarketingTagQueryService.mgetShopActivitiesTagsForShopList 异常，参数：merchantId：{}，shopCodeList：{}，异常信息：", merchantId, JSONArray.toJSONString(shopCodes), e);
        }
        List<ShopInfoDTO> shopInfoDTOS = res.getData();
        // 创建shopCode到索引的映射
        Map<String, Integer> shopCodeToIndex = new HashMap<>();
        for (int i = 0; i < shopCodes.size(); i++) {
            shopCodeToIndex.put(shopCodes.get(i), i);
        }
        // 根据shopCodes的顺序对店铺信息进行排序
        shopInfoDTOS.sort(Comparator.comparingInt(o -> shopCodeToIndex.getOrDefault(o.getShopCode(), Integer.MAX_VALUE)));
        for (ShopInfoDTO dto : shopInfoDTOS) {
            ShotInfoVO vo = ShotInfoVO.builder()
                    .id(dto.getId())
                    .shopCode(dto.getShopCode())
                    .name(dto.getName())
                    .showName(dto.getShowName())
                    .pcLogoUrl(dto.getPcLogoUrl())
                    .appLogoUrl(dto.getAppLogoUrl())
                    .freightTips(dto.getFreightTips())
                    .orgId(dto.getOrgId())
                    .isThirdCompany(dto.getShopTagsDto() == null ? null : dto.getShopTagsDto().getIsThirdCompany())
                    .build();

            if (MapUtils.isNotEmpty(tempMap)) {
                List<ShopActivitiesTagView> shopActivitiesTagViews = tempMap.get(dto.getShopCode());
                if (CollectionUtils.isNotEmpty(shopActivitiesTagViews)) {
                    List<ShopActivityInfoDTO> shopActivityInfoDTOList = shopActivitiesTagViews.stream().map(ShopActivityInfoDTO::cloneShopActivity).collect(Collectors.toList());
                    vo.setActivityInfo(shopActivityInfoDTOList);
                }
            }
            shotVOS.add(vo);
        }
        return shotVOS;
    }


    /**
     * 根据 pageSize 补齐 shopCodes 个数
     *
     * @param shopCodes 需要补齐的 shopCodes 列表
     * @param pageSize  页面大小
     * @return 补齐后的 shopCodes 列表
     */
    public List<String> padShopCodes(List<String> shopCodes, int pageSize) {
        if (shopCodes.size() >= pageSize) {
            shopCodes = shopCodes.subList(0, pageSize);
            return shopCodes;
        }
        List<String> allShopCodes = new ArrayList<>(shopCodes);
        handleShopCode(pageSize, findShopList(pageSize), allShopCodes);
        return allShopCodes;
    }

    /**
     * 处理shopCode
     *
     * @param pageSize     shop数量
     * @param shopList     待处理shopCode
     * @param allShopCodes 目标
     */
    private void handleShopCode(int pageSize, List<String> shopList, List<String> allShopCodes) {
        if (CollUtil.isEmpty(shopList)) {
            return;
        }
        for (String shopCode : shopList) {
            if (!allShopCodes.contains(shopCode)) {
                allShopCodes.add(shopCode);
                if (allShopCodes.size() == pageSize) {
                    break;
                }
            }
        }
    }


    /**
     * 查询精品店铺列表
     *
     * @param pageSize 商户id
     * @return 店铺列表
     */
    public List<String> findShopList(Integer pageSize) {
        ShopTagQueryParam shopTagQueryParam = new ShopTagQueryParam();
        shopTagQueryParam.setQuality(1);
        shopTagQueryParam.setPageNum(1);
        shopTagQueryParam.setPageSize(pageSize);
        ApiRPCResult<PageInfo<ShopTagForAppDTO>> result = shopTagForAppApi.queryShopByShopTag(shopTagQueryParam);
        if (null == result || !result.isSuccess()) {
            throw new RuntimeException("查询店铺服务异常");
        }
        List<ShopTagForAppDTO> shopTagForAppDTOS = result.getData().getList();
        if (CollUtil.isNotEmpty(shopTagForAppDTOS)) {
            return shopTagForAppDTOS.stream().map(ShopTagForAppDTO::getShopCode).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }


}
