package com.xyy.ec.pc.base;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: WanKp
 * @Date: 2018/8/26 13:57
 **/
public class Page <T> implements Serializable {

    private final static ThreadLocal<Page> page = new ThreadLocal<Page>();

    // 总记录数
    private Long total;
    // 页码
    private Integer offset = 0;
    // 每页显示条数
    private Integer limit = 10;
    // 具体业务数据
    private List<T> rows;
    // 总页数
    private Integer pageCount = 0;

    /**
     * 当前页
     */
    private Integer currentPage = 0;

    private Map<String, Object> requestParameters;

    private String requestUrl;

    // 默认构造函数
    public Page() {
        page.set(this);
    }

    /**
     * 获取当前线程ThreadLocal变量绑定的pageBean
     */
    public static Page get() {
        return null != page.get() ? page.get() : new Page();
    }

    /**
     * 清除当前线程ThreadLocal变量
     */
    public static void clear() {
        page.remove();
    }

    public Page(Integer offset, Integer limit) {
        this.offset = offset;
        this.limit = limit;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
        this.pageCount = total.intValue() / limit
                + ((total.intValue() % limit == 0) ? 0 : 1);
        if (currentPage > pageCount)
            currentPage = pageCount;
        if (currentPage <= 0)
            currentPage = 1;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public List<T> getRows() {
        return rows;
    }

    public void setRows(List<T> rows) {
        this.rows = rows;
    }

    public Integer getPageCount() {
        return pageCount;
    }

    public void setPageCount(Integer pageCount) {
        this.pageCount = pageCount;

    }

    public Integer getCurrentPage() {
        if (offset <= 0) {
            return currentPage;
        } else {
            return offset;
        }

    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    /**
     * 是否可以到第一页
     *
     * @return
     */
    public boolean canGoFirst() {
        return (this.currentPage > 1);
    }

    public Map<String, Object> getRequestParameters() {
        return requestParameters;
    }

    public void setRequestParameters(Map<String, Object> requestParameters) {
        this.requestParameters = requestParameters;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

}
