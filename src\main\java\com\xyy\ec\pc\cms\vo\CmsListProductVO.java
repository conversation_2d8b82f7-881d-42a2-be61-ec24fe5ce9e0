package com.xyy.ec.pc.cms.vo;

import com.xyy.ec.pc.newfront.dto.ShopFreightDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingGroupBuyingActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingWholesaleActivityInfoDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.ecp.csufillattr.dto.LevelPriceDTO;
import com.xyy.track.dto.TrackDataEntity;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class CmsListProductVO extends ListProduct {

    /**
     * 商品店铺展示名称
     */
    private String shopName;

    /**
     * 店铺首页地址
     */
    private String shopUrl;

    /**
     * 商品店铺app首页地址，以后都用<code>shopUrl</code>
     */
    private String shopAppIndexUrl;

    /**
     * 商品店铺pc首页地址，以后都用<code>shopUrl</code>
     */
    private String shopPcIndexUrl;

    /**
     * 图片地址前缀
     */
    private String imageUrlPath;

    /**
     * 标签地址全路径
     */
    private String markerUrlPath;


    /**
     * 商品 折后价
     */
    private String price;

    /**
     * 店铺起送包邮信息
     */
    private ShopFreightDTO shopFreight;

    /**
     * 秒杀活动信息
     */
    private MarketingSeckillActivityInfoDTO actSk;

    /**
     * 拼团活动信息
     */
    private MarketingGroupBuyingActivityInfoDTO actPt;

    /**
     * 批购包邮活动信息
     */
    private MarketingWholesaleActivityInfoDTO actPgby;

    /**
     * 标签列表
     */
    private Map<String, Object> tags;
    /**
     * 阶梯价信息  没有阶梯价时为null
     */
    private LevelPriceDTO levelPriceDTO;

    /**
     * 埋点包
     */
    private TrackDataEntity trackData;

}
