/**
 * @(#)PasswordVerifier.java Copyright 2011 jointown, Inc. All rights reserved.
 */
package com.xyy.ec.pc.base;

/**
 * 密码认证校验器
 * 
 * 
 * 
 * @version 1.0,2011-2-17
 */
public class PasswordVerifier extends KeepLoginStatusVerifier {
    private String loginName;
    private String password;
	private String branchId;
	//判断是否强制走主库(默认不强制走主库)
    private boolean flag=true;
    /**
     * 设备类型
     */
    private String deviceId;

	public String getBranchId() {
		return branchId;
	}

	public void setBranchId(String branchId) {
		this.branchId = branchId;
	}
	
    public String getPassword() {
        return password;
    }

    public String getLoginName() {
        return loginName;
    }

    public PasswordVerifier(String loginName, String password){
        this(loginName, password, "1" , true);
    }

    public PasswordVerifier(String loginName, String password,String branchId){
        this(loginName, password, branchId, false);
    }

    public PasswordVerifier(String loginName, String password,String branchId,String deviceId){
        super(true, DEFAULT_KEEP_LOGIN_DAY);
        this.loginName = loginName;
        this.password = password;
        this.branchId = branchId;
        this.deviceId = deviceId;
    }

    public PasswordVerifier(String loginName, String password,String branchId, boolean keepLoginStatus){
        this(loginName, password,branchId, keepLoginStatus, DEFAULT_KEEP_LOGIN_DAY);
    }

    public PasswordVerifier(String loginName, String password, String branchId,boolean keepLoginStatus, int keepLoginDay){
        super(keepLoginStatus, keepLoginDay);
        this.loginName = loginName;
        this.password = password;
        this.branchId = branchId;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public boolean getFlag() {
        return flag;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
}
