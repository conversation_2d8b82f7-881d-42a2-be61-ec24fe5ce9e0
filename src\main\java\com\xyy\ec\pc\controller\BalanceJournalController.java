package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.bussiness.api.BalanceBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.BalanceBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.BalanceJournalBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderExtendBusinessApi;
import com.xyy.ec.order.business.common.ResultDTO;
import com.xyy.ec.order.business.dto.OrderBusinessDto;
import com.xyy.ec.order.business.dto.OrderExtendBusinessDto;
import com.xyy.ec.order.business.model.ServiceResponse;
import com.xyy.ec.order.core.dto.Order;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.constants.CodeItemConstants;
import com.xyy.ec.pc.constants.CodeMapConstants;
import com.xyy.ec.pc.enums.BalanceStatusEnum;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.system.business.dto.CodeitemBusinessDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * BalanceJournal控制层
 *
 * <AUTHOR>
 * @ClassName: BalanceJournalController
 * @date 2017-03-21 09:35:14
 */
@Controller
@RequestMapping("/merchant/center/balanceJournal")
public class BalanceJournalController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BalanceJournalController.class);


    @Reference(version = "1.0.0")
    private BalanceBussinessApi balanceBussinessApi;

    @Reference(version = "1.0.0")
    private OrderExtendBusinessApi orderExtendBusinessApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;

    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Value("${ka.business.type}")
    private String kaBusinessType;

    @Value("${ka.branch}")
    private String kaBranch;

    @Value("${ka.balance.switch}")
    private Boolean kaBalanceSwitch;
    /**
     * 金额明细
     *
     * @return
     */
    @RequestMapping(value = "/index.htm", method = RequestMethod.GET)
    public ModelAndView index(HttpServletRequest request) throws Exception {
        Map<String, Object> model = new HashMap<String, Object>();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            // 可用余额
            BalanceBussinessDto balancetmp = balanceBussinessApi.selectByMerchant(merchant.getId());
            double balancemoney = 0;
            double totalbalance = 0;
            if (balancetmp != null) {
                balancemoney = balancetmp.getBalance() == null ? 0 : balancetmp.getBalance().doubleValue();
                totalbalance = balancetmp.getTotalBalance() == null ? 0 : balancetmp.getTotalBalance().doubleValue();
            }
            model.put("balancemoney", balancemoney);
            // 累计余额
            model.put("totalbalance", totalbalance);
            Page page = new Page();
            page.setLimit(10);
            int offset;
            String offsetStr = request.getParameter("offset");
            int balanceType = 5;
            if (StringUtil.isEmpty(offsetStr)) {
                offset = 1;
            } else {
                if (offsetStr.equals("0")){
                   offset =1;
                }else {
                    offset = Integer.parseInt(offsetStr);
                }
            }
            page.setOffset(offset);
            int oldOffset = page.getOffset();
            //封装查询余额实体
            BalanceJournalBussinessDto balanceJournal = new BalanceJournalBussinessDto();
            balanceJournal.setMerchantId(merchant.getId());
            if (StringUtil.isNotEmpty(request.getParameter("balanceType"))) {
                balanceType = Integer.parseInt(request.getParameter("balanceType"));
            }
            //查询余额的类型（3：待领取余额 4：累计余额  5：可用余额）
            balanceJournal.setBalanceType(balanceType);
            model.put("balanceType", balanceType);
            String branchCode = null;
            Map<String, String> codeMap = null;
            try {
                branchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchant.getId());
                codeMap = codeItemServiceRpc.findCodeMap("BALANCE_INFO", branchCode);
            } catch (Exception e) {
                LOGGER.error("查询字典异常,e=",e);
            }
            Page pageBalanceJournalList = new Page<>();
            PageInfo pageInfo =null;
            if (kaBalanceSwitch && kaBusinessType.contains("," + merchant.getBusinessType() + ",") && kaBranch.contains(merchant.getRegisterCode())) {
                model.put("balancemoney", BigDecimal.ZERO);
                // 累计余额
                model.put("totalbalance", BigDecimal.ZERO);
                pageInfo=new PageInfo();
                pageInfo.setPageNum(page.getOffset());
                pageInfo.setPageSize(page.getLimit());
                Page<BalanceJournalBussinessDto> pageBalanceJournalLists = new Page<>();
                List<BalanceJournalBussinessDto> result = new ArrayList<>();
                String requestUrl = this.getRequestUrl(request);
                pageBalanceJournalLists.setRequestUrl(requestUrl);
                pageBalanceJournalLists.setOffset(oldOffset);
                pageBalanceJournalLists.setRows(result);
                pageBalanceJournalLists.setTotal(pageInfo.getTotal());
                pageBalanceJournalLists.setCurrentPage(pageInfo.getPageNum());
                pageBalanceJournalLists.setPageCount(pageInfo.getPages());
                pageBalanceJournalLists.setOffset(pageBalanceJournalList.getOffset());
                model.put("pager", pageBalanceJournalLists);
                model.put("merchant", merchant);

                model.put("isShowCash", 0);
                // 待返余额
                BigDecimal totalmoneyTmp = BigDecimal.ZERO;
                model.put("totalmoneyTmp", totalmoneyTmp);
                return new ModelAndView("/balanceJournal/index.ftl", model);
            }
            try {
                if(Objects.equals(balanceType,3)){
                    pageInfo=new PageInfo();
                    pageInfo.setPageNum(page.getOffset());
                    pageInfo.setPageSize(page.getLimit());
                    OrderExtendBusinessDto orderExtend=new OrderExtendBusinessDto();
                    orderExtend.setMerchantId(merchant.getId());
                    ServiceResponse<PageInfo<OrderExtendBusinessDto>> response = orderExtendBusinessApi.selectUnclaimedBalanceList(pageInfo, orderExtend);
                    String code = response.getCode();
                    if("10000".equals(code)){
                        pageInfo=response.getResult();
                    }
                }else{
                    pageInfo = balanceBussinessApi.selectBalanceJournalPageList(page.getOffset(), page.getLimit(), balanceJournal);
                }
            }catch (Exception e){
                LOGGER.error("查询待领取余额明细或者余额明细异常,e=",e);
            }
            setResultPage(pageBalanceJournalList, pageInfo);
            List balanceJournalList = pageBalanceJournalList.getRows();
            List<BalanceJournalBussinessDto> result = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(balanceJournalList)){
                for (Object map : balanceJournalList) {
                    String json = JSONObject.toJSONString(map);
                    BalanceJournalBussinessDto balanceJournaltmp = JSONObject.parseObject(json, BalanceJournalBussinessDto.class);
                    String cnt = balanceJournaltmp.getBalanceStatus().toString();
                    if (CollectionUtil.isNotEmpty(codeMap)) {
                        balanceJournaltmp.setStatusMemo(codeMap.get(cnt));
                    }
                    if (cnt.equals(String.valueOf(BalanceStatusEnum.PLATFORM.getId()))
                            && StringUtils.isNoneBlank(balanceJournaltmp.getRemark())) {
                        balanceJournaltmp.setStatusMemo(balanceJournaltmp.getRemark());
                    }
                    // 高毛 明星  厂商  618返利直接显示备注
                    if (balanceJournaltmp.getBalanceStatus().equals(BalanceStatusEnum.GAOMAOAGREEMENT.getId()) ||
                            balanceJournaltmp.getBalanceStatus().equals(BalanceStatusEnum.MINGXINGAGREEMENT.getId())
                            || balanceJournaltmp.getBalanceStatus().equals(BalanceStatusEnum.DUJIAAGREEMENT.getId())
                            || balanceJournaltmp.getBalanceStatus().equals(BalanceStatusEnum.SIXONEEIGHTFANLI.getId())
                            || balanceJournaltmp.getBalanceStatus().equals(BalanceStatusEnum.DAZHUANPAN.getId())) {
                        balanceJournaltmp.setStatusMemo(balanceJournaltmp.getRemark());
                    }
                    if (balanceJournaltmp.getBalanceStatus().equals(BalanceStatusEnum.NOREDUCTION.getId())){
                        balanceJournaltmp.setStatusMemo(BalanceStatusEnum.NOREDUCTION.getValue());
                    }
                    if (balanceJournaltmp.getBalanceStatus().equals(BalanceStatusEnum.SUBSIDY.getId())){
                        balanceJournaltmp.setStatusMemo(BalanceStatusEnum.SUBSIDY.getValue());
                    }
                    if (balanceJournaltmp.getBalanceStatus().equals(BalanceStatusEnum.REUNFDDELETE.getId())){
                        balanceJournaltmp.setStatusMemo(BalanceStatusEnum.REUNFDDELETE.getValue());
                    }
                    if (balanceJournaltmp.getBalanceStatus().equals(BalanceStatusEnum.REUNFDCLOSE.getId())){
                        balanceJournaltmp.setStatusMemo(BalanceStatusEnum.REUNFDCLOSE.getValue());
                    }
                    if (balanceJournaltmp.getBalanceStatus().equals(BalanceStatusEnum.GUANGFA.getId())){
                        balanceJournaltmp.setStatusMemo(BalanceStatusEnum.GUANGFA.getValue());
                    }
                    result.add(balanceJournaltmp);
                }
            }

            Page<BalanceJournalBussinessDto> pageBalanceJournalLists = new Page<>();
            String requestUrl = this.getRequestUrl(request);
            pageBalanceJournalLists.setRequestUrl(requestUrl);
            pageBalanceJournalLists.setOffset(oldOffset);
            pageBalanceJournalLists.setRows(result);
            pageBalanceJournalLists.setTotal(pageInfo.getTotal());
            pageBalanceJournalLists.setCurrentPage(pageInfo.getPageNum());
            pageBalanceJournalLists.setPageCount(pageInfo.getPages());
            pageBalanceJournalLists.setOffset(pageBalanceJournalList.getOffset());
            model.put("pager", pageBalanceJournalLists);
            model.put("merchant", merchant);

            //余额提现功能关闭
//            Map<String, CodeitemBusinessDto> codeItem = codeItemServiceRpc.selectByCodemapRTMap(CodeMapConstants.SHOW_CASH, merchant.getRegisterCode());
//            int isShowCash = getIsShowCash(branchCode, codeItem);
            model.put("isShowCash", 0);
            // 待返余额
            BigDecimal totalmoneyTmp = BigDecimal.ZERO;
            try {
                totalmoneyTmp = orderExtendBusinessApi.selectdunClaimedAmount(merchant.getId());
                totalmoneyTmp = totalmoneyTmp == null?BigDecimal.ZERO:totalmoneyTmp;
            }catch (Exception e){
                LOGGER.error("查询用户待领取余额异常,e",e);
                totalmoneyTmp = BigDecimal.ZERO;
            }
            model.put("totalmoneyTmp", totalmoneyTmp);
        } catch (Exception e) {
            LOGGER.error("PC查询余额明细接口异常,e=",e);
        }
        return new ModelAndView("/balanceJournal/index.ftl", model);
    }

    private int getIsShowCash(String branchCode, Map<String, CodeitemBusinessDto> codeItem) {
        int isShowCash = 0;
        if (codeItem != null && !codeItem.isEmpty()) {
            CodeitemBusinessDto codeitem = codeItem.get(CodeItemConstants.CASH_BRANCH_CODE);
            String branchCodes = codeitem.getName();
            if (StringUtil.isNotEmpty(branchCodes) && branchCodes.indexOf(branchCode) > -1) {
                isShowCash = 1;
            }
        }
        return isShowCash;
    }

    @RequestMapping("/helpBalance.html")
    public Object helpBalance(HttpServletRequest request)  {
        Map<String, Object> model = new HashMap<String, Object>();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            String branchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchant.getId());
            Map<String, CodeitemBusinessDto> codeItem = codeItemServiceRpc.selectByCodemapRTMap(CodeMapConstants.SHOW_CASH, merchant.getRegisterCode());
            int isShowCash = getIsShowCash(branchCode, codeItem);
            model.put("isShowCash", isShowCash);
            return new ModelAndView("/balanceJournal/helpBalance.ftl", model);
        }catch (Exception e){
            LOGGER.error("helpBalance接口异常,e=",e);
            return new ModelAndView("/balanceJournal/helpBalance.ftl", model);
        }
    }

    /**
     * BalanceJournal 领取余额
     *
     * @param orderId 添加对象
     * @return Object
     * @Title: add
     * <AUTHOR>
     * @date 2017-03-21 09:35:14
     */
    @RequestMapping("/addBalance.json")
    @ResponseBody
    public Object addBalance(Long orderId){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            LOGGER.info("addBalance.json,merchantId={},orderId={}", new Object[]{merchant.getId(), orderId});
            OrderBusinessDto orders = orderBusinessApi.selectById(orderId);
            Order order = new Order();
            order.setOrderNo(orders.getOrderNo());
            OrderExtendBusinessDto orderExtend = orderExtendBusinessApi.selectByOrderNo(orders.getOrderNo());
            if (orderExtend != null && Objects.equals(orderExtend.getBalanceStatus(), 0)) {
                BalanceJournalBussinessDto balanceJournaltmp = new BalanceJournalBussinessDto();
                balanceJournaltmp.setBalanceStatus(1);
                balanceJournaltmp.setOrderNo(order.getOrderNo());
                balanceJournaltmp.setOrderMoney(order.getTotalAmount());
                balanceJournaltmp.setMerchantId(orderExtend.getMerchantId());
                balanceJournaltmp.setBalanceJournal(orderExtend.getBalance());
                balanceJournaltmp.setTransactionSerialNumbers(order.getOrderNo());
                //判断订单是都还有待审核的退款单
                ResultDTO<Long> booleanResultDTO = orderBusinessApi.addBalanceJournal(balanceJournaltmp, true);
                Boolean state = booleanResultDTO.getIsSuccess();
                LOGGER.info("addBalance.json,merchantId={},orderId={},result={}", new Object[]{merchant.getId(), orderId, state});
                if (state != null && state) {
                    return this.addResult("BalanceJournal", orderExtend.getBalance());
                } else {
                    if(StringUtils.isNotEmpty(booleanResultDTO.getErrorMsg())){
                        return this.addError(booleanResultDTO.getErrorMsg());
                    }
                    return this.addError("余额领取失败！");
                }
            } else {
                return this.addError("没有余额可以领取！");
            }
        } catch (Exception e) {
            LOGGER.error("余额领取异常", e);
            return this.addError("余额领取失败，请稍后重试");
        }
    }

}

