<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description"
          content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>药帮忙官网-便宜买好药，当然药帮忙</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link href="/favicon.ico" rel="shortcut icon">
    <link rel="stylesheet" type="text/css" href="/static/css/sui.min.css"/>
    <link rel="stylesheet" type="text/css" href="/static/css/sui-append.min.css"/>
    <link rel="stylesheet" type="text/css" href="/static/css/reset.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/headerAndFooter.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/common.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/lib.css?t=${t_v}"/>
    <link rel="stylesheet" href="/static/css/index.css?t=${t_v}"/>

    <script type="text/javascript" src="/static/js/jquery-1.11.3.min.js"></script>
    <script type="text/javascript" src="/static/js/sui.min.js"></script>
    <script type="text/javascript" src="/static/js/common.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/plugins/layer/layer.js"></script>
    <script type="text/javascript" src="/static/js/lib.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/util.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/qt.js?t=${t_v}"></script>
    <script src="/static/js/plugins/jquery.md5.js" type="text/javascript"></script>
    <script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/indexV2.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/zhuge/zhugeio.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>

    <style>
    .sui-modal .modal-body{
            max-height: none !important;
        }

        .bannerbox2.con-618 {
            margin-top: 15px;
        }

        .bannerbox2.con-618 a {
            width: 100%;
            max-width: 100%;
        }

        .bannerbox2.con-618 a img {
            width: 100%;
            max-width: 100%;
            height: auto;
        }

        .bannerbox2.con-618 a .money_lev img {
            width: 20px;
        }

        .jd_con {
            position: absolute;
            left: 2px;
            top: 6px;
            z-index: 1;
            height: 16px;
            width: 20%;
            max-width: 99.5%;
            border-radius: 7px;
            background: -webkit-linear-gradient(90deg, #ff8d00, #ffe800); /* Safari 5.1 - 6.0 */
            background: -o-linear-gradient(90deg, #ff8d00, #ffe800); /* Opera 11.1 - 12.0 */
            background: -moz-linear-gradient(90deg, #ff8d00, #ffe800); /* Firefox 3.6 - 15 */
            background: linear-gradient(90deg, #ff8d00, #ffe800); /* 标准的语法 */
        }

        .jd_con.ie_css {
            background: #ff8d00;
        }

        .money_lev {

        }

        .money_lev img {
            vertical-align: middle;
        }

        .money_lev span {
            color: #fc2137;
            font-size: 25px;
            font-weight: 600;
            vertical-align: middle;
        }

        /*.money_lev::after {*/
        /*    display: block;*/
        /*    content: '';*/
        /*    width: 0;*/
        /*    height: 0;*/
        /*    z-index: -1;*/
        /*    left: 44%;*/
        /*    top: 28px;*/
        /*    border: 16px solid #e02a11;*/
        /*    border-radius: 5px;*/
        /*    border-right-width: 0px;*/
        /*    border-bottom-width: 0px;*/
        /*    position: absolute;*/
        /*    transform: rotate(45deg);*/
        /*}*/

        .money_lev_after {
            display: block;
            content: '';
            width: 0;
            height: 0;
            z-index: -1;
            left: 44%;
            top: 28px;
            border: 16px solid #e02a11;
            border-radius: 5px;
            border-right-width: 0px;
            border-bottom-width: 0px;
            position: absolute;
            transform: rotate(45deg);
        }

        /*.money_lev_r::after{*/
        /*    display: block;*/
        /*    content: '';*/
        /*    width: 0;*/
        /*    height: 0;*/
        /*    z-index: -1;*/
        /*    left: 44%;*/
        /*    top: 28px;*/
        /*    border: 16px solid #df5602;*/
        /*    border-radius: 5px;*/
        /*    border-right-width: 0px;*/
        /*    border-bottom-width: 0px;*/
        /*    position: absolute;*/
        /*    transform: rotate(45deg);*/
        /*}*/

        .money_lev_r_after {
            display: block;
            content: '';
            width: 0;
            height: 0;
            z-index: -1;
            left: 44%;
            top: 28px;
            border: 16px solid #df5602;
            border-radius: 5px;
            border-right-width: 0px;
            border-bottom-width: 0px;
            position: absolute;
            transform: rotate(45deg);
        }

        .remark_con {
            width: 60%;
            max-width: 60%;
            height: 36px;
            margin-left: 0%;
        }

        .remark_con div {
            line-height: 36px;
            border-radius: 20px;
            font-size: 20px;
            font-weight: 600;
            background: -webkit-linear-gradient(90deg, #ff1a18, #ff8900); /* Safari 5.1 - 6.0 */
            background: -o-linear-gradient(90deg, #ff1a18, #ff8900); /* Opera 11.1 - 12.0 */
            background: -moz-linear-gradient(90deg, #ff1a18, #ff8900); /* Firefox 3.6 - 15 */
            background: linear-gradient(90deg, #ff1a18, #ff8900); /* 标准的语法 */
        }

        .remark_con div.iecss {
            background: #ff8900;
        }

        /*.remark_con::before {*/
        /*    display: block;*/
        /*    content: '';*/
        /*    width: 0;*/
        /*    height: 0;*/
        /*    z-index: -1;*/
        /*    left: 10%;*/
        /*    top: 58px;*/
        /*    border: 16px solid #ff1d18;*/
        /*    border-radius: 5px;*/
        /*    border-right-width: 0px;*/
        /*    border-bottom-width: 0px;*/
        /*    position: absolute;*/
        /*    transform: rotate(45deg);*/
        /*}*/


        .remark_con_before {
            display: block;
            content: '';
            width: 0;
            height: 0;
            z-index: -1;
            left: 10%;
            top: 58px;
            border: 16px solid #ff1d18;
            border-radius: 5px;
            border-right-width: 0px;
            border-bottom-width: 0px;
            position: absolute;
            transform: rotate(45deg);
        }


        .remark_con_before.remark_con_r {
            border: 16px solid #ff7c03;
        }

        .remark_1 {
            color: #fff;
        }

        .remark_2 {
            color: #ffe800;
        }

        .hd_618 a, .hd_618 a img {
            width: 100%;
            height: auto;
        }
        .site-gray, .site-gray *{
            filter: gray !important;
            filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
            filter: grayscale(100%);
            -webkit-filter: grayscale(100%);
            -moz-filter: grayscale(100%);
            -ms-filter: grayscale(100%);
            -o-filter: grayscale(100%);
        }
    </style>

    <!--[if lte IE 9]>

    <style>

        .remark_con div {
            background: #ff4d0d !important;
        }

        .jd_con {
            background: #ffbb00 !important;
        }
    </style>

    <![endif]-->
    <!--推送弹框css-->
    <style>
        .push-modal.sui-modal{
            width:500px;

            background:none;
            box-shadow: none;
                margin-left: 0 !important;
              transform: translateX(-50%) !important;
        }
        .push-modal .modal-body{
            padding:0;
            border-radius: 4px 4px 0 0;
        }

        .push-modal .modal-footer{
            position:relative;
            padding:0;
            height:53px;
            box-shadow:none;
        }
        .push-modal .modal-footer i.del-icon{
            display:inline-block;
            background:url('../static/images/modal-del.png') 0 0 no-repeat;
            width:38px;
            height:38px;
            position:absolute;
            left:50%;
            margin-left:-19px;
            top:15px;
            cursor:pointer;
        }

        .push-modal .modal-body .normal-push{
            box-sizing: border-box;
            padding:10px 10px 20px;
            background:none;
            position:relative;
        }

        .push-modal .modal-body .normal-push i{
            position:absolute;
            display:none;
            width:64px;
            height:20px;
            left:50%;
            margin-left:-32px;
            cursor:pointer;
        }

        .push-modal .modal-body .normal-push i.top-icon{
            background:url('../static/images/modal-top-icon.png') 0 0;
            top:10px;
        }

        .push-modal .modal-body .normal-push i.bottom-icon{
            background:url('../static/images/modal-bottom-icon.png') 0 0;
            bottom:20px;
        }

        .push-modal .modal-body .normal-push .push-box{


        }

        .push-modal .modal-body .normal-push .push-box::-webkit-scrollbar {
            width: 5px;
            height: 5px;
        }
        /* 滚动槽 */
        .push-modal .modal-body .normal-push .push-box::-webkit-scrollbar-track {
            box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }
        /* 滚动条滑块 */
        .push-modal .modal-body .normal-push .push-box::-webkit-scrollbar-thumb {
            border-radius: 5px;
            background: #bbb;
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
        }
        ::-webkit-scrollbar-thumb:window-inactive {
            background: #ccc;
        }

        .push-modal .modal-body .normal-push .push-box a{
            width:100%;
            display:block;
        }

        .push-modal .modal-body .normal-push .push-box a.last{
            margin-bottom:0;
        }

        .push-modal .modal-body .normal-push .push-box a img{

            vertical-align: bottom;
            height:50vh
        }

        .push-modal .modal-body .coupon-push{
            background:#FF4244;
        }

        .push-modal .modal-body .coupon-push .ad-box{
            height:150px;
        }

        .push-modal .modal-body .coupon-push .ad-box a{
            width:100%;
            height:100%;
        }

        .push-modal .modal-body .coupon-push .ad-box a img{
            width:100%;
            height:100%;
        }

        .push-modal .modal-body .coupon-push .coupon-box{
            box-sizing: border-box;
            padding:10px 10px 15px;
        }

        .push-modal .modal-body .coupon-push .coupon-box li{
            margin-bottom:10px;
            height:100px;
            overflow:hidden;
            border-radius: 4px;
            background:url('../static/images/coupon-bg.png') 0 0 no-repeat;
        }

        .push-modal .modal-body .coupon-push .coupon-box li.last{
            margin-bottom:0;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div{
            float:left;
            height:100%;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div.left-box{
            width:138px;
            padding:25px 0;
            box-sizing: border-box;
        }
        .push-modal .modal-body .coupon-push .coupon-box li > div.left-box p{
            text-align: center;
            color:#FF2121;
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            margin:5px 0;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div.left-box p span{
            font-size:40px;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div.right-box{
            width:calc(100% - 138px);
            box-sizing: border-box;
            padding:10px 30px 10px 10px;
            position:relative;
        }
        .push-modal .modal-body .coupon-push .coupon-box li > div.right-box p{
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            text-align: left;
            color: #292933;
            overflow: hidden;
            line-height: 20px;
        }
        .push-modal .modal-body .coupon-push .coupon-box li > div.right-box p i{
            float:left;
            padding:0 10px;
            height:20px;
            border-radius: 12px;
            background:#FF4244;
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: center;
            color: #ffffff;
            font-style: normal;
            margin-right:10px;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div.right-box p.time-text{
            font-size: 14px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: left;
            color: #999999;
            position: absolute;
            bottom:10px;
        }

        .push-modal .modal-body .coupon-push .coupon-box li > div.right-box button{
            position:absolute;
            padding:0 10px;
            line-height: 32px;
            height: 32px;
            background: linear-gradient(90deg,#ff6025, #ff4244);
            border:none;
            border-radius: 18px;
            font-size: 16px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: center;
            color:#fff;
            right:30px;
            bottom:32px;
            outline: none;
        }

        .push-modal .modal-body .coupon-push .more-box{
            height:55px;
            box-sizing: border-box;
        }

        .push-modal .modal-body .coupon-push .more-box button{
            width:280px;
            height: 40px;
            background: linear-gradient(180deg,#fff6dd, #ffc362 92%, #ffc362 92%);
            border-radius: 32px;
            font-size: 20px;
            font-family: PingFangSC, PingFangSC-Medium;
            font-weight: 500;
            text-align: center;
            color: #ff4244;
            border:none;
            outline:none;
        }

    </style>
    <style>
        .personal-advisor{
            position: fixed;top: 0;left: 0;width: 100%;height: 100%;background-color: rgba(0,0,0,0.5);display: none;z-index: 2222;
        }
        .personal-advisor .guide-content{
            position: fixed;
            top: 400px;
            right: 85px;
        }
         .personal-advisor .guide-content .tip-content{
            background: #fff;
            position: relative;
            width: 247px;
            border-radius: 11.51px;
            margin: 10px;
            padding: 14px 10px 0 10px;
        }
        .personal-advisor .guide-content .tip-content .wx-advisor{
            font-family: PingFangSC;color: #000;font-weight: bold;font-size: 16px;text-align: center;margin-bottom: 4px;
        }
        .personal-advisor .guide-content .tip-content .wx-advisor .wx-tip-weight{
            color: #00be57;font-style: italic;font-weight: bold;font-size: 18px;
        }
        .personal-advisor .guide-content .tip-content .tip-button{
            color: #fff;
            border-radius: 20px;
            background-color: #00be57;
            width: 110.5px;
            height: 32px;
            line-height: 32px;
            margin-left: 68.5px;
            border: none;margin-bottom: 13px;
        }
        .personal-advisor .guide-content .tip-content .r-topbox{
            position: absolute;right: -100px;top: 20px;
        }
        @media only screen and (max-height: 768px) {
            .personal-advisor .guide-content{
               top: 290px !important;
            }
        }

        /* 轮播容器样式 */
        .carousel-container {
            position: relative;
            overflow: hidden;
        }

        .carousel-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .carousel-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }

        .carousel-item.active {
            opacity: 1;
        }

        /* SaaS广告区域样式 */
        .saas-ad {
            background: #007DFF;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .saas-content {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .saas-text-box {
            width: 190px;
            height: 80px;
            background: #FFFFFF;
            border: 1px solid #007DFF;
            border-radius: 8px;
            position: relative;
            top: -10px;
            padding: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
        }

        .saas-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 5px;
        }

        .saas-logo {
            height: 20px;
        }

        .saas-text {
            width: 117px;
            height: 17px;
            font-family: AlimamaShuHeiTi-Bold, sans-serif;
            font-weight: 700;
            font-size: 16.63px;
            color: #007DFF;
            letter-spacing: 0;
            line-height: 16.63px;
            text-align: center;
            margin-bottom: 8px;
        }

        .saas-btn {
            width: 108px;
            height: 32px;
            background: #FFF76C;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .saas-btn:hover {
            background: #FFE55C;
        }

        /* 轮播箭头样式 */
        .carousel-arrows {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            pointer-events: none;
        }

        .arrow-left,
        .arrow-right {
            position: absolute;
            top: 0;
            width: 30px;
            height: 30px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
        }

        .arrow-left {
            left: 10px;
        }

        .arrow-right {
            right: 10px;
        }

        .arrow-left img,
        .arrow-right img {
            width: 16px;
            height: 16px;
        }

        .carousel-container:hover .arrow-left,
        .carousel-container:hover .arrow-right {
            opacity: 1;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .saas-text-box {
                width: 160px;
                height: 70px;
                padding: 8px;
            }

            .saas-text {
                font-size: 14px;
                line-height: 14px;
                width: 100px;
            }

            .saas-btn {
                width: 90px;
                height: 28px;
                font-size: 12px;
            }

            .arrow-left,
            .arrow-right {
                width: 25px;
                height: 25px;
            }

            .arrow-left img,
            .arrow-right img {
                width: 12px;
                height: 12px;
            }
        }

    </style>
    <#if (cmsIndexHtmlPcMainCssName??)>
        <link rel="stylesheet" href="${assetsDomain}/static/${cmsIndexHtmlPcMainCssName}" />
    </#if>
</head>
<prop key="classic_compatible"  style="display: none;">true</prop>
<body>
    <div class="container">
        <#if alertFlag ==2>
            <!--被委托人认证弹窗-->
            <!--$('#authModal').modal('show');-->
            <div id="authModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade" data-show="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                            <h4 id="myModalLabel" class="modal-title">温馨提示</h4>
                        </div>
                        <div class="modal-body" style="text-align: left;font-size: 14px;line-height:20px;">为了确保配送药品的安全，邀请您进行企业被委托人身份信息认证</div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">暂不认证</button>
                            <a type="button" class="sui-btn btn-primary btn-large" href="/authentication/indexAuthentication.htm?reSubmit=1">去认证</a>
                        </div>
                    </div>
                </div>
            </div>
        </#if>
        <#if alertFlag1==true >
            <!--被委托人信息变更弹窗-->
            <div id="modifyModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade" data-backdrop="static" style="display: block;">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                            <h4 id="myModalLabel" class="modal-title">温馨提示</h4>
                        </div>
                        <div class="modal-body" style="text-align: left;font-size: 14px;line-height: 20px;">企业被委托人信息已变更，为了确保配送药品的安全，邀请您重新进行企业被委托人身份信息认证</div>
                        <div class="modal-footer">
                            <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">暂不认证</button>
                            <a type="button" class="sui-btn btn-primary btn-large" href="/authentication/indexAuthentication.htm">去认证</a>
                        </div>
                    </div>
                </div>
            </div>
        </#if>
        <#if count!=null && count gt 0 >
        <div id="myModal" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" data-bgcolor="rgba(0, 0, 0, 0.7411764705882353)"  data-show="true" class="sui-modal hide">
            <div class="modal-dialog">
                <div class="modal-content">

                    <div class="modal-body" style="line-height: 25px;font-size:18;color:#333;font-weight:600;padding:25px 0px;">
                        您的资质即将过期/已过期
                        <br/>
                        为避免影响您的正常采购，请及时联系工作人员</div>
                    <div class="modal-footer" style="text-align: center;">
                        <a href="/merchant/center/license/findLicenseCategoryInfo.htm" class="sui-btn btn-primary btn-large">去查看</a>
                    </div>
                </div>
            </div>
        </div>
        </#if>
        <script>
            $(function(){
                if($('#myModal') && $('#myModal').length){
                    $('#myModal').modal({
                        show:true,
                        backdrop:"static"
                    })
                }
            })
        </script>
        <input type="hidden" id="branchCode" name="branchCode" value="${branchCode}"/>
        <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
        <input type="hidden" id="pageId" name="pageId" value="${pageId}"/>
        <input type="hidden" id="version" name="version" value="${pageVersion}"/>
        <input type="hidden" id="customerGroupIdModuleHeader" name="customerGroupIdModuleHeader" value="${moduleHeader.items[0].customerGroupId}"/>
        <input type="hidden" id="exhibitionIdStrModuleHeader" name="exhibitionIdStrModuleHeader" value="${moduleHeader.exhibitionIdStr }"/>
        <input type="hidden" id="textModuleHeader" name="textModuleHeader" value="${moduleHeader.items[0].text}"/>
        <#if (moduleHeader??)>
            <#if moduleHeader.style == 100>
            <!--806顶部广告图片-->
            <#if (moduleHeader.bgRes?? && moduleHeader.bgRes != '')>
                <div class="topbanner-new" id="topbanner-new" style="background-color: ${moduleHeader.bgRes}" >
            <#else>
                <div class="topbanner-new" id="topbanner-new"  style="background-color: rgba(0,0,0,0);">
            </#if>
                <#if moduleHeader.items?? && (moduleHeader.items?size > 0)>
                    <#list moduleHeader.items as item>
                        <#if item_index == 0>
                            <#if (item.action?? && item.action!='')>
                                <a href="${item.action}" class="topbanner-new-a" onclick="topbannernew(this,'${item.action}')">
                                    <img src="${item.imgUrl}" alt=""/>
                                </a>
                            <#else>
                                <a href="javascript:void(0);">
                                    <#--<a href="javascript:void(0);" onclick="receiveTemplate(${merchantId},120)">-->
                                    <img src="${item.imgUrl}" alt="" class="topbanner-new-a"  onclick="topbannernew(this,0)" />
                                </a>
                            </#if>
                        </#if>
                    </#list>
                </#if>
            </div>
            <#elseif (merchantId > 0)>
                <!--顶部广告图片-->
                <input type="hidden" id="moduleHeaderAction" name="moduleHeaderAction" value="${moduleHeader.action}"/>
                <div class="topbanner-new" id="topbanner-new" onclick="topbannernew(this,'${moduleHeader.action}')">
                    <#if (moduleHeader.action?? && moduleHeader.action!='')>
                        <a href="${moduleHeader.action}">
                            <img src="${productImageUrl}${moduleHeader.bgRes}" alt=""/>
                        </a>
                    <#else>
                        <a href="javascript:void(0);" onclick="openGetQuan()">
                            <#--<a href="javascript:void(0);" onclick="receiveTemplate(${merchantId},120)">-->
                            <img src="${productImageUrl}${moduleHeader.bgRes}" alt="" />
                        </a>
                    </#if>
                </div>
                <!--领券弹窗-->
                <div id="getQuan" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static"
                     class="sui-modal hide fade spe-tc">
                    <div class="fltcbox">
                        <a href="javascript:void(0);" class="close-btn"><img
                                src="http://upload.ybm100.com/ybm/app/layout/20170531/fltcolse.png" alt=""></a>
                        <a href="javascript:void(0);" onclick="receiveTemplate(${merchantId},${moduleHeader.api})"
                           class="fl-detail" target="_blank">
                            <img src="http://upload.ybm100.com/ybm/app/layout/20170531/linquanbg.png" alt="">
                        </a>
                    </div>
                </div>
            <#elseif moduleHeader.style == 1>
                <div class="topbanner-new" id="topbanner-new"  onclick="topbannernew(this,'${moduleHeader.action}')">
                    <#if (moduleHeader.action?? && moduleHeader.action!='')>
                        <a href="${moduleHeader.action}">
                            <img src="${productImageUrl}${moduleHeader.bgRes}" alt=""/>
                        </a>
                    <#else>
                        <a href="javascript:void(0);" onclick="openGetQuan()">
                            <#--<a href="javascript:void(0);" onclick="receiveTemplate(${merchantId},120)">-->
                            <img src="${productImageUrl}${moduleHeader.bgRes}" alt=""/>
                        </a>
                    </#if>
                </div>
            </#if>
        </#if>

        <!--置顶搜索栏-->
        <div class="topsearcher" id="topsearcherView">
            <div class="w1200">
                <a href="/" class="sep_seach fl"><img src="/static/images/logo-zhiding.png" alt=""/></a>
                <div class="search fl">
                    <div class="inputbox fl">
                        <input type="text" id="search-top" placeholder="药品名称/厂家名称/助记码" value="${keywordSearch}"/>
                    </div>
                    <div class="ss fl">
                        <a href="#" id="searchproductTop" onclick="searchProductTop();">搜索</a>
                    </div>
                </div>
            <#--<div class="cgoubox fl">-->
            <#--<div class="caigou">-->
            <#--<a href="/merchant/center/cart/index.htm" id="myCart"><i class="sui-icon  icon-tb-cart"></i>我的采购单</a>-->
            <#--</div>-->
            <#--<div id="firstCartNumberDiv" class=""></div>-->
            <#--</div>-->
                <!--搜索下拉弹窗-->
                <ul class="searchUl-top" id="searchUl-top">
                </ul>
            </div>
        </div>


        <!--头部导航区域开始-->
        <div class="headerBox" id="headerBox">
            <#include "/common/header.ftl" />
        </div>
        <!--头部导航区域结束-->

        <!--主体部分开始-->
        <div class="main"
                <#if index_bg??>
                    style="background-image: url('${productImageUrl}${index_bg.titleRes}');
                    <#if index_bg.bgRes?default("")?trim?length lt 1>
                        background-color:#fcf3f2;
                    <#else >
                        background-color:${index_bg.bgRes};
                    </#if>
                    "
                </#if>
        >
            <!--商品分类-->
            <div class="goodsmain-index">
                <#if index_bg??>
                    <div class="main_left" onclick="window.location.href='${index_bg.action}'">
                    </div>
                </#if>
                <div class="hdpbox ">
                    <!--幻灯片-->
                    <div class="l-box">
                        <div id="myCarousel2" class="sui-carousel slide">
                            <ol class="carousel-indicators">
                                <#list moduleBanner as item>
                                    <#if (item_index==0)>
                                        <li data-target="#myCarousel2" data-slide-to="0" class="active"></li>
                                    <#else>
                                        <li data-target="#myCarousel2" data-slide-to="${item_index}"></li>
                                    </#if>
                                </#list>
                            </ol>
                            <div class="carousel-inner">
                                <#list moduleBanner as item>
                                    <#if (item_index==0)>
                                        <div class="active item carousel-item-lwq">
                                            <a href="${item.action}" target="_blank" index-data="${item_index + 1}" customerGroupId-data="${item.customerGroupId}" exhibitionId-data="${item.exhibitionIdStr}" text-data="${item.text}"><img
                                                    src="${item.imgUrl}"></a>
                                        </div>
                                    <#else>
                                        <div class="item carousel-item-lwq">
                                            <a href="${item.action}" target="_blank" index-data="${item_index + 1}" customerGroupId-data="${item.customerGroupId}" exhibitionId-data="${item.exhibitionIdStr}" text-data="${item.text}"><img
                                                    src="${item.imgUrl}"></a>
                                        </div>
                                    </#if>
                                </#list>
                            </div>
                            <div class="jiantou">
                                <a href="#myCarousel2" data-slide="prev" class="carousel-control left">‹</a>
                                <a href="#myCarousel2" data-slide="next" class="carousel-control right">›</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!--右侧内容-->
                <div class="yjxdbox">
                    <div class="model1">
                    <#--<a href="/merchant/center/collection/findAttention.htm?tab=2" class="yjcgimg"><img src="/static/images/yijiancaigou.png"  alt=""></a>-->
                    <#--<div class="tuxiangbox"><img src="http://upload.ybm100.com/ybm/app/layout/newpc/touxiang.png" alt=""></div>-->
                        <div class="huanying">Hi，欢迎来到药帮忙</div>
                        <#if (merchantId > 0)>
                        <#--<!--已登录&ndash;&gt;-->
                        <#--<div class="loginin" align="center">-->
                        <#--<a class="noDownLine text-overflow" href="/merchant/center/index.htm" target="_blank">-->
                        <#--${merchant.realName}！欢迎您！-->
                        <#--</a>-->
                        <#--</div>-->
                            <!--已登录-->
                            <div class="loginin">
                                ${merchant.realName}！欢迎您！
                            </div>
                        <#else>
                            <!--未登录-->
                            <div class="loginout ">
                                <a href="/login/login.htm" target="_self" class="login-new">登录</a>
                                <a href="/newstatic/#/register/index" class="reg-new">注册</a>
    <#--                            <a href="/login/register.htm" target="_self" class="reg-new">注册</a>-->
                            </div>
                        </#if>
                    </div>
                    <!--公告-->
                    <div class="gg-new">
                        <div class="gg-new-tit">
                            <span class="fl g-gao">公告</span>
                            <a href="/notice/noticeList" target="_blank" class="fr g-more">更多</a>
                        </div>
                        <ul class="gg-new-list" >
                            <#list noticeDetails as notice>
                                <li id="notice-${notice.id}" style="position: relative"><a class="noDownLine text-overflow" href="/notice/noticeDetail?id=${notice.id}"
                                       target="_blank">${notice.noticeTitle}</a>
                                       <span id="${notice.id}" style="position: absolute;top: -2px;right: 0;width: 6px;height: 6px;background-color: red;border-radius: 50%;"></span></li>

                            </#list>
                        </ul>
                    </div>
                    <#--  <div class="jyzzbox">
                        <img src="/static/images/${branchCode}_zgz.png" alt="小药药资格证---${merchantId}">
                        <img src="/static/images/${branchCode}_zgz.png" alt="小药药资格证---${merchantId}">
                    </div>  -->
                </div>

                <#if index_bg??>
                    <div class="main_right" onclick="window.location.href='${index_bg.action}'">
                    </div>
                </#if>
            </div>

            <div>
                ${cmsIndexHtmlContent}
            </div>

            <#--底部悬浮层-->
            <#if index_footer_div??>
              <div class="main_bottom" style="background:url('${productImageUrl}${index_footer_div.titleRes}') center center no-repeat;background-size: 100%;" onclick="window.location.href='${index_footer_div.action}'">
                  <img src="${productImageUrl}${index_footer_div.titleRes}" alt="" style="opacity:0;width:100%;">
                  <div class="main_img">
                  <#list index_footer_div.items as item>

                      <a href="${item.action}">
                          <img src="${productImageUrl}${item.imgUrl}" alt="" style="width:130px;">
                      </a>
                  </#list>
                  </div>
              </div>
              <script type="text/javascript">
                  if ($('.main_bottom').length > 0) {
                      $.fn.scrollEnd = function (callback, timeout) {
                          $(this).scroll(function () {
                              $('.main_bottom').show();
                              var $this = $(this);
                              if ($this.data('scrollTimeout')) {
                                  clearTimeout($this.data('scrollTimeout'));
                              }
                              $this.data('scrollTimeout', setTimeout(callback, timeout));
                          });
                      };

                      //with a 1000ms timeout
                      $(window).scrollEnd(function () {
                          $('.main_bottom').hide();
                      }, 10000);
                      $(window).scroll(function () {
                          $(".main_bottom").css(" display", "block");
                      });


                  }
              </script>
            </#if>
        </div>
        <!--主体部分结束-->
    </div>
    <!--底部导航区域开始-->
    <div class="footer" id="footer">
        <#include "/common/footer.ftl" />
    </div>
    <!--底部导航区域结束-->

    <!--秒杀活动未开始浮层 1S消失-->
    <div id="msfc" tabindex="-1" role="dialog"
         data-hasfoot="false"
         data-backdrop="static" class="sui-modal hide fade">
        <span>活动尚未开始</span>
    </div>

    <!--319弹窗-->
    <#--<div id="319Frame" tabindex="-1" role="dialog"
         style="display: none;margin-left: -220px; background: none; box-shadow: none;"
         data-hasfoot="false"
         data-backdrop="static"
         class="sui-modal hide fade spe-tc">
        <div style="text-align: right;"
             onclick="colse319Frame()"><span
                class="sui-icon icon-tb-roundclose"
                style="font-size: 35px;color: #fff;">
        </span></div>

    &lt;#&ndash;"sui-modal hide fade spe-tc"&ndash;&gt;
        <a href="#" class="close-btn" id="319Frame_A">
            <img src="" alt="" id="319Frame_img"></a>
    </div>-->

    <!-- 大礼包 -->
    <div class="yin_box" id="giftBag" style="display: none;">
        <div class="img_yin">
            <a href="giftBag/findGiftBag.htm">
                <img src="static/images/giftbag/tanchuang.png"
                     alt="">
            </a>
            <span id="giftBagTime"></span>
            <i class="sui-icon icon-tb-roundclose"></i>
        </div>
    </div>
    <!--客服入口开始-->
    <div id="kefuFloat" class="kefu-box" style="position: fixed;bottom:25px;right:40px;z-index:1000;">
        <a href="javaScript:callKf('','${merchant.id}');">
            <img src="/static/images/kefu-online.png" alt="" width="80px">
        </a>
    </div>
    <!--客服入口结束-->
    <!--首页推送弹框-->
    <div id="pushModal" tabindex="-1" role="dialog" class="push-modal sui-modal fade hide" data-backdrop="static" data-show="false" style="width: max-content;">
        <div class="modal-dialog" style="width: max-content;">
            <div class="modal-content" style="width: max-content;position: relative;">
                <div class="modal-body"></div>
                <#--  <div class="modal-footer">
                    <i class="del-icon" onclick="closePush();"></i>
                </div>  -->
                <img src='../static/images/modal-del.png' style="position: absolute;right: -40px; top: 0;cursor:pointer;" onclick="closePush();" />
            </div>
        </div>
    </div>
    <!--消费返入口和SaaS广告轮播区域-->
    <div id="carouselContainer" class="carousel-container">
        <!-- 轮播内容区域 -->
        <div class="carousel-wrapper">
            <!-- 消费返区域 -->
            <div id="rebatePage" class="consumer-rebate carousel-item active">
                <div class="rebate-head">
                    <div class="rebate-flex">
                        <img style="height: 20px;" src="/static/images/rebate-title.png" alt="">
                        <img class="red-packet-h" src="/static/images/red-packet-h.png" alt="">
                    </div>
                    <div id="rebateContent" class="rebate-content-box">
                    参与下单返活动，预估月均可返红包 <span>30元</span>
                    </div>
                    <div class="rebate-look-btn">
                            立即查看
                            <img class="rebate-btn-icon" src="/static/images/more_icon.png">
                    </div>
                </div>
            </div>

            <!-- SaaS广告区域 -->
            <div id="saasAdPage" class="saas-ad carousel-item">
                <div class="saas-content">
                    <div class="saas-text-box">
                        <div class="saas-header">
                            <img src="/static/images/saas_logo_img.png" alt="SaaS Logo" class="saas-logo">
                        </div>
                        <div class="saas-text">
                            智慧脸支持对接<br />码上放心 和 医保追溯码
                        </div>
                        <div class="saas-btn">
                            立即体验 >
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 轮播控制箭头 -->
        <div class="carousel-arrows">
            <div class="arrow-left">
                <img src="/static/images/changePage-left.png" alt="上一页">
            </div>
            <div class="arrow-right">
                <img src="/static/images/changePage-right.png" alt="下一页">
            </div>
        </div>
    </div>
    <!--专属顾问指引-->
    <div id="personalAdvisor" class="personal-advisor">
      <div class="guide-content">
        <div class="tip-content">
          <p
          class="wx-advisor">
            添加<span class="wx-tip-weight">&nbsp专属采购顾问&nbsp</span>微信</p>
          <p
            class="wx-advisor">
            第一时间获取活动优惠信息</p>
          <button class="tip-button" type="button" id="closeBtn">我知道了</button>
          <div class="r-topbox" >
            <img style="width: 60px;height: 80px;" src="/static/images/guide.png" alt="">
          </div>
        </div>
      </div>
    </div>
    
    <!--消费返入口-->
    <div id="rebatePage" class="consumer-rebate">
            <div class="rebate-head">
                <div class="rebate-flex">
                    <img style="height: 20px;" src="/static/images/rebate-title.png" alt="">
                    <img class="red-packet-h" src="/static/images/red-packet-h.png" alt="">
                </div>
                <div id="rebateContent" class="rebate-content-box">
                参与下单返活动，预估月均可返红包 <span>30元</span>
                </div>
                <div class="rebate-look-btn">
                        立即查看
                        <img class="rebate-btn-icon" src="/static/images/more_icon.png">
                </div>
        </div>
    </div>
</body>
<script type="text/javascript">
    window.isIndexV2 = true;
    window.keepChineseEnglishAndNumbers=function(text) {
      return text.replace(/[._\-~|@]/g, "");
    }
    // saveId 函数用于保存 id 到 localStorage
    function saveId(id) {
        // 获取本地存储中的公告 id 数组（如果没有则初始化为空数组）
        let idArray = JSON.parse(localStorage.getItem("noticeIds")) || [];

        // 如果 id 不在数组中，则添加它
        if (!idArray.includes(id)) {
            idArray.push(id);
        }

        // 将更新后的数组存储回 localStorage
        localStorage.setItem("noticeIds", JSON.stringify(idArray));

        // 打印存储的公告 ID 数组到控制台
        //console.log("存储的公告 ID 数组: ", idArray);
    }
    $(document).ready(function () {
         // 页面加载时检查 localStorage 中的已隐藏公告 ID，并隐藏对应的 span
        let hiddenIds = JSON.parse(localStorage.getItem("noticeIds")) || [];
        //轮播图
        let carouselBox = document.getElementById("myCarousel2");
        const observer = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
            if (entry.isIntersecting) {
                observer.unobserve(carouselBox);
                if (window.isIndexV2) {
                    let spm_cnt = '1_4';
                    spm_cnt += ".home_"+$("#pageId").val()+"-"+$("#version").val()+"_0";
                    spm_cnt += ".myCarousel2@" + window.getPosition(4);
                    spm_cnt += ".0";
                    spm_cnt += "." + window.getSpmE();
                    let data = {
                        spm_cnt: spm_cnt,

                    }
                    aplus_queue.push({
                        'action': 'aplus.record',
                        'arguments': ['page_component_exposure', 'EXP', data]
                    });
                }
                //binding.value(el,true); // 元素可见时，调用传入的回调函数
            }
            });
        });
        observer.observe(carouselBox);
        $('.carousel-item-lwq a').click(function () {
            if (!$(this).attr("data-scmE")) {
                window.addScme(this,1)
            }
            let custom = $(this).attr('customerGroupId-data') == -1 ? 'all' : $(this).attr('customerGroupId-data');
            let exhibitionId = window.getExhibitionIdStr($(this).attr('exhibitionId-data'));
            let spm_cnt = '1_4';
            spm_cnt += ".home_"+$("#pageId").val()+"-"+$("#version").val()+"_0";
            spm_cnt += ".myCarousel2@" + window.getPosition(4);
            spm_cnt += ".img@" + $(this).attr("index-data");
            spm_cnt += "." + window.getSpmE();
            let scm_cnt = "admin";
            scm_cnt += ".0";
            scm_cnt += "." + custom + "_" + exhibitionId;
            scm_cnt += ".h5-" + window.getId($(this).attr("href")) + "_text-" + window.keepChineseEnglishAndNumbers($(this).attr("text-data"));
            scm_cnt += "." + window.addScmeV2(1);
            aplus_queue.push({
                'action': 'aplus.record',
                'arguments': ['action_sub_module_click', 'CLK', {
                    spm_cnt: spm_cnt,
                    scm_cnt: scm_cnt,

                }]
            });
        })
        // 隐藏本地存储中记录的公告的 span
        hiddenIds.forEach(function(id) {
            $("#" + id).hide();  // 隐藏与 id 对应的 span
        });

        // 点击事件：点击时隐藏对应的 span
        $('.gg-new-list li').click(function() {
            // 获取当前 <li> 元素的 id，假设格式为 notice-123
            let noticeId = $(this).attr('id').split('-')[1];

        // 隐藏点击的 span
            $("#" + noticeId).hide();

            // 调用 saveId 函数保存 id
            saveId(noticeId);
        });
        // 未登录隐藏在线客服入口
        if ($('#merchantId').val() == '0') {
            $('#kefuFloat').hide();
        }
        // if (window.ybmJsBridge && window.ybmJsBridge.setUserId) {
        //     var bridgeLoaclStorage = localStorage.getItem('bridgeLoaclStorage')
        //     var merchantId = document.getElementById('merchantId').value
        //     if (!bridgeLoaclStorage && merchantId != '0') {
        //         localStorage.setItem('bridgeLoaclStorage', 'has');
        //         window.ybmJsBridge.setUserId(merchantId)
        //     }
        // }
        $("#authModal").modal('show')
        $("#modifyModal").modal('show')
        /*         var int = 0;
                //搜索弹窗响应键盘事件

                $("#search-top").unbind("keyup");
                $("#search-top").keyup(function (event) {
                    var e = event || window.event;
                    var k = e.keyCode || e.which;
                    if(k == 38 || k == 37 || k == 39 || k == 40){
                    }else{
                        var name = $("#search-top").val();
                        if(name == null || name ==""){
        //                    name="阿莫西林胶囊";
                        }else{
                            $("#search-top").val(name);
                        }
                        loadProductNameByAutoCompleteTop(name);
                    }

                    switch (k) {
                        case 13:
        //                    searchProductTopName();
                            break;

                    }
                }); */


        /*         $("#search").unbind("keyup");
                $("#search").keyup(function (event) {
                    var e = event || window.event;
                    var k = e.keyCode || e.which;
                    if(k == 38 || k == 37 || k == 39 || k == 40){
                    }else{
                        var name = $("#search").val();
                        if(name == null || name ==""){
        //                    name="阿莫西林胶囊";
                        }else{
                            $("#search").val(name);
                        }
                        loadProductNameByAutoComplete(name);
                    }

                    switch (k) {
                        case 13:
        //                    searchProductName();
                            break;

                    }
                }); */
    });

    /*     function searchProductName(){

            var name = $("#search").val();
            if(name == null || name ==""){
    //            name="阿莫西林胶囊";
            }else{
                var links ="/search/skuVO.htm?keyword="+name;
                //links = encodeURI(links);
                location.href= (links);
            }


        }
     */
    /*     function searchProductTopName(){

            var topName = $("#search-top").val();
            if(topName == null || topName ==""){
    //            topName="阿莫西林胶囊";
            }else{
                var links ="/search/skuVO.htm?keyword="+topName;
                //links = encodeURI(links);
                location.href= (links);
            }


        } */

    function changeList(divId, obj, id, time, state, changci) {
        if (state == 1) {
            $("#only").html("距开始");
        } else {
            $("#only").html("距结束");
        }
        $("#changci").html(changci + "点场");
        $("#skill_timer").attr("data-timer", time);
        $(".skillProductList").hide();
        $(".div_left_right").hide();
        $("#" + divId).show();
        $(".timebox li").attr("class", "");
        $("#" + obj.id).attr("class", "cur");
    }

    function left_move(id) {
        var tempLength = eval($('#tempLength_' + id).val()); //临时变量,当前移动的长度
        var moveNum = 1; //每次移动的数量
        var moveTime = 300; //移动速度,毫秒
        var scrollDiv = $("#miaosha_" + id + ""); //进行移动动画的容器
        var scrollItems = $("#miaosha_" + id + " li"); //移动容器里的集合
        var moveLength = (scrollItems.eq(0).outerWidth(true)) * moveNum; //计算每次移动的长度
        //上一张
        if (tempLength > 0) {
            if (tempLength > moveLength) {
                scrollDiv.animate({left: "+=" + moveLength + "px"}, moveTime);
                tempLength -= moveLength;
            } else {
                scrollDiv.animate({left: "+=" + tempLength + "px"}, moveTime);
                tempLength = 0;
            }
        }
        $('#tempLength_' + id).val(tempLength);

    }

    function right_move(id) {
        var tempLength = eval($('#tempLength_' + id).val()); //临时变量,当前移动的长度
        var viewNum = 5; //设置每次显示图片的个数量
        var moveNum = 1; //每次移动的数量
        var moveTime = 300; //移动速度,毫秒
        var scrollDiv = $("#miaosha_" + id + ""); //进行移动动画的容器
        var scrollItems = $("#miaosha_" + id + " li"); //移动容器里的集合
        var moveLength = (scrollItems.eq(0).outerWidth(true)) * moveNum; //计算每次移动的长度
        var countLength = (scrollItems.length - viewNum) * scrollItems.eq(0).outerWidth(true); //计算总长度,总个数*单个长度
        //下一张
        if (tempLength < countLength) {
            if ((countLength - tempLength) > moveLength) {
                scrollDiv.animate({left: "-=" + moveLength + "px"}, moveTime);
                tempLength += moveLength;
            } else {
                scrollDiv.animate({left: "-=" + (countLength - tempLength) + "px"}, moveTime);
                tempLength += (countLength - tempLength);
            }
        }
        $('#tempLength_' + id).val(tempLength);
    }
</script>
<script type="text/javascript">

    $(".addList").click(function () {
        var step = 10;
        var me = $(this),
                txt = me.prev(":text");
        var val = parseFloat(txt.val());
        var isSplit = txt.attr("isSplit");
        var middpacking = txt.attr("middpacking");
        step = parseFloat(middpacking);
        var num = 0;
        if (!isNaN(val)) {
            num = val;
        }
        txt.val(num + step);
    });

    $(".subList").click(function () {
        var step = 1;
        var me = $(this),
                txt = me.next(":text");
        var val = parseFloat(txt.val());
        var isSplit = txt.attr("isSplit");
        var middpacking = txt.attr("middpacking");
        if (isSplit == 0) {
            step = parseFloat(middpacking);
        }
        var num = 0;
        if (!isNaN(val)) {
            num = val;
        }
        if (num <= step) {
            txt.val(0);
        } else {
            txt.val(num - step);
        }
    });

    /*秒杀加1操作*/
    $(".addOne").click(function () {
        var step = 10;
        var me = $(this),
                txt = me.prev(":text");
        var val = parseFloat(txt.val());
        var isSplit = txt.attr("isSplit");
        var middpacking = txt.attr("middpacking");

        step = parseFloat(middpacking);

        var num = 0;
        if (!isNaN(val)) {
            num = val;
        }
        txt.val(num + step);
    });


    /*减操作*/
    $(".subOne").click(function () {
        var step = 1;
        var me = $(this),
                txt = me.next(":text");
        var val = parseFloat(txt.val());
        var isSplit = txt.attr("isSplit");
        var middpacking = txt.attr("middpacking");
        if (isSplit == 0) {
            step = parseFloat(middpacking);
        }
        var num = 0;
        if (!isNaN(val)) {
            num = val;
        }
        if (num <= step) {
            txt.val(0);
        } else {
            txt.val(num - step);
        }
    });

    //
    function openGetQuan() {
        $("#getQuan").modal('show');
    }

    // 領取優惠券
    function receiveTemplate(merchantId, receiveTemplateId) {
        if (merchantId && merchantId > 0) {
            $.ajax({
                url: "/merchant/center/voucher/receiveVoucher",
                type: "POST",
                dataType: "json",
                data: {
                    merchantId: merchantId,
                    voucherTemplateId: receiveTemplateId
                },
                success: function (result) {
                    $("#getQuan").modal('hide');
                    if (result.status == "success") {
                        $.alert({
                            title: '提示',
                            body: result.msg
                        });
                    } else {
                        $.alert({
                            title: '提示',
                            body: result.errorMsg
                        });
                    }
                },
                error: function () {
                    $("#getQuan").modal('hide');
                    $.alert({
                        title: '提示',
                        body: '因为某些原因导致优惠券领取异常哟!'
                    });
                }
            });
        } else {
            $.alert({
                title: '提示',
                body: '您还没有登录，请先登录!',
                okHidden: function (e) {
                    window.location.href = "/login/login.htm?redirectUrl=/";
                }
            });
        }
    }
    $.ajax({
            url: '/marketing/rebateVoucher/consumption/startActInfo',
            type: 'get',
            data: {

            },
            success(data) {
                if(data.status=="success"){
                    if(data.data.result!=null||data.data.redPacketRecord.length>0){
                        if(data.data.result.actResultList!=null||data.data.result.actResultList.length>0){
                            var str = "";
                            if(data.data.result.levelScene == 1){
                                if(data.data.result.realAmount==0 && data.data.result.lastMonthExpectedAmount){
                                    str = '<div class="state-content">参与下单返活动，预估月均可返红包 <span>'+data.data.result.lastMonthExpectedAmount+'元</span></div>'  
                                }else {
                                    str = '<div class="state-content">参与下单返活动，预估月均可返红包 <span>'+data.data.result.actTags.maxReturnRedPackageAmount+'元</span></div>'  
                                }   
                            }else if(data.data.result.levelScene == 2){
                                str = '<div class="state-content">仅差<span>'+data.data.result.nextLevelShortAmount+'元</span>参与返利活动，返利<span>'+data.data.result.nextLevelRate+'%</span>起</div>'
                            }else if(data.data.result.levelScene == 3&&data.data.result.nextLevelShortAmount!=0){
                                str = '<div class="state-content">已获得红包 <span>'+data.data.result.realAmount+'元</span>仅差<span>'+data.data.result.nextLevelShortAmount+'元</span>可返红包<span>'+data.data.result.nextLevelRedPacketAmount+'元</span></div>'
                            }else if(data.data.result.levelScene == 3&&data.data.result.nextLevelShortAmount==0){
                                str = '<div class="state-content">已获得红包<span class="amount-color">'+data.data.result.realAmount+'元</span></div>'
                            }else{
                                str = '<div class="state-content"> 已获得红包 <span>'+data.data.result.realAmount+'元</span>，多买多返最高返<span>'+data.data.result.actTags.maxReturnRedPackageAmount+'元</span></div>'
                            }
                            $("#rebateContent").html(str)
                            $("#rebatePage").show()
                        }
                    
                    }
                }
               
            },
            error(err) {
                parent.layer.msg('网络异常');
            }
        })
</script>
<!--首页推送js-->
<script type="text/javascript">
    $(function(){
        isUseJgAlias();
        $.post('/layout/frame/getNewCmsDialog',{sceneType:1},function(res){
            if (res.code == 5000) {
                alert(res.msg);
            } else if(!$.isEmptyObject(res.data)){
                var data = res.data.detail
                var type = data.style
                var datas = type==='60' ? data.imageDtos : data.couponDtos
                initPushDialog(type,datas,data.imageDtos)
                $('#pushModal').modal('show')
                $('#pushModal').on('shown.bs.modal', function () {
                    if(type==='60'){
                        var $box = $('#pushModal .push-box')
                        if($box.length){
                            if($box.scrollTop()>0){
                                $box.siblings('.top-icon').show()
                            }else{
                                $box.siblings('.top-icon').hide()
                            }
                            if($box[0].scrollHeight-$box.height()>$box.scrollTop()){
                                $box.siblings('.bottom-icon').show()
                            }else{
                                $box.siblings('.bottom-icon').hide()
                            }

                            $box.off('scroll').on('scroll',function(){
                                if($(this).scrollTop()>0){
                                    $(this).siblings('.top-icon').show()
                                }else{
                                    $(this).siblings('.top-icon').hide()
                                }
                                if($(this)[0].scrollHeight-$(this).height()>$(this).scrollTop()){
                                    $(this).siblings('.bottom-icon').show()
                                }else{
                                    $(this).siblings('.bottom-icon').hide()
                                }
                            })
                            var $up = $('#pushModal .top-icon')
                            var $down = $('#pushModal .bottom-icon')
                            // 滚动高度
                            var step = 10
                            if($up.length){
                                $up.off('click').on('click',function(){
                                    if($box.scrollTop()>0){
                                        $box.scrollTop($box.scrollTop()-step<0?0:$box.scrollTop()-step)
                                    }
                                })
                            }
                            if($down.length){
                                $down.off('click').on('click',function(){
                                    var max = $box[0].scrollHeight-$box.height()
                                    if(max > $box.scrollTop()){
                                        $box.scrollTop($box.scrollTop()+step>max?max:$box.scrollTop()+step)
                                    }
                                })
                            }
                        }
                    }
                })
            }else{
                // 显示指引
                var cookieToken =  getCookieValue('xyy_token');
                if(cookieToken){
                    if (shouldShowGuide()) {
                        $('#personalAdvisor').fadeIn(300);
                        updateShowRecord();
                    }
                }
            }
        },'json')
         // 检查是否应该显示指引
        function shouldShowGuide() {
            const lastShowDate = localStorage.getItem('guideLastShowDate');
            const showCount = parseInt(localStorage.getItem('guideShowCount')) || 0;
            // 如果从未显示过，则显示
            if (!lastShowDate) return true;
            const lastDate = new Date(lastShowDate);
            const now = new Date();
            const oneDay = 24 * 60 * 60 * 1000;
            const oneWeek = 7 * oneDay;
            // 检查是否超过一周
            if ((now - lastDate) > oneWeek) {
                localStorage.removeItem('guideShowCount');
                return false;
            }
            // 检查今天是否已经显示过
            if (now.toDateString() === lastDate.toDateString()) {
                return false;
            }
            // 检查一周内是否超过2次
            return showCount < 2;
        }
        // 更新显示记录
        function updateShowRecord() {
            const now = new Date();
            const lastShowDate = localStorage.getItem('guideLastShowDate');
            let showCount = parseInt(localStorage.getItem('guideShowCount')) || 0;
            // 如果是新的一天，重置计数
            if (!lastShowDate || new Date(lastShowDate).toDateString() !== now.toDateString()) {
                showCount++;
            }
            localStorage.setItem('guideLastShowDate', now);
            localStorage.setItem('guideShowCount', showCount);
        }

        // 关闭指引
        $('#closeBtn').click(function() {
            $("#personalAdvisor").fadeOut(300);
        });


    });

    (function($){
        $.couponDatas = []
        $.imgDatas = []
    })(jQuery)

   
    $("#rebatePage").click(function(e) {
       window.location.href = '/marketing/rebateVoucher/consumerRebate.htm'
    });

    function closePush(){
        $('#pushModal').modal('hide')
    }
    function isUseJgAlias() {
        let isJg_login = getCookieValue("jg_login") || 0;
        if (isJg_login == 1) {
            console.log($("#merchantId").val(), "调用alias");
            setCookie("jg_login",0,24*7,"/");
        }
    }

    /**
     * 初始化推送弹框
     * @param type 弹框类型
     * @param datas 弹框展示数据
     */
    function initPushDialog(type,datas,imgs){
        var html = ''
        if(type==='60'){
            html = '<div class="normal-push">' +
                '       <div class="push-box">'
            for(var i=0;i<datas.length;i++){
                var classHtml = ''
                if(i===datas.length-1){
                    classHtml = 'class="last"'
                }
                var hrefHtml = datas[i].action?'href="'+datas[i].action+'"':''
                html+= '<a '+classHtml+' '+hrefHtml+'>' +
                    '       <img src="'+datas[i].imgUrl+'">' +
                    '   </a>'
            }
            html += '</div>' +
                '   <i class="top-icon"></i>' +
                '   <i class="bottom-icon"></i>' +
                '</div>'
        }else{
            $.couponDatas = datas
            $.imgDatas = imgs
            var moreHtml = ''
            var len = datas.length>3 ? 3 : datas.length
            if(datas.length>=3){
                moreHtml = '<div class="more-box">' +
                    '         <button onclick="toCouponCenter()">点击领取更多</button>' +
                    '</div>'
            }
            var imgHtml = ''
            if(imgs && imgs.length){
                var hrefHtml = datas[0].action?'href="'+datas[0].action+'"':''
                imgHtml = '<a '+hrefHtml+' ><img src="'+imgs[0].imgUrl+'"></a>'
            }
            html = '<div class="coupon-push">' +
                '       <div class="ad-box">'+imgHtml+'</div>' +
                '           <ul class="coupon-box">'
            for(var i=0;i<len;i++){
                var priceHtml = ''
                if(datas[i].voucherState===0){
                    priceHtml = '￥'+ datas[i].moneyInVoucher
                }else{
                    priceHtml = datas[i].discount+'折'
                }
                var classHtml = ''
                if(i===len-1){
                    classHtml = 'class="last"'
                }
                var timeHtml = ''
                if(datas[i].expireDate){
                    timeHtml = '使用时间：'+formatDate(datas[i].expireDate)+'-'+formatDate(datas[i].validDate)
                }

                html += '<li '+classHtml+'>' +
                    '      <div class="left-box">' +
                    '          <p>'+priceHtml+'</p>' +
                    '          <p>'+datas[i].minMoneyToEnableDesc+'</p>' +
                    '      </div>' +
                    '      <div class="right-box">' +
                    '          <p><i>'+datas[i].voucherTypeDesc+'</i>'+datas[i].voucherTitle+'</p>' +
                    '          <p class="time-text">'+timeHtml+'</p>' +
                    '          <button onclick="toCoupon(\''+datas[i].pcUrl+'\','+datas[i].templateId+','+datas[i].state+');">'+(datas[i].state===1?'立即领取':'去使用')+'</button>' +
                    '      </div>' +
                    '   </li>'
            }
            html += '</ul>' + moreHtml +
                '</div>'
        }
        $('#pushModal .modal-body').html(html)
    }

    function formatDate(date){
        return date?new Date(date + 8 * 3600 * 1000)
            .toJSON()
            .substr(0, 19)
            .split('T')[0].replace(/-/g,'/'):''
    }

    /**
     * 领取优惠券
     * @param url 跳转地址
     */
    function toCoupon(url,id,type){
        if(Number(type)===1){
            // 立即领取
            $.post('/merchant/center/voucher/receiveVoucher',{voucherTemplateId:id,merchantId: $("#merchantId").val()},function(res){
                if(res.status==='success'){
                    for(var i=0;i<$.couponDatas.length;i++){
                        if($.couponDatas[i].templateId===id){
                            $.couponDatas[i].state=2
                            $.couponDatas[i].validDate = res.validDate
                            $.couponDatas[i].expireDate = res.expireDate
                            initPushDialog(70,$.couponDatas,$.imgDatas)
                        }
                    }
                }else{
                    $.alert({
                        title: '提示',
                        body: res.errorMsg || res.msg
                    });
                }
            },'json')
        }else{
            var a = document.createElement('a')
            // 去使用
            if(url!=='null'){
                a.href = url
            }else{
                a.href = '/voucher/centre/findVoucherSku.htm?voucherTemplateId='+id
            }
            a.click()
        }
    }

    /**
     * 去领券中心
     */
    function toCouponCenter(){
        var a = document.createElement('a')
        a.href = '/activity/voucherCenter.htm?isVoucherCenter=true'
        a.click()
    }


</script>
<script>
try{
     // 判断元素是否在视口内
    function isElementInViewport($element) {
      const elementTop = $element.offset().top; // 元素距离顶部的偏移量
      const elementBottom = elementTop + $element.outerHeight(); // 元素底部距离顶部的偏移量
      const viewportTop = $(window).scrollTop(); // 视口顶部位置
      const viewportBottom = viewportTop + $(window).height(); // 视口底部位置

      return elementBottom > viewportTop && elementTop < viewportBottom;
    }
    window.otherSix="";
    window.updateSpmESix = function () {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        window.otherSix=result;
      
    };
      window.updateSpmESix();

    //获取spme
    window.getSpmE=function(){

        return  (getCookieValue("qt_session")||"_").split('_')[0]+window.otherSix;
    }
    //获取scme
    window.getScmEjq=function(dom,stringid){
        if(stringid){
          return  stringid + $(dom).attr("data-scmE")
        }else{
          return  $(dom).attr("data-scmE")
        }
    }
    window.addScme=function(dom,type){
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        //14位
        if(type==1){
            for (let i = 0; i < 14; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            $(dom).attr("data-scmE",result)

        }
        //6位
        if(type==2){
            for (let i = 0; i < 6; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
           $(dom).attr("data-scmE",result)
        }
    }
    window.addScmeV2 = function(type) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        //14位
        if(type==1){
            for (let i = 0; i < 14; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;

        }
        //6位
        if(type==2){
            for (let i = 0; i < 6; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
           return result;
        }
    }

    // 轮播配置变量
    window.carouselConfig = {
        interval: 3000, // 轮播间隔时间（毫秒）
        showSaasAd: true, // 是否显示SaaS广告区域
        autoPlay: true, // 是否自动播放
        saasAdUrl: '', // SaaS广告点击跳转链接（可以在这里配置具体的跳转地址）
        pauseOnHover: true // 鼠标悬停时是否暂停
    };
    // <#if saasAdEnabled?? && saasAdEnabled>
    //     window.carouselConfig.showSaasAd = true;
    //     window.carouselConfig.saasAdUrl = '${saasAdUrl!""}';
    // <#else>
    //     window.carouselConfig.showSaasAd = false;
    // </#if>

    //
    $(document).ready(function(){

        //判断位置
        if($(".topbanner-new img").length){
           localStorage.setItem("qtPosition",1);
        }else{
            localStorage.setItem("qtPosition",0);
        }
       // <#--  scm赋值  -->
       setTimeout(function(){
         $(".topbanner-new").each(function(index,item){
            window.addScme(item,1)
        })
        $(".topbanner-new-a").each(function(index,item){
            window.addScme(item,1)
        })

        $(".hotKeywordTrack").each(function(index,item){
            window.addScme(item,1)
        })
        $(".abs-warp-tab").each(function(index,item){
            window.addScme(item,1)
        })
        $(".searchBtnLqw").each(function(index,item){
            window.addScme(item,1)
        })
        $(".carousel-item-lwq a").each(function(index,item){
            window.addScme(item,1)
        })
       },1500)
        //qt事件1 埋点页面曝光
        try {
            let spm_cnt="1_4.home_"+$("#pageId").val()+"-"+$("#version").val()+"_0.0.0."+window.getSpmE();
            aplus_queue.push({
                'action': 'aplus.record',
                'arguments': ['page_exposure', 'EXP', {
                "spm_cnt":spm_cnt,

                }]
            });
            //qt埋点
            // 监听滚动事件
            let arr=["#topbanner-new","#header_nav","#kuanjie"]
            window.page_component_exposure=function(){
                arr.forEach(item=>{
                    const $target = $(item);
                    if ($(item).length&&isElementInViewport($target)) {
                        let data={}
                        //qt事件2 头部广告topbannernew组件曝光
                        if(item=="#topbanner-new"){
                                //曝光后移除
                                arr.splice(arr.indexOf(item), 1);
                                data.spm_cnt="1_4.home_"+$("#pageId").val()+"-"+$("#version").val()+"_0.topbannernew@"+window.getPosition(0)+".0."+window.getSpmE();
                        }
                        //qt事件4 导航header组件曝光
                        if(item=="#header_nav"){
                            arr.splice(arr.indexOf(item), 1);
                            data.spm_cnt="1_4.home_"+$("#pageId").val()+"-"+$("#version").val()+"_0.header@"+window.getPosition(1)+".0."+window.getSpmE();
                        }
                        //qt事件26 右侧快捷导航kuaijie组件组件曝光
                        if(item=="#kuanjie"){
                            arr.splice(arr.indexOf(item), 1);
                            data.spm_cnt="1_4.home_"+$("#pageId").val()+"-"+$("#version").val()+"_0.kuaijie@Z.0."+window.getSpmE();
                        }

                         aplus_queue.push({
                            'action': 'aplus.record',
                            'arguments': ['page_component_exposure', 'EXP', data]
                        });

                    }else{
                        console.log('Target Element is not in the viewport!');
                    }
                })
            }
            $(window).on('scroll',window.page_component_exposure);
            setTimeout(function(){
            //初始组件曝光
            window.page_component_exposure()
            //搜索直接曝光
            //qt事件6    搜索框searcherBox1组件
                    aplus_queue.push({
                        'action': 'aplus.record',
                        'arguments': ['page_component_exposure', 'EXP', {
                            spm_cnt:"1_4.home_"+$("#pageId").val()+"-"+$("#version").val()+"_0.searcherBox1@"+window.getPosition(2)+".0."+window.getSpmE(),

                    }]
                });
            },1500)
        }catch(e){

        }
        window.action_sub_module_click=function(data){
            aplus_queue.push({
                'action': 'aplus.record',
                'arguments': ['action_sub_module_click', 'CLK',data]
            });
        }
        window.getId=function(e){
          if(e){
            const regex = /(\d+)\.htm/;
                const match = e.match(regex);
                if (match && match[1]) {
                    const id = match[1];
                   return id
                } else {
                   return 0
                }
          }else{
            return 0
          }
        }
        //qt事件5 埋点导航header组件子模块点击
        setTimeout(function(){
            $(".header-li").each(function(index,item){
                    $(item).on("click",function(){ 
                        if($(this).attr("scmdata")=="400-0505-111"){
                            return
                        }
                        function checkText(text){
                            return text.replace(/[._\-~|@]/g, "");
                        }
                        let scm_cnt=""             
                         if ($(this).attr("scmtype")=="other") {
                            scm_cnt="admin.0."+('all')+"_0.text-"+checkText($(this).attr("scmdata"))+"."+window.addScmeV2(1)
                        }else{
                            scm_cnt="admin.0."+('all')+"_0.link-"+$(this).attr("scmtype")+"_text-"+checkText($(this).attr("scmdata"))+"."+window.addScmeV2(1)
                        }
                        window.action_sub_module_click({
                            "spm_cnt": "1_4.home_"+$("#pageId").val()+"-"+$("#version").val()+"_0.header@"+window.getPosition(1)+".btn@"+(index+1)+"."+window.getSpmE(),
                            "scm_cnt":scm_cnt
                        })
                    })
                })
            },1500)
        })
        //qt事件3 头部广告topbannernew组件子模块点击
        function topbannernew(dom,val){
            let customerGroupIdModuleHeader='all'
            if($("#customerGroupIdModuleHeader").val()==-1){
                customerGroupIdModuleHeader='all'
            }else{
                customerGroupIdModuleHeader=$("#customerGroupIdModuleHeader").val()
            }
            window.action_sub_module_click({
                    "spm_cnt": "1_4.home_"+$("#pageId").val()+"-"+$("#version").val()+"_0.topbannernew@"+window.getPosition(0)+".img@1."+window.getSpmE(),
                    "scm_cnt": "admin.0."+(customerGroupIdModuleHeader)+"_"+(window.getExhibitionIdStr($("#exhibitionIdStrModuleHeader").val()))+".h5-"+window.getId(val)+"_title-"+  window.keepChineseEnglishAndNumbers($("#textModuleHeader").val())+"."+window.addScmeV2(1),
                })
        }
        //qt事件7   搜索框searcherBox1组件热词子模块点击
        window.actionSearchClick=function(dom,index,url,action){
            function getData(url){
              if(!url){
                return "text-"+ window.keepChineseEnglishAndNumbers(action)+"."
              }else{
               return  "h5-"+window.getId(url)+"_text-"+window.keepChineseEnglishAndNumbers(action)+"."
              }
            }
            window.action_sub_module_click({
                    "spm_cnt": "1_4.home_"+$("#pageId").val()+"-"+$("#version").val()+"_0.searcherBox1@"+window.getPosition(2)+".hotword@"+(Number(1+Number(index)))+"."+window.getSpmE(),
                    "scm_cnt": "admin.0."+('all')+"_"+("0")+"."+getData(url)+window.addScmeV2(1),
            })
        }
        //qt事件27   kuaijie组件子模块点击
         setTimeout(function(){
            $(".abs-warp-tab").each(function(index,item){
                    $(item).on("click",function(){
                        let scm_cnt="admin.0."+('all')+"_0."+$(this).attr("scmdata")+"."+window.addScmeV2(1)
                        window.action_sub_module_click({
                            "spm_cnt": "1_4.home_"+$("#pageId").val()+"-"+$("#version").val()+"<EMAIL>@"+(index+1)+"."+window.getSpmE(),
                            "scm_cnt":scm_cnt
                        })
                    })
                })
            },1500)
}catch(e){}
</script>
</html>

