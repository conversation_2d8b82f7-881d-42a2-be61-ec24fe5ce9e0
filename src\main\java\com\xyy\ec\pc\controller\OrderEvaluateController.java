package com.xyy.ec.pc.controller;

import cn.hutool.json.JSON;
import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderEvaluateBusinessApi;
import com.xyy.ec.order.business.api.OrderEvaluateLabelBusinessApi;
import com.xyy.ec.order.business.dto.MyOrderInfoBusinessDto;
import com.xyy.ec.order.business.dto.OrderEvaluateBusinessDto;
import com.xyy.ec.order.business.dto.OrderEvaluateLabelBusinessDto;
import com.xyy.ec.order.core.dto.OrderEvaluateDto;
import com.xyy.ec.order.core.enums.OrderDetailEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.CDNConfig;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.FileUploadUtil;
import com.xyy.ec.pc.util.SysConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: willow
 * @Date: 2018/9/28 14:08
 * @Description:
 */
@Controller
@RequestMapping("/merchant/center/order/evaluate")
public class OrderEvaluateController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderEvaluateController.class);
    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Reference(version = "1.0.0")
    private OrderEvaluateBusinessApi orderEvaluateBusinessApi;
    @Reference(version = "1.0.0")
    private OrderEvaluateLabelBusinessApi orderEvaluateLabelBusinessApi;

    @Value("${config.product_image_path_url}")
    private String PRODUCT_IMAGE_PATH_URL;


    /**
     * 进入发布评论
     * @Title: detail
     * @date 2016-12-21 下午2:43:15
     */
    @RequestMapping(value = "/save.htm", method = RequestMethod.POST)
    public ModelAndView save(OrderEvaluateBusinessDto orderEvaluate, HttpServletRequest request) {
        try{
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
           // imageUpload(request,orderEvaluate,merchant.getId());
            orderEvaluate.setMerchantId(merchant.getId());
            List<OrderEvaluateLabelBusinessDto> labels=orderEvaluateLabelBusinessApi.getAllLabels();
            Map<Long,String> orderEvaluateLabelMap=new HashMap<>(labels.size());
            for(OrderEvaluateLabelBusinessDto orderEvaluateLabel:labels){
                orderEvaluateLabelMap.put(orderEvaluateLabel.getId(),orderEvaluateLabel.getEvaluateLabelName());
            }
            orderEvaluate.setClientServiceLabel(getLableName(orderEvaluateLabelMap,orderEvaluate.getClientServiceLabelIds()));
            orderEvaluate.setSaleServiceLabel(getLableName(orderEvaluateLabelMap,orderEvaluate.getSaleServiceLabelIds()));
            orderEvaluate.setTransportServiceLabel(getLableName(orderEvaluateLabelMap,orderEvaluate.getTransportServiceLabelIds()));
            orderEvaluate.setBranchCode(merchant.getRegisterCode());
            orderEvaluateBusinessApi.addOrderEvaluate(orderEvaluate);
            return  new ModelAndView(new RedirectView("/merchant/center/order/index.htm",true,false));
            //return this.addResult("发布成功！");
        }catch (Exception e){
            LOGGER.error(e.toString());
           // return this.addError("网络异常,请稍后重试！");
        }
        return  new ModelAndView(new RedirectView("/merchant/center/order/index.htm",true,false));
    }






    /**
     * 获取标签的name
     * @param orderEvaluateLabelMap
     * @param idStr
     * @return
     */
    public String getLableName(Map<Long,String> orderEvaluateLabelMap,String idStr){
        String labelNameStr="";
        if(StringUtils.isNotEmpty(idStr)){
            StringBuffer labelsName=new StringBuffer();
            String []ids=idStr.split(",");
            for(String id:ids){
                labelsName.append(orderEvaluateLabelMap.get(Long.parseLong(id))+"、");
            }
            labelNameStr=labelsName.substring(0,labelsName.length()-1);
        }
        return labelNameStr;
    }

    /**
     * 查询评论
     * @Title: detail
     * @param modelMap
     * @date 2016-12-21 下午2:43:15
     */
    @RequestMapping("/edit/{id}.htm")
    public String edit(@PathVariable Long id, ModelMap modelMap) {
        MyOrderInfoBusinessDto order=findOrder(id);
        modelMap.put("order", order);
        List<OrderEvaluateLabelBusinessDto> labels=orderEvaluateLabelBusinessApi.getAllLabels();
        modelMap.put("labels", labels);
        return "/order/evaluate_edit.ftl";
    }



    /**
     * 查看评论详情
     * @Title: detail
     * @param modelMap
     * @date 2016-12-21 下午2:43:15
     */
    @RequestMapping("/detail/{orderNo}.htm")
    public String detail(@PathVariable String orderNo, Long id, ModelMap modelMap) {
        try {

            LOGGER.error("orderNo:"+orderNo);
            LOGGER.error("id:"+id);

            LOGGER.error("modelMap:"+ modelMap.size());


            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            LOGGER.error("merchant.getId():"+merchant.getId());

            OrderEvaluateBusinessDto orderEvaluate=orderEvaluateBusinessApi.findEvaluateByOrderNoAndMerchantId(orderNo,merchant.getId());
            if(StringUtils.isNotEmpty(orderEvaluate.getClientServiceLabel())){
                orderEvaluate.setClientServiceLabelList(Arrays.asList(orderEvaluate.getClientServiceLabel().split("、")));
            }
            if(StringUtils.isNotEmpty(orderEvaluate.getSaleServiceLabel())){
                orderEvaluate.setSaleServiceLabelList(Arrays.asList(orderEvaluate.getSaleServiceLabel().split("、")));
            }
            if(StringUtils.isNotEmpty(orderEvaluate.getTransportServiceLabel())){
                orderEvaluate.setTransportServiceLabelList(Arrays.asList(orderEvaluate.getTransportServiceLabel().split("、")));
            }
            modelMap.put("orderEvaluate", orderEvaluate);
            String productImageUrl = PRODUCT_IMAGE_PATH_URL;
            modelMap.put("imageUrlPrefix", productImageUrl);
            MyOrderInfoBusinessDto order=orderBusinessApi.selectOrderDetail(merchant.getId(),Long.valueOf(orderNo), OrderDetailEnum.DELIVERING.getId());
            modelMap.put("order", order);
        } catch (Exception e) {
            LOGGER.error("查看评论异常", e);
        }
        return "/order/evaluate_detail.ftl";
    }

    /**
     * 根据订单id 查询
     * @param id
     * @return
     */
    public MyOrderInfoBusinessDto findOrder(Long id){
        MyOrderInfoBusinessDto order=null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order=orderBusinessApi.selectOrderDetail(merchant.getId(),id, OrderDetailEnum.DELIVERING.getId());
            orderBusinessApi.putOrderAppraiseStatus(order);
        } catch (Exception e) {
            LOGGER.error("查看评论异常", e);
        }
        return order;
    }

    /**
     * 图片上传
     * @param request
     * @param merchantId
     * @param
     * @return
     */
    public void imageUpload(HttpServletRequest request, OrderEvaluateDto orderEvaluate, Long merchantId){
        String localTempPath = System.getProperty("xyy-shop");
        CDNConfig cdnConfig = SysConfig.getCDNConfig();
        String uploadPath = "/ybm/order/evaluate/" + merchantId + "/";
        try{
            Map<String, Object> stringObjectMap = FileUploadUtil.imageUpload(request, uploadPath, cdnConfig, null, localTempPath);
            Map<String,List<String>> fileUrls= (Map<String,List<String>>)stringObjectMap.get("fileNames");
            orderEvaluate.setSaleServicePhotoUrl(fileUrls.get("saleImageUrl"));
            orderEvaluate.setTransportServicePhotoUrl(fileUrls.get("transImageUrl"));
            orderEvaluate.setClientServicePhotoUrl(fileUrls.get("clientImageUrl"));
        }catch (Exception e){
            e.printStackTrace();
        }
    }





}
