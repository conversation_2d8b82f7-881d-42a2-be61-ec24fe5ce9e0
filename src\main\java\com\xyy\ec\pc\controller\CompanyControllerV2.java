package com.xyy.ec.pc.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.popshop.service.PopShopService;
import com.xyy.ec.pc.popshop.vo.ShopOpenAccountProcessVo;
import com.xyy.ec.pc.popshop.vo.ShopQualificationVO;
import com.xyy.ec.pc.service.ShopConfuseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 *
 * @Auther: lijincheng
 * @Date: 2021/09/27/15:44
 * @Description:
 */
@RequestMapping("/company/center/companyInfo")
@Controller
@Slf4j
public class CompanyControllerV2 extends BaseController {

    @Autowired
    private PopShopService popShopService;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private ShopConfuseService shopConfuseService;

    @RequestMapping("/getCorporationQualification")
    @ResponseBody
    public Map<String, Object> getCorporationQualification(@NotNull(message = "机构编码不能为空") String orgId){
        try {
            log.info("CompanyControllerV2.getCorporationQualification orgId:{}",orgId);
            if (StringUtils.isEmpty(orgId)){
                return this.errorResult("机构编码不能为空");
            }
            if (BooleanUtils.isTrue(shopConfuseService.isCantShowShop(orgId))) {
                log.warn("CompanyController.getCorporationQualification，获取店铺资质信息，不显示店铺，orgId：{}", orgId);
                return addError(CODE_ERROR, appProperties.getShopConfuseNotShowShopTip());
            }
            Set<String> specifyPopShopLicenseOrgIds = appProperties.getSpecifyPopShopLicenseOrgIds();
            boolean isSpecifyPopShop = CollectionUtils.isNotEmpty(specifyPopShopLicenseOrgIds) && specifyPopShopLicenseOrgIds.contains(orgId);
            if (isSpecifyPopShop) {
                return this.successResult(Lists.newArrayList());
            }
            List<ShopQualificationVO> corporationQualification = popShopService.getCorporationQualification(orgId);
            log.info("CompanyControllerV2.getCorporationQualification result:{}", JSON.toJSONString(corporationQualification));
            return this.successResult(corporationQualification);
        }catch (Exception e){
            log.error("CompanyControllerV2.getCorporationQualification error orgId:{}",orgId,e);
            return this.errorResult("获取店铺资质信息失败");
        }
    }

    @RequestMapping("/getAccountInfo")
    @ResponseBody
    public Map<String, Object> getAccountInfo(@NotNull(message = "机构编码不能为空") String orgId){
        try {
            log.info("CompanyControllerV2.getAccountInfo orgId:{}",orgId);
            if (StringUtils.isEmpty(orgId)){
                return this.errorResult("机构编码不能为空");
            }
            ShopOpenAccountProcessVo clientAccountInfo= popShopService.getClientAccountInfoByOrgId(orgId);
            log.info("CompanyControllerV2.getAccountInfo result:{}", JSON.toJSONString(clientAccountInfo));
            return this.successResult(clientAccountInfo);
        }catch (Exception e){
            log.error("CompanyControllerV2.getAccountInfo error orgId:{}",orgId,e);
            return this.errorResult("获取开户流程信息失败");
        }
    }

    @RequestMapping("/getReturnNotice")
    @ResponseBody
    public Map<String, Object> getReturnNotice(@NotNull(message = "机构编码不能为空") String orgId){
        try {
            log.info("CompanyControllerV2.getReturnNotice orgId:{}",orgId);
            if (StringUtils.isEmpty(orgId)){
                return this.errorResult("机构编码不能为空");
            }
            String returnNotice = popShopService.getReturnNoticeByOrgId(orgId);
            log.info("CompanyControllerV2.getReturnNotice result:{}", JSON.toJSONString(returnNotice));
            return this.successResult(returnNotice);
        }catch (Exception e){
            log.error("CompanyControllerV2.getReturnNotice error orgId:{}",orgId,e);
            return this.errorResult("获取售后配送信息失败");
        }
    }

    @RequestMapping("/licenseDown")
    @ResponseBody
    public Map<String, Object> licenseDown(@NotNull(message = "机构编码不能为空") String orgId,Long contractId){
        try {
            log.info("CompanyControllerV2.licenseDown orgId:{}",orgId);
            if (StringUtils.isEmpty(orgId)){
                return this.errorResult("机构编码不能为空");
            }

            String zipUrl = popShopService.compressionCorporationQualification(orgId, contractId);
            log.info("CompanyControllerV2.licenseDown result:{}", JSON.toJSONString(zipUrl));
            return this.successResult(zipUrl);
        }catch (Exception e){
            log.error("CompanyControllerV2.licenseDown error orgId:{}",orgId,e);
            return this.errorResult("下载资质失败");
        }
    }

    @RequestMapping("/afterSales.htm")
    public ModelAndView afterSales(String orgId){
        Map<String,Object> map = new HashMap<>();
        map.put("orgId",orgId);
        Set<String> specifyPopShopLicenseOrgIds = appProperties.getSpecifyPopShopLicenseOrgIds();
        boolean isSpecifyPopShop = CollectionUtils.isNotEmpty(specifyPopShopLicenseOrgIds) && specifyPopShopLicenseOrgIds.contains(orgId);
        map.put("isSpecifyPopShop", isSpecifyPopShop);
        return new ModelAndView("/company/afterSales.ftl",map);
    }

    public Map<String, Object> successResult(Object o){
        Map<String, Object> responseData = new HashMap<String, Object>();
        responseData.put(CODE, CODE_SUCCESS);
        responseData.put("data",o);
        return responseData;
    }

    public Map<String, Object> errorResult(String message){
        Map<String, Object> responseData = new HashMap<String, Object>();
        responseData.put(CODE, CODE_ERROR);
        responseData.put(RESULT_MSG,message);
        return responseData;
    }
}
