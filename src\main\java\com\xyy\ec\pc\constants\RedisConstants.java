package com.xyy.ec.pc.constants;

public class RedisConstants {
	// jpush用户存储key
	public static final String JPUSHIDKEY = "jpush:id:";
	// jpush设备存储key
	public static final String JPUSHIMEKEY = "jpush:ime:";
	// 分类Map缓存key
	public static final String CATEGORY_MAP = "category:map:";
	// 分类Map缓存key(排除易碎品)
	public static final String CATEGORY_NOYISUI_MAP = "category:noyisui:map:";
	// codeitem字典表缓存key
	public static final String CODEITEM_MAP = "codeitem:map:";
	// 易碎商品69码
	public static final String FRAGILE_PRODUCT_CODE = "fragile:product:code";
	// 小药药根菜单管理key
	public static final String XYY_ROOT_RESOURCE = "xyy:root:resource:";
	// 小药药菜单管理key
	public static final String XYY_RESOURCE = "xyy:resource:";
	// 小药药菜单管理key
	public static final String XYY_REMEMBER_PWD = "xyy:remember:pwd:";
	// 打标商品缓存key
	public static final String PRODUCT_ACTIVITY_PREFIX = "product:activity:";
	// 订单状态和ship_times上次同步时间key
	public static final String SYNCHRONIZED_DATE_KEY = "ordersynchronizedinfo";
	// 小药药销售区域缓存key
	public static final String SALE_BRANCH_KEY = "sale:brach:map";
	// 小药药销售区域和商家关联关系缓存key
	public static final String SALE_COMPANY_BRANCH_KEY = "sale:company:brach:";
	// 行政区域缓存key
	public static final String DIC_AREA_KEY = "dic:area:map";
	// 行政区域缓存key
	public static final String SALE_COMPANY_KEY = "sale:company:map";
	// 小药药客户资质、收货地址数据同步的时间戳key
	public static final String XYY_SYN_DATA_APP_FIND_CLIENT = "XYY_SYN_DATA_APP_FIND_CLIENT";

	// 小药药客户资质、收货地址数据同步的时间戳key
	public static final String XYY_SYN_DATA_SHIPPING_ADDRESS = "XYY_SYN_DATA_SHIPPING_ADDRESS";

	// 订单状态和ship_times上次同步时间key
	public static final String ELASTIC_DATE_KEY = "elasticsynchronizedinfo";

	// 热门搜索的控制开关
	public static final String HOT_SEARCH_KEY = "hotsearchkey";

	//商户branch缓存key
	public static final String BRANCH_CODE = ":BRANCH_CODE";

	// 销售的token的key
	public static final String USER_TOKEN_KEY = "uid:token:";
	// 商户的token的key
	public static final String MERCHANT_TOKEN_KEY = "merchant:token:";

	public static final String PASSWORD_LIMIT_TIME = "PASSWORD_LIMIT_TIME";
	public static final String WEB_USER_TOKEN_KEY = "web:uid:token:";

	public static final String CRM_VALIDATE_CODE = "crm:validate:code";

	public static final String WEB_SERVICE_VALIDATE_CODE = "webservice:validate:code";

	public static final String WEB_SERVICE_VALIDATE_CODE_MAX_TIME = "webservice:validate:code:maxtime";

	// redis中商品对应的价格区间key前缀
	public static final String PRODUCT_RANGE = "PRODUCT_RANGE:";

	// redis中商品对应的秒杀信息key前缀
	public static final String SKU_SECKILL_KYE = "SKU_SECKILL_KYE:";

	// redis中商品对应的促销信息key前缀
	public static final String SKU_PROMOTION_KEY = "SKU_PROMOTION_KEY:";

	// redis中商品对应的限购信息key
	public static final String SKU_LIMITED_KEY = "SKU_LIMITED_KEY";

	//商户资质
	public static final String FIND_CLIENT_KEY = "FIND_CLIENT_KEY:";

	// redis中商品对应的秒杀库存key前缀
	public static final String SKU_SECKILL_STOCK_KEY = "SKU_SECKILL_STOCK_KEY:";

	// redis中商品对应的促销库存key前缀
	public static final String SKU_PROMOTION_STOCK_KEY = "SKU_PROMOTION_STOCK_KEY:";

	//redis 中 商品库存key前缀
	public static final String SKU_STOCK_KEY = "SKU_STOCK_KEY:";

	//redis 中商品信息前缀
	public static final String SKU_CART_KEY = "SKU_CART_KEY:";

	//redis 中客户默认地址前缀
	public static final String DEFAULT_MERCHANT_ADDRESS_KEY = "DEFAULT_MERCHANT_ADDRESS_KEY:";
	//redis 中客户地址前缀
	public static final String MERCHANT_ADDRESS_KEY = "MERCHANT_ADDRESS_KEY:";


	public static final String IS_NEW_MERCHANT_KEY = "IS_NEW_MERCHANT_KEY:";

	//redis 套餐信息
	public static final String PACKAGE_INFO_KEY = "PACKAGE_INFO_KEY:";

	//redis 套餐商品信息
	public static final String PACKAGE_SKU_INFO_KEY = "PACKAGE_SKU_INFO_KEY:";

	//redis 套餐限购信息
	public static final String PACKAGE_LIMIT_KEY = "PACKAGE_LIMIT_KEY:";

	//redis 套餐限购区域信息
	public static final String PACKAGE_LIMIT_AREA_KEY = "PACKAGE_LIMIT_AREA_KEY:";

	/** 商品库存相关key和script */
	public static final String SCRIPT="local size=#KEYS " + "for i=1,size do " + "local n = tonumber(ARGV[i]) " + "if not n  or n == 0 then" + "    return 0 " + "end   " + "local vals = redis.call(\"HMGET\", KEYS[i], \"Total\", \"Booked\");" + "local total = tonumber(vals[1]) " + "local blocked = tonumber(vals[2]) " + "if not total or not blocked then " + " return {2,KEYS[i]}" + "end " + "if blocked + n > total then" + "    return {3,KEYS[i]};   " + "end     " + "end" + "  for j=1,size do " + "local n = tonumber(ARGV[j]) " + "redis.call(\"HINCRBY\", KEYS[j], \"Booked\", n) " + "end " + " return 1";

	/** 普通商品库存key */
	public static final String SKU_STOCK_NORMAL_PREFIX="sku_stock:goodsId:";

	/** 品牌推荐key */
	public  static  final  String PREFERRED_BRAND_KEY="PREFERRED_BRAND:";

	/** 秒杀商品库存key */
	public static final String SKU_STOCK_SECKILL_PREFIX="sku_stock:seckill_group_id:{}:goodsId:{}";

	/** 促销商品库存key */
	public static final String SKU_STOCK_PROMOTION_PREFIX="sku_stock:promotion_id:{}:goodsId:{}";

	/** 库存总数变量key */
	public static final String TOTAL="Total";

	/** 已占用库存变量key */
	public static final String BOOKED="Booked";

	/** 默认预占成功结果 */
	public static final String SUCCESS="1";

	/** 同步标志 */
	public static final String SYNC_FLAG="sync";

	//验证码图片标识
	public static final String VERIF_CODE="VERIF_CODE:";

	//短信验证码发送次数标识
	public static final String VERIF_CODE_SEND_COUNT="VERIF_CODE_SEND_COUNT:";

	/** 订单自增长标识 */
	public static final String INCR_ORDERNO_KEY="INCR_ORDERN_KEY:";

	/**
	 * 获取可用库存的脚本
	 */
	public static final String GET_AVAILABLE_SKU_STOCK_SCRIPT = "local vals=redis.call(\"HMGET\", KEYS[1], \"Total\", \"Booked\"); local total = tonumber(vals[1]); local blocked = tonumber(vals[2]); if not total or not blocked then return 0 end local availableSkuStock=total-blocked; if availableSkuStock<0 then return 0 end return availableSkuStock";


	/**
	 * 更新商品可用库存脚本,用于补偿可用库存
	 */
	public static final String UPDATE_ACAILABLE_SKU_STOCK="local size=#KEYS for j=1,size do  local n = tonumber(ARGV[j]) if not n then return 0 end  local vals=redis.call(\"HMGET\", KEYS[j], \"Booked\") local booked=tonumber(vals[1]) if  booked and booked<n then n=booked end redis.call(\"HINCRBY\", KEYS[j], \"Booked\", -n)  end   return 1";

	/** 指定商品活动缓存key */
	public static final String SKU_PROMO_CACHE_KEY_ZD = "SKU_PROMO_CACHE_KEY_ZD:";// 指定商品
	public static final String SKU_PROMO_CACHE_KEY_QB = "SKU_PROMO_CACHE_KEY_QB:";// 全部商品

	/**
	 * 占位符
	 */
	public static final String PLACE_HOLDER = "\\{\\}";

	public static final String HOTSEARCH_KEY = "HOTSEARCH_KEY:";
	/** 满减等促销 */
	public static final String PROMO_KEY = "PROMO_KEY:";


	public static final String IS_GENERATES_ORDER = "is:generate:order:";

	public static final String ZERO = "0";

	public static final String NUMBER_2 = "2";

	public static final String NUMBER_3 = "3";

	/** 公海code自增key*/
	public static final String OPEN_SEA_CODE = "OPEN_SEA_CODE";

	public static final String ADMIN_USER_AGENT_CACHE_PRE = "ADMIN_UA";
	public static final String ADMIN_USER_AGENT_CACHE_LAST_REQUEST = "ADMIN_UAQ";
	//厂家用户手机号redis key
	public static final String MANUFACTURER_USER_MOBILE = "MANUFACTURER_USER_MOBILE";



	/** 商品分类Redis数据标识 */
	public static final String CATEGORY_TREE_DATA = "CATEGORY_TREE_DATA";



	/** 更新电子计划单选中状态key */
	public static final String UPDATE_PLAN_ORDER_CHECKED = "UPDATE_PLAN_ORDER_CHECKED:";

	/** 首营资质:二审通过时间 */
	public static final String LICENSE_AUDIT_TIME = "license_audit:";

	/** 首营资质:二审通过时间 */
	public static final String LICENSE_EXISTS = "license_exists:";

	/** 訂單分享緩存key */
	public static final String ORDER_SHARE = "order_share:";

	/**控销缓存*/
	public static final String CONTROL_SALES_CACHE_KEY = "CONTROL_SALES_CACHE_KEY:";

	public static final String ACTIVITY_PACKAGE_CACHE_KEY = "ACTIVITY_PACKAGE_CACHE_KEY:";
	/** 下单返券记录*/
	public static final String ORDER_COUPON_CACHE_KEY = "ORDER_COUPON_CACHE_KEY:";

	/** 布局项(图片,子数据等项) */
	public static final String APP_MODULE_DATA = "APP_MODULE_DATA";

	public static final String APP_MODULE_CATEGORY_DATA = "APP_MODULE_CATEGORY_DATA";

	/** 药学院分类标识 */
	public static final String YAO_CATEGORY = "YAO_CATEGORY:";

	/** 药学院分类树标识 */
	public static final String YAO_CATEGORY_TREE = "YAO_CATEGORY_TREE";

	/** 药学院子分类树标识 */
	public static final String YAO_CATEGORY_LIST = "YAO_CATEGORY_LIST:";

	//电子计划单进度
	public static final String PLAN_SCHEDULE_PROGRESS = "PLAN_SCHEDULE_PROGRESS:";

	//电子计划单点击待生成的key
	public static final String PLAN_SCHEDULE_WATING_GENERATE = "PLAN_SCHEDULE_WATING_GENERATE:";


	public static final String PLAN_SCHEDULE_GENERATE_LOCK = "PLAN_SCHEDULE_GENERATE_LOCK:";

	//用户首单key
	public static final String MERCHANT_FIRSTORDER_KEY = "MERCHANT_FIRSTORDER:";


	/**订单在线和线下转账 提前通知的订单快过了超时时间 key **/
	public static final String PRE_REMINDER_ORDER_TIMEOUT = "pre_reminder_order_timeout:";

	/**在线支付，订单超时提前1个小时缓存失效时间，防止重复从数据查询的数据，继续发送订单超时提醒消息*/
	public static final int PRE_REMINDER_EXPIRE_ONLINE=60*60*1;
	/**线下转账，订单超时提前48个小时缓存失效时间，防止重复从数据查询的数据，继续发送订单超时提醒消息*/
	public static final int PRE_REMINDER_EXPIRE_OFFLINE=60*60*48;

	/*优惠券库存*/
	public static final String PRE_VOUCHER_STOCK = "VOUCHER:STOCK:";
	/*用户领券*/
	public static final String PRE_VOUCHER_RECEIVE_LOCK = "VOUCHER:RECEIVE:LOCK:";

	/** 公用湖北商品的其他域属性 */
	public static final String SKU_OTHER_PROPERTY= "sku_other_property:";

	/* 首页加载菜单*/
	public static final String APP_INIT_MODULE_CATEGORY = "APPINITMODULECATEGORY:";

	public static final String PREFERRED_BRAND_SKU =  "PREFERRED_BRAND_SKU:";

	//OEM协议商品key
	public static final String AGREEMENT_SKU_OEM = "AGREEMENT:SKU:BRANCH:";

	//用户签订的OEM协议商品key
	public static final String AGREEMENT_SKU_OEM_SIGNED = "AGREEMENT:SKU:SIGNED:";

	//用户签订的有效OEM协议商品key
	public static final String AGREEMENT_SKU_OEM_EFFECTIVE = "AGREEMENT:SKU:EFFECTIVE:";

	/** 下载防止多次提交 */
	public static final String ORDER_DOWNLOSD_LOCK = "ORDER_downlosd_lock:";
	//批量采购进度
	public static final String MATCH_PROGRESS = "PC_MATCH_PROGRESS:";
	//批量采购进度缓存时间
	public static final int MATCH_PROGRESS_TIME=60*60*1;

	/**
	 * 获取用户批量采购进度的可以
	 * @param merchantId
	 * @return
	 */
	public static String getMatchProgressKey(Long merchantId ){
		return  new StringBuffer().append(PRE_REMINDER_ORDER_TIMEOUT).append(merchantId).toString();
	}

	/***
	 * 获取提前通知的订单快过了超时时间 的key
	 * @param orderNo
	 * @return
	 */
	public static String getReminderTimeOutOrderKey(String orderNo ){
		return  new StringBuffer().append(PRE_REMINDER_ORDER_TIMEOUT).append(orderNo).toString();
	}

	/*发票key*/
	public static final String INVOICE_KEY ="INVOICE_KEY:";

	/*是否查看入库价*/
	public static final String SHOW_TREASURY_PRICES = "SHOW_TREASURY_PRICES:";

	/* 爬虫key*/
	public static final String CRAWLER = "CRAWLER:";

	/** 爬虫用户短信验证码有效时间key **/
	public static final String CRAWLER_CODE="CRAWLER_CODE:";

	/** 登出用户key，PC端 **/
	public static final String KICK_OUT_PC = "KICK_OUT_PC:";

	/** 登出用户短信验证key，PC端 **/
	public static final String KICK_OUT_VERIFY_PC = "KICK_OUT_VERIFY_PC:";

	/** 需要登录的URL KEY  */
	public static final String NEED_LOGIN_URL_KEY = "NEED_LOGIN_URL";

	/**
	 * 金蝶支付短链
	 */
	public static final String KING_DEE_QRCODE_PAY_SHORT_URL = "KING_DEE_QRCODE_PAY_SHORT_URL:";
	public static final int KING_DEE_QRCODE_PAY_SHORT_URL_EXPIRE = 24 * 3600 + 5;
	public static final String PINGAN_ACCOUNT_EXPORT_PRIFIX = "pingan:export:";

}
