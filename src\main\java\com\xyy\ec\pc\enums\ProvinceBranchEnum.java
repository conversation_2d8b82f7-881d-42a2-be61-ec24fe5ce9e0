package com.xyy.ec.pc.enums;

/**
 * 省 域编码关系维护
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/10/26
 */
public enum ProvinceBranchEnum {
    /**
     * 湖北
     */
    HU_BEI("湖北", "XS420000", 420000),
    /**
     * 重庆
     */
    CHONG_QING("重庆", "XS500000", 500000),
    /**
     * 四川
     */
    SI_CHUAN("四川", "XS510000", 510000),
    /**
     * 安徽
     */
    AN_HUI("安徽", "XS340000", 340000),
    /**
     * 浙江
     */
    ZHE_JIANG("浙江", "XS330000", 330000),
    /**
     * 福建
     */
    FU_JIAN("福建", "XS350000", 350000),
    /**
     * 湖南
     */
    HU_NAN("湖南", "XS430000", 430000),
    /**
     * 山东
     */
    SHANDONG("山东", "XS370000", 370000),
    /**
     * 河南
     */
    HE_NAN("河南", "XS410000", 410000),
    /**
     * 江西
     */
    JIANG_XI("江西", "XS360000", 360000),
    /**
     * 山西
     */
//    SHAN_XI("山西","XS140000"),
    SHAN_XI("山西", "XS140001", 140000),
    /**
     * 云南
     */
    YUN_NAN("云南", "XS530000", 530000),
    /**
     * 河北
     **/
    HE_BEI("河北", "XS130000", 130000),
    /**
     * 上海
     **/
    SHANG_HAI("上海", "XS310000", 310000),
    /**
     * 吉林
     **/
    JI_LIN("吉林", "XS220000", 220000),
    /**
     * 江苏
     **/
    JIANG_SU("江苏", "XS320000", 320000),

    /**
     * 香港
     **/
    XIANG_GANG("香港", "XS810000", 810000),

    /**
     * 天津
     **/
    TIAN_JIN("天津", "XS120000", 120000),

    /**
     * 陕西
     **/
    SHAN_XII("陕西子公司", "XS610000", 610000),

    /**
     * 北京
     **/
    BEI_JING("北京", "XS110000", 110000),

    /**
     * 辽宁
     **/
    LIAO_NING("辽宁", "XS210000", 210000),

    /**
     * 水星
     **/
    SHUI_XING("水星", "XS666666", 666666),

    /**
     * 火星
     **/
    HUO_XING("火星", "XS777777", 777777),

    /**
     * 广东
     **/
    GUANG_DONG("广东", "XS440000", 440000),

    /**
     * 广西
     **/
    GUANG_XI("广西", "XS450000", 450000),

    /**
     * 贵州
     **/
    GUI_ZHOU("贵州", "XS520000", 520000),

    /**
     * 黑龙江
     **/
    HEI_LONG_JIANG("黑龙江", "XS230000", 230000),

    /**
     * 内蒙古
     **/
    NEI_MENG_GU("内蒙古", "XS150000", 150000),

    /**
     * 新疆
     **/
    XIN_JIANG("新疆子公司", "XS650000", 650000),
    GAN_SU("甘肃子公司", "XS620000", 620000),
    HAI_NAN("海南子公司", "XS460000", 460000),
    XI_ZANG("西藏子公司", "XS540000", 540000),
    QING_HAI("青海子公司", "XS630000", 630000),
    NING_XIA("宁夏子公司", "XS640000", 640000);

    /**
     * 省市
     */
    private String province;
    /**
     * 省份编码
     */
    private int provinceCode;
    /**
     * 省市区域码
     */
    private String branchCode;

    ProvinceBranchEnum(String province, String branchCode, int provinceCode) {
        this.province = province;
        this.branchCode = branchCode;
        this.provinceCode = provinceCode;
    }

    public String getProvince() {
        return province;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public int getProvinceCode() {
        return provinceCode;
    }

    /**
     * 根据省获取 省域编码枚举
     *
     * @param province
     * @return
     */
    public static ProvinceBranchEnum getEnumByProvince(String province) {
        ProvinceBranchEnum provinceBranchEnum = HU_BEI;
        if (province == null) {
            return provinceBranchEnum;
        }
        switch (province) {
            case "湖北":
                provinceBranchEnum = HU_BEI;
                break;
            case "重庆":
                provinceBranchEnum = CHONG_QING;
                break;
            case "四川":
                provinceBranchEnum = SI_CHUAN;
                break;
            case "安徽":
                provinceBranchEnum = AN_HUI;
                break;
            case "浙江":
                provinceBranchEnum = ZHE_JIANG;
                break;
            case "福建":
                provinceBranchEnum = FU_JIAN;
                break;
            case "湖南":
                provinceBranchEnum = HU_NAN;
                break;
            case "山东":
                provinceBranchEnum = SHANDONG;
                break;
            case "河南":
                provinceBranchEnum = HE_NAN;
                break;
            case "江西":
                provinceBranchEnum = JIANG_XI;
                break;
            case "山西":
                provinceBranchEnum = SHAN_XI;
                break;
            case "云南":
                provinceBranchEnum = YUN_NAN;
                break;
            case "河北":
                provinceBranchEnum = HE_BEI;
                break;
            case "上海":
                provinceBranchEnum = SHANG_HAI;
                break;
            case "吉林":
                provinceBranchEnum = JI_LIN;
                break;
            case "江苏":
                provinceBranchEnum = JIANG_SU;
                break;
            case "香港":
                provinceBranchEnum = XIANG_GANG;
                break;
            case "天津":
                provinceBranchEnum = TIAN_JIN;
                break;
            case "陕西":
                provinceBranchEnum = SHAN_XII;
                break;
            case "北京":
                provinceBranchEnum = BEI_JING;
                break;
            case "辽宁":
                provinceBranchEnum = LIAO_NING;
                break;
            case "水星":
                provinceBranchEnum = SHUI_XING;
                break;
            case "火星":
                provinceBranchEnum = HUO_XING;
                break;
            case "广东":
                provinceBranchEnum = GUANG_DONG;
                break;
            case "广西":
                provinceBranchEnum = GUANG_XI;
                break;
            case "贵州":
                provinceBranchEnum = GUI_ZHOU;
                break;
            case "黑龙江":
                provinceBranchEnum = HEI_LONG_JIANG;
                break;
            case "内蒙古":
                provinceBranchEnum = NEI_MENG_GU;
                break;
            case "新疆":
                provinceBranchEnum = XIN_JIANG;
                break;
            case "甘肃":
                provinceBranchEnum = GAN_SU;
                break;
            case "海南":
                provinceBranchEnum = HAI_NAN;
                break;
            case "西藏":
                provinceBranchEnum = XI_ZANG;
                break;
            case "青海":
                provinceBranchEnum = QING_HAI;
                break;
            case "宁夏":
                provinceBranchEnum = NING_XIA;
                break;
            default:
                provinceBranchEnum = HU_BEI;
                break;
        }
        return provinceBranchEnum;
    }

    /**
     * 根据域编码获取 省域编码枚举
     *
     * @param branchCode
     * @return
     */
    public static ProvinceBranchEnum getEnumByBranchCode(String branchCode) {
        ProvinceBranchEnum provinceBranchEnum = HU_BEI;
        if (branchCode == null) {
            return provinceBranchEnum;
        }
        switch (branchCode) {
            case "XS420000":
                provinceBranchEnum = HU_BEI;
                break;
            case "XS500000":
                provinceBranchEnum = CHONG_QING;
                break;
            case "XS510000":
                provinceBranchEnum = SI_CHUAN;
                break;
            case "XS340000":
                provinceBranchEnum = AN_HUI;
                break;
            case "XS330000":
                provinceBranchEnum = ZHE_JIANG;
                break;
            case "XS350000":
                provinceBranchEnum = FU_JIAN;
                break;
            case "XS430000":
                provinceBranchEnum = HU_NAN;
                break;
            case "XS370000":
                provinceBranchEnum = SHANDONG;
                break;
            case "XS410000":
                provinceBranchEnum = HE_NAN;
                break;
            case "XS360000":
                provinceBranchEnum = JIANG_XI;
                break;
            case "XS140001":
                provinceBranchEnum = SHAN_XI;
                break;
            case "XS530000":
                provinceBranchEnum = YUN_NAN;
                break;
            case "XS130000":
                provinceBranchEnum = HE_BEI;
                break;
            case "XS310000":
                provinceBranchEnum = SHANG_HAI;
                break;
            case "XS220000":
                provinceBranchEnum = JI_LIN;
                break;
            case "XS320000":
                provinceBranchEnum = JIANG_SU;
                break;
            case "XS810000":
                provinceBranchEnum = XIANG_GANG;
                break;
            case "XS120000":
                provinceBranchEnum = TIAN_JIN;
                break;
            case "XS610000":
                provinceBranchEnum = SHAN_XII;
                break;
            case "XS110000":
                provinceBranchEnum = BEI_JING;
                break;
            case "XS210000":
                provinceBranchEnum = LIAO_NING;
                break;
            case "XS666666":
                provinceBranchEnum = SHUI_XING;
                break;
            case "XS777777":
                provinceBranchEnum = HUO_XING;
                break;
            case "XS440000":
                provinceBranchEnum = GUANG_DONG;
                break;
            case "XS450000":
                provinceBranchEnum = GUANG_XI;
                break;
            case "XS520000":
                provinceBranchEnum = GUI_ZHOU;
                break;
            case "XS230000":
                provinceBranchEnum = HEI_LONG_JIANG;
                break;
            case "XS150000":
                provinceBranchEnum = NEI_MENG_GU;
                break;
            case "XS650000":
                provinceBranchEnum = XIN_JIANG;
                break;
            case "XS620000":
                provinceBranchEnum = GAN_SU;
                break;
            case "XS460000":
                provinceBranchEnum = HAI_NAN;
                break;
            case "XS540000":
                provinceBranchEnum = XI_ZANG;
                break;
            case "XS630000":
                provinceBranchEnum = QING_HAI;
                break;
            case "XS640000":
                provinceBranchEnum = NING_XIA;
                break;
            default:
                provinceBranchEnum = HU_BEI;
                break;
        }
        return provinceBranchEnum;
    }
}
