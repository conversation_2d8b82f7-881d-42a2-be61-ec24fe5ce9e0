package com.xyy.ec.pc.common.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.merchant.bussiness.result.MerchantAccountRelatedMerchantInfoDTO;
import com.xyy.ec.pc.controller.vo.merchant.MerchantAccountRelatedMerchantInfoVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class MerchantAccountRelatedMerchantInfoVOHelper {

    public static MerchantAccountRelatedMerchantInfoVO create(MerchantAccountRelatedMerchantInfoDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        return MerchantAccountRelatedMerchantInfoVO.builder()
                .id(dto.getId())
                .merchantId(dto.getMerchantId())
                .status(dto.getStatus())
                .role(dto.getAccountRole())
                .merchantStatus(dto.getMerchantShopAuditStatus())
                .name(dto.getMerchantName())
                .address(dto.getMerchantAddress())
                .province(dto.getMerchantProvince())
                .city(dto.getMerchantCity())
                .district(dto.getMerchantDistrict())
                .street(dto.getMerchantStreet())
                .build();
    }

    public static List<MerchantAccountRelatedMerchantInfoVO> creates(List<MerchantAccountRelatedMerchantInfoDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Lists.newArrayList();
        }
        return dtos.stream().map(MerchantAccountRelatedMerchantInfoVOHelper::create)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}
