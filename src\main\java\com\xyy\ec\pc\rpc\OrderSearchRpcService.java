package com.xyy.ec.pc.rpc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.search.api.remote.dto.OrderSkuStatisticsQueryDto;
import com.xyy.ec.order.search.api.remote.order.OrderSkuStatisticsApi;
import com.xyy.ec.order.search.api.remote.result.OrderSkuBuyRecordResultDto;
import com.xyy.ec.order.search.api.remote.result.OrderSkuStatisticsResultDto;
import com.xyy.ec.pc.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>简介</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022年07月15日 14:34
 */
@Slf4j
@Service
public class OrderSearchRpcService {

    @Reference(version = "1.0.0", timeout = 100, loadbalance = "consistenthash", parameters = {"hash.arguments", "0"})
    private OrderSkuStatisticsApi orderSkuStatisticsApi;

    /**
     * 根据指定条件查询订单统计结果
     * @param queryDto
     * @return
     */
    public List<OrderSkuStatisticsResultDto> orderSkuStatistics(OrderSkuStatisticsQueryDto queryDto){
        try{
            ApiRPCResult<List<OrderSkuStatisticsResultDto>> rpcResult = orderSkuStatisticsApi.orderSkuStatistics(queryDto);
            log.info("orderSkuStatistics req param:{}, resp:{}", JsonUtil.toJson(queryDto), JsonUtil.toJson(rpcResult));
            if(rpcResult.isFail()){
                return Collections.emptyList();
            }
            return rpcResult.getData();
        }catch (Exception e){
            log.info("orderSkuStatistics req param:{}, error:{}", JsonUtil.toJson(queryDto), e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据条件查询商品订单购买记录
     * @param queryDto
     * @return
     */
    public List<OrderSkuBuyRecordResultDto> orderSkuBuyRecordStatistics(OrderSkuStatisticsQueryDto queryDto){
        try{
            ApiRPCResult<List<OrderSkuBuyRecordResultDto>> rpcResult = orderSkuStatisticsApi.orderSkuBuyRecordStatistics(queryDto);
            log.info("##测试环境 orderSkuBuyRecordStatistics req param:{}, resp:{}", JsonUtil.toJson(queryDto), JsonUtil.toJson(rpcResult));
            log.info("orderSkuBuyRecordStatistics req param:{}, resp:{}", JsonUtil.toJson(queryDto), JsonUtil.toJson(rpcResult));
            if(rpcResult.isFail()){
                return Collections.emptyList();
            }
            return rpcResult.getData();
        }catch (Exception e){
            log.info("orderSkuBuyRecordStatistics req param:{}, error:{}", JsonUtil.toJson(queryDto), e);
            return Collections.emptyList();
        }
    }
}
