<!DOCTYPE HTML>
<html>
<head>
    <#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>结算页</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">

    <link rel="stylesheet" href="/static/css/sprout/settle.css?v=${t_v}"  />

    <style>
        .main1{
            background: #F2F2F2;
        }
        .fd-warp .list-item span.spec {
            width: 240px;
        }

        .default_address{
            margin-top:15px;
            color: #999999;
        }
        .isdefaultSec.checkbox-pretty>span{
            position: relative;
        }
        .isdefaultSec.checkbox-pretty>span:before{
            content: "";
            width:15px;
            height:15px;
            display: block;
            border:1px solid #999999;
            border-radius: 2px;
            top:0px;
            left:-20px;
            font-size: 0px;
            margin:0px;
            margin-right:5px;
            position: absolute;
        }
        .isdefaultSec.checkbox-pretty.checked>span:before{
            content: "✓";
            width:17px;
            height:17px;
            display: block;
            border:0px solid black;
            border-radius: 2px;
            text-align: center;
            line-height: 17px;
            top:0px;
            left:-20px;
            background: #00dc82;
            font-size: 14px!important;
            font-weight: 600;
            color: #fff!important;
            margin:0px;
            margin-right:5px;
            position: absolute;
        }
        .sui-tooltip{
            border:1px solid #eee;
            border-radius:8px;
        }
        .sui-tooltip.bottom .tooltip-arrow, .tooltip-only-arrow.bottom .tooltip-arrow{
            border-bottom-color: #eee;
        }
        .tooltip-inner{
            line-height: 25px;
        }
        .sui-tooltip.default .tooltip-inner, .sui-tooltip.normal .tooltip-inner, .sui-tooltip.confirm .tooltip-inner{
            color:#333;
        }
        .cg-yhq {
            position: absolute;
            display: none;
            top:60px;
            width:900px;
            background: #FFFFFF;
            border-radius: 5px;
            z-index: 1008;
            box-shadow:0px 2px 12px 0px rgba(0,0,0,0.06);
            border:1px solid rgba(238,238,238,1);
        }
        .cg-yhq .address-top-title {
            margin-top: 21px;
            margin-left: 19px;
        }
        .cg-yhq ul{
            padding-left:10px;
        }
        .yhq-common {
            overflow: hidden;
        }
        .yhq-common li {
            margin: 10px 0 10px 10px;
            height: 120px;
        }
        .yhq-san {
            position: absolute;
            top: -10px;
            left: 50%;
            line-height: 0px;
            font-size: 40px;
            z-index: -1;
            width: 15px;
            height: 15px;
            margin-left: -15px;
            background: #fff;
            border:1px solid rgba(238,238,238,1);
            border-bottom: 1px solid #fff;
            border-right: 1px solid #fff;
            transform: rotate(45deg);
        }
        .tanbox{
            max-height: 300px;
            min-height: 140px;
            overflow-y:scroll;
        }
        .tanbox::-webkit-scrollbar {
            width: 2px;
        }
        .tanbox::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            /*-webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);*/
        }
        .defaultH{
            max-height:705px;
            overflow:hidden;
        }
        .shop-item{
            background:#ffffff;
        }
        .xyy-confirm{
            display:none;
            background:#fff;
            position:absolute;
            top:50%;
            left:50%;
            width:300px;
            height:150px;
            margin-left:-150px;
            margin-top:-75px;
            z-index:9999;
        }
        .xyy-confirm .xyy-header{
            height: 30px;
            line-height: 30px;
            padding: 0 15px;
            background: #00dc82;
            color:#fff;
        }
        .xyy-confirm .xyy-footer{
            position: absolute;
            width: 100%;
            bottom: 0;
            height: 40px;
            line-height: 40px;
            border-top: 1px solid #f5f5f5;
            text-align:right;
        }

        .xyy-confirm .xyy-footer button{
            padding:0 10px;
            height:30px;
            line-height:30px;
            border:none;
            margin:0 5px;
            outline:none;
        }

        .xyy-confirm .xyy-footer button.ok-btn{
            color: #fff;
            background: #00dc82;
        }

        .xyy-confirm .xyy-footer button.cancel-btn{
            color: #666;
            background: #fff;
            border: 1px solid #f5f5f5;
        }

        .xyy-confirm .xyy-body{
            height: 59px;
            padding: 10px;
        }

        .xyy-cover{
            display:none;
            position:fixed;
            background:#222;
            width:100%;
            height:100%;
            left:0;
            top:0;
            opacity: 0.5;
            filter: alpha(opacity=50);
            z-index:9998;
        }
        .kuaidi{
            margin-left: 60px;
            margin-top: 10px;
            color: #e73734;
            font-size: 12px;
        }
        .tuijian{
            /*margin-left: 750px;*/
            color:#fff;
            padding:5px 10px;
            border-radius:5px;
            background:#00dc7d;
            display:inline-block;
            cursor: pointer;
            position: absolute;
            right: 40px;
        }
        /*优惠券弹窗*/
        .youhuiquan-sum .cg-yhq{
            position: absolute;
            display: none;
            top: 50px;
            left:-619px;
            width: 900px;
            background: #FFFFFF;
            border-radius: 5px;
            z-index: 1008;
            box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.06);
            border: 1px solid rgba(238,238,238,1);
        }
        .youhuiquan-sum .yhq-san {
            position: absolute;
            top: -10px;
            left: 677px;
            line-height: 0px;
            font-size: 40px;
            z-index: -1;
            width: 15px;
            height: 15px;
            margin-left: -15px;
            background: #fff;
            border:1px solid rgba(238,238,238,1);
            border-bottom: 1px solid #fff;
            border-right: 1px solid #fff;
            transform: rotate(45deg);
        }
    </style>

    <script type="text/javascript" src="/static/js/jquery.cityselect.js?t=${t_v}" ></script>
    <script type="text/javascript" src="/static/js/sprout/settle20200326.js?v=${t_v}" ></script>
    <script type="text/javascript">
        var ctx="${ctx}";
        var shoppingCartImgUUID = "${shoppingCartImgUUID}";
        $('.xiala-all').click(function(){
            $(this).parent(".shop-item").find(".cg-yhq").slideDown();
        })
        $('.body').click(function(){
            $(".cg-yhq").slideUp();
        })
    </script>

</head>

<body>
<div class="container">
    <input type="hidden" id="divmore" value="0">
    <input type="hidden" id="tranNo" value="${tranNo}">
    <input type="hidden" id="purchaseNo" value="${purchaseNo}">
    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
        <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->

    <!--结算页面头部导航-->
    <div class="topbzbox">
        <div class="warp">
            <div class="bzcol1"><a href="/"><img src="/static/images/logo_login.png" ></a></div>
            <div class="bzcol2"></div>
            <div class="bzcol3">结算</div>
            <div class="bzcol4"><img src="/static/images/buzhoujiesuan.png" ></div>
        </div>
    </div>

    <!--主体部分开始-->
    <div class="main1">
        <!--提示文案-->
        <div class="titleTips" style=" width: 1200px; margin: 10px auto;">
            <div class="sui-msg msg-default msg-notice" style="position: relative;">
                <i class="sui-icon icon-touch-noti-circle" style="position: absolute;top:10px;left:10px;font-size:22px;"></i>
                <div class="msg-con" style="padding:10px 40px;background: #FFFDF4;color: #FBA475">声明：为严格执行《药品管理法》及《药品经营质量管理规范》的相关规定，送货地址默认为《药品经营许可证》中的经营地址或仓库地址，并不能随意修改。若地址有误，请联系客服：400-0505-111</div>
            </div>
        </div>
        <!--支付主体内容-->
        <div class="pay-content" style="margin: 0px auto;padding: 10px 25px 0 25px;">
            <!--填写并核对订单信息-->
            <#--<div class="p-title">填写并核对订单信息：</div>-->
            <!--收货人信息-->
            <div class="recebox">
                <div class="address-top-title">收货人信息</div>
                <!--地址列表-->
                <div class="default-show">
                    <#if (shippingAddressList?size>0)>
                        <#list shippingAddressList as shippingAddress>
                            <div class="gspadres clear">
                                <input type="hidden" hi="id" value="${shippingAddress.id }"/>
                                <input type="hidden" hi="remark" value="${shippingAddress.remark }"/>
                                <input type="hidden" hi="auditState" value="${shippingAddress.auditState }"/>
                                <input type="hidden" hi="isdefault" value="${shippingAddress.isdefault!"" }" class="isdefault"/>
                                <label class="adres-row adres-row-new radio-pretty inline <#if shippingAddress.isdefault>checked </#if>">
                                    <input type="radio" <#if shippingAddress.isdefault>checked="checked" </#if> name="radio"><span></span>
                                </label>
                                <span class="adres-row adres-row1 text-overflow" data-id="contactor">${shippingAddress.contactor }</span>
                                <span class="adres-row adres-row2">
									<p class="main-ades text-overflow" data-id="fullAddress" style="white-space: unset;">${shippingAddress.fullAddress }</p>
								</span>
                                <span class="adres-row adres-row3 text-overflow" data-id="mobile">
                                    <span class="mobile">${shippingAddress.mobile }</span>
                                    <#if shippingAddress.isdefault>
                                        <span class="def-biaoqian">
                                            <span class="default">默认</span>
                                        </span>
                                    </#if>
                                </span>
                                <span class="adres-row adres-row4">
									<a href="javascript:void(0)" class="res-btn" data-toggle="modal" onclick="editAddress(this)" data-keyboard="false" data-target="#editModal"><i class="sui-icon icon-touch-edit-rect"></i>编辑</a>
								</span>
                            </div>
                        </#list>
                    </#if>
                </div>
            </div>
            <!--支付方式-->
            <div class="zffs">
                <div class="address-top-title">选择支付方式</div>
                <ul>
                    <li class="cur" t="1">在线支付</li>
                    <li t="3">线下转账</li>
                    <#if shoppingCartInfo.isShow==1>
                        <li t="2">货到付款</li>
                    </#if>

                </ul>
                <#if shoppingCartInfo.isShow==1>
                    <div class="hdfk-box">
                        <span>${shoppingCartInfo.offlineMessage}</span>
                    </div>
                </#if>
                <input type="hidden" id="payTips" value="${payTips}"/>
            </div>
        </div>

        <!--送货清单-->
        <div class="shqdtitle">送货清单</div>
        <div class="shqd">
            <!--默认显示-->
            <div>
                <!--列表模式-->
                <div class="listmode">
                    <!--表头-->
                    <div class="headbox">
                        <ul>
                            <li class="li1">
                                <span>商品信息</span>
                            </li>
                            <li class="li_mingxi"><span class="head-tit">明细</span></li>
                            <li class="li3">单价</li>
                            <li class="li5">数量</li>
                            <li class="li4">金额</li>
                        </ul>
                    </div>
                    <div class="list-content">
                        <#if orderSettle.companys ?? && (orderSettle.companys?size >0)>
                            <#list orderSettle.companys as company>
                                <#if company.isThirdCompany == 0>
                                    <div class="list-ziying">
                                        <!--自营公司名称 -->
                                        <div class="cgd-qy">
                                            <span class="ziying">自营</span>
                                            <span class="qy-title">${company.companyName}</span>
                                        </div>
                                        <!-- 遍历店铺数据判断 -->
                                        <#if company.shops ?? && (company.shops?size >0) >
                                            <!--遍历店铺数据开始-->
                                            <#list company.shops as shop>
                                                <#if shop.shopPatternCode=='ybm'>
                                                    <input type="hidden" id="ybmTotalAmount" value="${shop.totalAmount}"/>
                                                    <input type="hidden" id="ybmPromoTotalAmt" value="${shop.promoTotalAmt}"/>
                                                </#if>
                                                <div class="shop-item">
                                                    <div class="defaultbox">
                                                        <!--店铺结构开始-->
                                                        <#assign showCartItemNum = 0 />
                                                        <div class="pro-box">
                                                            <!--店铺名称-->
                                                            <div class="cgd-qy cgd-qy1">
                                                                <span class="qy-title">${shop.shopName}</span>
                                                            </div>
                                                            <!--遍历分组数据判段-->
                                                            <#if shop.groups ?? && (shop.groups?size >0) >
                                                                <!--遍历分组数据开始-->
                                                                <#list shop.groups as shoppingCartGroup>
                                                                    <!--常规分组名称-->
                                                                    <#if shoppingCartGroup.type == 9>
                                                                        <div class="taocanbox-onther">
                                                                            <#--<span class="new">其它商品：</span>-->
                                                                        </div>
                                                                    </#if>
                                                                    <!--活动分组名称-->
                                                                    <#if (shoppingCartGroup.type!=10 && shoppingCartGroup.type!=9)>
                                                                        <div class="manjianbox" name="${shoppingCartGroup.id}" >
                                                                            <#if shoppingCartGroup.title??>
                                                                                <span class="title"><#if shoppingCartGroup.type==1>满减<#elseif shoppingCartGroup.type==2>满折<#elseif shoppingCartGroup.type==3>满赠<#elseif shoppingCartGroup.type==4>满减赠<#elseif shoppingCartGroup.type==6>一口价</#if></span>
                                                                                <span class="info">${shoppingCartGroup.title}</span>
                                                                            </#if>
                                                                            <#if shoppingCartGroup.type==5>
                                                                                <input type="radio" name="giftId" style="margin-left: 17px;margin-right: 5px;" value="${shoppingCartGroup.id}" <#if shoppingCartGroup.selectStatus==1>checked="true" data-mutex-check="true"</#if> <#if shoppingCartGroup.selectStatus==0> data-mutex-check="false"</#if> >
                                                                                <span class="title_hui">物料心愿单礼包</span>
                                                                                <span class="info_hui" value="${shoppingCartGroup.id}">不需要</span>
                                                                                <input type="hidden" name="giftIds" value="${shoppingCartGroup.id}"/>
                                                                                <input type="hidden" name="giftTotalAmount" value="${shoppingCartGroup.totalAmount}"/>
                                                                            </#if>
                                                                        </div>
                                                                    </#if>
                                                                    <!--遍历分组商品-->
                                                                    <div class="list-default-box defaultH">
                                                                        <#if shoppingCartGroup.sorted ?? && (shoppingCartGroup.sorted?size >0) >
                                                                            <!--遍历分组商品开始-->
                                                                            <#list shoppingCartGroup.sorted as shoppingCartItem>
                                                                                <#assign showCartItemNum = showCartItemNum + 1 />
                                                                                <!--套餐商品开始-->
                                                                                <#if shoppingCartItem.itemType=3>
                                                                                    <!--套餐商品标题-->
                                                                                    <div class="taocanbox">
                                                                                        <span class="new">套餐商品</span>
                                                                                    </div>
                                                                                    <div class="bodybox taocanspe">
                                                                                        <!---套餐商品列表开始-->
                                                                                        <#if shoppingCartItem.subItemList ?? && (shoppingCartItem.subItemList?size >0) >
                                                                                            <#list shoppingCartItem.subItemList as tcList>
                                                                                                <ul>
                                                                                                    <li class="lib1">
                                                                                                        <div class="l-box fl">
                                                                                                            <a href="/search/skuDetail/${tcList.id}.htm" target="_blank" title="${tcList.commonName }"><img src="${productImageUrl}/ybm/product/min/${tcList.imageUrl }" alt="${tcList.commonName}" onerror="this.src='/static/images/default-big.png'"></a>
                                                                                                            <!--标签-->
                                                                                                            <#if shoppingCartItem.item.valid=0>
                                                                                                                <div class="bq-box">
                                                                                                                    <img src="img/bq-qiangguang.png" alt="">
                                                                                                                </div>
                                                                                                            </#if>
                                                                                                            <#if tcList.blackSku=1>
                                                                                                                <!--不参与返点提示-->
                                                                                                                <#if (tcList.blackSkuText??) && (tcList.blackSkuText!="")>
                                                                                                                    <div class="nofd">
                                                                                                                        ${tcList.blackSkuText}
                                                                                                                    </div>
                                                                                                                </#if>
                                                                                                            </#if>
                                                                                                        </div>
                                                                                                        <div class="r-box fr">
                                                                                                            <div class="lib1-row1 text-overflow">
                                                                                                                <a href="/search/skuDetail/${tcList.id}.htm" target="_blank" title="${tcList.commonName }">${tcList.sku.showName}</a>
                                                                                                            </div>
                                                                                                            <div class="lib1-row3">
                                                                                                                <div class="row-biaoqian" id="${tcList.uniqueKey}row-biaoqian">
                                                                                                                    <#if tcList.tagList ?? && (tcList.tagList?size >0) >
                                                                                                                        <#list tcList.tagList as item >
                                                                                                                            <#if (item_index < 3)>
                                                                                                                                <span class="<#if item.uiType == 1>linqi</#if>
                                                                                                                <#if item.uiType == 2>quan</#if>
                                                                                                                <#if item.uiType == 3>manjian</#if>
                                                                                                                <#if item.uiType == 4>default</#if>
                                                                                                                <#if item.uiType == 5>yibao</#if>
                                                                                                                ">${item.name}</span>
                                                                                                                            </#if>
                                                                                                                        </#list>
                                                                                                                    </#if>
                                                                                                                </div>
                                                                                                            </div>
                                                                                                            <div class="lib1-row2 text-overflow">
                                                                                                                <span class="title">规　　格：</span>
                                                                                                                <span class="info">${tcList.sku.spec}</span>
                                                                                                            </div>
                                                                                                            <div class="lib1-row4 text-overflow">
                                                                                                                <span class="title">生产厂家：</span>
                                                                                                                <span class="info">${tcList.sku.manufacturer}</span>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </li>
                                                                                                    <li class="li_mingxi" id="${tcList.uniqueKey}">
                                                                                                        <div class="mxrow1">
                                                                                                            <span class="mxrow_tit">实付金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}realPayAmount">￥${tcList.realPayAmount}</span>
                                                                                                            <span class="mxrow_tit">优惠金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}discountAmount">￥${tcList.discountAmount}</span>
                                                                                                        </div>
                                                                                                        <div class="mxrow2">
                                                                                                            <span class="mxrow_tit">余额抵扣：</span><span class="mxrow_info" id="${tcList.uniqueKey}useBalanceAmount">￥${tcList.useBalanceAmount}</span>
                                                                                                            <span class="mxrow_tit">返点金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}balanceAmount">￥${tcList.balanceAmount}</span>
                                                                                                        </div>
                                                                                                        <div class="mxrow2">
                                                                                                            <span class="mxrow_tit">实付价：</span>
                                                                                                            <span class="mxrow_info ${tcList.uniqueKey}purchasePriceK">￥<#if 1 == tcList.sku.isGive >${tcList.sku.fob?string('0.00')}<#elseif tcList.purchasePrice ?? >${tcList.purchasePrice?string('0.00')}<#else>0.00</#if></span>
                                                                                                            <span class="mxrow_tit">成本价：</span><span class="mxrow_info  ${tcList.uniqueKey}costPriceK">￥<#if 1 == tcList.sku.isGive >${tcList.sku.fob?string('0.00')}<#elseif tcList.costPrice ?? >${tcList.costPrice?string('0.00')}<#else>0.00</#if></span>
                                                                                                        </div>
                                                                                                    </li>
                                                                                                    <li class="lib3">
                                                                                                        <div class="zkj">￥${tcList.sku.fob}</div>
                                                                                                        <div class="sjj"><span>￥${tcList.sku.retailPrice}</span></div>
                                                                                                    </li>
                                                                                                    <li class="lib5">
                                                                                                        <span>x${tcList.amount}</span>
                                                                                                    </li>
                                                                                                    <li class="lib4"></li>
                                                                                                </ul>
                                                                                            </#list>
                                                                                            <!--套餐小计-->
                                                                                            <div class="taocanxj">
                                                                                                <div class="zkj">￥${shoppingCartItem.item.subtotal}</div>
                                                                                            </div>
                                                                                        </#if>
                                                                                    </div>
                                                                                    <!---套餐商品列表结束-->
                                                                                </#if>
                                                                                <!--套餐商品结束-->
                                                                                <!--非套餐商品开始-->
                                                                                <#if shoppingCartItem.itemType!=3>
                                                                                    <div class="bodybox <#if shoppingCartGroup.type==5>bigGift${shoppingCartGroup.id} </#if>" >
                                                                                        <ul>
                                                                                            <li class="lib1">
                                                                                                <div class="l-box fl">
                                                                                                    <a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm" target="_blank" title="${shoppingCartItem.item.sku.showName }">
                                                                                                        <img src="${productImageUrl}/ybm/product/min/${shoppingCartItem.item.sku.imageUrl }" alt="${shoppingCartItem.item.sku.showName}" onerror="this.src='/static/images/default-big.png'">
                                                                                                        <div class="bq-box">
                                                                                                            <#if shoppingCartItem.item.sku.status==2>
                                                                                                                <img src="/static/images/product/bq-shouqing.png" alt="">
                                                                                                            </#if>
                                                                                                            <#if shoppingCartItem.item.sku.status==4  && shoppingCartItem.item.sku.isGive==0>
                                                                                                                <img src="/static/images/product/bq-xiajia.png" alt="">
                                                                                                            </#if>
                                                                                                        </div>
                                                                                                        <#if shoppingCartItem.item.blackSku=1>
                                                                                                            <!--不参与返点提示-->
                                                                                                            <#if (shoppingCartItem.item.blackSkuText??) && (shoppingCartItem.item.blackSkuText!="")>
                                                                                                                <div class="nofd">
                                                                                                                    ${shoppingCartItem.item.blackSkuText}
                                                                                                                </div>
                                                                                                            </#if>
                                                                                                        </#if>
                                                                                                    </a>
                                                                                                </div>
                                                                                                <div class="r-box fr">
                                                                                                    <div class="lib1-row1 text-overflow">
                                                                                        <span>
                                                                                            <a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm" target="_blank" title="${shoppingCartItem.item.sku.showName }">
                                                                                           <#if shoppingCartItem.item.isShow806 || shoppingCartItem.item.gift>
                                                                                               <div class="bq806">
                                                                                                    <img src="/static/images/bq806.png" alt="">
                                                                                                </div>
                                                                                           </#if>
                                                                                                <#if shoppingCartItem.item.agent == 1><span class="dujia">独家</span></#if>${shoppingCartItem.item.sku.showName }
                                                                                            </a>
                                                                                        </span>
                                                                                                    </div>
                                                                                                    <div class="lib1-row3">
                                                                                                        <div class="row-biaoqian" id="${shoppingCartItem.item.uniqueKey}row-biaoqian">
                                                                                                            <#if shoppingCartItem.item.tagList ?? && (shoppingCartItem.item.tagList?size >0) >
                                                                                                                <#list shoppingCartItem.item.tagList as item >
                                                                                                                    <#if (item_index < 3)>
                                                                                                                        <span class="<#if item.uiType == 1>linqi</#if>
                                                                                                         <#if item.uiType == 2>quan</#if>
                                                                                                         <#if item.uiType == 3>manjian</#if>
                                                                                                         <#if item.uiType == 4>default</#if>
                                                                                                         <#if item.uiType == 5>yibao</#if>
                                                                                                         ">${item.name}</span>
                                                                                                                    </#if>
                                                                                                                </#list>
                                                                                                            </#if>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div class="lib1-row5">
                                                                                                        <div class="row-last">
                                                                                                            <#if (shoppingCartItem.item.sku.suggestPrice ?? ) && (shoppingCartItem.item.sku.suggestPrice != '')>
                                                                                                                <div class="kongxiao-box">
                                                                                                                    <span class="s-kx">零售价</span><span class="jg">￥${shoppingCartItem.item.sku.suggestPrice}</span>
                                                                                                                </div>
                                                                                                            </#if>
                                                                                                            <#if shoppingCartItem.item.sku.grossMargin ?? && shoppingCartItem.item.sku.grossMargin !=''>
                                                                                                                <div class="maoli-box">
                                                                                                                    <span class="s-ml">毛利</span><span class="jg">${shoppingCartItem.item.sku.grossMargin}</span>
                                                                                                                </div>
                                                                                                            </#if>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div class="lib1-row2 text-overflow">
                                                                                                        <span class="title">规　　格：</span>
                                                                                                        <span class="info">${shoppingCartItem.item.sku.spec }</span>
                                                                                                    </div>
                                                                                                    <div class="lib1-row4 text-overflow">
                                                                                                        <span class="title">生产厂家：</span>
                                                                                                        <span class="info">${shoppingCartItem.item.sku.manufacturer }</span>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </li>
                                                                                            <li class="li_mingxi">
                                                                                                <div class="mxrow1">
                                                                                                    <span class="mxrow_tit">实付金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}realPayAmount">￥${shoppingCartItem.item.realPayAmount}</span>
                                                                                                    <span class="mxrow_tit">优惠金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}discountAmount">￥${shoppingCartItem.item.discountAmount}</span>
                                                                                                </div>
                                                                                                <div class="mxrow2">
                                                                                                    <span class="mxrow_tit">余额抵扣：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}useBalanceAmount">￥${shoppingCartItem.item.useBalanceAmount}</span>
                                                                                                    <span class="mxrow_tit">返点金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}balanceAmount">￥${shoppingCartItem.item.balanceAmount}</span>
                                                                                                </div>
                                                                                                <div class="mxrow2">
                                                                                                    <span class="mxrow_tit">实付价：</span><span class="mxrow_info ${shoppingCartItem.item.uniqueKey}purchasePrice">￥<#if 1 == shoppingCartItem.item.sku.isGive >${shoppingCartItem.item.price?string('0.00')}<#elseif shoppingCartItem.item.purchasePrice ?? >${shoppingCartItem.item.purchasePrice?string('0.00')}<#else>0.00</#if></span>
                                                                                                    <span class="mxrow_tit">成本价：</span><span class="mxrow_info  ${shoppingCartItem.item.uniqueKey}costPrice">￥<#if 1 == shoppingCartItem.item.sku.isGive >${shoppingCartItem.item.price?string('0.00')}<#elseif shoppingCartItem.item.costPrice ?? >${shoppingCartItem.item.costPrice?string('0.00')}<#else>0.00</#if></span>
                                                                                                </div>
                                                                                            </li>
                                                                                            <li class="lib3">
                                                                                                <div class="zkj">￥${shoppingCartItem.item.price }
                                                                                                </div>
                                                                                                <div class="sjj"><span>￥${shoppingCartItem.item.sku.retailPrice }</span></div>
                                                                                            </li>
                                                                                            <li class="lib5">
                                                                                                <span>x${shoppingCartItem.item.amount }</span>
                                                                                            </li>
                                                                                            <li class="lib4">
                                                                                                <div class="zkj">￥${shoppingCartItem.item.subtotal }</div>
                                                                                            </li>
                                                                                        </ul>
                                                                                    </div>
                                                                                </#if>
                                                                                <!--非套餐商品结束-->
                                                                            </#list>
                                                                            <!--遍历分组商品结束-->
                                                                        </#if>
                                                                    </div>
                                                                    <!--遍历分组商品判定结束-->
                                                                </#list>
                                                                <!--遍历分组数据结束-->
                                                            </#if>
                                                            <!--遍历分组数据判定结束-->
                                                        </div>
                                                        <!--店铺结构结束-->
                                                        <!--查看更多-->
                                                        <!--默认页面展示5行，更多数据通过点击查看更多-->
                                                        <#if (showCartItemNum > 5)>
                                                            <a href="javaScript:void(0); " class="more">点击展开 <i class="sui-icon icon-tb-unfold "></i> </a>
                                                        </#if>
                                                        <!--收起-->
                                                        <a href="javaScript:void(0); " class="no-more">点击收起 <i class="sui-icon icon-tb-fold"></i> </a>
                                                        <!--店铺优惠券-->
                                                        <#--<div class="youhuiquan" style="overflow: visible;position:relative;border-bottom: 1px dashed #e6e6e6;padding-left: 40px;padding-bottom:15px;">-->
                                                            <#--<span style="display: inline-block;margin-right: 30px;">优惠券</span>-->
                                                            <#--<span style="width:200px;display: inline-block;margin-right: 80px;color:#FF0000;font-size:12px;" id="${shop.shopCode}voucherTip">${shop.voucherTip}</span>-->
                                                            <#--<a href="javascript:;" class="xiala-all">-->
                                                                <#--<span style="font-size: 12px;">查看全部优惠券</span><img style="margin-left:5px;" src="/static/images/xiala.png" alt="">-->
                                                            <#--</a>-->
                                                            <#--<div class="cg-yhq">-->
                                                                <#--<div class="yhq-san"></div>-->
                                                                <#--<div class="address-top-title">-->
                                                                    <#--<div class="yhq-l-box">-->
                                                                        <#--<a href="javascript:;" class="ky-yhq cur">可用优惠券</a>-->
                                                                        <#--<a href="javascript:;" class="bky-yhq ">不可用优惠券</a>-->
                                                                    <#--</div>-->
                                                                <#--</div>-->
                                                                <#--<#if shop.shopPatternCode == 'ybm'>-->
                                                                    <#--<!--可用优惠券开始&ndash;&gt;-->
                                                                    <#--<#assign availItemNum = 0 />-->
                                                                    <#--<ul class="yhq-common weishiyong ky tanbox">-->
                                                                        <#--<#if (shop.availDjVoucherList ?? && shop.availDjVoucherList?size>0)>-->
                                                                            <#--<#assign availItemNum = availItemNum + 1 />-->
                                                                            <#--<!--叠加券&ndash;&gt;-->
                                                                            <#--<li <#if (shop.selectDjVoucherList ?? && shop.selectDjVoucherList?size>0)>class="cur"</#if>>-->
                                                                                <#--<input type="hidden" shopCode="${shop.shopCode}" shopPatternCode="${shop.shopPatternCode}" name="voucherId" value="1"/>-->
                                                                                <#--<input type="hidden" name="voucherMoney" value="${shop.availDjAmount }"/>-->
                                                                                <#--<div class="yhq-lb">-->
                                                                                    <#--<div class="yhq-lb-top">-->
                                                                                        <#--<span class="fuhao">￥</span><span class="price">${shop.availDjAmount}</span>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div class="yhq-lb-foot">-->
                                                                                        <#--&lt;#&ndash;满200元使用&ndash;&gt;无门槛-->
                                                                                    <#--</div>-->
                                                                                <#--</div>-->
                                                                                <#--<div class="yhq-rb">-->
                                                                                    <#--<div class="yhq-rb-top">-->
                                                                                        <#--<span class="quan quan-die">叠加券</span><span class="info" title="${voucherDemo}">${voucherDemo}</span>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div style="height:30px;overflow:hidden;"></div>-->
                                                                                    <#--<div class="yhq-rb-foot">-->
                                                                                        <#--<span>可与其他优惠券叠加</span>-->
                                                                                        <#--<a href="javascript:;" class="ck" style="float:right;margin-right:10px" data-toggle="modal"  data-keyboard="false" data-target="#yhqModal">查看</a>-->
                                                                                    <#--</div>-->
                                                                                <#--</div>-->
                                                                                <#--<div class="yhq-checkb">-->
                                                                                    <#--<label class="checkbox-pretty inline <#if (shop.selectDjVoucherList ?? && shop.selectDjVoucherList?size>0)>checked</#if>" type="6">-->
                                                                                        <#--<input type="checkbox" ><span></span>-->
                                                                                    <#--</label>-->
                                                                                <#--</div>-->
                                                                            <#--</li>-->
                                                                        <#--</#if>-->
                                                                        <#--<!--遍历可用-非叠加券&ndash;&gt;-->
                                                                        <#--<#if (shop.availVoucherList ?? && shop.availVoucherList?size>0)>-->
                                                                            <#--<#list shop.availVoucherList as voucher>-->
                                                                                <#--<#assign availItemNum = availItemNum + 1 />-->
                                                                                <#--<li class="<#if (voucher.isUse==1)>cur </#if><#if voucher.isUse==0>ygq </#if><#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">-->
                                                                                    <#--<input type="hidden" shopCode="${shop.shopCode}" shopPatternCode="${shop.shopPatternCode}" name="voucherId" value="${voucher.id }"/>-->
                                                                                    <#--<input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>-->
                                                                                    <#--<div class="yhq-lb">-->
                                                                                        <#--<div class="yhq-lb-top">-->
                                                                                            <#--<#if voucher.voucherState==1>-->
                                                                                                <#--<span class="price">${voucher.discountRatio }</span>-->
                                                                                                <#--<span class="fuhao">折</span>-->
                                                                                            <#--<#elseif voucher.voucherUsageWay==1>-->
                                                                                                <#--<span class="fuhao">￥</span><span class="price">${voucher.sourceMoneyInVoucher }</span>-->
                                                                                            <#--<#else>-->
                                                                                                <#--<span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>-->
                                                                                            <#--</#if>-->
                                                                                        <#--</div>-->
                                                                                        <#--<div class="yhq-lb-foot">-->
                                                                                            <#--${voucher.minMoneyToEnableDesc }-->
                                                                                        <#--</div>-->
                                                                                        <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
                                                                                            <#--<div class="yhq-lb-foot">-->
                                                                                                <#--${voucher.maxMoneyInVoucherDesc }-->
                                                                                            <#--</div>-->
                                                                                        <#--</#if>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div class="yhq-rb">-->
                                                                                        <#--<div class="yhq-rb-top">-->
                                                                                            <#--<#if voucher.voucherType == 2>-->
                                                                                                <#--<span class="quan">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 1>-->
                                                                                                <#--<span class="quan quan-tong">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 6>-->
                                                                                                <#--<span class="quan quan-die">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 5>-->
                                                                                                <#--<span class="quan quan-xin">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 7>-->
                                                                                                <#--<span class="quan quan-shop">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<span class="info" title="${voucher.shopName}">${voucher.shopName}</span>-->

                                                                                        <#--</div>-->
                                                                                        <#--<div style="height:30px;overflow:hidden;line-height:30px;">${voucher.voucherDesc }</div>-->
                                                                                        <#--<div class="yhq-rb-foot">-->
                                                                                            <#--<span>${voucher.voucherScope}</span>-->
                                                                                        <#--</div>-->
                                                                                        <#--<div style="font-size:12px;color:#999999;">${voucher.validDate?string("yyyy/MM/dd")}-${voucher.expireDate?string("yyyy/MM/dd")}</div>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div class="yhq-checkb">-->
                                                                                        <#--<label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>"">-->
                                                                                        <#--<input type="checkbox" <#if voucher.isUse==0>disabled=""</#if> ><span></span>-->
                                                                                        <#--</label>-->
                                                                                    <#--</div>-->
                                                                                <#--</li>-->
                                                                            <#--</#list>-->
                                                                        <#--</#if>-->
                                                                    <#--</ul>-->
                                                                    <#--<!--可用优惠券结束&ndash;&gt;-->
                                                                    <#--<!--不可用优惠券开始&ndash;&gt;-->
                                                                    <#--<ul class="yhq-common weishiyong bky tanbox">-->
                                                                        <#--<#assign unavailItemNum = 0 />-->
                                                                        <#--<!--叠加券&ndash;&gt;-->
                                                                        <#--<#if (shop.unavailDjVoucherList ?? &&shop.unavailDjVoucherList?size>0)>-->
                                                                            <#--<#assign unavailItemNum = unavailItemNum + 1 />-->
                                                                            <#--<li>-->
                                                                                <#--<div class="yhq-lb">-->
                                                                                    <#--<div class="yhq-lb-top">-->
                                                                                        <#--<span class="fuhao">￥</span><span class="price">${shop.unavailDjAmount}</span>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div class="yhq-lb-foot">-->
                                                                                        <#--&lt;#&ndash;满200元使用&ndash;&gt;无门槛-->
                                                                                    <#--</div>-->
                                                                                <#--</div>-->
                                                                                <#--<div class="yhq-rb">-->
                                                                                    <#--<div class="yhq-rb-top">-->
                                                                                        <#--<span class="quan quan-die">叠加券</span><span class="info"></span>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div style="height:30px;overflow:hidden;"></div>-->
                                                                                    <#--<div class="yhq-rb-foot">-->
                                                                                        <#--<span>可与其他优惠券叠加</span>-->
                                                                                        <#--<a href="javascript:;" class="ck" style="float:right;margin-right:10px" data-toggle="modal"  data-keyboard="false" data-target="#unyhqModal">查看</a>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div style="font-size:12px;color:#999999;">${voucher.validDate?string("yyyy/MM/dd")}-${voucher.expireDate?string("yyyy/MM/dd")}</div>-->
                                                                                <#--</div>-->
                                                                            <#--</li>-->
                                                                        <#--</#if>-->
                                                                        <#--<!--遍历不可用-非叠加券&ndash;&gt;-->
                                                                        <#--<#if (shop.unavailVoucherList ?? && shop.unavailVoucherList?size>0)>-->
                                                                            <#--<#list shop.unavailVoucherList as voucher>-->
                                                                                <#--<#assign unavailItemNum = unavailItemNum + 1 />-->
                                                                                <#--<li class="<#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">-->
                                                                                    <#--<input type="hidden"  name="voucherId" value="${voucher.id }"/>-->
                                                                                    <#--<input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>-->
                                                                                    <#--<div class="yhq-lb">-->
                                                                                        <#--<div class="yhq-lb-top">-->
                                                                                            <#--<#if voucher.voucherState==1>-->
                                                                                                <#--<span class="price">${voucher.discountRatio }</span>-->
                                                                                                <#--<span class="fuhao">折</span>-->
                                                                                            <#--<#elseif voucher.voucherUsageWay==1>-->
                                                                                                <#--<span class="fuhao">￥</span><span class="price">${voucher.sourceMoneyInVoucher }</span>-->
                                                                                            <#--<#else>-->
                                                                                                <#--<span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>-->
                                                                                            <#--</#if>-->
                                                                                        <#--</div>-->
                                                                                        <#--<div class="yhq-lb-foot">-->
                                                                                            <#--${voucher.minMoneyToEnableDesc }-->
                                                                                        <#--</div>-->
                                                                                        <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
                                                                                            <#--<div class="yhq-lb-foot">-->
                                                                                                <#--${voucher.maxMoneyInVoucherDesc }-->
                                                                                            <#--</div>-->
                                                                                        <#--</#if>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div class="yhq-rb">-->
                                                                                        <#--<div class="yhq-rb-top">-->
                                                                                            <#--<#if voucher.voucherType == 2>-->
                                                                                                <#--<span class="quan">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 1>-->
                                                                                                <#--<span class="quan quan-tong">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 6>-->
                                                                                                <#--<span class="quan quan-die">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 5>-->
                                                                                                <#--<span class="quan quan-xin">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 7>-->
                                                                                                <#--<span class="quan quan-shop">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<span class="info" title="${voucher.shopName}">${voucher.shopName}</span>-->
                                                                                        <#--</div>-->
                                                                                        <#--<div style="height:30px;overflow:hidden;line-height:30px;">${voucher.voucherDesc }</div>-->
                                                                                        <#--<div class="yhq-rb-foot">-->
                                                                                            <#--<span>${voucher.voucherScope }</span>-->
                                                                                        <#--</div>-->
                                                                                        <#--<div style="font-size:12px;color:#999999;">${voucher.validDate?string("yyyy/MM/dd")}-${voucher.expireDate?string("yyyy/MM/dd")}</div>-->
                                                                                    <#--</div>-->
                                                                                <#--</li>-->
                                                                            <#--</#list>-->
                                                                        <#--</#if>-->
                                                                    <#--</ul>-->
                                                                    <#--<!--不可用优惠券结束&ndash;&gt;-->
                                                                <#--</#if>-->
                                                                <#--<#if shop.shopPatternCode != 'ybm'>-->
                                                                    <#--<!--可用优惠券开始&ndash;&gt;-->
                                                                    <#--<#assign availItemNum = 0 />-->
                                                                    <#--<ul class="yhq-common weishiyong ky tanbox">-->
                                                                        <#--<!--遍历可用-非叠加券&ndash;&gt;-->
                                                                        <#--<#if (shop.shopSelectVouchers ?? && shop.shopSelectVouchers?size>0)>-->
                                                                            <#--<#list shop.shopSelectVouchers as voucher>-->
                                                                                <#--<#assign availItemNum = availItemNum + 1 />-->
                                                                                <#--<li class="<#if (voucher.isUse==1)>cur </#if><#if voucher.isUse==0>ygq </#if><#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">-->
                                                                                    <#--<input type="hidden" shopCode="${shop.shopCode}" shopPatternCode="${shop.shopPatternCode}" name="voucherId" value="${voucher.id }"/>-->
                                                                                    <#--<input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>-->
                                                                                    <#--<div class="yhq-lb">-->
                                                                                        <#--<div class="yhq-lb-top">-->
                                                                                            <#--<#if voucher.voucherState==1>-->
                                                                                                <#--<span class="price">${voucher.discountRatio }</span>-->
                                                                                                <#--<span class="fuhao">折</span>-->
                                                                                            <#--<#elseif voucher.voucherUsageWay==1>-->
                                                                                                <#--<span class="fuhao">￥</span><span class="price">${voucher.sourceMoneyInVoucher }</span>-->
                                                                                            <#--<#else>-->
                                                                                                <#--<span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>-->
                                                                                            <#--</#if>-->
                                                                                        <#--</div>-->
                                                                                        <#--<div class="yhq-lb-foot">-->
                                                                                            <#--${voucher.minMoneyToEnableDesc }-->
                                                                                        <#--</div>-->
                                                                                        <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
                                                                                            <#--<div class="yhq-lb-foot">-->
                                                                                                <#--${voucher.maxMoneyInVoucherDesc }-->
                                                                                            <#--</div>-->
                                                                                        <#--</#if>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div class="yhq-rb">-->
                                                                                        <#--<div class="yhq-rb-top">-->
                                                                                            <#--<#if voucher.voucherType == 2>-->
                                                                                                <#--<span class="quan">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 1>-->
                                                                                                <#--<span class="quan quan-tong">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 6>-->
                                                                                                <#--<span class="quan quan-die">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 5>-->
                                                                                                <#--<span class="quan quan-xin">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 7>-->
                                                                                                <#--<span class="quan quan-shop">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<span class="info" title="${voucher.shopName}">${voucher.shopName}</span>-->

                                                                                        <#--</div>-->
                                                                                        <#--<div style="height:30px;overflow:hidden;line-height:30px;">${voucher.voucherDesc }</div>-->
                                                                                        <#--<div class="yhq-rb-foot">-->
                                                                                            <#--<span>${voucher.voucherScope }</span>-->
                                                                                        <#--</div>-->
                                                                                        <#--<div style="font-size:12px;color:#999999;">${voucher.validDate?string("yyyy/MM/dd")}-${voucher.expireDate?string("yyyy/MM/dd")}</div>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div class="yhq-checkb">-->
                                                                                        <#--<label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>"">-->
                                                                                        <#--<input type="checkbox" <#if voucher.isUse==0>disabled=""</#if> ><span></span>-->
                                                                                        <#--</label>-->
                                                                                    <#--</div>-->
                                                                                <#--</li>-->
                                                                            <#--</#list>-->
                                                                        <#--</#if>-->
                                                                    <#--</ul>-->
                                                                    <#--<!--可用优惠券结束&ndash;&gt;-->
                                                                    <#--<!--不可用优惠券开始&ndash;&gt;-->
                                                                    <#--<ul class="yhq-common weishiyong bky tanbox">-->
                                                                        <#--<#assign unavailItemNum = 0 />-->
                                                                        <#--<!--遍历不可用-非叠加券&ndash;&gt;-->
                                                                        <#--<#if (shop.shopUnSelectVouchers ?? && shop.shopUnSelectVouchers?size>0)>-->
                                                                            <#--<#list shop.shopUnSelectVouchers as voucher>-->
                                                                                <#--<#assign unavailItemNum = unavailItemNum + 1 />-->
                                                                                <#--<li class="<#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">-->
                                                                                    <#--<input type="hidden" name="voucherId" value="${voucher.id }"/>-->
                                                                                    <#--<input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>-->
                                                                                    <#--<div class="yhq-lb">-->
                                                                                        <#--<div class="yhq-lb-top">-->
                                                                                            <#--<#if voucher.voucherState==1>-->
                                                                                                <#--<span class="price">${voucher.discountRatio }</span>-->
                                                                                                <#--<span class="fuhao">折</span>-->
                                                                                            <#--<#elseif voucher.voucherUsageWay==1>-->
                                                                                                <#--<span class="fuhao">￥</span><span class="price">${voucher.sourceMoneyInVoucher }</span>-->
                                                                                            <#--<#else>-->
                                                                                                <#--<span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>-->
                                                                                            <#--</#if>-->
                                                                                        <#--</div>-->
                                                                                        <#--<div class="yhq-lb-foot">-->
                                                                                            <#--${voucher.minMoneyToEnableDesc }-->
                                                                                        <#--</div>-->
                                                                                        <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
                                                                                            <#--<div class="yhq-lb-foot">-->
                                                                                                <#--${voucher.maxMoneyInVoucherDesc }-->
                                                                                            <#--</div>-->
                                                                                        <#--</#if>-->
                                                                                    <#--</div>-->
                                                                                    <#--<div class="yhq-rb">-->
                                                                                        <#--<div class="yhq-rb-top">-->
                                                                                            <#--<#if voucher.voucherType == 2>-->
                                                                                                <#--<span class="quan">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 1>-->
                                                                                                <#--<span class="quan quan-tong">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 6>-->
                                                                                                <#--<span class="quan quan-die">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 5>-->
                                                                                                <#--<span class="quan quan-xin">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<#if voucher.voucherType == 7>-->
                                                                                                <#--<span class="quan quan-shop">${voucher.voucherTypeDesc}</span>-->
                                                                                            <#--</#if>-->
                                                                                            <#--<span class="info" title="${voucher.shopName}">${voucher.shopName}</span>-->
                                                                                        <#--</div>-->
                                                                                        <#--<div style="height:30px;overflow:hidden;line-height:30px;">${voucher.voucherDesc }</div>-->
                                                                                        <#--<div class="yhq-rb-foot">-->
                                                                                            <#--<span>${voucher.voucherScope }</span>-->
                                                                                        <#--</div>-->
                                                                                        <#--<div style="font-size:12px;color:#999999;">${voucher.validDate?string("yyyy/MM/dd")}-${voucher.expireDate?string("yyyy/MM/dd")}</div>-->
                                                                                    <#--</div>-->
                                                                                <#--</li>-->
                                                                            <#--</#list>-->
                                                                        <#--</#if>-->
                                                                    <#--</ul>-->
                                                                    <#--<!--不可用优惠券结束&ndash;&gt;-->
                                                                <#--</#if>-->
                                                            <#--</div>-->
                                                        <#--</div>-->
                                                        <#if  (company.shops?size > 1)>
                                                            <!--店铺小计-->
                                                            <div style="background:#ffffff;overflow: hidden;margin-bottom: 8px;">
                                                                <div class="totalbox fr" style="display: inline-block;width:400px;clear:none;">
                                                                    <div class="rbox-total fr">
                                                                        <div class="fd-warp">
                                                                            <div class="right-fd-box">
                                                                                <div class="list-item">
                                                                                    <span class="spec">活动优惠:</span><span class="red">-￥</span><span class="red" id="${shop.shopCode}promoTotalAmt">${shop.promoTotalAmt?string('0.00')}</span>
                                                                                </div>
                                                                                <div class="list-item">
                                                                                    <span class="spec">优惠券:</span><span class="red">-￥</span><span class="red" id="${shop.shopCode}voucherTotalAmt">${shop.voucherTotalAmt?string('0.00')}</span>
                                                                                </div>
                                                                                <div class="list-item">
                                                                                    <span class="spec" style="font-size:16px;font-weight:500;">小计:</span><span style="font-size:16px;font-weight:500;">￥</span><span style="font-size:16px;font-weight:500;" name="balancespan"  id="${shop.shopCode}payAmount">${shop.payAmount?string('0.00')}</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </#if>
                                                    </div>
                                                </div>
                                            </#list>
                                            <!--遍历店铺数据结束-->
                                        </#if>
                                        <!-- 遍历店铺数据判断 -->
                                    </div>
                                    <!--店铺备注留言-->
                                    <div style="margin-top:8px;margin-bottom:8px;overflow: hidden;background:#ffffff;padding-left: 40px;">
                                        <div class="bzly fl" style="border:none;padding-bottom: 0;">
                                            <div class="address-top-title" style="display: inline-block;">备注留言</div>
                                            <input type="text" class="companyRemark" company = '${company.companyCode}' placeholder="选填，请先和商家协商一致">
                                            <!-- <div class="kuaidi">提示：抱歉，平台目前不支持指定快递。</div> -->
                                        </div>
                                        <!--总计-->
                                        <div class="totalbox fr" style="display: inline-block;width:460px;clear:none;">
                                            <div class="rbox-total fr">
                                                <div class="spzjbox">
                                                    共<span class="zhongshu" id="${company.companyCode}productVarietyNum">${company.productVarietyNum}</span>种商品，总件数<span class="zongjianshu" id="${company.companyCode}productTotalNum">${company.productTotalNum}</span>商品总计：￥<span id="id="${company.companyCode}totalAmount"" >${company.totalAmount?string('0.00')}</span>
                                                </div>
                                                <div class="fd-warp">
                                                    <!--中间竖线-->
                                                    <#--<div class="mid-line"></div>-->
                                                    <div class="right-fd-box">
                                                        <#--<div class="list-item">-->
                                                            <#--<span class="spec">运费:</span><span>￥</span><span id="${company.companyCode}freightTotalAmt">${company.freightTotalAmt?string('0.00')}</span>-->
                                                        <#--</div>-->
                                                        <div class="list-item">
                                                            <span class="spec" style="width:370px;">
                                                                <#if company.freightTipsShowStatus ?? && company.freightTipsShowStatus == 1>
                                                                    <p id="freightTipsP" style="font-size:12px;color:#666666;display: inline-block;background: rgba(255,243,206,0.3);border-radius: 1px;padding:2px 5px;">
                                                                        实付金额满<span class="red">${company.freeDeliveryAmount}</span>元包邮，还需凑<span class="red">${company.freeFreightDiffAmount}元
                                                                        <#if company.freightUrlText?? && company.freightUrlText != ''>
                                                                            <a href="/merchant/freight/list.htm?route=settle" style="color:#e73734;padding-left:10px;">${company.freightUrlText}></a>
                                                                        </#if>
                                                                    </p>
                                                                </#if>
                                                                <#if company.freightIconShowStatus?? && company.freightIconShowStatus == 1>
                                                                    <a id="freightTipsA" href="javascript:void(0)" class="fplx-a get-fei"  data-keyboard="false"><img src="/static/images/yunfei-tip.png" alt="" style="top: -2px;position: relative;width: 14px;"></a>
                                                                </#if>

                                                                运费:</span><span class="red">￥</span><span class="red" id="${company.companyCode}freightTotalAmt">${company.freightTotalAmt?string('0.00')}</span>
                                                        </div>
                                                        <div class="list-item">
                                                            <span class="spec" style="width:370px;">活动优惠:</span><span class="red">-￥</span><span class="red" id="${company.companyCode}promoTotalAmt">${company.promoTotalAmt?string('0.00')}</span>
                                                        </div>
                                                        <div class="list-item">
                                                            <span class="spec" style="width:370px;">优惠券:</span><span class="red">-￥</span><span class="red" id="${company.companyCode}voucherTotalAmt">${company.voucherTotalAmt?string('0.00')}</span>
                                                        </div>
                                                        <div class="list-item">
                                                            <span class="spec" style="font-size:16px;font-weight:500;width:335px;">总计:</span><span style="font-size:16px;font-weight:500;">￥</span><span style="font-size:16px;font-weight:500;" name="balancespan"  id="${company.companyCode}payAmount">${company.payAmount?string('0.00')}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--小药药自营公司结束-->
                                </#if>
                                <#if company.isThirdCompany == 1>
                                    <!--POP公司开始-->
                                    <div class="list-ziying">
                                        <!--自营公司名称 -->
                                        <div class="cgd-qy">
                                            <img src="/static/images/pop-shop-icon.png" alt="" style="margin-right: 3px;top: -1px;position: relative;width: 18px;">
                                            <span class="qy-title">${company.companyName}</span>
                                        </div>
                                        <!-- 遍历店铺数据判断 -->
                                        <#if company.shops ?? && (company.shops?size >0) >
                                            <!--遍历店铺数据开始-->
                                            <#list company.shops as shop>
                                                <!--店铺结构开始-->
                                                <#assign showCartItemNum = 0 />
                                                <div class="pro-box">
                                                    <!--POP没有店铺名称-->
                                                    <#--<div class="cgd-qy cgd-qy1">-->
                                                        <#--<span class="qy-title">${shop.shopName}</span>-->
                                                    <#--</div>-->
                                                    <!--遍历分组数据判段-->
                                                    <#if shop.groups ?? && (shop.groups?size >0) >
                                                        <!--遍历分组数据开始-->
                                                        <#list shop.groups as shoppingCartGroup>
                                                            <!--常规分组名称-->
                                                            <#if shoppingCartGroup.type == 9>
                                                                <div class="taocanbox-onther">
                                                                    <#--<span class="new">其它商品：</span>-->
                                                                </div>
                                                            </#if>
                                                            <!--活动分组名称-->
                                                            <#if (shoppingCartGroup.type!=10 && shoppingCartGroup.type!=9)>
                                                                <div class="manjianbox" name="${shoppingCartGroup.id}" >
                                                                    <#if shoppingCartGroup.title??>
                                                                        <span class="title"><#if shoppingCartGroup.type==1>满减<#elseif shoppingCartGroup.type==2>满折<#elseif shoppingCartGroup.type==3>满赠<#elseif shoppingCartGroup.type==4>满减赠<#elseif shoppingCartGroup.type==6>一口价</#if></span>
                                                                        <span class="info">${shoppingCartGroup.title}</span>
                                                                    </#if>
                                                                    <#if shoppingCartGroup.type==5>
                                                                        <input type="radio" name="giftId" style="margin-left: 17px;margin-right: 5px;" value="${shoppingCartGroup.id}" <#if shoppingCartGroup.selectStatus==1>checked="true" data-mutex-check="true"</#if> <#if shoppingCartGroup.selectStatus==0> data-mutex-check="false"</#if> >
                                                                        <span class="title_hui">物料心愿单礼包</span>
                                                                        <span class="info_hui" value="${shoppingCartGroup.id}">不需要</span>
                                                                        <input type="hidden" name="giftIds" value="${shoppingCartGroup.id}"/>
                                                                        <input type="hidden" name="giftTotalAmount" value="${shoppingCartGroup.totalAmount}"/>
                                                                    </#if>
                                                                </div>
                                                            </#if>
                                                            <!--遍历分组商品-->
                                                            <div class="list-default-box defaultH">
                                                                <#if shoppingCartGroup.sorted ?? && (shoppingCartGroup.sorted?size >0) >
                                                                    <!--遍历分组商品开始-->
                                                                    <#list shoppingCartGroup.sorted as shoppingCartItem>
                                                                        <#assign showCartItemNum = showCartItemNum + 1 />
                                                                        <!--套餐商品开始-->
                                                                        <#if shoppingCartItem.itemType=3>
                                                                            <!--套餐商品标题-->
                                                                            <div class="taocanbox">
                                                                                <span class="new">套餐商品</span>
                                                                            </div>
                                                                            <div class="bodybox taocanspe">
                                                                                <!---套餐商品列表开始-->
                                                                                <#if shoppingCartItem.subItemList ?? && (shoppingCartItem.subItemList?size >0) >
                                                                                    <#list shoppingCartItem.subItemList as tcList>
                                                                                        <ul>
                                                                                            <li class="lib1">
                                                                                                <div class="l-box fl">
                                                                                                    <a href="/search/skuDetail/${tcList.id}.htm" target="_blank" title="${tcList.commonName }"><img src="${productImageUrl}/ybm/product/min/${tcList.imageUrl }" alt="${tcList.commonName}" onerror="this.src='/static/images/default-big.png'"></a>
                                                                                                    <!--标签-->
                                                                                                    <#if shoppingCartItem.item.valid=0>
                                                                                                        <div class="bq-box">
                                                                                                            <img src="img/bq-qiangguang.png" alt="">
                                                                                                        </div>
                                                                                                    </#if>
                                                                                                    <#if tcList.blackSku=1>
                                                                                                        <!--不参与返点提示-->
                                                                                                        <#if (tcList.blackSkuText??) && (tcList.blackSkuText!="")>
                                                                                                            <div class="nofd">
                                                                                                                ${tcList.blackSkuText}
                                                                                                            </div>
                                                                                                        </#if>
                                                                                                    </#if>
                                                                                                </div>
                                                                                                <div class="r-box fr">
                                                                                                    <div class="lib1-row1 text-overflow">
                                                                                                        <a href="/search/skuDetail/${tcList.id}.htm" target="_blank" title="${tcList.commonName }">${tcList.sku.showName}</a>
                                                                                                    </div>
                                                                                                    <div class="lib1-row3">
                                                                                                        <div class="row-biaoqian" id="${tcList.uniqueKey}row-biaoqian">
                                                                                                            <#if tcList.tagList ?? && (tcList.tagList?size >0) >
                                                                                                                <#list tcList.tagList as item >
                                                                                                                    <#if (item_index < 3)>
                                                                                                                        <span class="<#if item.uiType == 1>linqi</#if>
                                                                                                            <#if item.uiType == 2>quan</#if>
                                                                                                            <#if item.uiType == 3>manjian</#if>
                                                                                                            <#if item.uiType == 4>default</#if>
                                                                                                            <#if item.uiType == 5>yibao</#if>
                                                                                                            ">${item.name}</span>
                                                                                                                    </#if>
                                                                                                                </#list>
                                                                                                            </#if>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div class="lib1-row2 text-overflow">
                                                                                                        <span class="title">规　　格：</span>
                                                                                                        <span class="info">${tcList.sku.spec}</span>
                                                                                                    </div>
                                                                                                    <div class="lib1-row4 text-overflow">
                                                                                                        <span class="title">生产厂家：</span>
                                                                                                        <span class="info">${tcList.sku.manufacturer}</span>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </li>
                                                                                            <li class="li_mingxi" id="${tcList.uniqueKey}">
                                                                                                <div class="mxrow1">
                                                                                                    <span class="mxrow_tit">实付金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}realPayAmount">￥${tcList.realPayAmount}</span>
                                                                                                    <span class="mxrow_tit">优惠金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}discountAmount">￥${tcList.discountAmount}</span>
                                                                                                </div>
                                                                                                <div class="mxrow2">
                                                                                                    <span class="mxrow_tit">余额抵扣：</span><span class="mxrow_info" id="${tcList.uniqueKey}useBalanceAmount">￥${tcList.useBalanceAmount}</span>
                                                                                                    <span class="mxrow_tit">返点金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}balanceAmount">￥${tcList.balanceAmount}</span>
                                                                                                </div>
                                                                                                <div class="mxrow2">
                                                                                                    <span class="mxrow_tit">实付价：</span>
                                                                                                    <span class="mxrow_info ${tcList.uniqueKey}purchasePriceK">￥<#if 1 == tcList.sku.isGive >${tcList.sku.fob?string('0.00')}<#elseif tcList.purchasePrice ?? >${tcList.purchasePrice?string('0.00')}<#else>0.00</#if></span>
                                                                                                    <span class="mxrow_tit">成本价：</span><span class="mxrow_info  ${tcList.uniqueKey}costPriceK">￥<#if 1 == tcList.sku.isGive >${tcList.sku.fob?string('0.00')}<#elseif tcList.costPrice ?? >${tcList.costPrice?string('0.00')}<#else>0.00</#if></span>
                                                                                                </div>
                                                                                            </li>
                                                                                            <li class="lib3">
                                                                                                <div class="zkj">￥${tcList.sku.fob}</div>
                                                                                                <div class="sjj"><span>￥${tcList.sku.retailPrice}</span></div>
                                                                                            </li>
                                                                                            <li class="lib5">
                                                                                                <span>x${tcList.amount}</span>
                                                                                            </li>
                                                                                            <li class="lib4"></li>
                                                                                        </ul>
                                                                                    </#list>
                                                                                    <!--套餐小计-->
                                                                                    <div class="taocanxj">
                                                                                        <div class="zkj">￥${shoppingCartItem.item.subtotal}</div>
                                                                                    </div>
                                                                                </#if>
                                                                            </div>
                                                                            <!---套餐商品列表结束-->
                                                                        </#if>
                                                                        <!--套餐商品结束-->
                                                                        <!--非套餐商品开始-->
                                                                        <#if shoppingCartItem.itemType!=3>
                                                                            <div class="bodybox <#if shoppingCartGroup.type==5>bigGift${shoppingCartGroup.id} </#if>" >
                                                                                <ul>
                                                                                    <li class="lib1">
                                                                                        <div class="l-box fl">
                                                                                            <a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm" target="_blank" title="${shoppingCartItem.item.sku.showName }">
                                                                                                <img src="${productImageUrl}/ybm/product/min/${shoppingCartItem.item.sku.imageUrl }" alt="${shoppingCartItem.item.sku.showName}" onerror="this.src='/static/images/default-big.png'">
                                                                                                <div class="bq-box">
                                                                                                    <#if shoppingCartItem.item.sku.status==2>
                                                                                                        <img src="/static/images/product/bq-shouqing.png" alt="">
                                                                                                    </#if>
                                                                                                    <#if shoppingCartItem.item.sku.status==4  && shoppingCartItem.item.sku.isGive==0>
                                                                                                        <img src="/static/images/product/bq-xiajia.png" alt="">
                                                                                                    </#if>
                                                                                                </div>
                                                                                                <#if shoppingCartItem.item.blackSku=1>
                                                                                                    <!--不参与返点提示-->
                                                                                                    <#if (shoppingCartItem.item.blackSkuText??) && (shoppingCartItem.item.blackSkuText!="")>
                                                                                                        <div class="nofd">
                                                                                                            ${shoppingCartItem.item.blackSkuText}
                                                                                                        </div>
                                                                                                    </#if>
                                                                                                </#if>
                                                                                            </a>
                                                                                        </div>
                                                                                        <div class="r-box fr">
                                                                                            <div class="lib1-row1 text-overflow">
                                                                                                    <span>
                                                                                                        <a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm" target="_blank" title="${shoppingCartItem.item.sku.showName }">
                                                                                                       <#if shoppingCartItem.item.isShow806 || shoppingCartItem.item.gift>
                                                                                                           <div class="bq806">
                                                                                                                <img src="/static/images/bq806.png" alt="">
                                                                                                            </div>
                                                                                                       </#if>
                                                                                                            <#if shoppingCartItem.item.agent == 1><span class="dujia">独家</span></#if>${shoppingCartItem.item.sku.showName }
                                                                                                        </a>
                                                                                                    </span>
                                                                                            </div>
                                                                                            <div class="lib1-row3">
                                                                                                <div class="row-biaoqian" id="${shoppingCartItem.item.uniqueKey}row-biaoqian">
                                                                                                    <#if shoppingCartItem.item.tagList ?? && (shoppingCartItem.item.tagList?size >0) >
                                                                                                        <#list shoppingCartItem.item.tagList as item >
                                                                                                            <#if (item_index < 3)>
                                                                                                                <span class="<#if item.uiType == 1>linqi</#if>
                                                                                                                     <#if item.uiType == 2>quan</#if>
                                                                                                                     <#if item.uiType == 3>manjian</#if>
                                                                                                                     <#if item.uiType == 4>default</#if>
                                                                                                                     <#if item.uiType == 5>yibao</#if>
                                                                                                                     ">${item.name}</span>
                                                                                                            </#if>
                                                                                                        </#list>
                                                                                                    </#if>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="lib1-row5">
                                                                                                <div class="row-last">
                                                                                                    <#if (shoppingCartItem.item.sku.suggestPrice ?? ) && (shoppingCartItem.item.sku.suggestPrice != '')>
                                                                                                        <div class="kongxiao-box">
                                                                                                            <span class="s-kx">零售价</span><span class="jg">￥${shoppingCartItem.item.sku.suggestPrice}</span>
                                                                                                        </div>
                                                                                                    </#if>
                                                                                                    <#if shoppingCartItem.item.sku.grossMargin ?? && shoppingCartItem.item.sku.grossMargin !=''>
                                                                                                        <div class="maoli-box">
                                                                                                            <span class="s-ml">毛利</span><span class="jg">${shoppingCartItem.item.sku.grossMargin}</span>
                                                                                                        </div>
                                                                                                    </#if>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="lib1-row2 text-overflow">
                                                                                                <span class="title">规　　格：</span>
                                                                                                <span class="info">${shoppingCartItem.item.sku.spec }</span>
                                                                                            </div>
                                                                                            <div class="lib1-row4 text-overflow">
                                                                                                <span class="title">生产厂家：</span>
                                                                                                <span class="info">${shoppingCartItem.item.sku.manufacturer }</span>
                                                                                            </div>
                                                                                        </div>
                                                                                    </li>
                                                                                    <li class="li_mingxi">
                                                                                        <div class="mxrow1">
                                                                                            <span class="mxrow_tit">实付金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}realPayAmount">￥${shoppingCartItem.item.realPayAmount}</span>
                                                                                            <span class="mxrow_tit">优惠金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}discountAmount">￥${shoppingCartItem.item.discountAmount}</span>
                                                                                        </div>
                                                                                        <div class="mxrow2">
                                                                                            <span class="mxrow_tit">余额抵扣：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}useBalanceAmount">￥${shoppingCartItem.item.useBalanceAmount}</span>
                                                                                            <span class="mxrow_tit">返点金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}balanceAmount">￥${shoppingCartItem.item.balanceAmount}</span>
                                                                                        </div>
                                                                                        <div class="mxrow2">
                                                                                            <span class="mxrow_tit">实付价：</span><span class="mxrow_info ${shoppingCartItem.item.uniqueKey}purchasePrice">￥<#if 1 == shoppingCartItem.item.sku.isGive >${shoppingCartItem.item.price?string('0.00')}<#elseif shoppingCartItem.item.purchasePrice ?? >${shoppingCartItem.item.purchasePrice?string('0.00')}<#else>0.00</#if></span>
                                                                                            <span class="mxrow_tit">成本价：</span><span class="mxrow_info  ${shoppingCartItem.item.uniqueKey}costPrice">￥<#if 1 == shoppingCartItem.item.sku.isGive >${shoppingCartItem.item.price?string('0.00')}<#elseif shoppingCartItem.item.costPrice ?? >${shoppingCartItem.item.costPrice?string('0.00')}<#else>0.00</#if></span>
                                                                                        </div>
                                                                                    </li>
                                                                                    <li class="lib3">
                                                                                        <div class="zkj">￥${shoppingCartItem.item.price }
                                                                                        </div>
                                                                                        <div class="sjj"><span>￥${shoppingCartItem.item.sku.retailPrice }</span></div>
                                                                                    </li>
                                                                                    <li class="lib5">
                                                                                        <span>x${shoppingCartItem.item.amount }</span>
                                                                                    </li>
                                                                                    <li class="lib4">
                                                                                        <div class="zkj">￥${shoppingCartItem.item.subtotal }</div>
                                                                                    </li>
                                                                                </ul>
                                                                            </div>
                                                                        </#if>
                                                                        <!--非套餐商品结束-->
                                                                    </#list>
                                                                    <!--遍历分组商品结束-->
                                                                </#if>
                                                            </div>
                                                            <!--遍历分组商品判定结束-->
                                                        </#list>
                                                        <!--遍历分组数据结束-->
                                                    </#if>
                                                    <!--遍历分组数据判定结束-->
                                                </div>
                                                <!--店铺结构结束-->
                                                <!--查看更多-->
                                                <!--默认页面展示5行，更多数据通过点击查看更多-->
                                                <#if (showCartItemNum > 5)>
                                                    <a href="javaScript:void(0); " class="more">点击展开 <i class="sui-icon icon-tb-unfold "></i> </a>
                                                </#if>
                                                <!--收起-->
                                                <a href="javaScript:void(0); " class="no-more">点击收起 <i class="sui-icon icon-tb-fold"></i> </a>
                                                <#if  (company.shops?size > 1)>
                                                <!--店铺小计-->
                                                <div style="background:#ffffff;overflow: hidden;margin-bottom: 8px;">
                                                    <div class="totalbox fr" style="display: inline-block;width:400px;clear:none;">
                                                        <div class="rbox-total fr">
                                                            <div class="fd-warp">
                                                                <div class="right-fd-box">
                                                                    <div class="list-item">
                                                                        <span class="spec">活动优惠:</span><span class="red">-￥</span><span class="red" id="${shop.shopCode}promoTotalAmt">${shop.promoTotalAmt?string('0.00')}</span>
                                                                    </div>
                                                                    <div class="list-item">
                                                                        <span class="spec">优惠券:</span><span class="red">-￥</span><span class="red" id="${shop.shopCode}voucherTotalAmt">${shop.voucherTotalAmt?string('0.00')}</span>
                                                                    </div>
                                                                    <div class="list-item">
                                                                        <span class="spec" style="font-size:16px;font-weight:500;">小计:</span><span style="font-size:16px;font-weight:500;">￥</span><span style="font-size:16px;font-weight:500;" name="balancespan"  id="${shop.shopCode}payAmount">${shop.payAmount?string('0.00')}</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                </#if>
                                            </#list>
                                            <!--遍历店铺数据结束-->
                                            <!--店铺备注留言-->
                                            <div style="border-top: 1px dashed #e6e6e6;overflow: hidden;background:#ffffff;padding-left: 40px;">
                                                <div class="bzly fl" style="border:none;padding-bottom: 0;">
                                                    <div class="address-top-title" style="display: inline-block;">备注留言</div>
                                                    <input type="text" class="companyRemark" company = '${company.companyCode}' placeholder="填写您的备注留言">
                                                </div>
                                                <!--总计-->
                                                <div class="totalbox fr" style="display: inline-block;width:400px;clear:none;">
                                                    <div class="rbox-total fr">
                                                        <div class="spzjbox">
                                                            共<span class="zhongshu" id="${company.companyCode}productVarietyNum">${company.productVarietyNum}</span>种商品，总件数<span class="zongjianshu" id="${company.companyCode}productTotalNum">${company.productTotalNum}</span>商品总计：￥<span id="${company.companyCode}totalAmount">${company.totalAmount?string('0.00')}</span>
                                                        </div>
                                                        <div class="fd-warp">
                                                            <!--中间竖线-->
                                                            <#--<div class="mid-line"></div>-->
                                                            <div class="right-fd-box">
                                                                <div class="list-item">
                                                                    <span class="spec">运费:</span><span>￥</span><span id="${company.companyCode}freightTotalAmt">${company.freightTotalAmt?string('0.00')}</span>
                                                                </div>
                                                                <div class="list-item">
                                                                    <span class="spec">活动优惠:</span><span class="red">-￥</span><span class="red" id="${company.companyCode}promoTotalAmt">${company.promoTotalAmt?string('0.00')}</span>
                                                                </div>
                                                                <div class="list-item">
                                                                    <span class="spec">优惠券:</span><span class="red">-￥</span><span class="red" id="${company.companyCode}voucherTotalAmt">${company.voucherTotalAmt?string('0.00')}</span>
                                                                </div>
                                                                <div class="list-item">
                                                                    <span class="spec" style="font-size:16px;font-weight:600;">总计:</span><span style="font-size:16px;font-weight:600;">￥</span><span style="font-size:16px;font-weight:600;" name="balancespan"  id="${company.companyCode}payAmount">${company.payAmount?string('0.00')}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </#if>
                                        <!-- 遍历店铺数据判断 -->
                                    </div>
                                    <!--POP公司结束-->
                                </#if>
                            </#list>
                        </#if>
                    </div>
                </div>
                <!--发票类型-->
                <div class="fplx">
                    <div class="address-top-title">选择发票类型 <a href="javascript:void(0)" class="fplx-a" data-toggle="modal"  data-keyboard="false" data-target="#fpxzTc" ><i class="sui-icon icon-notification"></i></a></div>
                    <ul id="invoiceUL">
                        <li class="cur" t="${invoiceType}">${invoiceText}</li>
                    </ul>
                    <#if ((invoiceType==1|| invoiceType==4) && ptdzInvoincePeer==1) >
                        <div class="fptx">
                            <label class="checkbox-pretty inline" id="peerTypeLab" >
                                <input type="checkbox" value="1" id="peerType" /><span>电子普通发票随货同行<i>勾选后，我们会将您的电子普通发票打印并加盖鲜章，随货为您寄出。</i></span>
                            </label>
                        </div>
                    </#if>
                </div>
                <#if (orderSettle.totalBalanceAmt>0)>
                    <!--我的余额-->
                    <div class="my-balance">
                        <div class="address-top-title">我的余额</div>
                        <div class="infobox">
                            <div class="infobox-top">
                                本次使用<input class="inputprice" type="text" id="myBalanceAmount1" data-placement="top" name="balanceAmount" onkeyup="clearNoNum(this)" onblur="getTotalGoodsMoney(3)" value="${orderSettle.balanceAmount }"/>元
                                <span class="dikou">抵扣</span>
                                <span class="redcolor">￥</span><span class="redcolor resultprice" id="myBalanceAmount2">${orderSettle.balanceAmount }</span> 元
                            </div>
                            <div class="infobox-bot">
                                账户可用余额共计为<span class="redcolor allprice" id="myTotalBalanceAmt">${orderSettle.totalBalanceAmt}</span>元

                                <span class="tishi">(扣减余额后最低起运价仍为${startingPriceText}，因此本次最多可使用<span id="myAvailBalanceAmt">${orderSettle.availBalanceAmt}</span>元)</span>

                                <a href="/merchant/center/balanceJournal/helpBalance.html" class="rightbox fr" target="_blank"><i class="sui-icon icon-tb-question"></i>了解什么是余额</a>
                            </div>
                        </div>
                    </div>
                </#if>
                <!--提交订单-->
                <div class="applybox">
                    <div class="js-total ">
                        <div class="fd-warp right-fd-box fr">
                            <div class="list-item">
                                <span class="spec">商品总金额:</span><span>￥</span><span name="price" id="finalTotalAmount">${orderSettle.totalAmount?string('0.00')}</span>
                            </div>
                            <div class="list-item">
                                <span class="spec">满减总额:</span><span class="red">-￥</span><span class="red" name="promoDiscountAmount" id="finalPromoTotalAmt">${orderSettle.promoTotalAmt?string('0.00')}</span>
                            </div>
                            <div class="list-item" style="position:relative;">
                                <!--店铺优惠券-->
                                <div class="youhuiquan youhuiquan-sum" style="padding:0;overflow: visible;position:absolute;left: 65px;">
                                <#--<span style="display: inline-block;margin-right: 30px;">优惠券</span>-->
                                <#--<span style="width:200px;display: inline-block;margin-right: 80px;color:#FF0000;font-size:12px;" id="${shop.shopCode}voucherTip">${shop.voucherTip}</span>-->
                                    <a href="javascript:;" class="xiala-all">
                                        <input type="hidden" id="skuInfoForPc" value='${orderSettle.skus}'>
                                        <input type="hidden" id="selectVoucherIds" value='${orderSettle.selectVoucherIds}'>
                                        <span style="color: #28a3ef;font-size: 12px;">查看全部优惠券</span><img style="margin-left:5px;" src="/static/images/xiala.png" alt="">
                                    </a>
                                    <div class="cg-yhq">
                                        <div class="xyy-cover"></div>
                                        <div class="xyy-confirm">
                                            <div class="xyy-header">提示</div>
                                            <div class="xyy-body">选中的自营叠加券优惠金额已超过应付总额，是否确认使用？</div>
                                            <div class="xyy-footer">
                                                <button class="ok-btn" id="xyyOk">确定使用</button>
                                                <button class="cancel-btn" id="xyyCancel">放弃使用</button>
                                            </div>
                                            <input type="hidden" id="curCoupon" value="">
                                        </div>
                                        <div class="yhq-san"></div>
                                        <div class="address-top-title">
                                            <div class="yhq-l-box">
                                                <a href="javascript:;" class="ky-yhq cur">可用优惠券</a>
                                                <a href="javascript:;" class="bky-yhq ">不可用优惠券</a>
                                            </div>
                                        </div>
                                        <div class="ybm">
                                            <!--可用优惠券开始-->
                                            <#assign availItemNum = 0 />
                                            <ul class="yhq-common weishiyong ky tanbox">
                                                <input type="hidden" value="${orderSettle}">
                                                <!--叠加券-->
                                                <#if (orderSettle.availDjVoucherList ?? && orderSettle.availDjVoucherList?size>0)>
                                                    <#list orderSettle.availDjVoucherList as voucher>
                                                    <input type="hidden" value="${voucher}">
                                                        <#assign availItemNum = availItemNum + 1 />
                                                        <li class="<#if (voucher.isUse==1)>cur </#if><#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher_index}">
                                                            <input type="hidden" shopcode="${voucher.shopCode}" shoppatterncode="${voucher.shopPatternCode}" name="voucherId" value="${voucher.id }"/>
                                                            <input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>
                                                            <div class="yhq-lb">
                                                                <div class="yhq-lb-top">
                                                                    <#if voucher.voucherState==1>
                                                                        <span class="price">${voucher.discountRatio }</span>
                                                                        <span class="fuhao">折</span>
                                                                    <#else>
                                                                        <span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>
                                                                    </#if>
                                                                </div>
                                                                <div class="yhq-lb-foot">
                                                                    ${voucher.minMoneyToEnableDesc }
                                                                </div>
                                                                <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                    <div class="yhq-lb-foot">
                                                                        ${voucher.maxMoneyInVoucherDesc }
                                                                    </div>
                                                                </#if>
                                                            </div>
                                                            <div class="yhq-rb">
                                                                <div class="yhq-rb-top">
                                                                    <span class="quan quan-die">${voucher.voucherTypeDesc}</span><span class="info">${voucher.voucherDesc }</span>
                                                                </div>
                                                                <div style="height:30px;overflow:hidden;"></div>
                                                                <div class="yhq-rb-foot" style="position:absolute;bottom:10px;">
                                                                    <span>${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }</span>
                                                                </div>
                                                            </div>
                                                            <div class="yhq-checkb">
                                                                <label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>">
                                                                    <input type="checkbox" <#if voucher.isUse==0>disabled=""</#if>><span></span>
                                                                </label>
                                                            </div>
                                                        </li>
                                                    </#list>
                                                </#if>
                                                <!--遍历可用-非叠加券-->
                                                <#if (orderSettle.availVoucherList ?? && orderSettle.availVoucherList?size>0)>
                                                    <#list orderSettle.availVoucherList as voucher>
                                                        <#assign availItemNum = availItemNum + 1 />
                                                        <li class="<#if (voucher.isUse==1)>cur </#if><#if voucher.isUse==0 || voucher.isUse==Null>ygq </#if><#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">
                                                            <input type="hidden" shopcode="${voucher.shopCode}" shoppatterncode="${voucher.shopPatternCode}" name="voucherId" value="${voucher.id }"/>
                                                            <input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>
                                                            <div class="yhq-lb">
                                                                <div class="yhq-lb-top">
                                                                    <#if voucher.voucherState==1>
                                                                        <span class="price">${voucher.discountRatio }</span>
                                                                        <span class="fuhao">折</span>
                                                                    <#elseif voucher.voucherUsageWay==1>
                                                                        <span class="fuhao">￥</span><span class="price">${voucher.sourceMoneyInVoucher }</span>
                                                                    <#else>
                                                                        <span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>
                                                                    </#if>
                                                                </div>
                                                                <div class="yhq-lb-foot">
                                                                    ${voucher.minMoneyToEnableDesc }
                                                                </div>
                                                                <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >
                                                                    <div class="yhq-lb-foot">
                                                                        ${voucher.maxMoneyInVoucherDesc }
                                                                    </div>
                                                                </#if>
                                                            </div>
                                                            <div class="yhq-rb">
                                                                <div class="yhq-rb-top">
                                                                    <#if voucher.voucherType == 2>
                                                                        <span class="quan">${voucher.voucherTypeDesc}</span>
                                                                    </#if>
                                                                    <#if voucher.voucherType == 1>
                                                                        <span class="quan quan-tong">${voucher.voucherTypeDesc}</span>
                                                                    </#if>
                                                                    <#if voucher.voucherType == 6>
                                                                        <span class="quan quan-die">${voucher.voucherTypeDesc}</span>
                                                                    </#if>
                                                                    <#if voucher.voucherType == 5>
                                                                        <span class="quan quan-xin">${voucher.voucherTypeDesc}</span>
                                                                    </#if>
                                                                    <#if voucher.voucherType == 7>
                                                                        <span class="quan quan-shop">${voucher.voucherTypeDesc}</span>
                                                                    </#if>
                                                                    <#if voucher.voucherType == 8>
                                                                        <span class="quan quan-platform">${voucher.voucherTypeDesc}</span>
                                                                    </#if>
                                                                    <span class="info" title="${voucher.shopName}">${voucher.shopName}</span>

                                                                </div>
                                                                <div style="height:30px;overflow:hidden;line-height:30px;">${voucher.voucherDesc }</div>
                                                                <div class="yhq-rb-foot">
                                                                    <span>${voucher.voucherScope}</span>
                                                                </div>
                                                                <div style="font-size:12px;color:#999999;">${voucher.validDate?string("yyyy/MM/dd")}-${voucher.expireDate?string("yyyy/MM/dd")}</div>
                                                            </div>
                                                            <div class="yhq-checkb">
                                                                <label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>">
                                                                    <input type="checkbox" <#if voucher.isUse==0>disabled=""</#if> ><span></span>
                                                                </label>
                                                            </div>
                                                        </li>
                                                    </#list>
                                                </#if>
                                            </ul>
                                            <!--可用优惠券结束-->
                                            <!--不可用优惠券开始-->
                                            <ul class="yhq-common weishiyong bky tanbox"></ul>
                                            <!--不可用优惠券结束-->
                                        </div>
                                    </div>
                                </div>
                                <span class="spec">优惠券总额:</span><span class="red">-￥</span><span class="red" name="voucherDiscountAmount" id="finalVoucherTotalAmt">${orderSettle.voucherTotalAmt?string('0.00')}</span>
                            </div>
                            <div class="list-item">
                                <span class="spec">运费总额:</span><span class="red">+￥</span><span class="red"  name="totalFreightAmount" id="finalFreightTotalAmt">${orderSettle.freightTotalAmt?string('0.00')}</span>
                            </div>
                            <div class="list-item">
                                <span class="spec">余额抵扣:</span><span class="red">-￥</span><span class="red " name="balancespan"  id="finalBalanceAmount"><#if orderSettle.balanceAmount ?? >${orderSettle.balanceAmount?string('0.00')}<#else>0.00</#if></span>
                            </div>

                            <#if orderSettle.fixedPriceAmount?? && orderSettle.fixedPriceAmount gt 0 >
                                <div class="list-item">
                                    <span class="spec">一口价优惠:</span><span class="red">-￥</span><span class="red " name="fixedpricespan"  id="finalFixedPriceAmount">${orderSettle.fixedPriceAmount?string('0.00')}</span>
                                </div>
                            </#if>


                        </div>
                    </div>
                    <div class="js-order">
                        <div class="acol6 ">
                            <div class="wrapbox ">
                                <p style="padding-top: 20px;">应付金额: <span class="money"  id="finalPayAmount">￥${orderSettle.payAmount?string('0.00')}</span> </p>
                                <p style="display: none;"><label class="isdefaultSec checkbox-pretty inline all checked">
                                        <input type="checkbox" checked="checked"><span>我已阅读并同意签订</span>
                                    </label><a href="purchases_contract.html"  target="_blank"  class="fr">《药帮忙网上购销合同》    </a>
                                </p>
                            </div>
                            <a href="javascript:void(0);" class="turn-down" id="trunDownBtn" data-purchaseno=${purchaseNo}>驳回</a>
                            <a href="javascript:void(0);" class="settle-btn-css" id="settle_btn">提交订单</a>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--主体部分结束-->
    </div>
    <!--底部导航区域开始-->
    <div class="footer" id="footer">
        <#include "/common/footer.ftl" />
    </div>
    <!--底部导航区域结束-->
</div>

<!--删除弹窗-->
<div id="delModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">删除</h4>
            </div>
            <div class="modal-body">
                <div class="scbox">
                    收货人地址将被删除，你确定吗？
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large" >确定</button>
                <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
            </div>
        </div>
    </div>
</div>

<!--编辑收货地址-->
<div id="editModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">修改配送信息</h4>
            </div>
            <div class="modal-body">
                <div class="add-address-modal">
                    <form class="sui-form form-horizontal sui-validate" onsubmit="return doEditAddress()">
                        <input type="hidden" name="id"/>
                        <div class="control-group">
                            <label for="contactor" class="control-label"><span class="sui-text-danger">*</span>姓名：</label>
                            <div class="controls">
                                <input type="text" id="contactor" placeholder="" data-rules="required|minlength_xm=2|maxlength_xm=8" maxlength="8" name="contactor">
                            </div>
                        </div>
                        <div class="control-group">
                            <label for="mobile" class="control-label"><span class="sui-text-danger">*</span>手机号码：</label>
                            <div class="controls">
                                <input type="text" id="mobile" placeholder="" data-rules="required|mobile" maxlength="11" name="mobile" />
                            </div>
                        </div>
                        <div class="control-group">
                            <label for="mobile" class="control-label">收货地址：</label>
                            <div class="controls">
                                <div class="adres-text" name="fullAddress"></div>

                            </div>
                        </div>

                        <#--<div class="control-group">-->
                            <#--<label for="mobile" class="control-label">地址备注：</label>-->
                            <#--<div class="controls">-->
                                <#--<div class="detailaddress">-->
                                    <#--<#if (shippingAddress.auditState == 2 || shippingAddress.auditState == 3)>-->
                                        <#--${shippingAddress.remark}-->
                                    <#--</#if>-->
                                    <#--<#if (shippingAddress.auditState != 2 && shippingAddress.auditState != 3)>-->
                                        <#--<input type="text" maxlength="50" class="detailinp" name="remark" placeholder="若以上gsp地址不能被识别，请在此输入框中填写gsp地址的街道和门牌号">-->
                                    <#--</#if>-->
                                <#--</div>-->
                            <#--</div>-->
                        <#--</div>-->
                        <!--地址备注提示-->
                        <div class="control-group" style="margin-top: -22px;">
                            <label for="mobile" class="control-label"></label>
                            <div class="controls">
                                <div class="addressTips"></div>
                                <div class="is_default" style="margin-left: 23px;margin-top:15px;">
                                    <label id="isdefault" class="isdefaultSec checkbox-pretty inline ">
                                        <input type="checkbox" name="isdefault"><span>设置为默认收货地址</span>
                                    </label>
                                </div>
                                <div class="default_address">
                                    <span>默认收货地址</span>
                                </div>
                            </div>
                        </div>
                        <#--<div class="control-group">
                            <label for="mobile" class="control-label"><span class="sui-text-danger">*</span>收货地址：</label>
                            <div class="controls">
                                <div id="city_5">
                                    <select class="prov" name="province"></select>
                                    <select class="city" disabled="disabled" name="city"></select>
                                    <select class="dist" disabled="disabled" name="district"></select>
                                </div>
                                <div class="detailaddress">
                                    <input type="text" class="detailinp" name="address" placeholder="详细地址" data-rules="required|minlength=2|maxlength=100">
                                </div>
                            </div>
                        </div>
                        <div class="control-group">
                            <label for="mobile" class="control-label"></label>
                            <div class="controls">
                                <label class="checkbox-pretty inline ">
                                    <input type="checkbox" name="isdefault"><span>设为默认地址</span>
                                </label>
                            </div>
                        </div>-->
                        <div class="modal-bot">
                            <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                            <button type="submit" class="sui-btn btn-primary btn-large">保存</button>
                        </div>
                    </form>
                </div>
            </div>

        </div>
    </div>
</div>
<!--设为默认地址弹窗-->
<div id="editDefaultModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">设为默认地址</h4>
            </div>
            <div class="modal-body">
                <div class="scbox">
                    您确认将该地址信息设为默认地址吗?
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large" >确定</button>
                <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
            </div>
        </div>
    </div>
</div>
<!--查看优惠券弹窗-->
<div id="yhqModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">叠加优惠券</h4>
            </div>
            <div class="modal-body">
                <ul class="yhq-common weishiyong">
                    <#list availDjVoucherList as voucher>
                        <li>
                            <input type="hidden" shopcode="${voucher.shopCode}" shoppatterncode="${voucher.shopPatternCode}" name="voucherId" value="${voucher.id }"/>
                            <input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>
                            <div class="yhq-lb">
                                <div class="yhq-lb-top">
                                    <#if voucher.voucherState==1>
                                        <span class="price">${voucher.discountRatio }</span>
                                        <span class="fuhao">折</span>
                                    <#else>
                                        <span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>
                                    </#if>
                                </div>
                                <div class="yhq-lb-foot">
                                    ${voucher.minMoneyToEnableDesc }
                                </div>
                                <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                    <div class="yhq-lb-foot">
                                        ${voucher.maxMoneyInVoucherDesc }
                                    </div>
                                </#if>
                            </div>
                            <div class="yhq-rb">
                                <div class="yhq-rb-top">
                                    <span class="quan quan-die">${voucher.voucherTypeDesc}</span><span class="info">${voucher.voucherDesc }</span>
                                </div>
                                <div style="height:30px;overflow:hidden;"></div>
                                <div class="yhq-rb-foot" style="position:absolute;bottom:10px;">
                                    <span>${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }</span>
                                </div>
                            </div>
                        </li>
                    </#list>
                </ul>
            </div>
        </div>
    </div>
</div>

<div id="unyhqModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">叠加优惠券</h4>
            </div>
            <div class="modal-body">
                <ul class="yhq-common weishiyong">
                    <#list unavailDjVoucherList as voucher>
                        <li>
                            <div class="yhq-lb">
                                <div class="yhq-lb-top">
                                    <#if voucher.voucherState==1>
                                        <span class="price">${voucher.discountRatio }</span>
                                        <span class="fuhao">折</span>
                                    <#else>
                                        <span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>
                                    </#if>
                                </div>
                                <div class="yhq-lb-foot">
                                    ${voucher.minMoneyToEnableDesc }
                                </div>
                                <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                    <div class="yhq-lb-foot">
                                        ${voucher.maxMoneyInVoucherDesc }
                                    </div>
                                </#if>
                            </div>
                            <div class="yhq-rb">
                                <div class="yhq-rb-top">
                                    <span class="quan quan-die">${voucher.voucherTypeDesc}</span><span class="info">${voucher.voucherDesc }</span>
                                </div>
                                <div style="height:30px;overflow:hidden;"></div>
                                <div class="yhq-rb-foot" style="position:absolute;bottom:10px;">
                                    <span>${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }</span>
                                </div>
                            </div>
                        </li>
                    </#list>
                </ul>
            </div>
        </div>
    </div>
</div>

<!--发票须知弹窗-->
<div id="fpxzTc" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4  class="modal-title">发票须知</h4>
            </div>
            <div class="modal-body">
                <div class="xzmain">
                    <p class="xz-title">1.电子普通发票</p>
                    <p class="xz-info">电子发票是税局认可的有效收付款凭证，其法律效力、基本用途及使用规范等同于纸质发票，可作为用户报销、维权的有效凭据。详见《国家税务总局公告2015年第84号》；</p>
                    <p class="xz-info">如需纸质发票，可自行下载打印。</p>

                    <p class="xz-title">2.增值税专用发票</p>
                    <p class="xz-info">纸质增值税专用发票，不仅具有商事凭证的作用，还可以进行进项抵扣。开具增值税专用发票需提供药店完整的开票信息，详询客服400-0505-111；</p>
                    <p class="xz-info">我司依法开具发票，请您依法使用。</p>

                    <p class="xz-title">3.发票金额</p>
                    <p class="xz-info">发票金额为实付金额，不包括优惠金额。</p>

                    <p class="xz-title">4. 电子普通发票开票时间</p>
                    <p class="xz-info">电子普通发票：确认收货后24小时内生成，您可以在个人中心--我的订单--订单详情中，查看下载（APP端只支持查看，PC端支持查看、下载、打印）。</p>

                    <p class="xz-title">5.已开具电子普通发票，申请退货，发票怎么办</p>
                    <p class="xz-info">部分退货：商品退回后，我司会把原电子发票冲红，开具一张新的发票，请您留意订单详情中电子发票的变更；</p>
                    <p class="xz-info">整单退货：商品退回后，我司会把原电子发票冲红，订单中的电子发票无法再次查看下载。</p>

                    <p class="xz-title">6.已开具增值税专用发票，申请退货，发票怎么办</p>
                    <p class="xz-info">部分退货或者整单退货，纸质发票都必须随商品一起退回。</p>
                </div>
            </div>

        </div>
    </div>
</div>

<!--配送服务费说明弹窗-->
<div id="fuwu" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4  class="modal-title" style="text-align: center;">配送服务收费标准</h4>
            </div>
            <div class="modal-body">
                <div class="xzmain freight">
                </div>
            </div>

        </div>
    </div>
</div>

<div style="display: none" id="voucherUnavailableDialog">
    ${voucherText}
</div>

<#if shippingAddress.auditState==0>
    <script>
        $.alert({
            title: '温馨提示：',
            body: '为严格执行《药品管理法》及《药品经营质量管理规范》的相关规定，收货地址将默认为许可证注册地址或仓库地址，并不能随意修改。如该地址无法被快递员识别请在收货地址右方点击修改并在输入框内加以描述，如有其它疑问您还可以联系官方客服进行咨询！', //必填
            okBtn : '我知道啦'
        })
    </script>
</#if>
<div class="ti_box">
    <input type="hidden" value="" id="bigPackage">
    <input type="hidden" value="" id="bigPackageTotalAmount">
    <div class="ti_shi">
        <div class="shi_tou">
            <i class="sui-icon icon-tb-infofill"></i>
            <span>提示</span>
            <i class="sui-icon icon-tb-close"></i>
        </div>
        <div class="shi_xia">
            <p>确定不需要后，您将无法获取该礼包</p>
        </div>
        <div class="shi_que">
            确 认
        </div>
        <div class="shi_qu">
            取 消
        </div>
    </div>
</div>

<div class="ti_box_v">
    <div class="ti_shi_v">
        <div class="shi_tou_v">
            <i class="sui-icon_v icon-tb-infofill_v"></i>
            <span>提示</span>
        </div>
        <div class="shi_xia_v">
            <p>您有优惠力度更高的优惠券，当前订单是否使用？</p>
        </div>
        <div class="shi_que_v">
            确 认
        </div>
        <div class="shi_qu_v">
            取 消
        </div>
    </div>
</div>
</body>
<script>
    // $('.xiala-all').click(function(e){
    //     e.stopPropagation();
    //     if($(this).hasClass("double")){
    //         $(this).parent(".youhuiquan").find(".cg-yhq").slideUp();
    //         $(this).removeClass("double");
    //     }else{
    //         $(this).parent(".youhuiquan").find(".cg-yhq").slideDown();
    //         $(this).addClass("double");
    //     }
    // });
    // $(document).click(function(e){
    //     var con = $(".cg-yhq")
    //     if(!con.is(e.target) && con.has(e.target).length === 0){
    //         $(".cg-yhq").slideUp();
    //     }
    // })
    /**运费提示**/
    $(".get-fei").click(function(){
        $.ajax({
            url: "/merchant/freight/query",
            type: "POST",
            success: function(result){
                if(result){
                    $("#fuwu").modal();
                    ele = '';
                    if(result.freightTemplateTipsList.length){
                        result.freightTemplateTipsList.forEach(function(item,index){
                            ele += '<p class="xz-title">' +  item.title +'</p>'
                            item.freightTips.forEach(function(item2,index2){
                                ele += '<p class="xz-info">' +  item2 +'</p>'
                            })
                        })
                    }
                    ele += '<br>';
                    $(".freight").html(ele);
                }else{
                    $.alert(result.errorMsg);
                }
            }
        })
    })


    $(document).click(function(e){
        var con = $(".cg-yhq")
        if(!con.is(e.target) && con.has(e.target).length === 0){
            $(".cg-yhq").slideUp();
        }
    })
</script>
</html>
