package com.xyy.ec.pc.enums;

/**
 * @Description
 * @Date 2025/7/31 17:26
 */
public enum AbnormalAccountTypeEnum {

    NOT_BIND_EC("1", "没有绑定EC账号"),
    SPIDER("2", "爬虫"),
    FORBIDDEN("3", "账号禁止登录（冻结、注销等）"),
    DIFFERENT_PLACES_LOGIN("4", "异地登录")
    ;

    private String code;
    private String name;

    AbnormalAccountTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
