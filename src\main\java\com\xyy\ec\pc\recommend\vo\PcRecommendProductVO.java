package com.xyy.ec.pc.recommend.vo;

import com.xyy.ec.pc.service.marketing.dto.MarketingGroupBuyingActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingWholesaleActivityInfoDTO;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchRecPurchaseTypeEnum;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchYesNoEnum;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.SearchProductCardPositionTypeEnum;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSpuRecallStrategyEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 推荐商品卡片信息
 *
 */
@Getter
@Setter
public class PcRecommendProductVO extends PcRecommendBaseProductVO {

    /**
     * 商品店铺展示名称
     */
    private String shopName;

    /**
     * 店铺首页地址
     */
    private String shopUrl;

    /**
     * 商品原来的展示名称
     */
    private String originalShowName;

    /**
     * 是否有货提醒
     */
    private Boolean isArrivalReminder;

    /**
     * 近远效期
     * 取值定义: 近远期不一致返回近效期/远效期时间，近远期一致返回一个效期时间，如果近效期|远效期只有一个则不返回值
     */
    private String effectStr;

    /**
     * 店铺更多同款商品: 0-否，1-是
     */
    private Integer hasSimilarGoods;

    /**
     * 秒杀活动信息
     */
    private MarketingSeckillActivityInfoDTO actSk;

    /**
     * 拼团活动信息
     */
    private MarketingGroupBuyingActivityInfoDTO actPt;

    /**
     * 批购包邮活动信息
     */
    private MarketingWholesaleActivityInfoDTO actPgby;

    /**
     * 标签列表
     */
    private Map<String, Object> tags;

    /**
     * 运营位商品组id
     */
    private String operationExhibitionId;

    /**
     * 运营位投放人群id
     */
    private Long operationCustomerGroupId;

    /**
     * 运营位Id
     */
    private Long operationId;

    /**
     * 品类扶持位ID列表，以英文逗号分隔组成的字符串
     */
    private String categorySupportIdsStr;

    /**
     * 品类扶持商品组ID列表，以英文逗号分隔组成的字符串
     */
    private String categorySupportExhibitionIdsStr;

    /**
     * 位置类型，1：普通位置，2：运营位位置，3：首位低价位置，4：凑单固定位置
     *
     * @see SearchProductCardPositionTypeEnum
     */
    private Integer positionType;

    /**
     * 位置类型名称，1：普通位置，2：运营位位置，3：首位低价位置，4：凑单固定位置
     *
     * @see SearchProductCardPositionTypeEnum
     */
    private String positionTypeName;

    /**
     * 埋点数据。
     * PS：订单顺手买一件使用。
     */
    private String mddata;

    /**
     * 拼团价。
     * PS：订单顺手买一件使用。
     */
    private BigDecimal price;

    /**
     * qt埋点数据
     */
    private String qtSkuData;

    /**
     * 排序位置
     */
    private Integer rank;

    /**
     * 二级排序号
     */
    private Integer subRank;

    /**
     * 运营位排序位置
     */
    private Integer operationRank;

    /**
     * 一级分类ID
     */
    private Long categoryFirstId;

    /**
     * 一级分类名称
     */
    private String categoryFirstName;

    /**
     * 进入商详是否显示推荐购买。1显示；其他值（如null、0）：不显示。
     *
     * @see EcpSearchYesNoEnum
     */
    private Integer enterProductDetailIsShowRecPurchase;

    /**
     * 进入商详显示推荐购买的方式。1组合购，2加价购。仅在显示时有效。
     *
     * @see EcpSearchRecPurchaseTypeEnum
     */
    private Integer enterProductDetailShowRecPurchaseType;

    /**
     * spu召回策略名称
     *
     * @see EcpRecommendSpuRecallStrategyEnum
     */
    private String spuRecallStrategyName;

    /**
     * 券后价
     */
    private BigDecimal couponPrice;
}
