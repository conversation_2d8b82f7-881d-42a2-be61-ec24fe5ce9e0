package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.virtualGold.VirtualGoldDetailAdminParamDto;
import com.xyy.ec.merchant.bussiness.enums.PaySourceEnums;
import com.xyy.ec.merchant.bussiness.utils.AesSecurityUtils;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.merchant.server.api.VirtualGoldApi;
import com.xyy.ec.merchant.server.constant.RedisCacheKeys;
import com.xyy.ec.merchant.server.dto.CheckPayPwdReturnDto;
import com.xyy.ec.merchant.server.dto.VirtualGoldParamDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.merchant.server.enums.VirtualGoldChangeTypeEnum;
import com.xyy.ec.order.api.pay.PingAnAccountApi;
import com.xyy.ec.order.api.voucher.VoucherForYbmApi;
import com.xyy.ec.order.business.utils.DateUtil;
import com.xyy.ec.order.dto.pay.PingAnCreditMerchantDto;
import com.xyy.ec.order.dto.voucher.RechargeDiscountDTO;
import com.xyy.ec.order.dto.voucher.RechargeReqDTO;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.param.VirtualGoldReqDto;
import com.xyy.ec.pc.service.MerchantService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.HttpState;
import com.xyy.ec.pc.util.excel.ExcelExport;
import com.xyy.ec.pc.util.excel.ExportExcelEntity;
import com.xyy.ec.pc.vo.KeyValueViwVO;
import com.xyy.ec.pc.vo.VirtualGoldLogVO;
import com.xyy.saas.payment.cores.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.ui.ModelMap;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 购物金后
 */
@RestController
@RequestMapping("/pc/virtual/gold")
@Slf4j
public class VirtualGoldController extends BaseController {

    @Reference(version = "1.0.0")
    private VirtualGoldApi virtualGoldApi;

    //支付密码密钥
    @Value("${pay_pwd_security:FmpHxGc9no95cvd4}")
    private String payPwdSecurity;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    @Reference(version = "1.0.0")
    private PingAnAccountApi pingAnAccountApi;

    @Autowired
    private MerchantService merchantService;

    @Reference(version = "1.0.0")
    private VoucherForYbmApi voucherForYbmApi;


    /**
     * 我的购物金主页
     * @return
     */
    @RequestMapping("/virtualGold/index")
    @ResponseBody
    public Object toVirtualGold(){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            modelMap.put("merchantId", merchant.getId());
            modelMap.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            modelMap.put("center_menu", "shoppingGold");
            return new ModelAndView("/order/shoppingGold.ftl", modelMap);
        } catch (Exception e) {
            log.error("toVirtualGold error ", e);
            return new ModelAndView("/error/500.ftl");
        }
    }


    /**
     * @param virtualGoldParamDto
     * @return
     */
    @RequestMapping(value = "/transferIn", method = RequestMethod.POST)
    @ResponseBody
    public Object transferIn(VirtualGoldParamDto virtualGoldParamDto) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            String pwd = AesSecurityUtils.AesEncrypt(payPwdSecurity, virtualGoldParamDto.getPwd());
            if (merchant == null) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }

            virtualGoldParamDto.setMerchantId(merchant.getId());
            virtualGoldParamDto.setOperatorId(merchant.getId());
            virtualGoldParamDto.setOperatorNo(merchant.getMobile());
            Long accountId = merchant.getAccountId();
            //如果是购物金 验证支付密码
            //tranNo 需要有值  type = 1 是购物金验证密码
            Integer type = 1;
            ApiRPCResult<CheckPayPwdReturnDto> checkpwd = loginAccountApi.checkPayPwdV3(accountId, pwd, virtualGoldParamDto.getTranNo(), null, null, type);
            log.info("transferIn--checkpwd：{}", JSONObject.toJSONString(checkpwd));
            if (checkpwd == null) {
                return this.addError(1001,"密码校验失败");
            }

            if (checkpwd.getData().getStatus() ==2) {
                return this.addError(1001,checkpwd.getData().getMsg());
            }
            virtualGoldParamDto.setAccountId(accountId);
            virtualGoldParamDto.setPaySource(PaySourceEnums.PC_PINGAN_TRANSFER.getValue());

            // 验证密码成功之后  进入购物金操作
            ApiRPCResult<Boolean> result = virtualGoldApi.payMentTransferInVirtualGold(virtualGoldParamDto);
            if (result != null && result.getCode() == 200) {
                return this.addResult();
            }
            return this.addError(1001, "支付失败");
        } catch (Exception e) {
            log.error("##-VirtualGoldController--transferIn出错:{}", e);
            return this.addError(1001, "支付失败");
        }
    }


    /**
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/getTranNo", method = RequestMethod.POST)
    @ResponseBody
    public Object getTranNo( VirtualGoldDetailAdminParamDto reqDTO) {
        try {
            String tranNo = this.getTranNo();
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("tranNo", tranNo);
            redisTemplate.opsForValue().set(reqDTO.getMerchantId()+"",tranNo,180, TimeUnit.SECONDS);
            return this.addResult("tranNo", tranNo);
        } catch (Exception e) {
            log.error("##-VirtualGoldController--getTranNo出错:{}",e);
            return this.addError(1001,e.getMessage());
        }
    }


    private String getTranNo(){
        Date date = new Date();
        Random random = new Random();
        int randomInt = random.nextInt(RedisCacheKeys.BOUND_ROM);
        Long currentNo = date.getTime();
        return RedisCacheKeys.PREFIX_SHOP + DateUtil.date2String(date, "yyyyMMddHHmmss") + (100000L+randomInt + currentNo);
    }
    /**
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/getResultByTranNo", method = RequestMethod.POST)
    @ResponseBody
    public Object getResultByTranNo(VirtualGoldParamDto reqDTO) {
        try {
            ApiRPCResult<Integer> result = virtualGoldApi.getTransResultByTranNo(reqDTO);
            if(result!=null && !result.getData().equals(2)){
                return this.addResult("payResult", result.getData());
            }else{
                return  this.addError(1001,"支付失败！");
            }
        } catch (Exception e) {
            log.error("##-VirtualGoldController--getResultByTranNo出错:{}",e);
            return this.addError(1001,e.getMessage());
        }
    }

    /**
     * PC获取我的购物金
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/queryMyVirtualGold", method = RequestMethod.POST)
    @ResponseBody
    public Object queryMyVirtualGold(VirtualGoldReqDto reqDTO) {
        Map resultMap = new HashMap();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            final BigDecimal virtualGold = merchantService.queryVirtualGold(merchant.getMerchantId());
            ApiRPCResult<PingAnCreditMerchantDto> pingan = pingAnAccountApi.queryCreditByMerchantId(merchant.getMerchantId());
            resultMap.put("availableVirtualGold", virtualGold);
            Integer accountState = 0;
            if (pingan.isSuccess() && pingan.getData() != null && pingan.getData().getAccountState().equals(32)) {
                accountState = 1;
            }
            resultMap.put("accountState", accountState);
            return this.addResult("data", resultMap);

        } catch (Exception e) {
            log.error("##-VirtualGoldController--queryMyVirtualGold 出错:{}", e);
            return this.addError(1001, e.getMessage());
        }
    }

    /**
     * PC获取我的购物金流水记录
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/queryMyRecord", method = RequestMethod.POST)
    @ResponseBody
    public Object queryMyRecord(@RequestBody VirtualGoldReqDto reqDTO) {
        try {
            Assert.notNull(reqDTO.getVirtualGoldType(),"页码类型不能为空");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            reqDTO.setMerchantId(merchant.getMerchantId());
            log.info("VirtualGoldController-queryMyRecord-入参:{}",reqDTO);
            Page<VirtualGoldLogVO> resultPage = merchantService.queryVirtualGoldLogList(reqDTO);
            return this.addResult("data", resultPage);
        } catch (Exception e) {
            log.error("##-VirtualGoldController--queryMyRecord 出错:{}",e);
            return this.addError(1001,e.getMessage());
        }
    }


    /**
     * 获取充值优惠信息
     * @param reqDTO
     * @return
     */
    @RequestMapping(value = "/getRechargeDiscount", method = RequestMethod.POST)
    @ResponseBody
    public Object getRechargeDiscount(@RequestBody VirtualGoldReqDto reqDTO) {
        try {
            //    ONLINE(0, "在线充值"),
            //    PING_AN_MERCHANT(1, "平安商户充值");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            RechargeReqDTO rechargeReqDTO =new RechargeReqDTO();
            rechargeReqDTO.setMerchantId(merchant.getMerchantId());
            rechargeReqDTO.setChannel(reqDTO.getChannel());
            log.error("##-VirtualGoldController--getRechargeDiscount:{}",rechargeReqDTO);
            ApiRPCResult<RechargeDiscountDTO> dataResult = voucherForYbmApi.getRechargeDiscountV2(rechargeReqDTO);
            return this.addResult("data", dataResult);
        } catch (Exception e) {
            log.error("##-VirtualGoldController--getRechargeDiscount出错:{}",e);
            return this.addError(1001,e.getMessage());
        }
    }


    /**
     * 购物金变动类型枚举
     * @return
     */
    @RequestMapping(value = "/queryVirtualGoldChangeType", method = RequestMethod.POST)
    @ResponseBody
    public Object queryVirtualGoldChangeType() {

        try {
            Map<Integer, String>  enumMap= VirtualGoldChangeTypeEnum.maps;
            List<KeyValueViwVO> enumList = new ArrayList<>();
            for (Map.Entry<Integer, String> entry : enumMap.entrySet()) {
                Integer  key = entry.getKey();
                String value = entry.getValue();
                KeyValueViwVO vo = new KeyValueViwVO();
                vo.setName(value);
                vo.setValue(key);
                enumList.add(vo);
            }
            return this.addResult("data", enumList);
        } catch (Exception e) {
            log.error("queryFbankTradeCodeEnum--出错", e);
            return this.addResult("data", null);
        }
    }

    /**
     * 我的购物金账户变动明细导出数据
     * @param reqDTO
     * @param response
     */

    @PostMapping("/exportExcel")
    public void exportExcel(@RequestBody  VirtualGoldReqDto reqDTO, HttpServletResponse response) {
        try {
            log.info("我的购物金账户导出数据,请求参数:{}", JSONObject.toJSONString(reqDTO));
            int pageNum = 1;
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (null == merchant) {
                log.error("导出购物金变更明细错误:未登录，请重新登录");
                response.setStatus(HttpState.PARAM_ERROR);
                response.getWriter().write(ResultCodeEnum.PARAM_ERROR.getDisplayMsg());
                return;
            }
            reqDTO.setMerchantId(merchant.getMerchantId());
            reqDTO.setPageSize(500);
            reqDTO.setPageNum(pageNum);
            List<VirtualGoldLogVO>  resultVO = new ArrayList<>();
            Page<VirtualGoldLogVO> resultPage = merchantService.queryVirtualGoldLogList(reqDTO);
            if (resultPage == null || resultPage.getTotal() == 0) {
                log.error("导出我的购物金账户数据异常:数据为空！");
                response.setStatus(HttpState.PARAM_ERROR);
                response.getWriter().write(ResultCodeEnum.PARAM_ERROR.getDisplayMsg());
                return;
            }
            resultVO.addAll(resultPage.getRows());
            while (resultPage.getCurrentPage() < resultPage.getPageCount()) {
                reqDTO.setPageNum(++pageNum);
                resultPage = merchantService.queryVirtualGoldLogList(reqDTO);
                if (resultPage == null || CollectionUtil.isEmpty(resultPage.getRows())) {
                    break;
                }
                 resultVO.addAll(resultPage.getRows());
            }
            // 写入excel并导出
            List<ExportExcelEntity> sheets = new ArrayList<>();
            String  month =LocalDate.now().getYear()+"年"+ LocalDate.now().getMonthValue()+"月";
            if(reqDTO.getPayStartTime()!=null){
                LocalDate payStartDate = reqDTO.getPayStartTime().toInstant().atZone(TimeZone.getDefault().toZoneId()).toLocalDate();
                month = payStartDate.getYear()+"年"+ payStartDate.getMonthValue()+"月";;

            }
            // 写入明细
            sheets.add(
                    ExportExcelEntity.builder()
                            .sheetName("购物金变更明细" )
                            .columnWidth(7000)
                            .flag(false)
                            .fieldNames(new String[]{"createTime", "tranNo", "changeDesc", "virtualGold", "afterChange"})
                            .headers(new String[]{"时间", "订单编号", "变动类型", "变动金额", "余额"})
                            .dataList(resultVO)
                            .build()
            );
            ExcelExport.exportSheets(response, "购物金变动明细"+month+".xlsx", sheets);
        } catch (Exception e) {
             log.error("导出购物金变更明细异常:", e);
             response.setStatus(HttpState.DEFAULT_ERROR);
            try {
                response.getWriter().write(ResultCodeEnum.DEFAULT_ERROR.getDisplayMsg());
            } catch (IOException ex) {
                log.error("导出购物金变更明细异常:", ex);
            }
        }
    }

}
