package com.xyy.ec.pc.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.search.api.remote.dto.OrderSkuStatisticsQueryDto;
import com.xyy.ec.order.search.api.remote.order.OrderSkuStatisticsApi;
import com.xyy.ec.order.search.api.remote.result.OrderSkuStatisticsResultDto;
import com.xyy.ec.pc.remote.OrderSkuStatisticsRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * {@link OrderSkuStatisticsApi} RPC调用Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderSkuStatisticsRemoteServiceImpl implements OrderSkuStatisticsRemoteService {

    @Reference(version = "1.0.0", timeout = 30)
    private OrderSkuStatisticsApi orderSkuStatisticsApi;

    @Override
    public Map<Long, Integer> mgetCsuSaleMerchantNum(Date startTimeQueryParam, Date endTimeQueryParam, List<Long> csuIds) {
        if (Objects.isNull(startTimeQueryParam) || Objects.isNull(endTimeQueryParam) || CollectionUtils.isEmpty(csuIds)) {
            return Maps.newHashMap();
        }
        Set<Long> csuIdsSet = Sets.newHashSet(csuIds);
        csuIdsSet.remove(null);
        if (CollectionUtils.isEmpty(csuIdsSet)) {
            return Maps.newHashMap();
        }
        Map<Long, Integer> csuIdToNumMap = Maps.newHashMapWithExpectedSize(16);
        List<List<Long>> csuIdsLists = Lists.partition(Lists.newArrayList(csuIdsSet), 100);
        OrderSkuStatisticsQueryDto queryParam = new OrderSkuStatisticsQueryDto();
        queryParam.setStartTime(startTimeQueryParam);
        queryParam.setEndTime(endTimeQueryParam);
        ApiRPCResult<List<OrderSkuStatisticsResultDto>> apiRPCResult;
        List<OrderSkuStatisticsResultDto> data;
        Map<Long, Integer> tempCsuIdToNumMap;
        for (List<Long> csuIdsList : csuIdsLists) {
            try {
                queryParam.setSkuIds(csuIdsList);
                apiRPCResult = orderSkuStatisticsApi.orderSkuStatistics(queryParam);
                if (log.isDebugEnabled()) {
                    log.debug("批量查询过去某个时间段内商品购买人数，入参：{}，出参：{}", JSONObject.toJSONString(queryParam),
                            JSONObject.toJSONString(apiRPCResult));
                }
                if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
                    log.error("批量查询过去某个时间段内商品购买人数失败，入参：{}，出参：{}", JSONObject.toJSONString(queryParam),
                            JSONObject.toJSONString(apiRPCResult));
                    continue;
                }
                data = apiRPCResult.getData();
                if (CollectionUtils.isEmpty(data)) {
                    continue;
                }
                tempCsuIdToNumMap = data.stream()
                        .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getSkuId()) && Objects.nonNull(item.getMerchantCount()))
                        .collect(Collectors.toMap(OrderSkuStatisticsResultDto::getSkuId, OrderSkuStatisticsResultDto::getMerchantCount));
                if (MapUtils.isEmpty(tempCsuIdToNumMap)) {
                    continue;
                }
                csuIdToNumMap.putAll(tempCsuIdToNumMap);
            } catch (Exception e) {
                log.error("批量查询过去某个时间段内商品购买人数出现异常，入参：{}", JSONObject.toJSONString(queryParam), e);
                continue;
            }
        }
        return csuIdToNumMap;
    }

    @Override
    public Map<String, OrderSkuStatisticsResultDto> mgetCsuMasterStandardIdSaleInfo(List<String> masterStandardIds) {
        if (CollectionUtils.isEmpty(masterStandardIds)) {
            return Maps.newHashMap();
        }
        Set<String> masterStandardIdsSet = Sets.newHashSet(masterStandardIds);
        masterStandardIdsSet.remove(null);
        masterStandardIdsSet.remove("");
        if (CollectionUtils.isEmpty(masterStandardIdsSet)) {
            return Maps.newHashMap();
        }
        Map<String, OrderSkuStatisticsResultDto> masterStandardIdToSaleInfoMap = Maps.newHashMapWithExpectedSize(16);
        List<List<String>> masterStandardIdsLists = Lists.partition(Lists.newArrayList(masterStandardIdsSet), 100);
        OrderSkuStatisticsQueryDto queryParam = new OrderSkuStatisticsQueryDto();
        ApiRPCResult<List<OrderSkuStatisticsResultDto>> apiRPCResult;
        List<OrderSkuStatisticsResultDto> data;
        Map<String, OrderSkuStatisticsResultDto> tempMasterStandardIdToSaleInfoMap;
        for (List<String> masterStandardIdsList : masterStandardIdsLists) {
            try {
                queryParam.setMasterStandardIds(masterStandardIdsList);
                apiRPCResult = orderSkuStatisticsApi.orderSkuStatistics(queryParam);
                if (log.isDebugEnabled()) {
                    log.debug("批量查询商品主标准库ID的销售信息，入参：{}，出参：{}", JSONObject.toJSONString(queryParam),
                            JSONObject.toJSONString(apiRPCResult));
                }
                if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
                    log.error("批量查询商品主标准库ID的销售信息失败，入参：{}，出参：{}", JSONObject.toJSONString(queryParam),
                            JSONObject.toJSONString(apiRPCResult));
                    continue;
                }
                data = apiRPCResult.getData();
                if (CollectionUtils.isEmpty(data)) {
                    continue;
                }
                tempMasterStandardIdToSaleInfoMap = data.stream()
                        .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getMasterStandardId()))
                        .collect(Collectors.toMap(OrderSkuStatisticsResultDto::getMasterStandardId, Function.identity(), (f, s) -> f));
                if (MapUtils.isEmpty(tempMasterStandardIdToSaleInfoMap)) {
                    continue;
                }
                masterStandardIdToSaleInfoMap.putAll(tempMasterStandardIdToSaleInfoMap);
            } catch (Exception e) {
                log.error("批量查询商品主标准库ID的销售信息出现异常，入参：{}", JSONObject.toJSONString(queryParam), e);
                continue;
            }
        }
        return masterStandardIdToSaleInfoMap;
    }

}
