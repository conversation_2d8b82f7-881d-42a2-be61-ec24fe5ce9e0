package com.xyy.ec.pc.recommend.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.data.core.metadata.IPage;
import com.xyy.ec.marketing.hyperspace.api.dto.OneClickRestockCouponDTO;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.common.enums.AppEventTrackingPropertyKeyEnum;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.recommend.dto.RecommendProcessResult;
import com.xyy.ec.pc.recommend.dto.RecommendQtListDataDTO;
import com.xyy.ec.pc.recommend.helpers.*;
import com.xyy.ec.pc.recommend.params.PcRecommendParam;
import com.xyy.ec.pc.recommend.params.PcRecommendPayParam;
import com.xyy.ec.pc.recommend.service.EcpPcRecommendService;
import com.xyy.ec.pc.recommend.vo.PcRecommendCardVO;
import com.xyy.ec.pc.rpc.HyperSpaceRpc;
import com.xyy.ec.pc.search.config.SearchProperties;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.pc.util.excel.ExcelExport;
import com.xyy.ec.pc.util.excel.ExportExcelEntity;
import com.xyy.recommend.ecp.api.EcpRecommendApi;
import com.xyy.recommend.ecp.api.EcpRecommendPayApi;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSceneEnum;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendTerminalTypeEnum;
import com.xyy.recommend.ecp.params.EcpRecommendPayQueryParam;
import com.xyy.recommend.ecp.params.EcpRecommendQueryParam;
import com.xyy.recommend.ecp.result.EcpRecommendBaseCardDTO;
import com.xyy.recommend.ecp.result.EcpRecommendPayResult;
import com.xyy.recommend.ecp.result.EcpRecommendResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.*;

/**
 * 推荐接口V2
 */
@RestController
@RequestMapping("/pc/recommend/v3")
@Slf4j
public class PcRecommendV3Controller extends BaseController {

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private EcpPcRecommendService ecpPcRecommendService;

    @Autowired
    private HyperSpaceRpc hyperSpaceRpc;

    @Reference(version = "1.0.0")
    private EcpRecommendApi ecpRecommendApi;

    @Reference(version = "1.0.0")
    private EcpRecommendPayApi ecpRecommendPayApi;

    @Autowired
    private PcVersionUtils pcVersionUtils;

    @Autowired
    private SearchProperties searchProperties;

    /**
     * 搜索首页
     *
     * @param request
     * @return
     */
    @RequestMapping("/oneClickRestock.htm")
    public ModelAndView skuInfo(HttpServletRequest request) {
        Long merchantId = null;
        try {
            Map<String, Object> objModel = new HashMap<>();
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (Objects.nonNull(merchant)) {
                objModel.put("merchantId", merchant.getMerchantId());
            }
            return new ModelAndView("/order/oneClickRestock.ftl", objModel);
        } catch (Exception e) {
            log.error("搜索首页，/pc/recommend/v3/oneClickRestock.htm 出现异常，merchantId：{}", merchantId, e);
            return new ModelAndView("/error/500.ftl");
        }
    }


    @ResponseBody
    @RequestMapping(value = "/listProducts", method = RequestMethod.POST)
    public XyyJsonResult listProducts(PcRecommendParam recommendParam, HttpServletRequest request) {
        Long accountId = null;
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (Objects.isNull(merchant)) {
                // 未登录 提示鉴权异常
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }

            accountId = merchant.getAccountId();
            merchantId = merchant.getMerchantId();

            if (log.isDebugEnabled()) {
                log.debug("推荐（V3），accountId：{}，merchantId：{}，recommendParam：{}", accountId, merchantId, JSONObject.toJSONString(recommendParam));
            }
            // 参数校验
            boolean validate = PcRecommendParamParamHelper.validate(recommendParam);
            if (BooleanUtils.isNotTrue(validate)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "推荐参数非法");
            }
            /* 埋点 */
            if (Objects.isNull(recommendParam.getScmId())) {
                recommendParam.setScmId(RandomStringUtils.randomAlphanumeric(8));
            }
            // 调用核心业务逻辑方法
            RecommendProcessResult processResult = processRecommendLogic(recommendParam, merchant, accountId, merchantId);

            if (!processResult.isSuccess()) {
                return processResult.getFailureResult();
            }

            EcpRecommendResult recommendResult = processResult.getRecommendResult();
            IPage<EcpRecommendBaseCardDTO> cardPage = recommendResult.getCardPage();
            String expId = recommendResult.getRecommendStrategyCode();
            boolean isEnd = (long) recommendParam.getPageNum() * recommendParam.getPageSize() >= cardPage.getTotalCount();
            List<PcRecommendCardVO> rows = processResult.getRows();
            Integer licenseStatus = processResult.getLicenseStatus();
            /* 埋点数据补全 */
            RecommendQuickTrackingDataHelper.handleRecommendQuickTrackingData(rows, cardPage.getPageNo(), cardPage.getPageSize());
            /* feed流 */
            recommendParam.setPageNum(recommendParam.getPageNum() + 1);
            //埋点数据
            RecommendQtListDataDTO qtListDataDTO = RecommendQtListDataDTO.builder()
                    .exp_id(recommendResult.getRecommendStrategyCode())
                    .result_cnt(cardPage.getTotalCount())
                    .page_no(cardPage.getPageNo())
                    .page_size(cardPage.getPageSize())
                    .total_page(cardPage.getPages())
                    .build();
            /* 组装响应数据 */
            return XyyJsonResult.createSuccess()
                    .addResult("expId", expId)
                    .addResult("rows", rows)
                    .addResult("requestParam", recommendParam)
                    .addResult("licenseStatus", licenseStatus)
                    .addResult("isEnd", isEnd)
                    .addResult("pageNo", cardPage.getPageNo())
                    .addResult("pageSize", cardPage.getPageSize())
                    .addResult("totalPage", cardPage.getPages())
                    .addResult("totalCount", cardPage.getTotalCount())
                    .addResult("qtListData", JSONObject.toJSONString(qtListDataDTO))
                    .addResult(AppEventTrackingPropertyKeyEnum.SCMID.getPropertyKey(), recommendParam.getScmId());
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("推荐（V3）失败，accountId：{}，merchantId：{}，terminalType：{}，，recommendParam：{}，msg：{}，异常信息：",
                        accountId, merchantId, EcpRecommendTerminalTypeEnum.PC, JSONObject.toJSONString(recommendParam), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("推荐（V3）失败，accountId：{}，merchantId：{}，terminalType：{}，recommendParam：{}，异常信息：",
                    accountId, merchantId, EcpRecommendTerminalTypeEnum.PC, JSONObject.toJSONString(recommendParam), e);
            return XyyJsonResult.createFailure().msg("搜索商品失败，请稍后重试");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/pay/listProducts", method = RequestMethod.POST)
    public XyyJsonResult payListProducts(PcRecommendPayParam recommendParam, HttpServletRequest request) {
        Long accountId = null;
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (Objects.isNull(merchant)) {
                // 未登录 提示鉴权异常
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }

            accountId = merchant.getAccountId();
            merchantId = merchant.getMerchantId();

            if (log.isDebugEnabled()) {
                log.debug("推荐-支付成功页（V3），accountId：{}，merchantId：{}，recommendParam：{}", accountId, merchantId, JSONObject.toJSONString(recommendParam));
            }
            // 参数校验
            boolean validate = PcRecommendPayParamHelper.validate(recommendParam);
            if (BooleanUtils.isNotTrue(validate)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "推荐参数非法");
            }
            /* 埋点 */
            if (Objects.isNull(recommendParam.getScmId())) {
                recommendParam.setScmId(RandomStringUtils.randomAlphanumeric(8));
            }

            EcpRecommendPayQueryParam ecpRecommendQueryParam = EcpRecommendPayParamHelper.create(recommendParam, merchantId, accountId);
            ApiRPCResult<EcpRecommendPayResult> apiRPCResult = ecpRecommendPayApi.recommend(ecpRecommendQueryParam);
            if (log.isDebugEnabled()) {
                log.debug("推荐-支付成功页（V3），accountId：{}，merchantId：{}，ecpRecommendQueryParam：{}，apiRPCResult：{}",
                        accountId, merchantId, JSONObject.toJSONString(ecpRecommendQueryParam), JSONObject.toJSONString(apiRPCResult));
            }

            if (apiRPCResult.isFail()) {
                log.error("推荐-支付成功页（V3）失败，accountId：{}，merchantId：{}，recommendParam：{}，api异常信息：{}",
                        accountId, merchantId, JSONObject.toJSONString(recommendParam), JSONObject.toJSONString(apiRPCResult));
                return XyyJsonResult.createFailure().code(apiRPCResult.getCode()).msg(apiRPCResult.getMsg());
            }

            EcpRecommendPayResult recommendResult = apiRPCResult.getData();
            IPage<EcpRecommendBaseCardDTO> cardPage = recommendResult.getCardPage();
            /* 资质处理 */
            pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            Integer licenseStatus = merchant.getLicenseStatus();
            Boolean priceDisplayFlag = merchant.getPriceDisplayFlag();
            String branchCode = merchant.getRegisterCode();
            /* 数据结构转换和填充相关信息 */
            boolean userOneClickReplenishment = Objects.equals(EcpRecommendSceneEnum.USER_ONE_CLICK_REPLENISHMENT.getScene(), recommendParam.getRecommendScene());
            List<PcRecommendCardVO> rows = ecpPcRecommendService.convertAndFillRecommendCards(merchantId, branchCode, priceDisplayFlag, userOneClickReplenishment, cardPage.getRecordList());
            if (log.isDebugEnabled()) {
                log.debug("推荐-支付成功页（V3），accountId：{}，merchantId：{}，branchCode：{}，priceDisplayFlag：{}，rows：{}",
                        accountId, merchantId, branchCode, priceDisplayFlag, JSONArray.toJSONString(rows));
            }

            String expId = recommendResult.getRecommendStrategyCode();
            boolean isEnd = (long) recommendParam.getPageNum() * recommendParam.getPageSize() >= cardPage.getTotalCount();

            /* 埋点数据补全 */
            RecommendQuickTrackingDataHelper.handleRecommendQuickTrackingData(rows, cardPage.getPageNo(), cardPage.getPageSize());
            /* feed流 */
            recommendParam.setPageNum(recommendParam.getPageNum() + 1);
            //埋点数据
            RecommendQtListDataDTO qtListDataDTO = RecommendQtListDataDTO.builder()
                    .exp_id(recommendResult.getRecommendStrategyCode())
                    .result_cnt(cardPage.getTotalCount())
                    .page_no(cardPage.getPageNo())
                    .page_size(cardPage.getPageSize())
                    .total_page(cardPage.getPages())
                    .build();
            /* 组装响应数据 */
            return XyyJsonResult.createSuccess()
                    .addResult("expId", expId)
                    .addResult("rows", rows)
                    .addResult("requestParam", recommendParam)
                    .addResult("licenseStatus", licenseStatus)
                    .addResult("isEnd", isEnd)
                    .addResult("pageNo", cardPage.getPageNo())
                    .addResult("pageSize", cardPage.getPageSize())
                    .addResult("totalPage", cardPage.getPages())
                    .addResult("totalCount", cardPage.getTotalCount())
                    .addResult("qtListData", JSONObject.toJSONString(qtListDataDTO))
                    .addResult(AppEventTrackingPropertyKeyEnum.SCMID.getPropertyKey(), recommendParam.getScmId());
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("推荐-支付成功页（V3）失败，accountId：{}，merchantId：{}，terminalType：{}，，recommendParam：{}，msg：{}，异常信息：",
                        accountId, merchantId, EcpRecommendTerminalTypeEnum.PC, JSONObject.toJSONString(recommendParam), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("推荐-支付成功页（V3）失败，accountId：{}，merchantId：{}，terminalType：{}，recommendParam：{}，异常信息：",
                    accountId, merchantId, EcpRecommendTerminalTypeEnum.PC, JSONObject.toJSONString(recommendParam), e);
            return XyyJsonResult.createFailure().msg("搜索商品失败，请稍后重试");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/saas/listProducts", method = RequestMethod.POST)
    public XyyJsonResult saasListProducts(PcRecommendParam recommendParam, HttpServletRequest request) {
        Long accountId = null;
        Long merchantId = null;
        try {
            String saasOrganSign = (String) request.getAttribute("saasOrganSign");
            Integer merchantProvinceCode = (Integer) request.getAttribute("merchantProvinceCode");
            Integer bindStatus = (Integer) request.getAttribute("bindStatus");
            recommendParam.setSaasOrganSign(saasOrganSign);
            recommendParam.setMerchantProvinceCode(merchantProvinceCode);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (Objects.isNull(merchant) && StringUtils.isEmpty(recommendParam.getSaasOrganSign())) {
                // 未登录 提示鉴权异常
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }
            // 判断是否绑定 如果绑定了才取
            if (Objects.nonNull(merchant)&&Objects.equals(bindStatus,1)) {
                accountId = merchant.getAccountId();
                merchantId = merchant.getMerchantId();
            }

            if (!Objects.equals(bindStatus,1)) {
                merchant = null;
            }

            if (log.isDebugEnabled()) {
                log.debug("推荐（V3），accountId：{}，merchantId：{}，recommendParam：{}", accountId, merchantId, JSONObject.toJSONString(recommendParam));
            }
            // 参数校验
            boolean validate = PcRecommendParamParamHelper.validate(recommendParam);
            if (BooleanUtils.isNotTrue(validate)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "推荐参数非法");
            }
            /* 埋点 */
            if (Objects.isNull(recommendParam.getScmId())) {
                recommendParam.setScmId(RandomStringUtils.randomAlphanumeric(8));
            }
            // 调用核心业务逻辑方法
            RecommendProcessResult processResult = processRecommendLogic(recommendParam, merchant, accountId, merchantId);

            if (!processResult.isSuccess()) {
                return processResult.getFailureResult();
            }

            EcpRecommendResult recommendResult = processResult.getRecommendResult();
            IPage<EcpRecommendBaseCardDTO> cardPage = recommendResult.getCardPage();
            String expId = recommendResult.getRecommendStrategyCode();
            boolean isEnd = (long) recommendParam.getPageNum() * recommendParam.getPageSize() >= cardPage.getTotalCount();
            List<PcRecommendCardVO> rows = processResult.getRows();
            Integer licenseStatus = processResult.getLicenseStatus();
            /* 埋点数据补全 */
            RecommendQuickTrackingDataHelper.handleRecommendQuickTrackingData(rows, cardPage.getPageNo(), cardPage.getPageSize());
            /* feed流 */
            recommendParam.setPageNum(recommendParam.getPageNum() + 1);
            //埋点数据
            RecommendQtListDataDTO qtListDataDTO = RecommendQtListDataDTO.builder()
                    .exp_id(recommendResult.getRecommendStrategyCode())
                    .result_cnt(cardPage.getTotalCount())
                    .page_no(cardPage.getPageNo())
                    .page_size(cardPage.getPageSize())
                    .total_page(cardPage.getPages())
                    .build();
            /* 组装响应数据 */
            return XyyJsonResult.createSuccess()
                    .addResult("expId", expId)
                    .addResult("rows", rows)
                    .addResult("requestParam", recommendParam)
                    .addResult("licenseStatus", licenseStatus)
                    .addResult("isEnd", isEnd)
                    .addResult("pageNo", cardPage.getPageNo())
                    .addResult("pageSize", cardPage.getPageSize())
                    .addResult("totalPage", cardPage.getPages())
                    .addResult("totalCount", cardPage.getTotalCount())
                    .addResult("qtListData", JSONObject.toJSONString(qtListDataDTO))
                    .addResult(AppEventTrackingPropertyKeyEnum.SCMID.getPropertyKey(), recommendParam.getScmId());
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("推荐（V3）失败，accountId：{}，merchantId：{}，terminalType：{}，，recommendParam：{}，msg：{}，异常信息：",
                        accountId, merchantId, EcpRecommendTerminalTypeEnum.PC, JSONObject.toJSONString(recommendParam), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("推荐（V3）失败，accountId：{}，merchantId：{}，terminalType：{}，recommendParam：{}，异常信息：",
                    accountId, merchantId, EcpRecommendTerminalTypeEnum.PC, JSONObject.toJSONString(recommendParam), e);
            return XyyJsonResult.createFailure().msg("搜索商品失败，请稍后重试");
        }
    }

    @RequestMapping(value = "/saas/exportListProducts")
    @ResponseBody
    public Object exportListProducts(PcRecommendParam recommendParam, HttpServletRequest request, HttpServletResponse response) {
        Long accountId = null;
        Long merchantId = null;
        try {
            String saasOrganSign = (String) request.getAttribute("saasOrganSign");
            Integer merchantProvinceCode = (Integer) request.getAttribute("merchantProvinceCode");
            Integer bindStatus = (Integer) request.getAttribute("bindStatus");
            recommendParam.setSaasOrganSign(saasOrganSign);
            recommendParam.setMerchantProvinceCode(merchantProvinceCode);
            recommendParam.setPageNum(1);
            recommendParam.setPageSize(50);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (Objects.isNull(merchant) && StringUtils.isEmpty(recommendParam.getSaasOrganSign())) {
                // 未登录 提示鉴权异常
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }

            // 判断是否绑定 如果绑定了才取
            if (Objects.nonNull(merchant)&&Objects.equals(bindStatus,1)) {
                accountId = merchant.getAccountId();
                merchantId = merchant.getMerchantId();
            }

            if (!Objects.equals(bindStatus,1)) {
                merchant = null;
            }


            if (log.isDebugEnabled()) {
                log.debug("导出推荐商品（V3），accountId：{}，merchantId：{}，recommendParam：{}", accountId, merchantId, JSONObject.toJSONString(recommendParam));
            }
            // 参数校验
            boolean validate = PcRecommendParamParamHelper.validate(recommendParam);
            if (BooleanUtils.isNotTrue(validate)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "推荐参数非法");
            }
            /* 埋点 */
            if (Objects.isNull(recommendParam.getScmId())) {
                recommendParam.setScmId(RandomStringUtils.randomAlphanumeric(8));
            }

            // 循环获取所有数据，直到达到200条上限
            List<PcRecommendCardVO> allRows = Lists.newArrayList();
            int maxTotalRecords = searchProperties.getSaasRecommendDownloadNum(); // 最多导出200条
            boolean hasMoreData = true;

            while (hasMoreData && allRows.size() < maxTotalRecords) {
                // 调用核心业务逻辑方法
                RecommendProcessResult processResult = processRecommendLogic(recommendParam, merchant, accountId, merchantId);

                if (!processResult.isSuccess()) {
                    return processResult.getFailureResult();
                }

                // 获取当前页数据
                List<PcRecommendCardVO> currentPageRows = processResult.getRows();

                // 如果当前页没有数据，说明没有更多数据了
                if (currentPageRows.isEmpty()) {
                    hasMoreData = false;
                    break;
                }

                // 添加当前页数据到总列表，但不超过200条
                int remainingSpace = maxTotalRecords - allRows.size();
                if (currentPageRows.size() <= remainingSpace) {
                    allRows.addAll(currentPageRows);
                } else {
                    // 只添加剩余需要的数量
                    allRows.addAll(currentPageRows.subList(0, remainingSpace));
                    break; // 已达到最大数量限制
                }

                // 检查是否还有更多数据
                EcpRecommendResult recommendResult = processResult.getRecommendResult();
                IPage<EcpRecommendBaseCardDTO> cardPage = recommendResult.getCardPage();
                long totalCount = cardPage.getTotalCount();
                long fetchedCount = (long) recommendParam.getPageNum() * recommendParam.getPageSize();

                // 如果已获取的数据量大于等于总数据量，则没有更多数据
                if (fetchedCount >= totalCount) {
                    hasMoreData = false;
                }

                // 准备下一页的请求
                recommendParam.setPageNum(recommendParam.getPageNum() + 1);
            }

            if (CollectionUtils.isEmpty(allRows)) {
                return XyyJsonResult.createFailure().msg("商品数据为空");
            }

            // 写入excel并导出
            List<ExportExcelEntity> sheets = new ArrayList<>();
            String month = LocalDate.now().getYear() + "年" + LocalDate.now().getMonthValue() + "月";
            // 写入明细
            sheets.add(
                    ExportExcelEntity.builder()
                            .sheetName("商品信息")
                            .columnWidth(7000)
                            .flag(false)
                            .fieldNames(new String[]{"shopName", "spec", "manufacturer", "price"})
                            .headers(new String[]{"商品名称", "规格", "厂家", "价格"})
                            .dataList(PcRecommendProductExportHelper.create(allRows))
                            .build()
            );
            ExcelExport.exportSheets(response, "品种推荐" + month + ".xlsx", sheets);
            return XyyJsonResult.createSuccess().msg("导出成功");
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("导出推荐商品（V3）失败，accountId：{}，merchantId：{}，terminalType：{}，recommendParam：{}，msg：{}，异常信息：",
                        accountId, merchantId, EcpRecommendTerminalTypeEnum.PC, JSONObject.toJSONString(recommendParam), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("导出推荐商品（V3）失败，accountId：{}，merchantId：{}，terminalType：{}，recommendParam：{}，异常信息：",
                    accountId, merchantId, EcpRecommendTerminalTypeEnum.PC, JSONObject.toJSONString(recommendParam), e);
            return XyyJsonResult.createFailure().msg("导出商品失败，请稍后重试");
        }
    }


    /**
     * 药帮忙商城saas用户是否可领取补货券
     *
     * @param request HttpServletRequest对象
     * @return XyyJsonResult 返回是否可领取补货券
     */
    @ResponseBody
    @GetMapping(value = "/saas/canReceiveRestockCoupon")
    public XyyJsonResult canReceiveRestockCoupon(HttpServletRequest request) {
        try {
            Integer bindStatus = (Integer) request.getAttribute("bindStatus");
            if (!Objects.equals(bindStatus,1)) {
                return XyyJsonResult.create().addResult("canReceive", false);
            }
            // 检查用户身份
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            // 如果商户为空且saas机构标识为空，则返回未登录错误
            if (Objects.isNull(merchant)) {
                return XyyJsonResult.create().addResult("canReceive", false);
            }

            if (log.isDebugEnabled()) {
                log.debug("检查是否可领取补货券，merchantId：{}", merchant.getMerchantId());
            }

            boolean canReceive = checkCanReceiveRestockCoupon(merchant);

            // 组装响应数据
            return XyyJsonResult.createSuccess()
                    .addResult("canReceive", canReceive)
                    .addResult("merchantId", merchant.getMerchantId());

        } catch (AppException e) {
            log.error("检查是否可领取补货券失败，异常信息：", e);
            return XyyJsonResult.create().addResult("canReceive", false);
        } catch (Exception e) {
            log.error("检查是否可领取补货券出现异常：", e);
            return XyyJsonResult.create().addResult("canReceive", false);
        }
    }

    /**
     * 检查是否可领取补货券的实际业务逻辑
     *
     * @param merchant 商户信息
     * @return boolean 是否可领取
     */
    private boolean checkCanReceiveRestockCoupon(MerchantBussinessDto merchant) {
        if (Objects.isNull(merchant) || Objects.isNull(merchant.getMerchantId())) {
            return false;
        }

        OneClickRestockCouponDTO oneClickRestockCoupon = hyperSpaceRpc.getOneClickRestockCoupon(merchant.getMerchantId());
        if (Objects.isNull(oneClickRestockCoupon)) {
            return false;
        }
        //1-未领取，2-已领取
        return Objects.equals(oneClickRestockCoupon.getState(), 1) || Objects.equals(oneClickRestockCoupon.getState(), 2);
    }


    /**
     * 推荐核心业务逻辑处理方法
     *
     * @param recommendParam 推荐参数
     * @param merchant       商户信息
     * @param accountId      账户ID
     * @param merchantId     商户ID
     * @return 处理结果
     */
    private RecommendProcessResult processRecommendLogic(PcRecommendParam recommendParam, MerchantBussinessDto merchant, Long accountId, Long merchantId) {
        try {
            EcpRecommendQueryParam ecpRecommendQueryParam = EcpRecommendParamHelper.create(recommendParam, merchantId, accountId);
            ApiRPCResult<EcpRecommendResult> apiRPCResult = ecpRecommendApi.recommend(ecpRecommendQueryParam);
            if (log.isDebugEnabled()) {
                log.debug("推荐（V3），accountId：{}，merchantId：{}，ecpRecommendQueryParam：{}，apiRPCResult：{}",
                        accountId, merchantId, JSONObject.toJSONString(ecpRecommendQueryParam), JSONObject.toJSONString(apiRPCResult));
            }

            if (apiRPCResult.isFail()) {
                log.error("推荐（V3）失败，accountId：{}，merchantId：{}，recommendParam：{}，api异常信息：{}",
                        accountId, merchantId, JSONObject.toJSONString(recommendParam), JSONObject.toJSONString(apiRPCResult));
                return RecommendProcessResult.failure(XyyJsonResult.createFailure().code(apiRPCResult.getCode()).msg(apiRPCResult.getMsg()));
            }

            EcpRecommendResult recommendResult = apiRPCResult.getData();
            IPage<EcpRecommendBaseCardDTO> cardPage = recommendResult.getCardPage();
            List<PcRecommendCardVO> rows;
            Integer licenseStatus = null;

            if (Objects.nonNull(merchant)) {
                /* 资质处理 */
                pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
                licenseStatus = merchant.getLicenseStatus();
                Boolean priceDisplayFlag = merchant.getPriceDisplayFlag();
                String branchCode = merchant.getRegisterCode();
                /* 数据结构转换和填充相关信息 */
                boolean userOneClickReplenishment = Objects.equals(EcpRecommendSceneEnum.USER_ONE_CLICK_REPLENISHMENT.getScene(), recommendParam.getRecommendScene());
                rows = ecpPcRecommendService.convertAndFillRecommendCards(merchantId, branchCode, priceDisplayFlag, userOneClickReplenishment, cardPage.getRecordList());
                if (log.isDebugEnabled()) {
                    log.debug("推荐（V3），accountId：{}，merchantId：{}，branchCode：{}，priceDisplayFlag：{}，rows：{}",
                            accountId, merchantId, branchCode, priceDisplayFlag, JSONArray.toJSONString(rows));
                }
            } else {
                /* 数据结构转换和填充相关信息 */
                rows = ecpPcRecommendService.convertAndFillRecommendCards(recommendParam.getSaasOrganSign(), cardPage.getRecordList());
                if (log.isDebugEnabled()) {
                    log.debug("推荐（V3） saasOrganSign：{}，rows：{}", recommendParam.getSaasOrganSign(), JSONArray.toJSONString(rows));
                }
            }

            return RecommendProcessResult.success(recommendResult, rows, licenseStatus);
        } catch (Exception e) {
            log.error("处理推荐逻辑时发生异常，accountId：{}，merchantId：{}，recommendParam：{}", accountId, merchantId, JSONObject.toJSONString(recommendParam), e);
            return RecommendProcessResult.failure(XyyJsonResult.createFailure().msg("处理推荐数据失败"));
        }
    }

}
