package com.xyy.ec.pc.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xyy.ec.merchant.server.dto.MerchantAndAccountDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRelStatusEnum;
import com.xyy.ec.merchant.server.enums.LoginAccountStatusEnum;
import com.xyy.ec.pc.authentication.consts.Constants;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.authentication.utils.ServletUtils;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.PasswordVerifier;
import com.xyy.ec.pc.enums.*;
import com.xyy.ec.pc.remote.AccountProviderService;
import com.xyy.ec.pc.rpc.SaasServiceRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.saas.user.center.api.pojo.response.TokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.Cookie;

/**
 * @Description
 * @Date 2025/7/30 17:08
 */
@Slf4j
@Controller
@RequestMapping("/saas")
public class SaasLoginController extends BaseController {

    @Autowired
    private SaasServiceRpc saasServiceRpc;

    @Autowired
    private AccountProviderService accountProviderService;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private TokenService tokenService;

    @ResponseBody
    @PostMapping(value = "/login")
    public Object login(@RequestParam("accessToken") String accessToken) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("saas授权登录ec，accessToken：{}", accessToken);
        }
        if (StringUtil.isEmpty(accessToken)) {
            return this.addError("参数错误");
        }
        try {
            TokenResponse tokenResponse = saasServiceRpc.validateAccessToken(accessToken);
            if (ObjectUtil.isNull(tokenResponse) || BooleanUtils.isNotTrue(tokenResponse.getValidOrNot())) {
                return this.addError(CODE_RETURN_TO_LOGIN, "accessToken无效");
            }

            String saasOrganSign = tokenResponse.getOrganSign();
            String syncNo = tokenResponse.getBindCode();
            String provinceCode = tokenResponse.getProvinceCode();

            // 生成saa_qt_session，埋点用
            this.writeCookie(Constants.SAAS_QT_SESSION_KET, StrUtil.format("{}_{}", RandomStringUtils.randomAlphanumeric(8), System.currentTimeMillis()));
            //标记当前请求已鉴权
            this.writeCookie(Constants.SAAS_REQUEST_PHASE, RequestPhaseEnum.Authorized.getCode());
            this.writeCookie(Constants.SAAS_ORGAN_SIGN, saasOrganSign);

            //情况1：当前Saas用户未绑定Ec用户，标记为未绑定类型
            if (StringUtil.isEmpty(syncNo)) {
                this.writeAbnormalCookie(AbnormalAccountTypeEnum.NOT_BIND_EC.getCode(), provinceCode, "");
                return this.addResult("登录成功！");
            }

            //情况2：如果Saas用户已经绑定Ec用户且和当前登录的是同一个用户，直接使用当前登录信息
            JwtPrincipal checkPrincipal = (JwtPrincipal) xyyIndentityValidator.currentPrincipal();
            if (ObjectUtil.isNotNull(checkPrincipal) && ObjectUtil.isNotNull(checkPrincipal.getMerchantId())
                    && StringUtils.equals(syncNo, ("SYNC" + checkPrincipal.getMerchantId()))) {
                //如果登录的是同一个用户，则使用当前登录的用户，并刷新登录信息缓存
                tokenService.refreshToken(checkPrincipal);
                this.clearAbnormalCookie();
                return this.addResult("登录成功！");
            }

            //未登录或登录的不是同一个用户，需要找到店长账号，执行登录
            MerchantAndAccountDto merchantAndAccountDto = accountProviderService.queryManagerAccountBySyncNo(syncNo);
            //情况3：如果账号被冻结，标记账号异常类型
            Boolean checked = checkAccountStatus(merchantAndAccountDto);
            if (BooleanUtils.isFalse(checked)) {
                //在Cookie写入异常账号标记
                this.writeAbnormalCookie(AbnormalAccountTypeEnum.FORBIDDEN.getCode(), provinceCode, syncNo);
                return this.addResult("登录成功！");
            }
            //情况4：账号正常，执行登录操作
            PasswordVerifier verifier = new PasswordVerifier(merchantAndAccountDto.getMobile(), merchantAndAccountDto.getPassword());
            JwtPrincipal loginPrincipal = (JwtPrincipal) xyyIndentityValidator.login(verifier);
            if (ObjectUtil.isNull(loginPrincipal)) {
                return this.addError(CODE_RETURN_TO_LOGIN, "登录失败，请重试！");
            }
            JwtPrincipal jwtPrincipal = (JwtPrincipal) xyyIndentityValidator.setPrincipalMerchant(merchantAndAccountDto.getMerchantId(),
                    loginPrincipal.getAccountId());
            this.clearAbnormalCookie();
            return this.addResult("登录成功！");
        } catch (Exception e) {
            return this.addError(CODE_RETURN_TO_LOGIN, "登录异常，请重试！");
        }
    }


    /**
     * 在Cookie写入账号标记
     *
     * @param provinceCode
     */
    private void writeAbnormalCookie(String abnormalAccountType, String provinceCode, String syncNo) {
        //在Cookie写入账号标记
        writeCookie(Constants.ABNORMAL_ACCOUNT_TYPE, abnormalAccountType);
        writeCookie(Constants.SAAS_PROVINCE_CODE, provinceCode);
        if (StringUtil.isNotEmpty(syncNo)) {
            writeCookie(Constants.SAAS_SYNC_NO, syncNo);
        }
    }

    /**
     * 清除Cookie异常账号标记
     */
    private void clearAbnormalCookie() {
        ServletUtils.removeCookie(Constants.ABNORMAL_ACCOUNT_TYPE, "/");
        ServletUtils.removeCookie(Constants.SAAS_PROVINCE_CODE, "/");
        ServletUtils.removeCookie(Constants.SAAS_SYNC_NO, "/");
    }

    /**
     * 创建并写入Cookie
     *
     * @param key
     * @param value
     */
    private void writeCookie(String key, String value) {
        Cookie syncNoCookie = ServletUtils.createCookie(key, value, Constants.COOKIE_MAX_AGE);
        ServletUtils.writeCookie(syncNoCookie);
    }


    /**
     * 校验账号是否有效
     *
     * @param merchantAndAccountDto
     * @return
     */
    private Boolean checkAccountStatus(MerchantAndAccountDto merchantAndAccountDto) {

        if (ObjectUtil.isNull(merchantAndAccountDto)) {
            return false;
        }
        if (ObjectUtil.isNull(merchantAndAccountDto.getAccountId())) {
            return false;
        }
        //账号状态是激活状态
        if (ObjectUtil.notEqual(merchantAndAccountDto.getMerchantStatus(), MerchantStatusEnum.STATUS_NORMAL.getId())) {
            return false;
        }
        //资质状态是已通过
        if (ObjectUtil.notEqual(merchantAndAccountDto.getLicenseStatus(), LicenseStatusEnum.PASSED.getId())) {
            return false;
        }
        //店铺审核状态是审核通过
        if (ObjectUtil.notEqual(merchantAndAccountDto.getAuditStatus(), AuditStatusEnum.PASSED.getId())) {
            return false;
        }
        //账号关系状态是审核通过
        if (ObjectUtil.isNotNull(merchantAndAccountDto.getAccountRelStatus()) && ObjectUtil.notEqual(merchantAndAccountDto.getAccountRelStatus(), AccountMerchantRelStatusEnum.PASS.getValue())) {
            return false;
        }
        //账号状态是激活状态
        if (ObjectUtil.isNotNull(merchantAndAccountDto.getAccountStatus()) && ObjectUtil.notEqual(merchantAndAccountDto.getAccountStatus(), LoginAccountStatusEnum.Valid.getValue())) {
            return false;
        }
        //账号密码不为空
        if (StringUtil.isEmpty(merchantAndAccountDto.getMobile()) || StringUtil.isEmpty(merchantAndAccountDto.getPassword())) {
            return false;
        }
        return true;
    }


}
