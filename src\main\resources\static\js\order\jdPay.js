var obj;
var timesRun = 0;
$(function() {
	function IEVersion() {
		var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
		var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
		var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
		var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
		if(isIE) {
			var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
			reIE.test(userAgent);
			var fIEVersion = parseFloat(RegExp["$1"]);
			if(fIEVersion <= 8) {
	          return  false;
			}
			} else if(isEdge) {
				return  false
			} else{
				return true;//不是ie浏览器
			}
	}
	var ieBoole=IEVersion();
	var imageUrl = $("#imageUrl").val();
	if(!ieBoole){
		$('#code').qrcode({
			render: "table",
			width: 200, //宽度
			height:200, //高度
			text: imageUrl
		});

	}else{
		$('#code').qrcode({
			width: 200, //宽度
			height:200, //高度
			text: imageUrl
		});
	}
	obj=setInterval(checkIsPay,15000);
});

function checkIsPay(){
	timesRun += 1;
	if(timesRun === 40){
		// 循环40次后，销毁循环，不在浪费系统资源
		clearInterval(obj);
		return;
	}else {
		if ($("#tarReqId").val()!="") {
			$.ajax({
				type: "get",
				url: "/shop/queryWeixinNativeResult",
				data: {"tarReqId":$("#tarReqId").val()},
				dataType: "json",
				success: function (data) {
					if (data.status == "success") {
						clearInterval(obj);
						window.location.href = "/shop/resultok.htm?orderno="+$("#ybmOrderNo").val();
					}
				}
			});
		}
	}
}