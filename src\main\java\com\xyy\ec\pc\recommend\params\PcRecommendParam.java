package com.xyy.ec.pc.recommend.params;

import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSceneEnum;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSortStrategyEnum;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PcRecommendParam implements Serializable {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 推荐场景
     *
     * @see EcpRecommendSceneEnum
     */
    private Integer recommendScene;

    /**
     * 推荐场景
     *
     * @see EcpRecommendSortStrategyEnum
     */
    private Integer sortStrategy;

    /**
     * 用户所在省份。
     * PS：可能为<code>null</code>。
     */
    private Integer merchantProvinceCode;

    /**
     * sass机构编码
     */
    private String saasOrganSign;

    /**
     * 用户指定需要置顶的SKU ID
     */
    private Long topSkuId;


    /**
     *  商品标签
     */
    private String tags;

    /**
     *  店铺编码列表 上限 200
     */
    private String shopCodes;

    /**
     * qt埋点唯一标识
     */
    private String scmId;
}
