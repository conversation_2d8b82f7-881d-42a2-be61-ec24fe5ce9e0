package com.xyy.ec.pc.config;

import java.lang.annotation.*;


/** 限流 自定义注解
 *
 */
@Inherited
@Documented
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RateFlowLimit {

    /** merchantId维度的次数限制   每个merchantId在1min内访问300次 */
    int merchantIdRateLimit() default 300;
    int merchantIdRateTime() default 60;

    /** ip维度的次数限制   每个ip在1min内访问300次*/
    int ipRateLimit() default 300;
    int ipRateTime() default 60;

    /** 缓存中的key的后缀: 比如 用接口关键字来作为后缀
     *  此属性的取值应该简短，唯一
     *
     *
     * */
    String methodString() default "" ;

}
