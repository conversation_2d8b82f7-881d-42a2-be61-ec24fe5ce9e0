package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.api.AppActivityBuineseApi;
import com.xyy.ec.layout.buinese.api.AppModuleManageBuineseApi;
import com.xyy.ec.layout.buinese.api.ExhibitionBuineseApi;
import com.xyy.ec.layout.buinese.api.PreferredBrandBuineseApi;
import com.xyy.ec.layout.buinese.dto.*;
import com.xyy.ec.layout.buinese.ecp.api.BrandPreferredBuineseApi;
import com.xyy.ec.layout.buinese.ecp.api.PcClinicManagementBusinessApi;
import com.xyy.ec.layout.buinese.ecp.api.PcModuleManageBuineseApi;
import com.xyy.ec.layout.buinese.utils.AppModuleCategoryEnum;
import com.xyy.ec.marketing.hyperspace.api.VoucherForCenterApi;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponCenterDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.cms.utils.LayoutGraySwitchUtils;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.enums.LayoutMerchantStatusEnum;
import com.xyy.ec.pc.model.PromotionDto;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.rpc.MarketActivityPackageRpc;
import com.xyy.ec.pc.service.ActivityEventsService;
import com.xyy.ec.pc.service.AppModuleCategoryService;
import com.xyy.ec.pc.service.ClientPageManageService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.layout.LayoutBaseService;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ms.marketing.nine.chapters.api.pc.ActivitySkuBuyLimitForPcApi;
import com.xyy.ms.marketing.nine.chapters.common.config.ActivityEnum;
import com.xyy.ms.promotion.business.api.pc.ActivityPackageForPcBusinessApi;
import com.xyy.ms.promotion.business.api.pc.SpecialPromotionForPcBusinessApi;
import com.xyy.ms.promotion.business.api.pc.VoucherForPcBusinessApi;
import com.xyy.ms.promotion.business.common.ErrorCodeEum;
import com.xyy.ms.promotion.business.common.ResultDTO;
import com.xyy.ms.promotion.business.common.constants.SpecialPromotionEnum;
import com.xyy.ms.promotion.business.common.constants.VoucherEnum;
import com.xyy.ms.promotion.business.dto.MerchantRequestDTO;
import com.xyy.ms.promotion.business.dto.activitypackage.ActivityPackageVo;
import com.xyy.ms.promotion.business.dto.specialpromotion.SpecialPromotionExtendDTO;
import com.xyy.ms.promotion.business.dto.specialpromotion.SpecialPromotionRequestDTO;
import com.xyy.ms.promotion.business.dto.voucher.VoucherCenterDto;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.net.URL;
import java.util.*;

/**
 * Created by shiwenlong on 2016/12/22.
 * 跳转到活动页面(二级页面)
 */
@Controller
@RequestMapping("/activity")
public class ActivityEventsController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActivityEventsController.class);

    @Autowired
    private CmsAppProperties cmsAppProperties;


    @Value("${coupon.center.isNew}")
    private Boolean couponCenterIsNew;

    @Autowired
    private ActivityEventsService activityEventsService;

    @Autowired
    private LayoutBaseService layoutBaseService;

    @Autowired
    private Config config;

    @Autowired
    private ClientPageManageService clientPageManageService;

    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;

    @Reference(version = "1.0.0")
    private VoucherForPcBusinessApi voucherBusinessApi;

    @Reference(version = "1.0.0")
    private VoucherForCenterApi voucherForCenterApi;

    @Reference(version = "1.0.0")
    private AppModuleManageBuineseApi appModuleManageBuineseApi;

    @Reference(version = "1.0.0")
    private PcModuleManageBuineseApi pcModuleManageBuineseApi;

    @Autowired
    private AppModuleCategoryService appModuleCategoryService;

    @Reference(version = "1.0.0")
    private AppActivityBuineseApi appActivityBuineseApi;

    @Reference(version = "1.0.0")
    private SpecialPromotionForPcBusinessApi specialPromotionApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private PreferredBrandBuineseApi preferredBrandBuineseApi;

    @Reference(version = "1.0.0")
    private BrandPreferredBuineseApi brandPreferredBuineseApi;

    @Reference(version = "1.0.0")
    private ActivityPackageForPcBusinessApi activityPackageBusinessApi;

    @Autowired
    private MarketActivityPackageRpc marketActivityPackageRpc;

    @Autowired
    private LayoutGraySwitchUtils layoutGraySwitchUtils;

    @Reference(version = "1.0.0")
    private ExhibitionBuineseApi exhibitionBuineseApi;

    @Reference(version = "1.0.0")
    private ActivitySkuBuyLimitForPcApi activitySkuBuyLimitForPcApi;

    @Reference(version = "1.0.0")
    private PcClinicManagementBusinessApi pcClinicManagementBusinessApi;

    @Autowired
    private IndexController indexController;

    private static final ImmutableMap<Integer, Integer> _receiveCenterSorter = ImmutableMap.<Integer, Integer>builder()
            .put(VoucherEnum.VoucherTypeEnum.OVERLYING.getId(), 0)
            .put(VoucherEnum.VoucherTypeEnum.PRODUCT.getId(), 1)
            .put(VoucherEnum.VoucherTypeEnum.COMMON.getId(), 2)
            .put(VoucherEnum.VoucherTypeEnum.NEWMAN.getId(), 3)
            .build();

    /**
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @throws
     * @Description: 跳转到活动页面(活动二级页面)
     * <AUTHOR>
     * @date 2018/8/27 15:28
     */
    @RequestMapping(value = {"/events.htm"}, method = RequestMethod.GET)
    public ModelAndView activityPage(HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            if (StringUtils.isNotEmpty(request.getParameter("type"))) {
                return new ModelAndView("/activityEvent/events-" + request.getParameter("type") + ".ftl", activityEventsService.getEventsModelMap(request, branchCode));
            } else {
                return new ModelAndView("/activityEvent/events.ftl", activityEventsService.getEventsModelMap(request, branchCode));
            }
        } catch (Exception e) {
            LOGGER.error("活动二级页面下加载失败", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * @param modelMap
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @throws
     * @Description: 登录后跳转到首页（如果需要跳转到最后访问页面，需要将最后访问页面传过来）
     * <AUTHOR>
     * @date 2018/8/21 17:35
     */
    @RequestMapping(value = {"/"})
    public ModelAndView index(ModelMap modelMap, HttpServletRequest request, RedirectAttributes redirectAttributes) {
        return indexController.index(modelMap, request, redirectAttributes);
    }


    /**
     * 跳转到新人专享活动界面
     */
    @RequestMapping(value = "/newBer.htm", method = RequestMethod.GET)
    public ModelAndView newBerPage(HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }
            modelMap.put("skuImageUrl", config.getProductImagePathUrl());
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
            return new ModelAndView("/activityEvent/newber_three.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("新人专享活动界面加载失败", e);
            return new ModelAndView("/error/500.ftl");
        }
    }


    /**
     * 获取活动页面数据
     * 可配
     */
    @RequestMapping(value = {"/initActivity.htm"}, method = RequestMethod.GET)
    public ModelAndView initActivity(HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            ModelMap modelMap = activityEventsService.getInitActivityMap(request, branchCode);
            String activityCustom = request.getParameter("activityType");
            return new ModelAndView("/activityEvent/" + activityCustom + ".ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("活动页面数据加载异常", e);
            return new ModelAndView("/error/500.ftl");
        }
    }


    /**
     * 活动对应实体
     * activityModel.put("title","疯狂免单");
     * activityModel.put("info","1、参与条件：活动期间所有下单成功用户均视为参与此免单活动，系统根据订单支付完成时间自动识别排名“第8...”订单；\n" +
     * "2、免单金额发放：免单金额由系统自动返还至账户余额；订单派送完成，客户确认订单后，即可自行领取余额；余额不提现、不找零，不逾期，具体请查阅 【余额使用说明】；\n" +
     * "3、免单额度：实际付款金额小于或等于1888元，药帮忙为您全额买单；实际付款金额大于1888元，药帮忙支付1888元，超出该金额客户自行支付余款；\n" +
     * "4、免单订单发生退货行为，即视为自行放弃免单福利；客户需重新支付相应商品金额；\n" +
     * "5、免单订单发生退款行为，即视为自行放弃免单福利，药帮忙不提供免单金额返现；\n" +
     * "6、为保证活动公平公开，用户可通过本页面下方“名单公示区”了解获奖名单；\n" +
     * "7、禁止使用任何违规方式参与此次活动，一经发现，取消免单资格；\n" +
     * "*本活动最终解释权归药帮忙所有");
     * <p>
     * <p>
     * 查询活动通知数据接口 create by shi.wenLong on 2017-6-5 13:57:32
     * 主要针对2017年6月份的逢8免单活动做的通用接口
     * 可以查询对应的活动数据,例如什么用户什么时间下了什么单,免单了多少数据等内容
     *
     * @param activityId 活动id
     * @return 活动数据列表
     * @throws Exception
     */
    @RequestMapping("/{activityId}/activity.htm")
    public Object activity(@PathVariable("activityId") Long activityId) {
        try {
            ModelMap modelMap = new ModelMap();
            ClientPageManageBuineseDto clientPageManage = clientPageManageService.getById(activityId);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }
            modelMap.put("merchantId", merchantId);
            modelMap.put("title", clientPageManage.getTitle());
            modelMap.put("areaInfo", clientPageManage.getAreaInfo());
            modelMap.put("jsList", Arrays.asList(clientPageManage.getJsPath().split(";")));
            modelMap.put("cssList", Arrays.asList(clientPageManage.getCssPath().split(";")));
            modelMap.put("merchant", merchant);
            return new ModelAndView("/activityEvent/activity.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("静态页数据加载失败", e);
            return new ModelAndView("/error/500.ftl");
        }

    }


    /**
     * @param request
     * @return java.lang.Object
     * @throws
     * @Description: 领券中心
     * <AUTHOR>
     * @date 2018/8/27 16:57
     */
    @RequestMapping("/{name}/voucherActivity.htm")
    public Object eventsVoucher(HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            Long merchantId;
            if (merchant != null && merchant.getId() != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            Map<String, String> codeMap = codeItemServiceRpc.findCodeMap("PC_ACTIVITY_CONFIG", branchCode);
            if (codeMap != null) {
                for (String key : codeMap.keySet()) {
                    modelMap.put(key, codeMap.get(key));
                }
            }
            ResultDTO<PageInfo<VoucherCenterDto>> voucherPageResult = voucherBusinessApi.getReceivedVoucherCenter(null, merchantId);
            if (null != voucherPageResult && voucherPageResult.getErrorCode() == ErrorCodeEum.SUCCESS.getErrorCode() && null != voucherPageResult.getData()) {
                modelMap.put("voucherList", voucherPageResult.getData().getList());
            } else {
                modelMap.put("voucherList", Lists.newArrayList());
            }
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
            return new ModelAndView("/activityEvent/events-voucher.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("领券中心加载失败", e);
            return new ModelAndView("/error/500.ftl");
        }

    }


    /**
     * 获取品牌推荐H5接口数据
     *
     * @param request
     * @return
     */
    @RequestMapping("/preferredBrand.htm")
    @ResponseBody
    public ModelAndView preferredBrand(HttpServletRequest request, Page page) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();

            modelMap.put("skuImageUrl", config.getProductImagePathUrl());
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
                modelMap.put("merchantId", merchantId);
            } else {
                merchantId = 0L;
            }
            int pageNum = page.getOffset();
            int pageSize = 5;
            if (pageNum == 0) {
                pageNum = 1;
            }

            modelMap.put("styleClass15", "cur");
            ApiRPCResult<List<PreferredBrandBuineseDto>> bannerApiRPCResult;
            ApiRPCResult<PageInfo<PreferredBrandBuineseDto>> apiRPCResult;
            //用户所属域的域编码
            String branchCode = null;
            if (merchant != null) {
                branchCode = merchant.getRegisterCode();
            } else {
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            }
            if (layoutGraySwitchUtils.isOpenGrayByBranchForOnePiece(branchCode)) {
                bannerApiRPCResult = brandPreferredBuineseApi.listAllBrandPreferredWithoutCsuInfoWithoutBranchCode(merchantId);
                apiRPCResult =
                        brandPreferredBuineseApi.listBrandPreferredInfosWithoutBranchCode(merchantId, pageNum, pageSize);
            } else {
                bannerApiRPCResult =
                        brandPreferredBuineseApi.listAllBrandPreferredWithoutCsuInfo(merchantId);
                apiRPCResult =
                        brandPreferredBuineseApi.listBrandPreferredInfos(merchantId, pageNum, pageSize);
            }
            if (!bannerApiRPCResult.isSuccess()) {
                LOGGER.error("【布局pc品牌推荐】preferredBrand.htm请求失败", bannerApiRPCResult.getMsg());
                return new ModelAndView("/error/500.ftl");
            }
            if (!apiRPCResult.isSuccess()) {
                LOGGER.error("【布局pc品牌推荐】preferredBrand.htm请求失败", apiRPCResult.getMsg());
                return new ModelAndView("/error/500.ftl");
            }
            PageInfo<PreferredBrandBuineseDto> data = apiRPCResult.getData();
            List<PreferredBrandBuineseDto> list = data.getList();
            if (CollectionUtils.isNotEmpty(list)) {
                List<ListProduct> skuVOList = null;
                for (PreferredBrandBuineseDto preferredBrandBuineseDto : list) {
                    if (preferredBrandBuineseDto != null) {
                        LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
                        if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                            skuVOList = preferredBrandBuineseDto.getSkuVOList();
                            skuVOList = layoutBaseService.setProductProperties(skuVOList, statusEnum, request);
                        }
                        skuVOList = layoutBaseService.setProductShopName(skuVOList);
                    }
                    preferredBrandBuineseDto.setSkuVOList(skuVOList);
                }
            }
            Page<PreferredBrandBuineseDto> pageInf = new Page<>();
            pageInf.setRows(list);
            pageInf.setTotal(data.getTotal());
            pageInf.setPageCount(data.getPages());
            String requestUrl = this.getRequestUrl(request);
            pageInf.setRequestUrl(requestUrl);
            pageInf.setOffset(data.getPageNum());
            pageInf.setLimit(data.getPageSize());
            modelMap.put("preferredBrandList", bannerApiRPCResult.getData());
            modelMap.put("pager", pageInf);
            modelMap.put("merchant", merchant);
            return new ModelAndView("/activityEvent/events-preferredBrand.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("品牌推荐加载失败", e);
            return new ModelAndView("/error/500.ftl");
        }

    }

    /**
     * 伪静态页
     *
     * @param pname 活动id
     * @return 静态页相关数据
     * @throws Exception
     */
    @RequestMapping("/{pname}/ybmActivity.htm")
    public Object ybmActivity(@PathVariable String pname, HttpServletRequest request) {
        try {
            ModelMap modelMap = new ModelMap();
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }
            ClientPageManageBuineseDto clientPageManage = clientPageManageService.getByPName(pname);

            if (clientPageManage != null) {
                modelMap.put("merchantId", merchantId);
                modelMap.put("title", clientPageManage.getTitle());
                modelMap.put("areaInfo", clientPageManage.getAreaInfo());
                modelMap.put("jsList", Arrays.asList(clientPageManage.getJsPath().split(";")));
                modelMap.put("cssList", Arrays.asList(clientPageManage.getCssPath().split(";")));
                modelMap.put("csstext", clientPageManage.getCsstext());
                modelMap.put("jstext", clientPageManage.getJstext());
                modelMap.put("clientPageManage", clientPageManage);
            }
            modelMap.put("center_menu","yikuaiqian");
            modelMap.put("subAccount", merchant == null ? 0 : Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            modelMap.put("merchant", merchant);
            return new ModelAndView("/activityEvent/newActivity.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("访问伪静态页失败", e);
            return new ModelAndView("/error/500.ftl");
        }

    }

    /**
     * 伪静态页，头部是高毛搜索头
     *
     * @param pname 活动id
     * @return 静态页相关数据
     * @throws Exception
     */
    @RequestMapping("/{pname}/gaomaoYbmActivity.htm")
    public Object gaomaoYbmActivity(@PathVariable String pname, HttpServletRequest request) {
        try {
            ModelMap modelMap = new ModelMap();
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }
            ClientPageManageBuineseDto clientPageManage = clientPageManageService.getByPName(pname);

            if (clientPageManage != null) {
                modelMap.put("merchantId", merchantId);
                modelMap.put("title", clientPageManage.getTitle());
                modelMap.put("areaInfo", clientPageManage.getAreaInfo());
                modelMap.put("jsList", Arrays.asList(clientPageManage.getJsPath().split(";")));
                modelMap.put("cssList", Arrays.asList(clientPageManage.getCssPath().split(";")));
                modelMap.put("csstext", clientPageManage.getCsstext());
                modelMap.put("jstext", clientPageManage.getJstext());
                modelMap.put("clientPageManage", clientPageManage);
            }
            modelMap.put("merchant", merchant);
            return new ModelAndView("/activityEvent/gaomaoNewActivity.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("访问伪静态页（高毛搜索头部）失败", e);
            return new ModelAndView("/error/500.ftl");
        }

    }

    /**
     * @param pname
     * @return java.lang.Object
     * @throws
     * @Description: 更新静态页缓存
     * <AUTHOR>
     * @date 2018/8/27 19:42
     */
    @RequestMapping("/{pname}/clearClientPage.json")
    @ResponseBody
    public Object clearClientPage(@PathVariable String pname) {
        return null;

    }


    /**
     * 跳转到通用静态活動頁面
     */
    @RequestMapping(value = "/clinicArea.htm", method = RequestMethod.GET)
    public ModelAndView clinicArea(HttpServletRequest request) {
        Set<Integer> clinicMerchantBusinessTypes = Sets.newHashSet(9,8,14,4,10,15,7,6);
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            Boolean isClinicAreaMerchant = false;
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
                /** 判断是否是诊所专区用户 */
                if (clinicMerchantBusinessTypes.contains(merchant.getBusinessType())) {
                    isClinicAreaMerchant = true;
                }
            } else {
                merchantId = 0L;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            /** 如果用户未登录或不是诊所专区用户,则将用户访问链接直接引导至PC首页 */
            /*if (!isClinicAreaMerchant) {
                return new ModelAndView(new RedirectView("/index.htm"));
            }*/

            Long manageId = null;

            if (layoutGraySwitchUtils.isOpenGrayByBranchForOnePiece(branchCode)) {
                ApiRPCResult<Long> apiRPCResult =
                        pcClinicManagementBusinessApi.getModuleManageIdByMerchantId(merchantId);
                if (apiRPCResult.isSuccess()) {
                    manageId = apiRPCResult.getData();
                }
            } else {
                /** 初始化首页模板参数(区分PC端和App端展示不同的模块)，诊所专区配置的编码应该从配置表里取 */
                Map<String, String> codeMap = codeItemServiceRpc.findCodeMap("CLINIC_AREA_CONFIG", branchCode);
                if (codeMap != null && codeMap.get("VERSION") != null) {
                    if (StringUtils.isEmpty(codeMap.get("VERSION")) || NumberUtils.toLong(codeMap.get("VERSION")) < 1) {
                        LOGGER.error("诊所专区布局加载失败！！！");
                        return new ModelAndView("/error/500.ftl");
                    }
                    manageId = NumberUtils.toLong(codeMap.get("VERSION"));
                }
            }

            if (manageId == null) {
                LOGGER.error("诊所专区布局加载失败！！！,未找到使用中的布局，merchantId:{}", merchantId);
                return new ModelAndView(new RedirectView("/index.htm",true,false));
            }

            AppModuleManageBuineseDto appModuleManageBuineseDto;

            if (layoutGraySwitchUtils.isOpenGrayByBranchForOnePiece(branchCode)) {
                ApiRPCResult<AppModuleManageBuineseDto> apiRPCResult =
                        pcModuleManageBuineseApi.getUseingModuleManage(1, 2, manageId,
                                AppModuleCategoryEnum.AppModuleUp.UP_TRUE.getId(), merchantId);
                if (!apiRPCResult.isSuccess()) {
                    LOGGER.error("新首页布局加载失败:" + apiRPCResult.getMsg());
                    throw new Exception();
                } else {
                    appModuleManageBuineseDto = apiRPCResult.getData();
                }
            } else {
                appModuleManageBuineseDto = appModuleManageBuineseApi.getUseingAppModuleManage(1, 2, manageId, AppModuleCategoryEnum.AppModuleUp.UP_TRUE.getId(), merchantId, branchCode);
            }

            List<AppModuleCategoryBuineseDto> appModuleCategoryBuineseDtoList =
                    appModuleManageBuineseDto.getAppModuleCategoryBuineseDtoList();
            if (CollectionUtils.isEmpty(appModuleCategoryBuineseDtoList)) {
                return new ModelAndView(new RedirectView("/index.htm",true,false));
            }

            for (AppModuleCategoryBuineseDto appModuleCategoryBuineseDto : appModuleManageBuineseDto.getAppModuleCategoryBuineseDtoList()) {
                if (appModuleCategoryBuineseDto.getAlias() != null) {
                    // 获取轮播图片信息
                    if (appModuleCategoryBuineseDto.getAlias() == 1048 || appModuleCategoryBuineseDto.getAlias() == 1049 || appModuleCategoryBuineseDto.getAlias() == 2003) { // 向左对齐的排列方式
                        for (AppModuleBuineseDto appModuleBuineseDto : appModuleCategoryBuineseDto.getItems()) {
                            if (StringUtils.isNotEmpty(appModuleBuineseDto.getApi())) {
                                appModuleBuineseDto.setSkuVOList(appModuleCategoryService.getProductDtoByApi(appModuleBuineseDto.getApi(), merchantId, branchCode, request));
                            }
                        }
                    }
                    if (appModuleCategoryBuineseDto.getAlias() == 2002) {
                        appModuleCategoryBuineseDto.setActivityPackageVoList(marketActivityPackageRpc.getAllActivityPackage(merchantId, branchCode, 3, 2));
                    }
                }
            }

            modelMap.put("skuImageUrl", config.getProductImagePathUrl());
            modelMap.put("moduleCategory", appModuleManageBuineseDto.getAppModuleCategoryBuineseDtoList());
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
            modelMap.put("styleClass8", "cur");

            return new ModelAndView("/activityEvent/events-20171218-lt-zhensuo.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("诊所专区布局加载失败！！！", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * @param id
     * @param a_url
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @throws
     * @Description: 跳转到黄金单品模板页
     * <AUTHOR>
     * @date 2018/8/27 18:23
     */
    @RequestMapping(value = {"/{id}/activePage.htm"}, method = RequestMethod.GET)
    public ModelAndView activePage(@PathVariable Long id, String a_url, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            ModelMap modelMap = activityEventsService.getActivityMap(id, request, branchCode);
            if (modelMap != null) {
                if (StringUtils.isNotEmpty(a_url)) {
                    return new ModelAndView("/activityEvent/" + a_url + ".ftl", modelMap);
                } else {
                    return new ModelAndView("/activityEvent/events-20180226-lt-hjdp.ftl", modelMap);
                }
            } else {
                return new ModelAndView(new RedirectView("/index.htm",true,false));
            }
        } catch (Exception e) {
            LOGGER.error("跳转到黄金单品模板页", e);
            return new ModelAndView("/error/500.ftl");
        }

    }


    /**
     * 诊所套餐数据加载页面
     */
    @RequestMapping(value = "/zhensuoTc.htm", method = RequestMethod.GET)
    public ModelAndView zhensuoTc(HttpServletRequest request) throws Exception {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            int mark = -1;
            Map<String, String> skuLimitedNumMap = codeItemServiceRpc.findCodeMap("ACTIVITY_PACKAGE_MARK", branchCode);
            for (String key : skuLimitedNumMap.keySet()) {
                if (skuLimitedNumMap.get(key).equals("诊所套餐")) {
                    mark = NumberUtils.toInt(key);
                }
            }
            List<ActivityPackageVo> packageList = marketActivityPackageRpc.getAllActivityPackage(merchantId, branchCode, 3, mark);
            if(CollectionUtils.isNotEmpty(packageList)) {
                packageList.stream().filter(item -> item != null && Objects.equals(item.getStatus(), 0))
                        .forEach(item -> {
                            Integer activityBuyLimitNumByActivity = activitySkuBuyLimitForPcApi.getActivityBuyLimitTotalSurplusNumByActivity(item.getId(), ActivityEnum.TAO_CAN.getCode());
                            if (activityBuyLimitNumByActivity != null && activityBuyLimitNumByActivity <= 0) {
                                // 限购且没有活动剩余可购买量
                                item.setStatus(1);
                            }
                        });
            }
            modelMap.addAttribute("activitySkuList", packageList);
            if (merchant != null) {
                modelMap.put("islogin", 1);
            } else {
                modelMap.put("islogin", 0);
            }
            modelMap.put("merchant", merchant);
            return new ModelAndView("activityEvent/events-20180301-lt-taocan.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("诊所套餐加载失败", e);
            return new ModelAndView("/error/500.ftl");
        }
    }


    /**
     * 每周推荐
     *
     * @param request
     * @return
     * @deprecated 页面没用使用，过时。
     */
    @Deprecated
    @RequestMapping("/promotionEveryWeek")
    public Object promotionEveryWeek(Long appActivityId, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);

            /** 获取每周特惠首页展示的商品列表 */
            SpecialPromotionRequestDTO specialPromotionRequestDTO = new SpecialPromotionRequestDTO();
            specialPromotionRequestDTO.setPromotionType(42);
            specialPromotionRequestDTO.setHasHotNow(SpecialPromotionEnum.PromotionStatus.IN_THE_PREFERENTIAL.getId());
            MerchantRequestDTO merchantRequestDTO = new MerchantRequestDTO();
            merchantRequestDTO.setMerchantId(merchantId);
            merchantRequestDTO.setBranchCode(branchCode);
            specialPromotionRequestDTO.setMerchantRequestDTO(merchantRequestDTO);
            /** 调用接口，查询特价促销活动 */
            ResultDTO<PageInfo<SpecialPromotionExtendDTO>> resultDTO = specialPromotionApi.findPromotionSku(null, specialPromotionRequestDTO);
            List<SpecialPromotionExtendDTO> promotionListToday = resultDTO.getData().getList();
             /*
                記得把緩存裏購物車内的數據回寫到商品數據内
             */
            if (CollectionUtils.isNotEmpty(promotionListToday)) {
                if (promotionListToday.size() > 0) {
                    SpecialPromotionExtendDTO tmp = promotionListToday.get(0);
                    PromotionDto promotionDto = new PromotionDto();
                    BeanUtils.copyProperties(tmp, promotionDto);
                    modelMap.put("promotionListTodayTime", promotionDto.getEndTime().getTime() / 1000 - System.currentTimeMillis() / 1000);
                    /*
                        虽然是促销,但是取的是展示管理的数据
                     */
                    if (appActivityId == null || appActivityId < 0) {
                        appActivityId = 242L;// 此处老数据已经写死，兼容老数据
                    }
                    AppActivityBuineseDto activity = appActivityBuineseApi.getAppActivityAndGroypById(appActivityId);
                    if (activity != null && CollectionUtils.isNotEmpty(activity.getGroups())) {
                        AppActivityGroupBuineseDto appActivityGroup = activity.getGroups().get(0);
                        promotionDto.setProductDtoList(appModuleCategoryService.getProductDtoByApi(appActivityGroup.getApi(), merchantId, branchCode, request));
                    }
                    modelMap.put("promotionListToday", promotionDto);
                }
            }
            modelMap.put("styleClass16", "cur");
            modelMap.put("skuImageUrl", config.getProductImagePathUrl());
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
            return new ModelAndView("/activityEvent/events-20180329-lt-mztj.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("获取每周特惠活动数据异常:", e);
            return new ModelAndView("/error/500.ftl");
        }

    }


    /**
     * 限时特惠
     * id： 活动布局项的id
     *
     * @param request
     * @return
     */
    @RequestMapping("/{id}/promotionType")
    public Object promotionType(@PathVariable Long id, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            PromotionDto promotionListToday = new PromotionDto();
            AppActivityBuineseDto appActivity = appActivityBuineseApi.getAppActivityAndGroypById(id);
            if (appActivity != null && CollectionUtils.isNotEmpty(appActivity.getGroups())) {
                AppActivityGroupBuineseDto appActivityGroup = appActivity.getGroups().get(0);
                promotionListToday.setProductDtoList(appModuleCategoryService.getProductDtoByApi(appActivityGroup.getApi(), merchantId, branchCode, request));
                if (CollectionUtils.isNotEmpty(promotionListToday.getProductDtoList())) {
//                    long promotionListTodayTime = promotionListToday.getPreEndTimeStamp().longValue();
//                    if (promotionListTodayTime > 0L) {
//                        modelMap.put("isPreHeat", 1);
//                    } else {
//                        promotionListTodayTime = skuVO.getEndTimeStamp().longValue();
//                    }
//                    promotionListTodayTime = promotionListTodayTime / 1000;
//                    modelMap.put("promotionListTodayTime", promotionListTodayTime);
                }
            }

            modelMap.put("promotionListToday", promotionListToday);
            modelMap.put("styleClass16", "cur");
            modelMap.put("skuImageUrl", config.getProductImagePathUrl());
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
            return new ModelAndView("/activityEvent/events-20180710-lt-xsth.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("获取每周特惠活动数据异常:", e);
            return new ModelAndView("/error/500.ftl");
        }

    }


    /**
     * @param id
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @throws
     * @Description: 跳转到返利专区模板页
     * <AUTHOR>
     * @date 2018/8/27 18:51
     */
    @RequestMapping(value = {"/{id}/rebateActivity.htm"}, method = RequestMethod.GET)
    public ModelAndView rebateActivity(@PathVariable Long id, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            ModelMap modelMap = activityEventsService.getActivityMap(id, request, branchCode);
            if (modelMap != null) {
                return new ModelAndView("/activityEvent/events-20180329-lt-flzq.ftl", modelMap);
            } else {
                return new ModelAndView(new RedirectView("/index.htm",true,false));
            }
        } catch (Exception e) {
            LOGGER.error("跳转到返利专区模板页", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 领券中心
     *
     * @param activityName
     * @return
     */
    @RequestMapping("/voucherCenter.htm")
    public Object voucherCenter(String activityName, boolean isVoucherCenter, boolean is806Style, HttpServletRequest request) {
        ModelMap modelMap = new ModelMap();
        MerchantBussinessDto merchant = null;
        Long merchantId = null;
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            merchant = merchantPrincipal;
            if (null == merchant) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            if (merchant != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }
            String branchCode = null;

            Map<String, String> codeMap = codeItemServiceRpc.findCodeMap("PC_ACTIVITY_CONFIG", branchCode);
            if (codeMap != null) {
                for (String key : codeMap.keySet()) {
                    modelMap.put(key, codeMap.get(key));
                }
            }

            if (BooleanUtils.isTrue(couponCenterIsNew)){
                ApiRPCResult<List<CouponCenterDto>> apiRpcResult = voucherForCenterApi.listCouponForCenter(merchantId);
                if (apiRpcResult.isSuccess()) {
                    List<CouponCenterDto> frontCoupon = Lists.newArrayList();
                    List<CouponCenterDto> lastCoupon = Lists.newArrayList();
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(apiRpcResult.getData())){
                        for (CouponCenterDto coupon : apiRpcResult.getData()) {
                            if (coupon.getIsLq() <= 0) {
                                if (null != coupon.getProvideTotalCount() && null != coupon.getReceiveTotalCount() && coupon.getProvideTotalCount() <= coupon.getReceiveTotalCount()) {
                                    lastCoupon.add(coupon);
                                } else {
                                    frontCoupon.add(coupon);
                                }
                            } else {
                                frontCoupon.add(coupon);
                            }
                        }
                    }
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastCoupon)) {
                        lastCoupon.sort((o1, o2) -> {
                            Integer order1 = (null == o1 || null == o1.getVoucherType()) ? null : _receiveCenterSorter.get(o1.getVoucherType());
                            Integer order2 = (null == o2 || null == o2.getVoucherType()) ? null : _receiveCenterSorter.get(o2.getVoucherType());
                            if (null == order1 && null == order2) {
                                return 0;
                            } else if (null == order1) {
                                return 1;
                            } else if (null == order2) {
                                return -1;
                            } else {
                                return order1 - order2;
                            }
                        });
                        frontCoupon.addAll(lastCoupon);
                    }
                    modelMap.put("voucherList", frontCoupon);

                } else {
                    modelMap.put("voucherList", Lists.newArrayList());
                }
            } else {
                ResultDTO<PageInfo<VoucherCenterDto>> voucherPageResult = voucherBusinessApi.getReceivedVoucherCenter(null, merchantId);

                if (null != voucherPageResult && voucherPageResult.getErrorCode() == ErrorCodeEum.SUCCESS.getErrorCode() && null != voucherPageResult.getData()) {
                    modelMap.put("voucherList", voucherPageResult.getData().getList());
                } else {
                    modelMap.put("voucherList", Lists.newArrayList());
                }
            }
        } catch (Exception e) {
            LOGGER.error("加载领券中心异常", e);
        }
        modelMap.put("merchantId", merchantId);
        modelMap.put("merchant", merchant);
        modelMap.put("is806Style", is806Style);
        return new ModelAndView("/activityEvent/events-20180605-lt-lqzx.ftl", modelMap);
    }

    @RequestMapping("/voucherCenterNew.htm")
    public Object voucherCenterNew(HttpServletRequest request) {
        try {
            ModelMap modelMap = new ModelMap();
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            modelMap.put("merchant", merchant);
            modelMap.put("merchantId", merchantId);
            return new ModelAndView("/activityEvent/events-202207121-lt-lqzx.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("访问cms静态页失败", e);
            return new ModelAndView("/error/500.ftl");
        }
    }







}

