package com.xyy.ec.pc.newfront.service;

import com.xyy.ec.merchant.bussiness.dto.licence.LicenseAuditParams;
import com.xyy.ec.pc.rest.AjaxResult;

/**
 * <AUTHOR>
 * @date 2025-07-23 15:25
 */
public interface LicenseAuditNewService {

    AjaxResult<Object> initLicenseAuditDetailVaild(Integer type);

    AjaxResult<Object> initLicenseAuditDetail(Integer customerType, Boolean remark, Integer invoiceType);

    AjaxResult<Object> initBillDetailVaild(int type, String orgCode);

    AjaxResult<Object> initBillDetail(int type, int customerType, String orgCode);

    AjaxResult<Object> addLicenseAudit(LicenseAuditParams licenseAudit);

    AjaxResult<Object> updateLicenseAudit(LicenseAuditParams licenseAudit);

    AjaxResult<Object> cacheMerchantInfo(LicenseAuditParams licenseAudit);
}
