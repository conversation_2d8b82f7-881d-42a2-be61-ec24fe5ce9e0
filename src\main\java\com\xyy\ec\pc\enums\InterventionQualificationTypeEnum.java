package com.xyy.ec.pc.enums;


/**
 * 资质类型
 */
public enum InterventionQualificationTypeEnum {
    EnterpriseQualifications(1,"企业首营资质",2),
    //购销合同
    Contract(2,"购销合同",2),
    //年度报告
    AnnualReport(3,"年度报告",2),
    //药检报告
    SnReport(4,"药检报告",2),
    //首营资料
    SnInformation(5,"首营资料",2);
    private int id;
    private String value;
    /**
     * @see InterventionTypeEnum
     */
    private int parentId;
    public  int getId() {
        return id;
    }
    public  String getValue() {
        return value;
    }

    public static String get(int id) {
        for (InterventionQualificationTypeEnum c : InterventionQualificationTypeEnum.values()) {
            if (c.getId() == id) {
                return c.value;
            }
        }
        return null;
    }
    InterventionQualificationTypeEnum(int id, String value) {
        this.id = id;
        this.value = value;
    }
    InterventionQualificationTypeEnum(int id, String value,  int parentId) {
        this.id = id;
        this.value = value;
        this.parentId = parentId;
    }
}
