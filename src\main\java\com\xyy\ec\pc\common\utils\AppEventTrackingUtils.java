package com.xyy.ec.pc.common.utils;

import com.xyy.ec.pc.common.enums.AppEventTrackingSpTypeEnum;
import com.xyy.ec.pc.common.params.AppEventTrackingSpIdGenerateParam;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchSceneEnum;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.Objects;

/**
 * APP埋点工具类。供于app项目埋点。
 *
 * <AUTHOR>
 * @see AppEventTrackingSpTypeEnum
 */
public class AppEventTrackingUtils {

    /**
     * 生成spId
     *
     * @param generateParam
     * @return
     */
    public static String generateSpId(AppEventTrackingSpIdGenerateParam generateParam) {
        if (generateParam == null) {
            return "";
        }
        AppEventTrackingSpTypeEnum eventTrackingSpTypeEnum = generateParam.getSpTypeEnum();
        if (eventTrackingSpTypeEnum == null) {
            return "";
        }
        String spId = "";
        switch (eventTrackingSpTypeEnum) {
            case SEARCH:
                // 搜索
                spId = generateSpIdForSearch(generateParam);
                break;
            case SEARCH_NO_RESULT_HOT_SALE:
                // 搜索无结果热销精选
                spId = generateSpIdForSearchNoResultHotSale(generateParam);
                break;
            default:
                spId = "";
                break;
        }
        return spId;
    }

    private static String generateSpIdForSearch(AppEventTrackingSpIdGenerateParam generateParam) {
        if (Objects.equals(generateParam.getSearchScene(), EcpSearchSceneEnum.MAIN)) {
            // 参数处理
            String queryWord = generateParam.getQueryWord();
            queryWord = StringUtils.isNotEmpty(queryWord) ? queryWord : "";
            return MessageFormat.format("1-{0}-0", queryWord);
        }
        return "";
    }

    private static String generateSpIdForSearchNoResultHotSale(AppEventTrackingSpIdGenerateParam generateParam) {
        if (Objects.equals(generateParam.getSearchScene(), EcpSearchSceneEnum.MAIN)) {
            // 参数处理
            Long merchantId = generateParam.getMerchantId();
            String merchantIdStr = Objects.nonNull(merchantId) ? String.valueOf(merchantId) : "";
            String queryWord = generateParam.getQueryWord();
            queryWord = StringUtils.isNotEmpty(queryWord) ? queryWord : "";
            return MessageFormat.format("5-{0}-{1}", merchantIdStr, queryWord);
        }
        return "";
    }

}
