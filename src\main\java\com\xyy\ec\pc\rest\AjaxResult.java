package com.xyy.ec.pc.rest;

import java.io.Serializable;

/**
 * 操作消息提醒
 *
 * <AUTHOR>
 */
public class AjaxResult<T> implements Serializable {
    public int code;
    public String msg;
    public T data;
    public static final String MSG_SUCCESS = "操作成功";
    public static final int CODE_SUCCESS = 200;
    public static final int CODE_ERROR = 500;

    public AjaxResult() {
        this.code = CODE_SUCCESS;
        this.msg = MSG_SUCCESS;
        this.data = null;
    }

    public AjaxResult(T data) {
        this.code = CODE_SUCCESS;
        this.msg = MSG_SUCCESS;
        this.data = data;

    }

    public AjaxResult(String msg) {
        this.code = CODE_ERROR;
        this.msg = msg;
        this.data = null;
    }

    public AjaxResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
        this.data = null;
    }

    public AjaxResult(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> AjaxResult<T> errResult(String msg) {
        return new AjaxResult<>(msg);
    }

    public static <T> AjaxResult<T> errResult(AjaxErrorEnum errorEnum) {
        return new AjaxResult<>(errorEnum.code, errorEnum.msg);
    }

    public static <T> AjaxResult<T> errResult(AjaxErrorEnum errorEnum, T data) {
        return new AjaxResult<>(errorEnum.code, errorEnum.msg, data);
    }

    public static <T> AjaxResult<T> errResult(String msg, T data) {
        return new AjaxResult<>(CODE_ERROR, msg, data);
    }

    public static <T> AjaxResult<T> successResult(T data) {
        return new AjaxResult<>(data);
    }

    public static <T> AjaxResult<T> successResultNotData() {
        return new AjaxResult<>();
    }

    public static <T> AjaxResult<T> successResultNotResult(String msg) {
        return new AjaxResult<>(CODE_SUCCESS, msg);
    }

    public boolean isSuccess() {
        return this.code == CODE_SUCCESS;
    }

    public boolean isFail() {
        return this.code != CODE_SUCCESS;
    }
}
