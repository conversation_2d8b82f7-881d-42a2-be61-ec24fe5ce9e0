package com.xyy.ec.pc.cms.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * CMS拼团活动信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsGroupBuyingActInfoVO implements Serializable {
    /**
     * 商品skuId
     */
    private Long goodId;

    /**
     * 拼团价
     */
    private BigDecimal assemblePrice;

    /**
     * 拼团剩余持续时间，单位秒
     */
    private Long surplusTime;

    /**
     * 拼团进度
     */
    private BigDecimal percentage;

    /**
     * 起拼数量
     */
    private Integer skuStartNum;

    /**
     * 已拼数量
     */
    private Long orderNum;

    /**
     * 拼团开始时间
     */
    private Date assembleStartTime;

    /**
     * 拼团结束时间
     */
    private Date assembleEndTime;

    /**
     * 活动id
     */
    private Long marketingId;

    /**
     * 拼团状态：0未开始 1 进行中 2 结束
     */
    private Integer assembleStatus;

    /**
     * 拼团预热期是否展示价格字段,0-不展示，1-展示
     */
    private Integer preheatShowPrice;

    /**
     * 是否是多阶梯价拼团 1:是 2:否
     */
    private Integer stepPriceStatus;

    /**
     * 最低拼团价格。仅当是多阶梯价拼团时有效且有值。
     */
    private BigDecimal minSkuPrice;

    /**
     * 最高拼团价格。仅当是多阶梯价拼团时有效且有值。
     */
    private BigDecimal maxSkuPrice;

    /**
     * 起始价显示文本，如¥30.22起。仅当是多阶梯价拼团时有效且有值。
     */
    private String startingPriceShowText;

    /**
     * 价格区间显示文本，如¥2.38-¥6.88。仅当是多阶梯价拼团时有效且有值。
     */
    private String rangePriceShowText;

    /**
     * 多梯度价显示文本信息列表，如["满20盒，22.10元/盒","满40盒，19.10元/盒"]。仅当是多阶梯价拼团时有效且有值。
     */
    private List<String> stepPriceShowTexts;
}
