package com.xyy.ec.pc.controller.vo.merchant;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantClerkLicenseAuditImageVO implements Serializable {

    /**
     * 名称
     */
    private String name;

    /**
     * 资质类型编码(ec)
     */
    private String licenseCode;

    /**
     * 资质图片URL列表
     */
    private List<String> listImgUrls;

}
