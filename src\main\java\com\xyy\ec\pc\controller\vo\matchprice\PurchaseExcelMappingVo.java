package com.xyy.ec.pc.controller.vo.matchprice;

import lombok.Data;

/**
 * 采购单导入模板映射关系
 * <AUTHOR>
 */
@Data
public class PurchaseExcelMappingVo {

    /**
     * 商品名称
     */
    private String commonName;
    /**
     * 规格
     */
    private String spec;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     * 商品条码（69码）
     */
    private String code;
    /**
     * 采购数量
     */
    private String purchaseNum;
    /**
     * 采购价格
     */
    private String purchasePrice;

    /**
     * 商家编码（只用于透传在计划单导出数据）
     * 商品ID 商品编码
     */
    private String merchantCode;

    /**
     * 商品id 导入计划单用
     */
    private String csuId;
}
