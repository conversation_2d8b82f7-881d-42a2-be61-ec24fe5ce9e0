package com.xyy.ec.pc.newfront.service;

import com.xyy.ec.pc.newfront.dto.HeaderRespVO;
import com.xyy.ec.pc.newfront.dto.VerifiedRespVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.product.business.module.CategoryVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface IndexNewService {

    boolean isNewIndex(Long merchantId);

    /**
     * 获取头部数据
     */
    HeaderRespVO getHeaderData(HttpServletRequest request);

    /**
     * 分类树
     */
    AjaxResult<List<CategoryVo>> categoryTree(HttpServletRequest request) throws Exception;

    /**
     * 获取首页弹窗
     */
    AjaxResult<VerifiedRespVO> getVerified(HttpServletRequest request);
}
