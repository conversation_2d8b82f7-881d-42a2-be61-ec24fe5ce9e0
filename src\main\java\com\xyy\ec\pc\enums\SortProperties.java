package com.xyy.ec.pc.enums;

/**
 * @ClassName: SortProperties
 * @Description: 排序属性
 * @author: denghp
 * @date: 2020/2/27
 */
public enum SortProperties {

    DEFAULT("smsr.sale_num",1, "综合排序"),
    SALE("spa.sale_num",2, "销量排序"),
    FOB("fob",3, "价格排序"),
    CREATE("s.create_time",4, "最新上架排序"),

    ACT_PT_DEFAULT("actPt",100,"拼团综合排序")
    ;
    private String property;
    private Integer type;
    private String desc;

    SortProperties(String property, Integer type, String desc) {
        this.property = property;
        this.type = type;
        this.desc = desc;
    }

    public String getProperty() {
        return property;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getType() {
        return type;
    }

    public static SortProperties getByProperty(String property) {
        SortProperties[] properties = values();
        int length = properties.length;

        for (int i = 0; i < length; i++) {
            SortProperties sortProperties = properties[i];
            if (sortProperties.property.equalsIgnoreCase(property)) {
                return sortProperties;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + property + "]");
    }

}
