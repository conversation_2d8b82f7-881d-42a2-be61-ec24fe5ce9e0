package com.xyy.ec.pc.shop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.client.api.query.MarketingTagQueryService;
import com.xyy.ec.marketing.client.api.query.resp.ShopActivitiesTagView;
import com.xyy.ec.marketing.client.constants.ProductOwnerBizTypeEnum;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingSkuDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.ShippingAddressBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.ShippingAddressBussinessDto;
import com.xyy.ec.order.api.freight.FreightTemplateApi;
import com.xyy.ec.order.dto.freight.FreightConditionDto;
import com.xyy.ec.order.dto.freight.FreightTemplateDto;
import com.xyy.ec.order.utils.FreightFormatUtil;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.controller.vo.FloorVO;
import com.xyy.ec.pc.controller.vo.PCShopListProductInfoVo;
import com.xyy.ec.pc.enums.FloorTypeEnum;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.exception.ShopException;
import com.xyy.ec.pc.param.ShopStatisticInfoQueryParam;
import com.xyy.ec.pc.popshop.rpc.ProductApiRpc;
import com.xyy.ec.pc.rpc.HyperSpaceRpc;
import com.xyy.ec.pc.rpc.ProductServiceRpc;
import com.xyy.ec.pc.rpc.ShopServiceRpc;
import com.xyy.ec.pc.search.vo.PinTuanActInfoVo;
import com.xyy.ec.pc.service.ShopConfuseService;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.marketing.dto.MarketingCsuResultDto;
import com.xyy.ec.pc.service.marketing.dto.MarketingWholesaleActivityInfoDTO;
import com.xyy.ec.pc.shop.helpers.ShopInfoVOHelper;
import com.xyy.ec.pc.shop.helpers.ShopStatisticInfoQueryParamHelper;
import com.xyy.ec.pc.shop.service.ShopService;
import com.xyy.ec.pc.shop.vo.ShopInfoVO;
import com.xyy.ec.pc.shop.vo.ShopStaticsVo;
import com.xyy.ec.pc.util.BeanUtils;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.pc.vo.EcProductInfoVO;
import com.xyy.ec.pc.vo.EcPtActInfoVO;
import com.xyy.ec.pc.vo.ShopInfoSupperVO;
import com.xyy.ec.pop.server.api.external.dto.ShippingQueryDto;
import com.xyy.ec.pop.server.api.external.ec.PopEcShippinApi;
import com.xyy.ec.pop.server.api.merchant.dto.ShippingDto;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.product.business.ecp.out.product.dto.ShopSkuCountDTO;
import com.xyy.ec.product.business.ecp.out.shop.ProductForShopApi;
import com.xyy.ec.search.engine.enums.CsuOrder;
import com.xyy.ec.search.engine.pagination.Page;
import com.xyy.ec.shop.server.business.api.ShopCategoryApi;
import com.xyy.ec.shop.server.business.api.ShopQueryApi;
import com.xyy.ec.shop.server.business.api.ShopStatisticsQueryApi;
import com.xyy.ec.shop.server.business.enums.ShopImagePlatformTypeEnum;
import com.xyy.ec.shop.server.business.enums.ShopListSortEnum;
import com.xyy.ec.shop.server.business.params.ShopStatisticParams;
import com.xyy.ec.shop.server.business.results.ShopActivityInfoDTO;
import com.xyy.ec.shop.server.business.results.ShopCategoryDTO;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.ec.shop.server.business.results.ShopStatisticsReportDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ShopServiceImpl implements ShopService {

    @Reference(version = "1.0.0", timeout = 500, check = false)
    private ShopQueryApi shopQueryApi;

    @Reference(version = "1.0.0", timeout = 500, check = false)
    private ShopCategoryApi shopCategoryApi;

    @Autowired
    private HyperSpaceRpc hyperSpaceRpc;

    @Autowired
    private MarketingService marketingService;
    @Autowired
    private ProductApiRpc productApiRpc;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private MarketingTagQueryService marketingTagQueryService;

    @Reference(version = "1.0.0")
    private ProductForShopApi productForShopApi;

    @Reference(version = "1.0.0")
    private ShopStatisticsQueryApi shopStatisticsQueryApi;

    @Reference(version = "1.0.0")
    private PopEcShippinApi popEcShippinApi;

    @Reference(version = "1.0.0")
    FreightTemplateApi freightTemplateApi;
    @Reference(version = "1.0.0")
    private ShippingAddressBussinessApi shippingAddressBussinessApi;
    @Autowired
    private ShopServiceRpc shopServiceRpc;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private ShopConfuseService shopConfuseService;

    @Autowired
    private CmsAppProperties cmsAppProperties;

    private static LoadingCache<String, ShopStaticsVo> SHOP_STATIC_INFO_CACHE;
    @Autowired
    private ProductServiceRpc productServiceRpc;


    @PostConstruct
    public void setUp(){
        SHOP_STATIC_INFO_CACHE = CacheBuilder.newBuilder()
                .maximumSize(2*1024)
                .refreshAfterWrite(24, TimeUnit.HOURS)
                .build(new CacheLoader<String, ShopStaticsVo>() {
                    @Override
                    public ShopStaticsVo load(String key){
                        Map<String, ShopStaticsVo> stringShopStaticsVoMap = mgetShopStaticInfo(Lists.newArrayList(key));
                        return stringShopStaticsVoMap.get(key);
                    }
                    @Override
                    public Map<String, ShopStaticsVo> loadAll(Iterable<? extends String> keys) {
                        return mgetShopStaticInfo(Lists.newArrayList(keys));
                    }
                });

    }

    private Map<String,ShopStaticsVo> mgetShopStaticInfo(List<String> shopCodes){
        ApiRPCResult<List<ShopInfoDTO>> listApiRPCResult = shopQueryApi.queryShopByShopCodes(shopCodes);
        if (null == listApiRPCResult || !listApiRPCResult.isSuccess()){
            throw new ShopException("批量查询店铺信息异常", XyyJsonResultCodeEnum.QUERY_SHOP_CATEGORY_BY_SHOP_CODE_ERROR);
        }
        if (CollectionUtils.isEmpty(listApiRPCResult.getData())){
            return Maps.newHashMap();
        }

        Map<String,ShopStaticsVo> result = Maps.newHashMapWithExpectedSize(listApiRPCResult.getData().size());
        for (ShopInfoDTO item : listApiRPCResult.getData()){
            ShopStaticsVo shopStaticsVo = new ShopStaticsVo();
            shopStaticsVo.setShopCode(item.getShopCode());
            shopStaticsVo.setName(item.getName());
            shopStaticsVo.setAppLogo(item.getAppLogo());
            shopStaticsVo.setProvinceCode(item.getProvinceCode());
            shopStaticsVo.setShowName(item.getShowName());
            shopStaticsVo.setSalesVolume(BigDecimal.ZERO);
            result.put(item.getShopCode(), shopStaticsVo);
        }
        ApiRPCResult<PageInfo<ShopStatisticsReportDTO>> pageInfoApiRPCResult = shopStatisticsQueryApi.queryShopsStatistics(shopCodes, Arrays.asList("ybm","pop"), 1, Math.min(shopCodes.size(), 200));
        if (null == pageInfoApiRPCResult || !pageInfoApiRPCResult.isSuccess() || null == pageInfoApiRPCResult.getData() || CollectionUtils.isEmpty(pageInfoApiRPCResult.getData().getList())){
            return result;
        }
        for (ShopStatisticsReportDTO item : pageInfoApiRPCResult.getData().getList()){
            ShopStaticsVo shopStaticsVo = result.get(item.getShopCode());
            if (null != shopStaticsVo && null != item.getSalesVolume()){
                shopStaticsVo.setSalesVolume(item.getSalesVolume());
            }
        }
        return result;
    }



    @Override
    public boolean getShopVisible(String shopCode, Long merchantId, int provinceCode) {
        boolean isVisible;
        if (merchantId == null) {
            isVisible = getDefaultVisibleByShopCode(shopCode, provinceCode);
        } else {
            isVisible = checkShopByBuyerCode(merchantId, shopCode);
        }
        if (log.isDebugEnabled()) {
            log.debug("获取店铺的可见性，merchantId：{}，provinceCode：{}，shopCode：{}，isVisible：{}",
                    merchantId, provinceCode, shopCode, isVisible);
        }
        return isVisible;
    }

    @Override
    public ShopInfoVO getByCode(String shopCode) {
        ApiRPCResult<ShopInfoDTO> apiRPCResult = shopQueryApi.queryShopByShopCode(shopCode);
        if (!apiRPCResult.isSuccess()) {
            String message = MessageFormat.format("入参，shopCode：{0}", shopCode);
            throw new ShopException(message, XyyJsonResultCodeEnum.QUERY_SHOP_BY_SHOP_CODE_ERROR);
        }
        ShopInfoDTO data = apiRPCResult.getData();
        if (data == null) {
            return null;
        }
        return ShopInfoVOHelper.create(data);
    }

    @Override
    public ShopInfoVO getByOrgId(String orgId) {
        ApiRPCResult<ShopInfoDTO> apiRPCResult = shopQueryApi.queryShopByOrgId(orgId);
        if (!apiRPCResult.isSuccess()) {
            String message = MessageFormat.format("入参，orgId：{0}", orgId);
            throw new ShopException(message, XyyJsonResultCodeEnum.QUERY_SHOP_BY_SHOP_CODE_ERROR);
        }
        ShopInfoDTO data = apiRPCResult.getData();
        if (data == null) {
            return null;
        }
        return ShopInfoVOHelper.create(data);
    }

    @Override
    public List<ShopCategoryDTO> queryCategoryTreeByShopCode(String shopCode) {
        ApiRPCResult<List<ShopCategoryDTO>> apiRPCResult = shopCategoryApi.queryCategoryTreeByShopCode(shopCode, ShopImagePlatformTypeEnum.PC.getType());
        if (!apiRPCResult.isSuccess()) {
            String message = MessageFormat.format("入参，shopCode：{0}", shopCode);
            throw new ShopException(message, XyyJsonResultCodeEnum.QUERY_SHOP_CATEGORY_BY_SHOP_CODE_ERROR);
        }
        return apiRPCResult.getData();
    }

    private boolean getDefaultVisibleByShopCode(String shopCode, int provinceCode) {
        ApiRPCResult<Boolean> apiRPCResult = shopQueryApi.getDefaultVisibleByShopCode(shopCode, provinceCode);
        if (!apiRPCResult.isSuccess()) {
            String message = MessageFormat.format("入参，shopCode：{0}，provinceCode：{1}", shopCode, provinceCode);
            throw new ShopException(message, XyyJsonResultCodeEnum.GET_DEFAULT_VISIBLE_BY_SHOP_CODE_ERROR);
        }
        return BooleanUtils.isTrue(apiRPCResult.getData());
    }

    private boolean checkShopByBuyerCode(Long merchantId, String shopCode) {
        String buyerCode = null;
        if (merchantId != null && !Objects.equals(merchantId, 0L)) {
            buyerCode = String.valueOf(merchantId);
        }
        ApiRPCResult<Boolean> apiRPCResult = shopQueryApi.checkShopByBuyerCode(buyerCode, shopCode);
        if (!apiRPCResult.isSuccess()) {
            String message = MessageFormat.format("入参，buyerCode：{0}，shopCode：{1}", buyerCode, shopCode);
            throw new ShopException(message, XyyJsonResultCodeEnum.CHECK_SHOP_BY_BUYER_CODE_ERROR);
        }
        return BooleanUtils.isTrue(apiRPCResult.getData());
    }

    @Override
    public Map<String, ShopInfoDTO> getEcShopInfoByShopCode(Set<String> shopCodeSet) {
        if (CollectionUtils.isEmpty(shopCodeSet)) {
            return Maps.newHashMap();
        }
        ApiRPCResult<List<ShopInfoDTO>> listApiRPCResult = shopQueryApi.queryShopByShopCodes(shopCodeSet);
        if (!listApiRPCResult.isSuccess()) {
            log.error("【优惠券凑单页】查询店铺信息失败getEcShopInfoByShopCode,shopCodeSet:{},msg:{}",
                    shopCodeSet, listApiRPCResult.getMsg());
            return Maps.newHashMap();
        }
        List<ShopInfoDTO> shopInfoDTOS = listApiRPCResult.getData();
        if (CollectionUtils.isEmpty(shopInfoDTOS)) {
            return Maps.newHashMap();
        }
        return  shopInfoDTOS.stream().collect(Collectors.toMap(item -> item.getShopCode(),
                item -> item, (o1, o2) -> o1));
    }

    /**
     * 商家店铺首页展示
     *
     * @param merchantId
     * @param shopCode
     * @return
     */
    @Override
    public List<FloorVO> shopIndexView(Long merchantId, String shopCode) {
        List<FloorVO> floorVOS = new ArrayList<>();
        String virtualShopCode = shopServiceRpc.getVirtualShopCode();
        List<MarketingEnum> floorList = hyperSpaceRpc.getFloorList(merchantId, shopCode.equals(virtualShopCode) ? ProductOwnerBizTypeEnum.VIRTUAL_SHOP.getCode() : ProductOwnerBizTypeEnum.SELF.getCode(), shopCode);
        if(com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(floorList)){
            for (int i = 0; i < floorList.size(); i++) {
                MarketingEnum floor = floorList.get(i);
                FloorVO floorVO = new FloorVO();
                if(MarketingEnum.PING_TUAN.getCode() == floor.getCode()){
                    floorVO.setFloorType(FloorTypeEnum.PING_TUAN.getCode());
                    floorVO.setFloorId((long) i);
                    floorVO.setFloorName(floor.getTitle());
                    floorVOS.add(floorVO);
                }/*else if(MarketingEnum.TE_JIA.getCode() == floor.getCode()){
                    floorVO.setFloorType(FloorTypeEnum.TE_JIA.getCode());
                }*/

            }
        }
        return floorVOS;
    }

    /**
     * 首页拼团商品列表展示
     *
     * @param merchantId
     * @param branchCode
     * @return
     */
    @Override
    public List<EcProductInfoVO> ptSkuList(Long merchantId, String branchCode, String shopCode) {
        List<MarketingCsuResultDto> marketingCsuResultDtos = this.getShopGroupBuyingGoods(merchantId, shopCode);
        if (log.isDebugEnabled()) {
            log.debug("ptSkuList，merchantId：{}，branchCode：{}，shopCode：{}，marketingCsuResultDtos：{}",
                    merchantId, branchCode, shopCode, JSONArray.toJSONString(marketingCsuResultDtos));
        }
        if (CollectionUtils.isEmpty(marketingCsuResultDtos)) {
            return Lists.newArrayList();
        }
        List<Long> skuIds = marketingCsuResultDtos.stream().map(MarketingCsuResultDto::getCsuId).collect(Collectors.toList());
        // 映射关系：商品ID - 拼团信息
        Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap = marketingCsuResultDtos.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCsuId()) && Objects.nonNull(item.getGroupBuyingInfoDto()))
                .collect(Collectors.toMap(MarketingCsuResultDto::getCsuId, MarketingCsuResultDto::getGroupBuyingInfoDto, (s, f) -> s));
        List<List<Long>> lists = Lists.partition(skuIds, 200);
        if (log.isDebugEnabled()) {
            log.debug("ptSkuList，merchantId：{}，branchCode：{}，shopCode：{}，lists：{}", merchantId, branchCode, shopCode, JSONArray.toJSONString(lists));
        }
        List<EcProductInfoVO> skuVOS = Lists.newArrayListWithExpectedSize(16);
        for (List<Long> ids : lists) {
            List<ListProduct> productList = productApiRpc.findOnSaleProductBySkuIdList(ids, merchantId, branchCode);
            if (log.isDebugEnabled()) {
                log.debug("ptSkuList，merchantId：{}，branchCode：{}，shopCode：{}，ids：{}，productList：{}",
                        merchantId, branchCode, shopCode, JSONArray.toJSONString(ids), JSONArray.toJSONString(productList));
            }
            if (CollectionUtils.isEmpty(productList)) {
                continue;
            }
            //填充库存信息
            productList = productServiceRpc.fillAllProductActTotalSurplusQtyToAvailableQty(productList);
            {
                // 处方药商品默认图处理
                if (log.isDebugEnabled()) {
                    log.debug("【处方药商品默认图处理】merchantId：{}，shopCode：{}，原商品信息：{}", merchantId, shopCode, JSONArray.toJSONString(productList));
                }
                if (BooleanUtils.isTrue(cmsAppProperties.getIsOpenIndexProductDefaultImageFeature())) {
                    String defaultImageUrl = cmsAppProperties.getProductDefaultImageUrl();
                    productList.stream().filter(item -> Objects.nonNull(item) && Objects.equals(item.getDrugClassification(), 3))
                            .forEach(item -> item.setImageUrl(defaultImageUrl));
                    if (log.isDebugEnabled()) {
                        log.debug("【处方药商品默认图处理】merchantId：{}，shopCode：{}，处理后商品信息：{}", merchantId, shopCode, JSONArray.toJSONString(productList));
                    }
                }
            }
            List<String> shopCodes = productList.stream().map(ListProduct::getShopCode).distinct().collect(Collectors.toList());
            ApiRPCResult<List<ShopInfoDTO>> apiRPCResult = shopQueryApi.queryShopByShopCodes(shopCodes);
            if (log.isDebugEnabled()) {
                log.debug("ptSkuList，merchantId：{}，branchCode：{}，shopCode：{}，shopCodes：{}，apiRPCResult：{}",
                        merchantId, branchCode, shopCode, JSONArray.toJSONString(shopCodes), JSONObject.toJSONString(apiRPCResult));
            }
            if (apiRPCResult == null || CollectionUtils.isEmpty(apiRPCResult.getData())) {
                continue;
            }
            // 映射关系：店铺编码 - 店铺展示名称
            Map<String, String> shopCodeToShowNameMap = apiRPCResult.getData().stream()
                    .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getShopCode()) && Objects.nonNull(item.getShowName()))
                    .collect(Collectors.toMap(ShopInfoDTO::getShopCode, ShopInfoDTO::getShowName, (s, f) -> s));
            //过滤商品并按库存倒序排序
            List<EcProductInfoVO> skuList = productList.stream()
                    .filter(this::filterProduct)
                    .map(product -> this.getPtSkuListVo(product, shopCodeToShowNameMap.get(product.getShopCode()),
                            csuIdToGroupBuyingInfoMap.get(product.getId())))
                    .sorted(Comparator.comparing(EcProductInfoVO::getAvailableQty).reversed()).collect(Collectors.toList());
            if (log.isDebugEnabled()) {
                log.debug("ptSkuList，merchantId：{}，branchCode：{}，shopCode：{}，ids：{}，skuList：{}",
                        merchantId, branchCode, shopCode, JSONArray.toJSONString(ids), JSONArray.toJSONString(skuList));
            }
            if (CollectionUtils.isNotEmpty(skuList)) {
                skuVOS.addAll(skuList);
            }
        }
        return skuVOS;
    }


    /**
     * 分页查询店铺列表
     *
     * @param shopStatisticInfoQueryParam
     * @return
     */
    @Override
    public PageInfo<ShopInfoSupperVO> pagingShopList(ShopStatisticInfoQueryParam shopStatisticInfoQueryParam) {
        Boolean validate = ShopStatisticInfoQueryParamHelper.validate(shopStatisticInfoQueryParam);
        if (!BooleanUtils.isTrue(validate)) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "店铺首页信息列表查询");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        PageInfo<ShopInfoSupperVO> shopInfoSupperVOPageInfo = new PageInfo<>();
        Long merchantId = shopStatisticInfoQueryParam.getMerchantId();
        //获取会员id的branchCode
        String branchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchantId);

        //查询店铺统计信息
        ApiRPCResult<PageInfo<ShopStatisticsReportDTO>> pageInfoApiRPCResult;

        if (CollectionUtils.isNotEmpty(shopStatisticInfoQueryParam.getShopCodes())) {
            pageInfoApiRPCResult = getShopsByCodesRPCResult(shopStatisticInfoQueryParam);
        } else {
            pageInfoApiRPCResult = getPageInfoApiRPCResult(shopStatisticInfoQueryParam, branchCode);
        }
        log.info("查询店铺统计信息返回状态：{},{}", pageInfoApiRPCResult == null ? "null" : pageInfoApiRPCResult.isSuccess(), JSONObject.toJSONString(pageInfoApiRPCResult));

        //是否查询到了统计信息
        boolean haveShopStatistics = pageInfoApiRPCResult != null && pageInfoApiRPCResult.isSuccess() && pageInfoApiRPCResult.getData() != null && CollectionUtils.isNotEmpty(pageInfoApiRPCResult.getData().getList());
        //有店铺统计信息
        if (haveShopStatistics) {
            List<ShopStatisticsReportDTO> shopStatisticsReportDTOList = pageInfoApiRPCResult.getData().getList();
            log.info("查询店铺统计信息返回信息：{}", JSONObject.toJSONString(shopStatisticsReportDTOList));
            //查询店铺信息
            List<String> shopCodeList = shopStatisticsReportDTOList.stream().map(ShopStatisticsReportDTO::getShopCode).collect(Collectors.toList());
            if (BooleanUtils.isTrue(appProperties.getIsOpenShopConfuseShopListNotShowShopFeature())) {
                shopCodeList = shopCodeList.stream().filter(shopCode -> BooleanUtils.isNotTrue(shopConfuseService.isCantShowShop(shopCode))).collect(Collectors.toList());
            }
            List<ShopInfoDTO> shopInfoDTOList = null;
            if (CollectionUtils.isNotEmpty(shopCodeList)) {
                //获取可见店铺列表
                ApiRPCResult<List<ShopInfoDTO>> shopsByBuyerCode = shopQueryApi.getShopsByBuyerCode(String.valueOf(merchantId), shopCodeList);
                log.info("查询店铺信息返回状态：{}", shopsByBuyerCode == null ? "null" : shopsByBuyerCode.isSuccess());
                boolean haveShopInfo = shopsByBuyerCode != null && shopsByBuyerCode.isSuccess() && CollectionUtils.isNotEmpty(shopsByBuyerCode.getData());
                if (haveShopInfo) {
                    shopInfoDTOList = shopsByBuyerCode.getData();
                }
            }
            //有可见店铺信息
            List<ShopInfoSupperVO> shopInfoSupperVOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(shopInfoDTOList)) {
                log.info("查询店铺信息返回信息：{}", JSONObject.toJSONString(shopInfoDTOList));
                //店铺信息
                Map<String, ShopInfoDTO> shopInfoDTOMap = shopInfoDTOList.stream().collect(Collectors.toMap(ShopInfoDTO::getShopCode, x -> x, (x1, x2) -> x2));
                //查询营销信息
                log.info("查询营销信息 入参：merchantId:{},shopCodeList:{}", merchantId, JSONObject.toJSONString(shopCodeList));
                Map<String, List<ShopActivitiesTagView>> stringListMap = new HashMap<>();
                List<List<String>> shopCodePage = Lists.partition(shopCodeList,20);
                try {
                    for(List<String> shopCodes : shopCodePage){
                        Map<String, List<ShopActivitiesTagView>> map = marketingTagQueryService.mgetShopActivitiesTagsForShopList(merchantId, shopCodes);
                        if(CollectionUtil.isNotEmpty(map)){
                            stringListMap.putAll(map);
                        }
                    }
                } catch (Exception e) {
                    log.error("调用营销服务异常：", e);
                }
                log.info("查询营销信息返回信息：{}", JSONObject.toJSONString(stringListMap));
                boolean haveShopActivity = stringListMap != null && stringListMap.size() > 0;
                //查询商品明细
                log.info("查询商品信息 入参：merchantId:{},shopCodeList:{}", merchantId, JSONObject.toJSONString(shopCodeList));
                ApiRPCResult<Map<String, List<ProductDTO>>> csuListByShopCodes = productForShopApi.findCsuListByShopCodes(merchantId, null, shopCodeList, 3);
                log.info("查询商品信息返回信息：{}", JSONObject.toJSONString(csuListByShopCodes));
                boolean haveProduct = csuListByShopCodes != null && csuListByShopCodes.isSuccess() && csuListByShopCodes.getData() != null;

                Map<String, String> shopCodeFreightInfoMap = getShopCodeFreightInfoMap(shopInfoDTOList, shopStatisticInfoQueryParam);
                log.info("查询店铺运费信息返回：{}", JSONObject.toJSONString(shopCodeFreightInfoMap));
                //查询店铺上架数量
                ApiRPCResult<List<ShopSkuCountDTO>> skuUpCountByShopCodes = productForShopApi.getSkuUpCountByShopCodes(shopCodeList);
                log.info("查询店铺上架数量返回信息：{}", JSONObject.toJSONString(skuUpCountByShopCodes));
                boolean haveShopSkuCount = skuUpCountByShopCodes != null && skuUpCountByShopCodes.isSuccess() && CollectionUtils.isNotEmpty(skuUpCountByShopCodes.getData());
                Map<String, ShopSkuCountDTO> shopSkuCountDTOMap = new HashMap<>();
                if (haveShopSkuCount) {
                    shopSkuCountDTOMap = skuUpCountByShopCodes.getData().stream().collect(Collectors.toMap(ShopSkuCountDTO::getShopCode, x -> x, (X1, X2) -> X2));
                }

                for (ShopStatisticsReportDTO shopStatisticsReportDTO : shopStatisticsReportDTOList) {
                    String shopCode = shopStatisticsReportDTO.getShopCode();
                    ShopInfoSupperVO shopInfoSupperVO = new ShopInfoSupperVO();
                    if (shopInfoDTOMap.get(shopCode) == null) {
                        continue;
                    }
                    //赋值店铺相关信息
                    shopInfoSupperVO.setShopCode(shopCode);
                    shopInfoSupperVO.setShowName(shopInfoDTOMap.get(shopCode).getShowName());
                    shopInfoSupperVO.setOrgId(shopInfoDTOMap.get(shopCode).getOrgId());
                    shopInfoSupperVO.setAppLogo(shopInfoDTOMap.get(shopCode).getAppLogo());
                    shopInfoSupperVO.setNewAppLink(shopInfoDTOMap.get(shopCode).getNewAppLink());
                    shopInfoSupperVO.setPcLink(shopInfoDTOMap.get(shopCode).getPcLink());
                    shopInfoSupperVO.setShopPatternCode(shopInfoDTOMap.get(shopCode).getShopPatternCode());
                    //赋值店铺商品信息
                    shopInfoSupperVO.setProductInfo(new ArrayList<>());

                    List<ProductDTO> shopCsuSimpleDtoList = csuListByShopCodes.getData().get(shopCode);
                    log.debug("查询店铺：{} 商品信息返回信息：{}", shopCode, JSONObject.toJSONString(shopCsuSimpleDtoList));
                    if (CollectionUtils.isNotEmpty(shopCsuSimpleDtoList)) {
                        // 拼团商品展示拼团和批购包邮样式
                        List<Long> csuIds = shopCsuSimpleDtoList.stream().map(ProductDTO::getId).collect(Collectors.toList());
                        //查询拼团活动信息
                        Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap = marketingService.getActCardInfoBySkuIdList(csuIds,
                                Lists.newArrayList(MarketingQueryStatusEnum.UN_START.getType(), MarketingQueryStatusEnum.STARTING.getType()), merchantId,
                                Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), null);
                        List<PCShopListProductInfoVo> shopProductInfoList = shopCsuSimpleDtoList.stream().map(productDTO -> {
                            PCShopListProductInfoVo pcShopListProductInfoVo = new PCShopListProductInfoVo();
                            BeanUtils.copyProperties(productDTO, pcShopListProductInfoVo);
                            setActPtInfo(groupBuyingInfoDtoMap, productDTO, pcShopListProductInfoVo);
                            setActPgbyInfo(groupBuyingInfoDtoMap, productDTO, pcShopListProductInfoVo);
                            return pcShopListProductInfoVo;
                        }).collect(Collectors.toList());
                        shopInfoSupperVO.setProductInfo(shopProductInfoList);
                    }

                    //赋值大数据统计的销量信息
                    shopInfoSupperVO.setSalesVolumeDesc("发货xxx件");
                    shopInfoSupperVO.setSalesVolume(shopStatisticsReportDTO.getSalesVolume() == null ? BigDecimal.ZERO : shopStatisticsReportDTO.getSalesVolume().setScale(0, BigDecimal.ROUND_HALF_UP));
                    if (shopStatisticsReportDTO.getSalesVolume() != null && shopStatisticsReportDTO.getSalesVolume().compareTo(new BigDecimal("10000")) >= 0
                            && shopStatisticsReportDTO.getSalesVolume().compareTo(new BigDecimal("100000000")) < 0) {
                        shopInfoSupperVO.setSalesVolume((shopStatisticsReportDTO.getSalesVolume().divide(new BigDecimal("10000"))).setScale(1, BigDecimal.ROUND_HALF_UP));
                        shopInfoSupperVO.setSalesVolumeDesc("发货xxx万件");
                    }
                    if (shopStatisticsReportDTO.getSalesVolume() != null && shopStatisticsReportDTO.getSalesVolume().compareTo(new BigDecimal("100000000")) >= 0) {
                        shopInfoSupperVO.setSalesVolume((shopStatisticsReportDTO.getSalesVolume().divide(new BigDecimal("100000000"))).setScale(1, BigDecimal.ROUND_HALF_UP));
                        shopInfoSupperVO.setSalesVolumeDesc("发货xxx亿件");
                    }
                    shopInfoSupperVO.setShelves(BigDecimal.ZERO);
                    //赋值上架信息
                    shopInfoSupperVO.setShelvesDesc("上架xxx种");
                    if (haveShopSkuCount) {
                        shopInfoSupperVO.setShelves(shopSkuCountDTOMap.get(shopCode) == null ? BigDecimal.ZERO : new BigDecimal(shopSkuCountDTOMap.get(shopCode).getSkuNum()));
                    }

                    //赋值店铺优惠信息
                    shopInfoSupperVO.setActivityInfo(new ArrayList<>());
                    if (haveShopActivity) {
                        List<ShopActivitiesTagView> shopActivitiesTagDtoList = stringListMap.get(shopCode);
                        log.debug("查询店铺：{} 活动信息返回信息：{}", shopCode, JSONObject.toJSONString(shopActivitiesTagDtoList));
                        if (CollectionUtils.isNotEmpty(shopActivitiesTagDtoList)) {
                            List<ShopActivityInfoDTO> shopActivityInfoDTOList = shopActivitiesTagDtoList.stream().map(ShopActivityInfoDTO::cloneShopActivity).collect(Collectors.toList());
                            shopInfoSupperVO.setActivityInfo(shopActivityInfoDTOList);
                        }
                    }
                    //赋值运费信息
                    String freightTips = "";
                    if (shopStatisticInfoQueryParam.getShopPropertyCode().equals("other")) {
                        String orgId = shopInfoDTOMap.get(shopCode).getOrgId();
                        if (StringUtils.isNotEmpty(orgId)) {
                            freightTips = shopCodeFreightInfoMap.get(orgId) == null ? "" : shopCodeFreightInfoMap.get(orgId);
                        }
                    } else {
                        String shopBranchCode = shopInfoDTOMap.get(shopCode).getBranchCode();
                        freightTips = shopCodeFreightInfoMap.get(shopBranchCode) == null ? "" : shopCodeFreightInfoMap.get(shopBranchCode);
                    }
                    log.info("freightTips={}", freightTips);
                    shopInfoSupperVO.setFreightTips(freightTips);
                    shopInfoSupperVOList.add(shopInfoSupperVO);
                }
            }
            // 填充返回信息
            PageInfo<ShopStatisticsReportDTO> shopStatisticsReportDTOPageInfo = pageInfoApiRPCResult.getData();
            shopInfoSupperVOPageInfo.setPageNum(shopStatisticsReportDTOPageInfo.getPageNum());
            shopInfoSupperVOPageInfo.setTotal(shopStatisticsReportDTOPageInfo.getTotal());
            shopInfoSupperVOPageInfo.setPageSize(shopStatisticsReportDTOPageInfo.getPageSize());
            shopInfoSupperVOPageInfo.setList(shopInfoSupperVOList);
        }
        return shopInfoSupperVOPageInfo;
    }


    private void setActPtInfo(Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap, ProductDTO productDTO, PCShopListProductInfoVo productInfoVo) {
        if (productDTO == null || productInfoVo == null || MapUtils.isEmpty(groupBuyingInfoDtoMap)) {
            return;
        }
        Optional.ofNullable(groupBuyingInfoDtoMap.get(productDTO.getId())).ifPresent(buyingInfoDto -> {
            if (!Objects.equals(buyingInfoDto.getActivityType(), MarketingEnum.PING_TUAN.getCode())) {
                return;
            }
            //活动信息
            GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtil.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList()) ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
            /** 拼团活动状态 1.未开始 ，2.拼团中，3.已结束 兼容老逻辑前端拼团活动状态 0-未开始 1-进行中 2-结束 **/
            Integer assembleStatus = buyingInfoDto.getStatus() == null ? 0 : buyingInfoDto.getStatus() - 1;

            PinTuanActInfoVo pinTuanActInfoVo = PinTuanActInfoVo.builder()
                    .marketingId(buyingInfoDto.getMarketingId())
                    .percentage(String.valueOf(buyingInfoDto.getPercentage().multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_UP))))
                    .assembleStatus(assembleStatus)
                    .assembleStartTime(buyingInfoDto.getStartTime().getTime())
                    .assembleEndTime(buyingInfoDto.getEndTime().getTime())
                    .surplusTime((buyingInfoDto.getEndTime().getTime() - System.currentTimeMillis()) / 1000)
                    .orderNum(buyingInfoDto.getOrderNum())
                    .skuStartNum(groupBuyingSkuDto.getSkuStartNum())
                    .preheatShowPrice(buyingInfoDto.getPreheatShowPrice())
                    // 拼团多阶梯价信息
                    .stepPriceStatus(buyingInfoDto.getStepPriceStatus())
                    .minSkuPrice(buyingInfoDto.getMinSkuPrice())
                    .maxSkuPrice(buyingInfoDto.getMaxSkuPrice())
                    .startingPriceShowText(buyingInfoDto.getStartingPriceShowText())
                    .rangePriceShowText(buyingInfoDto.getRangePriceShowText())
                    .stepPriceShowTexts(buyingInfoDto.generateStepPriceShowTexts(productInfoVo.getProductUnit()))
                    .build();
            Optional.ofNullable(groupBuyingSkuDto).ifPresent(groupBuyingSkuDto1 -> {
                pinTuanActInfoVo.setSkuStartNum(groupBuyingSkuDto1.getSkuStartNum());
                pinTuanActInfoVo.setAssemblePrice(groupBuyingSkuDto1.getSkuPrice());
            });
            //调整拼团商品的showName
            StringBuilder sbShowName = new StringBuilder();
            if (SearchUtils.isShouTuiYouXuan(productDTO.getFirstChoose(), productDTO.getHighGross())) {
                sbShowName.append(Constants.SHOU_TUI_YOU_XUAN_TEXT);
            } else if (StringUtils.isNotEmpty(buyingInfoDto.getTopicPrefix())) {
                sbShowName.append(buyingInfoDto.getTopicPrefix());
            }
            if (groupBuyingSkuDto.getSkuStartNum() != null) {
                String productUnit = Optional.ofNullable(productInfoVo.getProductUnit()).orElse("");
                sbShowName.append(groupBuyingSkuDto.getSkuStartNum()).append(productUnit).append("包邮").append(" ").append(productInfoVo.getShowName());
            } else {
                sbShowName.append(productInfoVo.getShowName());
            }
            //追加规格显示
            if (StringUtils.isNotEmpty(productInfoVo.getSpec()) && !productInfoVo.getSpec().equals(Constants.LINE)) {
                sbShowName.append("/").append(productInfoVo.getSpec());
            }
            productInfoVo.setShowName(sbShowName.toString());
            productInfoVo.setActPt(pinTuanActInfoVo);
        });
    }


    /**
     * 分页查询店铺列表
     *
     * @param shopStatisticInfoQueryParam
     * @return
     */
    @Override
    public PageInfo<ShopInfoSupperVO> simplePagingShopList(ShopStatisticInfoQueryParam shopStatisticInfoQueryParam) {
        Boolean validate = ShopStatisticInfoQueryParamHelper.validate(shopStatisticInfoQueryParam);
        if (!BooleanUtils.isTrue(validate)) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "店铺首页信息列表查询");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        PageInfo<ShopInfoSupperVO> shopInfoSupperVOPageInfo = new PageInfo<>();
        Long merchantId = shopStatisticInfoQueryParam.getMerchantId();
        //获取会员id的branchCode
        String branchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchantId);

        //查询店铺统计信息
        ApiRPCResult<PageInfo<ShopStatisticsReportDTO>> pageInfoApiRPCResult;

        if (CollectionUtils.isNotEmpty(shopStatisticInfoQueryParam.getShopCodes())) {
            pageInfoApiRPCResult = getShopsByCodesRPCResult(shopStatisticInfoQueryParam);
        } else {
            pageInfoApiRPCResult = getPageInfoApiRPCResult(shopStatisticInfoQueryParam, branchCode);
        }
        log.info("查询店铺统计信息返回状态：{},{}", pageInfoApiRPCResult == null ? "null" : pageInfoApiRPCResult.isSuccess(), JSONObject.toJSONString(pageInfoApiRPCResult));

        //是否查询到了统计信息
        boolean haveShopStatistics = pageInfoApiRPCResult != null && pageInfoApiRPCResult.isSuccess() && pageInfoApiRPCResult.getData() != null && CollectionUtils.isNotEmpty(pageInfoApiRPCResult.getData().getList());
        //有店铺统计信息
        if (haveShopStatistics) {
            List<ShopStatisticsReportDTO> shopStatisticsReportDTOList = pageInfoApiRPCResult.getData().getList();
            log.info("查询店铺统计信息返回信息：{}", JSONObject.toJSONString(shopStatisticsReportDTOList));
            //查询店铺信息
            List<String> shopCodeList = shopStatisticsReportDTOList.stream().map(ShopStatisticsReportDTO::getShopCode).collect(Collectors.toList());
            if (BooleanUtils.isTrue(appProperties.getIsOpenShopConfuseShopListNotShowShopFeature())) {
                shopCodeList = shopCodeList.stream().filter(shopCode -> BooleanUtils.isNotTrue(shopConfuseService.isCantShowShop(shopCode))).collect(Collectors.toList());
            }
            List<ShopInfoDTO> shopInfoDTOList = null;
            if (CollectionUtils.isNotEmpty(shopCodeList)) {
                //获取可见店铺列表
                ApiRPCResult<List<ShopInfoDTO>> shopsByBuyerCode = shopQueryApi.getShopsByBuyerCode(String.valueOf(merchantId), shopCodeList);
                log.info("查询店铺信息返回状态：{}", shopsByBuyerCode == null ? "null" : shopsByBuyerCode.isSuccess());
                boolean haveShopInfo = shopsByBuyerCode != null && shopsByBuyerCode.isSuccess() && CollectionUtils.isNotEmpty(shopsByBuyerCode.getData());
                if (haveShopInfo) {
                    shopInfoDTOList = shopsByBuyerCode.getData();
                }
            }
            //有可见店铺信息
            List<ShopInfoSupperVO> shopInfoSupperVOList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(shopInfoDTOList)) {
                log.info("查询店铺信息返回信息：{}", JSONObject.toJSONString(shopInfoDTOList));
                //店铺信息
                Map<String, ShopInfoDTO> shopInfoDTOMap = shopInfoDTOList.stream().collect(Collectors.toMap(ShopInfoDTO::getShopCode, x -> x, (x1, x2) -> x2));
                //查询营销信息
                log.info("查询营销信息 入参：merchantId:{},shopCodeList:{}", merchantId, JSONObject.toJSONString(shopCodeList));
                Map<String, List<ShopActivitiesTagView>> stringListMap = new HashMap<>();
                List<List<String>> shopCodePage = Lists.partition(shopCodeList,20);
                try {
                    for(List<String> shopCodes : shopCodePage){
                        Map<String, List<ShopActivitiesTagView>> map = marketingTagQueryService.mgetShopActivitiesTagsForShopList(merchantId, shopCodes);
                        if(CollectionUtil.isNotEmpty(map)){
                            stringListMap.putAll(map);
                        }
                    }
                } catch (Exception e) {
                    log.error("调用营销服务异常：", e);
                }
                log.info("查询营销信息返回信息：{}", JSONObject.toJSONString(stringListMap));
                boolean haveShopActivity = stringListMap != null && stringListMap.size() > 0;
                //查询商品明细
                log.info("查询商品信息 入参：merchantId:{},shopCodeList:{}", merchantId, JSONObject.toJSONString(shopCodeList));
                ApiRPCResult<Map<String, List<ProductDTO>>> csuListByShopCodes = productForShopApi.findSimpleCsuListByShopCodes(merchantId, null, shopCodeList, 3);
                log.info("查询商品信息返回信息：{}", JSONObject.toJSONString(csuListByShopCodes));
                boolean haveProduct = csuListByShopCodes != null && csuListByShopCodes.isSuccess() && csuListByShopCodes.getData() != null;

                Map<String, String> shopCodeFreightInfoMap = getShopCodeFreightInfoMap(shopInfoDTOList, shopStatisticInfoQueryParam);
                log.info("查询店铺运费信息返回：{}", JSONObject.toJSONString(shopCodeFreightInfoMap));
                //查询店铺上架数量
                ApiRPCResult<List<ShopSkuCountDTO>> skuUpCountByShopCodes = productForShopApi.getSkuUpCountByShopCodes(shopCodeList);
                log.info("查询店铺上架数量返回信息：{}", JSONObject.toJSONString(skuUpCountByShopCodes));
                boolean haveShopSkuCount = skuUpCountByShopCodes != null && skuUpCountByShopCodes.isSuccess() && CollectionUtils.isNotEmpty(skuUpCountByShopCodes.getData());
                Map<String, ShopSkuCountDTO> shopSkuCountDTOMap = new HashMap<>();
                if (haveShopSkuCount) {
                    shopSkuCountDTOMap = skuUpCountByShopCodes.getData().stream().collect(Collectors.toMap(ShopSkuCountDTO::getShopCode, x -> x, (X1, X2) -> X2));
                }

                for (ShopStatisticsReportDTO shopStatisticsReportDTO : shopStatisticsReportDTOList) {
                    String shopCode = shopStatisticsReportDTO.getShopCode();
                    ShopInfoSupperVO shopInfoSupperVO = new ShopInfoSupperVO();
                    if (shopInfoDTOMap.get(shopCode) == null) {
                        continue;
                    }
                    //赋值店铺相关信息
                    shopInfoSupperVO.setShopCode(shopCode);
                    shopInfoSupperVO.setShowName(shopInfoDTOMap.get(shopCode).getShowName());
                    shopInfoSupperVO.setOrgId(shopInfoDTOMap.get(shopCode).getOrgId());
                    shopInfoSupperVO.setAppLogo(shopInfoDTOMap.get(shopCode).getAppLogo());
                    shopInfoSupperVO.setNewAppLink(shopInfoDTOMap.get(shopCode).getNewAppLink());
                    shopInfoSupperVO.setPcLink(shopInfoDTOMap.get(shopCode).getPcLink());
                    shopInfoSupperVO.setShopPatternCode(shopInfoDTOMap.get(shopCode).getShopPatternCode());
                    //赋值店铺商品信息
                    shopInfoSupperVO.setProductInfo(new ArrayList<>());

                    List<ProductDTO> shopCsuSimpleDtoList = csuListByShopCodes.getData().get(shopCode);
                    log.debug("查询店铺：{} 商品信息返回信息：{}", shopCode, JSONObject.toJSONString(shopCsuSimpleDtoList));
                    if (CollectionUtils.isNotEmpty(shopCsuSimpleDtoList)) {
                        List<PCShopListProductInfoVo> shopProductInfoList = shopCsuSimpleDtoList.stream().map(productDTO -> {
                            PCShopListProductInfoVo pcShopListProductInfoVo = new PCShopListProductInfoVo();
                            BeanUtils.copyProperties(productDTO, pcShopListProductInfoVo);
                            return pcShopListProductInfoVo;
                        }).collect(Collectors.toList());
                        shopInfoSupperVO.setProductInfo(shopProductInfoList);
                    }

                    //赋值大数据统计的销量信息
                    shopInfoSupperVO.setSalesVolumeDesc("发货xxx件");
                    shopInfoSupperVO.setSalesVolume(shopStatisticsReportDTO.getSalesVolume() == null ? BigDecimal.ZERO : shopStatisticsReportDTO.getSalesVolume().setScale(0, BigDecimal.ROUND_HALF_UP));
                    if (shopStatisticsReportDTO.getSalesVolume() != null && shopStatisticsReportDTO.getSalesVolume().compareTo(new BigDecimal("10000")) >= 0
                            && shopStatisticsReportDTO.getSalesVolume().compareTo(new BigDecimal("100000000")) < 0) {
                        shopInfoSupperVO.setSalesVolume((shopStatisticsReportDTO.getSalesVolume().divide(new BigDecimal("10000"))).setScale(1, BigDecimal.ROUND_HALF_UP));
                        shopInfoSupperVO.setSalesVolumeDesc("发货xxx万件");
                    }
                    if (shopStatisticsReportDTO.getSalesVolume() != null && shopStatisticsReportDTO.getSalesVolume().compareTo(new BigDecimal("100000000")) >= 0) {
                        shopInfoSupperVO.setSalesVolume((shopStatisticsReportDTO.getSalesVolume().divide(new BigDecimal("100000000"))).setScale(1, BigDecimal.ROUND_HALF_UP));
                        shopInfoSupperVO.setSalesVolumeDesc("发货xxx亿件");
                    }
                    shopInfoSupperVO.setShelves(BigDecimal.ZERO);
                    //赋值上架信息
                    shopInfoSupperVO.setShelvesDesc("上架xxx种");
                    if (haveShopSkuCount) {
                        shopInfoSupperVO.setShelves(shopSkuCountDTOMap.get(shopCode) == null ? BigDecimal.ZERO : new BigDecimal(shopSkuCountDTOMap.get(shopCode).getSkuNum()));
                    }

                    //赋值店铺优惠信息
                    shopInfoSupperVO.setActivityInfo(new ArrayList<>());
                    if (haveShopActivity) {
                        List<ShopActivitiesTagView> shopActivitiesTagDtoList = stringListMap.get(shopCode);
                        log.debug("查询店铺：{} 活动信息返回信息：{}", shopCode, JSONObject.toJSONString(shopActivitiesTagDtoList));
                        if (CollectionUtils.isNotEmpty(shopActivitiesTagDtoList)) {
                            List<ShopActivityInfoDTO> shopActivityInfoDTOList = shopActivitiesTagDtoList.stream().map(ShopActivityInfoDTO::cloneShopActivity).collect(Collectors.toList());
                            shopInfoSupperVO.setActivityInfo(shopActivityInfoDTOList);
                        }
                    }
                    //赋值运费信息
                    String freightTips = "";
                    if (shopStatisticInfoQueryParam.getShopPropertyCode().equals("other")) {
                        String orgId = shopInfoDTOMap.get(shopCode).getOrgId();
                        if (StringUtils.isNotEmpty(orgId)) {
                            freightTips = shopCodeFreightInfoMap.get(orgId) == null ? "" : shopCodeFreightInfoMap.get(orgId);
                        }
                    } else {
                        String shopBranchCode = shopInfoDTOMap.get(shopCode).getBranchCode();
                        freightTips = shopCodeFreightInfoMap.get(shopBranchCode) == null ? "" : shopCodeFreightInfoMap.get(shopBranchCode);
                    }
                    log.info("freightTips={}", freightTips);
                    shopInfoSupperVO.setFreightTips(freightTips);
                    shopInfoSupperVOList.add(shopInfoSupperVO);
                }
            }
            // 填充返回信息
            PageInfo<ShopStatisticsReportDTO> shopStatisticsReportDTOPageInfo = pageInfoApiRPCResult.getData();
            shopInfoSupperVOPageInfo.setPageNum(shopStatisticsReportDTOPageInfo.getPageNum());
            shopInfoSupperVOPageInfo.setTotal(shopStatisticsReportDTOPageInfo.getTotal());
            shopInfoSupperVOPageInfo.setPageSize(shopStatisticsReportDTOPageInfo.getPageSize());
            shopInfoSupperVOPageInfo.setList(shopInfoSupperVOList);
        }
        return shopInfoSupperVOPageInfo;
    }

    private void setActPgbyInfo(Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap, ProductDTO productDTO, PCShopListProductInfoVo appSearchProductInfoVo) {
        if (productDTO == null || appSearchProductInfoVo == null || MapUtils.isEmpty(groupBuyingInfoDtoMap)) {
            return;
        }
        Optional.ofNullable(groupBuyingInfoDtoMap.get(productDTO.getId())).ifPresent(buyingInfoDto -> {
            if (!Objects.equals(buyingInfoDto.getActivityType(), MarketingEnum.PI_GOU_BAO_YOU.getCode())) {
                return;
            }
            // 活动信息
            GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtil.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList()) ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
            MarketingWholesaleActivityInfoDTO wholesaleActivityInfoDTO = MarketingWholesaleActivityInfoDTO.builder()
                    .marketingId(buyingInfoDto.getMarketingId())
                    .activityType(buyingInfoDto.getActivityType())
                    .skuStartNum(groupBuyingSkuDto.getSkuStartNum())
                    .build();
            Optional.ofNullable(groupBuyingSkuDto).ifPresent(tempGroupBuyingSkuDto -> {
                wholesaleActivityInfoDTO.setSkuStartNum(tempGroupBuyingSkuDto.getSkuStartNum());
                wholesaleActivityInfoDTO.setAssemblePrice(tempGroupBuyingSkuDto.getSkuPrice());
            });
            // 调整商品的showName
            StringBuilder sbShowName = new StringBuilder();
            if (groupBuyingSkuDto.getSkuStartNum() != null) {
                String productUnit = Optional.ofNullable(appSearchProductInfoVo.getProductUnit()).orElse("");
                sbShowName.append(groupBuyingSkuDto.getSkuStartNum()).append(productUnit).append("包邮").append(" ").append(appSearchProductInfoVo.getShowName());
            } else {
                sbShowName.append(appSearchProductInfoVo.getShowName());
            }
            // 追加规格显示
            if (StringUtils.isNotEmpty(appSearchProductInfoVo.getSpec()) && !Objects.equals(appSearchProductInfoVo.getSpec(), Constants.LINE)) {
                sbShowName.append("/").append(appSearchProductInfoVo.getSpec());
            }
            appSearchProductInfoVo.setShowName(sbShowName.toString());
            appSearchProductInfoVo.setActPgby(wholesaleActivityInfoDTO);
        });
    }

    @Override
    public Map<String, ShopStaticsVo> mgetShopStaticsByCode(List<String> shopCodes) {
        try {
            return SHOP_STATIC_INFO_CACHE.getAll(shopCodes);
        } catch (Exception e) {
            log.info("mgetShopStaticsByCode_shopCodes={}", JSON.toJSONString(shopCodes));
        }
        return Maps.newHashMap();
    }

    @Override
    public PageInfo<ShopStatisticsReportDTO> getShopsByCodes(List<String> shopCodes, List<String> patternCodeList, int pageNum, int pageSize) {
        ApiRPCResult<PageInfo<ShopStatisticsReportDTO>> pageInfoApiRPCResult = shopStatisticsQueryApi.queryShopsStatistics(shopCodes, patternCodeList, pageNum, pageSize);
        if (null == pageInfoApiRPCResult || !pageInfoApiRPCResult.isSuccess()){
            throw new RuntimeException("查询店铺服务异常");
        }
        return pageInfoApiRPCResult.getData();
    }

    /**
     * 根据店铺Code查询店铺信息列表
     *
     * @param shopStatisticInfoQueryParam
     * @return
     */
    private ApiRPCResult<PageInfo<ShopStatisticsReportDTO>> getShopsByCodesRPCResult(ShopStatisticInfoQueryParam shopStatisticInfoQueryParam) {
        log.info("根据店铺Code查询店铺信息列表 param：{}", JSONObject.toJSONString(shopStatisticInfoQueryParam));

        String shopPropertyCode = shopStatisticInfoQueryParam.getShopPropertyCode();
        List<String> patternCodeList = shopPropertyCode.equals("self") ? Arrays.asList("personal", "ybm") : Arrays.asList("pop", "fbp") ;

        int pageNum = shopStatisticInfoQueryParam.getPageNum();
        int pageSize = shopStatisticInfoQueryParam.getPageSize();

        List<String> shopCodes = shopStatisticInfoQueryParam.getShopCodes();

        return shopStatisticsQueryApi.queryShopsStatistics(shopCodes, patternCodeList, pageNum, pageSize);
    }


    /**
     * 获取店铺统计列表
     *
     * @param shopStatisticInfoQueryParam
     * @return
     */
    private ApiRPCResult<PageInfo<ShopStatisticsReportDTO>> getPageInfoApiRPCResult(ShopStatisticInfoQueryParam shopStatisticInfoQueryParam, String branchCode) {
        log.info("查询店铺统计信息 ShopStatisticInfoQueryParam：{}，branchCode：{}", JSONObject.toJSONString(shopStatisticInfoQueryParam), branchCode);
        ApiRPCResult<PageInfo<ShopStatisticsReportDTO>> pageInfoApiRPCResult;
        String sort = shopStatisticInfoQueryParam.getSort();
        String shopPropertyCode = shopStatisticInfoQueryParam.getShopPropertyCode();
        int pageNum = shopStatisticInfoQueryParam.getPageNum();
        int pageSize = shopStatisticInfoQueryParam.getPageSize();
        List<String> patternCodeList = shopPropertyCode.equals("self") ? Arrays.asList("personal", "ybm") : Arrays.asList("pop", "fbp") ;
        //自营默认（店铺30天购买人数）排序下 将会员所在域的店铺排在最前面
        if (sort.equals(ShopListSortEnum.DEFAULT.getSort()) && shopPropertyCode.equals("self")) {
            //第一页 查询会员所在域ybm店铺
            Map<String, ShopStatisticsReportDTO> firstSelf = new HashMap<>();
            List<ShopStatisticsReportDTO> shopStatisticsReportDTOMerge = new ArrayList<>();
            //查询会员所在域 店铺
            ShopStatisticParams shopStatisticParams =
                    ShopStatisticParams.builder().branchCode(branchCode).sort(sort).shopPatternCodeList(Collections.singletonList("ybm")).build();
            pageInfoApiRPCResult = shopStatisticsQueryApi.pagingShopStatistics(shopStatisticParams, pageNum, pageSize);
            //第一页 会员所在域店铺在前 排序后的其它域店铺 合并list
            if (pageInfoApiRPCResult != null && pageInfoApiRPCResult.isSuccess()) {
                PageInfo<ShopStatisticsReportDTO> shopStatisticsReportDTOPageInfo = pageInfoApiRPCResult.getData();
                if (CollectionUtils.isNotEmpty(shopStatisticsReportDTOPageInfo.getList())) {
                    firstSelf = shopStatisticsReportDTOPageInfo.getList().stream().collect(Collectors.toMap(ShopStatisticsReportDTO::getShopCode, a -> a, (k1, k2) -> k2));
                    List<ShopStatisticsReportDTO> shopStatisticsReportDTOList = shopStatisticsReportDTOPageInfo.getList();
                    if (pageNum == 1) {
                        shopStatisticsReportDTOMerge.addAll(shopStatisticsReportDTOList);
                    }
                }
            }
            //查询店铺列表
            ShopStatisticParams shopStatisticParamsOther = ShopStatisticParams.builder().shopPatternCodeList(patternCodeList)
                    .sort(sort).build();
            pageInfoApiRPCResult = shopStatisticsQueryApi.pagingShopStatistics(shopStatisticParamsOther, pageNum, pageSize);
            if (pageInfoApiRPCResult != null && pageInfoApiRPCResult.isSuccess()) {
                PageInfo<ShopStatisticsReportDTO> shopStatisticsReportDTOPageInfo = pageInfoApiRPCResult.getData();
                if (CollectionUtils.isNotEmpty(shopStatisticsReportDTOPageInfo.getList())) {
                    for (ShopStatisticsReportDTO a : shopStatisticsReportDTOPageInfo.getList()) {
                        if (firstSelf.containsKey(a.getShopCode())) {
                            continue;
                        }
                        shopStatisticsReportDTOMerge.add(a);
                    }
                }
                //设置合并list
                shopStatisticsReportDTOPageInfo.setList(shopStatisticsReportDTOMerge);
                pageInfoApiRPCResult.setData(shopStatisticsReportDTOPageInfo);
            }
            //非自营默认（自营最新 pop最新 pop默认）只按排序规则排序
        } else {
            ShopStatisticParams shopStatisticParamsOther = ShopStatisticParams.builder().shopPatternCodeList(patternCodeList)
                    .sort(sort).build();
            pageInfoApiRPCResult = shopStatisticsQueryApi.pagingShopStatistics(shopStatisticParamsOther, pageNum, pageSize);

        }
        return pageInfoApiRPCResult;
    }


    /**
     * 组织店铺运费赋值信息
     *
     * @param shopInfoDTOList
     * @param shopStatisticInfoQueryParam
     * @return
     */
    private Map<String, String> getShopCodeFreightInfoMap(List<ShopInfoDTO> shopInfoDTOList,
                                                          ShopStatisticInfoQueryParam shopStatisticInfoQueryParam) {
        try {
            log.info("查询店铺运费信息");
            MerchantBussinessDto merchantById = merchantBussinessApi.findMerchantById(shopStatisticInfoQueryParam.getMerchantId());
            //用户类型
            Integer businessType = merchantById.getBusinessType();
            String registerCode = merchantById.getRegisterCode();
            ShippingAddressBussinessDto defaultAddress = shippingAddressBussinessApi.getDefaultAddressByMerchantId(shopStatisticInfoQueryParam.getMerchantId());
            //pop店铺
            if (shopStatisticInfoQueryParam.getShopPropertyCode().equals("other")) {
                Map<String, String> shopCodeFreightInfoPopMap = new HashMap<>();
                //查询pop店铺运费信息
                List<String> orgIdList = shopInfoDTOList.stream().filter(a -> a.getOrgId() != null).map(ShopInfoDTO::getOrgId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(orgIdList)) {
                    return shopCodeFreightInfoPopMap;
                }
                List<ShippingQueryDto> shippingQueryDtoList = orgIdList.stream().map(a -> {
                    ShippingQueryDto shippingQueryDto = new ShippingQueryDto();
                    shippingQueryDto.setOrgId(a);
                    shippingQueryDto.setBranchCode(registerCode);
                    if (Objects.nonNull(defaultAddress)){
                        shippingQueryDto.setProvinceCode(Long.valueOf(defaultAddress.getProvinceCode()));
                        shippingQueryDto.setCityCode(Long.valueOf(defaultAddress.getCityCode()));
                        shippingQueryDto.setAreaCode(Long.valueOf(defaultAddress.getAreaCode()));
                    }
                    return shippingQueryDto;
                }).collect(Collectors.toList());
                log.info("查询pop店铺运费信息入参：{}", JSONObject.toJSONString(shippingQueryDtoList));
                ApiRPCResult<List<ShippingDto>> shippingInfoList = popEcShippinApi.getShippingInfoListByAreaCode(shippingQueryDtoList);
                log.info("查询pop店铺运费信息返回信息：{}", JSONObject.toJSONString(shippingInfoList));
                if (shippingInfoList != null && shippingInfoList.isSuccess()) {
                    if (CollectionUtils.isNotEmpty(shippingInfoList.getData())) {
                        shopCodeFreightInfoPopMap = shippingInfoList.getData().stream().collect(Collectors.toMap(ShippingDto::getOrgId, x -> {
                            if (x.getTemplateType() == 2) {
                                return "包邮";
                            } else {
                                return FreightFormatUtil.format(new BigDecimal(x.getLowBuyAmount()), new BigDecimal(x.getFreePostageAmount()));
                            }
                        }, (X1, X2) -> X2));
                    }
                }
                return shopCodeFreightInfoPopMap;
            } else {
                //自营店铺运费域集合
                Map<String, String> shopCodeFreightInfoSelfMap = new HashMap<>();
                List<String> branchCodeList = shopInfoDTOList.stream().map(ShopInfoDTO::getBranchCode).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(branchCodeList)) {
                    return shopCodeFreightInfoSelfMap;
                }
                //查询自营店铺运费信息
                FreightConditionDto freightConditionDto = new FreightConditionDto();
                freightConditionDto.setUserType(String.valueOf(businessType));
                freightConditionDto.setBranchRegionCode(registerCode);
                freightConditionDto.setBranchCodeList(branchCodeList);
                log.info("查询自营店铺运费信息入参：{}", JSONObject.toJSONString(freightConditionDto));
                ApiRPCResult<List<FreightTemplateDto>> listApiRPCResult = freightTemplateApi.queryFreightTemplateBaseInfo(freightConditionDto);
                log.info("查询自营店铺运费信息返回信息：{}", JSONObject.toJSONString(listApiRPCResult));
                if (listApiRPCResult != null && listApiRPCResult.isSuccess()) {
                    if (CollectionUtils.isNotEmpty(listApiRPCResult.getData())) {
                        shopCodeFreightInfoSelfMap = listApiRPCResult.getData().stream().collect(Collectors.toMap(FreightTemplateDto::getBranchCode, x -> {
                            return FreightFormatUtil.format(x.getMinimumAmount(), x.getFreeDeliveryAmount());
                        }, (X1, X2) -> X2));
                    }
                }
                return shopCodeFreightInfoSelfMap;
            }
        } catch (Exception e) {
            log.error("pagingShopList->查询运费信息异常", e);
        }
        return new HashMap<>();
    }


    /**
     * 获取店铺的拼团品列表。
     * 获取pageSize条数据，如果返回的结果大于pageSize则截取前pageSize条数据
     *
     * @param merchantId
     * @param shopCode
     * @return
     */
    public List<MarketingCsuResultDto> getShopGroupBuyingGoods(Long merchantId, String shopCode) {
        Integer pageNum = 1;
        Integer pageSize = 200;
        Page<MarketingCsuResultDto> marketingCsuResultDtoPage = marketingService.getMarketingShopIndex(pageNum, pageSize, merchantId,
                shopCode, CsuOrder.PT_DEFAULT, MarketingEnum.PING_TUAN);
        if (log.isDebugEnabled()) {
            log.debug("getShopGroupBuyingGoods，merchantId：{}，shopCode：{}，marketingCsuResultDtoPage：{}",
                    merchantId, shopCode, JSONObject.toJSONString(marketingCsuResultDtoPage));
        }
        if (Objects.isNull(marketingCsuResultDtoPage) || CollectionUtils.isEmpty(marketingCsuResultDtoPage.getRecordList())) {
            return Lists.newArrayList();
        }
        if (marketingCsuResultDtoPage.getRecordList().size() > pageSize) {
            marketingCsuResultDtoPage.setRecordList(marketingCsuResultDtoPage.getRecordList().subList(0, pageSize));
        }
        return marketingCsuResultDtoPage.getRecordList();
    }

    public Boolean filterProduct(ListProduct product){
        //只展示特惠中、销售中、售罄。
        Integer status = product.getStatus();
        if(status != ProductEnumDTO.SkuStatusEnum.ONSALE.getId() &&
                status != ProductEnumDTO.SkuStatusEnum.PRICEOFF.getId() &&
                status != ProductEnumDTO.SkuStatusEnum.SOLDOUT.getId()){
            return false;
        }
        //控销： /** 0:白名单1:可见不可买2:不可见*/
        Integer purchaseType = product.getPurchaseType();
        if(purchaseType != 2){
            return true;
        }
        return false;
    }

    public EcProductInfoVO getPtSkuListVo(ListProduct listProduct, String shopShowName, GroupBuyingInfoDto groupBuyingInfoDto) {
        EcProductInfoVO ptSkuVO = new EcProductInfoVO();
        BeanUtils.copyProperties(listProduct, ptSkuVO);
        ptSkuVO.setShopName(shopShowName);
        // 设置拼团信息
        GroupBuyingSkuDto skuDto = groupBuyingInfoDto.getGroupBuyingSkuDtoList().get(0);
        /** 拼团活动状态 1.未开始 ，2.拼团中，3.已结束 兼容老逻辑前端拼团活动状态 0-未开始 1-进行中 2-结束 **/
        Integer assembleStatus = groupBuyingInfoDto.getStatus() == null ? 0 : groupBuyingInfoDto.getStatus() - 1;
        //拼团
        EcPtActInfoVO ptActInfoVO = EcPtActInfoVO.builder()
                .marketingId(groupBuyingInfoDto.getMarketingId())
                .percentage(String.valueOf(groupBuyingInfoDto.getPercentage().multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_UP))))
                .assembleStatus(assembleStatus)
                .assembleStartTime(groupBuyingInfoDto.getStartTime())
                .assembleEndTime(groupBuyingInfoDto.getEndTime())
                .surplusTime((groupBuyingInfoDto.getEndTime().getTime() - System.currentTimeMillis()) / 1000)
                .orderNum(groupBuyingInfoDto.getOrderNum())
                .preheatShowPrice(groupBuyingInfoDto.getPreheatShowPrice())
                // 拼团多阶梯价信息
                .stepPriceStatus(groupBuyingInfoDto.getStepPriceStatus())
                .minSkuPrice(groupBuyingInfoDto.getMinSkuPrice())
                .maxSkuPrice(groupBuyingInfoDto.getMaxSkuPrice())
                .startingPriceShowText(groupBuyingInfoDto.getStartingPriceShowText())
                .rangePriceShowText(groupBuyingInfoDto.getRangePriceShowText())
                .stepPriceShowTexts(groupBuyingInfoDto.generateStepPriceShowTexts(ptSkuVO.getProductUnit()))
                .build();
        Optional.of(skuDto).ifPresent(groupBuyingSkuDto -> {
            ptActInfoVO.setSkuStartNum(groupBuyingSkuDto.getSkuStartNum());
            ptActInfoVO.setAssemblePrice(groupBuyingSkuDto.getSkuPrice());
        });
        //调整拼团商品的showName  前缀+起拼数+包邮+商品展示名称+“/”+规格
        StringBuilder sb = new StringBuilder();
        String skuNamePrefix = "";
        if (StringUtils.isNotEmpty(groupBuyingInfoDto.getTopicPrefix())){
            skuNamePrefix = groupBuyingInfoDto.getTopicPrefix();
        }
        if (ptActInfoVO.getSkuStartNum() != null) {
            String productUnit = Optional.ofNullable(ptSkuVO.getProductUnit()).orElse("");
            String productSpec = Optional.ofNullable(ptSkuVO.getSpec()).orElse("");
            sb.append(skuNamePrefix).append(ptActInfoVO.getSkuStartNum()).append(productUnit).append("包邮").append(" ")
                    .append(ptSkuVO.getShowName()).append("/").append(productSpec);
        } else {
            sb.append(skuNamePrefix).append(ptSkuVO.getShowName());
        }
        ptSkuVO.setShowName(sb.toString());
        ptSkuVO.setActPt(ptActInfoVO);
        return ptSkuVO;
    }

}
