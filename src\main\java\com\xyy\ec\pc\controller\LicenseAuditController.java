package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.*;
import com.xyy.ec.merchant.bussiness.api.crm.LicenseAuditBusinessCrmApi;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.merchant.bussiness.dto.InvoiceDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.ShippingAddressBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.licence.EnclosureParams;
import com.xyy.ec.merchant.bussiness.dto.licence.LicenseAuditImgParams;
import com.xyy.ec.merchant.bussiness.dto.licence.LicenseAuditParams;
import com.xyy.ec.merchant.bussiness.dto.licence.MerchantForLicense;
import com.xyy.ec.merchant.bussiness.enums.LicenseAuditStatusEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.rpc.CrmRpcService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2019/9/4 9:51
 * 单据上传,审核,修改相关功能
 */
@RequestMapping("/merchant/center/licenseAudit")
@Controller
public class LicenseAuditController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(LicenseAuditController.class);
    @Autowired
    private XyyConfig.CdnConfig cdnConfig;
    @Reference(version = "1.0.0")
    private LicenseAuditBusinessCrmApi licenseAuditBusinessCrmApi;

    @Reference(version = "1.0.0")
    private LicenseAuditImgBussinessApi licenseAuditImgBussinessApi;

    @Reference(version = "1.0.0")
    private LicenseCategoryBussinessApi licenseCategoryBussinessApi;

    @Reference(version = "1.0.0")
    private LicenseAuditBussinessApi licenseAuditBussinessApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private LicenseAuditLogBussinessApi licenseAuditLogBussinessApi;

    @Reference(version = "1.0.0")
    private MerchantCustomerTypeBusinessApi merchantCustomerTypeBusinessApi;

    @Reference(version = "1.0.0")
    private LicenseTemplateBusinessApi licenseTemplateBusinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private MerchantLicenseApi licenseApi;

    @Reference(version = "1.0.0")
    private ShippingAddressBussinessApi shippingAddressBussinessApi;

    @Value("${config.product_image_path_url}")
    private String PRODUCT_IMAGE_PATH_URL;

    @Value("${crm.webserviceUrl}")
    public String crmWebUrl;

    @Autowired
    private CrmRpcService crmRpcService;

    @Value("${invoiceStr}")
    public String invoiceStr;
    @Autowired
    private OssUtil ossUtil;

    /**
     * 上传资质图片
     *
     * @param request 请求request
     * @return
     */
    @ResponseBody
    @RequestMapping("/uploadImg")
    public Object uploadImg(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 创建一个通用的多部分解析器
            CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                    request.getSession().getServletContext());
            // 判断 request 是否有文件上传,即多部分请求
            if (multipartResolver.isMultipart(request)) {
                // 转换成多部分request
                MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
                // 取得request中的所有文件名
                Iterator<String> iter = multiRequest.getFileNames();
                while (iter.hasNext()) {
                    // 取得上传文件
                    List<MultipartFile> files = multiRequest.getFiles(iter.next());
                    if (!org.springframework.util.CollectionUtils.isEmpty(files)) {
                        for (MultipartFile file : files) {
                            if (file != null) {
                                // 取得当前上传文件的文件名称
                                String myFileName = file.getOriginalFilename();
                                //验证文件名称
                                if (checkFile(myFileName) == false) {
                                    logger.info("上传资质图片,文件名称不正确！myFileName is {}", myFileName);
                                    return JsonUtil.wapperObjToString(this.addError("上传文件格式不对!"));
                                }
                                //验证文件内容
                                if (FileTypeUtil.isImage(file.getInputStream()) == false) {
                                    logger.info("上传资质图片,文件内容不正确！");
                                    return JsonUtil.wapperObjToString(this.addError("上传文件格式不对!"));
                                }
                                // 如果名称不为“”,说明该文件存在，否则说明该文件不存在
                                if (!StringUtils.isEmpty(myFileName) && file.getSize() > (1024 * 1024 * 30)) {
                                    return JsonUtil.wapperObjToString(this.addError("上传图片不能超过30M"));
                                }
                            }
                        }
                    }
                }
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            //TODO 测试 后续还原
            if (Objects.isNull(merchant)){
                merchant=new MerchantBussinessDto();
                merchant.setId(1L);
            }
            //测试
            String uploadPath = null;
            if(merchant.getId() != null){
                uploadPath = "/ybm/license/" + merchant.getId() + "/";
            }else {
                uploadPath = "/ybm/license/account/" + merchant.getAccountId() + "/";
            }

//            Map<String, Object> resultMap = FileUploadUtil.fileUpload(request, uploadPath, cdnConfig, null, null);
//            List<String> fileNameList = (List<String>) resultMap.get("fileName");
//            List<String> newFileNameList = new ArrayList<>();
//            if (CollectionUtil.isEmpty(fileNameList)) {
//                return JsonUtil.wapperObjToString(this.addError("文件上传异常"));
//            }
//            for (String fileName : fileNameList) {
//                newFileNameList.add(uploadPath + fileName);
//            }
//            resultMap.put("fileName", newFileNameList);

            Map<String, Object> resultMap = upload2Oss(request, uploadPath);
            return JsonUtil.wapperObjToString(resultMap);
        } catch (Exception e) {
            logger.error("文件上传异常：", e);
            try {
                return JsonUtil.wapperObjToString(this.addError("文件上传异常"));
            } catch (JsonProcessingException ex) {
                logger.error("文件上传JSON转换异常：", ex);
                return "";
            }
        }
    }

    /**
     * 替换test环境的文件上传
     * @param request
     * @param dir
     * @return
     */
    private Map<String, Object> upload2Oss(HttpServletRequest request, String dir){
        Map<String,Object> retMap = new HashMap<>();
        List<String> nameList = new ArrayList<>();
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());
        if (multipartResolver.isMultipart(request)) {
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            Iterator<String> iter = multiRequest.getFileNames();
            while (iter.hasNext()) {
                String tempFileName = iter.next();
                List<MultipartFile> files = multiRequest.getFiles(tempFileName);
                for (MultipartFile file : files) {
                    String myFileName = file.getOriginalFilename();
                    String extensionName = myFileName.substring(myFileName.lastIndexOf(".") + 1);
                   String newFileName = UUID.randomUUID() + "." + extensionName;
                    try {
                        String s = ossUtil.uploadObjectByInputStream(file.getInputStream(), dir, newFileName);
                        nameList.add(s);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }

        retMap.put("status", "success");
        retMap.put("fileSize", "");
        retMap.put("fileName",nameList);
        return retMap;
    }

    /**
     * 新增资质审核保存
     *
     * @param licenseAudit 资质审核对象
     * @return
     */
    @ResponseBody
    @RequestMapping("/addLicenseAudit")
    public Object addLicenseAudit(@RequestBody LicenseAuditParams licenseAudit) {
        logger.info("新增资质审核记录,入参：licenseAudit:{}", JSONObject.toJSONString(licenseAudit));
        try {
            List<LicenseAuditImgParams> imgParams = licenseAudit.getCredentialList();
            for (LicenseAuditImgParams params : imgParams) {
                List<EnclosureParams> list = Lists.newArrayList();
                List<String> urls = Arrays.asList(params.getLicenseImgUrls().split(","));
                for (String s : urls) {
                    EnclosureParams e = new EnclosureParams();
                    e.setUrl(s);
                    list.add(e);
                }
                params.setEnclosureList(list);
            }
            licenseAudit.setCredentialList(imgParams);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            licenseAudit.setMerchantId(merchant.getId());
            ApiRPCResult rpcResult = licenseApi.addLicenseAudit(licenseAudit);
            logger.info("接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                logger.info("调用接口添加资质单据失败，药店ID：{}", licenseAudit.getMerchantId());
                return this.addError(rpcResult.getErrMsg());
            }
            return this.addResult();
        } catch (Exception e) {
            logger.error("调用接口添加资质单据失败:", e);
            return this.addError("系统错误，请稍后重试");
        }
    }

    /**
     * 资质更新保存
     *
     * @param licenseAudit 资质审核对象
     * @return
     */
    @RequestMapping(value = "/updateLicenseAudit")
    @ResponseBody
    public Object updateLicenseAudit(@RequestBody LicenseAuditParams licenseAudit) {
        logger.info("更新资质审核记录,入参：licenseAudit:{}", JSONObject.toJSONString(licenseAudit));
        try {
            List<LicenseAuditImgParams> imgParams = licenseAudit.getCredentialList();
            for (LicenseAuditImgParams params : imgParams) {
                List<EnclosureParams> list = Lists.newArrayList();
                List<String> urls = Arrays.asList(params.getLicenseImgUrls().split(","));
                for (String s : urls) {
                    EnclosureParams e = new EnclosureParams();
                    e.setUrl(s);
                    list.add(e);
                }
                params.setEnclosureList(list);
            }
            licenseAudit.setCredentialList(imgParams);

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            licenseAudit.setMerchantId(merchant.getId());
            ApiRPCResult rpcResult = licenseApi.updateLicenseAudit(licenseAudit);
            logger.info("接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                logger.info("修改资质单据失败，药店ID：{}", licenseAudit.getMerchantId());
                return this.addError(rpcResult.getErrMsg());
            }
            return this.addResult();
        } catch (Exception e) {
            logger.error("修改资质单据失败:", e);
            return this.addError("系统错误，请稍后重试");
        }
    }

    /**
     * 缓存首营资质添加的客户信息
     *
     * @param licenseAudit 资质审核对象
     * @return
     */
    @RequestMapping(value = "/cacheMerchantInfo")
    @ResponseBody
    public Object cacheMerchantInfo(@RequestBody LicenseAuditParams licenseAudit) {
        logger.info("更新资质审核记录,入参：licenseAudit:{}", JSONObject.toJSONString(licenseAudit));
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            licenseAudit.setMerchantId(merchant.getId());
            ApiRPCResult rpcResult = licenseApi.cacheMerchantInfo(licenseAudit);
            logger.info("接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                logger.info("修改资质单据失败，药店ID：{}", licenseAudit.getMerchantId());
                return this.addError(rpcResult.getErrMsg());
            }
            return this.addResult();
        } catch (Exception e) {
            logger.error("修改资质单据失败:", e);
            return this.addError("系统错误，请稍后重试");
        }
    }

    /**
     * 客户认证
     *
     * @param merchantForLicense
     * @return
     */
    @RequestMapping("/authentication")
    @ResponseBody
    public Object getMerchantInfoToAuthentication(MerchantForLicense merchantForLicense) {
        Map<String, Object> resultMap = Maps.newHashMap();
        if(Objects.isNull(merchantForLicense)){
            return this.addError("参数为空");
        }
        try {
            logger.info("用户认证,入参：merchantForLicense:{}", JSONObject.toJSONString(merchantForLicense));
            ApiRPCResult<MerchantForLicense> rpcResult = licenseApi.getMerchantInfoToAuthentication(merchantForLicense);
            logger.info("用户认证接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                return this.addError(rpcResult.getMsg(),rpcResult.getData());
            }
            resultMap.put("info", rpcResult.getData());
            return this.addResult("data", resultMap);
    }catch(Exception e){
        logger.error("用户认证失败:", e);
        return this.addError("用户认证失败");
    }

}

    /**
     * 基本信息展示
     *
     * @return
     */
    @RequestMapping(value = "/getMerchantBasicInfo")
    @ResponseBody
    public ModelAndView getMerchantBasicInfo() {
        Map<String, Object> resultMap = Maps.newHashMap();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ApiRPCResult rpcResult = licenseApi.getMerchantBasicInfo(merchant.getId());
            ShippingAddressBussinessDto shippingAddressBussinessDto = null;
            try {
                shippingAddressBussinessDto = shippingAddressBussinessApi.selectDeliveryAddress(merchant.getId());
            } catch (Exception e) {
                logger.error("获取地址信息失败,msg:{}",e.getMessage(), e);
            }
            logger.info("获取基本信息接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                logger.info("获取基本信息失败");
                return new ModelAndView("/basicInfo/index.ftl", resultMap);
            }
            resultMap.put("info", rpcResult.getData());
            resultMap.put("address", shippingAddressBussinessDto);
            resultMap.put("invoiceList", JSONObject.parseArray(invoiceStr, InvoiceDto.class));
            return new ModelAndView("/basicInfo/index.ftl", resultMap);
        } catch (Exception e) {
            logger.error("修改资质单据失败:", e);
            return new ModelAndView("/basicInfo/index.ftl", resultMap);
        }
    }

    /**
     * 客户信息用于资质添加时使用
     *
     * @return
     */
    @RequestMapping(value = "/licenseAddMerchantInfo")
    @ResponseBody
    public ModelAndView licenseAddMerchantInfo(int type) {
        logger.info("客户信息用于资质添加时使用,入参：type:{}", type);
        String viewName = type == 1 ? "/license/basicInfo.ftl" : "/license/basicInfoold.ftl";
        Map<String, Object> resultMap = Maps.newHashMap();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ApiRPCResult rpcResult = licenseApi.getMerchantBasicInfo(merchant.getId());
            MerchantForLicense license = JSONObject.parseObject(JSONObject.toJSONString(rpcResult.getData()),MerchantForLicense.class) ;
            if (license.getEdit() == 1) {
                // 资质审核单据列表
                ApiRPCResult<Map> map = licenseApi.queryLicenseAuditList(merchant.getId());

                List<LicenseAuditParams> licenseAuditList =  JSONObject.parseArray(JSONObject.toJSONString(map.getData().get("licenseAuditList")),LicenseAuditParams.class);

                if (CollectionUtils.isNotEmpty(licenseAuditList)) {

                    // 只取本机构下的资质单据
                    licenseAuditList = licenseAuditList.stream().filter(l -> Objects.equals(l.getEcOrgCode(), license.getRegisterCode())).collect(Collectors.toCollection(ArrayList::new));

                    logger.info("licenseAddMerchantInfo type: {}, licenseAuditList: {}", type, JSON.toJSONString(licenseAuditList));
                    //如果是首营且列表不为空
                    //if (type == 1 && CollectionUtils.isNotEmpty(licenseAuditList)) {
                    //    resultMap.put("msg", "您存在在途工单,不能再次添加！请联系您的专属销售");
                    //}
                    if (type == 1) {
                        licenseAuditList.stream()
                                .filter(param -> LicenseAuditStatusEnum.WAIT_AUDIT.getLicenseAuditStatus() == param.getAuditStatus())
                                .findAny()
                                .ifPresent(li -> resultMap.put("msg", "您存在在途工单,不能再次添加！请联系您的专属销售"));
                    }

                    //如果是资质变更且列表不为空且只有一条单据且单据没有四审通过
                    if (type == 2 && CollectionUtils.isNotEmpty(licenseAuditList) && licenseAuditList.size() == 1 && licenseAuditList.get(0).getAuditStatus() != 3) {
                        resultMap.put("msg", "您存在在途工单,不能再次添加！请联系您的专属销售");
                    }
                    List<LicenseAuditParams> list = licenseAuditList.stream().filter(s -> s.getAuditStatus() != 3).collect(Collectors.toList());

                    //如果是资质变更且列表不为空且有多条数据
                    if (type == 2 && CollectionUtils.isNotEmpty(list)) {
                        resultMap.put("msg", "您存在在途工单,不能再次添加！请联系您的专属销售");
                    }
                }
            }
            logger.info("获取基本信息接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                logger.info("获取基本信息失败");
                return new ModelAndView(viewName, resultMap);
            }
            resultMap.put("info", rpcResult.getData());
            if (type == 1) {
                resultMap.put("invoiceList", JSONObject.parseArray(invoiceStr, InvoiceDto.class));
            }
            logger.info("licenseAddMerchantInfo modelMap:{}", JSON.toJSONString(resultMap));
            return new ModelAndView(viewName, resultMap);
        } catch (Exception e) {
            logger.error("获取基本信息异常:", e);
            return new ModelAndView(viewName, resultMap);
        }
    }

    /**
     * 点击添加“添加首营资质/资质变更”按钮之前的效验
     *
     * @param type 1首营 2变更
     * @return
     */
    @RequestMapping(value = "/initLicenseAuditDetailVaild")
    @ResponseBody
    public Object initLicenseAuditDetailVaild(Integer type) {
        logger.info("新增资质审核记录页面跳转,入参：type:{}", type);
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
//            if(!merchant.getId().equals(merchantId)){
//                return this.addError("非法请求");
//            }
            ApiRPCResult<String> result = licenseApi.initLicenseAuditDetailVaild(merchant.getMerchantId(), type);
            if (result.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                logger.info("效验失败内容为：{}", result.getErrMsg());
                return this.addError(result.getErrMsg());
            }
            return this.addResult("校验成功");
        } catch (Exception e) {
            logger.error("新增资质审核记录效验异常：", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 点击添加“添加首营资质/资质变更”按钮
     *
     * @param customerType
     * @return
     */
    @RequestMapping(value = "/initLicenseAuditDetail")
    public Object initLicenseAuditDetail(Integer customerType, Boolean remark,Integer invoiceType) {
        logger.info("新增资质审核记录页面跳转,入参： customerType : {}", customerType);
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
//            if(!merchant.getId().equals(merchantId)){
//                return this.addError("非法请求");
//            }
            LicenseAuditParams params = LicenseAuditParams.builder().customerType(customerType).merchantId(merchant.getId()).isChangeLicense(remark).build();
            Map<String, Object> modelMap = Maps.newHashMap();
            ApiRPCResult<Map<String, Object>> result = licenseApi.initLicenseAuditDetail(params);
            if (result.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                logger.info("点击添加“添加首营资质/资质变更”按钮效验失败内容为：{}", result.getErrMsg());
                modelMap.put("msg", result.getErrMsg());
                return new ModelAndView("/license/licenseEdit.ftl", modelMap);
            }
            modelMap = result.getData();
            modelMap.put("imageUrl", PRODUCT_IMAGE_PATH_URL);
            if(modelMap.get("licenseAudit") == null){
                ApiRPCResult<LicenseAuditParams> info = licenseApi.getCacheMerchantInfo(merchant.getId());
                info.getData().setInvoiceType(invoiceType);
                modelMap.put("licenseAudit",info.getData());
            }
            LicenseAuditParams licenseAuditParams = (LicenseAuditParams) modelMap.get("licenseAudit");
            licenseAuditParams.setInvoiceType(invoiceType);
            modelMap.put("licenseAudit",licenseAuditParams);
            logger.info("initLicenseAuditDetail modelMap:{}", JSON.toJSONString(modelMap));
            return new ModelAndView("/license/licenseEdit.ftl", modelMap);
        } catch (Exception e) {
            logger.error("新增资质审核记录页面跳转异常：", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 点击修改资质按钮之前的效验
     *
     * @param type
     * @return
     */
    @RequestMapping(value = "/initBillDetailVaild")
    @ResponseBody
    public Object initBillDetailVaild(int type, @RequestParam("orgCode") String orgCode) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
//            if(!merchant.getId().equals(merchantId)){
//                return this.addError("非法请求");
//            }
            ApiRPCResult<String> result = licenseApi.initBillDetailVaild(merchant.getId(), type, orgCode);
            if (result.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                return this.addError(result.getErrMsg());
            }
            return this.addResult("校验成功");
        } catch (Exception e) {
            logger.error("编辑资质审核记录效验异常：", e);
            return this.addError("系统异常");
        }
    }

    /**
     * 点击修改资质按钮
     *
     * @param type
     * @param customerType
     * @return
     */
    @RequestMapping(value = "/initBillDetail")
    public Object initBillDetail(int type, int customerType, @RequestParam("orgCode") String orgCode) {
        try {
            logger.info("编辑资质审核记录页面跳转,入参：type:{},customerType:{}", type, customerType);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
//            if(!merchant.getId().equals(merchantId)){
//                return this.addError("非法请求");
//            }
            LicenseAuditParams params = LicenseAuditParams.builder().customerType(customerType).merchantId(merchant.getId()).type(type).ecOrgCode(orgCode).build();
            ApiRPCResult<Map<String, Object>> result = licenseApi.initBillDetail(params);
            if (result.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                return new ModelAndView("/license/licenseEdit.ftl", result.getData());
            }
            Map<String, Object> modelMap = result.getData();
            modelMap.put("imageUrl", PRODUCT_IMAGE_PATH_URL);
            logger.info("initBillDetail modelMap:{}", JSON.toJSONString(modelMap));
            return new ModelAndView("/license/licenseEdit.ftl", modelMap);
        } catch (Exception e) {
            logger.error("编辑资质审核记录页面跳转异常：", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    @RequestMapping(value = "/v2/queryLicenseAuditList")
    public Object queryLicenseAuditListV2() {
        return queryLicenseAuditList();
    }

    /**
     * 资质审核列表
     * //     * @param merchantId
     *
     * @return
     */
    @RequestMapping(value = "/queryLicenseAuditList")
    public Object queryLicenseAuditList() {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
//            if(!merchant.getId().equals(merchantId)){
//                return this.addError("非法请求");
//            }
            Map<String, Object> resultMap = new HashMap<>();
            ApiRPCResult<Map> mapApiRPCResult = licenseApi.queryLicenseAuditList(merchant.getId());
            if (mapApiRPCResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                return this.addError(mapApiRPCResult.getMsg());
            }
//            resultMap.put("requestUrl",getRequestUrl(request));
//            resultMap.put("retPage", mapApiRPCResult.getData().get("licenseAuditList"));
            resultMap.put("data", mapApiRPCResult.getData());
            resultMap.put("status", "success");
            logger.info("resultMap:{}", JSON.toJSONString(resultMap));
            return new ModelAndView("/license/licenseList.ftl", resultMap);
        } catch (Exception e) {
            logger.error("资质单据查询异常", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 单据详情
     *
     * @param type
     * @param applicationNumber
     * @return
     */
    @RequestMapping(value = "/queryLicenseAudit")
    public Object queryLicenseAudit(int type, @RequestParam("applicationNumber") String applicationNumber) {
        try {
            ApiRPCResult<Map<String, Object>> result = licenseApi.queryLicenseAuditDetail(applicationNumber, type);
            if (result.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                return new ModelAndView("/license/licenseDetail.ftl");
            }
            Map<String, Object> modelMap = result.getData();
            modelMap.put("imageUrl", PRODUCT_IMAGE_PATH_URL);
            logger.info("queryLicenseAudit modelMap:{}", JSON.toJSONString(modelMap));
            return new ModelAndView("/license/licenseDetail.ftl", modelMap);
        } catch (Exception e) {
            logger.error("单据详情查看异常", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 审核日志
     *
     * @param type
     * @param applicationNumber
     * @return
     */
    @RequestMapping(value = "/queryLicenseAuditLogList")
    @ResponseBody
    public Object queryLicenseAuditLogList(@RequestParam("type") int type, @RequestParam("applicationNumber") String applicationNumber) {
        try {
            ApiRPCResult<Map<String, Object>> result = licenseApi.queryLicenseAuditLogList(applicationNumber, type);
            logger.info("queryLicenseAuditLogList modelMap:{}", JSON.toJSONString(result.getData()));
            return this.addResult("data", result.getData());
        } catch (Exception e) {
            logger.error("资质审核日志详情查询失败", e);
            return this.addError("系统异常");
        }
    }


    /**
     * 资质列表
     *
     * @return
     */
    @RequestMapping(value = "/findLicenseCategoryInfo.htm", method = RequestMethod.GET)
    public ModelAndView findLicenseCategoryInfo() {
        try {
            MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            ApiRPCResult<Map<String, Object>> result = licenseApi.findLicenseList(merchant.getId());
            String bdPhoneByMerchantId = crmRpcService.getBdPhoneByMerchantId(merchant.getId());

            logger.info("资质列表信息接口返回值:{}", JSONObject.toJSONString(result));
            Map<String, Object> modelMap = result.getData();
            modelMap.put("phone", bdPhoneByMerchantId);

            return new ModelAndView("/license/license.ftl", modelMap);
        } catch (Exception e) {
            logger.error("查询资质列表异常", ExceptionUtils.getStackTrace(e));
        }
        return new ModelAndView("/license/license.ftl");
    }

    public Boolean isShow(Long id, String registerCode, Integer licenseStatus) throws Exception {
        Boolean boo = false;
        //判断用户是否在开放的区域
        String newbranchCodes = licenseAuditBussinessApi.getNewLicenseBranchCode();
        if (!newbranchCodes.contains(registerCode) && !newbranchCodes.contains(BranchEnum.ALL_COUNTRY.getKey())) {
            return boo;
        }
        //判断用户是否已过一审
        boo = licenseAuditBussinessApi.getAuditStatusByMerchantId(id);
        if (boo != null && boo) {
            return boo;
        }
        //根据用户信息，判断用户是不是未提交，首营待审核
        if (licenseStatus != 1 && licenseStatus != 5) {
            boo = true;
        }
        return boo;
    }


    /**
     * 判断是否为允许的上传文件类型,true表示允许
     */
    private boolean checkFile(String fileName) {
        if (null == fileName) {
            return false;
        }
        //设置允许上传文件类型
        String suffixList = "jpg,png,ico,bmp,jpeg";
        // 获取文件后缀
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
        if (suffixList.contains(suffix.trim().toLowerCase())) {
            return true;
        }
        return false;
    }
}
