package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.order.core.util.CollectionUtil;
import com.xyy.ec.order.core.util.StringUtil;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.constants.CodeMapConstants;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.dto.HeaderRespVO;
import com.xyy.ec.pc.newfront.dto.VerifiedRespVO;
import com.xyy.ec.pc.newfront.service.IndexNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.product.business.module.CategoryVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@CustomizeCmsResponse
@RestController
@RequestMapping("/new-front/index")
public class IndexNewController extends BaseController {

    private final IndexNewService indexNewService;
    private final CodeItemServiceRpc codeItemServiceRpc;

    //自营客服地址
    @Value("${self-run-im-pack-url:}")
    private String selfRunImPackUrl;


    @GetMapping("/im-pack-url")
    public AjaxResult<String> getIMPackUrl(@RequestParam(value = "isThirdCompany", required = false) Integer isThirdCompany) {
        try {
            Map<String, String> codeItemMap = codeItemServiceRpc.findCodeMap(CodeMapConstants.IM_PACK_URL, "");
            if (StringUtil.isNotEmpty(codeItemMap.get("IM_PACK_URL_CLOSE")) && codeItemMap.get("IM_PACK_URL_CLOSE").equals("0")) {
                return AjaxResult.errResult(codeItemMap.get("IM_PACK_URL_CLOSE_MSG"));
            }
            String imPackUrl = null;
            if (CollectionUtil.isNotEmpty(codeItemMap)) {
                if (isThirdCompany != null && isThirdCompany == 1) {
                    imPackUrl = codeItemMap.get("IM_POP_PACK_URL");
                } else {
                    imPackUrl = codeItemMap.get("IM_PACK_URL");
                }
            }

            if (isThirdCompany == null || isThirdCompany != 1) {
                if(StringUtils.isNotBlank(selfRunImPackUrl)){
                    imPackUrl = selfRunImPackUrl;
                }
            }

            return AjaxResult.successResult(imPackUrl);
        } catch (Exception e) {
            log.error("获取易客通配置的H5聊天页面URL异常", e);
            return AjaxResult.errResult("获取H5聊天页面URL异常");
        }
    }

    /**
     * 分类树
     */
    @GetMapping(value = "/category-tree" )
    public AjaxResult<List<CategoryVo>> categoryTree( HttpServletRequest request) throws Exception {

            return  indexNewService.categoryTree(request);

    }

    /**
     * 得到头部数据
     */
    @GetMapping("/header-data")
    public AjaxResult<HeaderRespVO> header_data(HttpServletRequest request) {
        try {
            HeaderRespVO headerData = indexNewService.getHeaderData(request);
            return AjaxResult.successResult(headerData);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

}
