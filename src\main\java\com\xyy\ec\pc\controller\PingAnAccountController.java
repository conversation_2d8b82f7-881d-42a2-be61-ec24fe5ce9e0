package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.utils.MobileValidateUtil;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.merchant.server.dto.MerchatForAppDto;
import com.xyy.ec.order.api.pay.PingAnAccountApi;
import com.xyy.ec.order.dto.pay.*;
import com.xyy.ec.order.enums.ApplyBankcardStatusEnum;
import com.xyy.ec.order.enums.OrderApiResultCodeEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.constants.RedisConstants;
import com.xyy.ec.pc.param.PingAnReqParam;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.DateUtil;
import com.xyy.ec.pc.util.HttpState;
import com.xyy.ec.pc.util.RandomUtil;
import com.xyy.ec.pc.util.excel.ExcelExport;
import com.xyy.ec.pc.util.excel.ExportExcelEntity;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ec.pc.vo.KeyValueViwVO;
import com.xyy.saas.payment.cores.api.FbankPayRecordsApi;
import com.xyy.saas.payment.cores.enums.FbankTradeCodeEnum;
import com.xyy.saas.payment.cores.enums.FbankTradeCodeEnumForShow;
import com.xyy.saas.payment.cores.enums.ResultCodeEnum;
import com.xyy.saas.payment.cores.param.FbankPayRecordsParam;
import com.xyy.saas.payment.cores.vo.FbankPayRecordsVO;
import com.xyy.saas.payment.cores.vo.ResultPageVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Controller
@RequestMapping("/app/pinganaccount")
public class PingAnAccountController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PingAnAccountController.class);

    @Reference(version = "1.0.0")
    private PingAnAccountApi pingAnAccountApi;
    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private FbankPayRecordsApi fbankPayRecordsApi;
    @RequestMapping("/queryPingAnCreditBalance")
    @ResponseBody
    public Object queryPingAnCreditBalance(@RequestParam(value = "merchantId") Long merchantId) {
        try {
            LOGGER.info("查询平安贷额度请求参数:{}", merchantId);
            if (null == merchantId) {
                LOGGER.error("查询平安贷额度请求参数参数:{}", merchantId);
                return this.addResult("data", null);
            }
            final ApiRPCResult<PingAnFianceAccountDto> result = pingAnAccountApi.queryPingAnCreditBalance(merchantId);
            if (result.isFail()) {
                LOGGER.error("查询平安贷额度失败:{}", JSON.toJSONString(result));
                return this.addResult("data", null);
            }
            return this.addResult("data", result.getData());
        } catch (Exception e) {
            LOGGER.error("queryPingAnCreditBalance:merchantId:{}", merchantId, e);
            return this.addResult("data", null);
        }
    }

    /**
     * 查询公司信息
     * @param merchantId
     * @return
     */
    @RequestMapping("/queryCompanyInfo")
    @ResponseBody
    public Object queryCompanyInfo(Long merchantId) {
        try {
            final MerchantBussinessDto merchant = merchantBussinessApi.findMerchantById(merchantId);
            Map map = new HashMap<>();
            map.put("corporateAccountName",merchant.getRealName());
            map.put("isKa",merchant.getIsKa()?1:0);
            LOGGER.info("queryCompanyInfo:merchantId:{},result:{}",merchantId,map);
            return this.addResult("data",map);
        } catch (Exception e) {
            LOGGER.error("queryCompanyInfo:merchantId:{}", merchantId, e);
        }

        return this.getErrorResult("查询失败");
    }

    /**
     * 创建平安账户
     * @param pingAnAccountDto
     * @return
     */
    @RequestMapping("/createPingAnAccount")
    @ResponseBody
    public Object createPingAnAccount(HttpServletRequest request, @RequestBody PingAnAccountDto pingAnAccountDto){
        LOGGER.info("createPingAnAccount param：{}",JSONObject.toJSONString(pingAnAccountDto));

        String realIP = IPUtils.getClientIP(request);
        if (StringUtils.isEmpty(realIP)) {
            pingAnAccountDto.setOperatorIp("***********");
        } else {
            pingAnAccountDto.setOperatorIp(realIP);
        }
        if (pingAnAccountDto.getStoreQty() == null) {
            pingAnAccountDto.setStoreQty(1);
        }
        final ApiRPCResult result = pingAnAccountApi.createPingAnAccount(pingAnAccountDto);
        LOGGER.info("createPingAnAccount result：{}", JSONObject.toJSONString(result));
        if (result.isFail()) {
            return this.getErrorResult(result.getMsg());
        }
        return this.addResult("提交成功");
    }

    /**
     * 查询开户信息
     * @param merchantId
     * @return
     */
    @RequestMapping("/queryPingAnAccountInfo")
    @ResponseBody
    public Object queryPingAnAccountInfo(Long merchantId){
        try {
            ApiRPCResult<PingAnAccountDto> apiRPCResult = pingAnAccountApi.queryPingAnAccountInfo(merchantId);
            final MerchantBussinessDto merchant = merchantBussinessApi.findMerchantById(merchantId);
            PingAnAccountDto data = apiRPCResult.getData();
            data.setIsKa(merchant.getIsKa()?1:0);
            if (apiRPCResult.isSuccess()){
                return this.addResult("data",apiRPCResult.getData());
            }
        } catch (Exception e) {
            LOGGER.error("queryPingAnAccountInfo:{}",merchantId,e);
        }
        return this.getErrorResult("查询失败");
    }

    @RequestMapping("/boundCard")
    @ResponseBody
    public Object boundCard(@RequestBody PingAnAccountDto pingAnAccountDto){
        LOGGER.info("boundCard param：{}",JSONObject.toJSONString(pingAnAccountDto));
        ApiRPCResult result = pingAnAccountApi.boundCard(pingAnAccountDto);
        LOGGER.info("boundCard result：{}",JSONObject.toJSONString(result));
        if (result.isFail()) {
            return getErrorResult(result.getCode(), result.getMsg());
        }
        return this.addResult("操作成功");
    }

    @RequestMapping("/unBoundCard")
    @ResponseBody
    public Object unBoundCard(@RequestBody PingAnAccountDto pingAnAccountDto){
        LOGGER.info("unBoundCard param：{}",JSONObject.toJSONString(pingAnAccountDto));
        ApiRPCResult result = pingAnAccountApi.unBoundCard(pingAnAccountDto);
        LOGGER.info("unBoundCard result：{}",JSONObject.toJSONString(result));
        if (result.isFail()) {
            return getErrorResult(result.getCode(), result.getMsg());
        }
        return this.addResult("操作成功");
    }

    /**
     * 打款验证
     * @param pingAnAccountDto
     * @return
     */
    @RequestMapping("/paymentAuth")
    @ResponseBody
    public Object paymentAuth(@RequestBody PingAnAccountDto pingAnAccountDto) {
        ApiRPCResult<Boolean> result = pingAnAccountApi.paymentAuth(pingAnAccountDto);
        if (result.getCode() == OrderApiResultCodeEnum.BANK_CARD_AUDIT_FAIL.getCode()) {
            return this.getErrorResult(1002, "银行卡验证失效，请重新提交开户申请");
        } else if (result.getCode() == OrderApiResultCodeEnum.BANK_CARD_AUTH_FAIL.getCode()) {
            return this.getErrorResult(1001, result.getMsg());
        }
        return this.addResult("验证成功");
    }
    /**
     * 查询账户余额
     * @param merchantId
     * @return
     */
    @RequestMapping("/queryPingAnAccountBalance")
    @ResponseBody
    public Object queryPingAnAccountBalance(Long merchantId) {
        ApiRPCResult<UserPingAnBlanceDto> apiRPCResult = pingAnAccountApi.queryPingAnAccountBalance(merchantId);
        if (apiRPCResult.isSuccess()) {
            return this.addResult("data", apiRPCResult.getData());
        }
        return this.getErrorResult("查询失败");
    }

    /**
     * 查询银行卡充值信息
     * @param merchantId
     * @return
     */
    @RequestMapping("/queryTopUpBankCardInfo")
    @ResponseBody
    public Object queryTopUpBankCardInfo(Long merchantId) {
        ApiRPCResult<TopUpBankCardInfoDto> apiRPCResult = pingAnAccountApi.queryTopUpBankCardInfo(merchantId);
        if (apiRPCResult.isSuccess()) {
            return this.addResult("data", apiRPCResult.getData());
        }
        return this.getErrorResult("查询失败");
    }

    /**
     * 查询平安开户银行列表
     * @param bankName
     * @return
     */
    @RequestMapping("/queryBankList")
    @ResponseBody
    public Object queryBankList(String bankName) {
        ApiRPCResult<List<BankInfoDto>> apiRPCResult = pingAnAccountApi.queryBankList(bankName);
        if (apiRPCResult.isSuccess()) {
            return this.addResult("data", apiRPCResult.getData());
        }
        return this.getErrorResult("查询失败");
    }

    /**
     * 查询平安开户银行支行列表
     * @param bankName
     * @param subBankName
     * @return
     */
    @RequestMapping("/querySubBankList")
    @ResponseBody
    public Object querySubBankList(String bankName, String subBankName) {
        ApiRPCResult<List<BankInfoDto>> apiRPCResult = pingAnAccountApi.querySubBankList(bankName, subBankName);
        if (apiRPCResult.isSuccess()) {
            return this.addResult("data", apiRPCResult.getData());
        }
        return this.getErrorResult("查询失败");
    }
    /**
     * 平安数字贷预申请
     * @param merchantId
     * @return
     */
    @RequestMapping("/preApply")
    @ResponseBody
    public Object pingAnCreditPreApply(Long merchantId) {
        ApiRPCResult apiRPCResult = pingAnAccountApi.pingAnCreditPreApply(merchantId);
        if (apiRPCResult.isSuccess()) {
            return this.addResult("data", apiRPCResult.getData());
        }
        return this.getErrorResult("查询失败");
    }

    /**
     * 发送验证码
     * @param mobileNumber
     * @return
     */
    @RequestMapping("/sendVerificationCode")
    @ResponseBody
    public Object sendVerificationCode(String mobileNumber, Long merchantId) {
        //手机号格式校验
        if (StringUtils.isEmpty(mobileNumber)
                || !MobileValidateUtil.isPass(mobileNumber)
                || mobileNumber.length() != 11) {
            return this.addError("手机号格式有误，请确认后重新填写！");
        }
        String verificationCode = RandomUtil.getRandomCode(6);
        try {
            ApiRPCResult apiRPCResult = pingAnAccountApi.sendSms(merchantId, mobileNumber, verificationCode);
            if (apiRPCResult.isSuccess()) {
                return this.addResult("data", apiRPCResult.getData());
            }
            return this.getErrorResult(apiRPCResult.getMsg());

//            ResultMessage<?> resultMessage = merchantBussinessApi.sendVerificationCode(mobileNumber,1);
//            int resultCode = resultMessage.getCode();
//            String msg = resultMessage.getMsg();
//            if(resultCode!= ResultCodeEnum.SUCCESS.getCode()){
//                return this.addError(msg);
//            }
        } catch (Exception e) {
            LOGGER.error("{}短信验证码发送失败", mobileNumber, e);
            return this.getErrorResult("短信验证码发送失败");
        }
    }


    /**
     * 我的平安主页
     * @return
     */
    @RequestMapping("/pingan/index")
    @ResponseBody
    public Object toPingAn() {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            String pinganUrl = queryPingAnUrl(merchant.getId());
            modelMap.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            modelMap.put("center_menu", "pingAnMerchant");
            //湖南宇轩科技 --测试账号
            if(merchant.getMerchantId().equals(1111143047L)){
                modelMap.put("pingAnUrl",  "my?merchantId=" + merchant.getMerchantId());
                LOGGER.info("pingAnUrl:{}",pinganUrl);
                return new ModelAndView("/order/pingan.ftl", modelMap);
            }
            if(org.apache.commons.lang3.StringUtils.isEmpty(pinganUrl)){
                return new ModelAndView("/error/500.ftl");
            }
            modelMap.put("pingAnUrl", pinganUrl);
            LOGGER.info("pingAnUrl:{}",pinganUrl);
            modelMap.put("pingAnUrl", queryPingAnUrl(merchant.getId()));

            LOGGER.info("modelMap:{}", JSONObject.toJSONString(modelMap));
            return new ModelAndView("/order/pingan.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("toPingAn error ", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 我的平安贷主页
     *
     * @return
     */
    @RequestMapping("/pinganLoan/index")
    @ResponseBody
    public Object toPingAnLoan() {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            modelMap.put("pingAnUrl", "?merchantId=" + merchant.getId());
            return new ModelAndView("/order/pinganLoan.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("toPingAn error ", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    private String queryPingAnUrl(Long merchantId) {
        try {
            ApiRPCResult<PingAnAccountDto> apiRPCResult = pingAnAccountApi.queryPingAnAccountInfo(merchantId);
            LOGGER.info("queryPingAnUrl:{}",JSONObject.toJSONString(apiRPCResult));
            if (apiRPCResult.isSuccess()){
                final PingAnAccountDto data = apiRPCResult.getData();
                // 已完成
                if (data.getStatus() == ApplyBankcardStatusEnum.COMPLETE.getId()) {
                    return  "my?merchantId=" + merchantId;
                }
                // 未开户
                else if (data.getStatus() == ApplyBankcardStatusEnum.NON_CREATE.getId()) {
                    return  "introduce?merchantId=" + merchantId;
                }
                // 其他
                else {
                    // 绑卡成功过返回my，否则返回开户
                    if (null != data.getBinded() && data.getBinded() > 0) {
                        return  "my?merchantId=" + merchantId;
                    }
                    return  "open?merchantId=" + merchantId;
                }
            }
        } catch (Exception e) {
            LOGGER.error("queryPingAnUrl error", e);
        }
        return null;
    }

    @RequestMapping(value = "/queryPayRecordsForPc", method = RequestMethod.POST)
    @ResponseBody
    public Object queryPayRecordsForPc(@RequestBody PingAnReqParam req) {
        Page resultPage = new Page();
        try {
            LOGGER.info("queryPayRecordsForPc:{}", req);
            FbankPayRecordsParam param = new FbankPayRecordsParam();
            param.setAccountId(req.getAccountId());
            param.setMerchantId(req.getMerchantId());
            param.setPageType(req.getPageType());
            if(req.getPayStartTime()==null){
                param.setPayStartTime(DateUtil.getAddDate(new Date(),-14)) ;
                param.setPayEndTime(new Date());
            }else{
                param.setPayStartTime(req.getPayStartTime());
                param.setPayEndTime(req.getPayEndTime());
            }
            param.setPage(req.getPage());
            param.setTradeCode(req.getTradeCode());
            param.setPageSize(req.getPageSize());
            ResultPageVo<List<FbankPayRecordsVO>> result = fbankPayRecordsApi.pageAllByParam(param);

            if(result.getCode()==0){
                resultPage.setOffset((param.getPage() - 1)*result.getPageSize());
                resultPage.setRows(result.getData());
                resultPage.setTotal(result.getTotalCount());
                resultPage.setPageCount(result.getTotalPage());
                return this.addResult("data", resultPage);
            }else{
                return this.addError("data", result.getMsg());
            }
        } catch (Exception e) {
            LOGGER.error("queryPayRecordsForPc--出错", e);
            return this.addResult("data", null);
        }
    }


    @RequestMapping(value="/queryFbankTradeCode", method = RequestMethod.POST)
    @ResponseBody
    public Object queryFbankTradeCodeEnum() {
        try {
            Map<String, Integer>  enumMap= FbankTradeCodeEnumForShow.maps;
            List<KeyValueViwVO> enumList = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : enumMap.entrySet()) {
                String  key = entry.getKey();
                Integer value = entry.getValue();
                KeyValueViwVO vo = new KeyValueViwVO();
                vo.setName(key);
                vo.setValue(value);
                enumList.add(vo);
            }
            return this.addResult("data", enumList);
        } catch (Exception e) {
            LOGGER.error("queryFbankTradeCodeEnum--出错", e);
            return this.addResult("data", null);
        }
    }



    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     *  平安账户支付明细EXCEL导出
     * @param req
     * @param response
     */
    @PostMapping("/exportExcel")
    public void exportExcel(@RequestBody PingAnReqParam req, HttpServletResponse response) {
        try {
            LOGGER.info("我的平安账户支付明细导出数据,请求参数:{}", JSONObject.toJSONString(req));
            Integer page=1;
            Integer pageSize=500;
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            String lockKey = redisTemplate.opsForValue().get(RedisConstants.PINGAN_ACCOUNT_EXPORT_PRIFIX+merchant.getId());
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(lockKey)) {
                response.setStatus(HttpState.SYSTEM_BUSY);
                response.getWriter().write("请过一分钟之后尝试");
                return;
            }
            redisTemplate.opsForValue().set(RedisConstants.PINGAN_ACCOUNT_EXPORT_PRIFIX+merchant.getId(),"1",60, TimeUnit.SECONDS);
            List<FbankPayRecordsVO> resultVO = new ArrayList<>();
            FbankPayRecordsParam param = new FbankPayRecordsParam();
            param.setAccountId(req.getAccountId());
            param.setMerchantId(req.getMerchantId());
            param.setPageType(req.getPageType());
            param.setPayStartTime(req.getPayStartTime());
            param.setPayEndTime(req.getPayEndTime());
            param.setPage(page);
            param.setPageSize(pageSize);
            ResultPageVo<List<FbankPayRecordsVO>> result = fbankPayRecordsApi.pageAllByParam(param);
            if (result == null || result.getTotalCount() == 0) {
                LOGGER.error("导出我的平安账户支付明细数据异常:数据为空！");
                response.setStatus(HttpState.PARAM_ERROR);
                response.getWriter().write(ResultCodeEnum.PARAM_ERROR.getDisplayMsg());
                return;
            }
            resultVO.addAll(result.getData());
            while (result.getPage() < result.getTotalPage()) {
                param.setPage(++page);
                result = fbankPayRecordsApi.pageAllByParam(param);
                if (result == null || CollectionUtil.isEmpty(result.getData())) {
                    break;
                }
                resultVO.addAll(result.getData());
            }
            // 写入excel并导出
            List<ExportExcelEntity> sheets = new ArrayList<>();
            String  month =LocalDate.now().getYear()+"年"+ LocalDate.now().getMonthValue()+"月";
            if(req.getPayStartTime()!=null){
                LocalDate payStartDate = req.getPayStartTime().toInstant().atZone(TimeZone.getDefault().toZoneId()).toLocalDate();
                month = payStartDate.getYear()+"年"+ payStartDate.getMonthValue()+"月";;

            }
            // 写入明细
            sheets.add(
                    ExportExcelEntity.builder()
                            .sheetName("银行支付明细" )
                            .columnWidth(7000)
                            .flag(false)
                            .fieldNames(new String[]{"payTimeStr", "businessOrderNo", "tradeType", "tradeAmount"})
                            .headers(new String[]{"时间", "订单编号", "交易类型", "变动资金"})
                            .dataList(resultVO)
                            .build()
            );
            ExcelExport.exportSheets(response, "银行支付明细"+month+".xlsx", sheets);
        } catch (Exception e) {
            LOGGER.error("导出平安银行支付明细异常:", e);
            response.setStatus(HttpState.DEFAULT_ERROR);
            try {
                response.getWriter().write(ResultCodeEnum.DEFAULT_ERROR.getDisplayMsg());
            } catch (IOException ex) {
                LOGGER.error("导出平安银行支付明细异常:", ex);
            }
        }
    }


}
