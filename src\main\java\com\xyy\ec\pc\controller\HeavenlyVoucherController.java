package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Maps;
import com.xyy.ec.layout.buinese.api.HeavenlyVoucherBuineseApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ms.promotion.business.api.pc.VoucherForPcBusinessApi;
import com.xyy.ms.promotion.business.common.ErrorCodeEum;
import com.xyy.ms.promotion.business.common.ResultDTO;
import com.xyy.ms.promotion.business.dto.voucher.ReceiveVoucherRequestDTO;
import com.xyy.ms.promotion.business.dto.voucher.VoucherExtendDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.Map;

/**
 * @Description: TODO
 * @Date: 2019/8/30 16:36
 * @Author: wb
 * @Version: 1.0
 */
@Controller
@RequestMapping("/heavenlyVoucher")
public class HeavenlyVoucherController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(HeavenlyVoucherController.class);

    @Reference(version = "1.0.0")
    private HeavenlyVoucherBuineseApi heavenlyVoucherBuineseApi;

    @Reference(version = "1.0.0")
    private VoucherForPcBusinessApi promotionVoucherApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @RequestMapping("/index.htm")
    public ModelAndView heavenlyIndex() {
        ModelMap modelMap = new ModelMap();
        try {
            //消费金额
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant == null) {
                merchantId = 0L;
            } else {
                merchantId = merchant.getId();
            }
            //优惠券集合
            Map<String, Object> center = heavenlyVoucherBuineseApi.getHeavenVoucherCenterByMerchantId(merchantId);
            modelMap.put("voucherCenterList", center.get("voucherCenterList"));
            modelMap.put("totalAcount", center.get("totalAcount"));
            modelMap.put("num4HadGet", center.get("num4HadGet"));
            modelMap.put("now", center.get("now"));
            modelMap.put("startTime", center.get("startTime"));
            modelMap.put("endTime", center.get("endTime"));
            if (merchantId != 0L) {
                modelMap.put("merchantId", merchantId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ModelAndView("/heavenlyVoucher/index.ftl", modelMap);
    }

    @RequestMapping("/receiveVoucher")
    @ResponseBody
    public Object receiveVoucher(@RequestParam(value = "voucherTemplateId", required = false) Long voucherTemplateId) {
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }

            Boolean aBoolean = heavenlyVoucherBuineseApi.hasMinMoneyToReceive(merchant.getId(), voucherTemplateId);
            if (!aBoolean) {
                return this.addError("优惠券领取失败");
            }
            ReceiveVoucherRequestDTO voucherRequestDTO = new ReceiveVoucherRequestDTO();
            voucherRequestDTO.setMerchantId(merchant.getId());
            voucherRequestDTO.setVoucherTemplateId(voucherTemplateId);
            ResultDTO<VoucherExtendDTO> resultDTO = promotionVoucherApi.receiveVoucher(voucherRequestDTO);
            VoucherExtendDTO voucherExtendDTO = resultDTO.getData();
            if(null == resultDTO){
                return this.addError("领取失败");
            }
            if(ErrorCodeEum.SUCCESS.getErrorCode() != resultDTO.getErrorCode()){
                return this.addError(resultDTO.getErrorCode(), resultDTO.getErrorMsg());
            }
            Map<String, Object> resultMap = this.addResult("优惠券领取成功");
            resultMap.put("validDate", voucherExtendDTO.getValidDate());
            resultMap.put("expireDate", voucherExtendDTO.getExpireDate());
            resultMap.put("voucherType", voucherExtendDTO.getVoucherType());
            resultMap.put("voucherTemplateId", voucherTemplateId);
            resultMap.put("jumpUrl", voucherExtendDTO.getPcUrl());
            resultMap.put("noReceiveCount", 0);
            return resultMap;
        }  catch (Exception e) {
            logger.error("优惠券领取失败", e);
        }
        return this.addError("优惠券领取失败");
    }

    @RequestMapping("/voucher/index")
    @ResponseBody
    public Object heavenlyIndex2() {
        Map<String, Object> dataMap = Maps.newHashMap();
        try {
            MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant == null) {
                merchantId = 0L;
            } else {
                merchantId = merchant.getId();
            }
            //优惠券集合
            Map<String, Object> center = heavenlyVoucherBuineseApi.getHeavenVoucherCenterByMerchantIdForShuang12(merchantId);
            dataMap.put("voucherCenterList", center.get("voucherCenterList"));
            dataMap.put("num4HadGet", center.get("num4HadGet"));
            dataMap.put("now", center.get("now"));
            dataMap.put("startTime", center.get("startTime"));
            dataMap.put("endTime", center.get("endTime"));
            return this.addResult("data", dataMap);
        } catch (Exception e) {
            return this.addError("下单返券pc接口数据异常:" + e.toString());
        }
    }

}
