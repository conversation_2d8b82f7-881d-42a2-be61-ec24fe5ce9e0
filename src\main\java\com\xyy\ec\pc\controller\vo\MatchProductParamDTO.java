package com.xyy.ec.pc.controller.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 匹配商品ID入参实体
 */
@Data
public class MatchProductParamDTO implements Serializable {
    //通用名
    private String generalName;
    //批准文号
    private String approvalNo;
    //生产厂家
    private String manufacturerName;
    //规格型号
    private String spec;
    //小包装条码
    private String smallPackageCode;
    //购买数
    private Integer buyNum;
    //价格
    private BigDecimal price;
    //行号
    private int lineNum;
    //导入保存的Id
    private Long excelId;

    private String merchantCode;
    /**
     * 比价类型: 1:折后价，2:连锁指导价
     */
    private Integer matchPriceType;
}
