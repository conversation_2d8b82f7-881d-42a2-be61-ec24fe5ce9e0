package com.xyy.ec.pc.controller.vo.merchant;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantAccountRelatedMerchantInfoVO implements Serializable {

    /**
     * 关联ID，没有实际意义。
     */
    private Long id;

    /**
     * 药店编号。
     */
    private Long merchantId;

    /**
     * 状态，1上传委托书，2审核通过，3审核不通过，4店铺审核中。
     */
    private Integer status;

    /**
     * 账号角色，1店长，2员工
     */
    private Integer role;

    /**
     * 店铺审核状态，1-审核中，2-审核通过，3-审核不通过
     */
    private Integer merchantStatus;

    /**
     * 药店店铺名称
     */
    private String name;

    /**
     * 药店地址
     */
    private String address;

    /**
     * 药店省
     */
    private String province;

    /**
     * 药店市
     */
    private String city;

    /**
     * 药店区
     */
    private String district;

    /**
     * 药店县
     */
    private String street;
}
