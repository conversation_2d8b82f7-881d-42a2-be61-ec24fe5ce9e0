package com.xyy.ec.pc.cms.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * CMS：期望商品查询参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsExpectProductQueryParam implements Serializable {

    /**
     * 分公司编码
     */
    private String branchCode;

    /**
     * 会员ID
     */
    private Long merchantId;

    /**
     * 商品展示组ID
     */
    private String exhibitionId;

    /**
     * 终端类型
     */
    private Integer terminalType;

    /**
     * 期望数量
     */
    private Integer expectNum;

}
