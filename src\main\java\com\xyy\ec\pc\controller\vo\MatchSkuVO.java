package com.xyy.ec.pc.controller.vo;

import com.xyy.ec.marketing.client.dto.CouponDto;
import com.xyy.ec.order.dto.settle.ActivityLevelInfo;
import com.xyy.ec.product.business.ecp.csutag.dto.TagDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 匹配到商品信息实体
 */
@Data
public class MatchSkuVO implements Serializable {
    private static final long serialVersionUID = -2015453871942532732L;

    //商品ID
    private Long skuId;
    // barcode
    private String barcode;
    //机构ID
    private String orgId;
    //店铺编码
    private String shopCode;
    //店铺名称
    private String shopName;
    //PC店铺链接
    private String pcShopUrl;
    //商品条码
    private String code;
    //通用名称
    private String commonName;
    //展示名称
    private String showName;
    //批准文号
    private String approvalNumber;
    //生产厂家
    private String manufacturer;
    /** 近效期 */
    private String nearEffect;
    /** 远效期 */
    private String farEffect;
    /** 商品规格*/
    private String spec;
    /** 中包装数量（默认为1） */
    private Integer mediumPackageNum;
    /** 阶梯价 */
    private List<ActivityLevelInfo> levelDiscountInfos;
    /**商品价格**/
    private BigDecimal fob;
    /** 连锁指导价 */
    private BigDecimal guidePrice;
    /** 商品单位 */
    private String productUnit;
    /** 是否可拆零（0:不可拆零；1:可拆零） */
    private Integer isSplit;
    /**商品库存**/
    private Integer availableQty;
    /** 是否可购买 */
    private Boolean isPurchase;
    /** 0:白名单1:可见不可买2:不可见*/
    private Integer purchaseType;
    /** 限购数据 */
    private Integer limitedQty;
    /** 最小起购数 */
    private Integer leastPurchaseNum;
    /**购买数量**/
    private Integer qty;
    /**商品毛利*/
    private BigDecimal skuGrossProfit;
    /**商品毛利率*/
    private BigDecimal skuGrossProfitRate;
    /**小计**/
    private BigDecimal subTotal;
    /**最优店铺编码**/
  /*  private String optimalShopCode;
    *//**最优店铺名称**//*
    private String optimalShopName;
    *//**最优价格**//*
    private BigDecimal optimalPrice;
    *//** 价格最优品 **//*
    private MatchSkuVO skuOptimalVO;*/
    /** 商品主图 */
    private String imageUrl;
    // 标签集合
    private List<TagDTO> tagList;
    /**不可购买枚举 MatchErrorEnum**/
    private List<String> noBuyMsg = new ArrayList<>();
    //预计到手价
    private BigDecimal discountPrice;
    //近效期，0正常，1临期，2近效期
    private Integer nearEffectiveFlag;
    //是否疑似匹配错误，0非，1是
    private Integer isMatchError;
    //生产日期近至
    private String nearManuf;
    //生产日期远至
    private String farManuf;
    //促销类型
    private Integer promotionType;
    //匹配差异字段,1条码，2批准文号，3规格，4通用名，5生产厂家
    private List<Integer> diffFieldList;
    //匹配度
    private String score;
    /**是否第三方（0：否；1：是）*/
    private Integer isThirdCompany;
    /** 含税结存单价 */
    private BigDecimal costWithTaxRatePrice;
    //优惠信息
    private List<String> discountDesc;
    /**
     * 选中状态
     * 0-未选中，1-选中
     */
    private Integer selectStatus = 0;
    /**
     * 实付价/入库价(单价)：实付价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）-余额抵扣；
     *
     */
    private BigDecimal purchasePrice;
    /**
     * 活动显示文案
     */
    private String title;
    /**
     * 限购提示
     */
    private List<String> inValidMsg;
    /**件装量*/
    private String pieceLoading;
    /**
     * 是否一键替换 false或null：否，true:是
     */
    private Boolean isOneKeyReplace;
    //是否品质店铺，0非，1是
    private Integer isQualityShop;
    /**
     * 可领取优惠卷
     */
    List<CouponDto> orderedUnClaimed;

    /**
     * 是否自动修改采购数量
     */
    private int autoAdjustNum;
    // 商家标签
    private List<String> shopLabels;
}
