package com.xyy.ec.pc.interceptor;

import com.xyy.ec.pc.authentication.interceptor.AuthInterceptor;
import com.xyy.ec.pc.authentication.interceptor.SaasAuthInterceptor;
import com.xyy.ec.pc.newfront.interceptor.CmsPcNewInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * @Description:  拦截器-注册拦截器
 * @Author: WanKp
 * @Date: 2018/8/31 09:40
 **/
@Slf4j
@RequiredArgsConstructor
@SpringBootConfiguration
public class AppConfigureAdapter extends WebMvcConfigurerAdapter {

    private final RateFlowInterceptor rateFlowInterceptor;
    private final XssInterceptor xssInterceptor;
    private final MerchantCurrentLimitInterceptor merchantCurrentLimitInterceptor;
    private final AuthInterceptor authInterceptor;
    private final CmsPcNewInterceptor cmsPcNewInterceptor;
    private final SaasAuthInterceptor saasAuthInterceptor;


    private static final String[] addPathPatterns = new String[]{"/merchant/**", "/orderDelivery/**", "/helpCenter/licenseList.htm",
            "/search/**", "/cms/activity/**", "/selfShop/center/**", "/company/center/companyInfo/**", "/app/pinganaccount/**",
            "/pc/search/v1/**", "/pc/search/v2/**", "/searchRecord/**", "/marketing/discount/**", "/marketing/rebateVoucher/**", "/header_data.json","/pc/recommend/v3/**"};

    private static final String[] excludePathPatterns = new String[]{"/merchant/center/relShop", "/merchant/center/delRelShop",
            "/merchant/center/uploadAuthorization", "/merchant/center/getUploadAuthorization", "/merchant/center/getLoginAccountInfo", "/merchant/center/account/merchantList",
            "/merchant/center/clerk", "/merchant/center/selectMerchant", "/merchant/center/add", "/merchant/center/getSimpleMerchantInfo","/merchant/center/licenseAudit/uploadImg",
            "/merchant/center/selectMerchant","/loginAgreement/getLoginAgreement","/pc/recommend/v3/saas/listProducts","/saas/login","/pc/recommend/v3/saas/exportListProducts","/pc/recommend/v3/saas/canReceiveRestockCoupon"};

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("注册拦截器:AppConfigureAdapter");
        // 限流拦截器
        registry.addInterceptor(merchantCurrentLimitInterceptor).addPathPatterns("/**");
        // 新CMS鉴权拦截器
        registry.addInterceptor(cmsPcNewInterceptor)
                .addPathPatterns("/new-front/**")
                .excludePathPatterns("/new-front/user/**","/new-front/validate/**","/new-front/loginAgreement/**");
        // 新鉴权拦截器
        registry.addInterceptor(authInterceptor).addPathPatterns(addPathPatterns).excludePathPatterns(excludePathPatterns);
        registry.addInterceptor(rateFlowInterceptor).addPathPatterns("/search/**");
        registry.addInterceptor(xssInterceptor).addPathPatterns("/**");
        registry.addInterceptor(saasAuthInterceptor).addPathPatterns("/pc/recommend/v3/saas/listProducts","/pc/recommend/v3/saas/exportListProducts","/pc/recommend/v3/saas/canReceiveRestockCoupon");
    }


}
