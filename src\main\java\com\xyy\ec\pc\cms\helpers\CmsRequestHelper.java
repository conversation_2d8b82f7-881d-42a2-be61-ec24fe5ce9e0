package com.xyy.ec.pc.cms.helpers;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.pc.cms.dto.CmsRequestDTO;
import com.xyy.ec.pc.cms.param.CmsRequestParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * CMS请求帮助类
 *
 * <AUTHOR>
 */
@Slf4j
public class CmsRequestHelper {

    /**
     * 尝试将字符串转为Long类型。
     * 若无法转换则返回null。
     *
     * @param str
     * @return
     */
    public static Long tryParseLong(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        try {
            return Long.parseLong(str.trim());
        } catch (Exception e) {
            log.error("CMS请求数据转换异常tryParseLong：{}", str, e);
            return null;
        }
    }

    /**
     * 尝试将字符串转为Integer类型。
     * 若无法转换则返回null。
     *
     * @param str
     * @return
     */
    public static Integer tryParseInteger(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        try {
            return Integer.parseInt(str.trim());
        } catch (Exception e) {
            log.error("CMS请求数据转换异常tryParseInteger：{}", str, e);
            return null;
        }
    }

    /**
     * 尝试将字符串转为Boolean类型。
     * 若无法转换则返回null。
     *
     * @param str
     * @return
     */
    public static Boolean tryParseBoolean(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        str = str.trim();
        if (str.equalsIgnoreCase("true")) {
            return Boolean.TRUE;
        }
        if (str.equalsIgnoreCase("false")) {
            return Boolean.FALSE;
        }
        log.error("CMS请求数据转换为Boolean类型失败tryParseBoolean：{}", str);
        return null;
    }

    public static List<Long> tryParseLongTypeItemList(List<String> stringTypeItemList) {
        if (Objects.isNull(stringTypeItemList)) {
            return null;
        }
        if (stringTypeItemList.isEmpty()) {
            return Lists.newArrayList();
        }
        return stringTypeItemList.stream().map(CmsRequestHelper::tryParseLong)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static Set<Long> tryParseLongTypeItemSet(Set<String> stringTypeItemSet) {
        if (Objects.isNull(stringTypeItemSet)) {
            return null;
        }
        if (stringTypeItemSet.isEmpty()) {
            return Sets.newHashSet();
        }
        return stringTypeItemSet.stream().map(CmsRequestHelper::tryParseLong)
                .filter(Objects::nonNull).collect(Collectors.toSet());
    }

    /**
     * 将以英文逗号分隔组成的字符串转成元素为Integer类型的Set集合。
     * 如：1,2,3,4 -> [1,2,3,4]
     *
     * @param itemListStr
     * @return
     */
    public static Set<Integer> tryParseStrToSetIntegerType(String itemListStr) {
        if (StringUtils.isEmpty(itemListStr)) {
            return Sets.newHashSet();
        }
        String[] split = itemListStr.split(",");
        return Arrays.stream(split).map(item -> tryParseInteger(item))
                .filter(Objects::nonNull).collect(Collectors.toSet());
    }

    public static CmsRequestDTO createDTO(CmsRequestParam cmsRequestParam) {
        if (Objects.isNull(cmsRequestParam)) {
            return null;
        }
        CmsRequestDTO cmsRequestDTO = new CmsRequestDTO();
        String moduleSourceType = cmsRequestParam.getModuleSourceType();
        cmsRequestDTO.setModuleSourceType(tryParseInteger(moduleSourceType));
        String isFillActPt = cmsRequestParam.getIsFillActPt();
        cmsRequestDTO.setIsFillActPt(tryParseBoolean(isFillActPt));
        String isFillActPgby = cmsRequestParam.getIsFillActPgby();
        cmsRequestDTO.setIsFillActPgby(tryParseBoolean(isFillActPgby));
        String expectedProductNum = cmsRequestParam.getExpectedProductNum();
        cmsRequestDTO.setExpectedProductNum(tryParseInteger(expectedProductNum));
        cmsRequestDTO.setExhibitionIdStr(cmsRequestParam.getExhibitionIdStr());
        cmsRequestDTO.setExhibitionId(cmsRequestParam.getExhibitionId());
        cmsRequestDTO.setCategoryId(tryParseLong(cmsRequestParam.getCategoryId()));
        cmsRequestDTO.setShopCodes(cmsRequestParam.getShopCodes());
        cmsRequestDTO.setSortType(tryParseInteger(cmsRequestParam.getSortType()));
        cmsRequestDTO.setAnchorCsuIds(cmsRequestParam.getAnchorCsuIds());
        String pageNum = cmsRequestParam.getPageNum();
        cmsRequestDTO.setPageNum(tryParseInteger(pageNum));
        String pageSize = cmsRequestParam.getPageSize();
        cmsRequestDTO.setPageSize(tryParseInteger(pageSize));
        Boolean isEnd = cmsRequestParam.getIsEnd();
        cmsRequestDTO.setIsEnd(isEnd);

        String nsid = cmsRequestParam.getNsid();
        cmsRequestDTO.setNsid(nsid);
        Integer currentCount = cmsRequestParam.getCurrentCount();
        cmsRequestDTO.setCurrentCount(currentCount);
        String pageSource = cmsRequestParam.getPageSource();
        cmsRequestDTO.setPageSource(pageSource);
        String pageUrl = cmsRequestParam.getPageUrl();
        cmsRequestDTO.setPageUrl(pageUrl);
        String currentPageSource = cmsRequestParam.getCurrentPageSource();
        cmsRequestDTO.setCurrentPageSource(currentPageSource);
        String pageId = cmsRequestParam.getPageId();
        cmsRequestDTO.setPageId(pageId);
        String pageTitle = cmsRequestParam.getPageTitle();
        cmsRequestDTO.setPageTitle(pageTitle);
        String sptype = cmsRequestParam.getSptype();
        cmsRequestDTO.setSptype(sptype);
        String spid = cmsRequestParam.getSpid();
        cmsRequestDTO.setSpid(spid);
        String sid = cmsRequestParam.getSid();
        cmsRequestDTO.setSid(sid);
        cmsRequestDTO.setScmE(cmsRequestParam.getScmE());
        log.info("CMS请求Param转DTO，param：{}，DTO：{}", JSONObject.toJSONString(cmsRequestParam), JSONObject.toJSONString(cmsRequestDTO));
        return cmsRequestDTO;
    }

    public static String getLongStr(Long longType) {
        if (Objects.isNull(longType)) {
            return null;
        }
        return String.valueOf(longType);
    }

    public static String getIntegerStr(Integer integerType) {
        if (Objects.isNull(integerType)) {
            return null;
        }
        return String.valueOf(integerType);
    }

    public static String getBooleanStr(Boolean booleanType) {
        if (Objects.isNull(booleanType)) {
            return null;
        }
        return String.valueOf(booleanType);
    }

    public static List<String> getStringTypeItemListByLongTypeItemList(List<Long> longTypeItemList) {
        if (Objects.isNull(longTypeItemList)) {
            return null;
        }
        if (longTypeItemList.isEmpty()) {
            return Lists.newArrayList();
        }
        return longTypeItemList.stream().map(CmsRequestHelper::getLongStr)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static Set<String> getStringTypeItemSetByLongTypeItemSet(Set<Long> longTypeItemSet) {
        if (Objects.isNull(longTypeItemSet)) {
            return null;
        }
        if (longTypeItemSet.isEmpty()) {
            return Sets.newHashSet();
        }
        return longTypeItemSet.stream().map(CmsRequestHelper::getLongStr)
                .filter(Objects::nonNull).collect(Collectors.toSet());
    }

    public static CmsRequestParam createParam(CmsRequestDTO cmsRequestDTO) {
        if (Objects.isNull(cmsRequestDTO)) {
            return null;
        }

        CmsRequestParam cmsRequestParam = new CmsRequestParam();
        Integer moduleSourceType = cmsRequestDTO.getModuleSourceType();
        cmsRequestParam.setModuleSourceType(getIntegerStr(moduleSourceType));
        Boolean isFillActPt = cmsRequestDTO.getIsFillActPt();
        cmsRequestParam.setIsFillActPt(getBooleanStr(isFillActPt));
        Boolean isFillActPgby = cmsRequestDTO.getIsFillActPgby();
        cmsRequestParam.setIsFillActPgby(getBooleanStr(isFillActPgby));
        Integer expectedProductNum = cmsRequestDTO.getExpectedProductNum();
        cmsRequestParam.setExpectedProductNum(getIntegerStr(expectedProductNum));
        cmsRequestParam.setExhibitionIdStr(cmsRequestDTO.getExhibitionIdStr());
        Integer pageNum = cmsRequestDTO.getPageNum();
        cmsRequestParam.setPageNum(getIntegerStr(pageNum));
        Integer pageSize = cmsRequestDTO.getPageSize();
        cmsRequestParam.setPageSize(getIntegerStr(pageSize));
        Boolean isEnd = cmsRequestDTO.getIsEnd();
        cmsRequestParam.setIsEnd(isEnd);

        String nsid = cmsRequestDTO.getNsid();
        cmsRequestParam.setNsid(nsid);
        Integer currentCount = cmsRequestDTO.getCurrentCount();
        cmsRequestParam.setCurrentCount(currentCount);
        String pageSource = cmsRequestDTO.getPageSource();
        cmsRequestParam.setPageSource(pageSource);
        String pageUrl = cmsRequestDTO.getPageUrl();
        cmsRequestParam.setPageUrl(pageUrl);
        String currentPageSource = cmsRequestDTO.getCurrentPageSource();
        cmsRequestParam.setCurrentPageSource(currentPageSource);
        String pageId = cmsRequestDTO.getPageId();
        cmsRequestParam.setPageId(pageId);
        String pageTitle = cmsRequestDTO.getPageTitle();
        cmsRequestParam.setPageTitle(pageTitle);
        String sptype = cmsRequestDTO.getSptype();
        cmsRequestParam.setSptype(sptype);
        String spid = cmsRequestDTO.getSpid();
        cmsRequestParam.setSpid(spid);
        String sid = cmsRequestDTO.getSid();
        cmsRequestParam.setSid(sid);
        cmsRequestParam.setScmE(cmsRequestDTO.getScmE());
        log.info("CMS请求DTO转Param，DTO：{}，param：{}", JSONObject.toJSONString(cmsRequestDTO), JSONObject.toJSONString(cmsRequestParam));
        return cmsRequestParam;
    }

}
