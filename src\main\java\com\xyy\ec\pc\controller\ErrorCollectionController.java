package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.exception.FileException;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CosUtil;
import com.xyy.ec.pc.util.JsonUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.ecp.csuerrorcorrection.api.EcpCsuErrorCorrectionBusinessApi;
import com.xyy.ec.product.business.ecp.csuerrorcorrection.vo.CsuErrorCorrectionBusinessVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @descriotion
 * @date 14:17 2019/8/3
 */
@Controller
@RequestMapping("errorCollection")
@Slf4j
public class ErrorCollectionController extends BaseController {


    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Reference(version = "1.0.0")
    private EcpCsuErrorCorrectionBusinessApi ecpCsuErrorCorrectionBusinessApi;

    private static final String CONTENT_PATTERN = "(\\d,)*\\d";

    /*@RequestMapping("saveImg")
    @ResponseBody
    public Map saveImg(@RequestParam MultipartFile file) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Map<String, String> descriptions = Maps.newHashMap();
            descriptions.put("userId", merchant.getId().toString());
            descriptions.put("userName", merchant.getRealName());
            descriptions.put("uploadReason", "ERROR_COLLECTION");
            String upload = CosUtil.upload(file, descriptions);
            Map<String, Object> success = addResult(RESULT_SUCCESS);
            success.put("url", upload);
            return success;
        }catch (FileException e){
            log.error("上传图片失败!", e);
            return addError("上传图片有问题!");
        } catch (Exception e) {
            log.error("上传图片失败!", e);
            return addError("提交出错");
        }
    }

    @RequestMapping("delImg")
    @ResponseBody
    public Map delImg(@RequestParam String url) {
        try {
            Integer integer = fdfsClient.delete_Img(url);
            if (integer <= 0) {
                throw new RuntimeException("删除图片失败!");
            }
            return addResult("success");
        } catch (Exception e) {
            log.error("删除图片失败!", e);
            return addError(RESULT_ERRORMSG);
        }
    }*/

    private List<MultipartFile> getMultipartFiles(HttpServletRequest request){
        List<MultipartFile> multipartFiles = Lists.newArrayList();
        // 创建一个通用的多部分解析器
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                request.getSession().getServletContext());
        // 判断 request 是否有文件上传,即多部分请求
        if (multipartResolver.isMultipart(request)) {
            // 转换成多部分request
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            // 取得request中的所有文件名
            Iterator<String> iter = multiRequest.getFileNames();
            while (iter.hasNext()) {
                // 取得上传文件
                List<MultipartFile> files = multiRequest.getFiles(iter.next());
                if (!CollectionUtils.isEmpty(files)) {
                    multipartFiles.addAll(files);
                }
            }
        }
        return multipartFiles;
    }

    @RequestMapping("save")
    @ResponseBody
    public String saveErrorCollection(HttpServletRequest request, CsuErrorCorrectionBusinessVO skuErrorCorrectionVO, @RequestParam("skuId") Long skuId) {
        try {
            if(Objects.nonNull(skuId)){
                skuErrorCorrectionVO.setSkuId(skuId);
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if(Objects.isNull(merchant)){
                return JsonUtil.wapperObjToString( addError(CODE_ERROR,"用户未登录!"));
            }
            List<MultipartFile> multipartFiles = getMultipartFiles(request);
            checkParam(skuErrorCorrectionVO,multipartFiles);
            List<String> urls = Lists.newArrayList();
            for (MultipartFile file : multipartFiles) {
                String upload = CosUtil.uploadAndGetFullPath(file.getInputStream(),file.getOriginalFilename());
                if (StringUtil.isNotEmpty(upload)) {
                    urls.add(upload);
                }
            }
            Map res = initErrVo(skuErrorCorrectionVO, merchant, urls);
            return JsonUtil.wapperObjToString(res);
        } catch (IllegalArgumentException e) {
            log.error("保存商品参数错误!", e);
            try {
                return JsonUtil.wapperObjToString(addError(e.getMessage()));
            } catch (JsonProcessingException ex) {
                return "";
            }
        } catch (FileException e) {
            log.error("图片文件有问题!SkuErrorCorrectionBusinessVO:{}", skuErrorCorrectionVO);
            try {
                return JsonUtil.wapperObjToString( addError(CODE_ERROR,"您上传的图片不符合要求\n请重新上传"));
            } catch (JsonProcessingException ex) {
                return "";
            }
        } catch (Exception e) {
            log.error("保存商品纠错失败!", e);
            try {
                return JsonUtil.wapperObjToString(addError("保存失败!"));
            } catch (JsonProcessingException ex) {
                return "";
            }
        }
    }

    private void checkParam(CsuErrorCorrectionBusinessVO param,List<MultipartFile> files){
        Integer type = param.getType();
        Assert.notNull(type,"纠错类型必填!");
        if(type.equals(1)){
            Integer priceAdjustType = param.getPriceAdjustType();
            Assert.notNull(priceAdjustType,"价格纠错类型必填!");
            if(priceAdjustType==1||priceAdjustType==2){
                BigDecimal modifyPrice = param.getModifyPrice();
                Assert.notNull(modifyPrice,"纠错价格必填");
                param.setModifyPrice(modifyPrice.setScale(2,BigDecimal.ROUND_HALF_UP));
                Assert.hasText(param.getRefPlatform(),"参考平台必填!");
            }else if(priceAdjustType==3){
                Assert.hasText(param.getSupplementNote(),"其它说明必填!");
                Assert.notEmpty(files,"图片必填!");
            }else {
                throw new IllegalArgumentException("价格纠错类型不合法!");
            }
        }else if(type.equals(2)){
            Assert.hasText(param.getProductContent(),"商品信息内容必填!");
            Assert.isTrue(Pattern.matches(CONTENT_PATTERN,param.getProductContent()),"商品信息内容格式有误!");
            Assert.hasText(param.getSupplementNote(),"文字说明必填!");
            Assert.notEmpty(files,"图片必填!");
        }else if(type.equals(3)){
            Assert.hasText(param.getSupplementNote(),"文字说明必填!");
            Assert.notEmpty(files,"图片必填!");
        }else {
            throw new IllegalArgumentException("纠错类型不合法!");
        }

    }

    public Map initErrVo(CsuErrorCorrectionBusinessVO skuErrorCorrectionVO, MerchantBussinessDto merchant, List<String> urls) {
        Date now = new Date();
        skuErrorCorrectionVO.setImgs(urls);
        skuErrorCorrectionVO.setCreateTime(now);
        skuErrorCorrectionVO.setUpdateTime(now);
        skuErrorCorrectionVO.setMerchantId(merchant.getId());
        skuErrorCorrectionVO.setBranchCode(merchant.getRegisterCode());
        skuErrorCorrectionVO.setMerchantName(merchant.getRealName());
        ecpCsuErrorCorrectionBusinessApi.save(skuErrorCorrectionVO);
        return addResult("success");
    }

}
