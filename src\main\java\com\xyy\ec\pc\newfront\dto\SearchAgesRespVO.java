package com.xyy.ec.pc.newfront.dto;

import com.xyy.ec.pc.search.ecp.vo.PcSearchAggregateResultVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchCusCategoryAggregateResultVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchShopAggregateResultVO;
import com.xyy.ec.search.engine.ecp.result.SearchDynamicLabelInfoDTO;
import lombok.Data;

import java.util.List;

@Data
public class SearchAgesRespVO {
    private List<PcSearchAggregateResultVO> specStats;
    private List<PcSearchShopAggregateResultVO> shopStats;
    private List<PcSearchAggregateResultVO> manufacturerStats;
    private List<PcSearchCusCategoryAggregateResultVO> catStats;
    private List<SearchDynamicLabelInfoDTO> dynamicLabelConfig;

}
