package com.xyy.ec.pc.remote;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.recommend.api.RecommendedApiV2;
import com.xyy.recommend.dto.OftenPurchaseOrSearchQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Data 2025/6/4 21:41
 */
@Service
@Slf4j
public class ProductAlsoLikeRpcRemote {
    @Reference(version = "2.0.0")
    private RecommendedApiV2 recommendedApiV2;

    public Map<Long,String> queryRecommendDescList(OftenPurchaseOrSearchQueryParam queryParam){
        log.debug("queryRecommendDescList start queryParam:{}", JSON.toJSONString(queryParam));
        try {
            ApiRPCResult<Map<Long, String>> mapApiRPCResult = recommendedApiV2.queryRecommendDescList(queryParam);
            log.debug("queryRecommendDescList end queryParam:{} mapApiRPCResult:{}",JSON.toJSONString(queryParam), JSON.toJSONString(mapApiRPCResult));
            if (mapApiRPCResult == null || mapApiRPCResult.isFail()){
                return Maps.newHashMap();
            }
            return mapApiRPCResult.getData();
        }catch (Exception e){
            log.error("queryRecommendDescList error queryParam:{}", JSON.toJSONString(queryParam), e);
            return Maps.newHashMap();
        }
    }

}
