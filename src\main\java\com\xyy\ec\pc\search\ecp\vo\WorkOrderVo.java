package com.xyy.ec.pc.search.ecp.vo;

import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class WorkOrderVo {
    //退款商品信息
    List<RefundProductInformation> refundProductInfoList;
    // 退款金额
    private BigDecimal refundMoney;
    // 退款原因
    private String refundReason;
    // 订单编号
    private String orderNo;
    // 退款状态
    private Integer auditState;
    // 申请时间
    private Date refundCreateTime;
    // 售后类型
    private int refundType;
    // 补充说明
    private String refundExplain;
    // 售后状态
    private int auditProcessState;
    // 退款店铺
    private String merchantName;
    //退款商品数量
    private int refundNum;
    // 图片（另一个图片字段，命名可根据实际场景调整区分）
    private String orderWorkImgsurl;
    // 创建时间
    private LocalDateTime createTime;
    // 当前处理节点
    private int currentProcessingPersonType;
    // 工单状态(0待领取,1处理中,2已完成,3失效)
    private int state;
    //节点信息
    private List<T> processList;
    //退款信息
    private List<RefundImageInformationVo> refundInfoList;




}
