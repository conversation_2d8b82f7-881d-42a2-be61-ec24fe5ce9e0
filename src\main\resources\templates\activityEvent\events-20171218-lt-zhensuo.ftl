<!DOCTYPE HTML>
<html>
<head>
<#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="诊所专区">
    <meta name="keyword" content="诊所专区">
    <title>诊所专区</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" type="text/css" href="/static/css/activityEvent/events-20171218-lt-zhensuo.css?t=${t_v}"/>
    <script type="text/javascript" src="/static/js/zhuge/zhugeio.js?t=${t_v}"></script>
    <link rel="stylesheet" href="/static/css/sui-append.min.css">
</head>

<body>
<div class="container">

    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
    <#include "/common/header.ftl" />
    </div>
    <div class="banner">
    </div>
    <!--头部导航区域结束-->
    <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
    <!--主体部分开始-->
<#list moduleCategory as category>
    <#if category.alias == 1045>
        <div class="banner clearfix">
            <div id="myCarousel" data-ride="carousel" data-interval="4000" class="sui-carousel slide">
                <ol class="carousel-indicators">
                    <#list category.items as appModule>
                        <li data-target="#myCarousel" data-slide-to="${appModule_index}"
                            <#if (appModule_index == 0)>
                            class="active"
                            </#if>
                        ></li>
                    </#list>
                </ol>
                <div class="carousel-inner">
                    <#list category.items as appModule>
                        <div class="
                                        <#if (appModule_index == 0)>
                                            active
                                        </#if>
                                     item">
                            <#if (appModule.action ?? && appModule.action!='')><a href="${appModule.action}"></#if>
                            <img src="${productImageUrl}/${appModule.imgUrl}">
                            <#if (appModule.action ?? && appModule.action!='')></a></#if>
                        </div>

                    </#list>
                </div>
                <#if (category.items?? && category.items?size >1 )>
                    <a href="#myCarousel" data-slide="prev" class="carousel-control left">‹</a><a href="#myCarousel" data-slide="next" class="carousel-control right">›</a>
                </#if>
            </div>
        </div>
    </#if>
</#list>


    <div class="main">
        <div class="nav">
        <#list moduleCategory as category>
            <#if category.alias == 1048>
                <#if category.title == '注射用药'>
                    <a href="#zhushe" class="tiaozhuan medication zs-color1 active">注射用药</a>
                <#elseif category.title == '口服用药'>
                    <a href="#koufu" class="tiaozhuan medication zs-color10">口服用药</a>
                </#if>
                <#list category.items as appModule>
                    <a href="#${appModule.remark}" class="tiaozhuan zs-color-${appModule.remark}">${appModule.text}</a>
                </#list>
            </#if>
        </#list>
            <a href="#fuliao" class="tiaozhuan medication zs-color19">器械敷料</a>
            <a href="javascript:;" class="tiaozhuan toTop"><i >
            </i>返回顶部</a>
        </div>

    <#list moduleCategory as category>
        <#if category.alias == 1046>
            <ul class="zs-nav clearfix">
                <#list category.items as appModule>
                    <li>
                        <a href="${appModule.action}" class="xinpin">
                            <img src="${productImageUrl}/${appModule.imgUrl}" alt="">
                        </a>
                    </li>
                </#list>
            </ul>

        </#if>
        <#if category.alias == 1047>
            <#if (category.items?? && category.items?size >=2)>
                <div class="pinpai top">
                    <h3><img src="http://upload.ybm100.com/ybm/pc/activitys/img/events/20171218-lt-zszq/pinpai_03.png" alt=""></h3>
                    <ul class="remen clearfix">
                        <#list category.items as appModule>
                            <li>
                                <a href="${appModule.action}">
                                    <img src="${productImageUrl}/${appModule.imgUrl}" alt="">
                                </a>
                            </li>
                        </#list>
                    </ul>
                </div>
            <#else>
                <div class="pinpai">
                    <#list category.items as appModule>
                        <#if (appModule_index ==0)>
                            <img src="${productImageUrl}/${appModule.imgUrl}" alt="">
                        </#if>
                    </#list>
                </div>
            </#if>
        </#if>
        <#if category.alias == 1048>
            <#if category.title == '注射用药'>
            <div class="featured-medication">
            <div class="zhushe clearfix luoti" id='zhushe'>
            <#elseif category.title == '口服用药'>
            <div class="oral-medication clearfix">
            <div class="koufu  clearfix luoti" id='koufu'>
            </#if>
            <h3 class="fl"><i></i>${category.title}</h3>
            <#if (category.action ?? && category.action!='')>
                <span class="fr"><a href="${category.action}">更多 ></a></span>
            </#if>
        </div>
            <#if category.title == '注射用药'>
            <div class="injection">
            <#elseif category.title == '口服用药'>
            <div class="oral">
            </#if>
            <#list category.items as appModule>
                <div id='${appModule.remark}' class="luoti">
                    <div class="medication">
                        <img src="${productImageUrl}/${appModule.imgUrl}" alt="">${appModule.text}
                    </div>
                    <ul class="mrth-new events clearfix">
                        <#list appModule.skuVOList as skuVO>
                            <!--大图模式-->
                            <#import "/common/skuVO.ftl" as pr>
                            <@pr.skuVO skuVO/>
                        </#list>
                    </ul>
                </div>
            </#list>
        </div>
        </div>
        </#if>

        <#if category.alias == 1049>
            <div class="equipment-accessories clearfix">
                <div class="fuliao clearfix luoti " id='fuliao'>
                    <h3 class="fl"><i></i>${category.title}</h3>
                    <span class="fr"><a href="${category.action}">更多 ></a></span>
                </div>
                <div class="accessories">
                    <ul class="mrth-new events clearfix">
                        <#list category.items as appModule>
                            <#list appModule.skuVOList as skuVO>
                                <!--大图模式-->
                                <#import "/common/skuVO.ftl" as pr>
                                <@pr.skuVO skuVO/>
                            </#list>
                        </#list>
                    </ul>
                </div>
            </div>
        </div>
        </#if>

        <#if category.alias == 2002 && (category.activityPackageVoList??) && (category.activityPackageVoList?size > 0)>
            <div class="zs-taocan">
                <div class="tc-tc">
                    <a href="${category.action}" class="tc-title" target="_blank">
                        更多<i class="sui-icon icon-tb-roundrightfill"></i>
                    </a>
                </div>
                <div class="tc-main clearfix">
                    <#list category.activityPackageVoList as activityPackageDTO>
                        <#if activityPackageDTO_index == 0>
                            <div class="tc-row1">
                                <div class="tc-price">
                                    <div class="tc-jia">
                                        套餐价：
                                        <span>￥${activityPackageDTO.totalPrice}</span>
                                    </div>
                                    <div class="tc-yuan">
                                        <s>原价：
                                            <span>￥${activityPackageDTO.totalPrice + activityPackageDTO.discountPrice}</span>
                                        </s>
                                    </div>
                                </div>
                                <div class="tc-caigou">
                                    <div class="row6">
                                        <a href="javascript:void(0);" class="sub1 fl">-</a>
                                        <input class="fl" type="text" value="0" id="num${activityPackageDTO.id}" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}">
                                        <a href="javascript:void(0);" class="add1 fl">+</a>
                                        <a href="javascript:void(0);" class="buy1 fl" onclick="changeCartPackage(${activityPackageDTO.id},event,this)">加入采购单</a>
                                        <!--灰色样式-->
                                        <!--<a href="javascript:void(0);" class="gary fl">加入采购单</a>-->
                                    </div>
                                </div>
                            </div>
                            <div class="tc-row2 clearfix">
                                <#list activityPackageDTO.skuList as skuVO>
                                    <div class="jia"><img src="/static/images/events/20170818/jia.png"></div>
                                    <div class="item">
                                        <div class="row1">
                                            <a href="/search/skuDetail/${skuVO.sku.id}.htm" target="_blank" title="${skuVO.sku.commonName}">
                                                <img id="dt_${skuVO.sku.id}" src="${productImageUrl}/ybm/product/${skuVO.sku.imageUrl}" alt="" onerror="this.src='/static/images/default-middle.png'"/>
                                            </a>
                                            <!--标签-->
                                            <div class="bq-box">
                                                <#if (skuVO.sku.status == 1 && skuVO.sku.availableQty == 0) || skuVO.sku.status == 2 || ((skuVO.sku.status == 3 || skuVO.sku.status == 5)  && skuVO.sku.promotionTotalQty == 0)  || (skuVO.sku.isSplit == 0 && skuVO.sku.availableQty - skuVO.sku.mediumPackageNum lt 0)>
                                                    <img src="/static/images/product/bq-shouqing.png" alt="">
                                                </#if>
                                                <#if skuVO.sku.status == 4>
                                                    <img src="/static/images/product/bq-xiajia.png" alt="">
                                                </#if>
                                            </div>
                                        </div>
                                        <div class="row2">
                                            <a href="/search/skuDetail/${skuVO.sku.id}.htm" target="_blank" title="${skuVO.sku.showName}">
                                            ${skuVO.sku.showName}
                                            </a>
                                        </div>
                                        <div class="row4 text-overflow">
                                        ${skuVO.sku.spec}
                                        </div>
                                        <div class="row5 text-overflow">
                                        ${skuVO.sku.manufacturer}
                                        </div>
                                        <div class="row3">
                                            <!--正常显示价格样式-->
                                            <span class="meiyuan">￥</span><span class="price">${skuVO.sku.fob}</span>
                                            <span class="tcsj">x${skuVO.productNumber}</span>
                                        </div>
                                        <!--标签-->
                                        <div class="row-biaoqian">
                                            <#list skuVO.sku.tagList as item >
                                                <#if item_index < 3>
                                                    <#if (item.uiType == 4)>
                                                        <span class="default">${item.name}</span>
                                                    </#if>
                                                    <#if item.uiType == 1>
                                                        <span class="linqi">${item.name}</span>
                                                    </#if>
                                                    <#if item.uiType == 2>
                                                        <span class="quan">${item.name}</span>
                                                    </#if>
                                                    <#if (item.uiType == 3)>
                                                        <span class="manjian">${item.name}</span>
                                                    </#if>
                                                </#if>
                                            </#list>
                                        </div>
                                        <div class="row-last">
                                            <#if (skuVO.sku.uniformPrice??) && (skuVO.sku.uniformPrice > 0)>
                                                <div class="kongxiao-box">
                                                    <span class="s-kx">控销价</span><span class="jg">￥${skuVO.sku.uniformPrice}</span>
                                                </div>
                                            </#if>
                                            <#if (skuVO.sku.suggestPrice ?? ) && (skuVO.sku.suggestPrice != '')>
                                                <div class="kongxiao-box">
                                                    <span class="s-kx">零售价</span><span class="jg">${skuVO.sku.suggestPrice}</span>
                                                </div>
                                            </#if>
                                            <#if (skuVO.sku.grossMargin??) && (skuVO.sku.grossMargin > 0) >
                                                <div class="maoli-box">
                                                    <span class="s-ml">毛利</span><span class="jg">${skuVO.sku.grossMargin}</span>
                                                </div>
                                            </#if>
                                        </div>
                                    </div>
                                </#list>
                            </div>
                        </#if>
                    </#list>


                </div>
            </div>
        </#if>
        <#if (category.alias == 2003 && category.items?? && category.items?size > 0)>
            <div class="zs-zhuanxiang">
                <div class="tc-tc">
                    <a href="${category.action}" class="tc-title">
                        更多<i class="sui-icon icon-tb-roundrightfill"></i>
                    </a>
                </div>
                <div class="zx-main">
                    <ul class="mrth-new events clearfix text-overflow ">
                        <#list category.items as appModule>
                            <#list appModule.skuVOList as skuVO>
                                <!--大图模式-->
                                <#import "/common/skuVO.ftl" as pr>
                                <@pr.skuVO skuVO/>
                            </#list>
                        </#list>
                    </ul>
                </div>
            </div>
        </#if>
    </#list>
    </div>
    </div>
        <!--主体部分结束-->

        <!--底部导航区域开始-->
        <div class="footer" id="footer">
        <#include "/common/footer.ftl" />
        </div>
        <!--底部导航区域结束-->
    </div>
</body>
<script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/activityEvent/events-20171218-lt-zhensuo.js?t=${t_v}"></script>
</html>