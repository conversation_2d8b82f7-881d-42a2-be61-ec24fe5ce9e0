package com.xyy.ec.pc.out.controller;

import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.account.LoginAccountDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.model.MerchantOut;
import com.xyy.ec.pc.service.MerchantService;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.util.CryptoUtil;
import com.xyy.ec.pc.out.controller.dto.SaasAuthRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @description  给ERP和EC使用的接口
 * @date  2018-10-13
 */
@Controller
@RequestMapping("/ec")
public class MerchantInfoController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MerchantInfoController.class);

    @Autowired
    private MerchantService merchantService;

    /**
     * @Description 供ERP使用 TO 刘玉琛
     * @param syncNo
     * @return
     */
    @RequestMapping(value = "/erp/merchantInfo",produces = "text/json;charset=UTF-8")
    @ResponseBody
    public String getMerchantInfoForErp(String syncNo){

        JSONObject jo = new JSONObject();
        try {
            if (StringUtil.isEmpty(syncNo)){
                jo.put("code",this.CODE_ERROR);
                jo.put("msg","参数不能为空");
                return jo.toJSONString();
            }

            MerchantBussinessDto merchant = merchantService.getMerchantBySync(syncNo);
            if (merchant==null){
                jo.put("code",this.CODE_SUCCESS);
                jo.put("msg","无此用户");
                return jo.toJSONString();
            }
            jo.put("code",this.CODE_SUCCESS);
            jo.put("msg","成功");
            jo.put("data",merchant.getRegisterCode());
            return jo.toJSONString();
        }catch (Exception e){
            logger.error("ERP根据同步码查询商户基本信息，查询异常,e="+e);
            jo.put("code",this.CODE_ERROR);
            jo.put("msg","系统异常");
            return jo.toJSONString();
        }
    }

    /**
     * @Description 一块钱通过手机号查询药店信息
     * @param mobile
     * @return
     */
    @RequestMapping(value = "/ykq/getMerchantByMobile",produces = "text/json;charset=UTF-8")
    @ResponseBody
    public String getMerchantByMobile(String mobile) {
        JSONObject jo = new JSONObject();
        try {
            if (StringUtil.isEmpty(mobile)){
                jo.put("code",CODE_ERROR);
                jo.put("msg","参数不能为空");
                return jo.toJSONString();
            }

            MerchantBussinessDto merchant = merchantService.findMerchantByMobile(mobile);
            if (merchant==null){
                jo.put("code",CODE_SUCCESS);
                jo.put("msg","无此用户");
                jo.put("state",false);
                return jo.toJSONString();
            }
            merchant.setAddress(merchant.getProvince()+merchant.getCity()+merchant.getDistrict()+merchant.getAddress());
            MerchantOut merchantOut = new MerchantOut();
            BeanUtils.copyProperties(merchant,merchantOut);
            jo.put("code",CODE_SUCCESS);
            jo.put("msg","成功");
            jo.put("state",true);
            jo.put("data",merchantOut);
            return jo.toJSONString();
        }catch (Exception e){
            logger.error("ERP根据手机号查询商户基本信息，查询异常,e="+e);
            jo.put("code",CODE_ERROR);
            jo.put("msg","系统异常");
            return jo.toJSONString();
        }
    }

    /**
     * @Description 内部加密接口 - 根据手机号查询商户信息
     * 使用双重验证：令牌验证 + 签名验证
     * @param authRequest 认证请求对象，包含token、signature、timestamp
     * @return
     */
    @RequestMapping(value = "/saas/merchantDetail", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String getMerchantInfoForSAAS(@RequestBody SaasAuthRequest authRequest) {
        JSONObject jo = new JSONObject();
        try {
            // 参数验证
            if (authRequest == null || StringUtil.isEmpty(authRequest.getToken()) || 
                StringUtil.isEmpty(authRequest.getSignature()) || authRequest.getTimestamp() == null) {
                jo.put("code", CODE_ERROR);
                jo.put("msg", "参数不能为空");
                return jo.toJSONString();
            }

            String token = authRequest.getToken();
            String signature = authRequest.getSignature();
            Long timestamp = authRequest.getTimestamp();

            // 验证时间戳（防止重放攻击，限制5分钟内有效）
            long currentTime = System.currentTimeMillis();
            if (Math.abs(currentTime - timestamp) > 5 * 60 * 1000) {
                jo.put("code", CODE_ERROR);
                jo.put("msg", "请求已过期");
                return jo.toJSONString();
            }

            // 解析并验证令牌（10分钟有效期）
            String mobile = CryptoUtil.validateAndParseToken(token, 10);
            if (StringUtil.isEmpty(mobile)) {
                jo.put("code", CODE_ERROR);
                jo.put("msg", "令牌无效或已过期");
                return jo.toJSONString();
            }

            // 验证签名
            if (!CryptoUtil.verifySignature(mobile, timestamp, signature)) {
                jo.put("code", CODE_ERROR);
                jo.put("msg", "签名验证失败");
                return jo.toJSONString();
            }

            // 查询商户信息
            List<MerchantBussinessDto> merchant = merchantService.findMerchantDetailsByMobile(mobile);
            if (merchant == null) {
                jo.put("code", CODE_SUCCESS);
                jo.put("msg", "无此用户");
                jo.put("state", false);
                return jo.toJSONString();
            }

            jo.put("code", CODE_SUCCESS);
            jo.put("msg", "成功");
            jo.put("state", true);
            jo.put("data", merchant);
            return jo.toJSONString();
            
        } catch (Exception e) {
            logger.error("SAAS加密接口根据手机号查询商户基本信息，查询异常,e=" + e);
            jo.put("code", CODE_ERROR);
            jo.put("msg", "系统异常");
            return jo.toJSONString();
        }
    }

    /**
     * @Description 内部加密接口 - 根据手机号查询账号信息
     * 使用双重验证：令牌验证 + 签名验证
     * @param authRequest 认证请求对象，包含token、signature、timestamp
     * @return
     */
    @RequestMapping(value = "/saas/accountInfo", produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String getAccountInfoForSAAS(@RequestBody SaasAuthRequest authRequest) {
        JSONObject jo = new JSONObject();
        try {
            // 参数验证
            if (authRequest == null || StringUtil.isEmpty(authRequest.getToken()) ||
                    StringUtil.isEmpty(authRequest.getSignature()) || authRequest.getTimestamp() == null) {
                jo.put("code", CODE_ERROR);
                jo.put("msg", "参数不能为空");
                return jo.toJSONString();
            }

            String token = authRequest.getToken();
            String signature = authRequest.getSignature();
            Long timestamp = authRequest.getTimestamp();

            // 验证时间戳（防止重放攻击，限制5分钟内有效）
            long currentTime = System.currentTimeMillis();
            if (Math.abs(currentTime - timestamp) > 5 * 60 * 1000) {
                jo.put("code", CODE_ERROR);
                jo.put("msg", "请求已过期");
                return jo.toJSONString();
            }

            // 解析并验证令牌（10分钟有效期）
            String mobile = CryptoUtil.validateAndParseToken(token, 10);
            if (StringUtil.isEmpty(mobile)) {
                jo.put("code", CODE_ERROR);
                jo.put("msg", "令牌无效或已过期");
                return jo.toJSONString();
            }

            // 验证签名
            if (!CryptoUtil.verifySignature(mobile, timestamp, signature)) {
                jo.put("code", CODE_ERROR);
                jo.put("msg", "签名验证失败");
                return jo.toJSONString();
            }

            // 查询商户信息
            LoginAccountDto accountInfo = merchantService.findAccountInfoByMobile(mobile);
            if (accountInfo == null) {
                jo.put("code", CODE_SUCCESS);
                jo.put("msg", "无此用户");
                jo.put("state", false);
                return jo.toJSONString();
            }

            jo.put("code", CODE_SUCCESS);
            jo.put("msg", "成功");
            jo.put("state", true);
            jo.put("data", accountInfo);
            return jo.toJSONString();

        } catch (Exception e) {
            logger.error("SAAS加密接口根据手机号查询用户基本信息，查询异常,e=" + e);
            jo.put("code", CODE_ERROR);
            jo.put("msg", "系统异常");
            return jo.toJSONString();
        }
    }

}
