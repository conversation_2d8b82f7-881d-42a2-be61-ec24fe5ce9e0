<!DOCTYPE HTML>
<html>

	<head>
		<#include "/common/common.ftl" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<title>我的计划单-全部商品</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
		<link rel="stylesheet" href="/static/css/user.css?t=${t_v}" />
		<link rel="stylesheet" href="/static/css/myplan-allproducts.css?t=${t_v}" />
		
		<script type="text/javascript" src="/static/js/planningSchedule/myplan-all.js?t=20180902"></script>
		<script type="text/javascript">
			var ctx="/static";
		
		</script>
	</head>
	
	<body>
		<div class="container">

			<!--头部导航区域开始-->
			<div class="headerBox" id="headerBox">
				<#include "/common/header.ftl" />
			</div>
			<!--头部导航区域结束-->

			<!--主体部分开始-->
			<div class="main">
				<div class="myplan row">
					<!--面包屑-->
					<ul class="sui-breadcrumb">
					  <li><a href="/">首页</a>></li>
					  <li><a href="/merchant/center/index.htm">用户中心</a>></li>
					  <li><a href="/merchant/center/planningSchedule/index.htm">我的计划单</a>></li>
					  <li class="active">全部商品</li>
					</ul>
					
					<div class="myqual-content clear">
						<div class="side-left fl">
							<#include "/common/merchantCenter/left.ftl" />
						</div>
						
						 <div class="main-right fr">
                                <div class="order-tab-title">
                                    <a href="javascript:;" class="order-tab-title-item <#if (param.status== null || param.status==0)>order-tab-title-item-cur</#if>">
                                        <span class="order-tab-title-name">全部商品</span>
                                    </a>
                                    <a href="javascript:;" class="order-tab-title-item <#if (param.status==1)>order-tab-title-item-cur</#if>">
                                        <span class="order-tab-title-name">有货商品</span>
                                    </a>
                                    <a href="javascript:;" class="order-tab-title-item <#if (param.status==2)>order-tab-title-item-cur</#if>">
                                        <span class="order-tab-title-name">无货商品</span>
                                    </a>
                                    <div class="plan-products">
										<input type="text" id="fuzzyPlanningName" name="fuzzyPlanningName" value="${param.fuzzyPlanningName}" placeholder="商品名称/厂家/助记码">
										<div class="products-search" onclick="searchList();">搜 索</div>
									</div>
                                </div>
							
								<div class="title">显示<span>#{begin}-#{end}</span>条，共<span>#{pager.total}</span>条</div>
								
								<input hidden id="planningScheduleId" value="#{param.planningScheduleId}" />
								
							<div class="tab-content  myorder-tab-content">
							 <#if (param.status== null || param.status==0)>
							 		<#if pager.rows??&&(pager.rows?size>0)> 
							 		
								<div class="tab-cont" style="display:block">
									<div class="shangpin">
							
										<table>
											<thead>
												<tr><td class="pp-row1">选择</td> 
												<td class="pp-row2"><span class="sp-1">商品名称</span></td>
												<td class="pp-row3"><span>规格</span></td>
												<td class="pp-row4"><span>生产厂家</span></td>
												<td class="pp-row5"><span>历史采购价格</span></td>
												<td class="pp-row6"><span class="sp-1">计划补货数量</span></td>
												<td class="pp-row8"><span>操作</span></td>
											</tr></thead>
											
											<tbody>
											 <#list pager.rows as order>
												<tr>
													<td>
														<label class="checkbox-pretty inline">
															<input type="checkbox" name="id" value="${order.id}"><span></span>
													</label>
													</td>
													<td><span class="pp-row2 shangpin-limit">${order.productName}</span>
													</td>
													<td><span>${order.spec}</span>
													</td>
													<td><span class="pp-row4 shangpin-limit">${order.manufacturer}</span>
													</td>
													<td>
														<span class="pp-price">¥<input type="text" maxlength="10" value="${order.price}" onfocus="hideMsg(this);" onblur="updatePrice(this);"/>
														 <span style="color:red;display:none;float: left;">请输入大于0的数字且最多有两位小数</span>
													</td>
													<td>
														<div class="row6">
																<input class="fl" maxlength="11" type="text" value="${order.purchaseNumber}" onfocus="hideMsg(this);" onblur="updateNum(this);"
																onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')"/>
														        <span style="color:red;display:none;float: left;">请输入大于等于0的整数</span>
														</div>
													</td>
												
													<td>
														<span class="close" data-target="#delModal" data-toggle="modal">删除</span>
													</td>
												</tr>
												</#list>
												</tbody>
											<tfoot>
											<tr><td>
												<label class="checkbox-pretty check-all-btn inline">
													<input type="checkbox"><span>全选</span>
												</label> 
											</td>
											<td>
													<span class="allClose" data-target="#delAllModal" data-toggle="modal">删除选中物品</span> 
											</td>
											<td></td>
											<td></td><td></td>
											<td>
											</td><td></td>
											</tr></tfoot>
										</table>
									<#else>
											<!--没有订单:   加样式和图片以及事件-->
											<div class="noplan" style="text-align: center;">
											<img src="/static/images/user/noplan.png" alt="">
											 </div>
									</#if>
								</div>
								</#if>
								<#if (param.status == 1)>
								<#if pager.rows??&&(pager.rows?size>0)>
							    	<div class="tab-cont" style="display:block">
									<div class="shangpin">
									
										<table>
											<thead>
												<tr><td class="pp-row1">选择</td> 
												<td class="pp-row2"><span class="sp-1">商品名称</span></td>
												<td class="pp-row3"><span>规格</span></td>
												<td class="pp-row4"><span>生产厂家</span></td>
												<td class="pp-row5"><span>历史采购价格</span></td>
												<td class="pp-row6"><span class="sp-1">计划补货数量</span></td>
												<td class="pp-row8"><span>操作</span></td>
											</tr></thead>
											<tbody>
												 <#list pager.rows as order>
												<tr>
													<td>
														<label class="checkbox-pretty inline">
															<input type="checkbox" name="id" value="${order.id}"><span></span>
														</label>
													</td>
													<td><span class="pp-row2 shangpin-limit">${order.productName}</span>
													</td>
													<td><span>${order.spec}</span>
													</td>
													<td><span class="pp-row4 shangpin-limit">${order.manufacturer}</span>
													</td>
													<td>
														<span class="pp-price">¥<input type="text" value="${order.price}" onfocus="hideMsg(this);" onblur="updatePrice(this);"></span>
														<span style="color:red;display:none;float: left;">只能输入带有两位小数的数字</span>
													</td>
													<td>
														<div class="row6">
																<input class="fl" type="text" value="${order.purchaseNumber}" onfocus="hideMsg(this);" onblur="updateNum(this);"
																onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')"/>
																<span style="color:red;display:none;float: left;">只能输入正整数</span>
														</div>
													</td>
												
													<td>
														<span class="close" data-target="#delModal" data-toggle="modal">删除</span>
													</td>
												</tr>
												</#list>
												</tbody>
											<tfoot>
											<tr><td>
												<label class="checkbox-pretty check-all-btn inline">
													<input type="checkbox"><span>全选</span>
												</label> 
											</td>
											<td>
													<span class="allClose" data-target="#delAllModal" data-toggle="modal">删除选中物品</span> 
											</td>
											<td></td>
											<td></td><td></td>
											<td>
											</td><td></td>
											</tr></tfoot>
										</table>
										<#else>
											<!--没有订单:   加样式和图片以及事件-->
											<div class="noplan" style="text-align: center;">
											<img src="/static/images/user/noplan.png" alt="">
											 </div>
									</#if>
								</div>
								</#if>
								<#if (param.status == 2)>
								<#if pager.rows??&&(pager.rows?size>0)>
											<div class="tab-cont" style="display:block">
										<div class="shangpin">
										
											<table>
												<thead>
													<tr><td class="pp-row1">选择</td> 
													<td class="pp-row2"><span class="sp-1">商品名称</span></td>
													<td class="pp-row3"><span>规格</span></td>
													<td class="pp-row4"><span>生产厂家</span></td>
													<td class="pp-row5"><span>历史采购价格</span></td>
													<td class="pp-row6"><span class="sp-1">计划补货数量</span></td>
													<td class="pp-row8"><span>操作</span></td>
												</tr></thead>
												<tbody>
													 <#list pager.rows as order>
												<tr>
													<td>
														<label class="checkbox-pretty inline">
															<input type="checkbox" name="id" value="${order.id}"><span></span>
													</label>
													</td>
													<td><span class="pp-row2 shangpin-limit">${order.productName}</span>
													</td>
													<td><span>${order.spec}</span>
													</td>
													<td><span class="pp-row4 shangpin-limit">${order.manufacturer}</span>
													</td>
													<td>
														<span class="pp-price">¥<input type="text" value="${order.price}" onfocus="hideMsg(this);" onblur="updatePrice(this);"></span>
														<span style="color:red;display:none;float: left;">请输入大于等于0的数字,最多带有两位小数</span>
													</td>
													<td>
														<div class="row6">
																<input class="fl" type="text" value="${order.purchaseNumber}" onfocus="hideMsg(this);" onblur="updateNum(this);"
																onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')"/>
														 	    <span style="color:red;display:none;float: left;">请输入大于等于0的整数</span>
														</div>
													</td>
												
													<td>
														<span class="close" data-target="#delModal" data-toggle="modal">删除</span>
													</td>
												</tr>
												</#list>
													</tbody>
												<tfoot>
												<tr><td>
													<label class="checkbox-pretty check-all-btn inline">
														<input type="checkbox"><span>全选</span>
													</label> 
												</td>
												<td>
														<span class="allClose" data-target="#delAllModal" data-toggle="modal">删除选中物品</span> 
												</td>
												<td></td>
												<td></td><td></td>
												<td>
												</td><td></td>
												</tr></tfoot>
											</table>
											<#else>
											<!--没有订单:   加样式和图片以及事件-->
											<div class="noplan" style="text-align: center;">
											<img src="/static/images/user/noplan.png" alt="">
											</div>
										</#if>
										</div>
									</#if>
										<!--分页器-->
								<#--<div class="page">-->
									 <#--<#import "/common/pager.ftl" as p>-->
				              		 <#--<@p.pager currentPage=pager.currentPage limit=pager.limit total=pager.total pageCount=pager.pageCount toURL=pager.requestUrl method="get"/>-->
								<#--</div>-->
								
									</div>
								</div>
							
						</div>
					</div>
				</div>
			</div>
			<!--主体部分结束-->

			<!--底部导航区域开始-->
			<div class="footer" id="footer">
				<#include "/common/footer.ftl" />
			</div>
			<!--底部导航区域结束-->

		</div>
	

		 <!-- 删除弹窗 -->
		 <div id="delModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                            <h4 id="myModalLabel" class="modal-title">删除</h4>
                        </div>
                        <div class="modal-body">
                            <div class="spebox">
                                <div class="row1"><i class='sui-icon icon-tb-warnfill'></i>您确认删除商品？</div>
                                <!-- <div class="row2">你可以选择移入收藏，或删除商品。</div> -->
                            </div>
    
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large delete" >确定</button>
                            <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                        </div>
                    </div>
                </div>
		</div>
		
		 <!-- 删除弹窗 -->
		 <div id="delAllModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                            <h4 id="myModalLabel" class="modal-title">删除</h4>
                        </div>
                        <div class="modal-body">
                            <div class="spebox">
                                <div class="row1"><i class='sui-icon icon-tb-warnfill'></i>您确认删除商品？</div>
                                <!-- <div class="row2">你可以选择移入收藏，或删除商品。</div> -->
                            </div>
    
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large delete" >确定</button>
                            <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                        </div>
                    </div>
                </div>
		</div>
	</body>
	
</html>