package com.xyy.ec.pc.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.unionpay.acp.sdk.SDKConstants;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.api.*;
import com.xyy.ec.order.business.api.ecp.order.EcpOrderBusinessApi;
import com.xyy.ec.order.business.api.ecp.pay.PayApi;
import com.xyy.ec.order.business.api.ecp.pay.PayNotifyApi;
import com.xyy.ec.order.business.api.ecp.pay.PayQueryApi;
import com.xyy.ec.order.business.config.OrderEnum;
import com.xyy.ec.order.business.dto.OrderBusinessDto;
import com.xyy.ec.order.business.dto.ecp.pay.*;
import com.xyy.ec.order.business.enums.pay.PayOperationEnums;
import com.xyy.ec.order.business.enums.pay.PaySourceEnums;
import com.xyy.ec.order.business.enums.pay.PayTypeEnums;
import com.xyy.ec.order.business.enums.pay.ThirdPayPlatformEnums;
import com.xyy.ec.order.business.model.OrderPayRequest;
import com.xyy.ec.order.business.utils.DateUtil;
import com.xyy.ec.order.business.utils.OrderConvertUtil;
import com.xyy.ec.order.core.dto.Order;
import com.xyy.ec.order.core.dto.OrderDto;
import com.xyy.ec.order.core.dto.cart.CodeItemDto;
import com.xyy.ec.order.core.util.EntityConvert;
import com.xyy.ec.order.enums.RechargeEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.constants.RedisConstants;
import com.xyy.ec.pc.controller.vo.ShortUrlResponseVO;
import com.xyy.ec.pc.param.PaymentParamDTO;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.service.OrderPayService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ec.pc.vo.order.PayVo;
import com.xyy.framework.redis.autoconfigure.core.RedisClient;
import com.xyy.ms.promotion.business.api.common.luckdraw.LuckDrawApi;
import com.xyy.ms.promotion.business.api.common.luckdraw.vo.LuckDrawReq;
import com.xyy.ms.promotion.business.api.common.luckdraw.vo.LuckDrawResp;
import com.xyy.ms.promotion.business.common.response.PromoResp;
import com.xyy.saas.payment.cores.api.PaymentLoanApi;
import com.xyy.saas.payment.cores.constants.PayExtraConstants;
import com.xyy.saas.payment.cores.param.JdCreditUnionLoginParam;
import com.xyy.saas.payment.cores.vo.ResultVO;
import com.xyy.saas.payment.cores.vo.jd.JdCreditUnionLoginVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支付相关控制器
 * Created by fw on 2016/9/24.
 */

@Controller
@RequestMapping("/shop")
public class ShopPayController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ShopPayController.class);

    @Value("${xyy.ec.admin.shortUrlDomain:https://d.ybm100.com/home/<USER>")
    private String shortUrlDomain;
    @Value("${xyy.ec.admin.shortUrlApikey:ec-202007312296-t890k3l0p9}")
    private String apiKey;
    @Value("${xyy.ec.admin.shortUrl.http.timeout:5000}")
    private int shortUrlHttpTimeout;
    @Resource
    private RedisClient redisClient;

    @Reference(version = "1.0.0")
    private PaymentAliPayBusinessApi paymentAliPayBusinessApi;

    @Reference(version = "1.0.0")
    private PaymentUnionBusinessApi paymentUnionBusinessApi;

    @Reference(version = "1.0.0")
    private PaymentWXBusinessApi paymentWXBusinessApi;

    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;

    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;
    @Reference(version = "1.0.0")
    private PaymentLoanApi paymentLoanApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private CouponBusinessApi couponBusinessApi;

    @Reference(version = "1.0.0")
    private LuckDrawApi luckDrawApi;

    @Autowired
    private Config config;

    @Autowired
    private XyyConfig.CdnConfig cdnConfig;

    @Autowired
    private OrderPayService orderPayService;

    @Reference(version = "1.0.0")
    private EcpOrderBusinessApi ecpOrderBusinessApi;

    @Reference(version = "1.0.0")
    private PayApi payApi;

    @Reference(version = "1.0.0")
    private PayNotifyApi payNotifyApi;

    @Reference(version = "1.0.0")
    private PayQueryApi payQueryApi;

    @Reference(version = "1.0.0")
    private OrderPayBusinessApi orderPayBusinessApi;

    /**
     * @description: 1. 收银台发起支付
     * @author: wcwanga
     * @create: 2019/11/7 13:36
     * @param: [request,
     *          response,
     *          paycode,    支付类型编码，1：支付宝,2：微信，3：银联，详见 PayTypeEnum
     *          orderId,    主订单Id
     *          isList]     是否是我的订单列表发起的支付，1：是，否则不是
     * @return: org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("/payment")
    @ResponseBody
    public Object payment(HttpServletRequest request, HttpServletResponse response,
                          String paycode, Long orderId,String token,String cardId,String bankCardInfo,String tranNo,
                          String isList,String qtdata, Boolean useVirtualGold,
                          @RequestParam(value = "rechargeType", required = false) String rechargeType,
                          @RequestParam(value = "amount", required = false) String amount,
                          @RequestParam(value = "reqScene", required = false) String reqScene) throws Exception {

        LOGGER.info("payment orderId:{} paycode:{} isList{} token:{} tranNo:{}--rechargeType:{}--amount:{}--reqScene:{}",orderId,paycode,isList,token,tranNo,rechargeType,amount,reqScene);
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        if (merchant == null) {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }
        Map<String, Object> map = new HashMap<String, Object>();
        Integer rechargeTypeNum = RechargeEnum.ORDER_PAY.getCode();
        // 购物金匹配锁 平移APP逻辑
        if(StringUtils.isNotEmpty(rechargeType)){
            rechargeTypeNum=Integer.valueOf(rechargeType);
        }
        boolean isGoldRecharge = RechargeEnum.GOLD_RECHARGE.getCode().equals(rechargeTypeNum);
        String redisKey ="";
        if(isGoldRecharge){
            redisKey = merchant.getAccountId()+"";
            PaymentParamDTO param = new PaymentParamDTO();
            param.setPaycode(paycode);
            param.setAmount(new BigDecimal(amount));
            param.setReqScene(reqScene);
            param.setCardId(cardId);
            param.setBankCardInfo(bankCardInfo);
            if("settle".contains(reqScene)){
                param.setPayRoute(0);
            }else if("cashier".contains(reqScene)){
                param.setPayRoute(1);
            }
            boolean lock = orderBusinessApi.payLock(redisKey);
            if (!lock) {
                return this.addError("操作太频繁,请5秒后再试！");
            }
            return goldPayment(param, merchant.getMerchantId(), PaySourceEnums.PC.getValue(), IPUtils.getClientIP(request), merchant.getAccountId());
        }
        map.put("rechargeType",RechargeEnum.ORDER_PAY.getCode());

        // 将母单和字段都查出来
        List<OrderBusinessDto> orderList = orderBusinessApi.selectByOrderIdWithSubOrderAndExtendInfoFromMaster(orderId);
        OrderBusinessDto order = orderList.stream().filter(o -> o.getId().equals(orderId)).findFirst().get();

        redisKey = order.getOrderNo()+"";
        if(order == null){
            map.put("errorMsg", "支付参数异常，获取不到订单信息");
            return new ModelAndView("/order/resulterr.ftl", map);
        }

        boolean lock = orderBusinessApi.payLock(redisKey);
        if (!lock) {
            return this.addError("操作太频繁,请5秒后再试！");
        }

        map.put("order", order);
        map.put("ybmOrderNo", order.getOrderNo());//药帮忙订单号

        int payFinalHour = orderBusinessApi.getOnlinePayOrderFinalPaymentHour(merchant.getRegisterCode());

        map.put("payFinalHour", payFinalHour);
        response.setCharacterEncoding("UTF-8");

        if (order.getMerchantId().doubleValue() == merchant.getId().doubleValue()) {
            LOGGER.info("==生成第三方支付单 orderId==" + orderId);
            try {

                try {
                    orderPayBusinessApi.blanketOrderQtDataForPayOrder(order.getOrderNo(),qtdata);
                }catch (Exception e) {
                    LOGGER.error("覆盖支付埋点数据异常,{}", e);
                }

                // 检查订单状态
                if (order.getStatus() != com.xyy.ec.order.core.enums.OrderEnum.OrderStatus.WAITBUYERPAY.getId()) {
                    map.put("errorMsg", "该订单状态已改变，请刷新我的订单列表!");
                    return new ModelAndView("/order/resulterr.ftl", map);
                }

                map.put("orderNo", order.getOrderNo());//订单号
                map.put("money", order.getCashPayAmount());//支付金额

                PayCommunicationDto payCommunication = buildPayCommunicationDto(order, token, tranNo, merchant, paycode, request, orderList, useVirtualGold);

                // 调用支付服务
                PayResponseDto<PayCommunicationDto> result;

                if (paycode.equals("1")) {
                    payCommunication.setPayTypeEnums(PayTypeEnums.ALIPAY);
                    result = payApi.qrCodePay(payCommunication);

                    if(result.isSuccess()){
                        ModelAndView modelAndView = redirectViewIfNeed(useVirtualGold, map, result.getData());
                        if (modelAndView != null) {
                            return modelAndView;
                        }
                        Boolean fuminPaySwitch = result.getData().getResponseDataJson().getBoolean("fuminPaySwitch");
                        LOGGER.info("订单号：{} fuminPaySwitch: {}", order.getOrderNo(),fuminPaySwitch);

                        map.put("payFinalHour", payFinalHour); //订单取消小时数
                        map.put("payCode", paycode);
                        map.put("imgurl", result.getData().getResponseDataJson().getString("imgurl"));//二维码地址
                        map.put("tarReqId",result.getData().getResponseDataJson().getString("tarReqId"));//本次请求支付流水号
                        LOGGER.info("fuminpay--{}",JSONObject.toJSONString(map));
                        return new ModelAndView("/order/fuminpay.ftl", map);
                    }else {
                        LOGGER.error("订单号：{}，支付宝支付异常，{}", order.getOrderNo(),result.getReturnMessage());
                        map.put("errorMsg", "支付宝支付异常，"+ result.getReturnMessage());
                        return new ModelAndView("/order/resulterr.ftl", map);
                    }
                } else if (paycode.equals("2")) {
                    payCommunication.setPayTypeEnums(PayTypeEnums.WECHAT);
                    result = payApi.qrCodePay(payCommunication);
                    if(result.isSuccess()){
                        ModelAndView modelAndView = redirectViewIfNeed(useVirtualGold, map, result.getData());
                        if (modelAndView != null) {
                            return modelAndView;
                        }
                        map.put("payFinalHour", payFinalHour); //订单取消小时数
                        map.put("imgurl", result.getData().getResponseDataJson().getString("imgurl"));//二维码地址
                        map.put("tarReqId",result.getData().getResponseDataJson().getString("tarReqId"));//本次请求支付流水号
                        LOGGER.info("weixinpay--{}",JSONObject.toJSONString(map));
                        return new ModelAndView("/order/weixinpay.ftl", map);
                    }else {
                        if(result.getData() != null && StringUtil.isNotEmpty(result.getData().getResponseDataJson().getString("err_code"))&&"ORDERPAID".equals(result.getData().getResponseDataJson().getString("err_code"))){
                            return new ModelAndView("redirect:/shop/resultok.htm?orderno="+order.getOrderNo());
                        }else {
                            LOGGER.error("订单号：{}，微信支付异常，{}", order.getOrderNo(),result.getReturnMessage());
                            map.put("errorMsg", "微信支付异常，获取不到订单信息.");
                            return new ModelAndView("/order/resulterr.ftl", map);
                        }
                    }
                } else if (paycode.equals("3")) {
                    payCommunication.setPayTypeEnums(PayTypeEnums.UNIONPAY);
                    result = payApi.bankPay(payCommunication);

                    if(result.isSuccess()){
                        ModelAndView modelAndView = redirectViewIfNeed(useVirtualGold, map, result.getData());
                        if (modelAndView != null) {
                            return modelAndView;
                        }
                        response.getWriter().write(result.getData().getResponseDataJson().getString("redirectUrl"));
                    }else {
                        LOGGER.error("订单号：{}，银联支付异常，{}", order.getOrderNo(),result.getReturnMessage());
                        map.put("errorMsg", "银联支付异常，"+ result.getReturnMessage());
                        return new ModelAndView("/order/resulterr.ftl", map);
                    }
                } else if (paycode.equals("4")) {
                    payCommunication.setPayTypeEnums(PayTypeEnums.PINGAN_CREDIT);
                    result = payApi.sdkPay(payCommunication);
                    LOGGER.info("{}，平安支付result:{}", order.getOrderNo(),JSONObject.toJSONString(result));
                    if(result.isSuccess()){
                        ModelAndView modelAndView = redirectViewIfNeed(useVirtualGold, map, result.getData());
                        if (modelAndView != null) {
                            return modelAndView;
                        }
                        return this.addResult("data",result.getData().getResponseDataJson().getString("redirectUrl"));
                       // response.getWriter().write(result.getData().getResponseDataJson().getString("redirectUrl"));
                    }else {
                        LOGGER.error("订单号：{} 平安支付异常，{}", order.getOrderNo(),result.getReturnMessage());
                        map.put("errorMsg", "平安支付异常，"+ result.getReturnMessage());
                        return new ModelAndView("/order/resulterr.ftl", map);
                    }
                } else if (paycode.equals("11")) {
                    payCommunication.setPayTypeEnums(PayTypeEnums.BANK_CARD);
                    payCommunication.getRequestDataJson().put("token",token);
                    payCommunication.getRequestDataJson().put("cardId",cardId);
                    payCommunication.getRequestDataJson().put("bankCardInfo",bankCardInfo);
                    result = payApi.sdkPay(payCommunication);
                    LOGGER.info("{}，京东绑卡支付:{}", order.getOrderNo(),JSONObject.toJSONString(result));
                    if(result.isSuccess()){
                        ModelAndView modelAndView = redirectViewIfNeed(useVirtualGold, map, result.getData());
                        if (modelAndView != null) {
                            return modelAndView;
                        }
                        return new ModelAndView("redirect:/shop/resultok.htm?orderno="+order.getOrderNo());                        // response.getWriter().write(result.getData().getResponseDataJson().getString("redirectUrl"));
                    }else {
                        LOGGER.error("订单号：{} 京东绑卡支付异常，{}", order.getOrderNo(),result.getReturnMessage());
                        map.put("errorMsg", "京东绑卡支付异常，"+ result.getReturnMessage());
                        return new ModelAndView("/order/resulterr.ftl", map);
                    }
                } else if (paycode.equals("12")) {
                    payCommunication.setPayTypeEnums(PayTypeEnums.JD_CREDIT);
                    result = payApi.sdkPay(payCommunication);
                    LOGGER.error("京东金融 {},{},{}", order.getOrderNo(),result.isSuccess(),result.getReturnMessage());
                    if(result.isSuccess()){
                        ModelAndView modelAndView = redirectViewIfNeed(useVirtualGold, map, result.getData());
                        if (modelAndView != null) {
                            return modelAndView;
                        }
                        String redirectUrl = result.getData().getResponseDataJson().getString("redirectUrl");
                        LOGGER.error("京东金融 {},{}", order.getOrderNo(),redirectUrl);
                        return this.addResult("data",login(payCommunication.getMerchantId().toString(),redirectUrl,request));
                        // response.getWriter().write(result.getData().getResponseDataJson().getString("redirectUrl"));
                    } else {
                        if (PayResponseDto.PROCESSING.equals(result.getReturnCode())) {
                            return this.addError(result.getReturnMessage());
                        }
                        return this.addError("网络超时，请稍后重试");

                    }
                } else if (paycode.equals("14")) {
                    payCommunication.setPayTypeEnums(PayTypeEnums.XYD_LOAN);
                    result = payApi.sdkPay(payCommunication);
                    LOGGER.error("小雨点 {},{},{}", order.getOrderNo(),result.isSuccess(),result.getReturnMessage());
                    if(result.isSuccess()){
                        ModelAndView modelAndView = redirectViewIfNeed(useVirtualGold, map, result.getData());
                        if (modelAndView != null) {
                            return modelAndView;
                        }
                        String redirectUrl = result.getData().getResponseDataJson().getString("paymentKey");
                        LOGGER.error("小雨点 {},{}", order.getOrderNo(),redirectUrl);
                        return this.addResult("data",redirectUrl);
                        // response.getWriter().write(result.getData().getResponseDataJson().getString("redirectUrl"));
                    } else {
                        if (PayResponseDto.PROCESSING.equals(result.getReturnCode())) {
                            return this.addError(result.getReturnMessage());
                        }
                        return this.addError("网络超时，请稍后重试");

                    }
                } else if (Objects.equals(paycode, "" + PayTypeEnums.KING_DEE.getValue())) {
                    // 金蝶
                    payCommunication.setPayTypeEnums(PayTypeEnums.KING_DEE);
                    result = payApi.sdkPay(payCommunication);
                    if (result.isSuccess()) {
                        ModelAndView modelAndView = redirectViewIfNeed(useVirtualGold, map, result.getData());
                        if (modelAndView != null) {
                            return modelAndView;
                        }
                        return this.addResult("data", result.getData().getResponseDataJson().getString("paymentKey"));
                    } else {
                        LOGGER.error("订单号：{}，金蝶支付异常，{}", order.getOrderNo(), JSON.toJSONString(result));
                        String returnMessage = result.getReturnMessage();
                        if (PayResponseDto.PROCESSING.equals(result.getReturnCode())) {
                            return this.addError(returnMessage);
                        }
                        return this.addError(StringUtils.isEmpty(returnMessage) ? "网络超时，请稍后重试" : returnMessage);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("ShopPayController#payment 支付异常,{}", JSON.toJSONString(order), e);
                map.put("errorMsg", e.toString());
                return new ModelAndView("/order/resulterr.ftl", map);
            }
            return null;
        } else {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }
    }

    private ModelAndView redirectViewIfNeed(Boolean useVirtualGold, Map<String, Object> map, PayCommunicationDto communicationDto) {
        PayRespDataDto respDataDto = communicationDto.getPayRespDataDto();

        // 重置扣减完购物金的金额
        if (Boolean.TRUE.equals(useVirtualGold) && respDataDto.getAmount() != null) {
            map.put("money", respDataDto.getAmount());
        }

        // 购物金全额抵扣，跳转订单列表页
        if (Objects.equals(2, respDataDto.getJumpType())) {
            return new ModelAndView(new RedirectView("/merchant/center/order/index.htm", true, false));
        }

        return null;
    }
    private PayCommunicationDto buildPayCommunicationDto(OrderBusinessDto order, String token, String tranNo, MerchantBussinessDto merchant, String paycode, HttpServletRequest request, List<OrderBusinessDto> orderList, Boolean useVirtualGold) {
        PayCommunicationDto payCommunication = new PayCommunicationDto();
        payCommunication.getRequestDataJson().put("orderId", order.getId() + "");
        payCommunication.getRequestDataJson().put("token", token);
        payCommunication.getRequestDataJson().put("tranNo", tranNo);
        payCommunication.getRequestDataJson().put("merchantName", order.getMerchantName());
        payCommunication.getRequestDataJson().put("orderNos", Collections.singletonList(order.getOrderNo()));
        payCommunication.getRequestDataJson().put("amount", order.getCashPayAmount());
        payCommunication.getRequestDataJson().put("payExpireTime", DateUtil.date2String(order.getPayExpireTime(), DateUtil.PATTERN_TIMESTAMP));


        // 下掉RequestDataJson 兼容过程
        payCommunication.getPayReqDataDto().setOrderId(order.getId().toString());
        payCommunication.getPayReqDataDto().setToken(token);
        payCommunication.getPayReqDataDto().setTranNo(tranNo);
        payCommunication.getPayReqDataDto().setMerchantName(order.getMerchantName());
        payCommunication.getPayReqDataDto().setAmount(order.getCashPayAmount());
        payCommunication.getPayReqDataDto().setOrderNo(order.getOrderNo());
        payCommunication.getPayReqDataDto().setUseVirtualGold(useVirtualGold);
        payCommunication.getPayReqDataDto().setPayExpireTime(DateUtil.date2String(order.getPayExpireTime(), DateUtil.PATTERN_TIMESTAMP));

        payCommunication.setIsPayMerge(order.getIsParent());

        // 拼接用户信息
        payCommunication.setMerchantId(merchant.getId());
        // 拼接域信息
        payCommunication.setOrderBranchCode(order.getBranchCode());
        //todo 支付相关参数根据销售域走
        payCommunication.setBranchCode(merchant.getRegisterCode());
        // 拼接支付来源
        payCommunication.setPaySource(PaySourceEnums.PC);
        payCommunication.setTerminalType(PaySourceEnums.PC.getValue());
        // 拼接IP
        payCommunication.setIp(IPUtils.getClientIP(request));
//        payCommunication.setPayChannelRoute(initPayCenterRoute(payCommunication, order));
        if (Objects.equals(paycode, "" + PayTypeEnums.KING_DEE.getValue())) {
            payCommunication.setMainOrderNo(order.getOrderNo());
            // 收件人信息
            JSONObject consigneeInfo = new JSONObject()
                    .fluentPut("name", order.getContactor())
                    .fluentPut("mobile", order.getMobile())
                    .fluentPut("address", order.getAddress());
            payCommunication.getRequestDataJson().put(PayExtraConstants.CONSIGNEE_INFO, consigneeInfo.toJSONString());
            payCommunication.getPayReqDataDto().setConsigneeInfo(consigneeInfo.toJSONString());
        }

        List<String> orderNos = orderList.stream().map(o -> o.getOrderNo()).distinct().collect(Collectors.toList());
        Map<String, String> payReqNoMap = payApi.getPayReqNo(orderNos).getData();
        payCommunication.setReqNo(payReqNoMap.get(order.getOrderNo()));

        if (NumberUtils.INTEGER_ONE.equals(order.getIsParent())) {
            List<SubOrderPayDto> subOrderPayDtoList = new ArrayList<>(orderList.size());
            for (OrderBusinessDto dto : orderList) {
                // 过滤掉主单
                if (!NumberUtils.INTEGER_ONE.equals(dto.getIsParent())) {
                    SubOrderPayDto subOrderPayDto = new SubOrderPayDto();
                    subOrderPayDto.setOrderNo(dto.getOrderNo());
                    subOrderPayDto.setOrgId(dto.getOrgId());
                    subOrderPayDto.setPayReqNo(payReqNoMap.get(dto.getOrderNo()));
                    subOrderPayDto.setIsFbp(dto.getIsFbp());
                    subOrderPayDto.setIsThirdCompany(dto.getIsThirdCompany());
                    subOrderPayDto.setAmount(dto.getCashPayAmount());
                    subOrderPayDto.setOrderBranchCode(dto.getBranchCode());
                    subOrderPayDto.setPayeeName(dto.getCompanyName());
                    subOrderPayDto.setMerchantName(dto.getMerchantName());
                    subOrderPayDto.setMerchantId(dto.getMerchantId());
                    subOrderPayDtoList.add(subOrderPayDto);
                }
            }
            payCommunication.setSubOrderPayDtoList(subOrderPayDtoList);
        } else {
            // 合并支付 的母单，商家信息没有意义
            payCommunication.getRequestDataJson().put("isFbp", order.getIsFbp());
            payCommunication.getRequestDataJson().put("orgId", order.getOrgId());
            payCommunication.getRequestDataJson().put("isThirdCompany",order.getIsThirdCompany());

            payCommunication.getPayReqDataDto().setIsFbp(order.getIsFbp());
            payCommunication.getPayReqDataDto().setOrgId(order.getOrgId());
            payCommunication.getPayReqDataDto().setIsThirdCompany(order.getIsThirdCompany());
        }

        return payCommunication;
    }


    private ModelAndView goldPayment(PaymentParamDTO param, Long merchantId, Integer terminalType, String deviceId, Long accountId) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            RechargeDto rechargeDto = buildRechargeDto(param, merchantId, terminalType, deviceId, accountId);
            if(!param.getPaycode().equals("1")&&!param.getPaycode().equals("2") &&!param.getPaycode().equals("11")){
                resultMap.put("errorMsg","支付渠道错误!");
                return new ModelAndView("/order/resulterr.ftl", resultMap);
            }
            LOGGER.info("##goldPayment--购物金在线充值入参；{}",rechargeDto.toString());
            ApiRPCResult<RechargeResultDto> result = payApi.recharge(rechargeDto);
            LOGGER.info("##goldPayment--购物金在线充值返回；{}",JSONObject.toJSONString(result));
            if (result.isSuccess() && result.getData() != null) {
                PayVo payVo = new PayVo();
                RechargeResultDto resultData = result.getData();
                payVo.setPaymentKey(resultData.getPaymentKey());
                payVo.setPayTypeForFrontKey(resultData.getPayTypeForFrontKey());
                payVo.setPayReqNo(resultData.getPayReqNo());
                payVo.setOrderNo(resultData.getOrderNo());
                payVo.setImgurl(resultData.getPaymentKey());
                resultMap = this.addResult("data",payVo);
                resultMap.put("rechargeType",RechargeEnum.GOLD_RECHARGE.getCode());
                resultMap.put("money",param.getAmount());
                resultMap.put("payFinalHour",1);
                resultMap.put("imgurl",payVo.getImgurl());
                if (param.getPaycode().equals("1")) {
                    LOGGER.info("fuminpay：{}",JSONObject.toJSONString(resultMap));
                    return new ModelAndView("/order/fuminpay.ftl", resultMap);
                } else if (param.getPaycode().equals("2")) {
                    LOGGER.info("weixinpay：{}",JSONObject.toJSONString(resultMap));
                    return new ModelAndView("/order/weixinpay.ftl", resultMap);
                }else {
                    Order order = new Order();
                    order.setCashPayAmount(param.getAmount());
                    order.setOrderNo(payVo.getOrderNo());
                    resultMap.put("order",order);
                    LOGGER.info("resultok-银行卡{}",JSONObject.toJSONString(resultMap));
                    return new ModelAndView("/order/resultok.ftl",resultMap);
                }
            } else {
                String returnMessage = StringUtils.isEmpty(result.getErrMsg()) ? "系统忙碌,稍后重试!" : result.getErrMsg();
                resultMap = this.addError(returnMessage);
                resultMap.put("errorMsg", "支付异常，"+ returnMessage);
                resultMap.put("rechargeType",RechargeEnum.GOLD_RECHARGE.getCode());
                Order order = new Order();
                order.setCashPayAmount(param.getAmount());
                resultMap.put("order",order);
                LOGGER.info("resulterr{}",JSONObject.toJSONString(resultMap));
                return new ModelAndView("/order/resulterr.ftl", resultMap);
            }
        } catch (Exception e) {
            resultMap.put("errorMsg", "支付异常!");
            resultMap.put("rechargeType",RechargeEnum.GOLD_RECHARGE.getCode());
            LOGGER.error("PaymentController#goldPayment error param:{}", param, e);
            return new ModelAndView("/order/resulterr.ftl", resultMap);
        }
    }

    private RechargeDto buildRechargeDto(PaymentParamDTO param, Long merchantId, Integer terminalType, String deviceId, Long accountId) {
        RechargeDto rechargeDto = new RechargeDto();
        rechargeDto.setMerchantId(merchantId);
        rechargeDto.setPayType(PayTypeEnums.getByValue(Integer.valueOf(param.getPaycode())).getValue());
        rechargeDto.setAmount(param.getAmount());
        rechargeDto.setPayerType(param.getPayerType());
        rechargeDto.setBankCardInfo(param.getBankCardInfo());
        rechargeDto.setCardId(param.getCardId());
        rechargeDto.setDeviceId(deviceId);
        rechargeDto.setTerminalType(terminalType);
        rechargeDto.setReqScene(param.getReqScene());
        rechargeDto.setAccountId(accountId);
        return rechargeDto;
    }


    private String convertShortUrl(String orderNo, Long merchantId, String redirectUrl) {
        if (StringUtils.isBlank(redirectUrl)) {
            return "";
        }
        try {
            String kingDeeShortUrlCacheKey = RedisConstants.KING_DEE_QRCODE_PAY_SHORT_URL + merchantId;
            String cacheVal = redisClient.get(kingDeeShortUrlCacheKey);
            if (StrUtil.isNotBlank(cacheVal)) {
                return cacheVal;
            }

            String shortUrl = getShortUrl(orderNo, redirectUrl);
            if (StrUtil.isNotBlank(shortUrl)) {
                redisClient.set(kingDeeShortUrlCacheKey, shortUrl, RedisConstants.KING_DEE_QRCODE_PAY_SHORT_URL_EXPIRE);
            }
            return shortUrl;
        } catch (Exception e) {
            LOGGER.error("{} 获取短链异常", orderNo, e);
        }
        return "";
    }

    private String getShortUrl(String orderNo, String redirectUrl) {
        Map<String, String> urlMaps = new HashMap<>();
        urlMaps.put("0", redirectUrl);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("urls", urlMaps);
        paramsMap.put("apikey", apiKey);
        paramsMap.put("isPersistence", true);
        String jsonParams = JSON.toJSONString(paramsMap);
        String result = HttpRequest.post(shortUrlDomain)
                .header("Content-Type", "application/json")
                .body(jsonParams)
                .timeout(shortUrlHttpTimeout)
                .execute().body();
        if (result == null) {
            LOGGER.error("{} 获取短链结果为空", orderNo);
            return "";
        }
        ShortUrlResponseVO response = JSONObject.parseObject(result, ShortUrlResponseVO.class);
        LOGGER.info("{} 获取短链结果:{}", orderNo, JSON.toJSONString(response));
        if (response == null || 0 != Optional.ofNullable(response.getCode()).orElse(0)) {
            LOGGER.error("{} 获取短链失败, {}", orderNo, JSON.toJSONString(response));
            return "";
        }

        List<String> shortUrls = new ArrayList<>();
        Map<Integer, String> urls = response.getUrl();
        if (CollUtil.isNotEmpty(urls)) {
            urls.values().forEach(url -> shortUrls.add(url.replace("https://duan.int.ybm100.com", "https://d.ybm100.com")));
        }
        if (CollUtil.isNotEmpty(shortUrls)) {
            return shortUrls.get(0);
        }
        return "";
    }

    public JdCreditUnionLoginVo  login(String merchantId,String url,HttpServletRequest request){
        try {
            JdCreditUnionLoginParam param =  new JdCreditUnionLoginParam();
            param.setPlatformUserId(merchantId);
            param.setBackUrl(url);
            param.setThirdUserId(merchantId);
            param.setLoginType("PC");
//            param.setOutCustNo();
            param.setClientAgent(request.getHeader("user-agent"));
            param.setClientIp(request.getRemoteAddr());
            LOGGER.info("jdCreditUnionLogin {}", JSONObject.toJSONString(param));

            ResultVO<JdCreditUnionLoginVo> resultVO = paymentLoanApi.jdCreditUnionLogin(param, "");
            LOGGER.info("jdCreditUnionLogin {}", JSONObject.toJSONString(resultVO));

            if (resultVO.isFail()) {
                return null;
            }
            JdCreditUnionLoginVo result = resultVO.getResult();
//            String html = createHtml(result);
            LOGGER.error("jd login:html:{}",result);
            return result;
        } catch (Exception e) {
            LOGGER.error("jd login:merchantId:{}",merchantId,e);
        }
        return null;
    }

    public String createHtml(JdCreditUnionLoginVo result) {
        StringBuffer sf = new StringBuffer();
        sf.append("<html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/></head><body>");
        sf.append("<form id = \"scenceForm\" action=\"" + result.getOpenUrl()+ "\" method=\"post\">");
        sf.append("<input type=\"hidden\" name=\"jrgw-user-id-type\" value=\"0\"/>");
        sf.append("<input type=\"hidden\" name=\"gw-encrypt-type\" value=\"3DES_RSA\"/>");
        sf.append("<input type=\"hidden\" name=\"gw-sign-type\" value=\"SHA256withRSA\"/>");
        sf.append("<input type=\"hidden\" name=\"encrypt\" id=\"encrypt\" value=\""+result.getEncrypt()+"\"/>");
        sf.append("<input type=\"hidden\" name=\"gw-sign\" id=\"gw-sign\" value=\""+result.getSign()+"\"/>");
        sf.append("<input type=\"hidden\" name=\"jrgw-request-time\" id=\"jrgw-request-time\" value=\""+result.getTime()+"\"/>");
        sf.append("<input type=\"hidden\" readonly=\"readonly\" name=\"jrgw-enterprise-user-id\" id=\"jrgw-enterprise-user-id\" value=\""+result.getUserId()+"\"/>");
        sf.append("</form>");
        sf.append("</body>");
        sf.append("<script type=\"text/javascript\">");
        sf.append("document.all.scenceForm.submit();");
        sf.append("</script>");
        sf.append("</html>");
        return sf.toString();
    }
    private PayChannelRouteDto initPayCenterRoute(PayCommunicationDto payCommunication, OrderBusinessDto order) {
        try {
            PayChannelRouteDto route = new PayChannelRouteDto();
            route.setAppVersion(-1);
            route.setIsThirdCompanyOrder(order.getIsThirdCompany() == 1 ? Boolean.TRUE : Boolean.FALSE);
            route.setBranchCode(order.getBranchCode());
            route.setCodeItems(getCodeItems(route));
            return route;
        } catch (Exception e) {
            LOGGER.error("initPayCenterRoute error order:{}",order.getOrderNo(),e);
            return null;
        }
    }

    private Map<String, String> getCodeItems(PayChannelRouteDto route) throws Exception{
        Map<String, String> orderPayConfigMaster;
        if(route.getIsThirdCompanyOrder()){
            orderPayConfigMaster = codeItemServiceRpc.findCodeMap("ORDER_PAY_POP_CONFIG_MASTER", null);
        }else {
            orderPayConfigMaster = codeItemServiceRpc.findCodeMap("ORDER_PAY_CONFIG_MASTER", null);
        }
        if(orderPayConfigMaster == null || orderPayConfigMaster.size() == 0){
            return null;
        }
        return orderPayConfigMaster;
    }

    private JSONObject checkOrder(OrderBusinessDto order) {
        JSONObject res = new JSONObject();
        res.put("code",false);
        // 检查订单状态
        if(order.getStatus()== com.xyy.ec.order.core.enums.OrderEnum.OrderStatus.WAITBUYERPAY.getId()){
            res.put("code",true);
        }else{
            res.put("msg","该订单状态已改变，请刷新我的订单列表!");
        }
        return res;
    }

    /**
     * @description: 2. 统一支付回调
     * @author: wcwanga
     * @create: 2019/11/7 13:39
     * @param: [req,
     *          resp,
     *          thirdPlatformCode]  三方支付通道编码 详见 ThirdPayPlatformEnums
     * @return: java.lang.Object
     **/
    @RequestMapping(value = "/notify/{thirdPlatform}")
    @ResponseBody
    public Object notify(HttpServletRequest req, HttpServletResponse resp,  @PathVariable(value = "thirdPlatform") String thirdPlatformCode){
        String result = "FAILED";
        try {
            // 判定回调渠道
            ThirdPayPlatformEnums platformEnum = ThirdPayPlatformEnums.getByCode(thirdPlatformCode);
            LOGGER.info("统一支付->[回调]匹配支付通道：{}", platformEnum);

            if(platformEnum == null){
                return result;
            }

            // 解析回调参数
            Map<String, String> paramMap = analysisParams(req, platformEnum);
            LOGGER.info("统一支付->[回调]回调参数解析完毕：{}", paramMap);
            if(paramMap==null||paramMap.size()==0){
                return result;
            }

            PayCommunicationDto payCommunication = new PayCommunicationDto();

            payCommunication.setThirdPayPlatformEnums(platformEnum);
            payCommunication.setNotifyDataMap(paramMap);

            PayResponseDto<PayCommunicationDto> res = payNotifyApi.notify(payCommunication);
            LOGGER.info("统一支付->[回调]调用统一回调服务处理完毕：{}", res);
            if(res.isSuccess()){
                result =  "SUCCESS";
                //如果是微信支付，重写返回结果，以XML格式返回
                if(ThirdPayPlatformEnums.WEIXIN_NATIVE.getCode().equals(thirdPlatformCode) || ThirdPayPlatformEnums.WEIXIN_SDK.getCode().equals(thirdPlatformCode)){
                    PayCommunicationDto weixinResult = res.getData();
                    if(weixinResult!=null && weixinResult.getResponseDataJson()!=null){
                        result = weixinResult.getResponseDataJson().getString("notifyReturnInfo");
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("统一支付->[回调]调用通知服务异常{}", e.toString());
        }

        return result;
    }

    /**
     * @description: 2.1 解析回调参数
     * @author: wcwanga
     * @create: 2019/11/7 13:40
     * @param: [request, platformEnum]
     * @return: java.util.Map<java.lang.String,java.lang.String>
     **/
    private Map<String, String> analysisParams(HttpServletRequest request, ThirdPayPlatformEnums platformEnum) throws Exception{
        if(platformEnum == ThirdPayPlatformEnums.WEIXIN_NATIVE || platformEnum == ThirdPayPlatformEnums.WEIXIN_SDK){
            // 微信
            return shopnotifyinfo(request);
        }else if(platformEnum == ThirdPayPlatformEnums.ALIPAY_JSDZ
                || platformEnum == ThirdPayPlatformEnums.ALIPAY_SDK
                || platformEnum == ThirdPayPlatformEnums.PAYCENTER_DIRECT){
            // 支付宝
            return getAlipaynotify(request);
        }else if(platformEnum == ThirdPayPlatformEnums.UNIONLAY_ONLINE || platformEnum == ThirdPayPlatformEnums.UNIONLAY_SDK){
            // 银联
            return getUnionNotify(request);
        }

        return null;
    }

    /**
     * @description: 3. 微信支付二维码页面主动查询支付结果
     * @author: wcwanga
     * @create: 2019/11/7 13:41
     * @param: [request,
     *          response,
     *          tarReqId]    支付流水号
     * @return: java.lang.Object
     **/
    @RequestMapping("/queryWeixinNativeResult")
    @ResponseBody
    public Object queryWeixinNativeResult(HttpServletRequest request, HttpServletResponse response, String tarReqId) throws Exception {

        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        if (merchant == null) {
            return this.addError("登录超时");
        }

        if(StringUtil.isNotEmpty(tarReqId)){
            LOGGER.info("统一支付->[查询]微信二维码界面主动查询，tarReqId：{}", tarReqId);
            PayCommunicationDto payCommunication = new PayCommunicationDto();
            payCommunication.setOperation(PayOperationEnums.QUERYANDUPDATE);
            payCommunication.getRequestDataJson().put("tarReqId",tarReqId);
            PayResponseDto<PayCommunicationDto> res = payQueryApi.query(payCommunication);

            if(res.isSuccess()){
                return this.addResult();
            }
        }

        return this.addError("fail");
    }

    /**
     * @description: 4. 跳转支付回调处理
     * @author: wcwanga
     * @create: 2019/11/7 19:01
     * @param: [request,
     *          response,
     *          thirdPlatformCode]  三方支付通道编码 详见 ThirdPayPlatformEnums
     * @return: java.lang.Object
     **/
    @RequestMapping("/resultPay/{thirdPlatform}")
    @ResponseBody
    public Object resultPay(HttpServletRequest request, HttpServletResponse response, @PathVariable(value = "thirdPlatform") String thirdPlatformCode){
        try {
            Map<String, Object> resultMap = new HashMap<String, Object>();
            // 判定回调渠道
            ThirdPayPlatformEnums platformEnum = ThirdPayPlatformEnums.getByCode(thirdPlatformCode);
            LOGGER.info("统一支付返回匹配支付通道：{}", platformEnum);

            if(platformEnum == null){
                resultMap.put("errorMsg","匹配支付通道失败");
                return new ModelAndView("/order/resulterr.ftl", resultMap);
            }

            // 解析回调参数
            Map<String, String> paramMap = analysisParams(request, platformEnum);
            LOGGER.info("统一支付返回回调参数解析完毕：{}", paramMap);
            if(paramMap==null||paramMap.size()==0){
                resultMap.put("errorMsg","回调参数解析完毕失败");
                return new ModelAndView("/order/resulterr.ftl", resultMap);
            }

            PayCommunicationDto payCommunication = new PayCommunicationDto();
            payCommunication.setThirdPayPlatformEnums(platformEnum);
            payCommunication.setNotifyDataMap(paramMap);

            PayResponseDto<PayCommunicationDto> res;
            if(platformEnum == ThirdPayPlatformEnums.PAYCENTER_DIRECT){
                payCommunication.setOperation(PayOperationEnums.QUERYANDUPDATE);
                payCommunication.getRequestDataJson().put("transactionId",paramMap.get("out_trade_no"));

                String orderId = paramMap.get("orderId");
                if (StringUtils.isNotBlank(orderId)) {
                    payCommunication.getRequestDataJson().put("transactionId",paramMap.get("orderId"));

                }
                LOGGER.info("resultPay payQueryApi.query {}", JSONObject.toJSONString(payCommunication));

                res = payQueryApi.query(payCommunication);
            }else {
                LOGGER.info("resultPay payQueryApi.notify {}", JSONObject.toJSONString(payCommunication));

                res = payNotifyApi.notify(payCommunication);

            }
            LOGGER.info("resultPay payQueryApi.notify result {}", JSONObject.toJSONString(res));

            if(res.isSuccess()){
                return new ModelAndView("redirect:/shop/resultok.htm?orderno="+res.getData().getResponseDataJson().getString("ybmOrderNo"));
            }else {
                resultMap.put("errorMsg",res.getReturnMessage());
                return new ModelAndView("/order/resulterr.ftl", resultMap);
            }
        } catch (Exception e) {
            LOGGER.error("统一支付->[回调]调用通知服务异常{}", e.toString());
            return "FAILED";
        }
    }

    /**
     * 支付后直接结果
     *
     * @return
     */
    @RequestMapping("/alipayresult")
    @ResponseBody
    public Object alipayResult(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> rawResult = new HashMap<String, String>();
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String parameterName = parameterNames.nextElement();
            rawResult.put(parameterName, request.getParameter(parameterName));
        }
        try {
            String outtradeno = rawResult.get("out_trade_no");
            LOGGER.info("==支付宝支付成功后跳转 1 outtradeno==" + outtradeno);

            Map<String, Object> model = new HashMap<String, Object>();
//            Order order = new Order();
//            order.setOrderNo(outtradeno);
//            order = orderBusinessApi.selectWebServiceOrder(order);
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNoFromMaster(outtradeno);
            if (orderBusinessDto == null) {
                return this.addError(outtradeno + "的订单不存在");
            }
            model.put("order", orderBusinessDto);
//            String result = paymentAliPayBusinessApi.shopResultUrl(rawResult);
            if (orderBusinessDto.getStatus() == OrderEnum.OrderStatus.PENDING.getId()) {
                LOGGER.info("==支付宝支付成功后跳转  2  outtradeno==" + outtradeno);
                //1.构建下单支付参数
//                OrderBusinessDto orderBusinessDto = new OrderBusinessDto();
//                BeanUtils.copyProperties(order, orderBusinessDto);

                // 1.构建下单支付参数
                OrderPayRequest orderPayRequest = OrderConvertUtil.buildOrderPayRequest(orderBusinessDto, Constants.IS1);
                String gainVoucher = couponBusinessApi.couponPromotionToOrder(orderPayRequest);
                if (StringUtil.isNotEmpty(gainVoucher)) {
                    model.put("gainVoucher", gainVoucher);
                }

                return new ModelAndView("/order/resultok.ftl", model);
            } else {
                return new ModelAndView("/order/resulterr.ftl", model);
            }
        } catch (Exception e) {
            LOGGER.error("alipayResult", e);
            return this.addError(e.toString());
        }
    }

    /**
     * 支付宝异步回调
     * 2019-03-04 准备弃用此方法
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/alipaynotify")
    @ResponseBody
    @Deprecated
    public String alipaynotify(HttpServletRequest request, HttpServletResponse response) {
        try {
            Map<String, String> params = getAlipaynotify(request);
            String result = paymentAliPayBusinessApi.shopResultUrl(params);
            LOGGER.info("==支付宝回调  params,{},response,{}==",new Object[]{JSON.toJSONString(params), result});
            return  result;
        } catch (IllegalArgumentException e) {
            LOGGER.error("alipaynotify", e);
        }
        return "failure";
    }

    private Map<String, String> getAlipaynotify(HttpServletRequest request){
        Map<String, String> params = new HashMap<String, String>();
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String parameterName = parameterNames.nextElement();
            params.put(parameterName, request.getParameter(parameterName));
        }

        return params;
    }

    /**
     * TODO 微信异步回调需要优化
     * TODO 可以参考 @see https://github.com/cuter44/wxpay-sdk/blob/master/src/main/java/com/github/cuter44/wxpay/servlet/WxpayNotifyGatewayServlet.java
     * 微信异步回调
     * 2019-03-04 准备弃用此方法
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/weixinNotify")
    @Deprecated
    public void weixinNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, String> resultMap = shopnotifyinfo(request);
        String result = paymentWXBusinessApi.shopnotify(resultMap);
        response.getWriter().write(result);// 通知微信是否继续接收回调信息
        String out_trade_no = resultMap.get("out_trade_no");
//        Map<String, Object> model = new HashMap<String, Object>();
//        OrderBusinessDto order = orderBusinessApi.selectByOrderNoFromMaster(out_trade_no);
//        model.put("order", order);
        LOGGER.info("微信回调方法返回结果result={}，order={}", result, out_trade_no);
    }

    public  Map<String, String> shopnotifyinfo(HttpServletRequest request) throws Exception {
        InputStream inStream = request.getInputStream();
        ByteArrayOutputStream outSteam = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        while ((len = inStream.read(buffer)) != -1) {
            outSteam.write(buffer, 0, len);
        }
        outSteam.close();
        inStream.close();
        String resultStr = new String(outSteam.toByteArray(), "UTF-8");
        Map<String, String> resultMap = parseSimpleXmlStr(resultStr);
        return resultMap;
    }

    /**
     * 转化简单格式的xml字符串
     *
     * @param str 需要转成map的String字段
     * @return 已经转换好的map
     */
    public static Map<String, String> parseSimpleXmlStr(String str) {
        Map<String, String> xml = new HashMap<String, String>();
        Document doc ;
        try {
            // 将字符串转为XML
            doc = DocumentHelper.parseText(str);
            // 获取根节点
            Element rootElt = doc.getRootElement();
            // 拿到根节点的名称
            // System.out.println("根节点：" + rootElt.getName());
            @SuppressWarnings("unchecked")
            List<Element> list = rootElt.elements();
            for (int i = 0; i < list.size(); i++) {
                Element elt = list.get(i);
                xml.put(elt.getName(), elt.getText());
            }
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return xml;
    }

    /**
     * 点击  已完成付款/重新付款时，查一遍支付结果
     * @param request
     * @param response
     * @param paycode
     * @param orderId
     * @throws Exception
     */
    @RequestMapping("/queryPay")
    @ResponseBody
    public void queryPayInfo(HttpServletRequest request, HttpServletResponse response, String paycode, Long orderId) throws Exception {
        try {
            try {
                if (paycode.equals("1")) {
                    paymentAliPayBusinessApi.alipayTradeQuery("alipay", orderId);
                } else if (paycode.equals("2")) {
                    paymentWXBusinessApi.findOrderType(orderId);
                } else if (paycode.equals("3")) {
                    paymentUnionBusinessApi.unionPayQueryRequest("unionpay", orderId, true);
                }
            } catch (Exception e) {
                LOGGER.error("支付查询异常了" + e.toString());
            }
        } catch (Exception e) {
            LOGGER.error("支付查询异常", e);
        }
    }

    /****
     * 当微信生成二维码后，停留在微信支付页面，每隔3秒查询一次微信订单状态，是否支付成功。
     * @param request
     * @param response
     * @param orderno
     * @return
     * @throws Exception
     */
    @RequestMapping("/weixinQueryPay")
    @ResponseBody
    public Object weixinQueryPay(HttpServletRequest request, HttpServletResponse response, String orderno) throws Exception {
        OrderBusinessDto ordertmp = orderBusinessApi.selectByOrderNo(orderno);
        String gscnt = paymentWXBusinessApi.findOrderType(ordertmp.getId());

        LOGGER.info("微信支付成功后，查询微信订单接口，是否支付成功状态 weixinQueryPay 返回gscnt={}", gscnt);
        if (gscnt.equals("1")) {
            return this.addResult();
        }
        return this.addError("fail");
    }

    @RequestMapping(value = "/resultok.htm")
    public Object resultok(String orderno) throws Exception {
        OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(orderno);
        Map<String, Object> model = new HashMap<String, Object>();
        model.put("order", orderBusinessDto);
        LOGGER.info("微信支付成功后，后端跳转页面resultok order={}", JSON.toJSONString(orderBusinessDto));
        if (null != orderBusinessDto) {
            //1.构建下单支付参数
            OrderPayRequest orderPayRequest = OrderConvertUtil.buildOrderPayRequest(orderBusinessDto, Constants.IS1);
            //少了gainVoucher
            String gainVoucher = couponBusinessApi.couponPromotionToOrder(orderPayRequest);
            if (StringUtil.isNotEmpty(gainVoucher)) {
                model.put("gainVoucher", gainVoucher);
            }


            Map<String, CodeItemDto> luckDraw = codeItemServiceRpc.selectCodeItem("LUCK_DRAW", orderBusinessDto.getBranchCode());

            String luckDrawOnOff = luckDraw.get("LUCK_DRAW_ON_OFF")!=null?luckDraw.get("LUCK_DRAW_ON_OFF").getName():"";
            String luckDrawText = luckDraw.get("LUCK_DRAW_TEXT")!=null?luckDraw.get("LUCK_DRAW_TEXT").getName():"";

            LuckDrawResp luckDrawResp = luckDrawRespPromoResp(orderBusinessDto.getBranchCode());

            if (luckDrawResp != null) {
                BigDecimal moneyCondition = luckDrawResp.getDrawOrderMoney();

                //判断是否开启开关 是否超过固定金额
                if ("0".equals(luckDrawOnOff) && orderBusinessDto.getMoney().compareTo(moneyCondition) > -1) {
                    //判断用户剩余次数
                    Integer drawCountResult = getDrawCount(orderBusinessDto.getBranchCode(), orderBusinessDto.getMerchantId());
                    if (drawCountResult > 0) {
                        String dialog = String.format(luckDrawText, moneyCondition.setScale(2, BigDecimal.ROUND_HALF_UP));
                        model.put("dialog", dialog);
                    }
                }
            }
            return new ModelAndView("/order/resultok.ftl", model);
        } else {
            return new ModelAndView("/order/resulterr.ftl", model);
        }
    }

    /**
     * 2019-03-04 准备弃用此方法
     * @param req
     * @param resp
     * @throws Exception
     */
    @Deprecated
    @RequestMapping(value = "/unionpaynotify")
    public void unionPayNotify(HttpServletRequest req, HttpServletResponse resp) throws Exception {
        doPost(req, resp);
    }

    @RequestMapping(value = "/unionPayResult")
    public Object unionPayResult(HttpServletRequest req, HttpServletResponse resp)
            throws Exception {
        Map<String, Object> model = new HashMap<String, Object>();
        String orderNo = req.getParameter("orderId");
        OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNoFromMaster(orderNo);
        model.put("order", orderBusinessDto);
        try {
            String result = doPost(req, resp);
            if ("success".equals(result)) {
                //1.构建下单支付参数
                OrderPayRequest orderPayRequest = OrderConvertUtil.buildOrderPayRequest(orderBusinessDto, Constants.IS1);
                // gainVoucher
                String gainVoucher = couponBusinessApi.couponPromotionToOrder(orderPayRequest);
                if (StringUtil.isNotEmpty(gainVoucher)) {
                    model.put("gainVoucher", gainVoucher);
                }
                return new ModelAndView("/order/resultok.ftl", model);
            } else {
                return new ModelAndView("/order/resulterr.ftl", model);
            }
        } catch (Exception e) {
            LOGGER.error("银联支付回显异常", e);
            return new ModelAndView("/order/resultok.ftl", model);
        }
    }

    /**
     * 收银台检测支付情况  主动回调
     * @param orderNo
     * @param payCode 支付类型编码，1：支付宝,2：微信，3：银联，详见 PayTypeEnum
     * @return
     * @throws Exception
     */
    @RequestMapping("/payCheck")
    @ResponseBody
    public Object payCheck(String orderNo,String payCode) throws Exception {
        if(StringUtils.isBlank(orderNo)){
            LOGGER.info("payCheck orderno is null" );
            return this.addError("fail");
        }
        OrderBusinessDto order = orderBusinessApi.selectByOrderNoFromMaster(orderNo);
        if(order == null){
            LOGGER.info("payCheck ORDER is null" + orderNo);
            return this.addError("fail");
        }
        if (order.getStatus() == OrderEnum.OrderStatus.PENDING.getId()) {
            return this.addResult();
        }

        PayCommunicationDto payCommunication = new PayCommunicationDto();
        payCommunication.setOperation(PayOperationEnums.QUERYANDUPDATE);
        payCommunication.getRequestDataJson().put("orderNo", orderNo);
        payCommunication.getRequestDataJson().put("isThirdCompany",order.getIsThirdCompany());
        PayResponseDto<PayCommunicationDto> res = payQueryApi.query(payCommunication);
        LOGGER.info("订单号：{},支付方式：{},用户待支付检测结果:{}",new Object[]{orderNo,payCode,res.toString()});
        if(res.isSuccess()){
            return this.addResult();
        }else {
            return this.addError(res.getReturnMessage());
        }
    }

    private String checkOnLinePay(OrderBusinessDto order) {
        StringBuilder message = new StringBuilder();
        // 判断如果是在线支付的订单,需要先核查第三方支付情况,并且下单5分钟不允许进行取消操作
        try {
            if (order.getPayType()==OrderEnum.OrderPayType.ONLINEPAYMENT.getId()
                    && order.getStatus()==OrderEnum.OrderStatus.WAITBUYERPAY.getId()) {
                paymentAliPayBusinessApi.alipayTradeQuery("alipay", order.getId());
                paymentWXBusinessApi.weixinFindOrderMap("weixin", EntityConvert.convert(OrderDto.class, order));
                paymentUnionBusinessApi.unionPayQueryRequest("unionpay", order.getId(), true);
                paymentUnionBusinessApi.unionPayQueryRequest("unionpay", order.getId(), false);
            }
        } catch (Exception e) {
            LOGGER.info("第三方交易校验异常:" + e.toString());
        }
        return message.toString();
    }

    public String doPost(HttpServletRequest req, HttpServletResponse resp) throws Exception {
        String encoding = req.getParameter(SDKConstants.param_encoding);
        // 获取请求参数中所有的信息
        Map<String, String> reqParam = getUnionNotify(req);
        String result = paymentUnionBusinessApi.notifyPay(reqParam, encoding);
        return result;
    }

    private Map<String, String> getUnionNotify(HttpServletRequest req)  throws Exception{
        req.setCharacterEncoding("ISO-8859-1");
        String encoding = req.getParameter(SDKConstants.param_encoding);
        // 获取请求参数中所有的信息
        Map<String, String> reqParam = getAllRequestParam(req);

        return reqParam;
    }

    /**
     * 获取请求参数中所有的信息
     *
     * @param request
     * @return
     */
    private static Map<String, String> getAllRequestParam(final HttpServletRequest request) {
        Map<String, String> res = new HashMap<String, String>();
        Enumeration<?> temp = request.getParameterNames();
        if (null != temp) {
            while (temp.hasMoreElements()) {
                String en = (String) temp.nextElement();
                String value = request.getParameter(en);
                res.put(en, value);
                if (null == res.get(en) || "".equals(res.get(en))) {
                    res.remove(en);
                }
            }
        }
        return res;
    }

    /**
     * 获取用户剩余转盘次数
     * @param branchCode
     * @param merchantId
     * @return
     */
    private Integer getDrawCount(String branchCode, Long merchantId) {
        LOGGER.info("getDrawCount: branchCode:{} merchantId:{}", branchCode, merchantId);
        LuckDrawReq luckDrawReq = new LuckDrawReq();
        luckDrawReq.setBranchCode(branchCode);
        luckDrawReq.setMerchantId(merchantId);
        PromoResp<Integer> promoResp = null;
        try{
            promoResp = luckDrawApi.queryOrderLastCount(luckDrawReq);
        }catch (Exception e) {
            LOGGER.error("获取用户剩余转盘次数异常： getDrawCount {}",e);
            return 0;
        }
        LOGGER.info("getDrawCount: promoResp:{}", JSONObject.toJSONString(promoResp));
        if(promoResp == null || promoResp.getData() == null) {
            return 0;
        }
        return promoResp.getData();
    }

    /**
     * 获取大转盘送次数订单金额
     * @param branchCode
     * @param branchCode
     * @return
     */
    private LuckDrawResp luckDrawRespPromoResp(String branchCode) {
        try {
            LuckDrawReq luckDrawReq = new LuckDrawReq();
            luckDrawReq.setBranchCode(branchCode);
            LOGGER.info("luckDrawRespPromoResp request: {}",JSONObject.toJSONString(luckDrawReq));
            PromoResp<LuckDrawResp> luckDrawRespPromoResp = luckDrawApi.queryLuckDraw(luckDrawReq);
            LOGGER.info("luckDrawRespPromoResp request: {}",JSONObject.toJSONString(luckDrawRespPromoResp));
            if (luckDrawRespPromoResp.getCode() == 200){
                return luckDrawRespPromoResp.getData();
            }
        } catch (Exception e) {
            LOGGER.error("luckDrawRespPromoResp error",e);
        }

        return null;
    }



}