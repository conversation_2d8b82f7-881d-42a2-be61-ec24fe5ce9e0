package com.xyy.ec.pc.newfront.vo;

import lombok.Data;

@Data
public class AddPurchaseCalculationParam {


    /**
     * 起购数
     */
    private Integer leastPurchaseNum;


    /**
     * 中包装
     */
    private Integer mediumPackageNum;

    /**
     * 是否可拆零
     */
    private Boolean split;

    /**
     * 当前值
     */
    private Integer current;

    /**
     * 运算 规则 1 加 2 减 3 输入
     */
    private Integer rule;


}
