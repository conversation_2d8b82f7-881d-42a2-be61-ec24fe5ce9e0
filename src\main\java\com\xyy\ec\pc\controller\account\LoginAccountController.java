package com.xyy.ec.pc.controller.account;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.account.LoginAccountApi;
import com.xyy.ec.merchant.bussiness.api.account.WechatLoginApi;
import com.xyy.ec.merchant.bussiness.api.wxwork.WxWorkBusinessApi;
import com.xyy.ec.merchant.bussiness.base.ResultMessage;
import com.xyy.ec.merchant.bussiness.dto.account.LoginAccountDto;
import com.xyy.ec.merchant.bussiness.dto.account.WechatUserDto;
import com.xyy.ec.merchant.bussiness.enums.ResultCodeEnum;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.service.IdentityValidator;
import com.xyy.ec.pc.util.MobileValidateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

@Slf4j
@Controller
@RequestMapping("/merchant/account")
public class LoginAccountController extends BaseController {

    @Autowired
    private IdentityValidator identityValidator;

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    /**
     * 获取账号异地检查开关状态
     */
    @GetMapping("/getAccountCheckSwitchStatus")
    @ResponseBody
    public Object getAccountCheckSwitchStatus() {

        try {
            JwtPrincipal jwtPrincipal = (JwtPrincipal) identityValidator.currentPrincipal();
            ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.selectLoginAccountById(jwtPrincipal.getAccountId());
            if (apiRPCResult.isSuccess() && apiRPCResult.getData() != null) {
                LoginAccountDto loginAccountDto = apiRPCResult.getData();
                return addDataResult("checkSwitch", loginAccountDto.getCheckSwitch());
            }
            else {
                log.error("获取当前账号信息失败, {}", jwtPrincipal.getAccountId());
                return addError("获取当前账号信息失败");
            }
        }
        catch (Exception e) {
            log.error("获取账号异地检查开关状态异常", e);
            return addError("获取账号异地检查开关状态异常");
        }
    }

    /**
     * 开启或关闭账号异地检查开关
     */
    @PostMapping("/openOrCloseCheckSwitch")
    @ResponseBody
    public Object openOrCloseCheckSwitch() {

        try {
            JwtPrincipal jwtPrincipal = (JwtPrincipal) identityValidator.currentPrincipal();
            ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.selectLoginAccountById(jwtPrincipal.getAccountId());
            if (apiRPCResult.isSuccess() && apiRPCResult.getData() != null) {
                LoginAccountDto loginAccountDto = apiRPCResult.getData();
                Integer checkSwitch = loginAccountDto.getCheckSwitch();
                Integer newCheckSwitch = Constants.IS0.equals(checkSwitch) ? Constants.IS1 : Constants.IS0;
                loginAccountDto.setCheckSwitch(newCheckSwitch);
                ApiRPCResult<Boolean> result = loginAccountApi.updateCheckSwitchOrProvinceById(loginAccountDto);
                if (result.isSuccess() && result.getData()) {
                    return addDataResult("checkSwitch", newCheckSwitch);
                }
                else {
                    return addError(Constants.IS0.equals(checkSwitch) ? "开启失败" : "关闭失败");
                }
            }
            else {
                log.error("获取当前账号信息失败, {}", jwtPrincipal.getAccountId());
                return addError("获取当前账号信息失败");
            }
        } catch (Exception e) {
            log.error("账号异地检查开关状态修改异常", e);
            return addError("账号异地检查开关状态修改异常");
        }
    }
}
