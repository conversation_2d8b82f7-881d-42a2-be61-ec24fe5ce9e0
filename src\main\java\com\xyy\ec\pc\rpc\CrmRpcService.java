package com.xyy.ec.pc.rpc;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.crm.operation.api.api.forec.merchant.CrmFollowMerchantApi;
import com.xyy.crm.operation.api.dto.ec.PrivateCustomerDTO;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.user.UserMiddleBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.user.UserInfoDTO;
import com.xyy.ec.merchant.bussiness.enums.SystemFlagEnum;
import com.xyy.ec.pc.enums.CrmSkuCollectTypeEnum;
import com.xyy.ec.pc.model.BdInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.terracotta.offheapstore.HashingMap;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrmRpcService {

    @Reference(version = "1.0.0")
    private CrmFollowMerchantApi crmFollowMerchantApi;

    @Reference(version = "1.0.0")
    private UserMiddleBusinessApi userMiddleBusinessApi;

    public String getBdPhoneByMerchantId(Long merchantId) {
        if (merchantId == null) {
            return "";
        }
        ApiRPCResult<List<PrivateCustomerDTO>> merchantBdList = crmFollowMerchantApi.getMerchantBdList(Long.valueOf(merchantId));
        log.info("crmFollowMerchantApi.getMerchantBdList 获取客户 关联db信息={} ", JSONObject.toJSONString(merchantBdList));
        Long oaId=null;
        if(merchantBdList!=null&&merchantBdList.isSuccess()){
            List<PrivateCustomerDTO> pricateCustomerDTOList = merchantBdList.getData();
            if(CollectionUtils.isNotEmpty(pricateCustomerDTOList)) {
                for (PrivateCustomerDTO privateCustomerDTO : pricateCustomerDTOList) {
                    //取普药
                    if (privateCustomerDTO.getSkuCollectType() == 1) {
                        oaId = privateCustomerDTO.getOaId();
                        break;
                    }
                }
            }
        }
        if(oaId==null){
            return "";
        }
        List<Long>oaIds= new ArrayList<>();
        oaIds.add(oaId);
        List<UserInfoDTO> userInfoDTOList = null;
        try {
            userInfoDTOList = userMiddleBusinessApi.queryMeuserByOaIds(oaIds, SystemFlagEnum.XYY_EC_PC.getSystemFlag());
        } catch (Exception e) {
            log.error("调用 中台接口 获取用户信息异常", e);
        }
        log.info("userInfoDTOList={} ", JSONObject.toJSONString(userInfoDTOList));
        String phone="";
        if(CollectionUtils.isNotEmpty(userInfoDTOList)){
            phone=userInfoDTOList.get(0).getMobile();
        }
        return phone;
    }


    /**
     * 根据客户id查询BD信息，优先展示控销；其次展示普药
     * @param merchantId
     * @return
     */
    public BdInfoVo getBdInfoByMerchantId(Long merchantId) {
        try {
            log.info("crmFollowMerchantApi.getBdInfoByMerchantId 获取客户 merchantId={} ", merchantId);
            ApiRPCResult<List<PrivateCustomerDTO>> merchantBdList = crmFollowMerchantApi.getMerchantBdList(merchantId);
            log.info("crmFollowMerchantApi.getBdInfoByMerchantId 获取客户 关联db信息={} ", JSONObject.toJSONString(merchantBdList));
            if(Objects.isNull(merchantBdList) || merchantBdList.isFail() || CollectionUtils.isEmpty(merchantBdList.getData())){
                return null;
            }
            Map<Integer, PrivateCustomerDTO> dtoMap = merchantBdList.getData().stream()
                    //过滤一下防止以后添加第三种类型就出bug了
                    .filter(dto -> CrmSkuCollectTypeEnum.CONTROL.getType()==dto.getSkuCollectType()|| CrmSkuCollectTypeEnum.GENERAL.getType()==dto.getSkuCollectType())
                    .collect(Collectors.toMap(PrivateCustomerDTO::getSkuCollectType, Function.identity(),(o,n) -> n));
            if(MapUtils.isEmpty(dtoMap)){
                return null;
            }
            PrivateCustomerDTO customerDTO = dtoMap.getOrDefault(CrmSkuCollectTypeEnum.CONTROL.getType(), dtoMap.get(CrmSkuCollectTypeEnum.GENERAL.getType()));
            if(Objects.isNull(customerDTO)){
                return null;
            }
            Long oaId = customerDTO.getOaId();
            log.info("crmFollowMerchantApi.getBdInfoByMerchantId 获取客户 oaId={} ", oaId);
            List<UserInfoDTO> userInfoDTOList = userMiddleBusinessApi.queryMeuserByOaIds(Collections.singletonList(oaId), SystemFlagEnum.XYY_EC_PC.getSystemFlag());
            log.info("crmFollowMerchantApi.getBdInfoByMerchantId 获取客户 关联userInfo信息={} ", JSONObject.toJSONString(userInfoDTOList));
            if(CollectionUtils.isEmpty(userInfoDTOList)){
                return null;
            }
            UserInfoDTO userInfoDTO = userInfoDTOList.get(0);

            BdInfoVo vo = new BdInfoVo();
            vo.setSalesName(CrmSkuCollectTypeEnum.getSalesName(customerDTO.getSkuCollectType(),userInfoDTO.getRealname()));
            vo.setSalesPhone(userInfoDTO.getMobile());
            return vo;
        } catch (Exception e) {
            log.info("crmFollowMerchantApi.getBdInfoByMerchantId error merchantId={} ", merchantId, e);
            return null;
        }
    }
}
