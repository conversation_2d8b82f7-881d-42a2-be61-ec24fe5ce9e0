/**
 * Created by Administrator on 2016/12/21.
 */
var step = 10;
var cartUrlPrefix ="/merchant/center/cart";
var merchantId = $("#merchantId").val();
var imgsrc = $("#imgSrc").val();

var platformVoucher = function () {
    return this;
};

$(function() {
    /* 显示收藏*/
    $(".mrth li").hover(
        function() {
            $(this).find(".showorno").css("display", "block");
        },
        function() {
            $(this).find(".showorno").css("display", "none");
        }
    );
    /*初始化定位*/
    setTimeout(initpositon,0);
    /*滚动置顶*/
    $(window).scroll(function() {
        initpositon();
    });

    /*减操作*/
    $(".sub").click(function() {

    	var type=0;
    	var step = 1;
        var me = $(this),txt = me.next(":text");
        var val = parseFloat(txt.val());
        var pid = txt.attr("pid");
        var packageId = txt.attr("packageId");
        var isSplit = txt.attr("isSplit");
		var middpacking = txt.attr("middpacking");
        if(packageId != null && packageId !=''){
        	pid = packageId;
        	type = 1;
        }else{
        	if(isSplit == 0){
    			step = parseFloat(middpacking);
    		}
        }
        if(val == 1) {
            remove(pid);
        } else {
            subCart(pid,type, val,step, function(result){
                if(result.status=="success"){
                    if (result.data.message) {
                        if (result.data.dialogStyle == 20) {
                            $.alert({
                                title: '提示',
                                body: result.data.message,
                                okBtn : '我知道了'
                            });
                        } else {
                            layer.msg(result.data.message, {
                                icon: 6,
                                time: 2000
                            });
                        }
                    }
                    refreshCart();
                }else{
                    $.alert(result.errorMsg);
                }
            });
        }
    });
    /*加操作
     * 不管是否中包装： 都按照中包装数量去加
     * */
    $(".add").click(function() {

    	var type=0;
        var me = $(this), txt = me.prev(":text");
        var val = parseFloat(txt.val());
        var step =10;
        // txt.val(val + step);
        // var amount = val + step;
        var isSplit = txt.attr("isSplit");
		var middpacking = txt.attr("middpacking");
        var pid = txt.attr("pid");
        var packageId = txt.attr("packageId");
        if(packageId){
        	pid = packageId;
        	type = 1;
        	step=1;
        }else{
        	step = parseFloat(middpacking);
        }
        addCart(pid,type, val,step, function(result){
            if(result.status=="success"){
                if (result.data.message) {
                    if (result.data.dialogStyle == 20) {
                        $.alert({
                            title: '提示',
                            body: result.data.message,
                            okBtn : '我知道了'
                        });
                    } else {
                        layer.msg(result.data.message, {
                            icon: 6,
                            time: 2000
                        });
                    }
                }
                // $(".main").load(cartUrlPrefix + "/list.htm");
                refreshCart();
            }else{
                $.alert(result.errorMsg);
            }
        });
        /*$.ajax({
            url: "changeCart.json",
            type: "POST",
            dataType: "json",
            data: {
                skuId: pid,
                amount: amount
            },
            success: function(result){
                if(result.status=="success"){
                    console.log(result.data.message);
                    if (result.data.message) {
                        layer.msg(result.data.message, {
                            icon: 6,
                            time: 2000
                        });
                    }
                    $(".main").load("list.htm");
                }else{
                    alert(result.errorMsg);
                }
            }
        });*/
    });

    /*全选*/
    $(".all ").click(function(event){
        event.preventDefault();
        // var $checkbox = $(".checkbox-pretty").checkbox();
        var index = layer.load(2);
        if($(this).hasClass("checked")){
            $.ajax({
                url: cartUrlPrefix + "/cancelAllItem",
                type: "POST",
                dataType: "json",
                success: function(result){
                    if(result.status=="success"){
                        refreshCart();
                    }else{
                        $.alert(result.errorMsg);
                    }
                    layer.close(index);
                }
            });
        }else{
            $.ajax({
                url: cartUrlPrefix + "/selectAllItem",
                type: "POST",
                dataType: "json",
                success: function(result){
                    if(result.status=="success"){
                        refreshCart();
                    }else{
                        $.alert(result.errorMsg);
                    }
                    layer.close(index);
                }
            });
        }
        // refreshCart();
    });

    /*控制是否全选*/
    $(".bodybox .putong").click(function(event){
        event.preventDefault();
        var $checkbox = $(this).checkbox();
        var $checkbox_con = $(".all").checkbox();

        var index = layer.load(2);
        var id = $checkbox.find("input").val();
        //var id = $checkbox.find("input[name='invalid']").val();
        var data ={
    			skuId: id
    		};
        
        if($(this).hasClass("checked")){
            $.ajax({
                url: cartUrlPrefix + "/cancelItem",
                type: "POST",
                dataType: "json",
                data: data,
                success: function(result){
                    if(result.status=="success"){
                        refreshCart();
                    }else{
                        $.alert(result.errorMsg);
                    }
                    layer.close(index);
                }
            });
        }else{
            $.ajax({
                url: cartUrlPrefix + "/selectItem",
                type: "POST",
                dataType: "json",
                data: {
                    skuId: id
                },
                success: function(result){
                    if(result.status=="success"){
                        refreshCart();
                    }else{
                        $.alert(result.errorMsg);
                    }
                    layer.close(index);
                }
            });
        };

        $(".bodybox .putong").each(function(){
            if($(this).hasClass("checked")){
                $checkbox_con.checkbox("check");
            }else{
                $checkbox_con.checkbox("uncheck");
                return false;
            }
        });

        /*控制背景颜色*/
        setColor();
    });

    // 优惠券滑出
    $('.yhq-button').click(function(){
        $.ajax({
            url: cartUrlPrefix + "/selectCartVoucher",
            type: "POST",
            dataType: "json",
            success: function(data) {
                if(data.status=="success"){
                    if(data.voucherDTOS){
                        var html = '';
                        var link = "";
                         data.voucherDTOS.forEach(function(element, index){
                             if (element.noEnoughMoney >0) {
                                 element.noEnoughMoneyStr = "还差 <span class='yhq-worse'>"+element.noEnoughMoney+"</span> 元可用";
                             } else {
                                 element.noEnoughMoneyStr = "已满足可用条件";
                             }
                             if(element.pcUrl){
                                 link = element.pcUrl;
                             }else{
                                 link = "/voucher/centre/findVoucherSku.htm?voucherTemplateId="+element.voucherTemplateId+"&source=2"
                             }
                             html += '<li> <div class="yhq-yhq"> <div class="yhq-lb"> <div class="yhq-lb-top"> <span class="fuhao">￥</span><span class="price">' +
                                 element.moneyInVoucher +
                                 '</span> </div> <div class="yhq-lb-foot">' +
                                 element.minMoneyToEnableDesc +
                                 ' </div> <div class="yhq-lb-foot">' + element.maxMoneyInVoucherDesc + '</div> </div> <div class="yhq-rb"> <div class="yhq-rb-top"> <span class="quan">商品券</span><span class="info" title="'+element.voucherTitle+'">' +
                                 element.voucherTitle +
                                 '</span> </div> <div style=" height:30px;overflow:hidden;"> </div><div class="yhq-rb-foot"> <span>' +
                                 element.validDateToString +
                                 '--' +
                                 element.expireDateToString +
                                 '</span> <a href="'+ link +'">去凑单</a> </div> </div> </div> <div class="yhq-tishi">已选' +
                                 element.isSelectSkuNum +
                                 '件，小计' +
                                 element.selectSkuAmount +
                                 '元，' +
                                 element.noEnoughMoneyStr
                                 + '</div> </li>';
                         })
                        $("#cartVoucherInfo").html(html);
                    };
                }else{
                    $.alert(data.errorMsg);
                }
            },
            error: function(XMLHttpRequest, textStatus, errorThrown){
                //$.alert('网络异常');
                $("#cartVoucherInfo").html('网络异常');
                $("#cartVoucherInfo").css({
                "text-align": "center",
                "color": "red"
                })
            }
        })
        showOverlay()
        $(".cg-yhq").slideDown();
    });

    /*平台券开始*/
        // 平台券滑出
        $('.platform-yhq-box').click(function(e){
            e.stopPropagation();
        
            var $that = $(this).find(".yhq-button-platform");
            var $panel = $(this).find(".cg-yhq-new");
            var $target = $(e.target);
        
            var canClick = $target.hasClass("yhq-button-platform") || $target.hasClass("core");
        
            // 点击领取按钮
            if ($target.hasClass("lingqu")) {
                receivePlatformVoucher(e);
            }
        
            // 点击 checkbox 或隐藏域
            if (e.target.type == "checkbox" || e.target.type == "hidden") {
                selectAndCancelPlatform($that, e.target);
            }
        
            // 展开或收起逻辑
            if (canClick) {
                if ($panel.is(":visible")) {
                    $panel.slideUp(); // 如果当前已经展开，则收起
                } else {
                    $(".cg-yhq-new").slideUp(); // 收起所有
                    $panel.slideDown();         // 展开当前
                    getPlatformVoucher($that);  // 加载数据
                }
            }
        });
        //获取平台券-弹窗
        function getPlatformVoucher(that){
            $.ajax({
                url: cartUrlPrefix + "/selectCartVoucher?type=2&merchantId="+merchantId,
                type: "POST",
                dataType: "json",
                success: function(data) {
                    if(data.status=="success"){
                        //已领取优惠券
                        if(data.data.availableVoucherList){
                            var html = '',spHtml = '';
                            var link = "";
                            var isZheKou = "";
                            if(data.data.availableVoucherList.length){
                                that.parent(".platform-yhq-box").find(".address-top-title1").html('<span class="yi-get">已领取优惠券</span>以下为已领取可使用优惠券');
                                data.data.availableVoucherList.forEach(function(element, index){
                                    spHtml = ''; var showSp = "";
                                    if(element.voucherState == 1){
                                        isZheKou = `<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}" style="font-size:22px;">`+element.moneyInVoucher+`</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}" style="font-size:17px;">折</span>`;
                                    }else{
                                        isZheKou = `<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}">￥</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}">` + element.moneyInVoucher + '</span>';
                                    }
                                    if(element.pcUrl){
                                        link = element.pcUrl;
                                    }else{
                                        link = "/voucher/centre/findVoucherSku.htm?voucherTemplateId="+element.voucherTemplateId+"&source=2"
                                    }
                                    //循环商品
                                    if(element.cartProductList && element.cartProductList.length){
                                        element.cartProductList.forEach(function(item, index){
                                            if(item.status == 1){
                                                var checkStatus = "checked"
                                            }else{
                                                checkStatus = ""
                                            }
                                            spHtml += '<div class="yhq-sp-item">' +
                                                '<p class="img-box">' +
                                                '<img src="'+ imgsrc + '/ybm/product/min/' + item.imageUrl +'" alt="" onerror="this.src=\'/static/images/default-big.png\'">' +
                                                '<label class="yhq-sp-check putong inline ' + checkStatus + '">' +
                                                '<input type="hidden" class="selectBox" checked="' + checkStatus + '" name="' + checkStatus + '" value="' + item.skuId + '" packageId="'+item.packageId+'"/>' +
                                                '<input type="checkbox" class="selectBox" name="' + checkStatus + '" value="' + item.skuId + '" packageId="'+item.packageId+'" ' + checkStatus + '>' +
                                                '<span></span>' +
                                                ' </label>' +
                                                '</p>' +
                                                '<p class="red">¥' + item.price + '</p>' +
                                                '<p>x' + item.amount + '</p>' +
                                                '</div>'
                                        })
                                        showSp = element.title;
                                    }

                                    html += '<li style="height:auto;float: none;overflow:hidden;"> <div class="yhq-yhq"> <div class="yhq-lb"> <div class="yhq-lb-top">' +
                                        isZheKou  +
                                        `</div> <div class="yhq-lb-foot ${element.voucherType == 9 ? 'yhq-lb-foot-zp' : ''}">` +
                                        element.minMoneyToEnableDesc +
                                        ` </div> <div class="yhq-lb-foot ${element.voucherType == 9 ? 'yhq-lb-foot-zp' : ''}">` + element.maxMoneyInVoucherDesc + `</div> </div> <div class="yhq-rb"> <div class="yhq-rb-top"> <span class="quan quan-shop ${element.voucherType == 9 ? 'quan-zhuanpin' : ''}">`+ element.voucherTypeDesc+'</span><span class="info" title="'+element.shopName+'">' +
                                        element.shopName +
                                        '</span> </div> <div style=" height:30px;overflow:hidden;line-height:30px;">'+ element.voucherTitle +'</div><div class="yhq-rb-foot"> <span>'
                                        +element.voucherScope+
                                        '</span> <a href="'+ link +'">去凑单</a> </div><div style="font-size:12px;color:#999999;">'+element.validDateToString +
                                        '--' +
                                        element.expireDateToString +'</div> </div> </div> <div class="yhq-tishi">' +
                                        showSp + '</div><div class="yhq-sp-box"> '+spHtml+'</div></li>';
                                });
                                that.parent(".platform-yhq-box").find("#cartVoucherInfo-new-yi-platform").html(html);
                            }
                        };
                        //未领取优惠券
                        if(data.data.unclaimedVoucherList){
                            var html = '';
                            var isZheKou = "";                        
                            if(data.data.unclaimedVoucherList.length){
                                that.parent(".platform-yhq-box").find(".address-top-title").html('<span class="wei-get">可领取优惠券</span>领取后可用于购物车商品');
                                data.data.unclaimedVoucherList.forEach(function(element, index){
                                    if(element.voucherState == 1){
                                        isZheKou = `<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}" style="font-size:22px;">`+element.moneyInVoucher+`</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}" style="font-size:17px;">折</span>`;
                                    }else{
                                        isZheKou = `<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}">￥</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}">` + element.moneyInVoucher + '</span>';
                                    }
                                    html += '<li style="height:auto;"> ' +
                                        '<div class="yhq-yhq"> ' +
                                        '<div class="yhq-lb"> ' +
                                        '<div class="yhq-lb-top"> ' +
                                        isZheKou +
                                        '</div> ' +
                                        `<div class="yhq-lb-foot ${element.voucherType == 9 ? 'yhq-lb-foot-zp' : ''}">` +
                                        element.minMoneyToEnableDesc +
                                        '</div> ' +
                                        `<div class="yhq-lb-foot ${element.voucherType == 9 ? 'yhq-lb-foot-zp' : ''}">` +
                                        element.maxMoneyInVoucherDesc +
                                        '</div> ' +
                                        '</div> ' +
                                        '<div class="yhq-rb"> ' +
                                        '<div class="yhq-rb-top"> ' +
                                        `<span class="quan quan-shop ${element.voucherType == 9 ? 'quan-zhuanpin' : ''}">`+ element.voucherTypeDesc+'</span>' +
                                        '<span class="info" title="'+element.shopName+'">' + element.shopName + '</span> ' +
                                        '</div> ' +
                                        '<div style=" height:30px;overflow:hidden;line-height:30px;">'+ element.voucherTitle +'</div>' +
                                        '<div class="yhq-rb-foot"> ' +
                                        '<span>'+element.voucherScope+'</span> <a href="javascript:void(0)" id="'+ element.voucherTemplateId +`" class="notling lingqu ${element.voucherType == 9 ? 'zp-a' : ''}">立即领取</a> ` +
                                        '</div>' +
                                        '<div style="font-size:12px;color:#999999;">'+element.validDateToString + '--' + element.expireDateToString +'</div> ' +
                                        '</div> ' +
                                        '</div> ' +
                                        '</li>';
                                });
                                that.parent(".platform-yhq-box").find("#cartVoucherInfo-new-platform").html(html);
                            }
                        };
                    }else{
                        $.alert(data.errorMsg);
                    }
                },
                error: function(XMLHttpRequest, textStatus, errorThrown){
                    //$.alert('网络异常');
                    that.parent(".yhq-box").find("#cartVoucherInfo-new").html('网络异常');
                    that.parent(".yhq-box").find("#cartVoucherInfo-new").css({
                        "text-align": "center",
                        "color": "red"
                    })
                }
            })
        }
        //领取平台券
        function receivePlatformVoucher(e){
            // e.stopPropagation();
            if (merchantId && merchantId > 0) {
                $.ajax({
                    url:  "/merchant/center/voucher/receiveVoucher",
                    type: "POST",
                    dataType: "json",
                    data: {
                        merchantId: merchantId,
                        voucherTemplateId: e.target.id
                    },
                    success: function (result) {
                        if (result.status == "success") {
                            e.target.outerHTML = '<a href="javascript:void(0)" id="6481" class="notling">已领取</a>';

                            // window.location.reload();
                        } else {
                            $.alert({
                                title: '提示',
                                body: result.errorMsg,
                                okHidden: function (e) {
                                    // location.reload();
                                }
                            });
                        }
                    },
                    error: function () {
                        $.alert({
                            title: '提示',
                            body: '因为某些原因导致优惠券领取异常哟!'
                        });
                    }
                });
            } else {
                $.alert({
                    title: '提示',
                    body: '您还没有登录，请先登录!',
                    okHidden: function (e) {
                        window.location.href = "/login/login.htm?redirectUrl=/";
                    }
                });
            }
        }
        //平台券弹窗中商品选中/取消
        function selectAndCancelPlatform(that,self){
            var checkStatus;
            var packageId =$(self).attr("packageId");
            if (packageId == 'null' || packageId == 'undefined') {
                packageId="";
            }
            if(self.name){
                checkStatus = 0;
            }else{
                checkStatus = 1;
            }
            $.ajax({
                url: cartUrlPrefix + "/voucher/changeCart",
                type: "POST",
                dataType: "json",
                data:{
                    merchantId : merchantId,
                    skuId : self.value,
                    packageId : packageId,
                    status : checkStatus,
                    type : 2
                },
                success: function(result){
                    if(result.status=="success"){
                        getPlatformVoucher(that);
                    }else{
                        $.alert(result.errorMsg);
                    }
                }
            });
        }
    /*平台券结束*/

    // 新优惠券滑出
    $('.yhq-box').click(function(e) {
        e.stopPropagation();
    
        var $that = $(this).find(".yhq-button-new");
        var $target = $(e.target);
        var $panel = $(this).find(".cg-yhq-new");
    
        var shopCode = $that.attr("shopCode");
    
        var canClick = $target.hasClass("yhq-button-new") || $target.hasClass("core");
    
        // 领取优惠券
        if ($target.hasClass("lingqu")) {
            receiveVoucher(e);
        }
    
        // 选中/取消商品
        if (e.target.type === "checkbox" || e.target.type === "hidden") {
            selectAndCancel($that, e.target);
        }
    
        // 展开 / 收起
        if (canClick) {
            if ($panel.is(":visible")) {
                $panel.slideUp(); // 当前已展开，再次点击则收起
            } else {
                $(".cg-yhq-new").slideUp(); // 收起所有
                $panel.slideDown();         // 展开当前
                getVoucher($that);          // 拉取数据
            }
        }
    });
    

    //获取店铺优惠券-弹窗
    function getVoucher(that){
        var shopCode =that.attr("shopCode");
        $.ajax({
            url: cartUrlPrefix + "/selectCartVoucher?type=1&shopCode="+shopCode,
            type: "POST",
            dataType: "json",
            success: function(data) {
                if(data.status=="success"){
                                    //已领取优惠券
                    if(data.data.availableVoucherList){
                        var html = '',spHtml = '';
                        var link = "";
                        var isZheKou = ""
                        if(data.data.availableVoucherList.length){
                            that.parent(".yhq-box").find(".address-top-title1").html('<span class="yi-get">已领取优惠券</span>以下为已领取可使用优惠券');
                            data.data.availableVoucherList.forEach(function(element, index){
                                spHtml = '';
                                if(element.voucherState == 1){
                                    isZheKou = `<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}" style="font-size:22px;">`+element.moneyInVoucher+`</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}" style="font-size:17px;">折</span>`;
                                }else{
                                    isZheKou = `<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}">￥</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}">` + element.moneyInVoucher + '</span>';
                                }
                                if(element.pcUrl){
                                    link = element.pcUrl;
                                }else{
                                    link = "/voucher/centre/findVoucherSku.htm?voucherTemplateId="+element.voucherTemplateId+"&source=2"
                                }
                                //循环商品
                                element.cartProductList.forEach(function(item, index){
                                    if(item.status == 1){
                                        var checkStatus = "checked"
                                    }else{
                                        checkStatus = ""
                                    }
                                    spHtml += '<div class="yhq-sp-item">' +
                                        '<p class="img-box">' +
                                        '<img src="'+ imgsrc + '/ybm/product/min/' + item.imageUrl +'" alt="" onerror="this.src=\'/static/images/default-big.png\'">' +
                                        '<label class="yhq-sp-check putong inline ' + checkStatus + '">' +
                                        '<input type="hidden" class="selectBox" checked="' + checkStatus + '" name="' + checkStatus + '" value="' + item.skuId + '" packageId="'+item.packageId+'"/>' +
                                        '<input type="checkbox" class="selectBox" name="' + checkStatus + '" value="' + item.skuId + '" packageId="'+item.packageId+'" ' + checkStatus + '>' +
                                        '<span></span>' +
                                        ' </label>' +
                                        '</p>' +
                                        '<p class="red">¥' + item.price + '</p>' +
                                        '<p>x' + item.amount + '</p>' +
                                        '</div>'
                                })

                                html += '<li style="height:auto;float: none;"> <div class="yhq-yhq"> <div class="yhq-lb"> <div class="yhq-lb-top">' +
                                    isZheKou  +
                                    `</div> <div class="yhq-lb-foot ${element.voucherType == 9 ? 'yhq-lb-foot-zp' : ''}">` +
                                    element.minMoneyToEnableDesc +
                                    ` </div> <div class="yhq-lb-foot ${element.voucherType == 9 ? 'yhq-lb-foot-zp' : ''}">` + element.maxMoneyInVoucherDesc + `</div> </div> <div class="yhq-rb"> <div class="yhq-rb-top"> <span class="quan quan-shop ${element.voucherType == 9 ? 'quan-zhuanpin' : ''}">`+ element.voucherTypeDesc+'</span><span class="info" title="'+element.shopName+'">' +
                                    element.shopName +
                                    '</span> </div> <div style=" height:30px;overflow:hidden;line-height:30px;">'+ element.voucherTitle +'</div><div class="yhq-rb-foot"> <span>'
                                    +element.voucherScope+
                                    '</span> <a href="'+ link +'">去凑单</a> </div><div style="font-size:12px;color:#999999;">'+element.validDateToString +
                                    '--' +
                                    element.expireDateToString +'</div> </div> </div> <div class="yhq-tishi">' +
                                    element.title + '</div><div class="yhq-sp-box"> '+spHtml+'</div></li>';
                            });
                            that.parent(".yhq-box").find("#cartVoucherInfo-new-yi").html(html);
                        }
                    };
                    //未领取优惠券
                    if(data.data.unclaimedVoucherList){
                        var html = '';
                        var isZheKou = ""
;                        if(data.data.unclaimedVoucherList.length){
                            that.parent(".yhq-box").find(".address-top-title").html('<span class="wei-get">可领取优惠券</span>领取后可用于购物车商品');
                            data.data.unclaimedVoucherList.forEach(function(element, index){
                                if(element.voucherState == 1){
                                    isZheKou = `<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}" style="font-size:22px;">`+element.moneyInVoucher+`</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}" style="font-size:17px;">折</span>`;
                                }else{
                                    isZheKou = `<span class="fuhao ${element.voucherType == 9 ? 'zp-fuhao' : ''}">￥</span><span class="price ${element.voucherType == 9 ? 'zp-price' : ''}">` + element.moneyInVoucher + '</span>';
                                }
                                html += '<li style="height:auto;"> ' +
                                            '<div class="yhq-yhq"> ' +
                                                '<div class="yhq-lb"> ' +
                                                    '<div class="yhq-lb-top"> ' +
                                                        isZheKou +
                                                    '</div> ' +
                                                    `<div class="yhq-lb-foot ${element.voucherType == 9 ? 'yhq-lb-foot-zp' : ''}">` +
                                                        element.minMoneyToEnableDesc +
                                                    '</div> ' +
                                                    `<div class="yhq-lb-foot ${element.voucherType == 9 ? 'yhq-lb-foot-zp' : ''}">` +
                                                        element.maxMoneyInVoucherDesc +
                                                    '</div> ' +
                                                '</div> ' +
                                                '<div class="yhq-rb"> ' +
                                                    '<div class="yhq-rb-top"> ' +
                                                        `<span class="quan quan-shop ${element.voucherType == 9 ? 'quan-zhuanpin' : ''}">`+ element.voucherTypeDesc+'</span>' +
                                                        '<span class="info" title="'+element.shopName+'">' + element.shopName + '</span> ' +
                                                    '</div> ' +
                                                    '<div style=" height:30px;overflow:hidden;line-height:30px;">'+ element.voucherTitle +'</div>' +
                                                        '<div class="yhq-rb-foot"> ' +
                                                            '<span>'+element.voucherScope+'</span> <a href="javascript:void(0)" id="'+ element.voucherTemplateId +`" class="notling lingqu ${element.voucherType == 9 ? 'zp-a' : ''}">立即领取</a> ` +
                                                        '</div>' +
                                                        '<div style="font-size:12px;color:#999999;">'+element.validDateToString + '--' + element.expireDateToString +'</div> ' +
                                                    '</div> ' +
                                                '</div> ' +
                                            '</li>';
                            });
                            that.parent(".yhq-box").find("#cartVoucherInfo-new").html(html);
                        }
                    };
                }else{
                    $.alert(data.errorMsg);
                }
            },
            error: function(XMLHttpRequest, textStatus, errorThrown){
                //$.alert('网络异常');
                that.parent(".yhq-box").find("#cartVoucherInfo-new").html('网络异常');
                that.parent(".yhq-box").find("#cartVoucherInfo-new").css({
                    "text-align": "center",
                    "color": "red"
                })
            }
        })
    }

    //领取优惠券
    function receiveVoucher(e){
        // e.stopPropagation();
        if (merchantId && merchantId > 0) {
            $.ajax({
                url:  "/merchant/center/voucher/receiveVoucher",
                type: "POST",
                dataType: "json",
                data: {
                    merchantId: merchantId,
                    voucherTemplateId: e.target.id
                },
                success: function (result) {
                    if (result.status == "success") {
                        e.target.outerHTML = '<a href="javascript:void(0)" id="6481" class="notling">已领取</a>';

                        // window.location.reload();
                    } else {
                        $.alert({
                            title: '提示',
                            body: result.errorMsg,
                            okHidden: function (e) {
                                // location.reload();
                            }
                        });
                    }
                },
                error: function () {
                    $.alert({
                        title: '提示',
                        body: '因为某些原因导致优惠券领取异常哟!'
                    });
                }
            });
        } else {
            $.alert({
                title: '提示',
                body: '您还没有登录，请先登录!',
                okHidden: function (e) {
                    window.location.href = "/login/login.htm?redirectUrl=/";
                }
            });
        }
    }

    //优惠券弹窗中商品选中/取消
    function selectAndCancel(that,self){
        var checkStatus;
        var shopCode =that.attr("shopCode");
        // console.log($(self),self)
        var packageId =$(self).attr("packageId");
        if (packageId == 'null' || packageId == 'undefined') {
            packageId="";
        }
        // var s = self.attributes["checked"];
        if(self.name){
            checkStatus = 0;
        }else{
            checkStatus = 1;
        }
        $.ajax({
            url: cartUrlPrefix + "/voucher/changeCart",
            type: "POST",
            dataType: "json",
            data:{
                merchantId : merchantId,
                shopCode : shopCode,
                skuId : self.value,
                packageId : packageId || '',
                status : checkStatus,
                type : 1
            },
            success: function(result){
                if(result.status=="success"){
                    getVoucher(that);
                }else{
                    $.alert(result.errorMsg);
                }
            }
        });
    }

    // 优惠券收起
    $('.cg-yhq .icon-tb-close').click(function(){
        hideOverlay()
        $(".cg-yhq").slideUp();
    })

    // 优惠券收起
    $('.cg-yhq-new .icon-tb-close').click(function(){
        hideOverlayNew()
        $(".cg-yhq-new").slideUp();
    })

    // 模态框点击收起
    $('#overlay').click(function(){
        hideOverlay()
        $(".cg-yhq").slideUp();
    })
    // 模态框点击收起
    $('#overlay-new').click(function(){
        hideOverlayNew()
        $(".cg-yhq-new").slideUp();
    })
    
    // $(".bodybox .taocan").click(function(event){
    //     event.preventDefault();
    //     var $checkbox = $(this).checkbox();
    //     var $checkbox_con = $(".all").checkbox();
    //
    //     var index = layer.load(2);
    //     var id = $checkbox.find("input").val();
    //     var data ={
    // 			packageId: id
    // 		};
    //
    //
    //     if($(this).hasClass("checked")){
    //         $.ajax({
    //             url: cartUrlPrefix + "/cancelItem",
    //             type: "POST",
    //             dataType: "json",
    //             data: data,
    //             success: function(result){
    //                 if(result.status=="success"){
    //                     refreshCart();
    //                 }else{
    //                     $.alert(result.errorMsg);
    //                 }
    //                 layer.close(index);
    //             }
    //         });
    //     }else{
    //         $.ajax({
    //             url: cartUrlPrefix + "/selectItem",
    //             type: "POST",
    //             dataType: "json",
    //             data: data,
    //             success: function(result){
    //                 if(result.status=="success"){
    //                     refreshCart();
    //                 }else{
    //                     $.alert(result.errorMsg);
    //                 }
    //                 layer.close(index);
    //             }
    //         });
    //     };
    //
    //     $(".bodybox .taocan").each(function(){
    //         if($(this).hasClass("checked")){
    //             $checkbox_con.checkbox("check");
    //         }else{
    //             $checkbox_con.checkbox("uncheck");
    //             return false;
    //         }
    //     });
    //
    //     /*控制背景颜色*/
    //     setColor1();
    // });

    //加载页面后判断是否全选
    $(".bodybox  .taocan.valid,.putong.valid").each(function(){
        var $checkbox_con = $(".all").checkbox();
        if($(this).hasClass("checked")){
            $checkbox_con.checkbox("check");
        }else{
            $checkbox_con.checkbox("uncheck");
            return false;
        }
    });
    $('label').click(function(event){
        event.preventDefault();
        $(this).toggleClass('checked')
    })
    $('label.all').click(function(event){
        event.preventDefault();
        var index = layer.load(2);
        var that =$(this);
        if(that.hasClass("checked")){
            // 若爷级全选选中，那么所有input框都被选中，并且添加checked类名
            $('label ').addClass('checked')
            $.ajax({
                url: cartUrlPrefix + "/selectAllItem",
                type: "POST",
                dataType: "json",
                success: function(result){
                    if(result.status=="success"){
                        refreshCart();
                    }else{
                        $.alert(result.errorMsg);
                    }
                    layer.close(index);
                }
            });
        }else{
            // 若爷级全选取消，那么所有input框都被取消，并且取消checked类名
            $('label ').removeClass('checked');
            $.ajax({
                url: cartUrlPrefix + "/cancelAllItem",
                type: "POST",
                dataType: "json",
                success: function(result){
                    if(result.status=="success"){
                        refreshCart();
                    }else{
                        $.alert(result.errorMsg);
                    }
                    layer.close(index);
                }
            });
        }
        setColor()
    })
    $('.zyclick').click(function(event){
        event.preventDefault();
        var that =$(this);
        var index = layer.load(2);
        var value1 =that.children('input:checkbox').val()

        if(that.hasClass("checked")){
            // 若父级全选选中，那么所有该父级下层的input框都被选中，并且添加checked类名
            $('input:checkbox[name="'+value1+'"]').parent().addClass('checked')
        }else{
            // 若父级全选取消，那么所有该父级下层的input框都被取消，并且取消checked类名
            $('input:checkbox[name="'+value1+'"]').parent().removeClass('checked');
        }
        // 所有父级input框的数量
        var numA =$('.cgd-qy label').length;
        // 所有父级input框已被选中的数量
        var numB =$('.cgd-qy label.checked').length;
        if(numA==numB){
            // 若所有父级input框的数量=所有父级input框已被选中的数量，那么爷级全选被选中，并添加checked类名
            $('.all').addClass('checked')
        }else{
            // 若所有父级input框的数量!=所有父级input框已被选中的数量，那么爷级全选被取消，并删除checked类名
            $('.all').removeClass('checked')
        }
        setColor()
        if(!$(this).hasClass("checked")){
            $.ajax({
                url: cartUrlPrefix + "/cancelAllItem?orgId="+value1+"&isThirdCompany="+$(this).attr("company"),
                type: "POST",
                dataType: "json",
                success: function(result){
                    if(result.status=="success"){
                        refreshCart();
                    }else{
                        $.alert(result.errorMsg);
                    }
                    layer.close(index);
                }
            });
        }else{
            $.ajax({
                url: cartUrlPrefix + "/selectAllItem?orgId="+value1+"&isThirdCompany="+$(this).attr("company"),
                type: "POST",
                dataType: "json",
                success: function(result){
                    if(result.status=="success"){
                        refreshCart();
                    }else{
                        $.alert(result.errorMsg);
                    }
                    layer.close(index);
                }
            });
        }
        layer.close(index);

    })
    $('.outerClick').click(function(event){
        event.preventDefault();
        var that =$(this);
        var index = layer.load(2);
        that.parent('.list-ziying').find('label').addClass('checked')
        // var value1 =that.children('input:checkbox').val()
        //
        // if(that.hasClass("checked")){
        //     // 若父级全选选中，那么所有该父级下层的input框都被选中，并且添加checked类名
        //     $('input:checkbox[name="'+value1+'"]').parent().addClass('checked')
        // }else{
        //     // 若父级全选取消，那么所有该父级下层的input框都被取消，并且取消checked类名
        //     $('input:checkbox[name="'+value1+'"]').parent().removeClass('checked');
        // }
        // 所有父级input框的数量
        // var numA =$('.cgd-qy label').length;
        // // 所有父级input框已被选中的数量
        // var numB =$('.cgd-qy label.checked').length;
        // if(numA==numB){
        //     // 若所有父级input框的数量=所有父级input框已被选中的数量，那么爷级全选被选中，并添加checked类名
        //     $('.all').addClass('checked')
        // }else{
        //     // 若所有父级input框的数量!=所有父级input框已被选中的数量，那么爷级全选被取消，并删除checked类名
        //     $('.all').removeClass('checked')
        // }
        // setColor()
        // if(!$(this).hasClass("checked")){
        //     $.ajax({
        //         url: cartUrlPrefix + "/cancelAllItem?orgId="+value1+"&isThirdCompany="+$(this).attr("company"),
        //         type: "POST",
        //         dataType: "json",
        //         success: function(result){
        //             if(result.status=="success"){
        //                 refreshCart();
        //             }else{
        //                 $.alert(result.errorMsg);
        //             }
        //             layer.close(index);
        //         }
        //     });
        // }else{
        //     $.ajax({
        //         url: cartUrlPrefix + "/selectAllItem?orgId="+value1+"&isThirdCompany="+$(this).attr("company"),
        //         type: "POST",
        //         dataType: "json",
        //         success: function(result){
        //             if(result.status=="success"){
        //                 refreshCart();
        //             }else{
        //                 $.alert(result.errorMsg);
        //             }
        //             layer.close(index);
        //         }
        //     });
        // }
        layer.close(index);

    })
    // 子级input框
    $('.valid').click(function(event){
        event.preventDefault();
        var index = layer.load(2);
        var that =$(this);

        // 所有子级input框数量
        var numA =$('label.valid').length;
        // 所有被选中的子级input框数量
        var numD =$('label.valid.checked').length;
        // 正在点击事件的子级input的父级下面的所有子级框数量
        var inputName =  that.find('input:checkbox').prop('name')
        var numB =that.parents('.main').find('input:checkbox[name="'+inputName+'"]').length;
        // 正在点击事件的子级input的父级下面的所有被选中的子级框数量
        var labelall =that.parents('.main').find('input:checkbox[name="'+inputName+'"]').parent();
        var numC =0;
        labelall.each(function(){
            if($(this).hasClass('checked')){
                numC++
            }
        })

        if(numB==numC){
            // 若数量相等，则正在点击事件的子级input的父级全选框被选中，并且添加checked类名
            that.parents('.main').find('input:checkbox[value="'+inputName+'"]').parent().addClass('checked')
        }else{
            // 若数量不相等，则正在点击事件的子级input的父级全选框不被选中，并且删除checked类名
            that.parents('.main').find('input:checkbox[value="'+inputName+'"]').parent().removeClass('checked')
        }
        if(numA==numD){
            // 若数量相等，则爷级全选框被选中，并且添加checked类名
            $('.all').addClass('checked')
        }else{
            // 若数量相等，则爷级全选框不被选中，并且删除checked类名
            $('.all').removeClass('checked')
        }
        setColor()


        var data ={
            skuId: that.find('input:checkbox').val()
        };
        if(that.hasClass("taocan")){
            data ={ packageId: that.find('input:checkbox').val() };
        }
        if(!$(this).hasClass("checked")){
            $.ajax({
                url: cartUrlPrefix + "/cancelItem",
                type: "POST",
                dataType: "json",
                data: data,
                success: function(result){
                    if(result.status=="success"){
                        refreshCart();
                    }else{
                        $.alert(result.errorMsg);
                    }
                    layer.close(index);
                }
            });
        }else{
            $.ajax({
                url: cartUrlPrefix + "/selectItem",
                type: "POST",
                dataType: "json",
                data: data,
                success: function(result){
                    if(result.status=="success"){
                        refreshCart();
                    }else{
                        $.alert(result.errorMsg);
                    }
                    layer.close(index);
                }
            });
        };

    })

});

function itemRemove(id) {

    $('#delModal').modal('show');

    $('#delModal').on('okHide', function (e) {
    	var index=layer.load(2);
        $.ajax({
            url: cartUrlPrefix + "/removeProductFromCart.json",
            type: "POST",
            dataType: "json",
            data: {
                id: id
            },
            success: function(result){
                if(result.status=="success"){
                    refreshCart();
                    var cartNum=parseInt($("#cartNumberDiv").html());
                    //$("#cartNumberLi").html(cartNum-1);
                    $("#rigthCartNum").html(cartNum-1);
                    $("#cartNumberDiv").html(cartNum-1);

                }else{
                    $.alert(result.errorMsg);
                }
                layer.close(index);
            }
        });
    });
}

function itemTCRemove(id) {
    $('#delTaoCanModal').on('okHide', function (e) {
    	var index=layer.load(2);
        $.ajax({
            url: cartUrlPrefix + "/removeProductFromCart.json",
            type: "POST",
            dataType: "json",
            data: {
                packageId: id
            },
            success: function(result){
                if(result.status=="success"){
                    refreshCart();
                    var cartNum=parseInt($("#cartNumberDiv").html());
                    //$("#cartNumberLi").html(cartNum-1);
                    $("#rigthCartNum").html(cartNum-1);
                    $("#cartNumberDiv").html(cartNum-1);

                }else{
                    $.alert(result.errorMsg);
                }
                layer.close(index);
            }
        });
    });
}


function itemBatchRemove() {
    var cartNum = 0;
	var ids = getCheckIdsArray();
    var tcIds = getCheckTCIdsArray();
    if (ids.length == 0 && tcIds.length == 0 ) {
        $.alert("请选择商品");
        return;
    }
    if(ids != null && ids !=""){
    	cartNum = ids.length;
    }
    if(tcIds != null && tcIds !=""){
    	cartNum += tcIds.length;
    }

    $('#delModal').modal('show');

    $('#delModal').on('okHide', function (e) {
    	var index=layer.load(2);
        $.ajax({
            url: cartUrlPrefix + "/batchRemoveProductFromCart.json",
            type: "POST",
            dataType: "json",
            data: {
                ids: ids.join(","),
                packageIds : tcIds.join(",")
            },
            success: function(result){
                if(result.status=="success"){
                    refreshCart();
                    var cartNum=parseInt($("#cartNumberDiv").html());
                    $("#cartNumberLi").html(cartNum-cartNum);
                    $("#rigthCartNum").html(cartNum-cartNum);
                    $("#cartNumberDiv").html(cartNum-cartNum);
                } else {
                    $.alert(result.errorMsg);
                }
                layer.close(index);
            }
        });
    });
}

function itemFollow(id, event) {

    var index = layer.load(2);
    $.ajax({
        url: cartUrlPrefix + "/followFavoriteForCart.json",
        type: "POST",
        dataType: "json",
        data: {
            ids: id
        },
        success: function(result){
            if(result.status=="success"){
                //shouc_fly(event, 1, id);

                refreshCart();
                var cartNum=parseInt($("#cartNumberDiv").html());
                $("#cartNumberLi").html(cartNum-1);
                $("#rigthCartNum").html(cartNum-1);
                $("#cartNumberDiv").html(cartNum-1);
            }else{
                $.alert(result.errorMsg);
            }
            layer.close(index);
        }
    });
}

function itemBatchFollow() {
    var ids = getCheckIdsArray();
    var TcIds = getCheckTCIdsArray();
    if(TcIds.length>0 && ids.length==0){
    	$.alert("套餐不能添加到收藏夹!");
    	return;
    }
    if (ids.length == 0) {
        $.alert("请选择商品");
        return;
    }

    $('#followModal').modal('show');

    $('#followModal').on('okHide', function (e) {
    	var index=layer.load(2);
        $.ajax({
            url: cartUrlPrefix + "/followFavoriteForCart.json",
            type: "POST",
            dataType: "json",
            data: {
                ids: ids.join(",")
            },
            success: function(result){
                if(result.status=="success"){
                    refreshCart();
                    var cartNum=parseInt($("#cartNumberDiv").html());
                    $("#cartNumberLi").html(cartNum-ids.length);
                    $("#rigthCartNum").html(cartNum-ids.length);
                    $("#cartNumberDiv").html(cartNum-ids.length);
                }else{
                    $.alert(result.errorMsg);
                }
                layer.close(index);
            }
        });
    });
}

function getCheckIds() {
    var ids = getCheckIdsArray();
    return ids.join(",");
}

function getCheckIdsArray() {
    var ids = [];
    $(".bodybox .putong").each(function(){
        if($(this).hasClass("checked")){
            ids.push($(this).find(":checkbox").val());
        }
    });
    return ids;
}
function getCheckTCIdsArray() {
    var ids = [];
    $(".bodybox .taocan").each(function(){
        if($(this).hasClass("checked")){
            ids.push($(this).find(":checkbox").val());
        }
    });
    return ids;
}

/*初始化判断位置*/
function initpositon(){
    var b = $(".listmode").height()+$(".topbzbox").height()+$(".sui-navbar").height()+parseInt($(".main").css("padding-top"));
    if(b <  ($(window).scrollTop() + $(window).height())) {
        $(".applybox").removeClass("orderfix");
        $(".applybox").removeClass('smallDev');
        $(".applybox").css('height',76);
        $(".applybox.crossStoreCoupon").css('height',44);
        $(".chaozhong").removeClass("orderfix1");
        $(".chaozhong").removeClass('smallDev');
        $(".chaozhong").addClass("orderRelative");
        $(".chaozhong").css('height',40);
    } else {
        var width= $(window).width();
        $(".applybox").addClass("orderfix");
        $(".chaozhong").addClass("orderfix1");
        var width= $(window).width();
        $(".applybox").addClass('smallDev');
        $(".chaozhong").addClass("smallDev");
        $(".chaozhong").removeClass('orderRelative');
        if(width<1035){
            $(".applybox").css('height',152);
            $(".chaozhong").css('height',152);
        }
    }
}

/*控制选中后背景颜色*/
function setColor(){
    $(".bodybox .putong").each(function(){
        if($(this).hasClass("checked")){
            $(this).closest('.bodybox').addClass("cur");
        }else{
            $(this).closest('.bodybox').removeClass("cur");
        }
    });
    
}
function setColor1(){
    $(".bodybox .taocan").each(function(){
        if($(this).hasClass("checked")){
            $(this).closest('.bodybox').addClass("cur");
        }else{
            $(this).closest('.bodybox').removeClass("cur");
        }
    });
    
}

/**
 * 购物车数量文本框的鼠标移开事件
 * @param obj
 */
function blurCartNum(obj){
	var type = 0;
	var num = parseInt($(obj).val());
	var isSplit = $(obj).attr("isSplit");
    var middpackNum = parseInt($(obj).attr("middpacking"));

	if(!num){
		$(obj).val(obj.defaultValue);
	} else {
        var pid = $(obj).attr("pid");
        var packageId = $(obj).attr("packageId");
        if(packageId != null && packageId !=''){
        	pid = packageId;
        	type=1;
        }
        /**
        else{
        	
            if(isSplit == 0){
            	if(num<middpackNum){
            		showBoxTC(middpackNum);
            		num = middpackNum;
            	}else{
            		var beishu = num % middpackNum;
            		if(beishu != 0){
            			showBoxTC(middpackNum);
						var yushu = Math.floor(num/middpackNum);
						num = yushu*middpackNum;
            		}
            	}
            	$("#cartNum_"+pid).val(num);
            	
            }
        }
        */
        changeCart(pid,type,num, function(result){
            if(result.status=="success"){
                if (result.data.message) {
                    if (result.data.dialogStyle == 20) {
                        $.alert({
                            title: '提示',
                            body: result.data.message,
                            okBtn : '我知道了'
                        });
                    } else {
                        layer.msg(result.data.message, {
                            icon: 6,
                            time: 2000
                        });
                    }
                }
                refreshCart();
            }else{
                $.alert(result.errorMsg);
            }
        },'','','','','','',undefined,3,'','','',0,'','');
    }
}

function shouc_fly(event, flag, id) {
    var offset = $(".r-souchang").offset();
    var scrollX = $(window).scrollTop(); //获取滚动条的距离。
    var img = "http://upload.ybm100.com/ybm/product/6936292110209.jpg";
    if (flag == 1) {
        img = $("#dt_" + id)[0].src;
    }
    flyer = $('<img class="u-flyer" src="' + img + '">');

    var start_left;
    var start_top;
    if (event.pageX == "undefined" || event.pageX == undefined) {
        start_left = $('#href_DT_'+id).offset().left; // X坐标
        start_top = $('#href_DT_'+id).offset().top-scrollX;  // Y坐标
    } else {
        start_left = event.pageX;
        start_top = event.pageY - scrollX;
    }


    flyer.fly({
        start: {
            left: start_left, //开始位置（必填）#fly元素会被设置成position: fixed
            top: start_top  //开始位置（必填）
        },
        end: {
            left: offset.left + 10, //结束位置（必填）
            top: offset.top + 10 - scrollX, //结束位置（必填）
            width: 0, //结束时宽度
            height: 0 //结束时高度
        },
        onEnd: function () { //结束回调
        }
    });
}

function refreshCart() {
    var ids = getCheckIdsArray();
    $.ajax({
        url: "list.htm?r=" + Math.random(),
        type: "POST",
        dataType: "html",
        traditional :true,
        data: {
            storeStatus: true
        },
        success: function(result){
            $(".main").html(result);

            var cartVarietyNum = $('#cartVarietyNum').text();
            $("#cartNumberLi").html(cartVarietyNum);
            $("#rigthCartNum").html(cartVarietyNum);
            $("#cartNumberDiv").html(cartVarietyNum);
            setTimeout(function(){
                try{
                    exposeShop()
                }catch(e){
                    console.log(e);
                }
            },1000)
        }
    });
}

/**
 * 批量删除失效商品
 */
function clearInvalidProduct(){
	var ids = "";
	var tcIds = "";
	var subNum = 0;

	$("input[name='invalid']").each(function(){
		ids +=$(this).val()+",";
	});
	$("input[name='invalidtc']").each(function(){
		tcIds +=$(this).val()+",";
	});
	if((ids != null && ids !="") || (tcIds != null && tcIds !="")){
		if(ids != null && ids !=""){
			ids = ids.substring(0,ids.length-1);
			idArr = ids.split(",");
			subNum = idArr.length;
		}
		if(tcIds != null && tcIds !=""){
			tcIds = tcIds.substring(0,tcIds.length-1);
			tcidArr = tcIds.split(",");
			subNum += tcidArr.length;
		}
		$.ajax({
            url: cartUrlPrefix + "/batchRemoveProductFromCart.json",
            type: "POST",
            dataType: "json",
            data: {
                ids: ids,
                packageIds : tcIds
            },
            success: function(result){
                if(result.status=="success"){
                    refreshCart();
                    var cartNum=parseInt($("#cartNumberLi").html());
                    $("#cartNumberLi").html(cartNum-subNum);
                    $("#rigthCartNum").html(cartNum-subNum);
                    $("#cartNumberDiv").html(cartNum-subNum);
                } else {
                    $.alert(result.errorMsg);
                }
                layer.close(index);
            }
        });
	}
}

/**
 * 批量移入收藏夹
 */
function mvToCollection(){
	var ids = "";
	var tcids = "";
	$("input[name='invalid']").each(function(){
		ids +=$(this).val()+",";
	});
	$("input[name='invalidtc']").each(function(){
		tcids +=$(this).val()+",";
	});
	if(tcids != null && tcids !=""){
		tcids = tcids.substring(0,tcids.length-1);
		tcidArr = tcids.split(",");
		if(tcidArr.length>0 && (ids =="" || ids == null)){
			$.alert("搭配套餐不能加入到收藏夹!");
			return;
		}
	}
	if(ids != null && ids !=""){
		ids = ids.substring(0,ids.length-1);
		idArr = ids.split(",");
		$.ajax({
	        url: cartUrlPrefix + "/followFavoriteForCart.json",
	        type: "POST",
	        dataType: "json",
	        data: {
	            ids: ids
	        },
	        success: function(result){
	            if(result.status=="success"){
	                refreshCart();
	                var cartNum=parseInt($("#cartNumberLi").html());
	                $("#cartNumberLi").html(cartNum-idArr.length);
	                $("#rigthCartNum").html(cartNum-idArr.length);
	                $("#cartNumberDiv").html(cartNum-idArr.length);
	            }else{
	                $.alert(result.errorMsg);
	            }
	            layer.close(index);
	        }
	    });
	}
}

function showOverlay() {
    $("#overlay").height(pageHeight());
    $("#overlay").width(pageWidth());

    // fadeTo第一个参数为速度，第二个为透明度
    // 多重方式控制透明度，保证兼容性，但也带来修改麻烦的问题
    $("#overlay").fadeTo(200, 0.5);
}
function showOverlayNew() {
    $("#overlay-new").height(pageHeight());
    $("#overlay-new").width(pageWidth());

    // fadeTo第一个参数为速度，第二个为透明度
    // 多重方式控制透明度，保证兼容性，但也带来修改麻烦的问题
    $("#overlay-new").fadeTo(200, 0.5);
}

/* 隐藏覆盖层 */
function hideOverlay() {
    $("#overlay").fadeOut(200);
}
/* 隐藏覆盖层 */
function hideOverlayNew() {
    $("#overlay-new").fadeOut(200);
}

/* 当前页面高度 */
function pageHeight() {
    return document.body.scrollHeight;
}

/* 当前页面宽度 */
function pageWidth() {
    return document.body.scrollWidth;
}
