package com.xyy.ec.pc.newfront.service;

import com.xyy.ec.pc.newfront.vo.ShotInfoVO;
import com.xyy.ec.pc.newfront.vo.ShotQueryParam;
import com.xyy.ec.pc.popshop.vo.CompanyShopFloorVo;

import java.util.List;

public interface ShopQueryService {

    List<ShotInfoVO> queryShopActivityInfoByShopCodes(ShotQueryParam param, long merchantId);

    List<CompanyShopFloorVo> companyFloor(String orgId, Long merchantId, String branchCode);
}
