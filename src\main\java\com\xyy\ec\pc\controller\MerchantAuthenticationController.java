package com.xyy.ec.pc.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.api.MerchantAuthenticationBusinessApi;
import com.xyy.ec.merchant.bussiness.base.ResultMessage;
import com.xyy.ec.merchant.bussiness.dto.MerchantAuthenticationBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantAuthenticationSwitchBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.enums.ResultCodeEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户 被委托人认证
 */
@Controller
@RequestMapping("/authentication")
public class MerchantAuthenticationController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantAuthenticationController.class);

    @Reference(version = "1.0.0")
    private MerchantAuthenticationBusinessApi merchantAuthenticationBusinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    /**
     * 跳转用户认证页面
     * @return
     */
    @RequestMapping("/indexAuthentication.htm")
    public ModelAndView indexAuthentication(String reSubmit)  {
        Map<String,Object> model = new HashMap<String,Object>();
        MerchantPrincipal merchant = null;
        MerchantAuthenticationBusinessDto merchantAuthenticationBusinessDto = null;
        try {
            merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            LOGGER.error("获取会员信息异常:",e);
        }
        if (merchant == null) {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }
        try {
            merchantAuthenticationBusinessDto = merchantAuthenticationBusinessApi.getInfoByMerchantId(merchant.getId());
            if (MerchantAuthenticationBusinessDto.StatusType.UN_CERTIFIED.getValue().equals(merchantAuthenticationBusinessDto.getStatus())
            || "1".equals(reSubmit)){
                model.put("merchantAuthenticationBusinessDto", merchantAuthenticationBusinessDto);
                return new ModelAndView("/merchantAuthentication/submit.ftl",model);
            }else {
                model.put("mobile",merchant.getMobile());
                model.put("merchantAuthenticationBusinessDto", merchantAuthenticationBusinessDto);
                return new ModelAndView("/merchantAuthentication/detail.ftl",model);
            }
        } catch (Exception e) {
            LOGGER.error("查看资质出现异常,e=",e);
        }
        return null;
    }

    /**
     * 跳转余额提现资质认证页面
     * @return
     */
    @RequestMapping("/detail")
    public ModelAndView detail()  {
        Map<String,Object> model = new HashMap<String,Object>();
        MerchantPrincipal merchant = null;
        MerchantAuthenticationBusinessDto merchantAuthenticationBusinessDto = null;
        try {
            merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            LOGGER.error("获取会员信息异常:",e);
        }
        if (merchant == null) {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }
        try {
            merchantAuthenticationBusinessDto = merchantAuthenticationBusinessApi.getInfoByMerchantId(merchant.getId());
        } catch (Exception e) {
            LOGGER.error("查看资质出现异常,e="+e);
        }
        model.put("mobile",merchant.getMobile());
        model.put("merchantAuthenticationBusinessDto", merchantAuthenticationBusinessDto);
        return new ModelAndView("/merchantAuthentication/detail.ftl",model);
    }

    /**
     * 保存或者修改认证信息
     * @param dto
     * @return
     */
    @RequestMapping("/saveMerchantAuthenticationInfo.json")
    @ResponseBody
    public Object saveMerchantAuthenticationInfo(MerchantAuthenticationBusinessDto dto){
        Map<String,Object> model = new HashMap<String,Object>();
        try{
            dto.setSource((byte)1);
            dto.setOperateSource(0); //用户操作
            ResultMessage<MerchantAuthenticationBusinessDto> resultMessage = merchantAuthenticationBusinessApi.saveOrUpdateMerchantAuthenticationInfo(dto);
            if(resultMessage.getCode() != ResultCodeEnum.SUCCESS.getCode()){
                return this.addError(resultMessage.getMsg());
            }
            model.put("merchantAuthenticationBusinessDto", resultMessage.getResult());
            if(MerchantAuthenticationBusinessDto.StatusType.FAIL.getValue().equals(resultMessage.getResult().getStatus())){
                return this.addError("输入的被委托人姓名/身份证号与平台留存的企业被委托人信息不符，如有疑问请联系您的专属销售进行核实");
            }
        }catch(Exception e){
            LOGGER.error("保存认证信息出错,e=",e);
        }
//        return new ModelAndView("/merchantAuthentication/detail.ftl",model);
        return this.addResult("保存药店认证信息成功",model);
    }

    /**
     * 更换确认手机号
     * @param dto
     * @return
     */
    @RequestMapping("/changeAuthMobile.json")
    @ResponseBody
    public Object changeAuthMobileJson(MerchantAuthenticationBusinessDto dto){
        try{
            dto.setOperateSource(0); //用户操作
            ResultMessage<MerchantAuthenticationBusinessDto> resultMessage = merchantAuthenticationBusinessApi.changeAuthMobile(dto);
            if(resultMessage.getCode() != ResultCodeEnum.SUCCESS.getCode()){
                return this.addError(resultMessage.getMsg());
            }
        }catch(Exception e){
            LOGGER.error("更换确认手机号出错,e=",e);
        }
        return this.addResult("更换手机号成功");
    }

    @RequestMapping("/getAuthenticationSwitch")
    @ResponseBody
    public Object getOrderEscortSwitch(){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null)   return "0";
            MerchantAuthenticationSwitchBusinessDto dto = merchantAuthenticationBusinessApi.getAuthenticationMap(merchant.getRegisterCode());

            if (dto != null){
                return "1";//委托人认证开
            }else{
                return "0";//委托人认证关
            }
        } catch (Exception e) {
            LOGGER.error("查询委托人认证开关异常", e);
        }
        return "0";
    }

}

