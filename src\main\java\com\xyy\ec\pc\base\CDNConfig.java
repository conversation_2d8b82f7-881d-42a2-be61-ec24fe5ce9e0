/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.xyy.ec.pc.base;

import java.io.Serializable;

/**
 * CND配置实体
 * ClassName: CDNConfig <br/> 
 * date: 2015-9-21 下午3:06:22 <br/> 
 * 
 * <AUTHOR> 
 * @version  
 * @since JDK 1.7
 */
public class CDNConfig implements Serializable{
	private static final long serialVersionUID = 1761418367589042882L;

	//上传CDN目录地址
    private String uploadPath;
    
    //CDN-hostname
    private String hostName;
    
    //CDN-端口
    private int port;
    
    //用户名
    private String username;
    
    //密码
    private String password;
    
    private String showUrl;

    public String getUploadPath() {
        return uploadPath;
    }

    public void setUploadPath(String uploadPath) {
        this.uploadPath = uploadPath;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getShowUrl() {
		return showUrl;
	}

	public void setShowUrl(String showUrl) {
		this.showUrl = showUrl;
	}
    
}
