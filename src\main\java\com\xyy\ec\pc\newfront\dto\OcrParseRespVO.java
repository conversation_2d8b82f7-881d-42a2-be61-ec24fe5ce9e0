package com.xyy.ec.pc.newfront.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString
public class OcrParseRespVO implements Serializable {

    /**
     * 解析状态 (1、解析失败，2、解析成功，3、正在解析)
     */
     private Integer status;

    /**
     * 类型
     */
    private String title;

    /**
     * 编码
     */
    private String creditCode;

    /**
     * 名称
     */
    private String companyName;

    /**
     * 地址
     */
    private String businessAddress;


    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;
}