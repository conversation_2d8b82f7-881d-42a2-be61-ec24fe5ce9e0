package com.xyy.ec.pc.recommend.helpers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.ec.pc.recommend.dto.RecommendQtListDataDTO;
import com.xyy.ec.pc.recommend.dto.RecommendQtSkuDataDTO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchProductSSMVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchProductVO;
import com.xyy.ec.pc.search.params.PcSearchProductListQueryParam;
import com.xyy.ec.pc.search.vo.PcSearchProductInfoVo;
import com.xyy.ec.pc.search.vo.SxpQtDataVo;
import com.xyy.ec.product.business.ecp.csutag.dto.TagDTO;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.SearchProductCardPositionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class RecommendQuickTrackingDataHelper {

    public static void handleSxpQuickTrackingData(List<PcSearchProductInfoVo> productInfos, RecommendQtListDataDTO qtListDataDTO, PcSearchProductListQueryParam queryParam) {
        if (CollectionUtils.isEmpty(productInfos)) {
            return;
        }

        Integer pageNo = qtListDataDTO.getPage_no();
        Integer pageSize = qtListDataDTO.getPage_size();
        String expId = "";
        String scmId;
        if (!Objects.equals(queryParam.getPageNum(), NumberUtils.INTEGER_ONE) && StringUtils.isNotEmpty(queryParam.getScmId())) {
            scmId = queryParam.getScmId();
        } else {
            scmId = RandomStringUtils.randomAlphanumeric(8);
        }

        long rankLong = (pageNo - 1) * pageSize;
        int rank = (int) rankLong;
        for (int i = 0; i < productInfos.size(); i++) {
            PcSearchProductInfoVo pcSearchProductVO = productInfos.get(i);
            SxpQtDataVo sxpQtDataVo = new SxpQtDataVo();
            sxpQtDataVo.setExpId(expId);
            sxpQtDataVo.setScmId(scmId);
            int skuRank = rank + i + 1;
            RecommendQtSkuDataDTO qtSkuDataDTO = RecommendQtSkuDataDTO.builder()
                    .rank(skuRank)
                    .list_position_type(String.valueOf(SearchProductCardPositionTypeEnum.NORMAL.getType()))
                    .list_position_type_name(SearchProductCardPositionTypeEnum.NORMAL.getName())
                    .product_type(pcSearchProductVO.getProductType())
                    .product_high_gross(pcSearchProductVO.getHighGross())
                    .product_category_first_id(pcSearchProductVO.getCategoryFirstId())
                    .product_category_first_name(pcSearchProductVO.getCategoryFirstName())
                    .shop_code(pcSearchProductVO.getShopCode())
                    .shop_name(pcSearchProductVO.getShopName())
                    .build();
            qtSkuDataDTO.setProduct_labels(getQtSkuDataProductLabels(pcSearchProductVO.getTags()));
            sxpQtDataVo.setRank(skuRank);
            sxpQtDataVo.setQtListData(JSONObject.toJSONString(qtListDataDTO));
            sxpQtDataVo.setQtSkuData(JSONObject.toJSONString(qtSkuDataDTO));
            pcSearchProductVO.setQtData(sxpQtDataVo);
        }
    }

    private static List<String> getQtSkuDataProductLabels(Map<String, Object> tags) {
        try {
            if (Objects.nonNull(tags) && Objects.nonNull(tags.get("productTags"))) {
                List<String> tagTexts = Lists.newArrayList();
                List<TagDTO> productTags = (List<TagDTO>) tags.get("productTags");
                for (TagDTO productTag : productTags) {
                    tagTexts.add(productTag.getText());
                }
                if (CollectionUtils.isNotEmpty(tagTexts)) {
                    return tagTexts;
                }
            }
        } catch (Exception e) {
            log.error("推荐埋点标签处理异常，异常信息为：", e);
        }
        return null;
    }


    public static List<PcSearchProductSSMVO> handleQuickTrackingDataForSSM(List<PcSearchProductVO> rows, List<Long> skuIds, String recommendStrategyCode) {
        if (CollectionUtils.isEmpty(rows)) {
            return null;
        }
        String expId = recommendStrategyCode;

        Integer pageNo = NumberUtils.INTEGER_ONE;
        Long pageSize = Long.valueOf(skuIds.size());
        RecommendQtListDataDTO qtListDataDTO = RecommendQtListDataDTO.builder()
                .result_cnt(Long.valueOf(skuIds.size()))
                .page_no(pageNo)
                .page_size(skuIds.size())
                .total_page(pageNo)
                .exp_id(expId)
                .key_word(null)
                .build();

        String scmId = RandomStringUtils.randomAlphanumeric(8);

        List<PcSearchProductSSMVO> result = new ArrayList<>();

        long rankLong = (pageNo - 1) * pageSize;
        int rank = (int) rankLong;
        for (int i = 0; i < rows.size(); i++) {
            PcSearchProductVO appSearchProductVO = rows.get(i);
            PcSearchProductSSMVO ssmvo = new PcSearchProductSSMVO();
            BeanUtils.copyProperties(appSearchProductVO, ssmvo);
            SxpQtDataVo sxpQtDataVo = new SxpQtDataVo();
            sxpQtDataVo.setExpId(expId);
            sxpQtDataVo.setScmId(scmId);
            int cardRank = rank + i + 1;
            RecommendQtSkuDataDTO qtSkuDataDTO = RecommendQtSkuDataDTO.builder()
                    .rank(cardRank)
                    .list_position_type(SearchProductCardPositionTypeEnum.NORMAL.getType().toString())
                    .list_position_type_name(SearchProductCardPositionTypeEnum.NORMAL.getName())
                    .product_type(appSearchProductVO.getProductType())
                    .product_high_gross(appSearchProductVO.getHighGross())
                    .product_category_first_id(appSearchProductVO.getCategoryFirstId())
                    .product_category_first_name(appSearchProductVO.getCategoryFirstName())
                    .shop_code(appSearchProductVO.getShopCode())
                    .shop_name(appSearchProductVO.getShopName())
                    .build();
            qtSkuDataDTO.setProduct_labels(getQtSkuDataProductLabels(appSearchProductVO.getTags()));
            sxpQtDataVo.setRank(cardRank);
            sxpQtDataVo.setQtListData(JSON.toJSONString(qtListDataDTO));
            sxpQtDataVo.setQtSkuData(JSONObject.toJSONString(qtSkuDataDTO));
            ssmvo.setQtData(sxpQtDataVo);
            result.add(ssmvo);
        }
        return result;
    }

}
