package com.xyy.ec.pc.cms.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * CMS：商品查询参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsProductQueryParam implements Serializable {

    private static final long serialVersionUID = -3734988508107200400L;
    /**
     * <pre>
     * 是否是admin侧。
     * 可接受值：true、false、null（不传）
     * true：admin侧。
     * false 或者 null（不传） ： 2C侧。
     * </pre>
     */
    private Boolean isAdmin;

    /**
     * 分公司编码
     */
    private String branchCode;

    /**
     * 会员ID
     */
    private Long merchantId;

    /**
     * 商品展示组ID
     */
    private String exhibitionId;

    /**
     * 建组来源
     */
    private Integer moduleSourceType;

    /**
     * 是否推荐
     */
    private Boolean isRecommend;

    /**
     * 终端类型
     */
    private Integer terminalType;
}
