package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.bussiness.api.SubstituteOrdersBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderSproutBusinessApi;
import com.xyy.ec.order.business.config.OrderEnum;
import com.xyy.ec.order.business.dto.OrderSproutBusinessDto;
import com.xyy.ec.order.business.dto.OrderSproutBusinessSearchDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.DateUtil;
import com.xyy.ec.pc.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 豆芽代下单
 * @date 2019/10/18 14:49
 */
@Controller
@RequestMapping("/merchant/center/sprout/order")
public class OrderSproutController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(OrderSproutController.class);

    @Reference(version = "1.0.0",timeout = 60000)
    private OrderSproutBusinessApi orderSproutBusinessApi;

    @Reference(version = "1.0.0",timeout = 60000)
    private SubstituteOrdersBusinessApi substituteOrdersBusinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private Config config;

    @Reference(version = "1.0.0", timeout = 500)
    private OrderBusinessApi orderBusinessApi;


    @RequestMapping("/index.htm")
    public ModelAndView order(OrderSproutBusinessSearchDto orderSproutBusinessSearchDto, Page page, Model model,HttpServletRequest request) throws Exception {
        if (null == page) {
            page = new Page();
        }
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        model.addAttribute("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
        orderSproutBusinessSearchDto.setMerchantId(merchant.getId());
        //pc时间控件不带时分秒，产品让我们后端查询的时候加上，返回给页面的时候要去掉，让用户无感知，这需求真的想吐血
        if(null != orderSproutBusinessSearchDto && StringUtil.isNotBlank(orderSproutBusinessSearchDto.getCreateTimeStart())){
            Date date = DateUtil.string2Date(orderSproutBusinessSearchDto.getCreateTimeStart(),"yyyy-mm-dd");
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.set(Calendar.HOUR_OF_DAY, 0);
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.SECOND, 0);
            orderSproutBusinessSearchDto.setCreateTimeStart(DateUtil.date2String(c.getTime(),DateUtil.PATTERN_STANDARD));
        }
        if(null != orderSproutBusinessSearchDto && StringUtil.isNotBlank(orderSproutBusinessSearchDto.getCreateTimeEnd())){
            Date date = DateUtil.string2Date(orderSproutBusinessSearchDto.getCreateTimeEnd(),"yyyy-mm-dd");
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.set(Calendar.HOUR_OF_DAY,23);
            c.set(Calendar.MINUTE,59);
            c.set(Calendar.SECOND,59);
            orderSproutBusinessSearchDto.setCreateTimeEnd(DateUtil.date2String(c.getTime(),DateUtil.PATTERN_STANDARD));
        }
        try {
            PageInfo pageInfoParam = new PageInfo<>();
            pageInfoParam.setPageNum(page.getOffset());
            pageInfoParam.setPageSize(page.getLimit());
            PageInfo<OrderSproutBusinessDto> myOrderInfo =  orderSproutBusinessApi.searchOrderSproutsPage(orderSproutBusinessSearchDto,pageInfoParam);
            Page<OrderSproutBusinessDto> pageInfo = new Page<>();
            pageInfo.setRows(myOrderInfo.getList());
            pageInfo.setTotal(myOrderInfo.getTotal());
            pageInfo.setPageCount(myOrderInfo.getPages());
            String requestUrl = this.getRequestUrl(request);
            pageInfo.setRequestUrl(requestUrl);
            pageInfo.setOffset(page.getOffset());
            int unAuthorizeCount =  substituteOrdersBusinessApi.getUnAuthorizeCount(merchant.getId(),String.valueOf(System.currentTimeMillis()));
            int unConfirmOrderCount  = orderSproutBusinessApi.getOrderSproutCountByStatus(merchant.getId(),OrderEnum.OrderSproutStatus.WATTING.getId());
            model.addAttribute("authorizeNum",unAuthorizeCount);
            model.addAttribute("orderNum",unConfirmOrderCount);
            if(StringUtil.isNotBlank(orderSproutBusinessSearchDto.getCreateTimeStart())){
                orderSproutBusinessSearchDto.setCreateTimeStart(orderSproutBusinessSearchDto.getCreateTimeStart().substring(0,10));
            }
            if(StringUtil.isNotBlank(orderSproutBusinessSearchDto.getCreateTimeEnd())){
                orderSproutBusinessSearchDto.setCreateTimeEnd(orderSproutBusinessSearchDto.getCreateTimeEnd().substring(0,10));
            }
            //历史参数
            model.addAttribute("paramOrder",orderSproutBusinessSearchDto);
            model.addAttribute("pager", pageInfo);
            model.addAttribute("center_menu", "sprout");
            model.addAttribute("productImageUrl", config.getProductImagePathUrl());
        } catch (Exception e) {
            logger.error("订单物流记载失败：" + e.getMessage());
            throw e;
        }
        return new ModelAndView("/sprout/order.ftl");
    }

    /**
     * 查看订单详情
     *
     * @param id
     * @param modelMap
     * @return
     */
    @RequestMapping("/detail/{id}.htm")
    public String detail(@PathVariable Long id, ModelMap modelMap) {
        OrderSproutBusinessDto order = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            modelMap.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            order = orderSproutBusinessApi.selectSproutOrderDetailById(merchant.getId(), id);
            modelMap.put("merchant", merchant);
        } catch (Exception e) {
            logger.error("查看订单异常", e);
        }
        modelMap.put("order", order);
        modelMap.put("center_menu", "sprout");
        return "/sprout/detail.ftl";
    }

    /**
     * 订单驳回
     */
    @RequestMapping("/orderRefuse.json")
    @ResponseBody
    public Object orderRefuse(@RequestParam(value = "purchaseNo",required = true) String purchaseNo,
            @RequestParam(value = "refuseReason",required = true) String refuseReason,
            @RequestParam(value = "refuseExplan",required = false) String refuseExplan) {
        Map<String, Object> result = this.addResult();
        try {
            OrderSproutBusinessDto orderSproutDto = orderSproutBusinessApi.selectSproutOrderByPurchaseNo(purchaseNo);
            if(orderSproutDto != null && orderSproutDto.getStatus() == OrderEnum.OrderSproutStatus.REFUSE.getId()){
                return this.addError("采购单已驳回，可至采购单列表页查询");
            } else if(orderSproutDto != null && orderSproutDto.getStatus() == OrderEnum.OrderSproutStatus.INVALID.getId()){
                return this.addError("采购单已取消，可至采购单列表页查询");
            } else if(orderSproutDto != null && orderSproutDto.getStatus() == OrderEnum.OrderSproutStatus.CONFIRM.getId()){
                return this.addError("采购单已生成订单，可至订单列表页查询");
            } else if(orderSproutDto != null && orderSproutDto.getStatus() == OrderEnum.OrderSproutStatus.WATTING.getId()){
                OrderSproutBusinessDto order  =  new OrderSproutBusinessDto();
                order.setPurchaseNo(purchaseNo);
                order.setStatus(OrderEnum.OrderSproutStatus.REFUSE.getId());//已驳回
                order.setRefuseReason(refuseReason);
                order.setRefuseExplan(refuseExplan);
                int count  = orderSproutBusinessApi.updateByPrimaryKeySelective(order);
                if(count<=0){
                    return this.addError("无效采购单");
                }
            }
            result.put("msg","success");
            return result;
        } catch (Exception e) {
            logger.error("驳回订单异常", e);
            return this.addError("驳回订单异常,请稍后");
        }
    }

    /**
     * 订单确认
     *
     * @param id
     * @return
     */
    @RequestMapping("/orderConfirm.json")
    @ResponseBody
    public Object orderConfirm(@RequestParam(value = "id",required = true) Long id) {
        Map<String, Object> result = this.addResult();
        try {
            OrderSproutBusinessDto order  =  new OrderSproutBusinessDto();
            order.setId(id);
            order.setStatus(OrderEnum.OrderSproutStatus.CONFIRM.getId());//已确认
            int count  = orderSproutBusinessApi.updateByPrimaryKeySelective(order);
            if(count<=0){
                return this.addError("无效订单");
            }
            result.put("msg","success");
            return result;
        } catch (Exception e) {
            logger.error("驳回订单异常", e);
            return this.addError("驳回订单异常,请稍后");
        }
    }
}
