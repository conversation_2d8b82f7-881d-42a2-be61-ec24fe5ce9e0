package com.xyy.ec.pc.vo;


import com.xyy.xtools.vkit.time.DateFormatUtil;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class VirtualGoldLogVO implements Serializable {

    private static final long serialVersionUID = 6902027436765430339L;

    private Long orderId;

    //1:订单详情 2:退款详情
    private Integer requestFlag;
    /**
     * 交易流水号
     */
    private String tranNo;

    /**
     * 购物金流水额
     */
    private BigDecimal virtualGold;

    /**
     * 购物金变动后余额
     */
    private BigDecimal   afterChange;

    /**
     * 购物金变动类型（1:充值 2：下单扣减 3：退款返还 4：取消返还）
     */
    private Byte changeType;

    /**
     * 备注
     */
    private String changeDesc;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 创建时间字符串
     */
    private String createTimeStr;


    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }



    public String getTranNo() {
        return tranNo;
    }

    public void setTranNo(String tranNo) {
        this.tranNo = tranNo;
    }

    public BigDecimal getVirtualGold() {
        return virtualGold;
    }

    public void setVirtualGold(BigDecimal virtualGold) {
        this.virtualGold = virtualGold;
    }

    public Byte getChangeType() {
        return changeType;
    }

    public void setChangeType(Byte changeType) {
        this.changeType = changeType;
    }

    public String getChangeDesc() {
        return changeDesc;
    }

    public void setChangeDesc(String changeDesc) {
        this.changeDesc = changeDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getRequestFlag() {
        return requestFlag;
    }

    public void setRequestFlag(Integer requestFlag) {
        this.requestFlag = requestFlag;
    }

    public BigDecimal getAfterChange() {
        return afterChange;
    }

    public void setAfterChange(BigDecimal afterChange) {
        this.afterChange = afterChange;
    }

    public String getCreateTimeStr() {

        if(null!=createTime){
            return DateFormatUtil.formatDate(DateFormatUtil.PATTERN_DEFAULT_ON_SECOND, createTime);
        }
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }
}
