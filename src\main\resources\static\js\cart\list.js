/**
 * Created by Administrator on 2016/12/21.
 */
var add_step = 1;
var sub_step = 1;
var cartUrlPrefix = "/merchant/center/cart";
var voucherUrlPrefix = "/merchant/center/voucher";

function changeCart(pid,type, qty, success, fail, error,event,shurukuangobj,middpacking,isSplit,sourceIdNum,real,productName,barcode,categoryId,sptype,spid,sid,bizSource) {

	var index=layer.load(2);
	var data ={
			skuId: pid,
	        amount: qty,
            sptype: sptype,
            spid: spid,
            sid: sid,
            direct: real,
            sourceId:sourceIdNum,
            bizSource:bizSource
		};
	if(type ==1){
		data ={
				packageId: pid,
		        amount: qty,
                sptype: sptype,
                spid: spid,
                sid: sid,
                direct: real,
                sourceId:sourceIdNum
			};
	}

    /* let jgInfo = sessionStorage.getItem("jgInfo") || null;
    if (jgInfo) {
        data.mddata = jgInfo;
    } */
    if(window.qtdata){
       try{
        data.qtdata = JSON.stringify(window.qtdata);
       }catch(e){}
    }
	
    $.ajax({
        url: cartUrlPrefix + "/changeCart.json",
        type: "POST",
        // dataType: "json",
        data: data,
        success: function(result){
            if(result.status == "success"){
                if (result.data.actPurchaseTip) {
                    $('#actPurchaseTip').html(`<div style="width:160px;padding: 5px;border-radius: 5px;background-color: #ffd5a0;margin:3px 0;word-wrap:break-word;">${ result.data.actPurchaseTip }</div>`);
                }
                if (bizSource && bizSource ==7){

                }else {

                    if(window.pcBillCollectHander){
                        window.pcBillCollectHander();
                    }
                    if(window.pcBaoyouCollectHander){
                        window.pcBaoyouCollectHander();
                    }
                    if (typeof success === "function") {
                        var voucherTemplateId = $("#voucherTemplateId").val();
                        if(voucherTemplateId){
                            var dota ={
                                voucherTemplateId: voucherTemplateId
                            };
                            $.ajax({
                                url: voucherUrlPrefix + "/findShangpinquanInfo.json",
                                type: "POST",
                                data: dota,
                                success: function(result){
                                    if(result.status == "success"){
                                        $("#selectSkuAmount").html(result.voucherDTO.selectSkuAmount.toFixed(2));
                                        $("#isSelectSkuNum").html(result.voucherDTO.isSelectSkuNum);
                                        $("#noEnoughMoney").html(result.voucherDTO.noEnoughMoney);
                                        $("#moneyInVoucher").html(result.voucherDTO.moneyInVoucher);
                                        $("#noEnoughMoneyDesc").html(result.voucherDTO.noEnoughMoneyDesc);
                                    }else {
                                        if(result.status == "failure"){
                                            $.alert({"title":"提示","body":result.errorMsg});
                                        }
                                    }
                                }
                            });
                        }
                        success(result,event);
                    }
                }
                // if(real != 2 && real != 3)
                if(real == 1){
                    //商品详情上报
                    webSdk.track('pc_page_CommodityDetails', {
                        'sptype': sptype,
                        'spid': spid,
                        'sid': sid,
                        'real': real,
                        'commodityId':pid,
                        'commodityName':productName,
                        'commodityCategory':categoryId,
                        'commodityCode':barcode
                    });
                }
            }else {
            	if(result.status == "failure"){
            		$.alert({"title":"提示","body":result.errorMsg});
            	}else if (typeof fail === "function") {
                    fail(result);
                    if(shurukuangobj){
                        shurukuangobj.val('0');
                    }
                }
            }
            layer.close(index);
        },
        error: error
    });
}

function groupChangeCart(pid,type, qty, success, fail, error,event,shurukuangobj,middpacking,isSplit,sourceIdNum,real,productName,barcode,categoryId,sptype,spid,sid) {

    var index=layer.load(2);
    var data ={
        skuId: pid,
        amount: qty,
        sptype: sptype,
        spid: spid,
        sid: sid,
        direct: real,
        sourceId:sourceIdNum,
        bizSource:7
    };

    $.ajax({
        url: cartUrlPrefix + "/changeCart.json",
        type: "POST",
        // dataType: "json",
        data: data,
        success: function(result){
            if(result.status == "success"){

                // if(real != 2 && real != 3)
                if(real == 1){
                    //商品详情上报
                    webSdk.track('pc_page_CommodityDetails', {
                        'sptype': sptype,
                        'spid': spid,
                        'sid': sid,
                        'real': real,
                        'commodityId':pid,
                        'commodityName':productName,
                        'commodityCategory':categoryId,
                        'commodityCode':barcode
                    });
                }
            }else {
                if(result.status == "failure"){
                    $.alert({"title":"提示","body":result.errorMsg});
                }else if (typeof fail === "function") {
                    fail(result);
                    if(shurukuangobj){
                        shurukuangobj.val('0');
                    }
                }
            }
            layer.close(index);
        },
        error: error
    });
}

function addCart(pid,type, qty,step, callback) {
	//if(type==1){
		add_step = step;
	//}
    var qty = parseFloat(qty);
    qty += add_step;
    changeCart(pid,type, qty, callback,'','','','','','',undefined,3,'','','',0,'','');
}

function subCart(pid,type, qty,step, callback) {
    var qty = parseFloat(qty);
    qty -= step;
    changeCart(pid,type, qty, callback,'','','','','','',undefined,3,'','','',0,'','');
}

var CONFIM = false;
function confim(val,canSettle,notSubmitOrderOrgIds, target) {
    console.log('notSubmitOrderOrgIds:', notSubmitOrderOrgIds)
    const giveNum = $('.giftPoolActCanSelectedNum')
    if (giveNum.length > 0 && !target) {
        if (giveNum.length == 1) {
            $('#mz-title').show();

            $('#mz-actList').hide();
        } else {
            const giveGifts = $('.giftPoolActTitle');
            for (let i = giveGifts.length - 1; i >= 0; i--) {
                giveGifts[i].click();
            }
            $('#mz-actList').show();
            $('#mz-title').hide();
        }
        $('#mz-normal').hide();
        $('#mz-submit').show();
        giveNum[0].click();
        return
    }
    if(canSettle == 1){
        CONFIM = false;
        gocartJS(val,notSubmitOrderOrgIds);
    }else{
        if(CONFIM === false){
            CONFIM = true;
            $('#confimModal').modal('show');
            // $('#confimModal .sure').click(function () {
            //     gocartJS(val,notSubmitOrderOrgIds);
            // });
            $('#confimModal .sure').off('click').on('click', function () {
                gocartJS(val, notSubmitOrderOrgIds);
            });
        }else {
            CONFIM = false;
        }
    }

    //gocartJS(val);
    // if (CONFIM === false) {9
     //     CONFIM = true;
    //     $('#confimModal').modal('show');
    //     $('#confimModal .sure').click(function () {
    //         gocartJS(val);
    //     });
    // }else {
    //     CONFIM = false;
    //     gocartJS(val);
    // }
}

function gocartJS(qty,notSubmitOrderOrgIds) {
    var qty = parseFloat(qty);
    if (qty==0)
    {
        $.alert({"title":"提示","body":"请选择需要采购的商品去结算！"});

    }else{

        if(notSubmitOrderOrgIds){
            //$.alert({"title":"提示","body":"不够起送价的店铺商品将不能参与订单提交，请注意结算页订单金额和优惠变化"});
            //layer.tips("不够起送价的店铺商品将不能参与订单提交，请注意结算页订单金额和优惠变化")
            layer.msg("不够起送价的店铺商品将不能参与订单提交，请注意结算页订单金额和优惠变化")
            setTimeout(function (){
                gocartJsNext(qty,notSubmitOrderOrgIds)
            },2000)
        }else {
            gocartJsNext(qty,notSubmitOrderOrgIds)
        }

    }
}

function gocartJsNext(qty,notSubmitOrderOrgIds){
    var index=layer.load(2);
    var ids = getCheckIds();
    $.ajax({
        url: "/merchant/center/order/gotoSettle.json",
        type: "POST",
        dataType: "json",
        data: {
            storeStatus: true,
            notSubmitOrderOrgIds:notSubmitOrderOrgIds
        },
        success: function(result){
			layer.close(index);
            if(result.status == "success"){
                if (result.dialogMsg) {
					$.alert({ title: '提示', body: result.dialogMsg, okHidden: function() {
						var claimVoucherNum = result.claimVoucherNum;
						var uri = "/merchant/center/order/settle.htm?settleType=2&storeStatus=true&useRedPacket=true&notSubmitOrderOrgIds="+notSubmitOrderOrgIds;
						if(claimVoucherNum){
							uri = uri+"&claimVoucherNum="+claimVoucherNum
						}
						window.location.href=uri;
					} })
				} else {
					var claimVoucherNum = result.claimVoucherNum;
					var uri = "/merchant/center/order/settle.htm?settleType=2&storeStatus=true&useRedPacket=true&notSubmitOrderOrgIds="+notSubmitOrderOrgIds;
					if(claimVoucherNum){
						uri = uri+"&claimVoucherNum="+claimVoucherNum
					}
					window.location.href=uri;
				}
            }else {

                if (result.errorCode == 3) {
                    $(".bodybox .valid .checkbox-pretty").checkbox().each(function(){
                        if($(this).hasClass("checked")){
                            $(this).checkbox("uncheck");
                        }
                    });
                    $(".all").checkbox().checkbox("uncheck");
                }

                if (result.errorCode == 2) {

                    $.alert({"title":"提示","body":result.errorMsg, okHidden:function () {
                            location.href = "/merchant/center/license/findLicenseCategoryInfo.htm";
                        }});
                    layer.close(index);
                } else if (result.errorCode == 3) {
                    $.ajax({
                        url: "list.htm?r=" + Math.random(),
                        type: "POST",
                        dataType: "html",
                        traditional :true,
                        data: {
                            storeStatus: true
                        },
                        success: function(cartResult){
                            $(".main").html(cartResult);

                            var cartVarietyNum = $('#cartVarietyNum').text();
                            $("#cartNumberLi").html(cartVarietyNum);
                            $("#rigthCartNum").html(cartVarietyNum);
                            $("#cartNumberDiv").html(cartVarietyNum);

                            $.alert({"title":"提示","body":result.errorMsg,okHidden:function () {
                                    var shixiaoH = $("#shixiaobox").offset().top;
                                    $('html,body').animate({
                                        scrollTop: shixiaoH
                                    }, 'slow');
                                }});
                            layer.close(index);
                        }
                    });
                }else if (result.errorCode == 404) {
                    $.confirm({
                        body: "您已申请注销，暂无法下单。撤销申请后，可继续下单，是否撤销申请？",
                        cancelBtn: "否",
                        okBtn: "是",
                        okHidden: function () {
                            $.ajax({
                                url: "/merchant/merchantBusiness/cancelDropAccount",
                                type: "POST",
                                dataType: "json",
                                success: function(result){
                                    if(result.status == "success") {
                                        // $.alert('已撤销');
                                        alert('已撤销')
                                    }else {
                                        $.alert(result.errorMsg);
                                    }
                                }
                            })
                        },
                    });
                }else {
                    $.alert({"title":"提示","body":result.errorMsg});
                    layer.close(index);
                }
            }
            // layer.close(index);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            layer.close(index);
            var sessionStatus = XMLHttpRequest.getResponseHeader('SessionStatus');
            if (sessionStatus === 'sessionTimeOut') {
                return;
            }
            $.alert("网络异常");
        }
    });
}

//拼团去结算
function groupSettle(qty,skuId, isWholesale,shopCode = 0,supportSuiXinPin = 0,isThirdCompany = 0) {
    var qty = parseFloat(qty);
    if (qty==0)
    {
        $.alert({"title":"提示","body":"请选择需要采购的商品去结算！"});

    }else{
        var index=layer.load(2);
        var ids = getCheckIds();
        $.ajax({
            url: "/merchant/center/order/group/preSettle.json",
            type: "POST",
            dataType: "json",
            data: {
                storeStatus: true,
                productNum:qty,
                skuId:skuId,
				isPiGou: isWholesale
            },
            success: function(result){
				layer.close(index);
                if(result.status == "success"){
					var biz = '7'
					if (isWholesale == 1) {
						biz = '10'
					}
                    if (result.dialogMsg) {
						$.alert({ title: '提示', body: result.dialogMsg, okHidden: function() {
							var claimVoucherNum = result.claimVoucherNum;
							var uri = "/merchant/center/order/group/settle.htm?storeStatus=true&bizSource=" + biz +"&useRedPacket=true&productNum="+qty+"&skuId="+skuId+"&shopCodes="+shopCode+"&isSupportOldSxp="+supportSuiXinPin+"&isThirdCompany="+isThirdCompany;
							if(claimVoucherNum){
								uri = uri+"&claimVoucherNum="+claimVoucherNum
							}
							window.location.href=uri;
						} })
					} else {
						var claimVoucherNum = result.claimVoucherNum;
						var uri = "/merchant/center/order/group/settle.htm?storeStatus=true&bizSource=" + biz + "&useRedPacket=true&productNum="+qty+"&skuId="+skuId+"&shopCodes="+shopCode+"&isSupportOldSxp="+supportSuiXinPin+"&isThirdCompany="+isThirdCompany;
						if(claimVoucherNum){
							uri = uri+"&claimVoucherNum="+claimVoucherNum
						}
						window.location.href=uri;
					}
                }else {

                    if (result.errorCode == 3) {
                        $(".bodybox .valid .checkbox-pretty").checkbox().each(function(){
                            if($(this).hasClass("checked")){
                                $(this).checkbox("uncheck");
                            }
                        });
                        $(".all").checkbox().checkbox("uncheck");
                    }

                    if (result.errorCode == 2) {

                        $.alert({"title":"提示","body":result.errorMsg, okHidden:function () {
                                location.href = "/merchant/center/license/findLicenseCategoryInfo.htm";
                            }});
                        layer.close(index);
                    }else if (result.errorCode == 404) {
                        $.confirm({
                            body: "您已申请注销，暂无法下单。撤销申请后，可继续下单，是否撤销申请？",
                            cancelBtn: "否",
                            okBtn: "是",
                            okHidden: function () {
                                $.ajax({
                                    url: "/merchant/merchantBusiness/cancelDropAccount",
                                    type: "POST",
                                    dataType: "json",
                                    success: function(result){
                                        if(result.status == "success") {
                                            // $.alert('已撤销');
                                            alert('已撤销')
                                        }else {
                                            $.alert(result.errorMsg);
                                        }
                                    }
                                })
                            },
                        });
                    } else {
                        $.alert({"title":"提示","body":result.errorMsg});
                        layer.close(index);
                    }
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.close(index);
                var sessionStatus = XMLHttpRequest.getResponseHeader('SessionStatus');
                if (sessionStatus === 'sessionTimeOut') {
                    return;
                }
                $.alert("网络异常");
            }
        });
    }
}

function getCheckIds() {
    var ids = getCheckIdsArray();
    return ids.join(",");
}

function getCheckIdsArray() {
    var ids = [];
    $(".bodybox .checkbox-pretty").each(function(){
        if($(this).hasClass("checked")){
            ids.push($(this).find(":checkbox").val());
        }
    });
    return ids;
}
var timerProgress;
var processBar = 0;
function getSignaturesDownload(code, branchCode){
    //根据订单号查询明细
    var layer_msg = layer.msg('查询中，请稍等...',{
        time:0
    });
    $.ajax({
        type: "GET",
        url: "/shop/product/downloadSignature",
        data: {
            "code" : code,
            "branchCode" : branchCode
        },
        dataType: "json",
        async: false,
        success: function (data) {
            layer.close(layer_msg);
            if(data.status === "fail" || !data.data){
                $.alert({"title":"温馨提示","body":"当前订单商品暂未上传相关资料，请联系客服人员进行咨询。客服电话：400-0505-111"});
                return;
            }
            // parent.layer.msg('正在为您下载商品资质文件，请稍候...');
            $('#progressModel').modal('show');
            processBar = 0;
            $('#progress_rate').css('width',Math.floor(processBar)+"%");
            $('#progress_text').html(Math.floor(processBar)+"%");
            timerProgress = setInterval(function(){
                downloadSignatureResult(data.data.taskId)
            }, 1000);
        },
        error: function () {
            parent.layer.msg('网络异常');
            layer.close(layer_msg);
        }
    });
}
function downloadSignatureResult(id) {
    $.ajax({
        type: "GET",
        url: "/shop/product/downloadSignatureResult",
        data: {
            "taskId" : id,
        },
        dataType: "json",
        async: false,
        success: function (data) {
            if(data.status === "fail" || data.data.taskResult.status == 2){
                $.alert({"title":"温馨提示","body":"当前商品相关资料下载失败，请联系客服人员进行咨询。客服电话：400-0505-111"});
                clearInterval(timerProgress);
                return;
            }
            // parent.layer.msg('正在为您下载商品资质文件，请稍候...');
            if(data.data.taskResult.progress && data.data.taskResult.progress == 100){
                $('#progressModel').modal('hide');
                $('#progress_rate').css('width',"0%");
                $('#progress_text').html("0%");
                clearInterval(timerProgress);
                window.open(data.data.taskResult.url);
            }else{
                var processBars = Number(data.data.taskResult.progress);
                $('#progress_rate').css('width',Math.floor(processBars)+"%");
                $('#progress_text').html(Math.floor(processBars)+"%");
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            parent.layer.msg('网络异常');
        }
    });
}
/* $("#fullGiveModalBtn").click(function () {// 选择赠品按钮
    $("#fullGiveModal").modal('show')

})
$("#someFullGiveModalBtn").click(function(){
    $("#someFullGiveModal").modal('show')
})
 */
//多个满赠弹窗tab切换

$('.elec').first().show();

// 为每个选项卡绑定点击事件
$('.navBox .nav').click(function(){
    // 移除所有选项卡的active类，并给当前点击的选项卡添加active类
    $('.navBox .nav').removeClass('active');
    $(this).addClass('active');

    // 获取当前选项卡的索引
    var index = $(this).index();

    // 隐藏所有内容，只显示对应索引的内容
    $('.elec').hide().eq(index).show();
});
let giftList = [];
let abandonStatus = false;
/**
 * 点击加号和减号
 * @param { string } id 
 * @param { 'add' | 'delete' } type 
 */
function fullNumChange(id, type) {
    if (abandonStatus) return;
    const list = giftList.filter(item => item.id == id);
    console.log(list[0]);
    
    let val = Number($('#num-' + id).val());
    val = Number.isNaN(val) ? 0 : val;
    val = type == 'add' ? val + 1 : val - 1;
    /* val = Number.isNaN(val) ? list[0].orderGiveMinQty : val < list[0].orderGiveMinQty ? list[0].orderGiveMinQty : val; */
    if (list[0].orderGiveMinQty) {
        //有赠送下限
        val = val < list[0].orderGiveMinQty ? list[0].orderGiveMinQty : val;
    } else {
        val = val < 0 ? 0 : val;
    }
    if (list[0].orderGiveMaxQty && list[0].orderGiveMaxQty != -1) {
        val = val > list[0].orderGiveMaxQty ? list[0].orderGiveMaxQty : val;
    }
    const canSelect = $('#canSelectNum').text();
    const selected = $('#selectedNum').text();
    console.log(selected + (type == 'add' ? 1 : -1));
    
    if (Number(selected) + (type == 'add' ? 1 : -1) > Number(canSelect)) {
        //超出限制
        parent.layer.msg(`还可选择${canSelect - selected}件商品，请重新选择`);
        return;
    }
    if (!list[0].isSelected) {
        $.ajax({
            url: '/app/selectItemGiftPool',
            type: 'get',
            data: {
                bizSource: $('#bizSource').val(),
                promoId: $('#promoId').val(),
                amount: val,
                skuId: id,
                merchantId: $('#merchantId').val()
            },
            success(data) {
                if (data.status == 'success') {
                    document.getElementById('check-' + id).checked = true;
                } else {
                    parent.layer.msg('添加失败');
                    fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
                }
            },
            error(err) {
                fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
                parent.layer.msg('网络异常');
            }
        })
    }
    $.ajax({
        url: '/app/changeGiftPool',
        type: 'get',
        data: {
            bizSource: $('#bizSource').val(),
            promoId: $('#promoId').val(),
            amount: val,
            skuId: id,
            totalSelectedNum: $('#canSelectNum').text(),
            merchantId: $('#merchantId').val()
        },
        success(data) {
            if (data.status == 'success') {
            } else {
                parent.layer.msg('添加失败');
            }
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
        },
        error(err) {
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
            parent.layer.msg('网络异常');
        }
    })
}
//放弃赠品
function abandonGift() {
    document.getElementById('abandon').checked
    let url = abandonStatus ? '/app/deleteAutoGiveUpActFlag' : '/app/addAutoGiveUpActFlag'
    
    $.ajax({
        url: url,
        type: 'get',
        data: {
            bizSource: $('#bizSource').val(),
            promoId: $('#promoId').val(),
            merchantId: $('#merchantId').val()
        },
        success(data) {
            if (data.status == 'success') {
            } else {
                parent.layer.msg('添加失败');
            }
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
        },
        error(err) {
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
            parent.layer.msg('网络异常');
        }
    })
}
//修改数量
function fullNumInput(id) {
    if (abandonStatus) return;
    const canSelect = $('#canSelectNum').text();
    const selected = $('#selectedNum').text();
    const list = giftList.filter(item => item.id == id);
    const curSelected = list[0].selectNum ? list[0].selectNum : 0;
    const target = document.getElementById('num-' + id)
    if (!(/^([0]|[1-9][0-9]*)?$/.test(target.value))) {
        target.value = list[0].selectNum;
    }
    if (!target.value) return
    if (list[0].orderGiveMinQty && target.value  < list[0].orderGiveMinQty) {
        //有赠送下限
        parent.layer.msg(`当前赠品至少需选择${list[0].orderGiveMinQty}个`);
        target.value = curSelected;
        return
        
    }
    if (list[0].orderGiveMaxQty && list[0].orderGiveMaxQty != -1 && target.value  > list[0].orderGiveMaxQty) {
        parent.layer.msg(`赠品最多可选择${list[0].orderGiveMaxQty}个`);
        target.value = curSelected;
        return
    }
    if (target.value - curSelected + Number(selected) > Number(canSelect)) {
        target.value = curSelected;
        //超出限制
        parent.layer.msg(`还可选择${canSelect - selected}件商品，请重新选择`);
        return
    }
    if (!list[0].isSelected) {
        $.ajax({
            url: '/app/selectItemGiftPool',
            type: 'get',
            data: {
                bizSource: $('#bizSource').val(),
                promoId: $('#promoId').val(),
                amount: target.value,
                skuId: id,
                merchantId: $('#merchantId').val()
            },
            success(data) {
                if (data.status == 'success') {
                    document.getElementById('check-' + id).checked = true;
                } else {
                    parent.layer.msg('添加失败');
                    fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
                }
            },
            error(err) {
                fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
                parent.layer.msg('网络异常');
            }
        })
    }
    $.ajax({
        url: '/app/changeGiftPool',
        type: 'get',
        data: {
            bizSource: $('#bizSource').val(),
            promoId: $('#promoId').val(),
            amount: target.value,
            totalSelectedNum: $('#canSelectNum').text(),
            skuId: id,
            merchantId: $('#merchantId').val()
        },
        success(data) {
            if (data.status == 'success') {

            } else {
                parent.layer.msg('添加失败');
            }
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
        },
        error(err) {
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
            parent.layer.msg('网络异常');
        }
    })
}
let fullGiveLoading = false;
//满赠弹框取消
function fullGiveClose() {
    if (fullGiveLoading) return;
    fullGiveLoading = true;
    console.log('fullGiveClose');
    $.ajax({
        url: "list.htm?r=" + Math.random(),
        type: "POST",
        dataType: "html",
        traditional :true,
        data: {
            storeStatus: true
        },
        success: function(cartResult){
            fullGiveLoading = false;
            document.body.style.overflow = 'auto';
            $(".main").html(cartResult);

            var cartVarietyNum = $('#cartVarietyNum').text();
            $("#cartNumberLi").html(cartVarietyNum);
            $("#rigthCartNum").html(cartVarietyNum);
            $("#cartNumberDiv").html(cartVarietyNum);

            $.alert({"title":"提示","body":result.errorMsg,okHidden:function () {
                var shixiaoH = $("#shixiaobox").offset().top;
                $('html,body').animate({
                    scrollTop: shixiaoH
                }, 'slow');
            }});
        },
        error: function() {
            fullGiveLoading = false;
        }
    });
}
//满赠弹框提交
function fullGiveSubmit() {
    console.log('fullGiveSubmit');
    if (fullGiveLoading) return;
    fullGiveLoading = true;
    $.ajax({
        url: "list.htm?r=" + Math.random(),
        type: "POST",
        dataType: "html",
        traditional :true,
        data: {
            storeStatus: true
        },
        success: function(cartResult){
            fullGiveLoading = false;
            document.body.style.overflow = 'auto';
            $(".main").html(cartResult);

            var cartVarietyNum = $('#cartVarietyNum').text();
            $("#cartNumberLi").html(cartVarietyNum);
            $("#rigthCartNum").html(cartVarietyNum);
            $("#cartNumberDiv").html(cartVarietyNum);

            $.alert({"title":"提示","body":result.errorMsg,okHidden:function () {
                var shixiaoH = $("#shixiaobox").offset().top;
                $('html,body').animate({
                    scrollTop: shixiaoH
                }, 'slow');
            }});
        },
        error: function() {
            fullGiveLoading = false;
        }
    });
}
//选择商品
function fullGiveClick(id) {
    //
    if (abandonStatus) return;
    const list = giftList.filter(item => item.id == id);
    const target = document.getElementById('check-' + id);
    const value = document.getElementById('num-' + id);
    value.value = value.value ? value.value : list[0].orderGiveMinQty ? list[0].orderGiveMinQty : 1;
    if (Number(value.value) + Number($('#selectedNum').text()) > Number($('#canSelectNum').text()) && !list[0].isSelected) {
        parent.layer.msg(`最多可选择${$('#canSelectNum').text()}件赠品`);
        target.checked = false;
        return
    }
    target.checked = false;
    let url = list[0].isSelected ? '/app/cancelItemGiftPool' : '/app/selectItemGiftPool'
    $.ajax({
        url: url,
        type: 'get',
        data: {
            bizSource: $('#bizSource').val(),
            promoId: $('#promoId').val(),
            amount: list[0].isSelected ? 0 : value.value,
            skuId: id,
            merchantId: $('#merchantId').val()
        },
        success(data) {
            if (data.status == 'success') {

            } else {
                parent.layer.msg('添加失败');
            }
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
        },
        error(err) {
            fullNumSelectGive($('#bizSource').val(), $('#selectedNum').text(), $('#canSelectNum').text(), $('#promoId').val())
            parent.layer.msg('网络异常');
        }
    })
}
function fullNumSelectGive(bizSource, giftPoolActHasSelectedNum, giftPoolActTotalSelectedNum, promoId, noRender) {
    if (document.getElementById("mz-normal").style.display == 'none') {
        $('#mz-actList').show();
        $('#mz-title').hide();
    } else {
        $('#mz-actList').hide();
        $('#mz-title').show();
    }
    //禁止蒙层下滚动
    document.body.style.overflow = 'hidden';
    //id: fullGiveModal      弹框
    //id: abandon   放弃按钮
    //id: canSelectNum  总共可选
    //id: selectedNum   当前已选
    $('#canSelectNum').text(giftPoolActTotalSelectedNum);
    $('#promoId').val(promoId);
    $('.i-actList-active').attr('class', '');
    $('#act-' + promoId).attr('class', 'i-actList-active')
    let selected = 0;
    $.ajax({
        url: '/promotion/promo/getGiftSkuPool',
        type: 'post',
        dataType: "json",
        data: {
            bizSource: $('#bizSource').val(),
            promoId: promoId,
            merchantId: $('#merchantId').val(),
        },
        success(data) {
            if (data.status === "success") {
                //是否放弃赠品
                document.getElementById('abandon').checked = data.data.isGiveUpGift ? true : false;
                abandonStatus = data.data.isGiveUpGift ? true : false;
                giftList = data.data.productList;
                giftList.forEach(item => {
                    selected += item.isSelected ? item.selectNum ? item.selectNum : 0 : 0;
                })
                $('#selectedNum').text(abandonStatus ? 0 : selected)
                //已选
                if (Number(giftPoolActTotalSelectedNum) == Number(selected)) {
                    $('#actStatus-' + promoId).attr('style', 'background: #26bb53;')
                    $('#actStatus-' + promoId).text('已选')
                } else if (abandonStatus || Number(selected) == 0) {
                    $('#actStatus-' + promoId).attr('style', 'background: #9f9f9f;')
                    $('#actStatus-' + promoId).text('未选')
                } else {
                    $('#actStatus-' + promoId).text('部分选')
                    $('#actStatus-' + promoId).attr('style', 'background: #FE8535;')
                }
                //未选
                //部分选
                if (!noRender) {
                    renderGift(giftList);
                }
            }
        },
        error(err) {
            parent.layer.msg('网络异常');
        }
    })
}
function renderGift(productList) {
    //渲染选择赠品弹框
    const str = productList.map(item => {
        let limit = '<div>'
        if (item.orderGiveMinQty && item.orderGiveMaxQty && item.orderGiveMinQty != -1 && item.orderGiveMaxQty != -1) {
            limit += item.orderGiveMinQty + '盒起送,限' + item.orderGiveMaxQty + '盒</div>'
        } else if (item.orderGiveMinQty && item.orderGiveMinQty != -1) {
            limit += item.orderGiveMinQty + '盒起送</div>'
        } else if (item.orderGiveMaxQty && item.orderGiveMaxQty != -1) {
            limit += '限' + item.orderGiveMaxQty + '盒</div>'
        } else {
            limit = '';
        }
        return `
            <div class="i-item">
                <div class="checkbox">
                    <input id="check-${item.id}" ${abandonStatus || item.status != 1 ? 'disabled' : ''} class="${abandonStatus || item.status != 1 ? 'i-disabled' : ''}" onclick="fullGiveClick('${item.id}')" style="transform: translateY(2px);" type="checkbox" name="abandon" ${item.isSelected ? 'checked' : ''}>
                </div>
                <div class="i-info">
                    <div class="i-img">
                        ${item.status == 1 ? '' : '<div style="position: absolute;border-radius: 50%;width: 70%;height: 70%;background: #000000a8;display: flex;justify-content: center;align-items: center;color: white;top: 50%;left: 50%;transform: translate(-50%, -50%);">已售罄</div>'}
                        <img style="width: 100%;height: 100%;" src="${item.imageUrl}" alt="">
                    </div>
                    <div style="width: 0;flex-grow: 1;display: flex;flex-direction: column;">
                        <p style="font-weight: 600;">${item.showName}  ${item.spec}</p>
                        <p style="font-size: 12px;flex-grow: 1;height: 0;color: #929292;">效期：${item.farEffect}</p>
                        <p style="font-size: 12px;">
                            <span style="color: red;font-size:14px;">￥${item.actPrice}元</span>
                            <span>/${item.productUnit}</span>
                            <span style="margin-left: 20px; color: #929292;">原价: ￥${item.fob}</span>
                        </p>
                    </div>
                </div>
                <div style="display: ${item.status == 1 ? 'auto' : 'none'}">
                    <div class="i-numBtn ${abandonStatus ? 'i-disabled' : ''}" >
                        <div onclick="fullNumChange('${item.id}', 'delete')">
                            <span style="font-size: 20px;;line-height: 30px;">-</span>
                        </div>
                        <input id="num-${item.id}" class="${abandonStatus ? 'i-disabled' : ''}" ${abandonStatus ? 'disabled' : ''} type="text" value="${item.selectNum ? item.selectNum : ''}" onblur="fullNumInput('${item.id}')">
                        <div onclick="fullNumChange('${item.id}', 'add')">
                            <span style="font-size: 20px;line-height: 30px;">+</span>
                        </div>
                    </div>
                    ${ limit }
                </div>
                ${item.status == 1 ? '' : '<div style="padding: 0 15px;font-size:16px;color:#8e8e8e;">商品已售罄</div>'}
            </div>
        `
    }).join('');
    $('#productList').html(str);
    giftList.forEach(item => {
        document.getElementById('check-' + item.id).checked = item.isSelected;
    })
    $('#fullGiveModal').show();
}
$(function () {
    console.log('462--------------')
    var text = $('.crossStoreVoucherTips').attr('data_tips')
    console.log(text)
    if (text) {
        var reg = /(\d+.\d+?元)/g
        var res = text.match(reg)
        console.log(res)
        text = text.replace(reg,'<span>$1</span>')

        $('.crossStoreVoucherTips').html(text)
    }
})
