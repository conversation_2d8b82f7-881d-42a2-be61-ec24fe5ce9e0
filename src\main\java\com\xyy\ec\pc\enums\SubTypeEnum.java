package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;


/**
 * 
 * <AUTHOR>
 *
 */
public enum SubTypeEnum {


	/**
	 *  '1:表示有货提醒业务类型，2：降价提醒';
	 */
    HASGOODS(1,"有货提醒的"),
    DOWNPRICE(2,"降价提醒")
    ;

    private int id;
    private  String value;

    SubTypeEnum(int id, String value){
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, SubTypeEnum> eMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(SubTypeEnum control : SubTypeEnum.values()) {
        	eMaps.put(control.getId(), control);
            maps.put(control.getId(),control.getValue());
        }
    }

    public static String get(int id) {
        return eMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
