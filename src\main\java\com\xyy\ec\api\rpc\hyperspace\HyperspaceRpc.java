package com.xyy.ec.api.rpc.hyperspace;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.base.log.ApiLog;
import com.xyy.ec.marketing.client.api.MarketingQueryApi;
import com.xyy.ec.marketing.common.domain.ActivityInfo;
import com.xyy.ec.marketing.hyperspace.api.common.dto.ApiActivityDto;
import com.xyy.ec.marketing.hyperspace.api.common.service.ParticipationMarketingActivityService;
import com.xyy.ec.pc.rpc.MarketActivityPackageRpc;
import com.xyy.ms.promotion.business.dto.activitypackage.ActivityPackageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/20 14:13
 * @Description:
 */
@Component
@Slf4j
public class HyperspaceRpc {
    @Reference(version = "1.0.0")
    ParticipationMarketingActivityService pmaService;
    @Reference(version = "1.0.0")
    private MarketingQueryApi marketingQueryApi;
    @Autowired
    private MarketActivityPackageRpc marketActivityPackageRpc;
    public List<Long> querySuiXinPinTopSku(Long merchantId, String shopCode) {
        ApiRPCResult<List<Long>> listApiRpcResult = marketingQueryApi.querySuiXinPinTopSku(merchantId, shopCode);
        if (null == listApiRpcResult || !listApiRpcResult.isSuccess()){
            log.info("querySuiXinPinTopSku_接口异常_merchantId={},shopCode={},listApiRpcResult={}", merchantId, shopCode, JSON.toJSONString(listApiRpcResult));
            return Lists.newArrayList();
        }
        if (null == listApiRpcResult.getData()){
            return Lists.newArrayList();
        }
        return listApiRpcResult.getData();
    }

    /**
     * 查询店铺活动
     * @return
     */
    @ApiLog
    public List<ApiActivityDto> findMarketingActivityForApi(String branchCode, Long merchantId, String shopCode,Long actId){
        List<ApiActivityDto> list =  pmaService.findMarketingActivityForApi(branchCode,merchantId,shopCode,actId);
        if (CollectionUtils.isNotEmpty(list)) {
            Collections.sort(list, new Comparator<ApiActivityDto>() {
                @Override
                public int compare(ApiActivityDto o1, ApiActivityDto o2) {
                    return o1.getEndTime().compareTo(o2.getEndTime());
                }
            });
        }
            return list;
        }

    /**
     * 查询活动商品
     * @param actId
     * @return
     */
    @ApiLog
    public ActivityInfo getShopMarketingActivityById(Long actId){
        if (actId==null){
            return null;
        }
        return pmaService.getShopMarketingActivityById(actId);
    }

    @ApiLog
    public boolean hasAct(String branchCode, Long merchantId, String shopCode){
        if (merchantId==null){
            return false;
        }
        List<ApiActivityDto> list =  pmaService.findMarketingActivityForApi(branchCode,merchantId,shopCode,null);
        return CollectionUtils.isNotEmpty(list);
    }

    /**
     * 校验是否有店铺套餐活动
     * @param shopCode
     * @param merchantId
     * @return
     */
    @ApiLog
    public boolean hasShopPackageActivity(String shopCode, Long merchantId){
        if (merchantId==null){
            return false;
        }
        List<ActivityPackageVo> list =  marketActivityPackageRpc.getShopActivityPackageList(shopCode,merchantId);
        return CollectionUtils.isNotEmpty(list);
    }
}
