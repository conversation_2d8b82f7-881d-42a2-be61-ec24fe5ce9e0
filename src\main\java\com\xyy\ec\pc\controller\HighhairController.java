package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.xyy.ec.layout.buinese.api.ExhibitionBuineseApi;
import com.xyy.ec.layout.buinese.api.HighhairManageBuineseApi;
import com.xyy.ec.layout.buinese.dto.HighhairManageBuineseDto;
import com.xyy.ec.layout.buinese.model.HighhairListProduct;
import com.xyy.ec.merchant.bussiness.api.AgreementForOrderBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.AgreementScheduleBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.api.OrderGaoMaoStatisticsBusinessApi;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.enums.LayoutMerchantStatusEnum;
import com.xyy.ec.pc.rpc.MarketActivityPackageRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.layout.LayoutBaseService;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.product.business.dto.ProductConditionDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import com.xyy.ec.product.business.dto.listOfSku.ProductForCmsDTO;
import com.xyy.ec.product.business.ecp.out.layout.api.ProductForLayoutApi;
import com.xyy.ms.promotion.business.api.common.activitypackage.ActivityPackageBusinessApi;
import com.xyy.ms.promotion.business.api.common.activitypackage.vo.ActivityPackageReq;
import com.xyy.ms.promotion.business.common.response.PromoResp;
import com.xyy.ms.promotion.business.dto.activitypackage.ActivityPackageVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

@Controller
@RequestMapping("/highhair")
public class HighhairController extends BaseController {

    Logger LOGGER = LoggerFactory.getLogger(HighhairController.class);
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private LayoutBaseService layoutBaseService;

    @Reference(version = "1.0.0")
    private ActivityPackageBusinessApi activityPackageBusinessApi;

    @Autowired
    private MarketActivityPackageRpc marketActivityPackageRpc;

    @Reference(version = "1.0.0")
    private HighhairManageBuineseApi highhairManageBuineseApi;

    @Reference(version = "1.0.0")
    private ExhibitionBuineseApi exhibitionBuineseApi;

    @Reference(version = "1.0.0",timeout = 10000)
    private ProductForLayoutApi productForLayoutApi;

    @Reference(version = "1.0.0",timeout = 10000)
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private AgreementForOrderBussinessApi agreementForOrderBussinessApi;

    @Reference(version = "1.0.0")
    private OrderGaoMaoStatisticsBusinessApi orderGaoMaoStatisticsBusinessApi;

    private List<String> exhibitions = Lists.newArrayList();

    private String image = null;

    @RequestMapping(value = {"/index.htm"})
    public ModelAndView index(ModelMap modelMap, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            if (merchant != null) {
                merchantId = merchant.getId();
                AgreementScheduleBussinessDto agreec = this.showSchedule();
                modelMap.put("agreec", agreec);
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
//            ActivityPackageReq activityPackageReq = new ActivityPackageReq();
//            activityPackageReq.setMerchantId(merchantId);
//            activityPackageReq.setType(1);
//            activityPackageReq.setMark(3);
//            PromoResp promoResp = activityPackageBusinessApi.queryOnePackage(activityPackageReq);
            List<ActivityPackageVo> allActivityPackage = marketActivityPackageRpc.getAllActivityPackage(merchantId, branchCode, 1, 3);
            ActivityPackageVo activityPackageVo = null;
            if (CollectionUtils.isNotEmpty(allActivityPackage)) {
                activityPackageVo = allActivityPackage.get(0);
            }
            String purchaseAmount = orderGaoMaoStatisticsBusinessApi.getGaoMaoTotalAumontByMerchantId(merchantId);
            modelMap.put("purchaseAmount", purchaseAmount);
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
//            modelMap.put("promoResp", promoResp.getData());
            modelMap.put("promoResp", activityPackageVo);
            modelMap.put("type",1);
            modelMap.put("mark",3);
            modelMap.put("branchCode", branchCode);
            HighhairManageBuineseDto highhairManageBuineseDto = highhairManageBuineseApi.getUseingHighhairManage(merchantId,branchCode);
            if (highhairManageBuineseDto != null) {
                String  sortingMenu = highhairManageBuineseDto.getSortingMenu();
                if(StringUtils.isNotEmpty(sortingMenu)){
                    JSONArray jsonArray = JSONObject.parseArray(sortingMenu);
                    if(jsonArray != null){
                        if (jsonArray.size() > 0){
                            exhibitions = Lists.newArrayList();
                            for (int i =0;i<jsonArray.size();i++){
                                // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                                JSONObject job = jsonArray.getJSONObject(i);
                                if(job.containsKey("sortid")){
                                    exhibitions.add(job.get("sortid") == null ? "" :job.get("sortid").toString());
                                }
                            }
                        }
                    }
                }
                LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
                if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                    List<ListProduct> recommendationListProducts = highhairManageBuineseDto.getRecommendationListProducts();
                    recommendationListProducts = layoutBaseService.setProductProperties(recommendationListProducts, statusEnum, request);
                    highhairManageBuineseDto.setRecommendationListProducts(recommendationListProducts);

                    List<HighhairListProduct> highhairhotListProducts = highhairManageBuineseDto.getHighhairhotListProducts();
                    highhairhotListProducts = layoutBaseService.setProductProperties(highhairhotListProducts, statusEnum, request);
                    highhairManageBuineseDto.setHighhairhotListProducts(highhairhotListProducts);

                    List<HighhairListProduct> highhairnewListProducts = highhairManageBuineseDto.getHighhairnewListProducts();
                    highhairnewListProducts = layoutBaseService.setProductProperties(highhairnewListProducts, statusEnum, request);
                    highhairManageBuineseDto.setHighhairnewListProducts(highhairnewListProducts);
                }
            }
            image = highhairManageBuineseDto != null ? highhairManageBuineseDto.getBannerImgUrl() : null;
            // 0没有配置高毛 1配置高毛
            String highhairDataResultFlag = highhairManageBuineseDto != null ? "1" : "0";
            modelMap.put("highhairDataResultFlag", highhairDataResultFlag);
            modelMap.put("hmb",highhairManageBuineseDto);
            return new ModelAndView("/highHair/index.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("PC高毛加载异常:", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    @Deprecated
    @RequestMapping(value = {"/type.htm"})
    public ModelAndView type(ModelMap modelMap, HttpServletRequest request) {
        String type = request.getParameter("type");
        String dataType = request.getParameter("dataType");
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            if (merchant != null) {
                merchantId = merchant.getId();
            }

            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);

            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);

            modelMap.put("pageSize",20);
            int page = 1;
            if(request.getParameter("page")!=null && !request.getParameter("page").equals("")){
                page= Integer.parseInt(request.getParameter("page"));
            }
            modelMap.put("currPage",page);


            ModelMap newmodelMap  = new ModelMap();

            if(dataType.equals("1")){
                ListSkuSearchData listSkuSearchData = exhibitionBuineseApi.getExhibitionProductSkuInfoWrapper(type, merchantId, branchCode, page, 20);
                double shang = 0L;
                double yus = 0L;
                List<ListProduct> skuDtoList;
                Long count;
                if (listSkuSearchData != null) {
                    shang = listSkuSearchData.getCount() / 20;
                    yus = listSkuSearchData.getCount() % 20;
                    skuDtoList = listSkuSearchData.getSkuDtoList();
                    count = listSkuSearchData.getCount();
                } else {
                    skuDtoList = new ArrayList<>(0);
                    count = 0L;
                }
                LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
                if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                    skuDtoList = layoutBaseService.setProductProperties(skuDtoList, statusEnum, request);
                }
                modelMap.put("skuList", skuDtoList);
                modelMap.put("total", count);
                int pageCount = 0;
                if(yus >0){
                    pageCount = (int)shang + 1;
                }else{
                    pageCount =(int)shang;
                }
                modelMap.put("pageCount",pageCount);
                modelMap.put("dataType",dataType);
                newmodelMap = modelMap;
            }else if(dataType.equals("2")){
                newmodelMap  =  this.findSkuInfo(modelMap,new ProductForCmsDTO(),page,20, request);
                if(newmodelMap==null){
                    LOGGER.error("PC高毛加载异常:");
                    return new ModelAndView("/error/500.ftl");
                }else{
                    int shang = (int)(newmodelMap.get("total")) / 20;
                    int yus = (int)(newmodelMap.get("total")) % 20;
                    int pageCount = 0;
                    if(yus >0){
                        pageCount = (int)shang + 1;
                    }else{
                        pageCount =(int) shang;
                    }
                    newmodelMap.put("pageCount",pageCount);
                    newmodelMap.put("dataType",dataType);
                }
            }
            return new ModelAndView("/highHair/type.ftl", newmodelMap);
        } catch (Exception e) {
            LOGGER.error("PC高毛加载异常:", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    @RequestMapping(value = {"/hotSale.htm"})
    public ModelAndView hotSale(ModelMap modelMap, HttpServletRequest request) {
        String img = request.getParameter("img");
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            this.inintImgAndExhibition(merchant, merchantId, branchCode, request);
            // 0没有配置高毛 1配置高毛
            String highhairDataResultFlag = CollectionUtil.isNotEmpty(exhibitions) ? "1" : "0";
            modelMap.put("highhairDataResultFlag", highhairDataResultFlag);
            List<HighhairListProduct> highhairExhibitionsProduct = exhibitionBuineseApi.listHighhairExhibitionProducts(exhibitions, merchantId, branchCode, 1, 200);
            LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
            if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                highhairExhibitionsProduct = layoutBaseService.setProductProperties(highhairExhibitionsProduct, statusEnum, request);
            }
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
            modelMap.put("skuList", highhairExhibitionsProduct);
            modelMap.put("img", image);
            return new ModelAndView("/highHair/hotSale.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("PC高毛加载异常:", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    @RequestMapping(value = {"/newArrival.htm"})
    public ModelAndView remai(ModelMap modelMap, HttpServletRequest request) {
        String type = request.getParameter("type");
        String img = request.getParameter("img");
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            List<String> list = Lists.newArrayList();
            list.add(type);
            List<HighhairListProduct> listSkuSearchData = exhibitionBuineseApi.listHighhairExhibitionProducts(list, merchantId, branchCode, 1, 200);
            LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
            if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                listSkuSearchData = layoutBaseService.setProductProperties(listSkuSearchData, statusEnum, request);
            }
            // 0没有配置高毛 1配置高毛
            String highhairDataResultFlag = image != null ? "1" : "0";
            modelMap.put("highhairDataResultFlag", highhairDataResultFlag);
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
            modelMap.put("skuList", listSkuSearchData);
            modelMap.put("img", image);
            return new ModelAndView("/highHair/remai.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("PC高毛加载异常:", e);
            return new ModelAndView("/error/500.ftl");
        }
    }


    @RequestMapping(value = {"/mergerPackage.htm"})
    public ModelAndView mergerPackage(ModelMap modelMap, HttpServletRequest request) {
        String img = request.getParameter("img");
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
//            ActivityPackageReq activityPackageReq = new ActivityPackageReq();
//            activityPackageReq.setMerchantId(merchantId);
//            activityPackageReq.setType(1);
//            activityPackageReq.setMark(3);
//            PromoResp promoResp = activityPackageBusinessApi.queryPackageList(activityPackageReq);
            List<ActivityPackageVo> allActivityPackage = marketActivityPackageRpc.getAllActivityPackage(merchantId, branchCode, 1, 3);
            if (CollectionUtils.isEmpty(allActivityPackage)) {
                allActivityPackage = Lists.newArrayList();
            }
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
//            modelMap.put("promoRespList", promoResp != null ? (promoResp.getData() != null ? promoResp.getData() : new ArrayList<>()) : new ArrayList<>());
            modelMap.put("promoRespList", allActivityPackage);
            modelMap.put("type",1);
            modelMap.put("mark",3);
            modelMap.put("img", image);
            return new ModelAndView("/highHair/taocan.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("PC高毛加载异常:", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 高毛搜索商品
     * @param
     * @param skuConditionDto
     * @return
     */
    @Deprecated
    @RequestMapping("/findSkuInfo")
    @ResponseBody
    public ModelMap findSkuInfo(ModelMap modelMap, ProductForCmsDTO skuConditionDto,Integer pagerNum,Integer pageSize, HttpServletRequest request){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            //根据用户id查询区域编码
            //用户所属域的域编码
            String branchCode = null;
            if (merchant != null){
                merchantId = merchant.getId();
                branchCode = merchant.getRegisterCode();
            }else {
                branchCode = this.getBranchCodeByMerchantId(request,merchantId);
            }
            skuConditionDto.setMerchantId(merchantId);
            skuConditionDto.setBranchCode(branchCode);
            //获取销量的排序标识
            String orderSort = request.getParameter("order_sort");
            if ("1".equals(orderSort)) {
                skuConditionDto.setProperty("spa.sale_num");
                skuConditionDto.setDirection("desc");
            } else if("2".equals(orderSort)){
                skuConditionDto.setProperty("spa.sale_num");
                skuConditionDto.setDirection("asc");
            }
            //获取人气排行的标识
            String orderSaleRank = request.getParameter("order_sale_rank");
            if ("1".equals(orderSaleRank)) {
                skuConditionDto.setProperty("smsr.sale_num");
                skuConditionDto.setDirection("desc");
            } else if("2".equals(orderSaleRank)){
                skuConditionDto.setProperty("smsr.sale_num");
                skuConditionDto.setDirection("asc");
            }
            //获取最新上架排序标识
            String orderTime = request.getParameter("order_time");
            if ("1".equals(orderTime)) {
                skuConditionDto.setProperty("s.create_time");
                skuConditionDto.setDirection("desc");
            } else if("2".equals(orderTime)){
                skuConditionDto.setProperty("s.create_time");
                skuConditionDto.setDirection("asc");
            }
            //获取价格排序标识
            String orderFob = request.getParameter("order_fob");
            if ("1".equals(orderFob)) {
                skuConditionDto.setProperty("s.fob");
                skuConditionDto.setDirection("desc");
            } else if("2".equals(orderFob)){
                skuConditionDto.setProperty("s.fob");
                skuConditionDto.setDirection("asc");
            }
            //如果销量排序、最新上架排序、价格排序都为空，默认人气排序
            if (StringUtil.isEmpty(orderSort) && StringUtil.isEmpty(orderTime) && StringUtil.isEmpty(orderFob)){
                if (orderSaleRank == null) {
                    orderSaleRank = "1";
                    skuConditionDto.setProperty("smsr.sale_num");
                    skuConditionDto.setDirection("desc");
                }
            }
            String exhibition = request.getParameter("type");
            List<Long> skulist = Lists.newArrayList();
            if (StringUtil.isNotEmpty(exhibition)){
                skulist = exhibitionBuineseApi.getRedisProductIds(exhibition,branchCode);
                skuConditionDto.setSkuIds(skulist);
            }
            skuConditionDto.setPageNum(1);
            skuConditionDto.setPageSize(200);
            //判断是否在自配区
            boolean isShowFragileGoods = merchantBussinessApi.checkFragileLimitedByMerchantId(merchantId);
            if(!isShowFragileGoods) {
                //易碎品是否可见
                skuConditionDto.setIsShowFragileGoods(1);
            }
            ProductConditionDTO productConditionDTO = new ProductConditionDTO();
            BeanUtils.copyProperties(skuConditionDto, productConditionDTO);
            productConditionDTO.setSkuIdList(skuConditionDto.getSkuIds());
            ListSkuSearchData skuSearchData = productForLayoutApi.findCsuListOnlySale(productConditionDTO);
            //ListSkuSearchData skuSearchData = productBusinessApi.findEsProductForCms(skuConditionDto);
            if (skuSearchData != null && CollectionUtils.isNotEmpty(skuSearchData.getSkuDtoList())) {
                Iterator<ListProduct> iterator = skuSearchData.getSkuDtoList().iterator();
                Integer sortint = 0;
                LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
                while (iterator.hasNext()) {
                    ListProduct listProduct = iterator.next();
                    /** 只展示销售中和已售罄状态的商品 */
                    if (listProduct.getStatus() == 1 || listProduct.getStatus() == 2) {
                        sortint++;
                        listProduct.setSort(sortint);
                        /** 如果售罄，则排序值增大 */
                        if (listProduct.getStock() == 0) {
                            listProduct.setSort(listProduct.getSort() + 9999);
                        }
                        if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                            listProduct = layoutBaseService.setProductProperties(listProduct, statusEnum, request);
                        }
                    } else {
                        iterator.remove();
                    }
                }
                skuSearchData.getSkuDtoList().sort((a, b) -> {
                    if (a.getSort() > b.getSort()) {
                        return 1;
                    } else {
                        return -1;
                    }
                });
                /*******************获取商品列表**********************/
                //处理分页
                if(pagerNum == null || pagerNum <= 0){
                    pagerNum = 1;
                }
                if(pagerNum == null || pagerNum <= 0){
                    pageSize = 20;
                }
                int startIndex = (pagerNum - 1) * pageSize;
                int endEndex = pagerNum * pageSize;
                if (startIndex > skuSearchData.getSkuDtoList().size()) {
                    startIndex = skuSearchData.getSkuDtoList().size();
                }
                if (endEndex > skuSearchData.getSkuDtoList().size()) {
                    endEndex = skuSearchData.getSkuDtoList().size();
                }
                skuSearchData.setCount(Long.valueOf(skuSearchData.getSkuDtoList().size()));
                List<ListProduct> list = skuSearchData.getSkuDtoList().subList(startIndex, endEndex);
                skuSearchData.setSkuDtoList(list);
            }
            if (skuSearchData != null){
                List<ListProduct> skuDtoList = skuSearchData.getSkuDtoList();
                Long count = skuSearchData.getCount();
                if (skuDtoList != null && count != null) {
                    modelMap.put("skuList",skuDtoList);
                    modelMap.put("total", count.intValue());
                }
            }
            return modelMap;
        } catch (Exception e) {
            LOGGER.error("PC高毛排序查询异常:", e);
            return null;
        }
    }


    /**
     * 显示进度条
     * @param
     * @return
     */
    public AgreementScheduleBussinessDto showSchedule(){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            AgreementScheduleBussinessDto agreementCompleteSchedule = agreementForOrderBussinessApi.getAgreementCompleteSchedule(merchantId);
            return agreementCompleteSchedule;
        } catch (Exception e) {
            LOGGER.error("查看高毛进度条失败", e);
            return null;
        }
    }

    private void inintImgAndExhibition(MerchantBussinessDto merchant, Long merchantId, String branchCode, HttpServletRequest request) throws Exception {
        HighhairManageBuineseDto highhairManageBuineseDto = highhairManageBuineseApi.getUseingHighhairManage(merchantId,branchCode);
        if (highhairManageBuineseDto != null) {
            String  sortingMenu = highhairManageBuineseDto.getSortingMenu();
            if(StringUtils.isNotEmpty(sortingMenu)){
                JSONArray jsonArray = JSONObject.parseArray(sortingMenu);
                if(jsonArray != null){
                    if (jsonArray.size() > 0){
                        exhibitions = Lists.newArrayList();
                        for (int i =0;i<jsonArray.size();i++){
                            // 遍历 jsonarray 数组，把每一个对象转成 json 对象
                            JSONObject job = jsonArray.getJSONObject(i);
                            if(job.containsKey("sortid")){
                                exhibitions.add(job.get("sortid") == null ? "" :job.get("sortid").toString());
                            }
                        }
                    }
                }
            }
            LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
            if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                List<ListProduct> recommendationListProducts = highhairManageBuineseDto.getRecommendationListProducts();
                recommendationListProducts = layoutBaseService.setProductProperties(recommendationListProducts, statusEnum, request);
                highhairManageBuineseDto.setRecommendationListProducts(recommendationListProducts);

                List<HighhairListProduct> highhairhotListProducts = highhairManageBuineseDto.getHighhairhotListProducts();
                highhairhotListProducts = layoutBaseService.setProductProperties(highhairhotListProducts, statusEnum, request);
                highhairManageBuineseDto.setHighhairhotListProducts(highhairhotListProducts);

                List<HighhairListProduct> highhairnewListProducts = highhairManageBuineseDto.getHighhairnewListProducts();
                highhairnewListProducts = layoutBaseService.setProductProperties(highhairnewListProducts, statusEnum, request);
                highhairManageBuineseDto.setHighhairnewListProducts(highhairnewListProducts);
            }
        }
        image = highhairManageBuineseDto != null ? highhairManageBuineseDto.getBannerImgUrl() : null;
    }
}
