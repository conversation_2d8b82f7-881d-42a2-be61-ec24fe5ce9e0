package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.order.business.api.OrderDetailBusinessApi;
import com.xyy.ec.order.business.api.OrderTransactionSnapshotBusinessApi;
import com.xyy.ec.order.business.dto.OrderDetailBusinessDto;
import com.xyy.ec.order.business.dto.OrderTransactionSnapshotBusinessDto;
import com.xyy.ec.order.business.dto.ProductTag;
import com.xyy.ec.order.core.enums.OrderEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.controller.vo.OrderTranscationSnapshotVO;
import com.xyy.ec.product.business.dto.SkuImagesVideos;
import com.xyy.ec.product.business.dto.TagDTO;
import com.xyy.ec.system.business.api.CodeitemBusinessApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 2019-07-25
 * 订单交易快照
 *
 * <AUTHOR>
 */

@Controller
@RequestMapping("/merchant/center/orderTransactionSnapshot")
@Slf4j
public class OrderTransactionSnapshotController extends BaseController {
    @Reference(version = "1.0.0")
    private MerchantLicenseApi licenseApi;
    @Reference(version = "1.0.0")
    private OrderTransactionSnapshotBusinessApi orderTransactionSnapshotBusinessApi;
    @Reference(version = "1.0.0", timeout = 3000)
    private CodeitemBusinessApi codeitemService;
    @Reference(version = "1.0.0")
    private OrderDetailBusinessApi orderDetailBusinessApi;

    @RequestMapping("/getOrderSnapshot")
    @ResponseBody
    public Object getOrderSnapshot(@RequestParam(value = "orderNo") String orderNo,
                                       @RequestParam(value = "skuId") Long skuId) {
        log.info("getOrderSnapshot,入参：orderNo：{}-skuId：{}-",orderNo,skuId);
        try {
            if (StringUtils.isEmpty(orderNo) || skuId==null) {
                return this.addError("入参有误!");
            }

            OrderTransactionSnapshotBusinessDto query = new OrderTransactionSnapshotBusinessDto();
            query.setOrderNo(orderNo);
            query.setSkuId(skuId);
            List<OrderTransactionSnapshotBusinessDto> orderTransactionSnapshotBusinessDtos = orderTransactionSnapshotBusinessApi.selectList(query);
            if(CollectionUtils.isEmpty(orderTransactionSnapshotBusinessDtos)){
                return this.addError("不存在订单快照相关信息!");
            }
            OrderTransactionSnapshotBusinessDto dto = orderTransactionSnapshotBusinessDtos.get(0);
            OrderTransactionSnapshotBusinessDto orderTransactionSnapshotBusinessDto = orderTransactionSnapshotBusinessApi.selectById(dto.getId());

            OrderTranscationSnapshotVO resultVo = new OrderTranscationSnapshotVO();

            if (orderTransactionSnapshotBusinessDto != null) {

                BeanUtils.copyProperties(orderTransactionSnapshotBusinessDto, resultVo);
                //处理商品促销标签列表
                List<TagDTO> cxTagList = new ArrayList<>();
                if (StringUtils.isNotEmpty(orderTransactionSnapshotBusinessDto.getCxTagStr())) {
                    cxTagList = JSONArray.parseArray(orderTransactionSnapshotBusinessDto.getCxTagStr(), TagDTO.class);
                }
                resultVo.setCxTagList(cxTagList);//商品促销标签集合

                //存储视频图片集合
                List<SkuImagesVideos> skuImagesVideos = new ArrayList<>();
                //获取商品视频跳转链接
                String videoUrl = orderTransactionSnapshotBusinessDto.getVideoUrl();
                if (StringUtils.isNotEmpty(videoUrl)) {
                    SkuImagesVideos imagesVideos = new SkuImagesVideos();
                    imagesVideos.setType(2);
                    imagesVideos.setVideoUrl(videoUrl);
                    skuImagesVideos.add(imagesVideos);
                }
                //定义一个存储商品所有图片（主图和附图）的集合
                List<String> imagesListArry = new ArrayList<>();
                //获取附图（是以逗号分隔的字符串）
                String imagesList = orderTransactionSnapshotBusinessDto.getImageListUrl();
                //获取附图集合
                String[] arryImagesList = imagesList.split(",");
                if (StringUtils.isNotEmpty(orderTransactionSnapshotBusinessDto.getImageUrl())) {
                    SkuImagesVideos imagesVideos = new SkuImagesVideos();
                    //存储主图到图片集合
                    imagesListArry.add(orderTransactionSnapshotBusinessDto.getImageUrl());
                    imagesVideos.setType(1);
                    imagesVideos.setImageUrl(orderTransactionSnapshotBusinessDto.getImageUrl());
                    skuImagesVideos.add(imagesVideos);
                }
                //处理附图集合
                for (String skuImages : arryImagesList) {
                    if (StringUtils.isNotEmpty(skuImages)) {
                        SkuImagesVideos imagesVideos = new SkuImagesVideos();
                        //存储附图到图片集合
                        imagesListArry.add(skuImages.trim());
                        imagesVideos.setType(1);
                        imagesVideos.setImageUrl(skuImages.trim());
                        skuImagesVideos.add(imagesVideos);
                    }
                }
                //存储所有商品图片
                resultVo.setImagesList(imagesListArry);
                //存储图片和视频集合
                resultVo.setImagesVideosList(skuImagesVideos);
//                //处方分类图片地址
//                Map<String, String> drugClassificationImageMap = codeitemServiceConfig.findCodeMap(CodeMapConstants.DRUG_CLASSIFICATION_CODE,orderTransactionSnapshotBusinessDto.getBranchCode());
//
//                /* 更新对应的药品分类的image信息(甲类OTC，乙类OTC，处方药的图片) */
//                if (CollectionUtil.isNotEmpty(drugClassificationImageMap)) {
//                    orderTranscationSnapshotAppDto.setDrugClassificationImage(drugClassificationImageMap.get(orderTransactionSnapshotBusinessDto.getDrugClassification()+""));
//                    if (NumberUtils.toInt(orderTransactionSnapshotBusinessDto.getDrugClassification()+"",0) == 0) {
//                        orderTranscationSnapshotAppDto.setDrugClassificationImage("");
//                    }
//                }

                if (orderTransactionSnapshotBusinessDto.getDrugClassification() != null) {
                    switch (orderTransactionSnapshotBusinessDto.getDrugClassification()) {
                        case 1:
                            resultVo.setDrugClassificationText("甲类OTC");
                            break;
                        case 2:
                            resultVo.setDrugClassificationText("乙类OTC");
                            break;
                        case 3:
                            resultVo.setDrugClassificationText("RX处方药");
                            break;
                        default:
                            resultVo.setDrugClassificationText("其他");
                            break;
                    }
                }

                if (orderTransactionSnapshotBusinessDto.getCategoryFirstId() != null && orderTransactionSnapshotBusinessDto.getCategoryFirstId() == 68) {
                    resultVo.setShelfLifeText("保质期");
                } else {
                    resultVo.setShelfLifeText("有效期");
                }
                //商品描述
                if (StringUtils.isEmpty(orderTransactionSnapshotBusinessDto.getDescription())) {
                    resultVo.setDescription("");
                }
                if (orderTransactionSnapshotBusinessDto.getPackageId() == null) {
                    resultVo.setPackageId(0L);
                    resultVo.setPackageCount(0);
                }
                if (orderTransactionSnapshotBusinessDto.getBalanceFlag() == null) {
                    resultVo.setBalanceFlag(0);
                    resultVo.setBalancePercent(BigDecimal.ZERO);
                }
                if (orderTransactionSnapshotBusinessDto.getPreferentialAmount() == null) {
                    resultVo.setPreferentialAmount(BigDecimal.ZERO);
                }
                if (orderTransactionSnapshotBusinessDto.getPreferentialCount() == null) {
                    resultVo.setPreferentialCount(0);
                }
                //处理近效期临期以及特价、直降、秒杀活动温馨提示
                if (StringUtils.isNotBlank(resultVo.getTagJsonStr())) {
                    List<ProductTag> tagList = JSONArray.parseArray(resultVo.getTagJsonStr(), ProductTag.class);
                    tagList.stream().filter(e -> e.getValue() != null && (e.getValue() == 1 || e.getValue() == 2)).map(e -> {
                        resultVo.setNearEffectiveFlag(e.getValue());
                        return e;
                    }).collect(Collectors.toList());
                }
                //查询订单明细，获取活动及效期
                OrderDetailBusinessDto orderDetailBusinessDto = new OrderDetailBusinessDto();
                orderDetailBusinessDto.setOrderNo(resultVo.getOrderNo());
                if (resultVo.getOrderDetailId() != null) {
                    orderDetailBusinessDto.setId(resultVo.getOrderDetailId());
                }
                orderDetailBusinessDto.setSkuId(resultVo.getSkuId());
                List<OrderDetailBusinessDto> orderDetails = orderDetailBusinessApi.selectList(orderDetailBusinessDto);
                OrderDetailBusinessDto matchDetail = orderDetails.stream().findAny().orElse(new OrderDetailBusinessDto());
                if (matchDetail.getType() != null && (matchDetail.getType() == OrderEnum.OrderDetailConstant.TYPE_PROMOTION.getId() ||
                        matchDetail.getType() == OrderEnum.OrderDetailConstant.TYPE_SECKILL.getId() ||
                        matchDetail.getType() == OrderEnum.OrderDetailConstant.TYPE_PLUMMET.getId())) {
                    resultVo.setActivityType(99);
                }

//                List<SkuCategoryRelationBusinessDTO> skuCategoryRelationBusinessDTOS = categoryRelationBusinessApi.findSkuCategoryRelationBySkuId(orderTranscationSnapshotAppDto.getSkuId());
//                if (CollectionUtils.isNotEmpty(skuCategoryRelationBusinessDTOS)) {
//                    if (CollectionUtils.isNotEmpty(skuCategoryRelationBusinessDTOS)) {
//                        // 一般一个商品对应一个分类关系，但线上有大量商品对应多条一样的分类关系，所以这里取最新一条关系作为商品的分类
//                        SkuCategoryRelationBusinessDTO skuCategoryRelationBusinessDTO = skuCategoryRelationBusinessDTOS.get(skuCategoryRelationBusinessDTOS.size() - 1);
//                        resultVo.setCategoryFirstId(skuCategoryRelationBusinessDTO.getCategoryFirstId());
//                    }
//                }
            }
            //上品需求，过滤远效期
            resultVo.setFarEffect(null);
            return this.addResult("data", resultVo);
        } catch (Exception e) {
            log.error("getOrderSnapshot-error：", e);
            return this.addError("快照详细查询失败");
        }
    }
}
