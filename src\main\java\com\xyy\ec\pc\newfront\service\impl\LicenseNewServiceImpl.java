package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.merchant.bussiness.dto.licence.MerchantForLicense;
import com.xyy.ec.pc.newfront.service.LicenseNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-07-25 16:28
 */
@Service
@Slf4j
public class LicenseNewServiceImpl implements LicenseNewService {

    @Reference(version = "1.0.0")
    private MerchantLicenseApi merchantLicenseApi;

    @Override
    public AjaxResult<Object> getMerchantInfoToAuthentication(MerchantForLicense merchantForLicense) {
        try {
            log.info("用户认证,入参：merchantForLicense:{}", JSONObject.toJSONString(merchantForLicense));
            ApiRPCResult<MerchantForLicense> rpcResult = merchantLicenseApi.getMerchantInfoToAuthentication(merchantForLicense);
            log.info("用户认证接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.isFail()) {
                String errMsg = StringUtils.isEmpty(rpcResult.getErrMsg()) ? rpcResult.getMsg() : rpcResult.getErrMsg();
                return AjaxResult.errResult(errMsg);
            }
            return AjaxResult.successResult(rpcResult.getData());
        } catch (Exception e) {
            log.error("用户认证失败:", e);
            return AjaxResult.errResult("用户认证失败");
        }
    }
}
