<!DOCTYPE HTML>
<html>
	<head>
		<#include "/common/common.ftl" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<title>${keyword} 搜索结果页</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
        <meta name="format-detection" content="telephone=no,email=no,address=no">
		<link rel="stylesheet" href="/static/css/search.css?t=${t_v}" />
<#--		<script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>-->
<#--		<script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>-->
<#--		<script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>-->
<#--		<script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>-->
<#--		<script type="text/javascript" src="/static/js/selfshop/search.js?t=${t_v}"></script>-->
<#--            <script type="text/javascript" src="/static/js/sku_search.js?t=${t_v}"></script>-->
            <link rel="stylesheet" href="/static/css/headerAndFooter1.css">
            <script type="text/javascript">
                function getUrlParam(name) {
                    const urlArr = window.location.href.split('?');
                    if (urlArr.length < 2) {
                        return '';
                    }
                    const tempArr = urlArr[1].split('&');
                    for (let i = 0; i < tempArr.length; i++) {
                        const item = tempArr[i].split('=');
                        if (item[0].trim() === name) {
                            return decodeURI(item[1]);
                        }
                    }
                    return '';
                }
                function setIframeHeight(iframe) {
                    if (iframe) {
                        window.addEventListener('message', function(e){
                            if (e.data.message === 'getSearchHeight') {
                                window.scrollTo(0, 0);
                                let searchHeight = e.data.height || '800';
                                let ifr = document.querySelector('#search-frame');
                                ifr.style.height = 0;
                                ifr.style.height = searchHeight + 'px';
                                console.log('iframe拿到的高度', searchHeight)
                            }
                        }, false);
                    }
                };
                window.onload = function () {
                    setIframeHeight(document.getElementById('search-frame'));
                    var keywordSearch = getUrlParam('keyword');
                };
            </script>
<#--		<script type="text/javascript">-->
<#--            var ctx="${ctx}";-->
<#--			var event = event || window.event;-->
<#--			var urlPath = '${pager.requestUrl}';-->
<#--			function getModelType(type){-->
<#--				var url = '${pager.requestUrl}';-->
<#--				url = url.replace("model_type=1","");-->
<#--				url = url.replace("model_type=2","");-->
<#--				if(url.indexOf(".htm?")>0){-->
<#--					if(url.substring(url.length-1,url.length)=='&'){-->
<#--						url = url.substring(0,url.length-1);-->
<#--					}-->
<#--					url += "&offset="+${pager.offset}+"&model_type="+type;-->
<#--				}else{-->
<#--					url += "?offset="+${pager.offset}+"&model_type="+type;-->
<#--				}-->
<#--				window.location.href=encodeURI(url);-->
<#--			}-->
<#--			-->
<#--			function searchProductBySort(sort){-->
<#--				var url = '${pager.requestUrl}';-->
<#--				url = url.replace("order_sort=1","");-->
<#--				url = url.replace("order_sort=2","");-->
<#--                url = url.replace("order_sale_rank=1","");-->
<#--                url = url.replace("order_sale_rank=2","");-->
<#--                url = url.replace("order_time=1","");-->
<#--                url = url.replace("order_time=2","");-->
<#--                url = url.replace("order_fob=1","");-->
<#--                url = url.replace("order_fob=2","");-->
<#--				if(url.indexOf(".htm?")>0){-->
<#--					if(url.substring(url.length-1,url.length)=='&'){-->
<#--						url = url.substring(0,url.length-1);-->
<#--					}-->
<#--					url += "&offset="+${pager.offset}+"&order_sort="+sort;-->
<#--				}else{-->
<#--					url += "?offset="+${pager.offset}+"&order_sort="+sort;-->
<#--				}-->
<#--				window.location.href=encodeURI(url);-->
<#--			}-->
<#--            function searchProductBySaleRank(sort){-->
<#--                var url = '${pager.requestUrl}';-->
<#--                url = url.replace("order_sale_rank=1","");-->
<#--                url = url.replace("order_sale_rank=2","");-->
<#--                url = url.replace("order_sort=1","");-->
<#--                url = url.replace("order_sort=2","");-->
<#--                url = url.replace("order_time=1","");-->
<#--                url = url.replace("order_time=2","");-->
<#--                url = url.replace("order_fob=1","");-->
<#--                url = url.replace("order_fob=2","");-->
<#--                if(url.indexOf(".htm?")>0){-->
<#--                    if(url.substring(url.length-1,url.length)=='&'){-->
<#--                        url = url.substring(0,url.length-1);-->
<#--                    }-->
<#--                    url += "&offset="+${pager.offset}+"&order_sale_rank="+sort;-->
<#--                }else{-->
<#--                    url += "?offset="+${pager.offset}+"&order_sale_rank="+sort;-->
<#--                }-->
<#--                window.location.href=encodeURI(url);-->
<#--            }-->
<#--			function searchProductByTime(sort){-->
<#--				var url = '${pager.requestUrl}';-->
<#--				url = url.replace("order_time=1","");-->
<#--				url = url.replace("order_time=2","");-->
<#--                url = url.replace("order_sale_rank=1","");-->
<#--                url = url.replace("order_sale_rank=2","");-->
<#--                url = url.replace("order_sort=1","");-->
<#--                url = url.replace("order_sort=2","");-->
<#--                url = url.replace("order_fob=1","");-->
<#--                url = url.replace("order_fob=2","");-->
<#--				if(url.indexOf(".htm?")>0){-->
<#--					if(url.substring(url.length-1,url.length)=='&'){-->
<#--						url = url.substring(0,url.length-1);-->
<#--					}-->
<#--					url += "&offset="+${pager.offset}+"&order_time="+sort;-->
<#--				}else{-->
<#--					url += "?offset="+${pager.offset}+"&order_time="+sort;-->
<#--				}-->
<#--				window.location.href = encodeURI(url);-->
<#--			}-->
<#--            function searchProductByFob(sort){-->
<#--                var url = '${pager.requestUrl}';-->
<#--                url = url.replace("order_time=1","");-->
<#--                url = url.replace("order_time=2","");-->
<#--                url = url.replace("order_sale_rank=1","");-->
<#--                url = url.replace("order_sale_rank=2","");-->
<#--                url = url.replace("order_sort=1","");-->
<#--                url = url.replace("order_sort=2","");-->
<#--				url = url.replace("order_fob=1","");-->
<#--                url = url.replace("order_fob=2","");-->
<#--                if(url.indexOf(".htm?")>0){-->
<#--                    if(url.substring(url.length-1,url.length)=='&'){-->
<#--                        url = url.substring(0,url.length-1);-->
<#--                    }-->
<#--                    url += "&offset="+${pager.offset}+"&order_fob="+sort;-->
<#--                }else{-->
<#--                    url += "?offset="+${pager.offset}+"&order_fob="+sort;-->
<#--                }-->
<#--                window.location.href = encodeURI(url);-->
<#--            }-->

<#--			function searchByIsControl(){-->
<#--				var url = '${pager.requestUrl}';-->
<#--				url = url.replace("is_control=1","");-->
<#--				url = url.replace("is_control=2","");-->
<#--				var value = 1;-->
<#--				var obj = document.getElementsByName('isControl'); -->
<#--				for(var i=0; i<obj.length; i++){ -->
<#--					if(obj[i].checked){-->
<#--						value =2;-->
<#--					}-->
<#--				}-->
<#--				if(url.indexOf(".htm?")>0){-->
<#--					if(url.substring(url.length-1,url.length)=='&'){-->
<#--						url = url.substring(0,url.length-1);-->
<#--					}-->
<#--					url += "&is_control="+value;-->
<#--				}else{-->
<#--					url += "?is_control="+value;-->
<#--				}-->
<#--				window.location.href=encodeURI(url);-->
<#--			}-->

<#--            function searchByHasStock(){-->
<#--                var url = '${pager.requestUrl}';-->
<#--                url = url.replace("has_stock=1","");-->
<#--                url = url.replace("has_stock=2","");-->
<#--                var value = 2;-->
<#--                var obj = document.getElementsByName('hasStock');-->
<#--                for(var i=0; i<obj.length; i++){-->
<#--                    if(obj[i].checked){-->
<#--                        value =1;-->
<#--                    }-->
<#--                }-->
<#--                if(url.indexOf(".htm?")>0){-->
<#--                    if(url.substring(url.length-1,url.length)=='&'){-->
<#--                        url = url.substring(0,url.length-1);-->
<#--                    }-->
<#--                    url += "&has_stock="+value;-->
<#--                }else{-->
<#--                    url += "?has_stock="+value;-->
<#--                }-->
<#--                window.location.href=encodeURI(url);-->
<#--            }-->

<#--            function searchByCanBuy(){-->
<#--                var url = '${pager.requestUrl}';-->
<#--                url = url.replace("isCanBuy=0","");-->
<#--                url = url.replace("isCanBuy=1","");-->
<#--                var value = 0;-->
<#--                var obj = document.getElementsByName('isCanBuy');-->
<#--                for(var i=0; i<obj.length; i++){-->
<#--                    if(obj[i].checked){-->
<#--                        value =1;-->
<#--                    }-->
<#--                }-->
<#--                if(url.indexOf(".htm?")>0){-->
<#--                    if(url.substring(url.length-1,url.length)=='&'){-->
<#--                        url = url.substring(0,url.length-1);-->
<#--                    }-->
<#--                    url += "&isCanBuy="+value;-->
<#--                }else{-->
<#--                    url += "?isCanBuy="+value;-->
<#--                }-->
<#--                window.location.href=encodeURI(url);-->
<#--            }-->
<#--		</script>-->
	</head>

	<body>
		<div class="container">
			<input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
            <input type="hidden" id="merchant" name="merchant" value="${merchant}"/>
			<input type="hidden" id="orgId" value="${orgId}"/>
            <input type="hidden" id="shopCode" value="${shopCode}"/>
			<!--头部导航区域开始-->
			<div class="headerBox" id="headerBox">
				<#include "/common/selfshop/header.ftl" />
			</div>
			<!--头部导航区域结束-->

			<!--主体部分开始-->
            <div class="main">
                <iframe id="search-frame" src="/newstatic/#/search/index?keywordSearch=${keywordSearch}&urlMerchantId=${merchantId}&shopCode=${shopCode}&fromPage=selfShop&categoryFirstId=${categoryFirstId}&categorySecondId=${categorySecondId}&categoryThirdId=${categoryThirdId}" width="100%" height="100%" style="min-height: 800px" frameborder='0'></iframe>
            </div>
            <#if false>
			<div class="main" style="display: none">
				<!--商品分类-->


				<!--当前搜索关键词-->
				<div class="dqss">
					<#if keyword ?exists>
						<span class="title">当前搜索关键词</span>
					<span class="info">"${keyword}"</span>
					</#if>
					
				</div>

				<!--推荐搜索-->
				<div class="tjss">
					<#if listKeywords ? exists>
						<span class="title">推荐搜索：</span>
						<#list listKeywords as words>	
							<span class="info"><a href="${ctx}/selfShop/center/shopSkuInfo.htm?keyword=${words.showName}&orgId=${orgId}">${words.showName}</a></span>
						</#list>
					</#if>
				</div>

				<!--搜索框-->
				<div class="searchbox">
                    <!--一级类目-->
                    <!--一级类目-->
                    <div class="sccjbox yjlm">
                        <div class="lbox fl">
                            一级类目：
                        </div>
                        <div class="mbox fl">
                            <!--默认显示-->
                            <div class="default-yjlm">
                                <!--点击锁定类目后的样式-->
							<#if "99999" == categoryFirstId>
								<input type="hidden" id="yjCategoryId" value="99999">
                                <span onclick="searchProdctByYj(99999)" class="suoding">全部分类</span>
							<#else>
                                <input type="hidden" id="yjCategoryId" value="0">
                                <span onclick="searchProdctByYj(99999)">全部分类</span>
							</#if>
							<#if listCategory ?? && (listCategory ?size>0) >
								<#list listCategory as c>
									<#if c.name !="全部药品">
										<#if c.id == categoryFirstId>
                                            <span class="suoding" id="${c.id}" onclick="searchProdctByYj(${c.id})">${c.name}</span>
										<#else>
                                            <span id="${c.id}" onclick="searchProdctByYj(${c.id})">${c.name}</span>
										</#if>
									</#if>
								</#list>
							</#if>
                            </div>
                        </div>

                    </div>

                    <!--二级类目-->
                    <div class="sccjbox ejlm">
                        <div class="lbox fl">
							<input type="hidden" id="ejZhan" value="${ejZhan}">
                            二级类目：
                        </div>
                        <div class="mbox fl">
                            <!--默认显示-->
                            <div class="default-ejlm">
							<#if listCategory ?? && (listCategory ?size>0) >
								<#list listCategory as c>
									<#if c.name !="全部药品">
										<#if categoryFirstId != null && categoryFirstId != 0>
											<#if "99999" == categoryFirstId>
												<#list c.children as child>
                                                    <span id="${child.id}" onclick="searchProdctByEJ(${child.id})">${child.name}</span>
												</#list>
											<#else >
												<#if c.id == categoryFirstId>
													<#list c.children as child>
														<#if child.id == categorySecondId>
                                                            <span class="suoding" id="${child.id}" onclick="searchProdctByEJ(${child.id})">${child.name}</span>
														<#else>
                                                            <span id="${child.id}" onclick="searchProdctByEJ(${child.id})">${child.name}</span>
														</#if>
													</#list>
												</#if>
											</#if>
										<#else>
											<#list c.children as child>
												<#if child.id == categorySecondId>
                                                    <span class="suoding" id="${child.id}" onclick="searchProdctByEJ(${child.id})">${child.name}</span>
												<#else>
                                                    <span id="${child.id}" onclick="searchProdctByEJ(${child.id})">${child.name}</span>
												</#if>
											</#list>
										</#if>
									</#if>
								</#list>
							</#if >
                            </div>
                            <!--展开显示-->
                            <div class="default-more-ejlm">
							<#if listCategory ?? && (listCategory ?size>0) >
								<#list listCategory as c>
									<#if c.name !="全部药品">
										<#if categoryFirstId != null && categoryFirstId != 0>
											<#if "99999" == categoryFirstId>
												<#list c.children as child>
                                                    <span id="${child.id}" onclick="searchProdctByEJ1(${child.id})">${child.name}</span>
												</#list>
											<#else >
												<#if c.id == categoryFirstId>
													<#list c.children as child>
														<#if child.id == categorySecondId>
                                                            <span class="suoding" id="${child.id}" onclick="searchProdctByEJ1(${child.id})">${child.name}</span>
														<#else>
                                                            <span id="${child.id}" onclick="searchProdctByEJ1(${child.id})">${child.name}</span>
														</#if>
													</#list>
												</#if>
											</#if>
										<#else>
											<#list c.children as child>
												<#if child.id == categorySecondId>
                                                    <span class="suoding" id="${child.id}" onclick="searchProdctByEJ1(${child.id})">${child.name}</span>
												<#else>
                                                    <span id="${child.id}" onclick="searchProdctByEJ1(${child.id})">${child.name}</span>
												</#if>
											</#list>
										</#if>
									</#if>
								</#list>
							</#if >
                            </div>
                        </div>
                        <div class="rbox fr">
                            <a href="javascript:void(0);" class="zhan-er">展开<i class="sui-icon icon-tb-unfold"></i></a>
                            <a href="javascript:void(0);" class="shou-er">收起<i class="sui-icon icon-tb-fold"></i></a>
                        </div>
                    </div>

                    <!--三级类目-->
                    <div class="sccjbox sjlm">
                        <div class="lbox fl">
                            <input type="hidden" id="sjZhan" value="${sjZhan}">
                            三级类目：
                        </div>
                        <div class="mbox fl">
                            <!--默认显示-->
                            <div class="default-sjlm">
							<#if listCategory ?? && (listCategory ?size>0) >
								<#list listCategory as c>
									<#if c.name !="全部药品">
										<#if categoryFirstId != null && categoryFirstId != 0>
											<#if "99999" == categoryFirstId>
												<#list c.children as parent>
													<#list parent.children as child>
                                                        <span id="${child.id}" onclick="searchProdctBySJ(${child.id})">${child.name}</span>
													</#list>
												</#list>
											<#else >
												<#if c.id == categoryFirstId>
													<#list c.children as parent>
														<#if categorySecondId != null>
															<#if parent.id==categorySecondId>
																<#list parent.children as child>
																	<#if child.id == categoryThirdId>
                                                                        <span class="suoding" id="${child.id}" onclick="searchProdctBySJ(${child.id})">${child.name}</span>
																	<#else>
                                                                        <span id="${child.id}" onclick="searchProdctBySJ(${child.id})">${child.name}</span>
																	</#if>
																</#list>
															</#if>
														<#else>
															<#list parent.children as child>
																<#if child.id == categoryThirdId>
                                                                    <span class="suoding" id="${child.id}" onclick="searchProdctBySJ(${child.id})">${child.name}</span>
																<#else>
                                                                    <span id="${child.id}" onclick="searchProdctBySJ(${child.id})">${child.name}</span>
																</#if>
															</#list>
														</#if>
													</#list>
												</#if>
											</#if>
										<#else>
											<#list c.children as parent>
												<#if categorySecondId != null>
													<#if parent.id==categorySecondId>
														<#list parent.children as child>
															<#if child.id == categoryThirdId>
                                                                <span class="suoding" id="${child.id}" onclick="searchProdctBySJ(${child.id})">${child.name}</span>
															<#else>
                                                                <span id="${child.id}" onclick="searchProdctBySJ(${child.id})">${child.name}</span>
															</#if>
														</#list>
													</#if>
												<#else>
													<#list parent.children as child>
														<#if child.id == categoryThirdId>
                                                            <span class="suoding" id="${child.id}" onclick="searchProdctBySJ(${child.id})">${child.name}</span>
														<#else>
                                                            <span id="${child.id}" onclick="searchProdctBySJ(${child.id})">${child.name}</span>
														</#if>
													</#list>
												</#if>
											</#list>
										</#if>
									</#if>
								</#list>
							</#if >
                            </div>
                            <!--展开显示-->
                            <div class="default-more-sjlm">
							<#if listCategory ?? && (listCategory ?size>0) >
								<#list listCategory as c>
									<#if c.name !="全部药品">
										<#if categoryFirstId != null && categoryFirstId != 0>
											<#if "99999" == categoryFirstId>
												<#list c.children as parent>
													<#list parent.children as child>
                                                        <span id="${child.id}" onclick="searchProdctBySJ1(${child.id})">${child.name}</span>
													</#list>
												</#list>
											<#else >
												<#if c.id == categoryFirstId>
													<#list c.children as parent>
														<#if categorySecondId != null>
															<#if parent.id==categorySecondId>
																<#list parent.children as child>
																	<#if child.id == categoryThirdId>
                                                                        <span class="suoding" id="${child.id}" onclick="searchProdctBySJ1(${child.id})">${child.name}</span>
																	<#else>
                                                                        <span id="${child.id}" onclick="searchProdctBySJ1(${child.id})">${child.name}</span>
																	</#if>
																</#list>
															</#if>
														<#else>
															<#list parent.children as child>
																<#if child.id == categoryThirdId>
                                                                    <span class="suoding" id="${child.id}" onclick="searchProdctBySJ1(${child.id})">${child.name}</span>
																<#else>
                                                                    <span id="${child.id}" onclick="searchProdctBySJ1(${child.id})">${child.name}</span>
																</#if>
															</#list>
														</#if>
													</#list>
												</#if>
											</#if>
										<#else>
											<#list c.children as parent>
												<#if categorySecondId != null>
													<#if parent.id==categorySecondId>
														<#list parent.children as child>
															<#if child.id == categoryThirdId>
                                                                <span class="suoding" id="${child.id}" onclick="searchProdctBySJ1(${child.id})">${child.name}</span>
															<#else>
                                                                <span id="${child.id}" onclick="searchProdctBySJ1(${child.id})">${child.name}</span>
															</#if>
														</#list>
													</#if>
												<#else>
													<#list parent.children as child>
														<#if child.id == categoryThirdId>
                                                            <span class="suoding" id="${child.id}" onclick="searchProdctBySJ1(${child.id})">${child.name}</span>
														<#else>
                                                            <span id="${child.id}" onclick="searchProdctBySJ1(${child.id})">${child.name}</span>
														</#if>
													</#list>
												</#if>
											</#list>
										</#if>
									</#if>
								</#list>
							</#if >
                            </div>
                        </div>
                        <div class="rbox fr">
                            <a href="javascript:void(0);" class="zhan-san">展开<i class="sui-icon icon-tb-unfold"></i></a>
                            <a href="javascript:void(0);" class="shou-san">收起<i class="sui-icon icon-tb-fold"></i></a>
                        </div>
                    </div>
                    <!--生产厂家-->
				<#if manufacturerList??>
                    <div class="sccjbox">
                        <div class="lbox fl">
                            <input type="hidden" id="cjZhan" value="${cjZhan}">
                            <input type="hidden" id="manufacturer" value="${manufacturer}">
                            生产厂家：
                        </div>
                        <div class="mbox fl">
                            <!--默认显示-->
                            <div class="default">
                                <!--点击锁定后的样式
                                <span class="suoding">武汉宏运堂药业有限公司</span>-->
								<#list manufacturerList as man>
									<#if man == manufacturer>
                                        <span class="suoding" onclick="searchProdctByCJ('${man}')">${man}</span>
									<#else>
                                        <span onclick="searchProdctByCJ('${man}')">${man}</span>
									</#if>
								</#list>
                            </div>
                            <!--展开显示-->
                            <div class="default-more">
								<#list manufacturerList as man>
									<#if man == manufacturer>
                                        <span class="suoding" onclick="searchProdctByCJ1('${man}')">${man}</span>
									<#else>
                                        <span onclick="searchProdctByCJ1('${man}')">${man}</span>
									</#if>
								</#list>
                            </div>

                        </div>
                        <div class="rbox fr">
                            <!--<a href="javascript:void(0);" class="duoxuan">多选</a>-->
                            <a href="javascript:void(0);" class="zhan">展开<i class="sui-icon icon-tb-unfold"></i></a>
                            <a href="javascript:void(0);" class="shou">收起<i class="sui-icon icon-tb-fold"></i></a>
                        </div>
                    </div>
				</#if>

					<!--促销形式-->
					<#--<div class="sccjbox cxxs">-->
						<#--<div class="lbox fl">-->
							<#--促销形式：-->
						<#--</div>-->
						<#--<div class="mbox fl">-->
							<#--<!--默认显示&ndash;&gt;-->
							<#--<div class="default-cxxs">-->
								<#--<span>全部促销</span>-->
								<#--<#if "1" == cxType>-->
									<#--<span onclick="searchProdctByCx(1)" class="suoding">限时折扣</span>-->
									<#--<span onclick="searchProdctByCx(2)">特价推荐</span>-->
									<#--<span onclick="searchProdctByCx(3)">毛利新品</span>-->
								<#--<#elseif "2" == cxType>-->
									<#--<span onclick="searchProdctByCx(1)">限时折扣</span>-->
									<#--<span onclick="searchProdctByCx(2)" class="suoding">特价推荐</span>-->
									<#--<span onclick="searchProdctByCx(3)">毛利新品</span>-->
								<#--<#elseif "3" == cxType>-->
									<#--<span onclick="searchProdctByCx(1)">限时折扣</span>-->
									<#--<span onclick="searchProdctByCx(2)">特价推荐</span>-->
									<#--<span onclick="searchProdctByCx(3)" class="suoding">毛利新品</span>-->
								<#--<#else>-->
									<#--<span onclick="searchProdctByCx(1)">限时折扣</span>-->
									<#--<span onclick="searchProdctByCx(2)">特价推荐</span>-->
									<#--<span onclick="searchProdctByCx(3)">毛利新品</span>-->
								<#--</#if>-->
								<#--<!--点击锁定后的样式-->
								<#--<span class="suoding">限时折扣</span>&ndash;&gt;-->
							<#--</div>-->
						<#--</div>-->

					<#--</div>-->
                    <!--经营类型-->
                    <div class="sccjbox jylx">
                        <div class="lbox fl">
                            <input type="hidden" id="drugClassification" value="${drugClassification}">
                            经营类型：
                        </div>
                        <div class="mbox fl">
                            <!--默认显示-->
                            <div class="default-cxxs">
								<#if "1" == drugClassification>
                                    <span onclick="searchProdctByJylx('all')">全部分类</span>
                                    <span onclick="searchProdctByJylx(1)" class="suoding">甲类OTC</span>
                                    <span onclick="searchProdctByJylx(2)">乙类OTC</span>
                                    <span onclick="searchProdctByJylx(3)">处方药RX</span>
                                    <span onclick="searchProdctByJylx(0)">其它</span>
								<#elseif "2" == drugClassification>
                                    <span onclick="searchProdctByJylx('all')">全部分类</span>
                                    <span onclick="searchProdctByJylx(1)">甲类OTC</span>
                                    <span onclick="searchProdctByJylx(2)" class="suoding">乙类OTC</span>
                                    <span onclick="searchProdctByJylx(3)">处方药RX</span>
                                    <span onclick="searchProdctByJylx(0)">其它</span>
								<#elseif "3" == drugClassification>
                                    <span onclick="searchProdctByJylx('all')">全部分类</span>
                                    <span onclick="searchProdctByJylx(1)">甲类OTC</span>
                                    <span onclick="searchProdctByJylx(2)">乙类OTC</span>
                                    <span onclick="searchProdctByJylx(3)" class="suoding">处方药RX</span>
                                    <span onclick="searchProdctByJylx(0)">其它</span>
								<#elseif "0" == drugClassification>
                                    <span onclick="searchProdctByJylx('all')">全部分类</span>
                                    <span onclick="searchProdctByJylx(1)">甲类OTC</span>
                                    <span onclick="searchProdctByJylx(2)">乙类OTC</span>
                                    <span onclick="searchProdctByJylx(3)">处方药RX</span>
                                    <span onclick="searchProdctByJylx(0)" class="suoding">其它</span>
								<#elseif "5" == drugClassification >
                                    <span onclick="searchProdctByJylx('all')" class="suoding">全部分类</span>
                                    <span onclick="searchProdctByJylx(1)">甲类OTC</span>
                                    <span onclick="searchProdctByJylx(2)">乙类OTC</span>
                                    <span onclick="searchProdctByJylx(3)">处方药RX</span>
                                    <span onclick="searchProdctByJylx(0)">其它</span>
								<#else >
                                    <span onclick="searchProdctByJylx('all')">全部分类</span>
                                    <span onclick="searchProdctByJylx(1)">甲类OTC</span>
                                    <span onclick="searchProdctByJylx(2)">乙类OTC</span>
                                    <span onclick="searchProdctByJylx(3)">处方药RX</span>
                                    <span onclick="searchProdctByJylx(0)">其它</span>
								</#if>
                                <#--<span>全部分类</span>-->
                                <#--<span>甲类OTC</span>-->
                                <#--<!--点击锁定后的样式&ndash;&gt;-->
                                <#--<span class="suoding">乙类OTC</span>-->
                                <#--<span>处方药RX</span>-->
                                <#--<span>其它</span>-->
                            </div>
                        </div>
                    </div>

				</div>




				<!--搜索结果标题-->
				<div class="ss-title clearfix">
					<span class="jieguo">搜索结果：</span>
					<#if orderSaleRank == "1">
						<a href="javascript:void(0);" class="paixu renqi con-down" onclick="searchProductBySaleRank(2)">综合排序<img src="/static/images/s-up.png" class="s-up"><img src="/static/images/s-down.png" class="s-down"></a>
					<#elseif orderSaleRank == "2">
						<a href="javascript:void(0);" class="paixu renqi con-up" onclick="searchProductBySaleRank(1)">综合排序<img src="/static/images/s-up.png" class="s-up"><img src="/static/images/s-down.png" class="s-down"></a>
					<#else>
                        <a href="javascript:void(0);" class="paixu renqi" onclick="searchProductBySaleRank(1)">综合排序<img src="/static/images/s-default.png" class="s-default"></a>
					</#if>
					<#if orderSort == "1">
						<a href="javascript:void(0);" class="paixu xiaoliang con-down" onclick="searchProductBySort(2)">总销量<img src="/static/images/s-up.png" class="s-up"><img src="/static/images/s-down.png" class="s-down"></a>
					<#elseif orderSort == "2">
						<a href="javascript:void(0);" class="paixu xiaoliang con-up" onclick="searchProductBySort(1)">总销量<img src="/static/images/s-up.png" class="s-up"><img src="/static/images/s-down.png" class="s-down"></a>
					<#else>
                        <a href="javascript:void(0);" class="paixu xiaoliang" onclick="searchProductBySort(1)">总销量<img src="/static/images/s-default.png" class="s-default"></a>
					</#if>
					<#if orderFob == "1">
                        <a href="javascript:void(0);" class="paixu con-down" onclick="searchProductByFob(2)">价格<img src="/static/images/s-up.png" class="s-up"><img src="/static/images/s-down.png" class="s-down"></a>
					<#elseif orderFob == "2">
                        <a href="javascript:void(0);" class="paixu con-up" onclick="searchProductByFob(1)">价格<img src="/static/images/s-up.png" class="s-up"><img src="/static/images/s-down.png" class="s-down"></a>
					<#else >
                        <a href="javascript:void(0);" class="paixu" onclick="searchProductByFob(1)">价格<img src="/static/images/s-default.png" class="s-default"></a>
					</#if>
					<#if orderTime == "1">
						<a href="javascript:void(0);" class="paixu zuixin con-down" onclick="searchProductByTime(2)">最新上架<img src="/static/images/s-up.png" class="s-up"><img src="/static/images/s-down.png" class="s-down"></a>
					<#elseif orderTime == "2">
						<a href="javascript:void(0);" class="paixu zuixin con-up" onclick="searchProductByTime(1)">最新上架<img src="/static/images/s-up.png" class="s-up"><img src="/static/images/s-down.png" class="s-down"></a>
					<#else>
                        <a href="javascript:void(0);" class="paixu zuixin" onclick="searchProductByTime(1)">最新上架<img src="/static/images/s-default.png" class="s-default"></a>
					</#if>
                    <span class="ss-pricerange">价格区间
						<i class="ss-price">
							<input type="text" class="price-range start" id="minPrice" value="${minPrice}" onkeyup="this.value = this.value.replace(/\D/g, '')"
                                   onafterpaste="this.value=this.value.replace(/\D/g,'')"
                                   onchange="this.value = this.value.trim()" maxlength="4"> - <input type="text" class="price-range" id="maxPrice" value="${maxPrice}" onkeyup="this.value = this.value.replace(/\D/g, '')"
                                                                                       onafterpaste="this.value=this.value.replace(/\D/g,'')"
                                                                                       onchange="this.value = this.value.trim()" maxlength="4">
						</i>
						<div class="ss-range">
								<a href="javascript:;" class="ss-clear" id="ss-clear">清空</a>
								<a href="javascript:;" class="ss-confirm" id="ss-confirm">确认</a>
						</div>
					</span>
					<#if merchant ??>
                    <label class="checkbox-pretty inline purchase <#if isCanBuy == 1>checked</#if>">
                        <input type="checkbox" id="isCanBuy" name="isCanBuy" <#if isCanBuy == 1>checked="checked"</#if> onclick="searchByCanBuy()"><span>只看可采购</span>
                    </label>
					</#if>
					<#if hasStock == "1">
                        <label class="checkbox-pretty inline stock checked">
                            <input type="checkbox" name="hasStock" onclick="searchByHasStock()" checked="checked"><span>只看有货</span>
                        </label>
					<#else >
                        <label class="checkbox-pretty inline stock">
                            <input type="checkbox" name="hasStock" onclick="searchByHasStock()"><span>只看有货</span>
                        </label>
                    </#if>
					<#if isControl == "2">
						<label class="checkbox-pretty inline control checked">
    						<input type="checkbox" name="isControl" onclick="searchByIsControl()" checked="checked"><span>我的控销商品</span>
    					</label>
    				<#else>
    					<label class="checkbox-pretty inline control">
    						<input type="checkbox" name="isControl" onclick="searchByIsControl()"><span>我的控销商品</span>
    					</label>	
    				</#if>
  					
  					<!--<label class="checkbox-pretty inline checked">
    					<input type="checkbox" checked="checked"><span>控销商品</span>
  					</label>-->
  					<#if (modelType ==2)>
	  					<a href="javascript:void(0);" onclick="getModelType(2)" class="lbms fr lbfn active"><i class="sui-icon icon-th-list"></i>列表模式</a>
						<a href="javascript:void(0);" onclick="getModelType(1)" class="lbms fr dtfn"><i class="sui-icon icon-th-large"></i>大图模式</a>
					<#else>
						<a href="javascript:void(0);" onclick="getModelType(2)" class="lbms fr lbfn"><i class="sui-icon icon-th-list"></i>列表模式</a>
						<a href="javascript:void(0);" onclick="getModelType(1)" class="lbms fr dtfn active"><i class="sui-icon icon-th-large"></i>大图模式</a>
					</#if>
                    <#--<a href="javascript:void(0);" class="lbms fr lbfn"><i class="sui-icon icon-th-list"></i>列表模式</a>-->
                    <#--<a href="javascript:void(0);" class="lbms fr dtfn active"><i class="sui-icon icon-th-large"></i>大图模式</a>-->
				</div>

				<#if pager.rows??&&(pager.rows?size>0)>
				<!--大图模式-->
				<#if (modelType ==2)>
					<ul class="mrth-new clearfix" style="display:none;">
				<#else>
					<ul class="mrth-new clearfix" style="display:block;">
				</#if>
				   <#list pager.rows as skuVO>
                       <#if skuVO.actPt??>
                           <#import "/common/skuPinVO.ftl" as pr>
                           <@pr.skuVO skuVO/>
                       <#elseif skuVO.actSk??>
                           <#import "/common/skuSkVO.ftl" as pr>
                           <@pr.skuVO skuVO/>
                       <#else >
                           <#import "/common/skuVO.ftl" as pr>
                           <@pr.skuVO skuVO/>
                       </#if>
                   </#list>
                   
				</ul>

				<!--列表模式-->
				<#if (modelType ==2)>
					<div class="listmode" style="display: block;">
				<#else>
					<div class="listmode" style="display: none;">
				</#if>
					<!--表头-->
					<#--<div class="headbox">-->
						<#--<ul>-->
							<#--<li class="li1">商品</li>-->
							<#--<li class="li2">优惠</li>-->
							<#--<li class="li3">价格</li>-->
							<#--<li class="li4">库存</li>-->
							<#--<li class="li5">数量</li>-->
							<#--<li class="li6">操作</li>-->
						<#--</ul>-->
					<#--</div>-->

					<!--表体-->
					 <#list pager.rows as skuVO>
					 	<#if skuVO_index % 2 == 0>
					 	    <div class="bodybox">
						<#else >
							<div class="bodybox odd">
					 	</#if>
							<ul>
								<li class="lib1">
									<div class="l-box fl">
										<a href="/search/skuDetail/${skuVO.id}.htm" target="_blank" title="${skuVO.showName}">
											<img id="lb_${skuVO.id}" src="${productImageUrl}/ybm/product/min/${skuVO.imageUrl}" alt="" onerror="this.src='/static/images/default-small.png'"/>
                                            <!--药狂欢角标 默认隐藏 去掉noshow显示-->
											<#if skuVO.markerUrl?? && skuVO.markerUrl!=''>
                                                <div class="yaokuanghuan-pos">
                                                    <img src="${productImageUrl}/${skuVO.markerUrl}" alt="">
                                                    <#if skuVO.reducePrice ? exists>
                    									<div class="tejia806">药采节价:<span class="price806">${skuVO.reducePrice?string("0.00")}</span></div>
                                                    </#if>
                                                </div>
											</#if>
											<!--标签-->
											<div class="bq-box">
												<#if (skuVO.status == 1 && skuVO.availableQty == 0) || skuVO.status == 2 || ((skuVO.status == 3 || skuVO.status == 5) && skuVO.promotionTotalQty == 0 ) || (skuVO.isSplit == 0 && skuVO.availableQty - skuVO.mediumPackageNum lt 0)>
													<img src="/static/images/product/bq-shouqing.png" alt="">
												</#if>
												<#if skuVO.status == 4>
													<img src="/static/images/product/bq-xiajia.png" alt="">
												</#if>
											</div>
										</a>																														
									</div>
									<div class="r-box fl">
										<div class="lib1-row1 text-overflow">
											<span>
												<a href="/search/skuDetail/${skuVO.id}.htm" target="_blank" title="${skuVO.showName}">
													<#if skuVO.isShow806 || skuVO.gift>
														<div class="bq806">
														<img src="/static/images/bq806.png" alt="">
														</div>
													</#if>
													<#if skuVO.agent == 1>
											            <span class="dujia">独家</span>
											        </#if>
													<#--<#if skuVO.gift>-->
                                                        <#--<img src="/static/images/xiangli_bg.png" class="xiangli">-->
													<#--</#if>-->
													${skuVO.showName}
												</a>
											</span>
										</div>
										<div class="row-biaoqian">
						 					<#if (merchant ? exists)>
											 <#list skuVO.tagList as item >
										        <#if item_index < 3>
										            <#if (item.uiType == 4)>
										                <span class="default">${item.name}</span>
										            </#if>
										            <#if item.uiType == 1>
										                <span class="linqi">${item.name}</span>
										            </#if>
										            <#if item.uiType == 2>
										                <span class="quan">${item.name}</span>
										            </#if>
										            <#if (item.uiType == 3)>
										                <span class="manjian">${item.name}</span>
										            </#if>
										        </#if>
											 </#list>
						 					</#if>
										</div>
										<div class="row-last">
											<#if (merchant ? exists)>
                                                <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
                                                <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
										        <#elseif skuVO.isControl ==1 >
										            <#if skuVO.isPurchase>
										                <#if (skuVO.uniformPrice ?? ) && (skuVO.uniformPrice != '')>
										                <div class="kongxiao-box">
										                    <span class="s-kx">控销价</span><span class="jg">￥${skuVO.uniformPrice}</span>
										                </div>
										                </#if>
										                <#if (skuVO.suggestPrice ?? ) && (skuVO.suggestPrice != '')>
														<div class="kongxiao-box">
										                    <span class="s-kx">零售价</span><span class="jg">￥${skuVO.suggestPrice}</span>
														</div>
										                </#if>
										                <#if (skuVO.grossMargin ??) && (skuVO.grossMargin != '')>
										                <div class="maoli-box">
										                    <span class="s-ml">毛利</span><span class="jg">${skuVO.grossMargin}</span>
										                </div>
										                </#if>
										            </#if>
										        <#else>
										            <#if (skuVO.uniformPrice ?? ) && (skuVO.uniformPrice != '')>
													<div class="kongxiao-box">
										                <span class="s-kx">控销价</span><span class="jg">￥${skuVO.uniformPrice}</span>
													</div>
										            </#if>
										            <#if (skuVO.suggestPrice ?? ) && (skuVO.suggestPrice != '')>
													<div class="kongxiao-box">
										                <span class="s-kx">零售价</span><span class="jg">￥${skuVO.suggestPrice}</span>
													</div>
										            </#if>
										            <#if (skuVO.grossMargin ??) && (skuVO.grossMargin != '')>
													<div class="maoli-box">
										                <span class="s-ml">毛利</span><span class="jg">${skuVO.grossMargin}</span>
													</div>
										            </#if>
										        </#if>
											</#if>
										</div>

										<div class="lib1-row2 text-overflow">
											<span class="title">规　　格：</span>
											<span class="info">${skuVO.spec}</span>
										</div>
										<div class="lib1-row4 text-overflow">
											<span class="title">生产厂家：</span>
											<span class="info">${skuVO.manufacturer}</span>
										</div>
                                        <div class="lib1-row7" >
                                            <#if skuVO.isThirdCompany == 0 && (skuVO.titleTagList?? && skuVO.titleTagList?size > 0)><span class="ziying">${skuVO.titleTagList[0].text}</span></#if>
                                            <#if skuVO.shopName ??><a href="javascript:;" style="text-decoration: none;width: 132px;display: inline-block;overflow: hidden;vertical-align: middle;text-overflow: ellipsis;white-space: nowrap;">${skuVO.shopName}</a></#if>
                                        </div>
									</div>
								</li>
 								<li class="lib2">
									<#if merchant ?exists>
										<#if skuVO.isControl ==1 >
											<#if skuVO.isPurchase>
<#--												<#if skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0'>-->
<#--													<div class="noright">-->
<#--                                                        <div>价格签署协议可见</div>-->
<#--                                                    </div>-->
                                                <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
                                                    <div class="noright">
                                                        <div>含税价认证资质后可见</div>
                                                    </div>
                                                <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
                                                    <div class="noright">
                                                        <div>含税价认证资质后可见</div>
                                                    </div>
                                                <#elseif skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0'>
                                                    <div class="noright">
                                                        <div>价格签署协议可见</div>
                                                    </div>
                                                <#elseif skuVO.agreementEffective?? && skuVO.agreementEffective =='3'>
                                                    <div class="noright">
                                                        <div>协议已冻结，价格解冻后可见</div>
                                                    </div>
												<#else>
													<div class="normal ">
                                                        <div class="newpricebox">
															<#if skuVO.priceType==1>
											                    <span class="newprice">￥${skuVO.fob}</span>
                                                                <span class="zhehou-price zhehou-price-${skuVO.id}"></span>
															<#else >
																<#if skuVO.skuPriceRangeList ??>
										                        <span class="newprice">￥
																<#list skuVO.skuPriceRangeList  as priceReange>
																	<#if priceReange_index==0>
																		${priceReange.price}
																	</#if>
																	<#if !priceReange_has_next>
										                              	  -${priceReange.price}
																	</#if>
																</#list>
										                        </span>
																</#if>
															</#if>
															<#if skuVO.promotionTag?? && skuVO.promotionTag!=''>
													            <span class="xiangou">
													            	<img src="/static/images/xgsanjiao.png" class="sanjiao">
																	${skuVO.promotionTag }
												            	</span>
															</#if>
                                                        </div>
                                                        <div class="oldpricebox">
                                                            <span class="oldprice-tit">原价：</span>
                                                            <span class="oldprice">￥${skuVO.retailPrice}</span>
                                                        </div>
                                                        <!--不参与返点提示-->
													<#if (skuVO.blackProductText)!>
														<div class="nofd">
															${skuVO.blackProductText}
                                                        </div>
													</#if>
                                                        <div class="endtimebox">
														<#if (skuVO.promotionEndTime?exists)>
                                                            <span>促销截止时间：</span>
                                                            <span>${skuVO.promotionEndTime?string("yyyy-MM-dd")}</span>
														</#if>
                                                        </div>
                                                    </div>
												</#if>
											<#else>
                                                <div class="noright">
                                                    <div>暂无购买权限</div>
                                                    <#--<div class="two-row">原价：暂无购买权限</div>-->
                                                </div>
											</#if>
										<#else>
<#--											<#if skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0'>-->
<#--												<div class="noright">-->
<#--                                                    <div>价格签署协议可见</div>-->
<#--                                                </div>-->
                                            <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
                                                <div class="noright">
                                                    <div>含税价认证资质后可见</div>
                                                </div>
                                            <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
                                                <div class="noright">
                                                    <div>含税价认证资质后可见</div>
                                                </div>
                                            <#elseif skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0'>
                                                <div class="noright">
                                                    <div>价格签署协议可见</div>
                                                </div>
                                            <#elseif skuVO.agreementEffective?? && skuVO.agreementEffective =='3'>
                                                <div class="noright">
                                                    <div>协议已冻结，价格解冻后可见</div>
                                                </div>
											<#else >
												<div class="normal ">
													<div class="newpricebox">
														  <#if skuVO.priceType==1 >
																<span class="newprice">￥${skuVO.fob}</span>
                                                                <span class="zhehou-price zhehou-price-${skuVO.id}"></span>
														  <#else >
															  <#if skuVO.skuPriceRangeList ??>
																	<span class="newprice">￥
																		<#list skuVO.skuPriceRangeList  as priceReange>
																			<#if priceReange_index==0>
																				${priceReange.price}
																			</#if>
																			<#if !priceReange_has_next>
																				  -${priceReange.price}
																			</#if>
																		</#list>
																	  </span>
															  </#if>
														  </#if>
															 <#if skuVO.promotionTag?? && skuVO.promotionTag!=''>
																<span class="xiangou">
																	<img src="/static/images/xgsanjiao.png" class="sanjiao">
																	${skuVO.promotionTag }
																</span>
															 </#if>
													</div>
													<div class="oldpricebox">
														<span class="oldprice-tit">原价：</span>
														<span class="oldprice">￥${skuVO.retailPrice}</span>
													</div>
													<!--不参与返点提示-->
														<#if skuVO.FdType != 4>
															<div class="nofd">
																${skuVO.FdType}
															</div>
														</#if>
												<#--<div class="endtimebox">-->
												<#--<span>限时促销倒计时：</span><span>02:23:25</span>-->
												<#--</div>-->
													<div class="endtimebox">
															<#if (skuVO.promotionEndTime?exists)>
																<span>促销截止时间：</span>
																<span>${skuVO.promotionEndTime?string("yyyy-MM-dd")}</span>
															</#if>
													</div>
												</div>
											</#if>
										</#if>
									<#else>
                                        <div class="login-show">
                                            <div>价格登录可见</div>
                                            <#--<div class="two-row">原价：价格登录可见</div>-->
                                        </div>
									</#if>
                                </li>
                                <li class="lib_zbz">
                                    <!--中包装-->
                                    <div class="zbz-listbox">
                                        <div class="zbz-listbox-top">${skuVO.mediumPackageTitle}
											<#if skuVO.isSplit == 0>
												<span class="chailin">${skuVO.isSplitTitle}</span>
											</#if>
										</div>
										<div class="kucun"><span>库存：</span>
											<span>
												<#if skuVO.isSplit == 0 && skuVO.availableQty - skuVO.mediumPackageNum lt 0>
													0
												<#else >
													<#if (skuVO.availableQty > 100)>
														大于100
													<#else >
													${skuVO.availableQty}
													</#if>
												</#if>
											</span>
										</div>
									</div>
									
                                </li>
                               <li class="lib5">
                                   <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
                                   <div class="verifyBox">
                                       <a href="/merchant/center/license/findLicenseCategoryInfo.htm">资质认证</a>
                                   </div>
                                   <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
                                       <div class="verifyBox">
                                           <a href="/merchant/center/license/findLicenseCategoryInfo.htm">资质审核中</a>
                                       </div>
                                   <#elseif skuVO.agreementEffective?? && skuVO.agreementEffective =='3'>
<#--                                       <div class="noright">-->
<#--                                           <div>协议已冻结，价格解冻后可见</div>-->
<#--                                       </div>-->
                                   <#else >
									<div class="suliang">
										<a href="javascript:void(0);" class="sub fl">-</a>
										<input class="fl" type="text" value="${skuVO.cartProductNum}" id="buyNumlb_${skuVO.id}" name="buyNumlb" isSplit="${skuVO.isSplit}" middpacking="${skuVO.mediumPackageNum}"
											   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
											   onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"/>
										<a href="javascript:void(0);" class="add fl">+</a>
									</div>
                                    <a href="javascript:void(0);" class="addbuy fl" onclick="addCartLb(${skuVO.id},${skuVO.mediumPackageNum},${skuVO.isSplit},event)" id="href_LB_${skuVO.id}">加入采购单</a>
									<#if skuVO.favoriteStatus == 1>
                                        <div class="w-collectZone_list hasCollect initial j-collectBtn fl" id="${skuVO.id}" onclick="rmCollectionLb(${skuVO.id},event,this)" >
                                            <div class="zone-1">
                                                <div class="top top-1">
                                                    <span class="w-icon-normal icon-normal-collectEpt"></span>
                                                </div>
                                                <div class="top top-2">
                                                    <span class="w-icon-normal icon-normal-collectFull"></span>
                                                </div>
                                            </div>
                                        </div>
									<#else>
                                        <div class="w-collectZone_list nopCollect initial j-collectBtn fl" id="${skuVO.id}" onclick="addCollectionLb(${skuVO.id},event,this)" >
                                            <div class="zone-1">
                                                <div class="top top-1">
                                                    <span class="w-icon-normal icon-normal-collectEpt"></span>
                                                </div>
                                                <div class="top top-2">
                                                    <span class="w-icon-normal icon-normal-collectFull"></span>
                                                </div>
                                            </div>
                                        </div>
									</#if>
                                   </#if>
								</li>
							</ul>
						</div>
					</#list>

				</div>
				<!--分页器-->
				<div class="page">
					 <#import "/common/pager.ftl" as p>
              		<@p.pager currentPage=pager.currentPage limit=pager.limit total=pager.total pageCount=pager.pageCount toURL=pager.requestUrl method="get"/>
				</div>
				<#else>
					<#if isControl==1>
						<#if merchant? exists>
							<!--没有控销商品 默认2222隐藏 显示去掉noshow即可-->
							<div class="nokongxbox">
								<div class="tpbox">
									<img src="${ctx}/static/images/nokongxiao.png" alt="">
								</div>
							</div>
						</#if>
						<#else>
							<!--没有搜索到商品 默222111认隐藏 显示去掉noshow即可-->
							<div class="nosearchgood">
								<div class="tpbox">
									<img src="${ctx}/static/images/nosearchgood.png" alt="">
								</div>
							</div>
						
					</#if>
				</#if>

				<!--控销商品登录可见 默认隐藏 显示去掉noshow即可-->
				<#if merchant? exists>
					<div class="kongxbox noshow" id="3">
				<#else>
					<#if isControl==1>
						<div class="kongxbox" id="1">
					<#else>
						<div class="kongxbox noshow" id="2">
					</#if>
							<div class="tpbox">
								<img src="/static/images/kongxiao.png" alt="">
							</div>
							<div class="infobox">
								控销商品登录可见哦~
							</div>
						</div>
				</#if>
			</div>
            </#if>
			<!--主体部分结束-->

			<!--底部导航区域开始-->
			<div class="footer" id="footer">
				<#include "/common/footer.ftl" />
			</div>
			<!--底部导航区域结束-->
	</body>
		
</html>