package com.xyy.ec.pc.newfront.vo;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class ShotQueryParam {

    /**
     * 会员ID
     */
    private Long merchantId;
    /**
     * 店铺类型 1 自营 2 POP
     */
    private Integer shopPropertyCode;
    /**
     * 排序规则 0 默认 1 最新
     */
    private String sort;
    /**
     * 页长
     */
    private Integer pageSize;
    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 店铺编码list
     */
    private List<String> shopCodes = Lists.newArrayList();

    /**
     * 优惠券模板ID
     */
    private Long voucherTemplateId;

}
