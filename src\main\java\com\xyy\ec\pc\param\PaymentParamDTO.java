package com.xyy.ec.pc.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName PaymentParamDTO
 * <AUTHOR>
 * @Date 2023/8/2 10:21
 * @Version 1.0
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentParamDTO implements Serializable {
    private Long orderId;
    private Integer payRoute;
    private Integer payerType;
    private String wxCode;
    private String quitUrl;
    private Integer directPay;
    private Integer payChannelId;
    private String bankCardInfo;
    private String paycode;
    private Integer payMode;
    private String cardId;
    private String token;
    private String tranNo;
    private String orderNo;
    private String reqScene;
    private BigDecimal lastPaymentDiscount;

    // 结算页购物金充值 or 购物金直充，这里设置充值金额
    private BigDecimal amount;

    // 充值类型 2 是购物金充值
    private Integer rechargeType;
}