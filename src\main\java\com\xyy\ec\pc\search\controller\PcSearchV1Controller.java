package com.xyy.ec.pc.search.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.enums.MaiDianActionEnum;
import com.xyy.ec.pc.enums.SnowGroundTypeEnum;
import com.xyy.ec.pc.enums.TerminalTypeEnum;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.recommend.dto.RecommendQtListDataDTO;
import com.xyy.ec.pc.recommend.helpers.RecommendQuickTrackingDataHelper;
import com.xyy.ec.pc.search.config.SearchProperties;
import com.xyy.ec.pc.search.dto.QtListDataDTO;
import com.xyy.ec.pc.search.ecp.helpers.QuickTrackingDataHelper;
import com.xyy.ec.pc.search.enums.MaiDianSearchSpIdTypeEnum;
import com.xyy.ec.pc.search.enums.SearchSourceEnum;
import com.xyy.ec.pc.search.enums.SuiXinPinSourceType;
import com.xyy.ec.pc.search.params.PcSearchProductListQueryParam;
import com.xyy.ec.pc.search.service.LayoutService;
import com.xyy.ec.pc.search.service.SearchEngineService;
import com.xyy.ec.pc.search.service.SkuProductService;
import com.xyy.ec.pc.search.utils.HandleFiledsUtil;
import com.xyy.ec.pc.search.utils.SnowGroundUtil;
import com.xyy.ec.pc.search.vo.MaiDianVo;
import com.xyy.ec.pc.search.vo.PcSearchProductInfoVo;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.EncodeUtil;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.constants.ProductStatus;
import com.xyy.ec.search.engine.entity.AggregatedVo;
import com.xyy.ec.search.engine.entity.EcProductVo;
import com.xyy.ec.search.engine.enums.SearchType;
import com.xyy.ec.search.engine.metadata.IPage;

import javax.servlet.http.HttpServletRequest;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;


/**
 * 描述:搜索接口一期接口
 *
 * <AUTHOR>
 * @version V1.0
 * @Descriotion: PC搜索接口
 * @create 2021/11/29 19:38
 */
@Slf4j
@RequestMapping("/pc/search/v1")
@RestController

public class PcSearchV1Controller extends BaseController {

    @Value("${search.sxp.filter.soldOut.product:false}")
    private Boolean filterSoldOutProduct;

    @Autowired
    private SearchEngineService searchEngineService;
    @Autowired
    private SkuProductService productService;
    @Autowired
    private SearchUtils searchUtils;

    @Autowired
    private SnowGroundUtil snowGroundUtil;

    @Autowired
    private PcVersionUtils appVersionUtils;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private LayoutService layoutService;

    @Autowired
    private SearchProperties searchProperties;

    /**
     * 大搜接口
     *
     * @param queryParam
     * @param request
     * @return
     */
    @PostMapping("/productList")
    @ResponseBody
    public XyyJsonResult searchProductList(PcSearchProductListQueryParam queryParam, HttpServletRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("searchProductList queryParam：{}", JSONObject.toJSONString(queryParam));
        }
        String spType = queryParam.getSptype();
        String spId = queryParam.getSpid();
        String sid = queryParam.getSid();
        String nsid = queryParam.getNsid();
        String listoffset = queryParam.getListoffset();
        String nsptype = queryParam.getNsptype();
        String infurl = "/pc/search/v1/productList";
        String nspid = queryParam.getNspid();

        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                // 未登录 提示鉴权异常
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }
            Boolean isNoResult = false;
            Integer count = 0;

            //判断是否KA用户，如果是KA用户则只查询有连锁指导价的商品
            if (merchant != null && merchant.getIsKa()) {
                queryParam.setHasGuidePrice(true);
            }
            Map<String, Object> resultMap = new HashMap<>();
            Long merchantId = merchant.getId();
            queryParam.setBranchCode(merchant.getRegisterCode());
            queryParam.setMerchantId(merchantId);
            //获取请求类型
            int preType = queryParam.getType() == null ? 1 : queryParam.getType();
            //新增搜索热搜词
            searchEngineService.saveHistoryWord(merchantId, queryParam.getKeyword());
            queryParam.setPlatform(TerminalTypeEnum.PC.getValue());
            Integer pageNum = Optional.ofNullable(queryParam.getPageNum()).orElse(1);
            Integer pageSize = Optional.ofNullable(queryParam.getPageSize()).orElse(20);
            queryParam.setPageNum(pageNum);
            queryParam.setPageSize(pageSize);
            IPage<EcProductVo> iPage;
            if (Objects.nonNull(queryParam.getRequestType()) && queryParam.getRequestType().intValue() == BigInteger.ONE.intValue()) {
                //分页请求
                iPage = searchEngineService.searchForPage(pageNum, pageSize, TerminalTypeEnum.PC.getValue(), queryParam);
            } else {
                //流式请求
                iPage = searchEngineService.searchForList(pageNum, pageSize, TerminalTypeEnum.PC.getValue(), queryParam);
            }
            if (log.isDebugEnabled()) {
                log.debug("searchProductList iPage：{}", JSONObject.toJSONString(iPage));
            }
            Integer resultType = iPage.getExtendsMap().get("type") == null ? SearchType.NORMAL_SEARCH.getType() : (Integer) iPage.getExtendsMap().get("type");
            if (resultType.equals(3)) {
                isNoResult = true;
            }
            resultMap.put("keyword", queryParam.getKeyword());
            resultMap.put("type", resultType);
            resultMap.put("wordList", searchEngineService.getHotSearchWordList(queryParam.getMerchantId(), queryParam.getBranchCode(), TerminalTypeEnum.PC.getValue(), resultType));
            resultMap.put("rows", new ArrayList<>());
            resultMap.put("isEnd", true);
            resultMap.putAll(getActivityInfo(queryParam.getBranchCode(), queryParam.getTagList()));
            count = iPage.getRecordList().size();
            HandleFiledsUtil<PcSearchProductListQueryParam> queryParamHandleFiledsUtil = new HandleFiledsUtil<>();
            queryParam = queryParamHandleFiledsUtil.handleSnowGroundParam(nsptype, nspid, nsid, listoffset, merchantId, TerminalTypeEnum.PC.getValue(), count, isNoResult, queryParam.getPageSource(), queryParam.getKeyword(), queryParam, PcSearchProductListQueryParam.class);
            if (CollectionUtil.isNotEmpty(iPage.getRecordList())) {
                List<PcSearchProductInfoVo> csuInfoList = productService.getProductInfoList(queryParam.getMerchantId(), queryParam.getBranchCode(), iPage.getRecordList(), false, SearchUtils.getSearchSource(queryParam));
                //处理随心拼白名单商品
                csuInfoList = handleWhiteProductList(queryParam, csuInfoList);
                csuInfoList = productService.handleSnowGroundParam(csuInfoList, queryParam.getNsid(), queryParam.getListoffset());
                HandleFiledsUtil<PcSearchProductInfoVo> fieldFilterUtil = new HandleFiledsUtil<>();
                //根据资质状态判断价格是否展示
                MerchantBussinessDto merchantBussinessDto = appVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
                //无资质用户隐藏价格信息
                Integer licenseStatus = fieldFilterUtil.hideFobForProductList(merchantBussinessDto, csuInfoList);
                //随心拼埋点数据
                if (SearchUtils.isSuiXinPinSearch(queryParam) && CollectionUtil.isNotEmpty(csuInfoList)) {
                    //过滤售罄状态的商品，正常情况不需要，兜底
                    if (BooleanUtils.isTrue(filterSoldOutProduct)) {
                        csuInfoList = csuInfoList.stream().filter(item -> ProductStatus.SOLD_OUT.getStatus() != item.getStatus()).collect(Collectors.toList());
                    }
                    RecommendQtListDataDTO qtListDataDTO = RecommendQtListDataDTO.builder()
                            .result_cnt(iPage.getTotalCount())
                            .page_no(pageNum)
                            .page_size(pageSize)
                            .total_page((int) iPage.getPages())
                            .key_word(null)
                            .exp_id(null)
                            .build();
                    RecommendQuickTrackingDataHelper.handleSxpQuickTrackingData(csuInfoList, qtListDataDTO, queryParam);
                }
                resultMap.put("rows", csuInfoList);
                resultMap.put("licenseStatus", licenseStatus);
                resultMap.put("isEnd", iPage.isEnd());
                if (!iPage.isEnd()) {
                    queryParam.setPageNum(++pageNum);
                    queryParam.setPageSize(pageSize);
                    queryParam.setType(resultType);
                    resultMap.put("requestParam", queryParam);
                }
            } else if(SearchUtils.isSuiXinPinSearch(queryParam)) {
                //随心拼搜索结果为空添加白名单配置数据
                List<PcSearchProductInfoVo> csuInfoList = handleWhiteProductList(queryParam, null);
                if (log.isDebugEnabled()) {
                    log.debug("searchProductList 随心拼搜索结果为空添加白名单配置数据，queryParam：{}，csuInfoList：{}", JSONObject.toJSONString(queryParam), JSONArray.toJSONString(csuInfoList));
                }
                //随心拼埋点数据
                if (CollectionUtil.isNotEmpty(csuInfoList)) {
                    RecommendQtListDataDTO qtListDataDTO = RecommendQtListDataDTO.builder()
                            .result_cnt(Long.valueOf(csuInfoList.size()))
                            .page_no(NumberUtils.INTEGER_ONE)
                            .page_size(csuInfoList.size())
                            .total_page(NumberUtils.INTEGER_ONE)
                            .key_word(null)
                            .exp_id(null)
                            .build();
                    RecommendQuickTrackingDataHelper.handleSxpQuickTrackingData(csuInfoList, qtListDataDTO, null);
                }
                resultMap.put("rows", csuInfoList);
            }
            snowGroundUtil.skuPageExposureEvent(request, String.valueOf(TerminalTypeEnum.PC.getValue()), queryParam.getPageSource(), queryParam.getPageurl(), queryParam.getListdata(),
                    infurl, queryParam.getNsptype(), queryParam.getNspid(), queryParam.getNsid(), queryParam.getListoffset());
            //生成埋点参数
            MaiDianVo maiDianVo = SearchUtils.buildMaiDianVoInfo(queryParam, TerminalTypeEnum.PC.getValue(), SnowGroundTypeEnum.SEARCH);
            spIdFormat(queryParam, maiDianVo);
            String newSpType = StringUtil.isEmpty(spType) ? maiDianVo.getSpType() : spType;
            String newSpId = maiDianVo.getSpId();
            String newSid = StringUtil.isEmpty(sid) ? maiDianVo.getSid() : sid;
            List<Long> skuIdList = iPage.getRecordList().stream().map(ecProductVo -> ecProductVo.getId()).collect(Collectors.toList());
            //1、正常搜索请求，返回无结果推荐类型重新生成埋点数据，2、直接请求推荐类型，不需要重新生成埋点数据
            if (preType == SearchType.NORMAL_SEARCH.getType() && resultType == SearchType.RECOMMENDATION.getType()) {
                queryParam.setSpFrom(SnowGroundTypeEnum.RecommendSpFrom.PC_SEARCH_NO_RECALL_REC.getValue());
                maiDianVo = SearchUtils.buildMaiDianVoInfo(queryParam, TerminalTypeEnum.PC.getValue(), SnowGroundTypeEnum.RECOMMEND);
                //无结果sptype设置为3
                sid = SearchUtils.commaFormatStr(newSid, maiDianVo.getSid());
                resultMap.put("sptype", SearchUtils.commaFormatStr(newSpType, maiDianVo.getSpType()));
                resultMap.put("spid", SearchUtils.commaFormatStr(newSpId, maiDianVo.getSpId()));
                resultMap.put("sid", sid);
                //曝光埋点上报
                searchUtils.searchExposureEvent(request, sid, maiDianVo.getSpType(), maiDianVo.getSpId(), pageNum, pageSize, skuIdList);
            } else if (preType == SearchType.RECOMMENDATION.getType() && resultType == SearchType.RECOMMENDATION.getType()) {
                //2、直接请求推荐类型，不需要重新生成埋点数据
                resultMap.put("sptype", spType);
                resultMap.put("spid", spId);
                resultMap.put("sid", sid);
                //曝光埋点上报
                searchUtils.searchExposureEvent(request, sid, maiDianVo.getSpType(), maiDianVo.getSpId(), pageNum, pageSize, skuIdList);
            } else {
                resultMap.put("sptype", newSpType);
                resultMap.put("spid", newSpId);
                resultMap.put("sid", newSid);
                //曝光埋点上报
                searchUtils.searchExposureEvent(request, newSid, maiDianVo.getSpType(), maiDianVo.getSpId(), pageNum, pageSize, skuIdList);
            }
            //返回总数
            resultMap.put("pages", Objects.nonNull(iPage.getPages()) ? iPage.getPages() : 0);
            resultMap.put("total", Objects.nonNull(iPage.getTotalCount()) ? iPage.getTotalCount() : 0);
            queryParam.setSptype(String.valueOf(resultMap.get("sptype")));
            queryParam.setSpid(String.valueOf(resultMap.get("spid")));
            queryParam.setSid(String.valueOf(resultMap.get("sid")));
            resultMap.put("requestParam", queryParam);
            XyyJsonResult result = XyyJsonResult.createSuccess();
            resultMap.forEach((k, v) -> {
                result.addResult(k, v);
            });
            if (log.isDebugEnabled()) {
                log.debug("searchProductList result：{}", JSONObject.toJSONString(result));
            }
            return result;
        } catch (Exception e) {
            log.error("/app/search/v1/productList ,请求参数 : {}, error : ", JSONObject.toJSONString(queryParam), e);
            return XyyJsonResult.createFailure();
        }
    }

    /**
     * 查随心拼店铺白名单商品
     *
     * @param queryParam
     * @param csuInfoList
     * @return
     */
    private List<PcSearchProductInfoVo> handleWhiteProductList(PcSearchProductListQueryParam queryParam, List<PcSearchProductInfoVo> csuInfoList) {
        //随心拼全部页面 && 第一页 查询随心拼白名单商品
        if (SearchUtils.isSuiXinPinSearch(queryParam)) {
            //判断搜索结果是否为空，不为空则改变sourceType=1
            csuInfoList = Optional.ofNullable(csuInfoList).orElseGet(()->new ArrayList<>()).stream().map(appSearchProductInfoVo -> {
                appSearchProductInfoVo.setSourceType(SuiXinPinSourceType.STRATEGY_REC.getValue());
                return appSearchProductInfoVo;
            }).collect(Collectors.toList());

            //随心拼全部页面
            if (SearchUtils.isSuiXinPinAllDataPage(queryParam) && !Objects.equals(queryParam.getSpFrom(), SnowGroundTypeEnum.SearchSpFrom.SUI_XIN_PIN_CONFIRM_ORDER_PAGE.getValue())) {
                int total = csuInfoList.size();
                //随心拼全部页面第一页
                if (queryParam.getPageNum().equals(BigInteger.ONE.intValue())) {
                    List<PcSearchProductInfoVo> whiteProductList = productService.getSuiXinPinWhiteProductInfoList(queryParam.getMerchantId(), queryParam.getBranchCode(), queryParam.getShopCodes());
                    if (CollectionUtil.isEmpty(whiteProductList)) {
                        log.warn("随心拼全部页查询白名单, whiteProductList isEmpty.");
                        return csuInfoList;
                    }
                    int whiteTotal = whiteProductList.size();
                    //去重 & 设置下次请求排除原品ID + 白名单id
                    List<String> whiteIdList = whiteProductList.stream().map(appSearchProductInfoVo -> String.valueOf(appSearchProductInfoVo.getId())).collect(Collectors.toList());
                    List<String> excludeIds = StrUtil.split(queryParam.getExcludeIds(), StrPool.COMMA);
                    excludeIds = Optional.ofNullable(excludeIds).orElseGet(()->new ArrayList<>());
                    //注意: 随心拼页面排除原品ID必须在第一位，否则排除原品ID失效
                    String excludePid = CollectionUtil.isNotEmpty(excludeIds) ? excludeIds.get(0) : "";
                    //下次请求排除商品ID + 白名单ID
                    excludeIds.addAll(whiteIdList);
                    log.debug("随心拼全部页查询白名单, excludeIds : {}, excludePid : {}, whiteIdList : {}, whiteTotal : {}", excludeIds, excludePid, whiteIdList, whiteTotal);
                    //搜索结果排除白名单ID
                    csuInfoList = csuInfoList.stream().filter(appSearchProductInfoVo -> !whiteIdList.contains(String.valueOf(appSearchProductInfoVo.getId()))).collect(Collectors.toList());
                    log.debug("随心拼全部页查询白名单, 搜索数量 : {}, 排除白名单ID : {}, 排除白名单后数量 : {}",total, whiteIdList, csuInfoList.size());
                    //白名单结果排除原商品ID，搜索结果中已经排除过原商品ID
                    whiteProductList = whiteProductList.stream().filter(appSearchProductInfoVo -> !String.valueOf(appSearchProductInfoVo.getId()).equals(excludePid)).collect(Collectors.toList());
                    log.debug("随心拼全部页查询白名单, 白名单ID : {}, 白名单数量 : {}, 排除原商品ID : {}, 排除原商品后数量 : {}",whiteIdList, whiteTotal, excludePid, whiteProductList.size());
                    csuInfoList.addAll(0, whiteProductList);
                    log.debug("随心拼全部页查询白名单, 搜索数量 : {}, 下次请求排除商品ID : {}, 白名单数量 : {}, 返回数量 : {}",total, excludeIds, whiteProductList.size(), csuInfoList.size());
                    //更新请求参数
                    queryParam.setExcludeIds(CollUtil.join(excludeIds, StrPool.COMMA));
                } else {
                    //排除原商品ID + 白名单ID
                    List<String> excludeIds = StrUtil.split(queryParam.getExcludeIds(), StrPool.COMMA);
                    if (CollectionUtil.isNotEmpty(excludeIds)) {
                        csuInfoList = csuInfoList.stream().filter(appSearchProductInfoVo -> !excludeIds.contains(String.valueOf(appSearchProductInfoVo.getId()))).collect(Collectors.toList());
                    }
                    log.debug("随心拼全部页查询白名单, 排除ID列表 : {}, 搜索数量 : {}, 排除后数量 : {}", excludeIds, total, csuInfoList.size());
                }
            }
        }

        return csuInfoList;
    }

    /**
     * 搜索聚合接口
     *
     * @param queryParam
     * @param request
     * @return
     */
    @PostMapping("/aggs")
    @ResponseBody
    public XyyJsonResult searchAggs(PcSearchProductListQueryParam queryParam, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                // 未登录 提示鉴权异常
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }
            Map<String, Object> resultMap = new HashMap<>();
            Long merchantId = merchant.getId();
            queryParam.setBranchCode(merchant.getRegisterCode());
            queryParam.setMerchantId(merchantId);
            queryParam.setPlatform(TerminalTypeEnum.PC.getValue());

            //请求聚合接口
            AggregatedVo aggregatedVo = searchEngineService.queryForAggregated(queryParam);

            //埋点上报处理
            MaiDianVo maiDianVo = SearchUtils.buildMaiDianVoInfo(queryParam,TerminalTypeEnum.PC.getValue(), SnowGroundTypeEnum.SEARCH );
            spIdFormat(queryParam, maiDianVo);
            snowGroundUtil.searchAggsExposureEvent(request, maiDianVo, aggregatedVo == null ? Maps.newHashMap() : aggregatedVo.getAggregations());

            String newSpType = StringUtil.isEmpty(queryParam.getSptype()) ? maiDianVo.getSpType() : queryParam.getSptype();
            String newSpId = maiDianVo.getSpId();
            String newSid = StringUtil.isEmpty(queryParam.getSid()) ? maiDianVo.getSid() : queryParam.getSid();

            resultMap.put("keyword", queryParam.getKeyword());
            resultMap.put("sptype", newSpType);
            resultMap.put("spid", newSpId);
            resultMap.put("sid", newSid);
            resultMap.put("aggregations", aggregatedVo == null ? Maps.newHashMap() : aggregatedVo.getAggregations());

            XyyJsonResult result = XyyJsonResult.createSuccess();
            resultMap.forEach((k, v) -> {
                result.addResult(k, v);
            });

            return result;
        } catch (Exception e) {
            log.error("/app/search/v1/aggs, 请求参数 : {}, error : ", JSONObject.toJSONString(queryParam), e);
            return XyyJsonResult.createFailure();
        }
    }

    private void spIdFormat(PcSearchProductListQueryParam queryParam, MaiDianVo maiDianVo) {
        //获取spidType
        Integer spidType = SearchUtils.getSearchSpIdType(queryParam.getSpid());
        if (spidType != null) {
            maiDianVo.setSpId(String.format(maiDianVo.getSpId(), spidType));
        } else {
            maiDianVo.setSpId(String.format(maiDianVo.getSpId(), MaiDianSearchSpIdTypeEnum.SEARCH.getValue()));
        }
    }

    private Map<String, Object> getActivityInfo(String branchCode, String tags) {
        Map<String, Object> activityMap = new HashMap<>();
        try {
            if (StringUtil.isNotEmpty(searchProperties.getActivityInfo())) {
                String activityInfo = EncodeUtil.urlDecode(searchProperties.getActivityInfo());
                activityMap = JSON.parseObject(activityInfo, Map.class);
                String activityScreenTagId = activityMap.get("activityScreenTagId").toString();
                List<String> activityScreenTagIdList = Arrays.asList(activityScreenTagId.split(","));
                activityMap.remove("activityScreenTagId");
                for (String id : activityScreenTagIdList) {
                    if (id.contains(branchCode)) {
                        if (!layoutService.isContainCmsTag(branchCode, tags)) {
                            activityMap.put("activityScreenTagId", id.split(":")[1]);
                        }
                    }
                }
                if (!activityMap.containsKey("activityScreenTagId")) {
                    activityMap.clear();
                }
            }
        } catch (Exception e) {
            activityMap.clear();
            log.warn("getActivityInfo warn", e);
        }
        return activityMap;
    }


}
