package com.xyy.ec.pc.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/** 限流专用
 *
 *
 */
@Configuration
public class RedisConfig {

    private static final Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    @Value("${spring.redis.flowrate.host}")
    private String host;

    @Value("${spring.redis.flowrate.port}")
    private int port;

    @Value("${spring.redis.flowrate.password}")
    private String password;

    @Value("${spring.redis.flowrate.timeout}")
    private int timeout;

    /**
     * 支持最大连接数
     */
    @Value("${spring.redis.flowrate.pool.max-active}")
    private int maxTotal;

    /**
     * 最大空闲连接数
     */
    @Value("${spring.redis.flowrate.pool.max-idle}")
    private int maxIdle;

    /**
     * 最小空闲连接数
     */
    @Value("${spring.redis.flowrate.pool.min-idle}")
    private int minIdle;

    @Value("${spring.redis.flowrate.pool.max-wait}")
    private long maxWaitMillis;

    @Value("${spring.redis.flowrate.block-when-exhausted}")
    private boolean blockWhenExhausted;

    @Bean
    public JedisPool redisPoolFactory() throws Exception {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(maxTotal);
        jedisPoolConfig.setMaxIdle(maxIdle);
        jedisPoolConfig.setMinIdle(minIdle);
        jedisPoolConfig.setMaxWaitMillis(maxWaitMillis);
        // 连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true
        jedisPoolConfig.setBlockWhenExhausted(blockWhenExhausted);
        // 是否启用pool的jmx管理功能, 默认true
        jedisPoolConfig.setJmxEnabled(true);
        JedisPool jedisPool = new JedisPool(jedisPoolConfig, host, port, timeout, password);
        BaseRedisUtil.setJedisPool(jedisPool);
        return jedisPool;
    }
}
