package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.dto.HostWordsRespVO;
import com.xyy.ec.pc.newfront.service.HeaderNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@CustomizeCmsResponse
@RestController
@RequestMapping("/new-front/header")
public class HeaderNewController {

    @Resource
    private HeaderNewService headerNewService;

    @GetMapping("/hot-words")
    public AjaxResult<HostWordsRespVO> getHeaderData() {
        try {
            HostWordsRespVO hotWords = headerNewService.getHotWords();
            return AjaxResult.successResult(hotWords);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

}
