package com.xyy.ec.pc.newfront.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class CouponRespVO {
    /**
     * 生效时间
     */
    private Date validDate;
    /**
     * 失效时间
     */
    private Date expireDate;
    /**
     * 券类型
     */
    private Integer voucherType;

    private String voucherTypeDesc;
    private Long voucherTemplateId;
    private String jumpUrl;
    private Integer noReceiveCount;
    /**
     * 券文案
     */
    private String couponText;

    /**
     * 折扣额度
     */
    private BigDecimal discount;

    /**
     * 优惠券领取状态
     */
    private Integer state;

    /**
     * 券描述
     */
    private String voucherInstructions;

    /**
     * 代金券中的金额
     */
    private BigDecimal moneyInVoucher;

    /**
     * 有效时间（天数）
     */
    private Integer validDays;

    /**
     * 券模板ID
     */
    private Long templateId;

    /**
     * 优惠券名称
     */
    private String templateName;

    /**
     * 券文案
     */
    private String voucherTitle;

}
