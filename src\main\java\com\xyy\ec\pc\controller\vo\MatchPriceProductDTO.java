package com.xyy.ec.pc.controller.vo;

import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 匹配商品计算实体
 */
@Data
public class MatchPriceProductDTO extends ProductDTO {

    private static final long serialVersionUID = -4442468567515935820L;
    /**
     * 实付价/入库价(单价)：实付价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）-余额抵扣；
     */
    private BigDecimal purchasePrice;
}
