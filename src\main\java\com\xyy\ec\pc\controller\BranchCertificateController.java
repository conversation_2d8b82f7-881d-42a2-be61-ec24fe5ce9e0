package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.layout.buinese.api.BranchCertificateBuineseApi;
import com.xyy.ec.layout.buinese.dto.BranchCertificateDetailBuineseDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 区域证书 Controller
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/branchCertificate")
@Slf4j
public class BranchCertificateController extends BaseController {

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private BranchCertificateBuineseApi branchCertificateBuineseApi;

    @Autowired
    private Config config;

    /**
     * 查看证书信息
     *
     * @param name
     * @return
     */
    @RequestMapping("/showByName/{name}.htm")
    public ModelAndView showByName(@PathVariable("name") String name) {
        try {
            ModelMap modelMap = new ModelMap();
            modelMap.put("name", name);
            return new ModelAndView("/branchCertificate/showByName.ftl", modelMap);
        } catch (Exception e) {
            log.error("查看区域证书信息失败", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 查看证书信息
     *
     * @param id
     * @return
     */
    @RequestMapping("/show/{id}.htm")
    public ModelAndView show(@PathVariable("id") Long id) {
        try {
            ModelMap modelMap = new ModelMap();
            String imageRelativePath = branchCertificateBuineseApi.getImageRelativePath(id);
            // 图片URI
            String branchCertificateUri = null;
            if (StringUtil.isNotEmpty(imageRelativePath)) {
                branchCertificateUri = config.getProductImagePathUrl() + imageRelativePath;
            }
            modelMap.put("branchCertificateUri", branchCertificateUri);
            return new ModelAndView("/branchCertificate/show.ftl", modelMap);
        } catch (Exception e) {
            log.error("查看区域证书信息失败", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 获取区域编码的证书列表
     *
     * @return
     */
    @RequestMapping("listByBranchCode")
    @ResponseBody
    public Map<String, Object> listByBranchCode(HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant == null) {
                merchantId = 0L;
            } else {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            List<BranchCertificateDetailBuineseDto> list = branchCertificateBuineseApi.selectOneByBranchCode(branchCode);
            return this.addResult("data", list);
        } catch (Exception e) {
            log.error("获取区域编码的证书列表异常", e);
            return this.addError("获取区域编码的证书列表异常");
        }
    }

}
