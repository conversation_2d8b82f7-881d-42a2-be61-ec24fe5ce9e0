package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.licence.EnclosureParams;
import com.xyy.ec.merchant.bussiness.dto.licence.LicenseAuditImgParams;
import com.xyy.ec.merchant.bussiness.dto.licence.LicenseAuditParams;
import com.xyy.ec.pc.newfront.service.LicenseAuditNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-07-23 15:25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class LicenseAuditNewServiceImpl implements LicenseAuditNewService {

    @Reference(version = "1.0.0")
    private MerchantLicenseApi licenseApi;

    @Value("${config.product_image_path_url}")
    private String PRODUCT_IMAGE_PATH_URL;

    private final XyyIndentityValidator xyyIndentityValidator;


    /**
     * 点击添加“添加首营资质/资质变更”按钮之前的效验
     *
     * @param type 1首营 2变更
     * @return
     */
    @Override
    public AjaxResult<Object> initLicenseAuditDetailVaild(Integer type) {
        log.info("新增资质审核记录页面跳转,入参：type:{}", type);
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ApiRPCResult<String> result = licenseApi.initLicenseAuditDetailVaild(merchant.getMerchantId(), type);
            if (result.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                log.info("效验失败内容为：{}", result.getErrMsg());
                return AjaxResult.errResult(result.getErrMsg());
            }
            return AjaxResult.successResultNotResult("校验成功");
        } catch (Exception e) {
            log.error("新增资质审核记录效验异常：", e);
            return AjaxResult.errResult(e.getMessage());
        }
    }

    /**
     * 点击添加“添加首营资质/资质变更”按钮
     *
     * @param customerType
     * @return
     */
    @Override
    public AjaxResult<Object> initLicenseAuditDetail(Integer customerType, Boolean remark, Integer invoiceType) {
        log.info("新增资质审核记录页面跳转,入参： customerType : {}", customerType);
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            LicenseAuditParams params = LicenseAuditParams.builder().customerType(customerType).merchantId(merchant.getId()).isChangeLicense(remark).build();
            Map<String, Object> modelMap = Maps.newHashMap();
            ApiRPCResult<Map<String, Object>> result = licenseApi.initLicenseAuditDetail(params);
            if (result.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                log.info("点击添加“添加首营资质/资质变更”按钮效验失败内容为：{}", result.getErrMsg());
                return AjaxResult.errResult(result.getErrMsg());
            }
            modelMap = result.getData();
            modelMap.put("imageUrl", PRODUCT_IMAGE_PATH_URL);
            if (modelMap.get("licenseAudit") == null) {
                ApiRPCResult<LicenseAuditParams> info = licenseApi.getCacheMerchantInfo(merchant.getId());
                info.getData().setInvoiceType(invoiceType);
                modelMap.put("licenseAudit", info.getData());
            }
            LicenseAuditParams licenseAuditParams = (LicenseAuditParams) modelMap.get("licenseAudit");
            licenseAuditParams.setInvoiceType(invoiceType);
            modelMap.put("licenseAudit", licenseAuditParams);
            log.info("initLicenseAuditDetail modelMap:{}", JSON.toJSONString(modelMap));
            return AjaxResult.successResult(modelMap);
        } catch (Exception e) {
            log.error("新增资质审核记录页面跳转异常：", e);
            return AjaxResult.errResult(e.getMessage());
        }
    }

    /**
     * 点击修改资质按钮之前的效验
     *
     * @param type
     * @return
     */
    @Override
    public AjaxResult<Object> initBillDetailVaild(int type, String orgCode) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ApiRPCResult<String> result = licenseApi.initBillDetailVaild(merchant.getId(), type, orgCode);
            if (result.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                return AjaxResult.errResult(result.getErrMsg());
            }
            return AjaxResult.successResultNotResult("校验成功");
        } catch (Exception e) {
            log.error("编辑资质审核记录效验异常：", e);
            return AjaxResult.errResult("系统异常");
        }
    }

    /**
     * 点击修改资质按钮
     *
     * @param type
     * @param customerType
     * @return
     */
    @Override
    public AjaxResult<Object> initBillDetail(int type, int customerType, String orgCode) {
        try {
            log.info("编辑资质审核记录页面跳转,入参：type:{},customerType:{}", type, customerType);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            LicenseAuditParams params = LicenseAuditParams.builder().customerType(customerType).merchantId(merchant.getId()).type(type).ecOrgCode(orgCode).build();
            ApiRPCResult<Map<String, Object>> result = licenseApi.initBillDetail(params);
            if (result.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                return AjaxResult.errResult(result.getErrMsg());
            }
            Map<String, Object> modelMap = result.getData();
            modelMap.put("imageUrl", PRODUCT_IMAGE_PATH_URL);
            log.info("initBillDetail modelMap:{}", JSON.toJSONString(modelMap));
            return AjaxResult.successResult(modelMap);
        } catch (Exception e) {
            log.error("编辑资质审核记录页面跳转异常：", e);
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @Override
    public AjaxResult<Object> addLicenseAudit(LicenseAuditParams licenseAudit) {
        log.info("新增资质审核记录,入参：licenseAudit:{}", JSONObject.toJSONString(licenseAudit));
        try {
            List<LicenseAuditImgParams> imgParams = licenseAudit.getCredentialList();
            for (LicenseAuditImgParams params : imgParams) {
                List<EnclosureParams> list = Lists.newArrayList();
                List<String> urls = Arrays.asList(params.getLicenseImgUrls().split(","));
                for (String s : urls) {
                    EnclosureParams e = new EnclosureParams();
                    e.setUrl(s);
                    list.add(e);
                }
                params.setEnclosureList(list);
            }
            licenseAudit.setCredentialList(imgParams);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            licenseAudit.setMerchantId(merchant.getId());
            ApiRPCResult rpcResult = licenseApi.addLicenseAudit(licenseAudit);
            log.info("接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                log.info("调用接口添加资质单据失败，药店ID：{}", licenseAudit.getMerchantId());
                return AjaxResult.errResult(rpcResult.getErrMsg());
            }
            return AjaxResult.successResultNotData();
        } catch (Exception e) {
            log.error("调用接口添加资质单据失败:", e);
            return AjaxResult.errResult("系统错误，请稍后重试");
        }
    }

    @Override
    public AjaxResult<Object> updateLicenseAudit(LicenseAuditParams licenseAudit) {
        log.info("更新资质审核记录,入参：licenseAudit:{}", JSONObject.toJSONString(licenseAudit));
        try {
            List<LicenseAuditImgParams> imgParams = licenseAudit.getCredentialList();
            for (LicenseAuditImgParams params : imgParams) {
                List<EnclosureParams> list = Lists.newArrayList();
                List<String> urls = Arrays.asList(params.getLicenseImgUrls().split(","));
                for (String s : urls) {
                    EnclosureParams e = new EnclosureParams();
                    e.setUrl(s);
                    list.add(e);
                }
                params.setEnclosureList(list);
            }
            licenseAudit.setCredentialList(imgParams);

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            licenseAudit.setMerchantId(merchant.getId());
            ApiRPCResult rpcResult = licenseApi.updateLicenseAudit(licenseAudit);
            log.info("接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                log.info("修改资质单据失败，药店ID：{}", licenseAudit.getMerchantId());
                return AjaxResult.errResult(rpcResult.getErrMsg());
            }
            return AjaxResult.successResultNotData();
        } catch (Exception e) {
            log.error("修改资质单据失败:", e);
            return AjaxResult.errResult("系统错误，请稍后重试");
        }
    }

    @Override
    public AjaxResult<Object> cacheMerchantInfo(LicenseAuditParams licenseAudit) {
        log.info("更新资质审核记录,入参：licenseAudit:{}", JSONObject.toJSONString(licenseAudit));
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            licenseAudit.setMerchantId(merchant.getId());
            ApiRPCResult rpcResult = licenseApi.cacheMerchantInfo(licenseAudit);
            log.info("接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                log.info("修改资质单据失败，药店ID：{}", licenseAudit.getMerchantId());
                return AjaxResult.errResult(rpcResult.getErrMsg());
            }
            return AjaxResult.successResultNotData();
        } catch (Exception e) {
            log.error("修改资质单据失败:", e);
            return AjaxResult.errResult("系统错误，请稍后重试");
        }
    }


}
