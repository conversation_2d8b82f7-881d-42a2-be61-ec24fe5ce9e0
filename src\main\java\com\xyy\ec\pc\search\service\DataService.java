package com.xyy.ec.pc.search.service;

import com.xyy.ec.pc.search.params.SearchCsuDataTagQueryParam;
import com.xyy.ec.pc.search.vo.PcSearchProductInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * <AUTHOR> cao
 * @version V1.0
 * @Descriotion: TODO
 * @create 2020/11/30 20:14
 */
public interface DataService {
    /**
     * 获取商品数据相关标签
     *
     * @param merchantId
     * @param branchCode
     * @param productInfoVoList
     * @param isNeedPurchaseCutPriceTag 是否展示降价提醒标签
     * @return
     */
    Map<Long, Map<String, Object>> getDataTagList(Long merchantId, String branchCode, List<PcSearchProductInfoVo> productInfoVoList, Boolean isNeedPurchaseCutPriceTag);


    /**
     * 基于原商品ID计算数据标签
     *
     * @param merchantId        用户ID
     * @param productInfoVoList 搜索结果列表
     * @param hasShopDataTags   是否需要店铺数据标签，默认-true
     * @return
     */
    Map<Long, Map<String, Object>> getSkuDataTagList(Long merchantId, List<PcSearchProductInfoVo> productInfoVoList, Boolean hasShopDataTags);

    /**
     * 获取商品数据标签
     *
     * @param queryParam
     * @return
     */
    Map<Long, Map<String, Object>> batchGetCsuDataTags(SearchCsuDataTagQueryParam queryParam);
}
