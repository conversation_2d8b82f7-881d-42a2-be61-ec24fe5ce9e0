package com.xyy.ec.pc.enums;


import com.xyy.ec.pc.util.BeanUtils;

/**
 * 状态
 */
public enum InterventionStatusEnum {
    /**
     * @see InterventionTypeEnum
     */
    ProductIssues(1,"未收货",1),
    QualificationsIssues(2,"已收货",1),
    /**
     * @see InterventionBillTypeEnum
     */
    NoReceived(3,"未收到-无票",1),
    ReceivedErrorTicket(4,"已收到-错票",1),
    NoReceivedOutboundOrder(5,"未收到-无出库单",2),
    ReceivedError(6,"已收到-出库单有误",2),
    /**
     * @see InterventionQualificationTypeEnum
     *  因上一级可以多选,未准确填写,暂且用此
     */
    NoReceivedQualifications(7,"未收到-无资质",1),
    ReceivedQualificationsError(8,"已收到-资质异常",1);


    private int id;
    private String value;
    private int parentId;


    public  int getId() {
        return id;
    }
    public  String getValue() {
        return value;
    }

    public static String get(int id) {
        for (InterventionStatusEnum c : InterventionStatusEnum.values()) {
            if (c.getId() == id) {
                return c.value;
            }
        }
        return null;
    }
    InterventionStatusEnum(int id, String value) {
        this.id = id;
        this.value = value;
    }
    InterventionStatusEnum(int id, String value, int parentId) {
        this.id = id;
        this.value = value;
        this.parentId = parentId;
    }
}
