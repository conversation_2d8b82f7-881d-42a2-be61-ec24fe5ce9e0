package com.xyy.ec.pc.search.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.pc.search.dto.SearchCsuDataTagCsuDTO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchBaseProductVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchProductVO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class SearchCsuDataTagCsuDTOHelper {

    /**
     * 创建
     *
     * @param listProduct
     * @return
     */
    public static <T extends ListProduct> SearchCsuDataTagCsuDTO create(T listProduct) {
        return SearchCsuDataTagCsuDTO.builder()
                .id(listProduct.getId())
                .pid(listProduct.getPid())
                .shopCode(listProduct.getShopCode())
                .isVirtualSupplier(listProduct.getIsVirtualSupplier())
                .build();
    }

    /**
     * 创建
     *
     * @param listProducts
     * @return
     */
    public static <T extends ListProduct> List<SearchCsuDataTagCsuDTO> creates(List<T> listProducts) {
        if (CollectionUtils.isEmpty(listProducts)) {
            return Lists.newArrayList();
        }
        return listProducts.stream().map(SearchCsuDataTagCsuDTOHelper::create).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 创建
     *
     * @param listProducts
     * @return
     */
    public static <T extends PcSearchProductVO> List<SearchCsuDataTagCsuDTO> creates2(List<T> listProducts) {
        if (CollectionUtils.isEmpty(listProducts)) {
            return Lists.newArrayList();
        }
        return listProducts.stream().map(SearchCsuDataTagCsuDTOHelper::create).filter(Objects::nonNull).collect(Collectors.toList());
    }
    public static <T extends PcSearchProductVO> SearchCsuDataTagCsuDTO create(T product) {
        return SearchCsuDataTagCsuDTO.builder()
                .id(product.getId())
                .pid(product.getPid())
                .shopCode(product.getShopCode())
                .isVirtualSupplier(product.getIsVirtualSupplier())
                .build();
    }
    /**
     * 创建
     *
     * @param product
     * @return
     */
    public static <T extends PcSearchBaseProductVO> SearchCsuDataTagCsuDTO createProductDTO(T product) {
        return SearchCsuDataTagCsuDTO.builder()
                .id(product.getId())
                .pid(product.getPid())
                .shopCode(product.getShopCode())
                .isVirtualSupplier(product.getIsVirtualSupplier())
                .build();
    }

    /**
     * 创建
     *
     * @param products
     * @return
     */
    public static <T extends PcSearchBaseProductVO> List<SearchCsuDataTagCsuDTO> createsProductDTO(List<T> products) {
        if (CollectionUtils.isEmpty(products)) {
            return Lists.newArrayList();
        }
        return products.stream().map(SearchCsuDataTagCsuDTOHelper::createProductDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
