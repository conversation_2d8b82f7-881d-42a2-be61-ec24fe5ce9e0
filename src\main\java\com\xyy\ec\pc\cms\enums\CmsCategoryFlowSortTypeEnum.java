package com.xyy.ec.pc.cms.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum CmsCategoryFlowSortTypeEnum {

    /**
     * 精选
     */
    CHOICENESS(1, "精选"),

    /**
     * 热门
     */
    HOT(2, "热门"),
    ;

    private Integer type;
    private String name;

    CmsCategoryFlowSortTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param type
     * @return
     */
    public static CmsCategoryFlowSortTypeEnum valueOfCustom(Integer type) {
        for (CmsCategoryFlowSortTypeEnum anEnum : values()) {
            if (Objects.equals(anEnum.getType(), type)) {
                return anEnum;
            }
        }
        return null;
    }

}
