package com.xyy.ec.pc.cms.service.complement.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * CMS：分页查询参数，秒杀商品ID列表
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsSeckillProductIdPagingQueryParam implements Serializable {

    /**
     * 用户ID
     */
    private Long merchantId;

    /**
     * 商品区域编码
     */
    private String productBranchCode;

    /**
     * 会员是否不能看易碎品
     */
    private Boolean merchantIsNotWatchFragileGoods;

    /**
     * 标签ID列表，商品组ID列表
     */
    private List<String> exhibitionIdStrList;

    /**
     * 当前页
     */
    private Integer pageNum;

    /**
     * 当前页的记录数
     */
    private Integer pageSize;

    /**
     * 结果集：商品ID列表集合
     */
    private List<List<Long>> csuIdsLists;

    /**
     * 结果集：商品ID列表总数
     */
    private Long csuIdsTotal;
}
