package com.xyy.ec.pc.common.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.merchant.bussiness.params.MerchantClerkLicenseAuditImageParam;
import com.xyy.ec.pc.common.params.AppMerchantClerkLicenseAuditImageParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class MerchantClerkLicenseAuditImageParamHelper {

    public static List<MerchantClerkLicenseAuditImageParam> creates(List<AppMerchantClerkLicenseAuditImageParam> appImageParams) {
        if (CollectionUtils.isEmpty(appImageParams)) {
            return Lists.newArrayList();
        }
        return appImageParams.stream().map(MerchantClerkLicenseAuditImageParamHelper::create)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static MerchantClerkLicenseAuditImageParam create(AppMerchantClerkLicenseAuditImageParam appImageParam) {
        if (Objects.isNull(appImageParam)) {
            return null;
        }
        List<String> licenseImageUrls;
        String licenseImgUrlsStr = appImageParam.getLicenseImgUrls();
        if (StringUtils.isNotEmpty(licenseImgUrlsStr)) {
            licenseImageUrls = Arrays.stream(licenseImgUrlsStr.split(","))
                    .filter(StringUtils::isNotEmpty).map(String::trim).collect(Collectors.toList());
        } else {
            licenseImageUrls = Lists.newArrayList();
        }
        return MerchantClerkLicenseAuditImageParam.builder()
                .name(appImageParam.getName()).licenseCode(appImageParam.getLicenseCode())
                .licenseImageUrls(licenseImageUrls).build();
    }
}
