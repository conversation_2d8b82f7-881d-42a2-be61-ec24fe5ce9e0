package com.xyy.ec.pc.service.marketing.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.ecp.api.ProductExhibitionGroupBusinessForMarketingApi;
import com.xyy.ec.marketing.client.dto.ActInfoDTO;
import com.xyy.ec.marketing.client.dto.SuiXinPinSkuDTO;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.ActCardInfoParamDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingRecordDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingSkuDto;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateConsumptionReturnAmountDTO;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateConsumptionReturnResult;
import com.xyy.ec.order.business.dto.ecp.orderforpromotion.PromotionMsg;
import com.xyy.ec.order.business.dto.ecp.orderforpromotion.PromotionRecord;
import com.xyy.ec.order.business.enums.promo.OrderPromoTypeEnum;
import com.xyy.ec.order.search.api.remote.result.OrderSkuStatisticsResultDto;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.controller.vo.ConsumeRebateDetailVo;
import com.xyy.ec.pc.enums.marketing.MarketingSeckillActivityStatusEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.remote.OrderSkuStatisticsRemoteService;
import com.xyy.ec.pc.remote.ProductExhibitionGroupBusinessAdminRemoteService;
import com.xyy.ec.pc.remote.ProductForPromotionRemoteService;
import com.xyy.ec.pc.rpc.HyperSpaceRpc;
import com.xyy.ec.pc.rpc.OrderServerRpcService;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.marketing.dto.MarketingCsuKeyDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingCsuResultDto;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.helpers.MarketingSeckillActivityInfoDTOHelper;
import com.xyy.ec.product.business.ecp.out.promotion.dto.SkuSimpleDTO;
import com.xyy.ec.search.engine.api.EcSearchApi;
import com.xyy.ec.search.engine.dto.SearchCsuDTO;
import com.xyy.ec.search.engine.entity.CsuInfo;
import com.xyy.ec.search.engine.enums.CsuOrder;
import com.xyy.ec.search.engine.enums.EcRankType;
import com.xyy.ec.search.engine.enums.SortOrder;
import com.xyy.ec.search.engine.metadata.IPage;
import com.xyy.ec.search.engine.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
public class MarketingServiceImpl implements MarketingService {

    @Autowired
    private HyperSpaceRpc hyperSpaceRpc;
    @Autowired
    private OrderServerRpcService orderServerRpcService;
    @Reference(version = "1.0.0")
    private EcSearchApi ecSearchApi;
    @Reference(version = "1.0.0")
    private ProductExhibitionGroupBusinessForMarketingApi productExhibitionGroupBusinessForMarketingApi;

    @Autowired
    private ProductExhibitionGroupBusinessAdminRemoteService productExhibitionGroupBusinessAdminRemoteService;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private OrderSkuStatisticsRemoteService orderSkuStatisticsRemoteService;

    @Autowired
    private ProductForPromotionRemoteService productForPromotionRemoteService;

    @Autowired
    private MarketingService marketingService;

    @Override
    public Map<Long, MarketingSeckillActivityInfoDTO> getShowingSeckillActivityInfoByCsuIds(Long merchantId, List<Long> csuIds) {
        if (Objects.isNull(merchantId) || merchantId <= 0L || CollectionUtils.isEmpty(csuIds)) {
            return Maps.newHashMap();
        }
        Map<Long, ActInfoDTO> startingCsuIdToActInfoMap = hyperSpaceRpc.batchGetCsuJoiningActInfo(merchantId, MarketingEnum.MIAO_SHA, csuIds);
        if (log.isDebugEnabled()) {
            log.debug("根据商品ID获取展示中的秒杀活动信息（通用），获取进行中，merchantId：{}，csuIds：{}，响应：{}",
                    merchantId, JSONArray.toJSONString(csuIds), JSONObject.toJSONString(startingCsuIdToActInfoMap));
        }
        Set<Long> startingCsuIdsSet = Sets.newHashSetWithExpectedSize(16);
        if (MapUtils.isNotEmpty(startingCsuIdToActInfoMap)) {
            Set<Long> tempCsuIdsSet = startingCsuIdToActInfoMap.keySet();
            startingCsuIdsSet.addAll(tempCsuIdsSet);
        }
        // 尝试获取未开始
        List<Long> notStartingCsuIds = Lists.newArrayListWithExpectedSize(16);
        if (CollectionUtils.isNotEmpty(startingCsuIdsSet)) {
            List<Long> tempCsuIds = Lists.newArrayList(csuIds);
            tempCsuIds.removeAll(startingCsuIdsSet);
            notStartingCsuIds.addAll(tempCsuIds);
        } else {
            notStartingCsuIds.addAll(csuIds);
        }
        ActInfoDTO actInfoDTO;
        Map<Long, ActInfoDTO> unStartingCsuIdToActInfoMap = Maps.newHashMapWithExpectedSize(notStartingCsuIds.size());
        if (CollectionUtils.isNotEmpty(notStartingCsuIds)) {
            Map<Long, List<ActInfoDTO>> unStartingCsuIdToActInfosMap = hyperSpaceRpc.batchGetCsuPreheatingActInfos(merchantId, MarketingEnum.MIAO_SHA, notStartingCsuIds);
            if (log.isDebugEnabled()) {
                log.debug("根据商品ID获取展示中的秒杀活动信息（通用），获取预热中，merchantId：{}，notStartingCsuIds：{}，响应：{}",
                        merchantId, JSONArray.toJSONString(notStartingCsuIds), JSONObject.toJSONString(unStartingCsuIdToActInfosMap));
            }
            for (Map.Entry<Long, List<ActInfoDTO>> entry : unStartingCsuIdToActInfosMap.entrySet()) {
                Long csuId = entry.getKey();
                List<ActInfoDTO> actInfoDTOS = entry.getValue();
                if (Objects.nonNull(csuId) && CollectionUtils.isNotEmpty(actInfoDTOS)) {
                    // 取出最近的，排序：同状态中按活动开始时间升序、活动ID降序排序。
                    actInfoDTOS = actInfoDTOS.stream().sorted(Comparator.comparing(ActInfoDTO::getStime)
                            .thenComparing((a, b) -> b.getMarketingId().compareTo(a.getMarketingId())))
                            .collect(Collectors.toList());
                    actInfoDTO = actInfoDTOS.get(0);
                    unStartingCsuIdToActInfoMap.putIfAbsent(csuId, actInfoDTO);
                }
            }
        }
        Map<Long, MarketingSeckillActivityInfoDTO> result = Maps.newHashMapWithExpectedSize(csuIds.size());
        // 转换
        Long csuId;
        for (Map.Entry<Long, ActInfoDTO> entry : startingCsuIdToActInfoMap.entrySet()) {
            csuId = entry.getKey();
            actInfoDTO = entry.getValue();
            if (Objects.nonNull(csuId) && Objects.nonNull(actInfoDTO)) {
                MarketingSeckillActivityInfoDTO marketingSeckillActivityInfoDTO = MarketingSeckillActivityInfoDTOHelper.convert(csuId, actInfoDTO);
                if (Objects.nonNull(marketingSeckillActivityInfoDTO)) {
                    result.putIfAbsent(csuId, marketingSeckillActivityInfoDTO);
                }
            }
        }
        for (Map.Entry<Long, ActInfoDTO> entry : unStartingCsuIdToActInfoMap.entrySet()) {
            csuId = entry.getKey();
            actInfoDTO = entry.getValue();
            if (Objects.nonNull(csuId) && Objects.nonNull(actInfoDTO)) {
                MarketingSeckillActivityInfoDTO marketingSeckillActivityInfoDTO = MarketingSeckillActivityInfoDTOHelper.convert(csuId, actInfoDTO);
                if (Objects.nonNull(marketingSeckillActivityInfoDTO)) {
                    result.putIfAbsent(csuId, marketingSeckillActivityInfoDTO);
                }
            }
        }
//        // 筛出进行中的秒杀活动，填充已售数
//        result = this.fillSeckillActivityCsuSoldInfo(result);
        if (log.isDebugEnabled()) {
            log.debug("根据商品ID获取展示中的秒杀活动信息（通用），最终响应，merchantId：{}，csuIds：{}，响应：{}",
                    merchantId, JSONArray.toJSONString(csuIds), JSONObject.toJSONString(result));
        }
        return result;
    }

    @Override
    public MarketingSeckillActivityInfoDTO getShowingSeckillActivityInfoByCsuId(Long merchantId, Long csuId) {
        if (Objects.isNull(merchantId) || merchantId <= 0L || Objects.isNull(csuId)) {
            return null;
        }
        Map<Long, MarketingSeckillActivityInfoDTO> csuIdToSeckillActivityInfoMap = this.getShowingSeckillActivityInfoByCsuIds(merchantId, Lists.newArrayList(csuId));
        return csuIdToSeckillActivityInfoMap.get(csuId);
    }

    @Override
    public Map<Long, MarketingSeckillActivityInfoDTO> getShowingSeckillActivityInfoByCsuIds(Long merchantId, List<Long> csuIds,
                                                                                            List<Integer> statusList) {
        if (Objects.isNull(merchantId) || merchantId <= 0L || CollectionUtils.isEmpty(csuIds) || CollectionUtils.isEmpty(statusList)) {
            return Maps.newHashMap();
        }
        Set<Integer> statusSet = Sets.newHashSet(statusList);
        statusSet.remove(null);
        if (CollectionUtils.isEmpty(statusSet)) {
            return Maps.newHashMap();
        }
        for (Integer status : statusSet) {
            if (!Objects.equals(status, MarketingQueryStatusEnum.UN_START.getType())
                    && !Objects.equals(status, MarketingQueryStatusEnum.STARTING.getType())) {
                return Maps.newHashMap();
            }
        }
        boolean isContainPreheating = statusSet.contains(MarketingQueryStatusEnum.UN_START.getType());
        // 不是进行中的商品ID列表
        List<Long> notStartingCsuIds = Lists.newArrayListWithExpectedSize(16);
        List<Long> tempNotStartingCsuIds;
        // 分组
        List<List<Long>> skuIdsLists = Lists.partition(csuIds, 20);
        // 进行中
        Map<Long, MarketingSeckillActivityInfoDTO> startingCsuIdToGroupBuyingInfoMap = Maps.newHashMapWithExpectedSize(csuIds.size());
        for (List<Long> skuIdsList : skuIdsLists) {
            Map<Long, MarketingSeckillActivityInfoDTO> tempStartingCsuIdToGroupBuyingInfoMap = this.getShowingSeckillActivityInfoByCsuIds(merchantId,
                    skuIdsList, MarketingQueryStatusEnum.STARTING.getType());
            if (MapUtils.isNotEmpty(tempStartingCsuIdToGroupBuyingInfoMap)) {
                startingCsuIdToGroupBuyingInfoMap.putAll(tempStartingCsuIdToGroupBuyingInfoMap);
                if (isContainPreheating) {
                    // 包含预热，则筛出未参加进行中活动的商品ID。
                    tempNotStartingCsuIds = skuIdsList.stream()
                            .filter(item -> Objects.nonNull(item) && Objects.isNull(tempStartingCsuIdToGroupBuyingInfoMap.get(item)))
                            .collect(Collectors.toList());
                    notStartingCsuIds.addAll(tempNotStartingCsuIds);
                }
            } else {
                if (isContainPreheating) {
                    // 包含预热，则筛出未参加进行中活动的商品ID。
                    notStartingCsuIds.addAll(skuIdsList);
                }
            }
        }
        // 预热中且不是未开始
        Map<Long, MarketingSeckillActivityInfoDTO> preheatingCsuIdToGroupBuyingInfoMap = Maps.newHashMapWithExpectedSize(notStartingCsuIds.size());
        if (isContainPreheating && CollectionUtils.isNotEmpty(notStartingCsuIds)) {
            List<List<Long>> notStartingCsuIdsLists = Lists.partition(notStartingCsuIds, 20);
            Map<Long, MarketingSeckillActivityInfoDTO> tempPreheatingCsuIdToGroupBuyingInfoMap;
            for (List<Long> skuIdsList : notStartingCsuIdsLists) {
                tempPreheatingCsuIdToGroupBuyingInfoMap = this.getShowingSeckillActivityInfoByCsuIds(merchantId,
                        skuIdsList, MarketingQueryStatusEnum.UN_START.getType());
                if (MapUtils.isNotEmpty(tempPreheatingCsuIdToGroupBuyingInfoMap)) {
                    preheatingCsuIdToGroupBuyingInfoMap.putAll(tempPreheatingCsuIdToGroupBuyingInfoMap);
                }
            }
        }
        // 返回结果
        Map<Long, MarketingSeckillActivityInfoDTO> result = Maps.newHashMapWithExpectedSize(csuIds.size());
        if (statusSet.contains(MarketingQueryStatusEnum.STARTING.getType())) {
            // 进行中
            result.putAll(startingCsuIdToGroupBuyingInfoMap);
        }
        if (statusSet.contains(MarketingQueryStatusEnum.UN_START.getType())) {
            // 预热中
            result.putAll(preheatingCsuIdToGroupBuyingInfoMap);
        }
        return result;
    }

    @Override
    public Map<Long, MarketingSeckillActivityInfoDTO> getShowingSeckillActivityInfoByCsuIds(Long merchantId, List<Long> csuIds, Integer status) {
        if (Objects.isNull(merchantId) || merchantId <= 0L || CollectionUtils.isEmpty(csuIds)) {
            return Maps.newHashMap();
        }
        Map<Long, List<ActInfoDTO>> startingCsuIdToActInfoMap = hyperSpaceRpc.batchGetCsuSomeStatusActInfos(merchantId, MarketingEnum.MIAO_SHA, status, csuIds);
        if (log.isDebugEnabled()) {
            log.debug("根据商品ID获取展示中的某个状态的秒杀活动信息，merchantId：{}，status：{}，csuIds：{}，响应：{}",
                    merchantId, status, JSONArray.toJSONString(csuIds), JSONObject.toJSONString(startingCsuIdToActInfoMap));
        }
        Map<Long, MarketingSeckillActivityInfoDTO> result = Maps.newHashMapWithExpectedSize(csuIds.size());
        // 转换
        Long csuId;
        List<ActInfoDTO> actInfoDTOS;
        ActInfoDTO actInfoDTO;
        for (Map.Entry<Long, List<ActInfoDTO>> entry : startingCsuIdToActInfoMap.entrySet()) {
            csuId = entry.getKey();
            actInfoDTOS = entry.getValue();
            if (Objects.nonNull(csuId) && CollectionUtils.isNotEmpty(actInfoDTOS)) {
                // 排序：同状态中按活动开始时间升序、活动ID降序排序
                actInfoDTOS = actInfoDTOS.stream().sorted(Comparator.comparing(ActInfoDTO::getStime)
                        .thenComparing((a, b) -> b.getMarketingId().compareTo(a.getMarketingId())))
                        .collect(Collectors.toList());
                actInfoDTO = actInfoDTOS.get(0);
                MarketingSeckillActivityInfoDTO marketingSeckillActivityInfoDTO = MarketingSeckillActivityInfoDTOHelper.convert(csuId, actInfoDTO);
                if (Objects.nonNull(marketingSeckillActivityInfoDTO)) {
                    result.putIfAbsent(csuId, marketingSeckillActivityInfoDTO);
                }
            }
        }
//        // 筛出进行中的秒杀活动，填充已售数
//        result = this.fillSeckillActivityCsuSoldInfo(result);
        if (log.isDebugEnabled()) {
            log.debug("根据商品ID获取展示中的某个状态的秒杀活动信息，最终响应，merchantId：{}，status：{}，csuIds：{}，响应：{}",
                    merchantId, status, JSONArray.toJSONString(csuIds), JSONObject.toJSONString(result));
        }
        return result;
    }

    @Override
    public Map<Long, MarketingSeckillActivityInfoDTO> getShowingSeckillActivityInfoByCsuIdsForSearch(Long merchantId, List<Long> csuIds) {
        if (Objects.isNull(merchantId) || merchantId <= 0L || CollectionUtils.isEmpty(csuIds)) {
            return Maps.newHashMap();
        }
        Map<Long, MarketingSeckillActivityInfoDTO> result = this.getShowingSeckillActivityInfoByCsuIds(merchantId, csuIds);
        if (log.isDebugEnabled()) {
            log.debug("根据商品ID获取展示中的秒杀活动信息（搜索专用），最终响应，merchantId：{}，csuIds：{}，响应：{}",
                    merchantId, JSONArray.toJSONString(csuIds), JSONObject.toJSONString(result));
        }
        return result;
    }

    @Override
    public Map<Long, SuiXinPinSkuDTO> getSuiXinPinSkuDiscountBySkuIds(Long merchantId, List<Long> skuIdList) {
        if (CollectionUtils.isEmpty(skuIdList)){
            return Maps.newHashMap();
        }
        List<SuiXinPinSkuDTO> suiXinPinSkuDtos = hyperSpaceRpc.getSuiXinPinSkuDiscountBySkuIds(merchantId, skuIdList);
        if (CollectionUtils.isEmpty(suiXinPinSkuDtos)){
            return Maps.newHashMap();
        }
        Map<Long, SuiXinPinSkuDTO> result = Maps.newHashMapWithExpectedSize(suiXinPinSkuDtos.size());
        suiXinPinSkuDtos.forEach(suiXinPinSkuDiscountDTO -> result.put(suiXinPinSkuDiscountDTO.getSkuId(), suiXinPinSkuDiscountDTO));
        return result;
    }

    /**
     * 列表转map
     * @param groupBuyingList
     * @return
     */
    private Map<Long, GroupBuyingInfoDto> actListToMap(List<GroupBuyingInfoDto> groupBuyingList){
        Map<Long, GroupBuyingInfoDto> retMap = new HashMap<>();
        if(CollectionUtils.isEmpty(groupBuyingList)){
            return retMap;
        }
        for(GroupBuyingInfoDto act:groupBuyingList){
            List<GroupBuyingSkuDto> groupBuyingSkuDtoList = act.getGroupBuyingSkuDtoList();
            Long skuId = groupBuyingSkuDtoList.get(0).getSkuId();
            retMap.put(skuId.longValue(), act);
        }
        return retMap;
    }

    /**
     * 添加订单成团信息(从hyperspace迁移出来的)
     * @param list
     * @return
     */
    private List<GroupBuyingInfoDto> addOrderInfo(List<GroupBuyingInfoDto> list){
        //cms也需要成团数
        if(CollectionUtils.isNotEmpty(list)){
            //拼团订单数据
            Map<String, PromotionMsg> orderInfoMap = getOrderBuyingMap(list);
            for(GroupBuyingInfoDto dto:list){
                List<GroupBuyingSkuDto> groupBuyingSkuDtoList = dto.getGroupBuyingSkuDtoList();
                Long skuNum = 0L;
                for(GroupBuyingSkuDto sku:groupBuyingSkuDtoList){
                    //拼团信息(订单提供的接口一个sku查一次)
                    PromotionMsg pm =orderInfoMap.get(dto.getMarketingId() + "-" + sku.getSkuId());
                    if(null == pm){
                        continue;
                    }
                    if (Objects.isNull(pm.getMerchantCount())) {
                        pm.setMerchantCount(0L);
                    }
                    sku.setMerchantCount(pm.getMerchantCount());
                    sku.setProductCount(pm.getProductCount());
                    //转成营销dto
                    List<GroupBuyingRecordDto> recordList = buildBuyingRecordDtoList(pm.getPromotionRecords());
                    sku.setRecordList(recordList);
                    skuNum = skuNum + pm.getProductCount();
                }

                Long mCount = groupBuyingSkuDtoList.get(0).getMerchantCount();
                //人数>3 则已售数量 = 原已售数量 + （mock人数*起拼数）
                if(Objects.nonNull(mCount) && mCount >3 && null != dto.getMockNum() && dto.getMockNum() >0){
                    Long orderNum = skuNum + dto.getMockNum() * groupBuyingSkuDtoList.get(0).getSkuStartNum();
                    dto.setOrderNum(orderNum);
                }else{
                    dto.setOrderNum(skuNum);
                }
            }
        }
        return list;
    }

    /**
     * 添加订单成团信息
     *
     * @param groupBuyingInfoDtos
     * @return
     */
    private List<GroupBuyingInfoDto> addOrderInfoV2(List<GroupBuyingInfoDto> groupBuyingInfoDtos, Set<Long> gaoMaoSkuIdSet) {
        if (CollectionUtils.isEmpty(groupBuyingInfoDtos)) {
            return groupBuyingInfoDtos;
        }
        if (BooleanUtils.isNotTrue(appProperties.getIsApplyGaoMaoGroupBuyingSaleLogic())) {
            return this.addOrderInfo(groupBuyingInfoDtos);
        }
        /*
         * 是高毛拼团且有主标准库ID的走高毛拼团逻辑；其他的走非高毛拼团逻辑。
         */
        // 高毛拼团的商品ID列表
        List<Long> highGrossGroupBuyingCsuIds = groupBuyingInfoDtos.stream()
                .map(item -> {
                    // 忽略掉不合法的数据
                    if (Objects.isNull(item) || CollectionUtils.isEmpty(item.getGroupBuyingSkuDtoList())) {
                        return null;
                    }
                    GroupBuyingSkuDto groupBuyingSkuDto = item.getGroupBuyingSkuDtoList().get(0);
                    if (Objects.isNull(groupBuyingSkuDto)) {
                        return null;
                    }
                    if(BooleanUtils.isTrue(appProperties.getIsApplyGaoMaoGroupBuyingSaleSummary()) && null != gaoMaoSkuIdSet && gaoMaoSkuIdSet.contains(groupBuyingSkuDto.getSkuId())){
                        return groupBuyingSkuDto.getSkuId();
                    }
                    // 忽略掉非高毛拼团
                    if (BooleanUtils.isNotTrue(item.getIsHighGross())) {
                        return null;
                    }
                    return groupBuyingSkuDto.getSkuId();
                })
                .filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        // 获取映射关系：高毛拼团商品ID - 主标准库
        Map<Long, String> highGrossGroupBuyingCsuIdToMasterStandardIdMap = this.listCsuMasterStandardIds(highGrossGroupBuyingCsuIds);
        // 应用高毛拼团逻辑的商品ID
        Set<Long> highGrossGroupBuyingLogicCsuIdSet = highGrossGroupBuyingCsuIds.stream()
                .filter(item -> StringUtils.isNotEmpty(highGrossGroupBuyingCsuIdToMasterStandardIdMap.get(item)))
                .collect(Collectors.toSet());
        // 筛选出走非高毛拼团逻辑的拼团
        List<GroupBuyingInfoDto> commonLogicGroupBuyingInfoDtos = Lists.newArrayListWithExpectedSize(16);
        for (GroupBuyingInfoDto groupBuyingInfoDto : groupBuyingInfoDtos) {
            // 忽略掉不合法的数据
            if (Objects.isNull(groupBuyingInfoDto) || CollectionUtils.isEmpty(groupBuyingInfoDto.getGroupBuyingSkuDtoList())) {
                continue;
            }
            GroupBuyingSkuDto groupBuyingSkuDto = groupBuyingInfoDto.getGroupBuyingSkuDtoList().get(0);
            if (Objects.isNull(groupBuyingSkuDto)) {
                continue;
            }
            if (!highGrossGroupBuyingLogicCsuIdSet.contains(groupBuyingSkuDto.getSkuId())) {
                commonLogicGroupBuyingInfoDtos.add(groupBuyingInfoDto);
            }
        }
        if (CollectionUtils.isNotEmpty(commonLogicGroupBuyingInfoDtos)) {
            this.addOrderInfo(groupBuyingInfoDtos);
        }
        if (CollectionUtils.isNotEmpty(highGrossGroupBuyingLogicCsuIdSet)) {
            List<String> csuMasterStandardIds = highGrossGroupBuyingLogicCsuIdSet.stream()
                    .map(highGrossGroupBuyingCsuIdToMasterStandardIdMap::get)
                    .collect(Collectors.toList());
            // 获取映射关系：主标准库 - 销售信息
            Map<String, OrderSkuStatisticsResultDto> csuMasterStandardIdToSaleInfoMap = orderSkuStatisticsRemoteService
                    .mgetCsuMasterStandardIdSaleInfo(csuMasterStandardIds);
            for (GroupBuyingInfoDto groupBuyingInfoDto : groupBuyingInfoDtos) {
                // 忽略掉不合法的数据
                if (Objects.isNull(groupBuyingInfoDto) || CollectionUtils.isEmpty(groupBuyingInfoDto.getGroupBuyingSkuDtoList())) {
                    continue;
                }
                GroupBuyingSkuDto groupBuyingSkuDto = groupBuyingInfoDto.getGroupBuyingSkuDtoList().get(0);
                if (Objects.isNull(groupBuyingSkuDto)) {
                    continue;
                }
                Long skuId = groupBuyingSkuDto.getSkuId();
                // 跳过走非高毛拼团逻辑的拼团活动
                if (!highGrossGroupBuyingLogicCsuIdSet.contains(skuId)) {
                    continue;
                }
                String masterStandardId = highGrossGroupBuyingCsuIdToMasterStandardIdMap.get(skuId);
                OrderSkuStatisticsResultDto saleInfo = csuMasterStandardIdToSaleInfoMap.get(masterStandardId);
                Integer productCount = null;
                Integer merchantCount = null;
                if (Objects.nonNull(saleInfo)) {
                    productCount = saleInfo.getSalesVolume();
                    merchantCount = saleInfo.getMerchantCount();
                }
                if (Objects.isNull(productCount) || productCount < 0) {
                    productCount = 0;
                }
                if (Objects.isNull(merchantCount) || merchantCount < 0) {
                    merchantCount = 0;
                }
                groupBuyingSkuDto.setProductCount(productCount.longValue());
                groupBuyingSkuDto.setMerchantCount(merchantCount.longValue());
                // 不采用mock计算逻辑，即已拼数=商品售卖数
                groupBuyingInfoDto.setOrderNum(groupBuyingSkuDto.getProductCount());
                groupBuyingSkuDto.setRecordList(Lists.newArrayList());
            }
        }
        return groupBuyingInfoDtos;
    }

    /**
     * 查询商品的主标准库ID
     *
     * @param csuIds
     * @return
     */
    private Map<Long, String> listCsuMasterStandardIds(List<Long> csuIds) {
        if (CollectionUtils.isEmpty(csuIds)) {
            return Maps.newHashMap();
        }
        try {
            List<SkuSimpleDTO> skuSimpleDTOList = productForPromotionRemoteService.findProductSimpleInfoBySkuIdList(csuIds);
            if (CollectionUtils.isEmpty(skuSimpleDTOList)) {
                return Maps.newHashMap();
            }
            return skuSimpleDTOList.stream()
                    .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getSkuId())
                            && StringUtils.isNotEmpty(item.getMasterStandardProductId()))
                    .collect(Collectors.toMap(SkuSimpleDTO::getSkuId, SkuSimpleDTO::getMasterStandardProductId, (f, s) -> f));
        } catch (Exception e) {
            log.error("查询商品的主标准库ID异常，入参：{}", JSONArray.toJSONString(csuIds), e);
            return Maps.newHashMap();
        }
    }

    /**
     * 添加订单信息
     *
     * @param groupBuyingInfoDtos
     * @return
     */
    private List<GroupBuyingInfoDto> addOrderInfoV3(List<GroupBuyingInfoDto> groupBuyingInfoDtos, Set<Long> gaoMaoSkuIdSet) {
        if (CollectionUtils.isEmpty(groupBuyingInfoDtos)) {
            return groupBuyingInfoDtos;
        }
        // 添加拼团商品成团信息
        List<GroupBuyingInfoDto> supportGroupBuyingInfoDtos = groupBuyingInfoDtos.stream()
                .filter(groupBuyingInfoDto -> Objects.equals(MarketingEnum.PING_TUAN.getCode(), groupBuyingInfoDto.getActivityType()))
                .collect(Collectors.toList());
        this.addOrderInfoV2(supportGroupBuyingInfoDtos,  gaoMaoSkuIdSet);
        return groupBuyingInfoDtos;
    }

    /**
     * 填充秒杀活动品的售卖数及百分比
     *
     * @param csuIdToSeckillActivityInfoMap
     * @return
     */
    private Map<Long, MarketingSeckillActivityInfoDTO> fillSeckillActivityCsuSoldInfo(Map<Long, MarketingSeckillActivityInfoDTO> csuIdToSeckillActivityInfoMap) {
        if (MapUtils.isEmpty(csuIdToSeckillActivityInfoMap)) {
            return csuIdToSeckillActivityInfoMap;
        }
        // 筛出进行中的秒杀活动，填充已售数
        Collection<MarketingSeckillActivityInfoDTO> marketingSeckillActivityInfoDTOS = csuIdToSeckillActivityInfoMap.values();
        List<MarketingSeckillActivityInfoDTO> inProgressSeckillActivityInfos = this.listInProgressSeckillActivityInfos(marketingSeckillActivityInfoDTOS);
        if (CollectionUtils.isEmpty(inProgressSeckillActivityInfos)) {
            return csuIdToSeckillActivityInfoMap;
        }
        // 查询秒杀品售卖数
        List<MarketingCsuKeyDTO> marketingCsuKeyDTOS = this.convertSeckillActivityToMarketingCsuKeys(inProgressSeckillActivityInfos);
        Map<MarketingCsuKeyDTO, Long> marketingCsuSoldNumMap = this.getSeckillActivityCsuSoldNumMap(marketingCsuKeyDTOS);
        if (MapUtils.isEmpty(marketingCsuSoldNumMap)) {
            return csuIdToSeckillActivityInfoMap;
        }
        Long soldNum;
        Integer percentage;
        Long csuId;
        MarketingCsuKeyDTO marketingCsuKeyDTO;
        MarketingSeckillActivityInfoDTO marketingSeckillActivityInfoDTO;
        for (MarketingSeckillActivityInfoDTO inProgressSeckillActivityInfo : inProgressSeckillActivityInfos) {
            csuId = inProgressSeckillActivityInfo.getCsuId();
            marketingCsuKeyDTO = MarketingCsuKeyDTO.builder().activityType(inProgressSeckillActivityInfo.getActivityType())
                    .marketingId(inProgressSeckillActivityInfo.getMarketingId()).csuId(csuId).build();
            soldNum = marketingCsuSoldNumMap.get(marketingCsuKeyDTO);
            marketingSeckillActivityInfoDTO = csuIdToSeckillActivityInfoMap.get(csuId);
            if (Objects.isNull(marketingSeckillActivityInfoDTO)) {
                continue;
            }
            if (Objects.nonNull(soldNum)) {
                marketingSeckillActivityInfoDTO.setPayQty(soldNum.intValue());
            }
            // 百分比
            percentage = this.calculateSeckillActivityCsuSoldPercentage(marketingSeckillActivityInfoDTO.getTotalQty(),
                    marketingSeckillActivityInfoDTO.getPayQty());
            marketingSeckillActivityInfoDTO.setPercentage(percentage);
        }
        return csuIdToSeckillActivityInfoMap;
    }

    /**
     * <pre>
     * 计算秒杀商品售卖百分比。
     *  1.若已售百分比在区间[0, 0.3)中，已售卖百分比 = 实际百分比 + 0.2
     *  2.若已售百分比在区间[0.3, 0.5)中，已售卖百分比 = 0.5
     *  3.若已售百分比在区间[0.5, ＋∞)中，已售卖百分比 = 实际百分比
     * </pre>
     *
     * @param totalQty
     * @param payQty
     * @return
     */
    private Integer calculateSeckillActivityCsuSoldPercentage(Integer totalQty, Integer payQty) {
        totalQty = Optional.ofNullable(totalQty).orElse(0);
        payQty = Optional.ofNullable(payQty).orElse(0);
        BigDecimal inProgressPayRadioBigDecimal = BigDecimal.ZERO;
        BigDecimal payQtyBigDecimal = BigDecimal.valueOf(payQty);
        BigDecimal totalQtyBigDecimal = BigDecimal.valueOf(totalQty);
        // 计算已售百分比：小数尾数归零
        if (totalQtyBigDecimal.compareTo(BigDecimal.ZERO) > 0) {
            inProgressPayRadioBigDecimal = payQtyBigDecimal.divide(totalQtyBigDecimal, 2, RoundingMode.DOWN);
        }
        /*
            1.若已售百分比在区间[0, 0.3)中，已售卖百分比 = 实际百分比 + 0.2
            2.若已售百分比在区间[0.3, 0.5)中，已售卖百分比 = 0.5
            3.若已售百分比在区间[0.5, ＋∞)中，已售卖百分比 = 实际百分比
         */
        if (inProgressPayRadioBigDecimal.compareTo(new BigDecimal("0.3")) < 0) {
            inProgressPayRadioBigDecimal = inProgressPayRadioBigDecimal.add(new BigDecimal("0.2"));
        } else if (inProgressPayRadioBigDecimal.compareTo(new BigDecimal("0.5")) < 0) {
            inProgressPayRadioBigDecimal = new BigDecimal("0.5");
        }
        // 变换为百分比形式
        inProgressPayRadioBigDecimal = inProgressPayRadioBigDecimal.multiply(new BigDecimal("100"));
        inProgressPayRadioBigDecimal = inProgressPayRadioBigDecimal.setScale(0, RoundingMode.DOWN);
        return inProgressPayRadioBigDecimal.intValue();
    }

    /**
     * 获取进行中的秒杀商品Key列表
     *
     * @param marketingSeckillActivityInfoDTOS
     * @return
     */
    private List<MarketingSeckillActivityInfoDTO> listInProgressSeckillActivityInfos(Collection<MarketingSeckillActivityInfoDTO> marketingSeckillActivityInfoDTOS) {
        if (CollectionUtils.isEmpty(marketingSeckillActivityInfoDTOS)) {
            return Lists.newArrayList();
        }
        return marketingSeckillActivityInfoDTOS.stream()
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getActivityType())
                        && Objects.nonNull(item.getMarketingId()) && Objects.nonNull(item.getCsuId())
                        && Objects.equals(item.getStatus(), MarketingSeckillActivityStatusEnum.IN_PROGRESS.getStatus()))
                .collect(Collectors.toList());
    }

    /**
     * 将秒杀活动转换为营销商品Key列表
     *
     * @param marketingSeckillActivityInfoDTOS
     * @return
     */
    private List<MarketingCsuKeyDTO> convertSeckillActivityToMarketingCsuKeys(Collection<MarketingSeckillActivityInfoDTO> marketingSeckillActivityInfoDTOS) {
        if (CollectionUtils.isEmpty(marketingSeckillActivityInfoDTOS)) {
            return Lists.newArrayList();
        }
        return marketingSeckillActivityInfoDTOS.stream()
                .map(item -> {
                    MarketingCsuKeyDTO csuKeyDTO = new MarketingCsuKeyDTO();
                    csuKeyDTO.setActivityType(item.getActivityType());
                    csuKeyDTO.setMarketingId(item.getMarketingId());
                    csuKeyDTO.setCsuId(item.getCsuId());
                    return csuKeyDTO;
                }).collect(Collectors.toList());
    }

    /**
     * 批量查询活动品的已售数
     *
     * @param csuKeyDTOList
     * @return
     */
    private Map<MarketingCsuKeyDTO, Long> getSeckillActivityCsuSoldNumMap(List<MarketingCsuKeyDTO> csuKeyDTOList) {
        if (CollectionUtils.isEmpty(csuKeyDTOList)) {
            return Maps.newHashMap();
        }
        Map<MarketingCsuKeyDTO, Long> csuKeyToSoldNumMap = Maps.newHashMapWithExpectedSize(csuKeyDTOList.size());
        List<List<MarketingCsuKeyDTO>> csuKeyLists = Lists.partition(csuKeyDTOList, 200);
        PromotionMsg paramPromotionMsg;
        List<PromotionMsg> promotionMsgs;
        List<PromotionMsg> resultPromotionMsgs;
        MarketingCsuKeyDTO tempCsuKey;
        Long soldNum;
        for (List<MarketingCsuKeyDTO> csuKeyList : csuKeyLists) {
            promotionMsgs = Lists.newArrayListWithCapacity(csuKeyList.size());
            for (MarketingCsuKeyDTO csuKey : csuKeyList) {
                paramPromotionMsg = new PromotionMsg();
                // 入参是订单枚举的秒杀
                paramPromotionMsg.setActivityType(OrderPromoTypeEnum.SEC_KILL.getValue());
                paramPromotionMsg.setPromoId(csuKey.getMarketingId());
                paramPromotionMsg.setSkuId(csuKey.getCsuId());
                promotionMsgs.add(paramPromotionMsg);
            }
            resultPromotionMsgs = orderServerRpcService.queryPromotionListMsg(promotionMsgs);
            if (log.isDebugEnabled()) {
                log.debug("批量查询秒杀活动品的已售数，入参：{}，出参：{}", JSONArray.toJSONString(promotionMsgs),
                        JSONArray.toJSONString(resultPromotionMsgs));
            }
            if (CollectionUtils.isEmpty(resultPromotionMsgs)) {
                continue;
            }
            for (PromotionMsg resultPromotionMsg : resultPromotionMsgs) {
                tempCsuKey = new MarketingCsuKeyDTO();
                // 出参统一是营销活动枚举的秒杀
                tempCsuKey.setActivityType(MarketingEnum.MIAO_SHA.getCode());
                tempCsuKey.setMarketingId(resultPromotionMsg.getPromoId());
                tempCsuKey.setCsuId(resultPromotionMsg.getSkuId());
                soldNum = resultPromotionMsg.getProductCount();
                if (Objects.isNull(tempCsuKey) || Objects.isNull(soldNum)) {
                    continue;
                }
                csuKeyToSoldNumMap.put(tempCsuKey, soldNum);
            }
        }
        return csuKeyToSoldNumMap;
    }

    /**
     * 查询订单拼团数据(从hyperspace迁移出来的)
     * @param list
     * @return
     */
    private Map<String, PromotionMsg> getOrderBuyingMap(List<GroupBuyingInfoDto> list){
        Map<String, PromotionMsg> retMap = new HashMap<>();
        if(CollectionUtils.isEmpty(list)){
            return retMap;
        }
        List<PromotionMsg> param = new ArrayList<>();
        for(GroupBuyingInfoDto dto:list){
            for(GroupBuyingSkuDto skuDto : dto.getGroupBuyingSkuDtoList()){
                PromotionMsg pm = new PromotionMsg();
                pm.setPromoId(dto.getMarketingId());
                pm.setSkuId(skuDto.getSkuId());
                param.add(pm);
            }
        }
        List<PromotionMsg> buyingList = orderServerRpcService.queryPromotionListMsg(param);
        for(PromotionMsg p:buyingList){
            retMap.put(p.getPromoId() + "-" + p.getSkuId(), p);
        }
        return retMap;
    }

    /**
     * 将订单记录转化为营销记录(从hyperspace迁移出来的)
     * @param orderPromotionRecords
     * @return
     */
    private List<GroupBuyingRecordDto> buildBuyingRecordDtoList(List<PromotionRecord> orderPromotionRecords){
        List<GroupBuyingRecordDto> recordList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(orderPromotionRecords)){
            for(PromotionRecord pr:orderPromotionRecords){
                GroupBuyingRecordDto dto = new GroupBuyingRecordDto();
                dto.setMerchantName(pr.getMerchantName());
                dto.setProductAmount(pr.getProductAmount());
                dto.setCreateTime(pr.getCreateTime());
                recordList.add(dto);
            }
        }
        return recordList;
    }

    @Override
    public List<GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdList(List<Long> skuIdList, Long merchantId) {
        if (merchantId == null || merchantId <= 0L || CollectionUtils.isEmpty(skuIdList)) {
            return Lists.newArrayList();
        }
        //未开始+进行中活动
        List<Integer> statusList = Arrays.asList(MarketingQueryStatusEnum.UN_START.getType(), MarketingQueryStatusEnum.STARTING.getType());
        List<GroupBuyingInfoDto> groupBuyingInfoDtoList = hyperSpaceRpc.getGroupBuyingInfoBySkuIdList(skuIdList, merchantId, statusList);
        if(CollectionUtils.isEmpty(groupBuyingInfoDtoList)){
            return groupBuyingInfoDtoList;
        }
        //添加订单成团信息
        groupBuyingInfoDtoList = addOrderInfoV2(groupBuyingInfoDtoList, null);
        return groupBuyingInfoDtoList;
    }

    @Override
    public Map<Long, GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdList(List<Long> skuIdList, List<Integer> statusList, Long merchantId) {
        Map<Long, GroupBuyingInfoDto> retMap = new HashMap<>();
        if (merchantId == null || merchantId <= 0L || CollectionUtils.isEmpty(skuIdList) || CollectionUtils.isEmpty(statusList)) {
            return retMap;
        }
        List<GroupBuyingInfoDto> groupBuyingInfoDtoList = hyperSpaceRpc.getGroupBuyingInfoBySkuIdList(skuIdList, merchantId, statusList);
        if (CollectionUtils.isEmpty(groupBuyingInfoDtoList)) {
            return retMap;
        }
        //添加订单成团信息
        groupBuyingInfoDtoList = addOrderInfoV2(groupBuyingInfoDtoList, null);
        // 排序
        groupBuyingInfoDtoList = groupBuyingInfoDtoList.stream().sorted(Comparator.comparing(GroupBuyingInfoDto::getStartTime)
                .thenComparing((a, b) -> b.getMarketingId().compareTo(a.getMarketingId())))
                .collect(Collectors.toList());
        //转换成map，若有重复，存放最早出现的。
        if (CollectionUtils.isEmpty(groupBuyingInfoDtoList)) {
            return retMap;
        }
        for (GroupBuyingInfoDto act : groupBuyingInfoDtoList) {
            List<GroupBuyingSkuDto> groupBuyingSkuDtoList = act.getGroupBuyingSkuDtoList();
            if (CollectionUtils.isEmpty(groupBuyingSkuDtoList)) {
                continue;
            }
            Long skuId = groupBuyingSkuDtoList.get(0).getSkuId();
            retMap.putIfAbsent(skuId, act);
        }
        return retMap;
    }

    @Override
    public Map<Long, GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdListForCms(List<Long> skuIdList, List<Integer> statusList) {
        Map<Long, GroupBuyingInfoDto> retMap = new HashMap<>();
        if (CollectionUtils.isEmpty(skuIdList) || CollectionUtils.isEmpty(statusList)) {
            return retMap;
        }
        List<GroupBuyingInfoDto> groupBuyingInfoDtoList = hyperSpaceRpc.getGroupBuyingInfoBySkuIdListForCms(skuIdList, statusList);
        if (CollectionUtils.isEmpty(groupBuyingInfoDtoList)) {
            return retMap;
        }
        //添加订单成团信息
        groupBuyingInfoDtoList = addOrderInfoV2(groupBuyingInfoDtoList, null);

        // 排序
        groupBuyingInfoDtoList = groupBuyingInfoDtoList.stream().sorted(Comparator.comparing(GroupBuyingInfoDto::getStartTime)
                .thenComparing((a, b) -> b.getMarketingId().compareTo(a.getMarketingId())))
                .collect(Collectors.toList());

        //转换成map，若有重复，存放最早出现的。
        if (CollectionUtils.isEmpty(groupBuyingInfoDtoList)) {
            return retMap;
        }
        for (GroupBuyingInfoDto act : groupBuyingInfoDtoList) {
            List<GroupBuyingSkuDto> groupBuyingSkuDtoList = act.getGroupBuyingSkuDtoList();
            if (CollectionUtils.isEmpty(groupBuyingSkuDtoList)) {
                continue;
            }
            Long skuId = groupBuyingSkuDtoList.get(0).getSkuId();
            retMap.putIfAbsent(skuId, act);
        }
        return retMap;
    }

    @Override
    public Map<Long, GroupBuyingInfoDto> getGroupBuyingBySkuIdsForSearch(List<Long> skuIdList, Long merchantId) {
        if (Objects.isNull(merchantId) || merchantId <= 0L || CollectionUtils.isEmpty(skuIdList)) {
            return Maps.newHashMap();
        }
        return this.getGroupBuyingInfoBySkuIdList(skuIdList, Arrays.asList(MarketingQueryStatusEnum.UN_START.getType(), MarketingQueryStatusEnum.STARTING.getType()), merchantId);
    }


    @Override
    public GroupBuyingInfoDto getGroupBuyingInfoById(Long marketingId) {
        GroupBuyingInfoDto groupBuyingInfoDto = hyperSpaceRpc.getGroupBuyingInfoById(marketingId);
        if(null == groupBuyingInfoDto){
            return null;
        }
        //给前端提供的接口不查商品信息
        List<GroupBuyingInfoDto> groupBuyingList = Arrays.asList(groupBuyingInfoDto);
        //添加订单成团信息
        groupBuyingList = addOrderInfoV2(groupBuyingList, null);
        return groupBuyingList.get(0);
    }

    @Override
    public List<GroupBuyingInfoDto> getActCardInfoBySkuIdList(List<Long> skuIdList, Long merchantId, Set<Integer> activityTypeSet, Set<Long> gaoMaoSkuIdSet) {
        if (merchantId == null || merchantId <= 0L || CollectionUtils.isEmpty(skuIdList) || CollectionUtils.isEmpty(activityTypeSet)) {
            return Lists.newArrayList();
        }
        //未开始+进行中活动
        List<Integer> statusList = Arrays.asList(MarketingQueryStatusEnum.UN_START.getType(), MarketingQueryStatusEnum.STARTING.getType());
        List<GroupBuyingInfoDto> groupBuyingInfoDtoList = Lists.newArrayListWithExpectedSize(skuIdList.size());
        List<List<Long>> partitions = Lists.partition(skuIdList, 100);
        for (List<Long> partition : partitions) {
            groupBuyingInfoDtoList.addAll(hyperSpaceRpc.getActCardInfoBySkuIdList(ActCardInfoParamDto.builder()
                    .skuIdList(partition).statusList(statusList).merchantId(merchantId).activityTypeSet(activityTypeSet).build()));
        }
        // 添加订单信息
        groupBuyingInfoDtoList = addOrderInfoV3(groupBuyingInfoDtoList, gaoMaoSkuIdSet);
        return groupBuyingInfoDtoList;
    }

    @Override
    public Map<Long, GroupBuyingInfoDto> getActCardInfoBySkuIdList(List<Long> skuIdList, List<Integer> statusList, Long merchantId, Set<Integer> activityTypeSet, Set<Long> gaoMaoSkuIdSet) {
        if (merchantId == null || merchantId <= 0L || CollectionUtils.isEmpty(skuIdList) || CollectionUtils.isEmpty(statusList) || CollectionUtils.isEmpty(activityTypeSet)) {
            return Maps.newHashMap();
        }
        Map<Long, GroupBuyingInfoDto> csuIdToInfoMap = Maps.newHashMapWithExpectedSize(skuIdList.size());
        List<GroupBuyingInfoDto> groupBuyingInfoDtoList = Lists.newArrayListWithExpectedSize(skuIdList.size());
        List<List<Long>> partitions = Lists.partition(skuIdList, 100);
        for (List<Long> partition : partitions) {
            groupBuyingInfoDtoList.addAll(hyperSpaceRpc.getActCardInfoBySkuIdList(ActCardInfoParamDto.builder()
                    .skuIdList(partition).statusList(statusList).merchantId(merchantId).activityTypeSet(activityTypeSet).build()));
        }
        if (CollectionUtils.isEmpty(groupBuyingInfoDtoList)) {
            return csuIdToInfoMap;
        }
        // 添加订单信息
        groupBuyingInfoDtoList = addOrderInfoV3(groupBuyingInfoDtoList, gaoMaoSkuIdSet);
        // 排序
        groupBuyingInfoDtoList = groupBuyingInfoDtoList.stream().sorted(Comparator.comparing(GroupBuyingInfoDto::getStartTime)
                        .thenComparing((a, b) -> b.getMarketingId().compareTo(a.getMarketingId())))
                .collect(Collectors.toList());
        for (GroupBuyingInfoDto act : groupBuyingInfoDtoList) {
            List<GroupBuyingSkuDto> groupBuyingSkuDtoList = act.getGroupBuyingSkuDtoList();
            if (CollectionUtils.isEmpty(groupBuyingSkuDtoList)) {
                continue;
            }
            Long skuId = groupBuyingSkuDtoList.get(0).getSkuId();
            csuIdToInfoMap.putIfAbsent(skuId, act);
        }
        return csuIdToInfoMap;
    }

    @Override
    public Map<Long, GroupBuyingInfoDto> getActCardInfoBySkuIdListForCms(List<Long> skuIdList, List<Integer> statusList, Set<Integer> activityTypeSet, Set<Long> gaoMaoSkuIdSet) {
        if (CollectionUtils.isEmpty(skuIdList) || CollectionUtils.isEmpty(statusList) || CollectionUtils.isEmpty(activityTypeSet)) {
            return Maps.newHashMap();
        }
        Map<Long, GroupBuyingInfoDto> csuIdToInfoMap = Maps.newHashMapWithExpectedSize(skuIdList.size());
        List<GroupBuyingInfoDto> groupBuyingInfoDtoList = Lists.newArrayListWithExpectedSize(skuIdList.size());
        List<List<Long>> partitions = Lists.partition(skuIdList, 100);
        for (List<Long> partition : partitions) {
            groupBuyingInfoDtoList.addAll(hyperSpaceRpc.getActCardInfoBySkuIdListForCms(ActCardInfoParamDto.builder()
                    .skuIdList(partition).statusList(statusList).activityTypeSet(activityTypeSet).build()));
        }
        if (CollectionUtils.isEmpty(groupBuyingInfoDtoList)) {
            return csuIdToInfoMap;
        }
        // 添加订单信息
        groupBuyingInfoDtoList = addOrderInfoV3(groupBuyingInfoDtoList, gaoMaoSkuIdSet);
        // 排序
        groupBuyingInfoDtoList = groupBuyingInfoDtoList.stream().sorted(Comparator.comparing(GroupBuyingInfoDto::getStartTime)
                        .thenComparing((a, b) -> b.getMarketingId().compareTo(a.getMarketingId())))
                .collect(Collectors.toList());
        for (GroupBuyingInfoDto act : groupBuyingInfoDtoList) {
            List<GroupBuyingSkuDto> groupBuyingSkuDtoList = act.getGroupBuyingSkuDtoList();
            if (CollectionUtils.isEmpty(groupBuyingSkuDtoList)) {
                continue;
            }
            Long skuId = groupBuyingSkuDtoList.get(0).getSkuId();
            csuIdToInfoMap.putIfAbsent(skuId, act);
        }
        return csuIdToInfoMap;
    }

    @Override
    public Map<Long, GroupBuyingInfoDto> getActCardInfoBySkuIdListForSearch(List<Long> skuIdList, Long merchantId, Set<Integer> activityTypeSet, Set<Long> gaoMaoSkuIdSet) {
        return this.getActCardInfoBySkuIdList(skuIdList, Arrays.asList(MarketingQueryStatusEnum.UN_START.getType(), MarketingQueryStatusEnum.STARTING.getType()), merchantId, activityTypeSet, gaoMaoSkuIdSet);
    }

    @Override
    public Page<MarketingCsuResultDto> getMarketingShopIndex(Integer pageNum, Integer pageSize, Long merchantId, String shopCode, CsuOrder csuOrder, MarketingEnum activityTypeEnum) {
        //1.调用搜索，获取商品id列表
        Page<MarketingCsuResultDto> csuInfoPage = getCsuPage(pageNum, pageSize, merchantId, shopCode, csuOrder, activityTypeEnum);
        if(null == csuInfoPage || CollectionUtils.isEmpty(csuInfoPage.getRecordList())){
            //返回
            return csuInfoPage;
        }
        List<MarketingCsuResultDto> csuList = csuInfoPage.getRecordList();
        //添加活动信息
        if(Objects.equals(MarketingEnum.PING_TUAN.getCode(), activityTypeEnum.getCode())){
            csuList = addGroupBuyingInfo(csuList, merchantId);
        }else if(Objects.equals(MarketingEnum.PI_GOU_BAO_YOU.getCode(), activityTypeEnum.getCode())) {
            csuList = addPiGouInfo(csuList, merchantId);
        } else if(Objects.equals(MarketingEnum.MIAO_SHA.getCode(), activityTypeEnum.getCode())) {
            csuList = addCsuMarketingSeckillActivityInfoForShopFloor(merchantId, csuList);
        } else {
            csuList = addCommonMarketingInfo(csuList,merchantId, activityTypeEnum);
        }
        csuInfoPage.setRecordList(csuList);
        //4.返回
        return csuInfoPage;
    }

    @Override
    public ConsumeRebateDetailVo getMarketingRebateConsumptionReturnInfo(Long merchantId, BigDecimal money) {
        if (Objects.isNull(merchantId) || Objects.isNull(money)) {
            throw new AppException("缺少必填参数");
        }

        MarketingRebateConsumptionReturnResult returnInfo = hyperSpaceRpc.getMarketingRebateConsumptionReturnInfo(merchantId, money);
        ConsumeRebateDetailVo detailVo = new ConsumeRebateDetailVo();
        detailVo.setToEtimeSeconds(returnInfo.getToEtimeSeconds());
        detailVo.setLastMonthExpectedAmount(returnInfo.getLastMonthExpectedAmount());
        detailVo.setLevelScene(returnInfo.getLevelScene());
        detailVo.setNextLevelRate(returnInfo.getNextLevelRate());
        if (returnInfo.getActTags() != null) {
            detailVo.setMaxReturnRedPackageAmount(returnInfo.getActTags().getMaxReturnRedPackageAmount());
        }
        detailVo.setNextLevelShortAmount(returnInfo.getNextLevelShortAmount());
        detailVo.setNextLevelRedPacketAmount(returnInfo.getNextLevelRedPacketAmount());
        detailVo.setRealAmount(returnInfo.getRealAmount());
        detailVo.setActResultList(returnInfo.getActResultList());
        return detailVo;
    }
    /**
     * 添加拼团信息
     * @param csuList
     * @param merchantId
     * @return
     */
    private List<MarketingCsuResultDto> addGroupBuyingInfo(List<MarketingCsuResultDto> csuList, Long merchantId){
        List<MarketingCsuResultDto> retList = new ArrayList<>();
        //调用营销查询拼团活动信息
        List<Long> csuIdList = csuList.stream().map(MarketingCsuResultDto::getCsuId).collect(Collectors.toList());
        //进行中活动
        List<Integer> statusList = Arrays.asList(MarketingQueryStatusEnum.STARTING.getType());
        List<GroupBuyingInfoDto> groupBuyingInfoDtoList = hyperSpaceRpc.getGroupBuyingInfoBySkuIdList(csuIdList, merchantId, statusList);
        if(CollectionUtils.isEmpty(groupBuyingInfoDtoList)){
            //返回
            return retList;
        }
        //添加订单成团信息
        groupBuyingInfoDtoList = addOrderInfoV2(groupBuyingInfoDtoList, null);
        //转换成map
        Map<Long, GroupBuyingInfoDto> actMap = actListToMap(groupBuyingInfoDtoList);
        //3.构建返回值
        for(MarketingCsuResultDto csuDto: csuList){
            GroupBuyingInfoDto groupBuyingInfoDto = actMap.get(csuDto.getCsuId().longValue());
            csuDto.setGroupBuyingInfoDto(groupBuyingInfoDto);
            if(null != groupBuyingInfoDto){
                retList.add(csuDto);
            }
        }
        return retList;
    }


    private List<MarketingCsuResultDto> addPiGouInfo(List<MarketingCsuResultDto> csuList, Long merchantId){
        List<MarketingCsuResultDto> retList = new ArrayList<>();
        //调用营销查询拼团活动信息
        List<Long> csuIdList = csuList.stream().map(MarketingCsuResultDto::getCsuId).collect(Collectors.toList());
        //进行中活动
        Map<Long, GroupBuyingInfoDto> skuIdToInProgressGroupBuyingInfoMap = marketingService.getActCardInfoBySkuIdList(csuIdList,
                Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()), merchantId,Sets.newHashSet(MarketingEnum.PI_GOU_BAO_YOU.getCode()), null);
        if(MapUtils.isEmpty(skuIdToInProgressGroupBuyingInfoMap)){
            return retList;
        }
        //3.构建返回值
        for(MarketingCsuResultDto csuDto: csuList){
            GroupBuyingInfoDto groupBuyingInfoDto = skuIdToInProgressGroupBuyingInfoMap.get(csuDto.getCsuId().longValue());
            csuDto.setPiGouInfoDto(groupBuyingInfoDto);
            if(null != groupBuyingInfoDto){
                retList.add(csuDto);
            }
        }
        return retList;
    }



    /**
     * 添加秒杀活动信息
     *
     * @param csuResultDtos
     * @param merchantId
     * @return
     */
    private List<MarketingCsuResultDto> addCsuMarketingSeckillActivityInfoForShopFloor(Long merchantId, List<MarketingCsuResultDto> csuResultDtos) {
        if (Objects.isNull(merchantId) || CollectionUtils.isEmpty(csuResultDtos)) {
            return Lists.newArrayList();
        }
        List<Long> csuIds = csuResultDtos.stream().filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getCsuId()))
                .map(MarketingCsuResultDto::getCsuId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(csuIds)) {
            return Lists.newArrayList();
        }
        //进行中活动
        Map<Long, List<ActInfoDTO>> startingCsuIdToActInfoMap = hyperSpaceRpc.batchGetCsuSomeStatusActInfos(merchantId,
                MarketingEnum.MIAO_SHA, MarketingQueryStatusEnum.STARTING.getType(), csuIds);
        if (log.isDebugEnabled()) {
            log.debug("根据商品ID获取展示中的秒杀活动信息（店铺楼层），merchantId：{}，csuIds：{}，响应：{}",
                    merchantId, JSONArray.toJSONString(csuIds), JSONObject.toJSONString(startingCsuIdToActInfoMap));
        }
        Map<Long, MarketingSeckillActivityInfoDTO> csuIdToSeckillActivityInfoMap = Maps.newHashMapWithExpectedSize(csuIds.size());
        // 转换
        Long csuId;
        List<ActInfoDTO> actInfoDTOS;
        ActInfoDTO actInfoDTO;
        for (Map.Entry<Long, List<ActInfoDTO>> entry : startingCsuIdToActInfoMap.entrySet()) {
            csuId = entry.getKey();
            actInfoDTOS = entry.getValue();
            if (Objects.nonNull(csuId) && CollectionUtils.isNotEmpty(actInfoDTOS)) {
                // 排序：同状态中按活动开始时间升序、活动ID降序排序
                actInfoDTOS = actInfoDTOS.stream().sorted(Comparator.comparing(ActInfoDTO::getStime)
                        .thenComparing((a, b) -> b.getMarketingId().compareTo(a.getMarketingId())))
                        .collect(Collectors.toList());
                actInfoDTO = actInfoDTOS.get(0);
                MarketingSeckillActivityInfoDTO marketingSeckillActivityInfoDTO = MarketingSeckillActivityInfoDTOHelper.convert(csuId, actInfoDTO);
                if (Objects.nonNull(marketingSeckillActivityInfoDTO)) {
                    csuIdToSeckillActivityInfoMap.putIfAbsent(csuId, marketingSeckillActivityInfoDTO);
                }
            }
        }
        // 按照入参的顺序返回有秒杀活动信息的csu
        if (MapUtils.isEmpty(csuIdToSeckillActivityInfoMap)) {
            return Lists.newArrayList();
        }
//        // 筛出进行中的秒杀活动，填充已售数
//        csuIdToSeckillActivityInfoMap = this.fillSeckillActivityCsuSoldInfo(csuIdToSeckillActivityInfoMap);
        List<MarketingCsuResultDto> result = Lists.newArrayListWithExpectedSize(csuIds.size());
        MarketingSeckillActivityInfoDTO marketingSeckillActivityInfoDTO;
        for (MarketingCsuResultDto csuResultDto : csuResultDtos) {
            marketingSeckillActivityInfoDTO = csuIdToSeckillActivityInfoMap.get(csuResultDto.getCsuId());
            if (Objects.isNull(marketingSeckillActivityInfoDTO)) {
                continue;
            }
            csuResultDto.setSeckillActivityInfo(marketingSeckillActivityInfoDTO);
            result.add(csuResultDto);
        }
        if (log.isDebugEnabled()) {
            log.debug("根据商品ID获取展示中的秒杀活动信息（店铺楼层），最终响应，merchantId：{}，csuIds：{}，响应：{}",
                    merchantId, JSONArray.toJSONString(csuIds), JSONObject.toJSONString(result));
        }
        return result;
    }

    /**
     * 添加通用活动信息
     * @param csuList
     * @param merchantId
     * @return
     */
    private List<MarketingCsuResultDto> addCommonMarketingInfo(List<MarketingCsuResultDto> csuList, Long merchantId, MarketingEnum marketingEnum){
        List<MarketingCsuResultDto> retList = new ArrayList<>();
        List<Long> csuIdList = csuList.stream().map(MarketingCsuResultDto::getCsuId).collect(Collectors.toList());
        //调用营销查询活动信息
        Map<Long, List<ActInfoDTO>> actMap = hyperSpaceRpc.getMarketingInfoBySkuIdList(csuIdList, merchantId, marketingEnum);
        //3.构建返回值
        for(MarketingCsuResultDto csuDto: csuList){
            List<ActInfoDTO> actList = actMap.get(csuDto.getCsuId().longValue());
            csuDto.setCommonActList(actList);
            if(CollectionUtils.isNotEmpty(actList)){
                retList.add(csuDto);
            }
        }
        return retList;
    }

    /**
     * 调用搜索接口查询商品分页信息
     * @param pageNum
     * @param pageSize
     * @param merchantId
     * @param shopCode
     * @param csuOrder
     * @return
     */
    private Page<MarketingCsuResultDto> getCsuPage(Integer pageNum, Integer pageSize, Long merchantId, String shopCode, CsuOrder csuOrder, MarketingEnum activityTypeEnum){
        //调用cms查询商品组信息
        String exhibitionId = getMarketingProductGroupId(activityTypeEnum.getCode());
        Page page = new Page(pageNum, pageSize);
        if(StringUtils.isEmpty(exhibitionId)){
            return page;
        }
        SearchCsuDTO searchCsuDTO = new SearchCsuDTO();
        searchCsuDTO.setMerchantId(merchantId);
        searchCsuDTO.setShopCodes(Arrays.asList(shopCode));
        if (Objects.equals(csuOrder, CsuOrder.PT_DEFAULT)) {
            searchCsuDTO.setRankType(EcRankType.EC_PT_RANK.getValue());
            searchCsuDTO.setSortOrder(SortOrder.DESC);
        } else if (Objects.equals(csuOrder, CsuOrder.SK_DEFAULT)) {
            searchCsuDTO.setRankType(EcRankType.EC_SK_RANK.getValue());
            searchCsuDTO.setSortOrder(SortOrder.DESC);
        }
        searchCsuDTO.setCsuOrder(csuOrder);
        //筛选拼团活动
        searchCsuDTO.setTagList(exhibitionId);

        ApiRPCResult<IPage<CsuInfo>> apiRPCResultSearch = ecSearchApi.searchForPC(page, searchCsuDTO);
        log.info("查询ecSearchApi.searchForPC searchCsuDTO：{}，page：{}，响应结果：{}",
                JSONObject.toJSONString(searchCsuDTO), JSONObject.toJSONString(page), JSONObject.toJSONString(apiRPCResultSearch));
        // 商品组探活埋点
        productExhibitionGroupBusinessAdminRemoteService.asyncSendExhibitionLiveEventMQForSearch(searchCsuDTO);
        Page<MarketingCsuResultDto> csuPage = new Page<>();
        if (apiRPCResultSearch.isSuccess()) {
            IPage<CsuInfo> csuInfoIPage = apiRPCResultSearch.getData();
            if (csuInfoIPage != null) {
                long totalCount = csuInfoIPage.getTotalCount();
                long pages = csuInfoIPage.getPages();
                List<CsuInfo> searchRecordList = csuInfoIPage.getRecordList();
                List<MarketingCsuResultDto> marketingCsuResultDtoList = new ArrayList<>();
                for(CsuInfo searchCsu: searchRecordList){
                    MarketingCsuResultDto mCsuDto = new MarketingCsuResultDto();
                    mCsuDto.setCsuId(searchCsu.getId());
                    mCsuDto.setCsuInfo(searchCsu);
                    marketingCsuResultDtoList.add(mCsuDto);
                }
                csuPage.setPageNo(pageNum);
                csuPage.setPageSize(pageSize);
                csuPage.setTotalCount(totalCount);
                csuPage.setPages(pages);
                csuPage.setRecordList(marketingCsuResultDtoList);
            } else {
                log.error("MarketingServiceImpl.getCsuIdListByPage error，searchCsuDTO：{}，apiRPCResultSearch：{}",
                        JSONObject.toJSONString(searchCsuDTO), JSONObject.toJSONString(apiRPCResultSearch));
            }
        } else {
            log.error("MarketingServiceImpl.getCsuIdListByPage error，searchCsuDTO：{}，apiRPCResultSearch：{}",
                    JSONObject.toJSONString(searchCsuDTO), JSONObject.toJSONString(apiRPCResultSearch));
        }
        return csuPage;
    }

    /**
     * 通过cms获取活动标签
     * @param activityType
     * @return
     */
    private String getMarketingProductGroupId(Integer activityType){
        ApiRPCResult<String> result = productExhibitionGroupBusinessForMarketingApi.getMarketingProductGroupId(activityType);
        log.info("productExhibitionGroupBusinessForMarketingApi.getMarketingProductGroupId，activityType：{}，result：{}",
                activityType, JSONObject.toJSONString(result));
        if(!result.isSuccess()){
            log.error("productExhibitionGroupBusinessForMarketingApi.getMarketingProductGroupId error，activityType：{}，result：{}",
                    activityType, JSONObject.toJSONString(result));
            throw new RuntimeException("查询商品组信息异常");
        }
        return result.getData();
    }
}
