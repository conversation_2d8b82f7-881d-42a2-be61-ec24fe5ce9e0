package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.pc.model.AddLoginAgreementLogDto;
import com.xyy.ec.pc.newfront.service.LoginAgreementService;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/new-front/loginAgreement")
public class LoginAgreementNewController {

    @Resource
    private LoginAgreementService loginAgreementService;

    @GetMapping("/get-login-agreement")
    public AjaxResult<Object> loginAgreementInfo(@RequestParam(value = "lang",required = false) String lang) {
        try {
            return loginAgreementService.getLoginAgreement(lang);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @PostMapping("/add-log")
    public AjaxResult<Object> addLog(@RequestBody AddLoginAgreementLogDto param, HttpServletRequest request) {
        try {
            return loginAgreementService.addLog(param,request);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }
}
