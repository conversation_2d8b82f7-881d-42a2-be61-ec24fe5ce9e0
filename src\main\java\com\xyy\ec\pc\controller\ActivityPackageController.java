package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Sets;
import com.xyy.ec.layout.buinese.api.ExhibitionBuineseApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.rpc.MarketActivityPackageRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.product.business.dto.ProductDto;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import com.xyy.ec.product.business.dto.product.ProductActivityTag;
import com.xyy.ms.marketing.nine.chapters.api.pc.ActivitySkuBuyLimitForPcApi;
import com.xyy.ms.marketing.nine.chapters.common.config.ActivityEnum;
import com.xyy.ms.promotion.business.api.pc.ActivityPackageForPcBusinessApi;
import com.xyy.ms.promotion.business.dto.activitypackage.ActivityPackageDto;
import com.xyy.ms.promotion.business.dto.activitypackage.ActivityPackageSkuDTO;
import com.xyy.ms.promotion.business.dto.activitypackage.ActivityPackageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Title:
 * @Description:
 * @date 2018/9/6 00:35
 */
@Controller
@RequestMapping("/activityPackage")
@Slf4j
public class ActivityPackageController extends BaseController {

    Logger LOGGER = LoggerFactory.getLogger(ActivityPackageController.class);



    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private ActivityPackageForPcBusinessApi activityPackageBusinessApi;

    @Autowired
    private MarketActivityPackageRpc marketActivityPackageRpc;

    @Reference(version = "1.0.0")
    private ExhibitionBuineseApi exhibitionBuineseApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Autowired
    private PcVersionUtils pcVersionUtils;

    @Reference(version = "1.0.0")
    private ActivitySkuBuyLimitForPcApi activitySkuBuyLimitForPcApi;

    @Autowired
    private XyyConfig xyyConfig;

    /**
     * @param page
     * @param activityPackageDTO
     * @return org.springframework.web.servlet.ModelAndView
     * @throws
     * @Description: 套餐查询界面
     * <AUTHOR>
     * @date 2018/9/6 00:37
     */
    @RequestMapping("/index.html")
    @ResponseBody
    public ModelAndView index(Page<ActivityPackageVo> page, ActivityPackageDto activityPackageDTO, HttpServletRequest request) throws Exception {

        Map<String, Object> model = new HashMap<>();
        Long merchantId;
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
        if (merchant != null) {
            merchantId = merchant.getId();
            model.put("merchant", merchant);
            model.put("merchantId", merchantId);
        } else {
            merchantId = 0L;
        }

        String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
        List<ActivityPackageVo> packageList = new ArrayList<>();
        if(null != activityPackageDTO.getType() && null != activityPackageDTO.getMark()){
            packageList = marketActivityPackageRpc.getAllActivityPackage(merchantId, branchCode, activityPackageDTO.getType(), activityPackageDTO.getMark());
        }
        Set<Long> packageIdSet;
        try{
            if (StringUtils.isBlank(xyyConfig.getReWritePackagePriceIds())){
                packageIdSet = Sets.newHashSet();
            }else{
                packageIdSet = Sets.newHashSet(JSONArray.parseArray(xyyConfig.getReWritePackagePriceIds(), Long.class));
            }
        }catch (Exception e){
            packageIdSet = Sets.newHashSet();
            LOGGER.error("rewritePackagePrice_reWritePackagePriceIds={}", JSON.toJSONString(xyyConfig.getReWritePackagePriceIds()));
        }

        if(CollectionUtils.isNotEmpty(packageList) && (null == merchant || merchant.getPriceDisplayFlag() == false)){
            ProductActivityTag productActivityTagVO = new ProductActivityTag();
            productActivityTagVO.setTagUrl("");
            productActivityTagVO.setTagNoteBackGroupUrl("");
            productActivityTagVO.setSkuTagNotes(new ArrayList<>());
            for(ActivityPackageVo packageVo : packageList){
                if (packageIdSet.contains(packageVo.getId())){
                    LOGGER.info("rewritePackagePrice_item={}", JSON.toJSONString(packageVo));
                    packageVo.setTotalPrice(packageVo.getSubtotalPrice());
                }
                if(CollectionUtils.isNotEmpty(packageVo.getSkuList())){
                    for(ActivityPackageSkuDTO product : packageVo.getSkuList()){
                        product.setFob(BigDecimal.ZERO);
                        if(product.getSku() != null){
                            product.getSku().setFob(0d);
                            product.getSku().setActivityTag(productActivityTagVO);
                            //毛利
                            product.getSku().setGrossMargin("");
                            //建议零售价
                            product.getSku().setSuggestPrice(BigDecimal.ZERO);
                            //控销零售价
                            product.getSku().setUniformPrice(BigDecimal.ZERO);
                            //对比价
                            product.getSku().setRetailPrice(0.0);
                        }
                    }
                }
            }
        }
        if(CollectionUtils.isNotEmpty(packageList)) {
            packageList.stream().filter(item -> item != null && Objects.equals(item.getStatus(), 0))
            .forEach(item -> {
                Integer activityBuyLimitNumByActivity = activitySkuBuyLimitForPcApi.getActivityBuyLimitTotalSurplusNumByActivity(item.getId(), ActivityEnum.TAO_CAN.getCode());
                if (activityBuyLimitNumByActivity != null && activityBuyLimitNumByActivity <= 0) {
                    // 限购且没有活动剩余可购买量
                    item.setStatus(1);
                }
            });
        }
        page.setRows(packageList);
        if (merchant != null) {
            model.put("islogin", 1);
        } else {
            model.put("islogin", 0);
        }
        //type=2销售套餐的页面，需要一组套餐数据，同时查询下面2组商品数据传到前端
        if (null != activityPackageDTO.getType() && activityPackageDTO.getType() == 2) {
            //添加VIPA套餐 VIPB套餐
            if (CollectionUtils.isNotEmpty(packageList)) {
                if (packageList.size() > 1) {
                    model.put("package", packageList.get(0));
                    model.put("packageB", packageList.get(1));
                } else {
                    model.put("package", packageList.get(0));
                }
            }
            //跳转到东瑞制药销售套餐页面 页面mark=1
            if (activityPackageDTO.getMark() == 1) {
                model.put("currentDate", new Date());
                return new ModelAndView("/activityEvent/events-20170921-taocan.ftl", model);
            }
            // 获取数据参数
            Page<ProductDto> pageVIP = new Page<>();
            pageVIP.setLimit(500);
            pageVIP.setOffset(0);
            List<ListProduct> skuDtoList;
            ListSkuSearchData listSkuSearchData = exhibitionBuineseApi.getExhibitionProductSkuInfoWrapper("V_I_P_C", merchantId, branchCode, 1, 500);
            if (listSkuSearchData != null &&  listSkuSearchData.getSkuDtoList() != null) {
                skuDtoList = listSkuSearchData.getSkuDtoList();
                if(null == merchant || merchant.getPriceDisplayFlag() == false) {
                    ProductActivityTag productActivityTagVO = new ProductActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    for (ListProduct product : skuDtoList) {
                        product.setFob(0d);
                        product.setUnitPrice(null);
                        product.setUnitPriceTag(null);
                        product.setActivityTag(productActivityTagVO);
                        //毛利
                        product.setGrossMargin("");
                        //建议零售价
                        product.setSuggestPrice(BigDecimal.ZERO);
                        //控销零售价
                        product.setUniformPrice(BigDecimal.ZERO);
                        //对比价
                        product.setRetailPrice(0.0);
                    }
                }
            } else {
                skuDtoList = new ArrayList<>(0);
            }
            model.put("VIPCList", skuDtoList);
            model.put("currentDate", new Date());
            return new ModelAndView("/activityEvent/events-20170818-taocan.ftl", model);
            /**精品套餐列表页面**/
        } else if (null != activityPackageDTO.getType() && activityPackageDTO.getType() == 3) {
            model.put("packageList", packageList);
            model.put("currentDate", new Date());
            return new ModelAndView("/activityEvent/events-20171108-taocan.ftl", model);
        } else {
            // 此处留给第四种 混合套餐加多组商品数据
            model.put("pager", page);
            model.put("currentDate", new Date());
            return new ModelAndView("/activityPackage/index.ftl", model);
        }
    }
}
