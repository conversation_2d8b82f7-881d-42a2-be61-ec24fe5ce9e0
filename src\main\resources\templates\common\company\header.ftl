<@header_data headerData>
<script type="text/javascript" src="/static/js/jquery.placeholder.min.js"></script>
<script type="text/javascript" src="/static/js/zhuge/zhugeio.js?t=${t_v}"></script>
<div class="headerT">
    <!-- 判断是否是IE -->
    <div id="IETip"></div>
    <!--导航-->
    <div class="sui-navbar">
        <div class="navbar-inner">

            <ul class="sui-nav nologin" id="loginUserInfo">
                <li><a href="http://upload.ybm100.com/ybm/pc/activitys/html/药帮忙.url" class="cjzmkj"><img src="${ctx}/static/images/zmkj.png"  class="zmkj">桌面快捷</a></li>
                <li><a href="javascript:void(0)" onclick="addCookie()" class="addCookie-btn" rel='sidebar'>收藏本站</a></li>
                <li><a href="javascript:void(0);" class="userno">欢迎来到药帮忙！</a></li>
                <li><a href="#" >请 <span class="speano"> 登录</span></a></li>
                <li><a href="#" >注册有礼</a></li>
            </ul>

            <ul class="sui-nav pull-right">
                <li><a href="${ctx }/">首页</a></li>
                <li class="xiexian">/</li>
                <li><a href="${ctx }/merchant/center/order/index.htm">我的订单</a></li>
                <li class="xiexian">/</li>
                <li><a href="${ctx }/merchant/center/index.htm">用户中心</a></li>
                <li class="xiexian">/</li>
                <li><a href="javascript:void(0);" class="userno">400-0505-111</a></li>
                <li class="xiexian">/</li>
                <li><a href="javascript:callKf('','${merchant.id}');">在线客服</a></li>
                <li class="xiexian">/</li>
                <li><a href="${ctx }/helpCenter/about.htm">帮助中心</a></li>
                <li class="xiexian">/</li>
                <li class="shoujima"><a href="${ctx }/helpCenter/about.htm"><img src="${ctx }/static/images/shouji.png"  class="shoujiico">手机药帮忙 <img src="${ctx }/static/images/top-erweima.png" class="posimg"  /> </a></li>
            </ul>
        </div>
    </div>

</div>
<!--搜索栏-->
<div class="searcherBox" id="searchBox">
    <div class="col1 fl">
    </div>

    <div class="col2 fl">
        <div class="search"  >
            <div class="inputbox fl">
                <input type="text" id="search" placeholder="药品名称/厂家名称/助记码" value="${keywordSearch}" autocomplete="off"/>
            </div>
            <div class="ss fl">
                <a href="#" onclick="searchOrgProduct()">搜本店</a>
                <a href="#" onclick="searchProduct();" class="ss-quanzhan">搜全站</a>
            </div>
            <!--搜索下拉弹窗-->
            <ul class="searchUl" id="searchUl"></ul>
        </div>
        <div class="rmtag" id="hotWordDiv">
            <#list model.listHostSearch as hot>
                <span onclick="hotKeywordTrack(${hot_index})">
                    <#if hot.url?? && hot.url!=''>
                        <a href="${ctx}/${hot.url}" target="_blank">${hot.keyword}</a>
                    <#else>
                        <a href="${ctx}/company/center/companyInfo/shopSkuInfo.htm?keyword=${hot.keyword}&orgId=${orgId}" target="_blank">${hot.keyword}</a>
                    </#if>
                </span>
            </#list>
        </div>
    </div>

</div>

<!--商品分类栏-->
<div class="newwarp">
    <div class="goodsmain">
        <div class="leftbox fl">
            <div class="fltitle index-noshow-fenlei">
                <i class="sui-icon icon-pc-list"></i>全部分类
            </div>
        </div>
        <div class="rightbox fl">
            <ul class="topnav" id="topnav">
                <li class="index-noshow">
                    <a href="${ctx }/company/center/companyInfo/shopIndex.htm?orgId=${orgId}">首页</a>
                </li>
                <li>
                    <a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?orgId=${orgId}" class="${styleClassa}"
                       target="_blank">全部商品</a>
                </li>
                <#--                <li>-->
                <#--                    <a href="${ctx }/company/center/companyInfo/shopIntroduce.htm?orgId=${orgId}" class="${styleClass2}" target="_blank">关于我们</a>-->
                <#--                </li>-->
                <#--                <li>-->
                <#--                    <a href="${ctx }/company/center/companyInfo/openAccount.htm?orgId=${orgId}" class="${styleClass2}" target="_blank">开户流程</a>-->
                <#--                </li>-->
                <#if orgId != "SH999">
                <li id="afterSales">
                    <a href="${ctx }/company/center/companyInfo/afterSales.htm?orgId=${orgId}" class="${styleClass2}"
                       target="_blank">资质售后</a>
                </li>
                </#if>

                <#if  merchant != null && orgId != "SH999">
                    <a href="javaScript:callKf('','${merchant.id}','${orgId}','1');"
                       style="line-height:20px;font-size:14px;color:#09c677;position:absolute;right:5px;background:#fff;padding:5px 15px;border-radius: 4px;top:5px;right:20px;">
                        联系商家
                </a>
                </#if>
            </ul>

        </div>

        <!--悬浮类目-->
        <div class="xfubox">
            <div id="firstBox" class="flbox index-spe">
            </div>
            <!--二级类目弹窗-->
            <div class="leimu-TC">
                <ul class="two-box-ul">

                </ul>
            </div>
        </div>
    </div>
</div>

<div class="kuanjie">
    <!--导航-->
    <div class="abs-warp">
        <!--采购单-->
        <a href="${ctx}/merchant/center/cart/index.htm" class="tongyong cgdbox ">
            <div class="r-topbox">
                <i class="icon iconfont icon-caigoudan"></i>

            </div>
            <div class="r-footbox cycle2">采购单</div>
            <!--<span id="rigthCartNum" class="topp"></span>-->
            <span id="rigthCartNum"></span>
        </a>

        <!--收藏-->
        <a href="${ctx}/merchant/center/collection/findAttention.htm" class="tongyong jdbox">
            <div class="r-topbox"><i class="icon iconfont icon-shoucang"></i></div>
            <!--<div class="r-footbox">收藏</div>-->
            <div class="zuotc">收藏</div>
        </a>

        <!--客服-->
        <a href="javaScript:callKf('','${merchant.id}');" class="tongyong jdbox">
            <div class="r-topbox"><i class="icon iconfont icon-kefu"></i></div>
            <!--<div class="r-footbox">客服</div>-->
            <div class="zuotc">客服</div>
        </a>

        <!--意见反馈-->
        <#--  <a href="${ctx}/feedback/indexFeedback.html" class="tongyong jdbox">
            <div class="r-topbox"><i class="icon iconfont icon-yijianfankui"></i></div>
            <div class="zuotc">意见反馈</div>
        </a>  -->

        <!--心愿单-->
        <#--<a href="${ctx}/merchant/center/wish/index.htm?tab=1" class="tongyong jdbox">-->
        <#--<div class="r-topbox"><i class="icon iconfont icon-xinyuandan"></i></div>-->
        <#--<!--<div class="r-footbox">心愿单</div>&ndash;&gt;-->
        <#--<!--<div class="you-er"><img src="img/xinyuandan-new.png" alt="" /></div>&ndash;&gt;-->
        <#--<div class="zuotc">心愿单</div>-->
    <#--</a>-->

        <div class="dingwei">
            <!--APP-->
            <a href="#" class="tongyong speapp jdbox app">
                <div class="r-topbox"><i class="icon iconfont icon-shouji"></i></div>
            </a>

            <!--top-->
            <a href="javascript:void(0);" id="toTop" class="tongyong jdbox">
                <div class="r-topbox"><i class="sui-icon sui-icon icon-touch-chevron-up"></i></div>
                <div class="zuotc">顶部</div>
            </a>
        </div>

    </div>
    <!--二维码动画-->
    <div class="erm-abs">
        <img src="${ctx}/static/images/xzma.png" />
    </div>

</div>



</div>
</@header_data>



<script type="text/javascript">
    var merchantio = '';
    $(function() {
        IEVersion();
        $.ajax({
            url: "/header_data.json",
            type: "GET",
            async:true,
            cache: false,
            dataType: "json",
            success: function(data) {
                var province = data.header_data.province;
                merchantio = data.header_data.merchant;
                if(data.header_data.merchant != null){
                    $("#loginUserInfo").removeClass("sui-nav nologin");
                    $("#loginUserInfo").addClass("sui-nav");
                    var html ='<li class="nav-province"> <a href="javascript:void(0)" class="field"><span class="sui-icon icon-touch-location-sign"></span><span class="province">'+province+'</span></a> </li><li><a href="http://upload.ybm100.com/ybm/pc/activitys/html/药帮忙.url" class="cjzmkj"><img src="${ctx}/static/images/zmkj.png"  class="zmkj">桌面快捷</a></li>'
                            +'<li><a href="javascript:void(0)" onclick="addCookie()" class="addCookie-btn" rel="sidebar">收藏本站</a></li>'
                            +'<li><a href="javascript:void(0);" class="user">'+data.header_data.merchant.realName+'！欢迎您！</a></li>'
                            +'<li><a href="${ctx}/login/logout.htm" class="spea">退出</a></li>';
                    $("#loginUserInfo").html(html);
                    if(data.header_data.merchantCartCount>0){
                        $("#cartNumberLi").addClass("cycle");
                        $("#cartNumberLi").html(data.header_data.merchantCartCount);
                        $("#cartNumberDiv").addClass("topp");
                        $("#cartNumberDiv").html(data.header_data.merchantCartCount);
                        $("#rigthCartNum").addClass("topp");
                        $("#rigthCartNum").removeClass("noshow");
                        $("#rigthCartNum").addClass("cycle2");
                        $("#rigthCartNum").html(data.header_data.merchantCartCount);
                        $("#cartNumberDiv").removeClass("noshow");
                        $("#firstCartNumberDiv").removeClass("noshow");
                        $("#firstCartNumberDiv").addClass("cycle2");
                        $("#firstCartNumberDiv").addClass("topp");
                        $("#firstCartNumberDiv").html(data.header_data.merchantCartCount);
                    }else{
                        $("#cartNumberLi").html("");
                        $("#cartNumberDiv").html("");
                        $("#cartNumberDiv").text("");
                        $("#cartNumberDiv").addClass("noshow");
                        $("#rigthCartNum").removeClass("noshow");
                        $("#rigthCartNum").removeClass("topp");
                        $("#rigthCartNum").addClass("cycle2 noshow");
                        $("#rigthCartNum").html("");
                        $("#firstCartNumberDiv").html("");
                        $("#firstCartNumberDiv").removeClass("topp");
                    }
                    zhugeio();
                }else{
                    /* var html =  "<li class='nav-province'>"
                    +"<a href='javascript:void(0)' class='field'>"
                    +"<span class='sui-icon icon-touch-location-sign'></span>"
                    +"<span class='province'></span><span class='sui-icon icon-tb-unfold'></span></a>"
                    +"<div class='field-show'><ul>";
                    var provinceList = data.header_data.provinceList;
                    for ( var element in provinceList) {
                        html+="<li code='XS"+provinceList[element].areaCode+"'>"+provinceList[element].areaName+"</li>";
                    }
                    html+="</ul></div></li>" */
                    var html = '<li class="nav-province"> <a href="javascript:void(0)" class="field"><span class="sui-icon icon-touch-location-sign"></span><span class="province">'+province+'</span></a> </li><li><a href="http://upload.ybm100.com/ybm/pc/activitys/html/药帮忙.url" class="cjzmkj"><img src="${ctx}/static/images/zmkj.png"  class="zmkj">桌面快捷</a></li>'
                            +'<li><a href="javascript:void(0)" onclick="addCookie()" class="addCookie-btn" rel="sidebar">收藏本站</a></li>'
                            +'<li><a href="javascript:void(0);" class="userno">欢迎来到药帮忙！</a></li>'
                            +'<li><a href="${ctx}/login/login.htm" >请 <span class="speano"> 登录</span></a></li>'
                            +'<li><a href="${ctx}/newstatic/#/register/index" >注册有礼</a></li>';
                            <#--+'<li><a href="${ctx}/login/register.htm" >注册有礼</a></li>';-->
                    $("#loginUserInfo").html(html);
                    $("#cartNumberLi").removeClass("cycle");
                    $("#cartNumberLi").html("");
                    $("#cartNumberDiv").html("");
                    $("#cartNumberDiv").text("");
                    $("#cartNumberDiv").addClass("noshow");
                    $("#rigthCartNum").removeClass("topp");
                    $("#rigthCartNum").html("");
                    $("#firstCartNumberDiv").html("");

                    $(".nologin .nav-province").hover(function(){
                        $(".field-show").show();
                        $(this).find(".icon-tb-unfold").attr("class","sui-icon icon-tb-fold");
                    },function(){
                        $(".field-show").hide();
                        $(this).find(".icon-tb-fold").attr("class","sui-icon icon-tb-unfold");
                    });
                    $(".field-show li").click(function(){
                        var pro = $(this).text();
                        $(".nav-province .province").text(pro);
                        setCookie("XSESSION_CODE",$(this).attr("code"),24*7,"/");
                        location.reload();
                    });
                    /** 改用ip获取域 */
                    //获取当前用户所在省份
                    /*var sessionCode = getCookieValue("XSESSION_CODE");
                    if(!sessionCode){
                        $.ajax({
                            url: "http://api.map.baidu.com/location/ip?ak=SbCKuq9uoYxlmUwvidfcGi97csggOaaY",
                            type: "GET",
                            async: false,
                            dataType: "jsonp",
                            success: function(data) {
                                var sessionProvince = data.content.address_detail.province;
                                switch (sessionProvince) {
                                        /!* case "重庆市":
                                            sessionCode = "XS500000";
                                            break;
                                        case "安徽省":
                                            sessionCode = "XS340000";
                                            break;
                                        case "浙江省":
                                            sessionCode = "XS330000";
                                            break; *!/
                                    default:
                                        sessionCode = "XS420000";
                                        break;
                                }
                                //$(".nav-province .province").text($(".field-show ul li[code='"+sessionCode+"']").text());
                                setCookie("XSESSION_CODE",sessionCode,24*7,"/");
                            }
                        });
                    }else{
                        setCookie("XSESSION_CODE","XS420000",24*7,"/");
                        //$(".nav-province .province").text($(".field-show ul li[code='"+sessionCode+"']").text());
                    }*/
                }
            }
        });
        /* 解决前端兼容性问题 */
        if(window.btoa && window.location){
            $.ajax({
                url: "/header_guide.json?thisPath=" + window.btoa(window.location.pathname + window.location.search),
                type: "GET",
                async: true,
                cache: false,
                dataType: "json",
                success: function(data) {
                    if(data.status == "success" && data.header_guide.headerGuideList && data.header_guide.headerGuideList.length>0){
                        $("#diyGuide").html("");
                        var headerGuideList = data.header_guide.headerGuideList;
                        var html ='';
                        for(i in headerGuideList){
                            var headerGuide = headerGuideList[i];
                            html += "<li>";
                            html += "<a href=${ctx}'"+headerGuide.url+"'";
                            if(headerGuide.className){
                                html += "class='"+headerGuide.className+"'"
                            }
                            html +=" target='_blank'>"+headerGuide.title;
                            if(headerGuide.icon && headerGuide.icon!=''){
                                html+= "<img src='${ctx}"+headerGuide.icon+"' class='hot'>"
                            }
                            html += "</a>";
                            html += "</li>";
                        }
                        $("#diyGuide").append(html);
                        /*导航添加底部横线*/
                        // $('.topnav li').hover(function(){$(this).addClass("hovercur").siblings().removeClass("hovercur")},function(){$(this).removeClass("hovercur")});
                    }
                }
            });
        }
        zhugeio();
        initShopInfo();
        initCategory();

        if (window.location.href.indexOf('afterSales.htm') > -1) {
            $("#afterSales").addClass('hovercur').siblings().removeClass("hovercur");
        }
    });
    //诸葛IO埋点
    function zhugeio() {
        //console.log('company-header', merchantio)
        if(merchantio){
            console.log(merchantio.id)
            var channelStr = '';
            var merchatChannelList = merchantio.channelList;
            if (merchatChannelList && Array.isArray(merchatChannelList)) {
                var channelCodeNameMap = {'1':'B2B','2':'宜块钱'};
                var channelCodeName;
                for (var i = 0; i < merchatChannelList.length; i ++) {
                    channelCodeName = channelCodeNameMap[merchatChannelList[i]] || '';
                    if (channelCodeName) {
                        channelStr += channelCodeName + ',';
                    }
                }
                if (channelStr && channelStr.length > 0) {
                    channelStr = channelStr.substring(0, channelStr.length-1);
                }
            }
            // zhuge.setSuperProperty({
            //     'channelCode': channelStr
            // });
            // zhuge.identify(merchantio.id,{
            //     name:merchantio.nickname,
            //     loginName :merchantio.loginName,
            //     phoneNum : merchantio.mobile,
            //     nickname :merchantio.nickname,
            //     realName :merchantio.realName,
            //     registeredDate:merchantio.createTime,
            //     lastLoginTime:merchantio.lastLoginTime,
            //     businessType:merchantio.businessType,
            //     businessTypeName:merchantio.businessTypeName,
            //     provinceId:merchantio.provinceCode,
            //     provinceName:merchantio.province,
            //     cityId:merchantio.cityCode,
            //     cityName:merchantio.city,
            //     districtId:merchantio.areaCode,
            //     districtName:merchantio.district,
            //     address:merchantio.address,
            //     channelCode:channelStr
            // });
            webSdk.setSuperProperty({
                'channelCode': channelStr
            });
            webSdk.identify(merchantio.id,{
                name:merchantio.nickname,
                loginName :merchantio.loginName,
                phoneNum : merchantio.mobile,
                nickname :merchantio.nickname,
                realName :merchantio.realName,
                registeredDate:merchantio.createTime,
                lastLoginTime:merchantio.lastLoginTime,
                businessType:merchantio.businessType,
                businessTypeName:merchantio.businessTypeName,
                provinceId:merchantio.provinceCode,
                provinceName:merchantio.province,
                cityId:merchantio.cityCode,
                cityName:merchantio.city,
                districtId:merchantio.areaCode,
                districtName:merchantio.district,
                address:merchantio.address,
                channelCode:channelStr
            });
        }else {
            //游客
            // zhuge.identify('0',{
            //     name:"游客"
            // });
            webSdk.identify('0',{
                name:"游客"
            });
        }
        $(".sui-navbar").find("li").find("a").click(function () {
            var text = this.innerText;
            if (text){
                kuanjie(text,'pc_top_click');
            }
        });
        $("#topnav").find("li").find("a").click(function() {
            var link = $(this);
            // zhuge.track('pc_Navigation_Menu', {
            //             'navigationName': $(link).text()
            //         },
            //         function() {
            //             location.href = $(link).attr('href'); //继续跳转到目标页面
            //         });
            webSdk.track('pc_Navigation_Menu', {
                        'navigationName': $(link).text()
                    },
                    function() {
                        location.href = $(link).attr('href'); //继续跳转到目标页面
                    });
            return false;
        });
        $(".xfubox").find("ul").find("li").find("a").click(function() {
            var link = $(this);
            // zhuge.track('pc_Navigation_Drugclassification', {
            //             'className': $(link).text()
            //         },
            //         function() {
            //             location.href = $(link).attr('href'); //继续跳转到目标页面
            //         });
            webSdk.track('pc_Navigation_Drugclassification', {
                        'className': $(link).text()
                    },
                    function() {
                        location.href = $(link).attr('href'); //继续跳转到目标页面
                    });
            return false;
        });
        $("#hotWordDiv").find("a").click(function () {
            var name = this.text;
            // zhuge.track('pc_action_Search', {
            //     'searchTerms' : name,
            //     'source' : 3 //(1:输入2:历史3:推荐4:联想)
            // });
            webSdk.track('pc_action_Search', {
                'searchTerms' : name,
                'source' : 3 //(1:输入2:历史3:推荐4:联想)
            });
        });
        $(".kuanjie").find(".cycle2").click(function () {
            var text = this.innerText;
            kuanjie(text,'pc_right_kuanjie');
        });
        $(".kuanjie").find("a").find(".r-topbox").click(function () {
            var text = $(this).next().text();
            if (text){
                kuanjie(text,'pc_right_kuanjie');
            }
        });
    }

    function searchProduct(){
        var name = $.trim($("#search").val());
        if(name == null || name ==""){
//		name="阿莫西林胶囊";
        }else{
            var pathname=window.location.pathname;
            if(pathname=="/"){
                window.open("${ctx}/search/skuInfo.htm?keyword="+encodeURI(name),"_blank");
            }else{
                window.open("${ctx}/search/skuInfo.htm?keyword="+encodeURI(name),"_top");
            }
        }
    }

    function getUrlParam(name) {
        var urlArr = window.location.href.split('?');
        if (urlArr.length < 2) {
            return '';
        }
        var tempArr = urlArr[1].split('&');
        for (var i = 0; i < tempArr.length; i++) {
            var item = tempArr[i].split('=');
            if (item[0].trim() === name) {
                return decodeURI(item[1]);
            }
        }
        return '';
    }
    // 搜索埋点
    function searchTrack() {
        var name = $.trim($("#search").val());
        var merchantId = $('#merchantId').val();
        var sug = [];

        $('#searchUl').find('li a').each(function() {
            sug.push($(this).text());
        });
        webSdk.track('action_Search', {
            user_id: merchantId,
            sdk: 'sg-js',
            sptype: 1,
            spid: 2, // 1-大搜，2-店铺搜索，3-专区搜索
            wq: name,
            keyword: name,
            sug: '', // 如果用户未点击sug则此参数为空
            sg1: sug[0] || '',
            sg2: sug[1] || '',
            sg3: sug[2] || '',
            sg4: sug[3] || '',
            sg5: sug[4] || '',
            pkw: getUrlParam('keyword'),
        });
    }

    function searchOrgProduct(){
        var name = $.trim($("#search").val());

        searchTrack();

        if(name == null || name ==""){
//		name="阿莫西林胶囊";
        }else{
            var pathname=window.location.pathname;
            if(pathname=="/"){
                window.open("${ctx}/company/center/companyInfo/shopSkuInfo.htm?orgId=${orgId}&keyword="+encodeURI(name),"_blank");
            }else{
                window.open("${ctx}/company/center/companyInfo/shopSkuInfo.htm?orgId=${orgId}&keyword="+encodeURI(name),"_top");
            }
        }
    }
    function checkSearchText(text, keyword) {
        let index = 0;
        let newStr = "";
        index = text.indexOf(keyword);
        console.log(text, keyword, "店铺搜索");
        if (index > -1) {
            text = text.split(keyword);
            newStr = text[0] +"<span style='color: #00b955;'>" + keyword + "</span>" + text[1];
        } else {
            newStr = text;
        }
        return newStr;
    }
    function loadProductNameByAutoComplete(name){
        url="${ctx}/search/autoComplate.json?orgId=${orgId}&showName="+encodeURI(name);
        var html = "";
        if(name == null || name.trim() ==""){
        }else{
            name = name.trim();
            $.ajax({
                type : "GET",
                url: url,
                async:true,
                dataType: "json",
                success: function(data){
                    $.each(data.showNameList, function (index, entry) {
                        //var  name = entry.showName;
                        html += "<li><a onclick='searchTrack()' href='${ctx}/company/center/companyInfo/shopSkuInfo.htm?orgId=${orgId}&keyword="+encodeURI(entry.showName)+"' target='_self'><span class='t9'>"+checkSearchText(entry.showName, name)+"</span></a></li>";
                    });
                    if(data.showNameList && data.showNameList.length>0){
                        $("#searchUl").html(html);
                        $('#searchUl').css("display","block");
                    }else{
                        $('#searchUl').css("display","none");
                    }
                }
            });
        }
    }

    // 热词埋点
    function hotKeywordTrack(index) {
        // var merchantId = $('#merchantId').val();
        webSdk.track('action_Search', {
            sdk: 'sg-js',
            sptype: 1,
            spid: 2, // 1-大搜，2-店铺搜索，3-专区搜索
            sug: 'hotword_' + index,
            keyword: $("#search").val().trim()
        });
    }
    // 判断是否IE
    function IEVersion() {
        var isIEBrowser = false;
        if (!!window.ActiveXObject || "ActiveXObject" in window || (window.navigator && window.navigator.msSaveOrOpenBlob)) {
            isIEBrowser = true;
        } else {
            isIEBrowser = false;
        }
        // var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        // var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
        // var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
        // var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
        // if(isIE) {
        //     isIEBrowser = true;
        // } else if(isEdge) {
        //     isIEBrowser = true;
        // } else if(isIE11) {
        //     isIEBrowser = true;
        // }else{
        //     isIEBrowser = false;//不是ie浏览器
        // }
        if (isIEBrowser) {
            var IEHtml = '<div style="width: 1200px;height: 35px;margin: 0 auto;padding: 0 20px;line-height: 35px;background: #FFD793;">'
               + '<span style="font-size: 16px;color: #505050;">您当前使用的IE浏览器存在兼容性问题，无法体验完整服务，请点击此处查看解决方法</span>'
               + '<a href="/static/word/浏览器兼容方法说明.docx" style="font-size: 16px;color: #505050;margin-left: 20px;text-decoration:underline;cursor:pointer">下载</a>'
               + '</div>';

            $("#IETip").html(IEHtml);
        }
    }

    var pressKeyInt = 0;
    /*搜索弹窗响应键盘事件*/
    $("#search").unbind("keyup");
    $("#search").keyup(function (event) {
        console.log("2");
        var e = event || window.event;
        var k = e.keyCode || e.which;

        if(k == 38 || k == 37 || k == 39 || k == 40){
        }else{
            var name = $("#search").val();
            loadProductNameByAutoComplete(name);
        }
        $('#searchUl').css("display","block");
        switch (k) {
            case 38:
                pressKeyInt--;
                var tmp=$('#searchUl li').length-1;
                if (pressKeyInt <0) {
                    pressKeyInt =tmp;
                }
                $('#searchUl li').eq(pressKeyInt-1).addClass('active').siblings().removeClass('active');
                $("#search").val($('#searchUl li').eq(pressKeyInt-1).text());
                break;
            case 40:
                pressKeyInt++;
                var tmp=$('#searchUl li').length-1;
                if (pressKeyInt >tmp) {
                    pressKeyInt =0;
                }
                $('#searchUl li').eq(pressKeyInt-1).addClass('active').siblings().removeClass('active');
                $("#search").val($('#searchUl li').eq(pressKeyInt-1).text());
                break;
            case 13:
                // searchProduct();
                searchOrgProduct();
                console.log("回车");
                break;

        }
    });

    /*点击搜索下拉框事件*/
    $('#searchUl li').click(function(){
        $("#search").val($(this).text());
        $(".searchUl").hide();
    });


    /*隐藏搜索弹窗*/
    $("body").click(function (e) {
        if (!$(e.target).closest(".searchUl").length) {
            $(".searchUl").hide();
        }
    });

    /*显示类目*/
    $(".goodsmain .fltitle,.xfubox .flbox").hover(function(){
        $(".xfubox .flbox").css("display","block");
    },function(){
        $(".xfubox .flbox").css("display","none");
    })


    /*去掉分类高亮*/
    $(".xfubox").hover(function(){},function(){$(".newul li").removeClass("cur");});

    /*导航添加底部横线*/
    // $('.topnav li').hover(function(){$(this).addClass("hovercur").siblings().removeClass("hovercur")},function(){$(this).removeClass("hovercur")});



    /*回到顶部*/
    $('#toTop').click(function() {
        $('html,body').animate({
            scrollTop: '0px'
        }, 'slow');
    });

    /*导航切换样式*/
    $('.topnav  li a').click(function(){
        $(this).addClass("cur").parent("li").siblings().find("a").removeClass("cur");
    });

    $('input, textarea').placeholder();

    $(".app,.erm-abs").hover(function(){
        $(".erm-abs").css("left","-108px");
    },function(){
        $(".erm-abs").css("left","0");
    });

    /*心愿单*/
    var cb=(function(){
        var xyd_timer,xyd_timeout=600;
        $(".xyd-pos-init,.xyd-pos-leftbox").hover(function(){
            if(!xyd_timer){
                $(".xyd-pos-leftbox").fadeIn();
            }else{
                xyd_timer = null;
            }
        },function(){
            xyd_timer=setTimeout(function(){
                $(".xyd-pos-leftbox").hide();
                xyd_timer = null;
            },xyd_timeout);
        });
    })();

    function addCookie(){
        var url = window.location;
        var title = document.title;
        if (document.all){
            window.external.addFavorite(url,title);
        } else if (window.sidebar){
            window.sidebar.addPanel(title, url, "");
        } else {
            alert("对不起，您的浏览器不支持此操作!\n请您使用菜单栏或Ctrl+D收藏本站。");
        }
    }
    var categoryData;
    var companyDetailData;

    // function callKf(){
    //     window.open('/custom/kit.htm', 'webcall', 'toolbar=no, status=no,scrollbars=0,resizable=0,menubar＝0,location=0,width=700,height=680');
    // };
    function callKf(id,userId,orgId,isThirdCompany){
        var url = '';
        if(url == ''){
            var path = isThirdCompany ? "/custom/getIMPackUrl?isThirdCompany="+isThirdCompany : "/custom/getIMPackUrl";
            $.ajax({
                url: path,
                type: "GET",
                async: false,
                cache: false,
                dataType: "json",
                success: function(data) {
                    if(data.status == "success"){
                        //url = data.data.IM_PACK_URL
                        if(isThirdCompany){ //pop客服入口
                            var merchantName = $(".zy-title").text();
                            if(userId){
                                if(id){
                                    url = data.data.IM_PACK_URL+'&orderNo='+id+'&userid='+userId+'&merchantCode='+orgId+'&pop=1&ws=1&sc=1000&portalType=1&merchantName='+merchantName;
                                }else {
                                    url = data.data.IM_PACK_URL+'&userid='+userId+'&merchantCode='+orgId+'&pop=1&ws=1&sc=1000&portalType=1&merchantName='+merchantName;
                                }
                            }else {
                                window.location.href="/login/login.htm";
                            }
                        }else{//自营客服入口
                            if(userId){
                                if(id){
                                    url = data.data.IM_PACK_URL+'&orderNo='+id+'&userid='+userId+'&sc=1000&portalType=2';
                                }else {
                                    url = data.data.IM_PACK_URL+'&userid='+userId+'&sc=1000&portalType=1';
                                }
                            }else {
                                url = data.data.IM_PACK_URL+'&sc=1000&portalType=1';
                            }
                        }

                    }
                }
            });
        }
        if(url){
            if(isThirdCompany) { //pop客服入口
                window.open(url, '_blank', 'webcall', 'toolbar=no, status=no,scrollbars=0,resizable=0,menubar＝0,location=0,width=680,height=680');
            }else{
                window.open(url, 'webcall', 'toolbar=no, status=no,scrollbars=0,resizable=0,menubar＝0,location=0,width=680,height=680');
            }
        }else {
            alert("未连接到在线客服，请联系管理员");
        }
    }

    /**
     *  初始化商家信息
     */
    function initShopInfo() {
        var orgId = '${orgId}';
        $.ajax({
            url:'/company/center/companyInfo/getPopCompanyDetail.json',
            data:{orgId:orgId},
            dataType:'json',
            success:function (result) {
                var $status = result['status'];
                if ($status && $status == 'failure') {
                    var $errorMsg = result['errorMsg'];
                    if (!$errorMsg) {
                        $errorMsg = '网络异常，请稍后重试！';
                    }
                    $.alert({
                       title:'提示',
                        body:$errorMsg,
                        timeout:1000,
                        hidden:function(e){
                           window.location.href="/";
                        }
                    });
                    return;
                }
                companyDetailData = result.data;
                if(companyDetailData.orgName == null){
                    $.alert({
                       title:'提示',
                        body:'该店铺不存在',
                        timeout:1000,
                        hidden:function(e){
                           window.location.href="/";
                        }
                    });
                    return;
                }
                var html = '<a href="javascript:;" class="fl"><img src="'+companyDetailData.orgLogo+'" alt="" /></a>';
                    html +='<div class="zy-title">'+companyDetailData.orgName+'</div>';
                    html +='<div class="zy-shuliang"><div class="sl-box1">上架'+companyDetailData.upSkuNum+'种</div>';
                    html +='<div class="sl-box2">销量';
                    if(companyDetailData.saleSkuNum > 10000){
                        html += (companyDetailData.saleSkuNum/10000).toFixed(2);
                        html += '万';
                    }else {
                        html += companyDetailData.saleSkuNum;
                    }
                    html += '件</div>';
                    $('.col1').html(html);
            }
        });
    }

    /**
     * 初始化店铺分类数据
     */
    function initCategory() {
        var orgId = '${orgId}';
        $.ajax({
            url:'/company/center/companyInfo/getPopCategory.json',
            data:{orgId:orgId},
            dataType:'json',
            success:function (result) {
                categoryData = result.data;
                initFirstCategory(categoryData);
                initSecondCategory(categoryData);
            }
        });
    }


    /**
     * 组装一级分类
     * @param categoryData
     */
    function initFirstCategory(categoryData) {
        var html = '<ul class="newul">';
        for(var i = 0 ; i < categoryData.length; i ++){
            var category = categoryData[i];
            if(category.id == 1){
                html += '<li><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryFirstId=1&orgId=${orgId}" class="lia1" target="_blank"><i class="icon iconfont icon-zhongxichengyao"></i><span>中西成药</span></a></li>';
            }
            else if(category.id == 225){
                html += '<li><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryFirstId=225&orgId=${orgId}" class="lia2" target="_blank"><i class="icon iconfont1 icon-zhongyao"></i><span>中药养生</span></a></li>';
            }
            else if(category.id == 3){
                html += '<li><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryFirstId=3&orgId=${orgId}" class="lia3" target="_blank"><i class="icon iconfont icon-yiliaoqixie"></i><span>医疗器械</span></a></li>';
            }
            else if(category.id == 65){
                html += '<li><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryFirstId=65&orgId=${orgId}" class="lia4" target="_blank"><i class="icon iconfont icon-jishengyongpin"></i><span>计生用品</span></a></li>';
            }
            else if(category.id == 66){
                html += '<li><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryFirstId=66&orgId=${orgId}" class="lia5" target="_blank"><i class="icon iconfont icon-xiaoduyongpin"></i><span>消毒用品</span></a></li>';
            }
            else if(category.id == 67){
                html += '<li><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryFirstId=67&orgId=${orgId}" class="lia6" target="_blank"><i class="icon iconfont icon-gerenhuli"></i><span>个人护理</span></a></li>';
            }
            else if(category.id == 68){
                html += '<li><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryFirstId=68&orgId=${orgId}" class="lia7" target="_blank"><i class="icon iconfont icon-shipinbaojian"></i><span>食品保健</span></a></li>';
            }
            else if(category.id == 69){
                html += '<li><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryFirstId=69&orgId=${orgId}" class="lia8" target="_blank"><i class="icon iconfont icon-zhusheyongpin"></i><span>注射用药</span></a></li>';
            }else{
                html += '<li><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryFirstId='+category.id+'&orgId=${orgId}" class="lia1" target="_blank">' ;

                if(category.icon !='unknown' && category.icon != null && category.icon !=''){
                    html += '<img src="'+category.icon+'" style="width:21px;height:21px;;float: left;margin-top: 16px;margin-right: 10px;" />';
                }
                html +='<span>'+category.name+'</span></a></li>';
            }
        }
        html += '</ul>';
        $('#firstBox').html(html);
    }

    /**
     * 初始化二级分类
     * @param categoryData
     */
    function initSecondCategory(categoryData) {
        var html = '';
        for(var i = 0; i< categoryData.length; i++ ){
            var category = categoryData[i];
            var categoryChildren = category.children;
            html += '<li class="warp-li">';
            if(categoryChildren != undefined && categoryChildren.length > 0){
                for(var j = 0 ; j < categoryChildren.length; j++){
                    var twoLevelCategory = categoryChildren[j];
                    if(j == 0 || j%3 == 0) {
                        html += '<div class="hangbox">';
                    }
                    html += '<div class="hang-col'+(j%3 + 1)+'">';
                    var twolevelChildren = twoLevelCategory.children;
                    if(twolevelChildren != undefined && twolevelChildren.length > 0){
                        html += '<div class="com-title"><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categorySecondId='+twoLevelCategory.id+'&orgId=${orgId}" target="_blank">'+twoLevelCategory.name+'</a></div>';
                        html += '<div class="com-info">';

                        var lineStrLength = 0;
                        for(var k = 0 ; k < twolevelChildren.length; k++){
                            var threeLevelCategory = twolevelChildren[k];
                            lineStrLength += threeLevelCategory.name.length + 1;

                            if(lineStrLength > 22 || k == twolevelChildren.length - 1){
                                lineStrLength = 0;
                                html += '<a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryThirdId='+threeLevelCategory.id+'&orgId=${orgId}" target="_blank" class="last-no-line">'+threeLevelCategory.name+'</a>';
                            }else{
                                if(k < twolevelChildren.length - 1){
                                    var nextLineStrLength = lineStrLength + twolevelChildren[k+1].name.length;
                                    if(nextLineStrLength > 23){
                                        lineStrLength = 0;
                                        nextLineStrLength = 0;
                                        html += '<a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryThirdId='+threeLevelCategory.id+'&orgId=${orgId}" target="_blank" class="last-no-line">'+threeLevelCategory.name+'</a>';
                                    }else {
                                        html += '<a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categoryThirdId='+threeLevelCategory.id+'&orgId=${orgId}" target="_blank">'+threeLevelCategory.name+'</a>';
                                    }
                                }
                            }
                        }
                        html += '</div>';
                    }else {
                        html += '<div class="com-title no-three-fl"><a href="${ctx }/company/center/companyInfo/shopSkuInfo.htm?categorySecondId='+twoLevelCategory.id+'&orgId=${orgId}" target="_blank">'+twoLevelCategory.name+'</a></div>';
                    }
                    html += '</div>';
                    if(j == categoryChildren.length - 1 || (j>0 && (j+1)%3 == 0)){
                        html += '</div>';
                    }
                }
            }
            html += '</li>';
        }
        $('.two-box-ul').html(html);
        setTimeout(function () {
            $(".xfubox .flbox .newul,.leimu-TC").hover(function(){
                $(".leimu-TC").css("display","block");
                $(".xfubox .flbox").css("display","block");
            },function(){
                $(".leimu-TC").css("display","none");
                $(".xfubox .flbox").css("display","none");
            })

            /*切换显示*/
            $('.newul li').hover(function(){
                $(".two-box-ul .warp-li").eq($(this).index()).addClass("show").siblings().removeClass('show');
                $(this).addClass("cur").siblings().removeClass("cur");
            });
        },10)
    }

</script>


      