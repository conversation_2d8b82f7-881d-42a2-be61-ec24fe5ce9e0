$(function(){
    window.uploadImages = []
    $('#refuseRefound').click(function () {
        var refundId =  $(this).data('id');
        var content = '<style>.el-popper.el-cascader__dropdown,.el-select-dropdown.el-popper,.el-loading-mask.is-fullscreen,.el-message-box__wrapper{z-index: 99!important;}</style>' +
        `<div id="addUserDataNew" style="padding:15px;">
           <el-form :model="form" ref="refForm" label-width="120px"  :rules="rules">
              <el-form-item label="拒绝退款原因" prop="rejectReason">
                <el-radio-group v-model="form.rejectReason">
                    <el-radio
                    v-for="(item, index) in reasons"
                    :key="index+1"
                    :label="item"
                >
                    {{ item }}
                </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="补充说明">
                <el-input
                    v-model="form.remarks"
                    type="textarea"
                    maxlength="50"
                    show-word-limit
                />
              </el-form-item>
              <el-form-item label="上传凭证（最多三张）">
                <input class="refundImageUpload" type="file" name="images" id="images" multiple="multiple">
                <div id="showImages" style="display: flex"></div>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleRefuseRefound" :loading="loading">确定</el-button>
                <el-button @click="close">返回</el-button>
              </el-form-item>
            </el-form>
            </div>`
        layer.open({
            title: '拒绝退款'
            , content: content
            , skin: 'add-box'
            , area: ['80%', '500px']
            , type:1
            , success: function (dom) {
                $(dom).ready(function () {
                    setTimeout(function(){
                        addUserDataNew_VUE(layer, refundId)
                    },50)
                })
            }, end: function() {
                window.uploadImages = [];
            }
        })
    })

    $(document).on('change', 'input.refundImageUpload', function (e) {
        var merchant = $('#merchantId').val();
        console.log(232323232323, window.uploadImages, e.target.files)
        if(window.uploadImages.length + e.target.files.length > 3){
            layer.alert('最多只能添加三张图片'); 
            return false; 
        }
        var file = e.target.files[0]; //获取图片资源
        var fileTypes = ["jpg", "png", "jpeg"];
        var bTypeMatch = false;
        for (var i = 0; i < fileTypes.length; i++) {
            var start = file.name.lastIndexOf(".");
            var fileType = file.name.substring(start + 1);
            if (fileType.toLowerCase() == fileTypes[i]) {
                bTypeMatch = true;
                break;
            }
        }
        if (bTypeMatch) {
            if (file.size <= 1024 * 1024 * 2) {
                var data = new FormData();
                data.append("file", $("#images")[0].files[0]);
                data.append("uploadPath", '/ybm/pc/order/' + merchant);
                data.append("targetFileName", '');
                var xhr = new XMLHttpRequest();
                xhr.withCredentials = true;
                xhr.addEventListener("readystatechange", function () {
                    if (this.readyState === 4) {
                        layer.close(layerIndex);
                        var res = JSON.parse(this.responseText)
                        if (res.status === 'success') {
                            window.uploadImages = window.uploadImages.concat(res.filePaths || []);
                            var str= '';
                            for ( var i = 0; i < window.uploadImages.length; i++) {
                                str += "<img style='width: 60px;height: 60px;display: block;margin-right: 10px;' src="+window.uploadImages[i].absoluteFilePath+" alt=''/>"
                            }
                            $('#showImages').html(str);
                        } else {
                            layer.alert('获取图片失败')
                        }
                    }
                });
                xhr.open("POST", "/merchant/center/uploadFile/invoke");
                xhr.send(data);
                //eg1
                var layerIndex = layer.load();
            } else {
                layer.msg('仅支持不超过2M的图片')
            }
        } else {
            layer.msg('仅限jpg，png，jpeg图片格式');
        }
     })

    function addUserDataNew_VUE(layer, refundId){
        // console.log(data,"addUserDataNew_VUE")
        new Vue({
            el:"#addUserDataNew",
            data: function () {
                return {
                    form: {
                        rejectReason: '',
                        images: [],
                        remarks: '',
                    },
                    rules:{
                        rejectReason:[
                            {
                                required: true, message: '请选择退款原因', trigger: 'change'
                            }
                        ],
                    } ,
                    reasons: [],
                    loading:false,
                    isCanSubmit:true
                };
            },
            created:function(){
                var that = this;
                $.ajax({
                    type: "get",
                    url: '/merchant/center/order/listAuditRejectReasons',
                    data: {},
                    cache: false,
                    async: false,
                    dataType: "json",
                    success: function (res) {
                        that.reasons = res.data.list
                    }
                });
            },
            mounted: () => {
                if (window.uploadImages && window.uploadImages.length) {
                    var str= '';
                    for ( var i = 0; i < window.uploadImages.length; i++) {
                        str += "<img style='width: 60px;height: 60px;display: block;margin-right: 10px;' src="+window.uploadImages[i].absoluteFilePath+" alt=''/>"
                    }
                    $('#showImages').html(str);
                }      
            },
            methods: {
                handleRemove(file, fileList, idStr) {
                    fileList.forEach((item, index) => {
                        if (item.name === file.name) {
                        fileList.splice(index, 1);
                        }
                    });
                    // if (fileList.length < 1) {
                    //   this.$set(this.mergeData, `${idStr}imgList`, '');
                    // }
                    if (idStr) {
                        this.goodsBasicComVo[idStr].isUpload = false;
                    }
                },
                uploadImg(file) {
                    var merchant = $('#merchantId').val();
                    var that = this;
                    var data = new FormData();
                    // var fileElementId = "el-upload__input";
                    $('.el-upload__input').attr({ id: 'eleupload'})
                    // data.append("file", file.file);
                    // data.append("uploadPath", '/ybm/pc/order/' + merchant);
                    // data.append("targetFileName", '');
                    $('.el-upload__input').val()
                    $.ajaxFileUpload({
                        url: '/merchant/center/uploadFile/invoke',
                        secureuri: false, //是否需要安全协议，一般设置为false
                        fileElementId: 'eleupload',
                        data: {uploadPath: '/ybm/pc/order/' + merchant, targetFileName: ''},
                        dataType: "json",
                        success: function (res) {
                            that.form.images.push({
                                name: file.fileName,
                                url: res.filePaths ? res.filePaths[0].relativeFilePath : '',
                                images: res.filePaths ? res.filePaths[0].relativeFilePath : '',
                                uid: file.file.uid,
                                
                            });
                        }
                    });
                },
                beforeAvatarUpload(file) {
                    console.log(file);
                    const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
                    const isLt2M = file.size / 1024 / 1024 < 2;
                
                    if (!isJPG) {
                        this.$message.error('上传图片只能是 JPG、PNG 格式!');
                        return false;
                    }
                    if (!isLt2M) {
                        this.$message.error('上传图片大小不能超过 2MB!');
                        return false;
                    }
                    return isJPG && isLt2M;
                },
                close(){
                    layer.closeAll();
                    window.uploadImages = [];
                },
                handleRefuseRefound(e){
                    var that = this;
                    this.$refs.refForm.validate((valid) => {
                        if (valid) {
                            var images = [];
                            for (var i = 0; i < window.uploadImages.length; i++) {
                                images.push(window.uploadImages[i].relativeFilePath)
                            }
                            images = images.join(',');
                            $.ajax({
                                url: '/merchant/center/order/auditOrderRefund',
                                type: "get",
                                dataType: "json",
                                traditional: true,
                                async: false,
                                data: {
                                    refundId: refundId,
                                    auditResult: 2,
                                    rejectReason: that.form.rejectReason,
                                    images: images,
                                    remarks: that.form.remarks,
                                },
                                success: function (res) {
                                    layer.closeAll();
                                    window.uploadImages = [];
                                    if (res.success) {
                                        location.reload();
                                    } else {
                                        alert(res.msg);
                                    }
                                    // location.reload();
                                }
                            });
                        }
                    });
                },
            }
        });
      }

    $('#agreeRefound').click(function () {
        var refundId =  $(this).data('id');
        $.confirm({
            title: '提示',
            body:'确认同意退款吗？',
            okBtn : '确定',
            cancelBtn : '我再想想',
            okHidden : function(e){
                $.ajax({
                    url: '/merchant/center/order/auditOrderRefund',
                    type: "get",
                    dataType: "json",
                    traditional: true,
                    async: false,
                    data: {
                        refundId: refundId,
                        auditResult: 1,
                    },
                    success: function (res) {
                        if (res.success) {
                            location.reload();
                        } else {
                            alert(res.msg);
                        }
                        // location.reload();
                    }
                });
            }
        });
    })
    $('.hasphoto').hover(function(){
        $(".wishshade").toggle();
        $(".del-btn").toggle();
    });
    $(".myreordereasonphoto input[type='file']").each(function(){
        var id = $(this).attr("id");
        var imgId = id.substr(id.indexOf("_")+1);
        $(this).parent().find('img').attr('id', imgId);
        $(this).uploadPreview({
            Img: id = imgId, Width: 100, Height: 100, Callback: function () {

            }
        });
    });
    //提交申请成功 弹框
    $('.reordereason-subtn').on('click', function(e){
        if($("#myreordereason-amount-num").val() <= 0){
            $.alert({"title":"提示","body":"您没有可退款的金额，请重新刷新退款商品列表！"});
            return false;
        }

        var submitFlag = false;
        $.ajax({
            type: "GET",
            url: ctx + "/merchant/center/order/findOrderStatus",
            data: {"orderId": $('#orderId').val()},
            dataType: "json",
            async: false,
            success: function (data) {
                if (data.status === "success") {
                    if(data.order && data.order.status && data.order.status === 7){
                        $.alert({
                            title:"提示",
                            body:'当前订单正在出库中,若需退款请联系客服人员！'
                        });
                    }else{
                        $.alert({
                            title:"提示",
                            body:'<div style="text-align: center;"><i class="sui-icon icon-tb-roundcheckfill"></i><p>提交成功</p><br /><p>您的申请已提交成功，正在为您跳转查看退款页面！</p></div>',
                            okBtn:' ',
                            closeBtn:false,
                            hasfoot:false
                        });
                        layer.load(2);
                        submitFlag = true;
                    }
                } else {
                    $.alert({
                        title:"提示",
                        body:data.errorMsg
                    });
                }
            }
        });
        // 判断上传图片大小
        if(!image_judge(5)){
            return;
        }
        if(submitFlag){
            $(this).parents("form").submit();
        }
    });
    //信息的校验
    var alert = $.alert

    //必填信息全部填写返回true
    function check_page() {
        var required = $("input[required]")
        for (var i = 0; i < required.length; i++) {
            if (required[i].value.length === 0) {
                return false
            }
        }
        return true
    }

    //检查银行卡号和电话是否合法
    function check_right() {
        if($(".holder-phone")&& $(".holder-phone").length &&$(".card-num") && $(".card-num").length) {
            var cart_len = "";
            for (var i = 0; i < $(".card-num").length; i++) {
                cart_len = cart_len + $(".card-num")[i].value
            }
            if ($(".holder-phone").val().length >= 5 && $(".holder-phone").val().length <= 13 && !isNaN($(".holder-phone").val())) {
                return true
            } else {
                return false
            }
            // if ($(".holder-phone").val().length >= 5 && $(".holder-phone").val().length <= 13 && cart_len.length >= 16 && cart_len.length <= 19 && !isNaN($(".holder-phone").val())) {
            //     return true
            // } else {
            //     return false
            // }
        }else{
            return true;
        }
    }

    //页面加载完成后提交按纽处理
    if (check_page()) {
        $("#btn").removeClass("btn_style")
    } else {
        $("#btn").addClass("btn_style")
    }

    //提交按钮显示情况
    $("input").on("change", function () {
        if (check_page()) {
            $("#btn").removeClass("btn_style")
        } else {
            $("#btn").addClass("btn_style")
        }
    });

    $("input").on("blur", function () {
        if (check_page()) {
            $("#btn").removeClass("btn_style")
        } else {
            $("#btn").addClass("btn_style")
        }
    })

    //开户银行校验
    $(".bank-msg").on("input", function () {
        $(this).trigger('change');
    });

    //银行卡校验
    $(".card-num").on("input", function () {
        // var self = $(this)
        // if (self.val().length > 5) {
        //     self.val(self.val().slice(0, 5))
        //     self.next().focus()
        // }
        $(this).trigger('change');
    });
    //开户人校验
    $(".holder").on("input", function () {
        $(this).trigger('change');
    });
    //开户人电话校验最长 +86185****8595 14位
    $(".holder-phone").on("input", function () {
        $(this).trigger('change');
    });

    //未填写信息
    $(".tips").hide()
    $("#btn").click(function () {
        var is_required = check_page();

        if (!is_required) {
            $(".tips").show()
            setTimeout(function () {
                $(".tips").hide()
            }, 2000)
            return
        }


        if ($('.refundTips').hasClass('required')) {
            var value = $("textarea[name='refundExplain']").val()
            if (!value) {
                alert({
                    body: '请填写相关信息！',
                    title: '  <i class="sui-icon icon-pc-info-circle"></i>提示',
                    okBtn: '确认',
                });
                return
            }
        }
        console.log('submit!!!!')
        // 填写信息了,但是不合法
        if (!check_right()) {
            var cart_len = "";
            for (var i = 0; i < $(".card-num").length; i++) {
                cart_len = cart_len + $(".card-num")[i].value
            }
            if (!($(".holder-phone").val().length >= 5 && $(".holder-phone").val().length <= 13 && !isNaN($(".holder-phone").val()))){
                alert({
                    body: '联系电话输入不合法，请检查',
                    title: '  <i class="sui-icon icon-pc-info-circle"></i>提示',
                    okBtn: '确认',
                });
            }
            // if(!(cart_len.length >= 16 && cart_len.length <= 19) && !($(".holder-phone").val().length >= 5 && $(".holder-phone").val().length <= 13 && !isNaN($(".holder-phone").val()))){
            //     alert({
            //         body: '银行卡号/联系电话输入不合法，请检查',
            //         title: '  <i class="sui-icon icon-pc-info-circle"></i>提示',
            //         okBtn: '确认',
            //     });
            // }else{
            //     if(!(cart_len.length >= 16 && cart_len.length <= 19)){
            //         alert({
            //             body: '银行卡号输入不合法，请检查',
            //             title: '  <i class="sui-icon icon-pc-info-circle"></i>提示',
            //             okBtn: '确认',
            //         });
            //     }else if (!($(".holder-phone").val().length >= 5 && $(".holder-phone").val().length <= 13 && !isNaN($(".holder-phone").val()))){
            //         alert({
            //             body: '联系电话输入不合法，请检查',
            //             title: '  <i class="sui-icon icon-pc-info-circle"></i>提示',
            //             okBtn: '确认',
            //         });
            //     }
            // }
            return;
        }
        const afterSalesType = $('#afterSalesTypeInput').val();
        const freightRefund = $('#freightRefund').val();
    
        if (!afterSalesType || !freightRefund) {
            alert({"title":"提示","body":"请先选择售后类型和运费退款信息！"});
            return false; // 阻止提交
        }
        var invoiceType = $("#invoice-type").val();
        if (invoiceType && (invoiceType == 2 || invoiceType == 3)) {
            $.alert({"title":"提示","body":"订单为纸质发票，请将发票随退货一并寄回"});
        }

        const orderId = $('#orderId').val();
        const str = $('input[name="str"]').val();

        $.ajax({
            url: '/merchant/center/order/refundCheckWithPlatformIn',
            type: 'POST',
            data: {
                orderId: orderId,
                str: str
            },
            dataType: 'json',
            success: function (res) {
                if (res && res.code === 9999) {
                    $.alert({
                        title: '提示',
                        body: res.errorMsg || '操作受限，请查看提示信息',
                        okBtn: '确认',
                        onOk: function () {
                            const redirectUrl = res.data && res.data.URL;
                            if (redirectUrl) {
                                window.location.href = redirectUrl;
                            }
                        }
                    });
                    return;
                } else {
                    $('#subApplyRefund').submit();
                }
            },
            error: function (xhr, status, error) {
                console.error('refundCheckWithPlatformIn 接口调用异常:', error);
                // $('#subApplyRefund').submit();
            }
        });
    })

    $('.refundReason').focus(function () {
        $('.refundReasonOption').show()
    })
    let refundChangeText = ''
    $('.refundReasonOption li').click(function (e) {
        // if ($(this).hasClass('reasonLi')){
        //     return false
        // }
        e.stopPropagation();
        var value = $(this).attr('value')
        console.log(value)
        $('.refundTips').removeClass('required')
        if (value === '其他') {
            $('.refundTips').addClass('required')
        }
        $('.refundReasonOption li').removeClass('selected')
        $(this).addClass('selected')
        $('.refundReason').val(value)
        $('.refundReasonOption').hide();
        const uploadPlusTips = $(this).data('uploadplustips')
        if(refundChangeText !== value) {
            changeTKReason(value,uploadPlusTips)
        }
        if (value === '商品实物与展示不符') {
            $('.refundReasonTip').html('商品实物图片、交易快照图片');
        } else if (value === '破损或质量问题') {
            $('.refundReasonTip').html('破损请提供快递面单图片、快递外箱图片/视频、破损产品图片；质量问题请提供问题实物图片、实物批号图片');
        } else if (value === '商品错漏发') {
            $('.refundReasonTip').html('错发请提供错发实物图片、实物批号图片');
        } else {
            $('.refundReasonTip').html('')
        }
        refundChangeText = value
    })
    // 当售后类型可选时，先隐藏退款原因
    if(!$('#afterSalesTypeInput').val() || $('#afterSalesTypeInput').val() == '请先选择售后类型') {
        $('.refundReasonOption li').each(function() {
            $(this).hide()
        })
    }else {
        const refundTypeNumber = getRefundNumber($('#afterSalesTypeInput').val())
        $('#afterSalesTypeInput').val(refundTypeNumber)
    }

// 退款詳情倒計時
    var $expressCountDownTime = $('#expressCountDownTime')
    var downTime = 0
    var interval = null
    if (interval) {
        clearInterval(interval)
    }
    if ($expressCountDownTime.length > 0) {
        downTime = $expressCountDownTime.attr('data_time')
        if (downTime && downTime > 0) {
            interval = setInterval(function () {
                formatSeconds()
            }, 1000)
        } else {
            clearInterval(interval)
        }
    }

    function formatSeconds() {
        downTime = downTime - 1;
        var minite = Math.floor((downTime / 60) % 60);      //计算分
        var hour = Math.floor((downTime / 3600) % 24);      //计算小时
        var day = Math.floor((downTime / 3600) / 24);       //计算天
        var dayText = day > 10 ? day : '0' + day
        var hourText = hour > 10 ? hour : '0' + hour
        var miniteText = minite > 10 ? minite : '0' + minite
        $('#expressCountDownTime').html(dayText + '天' + hourText + '小时' + miniteText + '分钟')
    }
    function changeTKReason(value,uploadPlusTipsOrg) {
        let uploadPlusTips
        if(!uploadPlusTipsOrg) {
            uploadPlusTips = []
        }else {
            uploadPlusTips = Array.isArray(uploadPlusTipsOrg) ? uploadPlusTipsOrg : JSON.parse(uploadPlusTipsOrg || '[]')
        }
        
        let voucherHtml = ''
        
        if(uploadPlusTips.length > 0) {
            $('#otherVoucher').remove()
            $('#specifyVoucher').remove()
            // 根据 uploadPlusTips 生成指定凭证区域
            let specifyVoucherItems = ''
            let currentIndex = 1
            
            if(uploadPlusTips && uploadPlusTips.length > 0) {
                uploadPlusTips.forEach(tip => {
                    const endIndex = currentIndex + tip.quantity - 1
                    specifyVoucherItems += `
                        <div style="display:flex;margin-bottom:15px;position: relative;">
                            <label style="width:120px;flex-shrink:0;">
                                ${tip.certificate}：
                                <span style="display: block;font-size: 12px;color: red;">${tip.exampleTest || ''}</span>
                            </label>
                            <div style="display:flex;flex-wrap: wrap;gap: 15px;flex:1;">
                                ${generateUploadItems(currentIndex, endIndex, 'myreordereasonphoto-itemSpec',tip.type + 'PC',tip.exampleDiagram)}
                            </div>
                        </div>
                    `
                    currentIndex = endIndex + 1
                })
            }            
            // 生成 specifyVoucher 的 HTML
            voucherHtml = `
                <div class="control-group" id="specifyVoucher">
                    <label class="control-label" style="vertical-align: text-top">上传凭证：</label>
                    <input type="hidden" id="imgList" name="imgList" value="">
                    <div class="controls myreordereasonphoto">
                        ${specifyVoucherItems}
                        <div style="display:flex;margin-top:15px;">
                            <label style="width:120px;flex-shrink:0;">其他凭证：</label>
                            <div style="display:flex;flex-wrap: wrap;gap: 10px;flex:1;">
                                ${generateUploadItems(1, 9, 'myreordereasonphoto-item')}
                            </div>
                        </div>
                    </div>
                </div>
            `
        } else {
            if($('#otherVoucher').length > 0) {
                return
            }
            $('#specifyVoucher').remove()
            // 生成 otherVoucher 的 HTML
            voucherHtml = `
                <div class="control-group" id="otherVoucher">
                    <label class="control-label">上传凭证：</label>
                    <input type="hidden" id="imgList" name="imgList" value="">
                    <div class="controls myreordereasonphoto">
                        <!-- 生成9个普通凭证上传项 -->
                        ${generateUploadItems(1, 9, 'myreordereasonphoto-item')}
                    </div>
                </div>
            `
        }
        $('#positionLabel').after(voucherHtml).promise().done(() => {
            // DOM 插入完成后执行
            $(".myreordereasonphoto input[type='file']").each(function(){
                var id = $(this).attr("id");
                var imgId = id.substr(id.indexOf("_")+1);
                
                $(this).parent().find('img').attr('id', imgId);
                $(this).uploadPreview({
                    Img: id = imgId, Width: 100, Height: 100, Callback: function () {

                    }
                });
            });
            $(".myreordereasonphoto-itemSpec input[type='file']").each(function(){
                var id = $(this).attr("id");
                var imgId = id + 'img';
                $(this).parent().find('img').attr('id', imgId);
                $(this).uploadPreview({
                    Img: id = imgId, Width: 100, Height: 100, Callback: function () {
                    }
                });
            });
            $('.example-link').off('click').on('click', function() {
                const exampleUrl = $(this).data('example');
                if (exampleUrl) {
                    showExampleModal(exampleUrl);
                }
            });
        })
    }
    // 辅助函数：生成上传项
    function generateUploadItems(start, end, className,idName = '',exampleDiagram = "") {
        let items = ''
        for(let i = start; i <= end; i++) {
            // 图片预览需要
            const id = idName ? idName + i : 'upload_evidence' + i
            items += `
                <div class="${className} noadd" ${idName && i === start ? 'style="border:1px solid rgb(42, 124, 184);position: relative;"' : ''}>
                    <input 
                        class="addphoto-btn" type="file" 
                        name="${idName ? idName : 'evidence'}"
                        value="" 
                        id="${id}">
                    <img class="default-photo" src="/static/img/user/defaultwish.png">
                    <span class="del-btn">x</span>
                    ${idName && i === start ? `<div class="example-link" data-example="${exampleDiagram}" style="position: absolute;bottom: -17px;left: 28%;font-size: 12px;color: rgb(42, 124, 184);cursor: pointer;">查看示例</div>` : ''}
                </div>
            `
        }
        return items
    }
    function getRefundNumber(selectedRefundType) {
        const refundTypeMap = new Map();
        refundTypeMap.set('我要退款(无需退货)', 1);
        refundTypeMap.set('我要退货退款', 2);
        return refundTypeMap.get(selectedRefundType);
    }
    window.handleRefundTypeChange = function(selectedRefundType) {
        // 获取当前选择的售后类型对应的值
        const targetRefundType = getRefundNumber(selectedRefundType);
        $('#afterSalesTypeInput').val(targetRefundType)
        // 获取当前已选择的退款原因值
        const currentSelectedValue = $('.refundReason').val();
        
        // 处理所有退款原因选项（包括父级和子级）
        $('.refundReasonOption li').each(function() {
            const $this = $(this);
            const itemRefundType = $this.data('refundtype');
            if (itemRefundType === targetRefundType || itemRefundType === '') {
                $this.show();
            } else {
                $this.hide();
            }
        });
        
        // 检查当前选中的值是否还可见
        let isCurrentSelectionVisible = false;
        $('.refundReasonOption li:visible').each(function() {
            if ($(this).attr('value') === currentSelectedValue) {
                isCurrentSelectionVisible = true;
                return false; // 跳出循环
            }
        });
        
        // 如果当前选择的选项被过滤掉了，清空选择
        if (!isCurrentSelectionVisible && currentSelectedValue) {
            $('.refundReason').val('');
        }
    }
     // 点击查看示例
    function showExampleModal(imageUrl) {
        // 创建弹窗HTML
        const modalHtml = `
            <div id="exampleModal" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 9999;
                display: flex;
                justify-content: center;
                align-items: center;
            ">
                <div style="
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    max-width: 80%;
                    max-height: 80%;
                    position: relative;
                ">
                    <span id="closeModal" style="
                        position: absolute;
                        top: 10px;
                        right: 15px;
                        font-size: 24px;
                        cursor: pointer;
                        color: #999;
                    ">&times;</span>
                    <h3 style="margin-top: 0;">示例图片</h3>
                    <img src="${imageUrl}" style="
                        max-width: 100%;
                        max-height: 500px;
                        display: block;
                        margin: 0 auto;
                    " alt="示例图片">
                </div>
            </div>
        `;
        
        // 添加到页面
        $('body').append(modalHtml);
        
        // 绑定关闭事件
        $('#closeModal, #exampleModal').on('click', function(e) {
            if (e.target === this) {
                $('#exampleModal').remove();
            }
        });
    }
});


function image_judge(size_big) {

    /*获取图片内容对象*/
    var imgFile1 = document.getElementById("upload_evidence1").files[0];
    var imgFile2 = document.getElementById("upload_evidence2").files[0];
    var imgFile3 = document.getElementById("upload_evidence3").files[0];
    var imgFile4 = document.getElementById("upload_evidence4").files[0];
    var imgFile5 = document.getElementById("upload_evidence5").files[0];
    var imgFile6 = document.getElementById("upload_evidence6").files[0];
    var imgFile7 = document.getElementById("upload_evidence7").files[0];
    var imgFile8 = document.getElementById("upload_evidence8").files[0];
    var imgFile9 = document.getElementById("upload_evidence9").files[0];

    if(imgFile1 && imgFile1.name){
        /*图片大小*/
        if(imgFile1.size > (1024 * 1024 * size_big)) {
            $.alert("上传失败，请上传" + size_big + "M" + "以内的图片");
            return false;
        }
    }

    if(imgFile2 && imgFile2.name){
        /*图片大小*/
        if(imgFile2.size > (1024 * 1024 * size_big)) {
            $.alert("上传失败，请上传" + size_big + "M" + "以内的图片");
            return false;
        }
    }

    if(imgFile3 && imgFile3.name){
        /*图片大小*/
        if(imgFile3.size > (1024 * 1024 * size_big)) {
            $.alert("上传失败，请上传" + size_big + "M" + "以内的图片");
            return false;
        }
    }
    if(imgFile4 && imgFile4.name){
        /*图片大小*/
        if(imgFile4.size > (1024 * 1024 * size_big)) {
            $.alert("上传失败，请上传" + size_big + "M" + "以内的图片");
            return false;
        }
    }
    if(imgFile5 && imgFile5.name){
        /*图片大小*/
        if(imgFile5.size > (1024 * 1024 * size_big)) {
            $.alert("上传失败，请上传" + size_big + "M" + "以内的图片");
            return false;
        }
    }
    if(imgFile6 && imgFile6.name){
        /*图片大小*/
        if(imgFile6.size > (1024 * 1024 * size_big)) {
            $.alert("上传失败，请上传" + size_big + "M" + "以内的图片");
            return false;
        }
    }
    if(imgFile7 && imgFile7.name){
        /*图片大小*/
        if(imgFile7.size > (1024 * 1024 * size_big)) {
            $.alert("上传失败，请上传" + size_big + "M" + "以内的图片");
            return false;
        }
    }
    if(imgFile8 && imgFile8.name){
        /*图片大小*/
        if(imgFile8.size > (1024 * 1024 * size_big)) {
            $.alert("上传失败，请上传" + size_big + "M" + "以内的图片");
            return false;
        }
    }
    if(imgFile9 && imgFile9.name){
        /*图片大小*/
        if(imgFile9.size > (1024 * 1024 * size_big)) {
            $.alert("上传失败，请上传" + size_big + "M" + "以内的图片");
            return false;
        }
    }
    return true;

}