/**
 * @(#)CookieIdentityValidatorImpl.java Copyright 2011 jointown, Inc. All rights reserved.
 */
package com.xyy.ec.pc.service;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.core.util.DateUtil;
import com.xyy.ec.pc.base.KeepLoginStatusVerifier;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.PasswordVerifier;
import com.xyy.ec.pc.base.Principal;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.util.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Date;


/**
 * description
 *
 *
 * @version 1.0,2011-2-17
 */
public abstract class CookieIdentityValidator implements IdentityValidator {

    private static final Log LOG = LogFactory.getLog(CookieIdentityValidator.class);

    public abstract void setAuthenticationProvider(AuthenticationProvider authenticationProvider);


    protected abstract String getPrincipalcookieName();

    protected abstract String getvisitorCookieName();

    protected abstract String getDeviceIdCookieName();

    protected abstract String getBizLastLoginTimeCookieName();

    /**
     * 是否是单一客户端登录，即一个账户同时只能有一个客户端登录<br/>
     * 如果是单一客户端登录那么采用帐号的最后登录时间戳作为sha摘要<br/>
     * 否则时间戳会保留在每个客户端保证每个客户端都可登录<br/>
     *
     * @return
     *
     */
    protected abstract boolean singleClientLogin();

    /**
     * 多账户登录的最后时间戳cookie name
     *
     * @return
     *
     */
    protected abstract String getLastLoginTimeCookieName();

    /**
     * 自动登录cookie name
     */
    public static final String  AUTO_LOGIN_COOKIE_NAME  = "xyy";

    /**
     * 多账户登录的最后时间戳cookie name
     */
    // public static final String LAST_LOGIN_TIME="last_login_time";

    /**
     * 会话cookie 超时时间(以秒为单位)
     */
    private static final int    PRINCIPALCOOKIE_MAX_AGE = 60 * 60 * 24 * 30;
    /**
     * 持久cookie 超时时间(以秒为单位)
     */
    private static final int    VISITORCOOKIE_MAX_AGE   = 3600 * 24 * 30;
    private static final String SPLIT                   = "&";
    private static final String ENCODE                  = "utf-8";

    private static final int KEEPLOGINTIME = 3600 * 24 * 30;

    private String getSHAParam(Serializable id, Long lastLoginTime) {
        return id.toString() + lastLoginTime.toString();
    }

    /**
     * 通过调用CookieUtils和MD5Utils产生会话cookie
     *
     *
     * @param principal 用户对象
     * @return 创建的cookie
     */
    private Cookie createPrincipalCookie(Principal principal) {
        StringBuffer cookValue = new StringBuffer();
        Long lastLoginTime = principal.getLastLoginTime();
        String shaParam = getSHAParam(principal.getIdentity(), lastLoginTime);
        cookValue.append(principal.getIdentity().toString());
        cookValue.append(SPLIT + Base64Utils.urlEncoding(DigestUtils.shaHex(shaParam)));
        cookValue.append(SPLIT + (principal.getMechantId() != null ? principal.getMechantId() : ""));
        Cookie cookie = new Cookie(getPrincipalcookieName(), cookValue.toString());
        cookie.setPath("/");
        cookie.setMaxAge(PRINCIPALCOOKIE_MAX_AGE);
        return cookie;
    }

    /**
     * 废弃
     * @param time
     * @return
     */
    private Cookie createBizLastLoginTime(Long time) {
        if (StringUtil.isNotEmpty(getBizLastLoginTimeCookieName())) {
            Cookie cookie = new Cookie(getBizLastLoginTimeCookieName(), time.toString());
            cookie.setPath("/");
            return cookie;
        }
        return null;
    }

    /**
     * 通过调用CookieUtils产生自动登录的持久cookie
     *
     * @param principal 用户对象
     * @return 创建的cookie
     */
    private Cookie createAutoLoginCookie(Verifier verifier, Principal principal) {
        CookieUtils.removeCookie(getvisitorCookieName(), "/");
        if (!(verifier instanceof KeepLoginStatusVerifier)) return null;
//        KeepLoginStatusVerifier vf = (KeepLoginStatusVerifier) verifier;
//        if (!vf.isKeepLoginStatus() || vf.getKeepLoginMaxTime() == 0)
//            return null;
        String value = principal.getIdentity().toString() + SPLIT + principal.getLoginName();
        Cookie cookie = new Cookie(AUTO_LOGIN_COOKIE_NAME, Base64Utils.urlEncoding(value));
        cookie.setMaxAge(KEEPLOGINTIME);
        cookie.setPath("/");
        return cookie;
    }

    /**
     * 通过调用CookieUtils产生持久cookie
     * @deprecated 废弃
     * @param registerName 用户名
     * @return 创建的cookie
     */
    private Cookie createVisitorCookie(String registerName) {
        try {
            registerName = URLEncoder.encode(registerName, ENCODE);
            Cookie cookie = new Cookie(getvisitorCookieName(), registerName);
            cookie.setMaxAge(VISITORCOOKIE_MAX_AGE);
            cookie.setPath("/");
            return cookie;
        } catch (UnsupportedEncodingException e) {
            LOG.error(e);
            return null;
        }
    }


    /**
     * 设备类型cookie
     * @param verifier
     * @return
     */
    private Cookie createDeviceIdCookie(Verifier verifier) {
        try {
            PasswordVerifier passwordVerifier = (PasswordVerifier) verifier;
            String deviceId = passwordVerifier.getDeviceId();
            if (StringUtil.isNotEmpty(deviceId)) {
                Cookie cookie = new Cookie(getDeviceIdCookieName(), deviceId);
                cookie.setMaxAge(VISITORCOOKIE_MAX_AGE);
                cookie.setPath("/");
                return cookie;
            }
            return null;
        } catch (Exception e) {
            LOG.error(e);
            return null;
        }
    }

    /**
     * 通过调用CookieUtils和MD5Utils产生最后登录的会话cookie 如果是单一客户端登录的设置将不会被创建
     *
     *
     * @param principal 用户对象
     * @return 创建的cookie
     */
    private Cookie createLastLoginTime(Principal principal) {
        //if (singleClientLogin()) return null;
        Cookie cookie = new Cookie(getLastLoginTimeCookieName(), principal.getLastLoginTime().toString());
        cookie.setPath("/");
        return cookie;
    }

    /**
     * 通过调用CookieUtils查找持久cookie，
     * @deprecated 废弃
     * @return 找到返回true，否则返回false
     */
    public boolean isVisited() {
        return currentVisitor() != null;
    }

    /**
     * 参照currentPrincipal方法的逻辑，如果用户已登录返回 true ,否则返回null
     * @deprecated 废弃
     * @return
     */
    public boolean isLogined() throws Exception {
        return currentPrincipal() != null;
    }

    /**
     * 通过调用CookieUtils查找会话cookie，<br>
     * 如果找到，取得cookie中id，调用验证提供者的get方法，得到用户，<br>
     * 再根据cookie摘要与生成摘要对比，如果一致再判断时间戳距离当前时间的间隔是否在合适范围，<br>
     * 如果是返回认证用户，否则返回null
     *
     * @return true 表示是已登录 false 表示没有登录
     */
    public Principal currentPrincipal() throws Exception {

        Principal principal = getByPrincipalCookie();
        if (principal != null && principal.getMechantId() != null) {
            return principal;
        }
        return null;
    }

    public Principal currentPrincipalEaseEx() {
        try {
            Principal principal = getByPrincipalCookie();
            if (principal == null || principal.getMechantId() == null) {
                return null;
            }
            return principal;
        } catch (Exception e) {
            throw new AppException("请求登录信息失败", XyyJsonResultCodeEnum.FAIL);
        }
    }

    public Principal currentAccountPrincipal() throws Exception {
        Principal principal = getByPrincipalCookie();
        if (principal != null) {
            return principal;
        }
        return principal;
    }



    /**
     * 通过调用CookieUtils查找会话cookie，<br>
     * 如果找到，取得cookie中id，调用验证提供者的get方法，得到用户，<br>
     * 再根据cookie摘要与生成摘要对比，如果一致再判断时间戳距离当前时间的间隔是否在合适范围，<br>
     * 如果是返回认证用户，否则返回null
     *
     * @return true 表示是已登录 false 表示没有登录
     */
    private Principal getByPrincipalCookie() throws Exception {
        // 记录需要登录访问的URI
        getAuthenticationProvider().saveNeedLoginUri();

        Cookie cookie = CookieUtils.getCookie(getPrincipalcookieName());
        if (cookie == null) return null;
        String cookieValue = cookie.getValue();
        String[] values = cookieValue.split(SPLIT);
        String accountId = values[0];
        String cookieSHAValue = values[1];
        String merchantId = null;
        if(values.length == 3){
            merchantId = values[2];
        }

        //判断店铺是不是属于账号关联
        Principal principal = getAuthenticationProvider().get(Long.valueOf(accountId),merchantId);
        //从redis中获取最后登录时间
        Date lastRequestTime = getAuthenticationProvider().getLastRequestTime(accountId);
        if (principal == null){
            LOG.info("cookie日志打印用户id:"+accountId+",用户信息："+JSON.toJSONString(principal));
            return null;
        }
        //多帐号之前是判断是否多端登录，如果多端登录使用cookie里面的登录时间，否则使用数据库的最后登录时间；现在改成所有都使用cookie里的时间
        Long lastLoginTime = getPrincipalLastLoginTime(principal);

        if (lastLoginTime == null){
            LOG.info("cookie日志打印用户id:"+accountId+",用户最后登录时间:"+lastLoginTime);
            return null;
        }

        if (lastRequestTime == null){
            LOG.info("cookie日志打印用户id:"+accountId+",用户最后请求时间:"+lastRequestTime);
            return null;
        }
        String shaParam = getSHAParam(principal.getIdentity(), lastLoginTime);
        String shaValue = Base64Utils.urlEncoding(DigestUtils.shaHex(shaParam));
        if (!shaValue.equals(cookieSHAValue)){
            LOG.info("cookie日志打印用户id:"+accountId+",cookie信息对比不一致shaParam:"+shaParam+",shaValue:"+shaValue);
            return null;
        }
        //重新设置cookie
        MerchantBussinessDto dto = (MerchantBussinessDto) principal;
        //冻结剔除出登录
        if (dto.getStatus() != null && dto.getStatus()==3){
            LOG.info("cookie日志打印用户id:"+accountId+",为冻结用户");
            //登出
            logout();
            return null;
        }
        Date maxAccessDate = DateUtil.getDateAfter(lastRequestTime, PRINCIPALCOOKIE_MAX_AGE, 4);
        Date nowDate = new Date();
        if (maxAccessDate.compareTo(nowDate) > 0) {
            getAuthenticationProvider().setLastRequestTime(nowDate, accountId);
            return principal;
        }

        LOG.info("cookie日志打印用户id:"+accountId+",用户最后请求时间:"+lastRequestTime+",处理后时间为:"+maxAccessDate);
        return null;
    }

    private Long getPrincipalLastLoginTime(Principal principal) {
        /*if (singleClientLogin()) {
            return principal.getLastLoginTime();
        }*/
        Cookie cookie = CookieUtils.getCookie(getLastLoginTimeCookieName());
        if (cookie == null) return null;
        try {
            return Long.valueOf(cookie.getValue());
        } catch (NumberFormatException e) {
            LOG.error("last_login_time cookie format number error.", e);
        }
        return null;
    }

    /**
     * 根据保持登录状态的持久cookie得到认证用户
     *
     * @return
     */
    private Principal getByAutoLogin() {
        Cookie cookie = CookieUtils.getCookie(AUTO_LOGIN_COOKIE_NAME);
        if (cookie == null) return null;
        String encodingValue = cookie.getValue();
        if (encodingValue == null) return null;
        String decodeValue = Base64Utils.decodeing(encodingValue);
        try {
            Long id = new Long(decodeValue.split(SPLIT)[0]);
            Principal principal = getAuthenticationProvider().get(id);
            if (principal == null){
                LOG.info("第二步cookie日志打印用户id:"+id+"不存在");
                return null;
            }
            if (!principal.getLoginName().equals(decodeValue.split(SPLIT)[1])){
                LOG.info("第二步cookie日志打印用户id:"+id+",用户LoginName:"+principal.getLoginName()+",cookie中LoginName:"+decodeValue.split(SPLIT)[1]);
                return null;
            }
            //判断一下最后请求时间和最大保持登录时间差距,若超过最大保持登录时间，则踢掉
            Date lastRequestTime = getAuthenticationProvider().getLastRequestTime(id);
            if (lastRequestTime==null){
                LOG.info("第二步cookie日志打印用户id:"+id+",缓存中最后请求时间不存在");
                return null;
            }
            Date maxAccessDate = DateUtil.getDateAfter(lastRequestTime,KEEPLOGINTIME , 4);
            Date nowDate = new Date();
            if (maxAccessDate.compareTo(nowDate) <= 0) {
                LOG.info("第二步cookie日志打印用户id:"+id+",最后请求时间:"+lastRequestTime+",最后请求时间加过期时间后时间:"+maxAccessDate);
                return null;
            }
            //重新设置cookie
            MerchantBussinessDto dto = (MerchantBussinessDto) principal;
            //冻结剔除出登录
            if (dto.getStatus()==3){
                LOG.info("第二步cookie日志打印用户id:"+id+",被冻结");
                //登出
                logout();
                return null;
            }
            PasswordVerifier verifier = new PasswordVerifier(dto.getMobile(), dto.getPassword());
            writeLoginCookie(principal, verifier);
            return principal;
        } catch (Exception e) {
            LOG.error(e);
            return null;
        }
    }

    /**
     * @deprecated 废弃
     * @return
     */
    private Principal getRequestPrincipal() {
        HttpServletRequest request = WebContext.currentRequest();
        if (request == null) return null;
        Principal principal = (Principal)request.getAttribute(getPrincipalcookieName());
        return principal;
    }

    /**
     * 通过调用CookieUtils查找持久cookie,
     * @deprecated 废弃
     * @return 找到返回cookie中用户名，否则返回null
     */
    public String currentVisitor() {
        Object visitorObj = WebContext.currentRequest().getAttribute(getvisitorCookieName());
        if (visitorObj != null) return (String) visitorObj;
        try {
            Cookie cookie = CookieUtils.getCookie(getvisitorCookieName());
            if (cookie == null) return null;
            return URLDecoder.decode(cookie.getValue(), ENCODE);
        } catch (UnsupportedEncodingException e) {
            LOG.error(e.toString());
            return null;
        }
    }

    public void logout() {
        CookieUtils.removeCookie(getPrincipalcookieName(), "/");
        CookieUtils.removeCookie(getLastLoginTimeCookieName(), "/");
        CookieUtils.removeCookie(AUTO_LOGIN_COOKIE_NAME, "/");
        CookieUtils.removeCookie("xyy_token", "/");
//        CookieUtils.removeCookie(getvisitorCookieName(), "/");
        WebContext.currentRequest().setAttribute(getPrincipalcookieName(), null);
//        WebContext.currentRequest().setAttribute(getvisitorCookieName(), null);
    }

    /**
     * 通过调用验证提供者进行登录，如果登录成功，将会话cookie和持久cookie写入客户端
     *
     * @param verifier 用户名
     * @return 返回登录后的认证主体
     * @throws Exception
     */
    public Principal login(Verifier verifier) throws Exception {
        Principal principal = getAuthenticationProvider().authenticate(verifier);
        if (principal == null) {
            throw new Exception("登录失败");
        }
        writeLoginCookie(principal, verifier);
        return principal;
    }

    public MerchantPrincipal setPrincipalMerchant(Long merchantId,Long accountId) throws Exception {
        Date lastLoginDate = new Date();
        MerchantPrincipal merchantPrincipal1 = (MerchantPrincipal)getAuthenticationProvider().get(merchantId);
        merchantPrincipal1.setAccountId(accountId);
        merchantPrincipal1.setLastLoginDate(lastLoginDate);
        PasswordVerifier verifier = new PasswordVerifier("","");
        writeLoginCookie(merchantPrincipal1, verifier);
        return merchantPrincipal1;
    }

    private void writeLoginCookie(Principal principal, Verifier verifier) {
//        if (principal.getBizLastLoginTime() != null) {
//            CookieUtils.writeCookie(createBizLastLoginTime(principal.getBizLastLoginTime()));
//        }
        CookieUtils.writeCookie(createPrincipalCookie(principal));
        CookieUtils.writeCookie(createLastLoginTime(principal));
//        CookieUtils.writeCookie(createVisitorCookie(principal.getLoginName()));
        CookieUtils.writeCookie(createAutoLoginCookie(verifier, principal));
        WebContext.currentRequest().setAttribute(getPrincipalcookieName(), principal);
//        WebContext.currentRequest().setAttribute(getvisitorCookieName(), principal.getLoginName());
        getAuthenticationProvider().setLastRequestTime(new Date(), principal.getIdentity());
        CookieUtils.writeCookie(createDeviceIdCookie(verifier));
    }

    public void newLogout() {
        CookieUtils.removeCookie(getPrincipalcookieName(), "/");
        CookieUtils.removeCookie(getLastLoginTimeCookieName(), "/");
        CookieUtils.removeCookie(AUTO_LOGIN_COOKIE_NAME, "/");
        CookieUtils.removeCookie("xyy_token", "/");
//        CookieUtils.removeCookie(getvisitorCookieName(), "/");
        WebContext.currentRequest().setAttribute(getPrincipalcookieName(), null);
//        WebContext.currentRequest().setAttribute(getvisitorCookieName(), null);
    }

}
