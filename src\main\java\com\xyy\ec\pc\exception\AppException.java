package com.xyy.ec.pc.exception;

import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import lombok.Getter;

/**
 * App运行时异常
 *
 * <AUTHOR>
 */
@Getter
public class AppException extends RuntimeException {

    private static final long serialVersionUID = 1369521370610687751L;
    /**
     * 是否告警
     */
    private boolean warn;
    /**
     * 应用名
     */
    private String appName;
    private int code;
    private String msg;

    public AppException(XyyJsonResultCodeEnum xyyJsonResultCodeEnum) {
        super(xyyJsonResultCodeEnum.getMsg());
        this.warn = xyyJsonResultCodeEnum.isWarn();
        this.appName = xyyJsonResultCodeEnum.getAppName();
        this.code = xyyJsonResultCodeEnum.getCode();
        this.msg = xyyJsonResultCodeEnum.getMsg();
    }

    public AppException(String message, XyyJsonResultCodeEnum xyyJsonResultCodeEnum) {
        super(message);
        this.warn = xyyJsonResultCodeEnum.isWarn();
        this.appName = xyyJsonResultCodeEnum.getAppName();
        this.code = xyyJsonResultCodeEnum.getCode();
        this.msg = xyyJsonResultCodeEnum.getMsg();
    }

    public AppException(Throwable cause, XyyJsonResultCodeEnum xyyJsonResultCodeEnum) {
        super(xyyJsonResultCodeEnum.getMsg(), cause);
        this.warn = xyyJsonResultCodeEnum.isWarn();
        this.appName = xyyJsonResultCodeEnum.getAppName();
        this.code = xyyJsonResultCodeEnum.getCode();
        this.msg = xyyJsonResultCodeEnum.getMsg();
    }

    public AppException(String message, Throwable cause, XyyJsonResultCodeEnum xyyJsonResultCodeEnum) {
        super(message, cause);
        this.warn = xyyJsonResultCodeEnum.isWarn();
        this.appName = xyyJsonResultCodeEnum.getAppName();
        this.code = xyyJsonResultCodeEnum.getCode();
        this.msg = xyyJsonResultCodeEnum.getMsg();
    }

    /* 为了应对可变参数，强制覆盖MessageCodeEnum的msg */

    /**
     * 构造方法。强制覆盖{@link XyyJsonResultCodeEnum}的<code>msg</code>。
     *
     * @param xyyJsonResultCodeEnum
     * @param overrideApiResultCodeMsg
     */
    public AppException(XyyJsonResultCodeEnum xyyJsonResultCodeEnum, String overrideApiResultCodeMsg) {
        super(overrideApiResultCodeMsg);
        this.warn = xyyJsonResultCodeEnum.isWarn();
        this.appName = xyyJsonResultCodeEnum.getAppName();
        this.code = xyyJsonResultCodeEnum.getCode();
        this.msg = overrideApiResultCodeMsg;
    }

    /**
     * 构造方法。强制覆盖{@link XyyJsonResultCodeEnum}的<code>msg</code>。
     *
     * @param message
     * @param xyyJsonResultCodeEnum
     * @param overrideApiResultCodeMsg
     */
    public AppException(String message, XyyJsonResultCodeEnum xyyJsonResultCodeEnum, String overrideApiResultCodeMsg) {
        super(message);
        this.warn = xyyJsonResultCodeEnum.isWarn();
        this.appName = xyyJsonResultCodeEnum.getAppName();
        this.code = xyyJsonResultCodeEnum.getCode();
        this.msg = overrideApiResultCodeMsg;
    }

    public AppException(String message) {
        super(message);
    }

    /**
     * 构造方法。强制覆盖{@link XyyJsonResultCodeEnum}的<code>msg</code>。
     *
     * @param cause
     * @param xyyJsonResultCodeEnum
     * @param overrideApiResultCodeMsg
     */
    public AppException(Throwable cause, XyyJsonResultCodeEnum xyyJsonResultCodeEnum, String overrideApiResultCodeMsg) {
        super(overrideApiResultCodeMsg, cause);
        this.warn = xyyJsonResultCodeEnum.isWarn();
        this.appName = xyyJsonResultCodeEnum.getAppName();
        this.code = xyyJsonResultCodeEnum.getCode();
        this.msg = overrideApiResultCodeMsg;
    }

}
