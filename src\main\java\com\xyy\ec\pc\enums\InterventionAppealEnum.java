package com.xyy.ec.pc.enums;


/**
 * 介入诉求
 */
public enum InterventionAppealEnum {
    //退货
    RETURN(1,"仅退款",1),
    //退货退款
    RETURN_REFUND(2,"全部退货退款退货退款",2),
    //部分退货退款
    PARTIAL_RETURN_REFUND(3,"部分退货退款",2),
    //补开发票
    REFUND_INVOICE(4,"补开发票",3),
    //重开发票
    REISSUE_INVOICE(5,"重开发票",4),
    //开专票
    OPEN_SPECIAL_INVOICE(6,"开专票",4),
    //补发出库单
    Reissue_OUTBOUND_ORDER(7,"补发出库单",5),
    //重开出库单
    REISSUE_OUTBOUND_ORDER(8,"重开出库单",6),
    //补发纸质资质
    REISSUE_PAPER_QUALIFICATION(9,"补发纸质资质",7),
    //补发电子资质
    REISSUE_ELECTRONIC_QUALIFICATION(10,"补发电子资质",7),
    //更换纸质资质
    CHANGE_PAPER_QUALIFICATION(11,"更换纸质资质",8),
    //更换电子资质
    CHANGE_ELECTRONIC_QUALIFICATION(12,"更换电子资质",8);


    private int id;
    private String value;

    /**
     * @see InterventionStatusEnum
     */
    private int parentId;



    public static String get(int id) {
        for (InterventionAppealEnum c : InterventionAppealEnum.values()) {
            if (c.getId() == id) {
                return c.value;
            }
        }
        return null;
    }
    public  int getId() {
        return id;
    }
    public  String getValue() {
        return value;
    }
    InterventionAppealEnum(int id, String value) {
        this.id = id;
        this.value = value;
    }

    InterventionAppealEnum(int id, String value, int parentId) {
        this.id = id;
        this.value = value;
        this.parentId = parentId;
    }
}
