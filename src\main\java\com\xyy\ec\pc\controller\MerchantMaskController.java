package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.xyy.ec.merchant.bussiness.api.MerchantMaskBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantMaskBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.DateUtils;
import com.xyy.ec.pc.util.FtpUtil;
import com.xyy.ec.pc.util.excel.UtilOrganizeExcelData;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 上传
 * 
 * <AUTHOR>
 *
 */
@RequestMapping("/merchant/mask")
@Controller
public class MerchantMaskController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(MerchantMaskController.class);

	@Reference(version = "1.0.0",timeout = 10000)
	private MerchantMaskBusinessApi merhchantMaskApi;

	@Autowired
	private XyyIndentityValidator xyyIndentityValidator;

	@Autowired
	private XyyConfig.CdnConfig cdnConfig;

	@Value("${upload.merchant.mask.path}")
	private String uploadPath;

	/**
	* @Description 上传
	* @Param
	* @return
	* <AUTHOR>
	* @Date 2020/2/10 19:57
	*/
	@RequestMapping("/add")
	@ResponseBody
	public Object batchAddMerchant(@RequestParam MultipartFile bindMerchantFile) {
		try {
			LOGGER.info("上传购买口罩数据");
			MerchantBussinessDto merchant = null;
			try {
				merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			} catch (Exception e) {
				LOGGER.error("获取会员信息异常:",e);
			}
			if (merchant==null){
				this.addError("请登录后在操作！");
			}
			List<String> excelList= UtilOrganizeExcelData.excelToList(bindMerchantFile.getInputStream(),bindMerchantFile.getOriginalFilename());
			if(CollectionUtil.isEmpty(excelList)){
				return this.addError("没有数据");
			}
			LOGGER.info("用户:{},上传购买口罩数据",merchant.getId());
			if (excelList.size()>=201){
				return this.addError("一次上传不能超过200条数据！");
			}
			Date currentTime = org.apache.commons.lang.time.DateUtils.round(new Date(), Calendar.SECOND);
			List<MerchantMaskBussinessDto> list = Lists.newArrayList();
			for (String element : excelList) {
				String[] cellArray=element.split("\\|");
				if(cellArray!=null ){
					//遍历解析的数据
					MerchantMaskBussinessDto dto = new MerchantMaskBussinessDto();
					dto.setMetchantId(merchant.getId());
					dto.setMetchantName(merchant.getRealName());
					if (StringUtils.isNotBlank(cellArray[0])){
						dto.setBuyTime(cellArray[0]);
					}
					if (StringUtils.isNotBlank(cellArray[1])){
						dto.setCustomerName(cellArray[1]);
					}
					if (StringUtils.isNotBlank(cellArray[2])){
						dto.setMobile(cellArray[2]);
					}
					if (StringUtils.isNotBlank(cellArray[3])){
					    dto.setCardNo(cellArray[3]);
					}
					if (StringUtils.isNotBlank(cellArray[4])){
						dto.setQuantity(  Integer.parseInt(cellArray[4]));
					}
					dto.setCreateTime(currentTime);
					list.add(dto);
				}
			}
			merhchantMaskApi.addMerchantMaskInfo(list);
			//上传附件服务器
			ftpUpload(merchant.getId(),bindMerchantFile,currentTime);
			return this.addResult("上传成功");
		} catch (Exception e) {
			LOGGER.error("导入失败",e);
		}
		return this.addError("导入失败");
	}

	/**
	 * 上傳文件
	 * @param merchantId
	 * @param multipartFile
	 */
	private void ftpUpload(Long merchantId, MultipartFile multipartFile,Date currentTime){
		try {
			if(null == multipartFile ){
				LOGGER.info("用户{}口罩上传文件为空",merchantId);
				return;
			}
			if(null == uploadPath ){
				LOGGER.info("用户{}口罩上传路径为空",merchantId);
				return;
			}
			if(null == cdnConfig ){
				LOGGER.info("用户{}口罩上传cdnConfig为空",merchantId);
				return;
			}
			String filename = String.format("%s%s%s",merchantId,DateUtils.format(currentTime,"yyyyMMddHHmmss"),".xlsx");
			LOGGER.info("上传口罩文件服务开始,filename is {}",filename);
			boolean result = FtpUtil.uploadFile(cdnConfig, uploadPath, filename, multipartFile.getInputStream());
			if(result){
				LOGGER.info("用户{}上传口罩文件成功",merchantId);
			}else{
				LOGGER.info("用户{}上传口罩文件失败",merchantId);
			}
		}catch (Exception e){
			LOGGER.error("上传口罩文件错误,error 是{}",e);
		}
	}
}
