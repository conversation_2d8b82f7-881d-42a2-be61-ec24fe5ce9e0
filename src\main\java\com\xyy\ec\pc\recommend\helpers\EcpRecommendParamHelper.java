package com.xyy.ec.pc.recommend.helpers;

import com.xyy.ec.pc.recommend.params.PcRecommendParam;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSceneEnum;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSortStrategyEnum;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendTerminalTypeEnum;
import com.xyy.recommend.ecp.params.EcpRecommendQueryParam;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class EcpRecommendParamHelper {

    public static EcpRecommendQueryParam create(PcRecommendParam recommendParam, Long merchantId, Long accountId) {
        List<String> tagsList = splitToList(recommendParam.getTags());
        List<String> shopCodesList = splitToList(recommendParam.getShopCodes());
        return EcpRecommendQueryParam.builder()
                .merchantId(merchantId)
                .accountId(accountId)
                .saasOrganSign(recommendParam.getSaasOrganSign())
                .merchantProvinceCode(recommendParam.getMerchantProvinceCode())
                .terminalType(EcpRecommendTerminalTypeEnum.PC)
                .recommendScene(EcpRecommendSceneEnum.valueOfCustom(recommendParam.getRecommendScene()))
                .sortStrategy(EcpRecommendSortStrategyEnum.valueOfCustom(recommendParam.getSortStrategy()))
                .topSkuId(recommendParam.getTopSkuId())
                .tags(tagsList)
                .shopCodes(shopCodesList)
                .pageNum(recommendParam.getPageNum())
                .pageSize(recommendParam.getPageSize())
                .scmId(recommendParam.getScmId())
                .build();
    }

    private static List<String> splitToList(String input) {
        if (StringUtils.isEmpty(input)) {
            return Collections.emptyList();
        }
        return Arrays.asList(input.split(","));
    }
}
