package com.xyy.ec.api.rpc.gdd;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.chaos.api.ShopDecorateApi;
import com.xyy.chaos.model.ShopBaseInfo;
import com.xyy.chaos.model.ShopDetailInfo;
import com.xyy.chaos.model.vo.ResponseVo;
import com.xyy.ec.base.exception.BusiCommonException;
import com.xyy.ec.base.log.ApiLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/5/12 10:52
 * @Description
**/
@Component
@Slf4j
public class GongDuoDuoRpcService {
    private static final int SUCCESS = 1;
    @Reference(version = "1.0.0")
    ShopDecorateApi shopDecorateApi;

    /**
     * 查询工业店铺列表
     *
     * @return
     */
    @ApiLog
    public List<ShopBaseInfo> getShopList(String branchCode) {
        ResponseVo<List<ShopBaseInfo>> responseVo = shopDecorateApi.getShopList(branchCode);
        if (responseVo.getCode() == SUCCESS) {
            return responseVo.getData();
        }
        throw new BusiCommonException(responseVo.getCode(), responseVo.getMsg(), JSONObject.toJSONString(responseVo));
    }


    /**
     * 查询活动商品
     *
     * @param dpId
     * @return
     */
    @ApiLog
    public ShopDetailInfo getShopDetailInfo(String dpId) {
        ResponseVo<ShopDetailInfo> responseVo = shopDecorateApi.getShopDetailInfo(dpId);
        if (responseVo.getCode() == SUCCESS) {
            return responseVo.getData();
        }
//        throw new BusiCommonException(responseVo.getCode(), responseVo.getMsg(), JSONObject.toJSONString(responseVo));
        return null;
    }
}
