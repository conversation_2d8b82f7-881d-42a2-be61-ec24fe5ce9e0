package com.xyy.ec.pc.enums.marketing;

import lombok.Getter;

import java.util.Objects;

/**
 * 营销秒杀活动状态 枚举类。
 * 0-未开始，1-进行中，2-已结束。表示前后端协议定的活动状态。
 *
 * <AUTHOR>
 */
@Getter
public enum MarketingSeckillActivityStatusEnum {

    /**
     * 未开始
     */
    NOT_STARTED(0, "未开始"),
    /**
     * 进行中
     */
    IN_PROGRESS(1, "进行中"),
    /**
     * 已结束
     */
    END(2, "已结束");;

    private Integer status;
    private String name;

    MarketingSeckillActivityStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param status
     * @return
     */
    public static MarketingSeckillActivityStatusEnum valueOfCustom(Integer status) {
        if (Objects.isNull(status)) {
            return null;
        }
        for (MarketingSeckillActivityStatusEnum anEnum : values()) {
            if (Objects.equals(anEnum.getStatus(), status)) {
                return anEnum;
            }
        }
        return null;
    }
}
