package com.xyy.ec.pc.cms.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.layout.buinese.ecp.results.RuleExhibitionGroupSimpleAggregateShopDTO;
import com.xyy.ec.pc.cms.vo.CmsProductFlowAggShopVO;
import com.xyy.ec.search.engine.dto.layout.EcSearchSimpleAggregateShopDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class CmsProductFlowAggShopVOHelper {

    /**
     * 创建
     *
     * @param ecSearchSimpleAggregateShopDTO
     * @return
     */
    public static CmsProductFlowAggShopVO createForSearch(EcSearchSimpleAggregateShopDTO ecSearchSimpleAggregateShopDTO) {
        if (Objects.isNull(ecSearchSimpleAggregateShopDTO)) {
            return null;
        }
        return CmsProductFlowAggShopVO.builder()
                .shopCode(ecSearchSimpleAggregateShopDTO.getShopCode())
                .showName(ecSearchSimpleAggregateShopDTO.getShowName())
                .branchCode(ecSearchSimpleAggregateShopDTO.getBranchCode())
                .isThisSelfBranchCode(ecSearchSimpleAggregateShopDTO.getIsThisSelfBranchCode())
                .isThreadCompany(ecSearchSimpleAggregateShopDTO.getIsThreadCompany())
                .packageTips(ecSearchSimpleAggregateShopDTO.getPackageTips())
                .accountStatus(ecSearchSimpleAggregateShopDTO.getAccountStatus())
                .quality(ecSearchSimpleAggregateShopDTO.getQuality())
                .build();
    }

    public static List<CmsProductFlowAggShopVO> createsForSearch(List<EcSearchSimpleAggregateShopDTO> ecSearchSimpleAggregateShopDTOS) {
        if (CollectionUtils.isEmpty(ecSearchSimpleAggregateShopDTOS)) {
            return Lists.newArrayList();
        }
        return ecSearchSimpleAggregateShopDTOS.stream().map(item -> createForSearch(item)).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 创建
     *
     * @param ecSearchSimpleAggregateShopDTO
     * @return
     */
    public static CmsProductFlowAggShopVO createForLayout(RuleExhibitionGroupSimpleAggregateShopDTO ecSearchSimpleAggregateShopDTO) {
        if (Objects.isNull(ecSearchSimpleAggregateShopDTO)) {
            return null;
        }
        return CmsProductFlowAggShopVO.builder()
                .shopCode(ecSearchSimpleAggregateShopDTO.getShopCode())
                .showName(ecSearchSimpleAggregateShopDTO.getShowName())
                .branchCode(ecSearchSimpleAggregateShopDTO.getBranchCode())
                .isThisSelfBranchCode(ecSearchSimpleAggregateShopDTO.getIsThisSelfBranchCode())
                .isThreadCompany(ecSearchSimpleAggregateShopDTO.getIsThreadCompany())
                .packageTips(ecSearchSimpleAggregateShopDTO.getPackageTips())
                .accountStatus(ecSearchSimpleAggregateShopDTO.getAccountStatus())
                .quality(ecSearchSimpleAggregateShopDTO.getQuality())
                .build();
    }

    public static List<CmsProductFlowAggShopVO> createsForLayout(List<RuleExhibitionGroupSimpleAggregateShopDTO> ecSearchSimpleAggregateShopDTOS) {
        if (CollectionUtils.isEmpty(ecSearchSimpleAggregateShopDTOS)) {
            return Lists.newArrayList();
        }
        return ecSearchSimpleAggregateShopDTOS.stream().map(item -> createForLayout(item)).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
