package com.xyy.ec.pc.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.xyy.ec.ai.sg.client.KafkaProducerClient;
import com.xyy.ec.ai.sg.event.SnowGroundEvent;
import com.xyy.ec.ai.sg.event.SnowGroundEventBuilder;
import com.xyy.ec.ai.sg.filter.ISnowGroundFilterEventInfo;
import com.xyy.ec.ai.sg.util.ipip.IPUtils;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.MaiDianActionEnum;
import com.xyy.ec.pc.enums.SnowGroundTypeEnum;
import com.xyy.ec.pc.enums.SnowGroundTypeEnum.*;
import com.xyy.ec.pc.enums.TerminalTypeEnum;
import com.xyy.ec.pc.param.MaidianParams;
import com.xyy.ec.pc.search.enums.MaiDianSearchSpIdTypeEnum;
import com.xyy.ec.pc.search.enums.SearchSourceEnum;
import com.xyy.ec.pc.search.params.PcSearchProductListQueryParam;
import com.xyy.ec.pc.search.vo.MaiDianVo;
import com.xyy.ec.product.back.end.enums.NearEffectEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.xyy.ec.pc.enums.MaiDianActionEnum.CATEGORY;
import static com.xyy.ec.pc.enums.MaiDianActionEnum.PC_BATCH_PURCHASE;
import static com.xyy.ec.search.engine.constants.SearchConstants.YBM_ACT_SUI_XIN_PIN;

/**
 * @ClassName: SearchUtils
 * @Description:
 * @author: denghp
 * @date: 2020/3/26
 */
@Slf4j
@Component
public class SearchUtils {
    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 2,
            1,
            TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(10000));

    @Value("${sg.app.key}")
    private String appkey;

    @Autowired
    private KafkaProducerClient kafkaProducerClient;

    /**
     * 生成埋点参数
     *
     * @param merchantId
     * @param keyword
     * @param categoryFirstId
     * @param categorySecondId
     * @param categoryThirdId
     * @return
     */
    public static Map<String, String> generateMaiDianParams(Long merchantId,
                                                            String keyword,
                                                            String categoryFirstId,
                                                            String categorySecondId,
                                                            String categoryThirdId,
                                                            MaiDianActionEnum actionEnum
    ) {
        Map<String, String> params = Maps.newHashMap();
        String spType = "";
        String spId = "";

        switch (actionEnum) {
            case SEARCH:

            case CATEGORY:
                //keyword 不为空,spType = 1, 否则标识为分类类型
                if (StringUtil.isNotEmpty(keyword)) {
                    spType = String.valueOf(actionEnum.getSpType());
                    spId = keyword;
                } else {
                    //关键词为空，设置spType = 2
                    spType = String.valueOf(CATEGORY.getSpType());
                    spId = String.format("%s-%s-%s", StringUtil.isEmpty(categoryFirstId) ? 0 : categoryFirstId, StringUtil.isEmpty(categorySecondId) ? 0 : categorySecondId, StringUtil.isEmpty(categoryThirdId) ? 0 : categoryThirdId);
                }
                break;
            case APP_RECOMMEND:
            case APP_CSU_DETAIL_RECOMMEND:
            case PC_CSU_DETAIL_RECOMMEND:
            case PC_SEARCH_NO_RESULT_RECOMMEND:
            case APP_SEARCH_NO_RESULT_RECOMMEND:
            case APP_DISCOVER_RECOMMEND:
                spType = String.valueOf(actionEnum.getSpType());
                spId = String.format("%s-%s", actionEnum.getSpFrom(), merchantId == null ? 0 : merchantId);
                break;
            case PC_BATCH_PURCHASE:
                spType = PC_BATCH_PURCHASE.getSpType().toString();
                spId = String.format("%s-%s", spType,PC_BATCH_PURCHASE.getSpFrom());
        }
        params.put("spType", spType);
        params.put("spId", spId);
        params.put("sId", generateSidData(merchantId, TerminalTypeEnum.PC.getValue()));
        return params;
    }
    /**
     * 埋点生成
     *
     * @param mdParams
     * @param platform
     * @param snSpType
     * @return
     */
    public static MaiDianVo buildMaiDianVoInfo(Object mdParams, int platform, SnowGroundTypeEnum snSpType) {
        try {
            MaidianParams maidianParams = JsonUtil.convertValue(mdParams, MaidianParams.class);
            String spType = "";
            //如果请求参数sptype 为空则根据默认调用sptype值，否则使用请求参数sptype值
            if (StringUtil.isEmpty(maidianParams.getSptype()) || "0".equals(maidianParams.getSptype())) {
                spType = String.valueOf(snSpType.getValue());
            } else {
                spType = maidianParams.getSptype();
            }
            String keyword = maidianParams.getKeyword();
            Long categoryId = StringUtils.isNotEmpty(maidianParams.getCategoryId()) ? Long.valueOf(maidianParams.getCategoryId()) : null;
            String spId = "";
            switch (snSpType) {
                case SEARCH:
                    spId = getSearchSpId(keyword, categoryId, maidianParams.getSpFrom());
                    break;
                case SHOP:
                    spId = getShopSearchSpId(keyword, categoryId, maidianParams.getSpFrom());
                    break;
                case CAT_PAGE:
                    //keyword 不为空,设置搜索spType = 1, 否则标识为分类类型
                    if (StringUtil.isNotEmpty(keyword)) {
                        spType = String.valueOf(SnowGroundTypeEnum.SEARCH.getValue());
                        spId = getSearchSpId(keyword, categoryId, SearchSpFrom.SEARCH.getValue());
                    } else {
                        //关键词为空，设置spType = 2
                        spType = String.valueOf(SnowGroundTypeEnum.CAT_PAGE.getValue());
                        spId = String.format("%s-%s-%s", StringUtil.isEmpty(maidianParams.getCategoryId()) ? 0 : maidianParams.getCategoryId(),
                                StringUtil.isEmpty(maidianParams.getCategorySecondId()) ? 0 : maidianParams.getCategorySecondId(), StringUtil.isEmpty(maidianParams.getCategoryThirdId()) ? 0 : maidianParams.getCategoryThirdId());
                    }
                    break;
                case ACT_PAGE:
                    //活动页面
                    spId = StringUtils.isEmpty(maidianParams.getPageSource()) ? "" : maidianParams.getPageSource();
                    break;
                case ALWAYS_BUY_LIST:
                    AlwaysBuyListSpFrom spFrom = AlwaysBuyListSpFrom.getByValue(maidianParams.getSpFrom());
                    spId = String.valueOf(spFrom == null ? AlwaysBuyListSpFrom.APP_INDEX_ABL.getValue() : spFrom.getValue());
                    break;
                case RECOMMEND:
                    RecommendSpFrom recommendSpFrom = RecommendSpFrom.getByValue(maidianParams.getSpFrom());
                    Object[] params;
                    if (StringUtil.isNotEmpty(keyword) && recommendSpFrom != null && RecommendSpFrom.APP_SEARCH_NO_RECALL_REC.equals(recommendSpFrom)) {
                        params = new Object[]{recommendSpFrom.getValue(), keyword, maidianParams.getMerchantId() == null ? 0 : maidianParams.getMerchantId()};
                        //搜索无结果推荐，强制将sptype 转换成推荐sptype值，忽略传递参数值
                        spType = String.valueOf(SnowGroundTypeEnum.RECOMMEND.getValue());
                    } else {
                        params = new Object[]{recommendSpFrom == null ? 0 : recommendSpFrom.getValue(), maidianParams.getMerchantId() == null ? 0 : maidianParams.getMerchantId()};
                    }

                    spId = StringUtils.join(params, Constants.LINE);
                    break;
                case AGG_CAT:
                    AggCatSpFrom catSpFrom = AggCatSpFrom.getByValue(maidianParams.getSpFrom());
                    Object[] elements = new Object[]{catSpFrom == null ? AggCatSpFrom.SEARCH.getValue() : catSpFrom.getValue(), keyword, categoryId == null ? 0 : categoryId};
                    spId = StringUtils.join(elements, Constants.LINE);
                    break;
                default:
                    break;
            }
            String sid = StringUtil.isEmpty(maidianParams.getSid()) ? generateSidData(maidianParams.getMerchantId(), platform) : maidianParams.getSid();
            return MaiDianVo.builder().spType(spType).spId(spId).sid(sid).build();
        } catch (Exception exception) {
            log.error("埋点数据生成异常 : ", exception);
        }
        return MaiDianVo.builder().spType("").spId("").sid("").build();
    }

    /**
     * 搜索spid
     *
     * @param keyword
     * @param categoryId
     * @param spFrom
     * @return
     */
    private static String getSearchSpId(String keyword, Long categoryId, Integer spFrom) {
        SearchSpFrom searchSpFrom = SearchSpFrom.getByValue(spFrom);
        Object[] elements = new Object[]{searchSpFrom == null ? SearchSpFrom.SEARCH.getValue() : searchSpFrom.getValue(), StringUtils.isEmpty(keyword) || keyword.equals("null") ? "" : keyword, categoryId == null ? 0 : categoryId};
        String spId = StringUtils.join(elements, Constants.LINE);
        return spId;
    }

    /**
     * 店铺搜索spid
     *
     * @param keyword
     * @param categoryId
     * @param spFrom
     * @return
     */
    private static String getShopSearchSpId(String keyword, Long categoryId, Integer spFrom) {
        ShopSpFrom shopSpFrom = ShopSpFrom.getByValue(spFrom);
        Object[] elements = {};
        if (shopSpFrom == null) {
            if (StringUtil.isEmpty(keyword)) {
                elements = new Object[]{ShopSpFrom.SHOP_CAT_LIST.getValue(), "", categoryId == null ? 0 : categoryId};
            } else {
                elements = new Object[]{ShopSpFrom.SHOP_SEARCH.getValue(), keyword, categoryId == null ? 0 : categoryId};
            }
        } else {
            elements = new Object[]{shopSpFrom.getValue(), StringUtil.isNotEmpty(keyword) ? keyword : "", categoryId == null ? 0 : categoryId};
        }

        String spId = StringUtils.join(elements, Constants.LINE);
        return spId;
    }


    /**
     * 生成埋点sid数据
     *
     * @param merchantId 用户ID
     * @param platform   平台值
     * @return
     */
    public static String generateSidData(Long merchantId, int platform) {
        return String.format("%s-%s-%s-%s", DateUtils.format(new Date(), "yyyyMMdd"), merchantId == null ? 0 : merchantId, RandomStringUtils.randomAlphanumeric(5), platform);
    }

    /**
     * 解析pageSource
     *
     * @param pageSource
     * @return
     */
    public static Map<String, String> analysisPageSource(String pageSource) {
        Map<String, String> map = new HashMap<>();
        if (StringUtil.isEmpty(pageSource)) {
            return map;
        }
        String keys = "a:pageSourceA,b:pageSourceB,c:pageSourceC,d:pageSourceD,e:pageSourceE";
        List<String> pageSourceList = Arrays.asList(pageSource.split("_"));
        List<String> keyList = Arrays.asList(keys.split(","));
        keyList.forEach(k -> {
            String key = k.split(":")[0];
            String mapKey = k.split(":")[1];
            String value = "";
            for (int i = pageSourceList.size() - 1; i >= 0; i--) {
                String str = pageSourceList.get(i);
                if (org.springframework.util.StringUtils.isEmpty(str)) {
                    continue;
                }
                if (key.equals("e")) {
                    str = UrlUtil.getURLDecoderString(str, UrlUtil.UTF_8_ENCODE);
                }
                if (String.valueOf(str.charAt(0)).equals(key)) {
                    value = str.substring(1, str.length());
                }
            }
            map.put(mapKey, value);
        });
        return map;
    }

    /**
     * 生成埋点nsid数据
     *
     * @param merchantId 用户ID
     * @param platform   平台值
     * @return
     */
    public static String generateNewSidData(Long merchantId, int platform) {
        return String.format("%s-%s-%s-%s", DateUtils.format(new Date(), "yyyyMMdd"), merchantId == null ? 0 : merchantId, RandomStringUtils.randomAlphanumeric(5), platform);
    }

    public static Integer getSearchSpIdType(String spid) {
        if (StringUtils.isEmpty(spid)) {
            return null;
        }
        String[] items = spid.split("-");
        try {
            Integer spidType = Integer.parseInt(items[0]);
            MaiDianSearchSpIdTypeEnum spIdTypeEnum = MaiDianSearchSpIdTypeEnum.getByValue(spidType);
            if (spIdTypeEnum != null) {
                return spIdTypeEnum.getValue();
            }
        } catch (NumberFormatException ex) {
            log.error("spid : {} is invalid", spid);
        }
        return null;
    }

    /**
     * 生成商品级埋点nsid数据
     *
     * @param nsid
     * @return
     */
    public static String generateSkuNewSidData(String nsid, Integer offsetId) {
        if (StringUtil.isEmpty(nsid)) {
            return null;
        }
        return String.format("%s-%s", nsid, offsetId);
    }

    /**
     * 埋点参数格式化
     *
     * @return "xx,xxx"
     */
    public static String commaFormatStr(Object... args) {
        if (args == null || args.length <= 0) {
            return "";
        }
        if (args.length == 1) {
            return String.valueOf(args[0]);
        } else {
            return String.format("%s,%s", args[0], args[1]);
        }
    }

    /**
     * 搜索推荐曝光埋点上报
     *
     * @param request
     * @param sid       埋点策略生成的SID
     * @param pageNum
     * @param pageSize
     * @param skuIdList
     */
    public void searchExposureEvent(HttpServletRequest request, String sid, String sptype, String spid, Integer pageNum, Integer pageSize, List<Long> skuIdList) {

        ISnowGroundFilterEventInfo snowGroundFilterEventInfo = new ISnowGroundFilterEventInfo() {
            @Override
            public Map<String, String> getRequestData(HttpServletRequest request) {
                Map<String, String> map = new HashMap<>();
                //埋点策略生成的SID
                map.put("sid", String.valueOf(sid));
                map.put("sptype", sptype);
                map.put("spid", spid);
                //page_num, page_size
                map.put("page_num", String.valueOf(pageNum));
                map.put("page_size", String.valueOf(pageSize));
                //曝光商品ID
                map.put("product_list", CollectionUtils.isEmpty(skuIdList) ? "" : StringUtils.join(skuIdList, "_"));
                return map;
            }
        };

        JSONObject jsonObject = SnowGroundEventBuilder.getSnowGroundEventJson(getRequestEventJSONObject(request, snowGroundFilterEventInfo));
        // 后端PV埋点Filter
        try {
            executor.execute(() ->
                    kafkaProducerClient.send(jsonObject));
        } catch (Throwable e) {
            log.error("Snow ground pv upload failed. Task count : {}, completed task count : {}, active count : {}, queue size : {}",
                    executor.getTaskCount(), executor.getCompletedTaskCount(), executor.getActiveCount(), executor.getQueue().size(), e);
        }
    }

    public SnowGroundEvent getRequestEventJSONObject(HttpServletRequest request, ISnowGroundFilterEventInfo eventInfo) {
        SnowGroundEvent sge = SnowGroundEventBuilder.builder();
        SnowGroundEventBuilder.updateEventHeader(sge, request.getMethod(), request.getHeader("host"),
                request.getHeader("referer"), request.getHeader("user-agent"));
        SnowGroundEventBuilder.updateEventUA(sge, request.getHeader("user-agent"));
        SnowGroundEventBuilder.updateEventIp(sge, IPUtils.getClientIP(request));
        SnowGroundEventBuilder.updateBaseEventMsg(sge,
                //String session_id
                eventInfo.getRequestSessionId(request),
                //Long event_create_time
                eventInfo.getRequestEventCreateTime(request),
                //String appkey
                appkey,
                //String uuid
                eventInfo.getRequestUuid(request),
                //String cuid
                eventInfo.getRequestCuid(request),
                //String device_id
                eventInfo.getRequestDeviceId(request),
                //String action_type
                Constants.SEARCH_RECOMMEND_ACTION_TYPE,
                //String url
                request.getRequestURL().toString(),
                //String referrer_url
                request.getHeader("referer"),
                //String referrer_domain
                request.getRequestURL().toString().replaceAll(request.getRequestURI(), ""),
                //String page_name
                eventInfo.getRequestPageName(request),
                //String page_title
                eventInfo.getRequestPageTitle(request),
                //String submod
                eventInfo.getRequestSubmod(request),
                //String event_seq_id
                eventInfo.getRequestEventSequenceId(request),
                //String poi_id
                eventInfo.getRequestPoiID(request),
                //String channel
                eventInfo.getRequestChannel(request),
                //String longitude
                eventInfo.getRequestLongitude(request),
                //String latitude
                eventInfo.getRequestLatitude(request),
                //Map<String,String> data
                eventInfo.getRequestData(request)
        );
        return sge;
    }


    /**
     * 获取临期/近效期文案
     *
     * @param nearEffectiveFlag
     */
    public static String getEffectiveText(Integer nearEffectiveFlag) {
        AtomicReference<String> effectiveStr = new AtomicReference<>("");
        Optional.ofNullable(nearEffectiveFlag).ifPresent(effectiveFlag -> {
            switch (effectiveFlag) {
                case NearEffectEnum.NEAR_CODE:
                    effectiveStr.set(String.format("【%s】", NearEffectEnum.NEAR.getMessage()));
                    break;
                case NearEffectEnum.NEAR_EFFECT_DATE_CODE:
                    effectiveStr.set(String.format("【%s】", NearEffectEnum.NEAR_EFFECT_DATE.getMessage()));
                    break;
            }
        });
        return effectiveStr.get();
    }
    /**
     * 判断是否首推优选
     * @return
     */
    public static boolean isShouTuiYouXuan(Integer firstChoose, Integer highGross) {
        if (Constants.IS1.equals(firstChoose) && Constants.highGross.contains(highGross)) {
            return true;
        }
        return false;
    }

    /**
     * 查找搜索类型
     * @param queryParam
     * @return
     */
    public static Integer getSearchSource(PcSearchProductListQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            return SearchSourceEnum.SEARCH.getValue();
        }
        //专区搜索
        if (StringUtils.isNotEmpty(queryParam.getTagList())) {
            List<String> tagList = StrUtil.splitTrim(queryParam.getTagList(), Constants.COMMA_SEPARATOR_CHAR);
            //随心拼搜索
            if (tagList.contains(YBM_ACT_SUI_XIN_PIN)) {
                return SearchSourceEnum.SUI_XIN_PIN_SEARCH.getValue();
            }
            //过滤专区标签
            List<String> actTagList = Optional.ofNullable(tagList).orElseGet(()-> Collections.emptyList()).stream().filter(tag-> StringUtils.isNotEmpty(tag) && tag.startsWith(Constants.ACT_TAG_PRE)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(actTagList)) {
                return SearchSourceEnum.ACT_SEARCH.getValue();
            }
        }
        //店铺搜索
        if (Objects.nonNull(queryParam.getSpFrom()) && queryParam.getSpFrom().intValue() == SearchSpFrom.SHOP_SEARCH.getValue()
                && StringUtils.isNotEmpty(queryParam.getShopCodes())) {
            List<String> shopCodes = StrUtil.splitTrim(queryParam.getShopCodes(), Constants.COMMA_SEPARATOR_CHAR);
            if (CollectionUtil.isNotEmpty(shopCodes) && shopCodes.size() == BigInteger.ONE.intValue()) {
                return SearchSourceEnum.SHOP_SEARCH.getValue();
            }
        }
        return SearchSourceEnum.SEARCH.getValue();
    }

    public static String getSearchIndexHtmlAssetsIndexJsRelativePath(String searchIndexV2Html) {
        if (StringUtils.isEmpty(searchIndexV2Html)) {
            return null;
        }
        Pattern compile = Pattern.compile("/newstatic/assets/index\\.([0-9a-zA-Z]+)\\.js");
        Matcher matcher = compile.matcher(searchIndexV2Html);
        while (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    public static String getSearchIndexHtmlAssetsVendorJsRelativePath(String searchIndexV2Html) {
        if (StringUtils.isEmpty(searchIndexV2Html)) {
            return null;
        }
        Pattern compile = Pattern.compile("/newstatic/assets/vendor\\.([0-9a-zA-Z]+)\\.js");
        Matcher matcher = compile.matcher(searchIndexV2Html);
        while (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    public static String getSearchIndexHtmlAssetsIndexCssRelativePath(String searchIndexV2Html) {
        if (StringUtils.isEmpty(searchIndexV2Html)) {
            return null;
        }
        Pattern compile = Pattern.compile("/newstatic/assets/index\\.([0-9a-zA-Z]+)\\.css");
        Matcher matcher = compile.matcher(searchIndexV2Html);
        while (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    public static String getSearchIndexHtmlBody(String searchIndexV2Html) {
        if (StringUtils.isEmpty(searchIndexV2Html)) {
            return null;
        }
        int startIndex = searchIndexV2Html.indexOf(">", searchIndexV2Html.indexOf("<body"));
        int endIndex = searchIndexV2Html.lastIndexOf("</body>");
        return searchIndexV2Html.substring(startIndex + 1, endIndex);
    }

    /**
     * 判断是否随心拼搜索
     *
     * @param queryParam
     * @return
     */
    public static boolean isSuiXinPinSearch(PcSearchProductListQueryParam queryParam) {
        if (StringUtil.isNotEmpty(queryParam.getTagList()) && queryParam.getTagList().equalsIgnoreCase(YBM_ACT_SUI_XIN_PIN)) {
            return true;
        }
        return false;
    }

    /**
     * 是否随心拼全部数据页面
     *
     * @param queryParam
     * @return
     */
    public static boolean isSuiXinPinAllDataPage(PcSearchProductListQueryParam queryParam) {
        Assert.notNull(queryParam, "queryParams不能为空.");
        //判断是否 (isNull or 0 or -1)
        if (StringUtil.isEmpty(queryParam.getKeyword())) {
            return true;
        }
        return false;
    }

}
