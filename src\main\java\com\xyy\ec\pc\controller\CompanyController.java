/**
 * Copyright (C), 2015-2018,  武汉小药药医药科技有限公司
 * FileName: CompanyController
 * Author:   dell
 * Date:     2018/10/18 17:08
 * Description: 商家接口
 * History:
 * <author>          <time>          <version>          <desc>
 * 作者姓名           修改时间           版本号              描述
 */
package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.exception.search.XyyEcSearchBizCheckException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.api.PopOrderBusinessApi;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.controller.vo.ShopGoodsVo;
import com.xyy.ec.pc.helper.ProductDataFilterHelper;
import com.xyy.ec.pc.controller.vo.ShopGoodsVo;
import com.xyy.ec.pc.model.ClientAccountInfo;
import com.xyy.ec.pc.model.ClientResources;
import com.xyy.ec.pc.newfront.service.ShopQueryService;
import com.xyy.ec.pc.popshop.rpc.ClientAccountServerRpc;
import com.xyy.ec.pc.popshop.service.PopShopService;
import com.xyy.ec.pc.popshop.vo.CompanyDetailVo;
import com.xyy.ec.pc.popshop.vo.CompanyShopFloorVo;
import com.xyy.ec.pc.popshop.vo.ShopIndexVo;
import com.xyy.ec.pc.popshop.vo.ShopListProductVo;
import com.xyy.ec.pc.remote.ProductExhibitionGroupBusinessAdminRemoteService;
import com.xyy.ec.pc.service.ShopConfuseService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.shop.service.ShopService;
import com.xyy.ec.pc.shop.vo.ShopInfoVO;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.pop.server.api.account.dto.ClientAccountInfoExDto;
import com.xyy.ec.product.business.api.HostSearchBusinessApi;
import com.xyy.ec.product.business.api.ProductCompanyBusinessApi;
import com.xyy.ec.product.business.api.ecp.skucategory.EcpCategoryBusinessApi;
import com.xyy.ec.product.business.dto.CategoryBusinessDTO;
import com.xyy.ec.product.business.dto.ProductBusinessDto;
import com.xyy.ec.product.business.dto.ProductDto;
import com.xyy.ec.product.business.dto.pop.CompanyListVO;
import com.xyy.ec.product.business.module.CategoryVo;
import com.xyy.ec.search.engine.api.EcSearchApi;
import com.xyy.ec.search.engine.dto.SearchCsuDTO;
import com.xyy.ec.search.engine.entity.CsuActivityTag;
import com.xyy.ec.search.engine.entity.CsuInfo;
import com.xyy.ec.search.engine.enums.CsuOrder;
import com.xyy.ec.search.engine.enums.SortOrder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.net.URL;
import java.util.*;

/**
 * 〈一句话功能简述〉<br>
 * 〈商家接口〉
 *
 * <AUTHOR>
 * @create 2018/10/18
 * @since 1.0.0
 */
@RequestMapping("/company/center/companyInfo")
@Controller
public class CompanyController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CompanyController.class);

    @Resource
    private XyyIndentityValidator xyyIndentityValidator;

    @Resource
    private PcVersionUtils appVersionUtils;

    @Reference(version = "1.0.0")
    private ProductCompanyBusinessApi companyService;

    @Reference(version = "1.0.0", timeout = 10000)
    private EcpCategoryBusinessApi categoryBusinessApi;

    @Reference(version = "1.0.0")
    private EcSearchApi searchApi;

    @Reference(version = "1.0.0")
    private HostSearchBusinessApi hostSearchBusinessApi;

    @Reference(version = "1.0.0", timeout = 10000)
    private MerchantBussinessApi merchantBussinessApi;

    @Resource
    private ClientAccountServerRpc clientAccountServerRpc;
    @Resource
    private PopShopService popShopService;
    @Reference(version = "1.0.0")
    private PopOrderBusinessApi popOrderBusinessApi;

    @Autowired
    private ShopService shopService;

    @Value("${POP_HOST}")
    private String POP_HOST;

    @Autowired
    private ProductExhibitionGroupBusinessAdminRemoteService productExhibitionGroupBusinessAdminRemoteService;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private ShopConfuseService shopConfuseService;

    @Resource
    private ShopQueryService shopQueryService;

    @RequestMapping(value = "getPopCompanyDetail.json", method = RequestMethod.GET)
    @ResponseBody
    public Object getPopCompanyDetail(String orgId, HttpServletRequest request) {
        try {
            if (BooleanUtils.isTrue(shopConfuseService.isCantShowShop(orgId))) {
                LOGGER.warn("CompanyController.getPopCompanyDetail，PC请求商家信息，不显示店铺，orgId：{}", orgId);
                return addError(CODE_ERROR, appProperties.getShopConfuseNotShowShopTip());
            }
            String branchCode = this.getBranchCode(request);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("PC请求商家信息，orgId：{}，branchCode：{}", orgId, branchCode);
            }
            CompanyDetailVo companyDetailVo = popShopService.getPopCompanyDetail(orgId, branchCode);
            return this.addResult("data", companyDetailVo);
        } catch (Exception e) {
            LOGGER.error("pop商家信息", e);
        }
        return addError("pop商家信息查询失败");
    }

    /**
     * 商品分类
     *
     * @param orgId
     * @return
     */
    @RequestMapping(value = "getPopCategory.json", method = RequestMethod.GET)
    @ResponseBody
    public Object getPopCategory(String orgId, HttpServletRequest request) {
        try {
            if (BooleanUtils.isTrue(shopConfuseService.isCantShowShop(orgId))) {
                LOGGER.warn("CompanyController.getPopCategory，pop商品分类，不显示店铺，orgId：{}", orgId);
                return addError(CODE_ERROR, appProperties.getShopConfuseNotShowShopTip());
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            //没有登录强制不选中合作商家
            if (merchant == null || merchant.getId() == null) {
                merchantId = NumberUtils.LONG_ZERO;
            } else {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            return this.addResult("data", categoryBusinessApi.getCategoryTree(branchCode));
        } catch (Exception e) {
            LOGGER.error("pop商家信息", e);
        }
        return addError("pop商家信息查询失败");
    }

    /**
     * pop商家列表<br/>
     * pc端展示4个商品
     *
     * @param page
     * @param isBuyCompany 是否选中已合作商家
     * @return
     */
    @RequestMapping(value = "index.htm", method = RequestMethod.GET)
    public ModelAndView findPageCompany(Page<CompanyListVO> page, Integer isBuyCompany, HttpServletRequest request) {
        Map<String, Object> model = new HashMap<>();
        int pcDefaultViewSkuNum = 4;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            //没有登录强制不选中合作商家
            if (merchant == null || merchant.getId() == null) {
                isBuyCompany = 0;
                model.put("islogin", 0);
                merchantId = NumberUtils.LONG_ZERO;
            } else {
                merchantId = merchant.getId();
                model.put("merchant", merchant);
                model.put("merchantId", merchantId);
                model.put("islogin", 1);
            }
            if (isBuyCompany == null) {
                isBuyCompany = 0;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            String url = getRequestUrl(request);
            page.setRequestUrl(url);
            int currentPage = page.getOffset();
            page.setOffset((page.getOffset() > 0 ? (page.getOffset() - 1) : 0) * page.getLimit());
            PageInfo<CompanyListVO> pageInfo = companyService.viewCompanyList(page.getOffset(), page.getLimit(), isBuyCompany, merchantId, pcDefaultViewSkuNum, branchCode);
            page.setRows(pageInfo.getList());
            page.setPageCount(pageInfo.getPages());
            page.setOffset(currentPage);
            page.setTotal(pageInfo.getTotal());
        } catch (Exception e) {
            LOGGER.error("pop商家列表", e);
        }
        model.put("isBuyCompany", isBuyCompany);
        model.put("pager", page);
        return new ModelAndView("/company/index.ftl", model);
    }

    /**
     * 店铺首页
     *
     * @param orgId
     * @return
     */
    @RequestMapping(value = "shopIndex.htm", method = RequestMethod.GET)
    public ModelAndView shopIndex(String orgId, HttpServletRequest request) {
        Map<String, Object> model = new HashMap<>();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            //没有登录强制不选中合作商家
            if (merchant == null || merchant.getId() == null) {
                model.put("islogin", 0);
                merchantId = NumberUtils.LONG_ZERO;
            } else {
                merchantId = merchant.getId();
                model.put("merchant", merchant);
                model.put("merchantId", merchantId);
                model.put("islogin", 1);
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            ShopIndexVo companyIndexVO = popShopService.shopIndexView(orgId, merchantId, branchCode);
            if (companyIndexVO != null && CollectionUtil.isNotEmpty(companyIndexVO.getFloorVOS())){
                for (CompanyShopFloorVo floorVo: companyIndexVO.getFloorVOS()) {
                    if (CollectionUtil.isNotEmpty(floorVo.getListProductVos())){
                        ProductDataFilterHelper.fillBlankShop(floorVo.getListProductVos());
                    }
                }
            }
            model.put("orgId", orgId);
            model.put("indexVo", companyIndexVO);
            LOGGER.info("CompanyController.shopIndex.htm参数# orgId:{}, indexVo:{}", orgId, JSON.toJSONString(companyIndexVO));
        } catch (Exception e) {
            LOGGER.error("pop商家列表", e);
        }
        model.put("orgId", orgId);
        return new ModelAndView("/company/shopIndex.ftl", model);
    }


    /**
     * 店铺楼层商品
     * @param orgId
     * @param request
     * @return
     */
    @GetMapping("/company-floor")
    public ModelAndView companyFloor(String orgId, HttpServletRequest request) {
        Map<String, Object> model = new HashMap<>();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant == null || merchant.getId() == null ? NumberUtils.LONG_ZERO : merchant.getId();
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            List<CompanyShopFloorVo> companyShopFloorVos = shopQueryService.companyFloor(orgId, merchantId, branchCode);
            if (CollectionUtil.isNotEmpty(companyShopFloorVos)) {
                for (CompanyShopFloorVo floorVo : companyShopFloorVos) {
                    if (CollectionUtil.isNotEmpty(floorVo.getListProductVos())) {
                        ProductDataFilterHelper.fillBlankShop(floorVo.getListProductVos());
                    }
                }
            }
            model.put("orgId", orgId);
            model.put("floorVos", companyShopFloorVos);
            return new ModelAndView("/company/shop_floor_goods.ftl", model);
        } catch (Exception e) {
            LOGGER.error("pop商家列表", e);
            throw new RuntimeException();
        }
    }

    @Value("${company.shopGood.trace.switch:true}")
    private boolean traceSwitch;
    /**
     * 商品列表
     *
     * @param orgId
     * @return
     */
    @RequestMapping(value = "shopGood.htm", method = RequestMethod.GET)
    public ModelAndView shopGood(String orgId, Integer floorId, Integer floorType, HttpServletRequest request) {
        Map<String, Object> model = new HashMap<>();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            //没有登录强制不选中合作商家
            if (merchant == null || merchant.getId() == null) {
                model.put("islogin", 0);
                merchantId = NumberUtils.LONG_ZERO;
            } else {
                merchantId = merchant.getId();
                model.put("merchant", merchant);
                model.put("merchantId", merchantId);
                model.put("islogin", 1);
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            if (traceSwitch) {
                ShopGoodsVo shopGoodsVo = popShopService.shopGoodV2(orgId, merchantId, floorId, floorType, branchCode);
                LOGGER.info("CompanyController.shopGood.htm参数# orgId:{}, shopGoodsVo:{}", orgId, JSON.toJSONString(shopGoodsVo));
                model.put("orgId", orgId);
                model.put("floorId", floorId);
                model.put("floorType", floorType);
                model.put("shopListProductVos", shopGoodsVo.getShopListProductVos());
                model.put("shopCode", shopGoodsVo.getShopCode());
             }else {
                List<ShopListProductVo> shopListProductVos = popShopService.shopGood(orgId, merchantId, floorId, floorType, branchCode);
                LOGGER.info("CompanyController.shopGood.htm参数# orgId:{}, indexVo:{}", orgId, JSON.toJSONString(shopListProductVos));
                model.put("orgId", orgId);
                model.put("floorId", floorId);
                model.put("floorType", floorType);
                model.put("shopListProductVos", shopListProductVos);
            }
        } catch (Exception e) {
            LOGGER.error("pop商家列表", e);
        }
        model.put("orgId", orgId);
        return new ModelAndView("/company/shopGood.ftl", model);
    }

    /**
     * 全部药品列表
     *
     * @param request
     * @return
     */
    @RequestMapping("/shopSkuInfo.htm")
    public ModelAndView productList(HttpServletRequest request) {
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (Objects.isNull(merchant)) {
                LOGGER.warn("用户未登录，跳转到登录页面.");
                // 未登录，跳转登录页
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            Long merchantId = merchant.getId();

            Boolean isApplySearchIndexV2 = appProperties.getIsApplySearchIndexV2();
            String searchIndexV2Html = null;
            if (BooleanUtils.isTrue(isApplySearchIndexV2) && StringUtils.isNotEmpty(appProperties.getSearchIndexV2PageUrl())) {
                try {
                    // 获取pc搜索首页 html 内容
                    String searchIndexUrl = StringUtils.join(appProperties.getBasePathUrl(), appProperties.getSearchIndexV2PageUrl());
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("pop店铺搜索首页，merchantId：{}，searchIndexUrl：{}", merchantId, searchIndexUrl);
                    }
                    searchIndexV2Html = IOUtils.toString(new URL(searchIndexUrl), "UTF-8");
                } catch (Exception e) {
                    LOGGER.warn("pop店铺获取搜索首页V2 html 异常，使用搜索首页V1，merchantId：{}", merchantId, e);
                    isApplySearchIndexV2 = false;
                }
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("pop店铺搜索首页，merchantId：{}，isApplySearchIndexV2：{}，searchIndexV2Html：{}", merchantId, isApplySearchIndexV2, searchIndexV2Html);
            }

            Map<String, Object> objModel = new HashMap<>();
            objModel.put("merchantId", merchant.getId());
            objModel.put("shopCode", request.getParameter("shopCode"));
            objModel.put("keywordSearch", request.getParameter("keyword"));
            //商品一级分类筛选数据
            objModel.put("categoryFirstId", request.getParameter("categoryFirstId"));
            //商品二级分类筛选数据
            objModel.put("categorySecondId", request.getParameter("categorySecondId"));
            //商品三级分类筛选数据
            objModel.put("categoryThirdId", request.getParameter("categoryThirdId"));

            //获取店铺编码
            String orgId = request.getParameter("orgId");
            ShopInfoVO shopInfoVO = shopService.getByOrgId(orgId);
            if(Objects.isNull(shopInfoVO)) {
                LOGGER.error("未查询到店铺信息，orgId : {}, 用户信息 : {}", orgId, JSONObject.toJSONString(merchant));
                return new ModelAndView();
            }
            String shopCode = shopInfoVO.getShopCode();
            objModel.put("shopCode", shopCode);
            objModel.put("orgId", orgId);
            if (BooleanUtils.isTrue(isApplySearchIndexV2) && StringUtils.isNotEmpty(searchIndexV2Html)) {
                // 获取pc首页cms html 内容
                String searchIndexHtmlAssetsIndexJsRelativePath = SearchUtils.getSearchIndexHtmlAssetsIndexJsRelativePath(searchIndexV2Html);
                String searchIndexHtmlAssetsVendorJsRelativePath = SearchUtils.getSearchIndexHtmlAssetsVendorJsRelativePath(searchIndexV2Html);
                String searchIndexHtmlAssetsIndexCssRelativePath = SearchUtils.getSearchIndexHtmlAssetsIndexCssRelativePath(searchIndexV2Html);
                String searchIndexHtmlContent = SearchUtils.getSearchIndexHtmlBody(searchIndexV2Html);
                objModel.put("searchIndexHtmlAssetsIndexJsRelativePath", searchIndexHtmlAssetsIndexJsRelativePath);
                objModel.put("searchIndexHtmlAssetsVendorJsRelativePath", searchIndexHtmlAssetsVendorJsRelativePath);
                objModel.put("searchIndexHtmlAssetsIndexCssRelativePath", searchIndexHtmlAssetsIndexCssRelativePath);
                objModel.put("searchIndexHtmlContent", searchIndexHtmlContent);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("pop店铺搜索首页，merchantId：{}，searchIndexHtmlAssetsIndexJsRelativePath：{}，searchIndexHtmlAssetsVendorJsRelativePath：{}，searchIndexHtmlAssetsIndexCssRelativePath：{}",
                            merchantId, searchIndexHtmlAssetsIndexJsRelativePath, searchIndexHtmlAssetsVendorJsRelativePath, searchIndexHtmlAssetsIndexCssRelativePath);
                }
                return  new ModelAndView("/company/shop_search_list_v2.ftl", objModel);
            }
            return new ModelAndView("/company/shop_search_list.ftl", objModel);
        } catch (Exception ex) {
            LOGGER.error("/company/center/companyInfo/shopSkuInfo.htm--店铺商品信息查询异常, 用户信息 : {}, 请求参数 : {}, error : ",
                    Objects.nonNull(merchant) ? JSONObject.toJSONString(merchant) : "",
                    JSONObject.toJSONString(request.getParameterMap()), ex);
            return new ModelAndView();
        }
    }
    /**
     * 全部药品列表
     *
     * @param page
     * @param skuPOJO
     * @return
     * @deprecated 使用异步实现 {@link CompanyController#productList(HttpServletRequest)}
     */
    //@RequestMapping("/shopSkuInfo.htm")
    public ModelAndView skuInfo(Page page, SearchCsuDTO skuPOJO, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            Long merchantId = 0L;
            //用户所属域的域编码
            String branchCode;
            if (merchant != null) {
                merchantId = merchant.getId();
                branchCode = merchant.getRegisterCode();
            } else {
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            }
            if (StringUtil.isNotEmpty(skuPOJO.getKeyword()) && ObjectUtils.isEmpty(skuPOJO.getType())) {
                skuPOJO.setType(1);
            }
            if (StringUtil.isNotEmpty(skuPOJO.getKeyword()) && merchantId != 0L) {
                ProductBusinessDto productBusinessDto = new ProductBusinessDto();
                productBusinessDto.setShowName(skuPOJO.getKeyword());
                Long finalMerchantId = merchantId;
                RpcContext.getContext().setAttachment("async", "true").asyncCall(() -> {
                    try {
                        hostSearchBusinessApi.addHotSearch(productBusinessDto, finalMerchantId);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
                RpcContext.getContext().removeAttachment("async");
            }

            skuPOJO.setBranchCode(branchCode);

            Map<String, Object> objModel = new HashMap<>();
            // 分页要用，将url传到前台
            String url = getRequestUrl(request);

            //搜索关键词
            String keyword = request.getParameter("keyword");
            /***********处理搜索关键词**************/
            if (StringUtil.isNotEmpty(keyword)) {
                skuPOJO.setKeyword(keyword);
            }
            //大图模式和列表模式的类型
            String modelTypeStr = request.getParameter("model_type");
            /*********默认大图模式展示搜索页********/
            int modelType = 1;
            if (StringUtil.isNotEmpty(modelTypeStr)) {
                modelType = Integer.parseInt(modelTypeStr);
            }

            //筛选类目展开状态
            String cjZhan = request.getParameter("cjZhan");
            String ejZhan = request.getParameter("ejZhan");
            String sjZhan = request.getParameter("sjZhan");

            /****************处理商品分类**********************/
            //一级分类筛选条件
            String categoryFirstId = request.getParameter("categoryFirstId");
            //二级分类筛选条件
            String categorySecondId = request.getParameter("categorySecondId");
            //三级分类筛选条件
            String categoryThirdId = request.getParameter("categoryThirdId");
            if ("99999".equals(categoryFirstId)) {//当选择的为全部分类时，二级分类和三级分类置空，且展开状态为关闭
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                    categorySecondId = null;
                }
                if (StringUtil.isNotEmpty(categoryThirdId)) {
                    url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                    categoryThirdId = null;
                }
                ejZhan = "2";
                sjZhan = "2";
            }

            Map<Long, CategoryVo> categoryVoMap = categoryBusinessApi.getCategory(branchCode);

            //处理分类的筛选条件---当所选的分类的父类与所选的父类不对应时，清楚该分类的筛选
            //当所选的三级分类不为空时进行判断
            if (StringUtil.isNotEmpty(categoryThirdId)) {
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    //得到所选三级分类信息
                    CategoryBusinessDTO c3 = categoryVoMap.get(Long.parseLong(categoryThirdId));
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {//当所选一级分类不为空时，所选的二三级分类都要和一级分类比较
                        //得到所选二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(Long.parseLong(categorySecondId));
                        //如果所选二级分类的父类即一级分类也不是所选的一级分类，则将二级分类的筛选条件也去掉
                        if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                            categorySecondId = null;
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        } else {
                            //如果所选三级分类的父类即二级分类不是所选的二级分类，则将三级分类的筛选条件去掉
                            if (Long.parseLong(categorySecondId) != c3.getParentId()) {
                                url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                                categoryThirdId = null;
                            }
                        }
                    } else {
                        //如果所选三级分类的父类即二级分类不是所选的二级分类，则将三级分类的筛选条件去掉
                        if (Long.parseLong(categorySecondId) != c3.getParentId()) {
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        }
                    }
                } else {//当所选二级分类为空时，只和所选一记分类比较
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {
                        //得到所选的三级分类信息
                        CategoryBusinessDTO c3 = categoryVoMap.get(Long.parseLong(categoryThirdId));
                        //得到所选三级分类的二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(c3.getParentId());
                        //如果所选三级分类的二级分类的父类即一级分类不是所选的一级分类，则将三级分类的筛选条件去掉
                        if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        }
                    }
                }
            } else {
                //当所选的二级分类不为空时进行判断
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {
                        //得到所选二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(Long.parseLong(categorySecondId));
                        //如果所选二级分类的父类即一级分类不是所选的一级分类，则将二级分类的筛选条件去掉
                        if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                            categorySecondId = null;
                        }
                    }
                }
            }

            //处理所选的一级分类
            if (StringUtil.isNotEmpty(categoryFirstId)) {
                if (!"99999".equals(categoryFirstId)) {
                    skuPOJO.setCategoryFirstId(Long.parseLong(categoryFirstId));
                } else {
                    //当一级分类的筛选条件为99999事搜索的是全部药品，分类的筛选条件置空
                    skuPOJO.setCategoryFirstId(null);
                    skuPOJO.setCategorySecondId(null);
                    skuPOJO.setCategoryThirdId(null);
                }
            }

            //分类树
            //获取缓存中的商品分类集合
            LOGGER.info("merchantId:" + merchantId + "-----branchCode:" + branchCode);

            // 推荐搜索关键字（展示在搜索框下面的）
            objModel.put("styleClass", "");
            String gjz = "";
            String gjz1 = "";

            //将搜索的分类展示在搜索框下面
            if (StringUtil.isNotEmpty(categorySecondId) && !"0".equals(categorySecondId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categorySecondId));
                String categoryName = "";
                if (category != null) {
                    categoryName = category.getName();
                }
                if (StringUtil.isNotEmpty(keyword)) {
                    gjz = keyword + " 、" + categoryName;
                } else if (StringUtil.isNotEmpty(gjz1)) {
                    gjz = gjz1 + " 、" + categoryName;
                } else {
                    gjz = categoryName;
                }

            } else if (StringUtil.isNotEmpty(categoryThirdId) && !"0".equals(categoryThirdId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categoryThirdId));
                String categoryName = "";
                if (category != null) {
                    categoryName = category.getName();
                }
                if (StringUtil.isNotEmpty(keyword)) {
                    gjz = keyword + " 、" + categoryName;
                } else if (StringUtil.isNotEmpty(gjz1)) {
                    gjz = gjz1 + " 、" + categoryName;
                } else {
                    gjz = categoryName;
                }
            } else if (StringUtil.isNotEmpty(categoryFirstId) && !"0".equals(categoryFirstId) && !"99999".equals(categoryFirstId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categoryFirstId));
                String categoryName = "";
                if (category != null) {
                    categoryName = category.getName();
                }
                if (StringUtil.isNotEmpty(keyword)) {
                    gjz = keyword + " 、" + categoryName;
                } else if (StringUtil.isNotEmpty(gjz1)) {
                    gjz = gjz1 + " 、" + categoryName;
                } else {
                    gjz = categoryName;
                }
            } else {
                if (StringUtil.isEmpty(keyword)) {
                    if (StringUtil.isNotEmpty(gjz1)) {
                        gjz = gjz1;
                    } else {
                        gjz = "全部商品";
                    }
                } else {
                    gjz = keyword;
                }
            }

            //获取筛选条件的厂商
            String manufacturer = request.getParameter("manufacturer");
            //获取销量的排序标识
            String orderSort = request.getParameter("order_sort");
            if (StringUtil.isNotEmpty(orderSort)) {
                skuPOJO.setCsuOrder(CsuOrder.SALE);
                if (orderSort.equals(SortOrder.DESC.getIntValue().toString())) {
                    skuPOJO.setSortOrder(SortOrder.DESC);
                } else {
                    skuPOJO.setSortOrder(SortOrder.ASC);
                }
            }

            //获取综合排行的标识
            String orderSaleRank = request.getParameter("order_sale_rank");
            if (StringUtil.isNotEmpty(orderSaleRank)) {
                skuPOJO.setCsuOrder(CsuOrder.DEFAULT);
                if (orderSaleRank.equals(SortOrder.DESC.getIntValue().toString())) {
                    skuPOJO.setSortOrder(SortOrder.DESC);
                } else {
                    skuPOJO.setSortOrder(SortOrder.ASC);
                }
            }

            //获取最新上架排序标识
            String orderTime = request.getParameter("order_time");
            if (StringUtil.isNotEmpty(orderTime)) {
                skuPOJO.setCsuOrder(CsuOrder.CREATE);
                if (orderTime.equals(SortOrder.DESC.getIntValue().toString())) {
                    skuPOJO.setSortOrder(SortOrder.DESC);
                } else {
                    skuPOJO.setSortOrder(SortOrder.ASC);
                }
            }

            //获取价格排序标识
            String orderFob = request.getParameter("order_fob");
            if (StringUtil.isNotEmpty(orderFob)) {
                skuPOJO.setCsuOrder(CsuOrder.FOB);
                if (orderFob.equals(SortOrder.DESC.getIntValue().toString())) {
                    skuPOJO.setSortOrder(SortOrder.DESC);
                } else {
                    skuPOJO.setSortOrder(SortOrder.ASC);
                }
            }

            //如果销量排序、最新上架排序、价格排序都为空，默认人气排序
            if (StringUtil.isEmpty(orderSort) && StringUtil.isEmpty(orderTime) && StringUtil.isEmpty(orderFob)) {
                if (orderSaleRank == null) {
                    orderSaleRank = "1";
                    skuPOJO.setCsuOrder(CsuOrder.DEFAULT);
                    skuPOJO.setSortOrder(SortOrder.DESC);
                }
            }
            //只看有货筛选条件
            String hasStock = request.getParameter("has_stock");
            if (hasStock != null) {
                skuPOJO.setHasStock(Integer.valueOf(hasStock));
            }
            //处方分类筛选条件
            String drugClassification = request.getParameter("drugClassification");
            if (StringUtil.isNotEmpty(drugClassification)) {
                if ("5".equals(drugClassification)) {
                    skuPOJO.setDrugClassificationList(null);
                } else {
                    skuPOJO.setDrugClassificationList(Lists.newArrayList(Integer.parseInt(drugClassification)));
                }
            }
            /****************价格区间的筛选条件********************/
            String minPrice = request.getParameter("minPrice");
            String maxPrice = request.getParameter("maxPrice");
            //设置筛选的价格
            if (StringUtil.isNotEmpty(minPrice)) {
                skuPOJO.setMinPrice(Double.parseDouble(minPrice));
            }
            if (StringUtil.isNotEmpty(maxPrice)) {
                skuPOJO.setMaxPrice(Double.parseDouble(maxPrice));
            }

            /*******************获取厂家列表*************************/
            List<String> manufacturerList = Collections.emptyList();
            skuPOJO.setMerchantId(merchantId);
            ApiRPCResult apiRPCResult = searchApi.manufacturerList(skuPOJO);
            if (apiRPCResult.isFail()) {
                LOGGER.info("调用厂家接口异常 code : {}, msg : {}, errMsg : {}", apiRPCResult.getCode(), apiRPCResult.getMsg(), apiRPCResult.getErrMsg());
            }
            manufacturerList = (List<String>) apiRPCResult.getData();
            /*******************获取商品列表**********************/
            com.xyy.ec.search.engine.pagination.Page newPage = new com.xyy.ec.search.engine.pagination.Page();
            newPage.setPageSize(20);
            newPage.setPageNo(page.getOffset() <= 0 ? 1 : page.getOffset());

            //判断是否在自配区, false-表示自配区，true-非自配区，自配区用户才能查看易碎商品
            boolean isShowFragileGoods = merchantBussinessApi.checkFragileLimitedByMerchantId(merchantId);
            //易碎品是否可见
            if (!isShowFragileGoods) {
                skuPOJO.setIsShowFragileGoods(1);
            }
            LOGGER.info("params : {}", JsonUtil.toJson(skuPOJO));
            ApiRPCResult searchApiRPCResult = searchApi.searchForPC(newPage, skuPOJO);
            // 商品组探活埋点
            productExhibitionGroupBusinessAdminRemoteService.asyncSendExhibitionLiveEventMQForSearch(skuPOJO);
            if (searchApiRPCResult.isFail()) {
                LOGGER.error("调用搜索接口异常 code : {}, msg : {}, errMsg : {}", searchApiRPCResult.getCode(), searchApiRPCResult.getMsg(), searchApiRPCResult.getErrMsg());
                throw new XyyEcSearchBizCheckException("搜索接口调用异常");
            }
            com.xyy.ec.search.engine.pagination.Page pageInFo = (com.xyy.ec.search.engine.pagination.Page) searchApiRPCResult.getData();
            if (pageInFo != null) {
                List<CsuInfo> csuInfoList = pageInFo.getRecordList();

                if (CollectionUtils.isNotEmpty(csuInfoList)) {
                    //商户资质状态，1和5时修改fob为0
                    handleCsuInfoFob(csuInfoList, merchant);
                }
            }
            //截断返回当前推荐结果的搜索词
            String blockWord = "";
            List<String> wordList = new ArrayList<>();
            Map<String, Object> extendsMap = pageInFo.getExtendsMap();
            Integer type = (Integer) extendsMap.get("type");
            if (type != null) {
                switch (type) {
                    case 2:
                        wordList = (List<String>) extendsMap.get("wordList");
                        blockWord = wordList != null && wordList.size() > 0 ? wordList.get(0) : "";
                        wordList = wordList != null && wordList.size() > 1 ? wordList.subList(1, wordList.size()) : new ArrayList<>();
                        break;
                    case 3:
                        //兜底推荐，返回搜索热搜词
//                    List<HotSearchBusinessDto> hotSearchBusinessDtos = hostSearchBusinessApi.finhotSearchlist(merchantId, 1, branchCode);
//                    List<String> hotList = hotSearchBusinessDtos.stream().map(HotSearchBusinessDto::getKeyword).collect(Collectors.toList());
//                    wordList = hotList != null && hotList.size() > 0 ? hotList.subList(0, hotList.size() > Constants.MAX_HOT_WORD_SIZE ? Constants.MAX_HOT_WORD_SIZE : hotList.size()) : new ArrayList<>();
                        if (!url.contains("type=3")) {
                            url += "&type=" + 3;
                        }
                        break;
                }
            }

            //兼容老的Page
            Page<ProductDto> skuPOJOPage = new Page<>();
            skuPOJOPage.setLimit((int) pageInFo.getPageSize());
            skuPOJOPage.setTotal(pageInFo.getTotalCount());
            skuPOJOPage.setRows(pageInFo.getRecordList());
            skuPOJOPage.setOffset((int) pageInFo.getPageNo());
            skuPOJOPage.setRequestUrl(url);
            List<CategoryVo> listCategory = categoryBusinessApi.getCategoryTree(branchCode);
            //处理店铺编码
            String shopCode = "";
            if (Objects.nonNull(pageInFo) && CollectionUtil.isNotEmpty(pageInFo.getRecordList())) {
                CsuInfo csuInfo = (CsuInfo) pageInFo.getRecordList().get(0);
                shopCode = csuInfo.getShopCode();
            } else {
                ShopInfoVO shopInfoVO = shopService.getByOrgId(skuPOJO.getOrgId());
                shopCode = shopInfoVO.getShopCode();
            }
            objModel.put("shopCode", shopCode);
            objModel.put("orgId", skuPOJO.getOrgId());

            objModel.put("count", skuPOJOPage.getTotal());
            objModel.put("listCategory", listCategory);
            //商品搜索页分页数据
            objModel.put("pager", skuPOJOPage);
            //商品一级分类筛选数据
            objModel.put("categoryFirstId", categoryFirstId);
            //商品二级分类筛选数据
            objModel.put("categorySecondId", categorySecondId);
            //商品三级分类筛选数据
            objModel.put("categoryThirdId", categoryThirdId);
            //用户数据
            objModel.put("merchant", merchant);
            //用户id
            objModel.put("merchantId", merchantId);
            //厂家数据
            objModel.put("manufacturerList", manufacturerList);
            //筛选的厂家数据
            objModel.put("manufacturer", manufacturer);
            //销量筛选条件
            objModel.put("orderSort", orderSort);
            //人气筛选条件
            objModel.put("orderSaleRank", orderSaleRank);
            //最新上架筛选条件
            objModel.put("orderTime", orderTime);
            //价格筛选条件
            objModel.put("orderFob", orderFob);
            //我的控销筛选条件
//            objModel.put("isControl", isControl);
            //是否有货筛选条件
            objModel.put("hasStock", hasStock);
            //处方类型
            objModel.put("drugClassification", drugClassification);
            //最小价格筛选条件
            objModel.put("minPrice", minPrice);
            //最大价格的筛选条件
            objModel.put("maxPrice", maxPrice);
            //三级分类是否展开的标识
            objModel.put("cjZhan", StringUtil.isNotEmpty(cjZhan) ? Integer.parseInt(cjZhan) : 0);
            objModel.put("ejZhan", StringUtil.isNotEmpty(ejZhan) ? Integer.parseInt(ejZhan) : 0);
            objModel.put("sjZhan", StringUtil.isNotEmpty(sjZhan) ? Integer.parseInt(sjZhan) : 0);
            //大图模式和列表模式的类型
            objModel.put("modelType", modelType);
            //关键字（展示当前搜索的关键字用的）
            objModel.put("keyword", gjz);
            //搜索关键字（填充搜索框用的）
            objModel.put("keywordSearch", keyword);
            objModel.put("styleClassa", "cur");
            objModel.put("searchType", type == null ? 1 : type);
            objModel.put("blockWord", blockWord);
            objModel.put("wordList", wordList);
            return new ModelAndView("/company/shop_search_list.ftl", objModel);
        } catch (Exception e) {
            LOGGER.error("/company/center/companyInfo/shopSkuInfo.htm--店铺商品信息查询异常, 请求参数 : {}, error : ", JSONObject.toJSONString(skuPOJO), e);
            return new ModelAndView();
        }
    }

    //通过登录资质处理商品价格
    public void handleCsuInfoFob(List<CsuInfo> csuInfoList, MerchantBussinessDto merchant) {
        if (merchant != null) {
            merchant = appVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
        }
        if (merchant == null || !merchant.getPriceDisplayFlag()) {
            if (CollectionUtil.isNotEmpty(csuInfoList)) {
                for (CsuInfo csuInfo : csuInfoList) {
                    csuInfo.setFob(0.0);
                    //商品标签
                    CsuActivityTag productActivityTagVO = new CsuActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    csuInfo.setActivityTag(productActivityTagVO);
                    //毛利
                    csuInfo.setGrossMargin("");
                    //建议零售价
                    csuInfo.setSuggestPrice(BigDecimal.ZERO);
                    //控销零售价
                    csuInfo.setUniformPrice(BigDecimal.ZERO);
                    //对比价
                    csuInfo.setRetailPrice(0.0);
                }
            }
        }
    }

    /**
     * 店铺关于我们的页面
     *
     * @param orgId
     * @return
     */
    @RequestMapping(value = "shopIntroduce.htm", method = RequestMethod.GET)
    public ModelAndView shopIntroduce(String orgId) {
        Map<String, Object> model = new HashMap<String, Object>();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            //没有登录强制不选中合作商家
            if (merchant == null || merchant.getId() == null) {
                model.put("islogin", 0);
            } else {
                merchantId = merchant.getId();
                model.put("merchant", merchant);
                model.put("merchantId", merchantId);
                model.put("islogin", 1);
            }
        } catch (Exception e) {
            LOGGER.error("pop商家介绍", e);
        }
        model.put("orgId", orgId);
        return new ModelAndView("/company/about.ftl", model);
    }


    /**
     * 开户流程<br/>
     *
     * @return
     */
    @RequestMapping(value = "openAccount.htm", method = RequestMethod.GET)
    public ModelAndView openAccount(String orgId) {
        Map<String, Object> model = new HashMap<>();
        try {
            model.put("orgId", orgId);

            //查询POP开户流程
            ClientAccountInfoExDto rpcdata = clientAccountServerRpc.getClientAccountInfoByOrgId(orgId);
            //转化下 防止前端页码错乱
            ClientAccountInfo data = JSONObject.parseObject(JSONObject.toJSONString(rpcdata), ClientAccountInfo.class);
            if (null != data) {
                if (CollectionUtils.isNotEmpty(data.getClientResourcesList())) {
                    for (ClientResources clientResources : data.getClientResourcesList()) {
                        clientResources.setResourceName(clientResources.getResourceName().replace("\n", "<br />"));
                        String[] s = clientResources.getResourceDescription().split("\n");

                        StringBuilder sb = new StringBuilder();
                        for (String value : s) {
                            sb.append("<p class=\"cent_rong\">");
                            sb.append(value);
                            sb.append("</p>");
                        }
                        clientResources.setResourceDescription(sb.toString());
                    }
                }
                if (data.getClientExplanation() != null) {
                    data.getClientExplanation().setExplanationContent(data.getClientExplanation().getExplanationContent().replace("\n", "<br />").replace("*", "</i><span style=\"color: red\" class=\"xing\">*</span>"));
                }
            }
            model.put("data", data);
        } catch (Exception e) {
            LOGGER.error("调用POP查询开户流程失败：" + orgId, e);
        }
        return new ModelAndView("/company/openAccount.ftl", model);
    }
}
