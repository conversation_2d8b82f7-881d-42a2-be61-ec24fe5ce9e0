package com.xyy.ec.pc.controller.order;

import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.backend.order.dto.OrderShopHistoryDTO;
import com.xyy.ec.order.backend.order.dto.OrderShopHistoryQueryDTO;
import com.xyy.ec.order.search.api.remote.order.OrderSearchApi;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.constants.MatchErrorEnum;
import com.xyy.ec.pc.constants.RedisConstants;
import com.xyy.ec.pc.controller.vo.*;
import com.xyy.ec.pc.exception.XyyExcelException;
import com.xyy.ec.pc.model.excel.PurchasePlanImportTemplate;
import com.xyy.ec.pc.model.excel.QuotationTemplate;
import com.xyy.ec.pc.remote.ShopQueryRemoteService;
import com.xyy.ec.pc.rest.ResponseVo;
import com.xyy.ec.pc.rpc.OrderServerRpcService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.order.MatchPriceService;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.util.excel.ExcelImportUtils;
import com.xyy.framework.redis.autoconfigure.core.RedisClient;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量匹价（批量采购）
 */
@RequestMapping("/matchPrice")
@Controller
public class MatchPriceController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(MatchPriceController.class);

	@Autowired
	private XyyIndentityValidator xyyIndentityValidator;
	@Autowired
	private MatchPriceService matchPriceService;

	@Autowired
	private RedisClient redisClient;

	@Autowired
	private PcVersionUtils appVersionUtils;
	@Autowired
	private XyyConfig xyyConfig;

	@Value("${delMatchProgress:false}")
	private boolean delMatchProgress;

	@Reference(version = "1.0.0")
	private OrderSearchApi orderSearchApi;

	@Autowired
	private ShopQueryRemoteService shopQueryRemoteService;

	@Autowired
	private OrderServerRpcService orderServerRpcService;
	/**
	 * 批量采购初始页
	 * @param request
	 * @return
	 */
	@RequestMapping("/batchPurchaseIndex.htm")
	public Object batchPurchaseIndex(HttpServletRequest request) {
		try {
			ModelMap modelMap = new ModelMap();
			MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchant == null) {
				return this.addError("请登录后再试！");
			}
			Long merchantId = 0L;
			if (merchant != null) {
				merchantId = merchant.getId();
			}
			merchant = appVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
			if(!merchant.getPriceDisplayFlag()){//资质判断
				return this.addError("请完成资质认证");
			}
			String errorMsg = request.getParameter("errorMsg");
			if (StringUtil.isNotEmpty(errorMsg)) {
				modelMap.put("errorMsg", errorMsg);
			}
			modelMap.put("merchant", merchant);
			modelMap.put("merchantId", merchantId);
			return new ModelAndView("/cart/batchPurchase.ftl", modelMap);
		} catch (Exception e) {
			LOGGER.error("访问批量采购静态页失败", e);
			return new ModelAndView("/error/500.ftl");
		}
	}

	/**
	 * 刷新匹配结果
	 * @param matchParamVO
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/refreshPurchasePlan")
    @ResponseBody
    public Object refreshPurchasePlan(@RequestBody MatchParamVO matchParamVO) throws Exception{
		String canBuyStr = matchParamVO.getMatchBaseParamVO().getCanBuyStr();
		if ("deficiency".equals(canBuyStr)) {
			return this.selectDeficiency();
		} else {
			if ("canBuy".equals(canBuyStr)) {
				matchParamVO.getMatchBaseParamVO().setCanBuyIs(true);
			} else if ("notCanBuy".equals(canBuyStr)) {
				matchParamVO.getMatchBaseParamVO().setCanBuyIs(false);
			}
			return this.sourceReFreshPurchasePlan(matchParamVO);
		}
	}

	public Map<String, Object> selectDeficiency() {
		MerchantBussinessDto merchantBussinessDto;
		try {
			merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return this.addError("请登录后再试！");
			}
			merchantBussinessDto = appVersionUtils.getPriceDisplayFlagByMerchantId(merchantBussinessDto);
			if(!merchantBussinessDto.getPriceDisplayFlag()){//资质判断
				return this.addError("请完成资质认证");
			}
			MatchPriceResultDTO resultDTO = orderServerRpcService.selectImportDeficiencyProduct(merchantBussinessDto.getId());
			return this.addResult("result", resultDTO);
		} catch (Exception e) {
			return this.addError("刷新匹配结果异常");
		}
	}


	private Map<String, Object> sourceReFreshPurchasePlan(MatchParamVO matchParamVO) {
		MatchRuleParamVO matchRuleParamVO = matchParamVO.getMatchRuleParamVO();
		MerchantBussinessDto merchantBussinessDto = null;
		try {
			merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return this.addError("请登录后再试！");
			}
			merchantBussinessDto = appVersionUtils.getPriceDisplayFlagByMerchantId(merchantBussinessDto);
			if(!merchantBussinessDto.getPriceDisplayFlag()){//资质判断
				return this.addError("请完成资质认证");
			}
			if(delMatchProgress){
				redisClient.del(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			}
			String result = redisClient.get(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			if(StringUtils.isNotBlank(result) && Integer.parseInt(result) < 10000){//匹配中
				return this.addError("匹配中，请稍候重试！");
			}
			redisClient.setEx(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()), "0",RedisConstants.PRE_REMINDER_EXPIRE_ONLINE);
			matchRuleParamVO.setMerchantId(merchantBussinessDto.getId());
			MatchPriceResultDTO resultDTO = matchPriceService.selectPurchaseList(matchRuleParamVO);
			//处理重复数据提示 过滤数据
			checkRepeatAndFilterProduct(resultDTO, matchParamVO);

			/*
			 新增逻辑，返回前端之前，先确认一下不可买原因等信息，为了导出功能
			 */
			try {
				for (MatchLineVO matchLineVo : resultDTO.getMatchSkuList()) {
					// 如果NoMatchMsg有值，说明上面方法已经设置过，不要覆盖
					if (StringUtils.isBlank(matchLineVo.getNoMatchMsg()) && matchLineVo.getNoMatchFlag() != null) {
						matchLineVo.setNoMatchMsg(MatchErrorEnum.valueOf(matchLineVo.getNoMatchFlag()).getDetail());
					}
					if (matchLineVo.getSkus() != null && CollectionUtils.isNotEmpty(matchLineVo.getSkus())) {
						boolean hasChange = false;
						for (MatchSkuVO matchSkuVo : matchLineVo.getSkus()) {
							if (!Objects.equals(matchSkuVo.getQty(), matchLineVo.getExcelBuyNum())) {
								matchSkuVo.setAutoAdjustNum(1);
								hasChange = true;
							}
						}
						matchLineVo.setAutoAdjustNum(hasChange ? 1 : 0);
					}
				}
			} catch (Exception e) {
				LOGGER.info("确认不可买原因等信息异常", e);
			}
			redisClient.setEx(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()), "10000",RedisConstants.PRE_REMINDER_EXPIRE_ONLINE);
			return this.addResult("result", resultDTO);
		} catch (Exception e) {
			LOGGER.error("刷新匹配结果异常",e);
			if(merchantBussinessDto != null){ //匹配异常删除匹配中标识
				redisClient.del(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			}
			return this.addError("刷新匹配结果异常");
		}
	}

	private void checkRepeatAndFilterProduct(MatchPriceResultDTO resultDTO,MatchParamVO matchParamVO){
		if(resultDTO != null){
			List<MatchLineVO> matchLineVOList = resultDTO.getMatchSkuList();

			if(CollectionUtil.isNotEmpty(matchLineVOList)){
				List<String> shopCodes = new ArrayList<>();
				//查询90天内购买过的店
				if (Objects.nonNull(matchParamVO.getMatchBaseParamVO())&&Objects.nonNull(matchParamVO.getMatchBaseParamVO().getBuyShopIs())){
					OrderShopHistoryQueryDTO queryDTO = new OrderShopHistoryQueryDTO();
					queryDTO.setMerchantId(matchParamVO.getMatchRuleParamVO().getMerchantId());
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(new Date());
					calendar.add(Calendar.DATE, -90);
					queryDTO.setBeginTime(calendar.getTime());
					List<OrderShopHistoryDTO> data = orderServerRpcService.getOrderShopHistory(queryDTO);
					shopCodes = Optional.ofNullable(data).orElse(new ArrayList<>())
							.stream()
							.filter(dto -> StringUtils.isNotBlank(dto.getShopCode()))
							.map(OrderShopHistoryDTO::getShopCode)
							.collect(Collectors.toList());
				}


				//匹配到有小计的商品
				Map<Long,Integer> skuLineNumMap = new HashMap<>();
				//匹配到没有小计的商品
				Set<String> noSubTotalList = new HashSet<>();

				Iterator<MatchLineVO> iterator = matchLineVOList.iterator();

				while(iterator.hasNext()){
					MatchLineVO matchLineVO = iterator.next();
					//过滤基础查询条件
					if(filterProduct(matchLineVO,matchParamVO,shopCodes)){
						iterator.remove();
						continue;
					}
					if(CollectionUtil.isNotEmpty(matchLineVO.getSkus())){
						MatchSkuVO matchSkuVO = matchLineVO.getSkus().get(0);
						if(matchSkuVO.getSubTotal() != null){
							skuLineNumMap.put(matchSkuVO.getSkuId(),matchLineVO.getLineNum());
						}else{
							noSubTotalList.add(matchSkuVO.getSkuId()+"_"+matchLineVO.getLineNum());
						}
					}
				}


				for(MatchLineVO matchLineVO : matchLineVOList){
					if(CollectionUtil.isNotEmpty(matchLineVO.getSkus())){
						matchLineVO.getSkus().forEach(matchSkuVO -> {
							if(matchSkuVO.getSubTotal() != null){
								skuLineNumMap.put(matchSkuVO.getSkuId(),matchLineVO.getLineNum());
							}else{
								noSubTotalList.add(matchSkuVO.getSkuId()+"_"+matchLineVO.getLineNum());
							}
						});
					}
				}
				if(CollectionUtil.isNotEmpty(noSubTotalList)){
					for(MatchLineVO matchLineVO : matchLineVOList){
						if(CollectionUtil.isNotEmpty(matchLineVO.getSkus())){
							MatchSkuVO matchSkuVO = matchLineVO.getSkus().get(0);
							if(matchSkuVO.getSubTotal() == null && noSubTotalList.contains(matchSkuVO.getSkuId()+"_"+matchLineVO.getLineNum())){
								matchLineVO.setNoMatchFlag(MatchErrorEnum.TYPE_14.getType());
								matchLineVO.setNoMatchMsg(String.format(MatchErrorEnum.TYPE_14.getDetail(),skuLineNumMap.get(matchSkuVO.getSkuId()).toString()));
								matchLineVO.setSkus(Lists.newArrayList());
							}
						}
					}
				}
			}
		}
	}


	/**
	 * 过滤商品 供应商 是否可买 近效期 有优惠券 买过的店
	 * @param matchLineVO
	 * @param matchParamVO
	 * @return
	 */
	private Boolean filterProduct(MatchLineVO matchLineVO, MatchParamVO matchParamVO,List<String> shopCodes) {
		MatchBaseParamVO matchBaseParamVO = matchParamVO.getMatchBaseParamVO();
		//核心筛选参数无效，不进行筛选数据
		if (Objects.isNull(matchBaseParamVO)) {
			return false;
		}
		//通过未匹配到商品类型 判断是否能购买
		if (Objects.nonNull(matchBaseParamVO.getCanBuyIs()) && Objects.equals(matchBaseParamVO.getCanBuyIs(), Objects.nonNull(matchLineVO.getNoMatchFlag()))) {
			return true;
		}

		// 有用筛选条件:供应商  近效期 有优惠券 买过的店
		if (CollectionUtil.isNotEmpty(matchBaseParamVO.getShopCodes()) || (Objects.nonNull(matchBaseParamVO.getNearEffectiveFlag()) && matchBaseParamVO.getNearEffectiveFlag() > 0)
				|| Objects.nonNull(matchBaseParamVO.getHasCouponIs()) || Objects.nonNull(matchBaseParamVO.getBuyShopIs())) {
			// 无sku数据，进行剔除
			if (CollectionUtil.isEmpty(matchLineVO.getSkus())) {
				return true;
			}
			MatchSkuVO matchSkuVO = matchLineVO.getSkus().get(0);

			if (CollectionUtil.isNotEmpty(matchBaseParamVO.getShopCodes()) && !matchBaseParamVO.getShopCodes().contains(matchSkuVO.getShopCode())) {
				return true;
			}
			if (matchBaseParamVO.getNearEffectiveFlag() > 0 && !Objects.equals(matchBaseParamVO.getNearEffectiveFlag(), matchSkuVO.getNearEffectiveFlag())) {
				return true;
			}
			if (Objects.nonNull(matchBaseParamVO.getHasCouponIs()) && !Objects.equals(matchBaseParamVO.getHasCouponIs(), CollectionUtil.isNotEmpty(matchSkuVO.getOrderedUnClaimed()))) {
				return true;
			}
			if (Objects.nonNull(matchBaseParamVO.getBuyShopIs())) {
				return !matchBaseParamVO.getBuyShopIs().equals(shopCodes.contains(matchSkuVO.getShopCode()));
			}
		}
		return false;
	}



	/**
	 * 查询匹配进度
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/getMatchProgress")
	@ResponseBody
	public Object getMatchProgress() throws Exception{
		try {
			MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return this.addError("请登录后再试！");
			}
			String result = redisClient.get(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			if(StringUtil.isNotBlank(result)){
				int progress = Integer.parseInt(result)/100;
				return this.addResult("result", progress);
			}
			return this.addResult("result", result);
		} catch (Exception e) {
			LOGGER.error("查询匹配进度异常",e);
			return this.addError("查询匹配进度异常");
		}
	}

	/**
	 * 清除匹配进度
	 * @return
	 * @throws Exception
	 */
	@RequestMapping("/clearMatchProgress")
	@ResponseBody
	public Object clearMatchProgress() throws Exception{
		try {
			MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return this.addError("请登录后再试！");
			}
			redisClient.del(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			return this.addResult("result", true);
		} catch (Exception e) {
			LOGGER.error("清除匹配进度异常",e);
			return this.addError("清除匹配进度");
		}
	}

	/**
	 * 选中商品、修改采购数时刷新匹价列表
	 * @param refreshParamVO
	 * @return
	 */
	@RequestMapping(value = "/selectMatchProduct", method = RequestMethod.POST)
	@ResponseBody
	public Object selectMatchSku(@RequestBody SelectMatchProductParamDTO refreshParamVO, HttpServletRequest request) {
		try {
			if (refreshParamVO == null) {
				return this.addError("参数错误！");
			}
			MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			List<MatchLineVO> matchSkuList = refreshParamVO.getMatchLineVOs();
			MatchPriceResultDTO resultDTO = matchPriceService.matchPriceCalculate(matchSkuList, merchantBussinessDto.getId());
			//填充选中数据
			fillSelectInfo(resultDTO);
			return this.addResult("result", resultDTO);
		}catch (Exception ex){
			ex.printStackTrace();
			return this.addError(ex.getMessage());
		}

	}

	/**
	 * 填充商家数量,品种数量，总数量
	 * @param resultDTO
	 */
	private void fillSelectInfo(MatchPriceResultDTO resultDTO){
		Set<String> shopCodes = new HashSet<>();
		Set<Long> skuIdList = new HashSet<>();
		List<Integer> qty = new ArrayList<>();
		if(resultDTO != null){
			List<MatchLineVO> matchSkuList = resultDTO.getMatchSkuList();
			if(CollectionUtil.isNotEmpty(matchSkuList)){
				for(MatchLineVO matchLineVO : matchSkuList){
					List<MatchSkuVO> matchSkuVOList = matchLineVO.getSkus();
					if(matchLineVO.getNoMatchFlag() == null && CollectionUtil.isNotEmpty(matchSkuVOList)){
						matchSkuVOList.forEach(matchSkuVO -> {
							if(matchSkuVO.getSkuId() != null && matchSkuVO.getSelectStatus() != null && matchSkuVO.getSelectStatus() == Constants.IS1){
								shopCodes.add(matchSkuVO.getShopCode());
								skuIdList.add(matchSkuVO.getSkuId());
								qty.add(matchSkuVO.getQty());
							}
						});
					}
				}
			}
		}
		resultDTO.setShopCount(shopCodes.size());
		resultDTO.setProductSelectedNum(skuIdList.size());
		resultDTO.setProductTotalNum(qty.stream().mapToInt(Integer::intValue).sum());
	}


	/**
	 * 获取表信息
	 * @param file
	 * @return
	 */
	@RequestMapping(value = "/getTableInformation", method = RequestMethod.POST)
	@ResponseBody
	public ResponseVo<TableInformationVO> getTableInformation(@RequestParam("file") MultipartFile file) throws Exception {
		TableInformationVO headNamesFromExcel = ExcelImportUtils.getHeadNamesFromExcel(file, xyyConfig.getImportMaxSize());
		return ResponseVo.successResult(headNamesFromExcel);
	}

	/**
	 * 导入采购计划单
	 *
	 * @param file
	 * @return
	 */
	@RequestMapping(value = "/importPurchasePlan", method = RequestMethod.POST)
	@ResponseBody
	public ResponseVo<String> importPurchasePlan(@RequestParam("file") MultipartFile file, PurchaseExcelMappingVO purchaseExcelMappingVO) {
		MerchantBussinessDto merchantBussinessDto = null;

		try {
			merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
		} catch (Exception e) {
			LOGGER.error("MatchPriceController.importPurchasePlan#error", e);
		}
		if (merchantBussinessDto == null) {
			return ResponseVo.errResult("请登陆后再试！");
		}
		try {
			List<PurchasePlanImportTemplate> purchasePlanImportTemplates = this.importPurchasePlanExcel(file,purchaseExcelMappingVO);
			if (CollectionUtils.isEmpty(purchasePlanImportTemplates)) {
				return ResponseVo.errResult("导入数据为空！");
			}
			String filePath = matchPriceService.importPurchasePlan(purchasePlanImportTemplates, merchantBussinessDto);
			if (StringUtils.isNotBlank(filePath)) {
				return ResponseVo.successResult(filePath);
			}
			return ResponseVo.successResultNotData();
		} catch (XyyExcelException e) {
			LOGGER.error("MatchPriceController.importPurchasePlan#自定义异常.导入采购单失败", e);
			return ResponseVo.errResult(e.getMessage());
		} catch (Exception e) {
			LOGGER.error("MatchPriceController.importPurchasePlan#error.导入采购单失败", e);
			return ResponseVo.errResult("导入采购单失败");
		}
	}

	private synchronized List<PurchasePlanImportTemplate> importPurchasePlanExcel(MultipartFile file, PurchaseExcelMappingVO purchaseExcelMappingVO){
		List<PurchasePlanImportTemplate> purchasePlanImportTemplates = null;
		try {
			// 转换表头
			 ExcelImportUtils.excelNameChange(PurchasePlanImportTemplate.class, BeanUtils.describe(purchaseExcelMappingVO));
			 purchasePlanImportTemplates = ExcelImportUtils.importExcel(file, PurchasePlanImportTemplate.class, new ImportParams(), xyyConfig.getImportMaxSize(), xyyConfig.getImportMaxRows(),false);

		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return purchasePlanImportTemplates;
	}

	/**
	 * 查询匹价店铺列表 自营+pop
	 *
	 * @return
	 */
	@RequestMapping(value = "/matchPriceRuleShopList", method = RequestMethod.GET)
	@ResponseBody
	public ResponseVo<MatchPriceShopVO> matchPriceRuleShopList() {
		LOGGER.info("MatchPriceController.matchPriceRuleShopList#");
		try {
			MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return ResponseVo.errResult("请登陆后再试");
			}
			MatchPriceShopVO matchPriceShopVO = matchPriceService.matchPriceRuleShopList(merchantBussinessDto.getId());

			return ResponseVo.successResult(matchPriceShopVO);
		} catch (Exception e) {
			LOGGER.error("MatchPriceController.matchPriceRuleShopList#异常", e);
			return ResponseVo.errResult("查询匹价店铺列表失败");
		}
	}


	/**
	 * 查询规则
	 */
	@RequestMapping("/getProcureMatchRule")
	@ResponseBody
	public ResponseVo<ProcureMatchRuleVO> getProcureMatchRule(){
		MerchantBussinessDto merchantBussinessDto = null;
		try {
			merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return ResponseVo.errResult("请登陆后再试");
			}
			ProcureMatchRuleVO procureMatchRuleVO =	matchPriceService.getProcureMatchRule(merchantBussinessDto.getId());
			return ResponseVo.successResult(procureMatchRuleVO);
		} catch (Exception e) {
			LOGGER.error("MatchPriceController.getProcureMatchRule#异常", e);
			return ResponseVo.errResult("查询用户规则失败");
		}
	}


	/**
	 * 保存规则
	 *
	 * @param matchRuleParamVO
	 * @return
	 */
	@RequestMapping("/saveMatchRule")
	@ResponseBody
	public ResponseVo saveMatchRule(@RequestBody MatchRuleParamVO matchRuleParamVO){
		MerchantBussinessDto merchantBussinessDto = null;
		try {
			merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return ResponseVo.errResult("请登陆后再试");
			}
			matchRuleParamVO.setMerchantId(merchantBussinessDto.getId());
			matchPriceService.saveMatchRule(matchRuleParamVO);
			return ResponseVo.successResultNotData();
		} catch (Exception e) {
			LOGGER.error("MatchPriceController.saveMatchRule#异常", e);
			return ResponseVo.errResult("保存规则失败");
		}
	}


	/**
	 * 修改采购单商品购买数量
	 *
	 * @param excelId
	 * @param qty
	 * @return
	 */
	@RequestMapping(value = "/updateStock", method = RequestMethod.POST)
	@ResponseBody
	public ResponseVo updateStock(@RequestParam Long excelId, @RequestParam Integer qty) {
		LOGGER.info("MatchPriceController.updateStock#excelId:{},qty:{}", excelId, qty);
		try {
			MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return ResponseVo.errResult("请登陆后再试");
			}
			if (excelId == null || qty == null) {
				return ResponseVo.errResult("参数不能为空");
			}
			if (qty <= 0) {
				return ResponseVo.errResult("库存必须为正整数");
			}
			int num = matchPriceService.updateImportStock(merchantBussinessDto.getId(), excelId, qty);
			if (num == -1) {
				return ResponseVo.errResult("请刷新列表后重试");
			}
			return ResponseVo.successResult(num);
		} catch (Exception e) {
			LOGGER.error("MatchPriceController.updateStock#excelId:{},qty:{}", excelId, qty, e);
			return ResponseVo.errResult("修改采购库存失败");
		}
	}

	/**
	 * 快速搜索
	 *
	 * @param matchQuickSearchParamDto
	 * @return
	 */
	@RequestMapping(value = "/quickSearchProduct", method = RequestMethod.POST)
	@ResponseBody
	public ResponseVo<MatchQuickSearchResultVo> quickSearchProduct(@RequestBody MatchQuickSearchParamDTO matchQuickSearchParamDto) {
		try {
			MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null || merchantBussinessDto.getId() == null) {
				return ResponseVo.errResult("请登陆后再试！");
			}
			if (matchQuickSearchParamDto == null) {
				return ResponseVo.errResult("参数错误！");
			}
			//匹价排序会用到，如果前端不传默认给1
			if (matchQuickSearchParamDto.getLineNum() == null) {
				matchQuickSearchParamDto.setLineNum(1);
			}
			LOGGER.info("MatchPriceController.quickSearchProduct param：{}", JSON.toJSONString(matchQuickSearchParamDto));
			//极速搜索
			MatchQuickSearchResultVo matchQuickSearchResultVo = matchPriceService.quickSearchProduct(matchQuickSearchParamDto, merchantBussinessDto.getId());
			return ResponseVo.successResult(matchQuickSearchResultVo);
		} catch (Exception ex) {
			LOGGER.error("MatchPriceController.quickSearchProduct#快速搜索失败 param：{}", JSON.toJSONString(matchQuickSearchParamDto), ex);
			return ResponseVo.errResult("快速搜索失败");
		}
	}

	/**
	 * 下载报价单
	 *
	 * @param matchParamVO
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/downloadQuotation")
	@ResponseBody
	public ResponseVo downloadQuotation(@RequestBody MatchParamVO matchParamVO, HttpServletResponse response) {
		LOGGER.info("MatchPriceController.downloadQuotation#matchParamVO:{}", JSON.toJSONString(matchParamVO));
		MatchRuleParamVO matchRuleParamVO = matchParamVO.getMatchRuleParamVO();
		MerchantBussinessDto merchantBussinessDto = null;
		try {
			merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return ResponseVo.errResult("请登录后再试！");
			}
			String result = redisClient.get(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			if(StringUtils.isNotBlank(result) && Integer.parseInt(result) < 10000){//匹配中
				return ResponseVo.errResult("匹配中，请稍候重试！");
			}
			redisClient.setEx(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()), "0",RedisConstants.PRE_REMINDER_EXPIRE_ONLINE);
			matchRuleParamVO.setMerchantId(merchantBussinessDto.getId());

			MatchPriceResultDTO resultDTO = matchPriceService.selectPurchaseList(matchRuleParamVO);
			//过滤数据
			checkRepeatAndFilterProduct(resultDTO,matchParamVO);

			matchPriceService.downloadQuotation(resultDTO, response);
			redisClient.del(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			return ResponseVo.successResultNotData();
		} catch (Exception e) {
			if(merchantBussinessDto != null){
				redisClient.del(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			}
			LOGGER.error("MatchPriceController.downloadQuotation#报价单下载失败 matchParamVO:{}", JSON.toJSONString(matchParamVO), e);
			return ResponseVo.errResult("报价单下载失败！");
		}

	}



	/**
	 * 下载报价单V2
	 * 按需下载（页面选择的数据）
	 * @param matchSkuList
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/downloadQuotationV2")
	@ResponseBody
	public void downloadQuotationV2(@RequestBody List<MatchLineVO> matchSkuList, HttpServletResponse response) {
		MerchantBussinessDto merchantBussinessDto = null;
		try {
			merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				throw new RuntimeException("请登录后再试！");
			}
			String result = redisClient.get(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			if(StringUtils.isNotBlank(result) && Integer.parseInt(result) < 10000){//匹配中
				throw new RuntimeException("匹配中，请稍候重试！");
			}
			redisClient.setEx(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()), "0",RedisConstants.PRE_REMINDER_EXPIRE_ONLINE);
			matchPriceService.downloadQuotationV2_tmp(matchSkuList, response);
			redisClient.del(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
		} catch (Exception e) {
			if(merchantBussinessDto != null){
				redisClient.del(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			}
			throw new RuntimeException("报价单下载失败！");
		}

	}

	/**
	 * 导入计划单组装去下单参数
	 */
	@RequestMapping(value = "/importPlan", method = RequestMethod.POST)
	@ResponseBody
	public ResponseVo<ImportPlanVO> importPlan(@RequestParam("file") MultipartFile file, PlanExcelMappingVO planExcelMappingVO) {
		MerchantBussinessDto merchantBussinessDto = null;
		try {
			merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return ResponseVo.errResult("请登录后再试！");
			}
			LOGGER.info("导入计划单映射入参:{}",JSON.toJSONString(planExcelMappingVO));
			List<QuotationTemplate> quotationExportDTOS = importPlanExcel(file,planExcelMappingVO);
			return ResponseVo.successResult(matchPriceService.importPlan(quotationExportDTOS,merchantBussinessDto.getId()));

		}catch (Exception e){
			LOGGER.error("MatchPriceController.importPlan#自定义异常.导入计划单失败", e);
			return ResponseVo.errResult("自定义异常.导入计划单失败");
		}
	}

	private synchronized List<QuotationTemplate> importPlanExcel(MultipartFile file, PlanExcelMappingVO planExcelMappingVO){
		List<QuotationTemplate> quotationExportDTOS = null;
		try {
			ExcelImportUtils.excelNameChange(QuotationTemplate.class, BeanUtils.describe(planExcelMappingVO));
			quotationExportDTOS = ExcelImportUtils.importExcel(file, QuotationTemplate.class, new ImportParams(), xyyConfig.getImportMaxSize(), xyyConfig.getImportMaxRows(),false);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return quotationExportDTOS;
	}

	/**
	 * 导出失败计划单商品
	 */
	@RequestMapping(value = "/exportFailedPlan", method = RequestMethod.POST)
	@ResponseBody
	public void exportFailedPlan(@RequestBody List<ImportPlanFailVO> failVOs, HttpServletResponse response) {
		MerchantBussinessDto merchantBussinessDto = null;
		try {
			merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				throw new RuntimeException("请登录后再试！");
			}
			String result = redisClient.get(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			if(StringUtils.isNotBlank(result) && Integer.parseInt(result) < 10000){//匹配中
				throw new RuntimeException("匹配中，请稍候重试！");
			}
			redisClient.setEx(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()), "0",RedisConstants.PRE_REMINDER_EXPIRE_ONLINE);
			matchPriceService.exportFailedPlan(failVOs,merchantBussinessDto.getMerchantId(),response);
			redisClient.del(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
		} catch (Exception e) {
			if(merchantBussinessDto != null){
				redisClient.del(RedisConstants.getMatchProgressKey(merchantBussinessDto.getId()));
			}
			LOGGER.error("MatchPriceController.exportFailedPlan#下载失败计划单商品失败 failVOs:{}", JSON.toJSONString(failVOs), e);
			throw new RuntimeException("下载失败计划单商品失败！");
		}
	}

}
