package com.xyy.ec.pc.search.ecp.params;
import lombok.*;

import java.io.Serializable;

/**
 * 搜索推荐购买查询参数
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PcSearchRecPurchaseQueryParam implements Serializable {

    /**
     * 主品id
     */
    private Long mainCsuId;

    /**
     * 显示推荐购买的方式。1组合购，2加价购。仅在显示时有效。
     */
    private Integer showRecPurchaseType;

    // 埋点相关数据
    /**
     * qt埋点唯一标识
     */
    private String scmId;

}
