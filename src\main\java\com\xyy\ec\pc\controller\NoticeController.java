package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.layout.buinese.api.NoticeDetailBuineseApi;
import com.xyy.ec.layout.buinese.dto.NoticeDetailBuineseDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Title:
 * @Description:
 * @date 2018/9/4 15:58
 */
@Controller
@RequestMapping("/notice")
public class NoticeController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(NoticeController.class);


    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;


    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private NoticeDetailBuineseApi noticeDetailBuineseApi;

    /**
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @Description: 公告列表界面
     * @throws
     * <AUTHOR>
     * @date 2018/9/4 15:59
     */
    @RequestMapping(value = {"","/noticeList","/noticeList.htm"} )
    public ModelAndView findNoticeList(HttpServletRequest request) {
        try{
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            }else{
                merchantId = 0L;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);

            ModelMap modelMap = new ModelMap();
            // 分页要用，将url传到前台
            String url = this.getRequestUrl(request);

            int offset = NumberUtils.toInt(request.getParameter("offset"));
            if (offset < 1) {
                offset = 1;
            }
            /** 初始化页面数据Page */
            NoticeDetailBuineseDto noticeDetail = new NoticeDetailBuineseDto();
            noticeDetail.setBranchCode(branchCode);

            PageInfo<NoticeDetailBuineseDto> pageInfo = noticeDetailBuineseApi.getUseingToRedis(branchCode, offset, 10);
            Page<NoticeDetailBuineseDto> noticePage = new Page<>();
            noticePage.setOffset(offset);
            noticePage.setLimit(10);
            noticePage.setRows(pageInfo.getList());
            noticePage.setTotal(pageInfo.getTotal());
            modelMap.put("pager", noticePage);
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
            return new ModelAndView("/notice/noticeList.ftl",modelMap);
        } catch(Exception e){
            logger.error("公告加载异常:", e);
            return new ModelAndView("/error/500.ftl");
        }

    }

    /**
     * @param noticeDetail
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @Description: 进入到公告详情界面
     * @throws
     * <AUTHOR>
     * @date 2018/9/4 16:11
     */
    @RequestMapping("/noticeDetail")
    public ModelAndView noticeDetail(NoticeDetailBuineseDto noticeDetail,HttpServletRequest request) throws Exception {
        try{
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            }else{
                merchantId = 0L;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);

            ModelMap modelMap = new ModelMap();
            NoticeDetailBuineseDto noticeDetail1 = noticeDetailBuineseApi.selectByPrimaryKey(noticeDetail.getId());
            modelMap.put("noticeDetail", noticeDetail1);
            modelMap.put("merchantId", merchantId);
            modelMap.put("previousId", noticeDetailBuineseApi.selectPreviousId(noticeDetail.getId(), branchCode));
            modelMap.put("nextId", noticeDetailBuineseApi.selectNextId(noticeDetail.getId(), branchCode));
            modelMap.put("merchant", merchant);
            return new ModelAndView("/notice/noticeDetail.ftl",modelMap);
        } catch(Exception e){
            logger.error("公告详情加载异常:", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

}
