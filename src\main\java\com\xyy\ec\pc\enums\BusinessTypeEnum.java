package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;

public enum BusinessTypeEnum {

    SELF(1,"单体药店"),
    CHAIN_JOIN(2,"连锁加盟"),
    CHAIN_DIRECTLY(3,"连锁直营"),
    CLINIC(4,"基层医疗机构-诊所"),
    DRUG_WHOLESALE(5,"药品批发"),
    PRIVATE_HOSPITAL(6,"民营医院"),
    PUBLIC_HOSPITAL(7,"公立医院"),
    TOWNSHIP_HOSPITAL(8,"基层医疗机构-卫生院"),
    COMMUNITY_HEALTH_STATION(9,"基层医疗机构-社区卫生服务站"),
    RURAL_HEALTH_ROOM(10,"基层医疗机构-卫生室"),
    MEDICAL_INSTITUTION(11,"医疗机构"),
    TERMINAL(12,"终端"),
    BASIC_MEDICAL_INSTITUTION(13,"基层医疗机构"),
    OUTPATIENT_DEPARTMENT(14,"基层医疗机构-门诊部"),
    OTHER_MEDICAL_INSTITUTION(15,"基层医疗机构-其他"),
    PHARMACEUTICAL_MANUFACTURING(16,"药品生产"),
    NON_PHARMACEUTICAL_MANUFACTURING(17,"非药类生产"),
    OVERSEAS_ENTERPRISE(18,"境外企业"),
    NON_PHARMACEUTICAL_MANAGE(19,"非药类经营"),
    OTHER_ENTERPRISES(20,"其他企业"),
    CHAIN_HEADQUARTERS(21,"连锁总部");

    private int id;
    private  String value;

    BusinessTypeEnum(int id,String value){
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, BusinessTypeEnum> enumMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(BusinessTypeEnum e : BusinessTypeEnum.values()) {
            enumMaps.put(e.getId(), e);
            maps.put(e.getId(),e.getValue());
        }
    }

    public static String get(int id) {
        return enumMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }

}
