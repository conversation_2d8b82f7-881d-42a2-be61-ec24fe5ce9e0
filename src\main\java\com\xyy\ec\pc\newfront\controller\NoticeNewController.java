package com.xyy.ec.pc.newfront.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.dto.NoticeDetailRespVO;
import com.xyy.ec.pc.newfront.dto.NoticeRowRespVO;
import com.xyy.ec.pc.newfront.service.NoticeNewService;
import com.xyy.ec.pc.newfront.vo.NoticeDetailParamVO;
import com.xyy.ec.pc.newfront.vo.NoticeParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@CustomizeCmsResponse
@RestController
@RequestMapping("/new-front/notice")
public class NoticeNewController {

    @Resource
    private NoticeNewService noticeNewService;

    /**
     * 分页获取公告列表界面
     */
    @PostMapping("/notice-list")
    public AjaxResult<PageInfo<NoticeRowRespVO>> findNoticeList(@RequestBody NoticeParamVO paramVO, HttpServletRequest request) {
        try {
            paramVO.setPageNum(paramVO.getPageNum() == null ? 1 : paramVO.getPageNum());
            paramVO.setPageSize(paramVO.getPageSize() == null ? 10 : paramVO.getPageSize());
            return AjaxResult.successResult(noticeNewService.getNoticeList(paramVO, request));
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }

    }

    /**
     * 获取公告详情
     */
    @PostMapping("/notice-detail")
    public AjaxResult<NoticeDetailRespVO> noticeDetail(@RequestBody NoticeDetailParamVO paramVO, HttpServletRequest request) throws Exception {
        try {
            NoticeDetailRespVO noticeDetail = noticeNewService.getNoticeDetail(paramVO, request);
            return AjaxResult.successResult(noticeDetail);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }


}
