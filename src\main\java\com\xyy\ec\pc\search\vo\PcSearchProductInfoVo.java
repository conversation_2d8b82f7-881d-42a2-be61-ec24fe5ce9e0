package com.xyy.ec.pc.search.vo;


import com.xyy.ec.pc.service.marketing.dto.MarketingWholesaleActivityInfoDTO;
import com.xyy.ec.product.business.ecp.csufillattr.dto.LevelPriceDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * <AUTHOR> cao
 * @version V1.0
 * @Descriotion:
 * @create 2020/11/30 11:00
 */
@Data
public class PcSearchProductInfoVo implements Serializable {
    private static final long serialVersionUID = 4045756533652171391L;
    /**
     * 商品id
     */
    private Long id;

    /**
     * 已有XXX人 下单
     */
    private String promoTag;

    /**
     * 原商品ID
     */
    private Long pid;
    /**
     * 商品条码
     */
    private String code;

    /**
     * 商品编码
     */
    private String barcode;
    /**
     * 域编码
     */
    private String branchCode;
    /**
     * 机构id
     */
    private String orgId;

    /**
     * 商家名称
     */
    private String companyName;

    /**
     * 是否易碎品（0：否；1：是）
     */
    private Integer isFragileGoods;

    /**
     * 商品推荐分类
     */
    private Long categoryId;

    /** 商品类型：1.普通土商品；2.秒杀商品 */
    private Integer productType;

    /**
     * 一级分类id
     */
    private Long categoryFirstId;

    /**
     * 一级分类名称
     */
    private String categoryFirstName;

    /**
     * 展示名称
     * 根据产品需求拼接的商品名称
     */
    private String showName;

    /**
     * 商品原来的展示名称
     */
    private String originalShowName;
    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 规格
     */
    private String spec;
    /**
     * 价格前缀
     */
    private String pricePrefix;

    /**
     * 状态:1-销售中，2-已售罄，4-下架，6-待上架
     */
    private Integer status;

    /**
     * 是否控销商品
     */
    private Integer isControl;

    /**
     * 1-统一价格，2-价格区间
     */
    private Integer priceType;

    /**
     * 对比价格
     */
    private BigDecimal retailPrice;

    /**
     * 药帮忙价
     */
    private BigDecimal fob;

    /**
     * 商品主图
     */
    private String imageUrl;

    /**
     * 商品标签URL /精选或必买
     */
    private String markerUrl;

    /**
     * 建议零售价
     */
    private BigDecimal suggestPrice;

    /**
     * 毛利率
     */
    private String grossMargin;

    /**
     * 中包装
     **/
    private String mediumPackage;

    /**
     * 控销零售价
     */
    private BigDecimal uniformPrice;

    /**
     * 中包装数量（默认为1）
     */
    private Integer mediumPackageNum;

    /**
     * 中包装文案
     */
    private String mediumPackageTitle;

    /**
     * 代理（0：非独家，1：独家）
     */
    private Integer agent;


    /**
     * 是否可拆零（0:不可拆零；1:可拆零）
     */
    private Integer isSplit;


    /**
     * 库存
     */
    private Integer availableQty;

    /**
     * 收藏标记(1:收藏；2:取消收藏)
     */
    private Integer favoriteStatus;

    /**
     * 价格区间
     */
    private List<PcSearchProductPriceRangeVo> skuPriceRangeList;


    /**
     * 是否可购买
     */
    private Boolean isPurchase;

    /**
     * 享礼标记(商品名称前的标记)
     */
    private Boolean gift;

    /**
     * 协议签署状态(0-未签署,1-已签署)
     */
    private Integer signStatus;

    /**
     * 是否OEM协议(此处设置为String类型是因为web页面boolean类型不支持判断是否存在,而为了兼容之前没有OEM需求的情况又需要判断)
     */
    private String isOEM;

    /**
     * 是否可用医保：0-否，1-是
     */
    private Integer isUsableMedicalStr;

    /**
     * 是否可用医保
     */
    private Boolean isUsableMedical;

    /**
     * 是否可拆零文案
     */
    private String isSplitTitle;

    /**
     * 近效期
     */
    private String nearEffect;

    /**
     * 近效期标识(1:临期，2:近效)
     */
    private Integer nearEffectiveFlag;

    /**
     * 是否显示806标签
     */
    private Boolean isShow806;

    /**
     * 购物车数量
     */
    private Integer cartProductNum;


    /**
     * 是否符合协议标准展示价格,1:符合0:不符合
     */
    private Integer showAgree;

    /**
     * 促销开始时间
     */
    private Date promotionStartTime;

    /**
     * 促销结束时间
     */
    private Date promotionEndTime;

    /**
     * 是否显示
     */
    private Integer promotionCountDown;

    /**
     * 限购商品总数量
     */
    private Integer promotionTotalQty;

    /**
     * 保质期（如 12个月）
     */
    private String shelfLife;
    /**
     * 购买次数 为null 或者 "" 不显示
     */
    private String buyedCountStr;


    /**
     * 是否第三方厂家（0：否；1：是）
     */
    private Integer isThirdCompany;

    /**
     * 远效期
     */
    private String farEffect;

    /**
     * 30天销量
     */
    private Long thirtyDaysAmount;


    /**
     * 是否有货提醒
     */
    private Boolean isArrivalReminder;


    /**
     * 1:不是髙毛，2:是髙毛
     */
    private Integer highGross;

    /**
     * 协议状态(0-未生效,1-生效中,2-已失效)
     */
    private Integer agreementEffective;
    /**
     * 标签列表
     */
    private Map<String, Object> tags;
    /**
     * 列表页商品追踪唯一标识id
     */
    private String nsid;
    /**
     * 商品扩展字段，可扩容
     */
    private String sdata;

    /**
     * 店铺URL
     */
    private String shopUrl;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 商品单位
     */
    private String productUnit;

    /**
     * 拼团信息
     */
    private PinTuanActInfoVo actPt;

    /**
     * 近远效期
     * 取值定义: 近远期不一致返回近效期/远效期时间，近远期一致返回一个效期时间，如果近效期|远效期只有一个则不返回值
     */
    private String effectStr;

    /**
     * 是否控销协议商品（0不是控销协议商品，1是控销协议商品）
     */
    private Integer isControlAgreement;

    /**
     * 秒杀活动信息
     */
    private SeckillActInfoVo actSk;

    /**
     * 0:不是髙毛拼团，1:是髙毛拼团
     */
    private Integer highGrossGroupBuying;
    /**
     * 控销价格展示文案
     */
    private String controlTitle;
    /**
     * 控销注释
     */
    private String controlNotes;
    /**
     * 控销购买按钮
     */
    private String controlPurchaseButton;
    /**
     * 资质类型为-1
     */
    private Integer controlType;

    /**
     * 商品路由地址
     */
    private String jumpUrl;

    /**
     * 店铺更多同款商品: 0-否，1-是
     */
    private Integer hasSimilarGoods;

    /**
     * 主标准库ID
     */
    private String masterStandardProductId;

    /**
     * 返回数据类型
     *
     * @see {com.xyy.ec.recommend.enums.AlsoLikeDsType}
     */
    private Integer sourceType;

    /**
     * 随心拼活动信息
     */
    private SuiXinPinActInfoVo actSuiXinPin;

    /**
     * 批购包邮活动信息
     */
    private MarketingWholesaleActivityInfoDTO actPgby;

    /**
     * 阶梯价信息  没有阶梯价时为null
     */
    private LevelPriceDTO levelPriceDTO;

    /**
     * 上市许可持有人
     */
    private String marketAuthor;

    /**
     * 委托生产厂家
     */
    private String entrustedManufacturer;

    /**
     * 委托生产厂家地址
     */
    private String entrustedManufacturerAddress;

    /**
     *  随心拼埋点数据
     */
    private SxpQtDataVo qtData;
}
