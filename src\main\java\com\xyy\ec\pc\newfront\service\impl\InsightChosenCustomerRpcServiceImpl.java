package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.insight.api.InsightChosenCustomerApi;
import com.xyy.ec.pc.newfront.service.InsightChosenCustomerRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class InsightChosenCustomerRpcServiceImpl implements InsightChosenCustomerRpcService {
    @Reference(version = "1.0.0")
    private InsightChosenCustomerApi insightChosenCustomerApi;

    @Override
    public List<Long> getChoseGroupsByMerchant(Long merchantId, List<Long> groupList) {
        if (null == merchantId || merchantId <= 0 || CollectionUtils.isEmpty(groupList)) {
            return Lists.newArrayList();
        }
        ApiRPCResult<List<Long>> result = insightChosenCustomerApi.getChoosedGroupsByMerchantSmallScaleUseCache(merchantId, groupList);
        if (null == result) {
            return Lists.newArrayList();
        }
        if (!result.isSuccess()) {
            throw new RuntimeException("查询人群服务异常");
        }
        return result.getData();
    }
}
