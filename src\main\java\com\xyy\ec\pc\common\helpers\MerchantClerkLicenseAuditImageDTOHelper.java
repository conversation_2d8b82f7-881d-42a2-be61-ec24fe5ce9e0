package com.xyy.ec.pc.common.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.merchant.bussiness.result.MerchantClerkLicenseAuditImageDTO;
import com.xyy.ec.pc.controller.vo.merchant.MerchantClerkLicenseAuditImageVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class MerchantClerkLicenseAuditImageDTOHelper {

    public static List<MerchantClerkLicenseAuditImageVO> creates(List<MerchantClerkLicenseAuditImageDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Lists.newArrayList();
        }
        return dtos.stream().map(MerchantClerkLicenseAuditImageDTOHelper::create)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static MerchantClerkLicenseAuditImageVO create(MerchantClerkLicenseAuditImageDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        return MerchantClerkLicenseAuditImageVO.builder()
                .name(dto.getName()).licenseCode(dto.getLicenseCode())
                .listImgUrls(dto.getLicenseImageUrls()).build();
    }

}
