package com.xyy.ec.pc.controller.vo.platformin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xyy.ec.cs.api.dto.PlatfromInNodeDTO;
import com.xyy.ec.cs.api.dto.QualificationInvoiceRefoundDTO;
import com.xyy.ec.cs.api.dto.RefundInfoDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PlatformInWorkOrderVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id; // 工单ID
    private String workOrderNo; // 工单编号
    private String workOrderNoDesc; // 工单编号
    private String merchantName; // 客户名称
    private Long merchantId; // 客户ID
    private Integer type; // 介入类型
    private Integer interventionAppeal; // 客户诉求
    private String typeDesc; // 介入类型
    private String interventionAppealDesc; // 客户诉求
    private Integer interventionState;
    private String interventionStateDesc;
    private String mobile; // 手机号
    private String email; // 邮箱
    private String orderNo; // 订单编号
    private Long orderId; // 订单编号
    private String refundNo; // 退款单号
    private String afsNo; // 售后单号
    private String orgId; // 商家id
    private String shopName; // 商家名称
    private Integer state; // 工单状态
    private String stateDesc; // 工单状态
    private String currentProcessingPersonTypeDesc; // 当前处理人类型节点
    private Date createTime; // 工单创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime; // 工单创建时间

    private String imUrl;
    private String popImUrl;


    private RefundInfoDTO refundInfo;

    private QualificationInvoiceRefoundDTO afsInfo;

    private PlatfromInNodeDTO nodeInfo;
}
