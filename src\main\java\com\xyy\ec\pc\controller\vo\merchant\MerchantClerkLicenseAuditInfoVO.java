package com.xyy.ec.pc.controller.vo.merchant;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantClerkLicenseAuditInfoVO implements Serializable {

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 药店编号
     */
    private Long merchantId;

    /**
     * 图片参数列表
     */
    private List<MerchantClerkLicenseAuditImageVO> images;

}
