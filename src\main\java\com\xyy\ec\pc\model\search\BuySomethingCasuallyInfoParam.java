package com.xyy.ec.pc.model.search;


import com.xyy.ec.order.dto.cart.ShopInfoSxp;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 搜索查询参数
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class BuySomethingCasuallyInfoParam implements Serializable {

    /**
     * 随心拼中的顺手买  查询入参，主要包含店铺信息
     */
    private List<ShopInfoSxp> shopInfoSxpList ;
    /**
     *   已选的顺手买的sku
     */

    private List<Long> buySomethingCasuallySkus;
    /**
     *   是否更多
     */
    private Boolean isMore;

}
