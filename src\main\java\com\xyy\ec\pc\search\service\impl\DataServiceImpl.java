package com.xyy.ec.pc.search.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.data.api.DataTagsApi;
import com.xyy.ec.data.api.EcDataApi;
import com.xyy.ec.data.dto.SkuDTO;
import com.xyy.ec.data.enums.DataTagEnum;
import com.xyy.ec.data.enums.ProductDataTagEnum;
import com.xyy.ec.data.vo.DataTagVO;
import com.xyy.ec.data.vo.ProductTagVo;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.TagTypeEnum;
import com.xyy.ec.pc.recommend.dto.RecommendCsuDataTagCsuDTO;
import com.xyy.ec.pc.recommend.params.RecommendCsuDataTagQueryParam;
import com.xyy.ec.pc.remote.ProductAlsoLikeRpcRemote;
import com.xyy.ec.pc.search.dto.SearchCsuDataTagCsuDTO;
import com.xyy.ec.pc.search.params.SearchCsuDataTagQueryParam;
import com.xyy.ec.pc.search.service.DataService;
import com.xyy.ec.pc.search.vo.PcSearchProductInfoVo;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.recommend.dto.OftenPurchaseOrSearchQueryParam;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSpuRecallStrategyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.xyy.ec.data.enums.DataTagEnum.PURCHASE_CUT_PRICE;

/**
 * 描述:
 *
 * <AUTHOR> cao
 * @version V1.0
 * @Descriotion: TODO
 * @create 2020/11/30 20:14
 */
@Service
@Slf4j
public class DataServiceImpl implements DataService {

    @Reference(version = "1.0.0")
    private EcDataApi ecDataApi;

    @Reference(version = "1.0.0")
    private DataTagsApi dataTagsApi;

    @Autowired
    private ProductAlsoLikeRpcRemote productAlsoLikeRpcRemote;
    @Override
    public Map<Long, Map<String, Object>> getDataTagList(Long merchantId, String branchCode, List<PcSearchProductInfoVo> productInfoVoList, Boolean isNeedPurchaseCutPriceTag) {
        Map<Long, Map<String, Object>> result = new HashMap<>();
        if (CollectionUtil.isEmpty(productInfoVoList)) {
            return result;
        }
        Boolean isSaasMerchant = false;
        try {
            ApiRPCResult<Boolean> apiRPCResult = ecDataApi.queryIsSaasMerchant(merchantId);
            if (apiRPCResult.isSuccess()) {
                isSaasMerchant = apiRPCResult.getData();
            }
        } catch (Exception e) {
            log.error("ecDataApi.queryIsSaasMerchant error", e);
        }

        try {
            List<SkuDTO> skuDTOList = productInfoVoList.stream().map(vo -> {
                return SkuDTO.builder().skuId(vo.getId()).categoryId(vo.getCategoryId()).price(vo.getFob().doubleValue()).build();
            }).collect(Collectors.toList());
            ApiRPCResult<List<ProductTagVo>> apiRPCResult = ecDataApi.queryProductTagList(branchCode, merchantId, skuDTOList, isSaasMerchant);
            if (apiRPCResult.isSuccess()) {
                if (CollectionUtils.isNotEmpty(apiRPCResult.getData())) {
                    List<ProductTagVo> dataTagList = apiRPCResult.getData();
                    dataTagList.forEach(dataTag -> {
                        List<ProductTagVo> productTagVos = new ArrayList<>();
                        Long key = dataTag.getSkuId();
                        Map<String, Object> value = result.get(key);
                        if (CollectionUtil.isEmpty(value)) {
                            value = new HashMap<>();
                        }
                        DataTagEnum dataTagEnum = DataTagEnum.getByType(dataTag.getType());
                        switch (dataTagEnum) {
                            case MUST_BUY:
                                value.put("markerTag", dataTag.getText());
                                break;
                            case PURCHASE_COUNT:
                                productTagVos.add(dataTag);
                                value.put("purchaseTags", productTagVos);
                                break;
                            case CAT_CTR_RANKING:
                            case SIXTY_DAYS_MIN_PRICE:
                            case AREA_GROSS_PROFIT_RANKING:
                            case PURCHASE_CUT_PRICE:
                                if (dataTagEnum.getType().equals(PURCHASE_CUT_PRICE.getType()) && !isNeedPurchaseCutPriceTag) {
                                    break;
                                }
                                if (value.containsKey("dataTags")) {
                                    productTagVos = (List<ProductTagVo>) value.get("dataTags");
                                }
                                productTagVos.add(dataTag);
                                value.put("dataTags", productTagVos);
                                break;

                            default:
                                break;
                        }
                        result.put(key, value);
                    });
                }
            }
        } catch (Exception e) {
            log.error("DataService getDataTagList error", e);
        }
        return result;
    }

    @Override
    public Map<Long, Map<String, Object>> getSkuDataTagList(Long merchantId, List<PcSearchProductInfoVo> productInfoVoList, Boolean hasShopDataTags) {
        if (Objects.isNull(merchantId) || CollectionUtil.isEmpty(productInfoVoList)) {
            log.error("params is invalid, merchantId : {}, productInfoVoList : {}",merchantId, CollectionUtil.isEmpty(productInfoVoList) ? "isEmpty" : productInfoVoList.size());
            return Maps.newHashMap();
        }
        Map<Long, Map<String, Object>> resultMap = new HashMap<>();
        //获取原商品ID，原商品ID不存在则取商品ID,并去重
        List<Long> skuIdList = productInfoVoList.stream().map(productInfoVo -> Objects.isNull(productInfoVo.getPid()) ? productInfoVo.getId() : productInfoVo.getPid()).distinct().collect(Collectors.toList());
        //获取店铺编码并去重
        List<String> shopCodeList = productInfoVoList.stream().map(appSearchProductInfoVo -> appSearchProductInfoVo.getShopCode()).distinct().collect(Collectors.toList());
        //查询商品数据标签
        Map<Long, DataTagVO> dataTagVOMap = querySkuDataTags(merchantId, skuIdList);
        //查询店铺数据标签
        Map<String, DataTagVO> shopDataTagVOMap = new HashMap<>();
        if (hasShopDataTags) {
            shopDataTagVOMap = queryShopDataTags(merchantId, shopCodeList);
        }
        for (PcSearchProductInfoVo searchProductInfoVo : productInfoVoList) {
            //虚拟店铺不展示购买次数&买过的店铺
            if (Constants.VIR_SHOP_CODE.equalsIgnoreCase(searchProductInfoVo.getShopCode())) {
                continue;
            }
            //如果该商品没有购买次数，则显示该用户是否购买过店铺标签
            Long skuId = Objects.nonNull(searchProductInfoVo.getPid()) ? searchProductInfoVo.getPid() : searchProductInfoVo.getId();
            DataTagVO skuDataTagVo = dataTagVOMap.get(skuId);
            //需要店铺数据标签 且 商品数据标签为空
            if (hasShopDataTags && Objects.isNull(skuDataTagVo)) {
                //店铺数据标签
                Map<String, Object> shopDataTagsMap = handleDataTags(shopDataTagVOMap.get(searchProductInfoVo.getShopCode()));
                if (MapUtils.isNotEmpty(shopDataTagsMap)) {
                    resultMap.put(searchProductInfoVo.getId(), shopDataTagsMap);
                }
            } else if (Objects.nonNull(skuDataTagVo)) {
                //商品数据标签
                Map<String, Object> dataTagsMap = handleDataTags(skuDataTagVo);
                if (MapUtils.isNotEmpty(dataTagsMap)) {
                    resultMap.put(searchProductInfoVo.getId(), dataTagsMap);
                }
            }
        }
        return resultMap;
    }

    @Override
    public Map<Long, Map<String, Object>> batchGetCsuDataTags(SearchCsuDataTagQueryParam queryParam) {
        if (Objects.isNull(queryParam) || Objects.isNull(queryParam.getMerchantId()) || queryParam.getMerchantId() <= 0L
                || CollectionUtils.isEmpty(queryParam.getCsuList())) {
            return Maps.newHashMap();
        }
        Long merchantId = queryParam.getMerchantId();
        List<SearchCsuDataTagCsuDTO> csuList = queryParam.getCsuList();
        Boolean isQueryShopDataTags = queryParam.getIsQueryShopDataTags();

        Map<Long, Map<String, Object>> resultMap = Maps.newHashMapWithExpectedSize(16);
        //获取原商品ID，原商品ID不存在则取商品ID,并去重
        List<Long> logicIds = csuList.stream().map(productInfoVo -> Objects.isNull(productInfoVo.getPid()) ? productInfoVo.getId() : productInfoVo.getPid()).distinct()
                .filter(Objects::nonNull).collect(Collectors.toList());
        //获取店铺编码并去重
        List<String> shopCodes = csuList.stream().map(appSearchProductInfoVo -> appSearchProductInfoVo.getShopCode()).distinct()
                .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        //查询商品数据标签
        Map<Long, DataTagVO> dataTagVOMap = querySkuDataTags(merchantId, logicIds);
        if (log.isDebugEnabled()) {
            log.debug("batchGetCsuDataTags，querySkuDataTags，merchantId：{}，logicIds：{}，dataTagVOMap：{}",
                    merchantId, JSONArray.toJSONString(logicIds), JSONObject.toJSONString(dataTagVOMap));
        }
        //查询店铺数据标签
        Map<String, DataTagVO> shopDataTagVOMap = Maps.newHashMapWithExpectedSize(16);
        if (BooleanUtils.isTrue(isQueryShopDataTags)) {
            shopDataTagVOMap = queryShopDataTags(merchantId, shopCodes);
        }
        if (log.isDebugEnabled()) {
            log.debug("batchGetCsuDataTags，queryShopDataTags，merchantId：{}，shopCodes：{}，shopDataTagVOMap：{}",
                    merchantId, JSONArray.toJSONString(shopCodes), JSONObject.toJSONString(shopDataTagVOMap));
        }
        for (SearchCsuDataTagCsuDTO searchCsuDataTagCsuDTO : csuList) {
            //虚拟店铺不展示购买次数&买过的店铺
            if (Constants.VIR_SHOP_CODE.equalsIgnoreCase(searchCsuDataTagCsuDTO.getShopCode())) {
                continue;
            }
            //如果该商品没有购买次数，则显示该用户是否购买过店铺标签
            Long logicId = Objects.nonNull(searchCsuDataTagCsuDTO.getPid()) ? searchCsuDataTagCsuDTO.getPid() : searchCsuDataTagCsuDTO.getId();
            DataTagVO skuDataTagVo = dataTagVOMap.get(logicId);
            if (log.isDebugEnabled()) {
                log.debug("batchGetCsuDataTags，queryShopDataTags，merchantId：{}，logicId：{}，skuDataTagVo：{}",
                        merchantId, logicId, JSONObject.toJSONString(skuDataTagVo));
            }
            //需要店铺数据标签 且 商品数据标签为空
            if (BooleanUtils.isTrue(isQueryShopDataTags) && Objects.isNull(skuDataTagVo)) {
                //店铺数据标签
                Map<String, Object> shopDataTagsMap = handleDataTags(shopDataTagVOMap.get(searchCsuDataTagCsuDTO.getShopCode()));
                if (MapUtils.isNotEmpty(shopDataTagsMap)) {
                    resultMap.put(searchCsuDataTagCsuDTO.getId(), shopDataTagsMap);
                }
            } else if (Objects.nonNull(skuDataTagVo)) {
                //商品数据标签
                Map<String, Object> dataTagsMap = handleDataTags(skuDataTagVo);
                if (MapUtils.isNotEmpty(dataTagsMap)) {
                    resultMap.put(searchCsuDataTagCsuDTO.getId(), dataTagsMap);
                }
            }
        }
        if (log.isDebugEnabled()) {
            log.debug("batchGetCsuDataTags，queryParam：{}，resultMap：{}", JSONObject.toJSONString(queryParam), JSONObject.toJSONString(resultMap));
        }
        return resultMap;
    }

    private Map<Long, DataTagVO> querySkuDataTags(Long merchantId, List<Long> skuIdList) {
        try {
            ApiRPCResult<List<DataTagVO>> apiRPCResult = dataTagsApi.querySkuDataTags(merchantId, skuIdList);
            if (apiRPCResult.isSuccess() && CollectionUtil.isNotEmpty(apiRPCResult.getData())) {
                //转换成map
                Map<Long,DataTagVO> dataTagVOMap = apiRPCResult.getData().stream().collect(Collectors.toMap(DataTagVO::getSkuId,dataTagVO -> dataTagVO, (k1,k2)->k2));
                if (log.isDebugEnabled()) {
                    log.debug("querySkuDataTags merchantId : {}, skuIdList : {}", merchantId, skuIdList, JSONObject.toJSONString(dataTagVOMap));
                }
                return dataTagVOMap;
            }
        } catch (Exception ex) {
            log.error("querySkuDataTags merchantId : {}, skuIdList : {}, error : ", merchantId, skuIdList, ex);
        }
        return Maps.newHashMap();
    }

    /**
     * 查询店铺数据标签
     * @param merchantId
     * @param shopCodes
     * @return
     */
    public Map<String, DataTagVO> queryShopDataTags(Long merchantId, List<String> shopCodes) {
        if (Objects.isNull(merchantId) || CollectionUtil.isEmpty(shopCodes)) {
            log.error("params is invalid, merchantId : {}, shopCodes : {}", merchantId, shopCodes);
            return Maps.newHashMap();
        }

        try {
            ApiRPCResult<List<DataTagVO>> apiRPCResult = dataTagsApi.queryShopDataTags(merchantId, shopCodes);
            if(apiRPCResult.isSuccess() && CollectionUtil.isNotEmpty(apiRPCResult.getData())) {
                Map<String, DataTagVO> dataTagVOMap = apiRPCResult.getData().stream().collect(Collectors.toMap(DataTagVO::getShopCode, dataTagVO -> dataTagVO, (k1,k2)-> k2));
                log.info("queryShopDataTags merchantId : {}, shopCodes : {}, dataTagVOMap : {}", merchantId, shopCodes, JSONObject.toJSONString(dataTagVOMap));
                return dataTagVOMap;
            }
        } catch (Exception ex) {
            log.error("queryShopDataTags merchantId : {}, shopCodes : {}, error : ", merchantId, shopCodes, ex);
        }
        return Maps.newHashMap();
    }

    @Override
    public Map<Long, Map<String, Object>> batchGetCsuDataTags(RecommendCsuDataTagQueryParam queryParam) {
        if (Objects.isNull(queryParam) || Objects.isNull(queryParam.getMerchantId()) || queryParam.getMerchantId() <= 0L
                || CollectionUtils.isEmpty(queryParam.getCsuList())) {
            return Maps.newHashMap();
        }
        Long merchantId = queryParam.getMerchantId();
        List<RecommendCsuDataTagCsuDTO> csuList = queryParam.getCsuList();
        Boolean isQueryShopDataTags = queryParam.getIsQueryShopDataTags();

        Map<Long, Map<String, Object>> resultMap = Maps.newHashMapWithExpectedSize(16);
        //获取原商品ID，原商品ID不存在则取商品ID,并去重
        List<Long> logicIds = csuList.stream().map(productInfoVo -> Objects.isNull(productInfoVo.getPid()) ? productInfoVo.getId() : productInfoVo.getPid()).distinct()
                .filter(Objects::nonNull).collect(Collectors.toList());
        //获取店铺编码并去重
        List<String> shopCodes = csuList.stream().map(appSearchProductInfoVo -> appSearchProductInfoVo.getShopCode()).distinct()
                .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        //查询商品数据标签
        Map<Long, DataTagVO> dataTagVOMap = querySkuDataTags(merchantId, logicIds);
        if (log.isDebugEnabled()) {
            log.debug("batchGetCsuDataTags，querySkuDataTags，merchantId：{}，logicIds：{}，dataTagVOMap：{}",
                    merchantId, JSONArray.toJSONString(logicIds), JSONObject.toJSONString(dataTagVOMap));
        }
        //查询店铺数据标签
        Map<String, DataTagVO> shopDataTagVOMap = Maps.newHashMapWithExpectedSize(16);
        if (BooleanUtils.isTrue(isQueryShopDataTags)) {
            shopDataTagVOMap = queryShopDataTags(merchantId, shopCodes);
        }
        if (log.isDebugEnabled()) {
            log.debug("batchGetCsuDataTags，queryShopDataTags，merchantId：{}，shopCodes：{}，shopDataTagVOMap：{}",
                    merchantId, JSONArray.toJSONString(shopCodes), JSONObject.toJSONString(shopDataTagVOMap));
        }
        Map<Long,DataTagVO> spuTagMap = querySpuRecTag(queryParam);

        for (RecommendCsuDataTagCsuDTO recommendCsuDataTagCsuDTO : csuList) {
            //如果该商品没有购买次数，则显示该用户是否购买过店铺标签
            Long logicId = Objects.nonNull(recommendCsuDataTagCsuDTO.getPid()) ? recommendCsuDataTagCsuDTO.getPid() : recommendCsuDataTagCsuDTO.getId();
            DataTagVO skuDataTagVo = dataTagVOMap.get(logicId);
            if (log.isDebugEnabled()) {
                log.debug("batchGetCsuDataTags，queryShopDataTags，merchantId：{}，logicId：{}，skuDataTagVo：{}",
                        merchantId, logicId, JSONObject.toJSONString(skuDataTagVo));
            }
            //需要店铺数据标签 且 商品数据标签为空
            if (BooleanUtils.isTrue(isQueryShopDataTags) && Objects.isNull(skuDataTagVo)) {
                //店铺数据标签
                Map<String, Object> shopDataTagsMap = handleDataTags(shopDataTagVOMap.get(recommendCsuDataTagCsuDTO.getShopCode()));
                handlePurchaseOrSearchTag(shopDataTagsMap,spuTagMap.get(recommendCsuDataTagCsuDTO.getSpuId()));
                if (MapUtils.isNotEmpty(shopDataTagsMap)) {
                    resultMap.put(recommendCsuDataTagCsuDTO.getId(), shopDataTagsMap);
                }
            } else if (Objects.nonNull(skuDataTagVo)) {
                //商品数据标签
                Map<String, Object> dataTagsMap = handleDataTags(skuDataTagVo);
                handlePurchaseOrSearchTag(dataTagsMap,spuTagMap.get(recommendCsuDataTagCsuDTO.getSpuId()));
                if (MapUtils.isNotEmpty(dataTagsMap)) {
                    resultMap.put(recommendCsuDataTagCsuDTO.getId(), dataTagsMap);
                }
            }

        }
        if (log.isDebugEnabled()) {
            log.debug("batchGetCsuDataTags，queryParam：{}，resultMap：{}", JSONObject.toJSONString(queryParam), JSONObject.toJSONString(resultMap));
        }
        return resultMap;
    }



    private Map<Long, DataTagVO> querySpuRecTag(RecommendCsuDataTagQueryParam queryParam) {
        List<RecommendCsuDataTagCsuDTO> csuList = queryParam.getCsuList();
        if (CollectionUtils.isEmpty(csuList)) {
            return Maps.newHashMap();
        }
        OftenPurchaseOrSearchQueryParam oftenPurchaseOrSearchQueryParam = OftenPurchaseOrSearchQueryParam.builder()
                .merchantId(queryParam.getMerchantId())
                .build();
        List<OftenPurchaseOrSearchQueryParam.ProductInfo> oftenPurchaseList = Lists.newArrayList();
        List<OftenPurchaseOrSearchQueryParam.ProductInfo> oftenSearchList = Lists.newArrayList();
        List<RecommendCsuDataTagCsuDTO> paramCsuList = queryParam.getCsuList();
        for (RecommendCsuDataTagCsuDTO item : paramCsuList) {
            String spuRecallStrategyName = item.getSpuRecallStrategyName();
            if (StringUtils.isBlank(spuRecallStrategyName)){
                continue;
            }
            EcpRecommendSpuRecallStrategyEnum recallStrategyEnum = EcpRecommendSpuRecallStrategyEnum.valueOf(spuRecallStrategyName);
            if (EcpRecommendSpuRecallStrategyEnum.USER_FREQUENTLY_PURCHASED.getType().equals(recallStrategyEnum.getType())){
                oftenPurchaseList.add(OftenPurchaseOrSearchQueryParam.ProductInfo.builder()
                        .skuId(item.getId())
                        .spuId(item.getSpuId()).build());
                continue;
            }
            if (EcpRecommendSpuRecallStrategyEnum.USER_FREQUENTLY_SEARCHED.getType().equals(recallStrategyEnum.getType())){
                oftenSearchList.add(OftenPurchaseOrSearchQueryParam.ProductInfo.builder()
                        .skuId(item.getId())
                        .spuId(item.getSpuId()).build());
                continue;
            }

        }
        oftenPurchaseOrSearchQueryParam.setOftenPurchaseSkuInfos(oftenPurchaseList);
        oftenPurchaseOrSearchQueryParam.setOftenSearchSkuInfos(oftenSearchList);
        Map<Long, String> longStringMap = productAlsoLikeRpcRemote.queryRecommendDescList(oftenPurchaseOrSearchQueryParam);
        if  (MapUtils.isEmpty(longStringMap)) {
            return Maps.newHashMap();
        }
        Map<Long, DataTagVO> resultMap = Maps.newHashMapWithExpectedSize(longStringMap.size());
        for (Map.Entry<Long, String> entry : longStringMap.entrySet()) {
            DataTagVO dataTagVO = buildPurchseDataTagVO(entry.getValue());
            dataTagVO.setSkuId(entry.getKey());
            resultMap.put(entry.getKey(), dataTagVO);
        }
        return resultMap;
    }

    private DataTagVO buildPurchseDataTagVO(String text){
        DataTagVO dataTagVO = DataTagVO.builder()
                .uiType(DataTagEnum.PURCHASE_COUNT.getUiType())
                .text(text)
                .build();
        ProductDataTagEnum productDataTagEnum = ProductDataTagEnum.UI_STYLE_THREE;
        dataTagVO.setUiStyle(productDataTagEnum.getUiStyle());
        dataTagVO.setBgColor(productDataTagEnum.getBgColor());
        dataTagVO.setBorderColor(productDataTagEnum.getBorderColor());
        dataTagVO.setTextColor(productDataTagEnum.getTextColor());
        dataTagVO.setText(text);
        return dataTagVO;
    }

    private void handlePurchaseOrSearchTag(Map<String, Object> dataTagsMap, DataTagVO dataTagVO) {
        if (dataTagVO == null){
            return;
        }
        if (dataTagsMap == null){
            dataTagsMap = Maps.newHashMap();
        }
        dataTagsMap.put(TagTypeEnum.PURCHASE_SEARCH_TAG.getName(), dataTagVO);
    }


    /**
     * 处理商品数据标签
     * @param skuDataTagVo
     * @return
     */
    private Map<String, Object> handleDataTags(DataTagVO skuDataTagVo) {
        if (Objects.isNull(skuDataTagVo)) {
            return Maps.newHashMap();
        }
        Map<String, Object> dataTagMap = new HashMap<>();
        DataTagEnum dataTagEnum = DataTagEnum.getByType(skuDataTagVo.getType());
        Optional.ofNullable(dataTagEnum).ifPresent(dataTagEnum1 -> {
            DataTagVO newDataTagVo = getNewDataTagVO(skuDataTagVo);
            switch (dataTagEnum) {
                case PURCHASE_SHOP:
                case PURCHASE_COUNT:
                    dataTagMap.put(TagTypeEnum.PURCHASE_TAG.getName(), Arrays.asList(newDataTagVo));
                    break;
            }
        });
        return dataTagMap;
    }

    private DataTagVO getNewDataTagVO(DataTagVO dataTagVO) {
        DataTagVO newDataTagVo = DataTagVO.builder()
                .text(dataTagVO.getText())
                .uiStyle(dataTagVO.getUiStyle())
                .bgColor(dataTagVO.getBgColor())
                .borderColor(dataTagVO.getBorderColor())
                .type(dataTagVO.getType())
                .build();
        return newDataTagVo;
    }
}
