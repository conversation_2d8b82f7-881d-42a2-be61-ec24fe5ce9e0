package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.shop.service.ShopService;
import com.xyy.ec.pc.shop.vo.ShopInfoVO;
import com.xyy.electron.data.bussiness.data.api.SubjectCompanyLetterBussinessApi;
import com.xyy.electron.data.bussiness.data.api.XyyLicenseDownloadBussinessApi;
import com.xyy.electron.data.bussiness.data.dto.LicensesDto;
import com.xyy.electron.data.bussiness.data.enums.LicenseDownloadSourceEnum;
import com.xyy.electron.data.bussiness.data.enums.SceneCodeEnum;
import com.xyy.electron.data.bussiness.data.params.LicensesParams;
import com.xyy.electron.data.bussiness.data.tool.ErrorCode;
import com.xyy.electron.data.bussiness.data.tool.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 店铺售后控制层
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/pc")
public class ShopLicenseAfterSalesController extends BaseController {
    @Autowired
    private ShopService shopService;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0",check = false)
    private SubjectCompanyLetterBussinessApi subjectCompanyLetterBussinessApi;

    @Reference(version = "1.0.0")
    private XyyLicenseDownloadBussinessApi xyyLicenseDownloadBussinessApi;

    /**
     * 售后跳转页面
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/shop/afterSales.htm", method = RequestMethod.GET)
    public ModelAndView afterSales(String shopCode) throws Exception {
        Map<String,Object> model = new HashMap<String,Object>();
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        if (merchant == null) {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }
        model.put("shopCode",shopCode);
        return new ModelAndView("/selfshop/afterSales.ftl",model);
    }
    /**
     * 店铺资质
     * @param shopCode
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/shop/license", method = RequestMethod.POST)
    public Object queryShopLicense(@RequestParam("shopCode") String shopCode) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                log.info("ShopLicenseAfterSalesController#queryShopLicense 查询用户信息失败");
                return this.addError("查询用户信息失败");
            }
            ShopInfoVO shopInfoVo = shopService.getByCode(shopCode);
            if (shopInfoVo == null) {
                log.warn("ShopLicenseAfterSalesController#queryShopLicense 查询店铺信息失败, shopCode {}", shopCode);
                return this.addError("查询店铺信息失败");
            }
            String sceneCode = SceneCodeEnum.ec_ybm_scene_code.getCode();
            String branchCode = shopInfoVo.getBranchCode();
            ResultUtil<List<LicensesDto>> resultUtil = subjectCompanyLetterBussinessApi.getContracts(branchCode, sceneCode, merchant.getRealName(), merchant.getId());
            log.info("ShopLicenseAfterSalesController#queryShopLicense 查询资质，req: branchCode {} ; rep: {} ", branchCode, JSON.toJSONString(resultUtil));
            if (resultUtil.getCode() == ErrorCode.FAIL) {
                log.warn("ShopLicenseAfterSalesController#queryShopLicense 查询资质失败, code {}, msg {}",resultUtil.getCode(), resultUtil.getMsg());
                if(StringUtils.isNotEmpty(resultUtil.getMsg())){
                    return this.addError(resultUtil.getMsg());
                }else {
                    return this.addError("查询店铺资质失败");
                }
            }
            return this.addResult("rows", resultUtil.getDataInfo());
        } catch (Exception e) {
            log.error("ShopLicenseAfterSalesController#queryShopLicense 异常", e);
            return this.addError("查询店铺异常");
        }
    }

    /**
     * 下载资质列表
     * @param contractId
     * @param shopCode
     * @return
     */
    @RequestMapping("/downLoadShopLicense")
    @ResponseBody
    public Object downLoadShopLicense(@RequestParam("contractId") String contractId, @RequestParam(value = "shopCode", required = false) String shopCode) {
        try {
            MerchantBussinessDto dto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (null == dto) {
                log.info("下载资质,用户不存在");
                return this.addError("用户不存在");
            }
            String branchCode = dto.getRegisterCode();
            if (StringUtils.isNotEmpty(shopCode)) {
                ShopInfoVO shopInfoVo = shopService.getByCode(shopCode);
                if (shopInfoVo == null) {
                    log.warn("查询店铺信息失败, shopCode {}", shopCode);
                    return this.addError(XyyJsonResultCodeEnum.QUERY_SHOP_BY_SHOP_CODE_ERROR.getMsg());
                }
                branchCode = shopInfoVo.getBranchCode();
            }
            String sceneCode = SceneCodeEnum.ec_ybm_scene_code.getCode();
            LicensesParams params = new LicensesParams();
            params.setContractId(contractId);
            params.setSceneCode(sceneCode);
            params.setDownloadSource(LicenseDownloadSourceEnum.ec_ybm_app.getCode());
            params.setEcId(dto.getId());
            params.setRealName(dto.getRealName());
            params.setEcCompanyCode(branchCode);
            params.setProvinceName(dto.getProvince());
            log.info("ShopLicenseAfterSalesController#downLoadShopLicense 入参rep: {} ",JSON.toJSONString(params));
            ResultUtil<String> resultUtil = xyyLicenseDownloadBussinessApi.downloadContract(params);
            if (resultUtil.getCode() == ErrorCode.FAIL) {
                log.info("下载资质获取失败,resultUtil是{}", JSONObject.toJSONString(resultUtil));
                if(StringUtils.isNotEmpty(resultUtil.getMsg())){
                    return this.addError(resultUtil.getMsg());
                }else {
                    return this.addError("下载店铺资质失败");
                }
            }
            Map<String,String> map = new HashMap<>();
            map.put("url",resultUtil.getDataInfo());
            return this.addResult("data", map);
        } catch (Exception e) {
            log.error("下载资质失败,e=" + e);
            return this.addError("下载资质失败");
        }
    }

    /**
     * 全量下载资质列表
     * @return
     */
    @RequestMapping("/batch/downLoadShopLicense")
    @ResponseBody
    public Object batchDownLoadShopLicense(@RequestParam(value = "shopCode")String shopCode) {
        try {

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (null == merchant) {
                log.info("全量下载资质,用户不存在");
                return this.addError("用户不存在");
            }
            ShopInfoVO shopInfoVo = shopService.getByCode(shopCode);
            if (shopInfoVo == null) {
                log.warn("查询店铺信息失败, shopCode {}", shopCode);
                return this.addError(XyyJsonResultCodeEnum.QUERY_SHOP_BY_SHOP_CODE_ERROR.getMsg());
            }
            String sceneCode = SceneCodeEnum.ec_ybm_scene_code.getCode();
            LicensesParams params = new LicensesParams();
            params.setSceneCode(sceneCode);
            params.setDownloadSource(LicenseDownloadSourceEnum.ec_ybm_app.getCode());
            params.setEcId(merchant.getId());
            params.setRealName(merchant.getRealName());
            params.setEcCompanyCode(shopInfoVo.getBranchCode());
            params.setProvinceName(merchant.getProvince());
            log.info("ShopLicenseAfterSalesController#batchDownLoadShopLicense 入参rep: {} ",JSON.toJSONString(params));
            ResultUtil<String> resultUtil = xyyLicenseDownloadBussinessApi.downloadContractList(params);
            if (resultUtil.getCode() == ErrorCode.FAIL) {
                log.info("全量下载资质获取失败,resultUtil是{}", JSONObject.toJSONString(resultUtil));
                if(StringUtils.isNotEmpty(resultUtil.getMsg())){
                    return this.addError(resultUtil.getMsg());
                }else {
                    return this.addError("全量下载店铺资质失败");
                }
            }
            Map<String,String> map = new HashMap<>();
            map.put("url",resultUtil.getDataInfo());
            return this.addResult("data", map);
        } catch (Exception e) {
            log.error("全量下载资质失败,e=" + e);
            return this.addError("全量下载资质失败");
        }
    }
}
