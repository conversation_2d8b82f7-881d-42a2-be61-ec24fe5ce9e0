package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;

public enum LicenseStatusEnum {

    NOT_SUBMITTED(1, "资质未提交"),
    SUBMITTED(2, "资质已提交"),
    EXPIRED(3, "资质已过期"),
    PASSED(4, "资质已通过"),
    ;

    private int id;
    private String value;

    LicenseStatusEnum(int id, String value) {
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, LicenseStatusEnum> enumMaps = new HashMap<>();
    public static Map<Integer, String> maps = new HashMap<>();

    static {
        for (LicenseStatusEnum e : LicenseStatusEnum.values()) {
            enumMaps.put(e.getId(), e);
            maps.put(e.getId(), e.getValue());
        }
    }

    public static String get(int id) {
        return enumMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }

}
