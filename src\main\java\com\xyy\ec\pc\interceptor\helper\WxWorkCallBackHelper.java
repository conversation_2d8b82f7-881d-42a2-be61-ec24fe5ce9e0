package com.xyy.ec.pc.interceptor.helper;

import com.xyy.ec.pc.util.ipip.IPUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class WxWorkCallBackHelper {

    @Value("#{'${wxwork.callback.ip.white.list:}'.split(',')}")
    private List<String> whitelist;

    @Value("${wxwork.callback.url}")
    private String callbackUrl;


    public boolean isCallBack(String url) {
        return isCallBackUrl(url) && isWhiteList();
    }


    private boolean isCallBackUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        return url.contains(callbackUrl);
    }


    private boolean isWhiteList() {
        String ip = IPUtils.getIp();
        return whitelist.contains(ip);
    }


}
