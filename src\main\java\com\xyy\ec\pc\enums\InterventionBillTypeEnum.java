package com.xyy.ec.pc.enums;


/**
 * 票据类型
 */
public enum InterventionBillTypeEnum {
    //发票
    INVOICE(1,"发票",3),
    //出库单
    OUTBOUND_ORDER(2,"出库单",3);
    private int id;
    private String value;
    /**
     * @see InterventionTypeEnum
     */
    private int parentId;

    public  int getId() {
        return id;
    }
    public  String getValue() {
        return value;
    }
    public int getParentId() {
        return parentId;
    }

    public static String get(int id) {
        for (InterventionBillTypeEnum c : InterventionBillTypeEnum.values()) {
            if (c.getId() == id) {
                return c.value;
            }
        }
        return null;
    }
    InterventionBillTypeEnum(int id, String value) {
        this.id = id;
        this.value = value;

    }
    InterventionBillTypeEnum(int id, String value,  int parentId) {
        this.id = id;
        this.value = value;
        this.parentId = parentId;
    }
}
