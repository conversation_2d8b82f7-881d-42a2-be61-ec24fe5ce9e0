package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.dto.ShoppingCartBusinessDto;
import com.xyy.ec.order.dto.cart.BatchSelectCartDto;
import com.xyy.ec.order.dto.cart.ChangeCartDto;
import com.xyy.ec.order.dto.cart.SelectCartDto;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.dto.CartRespVO;
import com.xyy.ec.pc.newfront.dto.ChangeCartRespVO;
import com.xyy.ec.pc.newfront.service.CartNewService;
import com.xyy.ec.pc.newfront.vo.CartParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


/**
 * @description: 购物车相关modelView拆 ajax接口
 * @date: 2025/5/27 12:39
 * @author: <EMAIL>
 * @version: 1.0
 */
@CustomizeCmsResponse
@RestController
@RequiredArgsConstructor
@RequestMapping("/new-front/cart")
@Slf4j
public class CartNewController {

    private final CartNewService cartNewService;


    /**
     * 获取购物车信息
     * @param
     * @return AjaxResult
     */
    @PostMapping("/list")
    public AjaxResult<CartRespVO> list(){

          return cartNewService.list();

    }

    /**
     * 获取购物车商品数量
     *
     * @return AjaxResult
     */
    @GetMapping("/num")
    public AjaxResult<Integer> getCartNum() {
        try {
            return cartNewService.getCartNum();
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }
    @PostMapping("/change-cart")
    public AjaxResult<ChangeCartRespVO> changeCart(@RequestBody ShoppingCartBusinessDto cart) {
        try{
            return cartNewService.changeCart(cart);
        }catch (Exception e){
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @PostMapping("/group/change-cart")
    public AjaxResult<ChangeCartRespVO> changeCartForGroup(@RequestBody ChangeCartDto cart, HttpServletRequest req) {
        try {
            return AjaxResult.successResult(cartNewService.changeGroupCart(cart,req));
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }


    /**
     * 购物车勾选商品
     * 购物车明细ID，merchantId
     * @param
     * @return
     */
    @RequestMapping("/select-item")
    @ResponseBody
    public AjaxResult selectItem(@RequestBody SelectCartDto cart) {
        return cartNewService.selectItem(cart);
    }

    /**
     * 购物车取消勾选商品
     * 购物车明细ID，merchantId
     * @param cart
     * @return
     */
    @RequestMapping("/cancel-item")
    @ResponseBody
    public AjaxResult cancelItem(@RequestBody SelectCartDto cart) {
        return cartNewService.cancelItem(cart);
    }

    /**
     * 购物车勾选全部商品
     * 购物车明细ID，merchantId
     * @param cart
     * @return
     */
    @RequestMapping("/select-all-item")
    @ResponseBody
    public  AjaxResult selectAllItem(@RequestBody BatchSelectCartDto cart) {

        return cartNewService.selectAllItem(cart);
    }


    /**
     * 购物车取消勾选全部商品
     * 购物车明细ID，merchantId
     * @param cart
     * @return
     */
    @RequestMapping("/cancel-all-item")
    @ResponseBody
    public AjaxResult cancelAllItem(@RequestBody BatchSelectCartDto cart) {
        return cartNewService.cancelAllItem(cart);
    }

    /**
     * 清空个人购物车
     * @return
     */
    @RequestMapping("/clean-cart")
    @ResponseBody
    public AjaxResult cleanCart(){
        return cartNewService.cleanCart();
    }

}
