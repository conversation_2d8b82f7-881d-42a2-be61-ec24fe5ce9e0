package com.xyy.ec.pc.remote;


import com.xyy.ec.cs.api.dto.PlatformInAddNodeDTO;
import com.xyy.ec.cs.api.dto.PlatformInWorkorderCreateDTO;
import com.xyy.ec.pc.model.SupplementaryDescriptionDTO;

public interface InterventionService {
    Long  create(PlatformInWorkorderCreateDTO param);
    Long  getCountdownTime(PlatformInAddNodeDTO workOrderId);

    /**
     * 查看退款详情
     */
    void  refundDetail(String refundNo);

    Boolean updateWorkOrder(PlatformInAddNodeDTO dto);

    Boolean addWorkOrderNode(PlatformInAddNodeDTO dto);
    //意见征询
    Boolean opinionSubmit(PlatformInAddNodeDTO dto);

    //资质异常
    Boolean abnormalQualification(PlatformInAddNodeDTO addNodeDTO);
    //确认无误
    Boolean confirmAccuracy(PlatformInAddNodeDTO addNodeDTO);

}
