package com.xyy.ec.pc.search.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.api.service.ProductService;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.data.vo.ProductTagVo;
import com.xyy.ec.marketing.client.dto.SuiXinPinSkuDTO;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingSkuDto;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.TagTypeEnum;
import com.xyy.ec.pc.remote.OrderBackendService;
import com.xyy.ec.pc.rpc.HyperSpaceRpc;
import com.xyy.ec.pc.rpc.ProductServiceRpc;
import com.xyy.ec.pc.search.enums.SearchSourceEnum;
import com.xyy.ec.pc.search.enums.SuiXinPinSourceType;
import com.xyy.ec.pc.search.service.DataService;
import com.xyy.ec.pc.search.service.SkuProductService;
import com.xyy.ec.pc.search.vo.*;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingWholesaleActivityInfoDTO;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.JsonUtil;
import com.xyy.ec.pc.util.ProductMangeUtils;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.product.business.ecp.csutag.dto.TagDTO;
import com.xyy.ec.product.business.ecp.out.product.ProdcutApi;
import com.xyy.ec.product.business.ecp.out.search.api.ProductForSearchApi;
import com.xyy.ec.product.business.enums.HighGrossEnum;
import com.xyy.ec.search.engine.entity.EcProductVo;
import com.xyy.ec.search.engine.enums.CsuVisible;
import com.xyy.ec.shop.server.business.api.ShopQueryApi;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * <AUTHOR> cao
 * @version V1.0
 * @Descriotion: TODO
 * @create 2020/11/30 10:35
 */
@Service
@Slf4j
public class SkuProductServiceImpl implements SkuProductService {

    @Reference(version = "1.0.0")
    private ProductForSearchApi productForSearchApi;

    @Reference(version = "1.0.0", timeout = 200)
    ProdcutApi prodcutApi;

    @Autowired
    private HyperSpaceRpc hyperSpaceRpc;

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private DataService dataService;

    @Reference(version = "1.0.0")
    private ShopQueryApi shopQueryApi;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private ProductServiceRpc productServiceRpc;

    @Autowired
    private CmsAppProperties cmsAppProperties;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private ProductService productService;
    @Autowired
    private OrderBackendService orderBackendService;

    @Override
    public List<PcSearchProductInfoVo> getProductInfoList(Long merchantId, String branchCode, List<EcProductVo> productVoList, Boolean isNeedPurchaseCutPriceTag, Integer searchSource) {
        List<PcSearchProductInfoVo> list = getProductBaseInfoList(merchantId, branchCode, productVoList, searchSource);
        try {
            if (CollectionUtil.isEmpty(list)) {
                return list;
            }
            //处理标签
            handlerTags(list, merchantId, hasShopDataTags(searchSource));
            //判断是否随心拼搜索
            if (Objects.nonNull(searchSource) && SearchSourceEnum.SUI_XIN_PIN_SEARCH.getValue().equals(searchSource)) {
                List<Long> skuIdList = list.stream().map(PcSearchProductInfoVo::getId).collect(Collectors.toList());
                Map<Long, SuiXinPinSkuDTO> suiXinPinDiscountDTOMap = marketingService.getSuiXinPinSkuDiscountBySkuIds(merchantId, skuIdList);
                list.forEach(appSearchProductInfoVo -> {
                    setActSuiXinPinInfo(suiXinPinDiscountDTOMap, appSearchProductInfoVo);
                });
                //过滤随心拼信息为空的sku
                int beforeCount = list.size();
                list = list.stream().filter(appSearchProductInfoVo -> Objects.nonNull(appSearchProductInfoVo.getActSuiXinPin())).collect(Collectors.toList());

                fillSearchProductExtra(list);

                log.info("随心拼过滤随心拼信息为空的品, 过滤前数量 : {}, 过滤后数量 : {}", beforeCount, CollectionUtil.isEmpty(list) ? BigInteger.ZERO.intValue() : list.size());
            }
        } catch (Exception e) {
            log.error("ProductService getCsuInfoList 获取商品基础信息失败", e);
        }
        return list;
    }

    private void fillSearchProductExtra(List<PcSearchProductInfoVo> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        List<Long> skuIds = list.stream().map(s -> s.getId()).collect(Collectors.toList());
        Map<Long, String> skuId2promoTag = orderBackendService.queryPromoTag(skuIds);

        // 填充promoTag
        for (PcSearchProductInfoVo infoVo : list) {
            infoVo.setPromoTag(skuId2promoTag.get(infoVo.getId()));

            // ...填充其他字段
        }
        
    }

    private List<PcSearchProductInfoVo> getProductBaseInfoList(Long merchantId, String branchCode, List<EcProductVo> ecProductVos, Integer searchSource) {
        List<PcSearchProductInfoVo> list = new ArrayList<>();
        try {
            List<Long> csuIds = ecProductVos.stream().map(ecProductVo -> ecProductVo.getId()).collect(Collectors.toList());
            ApiRPCResult apiRPCResult = productForSearchApi.findProductInfoBySkuIdList(csuIds, branchCode, merchantId);
            if (apiRPCResult.isFail()) {
                log.error("获取商品基础信息失败 code : {}, msg : {}, errMsg : {}", apiRPCResult.getCode(), apiRPCResult.getMsg(), apiRPCResult.getErrMsg());
                return list;
            }
            List<ProductDTO> productDTOList = (List<ProductDTO>) apiRPCResult.getData();
            //填充库存信息
            productDTOList = productServiceRpc.fillAllActTotalSurplusQtyToAvailableQty(productDTOList);
            {
                if (log.isDebugEnabled()) {
                    log.debug("【处方药商品默认图处理】merchantId：{}，searchSource：{}，原商品信息：{}", merchantId, searchSource, JSONArray.toJSONString(productDTOList));
                }
                if (Objects.equals(SearchSourceEnum.SHOP_SEARCH.getValue(), searchSource)) {
                    // 处方药商品默认图处理
                    if (BooleanUtils.isTrue(cmsAppProperties.getIsOpenIndexProductDefaultImageFeature())) {
                        String defaultImageUrl = cmsAppProperties.getProductDefaultImageUrl();
                        productDTOList.stream().filter(item -> Objects.equals(item.getDrugClassification(), 3))
                                .forEach(item -> item.setImageUrl(defaultImageUrl));
                        if (log.isDebugEnabled()) {
                            log.debug("【处方药商品默认图处理】merchantId：{}，searchSource：{}，处理后商品信息：{}", merchantId, searchSource, JSONArray.toJSONString(productDTOList));
                        }
                    }
                }
            }
            //获取店铺列表
            Set<String> shopCodes = productDTOList.stream().map(productDTO -> productDTO.getShopCode()).collect(Collectors.toSet());
            Map<String, ShopInfoDTO> shopInfoDTOMap = queryShopInfoForMap(new ArrayList<>(shopCodes));
            // 查询拼团和批购包邮活动信息
            List<Long> marketingActivityCsuIds = productDTOList.stream()
                    .filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.PROMOTION_SKU_TYPE.getId())
                            || Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.WHOLESALE_TYPE.getId()))
                    .map(ProductDTO::getId).collect(Collectors.toList());
            Set<Long> gaoMaoSkuIdSet = Sets.newHashSet();
            gaoMaoSkuIdSet = productDTOList.stream().filter(productDto -> {
                if(null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                        || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))){
                    return true;
                }
                return false;
            }).map(ProductDTO :: getId).collect(Collectors.toSet());
            //查询拼团活动信息
            Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap = marketingService.getActCardInfoBySkuIdListForSearch(marketingActivityCsuIds, merchantId,
                    Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
            //查询秒杀商品活动信息
            List<Long> skIdList = productDTOList.stream()
                    .filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.SECKILL_SKU_TYPE.getId()))
                    .map(ProductDTO::getId).collect(Collectors.toList());
            Map<Long, MarketingSeckillActivityInfoDTO> seckillActivityInfoDTOMap = marketingService.getShowingSeckillActivityInfoByCsuIdsForSearch(merchantId, skIdList);

            Map<Long, PcSearchProductInfoVo> csuInfoMap = productDTOList.stream().collect(Collectors.toMap(ProductDTO::getId, productDTO -> {
                PcSearchProductInfoVo pcSearchProductInfoVo = objectMapper.convertValue(productDTO, PcSearchProductInfoVo.class);
                Map<String, Object> tags = new HashMap<>();
                if (CollectionUtil.isNotEmpty(productDTO.getProductTagList())) {
                    tags.put(TagTypeEnum.PRODUCT_TAG.getName(), productDTO.getProductTagList());
                }
                tags.put(TagTypeEnum.MARKER_TAG.getName(), productDTO.getMarkerUrl());
                if (CollectionUtil.isNotEmpty(productDTO.getTitleTagList())) {
                    tags.put(TagTypeEnum.TITLE_TAG.getName(), productDTO.getTitleTagList());
                }
                tags.put(TagTypeEnum.ACTIVITY_TAG.getName(), productDTO.getActivityTag());

                //添加快递标签到数据标签集合
                if (CollectionUtil.isNotEmpty(productDTO.getExpressList())) {
                    List<ProductTagVo> expressProductTagVoList = productDTO.getExpressList().stream().map(express -> objectMapper.convertValue(express, ProductTagVo.class)).collect(Collectors.toList());
                    tags.put(TagTypeEnum.DATA_TAG.getName(), expressProductTagVoList);
                }
                if (productDTO.getUnitPriceTag() != null){
                    tags.put(TagTypeEnum.UNIT_PRICE_TAG.getName(), productDTO.getUnitPriceTag());
                }

                pcSearchProductInfoVo.setTags(tags);
                //设置店铺信息
                Optional.ofNullable(shopInfoDTOMap.get(productDTO.getShopCode())).ifPresent(shopInfoDTO -> setShopInfo(pcSearchProductInfoVo, shopInfoDTO));
                //设置拼团信息
                setActPtInfo(groupBuyingInfoDtoMap, productDTO, pcSearchProductInfoVo);
                //设置秒杀活动信息
                setActSkInfo(productDTO, pcSearchProductInfoVo, seckillActivityInfoDTOMap);
                // 设置批购包邮活动信息
                setActPgbyInfo(groupBuyingInfoDtoMap, productDTO, pcSearchProductInfoVo);
                //设置受托厂家
                if(StringUtils.isNotBlank(pcSearchProductInfoVo.getEntrustedManufacturer())){
                    pcSearchProductInfoVo.setManufacturer(ProductMangeUtils.getManufacturer(pcSearchProductInfoVo.getMarketAuthor(),pcSearchProductInfoVo.getManufacturer(),pcSearchProductInfoVo.getEntrustedManufacturer()));
                }
                return pcSearchProductInfoVo;
            }));

            //保持调用前顺序
            list = csuIds.stream().map(csuId -> csuInfoMap.get(csuId)).filter(csuInfo -> csuInfo != null).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("ProductService getProductBaseInfoList 获取商品基础信息失败", e);
        }
        return list;
    }

    /**
     * 查询spu聚合sku信息
     * @param skuId
     * @param merchantId
     * @return
     */
    public List<ComparePricesProdcutInfoVo> findSpuAggregatSku(Long skuId, Long merchantId) {
        List<ComparePricesProdcutInfoVo> result = Lists.newArrayList();

        try {
            if (ObjectUtil.isNull(skuId) || merchantId == null) {
                return Lists.newArrayList();
            }
            ApiRPCResult<List<ProductDTO>> apiRPCResult = prodcutApi.findSpuAggregatSku(skuId, merchantId);
            if (ObjectUtil.isNull(apiRPCResult) || apiRPCResult.isFail()) {
                log.error("findSpuAggregatSku skuId : {}, merchantId : {}, result : {}", skuId, merchantId, JsonUtil.toJson(apiRPCResult));
                return Lists.newArrayList();
            }
            List<ProductDTO> productDTOList = apiRPCResult.getData();
            if (CollectionUtil.isEmpty(productDTOList)) {
                return Lists.newArrayList();
            }

            // 查询拼团和批购包邮活动信息
            List<Long> marketingActivityCsuIds = productDTOList.stream()
                    .filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.PROMOTION_SKU_TYPE.getId())
                            || Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.WHOLESALE_TYPE.getId()))
                    .map(ProductDTO::getId).collect(Collectors.toList());
            Set<Long> gaoMaoSkuIdSet = Sets.newHashSet();
            gaoMaoSkuIdSet = productDTOList.stream().filter(productDto -> {
                if(null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                        || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))){
                    return true;
                }
                return false;
            }).map(ProductDTO :: getId).collect(Collectors.toSet());
            //查询拼团活动信息
            Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap = marketingService.getActCardInfoBySkuIdListForSearch(marketingActivityCsuIds, merchantId,
                    Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
            //查询秒杀商品活动信息
            List<Long> skIdList = productDTOList.stream()
                    .filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.SECKILL_SKU_TYPE.getId()))
                    .map(ProductDTO::getId).collect(Collectors.toList());
            Map<Long, MarketingSeckillActivityInfoDTO> seckillActivityInfoDTOMap = marketingService.getShowingSeckillActivityInfoByCsuIdsForSearch(merchantId, skIdList);

            List<ComparePricesProdcutInfoVo> comparePricesProdcutInfos = Lists.newArrayList();

            productDTOList.forEach(productDTO -> {
                PcSearchProductInfoVo pcSearchProductInfoVo = objectMapper.convertValue(productDTO, PcSearchProductInfoVo.class);
                //设置拼团信息
                setActPtInfo(groupBuyingInfoDtoMap, productDTO, pcSearchProductInfoVo);
                //设置秒杀活动信息
                setActSkInfo(productDTO, pcSearchProductInfoVo, seckillActivityInfoDTOMap);
                // 设置批购包邮活动信息
                setActPgbyInfo(groupBuyingInfoDtoMap, productDTO, pcSearchProductInfoVo);
                setPriceTags(pcSearchProductInfoVo,productDTO);
                ComparePricesProdcutInfoVo comparePricesProdcutInfoVo = new ComparePricesProdcutInfoVo();
                BeanUtils.copyProperties(pcSearchProductInfoVo, comparePricesProdcutInfoVo);

                //赋值排序拼团价格
                if (Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.PROMOTION_SKU_TYPE.getId())) {
                    if (ObjectUtil.isNull(pcSearchProductInfoVo.getActPt())) {
                        return;
                    }
                    comparePricesProdcutInfoVo.setSortPrices(pcSearchProductInfoVo.getActPt().getAssemblePrice());
                }
                //处理秒杀价格
                else if (Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.SECKILL_SKU_TYPE.getId())) {
                    if (ObjectUtil.isNull(pcSearchProductInfoVo.getActSk())) {
                        return;
                    }
                    comparePricesProdcutInfoVo.setSortPrices(pcSearchProductInfoVo.getActSk().getSkPrice());
                }
                //处理批购包邮价格
                else if (Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.WHOLESALE_TYPE.getId())) {
                    if (ObjectUtil.isNull(pcSearchProductInfoVo.getActPgby())) {
                        return;
                    }
                    comparePricesProdcutInfoVo.setSortPrices(pcSearchProductInfoVo.getActPgby().getAssemblePrice());
                }
                //默认取药帮忙价格
                else {
                    comparePricesProdcutInfoVo.setSortPrices(pcSearchProductInfoVo.getFob());
                }
                comparePricesProdcutInfos.add(comparePricesProdcutInfoVo);
            });

            //先对结果按照价格价格排序
            comparePricesProdcutInfos.sort(Comparator.comparing(ComparePricesProdcutInfoVo::getSortPrices));

            //选择高毛、甄选、特推商品
            ComparePricesProdcutInfoVo highGrossProduct = selectHighGrossProduct(comparePricesProdcutInfos);

            //高毛品的标准库id
            String highGrossMasterStandardProductId = ObjectUtil.isNull(highGrossProduct) ? "" : highGrossProduct.getMasterStandardProductId();

            Set<String> masterStandardProductIdIdSet = Sets.newHashSet();
            for (ComparePricesProdcutInfoVo item : comparePricesProdcutInfos) {

                //如果是已经选择出的高毛品的标准库id，则跳过
                if (StringUtils.isNotEmpty(highGrossMasterStandardProductId) && highGrossMasterStandardProductId.equals(item.getMasterStandardProductId())) {
                    continue;
                }

                //如果当前是结果中的第3个位置 & 存在高毛品 ,则放到第3个位置
                if (masterStandardProductIdIdSet.size() == 2 && ObjectUtil.isNotNull(highGrossProduct)) {
                    result.add(highGrossProduct);
                    masterStandardProductIdIdSet.add(highGrossProduct.getMasterStandardProductId());
                }

                //如果标准库id不存在，则放入到结果中，每个标准库id只取一个价格最低的品
                if (!masterStandardProductIdIdSet.contains(item.getMasterStandardProductId())) {
                    result.add(item);
                    masterStandardProductIdIdSet.add(item.getMasterStandardProductId());
                }
                if (masterStandardProductIdIdSet.size() >= 25) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("ProductService findSpuAggregatSku 获取spu聚合商品信息失败", e);
        }
        return result;
    }

    @Override
    public List<PcSearchProductInfoVo> getSuiXinPinWhiteProductInfoList(Long merchantId, String branchCode, String shopCode) {
        if (Objects.isNull(merchantId) || Objects.isNull(shopCode)) {
            return Collections.emptyList();
        }
        try {
            List<Long> suiXinPinTopSkuList = hyperSpaceRpc.querySuiXinPinTopSku(merchantId, shopCode);
            log.info("随心拼白名单列表, merchantId : {}, shopCode : {}, skuList : {}", merchantId, shopCode, suiXinPinTopSkuList);
            //控销过滤，返回可见可买商品
            List<Long> visibleList = productService.findVisibleListForRecommend(suiXinPinTopSkuList, branchCode, merchantId);
            log.info("随心拼白名单列表, merchantId : {}, shopCode : {}, skuList : {}, visibleList : {}", merchantId, shopCode, suiXinPinTopSkuList, visibleList);
            if (CollectionUtil.isNotEmpty(visibleList)) {
                List<EcProductVo> suiXinPinTopEcProductList = visibleList.stream().map(skuId -> EcProductVo.builder().id(skuId).build()).collect(Collectors.toList());
                List<PcSearchProductInfoVo> searchProductInfoVos = getProductInfoList(merchantId, branchCode, suiXinPinTopEcProductList, false,  SearchSourceEnum.SUI_XIN_PIN_SEARCH.getValue());
                return Optional.ofNullable(searchProductInfoVos).orElseGet(() -> Collections.emptyList()).stream().map(appSearchProductInfoVo -> {
                    appSearchProductInfoVo.setSourceType(SuiXinPinSourceType.WHITE_LIST.getValue());
                    return appSearchProductInfoVo;
                }).collect(Collectors.toList());
            }
        } catch (Exception ex) {
            log.error("查询随心拼白名单异常, merchantId : {}, shopCode : {}, branchCode : {}, error : ", merchantId, shopCode, branchCode, ex);
        }
        return Collections.emptyList();
    }

    /**
     * 查询商品有效最小起购数大于1的商品
     */
    @Override
    public List<Long> findMinPurchaseNumGreaterThanOne(List<Long> skuIdList) {
        try {
            if (CollectionUtil.isEmpty(skuIdList)) {
                return Collections.emptyList();
            }
            ApiRPCResult<List<Long>> apiRPCResult = productForSearchApi.findMinPurchaseNumGreaterThanOne(skuIdList);
            log.info("findMinPurchaseNumGreaterThanOne apiRpcResult : {}", JSONObject.toJSONString(apiRPCResult));
            if (apiRPCResult.isSuccess()) {
                return apiRPCResult.getData();
            }
        } catch (Exception ex) {
            log.error("findMinPurchaseNumGreaterThanOne error, skuIdList : {}, ex : ", skuIdList, ex);
        }
        return Collections.emptyList();
    }

    private void setPriceTags(PcSearchProductInfoVo pcSearchProductInfoVo,ProductDTO productDTO) {
        Map<String, Object> tags = pcSearchProductInfoVo.getTags();
        if (productDTO.getUnitPriceTag() != null){
            if (tags == null) {
                tags = Maps.newHashMap();
            }
            tags.put(TagTypeEnum.UNIT_PRICE_TAG.getName(), productDTO.getUnitPriceTag());
        }
        pcSearchProductInfoVo.setTags(tags);
    }

    /**
     * 选择 甄选>高毛>特推 的商品
     * @return
     */
    private ComparePricesProdcutInfoVo selectHighGrossProduct(List<ComparePricesProdcutInfoVo> comparePricesProdcutInfos) {
        Map<Integer, ComparePricesProdcutInfoVo> temp = Maps.newHashMap();

        for (ComparePricesProdcutInfoVo item : comparePricesProdcutInfos) {
            //甄选
            if (HighGrossEnum.PICK_CHOOSE.getId() == item.getHighGross() && !temp.containsKey(HighGrossEnum.PICK_CHOOSE.getId())) {
                return item;
            }
            //高毛
            else if (HighGrossEnum.HIGH_GROSS.getId() == item.getHighGross() && !temp.containsKey(HighGrossEnum.HIGH_GROSS.getId())) {
                temp.put(HighGrossEnum.HIGH_GROSS.getId(), item);
            }
            // 特推
            else if (HighGrossEnum.STRICT_SELECT.getId() == item.getHighGross() && !temp.containsKey(HighGrossEnum.STRICT_SELECT.getId())) {
                temp.put(HighGrossEnum.STRICT_SELECT.getId(), item);
            }
        }
        return temp.containsKey(HighGrossEnum.HIGH_GROSS.getId()) ? temp.get(HighGrossEnum.HIGH_GROSS.getId()) : temp.get(HighGrossEnum.STRICT_SELECT.getId());
    }

    /**
     * 填充秒杀活动信息
     *
     * @param productDTO
     * @param appSearchProductInfoVo
     */
    private void setActSkInfo(ProductDTO productDTO, PcSearchProductInfoVo appSearchProductInfoVo, Map<Long, MarketingSeckillActivityInfoDTO> seckillActivityInfoDTOMap) {
        if (productDTO == null || appSearchProductInfoVo == null || MapUtils.isEmpty(seckillActivityInfoDTOMap)) {
            return;
        }
        //秒杀类型商品
        Optional.ofNullable(seckillActivityInfoDTOMap.get(productDTO.getId())).ifPresent(ackSkInfo -> {
            if (productDTO.getProductType().equals(ProductEnumDTO.ProductTypeEnum.SECKILL_SKU_TYPE.getId())) {
                SeckillActInfoVo skInfo = SeckillActInfoVo.builder()
                        .currentTime(System.currentTimeMillis())
                        .startTime(ackSkInfo.getStartTime().getTime())
                        .endTime(ackSkInfo.getEndTime().getTime())
                        .skPrice(ackSkInfo.getSkPrice())
                        .status(ackSkInfo.getStatus())
                        .percentage(ackSkInfo.getPercentage())
                        .build();

                appSearchProductInfoVo.setPricePrefix("秒杀价");
                appSearchProductInfoVo.setActSk(skInfo);
            }
        });
    }

    private void setActPtInfo(Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap, ProductDTO productDTO, PcSearchProductInfoVo pcSearchProductInfoVo) {
        Optional.ofNullable(groupBuyingInfoDtoMap.get(productDTO.getId())).ifPresent(buyingInfoDto -> {
            if (!Objects.equals(buyingInfoDto.getActivityType(), MarketingEnum.PING_TUAN.getCode())) {
                return;
            }
            //活动信息
            GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtil.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList()) ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
            /** 拼团活动状态 1.未开始 ，2.拼团中，3.已结束 兼容老逻辑前端拼团活动状态 0-未开始 1-进行中 2-结束 **/
            Integer assembleStatus = buyingInfoDto.getStatus() == null ? 0 : buyingInfoDto.getStatus() - 1;

            PinTuanActInfoVo pinTuanActInfoVo = PinTuanActInfoVo.builder()
                    .marketingId(buyingInfoDto.getMarketingId())
                    .percentage(String.valueOf(buyingInfoDto.getPercentage().multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_UP))))
                    .assembleStatus(assembleStatus)
                    .assembleStartTime(buyingInfoDto.getStartTime().getTime())
                    .assembleEndTime(buyingInfoDto.getEndTime().getTime())
                    .surplusTime((buyingInfoDto.getEndTime().getTime() - System.currentTimeMillis()) / 1000)
                    .orderNum(buyingInfoDto.getOrderNum())
                    .skuStartNum(groupBuyingSkuDto.getSkuStartNum())
                    .preheatShowPrice(buyingInfoDto.getPreheatShowPrice())
                    // 拼团多阶梯价信息
                    .stepPriceStatus(buyingInfoDto.getStepPriceStatus())
                    .minSkuPrice(buyingInfoDto.getMinSkuPrice())
                    .maxSkuPrice(buyingInfoDto.getMaxSkuPrice())
                    .startingPriceShowText(buyingInfoDto.getStartingPriceShowText())
                    .rangePriceShowText(buyingInfoDto.getRangePriceShowText())
                    .stepPriceShowTexts(buyingInfoDto.generateStepPriceShowTexts(pcSearchProductInfoVo.getProductUnit()))
                    .build();
            Optional.ofNullable(groupBuyingSkuDto).ifPresent(groupBuyingSkuDto1 -> {
                pinTuanActInfoVo.setSkuStartNum(groupBuyingSkuDto1.getSkuStartNum());
                pinTuanActInfoVo.setAssemblePrice(groupBuyingSkuDto1.getSkuPrice());
            });
            //调整拼团商品的showName
            StringBuilder sbShowName = new StringBuilder();
            if (SearchUtils.isShouTuiYouXuan(productDTO.getFirstChoose(), productDTO.getHighGross())) {
                sbShowName.append(Constants.SHOU_TUI_YOU_XUAN_TEXT);
            } else if (StringUtils.isNotEmpty(buyingInfoDto.getTopicPrefix())){
                sbShowName.append(buyingInfoDto.getTopicPrefix());
            } else {
                sbShowName.append(Constants.PT_TEXT);
            }
            if (groupBuyingSkuDto.getSkuStartNum() != null) {
                String productUnit = Optional.ofNullable(pcSearchProductInfoVo.getProductUnit()).orElse("");
                sbShowName.append(groupBuyingSkuDto.getSkuStartNum()).append(productUnit).append("包邮").append(" ")
                        .append(pcSearchProductInfoVo.getShowName());
            } else {
                sbShowName.append(pcSearchProductInfoVo.getShowName());
            }

            pcSearchProductInfoVo.setShowName(sbShowName.toString());
            pcSearchProductInfoVo.setActPt(pinTuanActInfoVo);
        });
    }

    /**
     * 填充批购包邮信息
     *
     * @param groupBuyingInfoDtoMap
     * @param productDTO
     * @param pcSearchProductInfoVo
     */
    private void setActPgbyInfo(Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap, ProductDTO productDTO, PcSearchProductInfoVo pcSearchProductInfoVo) {
        if (productDTO == null || pcSearchProductInfoVo == null || MapUtils.isEmpty(groupBuyingInfoDtoMap)) {
            return;
        }
        Optional.ofNullable(groupBuyingInfoDtoMap.get(productDTO.getId())).ifPresent(buyingInfoDto -> {
            if (!Objects.equals(buyingInfoDto.getActivityType(), MarketingEnum.PI_GOU_BAO_YOU.getCode())) {
                return;
            }
            // 活动信息
            GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtil.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList()) ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
            MarketingWholesaleActivityInfoDTO marketingWholesaleActivityInfoDTO = MarketingWholesaleActivityInfoDTO.builder()
                    .marketingId(buyingInfoDto.getMarketingId())
                    .activityType(buyingInfoDto.getActivityType())
                    .skuStartNum(groupBuyingSkuDto.getSkuStartNum())
                    .build();
            Optional.ofNullable(groupBuyingSkuDto).ifPresent(tempGroupBuyingSkuDto -> {
                marketingWholesaleActivityInfoDTO.setSkuStartNum(tempGroupBuyingSkuDto.getSkuStartNum());
                marketingWholesaleActivityInfoDTO.setAssemblePrice(tempGroupBuyingSkuDto.getSkuPrice());
            });
            // 调整商品的showName
            StringBuilder sbShowName = new StringBuilder();
            if (groupBuyingSkuDto.getSkuStartNum() != null) {
                String productUnit = Optional.ofNullable(pcSearchProductInfoVo.getProductUnit()).orElse("");
                sbShowName.append(groupBuyingSkuDto.getSkuStartNum()).append(productUnit).append("包邮").append(" ").append(pcSearchProductInfoVo.getShowName());
            } else {
                sbShowName.append(pcSearchProductInfoVo.getShowName());
            }
            // 追加规格显示
            if (StringUtils.isNotEmpty(pcSearchProductInfoVo.getSpec()) && !Objects.equals(pcSearchProductInfoVo.getSpec(), Constants.LINE)) {
                sbShowName.append("/").append(pcSearchProductInfoVo.getSpec());
            }
            pcSearchProductInfoVo.setShowName(sbShowName.toString());
            pcSearchProductInfoVo.setActPgby(marketingWholesaleActivityInfoDTO);
        });
    }

    /**
     * 随心拼活动信息
     *
     * @param suiXinPinDiscountDTOMap
     * @param pcSearchProductInfoVo
     */
    private void setActSuiXinPinInfo(Map<Long, SuiXinPinSkuDTO> suiXinPinDiscountDTOMap, PcSearchProductInfoVo pcSearchProductInfoVo) {
        if (MapUtils.isEmpty(suiXinPinDiscountDTOMap) || Objects.isNull(pcSearchProductInfoVo)) {
            return;
        }
        //设置随心拼价格
        Optional.ofNullable(suiXinPinDiscountDTOMap.get(pcSearchProductInfoVo.getId())).ifPresent(suiXinPinInfoDto -> {
            pcSearchProductInfoVo.setActSuiXinPin(SuiXinPinActInfoVo.builder().suiXinPinPrice(suiXinPinInfoDto.getDiscountPrice()).build());
            //调整药帮忙售卖价格取随心拼返回的价格
            pcSearchProductInfoVo.setFob(suiXinPinInfoDto.getFob());
            //追加规格显示
            StringBuilder sbShowName = new StringBuilder(pcSearchProductInfoVo.getShowName());
            if (StringUtils.isNotEmpty(pcSearchProductInfoVo.getSpec()) && !pcSearchProductInfoVo.getSpec().equals(Constants.LINE)) {
                sbShowName.append("/").append(pcSearchProductInfoVo.getSpec());
            }
            pcSearchProductInfoVo.setShowName(sbShowName.toString());
        });
    }
    /**
     * 设置店铺信息
     *
     * @param pcSearchProductInfoVo
     * @param shopInfoDTO
     */
    private void setShopInfo(PcSearchProductInfoVo pcSearchProductInfoVo, ShopInfoDTO shopInfoDTO) {
        Optional.ofNullable(shopInfoDTO).ifPresent(shopInfoDTO1 -> {
            pcSearchProductInfoVo.setShopUrl(shopInfoDTO.getPcLink());
            pcSearchProductInfoVo.setShopName(shopInfoDTO.getShowName());
//            //兼容老逻辑，老商品需要单独拼装POP店铺跳转链接,店铺名称
//            if (pcSearchProductInfoVo.getIsThirdCompany() == 1) {
//                pcSearchProductInfoVo.setShopUrl(String.format(Constants.POP_SHOP_URL, pcSearchProductInfoVo.getOrgId()));
//            }
        });
    }


    @Override
    public List<PcSearchProductInfoVo> handleSnowGroundParam(List<PcSearchProductInfoVo> list, String nsid, String listoffset) {
        if (CollectionUtil.isEmpty(list)) {
            return list;
        }
        Integer offset = NumberUtils.toInt(listoffset, 0);
        if (offset != 0) {
            offset = offset - list.size();
        }
        for (PcSearchProductInfoVo vo : list) {
            offset++;
            String voNsid = SearchUtils.generateSkuNewSidData(nsid, offset);
            vo.setNsid(voNsid);
        }
        return list;
    }


    /**
     * 查询店铺信息
     *
     * @param shopCodeList
     * @return
     */
    private Map<String, ShopInfoDTO> queryShopInfoForMap(List<String> shopCodeList) {
        //批量查询店铺信息
        ApiRPCResult<List<ShopInfoDTO>> apiRPCResult = shopQueryApi.queryShopByShopCodes(shopCodeList);
        if (apiRPCResult.isFail()) {
            log.error("shopQueryApi.queryShopByShopCodes failed, shopCodes : {}, code : {}, errMsg : {}", shopCodeList, apiRPCResult.getCode(), apiRPCResult.getErrMsg());
            return Maps.newHashMap();
        } else {
            Map<String, ShopInfoDTO> shopInfoDTOMap = apiRPCResult.getData().stream().collect(Collectors.toMap(ShopInfoDTO::getShopCode, shopInfoDTO -> shopInfoDTO, (k1, k2) -> k1));
            return shopInfoDTOMap;
        }
    }

    /**
     * 处理标签
     *
     * @param productInfoVoList
     */
    private void handlerTags(List<PcSearchProductInfoVo> productInfoVoList, Long merchantId, Boolean hasShopDataTags) {
        if (CollectionUtil.isNotEmpty(productInfoVoList)) {
            //查询数据标签
            Map<Long, Map<String, Object>> dataTagMaps = dataService.getSkuDataTagList(merchantId, productInfoVoList, hasShopDataTags);
            productInfoVoList.stream().forEach(productInfoVo -> {
                Map<String, Object> allTags = productInfoVo.getTags();
                List<TagDTO> tagDTOList = (List<TagDTO>) allTags.get(TagTypeEnum.PRODUCT_TAG.getName());
                //剔除uiType = 2 优惠券标签, 单独处理赋值 1: 临期, 2:券, 4: 自定义2, 3:其他 ,5:oem 6:三合
                List<TagDTO> couponTagList = Optional.ofNullable(tagDTOList).orElseGet(() -> Collections.emptyList()).stream().filter(tagDTO -> tagDTO.getPromoType() != null && tagDTO.getPromoType() == MarketingEnum.YOU_HUI_QUAN.getCode()).collect(Collectors.toList());
                //取优惠券列表
                if (CollectionUtil.isNotEmpty(couponTagList)) {
                    allTags.put(TagTypeEnum.COUPON_TAGS.getName(), couponTagList);
                }
                //提取活动标签,不包含优惠券
                List<TagDTO> activityTags = Optional.ofNullable(tagDTOList).orElseGet(() -> Collections.emptyList()).stream().filter(tagDTO -> Objects.nonNull(tagDTO.getPromoType()) && tagDTO.getPromoType() != MarketingEnum.YOU_HUI_QUAN.getCode()).collect(Collectors.toList());
                Optional.ofNullable(activityTags).ifPresent(activityTags1 -> {
                    allTags.put(TagTypeEnum.ACTIVITY_TAGS.getName(), activityTags);
                });
                //获取商品基本标签
                List<TagDTO> productTagList = Optional.ofNullable(tagDTOList).orElseGet(() -> Collections.emptyList()).stream().filter(tagDTO -> Objects.isNull(tagDTO.getPromoType())).collect(Collectors.toList());
                allTags.put(TagTypeEnum.PRODUCT_TAG.getName(), productTagList);

                //添加数据标签
                Optional.ofNullable(dataTagMaps.get(productInfoVo.getId())).ifPresent(dataTags -> {
                    allTags.putAll(dataTags);
                });

                //重新赋值所有标签数据
                productInfoVo.setTags(allTags);
            });
        }
    }

    /**
     * 判断是否包含店铺数据标签数据
     *
     * @param searchSource
     * @return
     */
    private boolean hasShopDataTags(Integer searchSource) {
        boolean hasShopDataTags = true;
        //判断是否包含店铺数据标签
        if (Objects.nonNull(searchSource) && SearchSourceEnum.SHOP_SEARCH.getValue().equals(searchSource)) {
            hasShopDataTags = false;
        }
        return hasShopDataTags;
    }

}
