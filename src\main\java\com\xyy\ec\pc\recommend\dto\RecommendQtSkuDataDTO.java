package com.xyy.ec.pc.recommend.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class RecommendQtSkuDataDTO {

    /**
     * 排名
     */
    private Integer rank;

    /**
     * 运营位ID
     */
    private String operation_id;

    /**
     * 运营位排名
     */
    private Integer operation_rank;

    /**
     * 二级排名
     */
    private Integer sub_rank;

    /**
     * 搜索坑位类型
     */
    private String list_position_type;

    /**
     * 搜索坑位类型名称
     */
    private String list_position_type_name;

    /**
     * 商品标签
     */
    private List<String> product_labels;

    /**
     * 商品类型
     */
    private Integer product_type;

    /**
     * 商品高毛
     */
    private Integer product_high_gross;

    /**
     * 商品一级分类ID
     */
    private Long product_category_first_id;

    /**
     * 商品一级分类名称
     */
    private String product_category_first_name;

    /**
     * 商品店铺编码
     */
    private String shop_code;

    /**
     * 商品店铺名称
     */
    private String shop_name;

    /**
     * 主商品id。目前仅组合购/加价购商品卡片有效，且主品时此字段为当前品id。
     */
    private Long main_product_id;

    /**
     * 主商品名称。目前仅组合购/加价购商品卡片有效，且主品时此字段为当前品商品名称。
     */
    private String main_product_name;


    /**
     * spu召回策略名称
     */
    private String spu_recall_strategy_name;
}
