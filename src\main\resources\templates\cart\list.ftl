<#assign ctx="">
<style>

    .baoyou{
        position:absolute;
    }
    .listmode{margin-top:15px;}
    .yhq-box .cg-yhq-new {
        position: absolute;
        display: none;
        right:0;
        top:42px;
        width:856px;
        background: #FFFFFF;
        /*min-height:208px;*/
        max-height:450px;
        overflow-y: auto;
        border-radius: 5px;
        z-index: 1008;
        box-shadow:0px 2px 12px 0px rgba(0,0,0,0.06);
        border:1px solid rgba(238,238,238,1);
    }
    .platform-yhq-box .cg-yhq-new {
        position: absolute;
        display: none;
        right: 0px;
        top:42px;
        width:856px;
        background: #FFFFFF;
        /*min-height:208px;*/
        max-height:450px;
        overflow-y: auto;
        border-radius: 5px;
        z-index: 1008;
        box-shadow:0px 2px 12px 0px rgba(0,0,0,0.06);
        border:1px solid rgba(238,238,238,1);
    }
    .cg-yhq-new::-webkit-scrollbar{
        width:3px;
    }

    .cg-yhq-new::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(0, 0, 0, 0.1);
        /*-webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);*/
    }
    .cg-yhq-new .address-top-title,.cg-yhq-new .address-top-title1 {
        margin-top: 5px;
        margin-left: 19px;
        font-size:11px;
        color:#676773;
        line-height:20px;
    }
    .cg-yhq-new .address-top-title{
        margin-top: 10px;
    }
    .wei-get,.yi-get{
        font-size: 12px;
        color:#4A4A4A;
        margin-right: 8px;
    }
    .cg-yhq-new ul{
        padding-left:10px;
    }
    .yhq-san {
        position: absolute;
        top: -10px;
        left: 50%;
        line-height: 0px;
        font-size: 40px;
        z-index: -1;
        width: 0;
        margin-left: -7px;
        height: 0;
        border-left: 20px solid transparent;
        border-right: 20px solid transparent;
        border-bottom: 17px solid #fff;
    }
    .yhq-main{
        width: 100%;
        /*max-height: 100%;*/
        overflow:hidden;
    }
    .yhq-common li{
        display: inline-block;
        float: none;
    }
    .yhq-sp-box{
        max-width: 370px;
        overflow-x: auto;
        white-space: nowrap;
        display: -webkit-box;
        margin-top: 10px;
        -webkit-overflow-scrolling:touch;
        height: 128px;
    }
    .yhq-sp-box::-webkit-scrollbar {/*滚动条整体样式*/
        width: 10px;     /*高宽分别对应横竖滚动条的尺寸*/
        height: 3px;
    }
    .yhq-sp-box::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
        border-radius: 10px;
        -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
        background: #aaaaaa;
    }
    .yhq-sp-box::-webkit-scrollbar-track {/*滚动条里面轨道*/
        -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
        background: #EDEDED;
        border-radius: 10px;
    }
    .yhq-sp-box .yhq-sp-item{
        margin-right: 10px;
        width:80px;
        display: inline-block;
    }
    .yhq-sp-box .yhq-sp-item p{
        color:#676773;
        font-size: 12px;
        text-align: center;
        line-height:20px;
    }
    .yhq-sp-box .yhq-sp-item .img-box{
        margin-bottom: 3px;
        width: 80px;
        height: 80px;
        border: 1px solid #f7f7f8;
        position:relative;
    }
    .yhq-sp-box .yhq-sp-item .red{
        color:#FF2121;
        font-size: 14px;
    }
    .yhq-sp-box .yhq-sp-item img{
        width: 80px;
        height: 80px;
        vertical-align: middle;
    }
    .yhq-sp-box  .yhq-sp-check{
        position: absolute;
        top:0px;
        left:0px;
        width:16px;
        height:16px;
        border-radius: 8px;
        color:#b2b2b2;
    }
    .applybox .acol5 .youhui{
        line-height: 18px;
    }
    .yhq-common li .yhq-rb .yhq-rb-foot a.notling{
        background: #dddddd;
        cursor: default;
    }
    .yhq-common li .yhq-rb .yhq-rb-foot a.lingqu{
        background: linear-gradient(90deg,rgba(255,96,37,1) 0%,rgba(255,66,68,1) 100%);
        cursor: pointer;
    }
    .yhq-tishi{
        height:18px;
    }
    /*超重样式*/
    .chaozhong{
        height:40px;
        background:#FFF7EF;
        width: 1198px;
        margin: 0 auto;
        line-height: 40px;
        padding-right:17px;
        box-sizing: border-box;
    }
    .chaozhong span{
        color:#99664D;
    }
    span.pr-num{
       color:#333333;
    }
    .chaozhong .dialog-box{
        position: absolute;
        bottom:40px;
        right:15px;
        width: 752px;
        /*height: 592px;*/
        background: #ffffff;
        border: 1px solid #eeeeee;
        box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.06);
    }
    .chaozhong .dialog-box .header-box{
        height:45px;
        line-height:45px;
    }
    .chaozhong .dialog-box .header-box h4{
        text-align: left;
        line-height:45px;
        font-weight: 500;
        color: #333333;
        font-size: 14px;
        padding-left: 15px;
    }
    .chaozhong .dialog-box .content-box{
        padding:0 15px 15px;
        max-height:450px;
        overflow-y: auto;
    }
    .chaozhong .dialog-box .content-box::-webkit-scrollbar{
        width:3px;
    }

    .chaozhong .dialog-box .content-box::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(0, 0, 0, 0.1);
    }
    .chaozhong .dialog-box .content-box ul{
        overflow: hidden;
        border: 1px solid #eeeeee;
        padding:10px 15px;
        border-top:none;
    }
    .chaozhong .dialog-box .content-box li {
        float: left;

    }
    .chaozhong .dialog-box .content-box  .lib1 {
        width: 348px;
        overflow: hidden;
        margin-right: 40px;
    }
    .chaozhong .dialog-box .content-box  .lib3 {
        width: 105px;
    }
    .chaozhong .dialog-box .content-box  .lib4 {
        width: 95px;
    }
    .chaozhong .dialog-box .content-box  .lib6 {
        width: 92px;
        text-align: right;
    }
    .chaozhong .dialog-box .content-box  .lib3 .zkj {
        color: #333333;
        margin-top: 0px;
        font-size: 16px;
    }
    .chaozhong .dialog-box .content-box  .lib4 .zkj {
        color: #333333;
        margin-top: 0px;
        font-size: 16px;
    }
    .chaozhong .dialog-box .content-box  .lib6 .zkj {
        color: #ff5b5b;
    }
    .chaozhong .dialog-box .content-box .lib1 .l-box {
        width: 96px;
        height: 96px;
        border: 1px solid #eeeeee;
        overflow: hidden;
        margin-top: 0px;
        margin-right: 10px;
    }
    .chaozhong .dialog-box .content-box .lib1 .l-box img{
        width: 96px;
        height: 96px;
        vertical-align: middle;
    }
    .chaozhong .dialog-box .content-box .lib1 .r-box span {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #333333;
    }
    .chaozhong .dialog-box .content-box .lib1 .r-box {
        width: 240px;
        padding-top: 0px;
    }
    .chaozhong .dialog-box .content-box .lib1 .r-box span.info {
        color: #666666;
        font-size: 12px;
    }
    .chaozhong .dialog-box .content-box .content-head{
        height: 50px;
        background: #f6f6f6;
        border: 1px solid #eeeeee;
        border-radius: 4px 4px 0px 0px;
        line-height: 50px;
        padding:0 15px;
    }
    .chaozhong .dialog-box .content-box .content-head span{
        color: #333333;
        display: inline-block;
    }
    .chaozhong .dialog-box .content-box .content-head .cell-1{
        width: 348px;
        margin-right: 40px;
    }
    .chaozhong .dialog-box .content-box .content-head .cell-2{
        width: 95px;
    }
    .chaozhong .dialog-box .content-box .content-head .cell-3{
        width: 95px;
    }
    .chaozhong .dialog-box .content-box .content-head .cell-4{
        width: 93px;
        text-align: right;
    }
    div.youhuidetail{
        display: inline-block;
        color: #333333;
        padding-left: 4px;
        position: relative;
        width: auto;
        background: transparent;
        bottom: 0;
        padding-right: 0;
    }
    div.youhuidetail span{
        color: #333333;
    }
    div.youhuidetail .dialog-box{
        bottom: 76px;
        right: 0;
        width: 440px;
        background: #ffffff;
        border: 1px solid #eeeeee;
        box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.06);
    }
    div.youhuidetail .header-box{
        border-bottom:1px solid #eeeeee;
    }
    div.youhuidetail .dialog-box .modal-title{
        font-size: 16px;
        font-weight: bold;
        color: #333333;
    }
    div.youhuidetail .dialog-box .pr-num{
        font-size: 12px;
        color: #9494a6;
    }
    /*商品选择框样式*/
    .yhq-sp-check{
        background: url("/static/images/no-select.png") 0 0 no-repeat;
        background-size: 16px 16px;
    }
    .yhq-sp-check.checked{
        background: url("/static/images/yi-select.png") 0 0 no-repeat;
        background-size: 16px 16px;
    }
    .yhq-sp-check input[type=checkbox]{
        visibility: hidden;
    }
    /*弹窗样式*/
    .sui-modal .modal-body{text-align: left;line-height: 23px;padding: 20px;}
    .yugu{
        padding: 0 8px;
        vertical-align: top;
        display: inline-block;
        position: relative;
        background: #FF2121;
        border-radius: 2px;
        color: #ffffff;
        font-size: 12px;
        margin-left: 8px;
    }
    .yugu .yusan{
        position: absolute;
        top: 0;
        left: -4px;
        width: 0;
        height: 0;
        border: 4px solid transparent;
        border-top-color: #FF2121;
    }
    .youhuidetail .line-list{
        overflow: hidden;
        width: 409px;
        margin: 0 auto;
        border-bottom: 1px dashed #eeeeee;
    }
    div.youhuidetail .noneBottom{
        border-bottom: none;
    }
    .youhuidetail .line-list p{
        overflow: hidden;
        line-height: normal;
    }
    .youhuidetail .line-list-d{
        padding: 10px 0;
    }
    .youhuidetail .line-list-d span{
        font-size: 14px;
        color: #30303c;
        margin-left: 0;
    }
    .youhuidetail .line-list-m{
        padding-bottom: 10px;
    }
    .youhuidetail .line-list-m span{
        font-size: 12px;
        color: #9494a6;
        margin-left: 0;
    }
    .youhuidetail .line-list .span-left{
        float: left;
    }
    .youhuidetail .line-list .span-right{
        float: right;
    }
    .youhuidetail .footer-box{
        border-top: 1px solid #eeeeee;
    }
    .youhuidetail .line-list-d span.color-red{
        color: ##FF2121;
    }
    .youhuidetail .footer-box .line-list-d{
        line-height: 22px;
    }
    .youhuidetail .footer-box .line-list-d span{
        font-size: 16px;
        font-weight: bold;
        color: #30303c;
    }

    .crossStoreCoupon{
        font-size: 14px;
    }

    .crossStoreCoupon.orderfix{
        height: 44px;
        bottom: 76px;
    }

    .crossStoreCoupon.applybox{
        line-height: 44px;
        background: #FFF4CE;
    }

    .crossStoreVoucherTips span{
        color: #FF2021
    }
    .lib5 .suliang .disabledSub, .lib5 .suliang .disabledAdd {
        display: inline-block;
        width: 30px;
        height: 32px;
        line-height: 32px;
        border-left: 1px solid #e0e0e0;
        text-align: center;
        color: #999;
        font-size: 18px;
    }
    .lib5 .suliang .disabledSub {
        float: left;
        border-left: none;
        border-right: 1px solid #e0e0e0;
    }
    #fullGiveModal .i-dialog-mask {
        position: fixed;
        width: 100%;
        height: 100%;
        top:0;
        background-color: #0000007a;
    }
    #fullGiveModal div {
        box-sizing: border-box;
        font-size: 14px;
    }
    #fullGiveModal .i-dialog-box {
        position: fixed;
        width: 600px;
        min-height: 450px;
        background-color: white;
        left: 50%;
        top: 50%;
        transform:translate(-50%, -50%);
        border-radius: 5px;
        overflow: hidden;
        display: flex;
        flex-direction: column
    }
    .i-dialog-box p {
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 最多显示两行 */
        -webkit-box-orient: vertical; /* 垂直布局 */
        overflow: hidden; /* 隐藏多余内容 */
        text-overflow: ellipsis; /* 用省略号表示多余内容 */
    }
    .i-dialog-box .i-title,
    .i-dialog-box .i-footer {
        flex-shrink: 0;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px;
    }
    .i-dialog-box .i-footer .i-submit,
    .i-dialog-box .i-footer .i-close {
        padding: 5px 25px;
        border-radius: 3px;
        cursor: pointer;
    }
    .i-dialog-box .i-footer .i-close {
        border: solid 1px #dfdfdf;
    }
    .i-dialog-box .i-footer .i-submit {
        background-color: #31cf60;
        border: solid 1px #26bb53;
        color: white;
    }
    .i-dialog-box .i-content {
        flex-grow: 1;
        flex-shrink: 0;
        width: 100%;
        border-top: solid 1px #dfdfdf;
        padding: 0 5px;
    }
    @supports not (gap: 10px) {
        .i-info {
            margin: 0 10px;
        }
        .i-close {
            margin-right: 10px;
        }
    }
    @supports (appearance: none) {
        input[type="checkbox"] {
            position: relative;
            width: 14px;
            height: 14px;
            border-radius: 3px;
            margin: 0;
            border: solid 1px #d2d2d2;
            cursor: pointer;
            outline: none;
            appearance: none;
        }
        input[type="checkbox"]:checked {
            background-color: #31cf60;
            border: solid 1px #26bb53;
        }
        input[type="checkbox"]:checked::after {
            content: '\2714';
            font-size: 12px;
            color: white;
            position: absolute;
            transform: translate(1px,-2px);
        }
    }
    .i-actList {
        width: 100%;
        overflow-x: auto;
    }
    .i-actList > div {
        position: relative;
        flex-shrink: 0;
        width: 100px;
        cursor: pointer;
        margin: 0 10px;
        text-align: center;
    }
    .i-actStatus {
        position: absolute;
        font-size: 10px !important;
        color: white;
        padding: 0 3px;
        border-radius: 3px;
        display: inline-block;
        top: -10px;
        right: -10px;
        width: max-content;
        background: #a56464;
    }
    .i-actList-active {
        color:#26bb53;
    }
    #productList {
        overflow-y: auto;
        max-height: 500px;
        padding: 10px;
        transition: all 0.3s;
        -webkit-transition: all 0.3s;
        /* scrollbar-width: thin;
        scrollbar-color: #ccc #f1f1f1; */
    }
    #productList .i-item {
        width: 100%;
        display: flex;
        align-items: center;
        padding: 15px 5px;
        cursor: pointer;
        transition: all 0.2s;
        border-bottom: solid 1px #e4e4e4;
    }
    #productList .i-item:hover {
        border-bottom-color: #0da600;
    }
    #productList .i-item > div {
        flex-grow: 0;
        flex-shrink: 0;
    }
    #productList .i-item > div:nth-child(2) {
        flex-grow: 1;
    }
    #productList .i-item .i-info {
        display: flex;
        gap: 10px;
        margin: 0 10px;
    }
    #productList .i-item .i-info .i-img {
        position: relative;
        width: 70px;
        height: 70px;
        object-fit: cover;
        overflow: hidden;
        border-radius: 5px;
        flex-shrink: 0;
    }
    #productList .i-item .i-numBtn {
        position: relative;
        height: 30px;
        display: flex;
        border: solid 1px #d2d2d2;
        border-radius: 3px 3px 0 0;
        align-items: center;
        color: #8b8b8b;
    }
    #productList .i-item .i-numBtn + div {
        background-color: #f0f0f0;
        padding: 3px;
        color: #686868;
        font-size: 12px;
        border-radius: 0 0 3px 3px;
    }
    #productList .i-item .i-numBtn input {
        width: 70px;
        text-align: center;
        border: none;
        font-size: 16px;
        color: #8b8b8b;
        background-color: transparent;
        outline: none;
    }
    #productList .i-item .i-numBtn > div {
        width: 30px;
        height: 100%;
        text-align: center;
    }
    #productList .i-item .i-numBtn > div:first-child {
        border-right: solid 1px #d2d2d2;
    }
    #productList .i-item .i-numBtn > div:last-child{
        border-left: solid 1px #d2d2d2;
    }
    .i-dialog-box ::-webkit-scrollbar-button {
        display: none !important;
    }
    .i-dialog-box ::-webkit-scrollbar {
        width: 5px;
        height: 5px;
        cursor: pointer;
    }
    .i-dialog-box ::-webkit-scrollbar-thumb {
        background-color: #d7d7d7;
        border-radius: 8px;
        cursor: pointer;
    }
    .i-disabled {
        background: #ededed;
        cursor: not-allowed;
    }
    .lib1 .l-box{
        width: 110px;
        min-height: 110px;
        border: 1px solid #e0e0e0;
        overflow: hidden;
        margin-top: 20px;
        margin-right: 30px;
        overflow: visible;
    }
    .lib1 .l-box img{
        width: 110px;
        vertical-align: middle;
    }
    .add_a {
        position: relative;
        border: 2px solid red;
        display: block;
    }
    .add_a .timer{
        background-color: #FF2121;
        font-size: 12px;
        color: #fff;
        text-align: center;
    }
    .add_a .timer .timeP {
        font-size: 12px;
        color: #fff;
    }
    .add_a .add_box {
        position: absolute;
        top: -2px;
        text-align: center;
    }
    .add_a .add_box img {
        width: 110px;
        height: 20px;
        vertical-align: middle;
    }
</style>
<span style="display: none" id="cartVarietyNum">${cartInfo.varietyNum}</span>
<input type="hidden" value="${cartInfo.hasPlatFormVouchers}">
<#if cartInfo.hasPlatFormVouchers == 1 >
    <div style="position: relative;width: 1198px;margin: 0 auto;display: block;text-align: right;">
        <div class="platform-yhq-box" style="position: relative;font-size:14px;color:#333;text-align: left;">平台优惠券：
            <a href="javascript:void(0);" class="spe yhq-button-new yhq-button-platform" shopCode="DP0005">
                <#--优惠券-->
                <#--<i class="sui-icon icon-tb-unfold core"></i>-->
            </a>
            <div class="cg-yhq-new">
                <div class="yhq-san"></div>
                <div class="yhq-main">
                    <!--已领取-->
                    <div class="address-top-title1"></div>
                    <ul class="yhq-common"  id="cartVoucherInfo-new-yi-platform"></ul>
                    <!--未领取-->
                    <div class="address-top-title"></div>
                    <ul class="yhq-common"  id="cartVoucherInfo-new-platform"></ul>
                </div>
            </div>
        </div>
    </div>
</#if>
<!--列表模式-->
<div class="listmode" style="margin-top:20px;">
    <!--表头-->
    <div class="headbox">
        <ul>
            <li class="li1">
                <#--<label class="inline all">
                </label>-->
                <label class="checkbox-pretty inline all">
                    <input type="checkbox"><span>全选</span>
                </label>
                商品信息
            </li>
            <li class="li3">单价</li>
            <li class="li5">数量</li>
            <li class="li4">金额</li>
            <li class="li6">操作</li>
        </ul>
    </div>
    <!--列表-->
    <div class="list-content">
        <!-- 隐藏的数据容器 -->
        <div id="shopInfoSxpListContainer" style="display: none;">
            <#if cartInfo.shopInfoSxpList?? && cartInfo.shopInfoSxpList?size gt 0>
                <#list cartInfo.shopInfoSxpList as shop>
                    <div class="shop-data" 
                         data-shop-code="${shop.shopCode?html}">
                        <#list shop.skuids as skuId>
                            <span class="sku-id" data-sku-id="${skuId?html}"></span>
                        </#list>
                    </div>
                </#list>
            </#if>
        </div>

        <script>
            // 从隐藏的HTML元素中构建shopInfoSxpList对象
            window.shopInfoSxpList = [];
            
            try {
                // 获取所有shop-data元素
                var shopElements = document.querySelectorAll('#shopInfoSxpListContainer .shop-data');
                
                // 遍历每个shop元素
                shopElements.forEach(function(shopElement) {
                    var shopCode = shopElement.getAttribute('data-shop-code');
                    var skuIds = [];
                    
                    // 获取该shop下的所有sku-id元素
                    var skuElements = shopElement.querySelectorAll('.sku-id');
                    
                    // 遍历每个sku元素
                    skuElements.forEach(function(skuElement) {
                        var skuId = skuElement.getAttribute('data-sku-id');
                        if (skuId) {
                            skuIds.push(skuId);
                        }
                    });
                    
                    // 将shop对象添加到shopInfoSxpList
                    if (shopCode) {
                        window.shopInfoSxpList.push({
                            shopCode: shopCode,
                            skuids: skuIds
                        });
                    }
                });
                
                // 将shopInfoSxpList存储到sessionStorage
                if (window.shopInfoSxpList && window.shopInfoSxpList.length > 0) {
                    var jsonString = JSON.stringify(window.shopInfoSxpList);
                    // 检查JSON字符串是否有效
                    JSON.parse(jsonString); // 如果无效会抛出异常
                    sessionStorage.setItem("shopInfoSxpList", jsonString);
                    console.log("shopInfoSxpList saved successfully:", jsonString);
                }
            } catch (error) {
                console.error("Error processing shopInfoSxpList:", error);
                console.log("Data that caused the error:", window.shopInfoSxpList);
            }
        </script>
        <#list cartInfo.company as company >
            <div class="list-ziying">
                <div class="cgd-qy">
                    <label class="checkbox-pretty inline zyclick <#if company.selectStatus=1>checked</#if>" company="${company.isThirdCompany}">
                        <input type="checkbox" name="main" value="${company.orgId}"  <#if company.selectStatus=1>checked="checked"</#if>><span></span>
                    </label>
                     <!--自营 -->
                     <#if (company.isThirdCompany == 0)>
                         <span class="ziying">自营</span>
                     <#else>
                         <img src="/static/images/pop-shop-icon.png" alt="" style="margin-right: 3px;top: -1px;position: relative;width: 18px;">
                    </#if>
                    <#if company.shopJumpUrl?? && company.shopJumpUrl != ''>
                     <#if company.shop?exists && company.shop?size gt 0>
                             <span class="qy-title"><a class="qy-title" href="${company.shopJumpUrl}" onclick="action_sub_module_click_v2('${company.shop[0].originalShopName}','${company.shop[0].originalShopCode}',${company_index},2)" target="_blank">${company.companyName}</a></span>
                        <#else>
                            <span class="qy-title"><a class="qy-title" href="${company.shopJumpUrl}" target="_blank">${company.companyName}</a></span>
                     </#if>
                       
                    <#else>
                        <#if company.shop?exists && company.shop?size gt 0>
                            <span class="qy-title" onclick="action_sub_module_click_v2('${company.shop[0].originalShopName}','${company.shop[0].originalShopCode}',${company_index},2)">${company.companyName}</span>
                        <#else>
                            <span class="qy-title">${company.companyName}</span>
                        </#if>
                    </#if>
                    <!--包邮-->
                    <input type="hidden" id="freightShopCode" value="${company.mainShopCode}">
                    <span class="spec" style="color:#333333">
                        <#if company.freightIconShowStatus?? && company.freightIconShowStatus == 1>
                            <a href="javascript:void(0)" style="padding: 0 3px 0 5px;" class="fplx-a get-fei"  data-keyboard="false"><img src="/static/images/yunfei-tip.png" alt="" style="top: -2px;position: relative;width: 14px;"></a>
                        </#if>
                        <#if company.freightTipsShowStatus?? && company.freightTipsShowStatus == 1>
                            ${company.freightTips}
                            <#if company.freightUrlText?? && company.freightUrlText != ''>
                                <a onclick="action_sub_module_click_v2('${company.freightUrlText}','${company.shop[0].originalShopCode}',${company_index},4)" href="${company.freightJumpUrl}" style="color: #00B377;">${company.freightUrlText}<img src="/static/images/right-arrow.png" alt="" style="margin-right: 3px;top: -1px;position: relative;width: 12px;"></a>
                            </#if>
                        </#if>
                    </span>

                    <div class="qy-price fr" style="line-height: normal;height:40px;display: table;">
                        <div style="display: table-cell;vertical-align: middle;">

                        <!-- 返利提示 去凑单 -->
                        <#if cartInfo.activity?? && cartInfo.activity.text??>
                            <span onclick="action_sub_module_click_v2('${cartInfo.activity.text}','${company.shop[0].originalShopCode}',${company_index},4)" target="_blank" style="margin-right: 20px;height: 20px;line-height: 20px;display:inline-block;" onmouseover="this.style.color='#f39800';" onmouseleave="this.style.color='rgb(102, 102, 102)';">   <a href="/search/skuInfoByCategory.htm?all=all" target="_blank" style="color: rgb(102, 102, 102);" onmouseover="this.style.color='#f39800';" onmouseleave="this.style.color='rgb(102, 102, 102)';">${cartInfo.activity.text}</a></span>
                        </#if>
                        </div>
                    </div>
                     <#if (company.shop[0].isHaveVoucher ==1)>
                            <div class="yhq-box" style="position: relative;float:right">
                                <a href="javascript:void(0);" class="spe yhq-button-new" shopCode="${company.shop[0].shopCode}">
                                    优惠券
                                    <i class="sui-icon icon-tb-unfold core"></i>
                                </a>
                                <div class="cg-yhq-new">
                                    <div class="yhq-san"></div>
                                    <div class="yhq-main">
                                        <!--已领取-->
                                        <div class="address-top-title1"></div>
                                        <ul class="yhq-common"  id="cartVoucherInfo-new-yi"></ul>
                                        <!--未领取-->
                                        <div class="address-top-title"></div>
                                        <ul class="yhq-common"  id="cartVoucherInfo-new"></ul>
                                    </div>
                                </div>
                            </div>
                        </#if>
                </div>


                <!--循环店铺-->
                <#list company.shop as shop>
                    <#if shop.marketingTipsList?has_content>
                        <div class="cgd-qy cgd-qy1" style="background:#ffffff;border-bottom:1px solid #EEEEEE;display:flex">
                            <#list shop.marketingTipsList as marketingTipsList>
                                <div style="padding-left:15px;color:FF2121">
                                    <span style="color:#FF2121;border:1px solid #FF2121;border-radius:2px;padding:0px 2px;margin-right:10px;">店铺优惠</span>
                                    ${marketingTipsList.tips}
                                    <a href="${marketingTipsList.pcUrl}" style="display:inline-block;color:#FF2121;margin-left:5px">去凑单></a>
                                </div>
                            </#list>
                            <#--  <label class="checkbox-pretty inline zyclick <#if shop.selectStatus=1>checked</#if>" company="${company.isThirdCompany}"  >
                                <input type="checkbox" name="main" value="${shop.shopCode}"    <#if shop.selectStatus=1>checked="checked"</#if>><span></span>
                            </label>
                            <#if shop.pcLinkUrl ?? && shop.pcLinkUrl != ''>
                                <a href="${shop.pcLinkUrl}"><span class="qy-title">${shop.shopName}</span></a>
                            <#else>
                                <span class="qy-title">${shop.shopName}</span>
                            </#if>  -->
                        </div>
                    </#if>

                    <!--平台活动-->
                     <#if shop.returnVoucherInfo?? && shop.returnVoucherInfo.text?? && shop.shopType == 'ybm'>
                    <div class="qy-price" style="display:block;background:#FFF3CE;padding-left: 48px;line-height: normal;height:40px;">
                        <div style="line-height:40px;color:#FF8E29;">
                            <span style="margin-right: 20px;height: 20px;line-height: 20px;display: inline-block;">
                            <#if shop.returnVoucherInfo.productRelationType == 0>
                                 <a href="/search/skuInfoByCategory.htm?all=all" target="_blank" style="color: #FF8E29;" onmouseover="this.style.color='#FF8E29';" onmouseleave="this.style.color='#FF8E29';">${shop.returnVoucherInfo.text}</a>
                            <#else >
                                 <a href="/voucher/centre/findVoucherSku.htm?voucherTemplateId=${shop.returnVoucherInfo.couponId}&activityType=1&shopCode=${shop.returnVoucherInfo.shopCode}" target="_blank" style="color: #FF8E29;" onmouseover="this.style.color='#f39800';" onmouseleave="this.style.color='#FF8E29';">${shop.returnVoucherInfo.text}</a>
                            </#if>
                            </span><br>

                        </div>
                    </div>
                   </#if>
                    <div class="pro-box">
                        <#list shop.shoppingGroupFrontDtos as group >
                            <input type="hidden" value="${group.canGoToGiftPool}">
                            <!--满减标题-->
                            <#if (group.type != 10 && group.type != 9)>
                            <div <#if (group.valid !=0)>class="manjianbox"</#if> >
                                <#if group.title??>
                                    <span class="title">
                                    <#if (group.valid !=0)>
                                        <#if group.type==1>满减<#elseif group.type==2>满折<#elseif group.type==3>满赠<#elseif group.type==4>满减赠<#elseif group.type==6>一口价<#elseif group.type==11>满返<#elseif group.type==12>满减返券
                                        <#elseif group.type==21>返券</#if>
                                    </#if>
                                    </span>
                                    <#if (group.valid !=0)>
                                    <span class="info">${group.title}</span>
                                    </#if>
                                    <#if group.canGoToGiftPool == true>
                                        <a id='fullGiveModalBtn' style="cursor:pointer;color:#00B377;" onclick="fullNumSelectGive(${cartInfo.bizSource},${group.giftPoolActHasSelectedNum},${group.giftPoolActTotalSelectedNum},${group.promoId})" style="color: #15BA81;margin: 0 5px;">
                                            <#if group.isGiveUpGift == false>
                                                选择赠品(${group.giftPoolActHasSelectedNum}/${group.giftPoolActTotalSelectedNum})<img src="/static/images/right-arrow.png" alt="" style="margin-right: 3px;top: -1px;position: relative;width: 12px;">
                                                <#if (group.giftPoolActCanSelectedNum>0) >
                                                    <input type="hidden" class="giftPoolActCanSelectedNum"  onclick="fullNumSelectGive(${cartInfo.bizSource},${group.giftPoolActHasSelectedNum},${group.giftPoolActTotalSelectedNum},${group.promoId})"/>
                                                </#if>
                                            <#else>
                                                已放弃赠品
                                            </#if>
                                            <input type="hidden" class="giftPoolActTitle"  onclick="fullNumSelectGive(${cartInfo.bizSource},${group.giftPoolActHasSelectedNum},${group.giftPoolActTotalSelectedNum},${group.promoId}, true)"/>
                                        </a>
                                    </#if>
                                    <#if group.titlePCUrl ?? && group.titlePCUrl != ''>
                                    <a href="${group.titlePCUrl}" style="color:red;" class="order-qcd">${group.titleUrlText}<span style="color:red;" class="order-qcd-fuhao">></span></a>
                                    </#if>
                                </#if>
                            </div>
                            <#elseif (group.type = 9) && (group_index > 0)>
                            <div class="taocanbox-onther">
                                <#--<span class="new">其它商品：</span>-->
                            </div>
                            </#if>
                            <#list group.sorted as sort>
                            <input type="hidden" value="${sort.item.skuId}">
                            <#if sort.itemType=3>
                                <!--套餐-->
                                <#if sort.item.valid==1>
                                <div class="taocanbox">
                                        <span class="title">套餐</span>
                                        <span class="info">套餐优惠单价：</span><span class="price">￥${sort.item.price}</span>
                                        <span class="info">套餐搭配商品小计：</span><span class="price">￥${sort.item.subtotal}</span>
                                        <span class="info">已优惠：</span><span class="price">￥${sort.item.discount}</span>
                                    </div>
                                </#if>
                                <!--套餐表体-->
                                <#--<div class="${(group.type != 10 && group.type != 9)?string("has-active" , "")}">-->
                                <div class="bodybox taocanspe <#if sort_index = (group.sorted?size -1)>has-active</#if> <#if sort.item.status=1 && (group.valid !=0)>cur</#if> <#if (group.valid =0)>shixiaospe</#if>">
                        <!--列表-->
                        <#assign status_index=0 />
                        <#list sort.subItemList as tcList>
                            <ul>
                                <li class="lib1 ${(group.type != 10 && group.type != 9)?string("active-link" , "")}">
                                    <div class="checkb fl"></div>
                                    <div class="l-box fl ">
                                        <a target="_blank" href="${ctx}/search/skuDetail/${tcList.sku.id}.htm" title="${tcList.sku.commonName }"><img src="${productImageUrl }/ybm/product/min/${tcList.imageUrl}"  alt="">
                                        </a>
                                        <!--标签-->
                                        <#if sort.item.valid=0>
                                            <div class="bq-box t1 ${tcList.skuStatus}">
                                                <#if tcList.skuStatus==2>
                                                    <img src="/static/images/product/bq-shouqing.png" alt="">
                                                </#if>
                                                <#if tcList.skuStatus==4>
                                                    <img src="/static/images/product/bq-xiajia.png" alt="">
                                                </#if>
                                                <#if tcList.skuStatus==95>
                                                    <img src="/static/images/product/bq-chaofanwei.png" alt="">
                                                </#if>
                                                <#if tcList.skuStatus==108>
                                                    <img src="/static/images/product/bq-chaofanwei.png" alt="">
                                                </#if>
                                                <#if tcList.skuStatus==105>
                                                    <img src="/static/images/product/bq-xieyishangpin.png" alt="">
                                                </#if>
                                                <#if tcList.skuStatus==106>
                                                    <img src="/static/images/product/bp-xieyishangpin2.png" alt="">
                                                </#if>
                                            </div>
                                        </#if>
                                    </div>
                                    <div class="r-box fl">
                                        <div class="lib1-row1 text-overflow">
                                            <#if tcList.tagTitle ?? && (tcList.tagTitle != "" && tcList.tagTitle != null)>
                                                <span class="tag-title">${tcList.tagTitle}</span>
                                            </#if>
                                            <span>
                                                <a target="_blank" href="${ctx}/search/skuDetail/${tcList.sku.id}.htm" title="${tcList.sku.commonName }">
                                                        ${tcList.sku.commonName}
                                                </a>
                                            </span>
                                        </div>
                                        <div class="lib1-row3">
                                            <div class="row-biaoqian">
                                                <#if sort.item.valid==1>
                                                    <#if tcList.tagList ?? && (tcList.tagList?size >0) >
                                                        <#list tcList.tagList as item >
                                                            <#if (item_index < 3)>
                                                                <span class="<#if item.uiType == 1>linqi</#if>
                                                                    <#if item.uiType == 2>quan</#if>
                                                                    <#if item.uiType == 3>manjian</#if>
                                                                    <#if item.uiType == 4>default</#if>
                                                                    <#if item.uiType == 5>yibao</#if>
                                                                ">
                                                                ${item.name}
                                                                </span>
                                                            </#if>
                                                        </#list>
                                                    </#if>
                                                </#if>
                                            </div>
                                        </div>
                                        <div class="lib1-row2 text-overflow">
                                            <span class="title">规 格：</span> <span class="info">${tcList.sku.spec}</span>
                                        </div>
                                        <#--<div class="lib1-row3">
                                            <span class="title">批准文号：</span> <span class="info">${tcList.balancePercentDesc}</span>
                                        </div>-->
                                        <div class="lib1-row4 text-overflow">
                                            <span class="title">生产厂家：</span> <span class="info">${tcList.sku.manufacturer}</span>
                                        </div>
                                        <div class="lib1-row4 text-overflow baoyou">
                                            <span class="tag-bao">${tcList.freightTips}</span>
                                        </div>
                                        <div class="lib1-row2  text-overflow">
                                            <span class="title">效 期：</span>
                                            <#if tcList.sku.validity?? && tcList.sku.validity != ''>
                                                <span class="info">${tcList.sku.validity}</span>
                                            <#elseif ((tcList.sku.nearEffect?? && tcList.sku.nearEffect != '') && (tcList.sku.farEffect?? && tcList.sku.farEffect != ''))>
                                                <#if tcList.sku.nearEffect == tcList.sku.farEffect>
                                                    <span class="info">${tcList.sku.farEffect}</span>
                                                <#else>
                                                    <span class="info">${tcList.sku.nearEffect+"/"+tcList.sku.farEffect}</span>
                                                </#if>
                                            </#if>
                                        </div>
                                        <div class="lib1-row3">
                                            <div class="row-biaoqian">
                                                <#if sort.item.valid==1>
                                                    <#if tcList.tagWholeOrderList ?? && (tcList.tagWholeOrderList?size >0) >
                                                        <#list tcList.tagWholeOrderList as item >
                                                            <div class="synthesis">
                                                                <div class="synthesis-biao">
                                                                    <#if item.description ?? && item.description != ''>
                                                                        <span class="synthesis-biao-left">${item.name}</span>
                                                                        <span class="synthesis-biao-shuoming">${item.description}</span>
                                                                    <#else>
                                                                        <span class="synthesis-biao-left-single">${item.name}</span>
                                                                    </#if>
                                                                </div>
                                                            </div>
                                                        </#list>
                                                    </#if>
                                                </#if>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                <li class="lib3">

                                    <div class="zkj">
                                        <p style="display: inline-block">
                                        <span>
                                            ￥
                                            <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.costPrice > 0)>
                                                <span>${sort.item.limitedTimeSupplement.costPrice}</span>
                                                <span style="margin-left: 2px;font-size: 14px">到手价</span>
                                            <#else>
                                                ${tcList.price}
                                            </#if>
                                        </span> 
                                        <span class="tcsj">x${tcList.packageProductQty}</span></p>
                                        <#if tcList.preferentialPrice?? && (tcList.preferentialPrice > 0)>
                                            <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.costPrice > 0)>
                                                <p></p>
                                            <#else>
                                                <p class="yugu">
                                                    <span class="yusan"></span>
                                                    预估到手价¥${ tcList.preferentialPrice?string("0.00") }
                                                </p>
                                            </#if>
                                        </#if>
                                    </div>
                                    <div class="sjj">
                                        <span>￥${tcList.sku.retailPrice}</span>
                                    </div> <!--价格登录可见样式--> <!--<div class="loginshow">价格登录可见</div>--> <!--暂无购买权限样式-->
                                    <!--<div class="notbug">暂无购买权限</div>-->
                                </li>
                                <li class="lib5"></li>
                                <li class="lib4"></li>
                                <li class="lib6"></li>
                            </ul>
                            <#assign status_index=status_index+1 />
                        </#list>
                        <div class="tccheckb">
                            <#if (group.valid !=0)>
                                <label class="checkbox-pretty taocan inline <#if sort.item.status=1>checked</#if> <#if (group.valid ==1)>valid</#if>">
                                    <#if (group.valid =0)>
                                        <span>失效</span>
                                        <input type="hidden" name="invalidtc" value="${sort.item.packageId}"/>
                                    <#else>
                                        <input type="hidden" name="validtc" value="${sort.item.packageId}"/>
                                        <input type="checkbox" name="${group.orgId}" value="${sort.item.packageId}" <#if sort.item.status=1>checked="checked"</#if>><span></span>
                                    </#if>
                                </label>
                            <#else>
                                <span>失效</span>
                                <input type="hidden" name="invalidtc" value="${sort.item.packageId}"/>
                            </#if>
                        </div>
                        <!--加减套餐数量-->
                        <div class="taocanjj">
                            <div class="suliang">
                                <a href="javascript:void(0);" class="sub fl">-</a> <input <#if (group.valid =0)>readonly</#if>
                                                                                          class="fl" type="text" value="${sort.item.amount}" packageId="${sort.item.packageId}"
                                                                                          onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                                                                          onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}" onblur="blurCartNum(this)" isSplit="1" middpacking="1"/>
                                <a href="javascript:void(0);" class="add fl">+</a>
                            </div>
                        </div>
                        <!--套餐小计-->
                        <div class="taocanxj">
                            <div class="zkj">
                                ￥${sort.item.subtotal}
                            </div>
                            <div class="sjj">
                                <!--<span>满减优惠￥</span><span>4.10</span>-->
                            </div>
                            <div class="sjj">
                                <!--<span>优惠券￥</span><span>4.10</span>-->
                            </div>
                        </div>
                        <!--删除套餐-->
                        <div class="deltc">
                            <a href="javascript:void(0)" class="addbuy" data-toggle="modal" data-keyboard="false" data-target="#delTaoCanModal" onclick="itemTCRemove(${sort.item.packageId})">删除</a>
                        </div>
                    </div>
                            </#if>
                            <#if sort.itemType !=3 && (group.valid !=0)>
                                <#--<div class="${(group.type != 10 && group.type != 9)?string("has-active" , "")}">-->
                                <div class="bodybox ${(group.type != 10 && group.type != 9)?string("has-border" , "")} <#if sort.item.status=1>cur</#if> <#if sort_index = (group.sorted?size -1)>has-active</#if>">
                                    <!--列表-->
                                    <ul  class="list-shop-card-qt" qtData="${company_index},${group_index},${sort_index},${sort.item.id},${shop.originalShopCode},${sort.item.gift},${cartInfo.scmEPrefix},${sort.item.name}">
                                        <li class="lib1 ${(group.type != 10 && group.type != 9)?string("active-link" , "")}">
                                            <div class="checkb fl ">
                                                <#if (group.valid !=0)>
                                                    <label class="checkbox-pretty putong inline <#if sort.item.status=1>checked</#if> <#if (group.valid ==1)>valid</#if>">
                                                        <#if (group.valid !=0)>
                                                            <input type="hidden" name="valid" value="${sort.item.id}"/>
                                                            <input type="checkbox" name="${group.orgId}" value="${sort.item.id}" <#if sort.item.status=1>checked="checked"</#if>><span></span>
                                                        </#if>
                                                    </label>
                                                </#if>
                                            </div>
                                            <div class="l-box fl">
                                                <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.remainingTime > 0)>
                                                <a  onclick='listShopCardQtClick(this)' target="_blank" href="${ctx}/search/skuDetail/${sort.item.skuId}.htm" title="${sort.item.name }" class="add_a">
                                                    <img id="dt_${sort.item.id}" src="${productImageUrl }/ybm/product/min/${sort.item.imageUrl}" alt="药帮忙" onerror="this.src='/static/images/default-big.png'">
                                                    <!--药狂欢角标 默认隐藏 去掉noshow显示-->
                                                    <#if sort.item.markerUrl?? && sort.item.markerUrl!=''>
                                                        <div class="yaokuanghuan-pos">
                                                            <img src="${productImageUrl}/${sort.item.markerUrl}" alt="">
                                                        </div>
                                                    </#if>
                                                    <div class="timer add_subsidy"from="you" data-timer="${sort.item.limitedTimeSupplement.remainingTime}">
                                                        <p class="timeP">   
                                                            <span class="f24 ySpan font timer-hour">00</span>
                                                            <span class="f18">:</span>
                                                            <span class="f24 ySpan font timer-minute">00</span>
                                                            <span class="f18">:</span>
                                                            <span class="f24 ySpan font timer-second">00</span>
                                                            <span class="only">后结束</span>
                                                        </p>
                                                    </div>
                                                    <div class="add_box">
                                                        <img src="/static/images/product/add-subsidy.png" alt="">
                                                    </div>
                                                    <div class="bq-box">
                                                        <#if sort.item.skuStatus==2>
                                                            <img src="/static/images/product/bq-shouqing.png" alt="">
                                                        </#if>
                                                        <#if sort.item.skuStatus==4>
                                                            <img src="/static/images/product/bq-xiajia.png" alt="">
                                                        </#if>
                                                        <#if sort.item.skuStatus==95>
                                                            <img src="/static/images/product/bq-chaofanwei.png" alt="">
                                                        </#if>
                                                        <#if sort.item.skuStatus==108>
                                                            <img src="/static/images/product/bq-chaofanwei.png" alt="">
                                                        </#if>
                                                        <#if sort.item.skuStatus==105>
                                                            <img src="/static/images/product/bq-xieyishangpin.png" alt="">
                                                        </#if>
                                                        <#if sort.item.skuStatus==106>
                                                            <img src="/static/images/product/bp-xieyishangpin2.png" alt="">
                                                        </#if>
                                                    </div>
                                                </a>
                                                <#else>
                                                <a  onclick='listShopCardQtClick(this)' target="_blank" href="${ctx}/search/skuDetail/${sort.item.skuId}.htm" title="${sort.item.name }">
                                                    <img id="dt_${sort.item.id}" src="${productImageUrl }/ybm/product/min/${sort.item.imageUrl}" alt="药帮忙" onerror="this.src='/static/images/default-big.png'">

                                                    <!--药狂欢角标 默认隐藏 去掉noshow显示-->
                                                    <#if sort.item.markerUrl?? && sort.item.markerUrl!=''>
                                                        <div class="yaokuanghuan-pos">
                                                            <img src="${productImageUrl}/${sort.item.markerUrl}" alt="">
                                                        </div>
                                                    </#if>

                                                    <div class="bq-box t2 ${tcList.skuStatus}">
                                                        <#if sort.item.skuStatus==2>
                                                            <img src="/static/images/product/bq-shouqing.png" alt="">
                                                        </#if>
                                                        <#if sort.item.skuStatus==4>
                                                            <img src="/static/images/product/bq-xiajia.png" alt="">
                                                        </#if>
                                                        <#if sort.item.skuStatus==95>
                                                            <img src="/static/images/product/bq-chaofanwei.png" alt="">
                                                        </#if>
                                                        <#if sort.item.skuStatus==108>
                                                            <img src="/static/images/product/bq-chaofanwei.png" alt="">
                                                        </#if>
                                                        <#if sort.item.skuStatus==105>
                                                            <img src="/static/images/product/bq-xieyishangpin.png" alt="">
                                                        </#if>
                                                        <#if sort.item.skuStatus==106>
                                                            <img src="/static/images/product/bp-xieyishangpin2.png" alt="">
                                                        </#if>
                                                    </div>
                                                </a>
                                                </#if>
                                            </div>
                                            <div class="r-box fl">
                                                <div class="lib1-row1 text-overflow">
                                                    <#if sort.item.tagTitle ?? && (sort.item.tagTitle != "" && sort.item.tagTitle != null)>
                                                        <span class="tag-title">${sort.item.tagTitle}</span>
                                                    </#if>
                                                    <span  onclick='listShopCardQtClick(this)'>
                                                        <a target="_blank" href="${ctx}/search/skuDetail/${sort.item.skuId}.htm" title="${sort.item.name }">
                                                            <#if sort.item.agent == 1><span class="dujia">独家</span></#if>${sort.item.name}
                                                        </a>
                                                    </span>
                                                </div>
                                                <div class="lib1-row3">
                                                    <div class="row-biaoqian">
                                                        <#if sort.item.valid==1>
                                                            <#if sort.item.tagList ?? && (sort.item.tagList?size >0) >
                                                                <#list sort.item.tagList as item >
                                                                    <#if (item_index < 3)>
                                                                        <span class="<#if item.uiType == 1>linqi</#if>
                                                                    <#if item.uiType == 2>quan</#if>
                                                                    <#if item.uiType == 3>manjian</#if>
                                                                    <#if item.uiType == 4>default</#if>
                                                                    <#if item.uiType == 5>yibao</#if>
                                                                    ">${item.name}</span>
                                                                    </#if>
                                                                </#list>
                                                            </#if>
                                                        </#if>
                                                    </div>
                                                </div>
                                                <div class="lib1-row5">
                                                    <div class="row-last">
                                                        <#if (sort.item.sku.suggestPrice ?? ) && (sort.item.sku.suggestPrice != '')>
                                                            <div class="kongxiao-box">
                                                                <span class="s-kx">零售价</span><span class="jg">￥${sort.item.sku.suggestPrice}</span>
                                                            </div>
                                                        </#if>
                                                        <#if sort.item.sku.grossMargin ?? && sort.item.sku.grossMargin !=''>
                                                            <div class="maoli-box">
                                                                <span class="s-ml">毛利</span><span class="jg">${sort.item.sku.grossMargin}</span>
                                                            </div>
                                                        </#if>
                                                    </div>
                                                </div>
                                                <div class="lib1-row2  text-overflow">
                                                    <span class="title">规　　格：</span>
                                                    <span class="info">${sort.item.spec}</span>
                                                </div>
                                                <div class="lib1-row4  text-overflow">
                                                    <span class="title">生产厂家：</span>
                                                    <span class="info">${sort.item.sku.manufacturer}</span>
                                                </div>
                                                <div class="lib1-row4 text-overflow baoyou">
                                                    <span class="tag-bao">${sort.item.freightTips}</span>
                                                </div>
                                                <div class="lib1-row2  text-overflow">
                                                    <div class="lib1-row2  text-overflow">
                                                        <span class="title">效　　期：</span>
                                                        <#if sort.item.sku.validity?? && sort.item.sku.validity != ''>
                                                            <span class="info">${sort.item.sku.validity}</span>
                                                        <#elseif ((sort.item.sku.nearEffect?? && sort.item.sku.nearEffect != '') && (sort.item.sku.farEffect?? && sort.item.sku.farEffect != ''))>
                                                            <#if sort.item.sku.nearEffect == sort.item.sku.farEffect>
                                                                <span class="info">${sort.item.sku.farEffect}</span>
                                                            <#else>
                                                                <span class="info">${sort.item.sku.nearEffect+"/"+sort.item.sku.farEffect}</span>
                                                            </#if>
                                                        </#if>
                                                    </div>
                                                </div>
                                                <div class="lib1-row3">
                                                    <div class="row-biaoqian">
                                                        <#if sort.item.valid==1>
                                                            <#if sort.item.tagWholeOrderList ?? && (sort.item.tagWholeOrderList?size >0) >
                                                                <#list sort.item.tagWholeOrderList as item >
                                                                    <div class="synthesis">
                                                                        <div class="synthesis-biao">
                                                                            <#if item.description ?? && item.description != ''>
                                                                                <span class="synthesis-biao-left">${item.name}</span>
                                                                                <span class="synthesis-biao-shuoming">${item.description}</span>
                                                                            <#else>
                                                                                <span class="synthesis-biao-left-single">${item.name}</span>
                                                                            </#if>
                                                                        </div>
                                                                    </div>
                                                                </#list>
                                                            </#if>
                                                        </#if>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li class="lib3">
                                            <#if sort.item.skuStatus!=105>
                                                <div class="zkj">
                                                    ￥
                                                    <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.costPrice > 0)>
                                                        <span>${sort.item.limitedTimeSupplement.costPrice}</span>
                                                        <span style="margin-left: 2px;font-size: 14px">到手价</span>
                                                    <#else>
                                                        ${sort.item.price}
                                                    </#if>
                                                    
                                                    <#if (sort.item.valid ?? ) && (sort.item.valid == 1)>
                                                    <#--<#if sort.item.hasBuyReward><span class="manjian">满减</span></#if>-->
                                                        <#if sort.item.promotionTag?? && sort.item.promotionTag!=''>
                                                            <span class="xiangou"><img src="${ctx}/static/images/xgsanjiao.png" class="sanjiao">${ sort.item.promotionTag }</span>
                                                        </#if>
                                                    </#if>
                                                    <#if sort.item.preferentialPrice?? && (sort.item.preferentialPrice > 0)>
                                                        <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.costPrice > 0)>
                                                            <p></p>
                                                        <#else>
                                                            <p class="yugu">
                                                                <span class="yusan"></span>
                                                                预估到手价¥${ sort.item.preferentialPrice?string("0.00") }
                                                            </p>
                                                        </#if>
                                                    </#if>
                                                </div>
                                                <div class="sjj"><span>￥${sort.item.sku.retailPrice}</span></div>
                                                <#if (sort.item.valid ?? ) && (sort.item.valid == 1)>
                                                    <div class="row-last">
                                                        <#if sort.item.hasProm && (sort.item.sku.promotionDetail.promotion??)>
                                                            <div class="time">
                                                                <span style="color: #ff2400;">促销截止时间：</span>
                                                                <span style="color: #ff2400;">${sort.item.sku.promotionDetail.promotion.endTime?string("yyyy-MM-dd")}</span>
                                                            </div>
                                                        </#if>
                                                        <#if sort.item.sku.uniformPrice ?? && sort.item.sku.uniformPrice !=''>
                                                            <div class="kongxiao-box">
                                                                <span class="s-kx">控销价</span><span class="jg">￥${sort.item.sku.uniformPrice}</span>
                                                            </div>
                                                        </#if>
                                                    </div>
                                                </#if>
                                            </#if>
                                             <div id="actPurchaseTip">
                                                <#if sort.item.actPurchaseTip??>
                                                    <div style="width:240px;padding: 5px;color:#333333;border-radius: 5px;background-color: #FFF7E9;margin:3px 0;word-wrap:break-word;">${ sort.item.actPurchaseTip }</div>
                                                </#if>
                                            </div>
                                        </li>
                                        <li class="lib5">
                                            <#if sort.item.gift>
                                                <div class="suliang">
                                                    <span class="disabledSub">-</span>
                                                    <input disabled="${sort.item.gift}" <#if (group.valid =0)>readonly</#if> class="fl" type="text" value="${sort.item.amount}" pid="${sort.item.skuId}" id="cartNum_${sort.item.skuId}"
                                                           onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                                           onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onblur="blurCartNum(this)" isSplit="${sort.item.sku.isSplit}" middpacking="${sort.item.sku.mediumPackageNum}"/>
                                                    <span class="disabledAdd">+</span>
                                                </div>
                                            <#else>
                                                <div class="suliang">
                                                    <a href="javascript:void(0);" onclick="shopBottomClick(this,'减',1)" class="sub fl">-</a>
                                                    <input <#if (group.valid =0)>readonly</#if> class="fl" type="text" value="${sort.item.amount}" pid="${sort.item.skuId}" id="cartNum_${sort.item.skuId}"
                                                           onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                                           onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onclick="shopBottomClick(this,this.value,2)" onblur="blurCartNum(this)" isSplit="${sort.item.sku.isSplit}" middpacking="${sort.item.sku.mediumPackageNum}"/>
                                                    <a href="javascript:void(0);" class="add fl" onclick="shopBottomClick(this,'加',3)">+</a>
                                                </div>
                                            </#if>





                                            <!--中包装-->
                                            <div class="zbz_box">
                                                <span class="zbz_box_sp1">中包装:${sort.item.sku.mediumPackageNum}${sort.item.sku.productUnit}</span>
                                                <#if sort.item.sku.isSplit==0>
                                                    <span class="chailin">不可拆零</span>
                                                </#if>
                                            </div>
                                            <div class="limit_box">
                                                <#if sort.item.normalQty gt 0 && sort.item.promoQty gt 0 && !sort.item.gift>
                                                    <span class="limit_box"
                                                          style="min-width: auto;min-height: auto">此商品为限量商品，超出“${sort.item.promoQty}${sort.item.sku.productUnit}”的部分将按照原价购买，超出部分也参与折后价计算</span>
                                                </#if>
                                            </div>
                                        </li>
                                        <li class="lib4">
                                            <div class="zkj">￥
                                                <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.subTotal > 0)>
                                                    ${sort.item.limitedTimeSupplement.subTotal}
                                                <#else>
                                                    ${sort.item.subtotal}
                                                </#if>
                                            </div>
                                            <#--<div class="sjj">-->
                                            <#--<span>满减优惠￥</span><span>4.10</span>-->
                                            <#--</div>-->
                                            <#--<div class="sjj">-->
                                            <#--<span>优惠券￥</span><span>4.10</span>-->
                                            <#--</div>-->
                                        </li>
                                        <li class="lib6">
                                            <#if !sort.item.gift>
                                            <a href="javascript:void(0)" class="addbuy" data-toggle="modal" data-keyboard="false" onclick="itemRemove(${sort.item.id})">删除</a>
                                            <a href="javascript:void(0)" id="href_DT_${sort.item.skuId}" class="souc" data-toggle="modal" data-keyboard="false" onclick="itemFollow(${sort.item.id}, parent.event);shopBottomClick(this,'移入收藏夹',5)">移入收藏夹</a>
                                            </#if>
                                        </li>
                                    </ul>
                                </div>
                            </#if>
                            </#list>
                        </#list>
                    </div>
                </#list>
            </div>
        </#list>

        <#if cartInfo.novalidGroup != null >
            <!--满减标题-->
            <#if (cartInfo.novalidGroup.type != 10 && cartInfo.novalidGroup.type != 9)>
                <div <#if (cartInfo.novalidGroup.valid !=0)>class="manjianbox"</#if> <#if (cartInfo.novalidGroup.valid =0)>id="shixiaobox" class="shixiaotitle"</#if> >
                    <#if cartInfo.novalidGroup.title??>
                        <span class="title"></span>
                        <span class="info">${cartInfo.novalidGroup.title}</span>
                        <#if (cartInfo.novalidGroup.valid =0)>
                            <a href="javascript:void(0);" class="clear-goods" onclick="clearInvalidProduct()">清除失效商品</a>
                            <a href="javascript:void(0);" class="move-goods" onclick="mvToCollection()">移入收藏夹</a>
                        </#if>
                        <#if cartInfo.novalidGroup.titlePCUrl ?? && cartInfo.novalidGroup.titlePCUrl != ''>
                            <a href="${cartInfo.novalidGroup.titlePCUrl}" class="order-qcd fr">${cartInfo.novalidGroup.titleUrlText}<span class="order-qcd-fuhao">></span></a>
                        </#if>
                    </#if>
                </div>
            </#if>
            <#list cartInfo.novalidGroup.sorted as sort>
                <#if sort.itemType=3>
                    <!--套餐-->
                    <#if sort.item.valid==1>
                        <div class="taocanbox">
                            <span class="title">套餐</span>
                            <span class="info">套餐优惠单价：</span><span class="price">￥${sort.item.price}</span>
                            <span class="info">套餐搭配商品小计：</span><span class="price">￥${sort.item.subtotal}</span>
                            <span class="info">已优惠：</span><span class="price">￥${sort.item.discount}</span>
                        </div>
                    </#if>
                    <!--套餐表体-->
                    <div class="bodybox taocanspe <#if sort.item.status=1 && (group.valid !=0)>cur</#if> <#if (group.valid =0)>shixiaospe</#if>">
                        <!--列表-->
                        <#assign status_index=0 />
                        <#list sort.subItemList as tcList>
                            <ul>
                                <li class="lib1">
                                    <div class="checkb fl"></div>
                                    <div class="l-box fl ">
                                        <a target="_blank" href="${ctx}/search/skuDetail/${tcList.sku.id}.htm" title="${tcList.sku.commonName }"><img src="${productImageUrl }/ybm/product/min/${tcList.imageUrl}"  alt="">
                                        </a>
                                        <!--标签-->
                                        <#if sort.item.valid=0>
                                            <div class="bq-box t3 ${tcList.skuStatus}">
                                                <#if tcList.skuStatus==2>
                                                    <img src="/static/images/product/bq-shouqing.png" alt="">
                                                </#if>
                                                <#if tcList.skuStatus==4>
                                                    <img src="/static/images/product/bq-xiajia.png" alt="">
                                                </#if>
                                                <#if tcList.skuStatus==95>
                                                    <img src="/static/images/product/bq-chaofanwei.png" alt="">
                                                </#if>
                                                <#if tcList.skuStatus==108>
                                                    <img src="/static/images/product/bq-chaofanwei.png" alt="">
                                                </#if>
                                                <#if tcList.skuStatus==105>
                                                    <img src="/static/images/product/bq-xieyishangpin.png" alt="">
                                                </#if>
                                                <#if tcList.skuStatus==106>
                                                    <img src="/static/images/product/bp-xieyishangpin2.png" alt="">
                                                </#if>
                                            </div>
                                        </#if>
                                    </div>
                                    <div class="r-box fl">
                                        <div class="lib1-row1 text-overflow">
                                            <#if tcList.tagTitle ?? && (tcList.tagTitle != "" && tcList.tagTitle != null)>
                                                <span class="tag-title">${tcList.tagTitle}</span>
                                            </#if>
                                            <span>
                                                <a target="_blank" href="${ctx}/search/skuDetail/${tcList.sku.id}.htm" title="${tcList.sku.commonName }">
                                                        ${tcList.sku.commonName}
                                                </a>
                                            </span>
                                        </div>
                                        <div class="lib1-row3">
                                            <div class="lib1-row3">
                                                <div class="row-biaoqian">
                                                    <#if sort.item.valid==1>
                                                        <#if tcList.tagList ?? && (tcList.tagList?size >0) >
                                                            <#list tcList.tagList as item >
                                                                <#if (item_index < 3)>
                                                                    <span class="<#if item.uiType == 1>linqi</#if>
                                                            <#if item.uiType == 2>quan</#if>
                                                            <#if item.uiType == 3>manjian</#if>
                                                            <#if item.uiType == 4>default</#if>
                                                            <#if item.uiType == 5>yibao</#if>
                                                            ">${item.name}</span>
                                                                </#if>
                                                            </#list>
                                                        </#if>
                                                    </#if>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="lib1-row2 text-overflow">
                                            <span class="title">规 格：</span> <span class="info">${tcList.sku.spec}</span>
                                        </div>
                                        <#--<div class="lib1-row3">
                                            <span class="title">批准文号：</span> <span class="info">${tcList.balancePercentDesc}</span>
                                        </div>-->
                                        <div class="lib1-row4 text-overflow">
                                            <span class="title">生产厂家：</span> <span class="info">${tcList.sku.manufacturer}</span>
                                        </div>
                                        <div class="lib1-row2  text-overflow">
                                            <span class="title">效 期：</span>
                                            <#if tcList.sku.validity?? && tcList.sku.validity != ''>
                                                <span class="info">${tcList.sku.validity}</span>
                                            <#elseif ((tcList.sku.nearEffect?? && tcList.sku.nearEffect != '') && (tcList.sku.farEffect?? && tcList.sku.farEffect != ''))>
                                                <#if tcList.sku.nearEffect == tcList.sku.farEffect>
                                                    <span class="info">${tcList.sku.farEffect}</span>
                                                <#else>
                                                    <span class="info">${tcList.sku.nearEffect+"/"+tcList.sku.farEffect}</span>
                                                </#if>
                                            </#if>
                                        </div>
                                        <div class="lib1-row3">
                                            <div class="row-biaoqian">
                                                <#if sort.item.valid==1>
                                                    <#if tcList.tagWholeOrderList ?? && (tcList.tagWholeOrderList?size >0) >
                                                        <#list tcList.tagWholeOrderList as item >
                                                            <div class="synthesis">
                                                                <div class="synthesis-biao">
                                                                    <#if item.description ?? && item.description != ''>
                                                                        <span class="synthesis-biao-left">${item.name}</span>
                                                                        <span class="synthesis-biao-shuoming">${item.description}</span>
                                                                    <#else>
                                                                        <span class="synthesis-biao-left-single">${item.name}</span>
                                                                    </#if>
                                                                </div>
                                                            </div>
                                                        </#list>
                                                    </#if>
                                                </#if>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                                <li class="lib3">

                                    <div class="zkj">
                                        <p style="display: inline-block">
                                            <span>￥
                                                <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.costPrice > 0)>
                                                    <span>${sort.item.limitedTimeSupplement.costPrice}</span>
                                                    <span style="margin-left: 2px;font-size: 14px">到手价</span>
                                                <#else>
                                                   ${tcList.price}
                                                </#if> 
                                            </span> 
                                            <span class="tcsj">x${tcList.packageProductQty}</span></p>
                                        <#if tcList.preferentialPrice?? && (tcList.preferentialPrice > 0)>
                                            <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.costPrice > 0)>
                                                <p></p>
                                            <#else>
                                                <p class="yugu">
                                                    <span class="yusan"></span>
                                                    预估到手价¥${ tcList.preferentialPrice?string("0.00") }
                                                </p>
                                            </#if>
                                        </#if>
                                    </div>
                                    <div class="sjj">
                                        <span>￥${tcList.sku.retailPrice}</span>
                                    </div> <!--价格登录可见样式--> <!--<div class="loginshow">价格登录可见</div>--> <!--暂无购买权限样式-->
                                    <!--<div class="notbug">暂无购买权限</div>-->
                                </li>
                                <li class="lib5"></li>
                                <li class="lib4"></li>
                                <li class="lib6"></li>
                            </ul>
                            <#assign status_index=status_index+1 />
                        </#list>
                        <div class="tccheckb">
                            <#if (cartInfo.novalidGroup.valid !=0)>
                                <label class="checkbox-pretty taocan inline <#if sort.item.status=1>checked</#if> <#if (cartInfo.novalidGroup.valid ==1)>valid</#if>">
                                    <#if (group.valid =0)>
                                        <span>失效</span>
                                        <input type="hidden" name="invalidtc" value="${sort.item.packageId}"/>
                                    <#else>
                                        <input type="hidden" name="validtc" value="${sort.item.packageId}"/>
                                        <input type="checkbox" name="${cartInfo.novalidGroup.orgId}" value="${sort.item.packageId}" <#if sort.item.status=1>checked="checked"</#if>><span></span>
                                    </#if>
                                </label>
                            <#else>
                                <span>失效</span>
                                <input type="hidden" name="invalidtc" value="${sort.item.packageId}"/>
                            </#if>
                        </div>
                        <!--加减套餐数量-->
                        <div class="taocanjj">
                            <div class="suliang">
                                <a href="javascript:void(0);" class="sub fl">-</a> <input <#if (cartInfo.novalidGroup.valid =0)>readonly</#if>
                                                                                          class="fl" type="text" value="${sort.item.amount}" packageId="${sort.item.packageId}"
                                                                                          onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                                                                          onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}" onblur="blurCartNum(this)" isSplit="1" middpacking="1"/>
                                <a href="javascript:void(0);" class="add fl">+</a>
                            </div>
                        </div>
                        <!--套餐小计-->
                        <div class="taocanxj">
                            <div class="zkj">￥${sort.item.subtotal}</div>
                            <div class="sjj">
                                <!--<span>满减优惠￥</span><span>4.10</span>-->
                            </div>
                            <div class="sjj">
                                <!--<span>优惠券￥</span><span>4.10</span>-->
                            </div>
                        </div>
                        <!--删除套餐-->
                        <div class="deltc">
                            <a href="javascript:void(0)" class="addbuy" data-toggle="modal" data-keyboard="false" data-target="#delTaoCanModal" onclick="itemTCRemove(${sort.item.packageId})">删除</a>
                        </div>
                    </div>

                </#if>
                <#if sort.itemType !=3>
                    <div class="bodybox <#if sort.item.status=1>cur</#if> <#if (group.valid =0)>shixiaospe</#if>">
                        <!--列表-->
                        <ul>
                            <li class="lib1">
                                <div class="checkb fl <#if (cartInfo.novalidGroup.valid =0)>shixiaotext valid</#if>">
                                    <#if (cartInfo.novalidGroup.valid !=0)>
                                        <label class="checkbox-pretty putong inline <#if sort.item.status=1>checked</#if> <#if (cartInfo.novalidGroup.valid ==1)>valid</#if>">
                                            <#if (cartInfo.novalidGroup.valid =0)>
                                                <span>失效</span>
                                                <input type="hidden" name="invalid" value="${sort.item.id}"/>
                                            <#else>
                                                <input type="hidden" name="valid" value="${sort.item.id}"/>
                                                <input type="checkbox" name="${group.orgId}" value="${sort.item.id}" <#if sort.item.status=1>checked="checked"</#if>><span></span>
                                            </#if>
                                        </label>
                                    <#else>
                                        <span>失效</span>
                                        <input type="hidden" name="invalid" value="${sort.item.id}"/>
                                    </#if>
                                </div>
                                <div class="l-box fl">
                                    <a target="_blank" href="${ctx}/search/skuDetail/${sort.item.skuId}.htm" title="${sort.item.name }">
                                        <img id="dt_${sort.item.id}" src="${productImageUrl }/ybm/product/min/${sort.item.imageUrl}" alt="药帮忙" onerror="this.src='/static/images/default-big.png'">

                                        <!--药狂欢角标 默认隐藏 去掉noshow显示-->
                                        <#if sort.item.markerUrl?? && sort.item.markerUrl!=''>
                                            <div class="yaokuanghuan-pos">
                                                <img src="${productImageUrl}/${sort.item.markerUrl}" alt="">
                                            </div>
                                        </#if>

                                        <div class="bq-box t4 ${tcList.skuStatus}">
                                            <#if sort.item.skuStatus==2>
                                                <img src="/static/images/product/bq-shouqing.png" alt="">
                                            </#if>
                                            <#if sort.item.skuStatus==4>
                                                <img src="/static/images/product/bq-xiajia.png" alt="">
                                            </#if>
                                            <#if sort.item.skuStatus==95>
                                                <img src="/static/images/product/bq-chaofanwei.png" alt="">
                                            </#if>
                                            <#if sort.item.skuStatus==108>
                                                <img src="/static/images/product/bq-chaofanwei.png" alt="">
                                            </#if>
                                            <#if sort.item.skuStatus==105>
                                                <img src="/static/images/product/bq-xieyishangpin.png" alt="">
                                            </#if>
                                            <#if sort.item.skuStatus==106>
                                                <img src="/static/images/product/bp-xieyishangpin2.png" alt="">
                                            </#if>
                                        </div>
                                    </a>

                                </div>
                                <div class="r-box fl">
                                    <div class="lib1-row1 text-overflow">
                                        <#if sort.item.tagTitle ?? && (sort.item.tagTitle != "" && sort.item.tagTitle != null)>
                                            <span class="tag-title">${sort.item.tagTitle}</span>
                                        </#if>
                                        <span>
                                            <a target="_blank" href="${ctx}/search/skuDetail/${sort.item.skuId}.htm" title="${sort.item.name }">
                                                <#if sort.item.gift || sort.item.isShow806>
                                                    <div class="bq806">
                                                        <img src="/static/images/bq806.png" alt="">
                                                    </div>
                                                </#if>
                                                <#if sort.item.agent == 1><span class="dujia">独家</span></#if>${sort.item.name}
                                            </a>
                                        </span>
                                    </div>
                                    <div class="lib1-row3">
                                        <div class="lib1-row3">
                                            <div class="row-biaoqian">
                                                <#if sort.item.valid==1>
                                                    <#if sort.item.tagList ?? && (sort.item.tagList?size >0) >
                                                        <#list sort.item.tagList as item >
                                                            <#if (item_index < 3)>
                                                                <span class="<#if item.uiType == 1>linqi</#if>
                                                <#if item.uiType == 2>quan</#if>
                                                <#if item.uiType == 3>manjian</#if>
                                                <#if item.uiType == 4>default</#if>
                                                <#if item.uiType == 5>yibao</#if>
                                                ">${item.name}</span>
                                                            </#if>
                                                        </#list>
                                                    </#if>
                                                </#if>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="lib1-row5">
                                        <div class="row-last">
                                            <#if (sort.item.sku.suggestPrice ?? ) && (sort.item.sku.suggestPrice != '')>
                                                <div class="kongxiao-box">
                                                    <span class="s-kx">零售价</span><span class="jg">￥${sort.item.sku.suggestPrice}</span>
                                                </div>
                                            </#if>
                                            <#if sort.item.sku.grossMargin ?? && sort.item.sku.grossMargin !=''>
                                                <div class="maoli-box">
                                                    <span class="s-ml">毛利</span><span class="jg">${sort.item.sku.grossMargin}</span>
                                                </div>
                                            </#if>
                                        </div>
                                    </div>
                                    <div class="lib1-row2  text-overflow">
                                        <span class="title">规　　格：</span>
                                        <span class="info">${sort.item.spec}</span>
                                    </div>
                                    <div class="lib1-row4  text-overflow">
                                        <span class="title">生产厂家：</span>
                                        <span class="info">${sort.item.sku.manufacturer}</span>
                                    </div>
                                    <div class="lib1-row2  text-overflow">
                                        <span class="title">效　　期：</span>
                                        <#if sort.item.sku.validity?? && sort.item.sku.validity != ''>
                                            <span class="info">${sort.item.sku.validity}</span>
                                        <#elseif ((sort.item.sku.nearEffect?? && sort.item.sku.nearEffect != '') && (sort.item.sku.farEffect?? && sort.item.sku.farEffect != ''))>
                                            <#if sort.item.sku.nearEffect == sort.item.sku.farEffect>
                                                <span class="info">${sort.item.sku.farEffect}</span>
                                            <#else>
                                                <span class="info">${sort.item.sku.nearEffect+"/"+sort.item.sku.farEffect}</span>
                                            </#if>
                                        </#if>
                                    </div>
                                    <div class="lib1-row3">
                                        <div class="row-biaoqian">
                                            <#if sort.item.valid==1>
                                                <#if sort.item.tagWholeOrderList ?? && (sort.item.tagWholeOrderList?size >0) >
                                                    <#list sort.item.tagWholeOrderList as item >
                                                        <div class="synthesis">
                                                            <div class="synthesis-biao">
                                                                <#if item.description ?? && item.description != ''>
                                                                    <span class="synthesis-biao-left">${item.name}</span>
                                                                    <span class="synthesis-biao-shuoming">${item.description}</span>
                                                                <#else>
                                                                    <span class="synthesis-biao-left-single">${item.name}</span>
                                                                </#if>
                                                            </div>
                                                        </div>
                                                    </#list>
                                                </#if>
                                            </#if>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="lib3">
                                <#if sort.item.skuStatus!=105 && sort.item.skuStatus!=91>
                                    <div class="zkj">￥
                                        <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.costPrice > 0)>
                                            <span>${sort.item.limitedTimeSupplement.costPrice}</span>
                                            <span style="margin-left: 2px;font-size: 14px">到手价</span>
                                        <#else>
                                            ${sort.item.price}
                                        </#if>
                                        <#if (sort.item.valid ?? ) && (sort.item.valid == 1)>
                                        <#--<#if sort.item.hasBuyReward><span class="manjian">满减</span></#if>-->
                                            <#if sort.item.promotionTag?? && sort.item.promotionTag!=''>
                                                <span class="xiangou"><img src="${ctx}/static/images/xgsanjiao.png" class="sanjiao">${ sort.item.promotionTag }</span>
                                            </#if>
                                        </#if>
                                        <#if sort.item.preferentialPrice?? && (sort.item.preferentialPrice > 0)>
                                            <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.costPrice > 0)>
                                                <p></p>
                                            <#else>
                                                <p class="yugu">
                                                    <span class="yusan"></span>
                                                    预估到手价¥${ sort.item.preferentialPrice?string("0.00") }
                                                </p>
                                            </#if>
                                        </#if>
                                    </div>
                                    <div class="sjj"><span>￥${sort.item.sku.retailPrice}</span></div>
                                    <#if (sort.item.valid ?? ) && (sort.item.valid == 1)>
                                        <div class="row-last">
                                            <#if sort.item.hasProm && (sort.item.sku.promotionDetail.promotion??)>
                                                <div class="time">
                                                    <span style="color: #ff2400;">促销截止时间：</span>
                                                    <span style="color: #ff2400;">${sort.item.sku.promotionDetail.promotion.endTime?string("yyyy-MM-dd")}</span>
                                                </div>
                                            </#if>
                                            <#if sort.item.sku.uniformPrice ?? && sort.item.sku.uniformPrice !=''>
                                                <div class="kongxiao-box">
                                                    <span class="s-kx">控销价</span><span class="jg">￥${sort.item.sku.uniformPrice}</span>
                                                </div>
                                            </#if>
                                        </div>
                                    </#if>
                                </#if>
                                <#if sort.item.skuStatus ==91 >
                                    <div class="notbug">暂无购买权限</div>
                                </#if>
                                <!--价格登录可见样式-->
                                <!--<div class="loginshow">价格登录可见</div>-->
                                <!--暂无购买权限样式-->
                                <!--<div class="notbug">暂无购买权限</div>-->
                            </li>
                            <li class="lib5">
                                <#if sort.item.gift>
                                    <div class="suliang">
                                        <span class="disabledSub">-</span>
                                        <input disabled="${sort.item.gift}" <#if (cartInfo.novalidGroup.valid =0)>readonly</#if> class="fl" type="text" value="${sort.item.amount}" pid="${sort.item.skuId}" id="cartNum_${sort.item.skuId}"
                                               onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                               onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onblur="blurCartNum(this)" isSplit="${sort.item.sku.isSplit}" middpacking="${sort.item.sku.mediumPackageNum}"/>
                                        <span class="disabledAdd">+</span>
                                    </div>
                                <#else>
                                    <div class="suliang">
                                        <a href="javascript:void(0);" class="sub fl">-</a>
                                        <input <#if (cartInfo.novalidGroup.valid =0)>readonly</#if> class="fl" type="text" value="${sort.item.amount}" pid="${sort.item.skuId}" id="cartNum_${sort.item.skuId}"
                                               onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                               onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onblur="blurCartNum(this)" isSplit="${sort.item.sku.isSplit}" middpacking="${sort.item.sku.mediumPackageNum}"/>
                                        <a href="javascript:void(0);" class="add fl">+</a>
                                    </div>
                                </#if>


                                <!--中包装-->
                                <div class="zbz_box">
                                    <span class="zbz_box_sp1">中包装:${sort.item.sku.mediumPackageNum}${sort.item.sku.productUnit}</span>
                                    <#if sort.item.sku.isSplit==0>
                                        <span class="chailin">不可拆零</span>
                                    </#if>
                                </div>
                                <div class="limit_box">
                                    <#if sort.item.normalQty gt 0 && sort.item.promoQty gt 0 && !sort.item.gift>
                                        <span class="limit_box"
                                              style="min-width: auto;min-height: auto">此商品为限量商品，超出“${sort.item.promoQty}${sort.item.sku.productUnit}”的部分将按照原价购买，超出部分也参与折后价计算</span>
                                    </#if>
                                </div>

                                <#--<#if sort.item.promotionTag??>
                                <!--限购 默认隐藏 去掉noshow显示&ndash;&gt;
                                <div class="xiangouinfo">
                                    <span>${sort.item.promotionTag}</span>
                                </div>
                                </#if>-->
                            </li>
                            <li class="lib4">
                                <div class="zkj">￥
                                    <#if sort.item.limitedTimeSupplement ?? && (sort.item.limitedTimeSupplement.subtotal > 0)>
                                        ${sort.item.limitedTimeSupplement.subtotal}
                                    <#else>
                                        ${sort.item.subtotal}
                                    </#if>
                                </div>
                                <#--<div class="sjj">-->
                                <#--<span>满减优惠￥</span><span>4.10</span>-->
                                <#--</div>-->
                                <#--<div class="sjj">-->
                                <#--<span>优惠券￥</span><span>4.10</span>-->
                                <#--</div>-->
                            </li>
                            <li class="lib6">
                                <#if !sort.item.gift>
                                <a href="javascript:void(0)" class="addbuy" data-toggle="modal" data-keyboard="false" onclick="itemRemove(${sort.item.id})">删除</a>
                                <a href="javascript:void(0)" id="href_DT_${sort.item.skuId}" class="souc" data-toggle="modal" data-keyboard="false" onclick="itemFollow(${sort.item.id}, parent.event)">移入收藏夹</a>
                                </#if>
                            </li>
                        </ul>
                    </div>
                </#if>
            </#list>
        </#if>

    </div>
    <#if (cartInfo == null || cartInfo.company?size=0) && (cartInfo.novalidGroup == null)>
        <div class="nogoodsbox">
            <img src="${ctx}/static/images/nogoods.png" alt="">
        </div>
    </#if>
</div>
<!--超重商品-->
<#if cartInfo.specialProductTipsShowStatus ?? && cartInfo.specialProductTipsShowStatus == 1 >
<div class="chaozhong zhongdiv">
    <div class="acol4 fr">
        <a href="javascript:void(0)" style="padding: 0 3px 0 5px;" class="fplx-a get-fei"  data-keyboard="false"><img src="/static/images/yunfei-tip.png" alt="" style="top: -2px;position: relative;width: 14px;"></a>
        <span>${cartInfo.specialProductTips}</span><span class="seeProduct" style="display: inline-block;margin-left: 10px;cursor: pointer;">${cartInfo.specialProductUrlTitle} <i class="sui-icon sui-icon icon-touch-chevron-up" style="font-size:12px;"></i></span>
    </div>
    <div class="dialog-box">
        <div class="header-box">
            <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
            <h4 class="modal-title">超重商品(<span class="pr-num" id="chaozhongSize">8</span>)</h4>
        </div>
        <div class="content-box">
            <div class="content-head">
                <span class="cell-1">商品</span>
                <span class="cell-2">价格</span>
                <span class="cell-3">数量</span>
                <span class="cell-4">小计</span>
            </div>
            <ul id="specialProduct">
            </ul>
        </div>
    </div>
</div>
</#if>
<!--结算-->
<#if cartInfo != null && (cartInfo.company?size>0 || cartInfo.novalidGroup != null)>
    <#if cartInfo.crossStoreVoucherDto !=null && cartInfo.crossStoreVoucherDto.crossStoreVoucherTips!=null && cartInfo.crossStoreVoucherDto.chooseUrl!=null>
        <div class="applybox crossStoreCoupon">
            <div class="acol2 fl crossStoreVoucherTips" style="margin-left: 50px" data_tips="${cartInfo.crossStoreVoucherDto.crossStoreVoucherTips}"></div>
            <div class="acol2 fl"><a href="${cartInfo.crossStoreVoucherDto.chooseUrl}" style="color: #FF2021;margin-left: 20px">去凑单 >></a></div>
        </div>
    </#if>
    <div class="applybox">
        <#--<#if activity?? && activity.text??>
            <div class="tishiwa"><a href="/search/skuInfoByCategory.htm?all=all" target="_blank" style="color: rgb(102, 102, 102);" onmouseover="this.style.color='#f39800';" onmouseleave="this.style.color='rgb(102, 102, 102)';">${activity.text}</a></div>
        </#if>-->
        <div class="acol1 fl">
            <label class="checkbox-pretty inline all" >
                <input type="checkbox"><span>全选</span>
            </label>
        </div>
        <div class="acol2 fl">
            <a href="javascript:void(0);" class="spe" onclick="itemBatchRemove()">删除选中的商品</a>
            <a href="javascript:void(0);" class="spe" onclick="itemBatchFollow()">移到我的收藏夹</a>
            <#if cartAvailableVoucherNum?? && cartAvailableVoucherNum == 1>
                <a href="javascript:void(0);" class="spe yhq-button">
                    优惠券
                    <i class="sui-icon icon-tb-fold"></i>
                </a>
                <div class="cg-yhq">
                    <div class="yhq-title">
                        <div class="fl yhq-ti">优惠券</div>
                        <div class="sui-icon icon-tb-close fr"></div>
                    </div>
                    <div class="yhq-main">
                        <ul class="yhq-common" id="cartVoucherInfo">

                        </ul>
                    </div>
                    <div class="yhq-san">

                    </div>
                </div>
                <div id="overlay"></div>
            </#if>
        </div>

        <div class="acol6 fr">
            <#if cartInfo.payAmount?? && (cartInfo.payAmount <= 0) >
                <span  style="display: block;width:142px;height:46px;background:rgba(169,174,183,1);text-align: center;margin-right: 17px;color:#fff;font-size: 16px;margin-top: 15px;line-height: 46px;">结算</span>
            <#elseif cartInfo.payAmount?? && (cartInfo.payAmount > 0) >
                <#if cartInfo.hasVouchersNotReceived == 1 >
                    <a id="payBtn"  href="javascript:void(0);" class="spe" onclick="action_sub_module_click('领券结算');confim(${cartInfo.selectNum},${cartInfo.canSettle},'${notSubmitOrderOrgIds}')">领券结算</a>
                <#else>
                    <a id="payBtn" href="javascript:void(0);" class="spe" onclick="action_sub_module_click('去结算');confim(${cartInfo.selectNum},${cartInfo.canSettle},'${notSubmitOrderOrgIds}')">去结算</a>
                </#if>
            </#if>
<#--            <#if cartInfo.differenceAmount?? && (cartInfo.differenceAmount > 0) >-->
<#--                <span  style="display: block;width:142px;height:46px;background:rgba(169,174,183,1);text-align: center;margin-right: 17px;color:#fff;font-size: 16px;margin-top: 15px;line-height: 46px;">差${cartInfo.differenceAmount?string("0.00")}起送</span>-->
<#--            <#elseif cartInfo.differenceAmount?? && (cartInfo.differenceAmount == -1 || cartInfo.differenceAmount == 0.00)>-->
<#--                <#if cartInfo.hasVouchersNotReceived == 1 >-->
<#--                    <a  href="javascript:void(0);" class="spe" onclick="confim(${cartInfo.selectNum},${cartInfo.allShipped}, ${cartInfo.allMeetStartPrice})">领券结算</a>-->
<#--                <#else>-->
<#--                    <a  href="javascript:void(0);" class="spe" onclick="confim(${cartInfo.selectNum},${cartInfo.allShipped}, ${cartInfo.allMeetStartPrice})">去结算</a>-->
<#--                </#if>-->
<#--            </#if>-->
        </div>
        <div class="acol5 fr">
            <div style="text-align: right"><div class="heji" style="display: inline-block">合计：</div> <div class="money" style="display: inline-block">￥${cartInfo.payAmount?string("0.00")}</div></div>
            <div>
                <div class="jianshu" style="display: inline-block"><span>已选择</span><span class="red">${cartInfo.selectNum }</span><span>件商品</span></div>
                <div class="youhui" style="display: inline-block">促销减：<span>￥${cartInfo.rePrice?string("0.00")}</span></div>
                <div class="youhui" style="display: inline-block">用券减：<span>￥${cartInfo.voucherDiscountAmount?string("0.00")}</span></div>
                <#if cartInfo.promoAmountDto?? && cartInfo.payAmount?? && (cartInfo.payAmount > 0) >
                <div class="chaozhong youhuidetail">
                    <span class="showdetail" style="cursor: pointer">优惠明细<i class="sui-icon sui-icon icon-touch-chevron-up" style="font-size:12px;"></i></span>
                    <div class="dialog-box">
                        <div class="header-box">
                            <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                            <h4 class="modal-title">优惠明细 <span class="pr-num">*应付实际金额请以结算页为准</span></h4>
                        </div>
                        <div>
                            <div class="line-list">
                                <p class="line-list-d">
                                    <span class="span-left">商品总额</span>
                                    <span class="span-right">¥${ cartInfo.promoAmountDto.subTotal?string("0.00") }</span>
                                </p>
                            </div>
                            <#list cartInfo.promoAmountDto.promoDiscountGroupList as promoList >
                                <div class="line-list <#if !promoList_has_next>noneBottom</#if>">
                                    <p class="line-list-d">
                                        <#if promoList.type == 1>
                                            <span class="span-left">优惠券</span>
                                            <span class="span-right color-red">-¥${ promoList.discountAmount?string("0.00")}</span>
                                        <#else>
                                            <span class="span-left">促销活动</span>
                                            <span class="span-right color-red">-¥${ promoList.discountAmount?string("0.00")}</span>
                                        </#if>
                                    </p>
                                    <#list promoList.promoDiscountDetailList as promoDetailList >
                                        <p  class="line-list-m">
                                            <span class="span-left">${ promoDetailList.name }</span>
                                            <span class="span-right">-¥${ promoDetailList.discountAmount?string("0.00") }</span>
                                        </p>
                                    </#list>
                                </div>
                            </#list>
                        </div>
                        <div class="footer-box">
                            <div class="line-list" style="border-bottom: none">
                                <p class="line-list-d">
                                    <span class="span-left">共优惠</span>
                                    <span class="span-right color-red">-¥${ cartInfo.promoAmountDto.totalDiscount?string("0.00") }</span>
                                </p>
                                <p  class="line-list-m">
                                    <span>以上优惠不包含余额抵扣，请在结算页查看</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                </#if>
            </div>
        </div>
        <div class="acol4 fr">


        </div>
        <!--记得删-->
        <#--  <input type="button" id="fullGiveModalBtn" style="display: block; width: 20px; height: 20px;">
        <input type="button" id="someFullGiveModalBtn" style="display: block; width: 20px; height: 20px;">  -->
        <#--<div class="acol3 fr">
            <span>已选择</span>
            <span class="red">${cartInfo.totalNum}</span>
            <span>件商品</span>
        </div>-->
    </div>
</#if>

<#--<!--确认弹窗&ndash;&gt;-->
<#--<div id="confimModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">-->
<#--    <div class="modal-dialog">-->
<#--        <div class="modal-content">-->
<#--            <div class="modal-header">-->
<#--                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>-->
<#--                <h4 id="myModalLabel" class="modal-title">温馨提示</h4>-->
<#--            </div>-->
<#--            <div class="modal-body">-->
<#--                <div class="spebox speboxCenter">-->
<#--                    <div class="row2">请确认您所加购的商品品种、数量、规格等是否无误，再进行结算。</div>-->
<#--                </div>-->
<#--            </div>-->
<#--            <div class="modal-footer">-->
<#--                <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large sure" >确认结算</button>-->
<#--                <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">我再看看</button>-->
<#--            </div>-->
<#--        </div>-->
<#--    </div>-->
<#--</div>-->

<#--凑单商品弹窗-->
<div id="confimModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">起送包邮金额提醒</h4>
            </div>
            <div class="modal-body">
                <#if (cartInfo != null && cartInfo.unsatisfiedStartPriceList?? && cartInfo.unsatisfiedStartPriceList?size>0) >
                    <div class="myModalTitle colorPink">以下店铺加购商品不够起送门槛，不参与提单</div>
                    <#list cartInfo.unsatisfiedStartPriceList as unsatisfiedStartPriceList >
                        <div class="myModalList colorPink myDiv">
                            <div>
                                <#if (unsatisfiedStartPriceList.isSelfCompany == 1) >
                                    <span class="ziying">自营</span>
                                </#if>
                                <span class="myMidalListTitle">${unsatisfiedStartPriceList.companyName}</span>
                                <a href="${unsatisfiedStartPriceList.freightJumpUrl}" style="color: #00B377;float: right" target="_blank">去凑单<img src="/static/images/right-arrow.png" alt="" style="margin-right: 3px;top: -1px;position: relative;width: 12px;"></a>
                            </div>
                            <div class="myModalTip">${unsatisfiedStartPriceList.freightTips} <span>暂不参与提单</span></div>
                        </div>
                    </#list>
                </#if>
                <#if (cartInfo != null && cartInfo.unsatisfiedFreeShippingList?? && cartInfo.unsatisfiedFreeShippingList?size>0) >
                    <div class="myModalTitle">以下店铺加购商品不够包邮门槛，需另付运费</div>
                    <#list cartInfo.unsatisfiedFreeShippingList as unsatisfiedFreeShippingList >
                        <div class="myModalList">
                            <div>
                                <#if (unsatisfiedFreeShippingList.isSelfCompany == 1) >
                                    <span class="ziying">自营</span>
                                </#if>
                                <span class="myMidalListTitle">${unsatisfiedFreeShippingList.companyName}</span>
                                <a href="${unsatisfiedFreeShippingList.freightJumpUrl}" style="color: #00B377;float: right" target="_blank">去凑单<img src="/static/images/right-arrow.png" alt="" style="margin-right: 3px;top: -1px;position: relative;width: 12px;"></a>
                            </div>
                            <div class="myModalTip">${unsatisfiedFreeShippingList.freightTips} <span>${unsatisfiedFreeShippingList.freightPriceTag}</span></div>
                        </div>
                    </#list>
                </#if>
<#--                <div class="spebox speboxCenter">-->
<#--                    <div class="row2">请确认您所加购的商品品种、数量、规格等是否无误，再进行结算。</div>-->
<#--                </div>-->
            </div>
            <div class="modal-footer">
                <#if cartInfo.canSettle == 3>
                    <button type="button" data-dismiss="modal" style="width: auto;margin-right: 10px" class="sui-btn btn-primary btn-large">继续加购</button>
                <#elseif cartInfo.canSettle == 2>
                    <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">继续加购</button>
                    <button type="button" data-ok="modal" style="width: auto;margin-right: 10px" class="sui-btn btn-primary btn-large sure" >暂不凑单，去结算</button>
                </#if>
            </div>
        </div>
    </div>
</div>

<!--提示弹窗-->
<div id="infoModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4  class="modal-title">提示</h4>
            </div>
            <div class="modal-body">
                <div class="tishibox">
                    您采购的商品A、B、C库存发生变化或已下架，系统已为您重置！
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large" >确定</button>
                <!--<button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>-->
            </div>
        </div>
    </div>
</div>

<!--删除弹窗-->
<div id="delModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">提示</h4>
            </div>
            <div class="modal-body">
                <div class="spebox">
                    <div class="row1"><i class='sui-icon icon-tb-warnfill'></i>确定删除该商品？</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large">确定</button>
                <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
            </div>
        </div>
    </div>
</div>

<div id="delTaoCanModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">提示</h4>
            </div>
            <div class="modal-body">
                <div class="spebox">
                    <div class="row1"><i class='sui-icon icon-tb-warnfill'></i>确认删除该套餐吗？</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large">确定</button>
                <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
            </div>
        </div>
    </div>
</div>

<!--配送服务费说明弹窗-->
<div id="fuwu" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4  class="modal-title" style="text-align: center;">配送服务收费标准</h4>
            </div>
            <div class="modal-body">
                <div class="xzmain freight">
                    <#--                            <p class="xz-title">收费说明：</p>-->
                    <#--                            <div class="fee-info-one"></div>-->
                    <#--                            <div class="fee-info-two"></div>-->

                    <#--                            <p class="xz-title" style="margin-top: 20px;">退款说明：</p>-->
                    <#--                            <p class="xz-info">1、申请退款时，若订单状态为“订单审核中”且申请整单退款或部分退款最终整单退款，此时支持退运费；若订单状态为“订单审核中”申请部分退款，此时不支持退运费；</p>-->
                    <#--                            <p class="xz-info">2、申请退款时，若订单状态为“订单出库中”且申请整单退款，支持退运费</p>-->
                    <#--                            <p class="xz-info">3、申请退款时，若订单状态为“配送中”和“已完成”，此时无论是申请的整单退款还是部分退款，均不支持退运费。</p><br>-->
                </div>
            </div>

        </div>
    </div>
</div>
<!--满赠凑单弹窗-->
<div id="fullGiveModal" style="display: none;">
    <input id="promoId" type="hidden" value=""></input>
    <input id="bizSource" type="hidden" value="${cartInfo.bizSource}">
    <div class="i-dialog-mask"></div>
    <div class="i-dialog-box">
        <div class="i-title">
            <div style="overflow-x: hidden;margin-right: 15px;">
                <#if cartInfo.needToBePerfectedActList?? && (cartInfo.needToBePerfectedActList?size>1) >
                    <div id="mz-actList" class="i-actList" style="padding: 20px 0;display: flex;">
                        <#list cartInfo.needToBePerfectedActList as actItem>
                            <div>
                                <div id="act-${actItem.promoId}" style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;" title="${actItem.labTitle}" onclick="fullNumSelectGive(${cartInfo.bizSource},'0',${actItem.giftPoolActTotalSelectedNum},${actItem.promoId})">${actItem.labTitle}</div>
                                <div id="actStatus-${actItem.promoId}" class="i-actStatus" style="color:#26bb53;">已选</div>
                            </div>
                        </#list>
                    </div>
                </#if>
                <div id="mz-title" style="padding: 10px 0;">
                    <span style="font-size: 14px;">选择赠品</span>
                </div>
            </div>
            <div onclick="fullGiveClose()">
                <svg style="cursor: pointer;" fill="#8f8f8f" focusable="false" class="" data-icon="close" width="1em" height="1em" fill="currentColor" aria-hidden="true" fill-rule="evenodd" viewBox="64 64 896 896"><path d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"></path></svg>
            </div>
        </div>
        <div class="i-content">
            <p style="margin: 10px 0;display: flex;justify-content: space-between;align-items: center;padding: 0 15px;">
                <span style="font-weight: 600;">
                    <span>可选择</span>
                    <span id="canSelectNum" style="color: #22b14c;">
                    </span>

                    <span>盒赠品，当前已选择</span>
                    <span id="selectedNum" style="color: #22b14c;">
                    </span>

                    <span>盒</span>
                </span>
                <span  onclick="abandonGift()">
                    <input id="abandon" type="checkbox" name="abandon">
                    <label for="abandon" style="cursor: pointer;user-select: none;">放弃当前赠品</label>
                </span>
            </p>
            <div id="productList">
            </div>
        </div>
        <div id="mz-normal" class="i-footer" style="justify-content:flex-end;padding-bottom:10px;">
            <div class="i-close" onclick="fullGiveClose()">取消</div>
            <div class="i-submit" style="margin-left: 5px;" onclick="fullGiveSubmit()">确定</div>
        </div>
        <div id="mz-submit" class="i-footer" style="justify-content:flex-end;display:none;padding-bottom:10px;">
            <div class="i-close" onclick="fullGiveClose()">取消</div>
            <div class="i-submit" style="margin-left: 5px;" onclick="confim(${cartInfo.selectNum},${cartInfo.canSettle},'${notSubmitOrderOrgIds}', true)">确定</div>
        </div>
    </div>
</div>
<#--  <div id="fullGiveModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">选择想要的商品</h4>
            </div>
            <div class="myModalTitle giftTitle">
                <span>
                    可选<span id="totleSelected" style="color:red">n1</span>件，已选<span id="selected" style="color:red">n1</span>件
                </span>
                <span style="float:right">
                    <input type="checkbox" id="abandon" name="abandon">
                    <label for="abandon">放弃当前赠品</label>
                </span>
            </div>
            <div class="modal-body">
                <div id="giftList" class="myModalList giftList">
                    <div class="giftItem">
                        <input type="checkbox" id="xyy1" name="xyy1">
                        <label for="xyy1">
                            <img class="shopJpg" src="./采购单_files/defaultPhoto(1).jpg" alt="">
                        </label>
                        <label class="giftDetail" for="xyy1">
                            <div class="giftName">安乃近<span>/20片</span></div>
                            <div class="giftDate">有效期: <span>2024-09-13</span></div>
                            <div class="giftValue">￥<red>1.00</red>/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>原价：￥10.00</span></div>
                        </label>
                        <div>
                            <div class="giftLimit">限xxx件</div>
                            <div class="giftLimitNum">
                                <a href="javascript:void(0);" class="sub fl">-</a>
                                <input class="fl giftNum" type="text" value="1" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,&#39;&#39;)}else{this.value=this.value.replace(/\D/g,&#39;&#39;)}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,&#39;&#39;)}else{this.value=this.value.replace(/\D/g,&#39;&#39;)}" onblur="blurCartNum(this)">
                                <a href="javascript:void(0);" class="add fl">+</a>
                            </div>
                        </div>
                    </div>
                    <div class="giftItem">
                        <input type="checkbox" id="xyy2" name="xyy2">
                        <label for="xyy2">
                            <img class="shopJpg" src="./采购单_files/defaultPhoto(1).jpg" alt="">
                        </label>
                        <label class="giftDetail" for="xyy2">
                            <div class="giftName">布洛芬<span>/20片</span></div>
                            <div class="giftDate">有效期: <span>2024-09-13</span></div>
                            <div class="giftValue">￥<red>1.00</red>/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>原价：￥10.00</span></div>
                        </label>
                        <div>
                            <div class="giftLimit">限xxx件</div>
                            <div class="giftLimitNum">
                                <a href="javascript:void(0);" class="sub fl">-</a>
                                <input class="fl giftNum" type="text" value="1" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,&#39;&#39;)}else{this.value=this.value.replace(/\D/g,&#39;&#39;)}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,&#39;&#39;)}else{this.value=this.value.replace(/\D/g,&#39;&#39;)}" onblur="blurCartNum(this)">
                                <a href="javascript:void(0);" class="add fl">+</a>
                            </div>
                        </div>
                    </div>
                    <div class="giftItem sellOut">
                        <input type="checkbox" id="xyy3" name="xyy3" disabled="">
                        <label for="xyy3">
                            <img class="shopJpg" src="./采购单_files/defaultPhoto(1).jpg" alt="">
                            <div class="sellOut">已售罄</div>
                        </label>
                        <label class="giftDetail" for="xyy3">
                            <div class="giftName">头孢<span>/20片</span></div>
                            <div class="giftDate">有效期: <span>2024-09-13</span></div>
                            <div class="giftValue">￥<red>1.00</red>/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>原价：￥10.00</span></div>
                        </label>
                        <div>
                            <div class="giftMsg">商品已售罄</div>
                        </div>
                    </div>
                    <div class="giftItem sellOut">
                        <input type="checkbox" id="xyy4" name="xyy4" disabled="">
                        <label for="xyy4">
                            <img class="shopJpg" src="./采购单_files/defaultPhoto(1).jpg" alt="">
                            <div class="sellOut">已售罄</div>
                        </label>
                        <label class="giftDetail" for="xyy4">
                            <div class="giftName">头孢<span>/20片</span></div>
                            <div class="giftDate">有效期: <span>2024-09-13</span></div>
                            <div class="giftValue">￥<red>1.00</red>/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>原价：￥10.00</span></div>
                        </label>
                        <div>
                            <div class="giftMsg">商品已售罄</div>
                        </div>
                    </div>
                    <div class="giftItem sellOut">
                        <input type="checkbox" id="xyy5" name="xyy5" disabled="">
                        <label for="xyy5">
                            <img class="shopJpg" src="./采购单_files/defaultPhoto(1).jpg" alt="">
                            <div class="sellOut">已售罄</div>
                        </label>
                        <label class="giftDetail" for="xyy5">
                            <div class="giftName">头孢<span>/20片</span></div>
                            <div class="giftDate">有效期: <span>2024-09-13</span></div>
                            <div class="giftValue">￥<red>1.00</red>/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>原价：￥10.00</span></div>
                        </label>
                        <div>
                            <div class="giftMsg">商品已售罄</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-ok="modal" class="sui-btn btn-default btn-large">取消</button>
                <button type="button" data-dismiss="modal" class="sui-btn btn-primary btn-large sure">确定</button>
            </div>
        </div>
    </div>
</div>  -->
<!--多个满赠活动弹窗-->
<#--  <div id="someFullGiveModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">
                    <div class="ele">
                        <ul class="navBox">
                                <li class="nav active"><div>满赠活动1</div></li>
                            </ul>
                    </div>
                </h4>
            </div>
            <div class="elec" style="display: none;">
                <div class="myModalTitle giftTitle">
                    <span>
                        可选<red>n1</red>件，已选<red>n1</red>件
                    </span>
                    <span style="float:right">
                        <input type="checkbox" id="abandon" name="abandon">
                        <label for="abandon">放弃当前赠品x1</label>
                    </span>
                </div>
                <div class="modal-body">
                    <div class="myModalList giftList">
                        <div class="giftItem">
                            <input type="checkbox" id="xyy1" name="xyy1">
                            <label for="xyy1">
                                <img class="shopJpg" src="./采购单_files/defaultPhoto(1).jpg" alt="">
                            </label>
                            <label class="giftDetail" for="xyy1">
                                <div class="giftName">安乃近<span>/20片</span></div>
                                <div class="giftDate">有效期: <span>2024-09-13</span></div>
                                <div class="giftValue">￥<red>1.00</red>/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>原价：￥10.00</span></div>
                            </label>
                            <div>
                                <div class="giftLimit">限xxx件</div>
                                <div class="giftLimitNum">
                                    <a href="javascript:void(0);" class="sub fl">-</a>
                                    <input class="fl giftNum" type="text" value="1" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,&#39;&#39;)}else{this.value=this.value.replace(/\D/g,&#39;&#39;)}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,&#39;&#39;)}else{this.value=this.value.replace(/\D/g,&#39;&#39;)}" onblur="blurCartNum(this)">
                                    <a href="javascript:void(0);" class="add fl">+</a>
                                </div>
                            </div>
                        </div>
                        <div class="giftItem">
                            <input type="checkbox" id="xyy2" name="xyy2">
                            <label for="xyy2">
                                <img class="shopJpg" src="./采购单_files/defaultPhoto(1).jpg" alt="">
                            </label>
                            <label class="giftDetail" for="xyy2">
                                <div class="giftName">布洛芬<span>/20片</span></div>
                                <div class="giftDate">有效期: <span>2024-09-13</span></div>
                                <div class="giftValue">￥<red>1.00</red>/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>原价：￥10.00</span></div>
                            </label>
                            <div>
                                <div class="giftLimit">限xxx件</div>
                                <div class="giftLimitNum">
                                    <a href="javascript:void(0);" class="sub fl">-</a>
                                    <input class="fl giftNum" type="text" value="1" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,&#39;&#39;)}else{this.value=this.value.replace(/\D/g,&#39;&#39;)}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,&#39;&#39;)}else{this.value=this.value.replace(/\D/g,&#39;&#39;)}" onblur="blurCartNum(this)">
                                    <a href="javascript:void(0);" class="add fl">+</a>
                                </div>
                            </div>
                        </div>
                        <div class="giftItem sellOut">
                            <input type="checkbox" id="xyy3" name="xyy3" disabled="">
                            <label for="xyy3">
                                <img class="shopJpg" src="./采购单_files/defaultPhoto(1).jpg" alt="">
                                <div class="sellOut">已售罄</div>
                            </label>
                            <label class="giftDetail" for="xyy3">
                                <div class="giftName">头孢<span>/20片</span></div>
                                <div class="giftDate">有效期: <span>2024-09-13</span></div>
                                <div class="giftValue">￥<red>1.00</red>/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>原价：￥10.00</span></div>
                            </label>
                            <div>
                                <div class="giftMsg">商品已售罄</div>
                            </div>
                        </div>
                        <div class="giftItem sellOut">
                            <input type="checkbox" id="xyy4" name="xyy4" disabled="">
                            <label for="xyy4">
                                <img class="shopJpg" src="./采购单_files/defaultPhoto(1).jpg" alt="">
                                <div class="sellOut">已售罄</div>
                            </label>
                            <label class="giftDetail" for="xyy4">
                                <div class="giftName">头孢<span>/20片</span></div>
                                <div class="giftDate">有效期: <span>2024-09-13</span></div>
                                <div class="giftValue">￥<red>1.00</red>/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>原价：￥10.00</span></div>
                            </label>
                            <div>
                                <div class="giftMsg">商品已售罄</div>
                            </div>
                        </div>
                        <div class="giftItem sellOut">
                            <input type="checkbox" id="xyy5" name="xyy5" disabled="">
                            <label for="xyy5">
                                <img class="shopJpg" src="./采购单_files/defaultPhoto(1).jpg" alt="">
                                <div class="sellOut">已售罄</div>
                            </label>
                            <label class="giftDetail" for="xyy5">
                                <div class="giftName">头孢<span>/20片</span></div>
                                <div class="giftDate">有效期: <span>2024-09-13</span></div>
                                <div class="giftValue">￥<red>1.00</red>/盒&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span>原价：￥10.00</span></div>
                            </label>
                            <div>
                                <div class="giftMsg">商品已售罄</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-ok="modal" class="sui-btn btn-default btn-large">取消</button>
                <button type="button" data-dismiss="modal" class="sui-btn btn-primary btn-large sure">确定</button>
            </div>
        </div>
    </div>
</div>  -->
<!--收藏弹窗-->
<div id="followModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">收藏</h4>
            </div>
            <div class="modal-body">
                <div class="spebox">
                    <div class="row1" style="font-size: 20px"><i class='sui-icon icon-tb-warnfill'></i>确定将选中商品移入收藏夹？</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large">确定</button>
                <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
            </div>
        </div>
    </div>
</div>
<link rel="stylesheet" type="text/css" href="${ctx}/static/css/lib.css" />
<script type="text/javascript" src="${ctx}/static/js/cart/list.js?t=${t_v}"></script>
<script type="text/javascript" src="${ctx}/static/js/cart/shop_cart.js?t=${t_v}"></script>
<script>
    <#-- 勾选，修改数量时 页面不闪 -->
    initpositon();
</script>
<script>
    /**超看超重商品**/
    $(".dialog-box").hide();
    $(".seeProduct").click(function(){
        getFreightBlacklist();
        $(".zhongdiv .dialog-box").slideDown();
        $(".zhongdiv .seeProduct>i").removeClass("icon-touch-chevron-up").addClass("icon-touch-chevron-down")
    })
    $(".header-box .sui-close").click(function(){
        $(".zhongdiv .dialog-box").slideUp();
        $(".zhongdiv .seeProduct i").removeClass("icon-touch-chevron-down").addClass("icon-touch-chevron-up")
    })
    /**优惠明细**/
    $(".showdetail").click(function(){
        if($(".youhuidetail .showdetail>i").hasClass('icon-touch-chevron-down')){
            $(".youhuidetail .dialog-box").slideUp();
            $(".youhuidetail .showdetail i").removeClass("icon-touch-chevron-down").addClass("icon-touch-chevron-up")
        }else{
            $(".youhuidetail .dialog-box").slideDown();
            $(".youhuidetail .showdetail>i").removeClass("icon-touch-chevron-up").addClass("icon-touch-chevron-down")
        }
    })
    $(".header-box .sui-close").click(function(){
        $(".youhuidetail .dialog-box").slideUp();
        $(".youhuidetail .showdetail i").removeClass("icon-touch-chevron-down").addClass("icon-touch-chevron-up")
    })
    /**运费提示**/
    $(".get-fei").click(function(){
        var shopCode = $("#freightShopCode").val();
        $.ajax({
            url: "/merchant/freight/query?shopCode="+shopCode,
            type: "POST",
            success: function(result){
                if(result){
                    $("#fuwu").modal();
                    ele = '';
                    if(result.freightTemplateTipsList.length){
                        result.freightTemplateTipsList.forEach(function(item,index){
                            ele += '<p class="xz-title">' +  item.title +'</p>'
                            item.freightTips.forEach(function(item2,index2){
                                ele += '<p class="xz-info">' +  item2 +'</p>'
                            })
                        })
                    }
                    ele += '<br>';
                    $(".freight").html(ele);
                }else{
                    $.alert(result.errorMsg);
                }
            }
        })
    })
    $('#J_set').tooltip({
        animation: false,
        // type: 'confirm',
        // okHide: function(){alert('okHide');console.log(this);this.tooltip('hide')},
        // hide: function(){console.log(this)}
    })
    $(document).click(function(e){
        var con = $(".cg-yhq-new");
        var isVisible = 0;
        if(!con.is(e.target) && con.has(e.target).length === 0){
            //判断是否有打开的优惠券弹窗
            /**for(var i=0; i<con.length;i++){
              if(con[i].getAttribute('style')){
                  isVisible = 1;
              }
            }
            if(isVisible){
                window.location.reload();
            } */
            $(".cg-yhq-new").slideUp();
        }
    });

    function getFreightBlacklist(){
        $.ajax({
            url: "/merchant/freight/queryFreightBlacklist",
            type: "POST",
            success: function(result){
                if(result){
                    $("#specialProduct").html("");
                    var chaozhongSize = 0;
                    for (var i in result.data) {
                        chaozhongSize++;
                        var sb = '<li class="lib1">';
                        sb += '<div class="l-box fl">';
                        sb += '<img id="dt_408421" src="${productImageUrl }/ybm/product/min/'+result.data[i].imageUrl+'" alt="药帮忙" onerror="this.src=\'/static/images/default-big.png\'">';
                        sb += '</div>';
                        sb += '<div class="r-box fl">';
                        sb += '<div class="lib1-row1">';
                        sb += '<span>'+result.data[i].name+'</span>';
                        sb += '</div>';
                        sb += '<div class="lib1-row2  text-overflow">';
                        sb += '<span class="info">'+result.data[i].spec+'</span>';
                        sb += '</div>';
                        sb += '</div>';
                        sb += '</li>';
                        sb += '<li class="lib3">';
                        sb += '<div class="zkj">￥'+result.data[i].price+'</div>';
                        sb += '</li>';
                        sb += '<li class="lib4">';
                        sb += '<div class="zkj">×'+result.data[i].amount+'</div>';
                        sb += '</li>';
                        sb += '<li class="lib6">';
                        sb += ' <div class="zkj">￥'+result.data[i].subtotal+'</div>';
                        sb += '</li>';
                        $("#specialProduct").append(sb);
                    }
                    $("#chaozhongSize").text(chaozhongSize);
                }
            }
        })
    }
    function initTimers() {
        const timeList = document.querySelectorAll('.add_subsidy')
        timeList.forEach(container => {
            const endTime = new Date().getTime() + parseInt(container.getAttribute('data-timer'), 10) - 1000;
            if (container.initialized) return;
            const elements = {
                h: container.querySelector('.timer-hour'),
                m: container.querySelector('.timer-minute'),
                s: container.querySelector('.timer-second')
            };
            // 更新函数
            const update = () => {
                const remaining = endTime - Date.now();
                if (remaining > 0) {
                    var hh = String(parseInt(remaining / 3600000 % 24, 10)).padStart(2, '0');
                    var mm = String(parseInt(remaining / 60000 % 60, 10)).padStart(2, '0');
                    var ss = String(parseInt(remaining / 1000 % 60, 10)).padStart(2, '0');
                    elements.h.textContent = hh;
                    elements.m.textContent = mm;
                    elements.s.textContent = ss;
                } else {
                    clearInterval(interval);
                    elements.h.textContent = elements.m.textContent = elements.s.textContent = '00';
                }
            };
            const interval = setInterval(update, 1000);
            container.initialized = true
            container.interval = interval 
        })
    }
    initTimers()
</script>
<script>
function action_sub_module_click(text){
    //部最右侧按钮点击
    try{
        aplus_queue.push({
        'action': 'aplus.record',
        'arguments': ['action_sub_module_click', 'CLK',{
            spm_cnt:"<EMAIL>@5."+window.getSpmE(),
            scm_cnt:"pcFE.0.all_0.text-"+text+'.'+window.scmEShopDetail(14)
        }]
	});
    }catch(e){}
}
function action_sub_module_click_v2(text,shopUrl,shopCardIndex,bntIndex){
       try{
            aplus_queue.push({
            'action': 'aplus.record',
            'arguments': ['action_sub_module_click', 'CLK',{
                spm_cnt:"<EMAIL>@"+(shopCardIndex+1)+"_btn@"+bntIndex+"."+window.getSpmE(),
                scm_cnt:"order.0.all_0.shop-"+shopUrl+"_text-"+text.replace(/[._\-~|@]/g, "")+'.'+window.scmEShopDetail(14) 
            }]
        });
       }catch(e){}
}
function isElementInViewport($element) {
  try{
      const elementTop = $element.offset().top; // 元素距离顶部的偏移量
    const elementBottom = elementTop + $element.outerHeight(); // 元素底部距离顶部的偏移量
    const viewportTop = $(window).scrollTop(); // 视口顶部位置
    const viewportBottom = viewportTop + $(window).height(); // 视口底部位置

    return elementBottom > viewportTop && elementTop < viewportBottom;
  }catch(e){}
}
function exposeShop(){
    try{
        $(".list-shop-card-qt").each(function(){
            if(isElementInViewport($(this))){
                let data=$(this).attr("qtData").split(",");
                if(data.length==8&&!data[5]){
                        aplus_queue.push({
                        'action': 'aplus.record',
                        'arguments': ['page_list_product_exposure', 'EXP',{
                            spm_cnt:"<EMAIL>@"+(Number(data[0])+1)+"_prodGroup@"+(Number(data[1])+1)+"_prod@"+( Number(data[2] )+1)+"."+window.getSpmE(),
                            scm_cnt:"order.0.all_0.shop-"+data[4]+"_prod-"+data[3]+'.'+data[6],
                            product_id:Number(data[3]),
                            product_name:data[7]
                        }]
                    });
                    $(this).removeClass("list-shop-card-qt");
                }                
            }     
        });
    }catch(e){

    }
}
function listShopCardQtClick(e){
    try{
        var ancestorElement = $(e).closest('[qtData]')
        if(ancestorElement.length){
            let data= ancestorElement.attr('qtData').split(",");
            if(data.length==8&&!data[5]){
                    aplus_queue.push({
                    'action': 'aplus.record',
                    'arguments': ['action_list_product_click', 'CLK',{
                        spm_cnt:"<EMAIL>@"+(Number(data[0])+1)+"_prodGroup@"+(Number(data[1])+1)+"_prod@"+( Number(data[2] )+1)+"."+window.getSpmE(),
                        scm_cnt:"order.0.all_0.shop-"+data[4]+"_prod-"+data[3]+'.'+data[6]+window.scmEShopDetail(6),
                        product_id:Number(data[3]),
                        product_name:data[7]
                    }]
                });
            } 
        }                 
    }catch(e){
        console.log(e)
    }
}
//商品按钮点击
function shopBottomClick(e,text,index){
     try{
        var ancestorElement = $(e).closest('[qtData]')
        if(ancestorElement.length){
            let data= ancestorElement.attr('qtData').split(",");
            if(data.length==8&&!data[5]){
                let scmE=data[6]+window.scmEShopDetail(6)
                //商品按钮点击
                let qtdata={
                     spm_cnt:"<EMAIL>@"+(Number(data[0])+1)+"_prodGroup@"+(Number(data[1])+1)+"_prod@"+( Number(data[2] )+1)+"_btn@"+index+"."+window.getSpmE(),
                     scm_cnt:"order.0.all_0.shop-"+data[4]+"_prod-"+data[3]+"_text-"+text+'.'+scmE,
                }
                    aplus_queue.push({
                        'action': 'aplus.record',
                        'arguments': ['action_product_button_click', 'CLK',{
                            spm_cnt: qtdata.spm_cnt,
                            scm_cnt: qtdata.scm_cnt,
                            product_id:Number(data[3]),
                            product_name:data[7]
                        }]
                    });
                    window.qtdata=qtdata
                //商品点击
                    aplus_queue.push({
                    'action': 'aplus.record',
                    'arguments': ['action_list_product_click', 'CLK',{
                        spm_cnt:"<EMAIL>@"+(Number(data[0])+1)+"_prodGroup@"+(Number(data[1])+1)+"_prod@"+( Number(data[2] )+1)+"."+window.getSpmE(),
                        scm_cnt:"order.0.all_0.shop-"+data[4]+"_prod-"+data[3]+'.'+scmE,
                        product_id:Number(data[3]),
                        product_name:data[7]
                    }]
                });
            } 
        }                 
    }catch(e){
        console.log(e)
    }
}
$(function(){
    //商品曝光
     document.addEventListener('scroll',exposeShop);  
     exposeShop()
    
})
</script>
