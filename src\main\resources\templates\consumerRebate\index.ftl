<!DOCTYPE HTML>
<html>
<head>
<#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>消费返活动</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="/static/css/user.css?t=${t_v}" />
    <link rel="stylesheet" href="/static/css/events-20181018-lt-sjlb.css?t=${t_v}" />
    <script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/company/index.js"></script>
    <script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/sku_search.js?t=${t_v}"></script>
     <script type="text/javascript">
        var ctx="${ctx}";
    </script>
</head>

<body>
    <div class="container">
        <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
        <input type="hidden" id="merchant" name="merchant" value="${merchant}"/>
        <input type="hidden" id="isBuyCompany" value="${isBuyCompany}">
        <!--头部导航区域开始-->
        <div class="headerBox" id="headerBox">
            <#include "/common/header.ftl" />
        </div>
        <!--主体部分开始-->
        <iframe id="intelligent-frame" src="/newstatic/#/consumerRebatePC/index" width="100%" height="800px"></iframe>
        <!--主体部分结束-->
    </div>
    <!--头部导航区域结束-->

</body>
<style>
    #intelligent-frame{
        border: none;
    }
</style>
<script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
<script type="text/javascript">
function callFromChild() {
    window.location.href='/'
}
  function adjustIframeSize() {
    var iframe = document.getElementById('intelligent-frame');
    var iframeDoc = null;
    // Safari兼容处理
    if (iframe.contentDocument) {
      iframeDoc = iframe.contentDocument;
    } else if (iframe.contentWindow && iframe.contentWindow.document) {
      iframeDoc = iframe.contentWindow.document;
    } else {
      // fallback
      iframeDoc = window.frames[iframe.name] ? window.frames[iframe.name].document : null;
    }
    if (!iframeDoc) return;
    var body = iframeDoc.body;
    var html = iframeDoc.documentElement;

    // 获取iframe内容的实际尺寸
    var iframeHeight = Math.max(
      body ? body.scrollHeight : 0,
      body ? body.offsetHeight : 0,
      html ? html.clientHeight : 0,
      html ? html.scrollHeight : 0,
      html ? html.offsetHeight : 0
    );

    // 设置iframe的高度
    iframe.style.height = iframeHeight + 'px';
  }
 
  // 监听iframe加载完成事件
  document.getElementById('intelligent-frame').onload = function() {
    adjustIframeSize();
  };

</script>