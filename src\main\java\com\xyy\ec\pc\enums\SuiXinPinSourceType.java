package com.xyy.ec.pc.enums;

public enum SuiXinPinSourceType {
    /**
     * 策略推荐
     */
    STRATEGY_REC(1, "策略推荐"),
    /**
     * 白名单数据
     */
    WHITE_LIST(2, "白名单数据");

    private Integer value;
    private String desc;

    SuiXinPinSourceType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static SuiXinPinSourceType getByValue(Integer value) {
        SuiXinPinSourceType[] suiXinPinSourceTypes = values();
        int length = suiXinPinSourceTypes.length;

        for (int i = 0; i < length; i++) {
            SuiXinPinSourceType suiXinPinSourceType = suiXinPinSourceTypes[i];
            if (value.equals(suiXinPinSourceType.value)) {
                return suiXinPinSourceType;
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
