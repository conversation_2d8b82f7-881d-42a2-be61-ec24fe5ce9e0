package com.xyy.ec.pc.controller;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.service.PinganLoanService;
import com.xyy.saas.payment.cores.param.PinganLoanDetailVo;
import com.xyy.saas.payment.cores.param.PinganLoanMyLoanVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 平安还款计划
 */
@Slf4j
@Controller
@RequestMapping("/pc/pinganLoan/")
public class PingAnLoanController extends BaseController {
    @Autowired
    private PinganLoanService pinganLoanService;

    /**
     * 我的平安贷页面
     */
    @ResponseBody
    @PostMapping(value = "/my_loan")
    public Object myLoan(@RequestBody PinganLoanMyLoanVo pinganLoanMyLoanVo) {
        try {
            return addResult("data", pinganLoanService.myLoan(pinganLoanMyLoanVo));
        } catch (Exception e) {
            log.error("pinganloan.myLoan error pinganLoanMyLoanVo:{},error={},e={}", JSON.toJSONString(pinganLoanMyLoanVo), e.getMessage(), e);
            return addError("我的平安贷列表异常");
        }
    }

    /**
     * 平安贷明细页面
     */
    @ResponseBody
    @PostMapping(value = "/loan_detail")
    public Object loanDetail(@RequestBody PinganLoanDetailVo pinganLoanDetailVo) {
        try {
            return addResult("data", pinganLoanService.loanDetail(pinganLoanDetailVo));
        } catch (Exception e) {
            log.error("pinganloan.loanDetail error pinganLoanDetailVo:{},error={},e={}", JSON.toJSONString(pinganLoanDetailVo), e.getMessage(), e);
            return addError("平安贷明细列表异常");
        }
    }

    /**
     * 我的平安贷明细导出
     */
    @PostMapping("/export")
    public void exportExcel(@RequestParam(value = "billCode") String billCode, @RequestParam(value = "merchantId") Long merchantId, HttpServletResponse response, HttpServletRequest req) {
        pinganLoanService.exportExcel(billCode, merchantId, response, req);
    }

}
















