package com.xyy.ec.pc.authentication.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.utils.PrincipalUtils;
import com.xyy.ec.pc.base.PasswordVerifier;
import com.xyy.ec.pc.exception.AuthenticationException;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/4/11 16:33
 * @File LoginService.class
 * @Software IntelliJ IDEA
 * @Description
 */
@Component
public class LoginService {

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    public JwtPrincipal login(PasswordVerifier passwordVerifier) throws Exception {

        String username = passwordVerifier.getLoginName();
        String password = passwordVerifier.getPassword();
        ApiRPCResult<Long> loginRes = loginAccountApi.login(username, password, 1);
        if (!loginRes.isSuccess()) {
            throw new AuthenticationException(loginRes.getCode(), loginRes.getErrMsg());
        }
        JwtPrincipal jwtPrincipal = new JwtPrincipal();
        jwtPrincipal.setAccountId(loginRes.getData());
        // 填充登录时间、登录设备信息、IP、地址等信息
        PrincipalUtils.initJwtPrincipal(jwtPrincipal);
        return jwtPrincipal;
    }
}