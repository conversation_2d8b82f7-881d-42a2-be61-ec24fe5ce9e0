package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.bussiness.api.SubstituteOrdersBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.SubstituteOrdersBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.api.OrderSproutBusinessApi;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * @program: xyy-ec-pc
 * @description: 代下单授权服务
 * @author: lsq
 * @create: 2019-10-10 19:36
 **/
@RequestMapping("/substitute/order/authorize")
@Controller
public class SubsituteOrderAuthorizeController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SubsituteOrderAuthorizeController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private SubstituteOrdersBusinessApi substituteOrdersBusinessApi;

    @Reference(version = "1.0.0")
    private OrderSproutBusinessApi orderSproutBusinessApi;


    @Autowired
    private Config config;

    /**
     * 个人授权列表申请列表
     *
     * @param authorizeDto
     * @param page
     * @param modelMap
     * @param request
     * @return
     */
    @RequestMapping(value = "/index.htm", method = RequestMethod.GET)
    public Object planningScheduleIndex(SubstituteOrdersBussinessDto authorizeDto, Page page, ModelMap modelMap, HttpServletRequest request) {
        // todo 代下单流程修改
        if(true) {
            return new ModelAndView("/subsituteOrderAuthrize/details.ftl");
        }
        Long merchantId = 0L;
        try {
            int authorizeCount = 0;
            int orderNum = 0;
            MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            try {
                if (merchant == null) {
                    return new ModelAndView(new RedirectView("/login/login.htm",true,false));
                }
                merchantId = merchant.getId();
            } catch (Exception e) {
                LOGGER.error("登录出错，error is {}",e);
            }
            authorizeDto.setMerchantId(merchantId);
            if (authorizeDto.getStartDate() != null) {
                authorizeDto.setStartDate(DateUtils.getEarlyInTheDay(authorizeDto.getStartDate()));
            }

            if (authorizeDto.getEndDate() != null) {
                authorizeDto.setEndDate(DateUtils.getLateInTheDay(authorizeDto.getEndDate()));
            }
            //设置页码和页容量默认值
            Integer pageNum = 1;
            Integer pageSize = 10;
            if (page != null) {
                pageNum = page.getOffset();
                pageSize = page.getLimit();
            }
            PageInfo<SubstituteOrdersBussinessDto> pageInfo = substituteOrdersBusinessApi.getPage(pageNum, pageSize, authorizeDto, null);
            Page<SubstituteOrdersBussinessDto> resultPage = new Page<>();
            resultPage.setRequestUrl(getRequestUrl(request));
            resultPage.setOffset(page.getOffset());
            resultPage.setLimit(page.getLimit());
            resultPage.setRows(pageInfo.getList());
            resultPage.setTotal(pageInfo.getTotal());
            modelMap.put("pager", resultPage);
            modelMap.put("total", pageInfo.getTotal());
            modelMap.put("authorizeDto", authorizeDto);
            try {
                authorizeCount = substituteOrdersBusinessApi.getAuthorizeCount(merchantId, 0, null);
                orderNum = orderSproutBusinessApi.getOrderSproutCountByStatus(merchantId, 1);
            } catch (Exception e) {
                LOGGER.error("查询代下单红点数量错误,error is {}", e);
            }
            modelMap.put("orderNum", orderNum);
            modelMap.put("authorizeNum", authorizeCount);
            modelMap.put("center_menu", "sprout");
            modelMap.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            return "/subsituteOrderAuthrize/list.ftl";
        } catch (Exception e) {
            LOGGER.error("查询代下单申请列表错误,error is {}", e);
            return "/subsituteOrderAuthrize/list.ftl";
        }
    }

    /**
     * 授权详情页面
     *
     * @param authorizeId
     * @param request
     * @param model
     * @return
     */
    @RequestMapping("/detail.htm")
    public ModelAndView toAgreementMerchantDetailPage(Integer authorizeId, HttpServletRequest request, Model model) {
        // todo 代下单流程修改
        if(true) {
            return new ModelAndView("/subsituteOrderAuthrize/details.ftl");
        }
        try {
            Long merchantId = 0L;
            if (null == authorizeId) {
                return new ModelAndView("/subsituteOrderAuthrize/details.ftl");
            }
            try {
                MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
                if (merchant == null) {
                    return new ModelAndView(new RedirectView("/login/login.htm",true,false));
                }
                model.addAttribute("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
                merchantId = merchant.getId();
            } catch (Exception e) {
                LOGGER.error("登录出错，error is {}", e);
            }
            String productImageUrl = config.getProductImagePathUrl();
            SubstituteOrdersBussinessDto dto = substituteOrdersBusinessApi.getAuthorizeDetail(authorizeId, merchantId, null);
            if (null != dto && dto.getReadFlag() == 0) {
                //修改已读
                substituteOrdersBusinessApi.modifyAuthorizeStatus(authorizeId, merchantId, null, 1, null);
            }
            model.addAttribute("info", dto);
            model.addAttribute("productImageUrl", productImageUrl);
            model.addAttribute("center_menu", "sprout");
            return new ModelAndView("/subsituteOrderAuthrize/details.ftl");
        } catch (Exception e) {
            LOGGER.error("查询授权详情信息错误,error is {}", e);
            return new ModelAndView("/subsituteOrderAuthrize/details.ftl");
        }
    }

    /**
     * 同意授权
     *
     * @param aid
     * @return
     */
    @RequestMapping("/apply")
    @ResponseBody
    public Object signAgreement(Integer aid) {
        // todo 代下单流程修改
        if(true) {
            return this.addError("代下单流程修改");
        }
        try {
            if (null == aid) {
                return this.addError("必要的参数不能为空");
            }
            MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            int result = substituteOrdersBusinessApi.modifyAuthorizeStatus(aid, merchant.getId(), 1, null, null);
            if (result >= 1) {
                return this.addResult("data", true);
            }else{
                return this.addError("fail");
            }
        } catch (Exception e) {
            return this.addError("同意授权失败");
        }
    }
}
