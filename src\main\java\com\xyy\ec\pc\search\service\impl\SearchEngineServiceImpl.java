package com.xyy.ec.pc.search.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.aviator.AviatorEvaluator;
import com.xyy.ec.api.rpc.hyperspace.HyperspaceRpc;
import com.xyy.ec.api.service.ProductService;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.utils.JsonUtil;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.SnowGroundTypeEnum;
import com.xyy.ec.pc.enums.SortProperties;
import com.xyy.ec.pc.enums.SuiXinPinSourceType;
import com.xyy.ec.pc.model.order.OrderSettleVo;
import com.xyy.ec.pc.model.sxp.SuiXinPinTopVo;
import com.xyy.ec.pc.recommend.dto.RecommendQtListDataDTO;
import com.xyy.ec.pc.recommend.dto.RecommendQtSkuDataDTO;
import com.xyy.ec.pc.recommend.helpers.RecommendQuickTrackingDataHelper;
import com.xyy.ec.pc.remote.OrderBackendService;
import com.xyy.ec.pc.remote.ProductExhibitionGroupBusinessAdminRemoteService;
import com.xyy.ec.pc.remote.ProductForSearchRemoteService;
import com.xyy.ec.pc.search.config.SearchProperties;
import com.xyy.ec.pc.search.ecp.service.EcpPcSearchService;
import com.xyy.ec.pc.search.ecp.vo.PcSearchProductSSMVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchProductVO;
import com.xyy.ec.pc.search.params.PcSearchProductListQueryParam;
import com.xyy.ec.pc.search.service.SearchEngineService;
import com.xyy.ec.pc.search.service.SkuProductService;
import com.xyy.ec.pc.search.vo.SxpQtDataVo;
import com.xyy.ec.pc.shop.service.ShopService;
import com.xyy.ec.pc.shop.vo.ShopInfoVO;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.product.business.ecp.csutag.dto.TagDTO;
import com.xyy.ec.search.engine.api.EcHotWordsApi;
import com.xyy.ec.search.engine.api.EcSearchApi;
import com.xyy.ec.search.engine.dto.HotWordsDTO;
import com.xyy.ec.search.engine.dto.SearchCsuDTO;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.SearchProductCardPositionTypeEnum;
import com.xyy.ec.search.engine.entity.AggregatedVo;
import com.xyy.ec.search.engine.entity.EcProductVo;
import com.xyy.ec.search.engine.enums.CsuOrder;
import com.xyy.ec.search.engine.enums.EcRankType;
import com.xyy.ec.search.engine.enums.SearchType;
import com.xyy.ec.search.engine.enums.SortOrder;
import com.xyy.ec.search.engine.metadata.IPage;
import com.xyy.ec.search.engine.pagination.Page;
import com.xyy.recommend.ecp.api.EcpRecommendOrderApi;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSceneEnum;
import com.xyy.recommend.ecp.params.EcpRecommendOrderQueryParam;
import com.xyy.recommend.ecp.params.EcpRecommendOrderShopDTO;
import com.xyy.recommend.ecp.result.EcpRecommendOrderResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 描述:
 *
 * <AUTHOR> cao
 * @version V1.0
 * @Descriotion: TODO
 * @create 2020/11/27 20:02
 */
@Service
@Slf4j
public class SearchEngineServiceImpl implements SearchEngineService {


    @Reference(version = "1.0.0")
    private EcSearchApi ecSearchApi;

    @Reference(version = "1.0.0")
    private EcHotWordsApi hotWordsApi;
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SearchProperties searchProperties;

    @Autowired
    private SkuProductService skuProductService;

    @Autowired
    private ProductExhibitionGroupBusinessAdminRemoteService productExhibitionGroupBusinessAdminRemoteService;
    @Reference(version = "1.0.0")
    private EcpRecommendOrderApi ecpRecommendOrderApi;

    @Autowired
    private EcpPcSearchService ecpPcSearchService;

    @Autowired
    private ProductService productService;
    @Autowired
    private HyperspaceRpc hyperspaceRpc;

    @Autowired
    private ProductForSearchRemoteService productForSearchRemoteService;

    @Autowired
    private ShopService shopService;
    @Autowired
    private OrderBackendService orderBackendService;

    @Override
    public IPage<Long> getSearchCsuIdList(Integer pageNum, Integer pageSize, Integer terminalType, PcSearchProductListQueryParam queryParam) {


        IPage<Long> iPage = new Page<>();
        try {
            Page page = new Page(pageNum, pageSize);
            SearchCsuDTO searchCsuDTO = buildSearchCsuDTO(queryParam);
            log.info("getSearchCsuIdList queryParams : {}", JSONObject.toJSONString(searchCsuDTO));
            ApiRPCResult<IPage<Long>> apiRPCResult = ecSearchApi.searchCsuIdListForApp(page, searchCsuDTO);
            // 商品组探活埋点
            productExhibitionGroupBusinessAdminRemoteService.asyncSendExhibitionLiveEventMQForSearch(searchCsuDTO);
            if (apiRPCResult.isSuccess()) {
                iPage = apiRPCResult.getData();
            }
        } catch (Exception e) {
            log.error("SearchEngineService getSearchCsuIdList 查询商品id列表失败", e);
        }
        return iPage;
    }

    @Override
    public AggregatedVo queryForAggregated(PcSearchProductListQueryParam queryParam) {
        SearchCsuDTO searchCsuDTO = buildSearchCsuDTO(queryParam);
        log.info("queryForAggregated queryParams : {}", JSONObject.toJSONString(searchCsuDTO));
        ApiRPCResult<AggregatedVo> apiRPCResult = ecSearchApi.aggregatedForPC(searchCsuDTO);
        if (apiRPCResult.isFail()) {
            log.error("aggregatedForPC fail, queryParam : {}, code : {}, errMsg : {}", JsonUtil.toJson(queryParam), apiRPCResult.getCode(), apiRPCResult.getErrMsg());
            return AggregatedVo.builder().aggregations(Maps.newHashMap()).build();
        }
        return apiRPCResult.getData();
    }

    @Override
    public ApiRPCResult<List<String>> manufacturerList(PcSearchProductListQueryParam queryParam) {
        SearchCsuDTO searchCsuDTO = buildSearchCsuDTO(queryParam);
        log.info("manufacturerList queryParams : {}", JSONObject.toJSONString(searchCsuDTO));
        return ecSearchApi.manufacturerList(searchCsuDTO);
    }

    @Override
    public IPage<EcProductVo> searchForList(Integer pageNum, Integer pageSize, Integer terminalType, PcSearchProductListQueryParam queryParam) {
        IPage<EcProductVo> iPage = new Page<>();
        try {
            Page page = new Page(pageNum, pageSize);
            SearchCsuDTO searchCsuDTO = buildSearchCsuDTO(queryParam);
            //随心拼逻辑判断
            if (SearchUtils.isSuiXinPinSearch(queryParam)) {
                //查询普通品
                searchCsuDTO.setProductType(Arrays.asList(BigInteger.ONE.intValue()));
            }
            log.info("searchForList queryParams : {}", JSONObject.toJSONString(searchCsuDTO));
            ApiRPCResult<IPage<EcProductVo>> apiRPCResult = ecSearchApi.searchListForPC(page, searchCsuDTO);
            if (Objects.nonNull(apiRPCResult) && apiRPCResult.isSuccess()) {
                iPage = apiRPCResult.getData();
                {
                    // 坑位推荐
                    if (Objects.equals(queryParam.getSpFrom(), SnowGroundTypeEnum.SearchSpFrom.SEARCH.getValue()) && Objects.equals(pageNum, 1)) {
                        List<EcProductVo> positionRecommendProducts = this.listPositionRecommendProducts(searchCsuDTO);
                        if (log.isDebugEnabled()) {
                            log.debug("搜索，主搜，获取坑位推荐商品，入参：{}，出参：{}", JSONObject.toJSONString(searchCsuDTO), JSONArray.toJSONString(positionRecommendProducts));
                        }
                        // 组装
                        this.assemblePositionRecommendProduct(queryParam, iPage, positionRecommendProducts);
                    }
                }
                //随心拼搜索，自营商品基于起购数实时过滤
                filterSelfSkuPurchaseNumGreaterThanOne(queryParam, iPage);
            }
        } catch (Exception e) {
            log.error("SearchEngineService searchListForPC 查询商品列表失败", e);
        }
        return iPage;
    }

    @Override
    public IPage<EcProductVo> searchForPage(Integer pageNum, Integer pageSize, Integer terminalType, PcSearchProductListQueryParam queryParam) {
        IPage<EcProductVo> iPage = new Page<>();
        try {
            Page page = new Page(pageNum, pageSize);
            SearchCsuDTO searchCsuDTO = buildSearchCsuDTO(queryParam);
            if (Objects.equals(queryParam.getSpFrom(), SnowGroundTypeEnum.SearchSpFrom.SEARCH.getValue()) && Objects.equals(pageNum, 1)) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("merchantId", searchCsuDTO.getMerchantId());
                Boolean isShowPositionRecommend = (Boolean) AviatorEvaluator.execute(searchProperties.getPositionRecommendABTestExpression(), paramMap, true);
                if (log.isDebugEnabled()) {
                    log.debug("搜索，主搜，获取坑位推荐商品，判断是否灰度，merchantId：{}，isShowPositionRecommend：{}", searchCsuDTO.getMerchantId(), isShowPositionRecommend);
                }
                if (BooleanUtils.isTrue(isShowPositionRecommend)) {
                    // 填充坑位推荐
                    searchCsuDTO.setIsApplyFillPositionRecommend(true);
                }
            }
            log.info("searchForPage queryParams : {}", JSONObject.toJSONString(searchCsuDTO));
            ApiRPCResult<IPage<EcProductVo>> apiRPCResult = ecSearchApi.searchForPCPage(page, searchCsuDTO);
            if (Objects.nonNull(apiRPCResult) && apiRPCResult.isSuccess()) {
                iPage = apiRPCResult.getData();
            }
        } catch (Exception e) {
            log.error("SearchEngineService searchForPCPage 查询商品列表失败", e);
        }
        return iPage;
    }

    /**
     * 过滤自营商品起购数大于1的商品
     *
     * @param queryParam
     * @param iPage
     */
    private void filterSelfSkuPurchaseNumGreaterThanOne(PcSearchProductListQueryParam queryParam, IPage<EcProductVo> iPage) {
        if (SearchUtils.isSuiXinPinSearch(queryParam) && Objects.nonNull(queryParam.getIsThirdCompany()) && queryParam.getIsThirdCompany().equals(BigInteger.ZERO.intValue())) {
            List<Long> skuIdList = Optional.ofNullable(iPage.getRecordList()).orElseGet(() -> Collections.emptyList()).stream().map(EcProductVo::getId).collect(Collectors.toList());
            //查询起购数大于1的商品
            List<Long> purchaseNumGreaterThanOneList = skuProductService.findMinPurchaseNumGreaterThanOne(skuIdList);
            Set<Long> purchaseNumGreaterThanOneSet = Sets.newHashSet(purchaseNumGreaterThanOneList);
            //过滤
            List<EcProductVo> lastEcProductList = Optional.ofNullable(iPage.getRecordList()).orElseGet(() -> Collections.emptyList()).stream().filter(ecProductVo -> !purchaseNumGreaterThanOneSet.contains(ecProductVo.getId())).collect(Collectors.toList());
            iPage.setRecordList(lastEcProductList);
            log.info("随心拼搜索过滤自营品起购数大于1的商品, 搜索返回数据 : {}, 过滤后数据 : {}", skuIdList.size(), lastEcProductList.size());
        }
    }


    /**
     * 获取坑位推荐商品，最多2个。
     *
     * @param searchQueryParam
     * @return
     */
    private List<EcProductVo> listPositionRecommendProducts(SearchCsuDTO searchQueryParam) {
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("merchantId", searchQueryParam.getMerchantId());
            Boolean isShowPositionRecommend = (Boolean) AviatorEvaluator.execute(searchProperties.getPositionRecommendABTestExpression(), paramMap, true);
            if (BooleanUtils.isNotTrue(isShowPositionRecommend)) {
                if (log.isDebugEnabled()) {
                    log.debug("搜索，主搜，获取坑位推荐商品，判断是否灰度，merchantId：{}，isShowPositionRecommend：{}", searchQueryParam.getMerchantId(), isShowPositionRecommend);
                }
                return Lists.newArrayList();
            }
            Page page = new Page(1, 10);
            SearchCsuDTO positionRecommendQueryParam = new SearchCsuDTO();
            BeanUtils.copyProperties(searchQueryParam, positionRecommendQueryParam);
            // 查询坑位推荐品
            positionRecommendQueryParam.setIsPositionRecommend(1);
            // 强制不执行加载更多
            positionRecommendQueryParam.setIsLoadMore(false);
            // 按照商品分降序排序
            positionRecommendQueryParam.setCsuOrder(CsuOrder.SPECIAL_PRODUCT_SCORE);
            positionRecommendQueryParam.setSortOrder(SortOrder.DESC);
            positionRecommendQueryParam.setIsNotApplyNoResultSecondRecall(true);
            positionRecommendQueryParam.setPageNum(1);
            positionRecommendQueryParam.setPageSize(10);
            ApiRPCResult<IPage<EcProductVo>> apiRPCResult = ecSearchApi.searchListForPC(page, positionRecommendQueryParam);
            if (log.isDebugEnabled()) {
                log.debug("搜索，主搜，获取坑位推荐商品，入参：{}，出参：{}", JSONObject.toJSONString(positionRecommendQueryParam), JSONObject.toJSONString(apiRPCResult));
            }
            if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
                log.error("搜索，主搜，获取坑位推荐商品失败，入参：{}，出参：{}", JSONObject.toJSONString(positionRecommendQueryParam), JSONObject.toJSONString(apiRPCResult));
                return Lists.newArrayList();
            }
            if (CollectionUtils.isEmpty(apiRPCResult.getData().getRecordList())) {
                return Lists.newArrayList();
            }
            List<EcProductVo> recordList = apiRPCResult.getData().getRecordList();
            int size = Math.min(recordList.size(), 2);
            return recordList.subList(0, size);
        } catch (Exception e) {
            log.error("搜索，主搜，获取坑位推荐商品失败，入参：{}，异常：", JSONObject.toJSONString(searchQueryParam), e);
            return Lists.newArrayList();
        }
    }

    /**
     * 组装坑位推荐商品
     *
     * @param searchPage
     * @param positionRecommendProducts
     * @param searchQueryParam
     */
    private void assemblePositionRecommendProduct(PcSearchProductListQueryParam searchQueryParam, IPage<EcProductVo> searchPage, List<EcProductVo> positionRecommendProducts) {
        if (CollectionUtils.isEmpty(positionRecommendProducts)) {
            return;
        }
        // 默认值
        if (Objects.isNull(searchPage.getRecordList())) {
            searchPage.setRecordList(Lists.newArrayList());
        }
        List<EcProductVo> searchProducts = searchPage.getRecordList();
        /*
            如果查询商品的前5位存在至少1个坑位推荐商品，则不向查询商品集中加入坑位推荐商品；
            否则从坑位推荐商品集中取2个商品加入查询商品集中，分别放在第二位或者第五位（或者是最后一位）；
         */
        // 前五位是否存在坑位推荐的品
        boolean beforeFiveSearchProductIsExistsPositionRecommend = false;
        Set<Long> positionRecommendIds = positionRecommendProducts.stream().map(EcProductVo::getId).collect(Collectors.toSet());
        int realBeforeFiveSearchProductSize = Math.min(searchProducts.size(), 5);
        for (int i = 0; i < realBeforeFiveSearchProductSize; i++) {
            if (positionRecommendIds.contains(searchProducts.get(i).getId())) {
                beforeFiveSearchProductIsExistsPositionRecommend = true;
                break;
            }
        }
        if (beforeFiveSearchProductIsExistsPositionRecommend) {
            if (log.isDebugEnabled()) {
                log.debug("搜索，主搜，获取坑位推荐商品，搜索推荐品在查询商品集的前5个内，不加入搜索推荐品，positionRecommendProducts：{}", JSONArray.toJSONString(positionRecommendProducts));
            }
            return;
        }
        // 查询商品集的查询参数的排除商品
        List<String> excludeIdList;
        if (StringUtil.isNotEmpty(searchQueryParam.getExcludeIds())) {
            excludeIdList = Arrays.asList(searchQueryParam.getExcludeIds().split(Constants.COMMA_SEPARATOR_CHAR));
        } else {
            excludeIdList = Lists.newArrayList();
        }
        // 全部的商品
        Map<Long, EcProductVo> csuIdToProductInfoMap = searchProducts.stream().collect(Collectors.toMap(EcProductVo::getId, Function.identity(), (f, s) -> f));
        for (EcProductVo positionRecommendProduct : positionRecommendProducts) {
            if (!csuIdToProductInfoMap.containsKey(positionRecommendProduct.getId())) {
                csuIdToProductInfoMap.put(positionRecommendProduct.getId(), positionRecommendProduct);
            }
        }
        List<Long> searchCsuIdList = searchProducts.stream().map(EcProductVo::getId).collect(Collectors.toList());
        List<Long> positionRecommendCsuIdList = positionRecommendProducts.stream().map(EcProductVo::getId).collect(Collectors.toList());
        for (int i = 1; i <= positionRecommendCsuIdList.size(); i++) {
            Long positionRecommendCsuId = positionRecommendCsuIdList.get(i - 1);
            int indexOf = searchCsuIdList.indexOf(positionRecommendCsuId);
            if (indexOf != -1) {
                searchCsuIdList.remove(indexOf);
            }
            if (i == 1) {
                if (searchCsuIdList.size() >= 2) {
                    searchCsuIdList.add(1, positionRecommendCsuId);
                } else {
                    searchCsuIdList.add(positionRecommendCsuId);
                }
            } else if (i == 2) {
                if (searchCsuIdList.size() >= 5) {
                    searchCsuIdList.add(4, positionRecommendCsuId);
                } else {
                    searchCsuIdList.add(positionRecommendCsuId);
                }
            }
            excludeIdList.add(String.valueOf(positionRecommendCsuId));
        }
        searchPage.setRecordList(searchCsuIdList.stream().map(csuIdToProductInfoMap::get).collect(Collectors.toList()));
        // 将坑位推荐的商品累计到排除的商品参数上。
        searchQueryParam.setExcludeIds(String.join(Constants.COMMA_SEPARATOR_CHAR, excludeIdList.stream().distinct().collect(Collectors.toList())));
    }

    /**
     * 构造搜索条件
     */
    private SearchCsuDTO buildSearchCsuDTO(PcSearchProductListQueryParam queryParam) {
        Assert.notNull(queryParam, "搜索条件不能为空");
        SearchCsuDTO searchCsuDTO = new SearchCsuDTO();
        BeanUtils.copyProperties(queryParam, searchCsuDTO);
        SortProperties sortProperties = SortProperties.DEFAULT;
        try {
            sortProperties = SortProperties.getByProperty(queryParam.getProperty());
        } catch (IllegalArgumentException ex) {
            log.warn("queryParam sort.property : {} invalid, use default sort.", queryParam.getProperty());
        }
        //判断如果是拼团排序，则设置拼团排序类型
        if (sortProperties.getType().equals(SortProperties.ACT_PT_DEFAULT.getType())) {
            searchCsuDTO.setRankType(EcRankType.EC_PT_RANK.getValue());
        }
        searchCsuDTO.setCsuOrder(CsuOrder.valueOf(sortProperties.getType()));
        searchCsuDTO.setSortOrder(queryParam.getDirection().equalsIgnoreCase(SortOrder.DESC.name()) ? SortOrder.DESC : SortOrder.ASC);
        if (StringUtil.isNotEmpty(queryParam.getShopCodes())) {
            searchCsuDTO.setShopCodes(Arrays.asList(queryParam.getShopCodes().split(Constants.COMMA_SEPARATOR_CHAR)));
        }
        //支持规格过滤
        if (StringUtil.isNotEmpty(queryParam.getSpec())) {
            searchCsuDTO.setSpecList(Arrays.asList(queryParam.getSpec().split(Constants.COMMA_SEPARATOR_CHAR)));
        }
        //支持排除商品
        if (StringUtil.isNotEmpty(queryParam.getExcludeIds())) {
            searchCsuDTO.setExcludeIds(Arrays.asList(queryParam.getExcludeIds().split(Constants.COMMA_SEPARATOR_CHAR)));
        }

        // 支持商品类型筛选
        if (StringUtil.isNotEmpty(queryParam.getProductTypes())) {
            List<String> productTypes = StrUtil.split(queryParam.getProductTypes(), Constants.COMMA_SEPARATOR_CHAR);
            if (CollectionUtil.isNotEmpty(productTypes)) {
                List<Integer> productTypeList = productTypes.stream().map(productType -> Integer.valueOf(productType)).collect(Collectors.toList());
                searchCsuDTO.setProductType(productTypeList);
            }
        }
        return searchCsuDTO;
    }

    @Override
    public void saveHistoryWord(Long merchantId, String keyWord) {
        try {
            if (!StringUtils.isEmpty(keyWord)) {
                RpcContext.getContext().setAttachment("async", "true").asyncCall(() -> {
                    try {
                        hotWordsApi.saveHistoryWord(merchantId, keyWord);
                    } catch (Exception e) {
                        log.error("saveHistoryWord error", e);
                    }
                });
                RpcContext.getContext().removeAttachment("async");
            }
        } catch (Exception e) {
            log.error("SearchEngineService saveHistoryWord error", e);
        }
    }

    @Override
    public List<String> getHotSearchWordList(Long merchantId, String branchCode, Integer terminalType, Integer resultType) {
        List<String> wordList = new ArrayList<>();
        try {
            List<HotWordsDTO> list = hotWordsApi.finhotSearchlist(merchantId, terminalType, branchCode);
            if (resultType == SearchType.RECOMMENDATION.getType()) {
                wordList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(list)) {
                    wordList = list.stream().filter(h -> h.getType() != 3).limit(Constants.MAX_HOT_WORD_SIZE).map(h -> h.getKeyword()).collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            log.error("SearchEngineService getHotSearchWordList error", e);
        }
        return wordList;
    }

    @Override
    public List<SuiXinPinTopVo> getAddPurchaseSuiXinPinList(OrderSettleVo order, Integer terminalType, Boolean isSupportOldSxp) {

        if(BooleanUtils.isTrue(isSupportOldSxp)) {
          //  return getAddPurchaseSuiXinPinList(order.getMerchantId(), order.getBranchCode(), order.getShopCode(), terminalType, order.getPid());
            return getAddPurchaseSuiXinPinList(order.getMerchantId(), order.getBranchCode(), order.getShopCode(), terminalType, null);
        }
        EcpRecommendOrderShopDTO shopDTO=  EcpRecommendOrderShopDTO.builder().shopCode(order.getShopCode())
                .csuIds(Arrays.asList(order.getSkuId())).build();
        EcpRecommendOrderQueryParam queryParam=EcpRecommendOrderQueryParam.builder().merchantId(order.getMerchantId())
                .accountId(order.getAccountId())
                .shops(Arrays.asList(shopDTO))
                .terminalType(terminalType)
                .recommendScene(EcpRecommendSceneEnum.ORDER_TO_BE_CONFIRMED_PAGE)
                .isCloseShopCodeFilter(Boolean.TRUE)
                .build();
        ApiRPCResult<EcpRecommendOrderResult> res= ecpRecommendOrderApi.recommend(queryParam);
        log.info("9528-ecpRecommendOrderApi.recommend,queryParam:{},res:{}", JSONObject.toJSONString(queryParam), JSONObject.toJSONString(res));

        if(res != null &&res.isSuccess() &&  res.getData() != null && CollectionUtils.isNotEmpty(res.getData().getCsuIds()) ) {
            List<Long>  skuIds=res.getData().getCsuIds().subList(0, Math.min(8, res.getData().getCsuIds().size()));

            List<PcSearchProductVO>  result=   ecpPcSearchService.getProductInfoForSxp(res.getData().getCsuIds().subList(0, Math.min(8, res.getData().getCsuIds().size())),order.getMerchantId());

            List<PcSearchProductSSMVO> resSSM = RecommendQuickTrackingDataHelper.handleQuickTrackingDataForSSM(result, skuIds, res.getData().getRecommendStrategyCode());

            List<SuiXinPinTopVo> resLists=new ArrayList<>();
            if(result != null &&  !result.isEmpty()) {
                for (PcSearchProductSSMVO vo:resSSM) {
                    List<TagDTO> tags= vo.getTagList().stream().filter(x->x.getName().equals("整单包邮")).collect(Collectors.toList());
                    Integer skuStartNum=null;
                    if(vo.getActPgby() != null) {
                        skuStartNum=vo.getActPgby().getSkuStartNum();
                    }else
                    if(vo.getActPt() != null) {
                        skuStartNum=vo.getActPt().getSkuStartNum();
                    }
                    resLists.add(SuiXinPinTopVo.builder().qtData(vo.getQtData()).skuId(vo.getId()).tag(JSON.toJSONString(CollectionUtils.isNotEmpty(tags) ? tags.get(0): ""))
                            .showName(vo.getShowName()).skuStartNum(skuStartNum).promoTag(vo.getPromoTag()).build());
                }
            }
            return resLists;
        }
        return null;
    }

    @Override
    public List<SuiXinPinTopVo> getAddPurchaseSuiXinPinList(Long merchantId, String branchCode, String shopCode, Integer terminalType, Long pid) {
        Assert.notNull(merchantId, "merchantId不能为空");
        Assert.hasText(branchCode, "branchCode不能为空");
        Assert.hasText(shopCode, "shopCode不能为空");

        LinkedHashMap<Long, SuiXinPinTopVo> suiXinPinTopVoLinkedHashMap = new LinkedHashMap<>();
        List<Long> whiteSkuList = hyperspaceRpc.querySuiXinPinTopSku(merchantId, shopCode);
        //查询店铺白名单列表
        //添加白名单
        Optional.ofNullable(whiteSkuList).orElseGet(() -> Collections.emptyList()).stream().forEach(skuId -> {
            if (!suiXinPinTopVoLinkedHashMap.containsKey(skuId)) {
                suiXinPinTopVoLinkedHashMap.put(skuId, SuiXinPinTopVo.builder().skuId(skuId).sourceType(SuiXinPinSourceType.WHITE_LIST.getValue()).build());
            }
        });
        log.info("随心拼加购推荐-查询白名单 merchantId : {}, shopCode : {}, whiteSkuList : {}", merchantId, shopCode, whiteSkuList);
        //搜索召回随心拼品
        PcSearchProductListQueryParam queryParam = buildSuiXinPinSearchQueryParam(merchantId, branchCode, shopCode);
        IPage<EcProductVo> iPage = searchForPage(BigInteger.ONE.intValue(), Constants.SEARCH_MAX_PAGE_SIZE, terminalType, queryParam);
        Optional.ofNullable(iPage).ifPresent(iPageN -> {
            List<Long> recordList = Optional.ofNullable(iPage.getRecordList()).orElseGet(() -> Collections.emptyList()).stream().map(EcProductVo::getId).collect(Collectors.toList());
            log.info("随心拼加购推荐-搜索召回 skuIdList : {}", recordList);
            Optional.ofNullable(recordList).orElseGet(() -> Collections.emptyList()).forEach(skuId -> {
                if (!suiXinPinTopVoLinkedHashMap.containsKey(skuId)) {
                    suiXinPinTopVoLinkedHashMap.put(skuId, SuiXinPinTopVo.builder().skuId(skuId).sourceType(SuiXinPinSourceType.STRATEGY_REC.getValue()).build());
                }
            });
        });
        //获取所有去重的的KEY集合
        List<Long> skuIdList = new ArrayList<>(suiXinPinTopVoLinkedHashMap.keySet());
        //排除原品ID
        if (Objects.nonNull(pid) && skuIdList.contains(pid)) {
            Integer beforeSize = skuIdList.size();
            skuIdList.remove(pid);
            log.info("随心拼加购推荐商品，排除原品ID : {}, beforeSize : {}, afterSize : {}", pid, beforeSize, skuIdList.size());
        }
        //控销过滤，返回可见可买商品
        List<Long> filterSkuList = productService.findVisibleListForRecommend(skuIdList, branchCode, merchantId);
        if (CollectionUtil.isEmpty(filterSkuList)) {
            log.info("随心拼加购推荐商品，控销后数量 : 0");
            return Collections.emptyList();
        }
        List<Long> resultList = filterSkuList.subList(BigInteger.ZERO.intValue(), filterSkuList.size() > Constants.TOP_EIGHT ? Constants.TOP_EIGHT : filterSkuList.size());
        List<SuiXinPinTopVo> suiXinPinTopVoList = resultList.stream().map(skuId -> suiXinPinTopVoLinkedHashMap.get(skuId)).collect(Collectors.toList());
        log.info("随心拼加购推荐商品，控销前数量 : {}, 控销后数量 : {}, suiXinPinTopVoList : {}", skuIdList.size(), CollectionUtil.isEmpty(filterSkuList) ? 0 : filterSkuList.size(), JSONObject.toJSONString(suiXinPinTopVoList));
        //填充随心拼埋点数据
        List<ProductDTO> productDTOS = productForSearchRemoteService.fillProductInfo(resultList, merchantId, branchCode);
        //查询店铺信息
        ShopInfoVO shopInfo = shopService.getByCode(shopCode);
        fillSuiXinPinExtra(suiXinPinTopVoList);
        fillSxpQtData(productDTOS, suiXinPinTopVoList, shopInfo);
        return suiXinPinTopVoList;
    }

    private void fillSuiXinPinExtra(List<SuiXinPinTopVo> topVos) {
        if (topVos == null || topVos.isEmpty()) {
            return;
        }

        List<Long> skuIds = topVos.stream().map(s -> s.getSkuId()).collect(Collectors.toList());
        Map<Long, String> skuId2promoTag = orderBackendService.queryPromoTag(skuIds);

        // 填充promoTag
        for (SuiXinPinTopVo infoVo : topVos) {
            infoVo.setPromoTag(skuId2promoTag.get(infoVo.getSkuId()));

            // ...填充其他字段
        }

    }

    private void fillSxpQtData(List<ProductDTO> productDTOS, List<SuiXinPinTopVo> suiXinPinTopVoList, ShopInfoVO shopInfo) {
        if (CollectionUtils.isEmpty(productDTOS) || CollectionUtils.isEmpty(suiXinPinTopVoList)) {
            return;
        }
        Map<Long, ProductDTO> productMap = productDTOS.stream().filter(item -> item != null)
                .collect(Collectors.toMap(ProductDTO::getId, item -> item, (first, second) -> first));
        Integer pageNo = NumberUtils.INTEGER_ONE;
        Long pageSize = Long.valueOf(suiXinPinTopVoList.size());
        RecommendQtListDataDTO qtListDataDTO = RecommendQtListDataDTO.builder()
                .result_cnt(Long.valueOf(suiXinPinTopVoList.size()))
                .page_no(pageNo)
                .page_size(suiXinPinTopVoList.size())
                .total_page(pageNo)
                .exp_id(null)
                .key_word(null)
                .build();

        String expId = "";
        String scmId = RandomStringUtils.randomAlphanumeric(8);

        long rankLong = (pageNo - 1) * pageSize;
        int rank = (int) rankLong;
        for (int i = 0; i < suiXinPinTopVoList.size(); i++) {
            SuiXinPinTopVo suiXinPinTopVo = suiXinPinTopVoList.get(i);
            ProductDTO productDTO = productMap.get(suiXinPinTopVo.getSkuId());
            SxpQtDataVo sxpQtDataVo = new SxpQtDataVo();
            sxpQtDataVo.setExpId(expId);
            sxpQtDataVo.setScmId(scmId);
            int cardRank = rank + i + 1;
            RecommendQtSkuDataDTO qtSkuDataDTO = RecommendQtSkuDataDTO.builder()
                    .rank(cardRank)
                    .list_position_type(String.valueOf(SearchProductCardPositionTypeEnum.NORMAL.getType()))
                    .list_position_type_name(SearchProductCardPositionTypeEnum.NORMAL.getName())
                    .product_type(productDTO.getProductType())
                    .product_high_gross(productDTO.getHighGross())
                    .product_category_first_id(productDTO.getCategoryFirstId())
                    .product_category_first_name(productDTO.getCategoryFirstName())
                    .shop_code(productDTO.getShopCode())
                    .shop_name(Objects.nonNull(shopInfo) ? shopInfo.getShowName() : "")
                    .build();
            qtSkuDataDTO.setProduct_labels(getQtSkuDataProductLabels(productDTO));
            sxpQtDataVo.setRank(cardRank);
            sxpQtDataVo.setQtListData(JSON.toJSONString(qtListDataDTO));
            sxpQtDataVo.setQtSkuData(JSONObject.toJSONString(qtSkuDataDTO));
            suiXinPinTopVo.setQtData(sxpQtDataVo);
        }
    }


    private static List<String> getQtSkuDataProductLabels(ProductDTO productInfo) {
        try {
            List<TagDTO> tagList = productInfo.getTagList();
            if (CollectionUtils.isEmpty(tagList)) {
                return null;
            }
            List<String> tagTexts = Lists.newArrayList();
            for (TagDTO productTag : tagList) {
                tagTexts.add(productTag.getText());
            }
            if (CollectionUtils.isNotEmpty(tagTexts)) {
                return tagTexts;
            }
        } catch (Exception e) {
            log.error("埋点标签处理异常，异常信息为：", e);
        }
        return null;
    }



    private PcSearchProductListQueryParam buildSuiXinPinSearchQueryParam(Long merchantId, String branchCode, String shopCode) {
        PcSearchProductListQueryParam queryParam = new PcSearchProductListQueryParam();
        queryParam.setShopCodes(shopCode);
        queryParam.setDisplayCategoryId(Constants.NEGATIVE_ONE);
        queryParam.setTagList(Constants.YBM_ACT_SUI_XIN_PIN);
        queryParam.setType(SearchType.NORMAL_SEARCH.getType());
        queryParam.setMerchantId(merchantId);
        queryParam.setBranchCode(branchCode);
        queryParam.setProductTypes("1");
        return queryParam;
    }
}
