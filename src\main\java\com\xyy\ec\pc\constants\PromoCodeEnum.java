package com.xyy.ec.pc.constants;

/**
 * 优惠券错误码定义常量类
 * <AUTHOR>
 * @date 2019-11-13
 */
public enum PromoCodeEnum {

    SUCCESS(200, "成功"),
    FAIL(9999, "失败"),
    PARAMETER_ERROR(300, "入参错误"),
    TIMEOUT_ERROR(400,"超时错误"),
    RPC_ERROR(401, "网络异常请稍后再试！"),
    RPC_OTHER_EXCEPTION(402, "RPC调用异常"),
    TOKEN_EXCEPTION(403, "token不匹配"),
    URL_ERROR(404,"路径错误"),
    REPEAT_ERROR(500,"请求已经成功，请勿重复请求"),
    METHOD_ERROR(504,"method not found"),
    UNKNOWN(0000,"结果未知"),
    LUCK_DRAW_USED_UP(1001, "抽奖次数已用完"),
    LUCK_DRAW_SHARE_MAX(1002, "已超过最大可获得次数"),
    VOUCHER_TEMPLATE_ERROR(1003,"优惠券模板不存在")

    ;

    int code;
    String msg;

    PromoCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
