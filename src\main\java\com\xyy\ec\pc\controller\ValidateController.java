package com.xyy.ec.pc.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.tencentcloudapi.captcha.v20190722.CaptchaClient;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultRequest;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultResponse;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.MerchantAuthenticationBusinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.account.LoginAccountApi;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.merchant.bussiness.base.ResultMessage;
import com.xyy.ec.merchant.bussiness.dto.SendAuthVerificationBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.account.LoginAccountDto;
import com.xyy.ec.merchant.bussiness.enums.ResultCodeEnum;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.authentication.utils.BaiduMapUtils;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.constants.RedisConstants;
import com.xyy.ec.pc.interceptor.helper.SpiderHelper;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CrawlerUtil;
import com.xyy.ec.pc.util.MobileValidateUtil;
import com.xyy.ec.pc.util.RandomUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.framework.redis.autoconfigure.core.RedisClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;


/**
 * 验证码控制类
 * @ClassName: ValidateController
 * <AUTHOR>
 * @date 2016-5-8 下午11:26:11
 */
@Controller
@RequestMapping("/validate")
public class ValidateController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class);
    public static final String SUCCESS = "success";

//	@Autowired
//	private MerchantService merchantService;
//
//	@Autowired
//	private SMSManager smsManager;
//
//	@Autowired
//	private ALiYunSMSManager aLiYunSMSManager;
//
//	@Reference
//	private MerchantBussinessApi xyyJedisCluster;
//
//	@Autowired
//	private CodeitemServiceImpl codeitemService;

	@Reference(version = "1.0.0")
	private MerchantBussinessApi merchantBussinessApi;

	@Reference(version = "1.0.0")
	private MerchantAuthenticationBusinessApi merchantAuthenticationBusinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

	@Autowired
    private CodeItemServiceRpc codeItemServiceRpc;

	@Reference(version = "1.0.0")
	private LoginAccountApi loginAccountApi;

	@Reference(version = "1.0.0")
	private MerchantLicenseApi licenseApi;

	@Autowired
	private RedisClient redisClient;

	@Resource
	private CrawlerUtil crawlerUtil;
	
	@Autowired
	private SpiderHelper spiderHelper;

	@Autowired
	private BaiduMapUtils baiduMapUtils;

	@Autowired
	private TokenService tokenService;

	/**
	 * 获取验证码
	 * @param mobileNumber
	 * @return
	 */
	@RequestMapping("/getValitionCode.json")
    @ResponseBody
    public Object getValitionCode(String mobileNumber) {
		 //手机号格式校验
        if (StringUtils.isEmpty(mobileNumber)
	           || !MobileValidateUtil.isPass(mobileNumber)
	           ||  mobileNumber.length() != 11) {
            return this.addError("手机号格式有误，请确认后重新填写！");
        }
        String verificationCode = RandomUtil.getRandomCode(4);
        try {

            ResultMessage<?> resultMessage = merchantBussinessApi.sendVerificationCode(mobileNumber,1);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if(resultCode!=ResultCodeEnum.SUCCESS.getCode()){
                return this.addError(msg);
            }
//        	MerchantBussinessDto oldMerchant = merchantService.findMerchantByMobile(mobileNumber);
//        	MerchantBussinessDto oldMerchant = merchantBussinessApi.findMerchantByMobile(mobileNumber);
//    		if (oldMerchant == null) {
//    			return this.addError("Sorry,该手机号还未注册!");
//    		}

//    		this.sendSmsMsg(verificationCode, mobileNumber,"verification_code");

//            xyyJedisCluster.set("verification_code" + mobileNumber, verificationCode);
//			merchantBussinessApi.set("verification_code" + mobileNumber, verificationCode,300);
            return this.addResult(verificationCode);
        } catch (Exception e) {
            LOGGER.error("短信验证码发送失败", e);
            return this.addError("短信验证码发送失败");
        }
    }

	/**
	 * 检验验证码的有效性
	 * @Title: checkValidate
	 * @param mobileNumber
	 * @param code
	 * @return
	 * Object
	 * <AUTHOR>
	 * @date 2017-4-27 上午11:21:03
	 */
	@RequestMapping("/checkValidate.json")
	@ResponseBody
	public Object checkValidate(String mobileNumber,String code) {
		try {
//			String vCode = merchantBussinessApi.get("verification_code"+mobileNumber);
//			if(StringUtil.isEmpty(vCode)){
//				return this.addError("验证码已失效");
//			}
//
//			if(!vCode.equals(code)){
//				return this.addError("请输入正确验证码");
//			}

            ResultMessage<?> resultMessage = merchantBussinessApi.checkVerificationCode(mobileNumber, code);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if(resultCode!=ResultCodeEnum.SUCCESS.getCode()){
                return this.addError(msg);
            }
            return this.addResult();
		} catch (Exception e) {
			LOGGER.error("短信验证码验证异常", e);
            return this.addError("短信验证码验证异常");
		}
	}

	/**
	 * 注册-获取验证码
	 *
	 * @param mobileNumber
	 * @return
	 */
	@RequestMapping("/sendRegisterVerificationCode.json")
	@ResponseBody
	public Object registerSendVerificationCode(@RequestParam("mobileNumber") String mobileNumber, @RequestParam("code") String code) {

		//手机号格式校验
        if (StringUtils.isEmpty(mobileNumber)
	           || !MobileValidateUtil.isPass(mobileNumber)
	           ||  mobileNumber.length() != 11) {
            return this.addError("手机号格式有误，请确认后重新填写！");
        }

        if(StringUtils.isEmpty(code)){
        	return this.addError("验证码不能为空");
		}

		try {
            String result = this.sendSmsMsg(code, mobileNumber);

            if(!SUCCESS.equals(result)){
                return this.addError(result);
            }
//			   String cacheCode = merchantBussinessApi.get("VCODE:"+mobileNumber);
//			   if(StringUtils.isEmpty(cacheCode)){
//		        	return this.addError("验证码已失效，请重新刷新验证码");
//			   }
//
//			   if(!cacheCode.equalsIgnoreCase(code)){
//				   return this.addError("验证码不正确，请重新填写");
//			   }
//
//			   //判断手机号是否已经注册过
////				Merchant tempMerchant = merchantService.findMerchantByMobile(mobileNumber);
//				MerchantBussinessDto tempMerchant = merchantBussinessApi.findMerchantByMobile(mobileNumber);
//				if (null != tempMerchant) {
//					return this.addError("该手机号已经注册，请直接登录");
//				}
//				//判断发送短信是否超过60秒
//				if (merchantBussinessApi.get("register_verification_code_time" + mobileNumber) != null) {
//
//				return this.addError("请不要频繁获取短信验证码，如果您未收到短信验证码，1分钟后在再尝试获取。");
//			}
//			//判断用户短信是否超过15分钟，没超过发送同样的验证码，超过了重新发
//			String verificationCode = merchantBussinessApi.get("register_verification_code" + mobileNumber);
//			if (StringUtil.isNotEmpty(verificationCode)) {
//				//业务字典 1： 代表阿里云 2: 代表华兴
//				this.sendSmsMsg(verificationCode, mobileNumber,"register_verification_code");
//
//				LOGGER.info("【发送注册验证成功~~~~~手机号:" + mobileNumber + "~~~~验证码：" + merchantBussinessApi.get("register_verification_code" + mobileNumber) + "】");
//                merchantBussinessApi.set("register_verification_code_time" + mobileNumber, merchantBussinessApi.get("register_verification_code" + mobileNumber), 60);
//                merchantBussinessApi.set("register_verification_code" + mobileNumber, merchantBussinessApi.get("register_verification_code" + mobileNumber), 15 * 60);
//			} else {
//				//生成4位数验证码
//                String verificationCode = RandomUtil.getRandomCode(4);
////
//				this.sendSmsMsg(verificationCode, mobileNumber,"register_verification_code");
//
//				LOGGER.info("【发送注册验证成功~~~~~手机号:" + mobileNumber + "~~~~验证码：" + verificationCode + "】");
//				//根据用户手机号生成redis的key  存储2条记录  生效时间为60秒和15分钟
//				//60秒的记录用来检测用户是否1分钟内重复点击发送验证码
//				//15分钟记录是短信有效期 15分钟内用户只收到同样的验证码
//                merchantBussinessApi.set("register_verification_code_time" + mobileNumber, verificationCode, 60);
//                merchantBussinessApi.set("register_verification_code" + mobileNumber, verificationCode, 15 * 60);
//			}
			return this.addResult("验证码已发送，请注意查收短信");
		} catch (Exception e) {
			LOGGER.error("短信验证码发送失败", e);
			return this.addError("短信验证码发送失败");
		}
	}


	private String sendSmsMsg(String verificationCode,String mobileNumber) throws Exception {
//            System.err.println("$$$$$$$$$$$$$verificationCode=="+verificationCode);
//		    Map<String,CodeitemBusinessDto> codeItemMap = codeItemServiceRpc.selectByCodemapRTMap(CodeMapConstants.SMS_CODE, null);
//
//        CodeitemBusinessDto codeitem = codeItemMap.get(CodeItemConstants.SMS_CODE);
//		Map<String,String> smsCodeParamMap = new HashMap<String,String>();

//		if(codeitem != null && "1".equals(codeitem.getName())){
//			smsCodeParamMap.put(Constants.CODE, verificationCode);
////        	aLiYunSMSManager.sendSMS(mobileNumber, templateName+"_template_no", smsCodeParamMap,
////        			templateName, new String[]{verificationCode});
//
//		}
//		//发送短信验证码
//		if(codeitem != null && "2".equals(codeitem.getName())){
////			smsManager.sendSMS(mobileNumber, templateName, new String[]{verificationCode});
//        }
//		merchantBussinessApi.pcRegisterSendMsmMsgCode(mobileNumber,verificationCode);

        ResultMessage<?> resultMessage = merchantBussinessApi.pcRegisterSendMsmMsgCode(mobileNumber, verificationCode.toUpperCase());

        int resultCode = resultMessage.getCode();
        String msg = resultMessage.getMsg();
        if(resultCode!=ResultCodeEnum.SUCCESS.getCode()){
            return msg;
        }
        return SUCCESS;
	}

	/**
	 * 认证-获取验证码
	 *
	 * @return
	 */
	@RequestMapping("/sendAuthenticationVerificationCode.json")
	@ResponseBody
	public Object authenticationSendVerificationCode(@RequestParam("mobile") String mobile, @RequestParam("password") String password,
													 @RequestParam("changeFlag") String changeFlag,@RequestParam("merchantId") Long merchantId) {
		//手机号格式校验
		if (StringUtils.isEmpty(mobile)
				|| !MobileValidateUtil.isPass(mobile)
				||  mobile.length() != 11) {
			return this.addError("手机号格式有误，请确认后重新填写！");
		}
		try {
			SendAuthVerificationBusinessDto dto = new SendAuthVerificationBusinessDto();
			dto.setLoginType(SendAuthVerificationBusinessDto.LoginType.PC.getValue());
			dto.setMobile(mobile);
			dto.setMerchantId(merchantId);
			dto.setPassword(password);
			dto.setChangeFlag(changeFlag);
			ResultMessage resultMessage = merchantAuthenticationBusinessApi.sendMsmMsgCodeByMobile(dto);
			int resultCode = resultMessage.getCode();
			String msg = resultMessage.getMsg();
			if(resultCode!=ResultCodeEnum.SUCCESS.getCode()){
				return this.addError(msg);
			}
			return this.addResult("验证码已发送，请注意查收短信");
		} catch (Exception e) {
			LOGGER.error("短信验证码发送失败", e);
			return this.addError("短信验证码发送失败");
		}
	}

	/**
	 * 检验验证码的有效性
	 */
	@RequestMapping("/checkAuthenticationValidate.json")
	@ResponseBody
	public Object checkAuthenticationValidate(String mobileNumber,String code,String ip) {
		try {
            MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			ResultMessage<?> resultMessage = merchantAuthenticationBusinessApi.checkAuthenticationValidate(mobileNumber, code,1,ip,merchant.getId());
			int resultCode = resultMessage.getCode();
			String msg = resultMessage.getMsg();
			if(resultCode!=ResultCodeEnum.SUCCESS.getCode()){
				return this.addError(msg);
			}
			return this.addResult();
		} catch (Exception e) {
			LOGGER.error("短信验证码验证异常", e);
			return this.addError("短信验证码验证异常");
		}
	}

	/**
	 * 滑动验证码校验
	 * @param mobile
	 * @param ticket
	 * @param randStr
	 * @return
	 */
	@RequestMapping("/slideVerification.json")
	@ResponseBody
	public Object slideVerification(HttpServletRequest request,String mobile, String ticket, String randStr) {
		LOGGER.info("滑动验证码校验入参mobile:{},ticket:{},randStr:{}",mobile,ticket,randStr);
		String ip = IPUtils.getClientIP(request);
		MerchantPrincipal merchant = null;
		LOGGER.info("滑动验证码校验ip为mobile:{},ticket:{},randStr:{},ip:{}",mobile,ticket,randStr,ip);
		try {
			merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			//限流判断,如果超过限流次数直接返回失败,提示稍后再试
			ApiRPCResult<Boolean> result =  merchantBussinessApi.slideCountCheck(mobile);
			if (result.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
				return this.addError(result.getErrMsg());
			}
			//滑动验证码校验
			Credential cred = new Credential(Constants.SECRET_ID, Constants.SECRET_KEY);
			HttpProfile httpProfile = new HttpProfile();
			httpProfile.setEndpoint(Constants.ENDPOINT);
			ClientProfile clientProfile = new ClientProfile();
			clientProfile.setHttpProfile(httpProfile);
			CaptchaClient client = new CaptchaClient(cred, Constants.REGION, clientProfile);
			HashMap<String,Object> map = Maps.newHashMap();
			map.put("CaptchaType",Constants.CAPTCHA_TYPE);
			map.put("Ticket",ticket);
			map.put("UserIp",ip);
			map.put("Randstr",randStr);
			map.put("CaptchaAppId",Constants.CAPTCHA_APP_ID);
			map.put("AppSecretKey",Constants.APP_SECRET_KEY);
			DescribeCaptchaResultRequest req = DescribeCaptchaResultRequest.fromJsonString(JSON.toJSONString(map), DescribeCaptchaResultRequest.class);
			DescribeCaptchaResultResponse resp = client.DescribeCaptchaResult(req);
			LOGGER.info("滑动验证码校验结果返回:{}",DescribeCaptchaResultResponse.toJsonString(resp));
			if (resp.getCaptchaCode() !=1) {
				return this.addError(resp.getCaptchaMsg());
			}
			//滑动验证通过将爬虫用户从爬虫系统中移除
			crawlerUtil.delCrawler(merchant.getId(),ip);
			
			return this.addResult();
		} catch (Exception e) {
			LOGGER.error("滑动验证码校验异常", e);
			return this.addError("验证异常");
		}
	}


	/**
	 * 爬虫用户发送短信验证码
	 * @return
	 */
	@RequestMapping("/sendCrawlerCode.json")
	@ResponseBody
	public Object sendCrawlerCode() {
		try {
			MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentAccountPrincipal();
			if (merchant == null) {
				LOGGER.info("爬虫用户发送短信验证码未登录");
				return this.addError("请先登录");
			}
			ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.selectLoginAccountById(merchant.getAccountId());
			if(!apiRPCResult.isSuccess() || Objects.isNull(apiRPCResult.getData())){
				LOGGER.info("爬虫用户发送短信验证码未登录");
				return this.addError("请先登录");
			}
			LoginAccountDto loginAccountDto = apiRPCResult.getData();
			LOGGER.info("爬虫用户发送短信验证码mobile:{}",loginAccountDto.getMobile());
			ApiRPCResult<Boolean> result =  merchantBussinessApi.sendCrawlerCode(loginAccountDto.getMobile());
			LOGGER.info("爬虫用户发送短信验证码结果返回:{},mobile:{}",JSON.toJSONString(result),loginAccountDto.getMobile());
			if (result != null && result.getCode()!=ApiResultCodeEum.SUCCESS.getCode()) {
				return this.addError(result.getErrMsg());
			}
			return this.addResult();
		} catch (Exception e) {
			LOGGER.error("爬虫用户发送短信验证码异常：", e);
			return this.addError("系统异常");
		}
	}

	/**
	 * 爬虫用户短信验证码校验
	 *
	 * @param code
	 * @return
	 */
	@RequestMapping("/checkCrawlerCode.json")
	@ResponseBody
	public Object checkCrawlerCode(HttpServletRequest request, String code) {
		LOGGER.info("爬虫用户短信验证码校验入参code:{}", code);
		try {
			JwtPrincipal merchant = (JwtPrincipal) xyyIndentityValidator.currentAccountPrincipal();
			if (merchant == null) {
				LOGGER.info("爬虫用户短信验证码校验未登录");
				return this.addError("请先登录");
			}

			ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.selectLoginAccountById(merchant.getAccountId());
			if(!apiRPCResult.isSuccess() || Objects.isNull(apiRPCResult.getData())){
				LOGGER.info("爬虫用户发送短信验证码未登录");
				return this.addError("请先登录");
			}
			LoginAccountDto loginAccountDto = apiRPCResult.getData();
			String result = redisClient.get(RedisConstants.CRAWLER_CODE + loginAccountDto.getMobile());
			if (StringUtils.isEmpty(result)) {
				LOGGER.info("爬虫用户短信验证码校验已过期code:{},result:{},mobile:{}", code, result, loginAccountDto.getMobile());
				return this.addError("验证码已过期或不存在,请重新获取");
			}
			if (!code.equals(result)) {
				LOGGER.info("爬虫用户短信验证码校验不匹配code:{},result:{},mobile:{}", code, result, loginAccountDto.getMobile());
				return this.addError("验证码校验不匹配,请重新输入");
			}
			
			if (spiderHelper.getNewSpiderInterceptionOpen()) {
				// 清除爬虫标记
				spiderHelper.removeSpider();
				// 记录验证记录到反爬系统
				spiderHelper.verifyRecord("sms");
			}
			// 清除异地登录标识
			merchant.setIsDifferentPlacesLogin(false);
			tokenService.refreshToken(merchant);
			// 记录ip地址省份到常登录省份
			List<String> oftenLoginProvinces = loginAccountDto.getOftenLoginProvinces();
			if (oftenLoginProvinces == null) {
				oftenLoginProvinces = new ArrayList<>();
			}
			String ipProvince = baiduMapUtils.getIpProvince();
			if (StrUtil.isNotBlank(ipProvince) && !oftenLoginProvinces.contains(ipProvince)) {
				oftenLoginProvinces.add(ipProvince);
			}
			loginAccountDto.setOftenLoginProvinces(oftenLoginProvinces.isEmpty() ? null : oftenLoginProvinces);
			loginAccountApi.updateCheckSwitchOrProvinceById(loginAccountDto);
			return this.addResult();

		} catch (Exception e) {
			LOGGER.error("爬虫用户短信验证码校验异常：", e);
			return this.addError("系统异常");
		}
	}
}
