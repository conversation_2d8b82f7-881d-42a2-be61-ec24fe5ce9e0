package com.xyy.ec.pc.newfront.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.account.WechatLoginApi;
import com.xyy.ec.merchant.bussiness.dto.account.WechatLoginUserDto;
import com.xyy.ec.merchant.bussiness.enums.SiteEnum;
import com.xyy.ec.pc.controller.vo.AccountRegisterVO;
import com.xyy.ec.pc.newfront.dto.ForgetPassLastRespVO;
import com.xyy.ec.pc.newfront.dto.LoginRespVO;
import com.xyy.ec.pc.newfront.dto.RegisterRespVO;
import com.xyy.ec.pc.newfront.dto.WxLoginRespVO;
import com.xyy.ec.pc.newfront.service.LoginNewService;
import com.xyy.ec.pc.newfront.vo.*;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @description: 登录注册 相关modelView拆 ajax接口
 * @date: 2025/5/27 12:39
 * @author: <EMAIL>
 * @version: 1.0
 */
@RestController
@RequestMapping("/new-front/user")
@Slf4j
public class LoginNewController {

    @Resource
    private LoginNewService loginNewService;

    @Reference(version = "1.0.0")
    private WechatLoginApi wechatLoginApi;

    /**
     * 登录接口
     * 验证用户名密码
     * 成功后会区分 跳转 药房页面 还是 选择店铺页面 或是首页
     */
    @PostMapping(value = "/login")
    public AjaxResult<LoginRespVO> login(@RequestBody LoginParamVO loginParamVO, HttpServletRequest request) {
        return loginNewService.login(loginParamVO, request);
    }

    /**
     * 退出
     */
    @GetMapping(value = "/logout")
    public AjaxResult<LoginRespVO> logOut() {
        return loginNewService.logOut();
    }


    /**
     * 得到验证码
     */
    @GetMapping("/code")
    public AjaxResult<String> getCode(HttpServletRequest request, HttpServletResponse response) {
        try {
            return loginNewService.getCode(request, response);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    /**
     * 校验验证码
     */
    @GetMapping("/check-pho-code")
    public AjaxResult<String> checkPhoCode(HttpServletRequest request, HttpServletResponse response) {
        try {
            return loginNewService.checkPhoCode(request, response);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }


    /**
     * 获取微信登录二维码链接
     */
    @GetMapping("/wechat/login-url")
    public AjaxResult<Object> getLoginUrl() {

        ApiRPCResult<String> apiRPCResult = wechatLoginApi.getLoginUrl();
        if (apiRPCResult.isFail()) {
            return AjaxResult.errResult("初始化微信登录二维码失败");
        }
        return AjaxResult.successResult(apiRPCResult.getData());
    }

    /**
     * 获取微信 accessToken
     */
    @PostMapping("/wechat/access-token")
    public AjaxResult<Object> wechatAccessToken(@RequestBody WxParamVO wxParamVO) {

        String code = wxParamVO.getCode();
        log.info("当前微信code, {}", code);
        // 换取 access_token
        ApiRPCResult<WechatLoginUserDto> apiRPCResult = wechatLoginApi.getAccessToken(SiteEnum.PC.getValue(), code);
        if (apiRPCResult.isFail()) {
            return AjaxResult.errResult("获取微信令牌失败，请稍后再试");
        }
        return AjaxResult.successResult(apiRPCResult.getData());
    }

    /**
     * 微信登录
     */
    @PostMapping("/wechat/login")
    public AjaxResult<WxLoginRespVO> wechatLogin(@RequestBody WxLoginParamVO wxLoginParamVO, HttpServletRequest request) {
        try {
            return loginNewService.wxLogin(wxLoginParamVO, request);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }


    /**
     * 绑定账号
     * 发送微信绑定验证码
     */
    @PostMapping("/wechat/sendBindValidCode")
    public AjaxResult<Object> sendBindSmsCode(@RequestBody WxParamVO wxParamVO) {
        try {
            return loginNewService.sendBindCode(wxParamVO.getPhone());
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }

    }

    /**
     * 绑定账号
     * 账号绑定微信
     */
    @PostMapping("/wechat/bind")
    public AjaxResult<Object> wechatBind(@RequestBody WxBindParamVO wxBindParamVO) {
        try {
            return loginNewService.wxBind(wxBindParamVO);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    /**
     * 账号绑定微信 - 注册
     */
    @PostMapping("/wechat/register/bind")
    public AjaxResult<Object> wechatBindRegister(@RequestBody WxBindParamVO wxBindParamVO) {
        try {
            return loginNewService.wxBindRegister(wxBindParamVO);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }


    /**
     * 注册：第二步（验证商户邀请码，完成注册）
     */
    @PostMapping(value = "/register-last")
    public AjaxResult<RegisterRespVO> registerLast(@RequestBody RegisterParamVO registerParamVO, HttpServletRequest request) throws Exception {
        return loginNewService.registerLast(registerParamVO, request);
    }

    /**
     * 忘记密码:第三步
     */
    @PostMapping(value = "/forget-pass-last")
    public AjaxResult<ForgetPassLastRespVO> forget_pass_last(@RequestBody ForgetPassLastParamVO param) throws Exception {
        return loginNewService.forgetPassLast(param);
    }


    /**
     * 注册页面(提交数据，并且跳转到下个页面)
     *
     * @return
     */
    @PostMapping(value = "/registerNext")
    public AjaxResult<Object> registerNext(@RequestBody AccountRegisterVO accountRegisterVO) {
        return loginNewService.registerNext(accountRegisterVO);
    }


}
