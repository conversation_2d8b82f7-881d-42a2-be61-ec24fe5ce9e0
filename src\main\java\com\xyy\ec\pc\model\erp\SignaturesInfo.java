package com.xyy.ec.pc.model.erp;

import java.io.Serializable;
import java.util.List;

public class SignaturesInfo implements Serializable {
    private static final long serialVersionUID = -7482236444541126144L;

    //EC的域编码
    private String ecOrgCode;
    //商品老编码
    private String oldProductCode;
    //商品名称
    private String productName;
    //商品品牌
    private String brand;
    //附件图片
    private String enclosureUrl;
    //签章图片
    private String signatureImageUrl;
    //附件图片
    private String enclosureName;
    //查询参数
    private List<String> oldProductCodeList;
    //图片唯一性标识
    private String uniqueId;

    public String getEcOrgCode() {
        return ecOrgCode;
    }

    public void setEcOrgCode(String ecOrgCode) {
        this.ecOrgCode = ecOrgCode;
    }

    public String getOldProductCode() {
        return oldProductCode;
    }

    public void setOldProductCode(String oldProductCode) {
        this.oldProductCode = oldProductCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getEnclosureUrl() {
        return enclosureUrl;
    }

    public void setEnclosureUrl(String enclosureUrl) {
        this.enclosureUrl = enclosureUrl;
    }

    public String getSignatureImageUrl() {
        return signatureImageUrl;
    }

    public void setSignatureImageUrl(String signatureImageUrl) {
        this.signatureImageUrl = signatureImageUrl;
    }

    public String getEnclosureName() {
        return enclosureName;
    }

    public void setEnclosureName(String enclosureName) {
        this.enclosureName = enclosureName;
    }

    public List<String> getOldProductCodeList() {
        return oldProductCodeList;
    }

    public void setOldProductCodeList(List<String> oldProductCodeList) {
        this.oldProductCodeList = oldProductCodeList;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }
}
