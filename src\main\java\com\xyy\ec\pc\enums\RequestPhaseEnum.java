package com.xyy.ec.pc.enums;

/**
 * @Description 请求阶段，用于标志是否初始化请求
 * @Date 2025/8/3 16:25
 */
public enum RequestPhaseEnum {

    INIT("1", "初始化"),
    Authorized("2", "已鉴权"),
    ;

    private String code;
    private String name;

    RequestPhaseEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


}
