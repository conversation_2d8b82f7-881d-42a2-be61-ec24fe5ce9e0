package com.xyy.ec.pc.cms.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * CMS拼团商品查询参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CmsGroupBuyingProductQueryParam implements Serializable {

    /**
     * 1：未开始
     * 2：进行中
     */
    private Integer type;

    /**
     * 商品种类ID
     */
    private Long categoryId;

    private Integer offset;

    private Integer limit;
}
