package com.xyy.ec.pc.newfront.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.api.busi.place.register.PoiReq;
import com.xyy.ec.merchant.bussiness.dto.MerchantPoiBusinessDto;
import com.xyy.ec.pc.newfront.service.RegisterNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025-07-11 10:24
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/new-front/register")
@Slf4j
public class RegisterNewController {

    private final RegisterNewService registerNewService;


    /**
     * 店铺列表查询
     *
     * @param poiReq
     * @return
     */
    @PostMapping("/search")
    public AjaxResult<PageInfo<MerchantPoiBusinessDto>> search(@RequestBody @Valid PoiReq poiReq) {

        try {
            return registerNewService.getPageMerchantInfo(poiReq);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }

    }
}
