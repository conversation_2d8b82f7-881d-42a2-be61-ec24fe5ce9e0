package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.licence.LicenseValidDto;
import com.xyy.ec.merchant.bussiness.result.MerchantRelateShopResult;
import com.xyy.ec.merchant.server.dto.SimpleMerchantDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.controller.vo.merchant.MerchantAccountInfoVO;
import com.xyy.ec.pc.enums.TerminalTypeEnum;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.dto.MerchantRespVO;
import com.xyy.ec.pc.newfront.dto.UploadAuthorizationVO;
import com.xyy.ec.pc.newfront.service.MerchantCenterNewService;
import com.xyy.ec.pc.newfront.vo.AddMerchantParamVO;
import com.xyy.ec.pc.newfront.vo.MerchantParamVO;
import com.xyy.ec.pc.newfront.vo.MerchantStatusSearchParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.util.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@CustomizeCmsResponse
@RequiredArgsConstructor
@RestController
@RequestMapping("/new-front/merchant-center")
@Slf4j
public class MerchantCenterNewController extends BaseController {


    private final MerchantCenterNewService merchantCenterNewService;


    /**
     * 获取资质提醒  type有值说明是主页弹窗，一天弹一次
     */
    @RequestMapping("/license/remind")
    @ResponseBody
    public AjaxResult<LicenseValidDto> validityRemind(@RequestParam("terminalType") TerminalTypeEnum terminalType) throws Exception {
            return merchantCenterNewService.validityRemind(terminalType);

    }

    /**
     * 资质过期/临期/正常 接口
     */
    @GetMapping("/license/validity")
    public AjaxResult<Integer> validity(){
        try {
            return merchantCenterNewService.validity();
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    /**
     * 首页用户中心
     *
     * @return AjaxResult
     */
    @GetMapping("/select-user")
    public AjaxResult<MerchantRespVO> selectUser() throws Exception {
        return merchantCenterNewService.selectUser();
    }



    /**
     * 分页查询账号关联的店铺
     *
     * @return AjaxResult
     */
    @PostMapping("/account/merchant-list")
    public AjaxResult<MerchantRespVO> listAccountRelatedMerchants(@RequestBody MerchantParamVO merchantParamVO) throws Exception {
           merchantParamVO.setPageNum(merchantParamVO.getPageNum() == null ? 1 : merchantParamVO.getPageNum());
           merchantParamVO.setPageSize(merchantParamVO.getPageSize() == null ? 10 : merchantParamVO.getPageSize());
           return  merchantCenterNewService.listAccountRelatedMerchants(merchantParamVO);


    }

    /**
     * 切换店铺
     * @param merchantParamVO
     * @return AjaxResult
     */
    @PostMapping(value = "/switch-merchant")
    public AjaxResult<MerchantRespVO> selectMerchant(HttpServletRequest request, @RequestBody MerchantParamVO merchantParamVO) throws Exception {

        return merchantCenterNewService.selectMerchant(request, merchantParamVO);

    }


    /**
     * 获取店铺资质信息
     *
     * @return AjaxResult
     */
    @GetMapping(value = "/licenseStatus")
    public AjaxResult<Integer> selectLicenseStatus(@RequestParam("merchantId") Long merchantId) {

        return merchantCenterNewService.getLicenseStatus(merchantId);

    }

    /**
     * 获取当前登录的账号信息
      * @return
     */
    @GetMapping("/getLoginAccountInfo")
    public AjaxResult<MerchantAccountInfoVO> getLoginAccountInfo() {
        try {
            return merchantCenterNewService.getAccountInfo();
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }

    }


    /**
     * 关联店铺
     *
     * @param poiIdStr
     * @param request
     * @return
     */
    @GetMapping("/relShop")
    public AjaxResult<MerchantRelateShopResult> relateShop(@RequestParam(name = "poiId", required = false) String poiIdStr, HttpServletRequest request) {

        try {
            return merchantCenterNewService.relateShop(request, poiIdStr);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }


    }


    /**
     * 添加店员资质审核信息
     *
     * @return
     */
    @PostMapping("/uploadAuthorization")
    public AjaxResult<Object> addClerkLicenseAudit(@RequestBody UploadAuthorizationVO vo) {

        return merchantCenterNewService.addClerkLicenseAudit(vo.getMerchantId(), vo.getLicenseAuditImagesJsonStr());

    }



    /**
     * 添加店铺
     * @param addMerchantParamVO
     * @return
     */
    @PostMapping("/add")
    public AjaxResult<Object> addMerchant(@RequestBody AddMerchantParamVO addMerchantParamVO) {
        log.info("MerchantCenterNewController addMerchant req:{}",addMerchantParamVO);
        try {
            AjaxResult<Object> objectAjaxResult = merchantCenterNewService.addMerchant(addMerchantParamVO);
            log.info("MerchantCenterNewController addMerchant resp:{}",objectAjaxResult);
            return objectAjaxResult;
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }


    /**
     * 获取工商信息
     * @param vo
     * @param request
     * @return
     */
    @PostMapping("/getBusinessInfo")
    public AjaxResult<Object> getBusinessInfo(@RequestBody AddMerchantParamVO vo,HttpServletRequest request) {
        try {
            return merchantCenterNewService.getBusinessInfo(request,vo);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    /**
     * 查询店铺状态
     * @param request
     * @param vo
     * @return
     */
    @PostMapping("/getMerchantStatus")
    public AjaxResult<Object> getMerchantStatus(@RequestBody MerchantStatusSearchParamVO vo,HttpServletRequest request) {
        try {
            return merchantCenterNewService.getMerchantStatus(request,vo);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }

    }

    /**
     * 店铺信息查询
     */
    @GetMapping("/checkMerchantInfo")
    public AjaxResult<Object> getSimpleMerchantInfo(@RequestParam(name = "merchantId") Long merchantId) {
        try {
            return merchantCenterNewService.getSimpleMerchantInfo(merchantId);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }




}
