package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.merchant.bussiness.api.DelegateTemplateRecordBusinessApi;
import com.xyy.ec.merchant.bussiness.api.LicenseAuditBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.DelegateTemplateRecordBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.enums.LicenseCategoryCodeEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.FileUploadUtil;
import com.xyy.ec.pc.util.ImgUtils;
import com.xyy.ec.pc.util.PdfUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.electron.data.bussiness.data.api.XyyLicenseDownloadBussinessApi;
import com.xyy.electron.data.bussiness.data.enums.LicenseDownloadSourceEnum;
import com.xyy.electron.data.bussiness.data.enums.SceneCodeEnum;
import com.xyy.electron.data.bussiness.data.params.LicensesParams;
import com.xyy.electron.data.bussiness.data.tool.ErrorCode;
import com.xyy.electron.data.bussiness.data.tool.ResultUtil;
import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Objects;

import static com.xyy.ec.pc.constants.Constants.*;
import static com.xyy.ec.pc.util.FileUploadUtil.delFile;

/**
 * <AUTHOR>
 * @date ：Created in 2019/9/4 9:51
 * 单据上传,审核,修改相关功能
 */
@RequestMapping("/licenseAudit/down")
@Controller
public class LicenseAuditDownController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(LicenseAuditDownController.class);

    @Resource
    private XyyConfig.CdnConfig cdnConfig;

    @Resource
    private XyyIndentityValidator xyyIndentityValidator;

    @Value("${config.product_image_path_url}")
    private String PRODUCT_IMAGE_PATH_URL;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private DelegateTemplateRecordBusinessApi recordBusinessApi;

    @Reference(version = "1.0.0")
    private XyyLicenseDownloadBussinessApi xyyLicenseDownloadBussinessApi;

    @Reference(version = "1.0.0")
    private LicenseAuditBussinessApi licenseAuditBussinessApi;


    /**
     * 委托书模板图片下载
     *
     * @param res        请求返回
     * @param merchantId 会员id
     * @throws Exception
     */
    @RequestMapping(value = "downLoadImg.json")
    public void downLoadImg(HttpServletResponse res, Long merchantId) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        merchantId = merchant.getId();
        //对单个用户同时发起多次请求处理
        String result = recordBusinessApi.addRedis(merchantId);
        if (StringUtil.isEmpty(result)) {
            logger.info("会员:{}已有下载的任务", merchantId);
            return;
        }
        //根据id查询会员药店名和对应销售信息同时判断该会员id是否有效
        MerchantBussinessDto merchantBussinessDto = merchantBussinessApi.findMerchantBaseInfoForApp(merchantId);
        if (merchantBussinessDto == null) {
            logger.info("会员:{}不存在", merchantId);
            recordBusinessApi.delRedis(merchantId);
            return;
        }
        String url = Constants.merFtpFilePath + merchantId + "/";
        String localUrl = Constants.localDirectoryPath + merchantId + "/";
        FTPClient ftpClient = new FTPClient();
        BufferedInputStream bis = null;
        OutputStream os = null;
        try {
            boolean flag = FileUploadUtil.createFtpConnect(cdnConfig.getCdnHostname(),
                    Integer.parseInt(cdnConfig.getCdnPort()), cdnConfig.getCdnUsername(),
                    cdnConfig.getCdnPassword(), ftpClient);
            if (!flag) {
                logger.info("委托书模板下载链接ftp失败");
                recordBusinessApi.delRedis(merchantId);
                return;
            }
            File tempFile = new File(localUrl);
            if (!tempFile.exists()) {
                tempFile.mkdirs();
            }
            ftpClient.enterLocalActiveMode();
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            DelegateTemplateRecordBusinessDto recordBusinessDto = recordBusinessApi.selectBymerchantId(merchantId);
            //判断数据库是否已有该会员的委托书模板记录
            if (recordBusinessDto == null) {
                ftpClient.makeDirectory(Constants.merFtpFilePath);
                //执行创建模板操作
                ftpClient.makeDirectory(url);
                //获取委托书模板图片
                String urlOut = Constants.localDirectoryPath + File.separator + Constants.xyyFileName;
                FileUploadUtil.downloadFile(Constants.xyyFileName, Constants.localDirectoryPath, Constants.merFtpFilePath, ftpClient);
                //创建会员信息委托书模板图片
                String urlIn = localUrl + Constants.xyyFileName;
                //向会员信息委托书模板图片写入会员相关信息
                ImgUtils.doImage(urlOut, urlIn, merchantBussinessDto.getRealName());
                //获得委托书模板图片本地文件流
                FileInputStream in = new FileInputStream(new File(urlIn));
                //上传会员模板到会员对应模板文件夹
                ftpClient.changeWorkingDirectory(url);
                if (ftpClient.storeFile(Constants.xyyFileName, in)) {
                    //上传成功入库
                    DelegateTemplateRecordBusinessDto dto = new DelegateTemplateRecordBusinessDto();
                    dto.setMerchantId(merchantId);
                    dto.setUrl(url + Constants.xyyFileName);
                    recordBusinessApi.insertSelective(dto);
                }
            } else {
                //下载到本地
                FileUploadUtil.downloadFile(Constants.xyyFileName, localUrl, url, ftpClient);
            }
            File file = new File(localUrl + Constants.xyyFileName);
            res.setHeader("Content-Disposition", "attachment;filename=" + new String(Constants.xyyFileName.getBytes("gb2312"), "ISO8859-1"));
            byte[] buff = new byte[1024];

            os = res.getOutputStream();
            bis = new BufferedInputStream(new FileInputStream(file));
            int i = bis.read(buff);
            while (i != -1) {
                os.write(buff, 0, buff.length);
                os.flush();
                i = bis.read(buff);
            }
            logger.info("会员:{}委托书模板图片下载结束。。。", merchantId);
        } catch (Exception e) {
            logger.error("委托书模板图片下载异常:", e);
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (Exception e) {
                    logger.error("委托书模板图片下载异常:", e);
                }
            }
            if (os != null) {
                try {
                    os.close();
                } catch (Exception e) {
                    logger.error("委托书模板图片下载异常:", e);
                }
            }
            FileUploadUtil.closeFtpConnect(ftpClient);
            recordBusinessApi.delRedis(merchantId);
            Thread.sleep(2);
            File file = new File(localUrl);
            delFile(file);
        }
    }

    /**
     * 委托书模板pdf下载（已有pdf直接下载）
     *
     * @param res 请求返回
     * @throws Exception
     */
    @RequestMapping(value = "downLoadPdf.json")
    public void downLoadPdf(HttpServletResponse res) {
        logger.info("委托书模板pdf下载开始。。。");
        FTPClient ftpClient = new FTPClient();
        BufferedInputStream bis = null;
        OutputStream os = null;
        String localUrl = localDirectoryPath + "pdf/";
        try {
            File file = new File(localUrl);
            if (!file.exists()) {
                String result = recordBusinessApi.addRedis(520l);
                if (StringUtil.isEmpty(result)) {
                    logger.info("正在生成...");
                    return;
                }
                boolean flag = FileUploadUtil.createFtpConnect(cdnConfig.getCdnHostname(),
                        Integer.parseInt(cdnConfig.getCdnPort()), cdnConfig.getCdnUsername(),
                        cdnConfig.getCdnPassword(), ftpClient);
                if (!flag) {
                    logger.info("委托书模板下载链接ftp失败");
                    recordBusinessApi.delRedis(520l);
                    return;
                }
                file.mkdirs();
                ftpClient.enterLocalActiveMode();
                ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
                ftpClient.makeDirectory(xyyFtpFilePath);
                FileUploadUtil.downloadFile(Constants.xyyPdfName, localUrl, xyyFtpFilePath, ftpClient);
            }
            file = new File(localUrl + File.separator + xyyPdfName);
            res.setHeader("Content-Disposition", "attachment;filename=" + new String(xyyPdfName.getBytes("gb2312"), "ISO8859-1"));
            byte[] buff = new byte[1024];
            os = res.getOutputStream();
            bis = new BufferedInputStream(new FileInputStream(file));
            int i = bis.read(buff);
            while (i != -1) {
                os.write(buff, 0, buff.length);
                os.flush();
                i = bis.read(buff);
            }
            logger.info("委托书模板pdf下载结束。。。");
        } catch (Exception e) {
            File file = new File(localUrl);
            delFile(file);
            logger.error("委托书模板下载异常:", e);
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    logger.error("委托书模板下载异常:", e);
                }
            }
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    logger.error("委托书模板下载异常:", e);
                }
            }
            FileUploadUtil.closeFtpConnect(ftpClient);
            recordBusinessApi.delRedis(520l);
        }
    }

    /**
     * 委托书模板pdf下载（根据图片生成pdf再下载）
     *
     * @param res 请求返回
     * @throws Exception
     */
    @RequestMapping(value = "downLoadPdfTo.json")
    public void downLoadPdfTo(HttpServletResponse res) {
        logger.info("委托书模板pdf下载开始。。。");
        FTPClient ftpClient = new FTPClient();
        BufferedInputStream bis = null;
        OutputStream os = null;
        String localUrl = localDirectoryPath + "pdf/";
        try {
            File file = new File(localUrl);
            if (!file.exists()) {
                String result = recordBusinessApi.addRedis(520l);
                if (StringUtil.isEmpty(result)) {
                    logger.info("正在生成...");
                    return;
                }
                boolean flag = FileUploadUtil.createFtpConnect(cdnConfig.getCdnHostname(),
                        Integer.parseInt(cdnConfig.getCdnPort()), cdnConfig.getCdnUsername(),
                        cdnConfig.getCdnPassword(), ftpClient);
                if (!flag) {
                    logger.info("委托书模板下载链接ftp失败");
                    recordBusinessApi.delRedis(520l);
                    return;
                }
                file.mkdirs();
                ftpClient.enterLocalActiveMode();
                ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
                //执行创建pdf模板操作
                ftpClient.makeDirectory(xyyFtpFilePath);
                //下载企业资质模板文件夹
                FileUploadUtil.downLoadDirectory(localUrl, xyyFtpFilePath, ftpClient);
                //3.生成pdf
                PdfUtil.imagesToPdf(localUrl, localUrl + xyyPdfName);
            }
            file = new File(localUrl + File.separator + xyyPdfName);
            res.setHeader("Content-Disposition", "attachment;filename=" + new String(xyyPdfName.getBytes("gb2312"), "ISO8859-1"));
            byte[] buff = new byte[1024];
            os = res.getOutputStream();
            bis = new BufferedInputStream(new FileInputStream(file));
            int i = bis.read(buff);
            while (i != -1) {
                os.write(buff, 0, buff.length);
                os.flush();
                i = bis.read(buff);
            }
            logger.info("委托书模板pdf下载结束。。。");
        } catch (Exception e) {
            File file = new File(localUrl);
            delFile(file);
            logger.error("委托书模板下载异常:", e);
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    logger.error("委托书模板下载异常:", e);
                }
            }
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    logger.error("委托书模板下载异常:", e);
                }
            }
            FileUploadUtil.closeFtpConnect(ftpClient);
            recordBusinessApi.delRedis(520l);
        }
    }

    /**
     * 委托书模板pdf删除
     *
     * @throws Exception
     */
    @RequestMapping(value = "delPdf.json")
    public void delPdf() {
        logger.info("委托书模板pdf删除开始。。。");
        String localUrl = localDirectoryPath + "pdf/";
        File file = new File(localUrl);
        delFile(file);
        logger.info("委托书模板pdf删除结束。。。");
    }

    /**
     * 委托书模板示例图片
     *
     * @throws Exception
     */
    @RequestMapping(value = "viewExamplesImg.json")
    @ResponseBody
    public Object viewExamplesImg() throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        Long merchantId = merchant.getId();
        String url = Constants.merFtpFilePath + merchantId + "/";
        String localUrl = Constants.localDirectoryPath + merchantId + "/";
        FTPClient ftpClient = new FTPClient();
        //根据id查询会员药店名和对应销售信息同时判断该会员id是否有效
        MerchantBussinessDto merchantBussinessDto = merchantBussinessApi.findMerchantBaseInfoForApp(merchantId);
        if (merchantBussinessDto == null) {
            logger.info("会员:{}不存在", merchantId);
            return this.addError("会员id不存在...");
        }
        //判断数据库是否已有该会员的委托书模板记录
        DelegateTemplateRecordBusinessDto recordBusinessDto = recordBusinessApi.selectBymerchantId(merchantId);
        if (recordBusinessDto == null) {
            logger.info("会员:{}委托书模板记录为空", merchantId);
            try {
                //对单个用户同时发起多次请求处理
                String result = recordBusinessApi.addRedis(merchantId);
                if (StringUtil.isEmpty(result)) {
                    logger.info("会员:{}示例图片正在生成,请稍等...", merchantId);
                    return this.addError("该会员示例图片正在生成,请稍等...");
                }
                boolean flag = FileUploadUtil.createFtpConnect(cdnConfig.getCdnHostname(),
                        Integer.parseInt(cdnConfig.getCdnPort()), cdnConfig.getCdnUsername(),
                        cdnConfig.getCdnPassword(), ftpClient);
                if (!flag) {
                    logger.info("委托书模板下载链接ftp失败");
                    recordBusinessApi.delRedis(merchantId);
                    return this.addError("委托书模板示例生成链接ftp失败");
                }

                File tempFile = new File(localUrl);
                if (!tempFile.exists()) {
                    tempFile.mkdirs();
                }
                ftpClient.enterLocalActiveMode();
                ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
                ftpClient.makeDirectory(Constants.merFtpFilePath);
                //执行创建模板操作
                ftpClient.makeDirectory(url);
                //获取委托书模板图片
                String urlOut = Constants.localDirectoryPath + File.separator + Constants.xyyFileName;
                FileUploadUtil.downloadFile(Constants.xyyFileName, Constants.localDirectoryPath, Constants.merFtpFilePath, ftpClient);
                //创建会员信息委托书模板图片
                String urlIn = localUrl + Constants.xyyFileName;
                //向会员信息委托书模板图片写入会员相关信息
                ImgUtils.doImage(urlOut, urlIn, merchantBussinessDto.getRealName());
                //获得委托书模板图片本地文件流
                FileInputStream in = new FileInputStream(new File(urlIn));
                //上传会员模板到会员对应模板文件夹
                ftpClient.changeWorkingDirectory(url);
                if (ftpClient.storeFile(Constants.xyyFileName, in)) {
                    //上传成功入库
                    DelegateTemplateRecordBusinessDto dto = new DelegateTemplateRecordBusinessDto();
                    dto.setMerchantId(merchantId);
                    dto.setUrl(url + Constants.xyyFileName);
                    recordBusinessApi.insertSelective(dto);
                }
                logger.info("会员:{}示例图片生成结束。。。", merchantId);
                return this.addResult(PRODUCT_IMAGE_PATH_URL + url + Constants.xyyFileName);
            } catch (Exception e) {
                logger.error("委托书模板下载异常:", e);
            } finally {
                FileUploadUtil.closeFtpConnect(ftpClient);
                recordBusinessApi.delRedis(merchantId);
                Thread.sleep(2);
                File file = new File(localUrl);
                delFile(file);
            }
        }
        return this.addResult(PRODUCT_IMAGE_PATH_URL + recordBusinessDto.getUrl());
    }

    /**
     * 资质模板下载
     *
     * @throws Exception
     */
    @RequestMapping(value = "/downloadLicenseTemplate.json")
    @ResponseBody
    public void downloadLicenseTemplate(HttpServletRequest request, HttpServletResponse response, @RequestParam String categoryCode) {
        //1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
        response.setContentType("multipart/form-data");
        ServletOutputStream out = null;
        InputStream inputStream = null;

        String fileName;
        if (LicenseCategoryCodeEnum.PharmacyEnum.FRSQ.getKey().equals(categoryCode)) {
            fileName = "授权委托书模板.doc";
        } else if (LicenseCategoryCodeEnum.PharmacyEnum.ZLBZ.getKey().equals(categoryCode)) {
            fileName = "质量保证协议模板-客户.docx";
        } else {
            logger.error("下载模板失败，错误的模板类型:{}", categoryCode);
            return;
        }

        try {
            String agent = request.getHeader("User-Agent").toLowerCase();
            if (agent.contains("msie") || agent.contains("edge") || agent.contains("rv:11")) {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8").replace("+", "%20"));
            } else {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=ISO8859-1");
                response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("utf-8"), "ISO8859-1"));
            }
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "max-age=0");
            inputStream = getClass().getClassLoader().getResourceAsStream("static/word/" + fileName);
            //3.通过response获取ServletOutputStream对象(out)
            out = response.getOutputStream();
            int b = 0;
            byte[] buffer = new byte[512];
            while (b != -1) {
                b = inputStream.read(buffer);
                if (b != -1) {
                    out.write(buffer, 0, b);//4.写到输出流(out)中
                }
            }
        } catch (IOException e) {
            logger.error("下载异常", e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (out != null) {
                    out.close();
                    out.flush();
                }
            } catch (IOException e) {
                logger.error("下载异常", e);
            }
        }

    }


    /**
     * 下载小药药公司资质
     *
     * @param contractId 合同id（all 表示下载全部公司资质）
     * @return
     */
    @RequestMapping("/downloadXyyLicense.json")
    @ResponseBody
    public Object downloadXyyLicense(@RequestParam String contractId) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if(Objects.isNull(merchant)){
                return this.addError("获取当前用户失败，请重新登录");
            }
            //判断用户是否在开放的区域
            String newBranchCodes = licenseAuditBussinessApi.getNewLicenseBranchCode();
            if (!newBranchCodes.contains(merchant.getRegisterCode()) && !newBranchCodes.contains(BranchEnum.ALL_COUNTRY.getKey())) {
                return this.addError("此功能暂未开放");
            }
            String branchCode = merchant.getRegisterCode();
            String sceneCode = SceneCodeEnum.ec_ybm_scene_code.getCode();
            LicensesParams params = new LicensesParams();

            params.setSceneCode(sceneCode);
            params.setDownloadSource(LicenseDownloadSourceEnum.ec_ybm_pc.getCode());
            params.setEcId(merchant.getId());
            params.setRealName(merchant.getRealName());
            params.setEcCompanyCode(branchCode);
            params.setProvinceName(merchant.getProvince());

            ResultUtil<String> resultUtil;
            if ("all".equals(contractId)) {
                // 下载小药药全部资质
                resultUtil = xyyLicenseDownloadBussinessApi.downloadContractList(params);
            } else {
                // 下载小药药单个资质
                params.setContractId(contractId);
                resultUtil = xyyLicenseDownloadBussinessApi.downloadContract(params);
            }
            if (resultUtil.getCode() == ErrorCode.FAIL) {
                logger.info("药帮忙资质获取失败,resultUtil:{}", JSONObject.toJSONString(resultUtil));
                return this.addError(resultUtil.getMsg());
            }
            return this.addResult("url", resultUtil.getDataInfo());

        } catch (Exception e) {
            logger.error("药帮忙资质获取异常", e);
            return this.addError("药帮忙资质获取异常，请稍后重试");
        }
    }


}
