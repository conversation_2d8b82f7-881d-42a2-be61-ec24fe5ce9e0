package com.xyy.ec.pc.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "app")
public class AppProperties {

    private final Url url = new Url();
    private final PharmacyCollege pharmacyCollege = new PharmacyCollege();

    @Setter
    @Getter
    public static class Url {
        /**
         * PC域名
         */
        private String pc;
    }

    /**
     * 药学院
     */
    @Setter
    @Getter
    public static class PharmacyCollege {
        /**
         * PC详情页Api
         */
        private String detailApiForPc;
        /**
         * PC首页Api
         */
        private String indexApiForPc;
        /**
         * 为saas提供的首页标题
         */
        private String indexTitleForSaas;
    }

    /**
     * PC导航菜单展示数量
     */
    @Value("${pcHeaderShowNum:4}")
    private Integer pcHeaderShowNum;

    /**
     * 是否应用高毛拼团销售逻辑
     */
    @Value("${app.isApplyGaoMaoGroupBuyingSaleLogic:false}")
    private Boolean isApplyGaoMaoGroupBuyingSaleLogic;

    /**
     * 应用高毛拼团销售逻辑的商品highGross值
     */
    @Value("#{'${apply.gaomao.group.sale.highGross.set:}'.split(',')}")
    private Set<Integer> applyGaoMaoGroupSaleHighGrossSet;

    /**
     * 是否应用高毛拼团销售逻辑(汇总)
     */
    @Value("${app.isApplyGaoMaoGroupBuyingSaleSummary:true}")
    private Boolean isApplyGaoMaoGroupBuyingSaleSummary;

    /**
     * fbp店铺code
     */
    @Value("#{'${fbp.shopCode.set:}'.split(',')}")
    private Set<String> fbpShopCodeSet;

    /**
     * 特殊处理pop店铺资质的机构id列表
     */
    @Value("#{'${app.specifyPopShopLicenseOrgIds:}'.split(',')}")
    private Set<String> specifyPopShopLicenseOrgIds;

    /**
     * 店铺混淆，是否开启不显示店铺特性
     */
    @Value("${app.shopConfuse.isOpenNotShowShopFeature:false}")
    private Boolean isOpenShopConfuseNotShowShopFeature;

    /**
     * 店铺混淆，是否开启不在店铺列表显示特性
     */
    @Value("${app.shopConfuse.isOpenShopListNotShowShopFeature:false}")
    private Boolean isOpenShopConfuseShopListNotShowShopFeature;

    /**
     * 店铺混淆，不显示店铺的编码列表（店铺编码及orgId）
     */
    @Value("#{'${app.shopConfuse.notShowShopCodeAndOrgIds:}'.split(',')}")
    private Set<String> shopConfuseNotShowShopCodeAndOrgIds;

    @Value("${app.shopConfuse.notShowShopTip:尚未开店，请核实后重试~}")
    private String shopConfuseNotShowShopTip;

    /**
     * 是否应用V2搜索首页
     */
    @Value("${xyy.ec.isApplySearchIndexV2:false}")
    private Boolean isApplySearchIndexV2;

    /**
     * V2搜索首页地址
     */
    @Value("${xyy.ec.searchIndexV2.pageUrl:}")
    private String searchIndexV2PageUrl;

    /**
     * 搜索首页地址
     */
    @Value("${xyy.ec.searchIndex.pageUrl}")
    private String searchIndexPageUrl;

    /**
     * 搜索首页地址
     */
    @Value("${xyy.ec.searchIndex.toPageUrl}")
    private String searchIndexToPageUrl;

    /**
     * pc根路径
     */
    @Value("${base_path_url}")
    private String basePathUrl;

    /**
     * 是否使用在列表页上弹出活动拼团动态面板的操作方式
     */
    @Value("${app.isApplyListShowType:true}")
    private Boolean isApplyListShowType;

    @Value("${xyy.shopIndex.getSkuPageSize:50}")
    private int shopIndexGetSkuPageSize;

    @Value("${xyy.shopIndex.shopIndexGetSkuConcurrentFlag:true}")
    private boolean shopIndexGetSkuConcurrentFlag;

    @Value("${xyy.shopIndex.shopIndexGetSkuIdNewFlag:true}")
    private boolean shopIndexGetSkuIdNewFlag;

    @Value("${xyy.shopIndex.shopIndexGetSkuConcurrent.timeout:1500}")
    private int shopIndexGetSkuConcurrentTimeout;

}
