package com.xyy.ec.pc.cms.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.api.ExhibitionBuineseApi;
import com.xyy.ec.layout.buinese.ecp.api.ProductExhibitionGroupBusinessAdminApi;
import com.xyy.ec.layout.buinese.ecp.api.ProductExhibitionGroupBusinessApi;
import com.xyy.ec.layout.buinese.ecp.api.ProductExhibitionGroupPcBusinessApi;
import com.xyy.ec.layout.buinese.ecp.api.RuleExhibitionGroupBusinessApi;
import com.xyy.ec.layout.buinese.ecp.params.ExhibitionGroupListUsingProductIdsQueryParam;
import com.xyy.ec.layout.buinese.ecp.params.ExhibitionProductQueryParam;
import com.xyy.ec.layout.buinese.ecp.params.ProductsQueryParam;
import com.xyy.ec.layout.buinese.ecp.params.RuleExhibitionQueryParam;
import com.xyy.ec.layout.buinese.ecp.utils.RuleExhibitionUtil;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.cms.dto.CmsListProductDto;
import com.xyy.ec.pc.cms.enums.LayoutComponentEnum;
import com.xyy.ec.pc.cms.helpers.CmsExpectProductQueryParamHelper;
import com.xyy.ec.pc.cms.helpers.CmsListProductVOHelper;
import com.xyy.ec.pc.cms.helpers.CmsProductQueryParamHelper;
import com.xyy.ec.pc.cms.param.CmsExpectProductQueryParam;
import com.xyy.ec.pc.cms.param.CmsProductQueryParam;
import com.xyy.ec.pc.cms.service.CmsSkuService;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;
import com.xyy.ec.pc.config.Constants;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.remote.MerchantBusinessRemoteService;
import com.xyy.ec.pc.remote.ProductForLayoutRemoteService;
import com.xyy.ec.pc.remote.SearchRecommendedRemoteService;
import com.xyy.ec.pc.remote.ShopQueryRemoteService;
import com.xyy.ec.pc.search.helpers.SearchCsuDataTagCsuDTOHelper;
import com.xyy.ec.pc.search.params.SearchCsuDataTagQueryParam;
import com.xyy.ec.pc.search.service.DataService;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CmsSkuServiceImpl implements CmsSkuService {

    @Reference(version = "1.0.0")
    private ProductExhibitionGroupBusinessAdminApi productExhibitionGroupBusinessAdminApi;

    @Reference(version = "1.0.0")
    private ProductExhibitionGroupBusinessApi productExhibitionGroupBusinessApi;

    @Reference(version = "1.0.0")
    private ProductExhibitionGroupPcBusinessApi productExhibitionGroupPcBusinessApi;

    @Reference(version = "1.0.0")
    private ExhibitionBuineseApi exhibitionBuineseApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBusinessApi;

    @Autowired
    private SearchRecommendedRemoteService searchRecommendedRemoteService;

    @Autowired
    private MerchantBusinessRemoteService merchantBusinessRemoteService;

    @Autowired
    private ProductForLayoutRemoteService productForLayoutRemoteService;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private CmsAppProperties cmsAppProperties;

    @Autowired
    private ShopQueryRemoteService shopQueryRemoteService;

    @Autowired
    private DataService dataService;

    @Reference(version = "1.0.0")
    private RuleExhibitionGroupBusinessApi ruleExhibitionGroupBusinessApi;

    @Override
    public List<ListProduct> listExpectProducts(CmsExpectProductQueryParam cmsExpectProductQueryParam) {
        // 会员未登录情况统一转为null。
        if (cmsExpectProductQueryParam != null && Objects.equals(cmsExpectProductQueryParam.getMerchantId(), 0L)) {
            cmsExpectProductQueryParam.setMerchantId(null);
        }
        Boolean validate = CmsExpectProductQueryParamHelper.validate(cmsExpectProductQueryParam);
        if (!BooleanUtils.isTrue(validate)) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "普通商品查询");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }

        String exhibitionId = cmsExpectProductQueryParam.getExhibitionId();
        String branchCode = cmsExpectProductQueryParam.getBranchCode();
        Long merchantId = cmsExpectProductQueryParam.getMerchantId();
        Integer expectNum = cmsExpectProductQueryParam.getExpectNum();
        Integer terminalType = cmsExpectProductQueryParam.getTerminalType();

        List<ListProduct> products = Lists.newArrayListWithExpectedSize(expectNum);
        int pageNum = 0;
        int pageSize = expectNum * 6;
        if (pageSize <= 30) {
            pageSize = 30;
        }
        int maxPage = cmsAppProperties.getListExpectProductsMaxPage();
        List<ListProduct> tempProducts;
        int pages;
        for (int i = 1; i <= maxPage; i++) {
            pageNum++;
            if (BooleanUtils.isTrue(RuleExhibitionUtil.isRuleExhibition(exhibitionId)) && cmsAppProperties.getUseRuleExhibitionSwitch()) {
                RuleExhibitionQueryParam param = RuleExhibitionQueryParam.builder()
                        .merchantId(merchantId).branchCode(branchCode)
                        .exhibitionIdList(Lists.newArrayList(exhibitionId))
                        .isNeedExpress(true).isNeedQualityShopTitleTag(true)
                        .pageNum(pageNum).pageSize(pageSize).build();
                ApiRPCResult<PageInfo<ListProduct>> apiRPCResult = ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProducts(param);
                if (log.isDebugEnabled()) {
                    log.debug("listExpectProducts，调用ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProducts，入参：{}，出参：{}",
                            JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
                }
                if (!apiRPCResult.isSuccess()) {
                    String message = MessageFormat.format("普通商品查询失败，param：{0}，出参：{1}", JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
                    throw new AppException(message, XyyJsonResultCodeEnum.PAGING_USING_EXHIBITION_PRODUCTS_ERROR);
                }
                PageInfo<ListProduct> pageInfo = apiRPCResult.getData();
                pageNum = pageInfo.getPageNum();
                tempProducts = pageInfo.getList();
                pages = pageInfo.getPages();
            } else {
                if (Objects.isNull(merchantId)) {
                    ListSkuSearchData listSkuSearchData = exhibitionBuineseApi.getExhibitionProductSkuInfoWrapper(
                            exhibitionId, null, branchCode, pageNum, pageSize);
                    if (log.isDebugEnabled()) {
                        log.debug("listExpectProducts，调用exhibitionBuineseApi.getExhibitionProductSkuInfoWrapper，merchantId：{}，pageNum：{}，pageSize：{}，exhibitionId：{}，branchCode：{}，出参：{}", merchantId, pageNum, pageSize, exhibitionId, branchCode, JSONObject.toJSONString(listSkuSearchData));
                    }
                    pageNum = listSkuSearchData.getPageNum();
                    tempProducts = listSkuSearchData.getSkuDtoList();
                    pages = listSkuSearchData.getPages();
                } else {
                    ExhibitionProductQueryParam exhibitionProductQueryParam = ExhibitionProductQueryParam.builder()
                            .branchCode(branchCode).exhibitionIdStr(exhibitionId).merchantId(merchantId)
                            .isNeedExpress(true).isNeedQualityShopTitleTag(true).build();
                    ApiRPCResult<PageInfo<ListProduct>> apiRPCResult = productExhibitionGroupPcBusinessApi.flowingUsingExhibitionProducts(exhibitionProductQueryParam, pageNum, pageSize);
                    if (log.isDebugEnabled()) {
                        log.debug("listExpectProducts，调用productExhibitionGroupPcBusinessApi.flowingUsingExhibitionProducts，入参：{}，pageNum：{}，pageSize：{}，出参：{}", JSONObject.toJSONString(exhibitionProductQueryParam), pageNum, pageSize, JSONObject.toJSONString(apiRPCResult));
                    }
                    if (!apiRPCResult.isSuccess()) {
                        String message = MessageFormat.format("普通商品查询失败，exhibitionProductQueryParam：{0}，pageNum：{1}，pageSize：{2}，出参：{3}",
                                JSONObject.toJSONString(exhibitionProductQueryParam), pageNum, pageSize, JSONObject.toJSONString(apiRPCResult));
                        throw new AppException(message, XyyJsonResultCodeEnum.PAGING_USING_EXHIBITION_PRODUCTS_ERROR);
                    }
                    PageInfo<ListProduct> pageInfo = apiRPCResult.getData();
                    pageNum = pageInfo.getPageNum();
                    tempProducts = pageInfo.getList();
                    pages = pageInfo.getPages();
                }
            }
            if (CollectionUtils.isEmpty(tempProducts)) {
                if (pageNum >= pages) {
                    break;
                }
                continue;
            }
            // 过滤掉售罄的商品
            tempProducts = tempProducts.stream().filter(item -> Objects.equals(item.getStatus(), 1)).collect(Collectors.toList());
            if (log.isDebugEnabled()) {
                log.debug("listExpectProducts，merchantId：{}，销售中的product'size：{}", merchantId, tempProducts.size());
            }
            if (CollectionUtils.isEmpty(tempProducts)) {
                if (pageNum >= pages) {
                    break;
                }
                continue;
            }
            for (ListProduct tempProduct : tempProducts) {
                products.add(tempProduct);
                if (Objects.equals(products.size(), expectNum)) {
                    break;
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("listExpectProducts，merchantId：{}，products'size：{}，pageNum：{}，pages：{}", merchantId, products.size(), pageNum, pages);
            }
            if (pageNum >= pages || Objects.equals(products.size(), expectNum)) {
                break;
            }
        }
        return products;
    }

    @Override
    public CmsListProductDto listProducts(CmsProductQueryParam cmsProductQueryParam, int pageNum, int pageSize) {
        // 会员未登录情况统一转为null。
        if (cmsProductQueryParam != null && Objects.equals(cmsProductQueryParam.getMerchantId(), 0L)) {
            cmsProductQueryParam.setMerchantId(null);
        }
        Boolean validate = CmsProductQueryParamHelper.validate(cmsProductQueryParam);
        if (!BooleanUtils.isTrue(validate)) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "普通商品查询");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }

        String exhibitionId = cmsProductQueryParam.getExhibitionId();
        String branchCode = cmsProductQueryParam.getBranchCode();
        Long merchantId = cmsProductQueryParam.getMerchantId();
        Boolean isAdmin = cmsProductQueryParam.getIsAdmin();
        Integer moduleSourceType = cmsProductQueryParam.getModuleSourceType();
        Boolean isRecommend = cmsProductQueryParam.getIsRecommend();
        Integer terminalType = cmsProductQueryParam.getTerminalType();

        PageInfo<ListProduct> pageInfo;
        //包装vo
        CmsListProductDto cmsListProductDto = new CmsListProductDto();
        cmsListProductDto.setIsRecommend(false);
        if (BooleanUtils.isTrue(isAdmin)) {
            // admin侧
            ApiRPCResult<PageInfo<ListProduct>> apiRPCResult;
            if (BooleanUtils.isTrue(RuleExhibitionUtil.isRuleExhibition(exhibitionId)) && cmsAppProperties.getUseRuleExhibitionSwitch()) {
                RuleExhibitionQueryParam param = RuleExhibitionQueryParam.builder()
                        .branchCode(branchCode).exhibitionIdList(Lists.newArrayList(exhibitionId))
                        .pageNum(pageNum).pageSize(pageSize).build();
                apiRPCResult = ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProducts(param);
                if (log.isDebugEnabled()) {
                    log.debug("listProducts，调用ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProducts，入参：{}，出参：{}",
                            JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
                }
                if (!apiRPCResult.isSuccess()) {
                    String message = MessageFormat.format("普通商品查询失败，param：{0}，出参：{1}", JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
                    throw new AppException(message, XyyJsonResultCodeEnum.PAGING_USING_EXHIBITION_PRODUCTS_ERROR);
                }
            } else {
                ExhibitionProductQueryParam exhibitionProductQueryParam = ExhibitionProductQueryParam.builder()
                        .branchCode(branchCode).exhibitionIdStr(exhibitionId).build();
                apiRPCResult = productExhibitionGroupBusinessAdminApi.pagingUsingExhibitionProducts(exhibitionProductQueryParam, pageNum, pageSize);
                if (!apiRPCResult.isSuccess()) {
                    String message = MessageFormat.format("普通商品查询失败，exhibitionProductQueryParam：{0}，pageNum：{1}，pageSize：{2}，出参：{3}",
                            JSONObject.toJSONString(exhibitionProductQueryParam), pageNum, pageSize, JSONObject.toJSONString(apiRPCResult));
                    throw new AppException(message, XyyJsonResultCodeEnum.PAGING_USING_EXHIBITION_PRODUCTS_ERROR);
                }
            }
            pageInfo = apiRPCResult.getData();
        } else {
            // 2C侧
            ExhibitionProductQueryParam exhibitionProductQueryParam = ExhibitionProductQueryParam.builder()
                    .branchCode(branchCode).exhibitionIdStr(exhibitionId).merchantId(merchantId)
                    .isNeedExpress(true).isNeedQualityShopTitleTag(true).build();
            boolean usingProductEndStrategy = LayoutComponentEnum.isUsingProductEndStrategy(moduleSourceType);
            boolean isUseRecommendedProductData = usingProductEndStrategy && BooleanUtils.isTrue(isRecommend);
            if (isUseRecommendedProductData) {
                pageInfo = this.listRecommendToYouSkuInfos(branchCode, merchantId, pageNum, pageSize, terminalType);
                cmsListProductDto.setIsRecommend(true);
                String recommendTip = cmsAppProperties.getRecommendTip();
                cmsListProductDto.setTip(recommendTip);
            } else {
                if (BooleanUtils.isTrue(RuleExhibitionUtil.isRuleExhibition(exhibitionId)) && cmsAppProperties.getUseRuleExhibitionSwitch()) {
                    RuleExhibitionQueryParam param = RuleExhibitionQueryParam.builder().merchantId(merchantId)
                            .branchCode(branchCode).exhibitionIdList(Lists.newArrayList(exhibitionId))
                            .isNeedExpress(true).isNeedQualityShopTitleTag(true)
                            .pageNum(pageNum).pageSize(pageSize).build();
                    ApiRPCResult<PageInfo<ListProduct>> apiRPCResult = ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProducts(param);
                    if (log.isDebugEnabled()) {
                        log.debug("listProducts，调用ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProducts，入参：{}，出参：{}",
                                JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
                    }
                    if (!apiRPCResult.isSuccess()) {
                        String message = MessageFormat.format("普通商品查询失败，param：{0}，出参：{1}", JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
                        throw new AppException(message, XyyJsonResultCodeEnum.PAGING_USING_EXHIBITION_PRODUCTS_ERROR);
                    }
                    pageInfo = apiRPCResult.getData();
                } else {
                    if (Objects.isNull(merchantId)) {
                        ListSkuSearchData listSkuSearchData =
                                exhibitionBuineseApi.getExhibitionProductSkuInfoWrapper(exhibitionProductQueryParam.getExhibitionIdStr(),
                                        null, exhibitionProductQueryParam.getBranchCode(), pageNum, pageSize);
                        pageInfo = new PageInfo<>();
                        pageInfo.setList(listSkuSearchData.getSkuDtoList());
                        pageInfo.setPageNum(listSkuSearchData.getPageNum());
                        pageInfo.setPageSize(listSkuSearchData.getPageSize());
                        pageInfo.setTotal(listSkuSearchData.getCount());
                    } else {
                        ApiRPCResult<PageInfo<ListProduct>> apiRPCResult = productExhibitionGroupBusinessApi.pagingUsingExhibitionProducts(exhibitionProductQueryParam, pageNum, pageSize);
                        if (log.isDebugEnabled()) {
                            log.debug("listProducts，调用productExhibitionGroupBusinessApi.pagingUsingExhibitionProducts，入参：exhibitionProductQueryParam：{}，pageNum：{}，pageSize：{}，出参：{}",
                                    JSONObject.toJSONString(exhibitionProductQueryParam), pageNum, pageSize, JSONObject.toJSONString(apiRPCResult));
                        }
                        if (!apiRPCResult.isSuccess()) {
                            String message = MessageFormat.format("普通商品查询失败，exhibitionProductQueryParam：{0}，pageNum：{1}，pageSize：{2}，出参：{3}",
                                    JSONObject.toJSONString(exhibitionProductQueryParam), pageNum, pageSize, JSONObject.toJSONString(apiRPCResult));
                            throw new AppException(message, XyyJsonResultCodeEnum.PAGING_USING_EXHIBITION_PRODUCTS_ERROR);
                        }
                        pageInfo = apiRPCResult.getData();
                    }
                }

                List<ListProduct> pageInfoList = pageInfo.getList();
                if (usingProductEndStrategy && pageNum == 1 && CollectionUtils.isEmpty(pageInfoList)) {
                    // 若2C侧控销接口第一页就没数据，则走兜底策略，获取为你推荐商品数据。
                    pageInfo = listRecommendToYouSkuInfos(branchCode, merchantId, pageNum, pageSize, terminalType);
                    cmsListProductDto.setIsRecommend(true);
                    String recommendTip = cmsAppProperties.getRecommendTip();
                    cmsListProductDto.setTip(recommendTip);
                }
            }
        }
        cmsListProductDto.setPageInfo(pageInfo);
        return cmsListProductDto;
    }

    @Override
    public List<ListProduct> pagingUsingProducts(Boolean isAdmin, String branchCode, List<Long> skuIds, Long merchantId) {
        ProductsQueryParam productsQueryParam = ProductsQueryParam.builder().branchCode(branchCode).skuIds(skuIds).merchantId(merchantId)
                .isNeedExpress(true).isNeedQualityShopTitleTag(true).build();
        if (BooleanUtils.isTrue(isAdmin)) {
            // admin侧
            ApiRPCResult<List<ListProduct>> apiRPCResult = productExhibitionGroupBusinessAdminApi.pagingUsingProducts(productsQueryParam);
            if (!apiRPCResult.isSuccess()) {
                String message = MessageFormat.format("普通商品查询入参，isAdmin：{0}，branchCode：{1}，skuIds：{2}，merchantId：{3}",
                        isAdmin, branchCode, skuIds, merchantId);
                throw new AppException(message, XyyJsonResultCodeEnum.USING_PRODUCTS_ERROR);
            }
            return apiRPCResult.getData();
        } else {
            // 2C侧
            ApiRPCResult<List<ListProduct>> apiRPCResult = productExhibitionGroupBusinessApi.pagingUsingProducts(productsQueryParam);
            if (!apiRPCResult.isSuccess()) {
                String message = MessageFormat.format("普通商品查询入参，isAdmin：{0}，branchCode：{1}，skuIds：{2}，merchantId：{3}",
                        isAdmin, branchCode, skuIds, merchantId);
                throw new AppException(message, XyyJsonResultCodeEnum.USING_PRODUCTS_ERROR);
            }
            return apiRPCResult.getData();
        }
    }

    @Override
    public PageInfo<ListProduct> listRecommendToYouSkuInfos(String branchCode, Long merchantId, int pageNum, int pageSize, Integer terminalType) {
        PageInfo<ListProduct> pageInfo = new PageInfo<>(Lists.newArrayList());
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setTotal(0L);
        pageInfo.setPages(0);
        List<Long> csuIds = searchRecommendedRemoteService.listRecommendToYouSkuIds(terminalType, Constants.PAGE_TYPE,
                merchantId, branchCode, pageNum, pageSize, null);
        if (CollectionUtils.isEmpty(csuIds)) {
            return pageInfo;
        }
        //易碎品是否可见
        boolean isNotWatchFragileGoods = merchantBusinessRemoteService.checkIsNotWatchFragileGoods(merchantId);
        //组装商品数据
        int pages = cmsAppProperties.getRecommendMaxPages();
        int total = pages * pageSize;
        List<ListProduct> products = productForLayoutRemoteService.fillProductInfoV2(csuIds, merchantId, isNotWatchFragileGoods, branchCode, true, true);
        pageInfo.setList(products);
        pageInfo.setTotal(total);
        pageInfo.setPages(pages);
        return pageInfo;
    }

    @Override
    public Map<Long, GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdList(List<Long> skuIdList, List<Integer> statusList, Long merchantId) {
        if (CollectionUtils.isEmpty(skuIdList) || CollectionUtils.isEmpty(statusList)) {
            return Maps.newHashMap();
        }
        Map<Long, GroupBuyingInfoDto> result = Maps.newHashMapWithExpectedSize(skuIdList.size());
        List<List<Long>> skuIdsLists = Lists.partition(skuIdList, 20);
        if (merchantId != null && merchantId > 0L) {
            for (List<Long> skuIdsList : skuIdsLists) {
                Map<Long, GroupBuyingInfoDto> tempMap = marketingService.getGroupBuyingInfoBySkuIdList(skuIdsList, statusList, merchantId);
                result.putAll(tempMap);
            }
        } else {
            for (List<Long> skuIdsList : skuIdsLists) {
                Map<Long, GroupBuyingInfoDto> tempMap = marketingService.getGroupBuyingInfoBySkuIdListForCms(skuIdsList, statusList);
                result.putAll(tempMap);
            }
        }
        return result;
    }

    @Override
    public Map<Long, GroupBuyingInfoDto> getMarketingActivityInfoBySkuIdList(List<Long> skuIdList, List<Integer> statusList, Long merchantId, Set<Integer> activityTypeSet, Set<Long> gaoMaoSkuIdSet) {
        if (CollectionUtils.isEmpty(skuIdList) || CollectionUtils.isEmpty(statusList) || CollectionUtils.isEmpty(activityTypeSet)) {
            return Maps.newHashMap();
        }
        Map<Long, GroupBuyingInfoDto> result = Maps.newHashMapWithExpectedSize(skuIdList.size());
        List<List<Long>> skuIdsLists = Lists.partition(skuIdList, 20);
        if (merchantId != null && merchantId > 0L) {
            for (List<Long> skuIdsList : skuIdsLists) {
                Map<Long, GroupBuyingInfoDto> tempMap = marketingService.getActCardInfoBySkuIdList(skuIdsList, statusList, merchantId, activityTypeSet, gaoMaoSkuIdSet);
                result.putAll(tempMap);
            }
        } else {
            for (List<Long> skuIdsList : skuIdsLists) {
                Map<Long, GroupBuyingInfoDto> tempMap = marketingService.getActCardInfoBySkuIdListForCms(skuIdsList, statusList, activityTypeSet, gaoMaoSkuIdSet);
                result.putAll(tempMap);
            }
        }
        return result;
    }

    @Override
    public List<CmsListProductVO> fillListProductsMarketingActivityInfo(List<CmsListProductVO> products,
                                                                        Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap) {
        if (CollectionUtils.isEmpty(products) || MapUtils.isEmpty(csuIdToGroupBuyingInfoMap)) {
            return products;
        }
        Long id;
        GroupBuyingInfoDto groupBuyingInfoDto;
        for (CmsListProductVO product : products) {
            id = product.getId();
            groupBuyingInfoDto = csuIdToGroupBuyingInfoMap.get(id);
            if (Objects.nonNull(groupBuyingInfoDto)) {
                if (Objects.equals(groupBuyingInfoDto.getActivityType(), MarketingEnum.PING_TUAN.getCode())) {
                    CmsListProductVOHelper.setActPt(product, groupBuyingInfoDto);
                } else if (Objects.equals(groupBuyingInfoDto.getActivityType(), MarketingEnum.PI_GOU_BAO_YOU.getCode())) {
                    CmsListProductVOHelper.setActPgby(product, groupBuyingInfoDto);
                }
            }
        }
        return products;
    }

    public List<CmsListProductVO> hideListProductsMarketingGroupBuyingActivityPriceInfo(List<CmsListProductVO> products) {
        if (CollectionUtils.isEmpty(products)) {
            return products;
        }
        for (CmsListProductVO product : products) {
            CmsListProductVOHelper.hideActPtPriceInfo(product);
        }
        return products;
    }

    @Override
    public List<CmsListProductVO> fillListProductsShopInfo(List<CmsListProductVO> products) {
        if (CollectionUtils.isEmpty(products)) {
            return Lists.newArrayList();
        }
        // 填充商品的店铺信息
        Set<String> shopCodes = products.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShopCode()))
                .map(CmsListProductVO::getShopCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(shopCodes)) {
            return products;
        }
        List<ShopInfoDTO> shopInfoDTOS = shopQueryRemoteService.queryShopInfosByShopCodes(Lists.newArrayList(shopCodes));
        if (CollectionUtils.isEmpty(shopInfoDTOS)) {
            return products;
        }
        Map<String, ShopInfoDTO> shopCodeToShopInfoDTOMap = shopInfoDTOS.stream().filter(item -> item != null && item.getShopCode() != null)
                .collect(Collectors.toMap(ShopInfoDTO::getShopCode, Function.identity(), (a, b) -> a));
        if (MapUtils.isEmpty(shopCodeToShopInfoDTOMap)) {
            return products;
        }
        products.stream().filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShopCode()))
                .forEach(item -> {
                    ShopInfoDTO shopInfoDTO = shopCodeToShopInfoDTOMap.get(item.getShopCode());
                    if (Objects.nonNull(shopInfoDTO)) {
                        item.setShopName(shopInfoDTO.getShowName());
                        item.setShopUrl(shopInfoDTO.getPcLink());
                        item.setShopAppIndexUrl(shopInfoDTO.getNewAppLink());
                        item.setShopPcIndexUrl(shopInfoDTO.getPcLink());
                    }
                });
        return products;
    }

    @Override
    public List<CmsListProductVO> fillListProductsTagInfo(Long merchantId, List<CmsListProductVO> products, Boolean isQueryShopDataTags) {
        if (CollectionUtils.isEmpty(products)) {
            return Lists.newArrayList();
        }
        //查询数据标签
        SearchCsuDataTagQueryParam queryParam = SearchCsuDataTagQueryParam.builder()
                .merchantId(merchantId).csuList(SearchCsuDataTagCsuDTOHelper.creates(products))
                .isQueryShopDataTags(isQueryShopDataTags).build();
        Map<Long, Map<String, Object>> dataTagMaps = dataService.batchGetCsuDataTags(queryParam);
        products.stream()
                .forEach(product -> CmsListProductVOHelper.setTagInfo(product, dataTagMaps.get(product.getId())));
        return products;
    }


    /**
     * 判定商品是否在进入索引的商品组中
     *
     * @param exhibitionIdStrList
     * @param productIds
     * @return
     */
    @Override
    public List<Long> listUsingExhibitionProductIds(List<String> exhibitionIdStrList, List<Long> productIds) {
        if (CollectionUtils.isEmpty(exhibitionIdStrList) || CollectionUtils.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        List<String> ruleExhibitionIds = exhibitionIdStrList.stream().filter(item -> BooleanUtils.isTrue(RuleExhibitionUtil.isRuleExhibition(item)) && cmsAppProperties.getUseRuleExhibitionSwitch())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ruleExhibitionIds)) {
            RuleExhibitionQueryParam param = RuleExhibitionQueryParam.builder()
                    .exhibitionIdList(ruleExhibitionIds).skuIdList(productIds)
                    .pageNum(1).pageSize(productIds.size()).build();
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            ApiRPCResult<PageInfo<Long>> apiRPCResult = ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProductIds(param);
            stopWatch.stop();
            if (log.isDebugEnabled()) {
                log.debug("listUsingExhibitionProductIds，调用ruleExhibitionGroupBusinessApi.pagingUsingExhibitionProductIds，入参：{}，出参：{}，耗时：{}",
                        JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult), stopWatch);
            }
            if (!apiRPCResult.isSuccess()) {
                throw new AppException(XyyJsonResultCodeEnum.FAIL, "判定商品是否在进入索引的商品组中失败：" + apiRPCResult.getMsg());
            }
            return apiRPCResult.getData().getList();
        } else {
            ExhibitionGroupListUsingProductIdsQueryParam queryParam = ExhibitionGroupListUsingProductIdsQueryParam
                    .builder().exhibitionIdStrList(exhibitionIdStrList).productIds(productIds).build();
            ApiRPCResult<List<Long>> apiRPCResult = productExhibitionGroupBusinessApi.listUsingExhibitionProductIds(queryParam);
            if (log.isDebugEnabled()) {
                log.debug("listUsingExhibitionProductIds，调用productExhibitionGroupBusinessApi.listUsingExhibitionProductIds，入参：{}，出参：{}",
                        JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
            }
            if (!apiRPCResult.isSuccess()) {
                throw new AppException(XyyJsonResultCodeEnum.FAIL, "判定商品是否在进入索引的商品组中失败：" + apiRPCResult.getMsg());
            }
            return apiRPCResult.getData();
        }
    }


}
