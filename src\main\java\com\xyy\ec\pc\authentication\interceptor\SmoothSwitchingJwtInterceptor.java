package com.xyy.ec.pc.authentication.interceptor;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xyy.ec.pc.authentication.consts.Constants;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.authentication.service.XyyJwtIdentityValidatorImpl;
import com.xyy.ec.pc.authentication.utils.PrincipalUtils;
import com.xyy.ec.pc.authentication.utils.ServletUtils;
import com.xyy.ec.pc.base.PasswordVerifier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/10 15:32
 * @File SmoothSwitchingJwtInterceptor.class
 * @Software IntelliJ IDEA
 * @Description
 */
@Slf4j
@Component
public class SmoothSwitchingJwtInterceptor implements HandlerInterceptor {

    @Value("${jwt_open:false}")
    private Boolean openJwt;

    @Value("${jwt_grayscale_value:0}")
    private Integer jwtGrayscaleValue;
    
    @ApolloJsonValue("${jwt_smooth_switching_url:[]}")
    private List<String> jwtSmoothSwitchingUrl;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private XyyJwtIdentityValidatorImpl xyyJwtIdentityValidator;

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {

        log.info("进入SomeSwitchingJwtInterceptor拦截器前置处理器, 响应提交结果 -> {}", httpServletResponse.isCommitted());
        try {
            if (jwtSmoothSwitchingUrl.size() == 0 || jwtSmoothSwitchingUrl.contains(httpServletRequest.getRequestURI())) {
                login();
            }
        }
        catch (Exception e) {
            log.error("JWT平滑登录异常，{}", e.getMessage());
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }

    private void login() {

        String token = tokenService.getToken();
        String xyyPrincipal = tokenService.getXyyPrincipal();
        
        if (openJwt) {

            // TODO 判断是否进行灰度
            Long xyyPrincipalAccountId = tokenService.getXyyPrincipalAccountId();
            if (!(xyyPrincipalAccountId != null && xyyPrincipalAccountId % 100 < jwtGrayscaleValue)) {
                log.info("账号不进行灰度[{}]", xyyPrincipalAccountId);
                return;
            }
            // TODO 判断是否进行灰度
                
            if (StrUtil.isBlank(token) && StrUtil.isNotBlank(xyyPrincipal)) {
                String[] split = xyyPrincipal.split(Constants.SPLIT);
                // 检查cookie中是否有merchantId
                if (split.length != 3) {
                    return;
                }
                String accountId = split[0];
                String merchantId = split[2];
                try {
//                    log.info("使用JWT认证SmoothSwitchingJwtInterceptor, {}", token);
                    // 存在账号ID，与会员ID，且已通过之前的认证，直接设置xyy_token
                    if (StrUtil.isAllBlank(accountId, merchantId)) {
                        return;
                    }
                    JwtPrincipal jwtPrincipal = xyyJwtIdentityValidator.getPrincipal(Long.valueOf(accountId), Long.valueOf(merchantId));
                    if (jwtPrincipal == null) {
                        return;
                    }
                    jwtPrincipal.setAccountId(Convert.toLong(accountId, null));
                    jwtPrincipal.setMerchantId(Convert.toLong(merchantId, null));
                    jwtPrincipal.setMechantId(Convert.toLong(merchantId, null));
                    // 填充登录时间、登录设备信息、IP、地址等信息
                    PrincipalUtils.initJwtPrincipal(jwtPrincipal);
                    // 重新创建token
                    String newToken = tokenService.createToken(jwtPrincipal);
                    // 写入cookie
                    ServletUtils.writeLoginCookie(jwtPrincipal, new PasswordVerifier(null, null), newToken);
                    
                    log.info("JWT平滑登录成功, accountId -> {}, merchantId -> {}", accountId, merchantId);
                } catch (Exception e) {
                    log.error("JWT平滑登录异常, accountId -> {}, merchantId -> {}", accountId, merchantId, e);
                }
            }
        }
    }
}