package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.dto.QualificationInfoDto;
import com.xyy.ec.order.business.query.Page;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.popshop.service.PopShopService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.DateUtils;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.system.business.api.CompanyBranchBusinessApi;
import com.xyy.ec.system.business.dto.CompanyBranchBusinessDto;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 我的供应商
 *
 * <AUTHOR>
 *
 */
@RequestMapping("/merchant/center/license")
@Controller
public class SupplierController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(SupplierController.class);

	@Resource
	private XyyIndentityValidator xyyIndentityValidator;

	@Resource
	private PopShopService popShopService;

	@Reference(version = "1.0.0")
	private CompanyBranchBusinessApi companyBranchService;

	@Reference(version = "1.0.0")
	private OrderBusinessApi orderBusinessApi;

	@Value("${POP_HOST}")
	private String popHost;

	/**
	 * 我的资质页面入口
	 *
	 * @return
	 */
	@RequestMapping(value = "/supplierList.htm", method = RequestMethod.GET)
	public ModelAndView supplierList(Page page, String companyName, ModelMap model, HttpServletRequest request) throws Exception {
		MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
		if (merchant == null) {
			return new ModelAndView(new RedirectView("/login/login.htm",true,false));
		}
		String name = "";
		if (StringUtils.isNotEmpty(companyName)) {
			name = new String(companyName.getBytes("ISO-8859-1"), "UTF-8");
		}
		CompanyBranchBusinessDto companyBranch = new CompanyBranchBusinessDto();
		companyBranch.setCompanyName(name);
		companyBranch.setBranchCode(merchant.getRegisterCode());
    	int oldOffset = page.getOffset();
//		page.setOffset((oldOffset > 0 ? (page.getOffset() - 1) : 0) * page.getLimit());
//		PageInfo pageInfoParam = new PageInfo<>();
//		pageInfoParam.setPageNum(page.getOffset());
//		pageInfoParam.setPageSize(page.getLimit());
//		PageInfo<CompanyBranchBusinessDto> modelPageInfo = companyBranchService.findCompanyBranch(companyBranch, pageInfoParam, merchant.getId());

		 int pageNum = Objects.isNull(page.getOffset())?1:page.getOffset();
		 int pageSize = Objects.isNull(page.getLimit())?10:page.getLimit();
		PageInfo<CompanyBranchBusinessDto> modelPageInfo = popShopService.pageQueryCompany(merchant.getId(),name,pageNum,pageSize);
		String requestUrl = this.getRequestUrl(request);
		Page<CompanyBranchBusinessDto> pageInfo = new Page<>();
		pageInfo.setRequestUrl(requestUrl);
		pageInfo.setOffset(oldOffset);
		pageInfo.setRows(modelPageInfo.getList());
		pageInfo.setTotal(modelPageInfo.getTotal());
		pageInfo.setPageCount(modelPageInfo.getPages());
		model.put("pager", pageInfo);
		model.put("center_menu", "supplier");
		model.put("companyBranch", companyBranch);
		model.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
		return new ModelAndView("/supplier.ftl", model);
	}


	@RequestMapping(value = "/aptitude.htm", method = RequestMethod.GET)
	public ModelAndView aptitudeList(String orgId, ModelMap model, HttpServletRequest request) {
        try {
            List<QualificationInfoDto> corporationQualificationList = orderBusinessApi.getCorporationQualification(orgId);
            if (corporationQualificationList != null) {
				//todo 暂时这样处理，后续增加版本号处理
				for (QualificationInfoDto infoDto : corporationQualificationList) {
					String url = infoDto.getUrl();
					if (StringUtils.isNotEmpty(url) && url.contains(",")) {
						url = url.split(",")[0];
						infoDto.setUrl(url);
					}
				}
                model.put("corporationQualificationList", corporationQualificationList);
            }
            model.put("orgId", orgId);
        } catch (Exception e) {
            LOGGER.error("获取我的供应商资质异常：", e);
        }
        return new ModelAndView("/aptitude.ftl", model);
    }


	/**
	 * 下载资质图片
	 *
	 * @param request
	 * @param response
	 * @param orgId
	 * @throws InvocationTargetException
	 * @throws NoSuchMethodException
	 * @throws NoSuchFieldException
	 * @throws IllegalAccessException
	 * @throws IOException
	 */
	@RequestMapping("/licenseImg.htm")
	@ResponseBody
	public void downloadAuditImg(HttpServletRequest request, HttpServletResponse response, String imgUrl, String orgId, String names) {
		try {
//			CompanyBranchBusinessDto companyBranchBusinessDto = companyBranchService.selectByOrgId(orgId);
//			if(Objects.nonNull(companyBranchBusinessDto) && StringUtils.isNotEmpty(companyBranchBusinessDto.getCompanyName())) {
//				String pinYinCompanyName = converterToSpell(companyBranchBusinessDto.getCompanyName());
//				String[] quanPinYinCompanyName = pinYinCompanyName.split(",");
			//todo 暂时把导出文件夹名字写死
				String downloadFilename = "supplierQualificationImage" + DateUtils.format(new Date(), "yyyyMMddHHmmss") + ".zip";
				//String downloadFilename = HZ2PY.getAllFirstLetter(companyName) + ".zip";// 文件的名称
				downloadFilename = new String(downloadFilename.getBytes("UTF-8"), "ISO-8859-1"); //文件名转码
				response.setContentType("application/octet-stream");// 指明response的返回对象是文件流
				response.setHeader("Content-Disposition", "attachment;filename=" + downloadFilename);// 设置在下载框默认显示的文件名
				ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());
				String[] urlsArray = StringUtil.splitStringToString(imgUrl, ",");
				String[] namesArray = StringUtil.splitStringToString(names, ",");
				for (int i = 0; i < urlsArray.length; i++) {
					URL url = new URL(urlsArray[i]);
					HttpURLConnection uRLConnection = (HttpURLConnection) url.openConnection();
					if (uRLConnection.getResponseCode() == Constants.ReturnCodes.CODE_200) {
						ZipEntry zipEntry = new ZipEntry(namesArray[i] + (i + ".jpg"));
						zos.putNextEntry(zipEntry);
						InputStream fis = uRLConnection.getInputStream();
						byte[] buffer = new byte[1024];
						int r = 0;
						while ((r = fis.read(buffer)) != -1) {
							zos.write(buffer, 0, r);
						}
						fis.close();
					}
				}

				zos.flush();
				zos.close();
//			}
		} catch (Exception e) {
			LOGGER.error("数据导出异常", e);
		}
	}

		public String converterToSpell(String chines){
			StringBuffer pinyinName = new StringBuffer();
			char[] nameChar = chines.toCharArray();
			HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
			defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
			defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
			for (int i = 0; i < nameChar.length; i++) {
				if (nameChar[i] > 128) {
					try {
						// 取得当前汉字的所有全拼
						String[] strs = PinyinHelper.toHanyuPinyinStringArray(nameChar[i], defaultFormat);
						if (strs != null) {
							for (int j = 0; j < strs.length; j++) {
								pinyinName.append(strs[j]);
								if (j != strs.length - 1) {
									pinyinName.append(",");
								}
							}
						}
					} catch (BadHanyuPinyinOutputFormatCombination e) {
						e.printStackTrace();
					}
				} else {
					pinyinName.append(nameChar[i]);
				}
				pinyinName.append(" ");
			}
			return parseTheChineseByObject(discountTheChinese(pinyinName.toString()));
		}

	public  List<Map<String, Integer>> discountTheChinese(String theStr) {
		// 去除重复拼音后的拼音列表
		List<Map<String, Integer>> mapList = new ArrayList<Map<String, Integer>>();
		// 用于处理每个字的多音字，去掉重复
		Map<String, Integer> onlyOne = null;
		String[] firsts = theStr.split(" ");
		// 读出每个汉字的拼音
		for (String str : firsts) {
			onlyOne = new Hashtable<String, Integer>();
			String[] china = str.split(",");
			// 多音字处理
			for (String s : china) {
				Integer count = onlyOne.get(s);
				if (count == null) {
					onlyOne.put(s, new Integer(1));
				} else {
					onlyOne.remove(s);
					count++;
					onlyOne.put(s, count);
				}
			}
			mapList.add(onlyOne);
		}
		return mapList;
	}

	public  String parseTheChineseByObject(List<Map<String, Integer>> list) {
		Map<String, Integer> first = null; // 用于统计每一次,集合组合数据
		// 遍历每一组集合
		for (int i = 0; i < list.size(); i++) {
			// 每一组集合与上一次组合的Map
			Map<String, Integer> temp = new Hashtable<String, Integer>();
			// 第一次循环，first为空
			if (first != null) {
				// 取出上次组合与此次集合的字符，并保存
				for (String s : first.keySet()) {
					for (String s1 : list.get(i).keySet()) {
						String str = s + s1;
						temp.put(str, 1);
					}
				}
				// 清理上一次组合数据
				if (temp != null && temp.size() > 0) {
					first.clear();
				}
			} else {
				for (String s : list.get(i).keySet()) {
					String str = s;
					temp.put(str, 1);
				}
			}
			// 保存组合数据以便下次循环使用
			if (temp != null && temp.size() > 0) {
				first = temp;
			}
		}
		String returnStr = "";
		if (first != null) {
			// 遍历取出组合字符串
			for (String str : first.keySet()) {
				returnStr += (str + ",");
			}
		}
		if (returnStr.length() > 0) {
			returnStr = returnStr.substring(0, returnStr.length() - 1);
		}
		return returnStr;
	}

	}
