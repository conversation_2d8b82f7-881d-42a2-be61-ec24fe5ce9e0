<!DOCTYPE HTML>
<html>
<head>
        <#include "/common/common.ftl" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<title>成功提交订单</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
		<!--
		<link rel="stylesheet" href="/static/css/order/headerAndFooter.css?t=${t_v}" />
		-->
        <link rel="stylesheet" href="/static/css/order/resultok.css?t=${t_v}" />
		<style type="text/css">
                /*中奖弹窗*/
                #zhongjiang{width: 636px;height: 501px;display: none;background: none;box-shadow: none;}
                #zhongjiang .fltcbox{position: relative;width: 636px;height: 501px;}
                #zhongjiang a.close-btn{width: 37px;height: 37px;position: absolute;left:570px;top:0px;}
                #zhongjiang a.fl-detail{display: block;width: 636px;height: 501px;}
                #zhongjiang a.fl-detail img{width: 636px;height: 501px;margin-top: 0px;}
                .spe-zhongjiang{margin-left: -328px;}
         </style>
         <style>
            #app {
                background: #f2f2f2;
            }
            #footer .foot_top {
                padding-top: 0;
            }
         </style>
<#--        <script type="text/javascript" src="/static/js/order/resultok.js?t=${t_v}"></script>-->
        <script type="text/javascript">
           	var ctx="/static";
            var cdnProduct="${cdnProduct}";
        </script>
        <script type="text/javascript" src="/static/js/qtShopDetail.js?t=${t_v}"></script>
	</head>
	<body <#if order.orderExtend?? && order.orderExtend.balance !=0>onload="showBalanceTitle()"</#if> >
		<div class="container">
			<!--头部导航区域开始-->
			<div class="headerBox" id="headerBox">
			<#include "/common/header.ftl" />
			</div>
			<!--头部导航区域结束-->

            <!--头部步骤条-->
            <div class="topbzbox">
                <div class="warp">
                    <div class="bzcol1"><a href="#"><img src="/static/images/logo_login.png" ></a></div>
                    <div class="bzcol2"></div>
                    <div class="bzcol3">收银台</div>
                    <div class="bzcol4"><img src="/static/images/buzhou4.png" ></div>
                </div>
            </div>

            <!--主体部分开始-->
            <div class="main">
                <!--中间内容-->
                <div class="m-mainbox" style="width: 1220px">
                    <div class="warpbox">
                        <div class="row1"><img src="/static/images/zhifuwancheng.png" alt="">支付成功，请耐心等待客服审核！</div>
                        <div class="row2">
                            <span class="spew">订单编号：</span>
                            <span id="orderNo">${order.orderNo }</span>
                        </div>
                        <div class="row3">
                            <span class="spew">实付金额：</span>
                            <spa  class="price">￥</spa>
                            <span class="price">${order.cashPayAmount}</span>
                        </div>
                        <div class="row5" hidden="hidden">
                            <div class="controls">
                                <input type="text" id="paycode" placeholder="" data-rules="required" name="paycode" value="${payType }" />
                                <input type="text" id="orderId" placeholder="" data-rules="required" name="orderId" value="${order.id}" />
                                <input type="text" id="balancefree" placeholder="" data-rules="required" name="balancefree" <#if order.orderExtend?? && order.orderExtend.balancefree !=0> value="1" </#if> />
                            </div>
                        </div>
                        <div class="row5">
                            <#if rechargeType?? && rechargeType == 2>
                                <a href="/pc/virtual/gold/virtualGold/index" class="goorder">查看购物金</a>
                            <#else>
                                <a href="/merchant/center/order/index.htm" class="goorder" id="qry_btn" >查看订单</a>
                            </#if>
                            <a href="/" class="goindex" id="index_btn" >返回首页</a>
                        </div>
                    </div>
                </div>

				<!--底部提示-->
				<div class="b-infobox" style="width: 1220px;margin-bottom: 10px;">
					<i class="sui-icon icon-pc-info-circle"></i> 提示：商品签收时如发现商品少发、错发、包装破损、商品与图片不符等问题，请第一时间与我们联系。
				</div>
                <div class="resultok-recommend-list">
                    <iframe 
                        style="border:none" 
                        id="external-frame" 
                        src="/newstatic/#/resultokRecommendList" 
                        width="100%" 
                        height="850px">
                    </iframe>
                </div>
			</div>
			<!--主体部分结束-->
			<!--底部导航区域开始-->
			<div class="footer" id="footer">
                <!--底部导航区域开始-->
				<#include "/common/footer.ftl" />
			</div>
			<!--底部导航区域结束-->
		</div>
        <!--提交成功提示-->
        <#--		<div id="successModel" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade">-->
        <#--			<img src="/static/images/payment/successtc.png" alt="支付成功 ">-->
        <#--		</div>-->
        <#--支付成功返券或红包-->
        <div id="couponDialog" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static"
             class="sui-modal hide fade">
            <div class="couponWarp">
                <div class="couponTitle">
<#--                    哇塞，天降券包 <br/>优惠券已到账，快去使用吧！-->
                    恭喜！多个福利红包<br/>已发放至您的账户中，快去使用吧~
                </div>
                <div class="couponList">
                    <ul>
                        <#--                        <li>-->
                        <#--                            <div class="li_left">-->
                        <#--                                <div>¥<span style="font-size: 32px;line-height: 56px;padding-left: 4px">20</span></div>-->
                        <#--                                <div>满200可用</div>-->
                        <#--                            </div>-->
                        <#--                            <div class="li_right">-->
                        <#--                                <div style="font-size: 14px;color: #292933;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:2;"><span class="tag">跨店券</span>华润三九专营店POP可编辑可编辑券名称，最多10个字多最多10个字多10</div>-->
                        <#--                                <div style="margin-top: 19px">全店商品可用</div>-->
                        <#--                                <div style="margin-top: 10px">2021/11/11-2021/12/12</div>-->
                        <#--                                <div class="make">立即使用</div>-->
                        <#--                            </div>-->
                        <#--                        </li>-->
                    </ul>
                </div>
                <div class="closeWarp">
                    <div class="close"></div>
                </div>
            </div>
        </div>
        <!--领钱提示-->
		<#if order.orderExtend?? && order.orderExtend.balance !=0>
			<div id="getModel" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade">
				<div class="lqbox">
					<div class="titlebox">提示：</div>
					<div class="bodybox">确认收货后，请前往‘我的订单’页面点击‘领取余额’，领取${order.orderExtend.balance }元余额。</div>
					<div class="footbox"><a href="javascript:void(0);" class="closeTC">我知道了</a></div>
					<div class="yaowan"><img src="/static/images/payment/yaowan.png" alt="支付成功"></div>
				</div>
			</div>
		</#if>
        <!--中奖弹窗-->
        <#if order.orderExtend?? && order.orderExtend.balanceFree !=0>
        <div id="zhongjiang" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade spe-zhongjiang">
            <div class="fltcbox">
                <a href="#" class="close-btn"><img src="http://upload.ybm100.com/ybm/app/layout/20170619/lqtcolse.png" alt=""></a>
                <a href="/activity/free8Page.htm" class="fl-detail" target="_blank">
                    <img src="http://upload.ybm100.com/ybm/app/layout/20170619/tc8.png" alt="">
                </a>
            </div>
        </div>
		</#if>
	<script type="text/javascript">
        var dialogStatus = true;
        function showBalanceTitle(){
            setTimeout(function(){
                $("#getModel").modal('show');
            },2000);
            $(".closeTC").click(function(){
                $("#getModel").modal('hide');
                <#if dialog??>
                    if(dialogStatus){
                        $.alert({
                            body:'${dialog}'
                        });
                        dialogStatus = false;
                        $('.modal-header').remove();
                        $('.modal-footer').css('text-align','center').children().html('确定');
                    }
                </#if>
            });
        }
        $(function(){
            /*提交成功提示*/
            function getCount (){
                $.ajax({
                    type: "POST",
                    url: "/marketing/rebateVoucher/getMarketingRebateVoucherTemplateList",
                    data: {"orderNo": $('#orderNo').text()},
                    dataType: "json",
                    async: false,
                    success: function (data) {
                        if (data.status === "success") {
                            var list = data.data.list
                            if (list && Array.isArray(list)) {
                                var str = ''
                                list.forEach(function (item) {
                                    if (item.rightsType == 1) { // 券类型
                                        str += '<li class="couponType">' +
                                            ' <div class="li_left">' +
                                            '<div>' + (item.voucherState == 1 ? '折' : '¥') + '<span style="font-size: 32px;line-height: 56px;padding-left: 4px">' + item.moneyInVoucher + '</span></div>' +
                                            '<div>' + item.minMoneyToEnableDesc + '</div></div>' +
                                            '<div class="li_right">' +
                                            ' <div style="font-size: 14px;color: #292933;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:2;width: 100%;height: 38px"><span class="tag">' + item.voucherTypeDesc + '</span>' + item.voucherTitle + '</div>' +
                                            '<div style="margin-top: 15px;width: 65%;height: 37px;overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp:2;">' + item.voucherInstructions + '</div>' +
                                            '<div>' + (item.validDays > 0 ? item.validDayStr : item.validDateToString + '-' + item.expireDateToString) + '</div>' +
                                            '<div class="make" data_isLq="' + item.isLq + '" data_templateId="' + item.templateId + '" data_pcUrl="' + item.pcUrl + '">' + (item.isLq > 0 ? '立即使用' : '立即领取') + '</div>' +
                                            ' </div></li>'
                                    } else if (item.rightsType == 2) { //红包类型
                                        str += '<li class="redPackageType">' +
                                            ' <div class="redPackageIcon">' + '</div>' +
                                            '<div class="redPackageContent">' +
                                            ' <div class="title">' + item.templateName + '</div>' +
                                            '<div class="redPackageDate">' + ((item.validDateToString && item.expireDateToString) ? item.validDateToString + '-' + item.expireDateToString : item.validDayStr) + '</div>' +
                                            '<div class="redPackageBtn">' + (item.activityState == 3 ? '<span class="moneyIcon">¥</span>' + item.redPacketOriginMoney : '已抢光') + '</div>' +
                                            ' </div></li>'
                                    }
                                })
                                if (str) {
                                    $('.couponList ul').html(str)
                                    $("#couponDialog").modal('show');
                                    $('.make').on('click', function () {
                                        var isLq = $(this).attr('data_isLq')
                                        var templateId = $(this).attr('data_templateId')
                                        var pcUrl = $(this).attr('data_pcUrl')
                                        console.log(isLq)
                                        console.log(templateId)
                                        console.log(pcUrl)
                                        if (isLq > 0 && pcUrl) {
                                            location.href = pcUrl
                                        } else {
                                            $.ajax({
                                                type: "POST",
                                                url: "/merchant/center/voucher/receiveVoucher",
                                                data: {"voucherTemplateId": templateId},
                                                dataType: "json",
                                                async: false,
                                                success: function (res) {
                                                    console.log(res)
                                                    if (res && res.status === 'success'){
                                                        alert(res.msg);
                                                        getCount()
                                                    } else {
                                                        alert(res.errorMsg || res.msg || "领取失败");
                                                    }
                                                }
                                            })
                                        }
                                    })
                                }
                            }
                        }
                    }
                });
            }
            console.log('1111111111')
            getCount()

            <#--setTimeout(function(){-->
            <#--    $("#successModel").modal('hide');-->
            <#--    <#if dialog??>-->
            <#--        if(dialogStatus){-->
            <#--            $.alert({-->
            <#--                body:'${dialog}'-->
            <#--            });-->
            <#--            dialogStatus = false;-->
            <#--            $('.modal-header').remove();-->
            <#--            $('.modal-footer').css('text-align','center').children().html('确定');-->
            <#--        }-->
            <#--    </#if>-->
            <#--},2000);-->

            $('.closeWarp').click(function (){
                $("#couponDialog").modal('hide')
            })

        });
	    var balancefree=  parseFloat($("#balancefree").val());
		if((balancefree>0) && (balancefree!=0.03))
		{
            $("#zhongjiang").modal('show');
            $(".close-btn").click(function(){
                $("#zhongjiang").modal('hide');
                <#if dialog??>
                    if(dialogStatus){
                        $.alert({
                            body:'${dialog}'
                        });
                        dialogStatus = false;
                        $('.modal-header').remove();
                        $('.modal-footer').css('text-align','center').children().html('确定');
                    }
                </#if>
            })
		}
		<#if gainVoucher??>
		    $(function(){
                $.alert({
                    body:'${gainVoucher}',
                    hide:function () {
                        <#if dialog??>
                            if(dialogStatus){
                                $.alert({
                                    body:'${dialog}'
                                });
                                dialogStatus = false;
                                $('.modal-header').remove();
                                $('.modal-footer').css('text-align','center').children().html('确定');
                            }
                        </#if>
                    }
                });
                $('.modal-header').remove();
                $('.modal-footer').css('text-align','center').children().html('我知道了');
            })
		</#if>
        //qt埋点三期
        //页面曝光
        function exposureBurying(){
            try{
                let spm_cnt = "1_4." +"orderPaySuccess_"+$('#orderNo').text()+"-0_0.0.0." + window.getSpmE()
                let data = {
                    spm_cnt: spm_cnt,
                    }
                aplus_queue.push({
                    action: 'aplus.record',
                    arguments: ['page_exposure', 'EXP', data]
                })
            }catch(e){
                console.log(e)
            }
        }
        $(document).ready(function(){
            exposureBurying()
        })
        </script>
	</body>
</html>