package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.cs.api.dto.HolidayDTO;
import com.xyy.ec.cs.api.dto.PlatformInWorkorderDetailDTO;
import com.xyy.ec.cs.api.order.CsOrderRefundApi;
import com.xyy.ec.merchant.bussiness.api.ReceiptInformationBusinessApi;
import com.xyy.ec.merchant.bussiness.api.account.LoginAccountApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.ReceiptInformationBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.account.LoginAccountDto;
import com.xyy.ec.order.business.api.*;
import com.xyy.ec.order.business.api.ecp.RefundReasonBusinessApi;
import com.xyy.ec.order.business.api.ecp.order.EcpOrderHandleApi;
import com.xyy.ec.order.business.common.ResultDTO;
import com.xyy.ec.order.business.config.OrderEnum;
import com.xyy.ec.order.business.config.YNEnum;
import com.xyy.ec.order.business.dto.*;
import com.xyy.ec.order.business.dto.pop.PopAddressDto;
import com.xyy.ec.order.business.enums.refund.FreightRefundTypeEnum;
import com.xyy.ec.order.business.enums.refund.RefundModeEnums;
import com.xyy.ec.order.business.param.MerchantAuditRefundOrderParam;
import com.xyy.ec.order.core.dto.Order;
import com.xyy.ec.order.core.dto.OrderRefundDetailDto;
import com.xyy.ec.order.core.dto.OrderRefundDto;
import com.xyy.ec.order.core.dto.cart.CodeItemDto;
import com.xyy.ec.order.core.util.EntityConvert;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.base.Sort;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.constants.CodeMapConstants;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.*;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.service.OrderService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.product.business.api.ecp.skucategory.EcpCategoryRelationBusinessApi;
import com.xyy.ec.product.business.dto.SkuCategoryRelationBusinessDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: hamer
 * @Date: 2018/11/12 16:30
 * @Description:
 */
@Controller
@RequestMapping("/merchant/center/order")
public class RefundOrderController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(RefundOrderController.class);

    @Reference(version = "1.0.0")
    private OrderRefundBusinessApi orderRefundBusinessApi;

    @Reference(version = "1.0.0")
    private OrderRefundBankBusinessApi orderRefundBankBusinessApi;

    @Reference(version = "1.0.0")
    private OrderRefundExpressBusinessApi orderRefundExpressBusinessApi;

    @Autowired
    private XyyConfig.CdnConfig cdnConfig;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;

    @Reference(version = "1.0.0")
    private ReceiptInformationBusinessApi receiptInformationBusinessApi;

    @Reference(version = "1.0.0")
    private Order618PromBusinessApi order618PromBusinessApi;

    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;

    @Reference(version = "1.0.0")
    private OrderRefundOperateLogBusinessApi logBusinessApi;

    @Reference(version = "1.0.0")
    private EcpOrderHandleApi ecpOrderHandleApi;

    @Reference(version = "1.0.0")
    private OrderDetailBusinessApi orderDetailBusinessApi;
    @Reference(version = "1.0.0")
    private PopOrderBusinessApi popOrderBusinessApi;
    @Reference(version = "1.0.0")
    private RefundReasonBusinessApi refundReasonBusinessApi;
    @Reference(version = "1.0.0")
    EcpCategoryRelationBusinessApi categoryRelationBusinessApi;
    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;
    @Reference(version = "1.0.0")
    private OrderRefundRefuseReasonBusinessApi orderRefundRefuseReasonBusinessApi;
    @Reference(version = "1.0.0")
    private CsOrderRefundApi csOrderRefundApi;
    @Value("${platformIn.switch:false}")
    private Boolean platformInSwitch;

    @Value("#{'${platformIn.gray.branchCode}'.split(',')}")
    private List<String> platformInGrayBranchCode;
    @Value("${app.rebuild.switch:false}")
    private Boolean appRebuildSwitch;

    /**
     * 跳转到选择退款服务类型页面
     *
     * @param id       订单编号
     * @param modelMap
     * @return String
     * @Title: selectServiceType
     * @date 2016-12-21 下午2:43:44
     */
    @RequestMapping("/selectServiceType.htm")
    public String selectServiceType(Long id, ModelMap modelMap) {
        try {
            OrderBusinessDto tempOrder = orderService.selectByPrimaryKey(id);
            modelMap.put("returnObject", tempOrder);
        } catch (Exception e) {
            logger.error("selectServiceType ", e);
        }
        return "/order/selectServiceType.ftl";
    }

    /**
     * 跳转到申请退款页面
     *
     * @param id       订单编号
     * @param modelMap
     * @return String
     * @Title: applyRefund
     * @date 2016-12-21 下午2:43:44
     */
    @RequestMapping("/applyRefund.htm")
    public String applyRefund(Long id, ModelMap modelMap, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            OrderBusinessDto tempOrder = orderService.getOrderDatails(merchant.getId(), id, OrderDetailEnum.FINISHED.getCode());
            if (tempOrder.getIsKa() == 1 && (tempOrder.getIsThirdCompany() != 1 || tempOrder.getIsFbp() == 1)) {
                modelMap.put("errorMsg", "该订单不允许自主发起退款");
                return "/order/applyRefund.ftl";
            }
//            calcOrderAdjust(tempOrder);    //计算订单优惠金额
            if (tempOrder.getActivityPackageList() == null) {
                tempOrder.setActivityPackageList(new ArrayList<>());
            }
            String errorMsg = request.getParameter("errorMsg");
            if (StringUtil.isNotEmpty(errorMsg)) {
                modelMap.put("errorMsg", errorMsg);
            }
            if (Objects.equals(tempOrder.getStatus(), 91) && Objects.equals(tempOrder.getIsThirdCompany(), 1) && Objects.equals(tempOrder.getIsFbp(), 0)) {
                FreightRefundDto freightRefundDto = orderRefundBusinessApi.freightRefundAmount(tempOrder.getOrderNo());
                tempOrder.setFreightRefundType(freightRefundDto.getFreightRefundType());
                tempOrder.setFreightRefundAmount(freightRefundDto.getRefundFreightAmount());
            } else {
                tempOrder.setFreightRefundType(FreightRefundTypeEnum.NOT_AVAILABLE.getId());
                tempOrder.setFreightRefundAmount(BigDecimal.ZERO);
            }
            logger.info("applyRefund.htm response:{}",JSONObject.toJSONString(tempOrder));
            modelMap.put("returnObject", tempOrder);
            modelMap.put("returnObjectJson", JsonUtil.toJson(tempOrder));
            modelMap.put("center_menu", "order");
        } catch (Exception e) {
            logger.error("applyRefund error" + e.toString(), e);
        }

        return "/order/applyRefund.ftl";
    }

    @RequestMapping("/refund/queryGiftRefundNum")
    @ResponseBody
    public Object queryGiftRefundNum(@RequestBody GiftRefundDetailParam giftRefundDetailParam ) {
        try {
            if (Objects.isNull(giftRefundDetailParam) ){
                return this.addError("入参不能为空");
            }

            ApiRPCResult apiRPCResult =  orderRefundBusinessApi.queryGiftRefundNum(giftRefundDetailParam);
            if (apiRPCResult==null) {
                return  this.addError("查询赠品信息异常");
            }

            if (!apiRPCResult.isSuccess()) {
                return  this.addError(apiRPCResult.getMsg());
            }
            return this.addResult("data", apiRPCResult.getData());
        } catch (Exception e) {
            logger.error("RefundOrderController.queryGiftRefundNum 异常", e);
        }
        return this.addError("查询赠品信息异常");
    }
    /**
     * 跳转到退款原因页面
     *
     * @param str      被选中的商品json对象
     * @param modelMap
     * @return String
     * @Title: refundReason
     */
    @SuppressWarnings("unchecked")
    @RequestMapping("/refundReason.htm")
    public ModelAndView refundReason(CustomRefundOrderBusinessDto customRefundOrder, String str, Integer refundFreightType, ModelMap modelMap, RedirectAttributes redirectAttributes) {
        CustomOrderBusniessDto customOrder = new CustomOrderBusniessDto();
        List<CustomOrderDetailBusniessDto> customOrderDetails = Lists.newArrayList();
        logger.info("跳转到退款原因页面请求入参：orderNo={},RefundType={}",customRefundOrder.getOrderNo(),customRefundOrder.getRefundType());
        customRefundOrder.setRefundType(CustomRefundOrderBusinessDto.REFUND_PART);
        if (customRefundOrder.getRefundType() == CustomRefundOrderBusinessDto.REFUND_PART) {
            /*
                解析退货商品
             */
            String refundStr = str;
            if (StringUtils.isNotEmpty(refundStr)) {
                byte[] path = Base64Util.decode(refundStr);
                str = new String(path);
            }
            customOrderDetails = JSONArray.parseArray(str, CustomOrderDetailBusniessDto.class);
        }
//        customOrderDetails = orderService.canRefundCustomOrderDeatils(customRefundOrder.getOrderId(), customOrderDetails, customRefundOrder.getRefundType());
        customOrder.setRefundOrderDetailList(customOrderDetails);
        customOrder.setRefundType(customRefundOrder.getRefundType());
        if (StringUtils.isEmpty(customRefundOrder.getOrderNo())) {
            OrderBusinessDto dataOrder = this.orderService.selectByPrimaryKey(customRefundOrder.getOrderId());
            customOrder.setOrderNo(dataOrder.getOrderNo());
        } else {
            customOrder.setOrderNo(customRefundOrder.getOrderNo());
        }

        //查找订单
        Order order = orderService.getOrderByOrderNo(customRefundOrder.getOrderNo());

        JSONObject object ;
        if (Objects.nonNull(refundFreightType) && Objects.equals(refundFreightType, 1)) {
            if(!Objects.equals(order.getStatus(),91) || Objects.equals(order.getIsThirdCompany(),0) || Objects.equals(order.getIsFbp(),1)){
                modelMap.put("errorMsg", "当前订单不支持仅退运费");
                modelMap.put("id", customRefundOrder.getOrderId());
                redirectAttributes.addFlashAttribute("errorMsg", "当前订单不支持仅退运费");
                RedirectView redirectView = new RedirectView("/merchant/center/order/applyRefund.htm?id="+customRefundOrder.getOrderId(), true, false, false);
                return new ModelAndView(redirectView);
            }
            object = (JSONObject) orderService.getOrderFreightRefundCensus(order.getOrderNo());
        }else {
            object  = (JSONObject) orderService.getOrderRefundCensus(customOrder);
        }
        if (object == null) {
            modelMap.put("errorMsg", "系统出现异常，请稍后重试");
            modelMap.put("id", customRefundOrder.getOrderId());
            redirectAttributes.addFlashAttribute("errorMsg", "系统出现异常，请稍后重试");
            RedirectView redirectView = new RedirectView("/merchant/center/order/applyRefund.htm?id="+customRefundOrder.getOrderId(), true, false, false);
            return new ModelAndView(redirectView);
        }
        String status = (String)object.get("status");
        String errorMsg = (String)object.get("errorMsg");
        if ("failure".equals(status)) {
            if (StringUtils.isBlank(errorMsg)) {
                errorMsg = "系统出现异常，请稍后重试";
            }
            modelMap.put("errorMsg", errorMsg);
            modelMap.put("id", customRefundOrder.getOrderId());
            redirectAttributes.addFlashAttribute("errorMsg", errorMsg);
            RedirectView redirectView = new RedirectView("/merchant/center/order/applyRefund.htm?id="+customRefundOrder.getOrderId(), true, false, false);
            return new ModelAndView(redirectView);
        }
        String money = "0.00";
        if (null != object) {
            money = String.valueOf(object.get("data"));
        }
        modelMap.put("orderId", customRefundOrder.getOrderId());
        modelMap.put("orderNo", customOrder.getOrderNo());
        modelMap.put("str", str);
        modelMap.put("money", money);            //可退款金额
        modelMap.put("refundType", customRefundOrder.getRefundType());
        modelMap.put("center_menu", "order");
        if (object.get("refundOrder") != null) {
            Map<String, Object> refundOrderMap = (Map<String, Object>) object.get("refundOrder");
            if (refundOrderMap.get("refundBalance") != null) {
                modelMap.put("refundBalance", refundOrderMap.get("refundBalance"));
            }
            if (refundOrderMap.get("virtualGold") != null) {
                modelMap.put("refundVirtualGold", refundOrderMap.get("virtualGold"));
            }

            if (refundOrderMap.get("refundReason") != null) {
                modelMap.put("refundReason", refundOrderMap.get("refundReason"));
            }
        }

        modelMap.put("payType", order.getPayType());//支付类型
        modelMap.put("status", order.getStatus());//支付类型
        modelMap.put("mobile", order.getMobile());//手机号
        modelMap.put("contactor", order.getContactor());//收货人
        modelMap.put("invoiceType", order.getBillType());
        //优化逻辑，获取会员最近一次提交的退款单收汇款账户信息
        OrderRefundBankBusinessDto orderRefundBankBusinessDto = orderService.selectLastOrderRefundBankByMerchantId(order.getMerchantId());
        modelMap.put("orderRefundBankBusinessDto",orderRefundBankBusinessDto);
//        List<RefundReasonBusinessDto> reasonBusinessDtoList = refundReasonBusinessApi.getByIsThirdCompanyAndOrderStatus(order.getIsThirdCompany(),order.getStatus());
//        List<RefundReasonBusinessDto> reasonBusinessDtoList = refundReasonBusinessApi.selectFilteredRefundReasonTreeByOrderNo(order.getOrderNo());
        List<Long> orderDetailIds = customOrderDetails.stream().map(CustomOrderDetailBusniessDto::getProductId).collect(Collectors.toList());
        if (!appRebuildSwitch){
            List<RefundReasonBusinessDto> reasonBusinessDtoList = refundReasonBusinessApi.selectFilteredRefundReasonTreeV3(customOrder.getOrderNo(), orderDetailIds);
            modelMap.put("refundReasonList", reasonBusinessDtoList);
        }
        if (appRebuildSwitch) {
            // 默认售后类型和是否可编辑
            String defaultRefundType = null;
            boolean isRefundTypeEditable = true;
            Integer orderStatus = order.getStatus();
            List<RefundReasonPlusDto> reasonBusinessDtoList = refundReasonBusinessApi.selectFilteredRefundReasonTreeV4(customOrder.getOrderNo(), orderDetailIds);
            // 判断商家类型以及订单状态
            boolean isPOP = order.getIsThirdCompany().intValue() == 1 && order.getIsFbp().intValue() == 0;
            boolean isProcessing = OrderStatusEnum.PENDING.getId().equals(orderStatus)
                    || OrderStatusEnum.OUTBOUND.getId().equals(orderStatus);
            boolean isProcessed = OrderStatusEnum.SHIPPING.getId().equals(orderStatus)
                    || OrderStatusEnum.FINISH.getId().equals(orderStatus);
            if (isProcessing) {
                defaultRefundType = "我要退款(无需退货)";
                isRefundTypeEditable = false;
                List<RefundReasonPlusDto> onlyRefundReasonList = reasonBusinessDtoList.stream().filter(r -> r.getRefundType().intValue() == 1).collect(Collectors.toList());
                modelMap.put("refundReasonList", onlyRefundReasonList);
            } else if (isProcessed) {
                if (isPOP) {
                    defaultRefundType = "我要退货退款";
                    isRefundTypeEditable = false;
                    List<RefundReasonPlusDto> returnRefundReasonList = reasonBusinessDtoList.stream().filter(r -> r.getRefundType().intValue() == 2).collect(Collectors.toList());
                    modelMap.put("refundReasonList", returnRefundReasonList);
                } else {
                    defaultRefundType = "请先选择售后类型";
                    isRefundTypeEditable = true;
                    modelMap.put("afterSalesType",Lists.newArrayList("我要退款(无需退货)","我要退货退款"));
                    modelMap.put("refundReasonList", reasonBusinessDtoList);
                }
            }
            modelMap.put("defaultRefundType", defaultRefundType);
            modelMap.put("isRefundTypeEditable", isRefundTypeEditable);
        }
        return new ModelAndView("/order/refundReason.ftl", modelMap);
    }

    @Value("${ec.pc.hostName:https://upload.ybm100.com/}")
    private String pcHostName;

    /**
     * 提交退款申请
     *
     * @param modelMap
     * @return Object
     * @Title: submitRefund
     * <AUTHOR>
     * @date 2016-12-27 上午10:22:09
     */
    @SuppressWarnings("unchecked")
    @RequestMapping("/submitApplyRefund.htm")
    public ModelAndView submitApplyRefund(HttpServletRequest request, CustomRefundOrderBusinessDto refundOrder, String str, ModelMap modelMap) throws Exception {
        logger.info("pc submitApplyRefund orderNo:{}", refundOrder.getOrderNo());
        CustomOrderBusniessDto customOrder = new CustomOrderBusniessDto();
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        String uploadPath = "/ybm/pc/order/" + merchant.getId() + "/";
        String localTempPath = System.getProperty("xyy-shop");
        Map<String, Object> uploadResultMap = null;
        if (appRebuildSwitch){
            uploadResultMap = FileUploadUtil.fileSpecifiedUpload(request, uploadPath, cdnConfig, null, localTempPath);
        }else {
            uploadResultMap = FileUploadUtil.fileUpload(request, uploadPath, cdnConfig, null, localTempPath);
        }
        if (uploadResultMap != null) {
            Object uploadResult = uploadResultMap.get("fileName");
            if (null != uploadResult) {
                List<String> fileNameList = (List<String>) uploadResult;
                logger.info("submitApplyRefund fileNameList:{}", fileNameList);

                List<String> imgUrlList = fileNameList.stream().filter(imgUrl -> StringUtils.isNotBlank(imgUrl)).map(x -> uploadPath + x).collect(Collectors.toList());
                customOrder.setImgList(JSONObject.toJSONString(imgUrlList));
//                for (int i = 0; i < fileNameList.size(); i++) {
//                    if (i == 0) {
//                        customOrder.setEvidence1(fileNameList.get(0) == null ? null : uploadPath + fileNameList.get(0));
//                    }
//                    if (i == 1) {
//                        customOrder.setEvidence2(fileNameList.get(1) == null ? null : uploadPath + fileNameList.get(1));
//                    }
//                    if (i == 2) {
//                        customOrder.setEvidence3(fileNameList.get(2) == null ? null : uploadPath + fileNameList.get(2));
//                    }
//                }
            }
        }
        List<CustomOrderDetailBusniessDto> customOrderDetails = Lists.newArrayList();
        if (refundOrder.getRefundType() == CustomRefundOrderBusinessDto.REFUND_PART) {
            customOrderDetails = JSONArray.parseArray(str, CustomOrderDetailBusniessDto.class);
        }
        customOrder.setRefundType(refundOrder.getRefundType());
        customOrder.setOrderNo(refundOrder.getOrderNo());
        customOrder.setRefundExplain(refundOrder.getRefundExplain());
        customOrder.setRefundReason(refundOrder.getRefundReason());
        customOrder.setRefundChannel(OrderEnum.OrderRefundConstant.APP_PUSH.getId());
        customOrder.setBankName(refundOrder.getBankName());
        customOrder.setBankCard(refundOrder.getBankCard());
        customOrder.setOwner(refundOrder.getOwner());
        customOrder.setCellphone(refundOrder.getCellphone());
        customOrder.setFullVersion(true);
        if(appRebuildSwitch){
            customOrder.setAfterSalesType(refundOrder.getAfterSalesType());
            Object map = uploadResultMap.get("map");
            if (null != map){
                HashMap<String, String> hashMap = (HashMap<String, String>) map;
                if (hashMap.containsKey("damagedGoods")){
                    customOrder.setDamagedGoods(uploadPath+hashMap.get("damagedGoods"));
                }
                if (hashMap.containsKey("expressOuterBoxPC")){
                    String[] physicalBatchNumbers = hashMap.get("expressOuterBoxPC").split(",");
                    String expressOuterBoxPC = Arrays.stream(physicalBatchNumbers)
                            .map(number -> uploadPath + number)
                            .collect(Collectors.joining(","));
                    customOrder.setExpressOuterBox(expressOuterBoxPC);
                }
                if (hashMap.containsKey("problemDisplay")){
                    customOrder.setProblemDisplay(uploadPath+hashMap.get("problemDisplay"));
                }
                if (hashMap.containsKey("physicalBatchNumber")){
                    customOrder.setPhysicalBatchNumber(uploadPath+hashMap.get("physicalBatchNumber"));
                }
                if (hashMap.containsKey("physicalPicture")){
                    customOrder.setPhysicalPicture(uploadPath+hashMap.get("physicalPicture"));
                }
                if (hashMap.containsKey("productProof")){
                    customOrder.setProductImage(uploadPath+hashMap.get("productProof"));
                }
                if (hashMap.containsKey("expressWaybillNumber")){
                    customOrder.setExpressWaybillNumber(uploadPath+hashMap.get("expressWaybillNumber"));
                }
                customOrder.setHostUrl(pcHostName);
            }
            logger.info("pc submitApplyRefund customOrder:{}", JsonUtil.toJson(customOrder));
        }
        if (refundOrder.getRefundReason().equals("退运费")) {
            orderService.applyFreightRefund(customOrder);
        }else {
            orderService.submitApplyRefund(customOrder, customOrderDetails);
        }
        return new ModelAndView(new RedirectView("/merchant/center/order/findRefundOrderList/" + refundOrder.getOrderId() + ".htm",true,false));
    }


    /**
     * 查看退款详情
     *
     * @param orderId
     * @param modelMap
     * @return String
     * @Title:
     * <AUTHOR>
     * @date 2016-12-21 下午2:43:15
     */
    @RequestMapping("/findRefundOrderList/{orderId}.htm")
    public String findRefundOrderList(@PathVariable Long orderId, String orderNo, ModelMap modelMap, Page page, Sort sort) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        OrderRefundDto orderRefundDto = new OrderRefundDto();
        OrderBusinessDto orderBusinessDto = orderService.selectByPrimaryKey(orderId);
        if(orderBusinessDto.getIsThirdCompany() == 1) {
            OrderExtendBusinessDto orderExtendBusinessDto = orderService.quereyExtendInfo(orderBusinessDto.getOrderNo());
            orderBusinessDto.setIsFbp(orderExtendBusinessDto.getIsFbp());
            if (orderBusinessDto.getIsFbp() == null) {
                orderBusinessDto.setIsFbp(0);
            }
        }
        if (StringUtils.isEmpty(orderNo)) {
            orderNo = orderBusinessDto.getOrderNo();
        }
        orderRefundDto.setOrderNo(orderNo);
        PageInfo<OrderRefundDto> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(page.getOffset());
        // 把数据全部取出来,通常一个订单退款单最多20个,这里我直接取1000条
        pageInfo.setPageSize(1000);
        pageInfo = orderRefundBusinessApi.findWebOrderRefundData(pageInfo,orderRefundDto);
        /*
            获取真实退款列表数据
         */
        List<OrderRefundBusinessDto> refundOrdersList = EntityConvert.convertList(OrderRefundBusinessDto.class, pageInfo.getList());
        Map<String, String> refundAdressMap = codeItemServiceRpc.findCodeMap("REFUND_ADRESS", merchant.getRegisterCode());

        String refundOrderName = refundAdressMap.get("refundOrderName");
        String refundOrderPhone = refundAdressMap.get("refundOrderPhone");
        String refundOrderAdress = refundAdressMap.get("refundOrderAdress");
        String refundOrderExpressDelivery = "";

        int isShowRefundInfo = 1;

        if(orderBusinessDto.getIsThirdCompany() == 1 && orderBusinessDto.getIsFbp() == 0){
            PopAddressDto address = popOrderBusinessApi.getOrgAddressInfo(orderBusinessDto.getOrgId(), orderBusinessDto.getBranchCode());
            if(address!=null){
                refundOrderName = address.getContactor();
                refundOrderPhone = address.getContactorMobile();
                refundOrderAdress = address.getAddress();
                refundOrderExpressDelivery = address.getRemark();
            }else {
                isShowRefundInfo = 0;
            }
        }else {
            // 通过后台获取收货人信息
            ReceiptInformationBusinessDto receiptInformationredis = receiptInformationBusinessApi.getReceiptInformationBusinessDtoByBranchcode(orderBusinessDto.getBranchCode());
            if (receiptInformationredis != null) {
                refundOrderName = receiptInformationredis.getName();
                refundOrderPhone = receiptInformationredis.getPhone();
                refundOrderAdress = receiptInformationredis.getAddress();
                refundOrderExpressDelivery = receiptInformationredis.getExpressDelivery();
            }
        }

        sort.setProperty("package_id");
        sort.setDirection("DESC");

        Map<String, String> codeMapShow = codeItemServiceRpc.findCodeMap(CodeMapConstants.SKU_PROMO_IS_SHOW, merchant.getRegisterCode());
        //是否显示返点活动标签
        boolean isShow = false;
        if (codeMapShow.get(CodeMapConstants.SKU_PROMO_SHOW_VALUE).equals(CodeMapConstants.SKU_PROMO_SHOW_VALUE_RESULT)) {
            isShow = true;
        }


        for (OrderRefundBusinessDto orderRefundDTO : refundOrdersList) {

            if (StringUtils.isNotBlank(orderRefundDTO.getImgList())) {
                orderRefundDTO.setImageList(JSONObject.parseArray(orderRefundDTO.getImgList(),String.class));
            }
            OrderRefundDetailBusinessDto refundOrderDetail = new OrderRefundDetailBusinessDto();
            refundOrderDetail.setRefundOrderNo(orderRefundDTO.getRefundOrderNo());
            Map<String, Object> resultMap = orderRefundBusinessApi.selectDetailByRefundId(refundOrderDetail, pageInfo, isShow);
            List<OrderRefundDetailDto> orderRefundVOList = ((PageInfo<OrderRefundDetailDto>) resultMap.get("refundOrderDetailList")).getList();
//            List<ActivityPackageDto> activityPackageList = (List<ActivityPackageDto>) resultMap.get("activityPackageList");
            orderRefundDTO.setOrderRefundVOList(EntityConvert.convertList(OrderRefundDetailBusinessDto.class,orderRefundVOList));
            orderRefundDTO.setActivityPackageList(new ArrayList<>());
            List<Long> skuIds = orderRefundDTO.getOrderRefundVOList().stream().map(OrderRefundDetailBusinessDto::getSkuId).collect(Collectors.toList());
            List<SkuCategoryRelationBusinessDTO> skuCategoryRelationBusinessDTOS = categoryRelationBusinessApi.findSkuCategoryRelationBySkuIdList(skuIds);
            if(CollectionUtils.isNotEmpty(skuCategoryRelationBusinessDTOS)){
                Map<Long, List<SkuCategoryRelationBusinessDTO>> skuCategoryRelationMap = skuCategoryRelationBusinessDTOS.stream().collect(Collectors.groupingBy(SkuCategoryRelationBusinessDTO::getSkuId));
                orderRefundDTO.getOrderRefundVOList().forEach(refundDetailDto -> {
                    List<SkuCategoryRelationBusinessDTO> skuCategoryRelations = skuCategoryRelationMap.get(refundDetailDto.getSkuId());
                    if(CollectionUtils.isNotEmpty(skuCategoryRelations)){
                        // 一般一个商品对应一个分类关系，但线上有大量商品对应多条一样的分类关系，所以这里取最新一条关系作为商品的分类
                        SkuCategoryRelationBusinessDTO skuCategoryRelationBusinessDTO = skuCategoryRelationBusinessDTOS.get(skuCategoryRelationBusinessDTOS.size() - 1);
                        refundDetailDto.setCategoryFirstId(skuCategoryRelationBusinessDTO.getCategoryFirstId());
                        refundDetailDto.setCategorySecondId(skuCategoryRelationBusinessDTO.getCategorySecondId());
                        refundDetailDto.setCategoryThirdId(skuCategoryRelationBusinessDTO.getCategoryThirdId());
                    }
                });
            }
            int cancelChannel = orderRefundDTO.getCancelChannel()==null ? 0 : orderRefundDTO.getCancelChannel();
            // 处理退款取消原因字段
            if(cancelChannel == Constants.IS2){
                orderRefundDTO.setCloseReason(StringUtils.isBlank(orderRefundDTO.getCloseReason()) ? "用户自行取消" : orderRefundDTO.getCloseReason());
                orderRefundDTO.setCloseTime(DateUtils.format(orderRefundDTO.getRefundAuditTime(),DateUtils.LONG_DATE_FORMAT_STR));
            }else{
                orderRefundDTO.setCloseReason(orderRefundDTO.getCloseReason());
                orderRefundDTO.setCloseTime(DateUtils.format(orderRefundDTO.getRefundAuditTime(),DateUtils.LONG_DATE_FORMAT_STR));
            }
            //增加收款账户银行信息
            OrderRefundBankBusinessDto orderRefundBankBusinessDto = orderRefundBankBusinessApi.getOrderRefundBankByOrderRefundId(orderRefundDTO.getId());
            orderRefundDTO.setOrderRefundBankBusinessDto(orderRefundBankBusinessDto);
            //增加快递单信息
            OrderRefundExpressBusinessDto orderRefundExpressBusinessDto = orderRefundExpressBusinessApi.getOrderRefundExpressByOrderRefundId(orderRefundDTO.getId());
            orderRefundDTO.setOrderRefundExpressBusinessDto(orderRefundExpressBusinessDto);
            handleRefundExpressCountDownTime(orderRefundDTO, orderRefundExpressBusinessDto);
            //设置平台介入权限
            buildPlatformInButton(orderBusinessDto, orderRefundDTO,merchant.getRegisterCode());
            if (appRebuildSwitch) {
                //获取并判断是否存在指定凭证
                OrderRefundExtDto orderRefundExtDto = orderRefundBusinessApi.findOrderRefundExtByOrderRefundNo(orderRefundDTO.getRefundOrderNo());
                logger.info("PC orderRefundExtDto:{}",JsonUtil.toJson(orderRefundExtDto));
                if (orderRefundExtDto != null) {
                    if (orderRefundExtDto.getExpressOuterBox()!=null){
                        String[] split = orderRefundExtDto.getExpressOuterBox().split(",");
                        orderRefundExtDto.setExpressOuterBoxList(Arrays.asList(split));
                    }
                    orderRefundDTO.setOrderRefundExtDto(orderRefundExtDto);
                }
                //查询退款类型
                int afterSalesTyoe = orderRefundBusinessApi.selectAfterSalesTyoeByOrderRefundNo(orderRefundDTO.getRefundOrderNo());
                orderRefundDTO.setAfterSalesType(afterSalesTyoe);
            }
            //设置平台介入权限
            buildPlatformInButton(orderBusinessDto, orderRefundDTO,merchant.getRegisterCode());
        }
        modelMap.put("refundOrdersList", refundOrdersList);
        modelMap.put("refundOrderName", refundOrderName);
        modelMap.put("refundOrderPhone", refundOrderPhone);
        modelMap.put("refundOrderAdress", refundOrderAdress);
        modelMap.put("refundOrderExpressDelivery", refundOrderExpressDelivery);
        modelMap.put("center_menu", "order");
        modelMap.put("merchant", merchant);
        modelMap.put("orderBusinessDto", orderBusinessDto);
        modelMap.put("isThirdCompany",orderBusinessDto.getIsThirdCompany());
        modelMap.put("isShowRefundInfo",isShowRefundInfo);
        return "/order/refundOrderList.ftl";
    }



    @RequestMapping(value = "/getRefundOrderListForPlatformIn")
    @ResponseBody
    public Object getRefundOrderListForPlatformIn(Long orderId,String orderNo,Page page) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        OrderRefundDto orderRefundDto = new OrderRefundDto();
        Order orderByOrderNo = orderService.getOrderByOrderNo(orderNo);

        orderRefundDto.setOrderNo(orderNo);
        PageInfo<OrderRefundDto> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(page.getOffset());
        pageInfo.setPageSize(1000);
        pageInfo = orderRefundBusinessApi.findWebOrderRefundData(pageInfo,orderRefundDto);
        List<OrderRefundDto> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return this.addResult("data",Collections.EMPTY_LIST);
        }
        List<OrderRefundBusinessDto> refundOrdersList = new ArrayList<>();
        Integer isFbp = orderByOrderNo.getIsFbp();
        for (OrderRefundDto refund: list) {
            if(Objects.equals(refund.getIsThirdCompany(), 1) && Objects.equals(isFbp,0) && (Objects.equals(refund.getAuditState(), 0) || (Objects.equals(refund.getAuditState(), -1) && Objects.equals(refund.getWaitClose(), 1)))){

                Integer auditState = refund.getAuditState();
                Date refundCreateTime = refund.getRefundCreateTime();

                Date now = new Date();
                Instant instant1 = refundCreateTime.toInstant();
                Instant instant2 = now.toInstant();
                Duration duration = Duration.between(instant1, instant2).abs();

                long minus = 0;
                ApiRPCResult<List<HolidayDTO>> result = csOrderRefundApi.get15Holiday(refundCreateTime);
                if (result.isSuccess()){
                    List<HolidayDTO> holidays = result.getData();
                    Duration duration1 = HolidayUtil.calculateHolidayDuration(instant1, instant2, holidays);
                    minus = duration1.toHours();
                }

                duration = duration.minusHours(minus);
                if (!(auditState != -1 && duration.toHours() <= 48)){
                    refundOrdersList.add(EntityConvert.convert(OrderRefundBusinessDto.class, refund));
                }
            }
        }
        return this.addResult("data", refundOrdersList);
    }

    /**
     * 设置退货物流填写倒计时
     * 逻辑同：com.xyy.ec.order.business.api.impl.OrderRefundOperateLogBusinessApiImpl#handleCountDownTime
     * @param refundDto
     * @param express
     */
    private void handleRefundExpressCountDownTime(OrderRefundBusinessDto refundDto, OrderRefundExpressBusinessDto express) {
        if (refundDto.getRefundMode() == null || refundDto.getRefundMode() == RefundModeEnums.SMALL_AMOUNT_PAYMENT.getId()) {
            return;
        }
        if (refundDto.getRefundMode() == null || refundDto.getRefundMode() == RefundModeEnums.ONLY_MONEY.getId()) {
            return;
        }
        if (refundDto.getRefundMode() == RefundModeEnums.GOODS_MONEY.getId()
                && refundDto.getErpRefundPushWmsState() != null && refundDto.getErpRefundPushWmsState() == 1) {
            return;
        }
        boolean noExpress = express == null || StringUtil.isEmpty(express.getExpressNo());
        boolean customerAuditSuccess = (refundDto.getAuditState() !=  null && refundDto.getAuditState() != -1)
                && (refundDto.getAuditProcessState() != null && refundDto.getAuditProcessState() == -1);
        boolean userClaim = refundDto.getRefundChannel() != null && refundDto.getRefundChannel()
                == com.xyy.ec.order.core.enums.OrderEnum.OrderRefundConstant.APP_PUSH.getId();
        if (noExpress && customerAuditSuccess && userClaim) {
            Date createTime = refundDto.getRefundCreateTime();
            Date expireDate = org.apache.commons.lang3.time.DateUtils.addDays(createTime, 7);
            long remained = expireDate.getTime() - System.currentTimeMillis();
            refundDto.setExpressCountDownTime(Math.max(remained / 1000, 0));
        }
    }

    /**
     * 设置平台介入权限
     * @param orderBusinessDto
     * @param orderRefundDTO
     */
    private void buildPlatformInButton(OrderBusinessDto orderBusinessDto,OrderRefundBusinessDto orderRefundDTO,String branchCode) {
        if (Objects.isNull(orderRefundDTO)){
            return;
        }
        boolean isPlatformIn = platformInSwitch;
        boolean isInGrayList = CollectionUtils.isEmpty(platformInGrayBranchCode) || platformInGrayBranchCode.contains(branchCode);
        if (isPlatformIn && isInGrayList) {
            Integer isFbp = orderBusinessDto != null ? orderBusinessDto.getIsFbp() : null;
            Integer isThirdCompany = orderRefundDTO.getIsThirdCompany();
            Integer auditState = orderRefundDTO.getAuditState();
            Integer auditProcessState = orderRefundDTO.getAuditProcessState();
            Integer waitClose = orderRefundDTO.getWaitClose();
            boolean mainCondition = Objects.equals(isThirdCompany, 1) &&
                    Objects.equals(isFbp, 0) && StringUtils.equals(orderRefundDTO.getAuditStatusName(), com.xyy.ec.order.core.enums.OrderRefundEnum.REFUND_WAIT_AUDITING.getValue());
            boolean specialCondition = Objects.equals(isThirdCompany, 1) &&
                    Objects.equals(isFbp, 0) &&
                    Objects.equals(auditState, -1) &&
                    Objects.equals(waitClose, 1);
            orderRefundDTO.setCanPlatformIn(mainCondition || specialCondition);
            ApiRPCResult<List<PlatformInWorkorderDetailDTO>> listApiRPCResult = csOrderRefundApi.detailByRefundNoOrAfsNo(orderRefundDTO.getRefundOrderNo());
            if (listApiRPCResult.isSuccess()) {
                List<PlatformInWorkorderDetailDTO> data = listApiRPCResult.getData();
                if (CollectionUtils.isNotEmpty(data)){
                    PlatformInWorkorderDetailDTO dto = data.stream()
                            .max(Comparator.comparingLong(PlatformInWorkorderDetailDTO::getId))
                            .orElse(null);
                    if (dto != null){
                        orderRefundDTO.setCanPlatformIn(Objects.equals(isThirdCompany, 1) && Objects.equals(isFbp, 0));
                    }
                }
            }
        } else {
            orderRefundDTO.setCanPlatformIn(false);
        }
    }
    /**
     * 判断是否618订单 能否发起退款
     *
     * */
    @RequestMapping(value = "/order618Prom/orderRefund")
    @ResponseBody
    public Object judgeOrderRefund(Long orderId){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long id = merchant.getId();
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectById(orderId);

            boolean flag = order618PromBusinessApi.applyOrderRefundJudge(id, orderBusinessDto.getOrderNo());

            String errorMessage = "";
            if(flag){
                if (orderBusinessDto.getPayChannel() != null && orderBusinessDto.getPayChannel() == com.xyy.ec.order.core.enums.OrderEnum.OrderPayChannelType.GUANGFAWHITEBAR.getId()){
                    errorMessage = "当前订单的支付方式为白条支付，请联系客服申请售后！~";
                    flag = false;
                }
            }else {
                errorMessage = "亲，年中超级返大促订单暂不支持无故退款，请联系客服处理";
            }

            return flag ? this.addResult() : addError(errorMessage);
        } catch (Exception e) {
            logger.error("系统异常",e);
            return this.addError("系统异常");
        }
    }

    /**
     * 判断是否能发起退款
     *
     * */
    @RequestMapping(value = "/canRefund")
    @ResponseBody
    public Object canRefund(Long orderId){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectById(orderId);
            if(orderBusinessDto == null){
                return this.addError("订单不存在");
            }
            boolean canReturn = orderRefundBusinessApi.canRefund(orderId);
            if(!canReturn){
                return this.addError("当前订单已申请退款");
            }else{
                return BaseController.addResult();
            }
        } catch (Exception e) {
            logger.error("系统异常",e);
            return this.addError("系统异常");
        }
    }

    /**
     * 判断是否是山西订单 能否发起退款
     *
     * */
    @RequestMapping(value = "/limitedOrderRefund")
    @ResponseBody
    public Object limitedOrderRefund(Long orderId){
        try {
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectById(orderId);
            Map<String, CodeItemDto>  codeItemMap= codeItemServiceRpc.selectCodeItem(CodeMapConstants.ORDER_REFUND_LIMITED, "XS420000");
            if (codeItemMap  != null) {
                String branchCode = codeItemMap.get("BRANCH_CODE")!=null?codeItemMap.get("BRANCH_CODE").getName():"";
                String createTime = codeItemMap.get("CREATE_TIME")!=null?codeItemMap.get("CREATE_TIME").getName():"";

                if(StringUtils.isNotEmpty(createTime) && StringUtils.isNotEmpty(branchCode) && branchCode.equals(orderBusinessDto.getBranchCode())){
                    int flag = DateUtil.compareDate(orderBusinessDto.getCreateTime(), DateUtil.string2Date(createTime, DateUtil.PATTERN_STANDARD));
                    if(1!=flag){
                        return this.addError("由于系统升级改造，当前订单暂不支持在线申请退货。如有疑问，请咨询客服。客服热线：400-0505-111");
                    }
                } else {
                    return this.addResult();
                }
            }
            return this.addResult();
        } catch (Exception e) {
            logger.error("系统异常", e);
            return this.addError("系统异常");
        }
    }
    /**
     * 判断是否是三合一协议商品
     *
     * */
    @RequestMapping(value = "/getTrinityAgreementMessage")
    @ResponseBody
    public Object getTrinityAgreementMessage(String orderNo, String str){

        try {
            List<CustomOrderDetailBusniessDto> customOrderDetails = Lists.newArrayList();
            String refundStr = str;
            if (StringUtils.isNotEmpty(refundStr)) {
                byte[] path = Base64Util.decode(refundStr);
                str = new String(path);
            }
            customOrderDetails = JSONArray.parseArray(str, CustomOrderDetailBusniessDto.class);

            List<Long> orderDetailIds = new ArrayList<>();
            for (CustomOrderDetailBusniessDto customOrderDetailBusniessDto:customOrderDetails) {
                orderDetailIds.add(customOrderDetailBusniessDto.getProductId());
            }

            String trinityAgreementMessage = orderRefundBusinessApi.getTrinityAgreementMessage(orderNo, orderDetailIds);
            if (StringUtil.isNotEmpty(trinityAgreementMessage)){
                return this.addError(trinityAgreementMessage);
            }


            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(orderNo);

            List<Long> skuIds = orderDetailBusinessApi.selectSkuList(orderDetailIds);

            //判断是否有口罩商品
            ResultDTO<BigDecimal> selfSupportOrderFreightAmount = ecpOrderHandleApi.getSelfSupportOrderFreightAmount(orderBusinessDto.getBranchCode(), orderBusinessDto.getCityCode(), skuIds);
            if (selfSupportOrderFreightAmount != null && selfSupportOrderFreightAmount.getIsSuccess()
                    && selfSupportOrderFreightAmount.getData() != null && selfSupportOrderFreightAmount.getData().compareTo(BigDecimal.ZERO) == 1) {
                return this.addError("您的退款申请中有不可退商品！");
            }


        } catch (Exception e) {
            logger.error("getTrinityAgreementMessage error",e);
        }

        return this.addResult();
    }

    /** 查看退款状态详情 */
    @RequestMapping(value = "/refundStatusDetail/{refundId}.htm")
    public Object refundStatusDetail(@PathVariable Long refundId, ModelMap modelMap){
        try {
            List<OrderRefundOperateLogBusinessDto> refundDetail = logBusinessApi.getRefundStatusDetail(refundId);
            logger.info("PC refundDetail data  -> " + JSONObject.toJSONString(refundDetail));
            if(CollectionUtils.isNotEmpty(refundDetail) && refundDetail.size()==4){
                modelMap.put("firstNode",refundDetail.get(0));
                modelMap.put("middleNode",refundDetail.get(1));
                modelMap.put("lastNode",refundDetail.get(2));
                modelMap.put("current",refundDetail.get(3));
            }
            return "/order/refundStatusDetail.ftl";
        } catch (Exception e) {
            logger.error("PC退款单详情查询异常",e);
            modelMap.put("code", "1001");
            return "/order/refundStatusDetail.ftl";
        }

    }

    /**
     * 获取客户拒绝退款的原因
     *
     * @return
     */
    @RequestMapping(value = "/listAuditRejectReasons")
    @ResponseBody
    public XyyJsonResult listAuditRejectReasons() {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getMerchantId();
            List<String> refundOrderRejectReasons = orderRefundRefuseReasonBusinessApi.listMerchantAuditRefundOrderRejectReasons();
            return XyyJsonResult.createSuccess().addResult("list", refundOrderRejectReasons);
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("获取客户拒绝退款的原因失败，accountId：{}，merchantId：{}，msg：{}", accountId, merchantId, e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(Constants.MSG_ERROR);
        } catch (Exception e) {
            logger.error("获取客户拒绝退款的原因失败，accountId：{}，merchantId：{}", accountId, merchantId, e);
            return XyyJsonResult.createFailure().msg(Constants.MSG_ERROR);
        }
    }

    /**
     * 客户审核退款单
     *
     * @param refundId
     * @param auditResult
     * @param rejectReason
     * @return
     */
    @RequestMapping(value = "/auditOrderRefund")
    @ResponseBody
    public XyyJsonResult auditOrderRefund(@RequestParam("refundId") Long refundId, @RequestParam("auditResult") Integer auditResult,
                                          @RequestParam(value = "rejectReason", required = false) String rejectReason, @RequestParam(value = "images", required = false) String images,
                                          @RequestParam(value = "remarks", required = false) String remarks) {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getId();

            ApiRPCResult<LoginAccountDto> getAccountApiResult = loginAccountApi.selectLoginAccountById(accountId);
            if (BooleanUtils.isNotTrue(getAccountApiResult.isSuccess()) || Objects.isNull(getAccountApiResult.getData())) {
                return XyyJsonResult.createFailure().msg("您还未登录或登录已失效，请您重新登录");
            }
            String operatorMobile = getAccountApiResult.getData().getMobile();

            MerchantAuditRefundOrderParam param = MerchantAuditRefundOrderParam.builder()
                    .refundId(refundId)
                    .auditResult(auditResult)
                    .rejectReason(rejectReason)
                    .accountId(accountId)
                    .merchantId(merchantId)
                    .operatorMobile(operatorMobile)
                    .images(images)
                    .remarks(remarks)
                    .terminalType(TerminalTypeEnum.PC.getValue())
                    .version(null)
                    .build();
            ApiRPCResult<Boolean> apiRPCResult = orderRefundBusinessApi.doMerchantAuditRefundOrder(param);
            if (BooleanUtils.isNotTrue(apiRPCResult.isSuccess())) {
                return XyyJsonResult.createFailure().msg(apiRPCResult.getMsg());
            }

            return XyyJsonResult.createSuccess();
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("客户审核退款单失败，accountId：{}，merchantId：{}，refundId：{}，auditResult：{}，rejectReason：{}，msg：{}",
                        accountId, merchantId, refundId, auditResult, rejectReason, e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(Constants.MSG_ERROR);
        } catch (Exception e) {
            logger.error("客户审核退款单失败，accountId：{}，merchantId：{}，refundId：{}，auditResult：{}，rejectReason：{}",
                    accountId, merchantId, refundId, auditResult, rejectReason, e);
            return XyyJsonResult.createFailure().msg(Constants.MSG_ERROR);
        }
    }

    /**
     * 退款订单编辑收款账户信息
     *
     * @param refundId
     * @param modelMap
     * @return
     */
    @RequestMapping(value = "/editOrderRefundBank/{refundId}.htm")
    public Object editOrderRefundBank(@PathVariable Long refundId, ModelMap modelMap) {
        OrderRefundBusinessDto dto = orderRefundBusinessApi.selectById(refundId);
        modelMap.put("money", null != dto.getRefundMoney() ? String.valueOf(dto.getRefundMoney().doubleValue()) : "0.00");            //可退款金额
        if (dto.getRefundBalance() != null) {
            modelMap.put("refundBalance", String.valueOf(dto.getRefundBalance().doubleValue()));
        }
        OrderRefundBankBusinessDto orderRefundBankBusinessDto = orderRefundBankBusinessApi.getOrderRefundBankByOrderRefundId(refundId);
        modelMap.put("orderRefundBankBusinessDto", orderRefundBankBusinessDto);
        modelMap.put("refundId", refundId);
        modelMap.put("auditState", dto.getAuditState());//订单状态：0-待审核 1-退款通过 -1-退款拒绝
        Order order = orderService.getOrderByOrderNo(dto.getOrderNo());
        modelMap.put("orderId", order.getId());
        return "/order/editOrderRefundBank.ftl";
    }

    @RequestMapping("/queryRefundId")
    @ResponseBody
    public Object queryRefundId(String refundNo) {
        logger.info("queryRefundId:{}", refundNo);
        if (StringUtils.isBlank(refundNo)) {
            return this.addError("退单号不允许为空！");
        }
        try {
            OrderRefundBusinessDto  refundBusinessDto  = orderRefundBusinessApi.selectByRefundOrderNo(refundNo);
            return this.addResult("data", refundBusinessDto);
        } catch (Exception e) {
             logger.error("queryRefundId:{}", refundNo);
             return this.addError(e.getMessage());
        }
    }

    /**
     * 退款订单保存或者更新收款账户信息
     *
     * @param orderRefundBankBusinessDto
     * @return
     */
    @RequestMapping(value = "/saveOrUpdateOrderRefundBank/{orderId}.html")
    public ModelAndView saveOrUpdateOrderRefundBank(OrderRefundBankBusinessDto orderRefundBankBusinessDto, @PathVariable Long orderId) {
        orderRefundBankBusinessDto.setBankCard(orderRefundBankBusinessDto.getBankCard().replaceAll(",", ""));
        if (null != orderRefundBankBusinessDto.getId()) {
            orderRefundBankBusinessApi.modifyOrderRefundBank(orderRefundBankBusinessDto);
        } else {
            orderRefundBankBusinessApi.saveOrderRefundBank(orderRefundBankBusinessDto);
        }
        return new ModelAndView(new RedirectView("/merchant/center/order/findRefundOrderList/" + orderId + ".htm",true,false));
    }

    /**
     * 退款订单编辑退货物流
     *
     * @param refundId
     * @param modelMap
     * @return
     */
    @RequestMapping(value = "/editOrderRefundExpress/{refundId}.htm")
    public Object editOrderRefundExpress(@PathVariable Long refundId, ModelMap modelMap) {
        OrderRefundBusinessDto dto = orderRefundBusinessApi.selectById(refundId);
        modelMap.put("money", null != dto.getRefundMoney() ? String.valueOf(dto.getRefundMoney().doubleValue()) : "0.00");            //可退款金额
        if (dto.getRefundBalance() != null) {
            modelMap.put("refundBalance", String.valueOf(dto.getRefundBalance().doubleValue()));
        }
        OrderRefundExpressBusinessDto orderRefundExpressBusinessDto = orderRefundExpressBusinessApi.getOrderRefundExpressByOrderRefundId(refundId);
        modelMap.put("orderRefundExpressBusinessDto", null != orderRefundExpressBusinessDto ? orderRefundExpressBusinessDto : new OrderRefundExpressBusinessDto());
        modelMap.put("refundId", refundId);
        modelMap.put("refundMode", dto.getRefundMode());//退款方式 1:退货+退款  2:仅退款
        modelMap.put("auditState",dto.getAuditState());
        modelMap.put("auditProcessState",dto.getAuditProcessState());
        Order order = orderService.getOrderByOrderNo(dto.getOrderNo());
        modelMap.put("orderId", order.getId());
        return "/order/editOrderRefundExpress.ftl";
    }

    /**
     *
     * @param orderId 订单id
     * @param request 图片流
     * @param id  运单表主键id
     * @param orderRefundId 运单表退款id
     * @param expressName 运单表快递名称
     * @param expressNo 运单表快递单号
     * @param expressEvidence 运单表运单截图
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/saveOrUpdateOrderRefundExpress/{orderId}.html")
    public ModelAndView saveOrUpdateOrderRefundExpress(@PathVariable Long orderId,HttpServletRequest request,Long id,Long orderRefundId,String expressName,String expressNo,String expressEvidence) throws Exception {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            String uploadPath = "/ybm/pc/order/" + merchant.getId() + "/";
            String localTempPath = System.getProperty("xyy-shop");
            try{
                Map<String, Object> uploadResultMap = FileUploadUtil.fileUpload(request, uploadPath, cdnConfig, null, localTempPath);
                Object uploadResult = uploadResultMap.get("fileName");
                if (null != uploadResult) {
                    List<String> fileNameList = (List<String>) uploadResult;
                    if(null!=fileNameList && fileNameList.size()>0) {
                        expressEvidence = fileNameList.get(0) == null ? null : uploadPath + fileNameList.get(0);//有新截图，替换
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        OrderRefundExpressBusinessDto orderRefundExpressBusinessDto = new OrderRefundExpressBusinessDto();
        orderRefundExpressBusinessDto.setId(id);
        orderRefundExpressBusinessDto.setOrderRefundId(orderRefundId);
        orderRefundExpressBusinessDto.setExpressName(expressName);
        orderRefundExpressBusinessDto.setExpressNo(expressNo);
        orderRefundExpressBusinessDto.setExpressEvidence(expressEvidence);
        if (null != id) {
            orderRefundExpressBusinessApi.modifyOrderRefundExpress(orderRefundExpressBusinessDto);
        } else {
            orderRefundExpressBusinessApi.saveOrderRefundExpress(orderRefundExpressBusinessDto);
        }
        return new ModelAndView(new RedirectView("/merchant/center/order/findRefundOrderList/" + orderId + ".htm",true,false));
    }

    @RequestMapping(value = "/findRefundOrderStatus")
    @ResponseBody
    public Object findRefundOrderStatus(@RequestParam(value = "refundId",required = true) Long refundId){
        Map<String,Object> dataMap = new HashMap<String,Object>();
        OrderRefundBusinessDto orderRefundBusinessDto =  orderRefundBusinessApi.selectById(refundId);
        if(null==orderRefundBusinessDto || null==orderRefundBusinessDto.getId()){
            return this.addError("无此订单");
        }
        Order order = orderService.getOrderByOrderNo(orderRefundBusinessDto.getOrderNo());
        if(null==order.getId()){
            return  this.addError("无此订单");
        }
        dataMap.put("payType", order.getPayType());//支付类型(1:在线支付 2:货到付款 3:线下转账）
        dataMap.put("refundMode", orderRefundBusinessDto.getRefundMode());//退款方式 1:退货+退款  2:仅退款
        dataMap.put("auditState",orderRefundBusinessDto.getAuditState());
        dataMap.put("auditProcessState",orderRefundBusinessDto.getAuditProcessState());
        //检查收汇款信息状态：1正确，0错误
        dataMap.put("checkBankState",(OrderEnum.OrderPayType.OFFLINETRANSFER.getId() == order.getPayType()
                && OrderEnum.OrderRefundConstant.APP_PUSH.getId() == orderRefundBusinessDto.getRefundChannel()
                &&  OrderEnum.OrderRefundConstant.AUDIT_WAIT.getId() == orderRefundBusinessDto.getAuditState()
                &&  OrderEnum.AuditProcessState.DEFAULT_VALUE.getId() == orderRefundBusinessDto.getAuditProcessState())? YNEnum.N.getCode().intValue():YNEnum.Y.getCode().intValue());
        //运单信息状态：1正确，0错误
        dataMap.put("checkExpressState",(OrderEnum.OrderRefundMode.MONEY_AND_PRODUCT.getId() == orderRefundBusinessDto.getRefundMode()
                && (OrderEnum.OrderRefundConstant.APP_PUSH.getId()  == orderRefundBusinessDto.getRefundChannel()
                || OrderEnum.OrderRefundConstant.WEBSERVICE_PUSH.getId()  == orderRefundBusinessDto.getRefundChannel()
                || OrderEnum.OrderRefundConstant.MERCHANT_PUSH.getId()  == orderRefundBusinessDto.getRefundChannel())
                && OrderEnum.OrderRefundConstant.AUDIT_WAIT.getId() ==orderRefundBusinessDto.getAuditState()
                && OrderEnum.AuditProcessState.CUSTOMER_SERVICE_PASS.getId()==orderRefundBusinessDto.getAuditProcessState())? YNEnum.N.getCode().intValue():YNEnum.Y.getCode().intValue());
        return  this.addResult("data",dataMap);


    }



    @RequestMapping(value = "/findRefundReason")
    @ResponseBody
    public Object findRefundReason(@RequestParam(value = "orderNo") String orderNo, @RequestParam(value = "orderStatus") Integer orderStatus){
        List<RefundReasonBusinessDto> reasonList = refundReasonBusinessApi.getByOrderNoAndOrderStatus(orderNo, orderStatus);
        return this.addResult("reasonList", reasonList);
    }
}
