package com.xyy.ec.pc.controller;

import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * 系统错误控制器
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/error")
public class ErrorController extends BaseController {

    private static final Logger LOG                     = LoggerFactory.getLogger(ErrorController.class);

    private static final String ATT_ERROR_URI           = "javax.servlet.error.request_uri";
    private static final String ATT_ERROR_QUERY_STRING  = "javax.servlet.forward.query_string";
    private static final String ATT_ERROR_CODE_KEY      = "javax.servlet.error.status_code";
    private static final String ATT_ERROR_EXCEPTION_KEY = "javax.servlet.error.exception";

    @RequestMapping("/404.htm")
    public ModelAndView handle404(HttpServletRequest request, HttpServletResponse response) {
        LOG.info("request url not url: " + getRequestUrl(request));
        return new ModelAndView("/error/404.ftl");
    }

    @RequestMapping("/500.htm")
    public ModelAndView handle500(HttpServletRequest request, HttpServletResponse response) {
        log(request);
        return new ModelAndView("/error/500.ftl");
    }

    @RequestMapping("/503.htm")
    public ModelAndView handle503(HttpServletRequest request, HttpServletResponse response) {
        log(request);
        return new ModelAndView("/error/500.ftl");
    }

    @RequestMapping("/4041.htm")
    public ModelAndView handle4041(HttpServletRequest request, HttpServletResponse response) {

        return new ModelAndView("/error/4041.ftl");
    }

    private void log(HttpServletRequest request) {
        LOG.info("error request url : " + getRequestUrl(request));

        String errorCode = request.getAttribute(ATT_ERROR_CODE_KEY).toString();
        LOG.info("error code : " + errorCode);
        if (errorCode.trim().startsWith("50")) {
            Exception e = (Exception) request.getAttribute(ATT_ERROR_EXCEPTION_KEY);
            LOG.error("error message : ", e);
        }

    }

    protected String getRequestUrl(HttpServletRequest request) {
        String requestUri = (String) request.getAttribute(ATT_ERROR_URI);
        String queryString = (String) request.getAttribute(ATT_ERROR_QUERY_STRING);
        return requestUri + ((StringUtil.isEmpty(queryString)) ? "" : "?" + queryString);
    }

}
