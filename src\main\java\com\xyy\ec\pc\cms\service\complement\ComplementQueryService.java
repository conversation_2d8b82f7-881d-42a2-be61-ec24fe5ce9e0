package com.xyy.ec.pc.cms.service.complement;

import com.xyy.ec.pc.cms.param.CmsSeckillProductExpectQueryParam;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;

import java.util.List;

public interface ComplementQueryService {

    /**
     * 按期望数量查询秒杀商品列表。
     * 进行中 > 预热中。
     *
     * @param expectQueryParam
     * @return
     */
    List<CmsListProductVO> listExpectCmsSeckillProducts(CmsSeckillProductExpectQueryParam expectQueryParam);

}
