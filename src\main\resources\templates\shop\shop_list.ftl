<!DOCTYPE HTML>
<html>
<head>
		<#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description"
          content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>${keyword} 搜索结果页</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <#--<link rel="stylesheet" href="/css/search.css?t=${t_v}"/>-->
    <script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/sku_search.js?t=${t_v}"></script>
    <script type="text/javascript">
        var ctx = "${ctx}";
        var event = event || window.event;
        var urlPath = '${requestUrl}';
    </script>
    <style>
        body{
            background: #f4f4f4;
        }
        .quan-box{
            width:1200px;
            margin: 15px auto;
            overflow: hidden;
        }
        .quan-box .quan-left{
            width:210px;
            height:97px;
            background: #FAE4E4;
            padding-top: 22px;
            box-sizing: border-box;
        }
        .quan-box .quan-left p{
            color:#FF4244;
            font-size:14px;
            text-align: center;
            margin-bottom: 7px;
        }
        .quan-box .quan-right{
            width:990px;
            height:97px;
            background: #FFFFFF;
            padding:15px 25px;
            box-sizing: border-box;
            overflow: hidden;
            box-sizing: border-box;
        }
        .quan-box .quan-right .quan-right-one{
            width:80%;
        }
        .quan-box .quan-right .quan-right-two{
            width:20%;
        }
        .quan-box .quan-right .quan-right-one p{
            color:#292933;
            margin-bottom: 5px;
        }
        .quan-box .quan-right .quan-right-two p a{
            color:#6470B0
        }
        .quan-box .quan-right .quan-right-two p{
            text-align: right;
        }
        .quan-box .quan-right .quan-right-two p.quan-button{
            width:84px;
            height:30px;
            background:#F7F7F8;
            border-radius:15px;
            text-align: center;
            line-height: 30px;
            margin-left: 103px;
            margin-top: 17px;
        }
        .content-box{
            width:1200px;
            overflow: hidden;
            box-sizing: border-box;
            margin:0 auto 30px;
        }
        .side-left {
            width: 210px;
            border-radius:2px;
            background: #fff;
        }
        .side-left .title{
            width: 173px;
            height: 40px;
            line-height: 40px;
            color: #fff;
            font-size: 18px;
            border-bottom: 1px solid #e8e8e8;
            background: #00dc82;
            padding-left: 25px;
        }
        .side-left .list .item{
            display: block;
            width: 175px;
            height: 40px;
            line-height: 40px;
            text-align: left;
            font-size: 16px;
            color: #666;
            padding-left: 36px;
            position: relative;
            cursor: pointer;
        }
        .side-left .list .item span {
            margin-right: 10px;
            font-size: 18px;
            color: #CFCFCF;
        }
        .side-left .uicon{
            display: inline-block;
            width: 20px;
            height: 100%;
            background: url(/static/images/user/uicon.png) no-repeat;
            background-size: 100%;
            float: left;
            margin-right: 10px;
        }
        .side-left .uicon1{
            background-position: 0 10px
        }
        .side-left .list .item .uicon2{
            background-position: 0 -25px
        }
        .side-left .list .item .uicon3{
            background-position: 0 -55px
        }
        .side-left .list .item .uicon4{
            background-position: 0 -87px
        }
        .side-left .list .item .uicon5{
            background-position: 0 -117px
        }
        .side-left .list .item .uicon6{
            background-position: 0 -150px
        }
        .side-left .list .item .uicon7{
            background-position: 0 -182px
        }
        .side-left .list .item .uicon8{
            background-position: 0 -219px
        }
        .side-left .list .item .uicon9{
            background-position: 0 -254px
        }
        .side-left .list .item .uicon10{
            background-position: 0 -290px
        }
        .side-left .list .item:hover,.side-left .list .item.active{
            background: #4B4B4B;
            color: #fff;
        }
        .right-list-box,.nosearchgood{
            width: 980px;
            float: right;
        }
        .nosearchgood{
            background:#ffffff;
            padding-top: 85px;
            padding-bottom: 270px;
        }
        .right-list-box .right-top-box{
            width:980px;
            height:50px;
            background: #F9F9F9;
            position: relative;
        }
        .right-list-box .right-top-box .search-box{
            position: absolute;
            border:none;
            width:245px;
            height:30px;
            top:10px;
            right:20px;
        }
        .right-list-box .right-top-box .search-box .search-input{
            width:180px;
            height:30px;
            background: #ffffff;
            border:1px solid #E0E0E0;
            padding-left: 5px;
            box-sizing: border-box;
            position: relative;
            top:-1px;
        }
        .right-list-box .right-top-box .search-box .search-btn{
            background: #E0E0E0;
            border-radius:0px 2px 2px 0px;
            color:#333333;
            width:60px;
            height:30px;
            display: inline-block;
            text-align: center;
            line-height: 30px;
            box-sizing: border-box;
            cursor: pointer;
        }
        ul.mrth-new{
            width:980px;
            background: #ffffff;
        }
        .page{
            overflow: hidden;
            clear: both;
            margin-top: 35px;
            width:100%;
        }
        /*小计样式*/
        .applybox{
            width: 1200px;
            border: 1px solid #EEEEEE;
            height: 64px;
            background-color: #ffffff;
            margin: 0 auto;
            position: sticky;
            bottom: 0;
            z-index: 100;
        }
        .applybox .acol6{
            width: 172px;
            height: 64px;
            line-height: 64px;
            font-size: 18px;
            background: rgba(0,198,117,1);
            margin-left: 31px;
        }
        .applybox .acol6 .spe{
            display: block;
            text-align: center;
            color: #ffffff;
        }
        .applybox .acol5{
            padding:7px 0 10px;
            height:100%;
            box-sizing: border-box;
        }
        .applybox .acol5 p:first-child{
            height:28px;
            text-align: right;
        }
        .applybox .acol5 p span{
            color: #666666;
            font-size: 14px;
        }
        .applybox .acol5 p span.red{
            color: #FF5B5B;
            font-size: 20px;
            position: relative;
            top: 1px;
        }
        .top-tit-box{
            padding: 10px 0;
            background: #ffffff;
            overflow: hidden;
        }
        .top-tit-box .m-box{
            padding-left: 8px;
        }
        .top-tit-box .m-box .com-tltle{
            font-size: 14px;
            font-weight: bold;
        }
        .top-tit-box .m-box .com-info{
            font-size: 12px;
            color: #666666;
        }
        .top-tit-box .m-box .com-info .quan{
            display: inline-block;
            background: #ff0000;
            padding: 0 2px;
            border-radius: 4px;
            color: #ffffff;
        }
        .top-tit-box .m-box .com-info .cu{
            display: inline-block;
            background: #f9a426;
            padding: 0 2px;
            border-radius: 4px;
            color: #ffffff;
        }
        .top-tit-box .r-box{
            width: 104px;
            height: 34px;
            opacity: 1;
            border: 1px solid #00c675;
            border-radius: 4px;
            text-align: center;
            line-height: 34px;
            margin-top: 10px;
            margin-right: 10px;
        }
        .top-tit-box .r-box a{
            color: #00c675;
        }
        .sort-btn{
            display: inline-block;
            margin-left: 10px;
            padding: 8px 14px;
            color: #999999;
            border-radius: 6px;
            background: #ffffff;
            border: 1px solid #f8f8f8;
            cursor: pointer;
        }
        .sort-active{
            background: #00c675;
            color: #ffffff;
        }
    </style>
</head>

<body>
<div class="container">
    <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
    <input type="hidden" id="activityType" name="activityType" value="${activityType}"/>
    <input type="hidden" id="voucherTemplateId" name="voucherTemplateId" value="${voucherTemplateId}"/>
    <input type="hidden" id="shopCodeList" name="shopCodeList" value="${shopCodeList}"/>
    <input type="hidden" id="havePopShop" name="havePopShop" value="${havePopShop}"/>
    <input type="hidden" id="haveSelfShop" name="haveSelfShop" value="${haveSelfShop}"/>
    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
        <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->

    <!--主体部分开始-->
    <div class="main">
        <!--商品分类-->
        <!--当前优惠券-->
<#--        <div class="quan-box">-->
<#--            <div class="quan-left fl">-->
<#--                <#if myVoucher.voucherState==1>-->
<#--                    <p class="right-row1">-->
<#--                        <span style="font-size:36px;">${myVoucher.moneyInVoucher}</span>-->
<#--                        <span style="font-size:36px;">折</span>-->
<#--                    </p>-->
<#--                <#else>-->
<#--                    <p>¥<span style="font-size:36px;">${myVoucher.moneyInVoucher}</span></p>-->
<#--                </#if>-->
<#--                <#if myVoucher.voucherState==1>-->
<#--                    <p>满${myVoucher.minMoneyToEnable}可用</p>-->
<#--                <#else>-->

<#--                    <#if (myVoucher.voucherUsageWay ==1 && myVoucher.maxMoneyInVoucherDesc?? && myVoucher.maxMoneyInVoucherDesc!=null&& myVoucher.maxMoneyInVoucherDesc!="") >-->
<#--                        <p>每满${myVoucher.minMoneyToEnable}减${myVoucher.moneyInVoucher}<br>${myVoucher.maxMoneyInVoucherDesc}</p>-->
<#--                    <#else>-->
<#--                        <p>满${myVoucher.minMoneyToEnable}减${myVoucher.moneyInVoucher}</p>-->
<#--                    </#if>-->
<#--                </#if>-->
<#--            </div>-->
<#--            <div class="quan-right fl">-->
<#--                <div class="quan-right-one fl">-->
<#--                    <p>${myVoucher.shopName}</p>-->
<#--                    <#if myVoucher.validDate?? && myVoucher.expireDate??>-->
<#--                        <p>使用期限：${myVoucher.validDate?string("yyyy/MM/dd")}-${myVoucher.expireDate?string("yyyy/MM/dd")}</p>-->
<#--                    </#if>-->
<#--                    <p>使用说明：<span>${myVoucher.voucherInstructions}</span></p>-->
<#--                </div>-->
<#--                <div class="quan-right-two fl">-->
<#--                    <p>-->
<#--                        <#if source ?? >-->
<#--                            <!--领券中心&ndash;&gt;-->
<#--                            <#if 1 == source>-->
<#--                                <a href="/activity/voucherCenter.htm?isVoucherCenter=true">更多优惠券>></a>-->
<#--                            <#else>-->
<#--                                <#if 2 == source>-->
<#--                                    <a href="/merchant/center/cart/index.htm">返回购物车>></a>-->
<#--                                <#else>-->
<#--                                    <a href="/merchant/center/voucher/findAllVoucherInfoWithShop.htm">返回我的优惠券>></a>-->
<#--                                </#if>-->
<#--                            </#if>-->
<#--                        <#else>-->
<#--                            <a href="/merchant/center/voucher/findAllVoucherInfoWithShop.htm">返回我的优惠券>></a>-->
<#--                        </#if>-->
<#--                    </p>-->
<#--                    <p class="quan-button">已领取</p>-->
<#--                </div>-->
<#--            </div>-->
<#--        </div>-->
        <div class="content-box">
            <div class="side-left fl">
                <div class="list" style="padding-top: 20px">
<#--                    <#if categoryList ?? && (categoryList ?size>0) >-->
<#--                        <#if voucherCenterDataList.categoryId??>-->
<#--                            <span class="item" id="9999" onclick="searchProdctByCT(9999)">全部</span>-->
<#--                        <#else>-->
<#--                            <span class="item active" id="9999" onclick="searchProdctByCT(9999)">全部</span>-->
<#--                        </#if>-->
<#--                        <#list categoryList as c>-->

<#--                            -->
<#--                        </#list>-->
<#--                    </#if>-->
                    <#if havePopShop??>

                    </#if>
                    <#if haveSelfShop??>

                    </#if>
                    <span class="item active" id="self" onclick="searchProdctByCT('self')">自营</span>
                    <span class="item" id="pop" onclick="searchProdctByCT('other')">合作商家</span>
                </div>
            </div>
            <#if pageInfo??&&pageInfo.rows??&&(pageInfo.rows?size>0)>
                <!--大图模式-->
                <#--<#if (modelType ==2)>-->
                <div class="right-list-box">
                    <div style="padding: 15px 0">
                        <div class="sort-btn sort-active" data-sort="0" onclick="sotrLst(0)">默认</div>
                        <div class="sort-btn" data-sort="1" onclick="sotrLst(1)">最新入驻</div>
                    </div>
<#--                    <div class="right-top-box">-->
<#--                        <div class="search-box">-->
<#--                            <input type="text" class="search-input" placeholder="在结果中搜索" value=""/><span class="search-btn" onclick="searchK()">搜索</span>-->
<#--                        </div>-->
<#--                    </div>-->
                    <#list pageInfo.rows as preferredBrand>
                        <div style="margin-bottom: 20px;overflow: hidden">
                            <div class="toptj-box clearfix">
                                <div class="top-tit-box">
                                    <div class="l-box fl">
                                        <span class="top-span1"><img style="width: 52px;height: 52px" src="${productImageUrl}${preferredBrand.appLogo}" alt=""></span>
                                    </div>
                                    <div class="m-box fl">
                                        <div class="com-tltle">${preferredBrand.showName}</div>
                                        <div class="com-info">${preferredBrand.salesVolumeDesc?replace('xxx',preferredBrand.salesVolume)}  上架${preferredBrand.shelves}种  ${preferredBrand.freightTips}</div>
                                        <div class="com-info">
                                            <#list preferredBrand.activityInfo as activiInfo>
                                                <#if activiInfo.activityType == 1 || activiInfo.activityType == 2 || activiInfo.activityType == 3>
                                                    <span class="quan">${activiInfo.activityTypeDesc}</span>
                                                <#else >
                                                    <span class="cu">${activiInfo.activityTypeDesc}</span>
                                                </#if>
                                                <span> ${activiInfo.activityContent}</span>
                                            </#list>
                                        </div>
                                    </div>
                                    <div class="r-box fr">
                                        <a href="${preferredBrand.pcLink}" class="more">进店逛逛</a>
                                    </div>
                                </div>
                            </div>
                            <ul class="mrth-new fr" style="box-shadow: none">
                                <#list preferredBrand.productInfo as skuVO>
                                    <#if skuVO.actPt??>
                                        <#import "/common/skuPinVO.ftl" as pr>
                                        <@pr.skuVO skuVO/>
                                    <#elseif skuVO.actPgby??>
                                        <#import "/common/skuPgbyVO.ftl" as pr>
                                        <@pr.skuVO skuVO/>
                                    <#elseif skuVO.actSk??>
                                        <#import "/common/skuSkVO.ftl" as pr>
                                        <@pr.skuVO skuVO/>
                                    <#else >
                                        <#import "/common/skuVO.ftl" as pr>
                                        <@pr.skuVO skuVO/>
                                    </#if>
                                </#list>
                            </ul>
                        </div>
                    </#list>
                    <!--分页器-->
                    <div class="page">
                         <#import "/common/pager.ftl" as p>
                        <@p.pager currentPage=pageInfo.currentPage limit=pageInfo.limit total=pageInfo.total pageCount=pageInfo.pageCount toURL=pageInfo.requestUrl method="get"/>
                    </div>
                </div>
            <#else>
                <div class="right-list-box">
<#--                    <div class="right-top-box">-->
<#--                        <div class="search-box">-->
<#--                            <input type="text" class="search-input" placeholder="在结果中搜索" value=""/><span class="search-btn" onclick="searchK()">搜索</span>-->
<#--                        </div>-->
<#--                    </div>-->
                    <div class="nosearchgood">
                        <div class="tpbox" style="text-align: center;margin-bottom: 30px;">
                            <img src="${ctx}/static/images/nosearchresult.png" alt="" style="width:290px;height:200px;">
                            <p style="color:#999;margin-top:38px;">啊哦...没有找到相关的店铺</p>
                        </div>
                    </div>
                </div>
            </#if>
        </div>
    </div>

    <!--主体部分结束-->
    <!--小计-->
<#--    <#if activityType==1 >-->
<#--        <div class="applybox">-->
<#--            <div class="acol6 fr">-->
<#--                <a href="/merchant/center/cart/index.htm" class="spe">去购物车</a>-->
<#--            </div>-->
<#--            <div class="acol5 fr">-->
<#--                <p>-->
<#--                    <span>小计：</span>-->
<#--                    <span class="all-price red"></span>-->
<#--                </p>-->
<#--                <p style="color:#FF5B5B;font-size:14px;" class="all-intro"></p>-->
<#--            </div>-->
<#--        </div>-->
<#--    </#if>-->
    <!--底部导航区域开始-->
    <div class="footer" id="footer">
            <#include "/common/footer.ftl" />
    </div>
    <!--底部导航区域结束-->

</body>
<script>
        function searchProdctByCT(id){
            var urlPath = window.location.href;
            var path = urlPath;
            path = appendParamToUrl(path,"shopPropertyCode",id);
            // path = path.replace("&categoryId=9999","");
            //path = path.replace("&offset=","");
            path = appendParamToUrl(path,"offset",1);
            window.location.href = path;
        }
        function sotrLst(val){
            console.log(val,'pppp')
            var urlPath = window.location.href;
            var path = urlPath;
            path = appendParamToUrl(path,"sort",val);
            // path = path.replace("&categoryId=9999","");
            path = appendParamToUrl(path,"offset",1);
            window.location.href = path;
        }
        $(function () {
            if(!$("#havePopShop").val() && $("#haveSelfShop").val()|| window.location.href.indexOf('self') > -1){
                $("#self").addClass('active').attr("onclick",null);
                $("#pop").removeClass('active')
            } else if(window.location.href.indexOf('other') > -1) {
                $("#self").removeClass('active');
                $("#pop").addClass('active').attr("onclick",null);
            }
            if(window.location.href.indexOf('sort=0') > -1){
                $(".sort-btn").eq(0).addClass('sort-active').attr("onclick",null);
                $(".sort-btn").eq(1).removeClass('sort-active')
            } else if(window.location.href.indexOf('sort=1') > -1) {
                $(".sort-btn").eq(1).addClass('sort-active').attr("onclick",null);
                $(".sort-btn").eq(0).removeClass('sort-active')
            }
            if(window.location.href.indexOf('shop/shopList.htm?sort') > -1) {
                setTimeout(function () {
                    $('#topnav a').each(function () {
                        console.log($(this).html())
                        if($(this).html() == '店铺列表'){
                            $(this).addClass('cur')
                        }
                    })
                },500)
            }
        })
</script>
</html>
