package com.xyy.ec.pc.remote;

import com.xyy.ec.merchant.server.dto.AccountInfoDto;
import com.xyy.ec.merchant.server.dto.MerchantAndAccountDto;

import java.util.List;

/**
 * @Auther chenshaobo
 * @Date 2024/7/16
 * @Description 子账号
 * @Version V1.0
 **/
public interface AccountProviderService {

    /**
     * 根据merchantId查询子账号信息
     * @param merchantId
     * @return
     */
    List<AccountInfoDto>  querySubAccountList(Long merchantId);

    /**
     * 根据syncNo查询主账号信息
     * @param syncNo
     * @return
     */
    MerchantAndAccountDto queryManagerAccountBySyncNo(String syncNo);

}
