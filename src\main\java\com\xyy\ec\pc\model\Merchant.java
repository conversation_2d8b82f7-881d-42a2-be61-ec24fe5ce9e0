package com.xyy.ec.pc.model;

import com.xyy.ec.pc.base.Principal;
import com.xyy.ec.pc.enums.BusinessTypeEnum;

import java.util.Date;

/**
 * @Description:
 * @Author: WanKp
 * @Date: 2018/8/25 19:28
 **/
public class Merchant implements Principal {

    //主键
    private Long id;

    //同步编号
    private String syncNo;

    //商户真实名称
    private String realName = "";

    //手机号
    private String mobile = "";

    //密码
    private String password;

    //邮箱地址
    private String email = "";

    //昵称
    private String nickname = "";

    //状态
    private Integer status;

    //商户类型：1-普通账户，2-测试账户
    private Integer type;

    //创建时间
    private Date createTime;

    //更新时间
    private Date updateTime;

    //邀请码
    private String authcode = "";

    //商户资质状态
    private Integer licenseStatus;

    private String licenseText;

    //是否可以点击邀请码
    private Integer checkcode;

    //新老客户标识(1：新客户，2：老客户)
    private Integer merchantType;

    //注册来源（2-ios，1-android，3-H5，4-pc）
    private Integer registerSource;

    //激活来源（2-ios，1-android，3-H5，4-pc）
    private Integer activeSource;

    //激活时间
    private Date activeTime;

    //新增字段  商户类型（业务类型）：1-个体药店，2-连锁药店（加盟），3-连锁药店（直营），4-诊所
    private Integer businessType;

    // 商户类型名称
    private String businessTypeName;

    //注册地址编码
    private String registerCode;

    private Date lastLoginDate;

    private Date createStartTime;            //开始时间
    private Date createEndTime;                //结束时间
    private Long bizLastLoginTime;

    @SuppressWarnings("unused")
    private String identity;            //当前对象标识

    @SuppressWarnings("unused")
    private String loginName;            //登录名

    //药店名称或者电话号码
    private String searchConditon;

    ////////商户资质状态///////////////////
    private String sysRealName;

    //是否新用户id:不要去掉
    private Long merchantId;

    //发票邮寄地址
    private String invoiceMailAddress;

    //联系方式备用
    private String mobileBackUp;

    private Integer customerType;

    private Integer saasUser;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSearchConditon() {
        return searchConditon;
    }

    public void setSearchConditon(String searchConditon) {
        this.searchConditon = searchConditon;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSyncNo() {
        return syncNo;
    }

    public void setSyncNo(String syncNo) {
        this.syncNo = syncNo;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname == null ? null : nickname.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getAuthcode() {
        return authcode;
    }

    public void setAuthcode(String authcode) {
        this.authcode = authcode == null ? null : authcode.trim();
    }

    public Integer getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(Integer licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public Date getLastLoginDate() {
        return lastLoginDate;
    }

    public void setLastLoginDate(Date lastLoginDate) {
        this.lastLoginDate = lastLoginDate;
    }

    @Override
    public String getIdentity() {
        return this.id + "";
    }

    @Override
    public Long getLastLoginTime() {
        return lastLoginDate == null ? null : lastLoginDate.getTime();
    }

    @Override
    public String getLoginName() {
        return mobile;
    }

    public void setBizLastLoginTime(Long bizLastLogin) {
        this.bizLastLoginTime = bizLastLogin;
    }

    @Override
    public Long getBizLastLoginTime() {
        return bizLastLoginTime;
    }

    @Override
    public Long getMechantId() {
        return this.id;
    }

    public Date getCreateStartTime() {
        return createStartTime;
    }

    public void setCreateStartTime(Date createStartTime) {
        this.createStartTime = createStartTime;
    }

    public Date getCreateEndTime() {
        return createEndTime;
    }

    public void setCreateEndTime(Date createEndTime) {
        this.createEndTime = createEndTime;
    }

    public Integer getCheckcode() {
        return checkcode;
    }

    public void setCheckcode(Integer checkcode) {
        this.checkcode = checkcode;
    }

    public Integer getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(Integer merchantType) {
        this.merchantType = merchantType;
    }

    public Integer getRegisterSource() {
        return registerSource;
    }

    public void setRegisterSource(Integer registerSource) {
        this.registerSource = registerSource;
    }

    public Integer getActiveSource() {
        return activeSource;
    }

    public void setActiveSource(Integer activeSource) {
        this.activeSource = activeSource;
    }

    public Date getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(Date activeTime) {
        this.activeTime = activeTime;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getLicenseText() {
        return licenseText;
    }

    public void setLicenseText(String licenseText) {
        this.licenseText = licenseText;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getBusinessTypeName() {
        if (BusinessTypeEnum.maps.containsKey(this.businessType)) {
            this.businessTypeName = BusinessTypeEnum.maps.get(this.businessType);
        } else {
            this.businessTypeName = "其他";
        }
        return this.businessTypeName;
    }

    public void setBusinessTypeName(String businessTypeName) {
        this.businessTypeName = businessTypeName;
    }

    public String getRegisterCode() {
        return registerCode;
    }

    public void setRegisterCode(String registerCode) {
        this.registerCode = registerCode;
    }

    public String getSysRealName() {
        return sysRealName;
    }

    public void setSysRealName(String sysRealName) {
        this.sysRealName = sysRealName;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public String getInvoiceMailAddress() {
        return invoiceMailAddress;
    }

    public void setInvoiceMailAddress(String invoiceMailAddress) {
        this.invoiceMailAddress = invoiceMailAddress;
    }

    public String getMobileBackUp() {
        return mobileBackUp;
    }

    public void setMobileBackUp(String mobileBackUp) {
        this.mobileBackUp = mobileBackUp;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public Integer getSaasUser() {
        return saasUser;
    }

    public void setSaasUser(Integer saasUser) {
        this.saasUser = saasUser;
    }
}
