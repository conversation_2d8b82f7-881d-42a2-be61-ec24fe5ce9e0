package com.xyy.ec.pc.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.Date;

/**
 * @Description: 加密解密工具类
 * @Author: System
 * @Date: 2024
 */
public class CryptoUtil {
    
    // 内部密钥，实际使用时应该从配置文件读取
    private static final String SECRET_KEY = "XYY_EC_INTERNAL_2025";
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    
    /**
     * 验证并解析令牌
     * @param token 加密令牌
     * @param validMinutes 有效期（分钟）
     * @return 手机号，如果验证失败返回null
     */
    public static String validateAndParseToken(String token, int validMinutes) {
        try {
            String decrypted = decrypt(token);
            String[] parts = decrypted.split("\\|");
            if (parts.length != 2) {
                return null;
            }
            
            String mobile = parts[0];
            long timestamp = Long.parseLong(parts[1]);
            long currentTime = System.currentTimeMillis();
            
            // 检查时间戳是否在有效期内
            if (currentTime - timestamp > validMinutes * 60 * 1000) {
                return null;
            }
            
            return mobile;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * AES解密
     */
    private static String decrypt(String encryptedData) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(getKey(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decrypted, StandardCharsets.UTF_8);
    }
    
    /**
     * 生成固定长度的密钥
     */
    private static byte[] getKey() throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] hash = md.digest(SECRET_KEY.getBytes(StandardCharsets.UTF_8));
        byte[] key = new byte[16]; // AES-128需要16字节密钥
        System.arraycopy(hash, 0, key, 0, 16);
        return key;
    }
    
    /**
     * 生成简单的签名验证
     * @param mobile 手机号
     * @param timestamp 时间戳
     * @return 签名
     */
    public static String generateSignature(String mobile, long timestamp) {
        try {
            String data = mobile + timestamp + SECRET_KEY;
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(data.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }
    
    /**
     * 验证签名
     * @param mobile 手机号
     * @param timestamp 时间戳
     * @param signature 签名
     * @return 是否验证通过
     */
    public static boolean verifySignature(String mobile, long timestamp, String signature) {
        String expectedSignature = generateSignature(mobile, timestamp);
        return expectedSignature.equals(signature);
    }
}