package com.xyy.ec.pc.authentication.domain;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Principal;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @Date 2024/4/10 15:20
 * @File JwtPrincipal.class
 * @Software IntelliJ IDEA
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JwtPrincipal extends MerchantPrincipal implements Principal {

    // TODO 登录用户过期时间
    private Long expireTime;

    // TODO 用户登录时间
    private Long loginTime;

    // TODO token
    private String token;

    // TODO 用户登录IP
    private String loginIpAddr;

    // TODO 登录设备号
    private String loginDeviceId;

    // TODO 登录版本号
    private String loginVersion;

    // TODO 登录操作系统
    private String loginOs;

    // TODO 登录浏览器
    private String loginBrowser;

    // TODO 最后登录时间
    private Long lastLoginTime;

    // TODO 会员ID
    private Long mechantId;

    // TODO 是否异地登录
    private Boolean isDifferentPlacesLogin = false;
}