package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;


/**
 * 
 * <AUTHOR>
 *
 */
public enum BalanceStatusEnum {


	/**
	 * （1：活动赠送 2：退款返还 3：取消返还 4：购物抵扣 13:协议返还 16:商品未降价）
	 */
    PROMOTION(1,"活动赠送"),
    REUNFD(2,"退款返还"),
    CANCEL(3,"取消返还"),
    PURCHASE(4,"购物抵扣"),
    CASH(11,"提现"),
    CASH_FAILE(12,"提现失败"),
    PLATFORM(13,"协议返还"),
    NOREDUCTION(16,"商品未降价"),
    SUBSIDY(17,"宜块钱补贴"),
    REUNFDDELETE(18,"退款扣除"),
    REUNFDCLOSE(19,"退款关闭"),
    GAOMAOAGREEMENT(20,"高毛协议"),
    DUJIAAGREEMENT(21,"明星协议"),
    MINGXINGAGREEMENT(22,"厂商协议"),
    SIXONEEIGHTFANLI(23,"618返利"),
    DAZHUANPAN(24,"大转盘活动返利"),
    GUANGFA(25,"小药白条贴息余额");
    private int id;
    private  String value;

    BalanceStatusEnum(int id, String value){
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, BalanceStatusEnum> eMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(BalanceStatusEnum control : BalanceStatusEnum.values()) {
        	eMaps.put(control.getId(), control);
            maps.put(control.getId(),control.getValue());
        }
    }

    public static String get(int id) {
        return eMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
