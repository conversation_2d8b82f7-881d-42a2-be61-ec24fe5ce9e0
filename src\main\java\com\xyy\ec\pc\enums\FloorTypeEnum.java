package com.xyy.ec.pc.enums;

/**
 * <p>楼层类型枚举</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021年06月15日 下午2:42
 */
public enum FloorTypeEnum {

    PING_TUAN(1, "拼团专区"),
    TE_JIA(2, "特价专区"),
    SELF_FLOOR(3, "自配专区");

    private int code;
    private String name;

    FloorTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
