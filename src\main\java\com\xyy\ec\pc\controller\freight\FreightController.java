package com.xyy.ec.pc.controller.freight;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.message.Transaction;
import com.xyy.cat.util.CatUtil;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.api.ecp.freight.FreightBusinessApi;
import com.xyy.ec.order.business.api.ecp.freight.FreightTemplateBusinessApi;
import com.xyy.ec.order.business.dto.ShoppingCartBusinessDto;
import com.xyy.ec.order.business.dto.freight.FreightProductConditionDto;
import com.xyy.ec.order.business.dto.freight.FreightTemplateBusinessDto;
import com.xyy.ec.order.business.dto.shop.CartBusinessDto;
import com.xyy.ec.order.core.dto.cart.ShoppingCartDto;
import com.xyy.ec.order.core.enums.CouponComputeAccountEnum;
import com.xyy.ec.order.core.util.BeanHelper;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.model.order.OrderSettleVo;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.OrderSettleUtil;
import com.xyy.ec.product.business.dto.ProductDto;
import com.xyy.ec.product.business.dto.SkuConditionDto;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * @description: 运费
 * @author: yejunjian
 * @create: 2020-07-08 13:43:06
 **/
@Controller
@RequestMapping("/merchant/freight/")
public class FreightController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(FreightController.class);

    @Reference(version = "1.0.0")
    private FreightTemplateBusinessApi freightTemplateBusinessApi;

    @Reference(version = "1.0.0")
    private FreightBusinessApi freightBusinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    /**
     * @description: 查询运费信息
     * @author: yejunjian
     * @create: 2020-07-08 13:22
     * @param: [order, terminalType, version]
     * @return: java.lang.Object
     **/
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    @ResponseBody
    public Object query(String shopCode) {
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchantId = merchant.getId();
            if (merchantId == null) {
                return addError("merchantId不能为空");
            }
            ApiRPCResult<FreightTemplateBusinessDto> result = freightTemplateBusinessApi.queryFreightTemplateByMerchantIdV2(merchantId,shopCode);

            if (result != null && result.isFail()) {
                return addError(result.getErrMsg());
            }

         return result.getData();
        } catch (Exception e) {
            LOGGER.error("merchantId={}查询运费信息出现异常 ", merchantId,e);
            return addError("查询运费信息出现异常");
        }

    }

    /**
     * 打开包邮凑单页
     * @param freightProductConditionDto
     * @return
     */
    @RequestMapping("/list.htm")
    public ModelAndView list(FreightProductConditionDto freightProductConditionDto, HttpServletRequest request){
        try{
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            freightProductConditionDto.setMerchantId(merchant.getId());
            freightProductConditionDto.setBranchCode(merchant.getRegisterCode());
            freightProductConditionDto.setPageSize(100000);
            freightProductConditionDto.setOrderSource(4);
            freightProductConditionDto.revertPriceRange();
            ApiRPCResult<List<ProductDTO>> apiRPCResult = freightBusinessApi.queryFreightProductForFreeShipping(freightProductConditionDto);

            Page<ProductDTO> skuPOJOPage = new Page<>();
            skuPOJOPage.setRows(apiRPCResult.getData());
            Map<String, Object> objModel = new HashMap<>();
            //商品搜索页分页数据
            objModel.put("pager", skuPOJOPage);
            //用户数据
            objModel.put("merchant", merchant);
            String url = getRequestUrl(request);
            skuPOJOPage.setRequestUrl(url);
            ApiRPCResult<JSONObject> freightSubApi = null;
            if(freightProductConditionDto.getRoute().equals("settle")){
                freightSubApi = getSettleFreight(merchant);
            }else if(freightProductConditionDto.getRoute().equals("cart")){
                freightSubApi = getCartFreight(merchant);
            }
            objModel.put("merchantId",merchant.getId());
            objModel.put("route", freightProductConditionDto.getRoute());
            objModel.put("data", freightSubApi.getData());
            objModel.put("suoding", freightProductConditionDto.getPriceRange());
            return new ModelAndView("/cart/baoyou_sku_list.ftl",objModel);
        }catch (Exception e){
            LOGGER.error("cartController list error",e);
            return new ModelAndView("/cart/baoyou_sku_list_s.ftl");
        }
    }

    /**
     * @description: 运费凑单页
     * @author: yejunjian
     * @create: 2020-08-03 13:22
     * @param: [order, terminalType, version]
     * @return: java.lang.Object
     **/
    @RequestMapping(value = "/queryFreightProductForFreeShippingSubtotal.json", method = RequestMethod.POST)
    @ResponseBody
    public Object queryFreightProductForFreeShippingSubtotal(FreightProductConditionDto freightProductConditionDto) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            ApiRPCResult<JSONObject> freightSubApi = null;
            if(freightProductConditionDto.getRoute().equals("settle")){
                freightSubApi = getSettleFreight(merchant);
            }else if(freightProductConditionDto.getRoute().equals("cart")){
                freightSubApi = getCartFreight(merchant);
            }
            if (freightSubApi.isSuccess()) {
                return this.addResult("data", freightSubApi.getData());
            }
            return addError(freightSubApi.getErrMsg());
        } catch (Exception e) {
            LOGGER.error("queryProductsForFreeFreight error :requestParams={} ", JSONObject.toJSONString(freightProductConditionDto),e);
            return addError("查询运费凑单页异常，请稍后再试。");
        }
    }

    @RequestMapping(value = "/queryFreightBlacklist", method = RequestMethod.POST)
    @ResponseBody
    public Object queryFreightBlacklist(FreightProductConditionDto freightProductConditionDto) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            freightProductConditionDto.setMerchantId(merchant.getId());
            freightProductConditionDto.setBranchCode(merchant.getRegisterCode());
            ApiRPCResult<List<ShoppingCartDto>> result = freightBusinessApi.queryFreightBlacklist(freightProductConditionDto);
            if(result == null){
                return addError("查询超重商品异常，请稍后再试。");
            }
            if (result.isSuccess()) {
                return this.addResult("data", result.getData());
            }
            return addError(result.getErrMsg());
        } catch (Exception e) {
            LOGGER.error("queryProductsForFreeFreight error :requestParams={} ", JSONObject.toJSONString(freightProductConditionDto),e);
            return addError("查询超重商品异常，请稍后再试。");
        }
    }

    private ApiRPCResult<JSONObject> getSettleFreight(MerchantBussinessDto merchant){
        OrderSettleVo order = new OrderSettleVo();
        order.setOrderSource(4);
        order.setAppVersion(1);
        order.setUseBalance(false);
        order.setMerchantId(merchant.getId());
        order.setBranchCode(merchant.getRegisterCode());

        ApiRPCResult<JSONObject> apiRPCResult = freightBusinessApi.querySettleFreightProductForFreeShippingSubtotal(OrderSettleUtil.getRequestDto(order, CouponComputeAccountEnum.INITIALIZE_SETTLEMENT_ALLOCATION));

        return apiRPCResult;
    }

    private ApiRPCResult<JSONObject> getCartFreight(MerchantBussinessDto merchant){
        ShoppingCartDto cart = new ShoppingCartDto();
        cart.setTerminalType(4);
        cart.setAppVersion(1);
        cart.setMerchantId(merchant.getId());

        ApiRPCResult<JSONObject> apiRPCResult = freightBusinessApi.queryCartFreightProductForFreeShippingSubtotal(cart);

        return apiRPCResult;
    }
}
