package com.xyy.ec.pc.newfront.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: TODO
 * @date: 2025/5/27 12:44
 * @author: <EMAIL>
 * @version: 1.0
 */

@Getter
@Setter
@ToString
public class LoginParamVO {
    String redirectUrl;
    String registerSource;
    String agreements; //: [{"agreementId":13,"agreementVersion":"20250425"},{"agreementId":16,"agreementVersion":"20250425"}]
    String checkTime; //: 1748327934810
    String name; //: 18700002008
    String password; //5690DDDFA28AE085D23518A035707282
    String agreement; //: on

}

