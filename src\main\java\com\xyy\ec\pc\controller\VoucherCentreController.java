package com.xyy.ec.pc.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.client.api.MarketingQueryApi;
import com.xyy.ec.marketing.client.dto.ActInfoDTO;
import com.xyy.ec.marketing.client.dto.param.ActInfoQueryDTO;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.CouponApi;
import com.xyy.ec.marketing.hyperspace.api.ShopCouponForShopcartQueryService;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponBaseDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.model.voucher.ProductPcDto;
import com.xyy.ec.pc.model.voucher.VoucherSkuSearchPcData;
import com.xyy.ec.pc.newfront.service.VoucherNewService;
import com.xyy.ec.pc.param.ShopStatisticInfoQueryParam;
import com.xyy.ec.pc.rpc.EcSearchServiceRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.product.vouchercenter.VoucherCenterService;
import com.xyy.ec.pc.service.product.vouchercenter.dto.ProductVoucherSearchDto;
import com.xyy.ec.pc.service.product.vouchercenter.rpc.ProductBusinessApiRpc;
import com.xyy.ec.pc.shop.service.ShopService;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.pc.vo.ShopInfoSupperVO;
import com.xyy.ec.product.business.api.ProductVoucherCenterDataBusinessApi;
import com.xyy.ec.product.business.api.ecp.skucategory.EcpCategoryBusinessApi;
import com.xyy.ec.product.business.constants.TagSortEnum;
import com.xyy.ec.product.business.dto.*;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import com.xyy.ec.product.business.dto.listOfSku.VoucherSkuSearchData;
import com.xyy.ec.product.business.dto.product.ProductActivityTag;
import com.xyy.ec.product.business.ecp.csu.dto.CsuDTO;
import com.xyy.ec.product.business.ecp.csu.dto.CsuQueryDTO;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.product.business.ecp.out.order.api.ProdcutForOrderApi;
import com.xyy.ec.product.business.ecp.out.product.ProdcutApi;
import com.xyy.ec.product.business.ecp.vouchercenter.api.ProductVoucherCenterBusinessApi;
import com.xyy.ec.product.business.ecp.vouchercenter.dto.VoucherCsuSearchDto;
import com.xyy.ec.product.business.module.CategoryVo;
import com.xyy.ec.search.engine.api.EcSearchForMarketingApi;
import com.xyy.ec.search.engine.dto.marketing.SearchCsuBaseDTO;
import com.xyy.ec.search.engine.metadata.IPage;
import com.xyy.ec.search.engine.params.marketing.EcSearchAggregateCsuFirstCategoryIdsQueryParam;
import com.xyy.ec.search.engine.params.marketing.EcSearchPagingListCsuBasesQueryParam;
import com.xyy.ec.shop.server.business.api.ShopQueryApi;
import com.xyy.ec.shop.server.business.enums.BuyerLogonTypeEnum;
import com.xyy.ec.shop.server.business.enums.ShopPropertyEnum;
import com.xyy.ec.shop.server.business.params.BuyerParam;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.ms.promotion.business.api.pc.VoucherForPcBusinessApi;
import com.xyy.ms.promotion.business.common.constants.VoucherEnum;
import com.xyy.ms.promotion.business.common.response.PromoResp;
import com.xyy.ms.promotion.business.dto.MerchantRequestDTO;
import com.xyy.ms.promotion.business.dto.voucher.MyVoucherDTO;
import com.xyy.ms.promotion.business.dto.voucher.MyVoucherPageDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 双十二
 * 领券中心改版
 *
 * @Date: 2019/11/29
 */
@RequestMapping("/voucher/centre")
@Controller
public class VoucherCentreController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VoucherCentreController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    ProductVoucherCenterDataBusinessApi productVoucherCenterDataBusinessApi;

    @Reference(version = "1.0.0",timeout = 10000)
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private VoucherForPcBusinessApi promotionVoucherApi;
    @Autowired
    private PcVersionUtils appVersionUtils;

    @Autowired
    VoucherCenterService voucherCenterService;
    @Reference(version = "1.0.0")
    private ShopCouponForShopcartQueryService shopCouponForShopcartQueryService;

    @Reference(version = "1.0.0")
    private CouponApi couponApi;

    @Reference(version = "1.0.0")
    private ShopQueryApi shopQueryApi;

    @Autowired
    private ShopService shopService;

    @Value("${voucher.shop.list.simple.sku.flag:1}")
    private int voucherShopListUseSimpleSkuFlag;

    @Reference(version = "1.0.0")
    MarketingQueryApi marketingQueryApi;

    @Autowired
    private EcSearchServiceRpc ecSearchServiceRpc;

    @Reference(version = "1.0.0")
    private ProdcutForOrderApi prodcutForOrderApi;

    @Reference(version = "1.0.0")
    private ProdcutApi prodcutApi;

    @Reference(version = "1.0.0")
    private EcpCategoryBusinessApi categoryBusinessApi;

    @Reference(version = "1.0.0")
    ProductVoucherCenterBusinessApi productVoucherCenterBusinessApi;

    @Autowired
    ProductBusinessApiRpc productBusinessApiRpc;

    @Value("${voucher.sku.info.switch:0}")
    private int voucherSkuInfoSwitch;

    @Autowired
    private VoucherNewService voucherNewService;




    /**
     * 查询劵商品信息
     * @param request
     * @return
     */
    @RequestMapping("/findVoucherSku.htm")
    public ModelAndView findSkuInfo(Page page, HttpServletRequest request, VoucherSkuSearchDto voucherSkuSearchDto, Integer source,
                                    @RequestParam(value = "shopCodeList", required = false) String shopCodeList,
                                    @RequestParam(value = "hasReceived", required = false) Integer hasReceived) {
        Map<String, Object> model = new HashMap<>();
        // 分页要用，将url传到前台
        String url = getRequestUrl(request);
        MerchantBussinessDto merchant=null;
        List<VoucherCategoryDto> categoryList=null;
        VoucherSkuSearchPcData voucherCenterDataList=null;
            try {
                merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
                if(voucherSkuSearchDto.getVoucherTemplateId()==null||null==merchant){
                    return new ModelAndView("/error/404.ftl");
                }
            } catch (Exception e) {
                LOGGER.error("/voucher/centre/findVoucherSku.htm--error:{}", e);
                return new ModelAndView("/error/404.ftl");
            }


            try {
                voucherNewService.receive(Collections.singletonList(voucherSkuSearchDto.getVoucherTemplateId()));

            }catch (Exception e){
                LOGGER.error("领取优惠券失败",e);
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchant.getId());
            voucherSkuSearchDto.setBranchCode(branchCode);
            voucherSkuSearchDto.setMerchantId(merchant.getId());
            if(Objects.isNull(voucherSkuSearchDto.getActivityType())){
                voucherSkuSearchDto.setActivityType(0);
            }else{
                voucherSkuSearchDto.setActivityType(voucherSkuSearchDto.getActivityType());
            }
            ProductVoucherSearchDto data=new ProductVoucherSearchDto();
            BeanUtils.copyProperties(voucherSkuSearchDto,data);
            categoryList=voucherCenterService.searchCsuCategoryForVoucher(data);
            if(page==null){
                page=new Page();
                page.setLimit(12);
                page.setOffset(1);
            }else{
                page.setLimit(12);
            }
            PageDto pageDto = new PageDto();
            pageDto.setLimit(page.getLimit());
            pageDto.setOffset(page.getOffset());
            ProductVoucherSearchDto productVoucherSearchDto=new ProductVoucherSearchDto();
            productVoucherSearchDto.setPageNum(pageDto.getOffset());
            productVoucherSearchDto.setHasReceived(hasReceived);
            productVoucherSearchDto.setPageSize(pageDto.getLimit());
            BeanUtils.copyProperties(voucherSkuSearchDto,productVoucherSearchDto);
            if (StringUtil.isNotEmpty(shopCodeList)) {
                List<String> shopCodes = Lists.newArrayList(shopCodeList.split(","));
                productVoucherSearchDto.setShopCodeList(shopCodes);
            }
            if (Objects.equals(0, voucherSkuInfoSwitch)) {
                voucherCenterDataList = voucherCenterService.searchCsuInfoForVoucher(productVoucherSearchDto);
            } else {
                voucherCenterDataList = voucherCenterService.searchCsuInfoForVoucherNew(productVoucherSearchDto);
            }
            List<ProductPcDto> skuPOJOList = null;
            Long pages=0L;
            if (voucherCenterDataList != null){
                skuPOJOList = voucherCenterDataList.getSkuList();
                if(CollectionUtil.isNotEmpty(skuPOJOList)){
                    pages = voucherCenterDataList.getCount() % voucherCenterDataList.getPageSize() == 0 ? (voucherCenterDataList.getCount() / voucherCenterDataList.getPageSize()) : (voucherCenterDataList.getCount() / voucherCenterDataList.getPageSize() + 1);
                    skuPOJOList.forEach(sku->{
                        //如果是髙毛商品则打髙毛标签
                        if(Objects.nonNull(sku.getHighGross())){
                            if (sku.getHighGross().equals(Constants.IS2)){
                                TagDTO tagDTO = new TagDTO();
                                tagDTO.setName("髙毛");
                                tagDTO.setUiType(2);
                                tagDTO.setSort(TagSortEnum.GaoMao.sortNum());
                                List<TagDTO> tagDTOList = sku.getTagList();
                                if (CollectionUtil.isNotEmpty(tagDTOList)){
                                    tagDTOList.add(tagDTO);
                                    //按打标的优先级从小到大排序
                                    tagDTOList = tagDTOList.stream().sorted(Comparator.comparing(TagDTO::getSort)).collect(Collectors.toList());
                                    sku.setTagList(tagDTOList);
                                }else{
                                    List<TagDTO> tagList = new ArrayList<>();
                                    tagList.add(tagDTO);
                                    sku.setTagList(tagList);
                                }
                            }
                            //委托厂家修改
                            if(StringUtils.isNotBlank(sku.getEntrustedManufacturer())){
                                sku.setManufacturer(ProductMangeUtils.getManufacturer(sku.getMarketAuthor(), sku.getManufacturer(), sku.getEntrustedManufacturer()));
                            }
                        }
                    });
                }
            }
        voucherCenterDataList.setPages(pages.intValue());
        voucherCenterDataList.setRequestUrl(url);
        List<MyVoucherDTO> vucherDTOList = new ArrayList<MyVoucherDTO>();
        if(voucherSkuSearchDto.getActivityType()==0){
            List voucherTemplateIdList = new ArrayList();
            voucherTemplateIdList.add(voucherSkuSearchDto.getVoucherTemplateId());
            //ResultDTO<List<VoucherCenterDto>> result = promotionVoucherApi.getVoucherListByTemplateIdList(voucherSkuSearchDto.getMerchantId(), voucherTemplateIdList);//获取优惠券模板信息
//            ResultDTO<MyVoucherPageDTO> voucherPageResult = promotionVoucherApi.getMerchantAllVoucher(null, voucherSkuSearchDto.getMerchantId(), VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState());//查询已领取的券
//            if(null == voucherPageResult || (ErrorCodeEum.SUCCESS.getErrorCode() == voucherPageResult.getErrorCode() && null == voucherPageResult.getData())){
//                LOGGER.error("/voucher/centre/findVoucherSku.htm--getMerchantAllVoucher error");
//                return new ModelAndView("/error/404.ftl");
//            }
//            if(ErrorCodeEum.SUCCESS.getErrorCode() != voucherPageResult.getErrorCode()){
//                LOGGER.error("/voucher/centre/findVoucherSku.htm--getMerchantAllVoucher error:{}", voucherPageResult.getErrorMsg());
//                return new ModelAndView("/error/404.ftl");
//            }
            //查询已领取的券
            MerchantRequestDTO merchantRequestDTO = new MerchantRequestDTO();
            merchantRequestDTO.setMerchantId(voucherSkuSearchDto.getMerchantId());
            PromoResp<MyVoucherPageDTO> voucherResp = shopCouponForShopcartQueryService.getAllMerchantVoucher(merchantRequestDTO, VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState(), false, false);
            if(!voucherResp.isSuccess()){
                LOGGER.error("/voucher/centre/findVoucherSku.htm--getMerchantAllVoucher error:{}", voucherResp.getMsg());
                return new ModelAndView("/error/404.ftl");
            }

            //获取我的优惠券中已领取的优惠券列表
            List<MyVoucherDTO> myVoucherDTOList =  voucherResp.getData().getVoucherDTOPageInfo().getList();
            if(CollectionUtil.isNotEmpty(myVoucherDTOList)) {
                List<Long> finalTemplateIdList = voucherTemplateIdList;
                myVoucherDTOList = myVoucherDTOList.stream().filter(myVoucherDTO -> {
                    //过滤指定templId的
                    if (!finalTemplateIdList.contains(myVoucherDTO.getVoucherTemplateId())) {
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
            }

            if(CollectionUtil.isNotEmpty(myVoucherDTOList)){
                vucherDTOList.add(myVoucherDTOList.get(0));
            }
        }
        if(voucherCenterDataList != null){
            List<ProductPcDto> productList = voucherCenterDataList.getSkuList();
            handleProductFob(productList,merchant);
            /*if (null != merchant && null != merchant.getMerchantId()){
                productList = filterPinTuan(merchant.getMerchantId(), productList);
            }*/
            voucherCenterDataList.setSkuList(productList);
        }
        if (CollectionUtils.isNotEmpty(vucherDTOList)) {
            vucherDTOList.stream().forEach(obj -> {
                if (Objects.equals(obj.getVoucherType(), 8) && Objects.equals(obj.getVoucherState(), 1)) {
                    obj.setMoneyInVoucher(obj.getDiscount());
                }
            });
        }

        ApiRPCResult<CouponBaseDto> apiRPCResult;
        if (CollectionUtils.isEmpty(vucherDTOList)) {
            apiRPCResult = couponApi.getCouponTemplateBaseDto(voucherSkuSearchDto.getVoucherTemplateId());
            if (apiRPCResult.isSuccess() && Objects.nonNull(apiRPCResult.getData())) {
                CouponBaseDto couponBaseDto = apiRPCResult.getData();
                MyVoucherDTO dto = new MyVoucherDTO();
                //未领取优惠券 没有id
                dto.setVoucherId(couponBaseDto.getId());
                dto.setVoucherTemplateId(couponBaseDto.getTemplateId());
                dto.setVoucherTitle(couponBaseDto.getVoucherTitle());
                dto.setState(couponBaseDto.getState());
                dto.setVoucherType(couponBaseDto.getVoucherType());
                dto.setVoucherState(couponBaseDto.getVoucherState());
                dto.setValidDate(couponBaseDto.getValidDate());
                dto.setExpireDate(couponBaseDto.getExpireDate());
                dto.setMoneyInVoucher(couponBaseDto.getMoneyInVoucher());
                dto.setMinMoneyToEnable(couponBaseDto.getMinMoneyToEnable());
                dto.setVoucherType(couponBaseDto.getVoucherType());
                dto.setVoucherTypeDesc(couponBaseDto.getVoucherTypeDesc());
                dto.setMaxMoneyInVoucher(couponBaseDto.getMaxMoneyInVoucher());
                dto.setAssignProductIds(couponBaseDto.getAssignProductIds());
                dto.setVoucherInstructions(couponBaseDto.getVoucherInstructions());
                dto.setShopCode(couponBaseDto.getShopCode());
                dto.setShopName(couponBaseDto.getShopName());
                dto.setDiscount(couponBaseDto.getDiscount());
                vucherDTOList.add(dto);
            } else {
                LOGGER.error("couponApi.getCouponTemplateBaseDto失败：查询优惠券失败 voucherSkuSearchDto:{}", JSON.toJSONString(voucherSkuSearchDto));
            }
        }

        model.put("voucherList", vucherDTOList);
        model.put("source", source);
        model.put("activityType", voucherSkuSearchDto.getActivityType());
        //用户数据
        model.put("merchant", merchant);
        model.put("merchantId", merchant.getId());
        model.put("categoryList", categoryList);
        model.put("voucherCenterDataList", voucherCenterDataList);
        model.put("isDesignateShop", NumberUtils.INTEGER_ZERO);
        return new ModelAndView("/voucher/sku_search_coupon_centre.ftl",model);
    }

    /**
     * 获取店铺列表 - 商品信息
     * @param shopPropertyCode
     * @param voucherTemplateId
     * @param offset
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/findVoucherShopV2.htm")
    public ModelAndView findVoucherShopV2(HttpServletRequest request,
                                        @RequestParam(name = "voucherTemplateId") Long voucherTemplateId,
                                        @RequestParam(name = "categoryId", required = false) Long categoryId,
                                        @RequestParam(name = "shopPropertyCode", required = false) String shopPropertyCode,
                                        @RequestParam(name = "offset", required = false) Integer offset,
                                        @RequestParam(name = "pageSize", required = false) Integer pageSize) {


        offset = offset == null ? 1 : offset;
        pageSize = pageSize == null ? 20 : pageSize;

        //校验页参数
        if (pageSize >= 50) {
            LOGGER.error("查询每页信息大于50。offset:{}, pageSize:{}, voucherTemplateId:{}", offset, pageSize, voucherTemplateId);
            return new ModelAndView("/error/500.ftl");
        }
        try {
            //获取登录人信息
            MerchantBussinessDto merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                LOGGER.error("/voucher/centre/findVoucherShop.htm, 用户未登录, voucherTemplateId:{}", voucherTemplateId);
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            //用户id
            Long merchantId = merchant.getId();
            // 分页要用，将url传到前台
            String url = getRequestUrl(request);

            //初始化返回结果 - 优雅返回
            Map<String, Object> model = new HashMap<>();
            model.put("activityType", NumberUtils.INTEGER_ZERO);
            model.put("merchantId", merchantId);
            model.put("voucherTemplateId", voucherTemplateId);
            model.put("voucherCenterDataList", new VoucherSkuSearchPcData());
            model.put("merchant", merchant);
            model.put("categoryList", Lists.newArrayList());
            model.put("voucherList", Lists.newArrayList());
            model.put("isDesignateShop", NumberUtils.INTEGER_ONE);
            // 根据优惠券模板ID，查询优惠券
            ApiRPCResult<CouponBaseDto> couponApiCouponTemplateBaseDto = couponApi.getCouponTemplateBaseDto(voucherTemplateId);

            if (!couponApiCouponTemplateBaseDto.isSuccess()) {
                LOGGER.error("跨店券凑单页-店铺商品列表失败：查询优惠券失败。merchantId:{}, voucherTemplateId:{}", merchantId, voucherTemplateId);
                return new ModelAndView("/error/500.ftl");
            }

            CouponBaseDto couponBaseDto = couponApiCouponTemplateBaseDto.getData();

            // 验证优惠券类型
            if (couponBaseDto.getVoucherType() != com.xyy.ms.promotion.business.common.constants.VoucherEnum.VoucherTypeEnum.CROSS_PLATFORM.getId()) {
                LOGGER.error("查询跨店券-凑单页-店铺列表的tab类型和shopCodeList失败：优惠券类型错误。merchantId:{}, voucherTemplateId:{}, voucherType:{}",
                merchantId, voucherTemplateId, couponBaseDto.getVoucherType());
                return new ModelAndView("/error/500.ftl");
            }
            List<String> shopCodes = couponBaseDto.getAssignShopCodes();

            if (CollectionUtils.isEmpty(shopCodes)) {
                LOGGER.error("优惠券店铺信息查询失败 merchantId:{}, voucherTemplateId:{}", merchantId, voucherTemplateId);
                return new ModelAndView("/error/500.ftl");
            }
            EcSearchPagingListCsuBasesQueryParam ecSearchPagingListCsuBasesQueryParam = EcSearchPagingListCsuBasesQueryParam.builder()
                    .merchantId(merchantId)
                    .shopCodes(shopCodes)
                    .firstCategoryId(Objects.isNull(categoryId) ? null : categoryId)
                    .pageNum(offset)
                    .pageSize(pageSize)
                    .build();
            //查询大搜接口获取商品信息
            IPage<SearchCsuBaseDTO>  searchCsuBaseDTOIPage = ecSearchServiceRpc.pagingListCsuBases(ecSearchPagingListCsuBasesQueryParam);

            if (Objects.isNull(searchCsuBaseDTOIPage)) {
                LOGGER.error("查询店铺对应的大搜接口失败 voucherTemplateId:{}, param:{}, resp:{}", voucherTemplateId, JSON.toJSONString(ecSearchPagingListCsuBasesQueryParam),JSON.toJSONString(searchCsuBaseDTOIPage));
                return new ModelAndView("/voucher/sku_search_coupon_centre.ftl",model);
            }
            if (CollectionUtils.isEmpty(searchCsuBaseDTOIPage.getRecordList())) {
                LOGGER.info("查询店铺对应的大搜接口无数据 voucherTemplateId:{}, param:{}, resp:{}", voucherTemplateId, JSON.toJSONString(ecSearchPagingListCsuBasesQueryParam),JSON.toJSONString(searchCsuBaseDTOIPage));
            }
            //商品ID集合
            List<Long> csuIdList = searchCsuBaseDTOIPage.getRecordList().stream().map(SearchCsuBaseDTO::getCsuId).collect(Collectors.toList());

            List<List<Long>> csuPartitionList = Lists.partition(csuIdList, 200);
            List<ProductDTO> productDTOList = new ArrayList<>();
            for (List<Long> skuList : csuPartitionList) {
                ProductConditionDTO param = new ProductConditionDTO();
                param.setSkuIdList(skuList);
                param.setMerchantId(merchantId);
                //根据商品ID集合查询商品信息
                ApiRPCResult<List<ProductDTO>> productDTOResult = prodcutApi.findProductInfoBySkuIdListV2(skuList, null, merchantId);
                if (productDTOResult.isFail()) {
                    LOGGER.error("查询商品信息失败 merchantId:{}, voucherTemplateId:{}, csuIdList:{} resp:{}", merchantId, voucherTemplateId, JSON.toJSONString(skuList), JSON.toJSONString(productDTOResult));
                    continue;
                }
                if (CollectionUtils.isEmpty(productDTOResult.getData())) {
                    continue;
                }
                productDTOList.addAll(productDTOResult.getData());
            }

            List<ProductPcDto> skuList = new ArrayList<>();
            for (ProductDTO productDTO : productDTOList) {
                ProductPcDto productPcDto = new ProductPcDto();
                BeanUtils.copyProperties(productDTO, productPcDto);
                //受托生产厂家
                if(StringUtils.isNotBlank(productDTO.getEntrustedManufacturer())){
                    productPcDto.setManufacturer(ProductMangeUtils.getManufacturer(productDTO.getMarketAuthor(),productDTO.getManufacturer(),productDTO.getEntrustedManufacturer()));
                }
                skuList.add(productPcDto);
            }
            VoucherSkuSearchPcData voucherSkuSearchPcData = new VoucherSkuSearchPcData();
            voucherSkuSearchPcData.setPageNum((int) searchCsuBaseDTOIPage.getPageNo());
            voucherSkuSearchPcData.setPageSize((int) searchCsuBaseDTOIPage.getPageSize());
            voucherSkuSearchPcData.setCount(searchCsuBaseDTOIPage.getTotalCount());
            voucherSkuSearchPcData.setPages((int) (searchCsuBaseDTOIPage.getTotalCount() % searchCsuBaseDTOIPage.getPageSize() == 0 ? (searchCsuBaseDTOIPage.getTotalCount() / searchCsuBaseDTOIPage.getPageSize()) : (searchCsuBaseDTOIPage.getTotalCount() / searchCsuBaseDTOIPage.getPageSize() + 1)));
            voucherSkuSearchPcData.setRequestUrl(url);
            voucherSkuSearchPcData.setSkuList(skuList);
            voucherSkuSearchPcData.setCategoryId(categoryId);
            model.put("voucherCenterDataList", voucherSkuSearchPcData);

            EcSearchAggregateCsuFirstCategoryIdsQueryParam ecSearchParam = EcSearchAggregateCsuFirstCategoryIdsQueryParam.builder()
                    .merchantId(merchantId)
                    .shopCodes(shopCodes)
                    .build();

            //查询大搜接口获取一级分类信息
            List<Long> categoryIdList = ecSearchServiceRpc.aggregateCsuFirstCategoryIds(ecSearchParam);

            List<VoucherCategoryDto> voucherCategoryDtoList = new ArrayList<>();

            if (CollectionUtils.isEmpty(categoryIdList)) {
                LOGGER.info("查询店铺对应的商品一级分类信息为空 voucherTemplateId:{}, req:{},resp:{}", voucherTemplateId, JSON.toJSONString(ecSearchParam), JSON.toJSONString(categoryIdList));
            } else {
                //分类ID查询分类
                List<CategoryVo> categoryVoList = categoryBusinessApi.getCategoryByIds(categoryIdList);
                //如果一级分类只有一个 则只显示全部
                if (CollectionUtils.isNotEmpty(categoryVoList) && categoryVoList.size() > 1) {
                    for (CategoryVo categoryVo : categoryVoList) {
                        VoucherCategoryDto voucherCategoryDto = new VoucherCategoryDto();
                        voucherCategoryDto.setId(categoryVo.getId());
                        voucherCategoryDto.setName(categoryVo.getName());
                        voucherCategoryDtoList.add(voucherCategoryDto);
                    }
                }
            }
            model.put("categoryList", voucherCategoryDtoList);

            //查询已领取的券
            MerchantRequestDTO merchantRequestDTO = new MerchantRequestDTO();
            merchantRequestDTO.setMerchantId(merchantId);
            PromoResp<MyVoucherPageDTO> voucherResp = shopCouponForShopcartQueryService.getAllMerchantVoucher(merchantRequestDTO, VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState(), false, false);
            if (!voucherResp.isSuccess()) {
                LOGGER.error("查询用户所有优惠券失败 voucherTemplateId:{}, req:{}, resp:{}", voucherTemplateId, JSON.toJSONString(merchantRequestDTO), JSON.toJSONString(voucherResp));
                return new ModelAndView("/voucher/sku_search_coupon_centre.ftl",model);
            }

            //获取我的优惠券中已领取的优惠券列表
            List<MyVoucherDTO> myVoucherDTOList = voucherResp.getData().getVoucherDTOPageInfo().getList();
            MyVoucherDTO myVoucherDTO = null;
            if (CollectionUtil.isNotEmpty(myVoucherDTOList)) {
                // 过滤指定的 voucherTemplateId
                Optional<MyVoucherDTO> optional = myVoucherDTOList.stream().filter(v -> {
                    return voucherTemplateId.equals(v.getVoucherTemplateId());
                }).findFirst();
                if (optional.isPresent()) {
                    myVoucherDTO = optional.get();
                }
            }
            List<MyVoucherDTO> voucherDTOList = new ArrayList<MyVoucherDTO>();
            if(Objects.nonNull(myVoucherDTO)){
                voucherDTOList.add(myVoucherDTO);
            }
            if (CollectionUtils.isNotEmpty(voucherDTOList)) {
                voucherDTOList.stream().forEach(obj -> {
                    if (Objects.equals(obj.getVoucherType(), 8) && Objects.equals(obj.getVoucherState(), 1)) {
                        obj.setMoneyInVoucher(obj.getDiscount());
                    }
                });
            }

            model.put("voucherList", voucherDTOList);

            return new ModelAndView("/voucher/sku_search_coupon_centre.ftl",model);
        } catch (Exception e) {
            LOGGER.error("voucher shopList 异常：", e);
            return new ModelAndView("/error/500.ftl");
        }
    }


    private List<ProductPcDto> filterPinTuan(Long merchantId, List<ProductPcDto> skuList) {
        ActInfoQueryDTO actInfoQueryDTO = ActInfoQueryDTO.builder()
                .cid(merchantId)
                .actTypeSet(Sets.newHashSet(MarketingEnum.PING_TUAN))
                .csuIds(skuList.stream().map(ProductPcDto::getId).collect(Collectors.toList()))
                .status(Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()))
                .isNeedActSkuInfo(false)
                .isNeedActSkuLimit(false)
                .build();
        ApiRPCResult<Map<Long, List<ActInfoDTO>>> mapApiRpcResult = marketingQueryApi.queryActInfoUseCache(actInfoQueryDTO);
        if (null != mapApiRpcResult && mapApiRpcResult.isSuccess() && MapUtils.isNotEmpty(mapApiRpcResult.getData())){
            List<ProductPcDto> data = Lists.newArrayListWithExpectedSize(skuList.size());
            for (ProductPcDto item : skuList){
                if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(mapApiRpcResult.getData().get(item.getId()))){
                    data.add(item);
                }
            }
            skuList = data;
        }
        return skuList;
    }


    //通过登录资质处理商品价格
    public void handleProductFob(List<ProductPcDto> productDtoList,MerchantBussinessDto merchant){
        if(merchant != null){
            merchant = appVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
        }
        if(merchant == null || !merchant.getPriceDisplayFlag()){
            if(CollectionUtil.isNotEmpty(productDtoList)){
                for(ProductDto productDto : productDtoList){
                    productDto.setFob(0.0);
                    productDto.setUnitPrice(null);
                    productDto.setUnitPriceTag(null);
                    //商品标签
                    ProductActivityTag productActivityTagVO = new ProductActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    productDto.setActivityTag(productActivityTagVO);
                    //毛利
                    productDto.setGrossMargin("");
                    //建议零售价
                    productDto.setSuggestPrice(BigDecimal.ZERO);
                    //控销零售价
                    productDto.setUniformPrice(BigDecimal.ZERO);
                    //对比价
                    productDto.setRetailPrice(0.0);
                }
            }
        }
    }


    /**
     * 获取店铺列表
     *
     * @param shopPropertyCode
     * @param voucherTemplateId
     * @param offset
     * @param pageSize
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/findVoucherShop.htm", method = RequestMethod.GET)
    public ModelAndView findVoucherShop(HttpServletRequest request,
                                        @RequestParam(name = "voucherTemplateId") Long voucherTemplateId,
                                        @RequestParam(name = "shopPropertyCode", required = false) String shopPropertyCode,
                                        @RequestParam(name = "offset", required = false) Integer offset,
                                        @RequestParam(name = "pageSize", required = false) Integer pageSize) {

        Map<String, Object> model = new HashMap<>();

        try {
            MerchantBussinessDto merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                LOGGER.error("/voucher/centre/findVoucherShop.htm, 用户未登录, voucherTemplateId:{}", voucherTemplateId);
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            //用户id
            Long merchantId = merchant.getId();

            //查询已领取的券
            MerchantRequestDTO merchantRequestDTO = new MerchantRequestDTO();
            merchantRequestDTO.setMerchantId(merchantId);
            PromoResp<MyVoucherPageDTO> voucherResp = shopCouponForShopcartQueryService.getAllMerchantVoucher(merchantRequestDTO, VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState(), false, false);
            if (!voucherResp.isSuccess()) {
                LOGGER.error("/voucher/centre/findVoucherShop.htm--getMerchantAllVoucher error:{}", voucherResp.getMsg());
                return new ModelAndView("/error/500.ftl");
            }

            MyVoucherDTO myVoucherDTO = null;

            //获取我的优惠券中已领取的优惠券列表
            List<MyVoucherDTO> myVoucherDTOList = voucherResp.getData().getVoucherDTOPageInfo().getList();
            if (CollectionUtil.isNotEmpty(myVoucherDTOList)) {
                Optional<MyVoucherDTO> optional = myVoucherDTOList.stream().filter(v -> {
                    return voucherTemplateId.equals(v.getVoucherTemplateId()); // 过滤指定的 voucherTemplateId
                }).findFirst();
                myVoucherDTO = optional.orElse(null);
            }

            // 根据优惠券模板ID，查询优惠券
            ApiRPCResult<CouponBaseDto> couponApiCouponTemplateBaseDto = couponApi.getCouponTemplateBaseDto(voucherTemplateId);

            if (!couponApiCouponTemplateBaseDto.isSuccess()) {
                LOGGER.error("跨店券凑单页-店铺列表失败：查询优惠券失败。merchant_id:{}, voucherTemplateId:{}", merchantId, voucherTemplateId);
                return new ModelAndView("/error/500.ftl");
            }

            CouponBaseDto couponBaseDto = couponApiCouponTemplateBaseDto.getData();

            // 验证优惠券类型
            if (couponBaseDto.getVoucherType() != VoucherEnum.VoucherTypeEnum.CROSS_PLATFORM.getId()) {
                LOGGER.error("查询跨店券-凑单页-店铺列表的tab类型和shopCodeList失败：优惠券类型错误。merchant_id:{}, voucherTemplateId:{}, voucherType:{}",
                        merchantId, voucherTemplateId, couponBaseDto.getVoucherType());
                return new ModelAndView("/error/500.ftl");
            }

            List<String> shopCodes = null;

            // 如果是全品券
            if (couponBaseDto.getSkuRelationType() == VoucherEnum.RelationTypeEnum.ALL_PRODUCT.getId()) {
                // 查询全部店铺列表（自营/POP）
                BuyerParam param = new BuyerParam();
                param.setLogonType(BuyerLogonTypeEnum.LOGIN.getType());
                param.setProvinceCode(-1);
                param.setBuyerCode(String.valueOf(merchantId));

                // 根据用户ID做控销过滤（可售区域过滤）
                // 查询该用户的可售店铺code列表
                ApiRPCResult<List<String>> shopCodeListResult = shopQueryApi.queryShopByBuyerCode(param);

                if (!shopCodeListResult.isSuccess()) {
                    LOGGER.error("查询跨店券-凑单页-店铺列表的tab类型和shopCodeList失败：查询可售店铺编码失败。merchant_id:{}, voucherTemplateId:{}",
                            merchantId, voucherTemplateId);
                    return new ModelAndView("/error/500.ftl");
                }
                shopCodes = shopCodeListResult.getData();
            }
            // 如果是指定店铺的跨店券
            else if (couponBaseDto.getSkuRelationType() == VoucherEnum.RelationTypeEnum.DESIGNATED_PRODUCT_PARTAKE.getId()) {
                // codeList = 跨店券的关联店铺列表
                shopCodes = couponBaseDto.getAssignShopCodes();
            }

            boolean haveSelfShop = false;
            boolean havePopShop = false;
            List<String> availableShopCodeList = Lists.newArrayList(); // 该优惠券下用户可见的店铺列表

            // 设置页面 店铺类型Tab（自营、合作商家）和 shopCodeList（搜索用）
            {
                if (CollectionUtil.isNotEmpty(shopCodes)) {
                    // 根据用户ID做控销过滤（可售区域过滤）
                    List<ShopInfoDTO> shopList;
                    try {
                        shopList = this.getShopsByBuyerCode(merchantId, shopCodes);
                    } catch (Exception e) {
                        LOGGER.error("查询跨店券-凑单页-店铺列表的tab类型和shopCodeList失败：查询店铺列表失败。merchant_id:{}, voucherTemplateId:{}, shopCodeList:{}",
                                merchantId, voucherTemplateId, JSON.toJSONString(shopCodes), e);
                        return new ModelAndView("/error/500.ftl");
                    }

                    if (CollectionUtil.isNotEmpty(shopList)) {
                        haveSelfShop = shopList.stream().anyMatch(s -> ShopPropertyEnum.SELF.getCode().equals(s.getShopPropertyCode()));
                        havePopShop = shopList.stream().anyMatch(s -> ShopPropertyEnum.OTHER.getCode().equals(s.getShopPropertyCode()));
                        availableShopCodeList = shopList.stream().map(ShopInfoDTO::getShopCode).collect(Collectors.toCollection(ArrayList::new));
                    }
                }

                // 优先显示自营的，如果自营的没有，则显示POP的，如果都没有，仍用self参数来查询
                if (StringUtil.isEmpty(shopPropertyCode)) {
                    shopPropertyCode = haveSelfShop ? "self" : havePopShop ? "pop" : "self";
                }
            }

            offset = offset == null ? 1 : offset;
            pageSize = pageSize == null ? 20 : pageSize;

            // 根据可售店铺列表调用 shopService 的接口，查询店铺info列表，并拼装其他信息返回。
            ShopStatisticInfoQueryParam shopStatisticInfoQueryParam =
                    ShopStatisticInfoQueryParam.builder()
                            .voucherTemplateId(voucherTemplateId)
                            .shopPropertyCode(shopPropertyCode)
                            .sort("1")
                            .merchantId(merchantId)
                            .pageNum(offset)
                            .pageSize(pageSize)
                            .shopCodes(shopCodes)
                            .build();

            LOGGER.info("voucher shopList 入参: {}", JSONObject.toJSONString(shopStatisticInfoQueryParam));

            //获取分页店铺列表
            PageInfo<ShopInfoSupperVO> shopInfoSupperVOPageInfo = new PageInfo<>();
            if(Objects.equals(voucherShopListUseSimpleSkuFlag, 1)){
                shopInfoSupperVOPageInfo = shopService.simplePagingShopList(shopStatisticInfoQueryParam);
            } else {
                shopInfoSupperVOPageInfo = shopService.pagingShopList(shopStatisticInfoQueryParam);
            }

            LOGGER.info("voucher shopList 返回: {}", JSONObject.toJSONString(shopInfoSupperVOPageInfo));

            model.put("myVoucher", myVoucherDTO);
            model.put("merchantId", merchantId);
            model.put("voucherTemplateId", voucherTemplateId);
            model.put("haveSelfShop", haveSelfShop);
            model.put("havePopShop", havePopShop);
            model.put("shopCodeList", String.join(",", availableShopCodeList));

            List<ShopInfoSupperVO> shopInfoVOS = shopInfoSupperVOPageInfo.getList();

            if (shopInfoVOS != null) {
                shopInfoVOS.forEach(x -> {
                    if (StringUtil.isEmpty(x.getOrgId()) && CollectionUtil.isNotEmpty(x.getProductInfo()))
                        x.setOrgId(x.getProductInfo().get(0).getOrgId());
                });
            }

            Page<ShopInfoSupperVO> pageInfo = new Page<>();
            pageInfo.setOffset(offset);
            pageInfo.setLimit(pageSize);
            pageInfo.setRows(shopInfoVOS);
            pageInfo.setPageCount(shopInfoSupperVOPageInfo.getPages());
            pageInfo.setTotal(shopInfoSupperVOPageInfo.getTotal());
            pageInfo.setRequestUrl(getRequestUrl(request));

            model.put("pageInfo", pageInfo);
            model.put("merchant", merchant);

            LOGGER.info("voucher shopList 给前端返回: {}", JSONObject.toJSONString(model));

            return new ModelAndView("/voucher/pin_coupon_centre.ftl", model);

        } catch (Exception e) {
            LOGGER.error("voucher shopList 异常：", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    private List<ShopInfoDTO> getShopsByBuyerCode(Long merchantId, List<String> shopCodes) {
        if (CollectionUtils.isEmpty(shopCodes)) {
            return Lists.newArrayList();
        }
        List<ShopInfoDTO> result = Lists.newArrayListWithExpectedSize(shopCodes.size());
        List<List<String>> shopCodesLists = Lists.partition(shopCodes, 200);
        for (List<String> shopCodesList : shopCodesLists) {
            // 根据用户ID做控销过滤（可售区域过滤）
            ApiRPCResult<List<ShopInfoDTO>> shopListResult = shopQueryApi.getShopsByBuyerCode(String.valueOf(merchantId), shopCodesList);
            if (!shopListResult.isSuccess()) {
                throw new RuntimeException("查询店铺可见性异常，merchantId：" + merchantId + "，msg：" + shopListResult.getMsg() + "，errorMsg：" + shopListResult.getErrMsg());
            }
            if (CollectionUtils.isNotEmpty(shopListResult.getData())) {
                result.addAll(shopListResult.getData());
            }
        }
        return result;
    }

    private Map<String, ShopInfoDTO> getEcShopInfoByShopCode(Set<String> shopCodeSet){
        if (CollectionUtils.isEmpty(shopCodeSet)) {
            return Maps.newHashMap();
        }
        ApiRPCResult<List<ShopInfoDTO>> listApiRPCResult = shopQueryApi.queryShopByShopCodes(shopCodeSet);
        if (!listApiRPCResult.isSuccess()) {
            LOGGER.error("【优惠券凑单页】查询店铺信息失败getEcShopInfoByShopCode,shopCodeSet:{},msg:{}",
                    shopCodeSet, listApiRPCResult.getMsg());
            return Maps.newHashMap();
        }
        List<ShopInfoDTO> shopInfoDTOS = listApiRPCResult.getData();
        if (CollectionUtils.isEmpty(shopInfoDTOS)) {
            return Maps.newHashMap();
        }
        return  shopInfoDTOS.stream().collect(Collectors.toMap(item -> item.getShopCode(),
                item -> item, (o1, o2) -> o1));
    }

}
