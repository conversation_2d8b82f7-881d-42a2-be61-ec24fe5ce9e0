package com.xyy.ec.pc.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Title:
 * @Description:
 * @date 2018/8/25 17:56
 */
@Component
public class Config {

    /** PC图片地址 */
    @Value("${config.product_image_path_url}")
    private String productImagePathUrl;

    /** 访问量统计：1.不统计，2.统计 */
    @Value("${config.home_statistics_flag}")
    private String homeStatisticsFlag;

    /** 跟路径配置*/
    @Value("${config.base_path_url}")
    private String bathPathUrl;

    @Value("${config.upload_path_url}")
    private String uploadPathUrl;

    @Value("${piwik_url}")
    private String piwikUrl;

    @Value("${chain.shop.url}")
    private String chainShopUrl;

    /** 系统编码 **/
    @Value("${systemCode}")
    private String systemCode;

    /** 通行证URL **/
    @Value("${passport.url}")
    private String passportUrl;

    /** 验证票据 **/
    private String ticketUrl = "/api/user/validateTicket";

    public String getPiwikUrl() {
        return piwikUrl;
    }

    public void setPiwikUrl(String piwikUrl) {
        this.piwikUrl = piwikUrl;
    }

    public String getChainShopUrl() {
        return chainShopUrl;
    }

    public void setChainShopUrl(String chainShopUrl) {
        this.chainShopUrl = chainShopUrl;
    }

    public String getProductImagePathUrl() {
        return productImagePathUrl;
    }

    public void setProductImagePathUrl(String productImagePathUrl) {
        this.productImagePathUrl = productImagePathUrl;
    }

    public String getHomeStatisticsFlag() {
        return homeStatisticsFlag;
    }

    public void setHomeStatisticsFlag(String homeStatisticsFlag) {
        this.homeStatisticsFlag = homeStatisticsFlag;
    }

    public String getBathPathUrl() {
        return bathPathUrl;
    }

    public void setBathPathUrl(String bathPathUrl) {
        this.bathPathUrl = bathPathUrl;
    }

    public String getUploadPathUrl() {
        return uploadPathUrl;
    }

    public void setUploadPathUrl(String uploadPathUrl) {
        this.uploadPathUrl = uploadPathUrl;
    }

    public String getSystemCode(){
        return systemCode;
    }

    public String getPassportUrl(){
        return passportUrl;
    }

    public String getTicketUrl(){
        return ticketUrl;
    }
}
