package com.xyy.ec.pc.newfront.service.cms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.VoucherForCmsApi;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponBaseDto;
import com.xyy.ec.marketing.insight.api.InsightChosenCustomerAdminApi;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupBaseInfoDTO;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.cms.dto.CmsListProductDto;
import com.xyy.ec.pc.cms.dto.CmsRequestDTO;
import com.xyy.ec.pc.cms.enums.LayoutComponentEnum;
import com.xyy.ec.pc.cms.helpers.CmsListProductVOHelper;
import com.xyy.ec.pc.cms.helpers.CmsRequestHelper;
import com.xyy.ec.pc.cms.param.CmsExpectProductQueryParam;
import com.xyy.ec.pc.cms.param.CmsProductQueryParam;
import com.xyy.ec.pc.cms.param.CmsRequestParam;
import com.xyy.ec.pc.cms.service.CmsSkuService;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.helper.ProductDataFilterHelper;
import com.xyy.ec.pc.newfront.dto.CouponRespVO;
import com.xyy.ec.pc.newfront.dto.MerchantRespVO;
import com.xyy.ec.pc.newfront.service.ProductGroupsService;
import com.xyy.ec.pc.newfront.service.cms.service.CustomizeCmsResponseService;
import com.xyy.ec.pc.newfront.vo.*;
import com.xyy.ec.pc.rpc.ProductServiceRpc;
import com.xyy.ec.pc.util.ProductMangeUtils;
import com.xyy.ec.pc.util.RandomUtil;
import com.xyy.ec.product.business.dto.common.CommonProductProperty;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
@RequiredArgsConstructor
@Service
@Slf4j
public class CustomizeCmsResponseServiceImpl implements CustomizeCmsResponseService {

    @ApolloJsonValue("${CustomizeCmsResponseService.queryShopList:[]}")
    private List<ShotInfoVO> queryShopList;
    @ApolloJsonValue("${CustomizeCmsResponseService.selectUser:{}}")
    private MerchantRespVO selectUser;

    private final CmsSkuService cmsSkuService;
    private final CmsAppProperties cmsAppProperties;
    private final ProductServiceRpc productServiceRpc;
    private final AppProperties appProperties;
    private final ProductGroupsService productGroupsService;

    @Reference(version = "1.0.0")
    private InsightChosenCustomerAdminApi insightChosenCustomerAdminApi;

    @Reference(version = "1.0.0")
    private VoucherForCmsApi voucherForCmsApi;

    @Override
    public ProductGroupsVO listExpectProducts(Object[] args) {
        Integer terminalType = (Integer) args[0];
        CmsRequestParam cmsRequestParam = (CmsRequestParam) args[1];
        HttpServletRequest request = (HttpServletRequest) args[2];
        CmsRequestDTO cmsRequestDTO = CmsRequestHelper.createDTO(cmsRequestParam);
        if (cmsRequestDTO == null) {
            cmsRequestDTO = new CmsRequestDTO();
        }
        Integer expectedProductNum = cmsRequestDTO.getExpectedProductNum();
        String exhibitionIdStr = cmsRequestDTO.getExhibitionIdStr();
        if (Objects.isNull(expectedProductNum) || expectedProductNum <= 0 || StringUtils.isEmpty(exhibitionIdStr)) {
            return null;
        }
        String realBranchCode = "XS000000";
        CmsExpectProductQueryParam cmsExpectProductQueryParam = CmsExpectProductQueryParam.builder().exhibitionId(exhibitionIdStr).branchCode(realBranchCode).terminalType(terminalType).expectNum(expectedProductNum).build();

        List<ListProduct> products = cmsSkuService.listExpectProducts(cmsExpectProductQueryParam);
        // 处方药商品默认图处理
        if (log.isDebugEnabled()) {
            log.debug("【处方药商品默认图处理】，原商品信息：{}", JSONArray.toJSONString(products));
        }
        if ((Objects.isNull(cmsRequestParam.getModuleSourceType()) || Integer.valueOf(cmsRequestParam.getModuleSourceType()).equals(LayoutComponentEnum.PC_INDEX_PRODUCT_FLOOR.getModuleSourceType())) && BooleanUtils.isTrue(cmsAppProperties.getIsOpenIndexProductDefaultImageFeature())) {
            String defaultImageUrl = cmsAppProperties.getProductDefaultImageUrl();
            products.stream().filter(item -> Objects.equals(item.getDrugClassification(), 3)).forEach(item -> item.setImageUrl(defaultImageUrl));
            if (log.isDebugEnabled()) {
                log.debug("【处方药商品默认图处理】处理后商品信息：{}", JSONArray.toJSONString(products));
            }
        }
        List<CmsListProductVO> cmsListProductVOS = CmsListProductVOHelper.creates(products);
        cmsListProductVOS = cmsSkuService.fillListProductsShopInfo(cmsListProductVOS);
        cmsListProductVOS = cmsSkuService.fillListProductsTagInfo(null, cmsListProductVOS, true);
        //填充库存信息
        cmsListProductVOS = productServiceRpc.fillCmsActTotalSurplusQtyToAvailableQty(cmsListProductVOS);

        if ((BooleanUtils.isTrue(cmsRequestDTO.getIsFillActPt()) || BooleanUtils.isTrue(cmsRequestDTO.getIsFillActPgby())) && CollectionUtils.isNotEmpty(products)) {
            // 尝试填充拼团活动信息
            List<Long> skuIdList = products.stream().map(ListProduct::getId).distinct().collect(Collectors.toList());
            Set<Long> gaoMaoSkuIdSet = cmsListProductVOS.stream().filter(productDto -> null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross()) || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))).map(CmsListProductVO::getId).collect(Collectors.toSet());
            Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap = cmsSkuService.getMarketingActivityInfoBySkuIdList(skuIdList, Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()), null, Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
            cmsListProductVOS = cmsSkuService.fillListProductsMarketingActivityInfo(cmsListProductVOS, csuIdToGroupBuyingInfoMap);
        }

        // 获取商品折扣信息
        Set<Long> skuId = cmsListProductVOS.stream().map(CommonProductProperty::getId).collect(Collectors.toSet());
        HashMap<Long, String> priceMap = productGroupsService.satisfactoryInHandPrice(new MerchantBussinessDto(), Lists.newArrayList(skuId), request);

        //受托生产厂家处理
        if (CollectionUtils.isNotEmpty(cmsListProductVOS)) {
            for (CmsListProductVO productVO : cmsListProductVOS) {
                productVO.setPrice(priceMap.get(productVO.getId()));
                // 移除价格
                if (StrUtil.isNotEmpty(productVO.getControlTitle()) && productVO.getControlType() != 5) {
                    productGroupsService.handleProductVO(productVO);
                }
                if (StringUtils.isNotBlank(productVO.getEntrustedManufacturer())) {
                    productVO.setManufacturer(ProductMangeUtils.getManufacturer(productVO.getMarketAuthor(), productVO.getManufacturer(), productVO.getEntrustedManufacturer()));
                }
            }
        }
        ProductDataFilterHelper.fillBlankCms(cmsListProductVOS);
        return ProductGroupsVO.builder().productVOList(cmsListProductVOS).scmE(RandomUtil.nanoId(8)).build();
    }


    @Override
    public List<ShotInfoVO> queryShopList(Object[] args) {
        ShotQueryParam param = (ShotQueryParam) args[0];
        if (param == null || param.getPageSize() == null) {
            return queryShopList;
        }
        return queryShopList.subList(0, param.getPageSize());
    }

    @Override
    public MerchantRespVO selectUser() {
        return selectUser;
    }

    @Override
    public ProductGroupsVO listProducts(Object[] args) {
        ProductParam param = (ProductParam) args[2];
        HttpServletRequest request = (HttpServletRequest) args[3];
        String exhibitionId = param.getExhibitionIdStr();
        Boolean isAdmin = param.getIsAdmin();
        Integer terminalType = param.getTerminalType();
        Integer moduleSourceType = param.getModuleSourceType();
        Boolean isRecommend = param.getIsRecommend();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        Long merchantId = null;
        String realBranchCode = "XS000000";

        CmsProductQueryParam cmsProductQueryParam = CmsProductQueryParam.builder().isAdmin(isAdmin).merchantId(merchantId).exhibitionId(exhibitionId).branchCode(realBranchCode).moduleSourceType(moduleSourceType).isRecommend(isRecommend).terminalType(terminalType).build();
        CmsListProductDto cmsListProductDto = cmsSkuService.listProducts(cmsProductQueryParam, pageNum, pageSize);
        PageInfo<ListProduct> pageInfo = cmsListProductDto.getPageInfo();
        // 商品信息根据资质状态过滤

        //推荐商品或者控销商品符合下列条件之一，则为最后一页
        Boolean resultIsRecommend = cmsListProductDto.getIsRecommend();
        List<ListProduct> listProduct = pageInfo.getList();
        List<CmsListProductVO> cmsListProductVOS = CmsListProductVOHelper.creates(listProduct);
        if (CollectionUtils.isNotEmpty(cmsListProductVOS)) {
            // 获取拼团或批购包邮活动信息
            List<Long> csuIds = cmsListProductVOS.stream().filter(Objects::nonNull).map(CmsListProductVO::getId).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(csuIds)) {
                Set<Long> gaoMaoSkuIdSet = cmsListProductVOS.stream().filter(productDto -> null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross()) || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))).map(CmsListProductVO::getId).collect(Collectors.toSet());
                Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap = cmsSkuService.getMarketingActivityInfoBySkuIdList(csuIds, Lists.newArrayList(MarketingQueryStatusEnum.UN_START.getType(), MarketingQueryStatusEnum.STARTING.getType()), merchantId, Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
                // 填充拼团或批购包邮活动信息
                cmsListProductVOS = cmsSkuService.fillListProductsMarketingActivityInfo(cmsListProductVOS, csuIdToGroupBuyingInfoMap);
            }
            cmsListProductVOS = cmsSkuService.fillListProductsShopInfo(cmsListProductVOS);
            cmsListProductVOS = cmsSkuService.fillListProductsTagInfo(merchantId, cmsListProductVOS, true);
            //填充库存信息
            cmsListProductVOS = productServiceRpc.fillCmsActTotalSurplusQtyToAvailableQty(cmsListProductVOS);

            // 获取商品折扣信息
            Set<Long> skuId = cmsListProductVOS.stream().map(CommonProductProperty::getId).collect(Collectors.toSet());
            HashMap<Long, String> priceMap = productGroupsService.satisfactoryInHandPrice(new MerchantBussinessDto(), Lists.newArrayList(skuId), request);


            for (CmsListProductVO productVO : cmsListProductVOS) {
                productVO.setPrice(priceMap.get(productVO.getId()));
                // 移除价格
                if (StrUtil.isNotEmpty(productVO.getControlTitle()) && productVO.getControlType() != 5) {
                    productGroupsService.handleProductVO(productVO);
                }

                //受托生产厂家处理
                if (StringUtils.isNotBlank(productVO.getEntrustedManufacturer())) {
                    productVO.setManufacturer(ProductMangeUtils.getManufacturer(productVO.getMarketAuthor(), productVO.getManufacturer(), productVO.getEntrustedManufacturer()));
                }
            }
        }

        ProductDataFilterHelper.fillBlankCms(cmsListProductVOS);

        ProductGroupsVO groups = ProductGroupsVO.builder().pageNo(pageInfo.getPageNum()).pageSize(pageInfo.getPageSize()).totalPage(pageInfo.getPages()).totalCount(pageInfo.getTotal()).productVOList(cmsListProductVOS).recommend(resultIsRecommend).tip(cmsListProductDto.getTip()).scmE(StrUtil.isBlank(param.getScmE()) ? RandomUtil.nanoId(8) : param.getScmE()).build();

        if (BooleanUtils.isTrue(isAdmin)) {
            groups.setIsAdmin(isAdmin);
        }
        return groups;

    }


    @Override
    public List<CouponRespVO> previewCouponsByTemplateIds(Object[] args) {

        List<Long> couponIds = (List<Long>) args[0];
        Boolean isAdmin = true;
        Long merchantId = null;
        if (CollectionUtils.isEmpty(couponIds)) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR);
        }
        ApiRPCResult<List<CouponBaseDto>> apiRPCResult = voucherForCmsApi.listCouponByIds(merchantId, couponIds);
        if (!apiRPCResult.isSuccess()) {
            String message = MessageFormat.format("【cms】listCouponByIds，请求入参，isAdmin：{0}，merchantId：{1}，couponIds：{2}，msg:{3}", isAdmin, String.valueOf(merchantId), JSONObject.toJSONString(couponIds), apiRPCResult.getMsg());
            log.error("【cms】listCouponByIds，请求入参，isAdmin：{}，merchantId：{}，couponIds：{}，msg:{}", isAdmin, merchantId, JSONObject.toJSONString(couponIds), apiRPCResult.getMsg());
            throw new AppException(message, XyyJsonResultCodeEnum.CMS_COUPON_QUERY_ERROR, apiRPCResult.getMsg());
        }
        List<CouponBaseDto> couponBaseDtos = apiRPCResult.getData();
        if (CollectionUtils.isEmpty(couponBaseDtos)) {
            return null;
        }
        List<CmsCouponBaseVO> cmsCouponBaseVOS = creates(couponBaseDtos);
        if (BooleanUtils.isTrue(isAdmin)) {
            // 填充人群名称
            cmsCouponBaseVOS = this.fillCouponsCustomerGroupName(cmsCouponBaseVOS);
        }
        // List<CouponRespVO> couponRespVOs = new ArrayList<>();
        return cmsCouponBaseVOS.stream().map(cms -> {
            CouponRespVO vo = new CouponRespVO();
            BeanUtils.copyProperties(cms, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    private List<CmsCouponBaseVO> fillCouponsCustomerGroupName(List<CmsCouponBaseVO> cmsCouponBaseVOS) {
        if (CollectionUtils.isEmpty(cmsCouponBaseVOS)) {
            return Lists.newArrayList();
        }
        List<Long> customerGroupIds = cmsCouponBaseVOS.stream().filter(Objects::nonNull).map(CmsCouponBaseVO::getCustomerGroupId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerGroupIds)) {
            return cmsCouponBaseVOS;
        }
        ApiRPCResult<List<MarketCustomerGroupBaseInfoDTO>> apiRPCResult = insightChosenCustomerAdminApi.mgetChoseCustomerBaseInfo(customerGroupIds);
        if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
            return cmsCouponBaseVOS;
        }
        List<MarketCustomerGroupBaseInfoDTO> marketCustomerGroupBaseInfoDTOS = apiRPCResult.getData();
        if (CollectionUtils.isEmpty(marketCustomerGroupBaseInfoDTOS)) {
            return cmsCouponBaseVOS;
        }
        Map<Long, String> idToNameMap = marketCustomerGroupBaseInfoDTOS.stream().filter(item -> Objects.nonNull(item.getId()) && Objects.nonNull(item.getGroupName())).collect(Collectors.toMap(MarketCustomerGroupBaseInfoDTO::getId, MarketCustomerGroupBaseInfoDTO::getGroupName));
        if (MapUtils.isEmpty(idToNameMap)) {
            return cmsCouponBaseVOS;
        }
        Long customerGroupId;
        String customerGroupName;
        for (CmsCouponBaseVO cmsCouponBaseVO : cmsCouponBaseVOS) {
            customerGroupId = cmsCouponBaseVO.getCustomerGroupId();
            if (Objects.isNull(customerGroupId)) {
                continue;
            }
            customerGroupName = idToNameMap.get(customerGroupId);
            cmsCouponBaseVO.setCustomerGroupName(customerGroupName);
        }
        return cmsCouponBaseVOS;
    }

    public static List<CmsCouponBaseVO> creates(List<CouponBaseDto> couponBaseDtos) {
        if (CollectionUtils.isEmpty(couponBaseDtos)) {
            return Lists.newArrayList();
        }
        return couponBaseDtos.stream().map(CustomizeCmsResponseServiceImpl::create).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static CmsCouponBaseVO create(CouponBaseDto couponBaseDto) {
        if (Objects.isNull(couponBaseDto)) {
            return null;
        }
        CmsCouponBaseVO cmsCouponBaseVO = new CmsCouponBaseVO();
        BeanUtils.copyProperties(couponBaseDto, cmsCouponBaseVO);
        cmsCouponBaseVO.setCouponText(couponBaseDto.getVoucherText());
        return cmsCouponBaseVO;
    }


}
