.about-money > div {
    float: left;
    width: 50%;
    box-sizing: border-box;
}

.about-money > div p:first-of-type {
    color: #333333;
    font-size: 14px;
    font-weight: 500;
}

.about-money > div p:first-of-type span {
    color: #999999;
    font-size: 12px;
    font-weight: 400;

}

.about-money > div p:last-of-type {
    color: #333333;
    font-size: 24px;
    margin-top: 5px;
    font-weight: 500;
}

.about-money {
    overflow: hidden;
    padding: 20px 0 20px 36px;
}

.msg-tips {
    width: 980px;
}

.msg-tips .msg-con {
    padding: 11px 17px !important;
    color: #FF5B5B;
    font-size: 12px;
}


.main-right {
    width: 980px;
    background-color: #ffffff;
    position: relative;
}

.sui-form {
    margin: 20px 0 40px 0;
    padding-left: 20px;
}

.input-large {
    width: 300px !important;
    height: 26px !important;
}

.select {
    width: 310px !important;
    height: 32px !important;
}

.textarea {
    width: 667px;
    height: 100px;
}

.card-num {
    width: 65px;
    height: 26px !important;
}

.btn-center {
    text-align: center;
    margin-top: 50px;
}

.tips {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 250px;
    height: 80px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    z-index: 1000;
    color: #ffffff;
    line-height: 80px;
    text-align: center;
}


.modal-title {
    font-weight: 500 !important;
    font-size: 20px !important;
}

.modal-body {
    padding: 10px 10px 10px 20px !important;
    font-size: 14px !important;
    height: 50px !important;
}

.modal-footer {
    padding: 10px !important;
}

.sui-btn {
    margin-right: 10px !important;
}

.icon-pc-info-circle {
    color: #FAAD14;
    font-size: 20px;
    margin-right: 10px;
}


.noadd {
    position: relative;
    background-color: #f9f9f9;
}

.noadd .addphoto-btn {
    top: 5px;
    left: 5px;
    position: absolute !important;
    width: 100px !important;
    height: 100px !important;
    opacity: 0;
    z-index: 1000;

}

.noadd .default-photo {
    max-height: 100px !important;
    max-width: 100px !important;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

}

.noadd .del-btn {
    position: absolute;
    right: 0;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    text-align: center;
    background: rgba(102, 102, 102, 1);
    color: #ffffff;
    font-size: 16px;
    display: none;
}

.btn_style {
    opacity: 0.4;
}


.about-money > div p.red {
    color: #FF0000;
}

.controls{
    position: relative;
}

.controls input{
    width: 100%;
}

.refundReasonOption {
    width: 310px;
    max-height: 430px;
    overflow: auto;
    position: absolute;
    top: 25px;
    left: 3px;
    z-index: 1000;
    background: #FFFFFF;
    box-shadow: 0 3px 8px 1px rgba(0, 0, 0, 0.18);
    border-radius: 2px;
    padding: 15px 0;
    display: none;
    box-sizing: border-box;
}

.refundReasonOption .second li {
    padding-left: 35px;
}

.refundReasonOption ul li {
    line-height: 35px;
    padding: 0 15px;
    cursor: pointer;
}


/* 父元素选中状态 */
.refundReasonOption .reasonLi.selected {
    background: white;
    color: #4C91F8;
}

/* 当父元素选中时，子元素保持原样 */
.refundReasonOption .reasonLi.selected ul,
.refundReasonOption .reasonLi.selected ul li {
    background: white;
    color: #333;
}

.refundReasonOption .onlyOne:hover,
.refundReasonOption .childReasonLi:hover {
    background: #4C91F8;
    color: #fff;
}

.refundReasonOption ul li.selected {
    background: #4C91F8;
    color: #fff;
}
.afterSalesTypeOption {
    width: 310px;
    max-height: 430px;
    overflow: auto;
    position: absolute;
    top: 25px;
    left: 3px;
    background: #FFFFFF;
    box-shadow: 0 3px 8px 1px rgba(0, 0, 0, 0.18);
    border-radius: 2px;
    padding: 15px 0;
    display: none;
    box-sizing: border-box;
    z-index: 1001;
}

.afterSalesTypeOption .second li {
    padding-left: 35px;
}

.afterSalesTypeOption ul li {
    line-height: 35px;
    padding: 0 15px;
}

.afterSalesTypeOption .onlyOne:hover, .afterSalesTypeOption .childReasonLi:hover{
    background: #4C91F8;
    color: #fff;
}

.afterSalesTypeOption ul li.selected {
    background: #4C91F8;
    color: #fff;
}
.refundTips.required:before{
    content: '*';
    color: #FF2021;
}

.refundReason{
    cursor: pointer !important;
}
.afterSalesType{
    cursor: pointer !important;
}
.downTag{
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 15px;
    height: 10px;
    background: url("../../static/img/zhan.png") no-repeat;
    background-size: 100% 100%;
    z-index: 1000;
}