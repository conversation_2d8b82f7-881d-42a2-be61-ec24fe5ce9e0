package com.xyy.ec.pc.util;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * lk
 */
public class ZipDownloadUtils {
    private static final int BUFFER_SIZE = 5 * 1024;
    private static  Logger logger = LoggerFactory.getLogger(ZipDownloadUtils.class);

    /**
      *
      * @param inputFileName
      *            输入一个文件夹
      * @param zipFileName
      *            输出一个压缩文件夹，打包后文件名字
      * @throws Exception
      */
    public static void zip(String inputFileName, String zipFileName) throws Exception {
        zip(zipFileName, new File(inputFileName));
    }  
              
    private static void zip(String zipFileName, File inputFile) throws Exception {
        ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFileName), StandardCharsets.UTF_8);
        zip(out, inputFile, "");
        out.close();  
    }  
              
     private static void zip(ZipOutputStream out, File f, String base) throws Exception {
         byte[] buf = new byte[BUFFER_SIZE];
        if (f.isDirectory()) { // 判断是否为目录  
            File[] fl = f.listFiles();  
            out.putNextEntry(new ZipEntry(base + "/"));
            base = base.length() == 0 ? "" : base + "/";  
            for (int i = 0; i < fl.length; i++) {  
                zip(out, fl[i], base + fl[i].getName());  
            }  
        } else { // 压缩目录中的所有文件  
            out.putNextEntry(new ZipEntry(base));
            FileInputStream in = new FileInputStream(f);
            int b;
            while ((b = in.read(buf)) != -1) {
                out.write(buf, 0, b);
            }
            in.close();  
        }  
    }  
              
     /**
      * 下载文件
      *
      * @param file
      * @param response
      */
     public static void downloadFile(File file, HttpServletRequest request,HttpServletResponse response, boolean isDelete) {
        try {
            // 以流的形式下载文件。  
            BufferedInputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()));
            byte[] buffer = new byte[fis.available()];  
            fis.read(buffer);  
            fis.close();  
            // 清空response  
            response.reset();
            //需设置头信息
            response.setHeader("Set-Cookie", "fileDownload=true; path=/");
            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition",
                                        "attachment;filename=" + getFileName(request,file.getName()));
            toClient.write(buffer);  
            toClient.flush();  
            toClient.close();  
            if (isDelete) {
                String absolutePath = file.getAbsolutePath();
                absolutePath = absolutePath.substring(0, absolutePath.lastIndexOf("."));
                FileUtil fileUtil = new FileUtil();
                boolean b = fileUtil.deleteDirectory(absolutePath);
                if(b){
                    logger.info("删除文件夹:{}",absolutePath);
                }
                file.delete(); // 是否将生成的服务器端文件删除
                logger.info("删除文件:{}",file.getName());
            }
        } catch (IOException ex) {
            logger.error("下载文件出错了",ex);
        }  
    }

    /**
     * 区分ie 和其他浏览器的下载文件乱码问题
     * @param req
     * @param fileName
     * @return
     */
    public static String getFileName(HttpServletRequest req, String fileName){
        String userAgent = req.getHeader("user-agent");
        userAgent = userAgent ==null?"":userAgent.toLowerCase();
        String name = fileName;
        try {
          //针对IE或者以IE为内核的浏览器：
            if(userAgent.contains("msie") ||userAgent.contains("trident")){
                name = URLEncoder.encode(name, "UTF-8");
            }else{
                name = new String(name.getBytes(), "iso-8859-1");
            }
        } catch (Exception e) {
            logger.error("区分ie 和其他浏览器的下载文件乱码出错",e);
        }
        return name;
    }

}
