package com.xyy.ec.pc.newfront.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.merchant.bussiness.base.ResultMessage;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.account.LoginAccountApi;
import com.xyy.ec.merchant.bussiness.dto.account.LoginAccountDto;
import com.xyy.ec.merchant.bussiness.enums.ResultCodeEnum;
import com.xyy.ec.merchant.bussiness.dto.licence.LicenseValidDto;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.authentication.utils.BaiduMapUtils;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.PasswordVerifier;
import com.xyy.ec.pc.constants.RedisConstants;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.interceptor.helper.SpiderHelper;
import com.xyy.ec.pc.newfront.service.ValidateNewService;
import com.xyy.ec.pc.newfront.vo.CheckCodeParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.MobileValidateUtil;
import com.xyy.ec.pc.util.RandomUtil;
import com.xyy.framework.redis.autoconfigure.core.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class ValidateNewServiceImpl implements ValidateNewService {

    public static final String SUCCESS = "success";

    @Resource
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SpiderHelper spiderHelper;

    @Autowired
    private BaiduMapUtils baiduMapUtils;

    @Autowired
    private RedisClient redisClient;


    @Override
    public AjaxResult<Object> sendCrawlerCode(String mobile) {
        try {
            log.info("爬虫用户发送短信验证码mobile:{}",mobile);
            ApiRPCResult<Boolean> result =  merchantBussinessApi.sendCrawlerCode(mobile);
            log.info("爬虫用户发送短信验证码结果返回:{},mobile:{}", JSON.toJSONString(result),mobile);
            if (result != null && result.getCode()!= ApiResultCodeEum.SUCCESS.getCode()) {
                throw new AppException(result.getErrMsg(), XyyJsonResultCodeEnum.FAIL);
            }
            return AjaxResult.successResultNotData();
        } catch (Exception e) {
            log.error("爬虫用户发送短信验证码异常：", e);
            throw new AppException("系统异常", XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public AjaxResult<Object> checkCrawlerCode(CheckCodeParamVO codeParamVO) {
        log.info("爬虫用户短信验证码校验入参code:{}", codeParamVO.getCode());
        try {
            String result = redisClient.get(RedisConstants.CRAWLER_CODE + codeParamVO.getPhone());
            if (StringUtils.isEmpty(result)) {
                log.info("爬虫用户短信验证码校验已过期code:{},result:{},mobile:{}", codeParamVO.getCode(), result, codeParamVO.getPhone());
                throw new AppException("验证码已过期，或者不存在", XyyJsonResultCodeEnum.FAIL);
            }
            if (!codeParamVO.getCode().equals(result)) {
                log.info("爬虫用户短信验证码校验不匹配code:{},result:{},mobile:{}", codeParamVO.getCode(), result, codeParamVO.getPhone());
                throw new AppException("验证码校验不匹配,请重新输入", XyyJsonResultCodeEnum.FAIL);
            }

            if (spiderHelper.getNewSpiderInterceptionOpen()) {
                // 清除爬虫标记
                spiderHelper.removeSpider();
                // 记录验证记录到反爬系统
                spiderHelper.verifyRecord("sms");
            }
            String key = String.format("login:%s", codeParamVO.getPhone());

            if(!redisClient.exists(key)){
                throw new AppException("验证过期，请重新登录", XyyJsonResultCodeEnum.FAIL);
            }
            PasswordVerifier verifier = redisClient.get(key, PasswordVerifier.class);
            JwtPrincipal merchant = (JwtPrincipal) xyyIndentityValidator.login(verifier);

            ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.selectLoginAccountById(merchant.getAccountId());
            if(!apiRPCResult.isSuccess() || Objects.isNull(apiRPCResult.getData())){
                log.info("爬虫用户发送短信验证码未登录");
                throw new AppException("请先登录", XyyJsonResultCodeEnum.FAIL);
            }
            LoginAccountDto loginAccountDto = apiRPCResult.getData();


            // 清除异地登录标识
            merchant.setIsDifferentPlacesLogin(false);
            tokenService.refreshToken(merchant);
            // 记录ip地址省份到常登录省份
            List<String> oftenLoginProvinces = loginAccountDto.getOftenLoginProvinces();
            if (oftenLoginProvinces == null) {
                oftenLoginProvinces = new ArrayList<>();
            }
            String ipProvince = baiduMapUtils.getIpProvince();
            if (StrUtil.isNotBlank(ipProvince) && !oftenLoginProvinces.contains(ipProvince)) {
                oftenLoginProvinces.add(ipProvince);
            }
            loginAccountDto.setOftenLoginProvinces(oftenLoginProvinces.isEmpty() ? null : oftenLoginProvinces);
            loginAccountApi.updateCheckSwitchOrProvinceById(loginAccountDto);
            return AjaxResult.successResultNotData();

        } catch (Exception e) {
            log.error("爬虫用户短信验证码校验异常：", e);
            throw new AppException("系统异常", XyyJsonResultCodeEnum.FAIL);
        }
    }



    @Override
    public AjaxResult<LicenseValidDto> sendRegisterMsgCode(String mobileNumber, String code) {
        //手机号格式校验
        if (StringUtils.isEmpty(mobileNumber)
                || !MobileValidateUtil.isPass(mobileNumber)
                || mobileNumber.length() != 11) {
            return AjaxResult.errResult("手机号格式有误，请确认后重新填写！");
        }
        if (StringUtils.isEmpty(code)) {
            return AjaxResult.errResult("验证码不能为空");
        }

        try {
            String result = this.sendSmsMsg(code, mobileNumber);
            if (!SUCCESS.equals(result)) {
                return AjaxResult.errResult(result);
            }
            return AjaxResult.successResultNotResult("验证码已发送，请注意查收短信");
        } catch (Exception e) {
            log.error("短信验证码发送失败", e);
            return AjaxResult.errResult("短信验证码发送失败");
        }
    }

    @Override
    public AjaxResult<Object> checkCode(String mobileNumber,String code) {
        try {
            ResultMessage<?> resultMessage = merchantBussinessApi.checkVerificationCode(mobileNumber, code);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if(resultCode!= ResultCodeEnum.SUCCESS.getCode()){
                return AjaxResult.errResult(msg);
            }
            return AjaxResult.successResultNotData();
        } catch (Exception e) {
            log.error("短信验证码验证异常", e);
            return AjaxResult.errResult("短信验证码验证异常");
        }
    }

    @Override
    public AjaxResult<Object> getValitionCode(String mobileNumber) {
        //手机号格式校验
        if (StringUtils.isEmpty(mobileNumber)
                || !MobileValidateUtil.isPass(mobileNumber)
                ||  mobileNumber.length() != 11) {
            return AjaxResult.errResult("手机号格式有误，请确认后重新填写！");
        }
        String verificationCode = RandomUtil.getRandomCode(4);
        try {

            ResultMessage<?> resultMessage = merchantBussinessApi.sendVerificationCode(mobileNumber,1);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if(resultCode!=ResultCodeEnum.SUCCESS.getCode()){
                return AjaxResult.errResult(msg);
            }
            return AjaxResult.successResult(verificationCode);
        } catch (Exception e) {
            log.error("短信验证码发送失败", e);
            return AjaxResult.errResult("短信验证码发送失败");
        }
    }

    private String sendSmsMsg(String verificationCode, String mobileNumber) throws Exception {

        ResultMessage<?> resultMessage = merchantBussinessApi.pcRegisterSendMsmMsgCode(mobileNumber, verificationCode.toUpperCase());

        int resultCode = resultMessage.getCode();
        String msg = resultMessage.getMsg();
        if (resultCode != ResultCodeEnum.SUCCESS.getCode()) {
            return msg;
        }
        return SUCCESS;
    }

}
