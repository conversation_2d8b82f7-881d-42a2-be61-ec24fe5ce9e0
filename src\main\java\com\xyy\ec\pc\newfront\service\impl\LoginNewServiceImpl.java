package com.xyy.ec.pc.newfront.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.*;
import com.xyy.ec.merchant.bussiness.api.account.AccountMerchantWechatRelApi;
import com.xyy.ec.merchant.bussiness.api.account.LoginAccountApi;
import com.xyy.ec.merchant.bussiness.api.account.WechatLoginApi;
import com.xyy.ec.merchant.bussiness.api.admin.MerchantAdminBusinessApi;
import com.xyy.ec.merchant.bussiness.api.wxwork.WxWorkBusinessApi;
import com.xyy.ec.merchant.bussiness.base.ResultMessage;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantMdLogBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantPwdModifiLogBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.account.AccountMerchantWechatRelDto;
import com.xyy.ec.merchant.bussiness.dto.account.LoginAccountDto;
import com.xyy.ec.merchant.bussiness.dto.account.UpdateAccountPasswordVo;
import com.xyy.ec.merchant.bussiness.dto.account.WechatUserDto;
import com.xyy.ec.merchant.bussiness.enums.ResultCodeEnum;
import com.xyy.ec.merchant.bussiness.utils.Pwdverifiers;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.base.PasswordVerifier;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.controller.vo.AccountRegisterVO;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.exception.AuthenticationException;
import com.xyy.ec.pc.interceptor.helper.DifferentPlacesLoginHelper;
import com.xyy.ec.pc.interceptor.helper.ForceUpdatePasswordHelper;
import com.xyy.ec.pc.interceptor.helper.SpiderHelper;
import com.xyy.ec.pc.newfront.dto.ForgetPassLastRespVO;
import com.xyy.ec.pc.newfront.dto.LoginRespVO;
import com.xyy.ec.pc.newfront.dto.RegisterRespVO;
import com.xyy.ec.pc.newfront.dto.*;
import com.xyy.ec.pc.newfront.service.LoginNewService;
import com.xyy.ec.pc.newfront.vo.*;
import com.xyy.ec.pc.remote.LoginAgreementBussinessRemoteService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.rpc.MerchantServiceRpc;
import com.xyy.ec.pc.search.utils.SnowGroundUtil;
import com.xyy.ec.pc.service.AsyncMerchantInfoService;
import com.xyy.ec.pc.service.LoginAccountService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.MD5Util;
import com.xyy.ec.pc.util.MobileValidateUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.util.VerifyCodeUtils;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.framework.redis.autoconfigure.core.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class LoginNewServiceImpl implements LoginNewService {


    @Value("${switch.login.agreement:false}")
    private Boolean switchLoginAgreement;

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginNewServiceImpl.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private LoginAgreementBussinessRemoteService loginAgreementBussinessRemoteService;

    @Autowired
    private LoginAccountService loginAccountService;

    @Reference(version = "1.0.0")
    private MerchantCancelFreezeLogBusinessApi merchantCancelFreezeLogBusinessApi;

    @Autowired
    private SpiderHelper spiderHelper;

    @Autowired
    private SnowGroundUtil snowGroundUtil;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Autowired
    private AsyncMerchantInfoService asyncMerchantInfoService;

    @Reference(version = "1.0.0")
    private MerchantMdLogBusinessApi merchantMdLogBusinessApi;

    @Autowired
    private MerchantServiceRpc merchantServiceRpc;

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;


    @Reference(version = "1.0.0")
    private MerchantPwdModifiLogBussinessApi logBussinessApi;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private DifferentPlacesLoginHelper differentPlacesLoginHelper;

    @Reference(version = "1.0.0")
    private WechatLoginApi wechatLoginApi;

    @Reference(version = "1.0.0")
    private AccountMerchantWechatRelApi accountMerchantWechatRelApi;

    @Reference(version = "1.0.0")
    private WxWorkBusinessApi wxWorkBusinessApi;

    @Autowired
    private ForceUpdatePasswordHelper forceUpdatePasswordHelper;

    @Resource
    private RedisClient redisClient;

    @Reference(version = "1.0.0")
    private MerchantAdminBusinessApi merchantAdminBusinessApi;

    @Reference(version = "1.0.0")
    private UserWechatBindApi userWechatBindApi;

    @Override
    public AjaxResult<LoginRespVO> login(LoginParamVO loginParamVO, HttpServletRequest request) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("登录，loginName：{}，redirectUrl：{}", loginParamVO.getName(), loginParamVO.getRedirectUrl());
        }
        try {
            PasswordVerifier verifier = new PasswordVerifier(loginParamVO.getName(), loginParamVO.getPassword());
            JwtPrincipal merchant = (JwtPrincipal) xyyIndentityValidator.login(verifier);
            if (switchLoginAgreement) {
                try {
                    String ip = IPUtils.getClientIP(request);
                    loginAgreementBussinessRemoteService.insertLoginAgreementLogForLogin(loginParamVO.getAgreements(), loginParamVO.getCheckTime(), loginParamVO.getName(), merchant.getAccountId(), ip);
                } catch (Exception e) {
                    LOGGER.error("登录日志插入失败，errorMsg： {}", e.getMessage());
                }
            }
            LoginRespVO loginRespVO = new LoginRespVO();
            if (merchant != null) {
                Map<String, Object> map = loginAccountService.loginBuildDate(merchant.getAccountId());
                //关联店铺数
                Integer shopCount = (Integer) map.get("shopCount");
                if (Objects.equals(shopCount, 1)) {
                    Long merchantId = (Long) map.get("merchantId");
                    Boolean isAudit = (Boolean) map.get("isAudit");
                    Integer status = (Integer) map.get("status");
                    if (BooleanUtils.isTrue(isAudit)) { //关联状态审核通过且店铺状态审核通过，跳转到首页
                        if (Objects.equals(status, 3) && Objects.nonNull(merchantId)) {
                            // 关联状态审核通过且店铺状态审核通过，跳转到首页时校验药店状态，若是冻结，则给出用户相应的提示
                            boolean result = merchantCancelFreezeLogBusinessApi.findIsCancellation(merchantId);
                            String errorMsg = "";
                            if (result) {
                                LOGGER.info("登录，accountId：{}，merchantId：{}，已注销", merchant.getAccountId(), merchantId);
                                errorMsg = "您的账号已成功注销";
                            } else {
                                LOGGER.info("登录，accountId：{}，merchantId：{}，已冻结", merchant.getAccountId(), merchantId);
                                errorMsg = "该店铺处于冻结状态，请联系客服解冻";
                            }
                            // 清除登录信息
                            xyyIndentityValidator.newLogout();
                            return AjaxResult.errResult(errorMsg);
                        }
                        JwtPrincipal jwtPrincipal = (JwtPrincipal) xyyIndentityValidator.setPrincipalMerchant(merchantId, merchant.getAccountId());
                        ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.selectLoginAccountById(merchant.getAccountId());
                        LoginAccountDto loginAccountDto = apiRPCResult.getData();
                        // 强制修改密码检查
                        if (forceUpdatePasswordHelper.check(loginAccountDto)) {
                            LOGGER.warn("强制修改密码, 账号[{}]", merchant.getAccountId());
                            // 刷新登录信息缓存
                            tokenService.refreshTokenOneHour(jwtPrincipal);

                            // 清除登录信息
                            xyyIndentityValidator.newLogout();
                            // 弹出提示需要修改密码弹窗
                            String errorMsg = "请修改密码";
                            loginRespVO.setName(loginParamVO.getName());
                            loginRespVO.setLevel(2);
                            loginRespVO.setErrorMsg(errorMsg);
                            // 前端统一处理 返回success
                            return AjaxResult.successResult(loginRespVO);
                        }
                        String realIP = IPUtils.getClientIP(request);
                        LOGGER.info("登录，保存登录凭证，loginName:{}, accountId:{}, merchantId:{}, realIP:{}", loginParamVO.getName(), merchant.getAccountId(), merchantId, realIP);
                        if (spiderHelper.getNewSpiderInterceptionOpen()) {
                            if (spiderHelper.needSmsVerify(merchant.getAccountId().toString())) {
                                LOGGER.warn("爬虫账号[{}], 需要短信验证", merchant.getAccountId());
                                // 刷新登录信息缓存
                                tokenService.refreshTokenOneHour(jwtPrincipal);

                                String key = String.format("login:%s", loginParamVO.getName());
                                redisClient.set(key,verifier,600);
                                // 清除登录信息
                                xyyIndentityValidator.newLogout();

                                // 弹出短信验证窗口
                                String errorMsg = "爬虫账号, 需要短信验证";
                                loginRespVO.setName(loginParamVO.getName());
                                loginRespVO.setLevel(0);
                                loginRespVO.setMerchantId(merchantId);
                                loginRespVO.setErrorMsg(errorMsg);
                                // 前端统一处理 返回success
                                return AjaxResult.successResult(loginRespVO);
                            }
                        }
                        // 异地登录检查
                        if (differentPlacesLoginHelper.isDifferentPlacesLogin(loginAccountDto)) {
                            LOGGER.warn("异地登录, 账号[{}]", merchant.getAccountId());
                            // 设置异地登录标识
                            merchant.setIsDifferentPlacesLogin(true);
                            // 刷新登录信息缓存
                            tokenService.refreshTokenOneHour(jwtPrincipal);

                            String key = String.format("login:%s", loginParamVO.getName());
                            redisClient.set(key,verifier,600);
                            // 清除登录信息
                            xyyIndentityValidator.newLogout();

                            // 弹出短信验证窗口
                            String errorMsg = "异地验证";
                            loginRespVO.setName(loginParamVO.getName());
                            loginRespVO.setLevel(1);
                            loginRespVO.setMerchantId(merchantId);
                            loginRespVO.setErrorMsg(errorMsg);
                            // 前端统一处理 返回success
                            return AjaxResult.successResult(loginRespVO);
                        }
                        snowGroundUtil.uploadEventInfoForLoginSuccess(request, merchantId);
                        //组装数据
                        // LoginRespVO respVO = new LoginRespVO(loginParamVO.getName(), "ok", loginParamVO.getRedirectUrl());
                        // 单个店铺跳转到首页
                        loginRespVO.setName(loginParamVO.getName());
                        loginRespVO.setMerchantId(merchantId);
                        return AjaxResult.successResult(loginRespVO);
                    } else {
                        // 跳转店铺页面
                        loginRespVO.setName(loginParamVO.getName());
                        loginRespVO.setSelectLoginShop(true);
                        return AjaxResult.successResult(loginRespVO);
                    }
                } else if (shopCount < Constants.IS1) {
                    // 跳转药店页面
                    loginRespVO.setName(loginParamVO.getName());
                    loginRespVO.setConnectPharmacy(true);
                    return AjaxResult.successResult(loginRespVO);
                } else if (shopCount > Constants.IS1) {
                    LoginRespVO respVO = new LoginRespVO();
                    if (spiderHelper.getNewSpiderInterceptionOpen()) {
                        if (spiderHelper.needSmsVerify(merchant.getAccountId().toString())) {
                            LOGGER.warn("爬虫账号[{}], 需要短信验证 ", merchant.getAccountId());
                            // 刷新登录信息缓存
                            tokenService.refreshTokenOneHour(merchant);

                            String key = String.format("login:%s", loginParamVO.getName());
                            redisClient.set(key,verifier,600);
                            // 清除登录信息
                            xyyIndentityValidator.newLogout();

                            String errorMsg = "爬虫账号, 需要短信验证";
                            respVO.setName(loginParamVO.getName());
                            respVO.setLevel(0);
                            respVO.setSelectLoginShop(true);
                            respVO.setErrorMsg(errorMsg);
                            // 前端统一处理 返回success
                            return AjaxResult.successResult(respVO);
                        }
                    }
                    ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.selectLoginAccountById(merchant.getAccountId());
                    LoginAccountDto loginAccountDto = apiRPCResult.getData();
                    // 强制修改密码检查
                    if (forceUpdatePasswordHelper.check(loginAccountDto)) {
                        LOGGER.warn("强制修改密码, 账号[{}]", merchant.getAccountId());

                        // 刷新登录信息缓存
                        tokenService.refreshTokenOneHour(merchant);

                        // 清除登录信息
                        xyyIndentityValidator.newLogout();
                        // 弹出提示需要修改密码弹窗
                        String errorMsg = "请修改密码";
                        respVO.setName(loginParamVO.getName());
                        respVO.setLevel(2);
                        respVO.setSelectLoginShop(true);
                        respVO.setErrorMsg(errorMsg);
                        // 前端统一处理 返回success
                        return AjaxResult.successResult(respVO);
                    }
                    // 异地登录检查
                    if (differentPlacesLoginHelper.isDifferentPlacesLogin(loginAccountDto)) {
                        LOGGER.warn("异地登录, 账号[{}]", merchant.getAccountId());
                        // 设置异地登录标识
                        merchant.setIsDifferentPlacesLogin(true);
                        // 刷新登录信息缓存
                        tokenService.refreshTokenOneHour(merchant);

                        String key = String.format("login:%s", loginParamVO.getName());
                        redisClient.set(key,verifier,600);
                        // 清除登录信息
                        xyyIndentityValidator.newLogout();

                        // 弹出短信验证窗口
                        String errorMsg = "异地登录";
                        respVO.setName(loginParamVO.getName());
                        respVO.setLevel(1);
                        respVO.setSelectLoginShop(true);
                        respVO.setErrorMsg(errorMsg);
                        // 前端统一处理 返回success
                        return AjaxResult.successResult(respVO);
                    }
                    // 跳转选择店铺页面
                    respVO.setName(loginParamVO.getName());
                    respVO.setSelectLoginShop(true);
                    return AjaxResult.successResult(respVO);
                }
            } else {
                return AjaxResult.errResult("登录失败");
            }
        } catch (Exception e) {
            LOGGER.error("登录出错,e=", e);
            return AjaxResult.errResult(e.getMessage());
        }
        return AjaxResult.errResult("登录失败");
    }

    @Override
    public AjaxResult<LoginRespVO> logOut() {
        try {
            MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (null != merchantBussinessDto) {
                LOGGER.info("退出登录踢出登录用户id:{}", merchantBussinessDto.getId());
                merchantBussinessApi.refreshMerchantSourceCache(merchantBussinessDto.getMerchantId(), 0);
            }
        } catch (Exception e) {
            LOGGER.error("退出登录获得基本信息出错,error is {}", e.getMessage());
        }
        xyyIndentityValidator.logout();
        return AjaxResult.successResultNotData();
    }

    @Override
    public AjaxResult<RegisterRespVO> registerLast(RegisterParamVO registerParamVO, HttpServletRequest request) throws Exception {
        String merchantId = registerParamVO.getMerchantId();
        String mobile = registerParamVO.getMobile();
        String authCode = registerParamVO.getAuthCode();
        String mobilepwd = registerParamVO.getMobilepwd();
        String registerSource = registerParamVO.getRegisterSource();

        RegisterRespVO registerRespVO = new RegisterRespVO();
        registerRespVO.setRegisterSource(registerSource);
        if (request.getMethod().equals("GET")) {
            registerRespVO.setMobile(mobile);
            registerRespVO.setMerchantId(merchantId);
            return AjaxResult.successResult(registerRespVO);
        }
        try {
            ResultMessage<MerchantBussinessDto> resultMessage = merchantBussinessApi.appRegisterSecond(mobile, authCode, 4);
            if (resultMessage.getCode() == ResultCodeEnum.ERROR.getCode()) {
                registerRespVO.setStatus(false);
                registerRespVO.setMsg(resultMessage.getMsg());
                registerRespVO.setMobilepwd(mobilepwd);
                registerRespVO.setSourceType(1);
                registerRespVO.setMobile(mobile);
                registerRespVO.setMerchantId(merchantId);
                return AjaxResult.errResult(resultMessage.getMsg(), registerRespVO);
            }
            //注册成功之后跳转都注册成功的页面
            PasswordVerifier verifier = new PasswordVerifier(mobile, mobilepwd);
            verifier.setFlag(false);
            //强制走主库
            xyyIndentityValidator.login(verifier);
            //同步到神农
            asyncMerchantInfoService.pushEcMerchantInfoToGodErp("SYNC" + resultMessage.getResult().getId());
            //添加埋点信息
            //补充数据
            generateMdData(registerRespVO, merchantId == null ? 0 : Long.parseLong(merchantId));
            //push 豆芽数据
            asyncMerchantInfoService.pushMerchantRegister(Long.parseLong(merchantId));
            //雨诺过来的数据且登录成功记为首页(埋点日志)
            if (StringUtils.isNotBlank(registerSource) && "7".equals(registerSource)) {
                MerchantMdLogBussinessDto merchantMdLogBussinessDto = new MerchantMdLogBussinessDto();
                merchantMdLogBussinessDto.setDataType("0");
                merchantMdLogBussinessDto.setOperateTime(new Date());
                merchantMdLogBussinessDto.setRegisteredSource((byte) 2);
                merchantMdLogBussinessDto.setMerchantId(Long.parseLong(merchantId));
                merchantMdLogBusinessApi.asyncInsert(merchantMdLogBussinessDto);
            }
            return AjaxResult.successResultNotData();
        } catch (Exception se) {
            LOGGER.error("注册第二步验证商户邀请码异常,e=" + ExceptionUtils.getStackTrace(se));
            registerRespVO.setStatus(false);
            registerRespVO.setMsg(se.getMessage());
            registerRespVO.setMobilepwd(mobilepwd);
            registerRespVO.setSourceType(1);
            registerRespVO.setMobile(mobile);
            registerRespVO.setMerchantId(merchantId);
            if (StringUtils.isNotBlank(registerSource) && "9".equals(registerSource)) {
                registerRespVO.setRegisterSource("9");
            }
            return AjaxResult.errResult(se.getMessage(), registerRespVO);
        }
    }

    @Override
    public AjaxResult<ForgetPassLastRespVO> forgetPassLast(ForgetPassLastParamVO param) throws Exception {
        ForgetPassLastRespVO forgetPassLastRespVO = new ForgetPassLastRespVO();
        String mobile = param.getMobile();
        String password = param.getPassword();
        String code = param.getYqcode();
        forgetPassLastRespVO.setMobile(mobile);
        try {
            String checkResult = Pwdverifiers.checkPwd(password);
            if (!checkResult.equals("OK")) {
                forgetPassLastRespVO.setYqcode(code);
                forgetPassLastRespVO.setMsg(checkResult);
                return AjaxResult.successResult(forgetPassLastRespVO);
            }

            ResultMessage<?> resultMessage = merchantBussinessApi.checkVerificationCode(mobile, code);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if (resultCode != ResultCodeEnum.SUCCESS.getCode()) {
                forgetPassLastRespVO.setYqcode(code);
                forgetPassLastRespVO.setMsg(msg);
                return AjaxResult.successResult(forgetPassLastRespVO);
            }
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage(), forgetPassLastRespVO);

        }
        com.xyy.ec.merchant.server.dto.LoginAccountDto loginAccountDto = loginAccountService.selectLoginAccountForLoginByMobile(mobile);
        if (loginAccountDto == null) {
            return AjaxResult.successResultNotData();
        } else {
            LoginAccountDto loginAccount = merchantServiceRpc.selectLoginAccountById(loginAccountDto.getId());
            // 密码一致
            if (loginAccount.getPassword().equals(MD5Util.getMD5Str(password))){
                return AjaxResult.errResult("新修改密码与原密码一致，请更换密码后保存");
            }
            merchantBussinessApi.updatePassword(mobile, null, password);
            loginAccount = merchantServiceRpc.selectLoginAccountById(loginAccountDto.getId());
            String newPassword = MD5Util.getMD5Str(password);
            loginAccount.setPassword(newPassword);
            merchantServiceRpc.updateLoginAccount(loginAccount);
        }
        //todo insert 忘记密码修改密码记录
        MerchantPwdModifiLogBussinessDto logBussinessDto = new MerchantPwdModifiLogBussinessDto();
        logBussinessDto.setMerchantId(loginAccountDto.getId());
        logBussinessDto.setType(3);
        logBussinessApi.insert(logBussinessDto);
        // 关闭账号强制密码修改
        UpdateAccountPasswordVo updateAccountPasswordVo = UpdateAccountPasswordVo.builder()
                .accountId(loginAccountDto.getId())
                .userId(loginAccountDto.getId())
                .username(loginAccountDto.getAccountName())
                .reason("完成修改密码, 解除账号强制修改密码")
                .build();
        merchantAdminBusinessApi.liftForceUpdatePasswordById(updateAccountPasswordVo);
        // ******** 删除 accountId 所有会话
        loginAccountApi.clearAccountSessionId(loginAccountDto.getId());
        return AjaxResult.successResult(forgetPassLastRespVO);
    }

    @Override
    public AjaxResult<String> getCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String mechantId = request.getParameter("merchantId");
        response.setContentType("image/jpeg");
        VerifyCodeUtils vCode = new VerifyCodeUtils(100,30,4,5);
        vCode.write(response.getOutputStream());
        String code = vCode.getCode().toUpperCase();
        merchantBussinessApi.setPCVcode(code,code,300);
        return null;
    }

    @Override
    public AjaxResult<String> checkPhoCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try{
            String photoCode = request.getParameter("photoCode");
            String mobile = request.getParameter("mobile");
            String vCode = merchantBussinessApi.getPCVcode(photoCode.toUpperCase());
            if(StringUtil.isEmpty(vCode)){
                String errMsg = "验证码已经失效，请重新生成";
                return AjaxResult.errResult(errMsg,mobile);
            }else{
                if(!photoCode.toUpperCase().equals(String.valueOf(vCode).toUpperCase())){
                    String errMsg = "验证码输入错误，请重新输入";
                    return AjaxResult.errResult(errMsg,mobile);
                }
            }
            return AjaxResult.successResult(mobile);
        }catch (Exception e){
            LOGGER.error("获取验证码异常,e="+e);
        }
        return null;
    }

    @Override
    public AjaxResult<WxLoginRespVO> wxLogin(WxLoginParamVO wxLoginParamVO, HttpServletRequest request) {
        String accessToken = wxLoginParamVO.getAccessToken();
        String openid = wxLoginParamVO.getOpenid();
        String checkTime = wxLoginParamVO.getCheckTime();
        String agreements = wxLoginParamVO.getAgreements();
        // 1、获取微信用户信息
        ApiRPCResult<WechatUserDto> apiRPCResult1 = wechatLoginApi.getUserinfo(accessToken, openid);
        if (apiRPCResult1.isFail()) {
            return AjaxResult.errResult("获取微信用户信息失败，请稍后再试");
        }
        WechatUserDto wechatUserDto = apiRPCResult1.getData();
        String unionId = wechatUserDto.getUnionid();
        // 2、查询 unionId 绑定的账号
        ApiRPCResult<LoginAccountDto> apiRPCResult2 = loginAccountApi.selectAccountByUnionId(unionId);
        LoginAccountDto loginAccountDto = apiRPCResult2.getData();
        WxLoginRespVO wxLoginRespVO = new WxLoginRespVO();
        if (apiRPCResult2.getData() == null) {
            // 未绑定账号，跳转绑定页进行账号绑定
            wxLoginRespVO.setStatus(0);
            return AjaxResult.successResult(wxLoginRespVO);
        }
        // 3、进行模拟账密登录
        String loginName = loginAccountDto.getMobile();
        String loginPass = loginAccountDto.getPassword();
        try {
            PasswordVerifier verifier = new PasswordVerifier(loginName, loginPass);
            JwtPrincipal merchant = (JwtPrincipal) xyyIndentityValidator.login(verifier);
            if (switchLoginAgreement){
                try {
                    loginAgreementBussinessRemoteService.insertLoginAgreementLogForLogin(agreements,checkTime,loginName,merchant.getAccountId(), IPUtils.getClientIP(request));
                } catch (Exception e){
                    LOGGER.error("登录日志插入失败，errorMsg", e);
                }
            }
            if (merchant == null) {
                return AjaxResult.errResult("登陆失败");
            }
            Map<String, Object> map = loginAccountService.loginBuildDate(merchant.getAccountId());
            Integer shopCount = (Integer) map.get("shopCount");
            Long merchantId = (Long) map.get("merchantId");
            Boolean isAudit = (Boolean) map.get("isAudit");
            Integer status = (Integer) map.get("status");
            if (Objects.equals(shopCount, 1)) {
                // 只关联一家店铺，自动进行店铺登录
                if (BooleanUtils.isFalse(isAudit)) {
                    // 关联状态不为审核通过 or 店铺状态不为审核通过，跳转到选择关联店铺页面
                    wxLoginRespVO.setAccount(loginName);
                    wxLoginRespVO.setStatus(1);
                    wxLoginRespVO.setSelectLoginShop(true);
                    return AjaxResult.successResult(wxLoginRespVO);
                }
                //关联状态审核通过且店铺状态审核通过，跳转到首页
                if (Objects.nonNull(merchantId) && Objects.equals(status, 3)) {
                    // 关联状态审核通过且店铺状态审核通过，跳转到首页时校验药店状态，若是冻结，则给出用户相应的提示
                    boolean result = merchantCancelFreezeLogBusinessApi.findIsCancellation(merchantId);
                    String errorMsg = result ? "您的账号已成功注销" : "该店铺处于冻结状态，请联系客服解冻";
                    LOGGER.info("登录，accountId：{}，merchantId：{}，{}", merchant.getAccountId(), merchantId, result ? "已注销" : "已冻结");
                    return AjaxResult.errResult(errorMsg);
                }
                LOGGER.info("登录，保存登录凭证，loginName:{}, accountId:{}, merchantId:{}, realIP:{}", loginName, merchant.getAccountId(), merchantId, IPUtils.getClientIP(request));
                snowGroundUtil.uploadEventInfoForLoginSuccess(request, merchantId);
                xyyIndentityValidator.setPrincipalMerchant(merchantId, merchant.getAccountId());
                // 跳转到首页 or 缓存页面
                wxLoginRespVO.setAccount(loginName);
                wxLoginRespVO.setStatus(1);
                wxLoginRespVO.setMerchantId(merchantId);
                return AjaxResult.successResult(wxLoginRespVO);
            }
            else if (shopCount < Constants.IS1) {
                // 没有关联店铺，跳转到选择关联店铺页面
                wxLoginRespVO.setAccount(loginName);
                wxLoginRespVO.setStatus(1);
                wxLoginRespVO.setConnectPharmacy(true);
                return AjaxResult.successResult(wxLoginRespVO);
            }
            else  {
                // 关联多家店铺，跳转到选择登录店铺页面
                wxLoginRespVO.setAccount(loginName);
                wxLoginRespVO.setStatus(1);
                wxLoginRespVO.setSelectLoginShop(true);
                return AjaxResult.successResult(wxLoginRespVO);
            }
        }
        catch (AuthenticationException ae) {
            LOGGER.error("登录出错, ae=", ae);
            return AjaxResult.errResult(ae.getMessage());
        }
        catch (Exception e) {
            LOGGER.error("登录异常, e=", e);
            return AjaxResult.errResult("登录异常");
        }
    }

    @Override
    public AjaxResult<Object> wxBind(WxBindParamVO wxBindParamVO) {
        try {
            String phone = wxBindParamVO.getPhone();
            String code = wxBindParamVO.getCode();
            String accessToken = wxBindParamVO.getAccessToken();
            String openid = wxBindParamVO.getOpenid();
            WxBindRespVO wxBindRespVO = new WxBindRespVO();
            // 1、校验验证码是否正确
            ResultMessage<?> resultMessage = merchantBussinessApi.checkVerificationCode(phone, code);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if (resultCode != ResultCodeEnum.SUCCESS.getCode()) {
                return AjaxResult.errResult(msg);
            }
            // 2、查询微信用户信息
            ApiRPCResult<WechatUserDto> apiRPCResult1 = wechatLoginApi.getUserinfo(accessToken, openid);
            WechatUserDto wechatUserDto = apiRPCResult1.getData();
            if (apiRPCResult1.isFail()) {
                return AjaxResult.errResult("获取微信用户信息失败，请稍后再试");
            }
            // 3、查询该微信是否已绑定过账号
            ApiRPCResult<LoginAccountDto> apiRPCResult2 = loginAccountApi.selectAccountByUnionId(wechatUserDto.getUnionid());
            LoginAccountDto loginAccountDto1 = apiRPCResult2.getData();
            if (loginAccountDto1 != null) {
                return AjaxResult.errResult(StrUtil.format("该微信已绑定要帮忙其他账号[{}]，不可重复绑定", loginAccountDto1.getMobile()));
            }
            // 4、判断手机号码是否已注册
            ApiRPCResult<LoginAccountDto> apiRPCResult3 = loginAccountApi.getAccountInfoByMobile(phone);
            LoginAccountDto loginAccountDto2 = apiRPCResult3.getData();
            if (apiRPCResult3.isFail()) {
                return AjaxResult.errResult(apiRPCResult3.getErrMsg());
            }
            if (apiRPCResult3.isSuccess() && apiRPCResult3.getData() == null) {
                 wxBindRespVO.setMsg("账号未注册，是否注册新账号");
                 wxBindRespVO.setRegisterStatus(0);
                return AjaxResult.successResult(wxBindRespVO);
            }
            // 5、进行账号绑定微信
            ApiRPCResult<Boolean> apiRPCResult4 = loginAccountApi.updateUnionIdByMobile(wechatUserDto.getOpenid(), wechatUserDto.getUnionid(), phone);
            if (apiRPCResult4.getData() == null || BooleanUtils.isFalse(apiRPCResult4.getData())) {
                throw new AppException("绑定微信失败", XyyJsonResultCodeEnum.FAIL);
            }
            // 6、查询该微信是否已绑定过账号关联关系 调用接口修改企业微信客户备注及标签
            this.wechatBindCommon(loginAccountDto2.getId(),wechatUserDto.getUnionid());
            userWechatBindApi.bind(loginAccountDto2.getId());
            wxBindRespVO.setMsg("wx绑定成功");
            wxBindRespVO.setRegisterStatus(1);
            return AjaxResult.successResult(wxBindRespVO);
        } catch (Exception e) {
            LOGGER.error("绑定微信异常", e);
            return AjaxResult.errResult("绑定微信异常");
        }
    }

    @Override
    public AjaxResult<Object> sendBindCode(String phone) {
        // 1、手机号格式校验
        if (org.apache.commons.lang3.StringUtils.isBlank(phone) || !MobileValidateUtil.isPass(phone) || phone.length() != 11) {
            return AjaxResult.errResult("手机号格式有误，请确认后重新填写！");
        }
        // 2、判断手机号码是否已注册
        ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.getAccountInfoByMobile(phone);
        if (apiRPCResult.isFail()) {
            return AjaxResult.errResult(apiRPCResult.getErrMsg());
        }
        if (apiRPCResult.isSuccess() && apiRPCResult.getData() == null) {
            WxBindRespVO wxBindRespVO = new WxBindRespVO();
            // 手机号代表未注册
            wxBindRespVO.setRegisterStatus(0);
            wxBindRespVO.setMsg("账号未注册，是否注册新账号");
            return AjaxResult.successResult(wxBindRespVO);
        }
        try {
            // 3、发送验证码
            ResultMessage<?> resultMessage = merchantBussinessApi.sendVerificationCode(phone,2);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if (resultCode != ResultCodeEnum.SUCCESS.getCode()) {
                return AjaxResult.errResult(msg);
            }
            WxBindRespVO wxBindRespVO = new WxBindRespVO();
            // 手机号已注册可以进行绑定
            wxBindRespVO.setRegisterStatus(1);
            wxBindRespVO.setMsg("发送验证码成功");
            return AjaxResult.successResult(wxBindRespVO);
        } catch (Exception e) {
            LOGGER.error("微信绑定短信验证码发送失败", e);
            return AjaxResult.errResult("微信绑定短信验证码发送失败");
        }
    }

    @Override
    public AjaxResult<Object> wxBindRegister(WxBindParamVO wxBindParamVO) {
        try {
            String accessToken = wxBindParamVO.getAccessToken();
            String openid = wxBindParamVO.getOpenid();
            // 1、查询微信用户信息
            ApiRPCResult<WechatUserDto> apiRPCResult1 = wechatLoginApi.getUserinfo(accessToken, openid);
            WechatUserDto wechatUserDto = apiRPCResult1.getData();
            if (apiRPCResult1.isFail()) {
                return AjaxResult.errResult("获取微信用户信息失败，请稍后再试");
            }
            // 2、查询该微信是否已绑定过账号
            ApiRPCResult<LoginAccountDto> apiRPCResult2 = loginAccountApi.selectAccountByUnionId(wechatUserDto.getUnionid());
            LoginAccountDto loginAccountDto1 = apiRPCResult2.getData();
            if (loginAccountDto1 != null) {
                return AjaxResult.errResult(StrUtil.format("该微信已绑定要帮忙其他账号[{}]，不可重复绑定", loginAccountDto1.getMobile()));
            }
            // 3、进行账号绑定微信
            JwtPrincipal jwtPrincipal = tokenService.getPrincipal();
            ApiRPCResult<Boolean> apiRPCResult4 = loginAccountApi.updateUnionIdById(wechatUserDto.getOpenid(), wechatUserDto.getUnionid(), jwtPrincipal.getAccountId());
            if (apiRPCResult4.getData() == null || BooleanUtils.isFalse(apiRPCResult4.getData())) {
                return AjaxResult.errResult("绑定微信失败");
            }
            // 4、查询该微信是否已绑定过账号关联关系 调用接口修改企业微信客户备注及标签
            this.wechatBindCommon(jwtPrincipal.getAccountId(), wechatUserDto.getUnionid());
            userWechatBindApi.bind(jwtPrincipal.getAccountId());
            WxBindRespVO wxBindRespVO = new WxBindRespVO();
            wxBindRespVO.setRegisterStatus(1);
            wxBindRespVO.setMsg("绑定微信成功");
            return AjaxResult.successResult(wxBindRespVO);
        } catch (Exception e) {
            LOGGER.error("绑定微信异常", e);
            throw new AppException("绑定微信异常", XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public AjaxResult<Object> registerNext(AccountRegisterVO accountRegisterVO) {
        try {
            if (StringUtils.isEmpty(accountRegisterVO.getContactName())) {
                return AjaxResult.errResult("联系人姓名不能为空");
            }
            accountRegisterVO.setRegisterSource(4);
            LoginAccountDto loginAccountDto = new LoginAccountDto();
            BeanUtils.copyProperties(accountRegisterVO, loginAccountDto);
            ApiRPCResult res = loginAccountService.register(loginAccountDto, accountRegisterVO.getCode());
            if (!res.isSuccess()) {
                return AjaxResult.errResult(res.getErrMsg());
            }
            //自动登录
            PasswordVerifier verifier = new PasswordVerifier(accountRegisterVO.getMobile(), MD5Util.getMD5Str(accountRegisterVO.getPassword()));
            xyyIndentityValidator.login(verifier);
            return AjaxResult.successResultNotData();
        } catch (Exception e) {
            log.error("注册失败,mobile:{}", accountRegisterVO.getMobile(), e);
            return AjaxResult.errResult("注册失败");
        }
    }

    private Object wechatBindCommon(Long accountId, String unionid) {
        // 2、查询该微信是否已绑定过账号关联关系 绑定其他账号时覆盖绑定关系
        ApiRPCResult<AccountMerchantWechatRelDto> apiRPCResult2 = accountMerchantWechatRelApi.selectByUnionId(unionid);
        if (apiRPCResult2.getData() != null) {
            AccountMerchantWechatRelDto existRel = apiRPCResult2.getData();
            if (accountId.equals(existRel.getAccountId())) {
                LOGGER.info("微信已绑定当前账号, accountId:{}, unionid:{}", accountId, unionid);
                WxBindRespVO wxBindRespVO = new WxBindRespVO();
                wxBindRespVO.setRegisterStatus(1);
                wxBindRespVO.setMsg("绑定微信成功");
                return AjaxResult.successResult(wxBindRespVO);
            }

            LOGGER.info("删除旧微信绑定关系, accountId:{}, unionid:{}", existRel.getAccountId(), unionid);
            ApiRPCResult<Boolean> deleteResult = accountMerchantWechatRelApi.deleteByUnionId(unionid);
            if (!deleteResult.isSuccess() || !BooleanUtils.isTrue(deleteResult.getData())) {
                LOGGER.error("删除旧微信绑定关系失败, unionid:{}, error:{}", unionid, deleteResult.getErrMsg());
                return AjaxResult.errResult("绑定失败");
            }
        }
        return wechatBindBase(accountId, unionid);
    }

    private Object wechatBindBase(Long accountId, String unionid) {
        // 3、绑定账号、店铺及微信关联关系
        AccountMerchantWechatRelDto accountMerchantWechatRelDto = AccountMerchantWechatRelDto.builder()
                .accountId(accountId)
                .unionid(unionid).build();
        ApiRPCResult<Boolean> apiRPCResult3 = accountMerchantWechatRelApi.insert(accountMerchantWechatRelDto);
        if (apiRPCResult3.isSuccess() && BooleanUtils.isTrue(apiRPCResult3.getData())) {
            try {
                LOGGER.info("wxWorkBusinessApi.updateRemarkAndTagByUnionId, 账号[{}], unionId[{}]", accountId, unionid);
                ApiRPCResult<String> apiRPCResult = wxWorkBusinessApi.updateRemarkAndTagByUnionId(unionid);
                LOGGER.info("wxWorkBusinessApi.updateRemarkAndTagByUnionId, 账号[{}], unionId[{}] 结果[{}]", accountId, unionid, apiRPCResult.getData());
            } catch (Exception e) {
                LOGGER.error("wxWorkBusinessApi.updateRemarkAndTagByUnionId, 异常 账号[{}], unionId[{}]", accountId, unionid, e);
            }
        }
        return BooleanUtils.isTrue(apiRPCResult3.getData()) ? AjaxResult.successResultNotData() : AjaxResult.errResult("绑定失败");
    }



    /**
     * 组装埋点数据
     */
    private void generateMdData(RegisterRespVO registerRespVO, Long merchantId) {
        try {
            if (null != registerRespVO && merchantId > 0) {
                MerchantBussinessDto cust = merchantBussinessApi.findMerchantById(merchantId);
                if (null != cust) {
                    registerRespVO.setLicenseStatus(cust.getLicenseStatus());
                    registerRespVO.setRealName(cust.getRealName());
                    registerRespVO.setProvince(cust.getProvince());
                    registerRespVO.setProvinceCode(cust.getProvinceCode());
                    registerRespVO.setCity(cust.getCity());
                    registerRespVO.setCityCode(cust.getCityCode());
                    registerRespVO.setDistrict(cust.getDistrict());
                    registerRespVO.setAreaCode(cust.getAreaCode());
                    registerRespVO.setActiveTime(cust.getActiveTime());
                    registerRespVO.setChannelCode(cust.getChannelNames());
                }
            }
        } catch (Exception e) {
            LOGGER.error("组装埋点数据失败,error is {}", e.getMessage());
        }
    }


}
