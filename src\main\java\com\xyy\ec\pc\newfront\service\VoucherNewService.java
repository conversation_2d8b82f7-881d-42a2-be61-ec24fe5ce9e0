package com.xyy.ec.pc.newfront.service;

import com.xyy.ec.pc.newfront.dto.CouponNewCenterVO;
import com.xyy.ec.pc.newfront.dto.CouponRespVO;
import com.xyy.ec.pc.newfront.dto.ReceiveCouponRespVO;
import com.xyy.ec.pc.newfront.vo.CheckCouponParamVO;
import com.xyy.ec.pc.rest.AjaxResult;

import java.util.List;

public interface VoucherNewService {
    /**
     * 领取优惠券
     */
    AjaxResult<Integer> receive(List<Long> templateIds);

    /**
     * 获取优惠券信息
     */
    AjaxResult<List<CouponRespVO>> getCoupon(List<Long> templateIds);

    /**
     * 已领取优惠券推荐楼层
     */
    AjaxResult<ReceiveCouponRespVO> getReceiveCouponsInfo();

    /**
     * 可领取优惠券楼层-平台券
     */
    AjaxResult<Object> getPlatformCouponsInfo(CheckCouponParamVO couponParamVO);

    /**
     * 可领取优惠券楼层-新人券
     */
    AjaxResult<Object> getNewCustomerCouponsInfo(CheckCouponParamVO couponParamVO);

    /**
     * 可领取优惠券楼层-商品券
     */
    AjaxResult<Object> getProductCouponsInfo(CheckCouponParamVO couponParamVO);

    /**
     * 可领取优惠券楼层-店铺券
     */
    AjaxResult<Object> getShopCouponsInfo(CheckCouponParamVO couponParamVO);
}
