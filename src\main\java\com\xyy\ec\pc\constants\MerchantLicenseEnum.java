package com.xyy.ec.pc.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 资质枚举类
 *
 * <AUTHOR>
 * @createDate 2020/01/07 18:34
 * @see com.xyy.ec.pc.constants
 */
public enum  MerchantLicenseEnum {
    license_uncommitted(1,"资质未提交"),
    license_committed(2,"资质已提交"),
    license_expired(3,"资质已过期"),
    license_auditing(4,"资质已通过"),
    license_camp_auditing(5,"表示首营资质审核中"),
    license_camp_pass(6,"表示首营一审通过");

    private int id;
    private  String value;

    MerchantLicenseEnum(int id,String value){
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, MerchantLicenseEnum> enumMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(MerchantLicenseEnum e : MerchantLicenseEnum.values()) {
            enumMaps.put(e.getId(), e);
            maps.put(e.getId(),e.getValue());
        }
    }

    public static String get(int id) {
        return enumMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
