package com.xyy.ec.pc.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 限定类型,0全场1指定csu
 *
 * <AUTHOR>
 */
@Getter
public enum MarketingShopScopeTypeEnum {

    /**
     * 全场
     */
    ALL(0, "全场"),

    /**
     * 指定csu参与
     */
    SPECIAL_CSU(1, "指定csu参与"),

    /**
     * 指定csu不参与
     */
    NON_SPECIAL_CSU(2, "指定csu不参与"),

    ;

    private Integer type;
    private String name;

    MarketingShopScopeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.name = desc;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param type
     * @return
     */
    public static MarketingShopScopeTypeEnum valueOfCustom(Integer type) {
        for (MarketingShopScopeTypeEnum anEnum : values()) {
            if (Objects.equals(anEnum.getType(), type)) {
                return anEnum;
            }
        }
        return null;
    }
}
