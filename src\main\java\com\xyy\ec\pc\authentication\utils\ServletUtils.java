package com.xyy.ec.pc.authentication.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.xyy.ec.pc.authentication.consts.Constants;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.base.KeepLoginStatusVerifier;
import com.xyy.ec.pc.base.PasswordVerifier;
import com.xyy.ec.pc.util.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.ServletRequest;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/4/10 14:13
 * @File ServletUtils.class
 * @Software IntelliJ IDEA
 * @Description
 */
public class ServletUtils {
    /**
     * 获取String参数
     */
    public static String getParameter(String name) {
        return getRequest().getParameter(name);
    }

    /**
     * 获取String参数
     */
    public static String getParameter(String name, String defaultValue) {
        return Convert.toStr(getRequest().getParameter(name), defaultValue);
    }

    public static String getCookieValue(String cookieKey) {

        Cookie cookie = getCookie(cookieKey);
        return cookie != null ? cookie.getValue() : null;
    }

    public static Cookie getCookie(String cookieKey) {

        Cookie[] cookies = getCookies();
        if (CollectionUtil.isNotEmpty(cookies)) {
            for (Cookie cookie : cookies) {
                if (cookie.getName() != null && cookie.getName().equals(cookieKey)) {
                    return cookie;
                }
            }
        }
        return null;
    }

    public static Cookie[] getCookies() {

        return getRequest().getCookies();
    }

    public static Cookie createCookie(String key, String value, Integer maxAge) {

        Cookie cookie = new Cookie(key, value);
        cookie.setPath("/");
        cookie.setSecure(false);
        if (maxAge != null) {
            cookie.setMaxAge(maxAge);
        }
        return cookie;
    }

    public static void writeCookie(Cookie cookie) {
        if (cookie == null) {
            return;
        }
        HttpServletRequest request = getRequest();
        if (request != null) {
            String host = request.getHeader("Host");
            if (ConfigUtils.WEBSITE.equals(host)) {
                cookie.setDomain("." + ConfigUtils.DOMAIN);
            }
        }
        HttpServletResponse response = getResponse();
        if (response != null) {
            response.addCookie(cookie);
        }
    }

    public static void removeCookie(String cookieName, String path)  {

        HttpServletRequest request = getRequest();
        Cookie[] cookies = request.getCookies();
        if (CollectionUtil.isEmpty(cookies)) {
            return;
        }
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals(cookieName)) {
                cookie.setMaxAge(0);
                cookie.setPath(path);
                String host = request.getHeader("Host");
                if (ConfigUtils.WEBSITE.equals(host)) {
                    cookie.setDomain("." + ConfigUtils.DOMAIN);
                }
                HttpServletResponse response = getResponse();
                if (response != null) {
                    response.addCookie(cookie);
                }
                break;
            }
        }
    }

    /** 写入cookie */
    public static void writeLoginCookie(JwtPrincipal principal, PasswordVerifier passwordVerifier, String token) {

        ServletUtils.writeCookie(createTokenCookie(token));
        ServletUtils.writeCookie(createPrincipalCookie(principal));
        ServletUtils.writeCookie(createLastLoginTime(principal));
        ServletUtils.writeCookie(createAutoLoginCookie(passwordVerifier, principal));
        ServletUtils.writeCookie(createDeviceIdCookie(passwordVerifier));
        ServletUtils.getRequest().setAttribute(Constants.PRINCIPAL_COOKIE_NAME, principal);
    }


    /** 清除cookie */
    public static void clearCookie() {

        ServletUtils.removeCookie(Constants.TOKEN_NAME, "/");
        ServletUtils.removeCookie(Constants.PRINCIPAL_COOKIE_NAME, "/");
        ServletUtils.removeCookie(Constants.LAST_LOGIN_TIME, "/");
        ServletUtils.removeCookie(Constants.AUTO_LOGIN_COOKIE_NAME, "/");
        ServletUtils.removeCookie(Constants.DEVICE_ID, "/");
        WebContext.currentRequest().setAttribute(Constants.PRINCIPAL_COOKIE_NAME, null);
    }

    /** 创建token cookie */
    public static Cookie createTokenCookie(String token) {

        return ServletUtils.createCookie(Constants.TOKEN_NAME, token, Constants.COOKIE_MAX_AGE);
    }

    /** 创建登录用户cookie */
    public static Cookie createPrincipalCookie(JwtPrincipal principal) {

        String cookValue = StrUtil.format("{}&{}&{}",
                principal.getAccountId(),
                Base64Utils.urlEncoding(DigestUtils.shaHex(principal.getAccountId().toString() + principal.getLoginTime().toString())),
                principal.getMechantId());
        return ServletUtils.createCookie(Constants.PRINCIPAL_COOKIE_NAME, cookValue, Constants.COOKIE_MAX_AGE);
    }

    /** 创建自动登录cookie */
    public static Cookie createAutoLoginCookie(Verifier verifier, JwtPrincipal principal) {

        ServletUtils.removeCookie(Constants.VISITOR_COOKIE_NAME, "/");
        if (!(verifier instanceof KeepLoginStatusVerifier)) {
            return null;
        }
        String value = StrUtil.format("{}&{}", principal.getAccountId(), principal.getLoginName());
        return ServletUtils.createCookie(Constants.AUTO_LOGIN_COOKIE_NAME, Base64Utils.urlEncoding(value), Constants.COOKIE_MAX_AGE);
    }

    /** 创建设备类型cookie */
    public static Cookie createDeviceIdCookie(PasswordVerifier verifier) {

        String deviceId = verifier.getDeviceId();
        if (StrUtil.isNotEmpty(deviceId)) {
            return ServletUtils.createCookie(Constants.DEVICE_ID, deviceId, Constants.COOKIE_MAX_AGE);
        }
        return null;
    }

    /** 创建的最后登录时间cookie */
    public static Cookie createLastLoginTime(JwtPrincipal jwtPrincipal) {

        return ServletUtils.createCookie(Constants.LAST_LOGIN_TIME, jwtPrincipal.getLoginTime().toString(), Constants.COOKIE_MAX_AGE);
    }

    /**
     * 获取Integer参数
     */
    public static Integer getParameterToInt(String name) {
        return Convert.toInt(getRequest().getParameter(name));
    }

    /**
     * 获取Integer参数
     */
    public static Integer getParameterToInt(String name, Integer defaultValue) {
        return Convert.toInt(getRequest().getParameter(name), defaultValue);
    }

    /**
     * 获取Boolean参数
     */
    public static Boolean getParameterToBool(String name) {
        return Convert.toBool(getRequest().getParameter(name));
    }

    /**
     * 获取Boolean参数
     */
    public static Boolean getParameterToBool(String name, Boolean defaultValue) {
        return Convert.toBool(getRequest().getParameter(name), defaultValue);
    }

    /**
     * 获得所有请求参数
     *
     * @param request 请求对象{@link ServletRequest}
     * @return Map
     */
    public static Map<String, String[]> getParams(ServletRequest request) {
        final Map<String, String[]> map = request.getParameterMap();
        return Collections.unmodifiableMap(map);
    }

    /**
     * 获得所有请求参数
     *
     * @param request 请求对象{@link ServletRequest}
     * @return Map
     */
    public static Map<String, String> getParamMap(ServletRequest request) {
        Map<String, String> params = new HashMap<>();
        for (Map.Entry<String, String[]> entry : getParams(request).entrySet()) {
            params.put(entry.getKey(), StringUtils.join(entry.getValue(), ","));
        }
        return params;
    }

    /**
     * 获取request
     */
    public static HttpServletRequest getRequest() {
        return getRequestAttributes().getRequest();
    }

    /**
     * 获取response
     */
    public static HttpServletResponse getResponse() {
        return getRequestAttributes().getResponse();
    }

    /**
     * 获取session
     */
    public static HttpSession getSession() {
        return getRequest().getSession();
    }

    public static ServletRequestAttributes getRequestAttributes() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        return (ServletRequestAttributes) attributes;
    }

    /**
     * 将字符串渲染到客户端
     *
     * @param response 渲染对象
     * @param string   待渲染的字符串
     */
    public static void renderString(HttpServletResponse response, String string) {
        try {
            response.setStatus(200);
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().print(string);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 内容编码
     *
     * @param str 内容
     * @return 编码后的内容
     */
    public static String urlEncode(String str) {
        try {
            return URLEncoder.encode(str, "utf-8");
        } catch (UnsupportedEncodingException e) {
            return "";
        }
    }

    /**
     * 内容解码
     *
     * @param str 内容
     * @return 解码后的内容
     */
    public static String urlDecode(String str) {
        try {
            return URLDecoder.decode(str, "utf-8");
        } catch (UnsupportedEncodingException e) {
            return "";
        }
    }

    public static String getCookieValueFromResponse(String cookieKey, HttpServletResponse response) {
        String cookieHeader = response.getHeader("Set-Cookie");

        if (cookieHeader == null) {
            return null;
        }

        String[] cookies = cookieHeader.split(";");
        for (String cookie : cookies) {
            String[] parts = cookie.trim().split("=");
            if (parts.length > 0 && parts[0].equals(cookieKey)) {
                return parts.length > 1 ? parts[1] : "";
            }
        }
        return null;
    }


    public static void deleteCookie() {
        HttpServletResponse response = WebContext.currentResponse();
        response.setHeader("Set-Cookie","");
    }
}