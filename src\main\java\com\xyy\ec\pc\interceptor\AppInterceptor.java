package com.xyy.ec.pc.interceptor;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.interceptor.helper.SpiderHelper;
import com.xyy.ec.pc.interceptor.helper.WxWorkCallBackHelper;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.layout.LayoutBaseService;
import com.xyy.ec.pc.util.AjaxUtil;
import com.xyy.ec.pc.util.CrawlerUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 登录状态拦截器，判断用户登录状态，session是否过期，是否爬虫等
 * @Author: WanKp
 * @Date: 2018/8/31 09:44
 **/

@Slf4j
@Component
public class AppInterceptor implements HandlerInterceptor {

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Resource
    private CrawlerUtil crawlerUtil;

    @Value("${environment}")
    private String environment;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private LayoutBaseService layoutBaseService;
    
    @Autowired
    private SpiderHelper spiderHelper;

    @Autowired
    private SpiderInterceptor spiderInterceptor;

    @Autowired
    private WxWorkCallBackHelper wxWorkCallBackHelper;



    //登录状态拦截器
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        String url = request.getServletPath();
        //企业微信回调
        if (wxWorkCallBackHelper.isCallBack(url)){
            return true;
        }

        // 爬虫拦截
        if (!spiderInterceptor.preHandle(request, response, handler)) {
            return false;
        }
        
        // test环境 或 cms管理侧不做状态拦截和爬虫拦截 --> 2024-03-22 11:47:35，进行代码重复合并，但逻辑有些奇怪，考虑改动影响可能很大，故而维持现状逻辑。
        if ("test".equals(environment) || layoutBaseService.judgeRequestIsFromCmsAdmin(request)) {
            log.info("Skip AppInterceptor, Request Is From CMS Or In Test. ENV:{}", environment);
            return true;
        }

        log.info("登录状态拦截器, Servlet Path: " + JSONObject.toJSONString(request.getServletPath()));

        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

        // 处理没有用户信息的请求，跳转到登录页
        if (!sessionTimeOutFilter(request, response, merchant)) return false;
        return true;
    }

    /**
     * 处理没有用户信息的请求，跳转到登录页
     *
     * @param request
     * @param response
     * @param merchant
     * @return
     * @throws Exception
     */
    private boolean sessionTimeOutFilter(HttpServletRequest request, HttpServletResponse response, MerchantBussinessDto merchant) throws Exception {

        if (merchant != null) return true;

        log.info("拦截到session失效请求, uri:" + request.getServletPath());

        // 跳转到登录页
        redirectToLoginPage(request, response, "sessionTimeOut");

        return false;
    }


    /**
     * 灰度踢出用户过滤器
     *
     * @param request
     * @param response
     * @param merchantId
     * @param realIP
     * @return
     * @throws Exception
     */
    private boolean kickOutFilter(HttpServletRequest request, HttpServletResponse response, Long merchantId, String realIP) throws Exception {
        
        // 判断请求是否需要被踢出
        if (!crawlerUtil.isKickOut(merchantId, realIP)) return true;

        // 否则，拦截，用户登出
        xyyIndentityValidator.logout();

        log.info("踢出灰度用户, merchantId:{}, ip:{}, url:{}", merchantId, realIP, request.getServletPath());

        // 记录踢出日志（调用反爬系统接口）
        crawlerUtil.saveKickOutLog(merchantId, realIP);

        // 跳转到登录页
        redirectToLoginPage(request, response, "isKickOut");

        // 拦截，不做后续处理
        return false;
    }


    /**
     * 踢出爬虫
     *
     * @param request
     * @param response
     * @param merchantId
     * @param realIP
     * @return
     * @throws Exception
     */
    private boolean crawlerFilter(HttpServletRequest request, HttpServletResponse response, Long merchantId, String realIP) throws Exception {
        
        if (!crawlerUtil.isCrawler(merchantId, realIP)) {
            return true;
        }

        // 是爬虫，登出，跳转到登录页
        log.info("踢出爬虫用户, merchantId: {}, ip:{}", merchantId, realIP);

        // 登出用户
        xyyIndentityValidator.logout();

        // 跳转到登录页
        redirectToLoginPage(request, response, "isCrawler");

        return false;
    }


    /**
     * 跳转到登录页
     *
     * @param request
     * @param response
     * @param sessionStatus
     * @throws Exception
     */
    private void redirectToLoginPage(HttpServletRequest request, HttpServletResponse response, String sessionStatus) throws Exception {
        if (AjaxUtil.isAjaxRequest(request)) {
            // 如果是ajax请求，返回sessionStatus为 sessionStatus，在common.js中会有处理，跳转到登录页
            response.setHeader("SessionStatus", sessionStatus);
            // 为了兼容前端（前端框架限制，没有响应体数据则取不到响应头）
            this.writeAjaxErrorJson(response, 90000, "登录失效，请重新登陆");
        } else {
            String redirectUrl = request.getParameter("redirectUrl");
            if (StringUtils.isEmpty(redirectUrl)) {
                redirectUrl = request.getServletPath();
                String queryString = request.getQueryString();
                redirectUrl = queryString == null ? redirectUrl : redirectUrl + "?" + queryString;
            }
            log.info("重定向至登录页, redirectUrl: {}", redirectUrl);
            // 如果是页面跳转请求，直接跳转到登录页，加上redirectUrl
            String loginUrl = appProperties.getBasePathUrl()+"/login/login.htm";
            if (StringUtils.isNotEmpty(redirectUrl)) {
                loginUrl += "?redirectUrl=" + URLEncoder.encode(redirectUrl, "utf-8");
            }
            log.info("重定向至登录页, loginUrl: {}", loginUrl);
            response.sendRedirect(loginUrl);
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

    }

    private static final String RESULT_STATUS = "status";
    private static final String RESULT_MSG = "msg";
    private static final String RESULT_ERRORMSG = "errorMsg";
    private static final String RESULT_ERRORCODE = "code";
    private static final String RESULT_FAILURE = "failure";

    private static void writeAjaxErrorJson(HttpServletResponse response, int code, String errorMsg) {
        Map<String, Object> result = new HashMap<>();
        result.put(RESULT_STATUS, RESULT_FAILURE);
        result.put(RESULT_ERRORCODE, code);
        result.put(RESULT_MSG, errorMsg);
        result.put(RESULT_ERRORMSG, errorMsg);
        try {
            ServletUtil.write(response, JSONObject.toJSONString(result), ContentType.APPLICATION_JSON.toString());
        } catch (Exception e) {
            log.info("ajax请求登录失效，响应结果失败：{}", JSONObject.toJSONString(result), e);
        }
    }

}
