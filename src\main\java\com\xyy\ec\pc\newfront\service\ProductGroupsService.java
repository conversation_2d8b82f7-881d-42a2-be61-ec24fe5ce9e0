package com.xyy.ec.pc.newfront.service;

import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.cms.param.CmsRequestParam;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;
import com.xyy.ec.pc.newfront.vo.AddPurchaseCalculationParam;
import com.xyy.ec.pc.newfront.vo.ProductGroupsVO;
import com.xyy.ec.pc.newfront.vo.ProductParam;
import com.xyy.ec.pc.rest.AjaxResult;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

public interface ProductGroupsService {


    AjaxResult<ProductGroupsVO> listExpectProducts(Integer terminalType, CmsRequestParam cmsRequestParam, HttpServletRequest request);

    void handleProductVO(CmsListProductVO productVO);

    HashMap<Long, String> satisfactoryInHandPrice(MerchantBussinessDto merchant, List<Long> skuIds, HttpServletRequest request);

    AjaxResult<Integer> calculateTheNumOfShoppingCarts(AddPurchaseCalculationParam param);

    AjaxResult<ProductGroupsVO> listProducts(ProductParam param, HttpServletRequest request);
}
