package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;

public enum MerchantStatusEnum {

    STATUS_NORMAL(1,"正常"),
    STATUS_NON_ACTIVATED(2,"未激活"),
    STATUS_IS_FROZEN(3,"冻结");

    private int id;
    private  String value;

    MerchantStatusEnum(int id,String value){
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, MerchantStatusEnum> enumMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(MerchantStatusEnum e : MerchantStatusEnum.values()) {
            enumMaps.put(e.getId(), e);
            maps.put(e.getId(),e.getValue());
        }
    }

    public static String get(int id) {
        return enumMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }

}
