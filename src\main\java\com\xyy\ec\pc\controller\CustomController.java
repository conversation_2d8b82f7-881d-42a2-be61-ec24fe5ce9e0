package com.xyy.ec.pc.controller;

import com.xyy.ec.layout.buinese.dto.OpenImLogBuineseDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.core.util.CollectionUtil;
import com.xyy.ec.order.core.util.StringUtil;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.constants.CodeMapConstants;
import com.xyy.ec.pc.model.Merchant;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.service.OpenImLogService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import java.util.HashMap;
import java.util.Map;

/**
 * 在线客服/联系客服 模块
 */
@Controller
@RequestMapping(value = "/custom")
public class CustomController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomController.class);


    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private OpenImLogService openImLogService;

    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;

    @RequestMapping(value = {"/","/kit.htm"})
    public ModelAndView ok(ModelMap modelMap) throws Exception {
        MerchantBussinessDto merchant  = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

        String merchantId;
        if (merchant != null) {
            merchantId = "YBM-"+merchant.getId();
            Merchant mer = new Merchant();
            BeanUtils.copyProperties(merchant,mer);
            openImLogService.registerImUser(mer);
        }else{
            merchantId = openImLogService.getTouristId();
        }

        if (merchant!=null && merchant.getMobile() != null) {
            modelMap.put("uid","YBM-"+merchant.getMobile());
        }else{
            modelMap.put("uid","YBM-***********");
        }

        /*
            如果用户是连锁店用户的话,就跳到开票组的客服
            开票组(连锁店用户):*********
            普通组:161690348
         */
        String params = "?uid="+merchantId+"&to=药帮忙va&appkey="+ OpenImLogBuineseDto.APPKEY+"&pwd=123456&fullscreen";
        if(merchant!=null && merchant.getBusinessType() == 3){
            params += "&groupId=*********";
        }
        modelMap.put("param",params);

        return new ModelAndView("/kit.ftl",modelMap);
    }

    /**
     * @param
     * @return java.lang.Object
     * @throws
     * @Description:获取易客通配置的H5聊天页面URLXS420000("暂时都用湖北的")
     * <AUTHOR>
     * @date 2018/12/11 10:47 PM
     */
    @RequestMapping("/getIMPackUrl")
    @ResponseBody
    public Object getIMPackUrl(@RequestParam(value = "isThirdCompany", required = false)Integer isThirdCompany) {
        try {
            Map<String, String> codeItemMap = codeItemServiceRpc.findCodeMap(CodeMapConstants.IM_PACK_URL, "");

            if(StringUtil.isNotEmpty(codeItemMap.get("IM_PACK_URL_CLOSE")) && codeItemMap.get("IM_PACK_URL_CLOSE").equals("0")){
                return this.addError(codeItemMap.get("IM_PACK_URL_CLOSE_MSG"));
            }

            Map<String, String> result = new HashMap<>();
            if (CollectionUtil.isNotEmpty(codeItemMap)) {
                if(isThirdCompany != null && isThirdCompany == 1){
                    result.put("IM_PACK_URL", codeItemMap.get("IM_POP_PACK_URL"));
                }else {
                    result.put("IM_PACK_URL", codeItemMap.get("IM_PACK_URL"));
                }
            }
            return this.addResult("data", result);
        } catch (Exception e) {
            LOGGER.error("获取易客通配置的H5聊天页面URL异常", e);
            return this.addError("获取易客通配置的H5聊天页面URL异常");
        }
    }

}
