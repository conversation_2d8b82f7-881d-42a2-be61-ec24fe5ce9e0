package com.xyy.ec.pc.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.server.api.SubAccountMgrApi;
import com.xyy.ec.merchant.server.common.Page;
import com.xyy.ec.merchant.server.dto.SubAccountDto;
import com.xyy.ec.merchant.server.dto.SubAccountModifyDto;
import com.xyy.ec.merchant.server.enums.SubAccountOperateTypeEnum;
import com.xyy.ec.merchant.server.param.SubAccountQueryParam;
import com.xyy.ec.order.business.exception.ServiceException;
import com.xyy.ec.pc.remote.SubAccountRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class SubAccountRemoteServiceImpl implements SubAccountRemoteService {
    @Reference(version = "1.0.0")
    private SubAccountMgrApi subAccountMgrApi;

    @Override
    public Page<SubAccountDto> subAccountList(SubAccountQueryParam queryParam) throws ServiceException {
        log.debug("SubAccountRemoteService subAccountList request queryParam:{}", JSON.toJSONString(queryParam));
        try {
            ApiRPCResult<Page<SubAccountDto>> result = subAccountMgrApi.subAccountList(queryParam);
            log.debug("SubAccountRemoteService subAccountList response result:{}", JSON.toJSONString(result));
            if (result == null || result.isFail()){
                throw new ServiceException("查询子账号失败");
            }
            return result.getData();
        }catch (Exception e){
            throw new ServiceException("查询子账号失败");
        }
    }

    @Override
    public Boolean saveSubAccount(SubAccountDto subAccountDto) throws ServiceException {
        log.debug("SubAccountRemoteService saveSubAccount request queryParam:{}", JSON.toJSONString(subAccountDto));
        try {
            ApiRPCResult<Boolean> result = subAccountMgrApi.saveSubAccount(subAccountDto);
            log.debug("SubAccountRemoteService saveSubAccount response result:{}", JSON.toJSONString(result));
            if (result == null || result.isFail()){
                throw new ServiceException("保存失败");
            }
            return result.getData();
        }catch (Exception e){
            log.error("SubAccountRemoteService saveSubAccount error param:{}",JSON.toJSONString(subAccountDto), e);
            throw new ServiceException("保存失败");
        }
    }

    @Override
    public String checkMobileLegitimate(String mobile, Long merchantId) throws ServiceException {
        log.debug("SubAccountRemoteService checkMobileLegitimate request mobile:{}-merchantId:{}", mobile,merchantId);
        try {
            ApiRPCResult<String> result = subAccountMgrApi.checkMobileLegitimate(mobile,merchantId);
            log.debug("SubAccountRemoteService checkMobileLegitimate response result:{}", JSON.toJSONString(result));
            if (result == null || result.isFail()){
                throw new ServiceException("校验失败");
            }
            return result.getData();
        }catch (Exception e){
            log.error("SubAccountRemoteService checkMobileLegitimate error mobile:{}-merchantId:{}",mobile,merchantId, e);
            throw new ServiceException("校验失败");
        }
    }

    @Override
    public String handleSubAccountData(SubAccountModifyDto enabledDto) throws ServiceException {
        log.debug("SubAccountRemoteService handleSubAccountData request queryParam:{}", JSON.toJSONString(enabledDto));
        SubAccountOperateTypeEnum anEnum = SubAccountOperateTypeEnum.getEnum(enabledDto.getOperateType());
        try {
            ApiRPCResult<String> result = subAccountMgrApi.handleSubAccountData(enabledDto);
            log.debug("SubAccountRemoteService handleSubAccountData response result:{}", JSON.toJSONString(result));
            if (result == null || result.isFail()){
                throw new ServiceException(anEnum == null ? "处理失败" : anEnum.getText());
            }
            return result.getData();
        }catch (Exception e){
            log.error("SubAccountRemoteService handleSubAccountData error param:{}",JSON.toJSONString(enabledDto), e);
            throw new ServiceException(anEnum == null ? "处理失败" : anEnum.getText());
        }
    }

    @Value("${ec.pc.sub.account.mobile.check.regex:/^1(3(0-9)|4(********)|5(0-35-9)|6(2567)|7(0-8)|8(0-9)|9(0-35-9))\\d{8}$/}")
    private String mobileCheckRegex;
    @Override
    public String checkSaveParam(SubAccountDto subAccountDto) {
        if (subAccountDto == null){
            return "子账号信息不能为空";
        }
        if (StringUtils.isBlank(subAccountDto.getMobile())){
            return "手机号不能为空";
        }
        if (StringUtils.isBlank(subAccountDto.getSubAccountName())){
            return "子账号名称不能为空";
        }
        Pattern pattern = Pattern.compile(mobileCheckRegex);
        Matcher matcher = pattern.matcher(subAccountDto.getMobile());
        if (!matcher.matches()){
            return "手机号格式不正确";
        }
        return "";
    }
}
