package com.xyy.ec.pc.controller.vo;

import com.xyy.ec.order.business.dto.OrderTransactionSnapshotBusinessDto;
import com.xyy.ec.product.business.dto.SkuImagesVideos;
import java.io.Serializable;
import java.util.List;

public class OrderTranscationSnapshotVO extends OrderTransactionSnapshotBusinessDto implements Serializable {

    /** 商品图片集合 */
    private List<String> imagesList;

    /** 存储商品图片和视频集合*/
    private List<SkuImagesVideos> imagesVideosList;

    /** 药品分类图标地址 */
    private String drugClassificationImage;

    /** 药品分类名称 */
    private String drugClassificationText;

    /** 有效期还是保质期 */
    private String shelfLifeText;

    //近效期临期标识 1：临期 2：近效期
    private Integer nearEffectiveFlag;

    //特价、直降、秒杀活动标识 （和商品一致=99）
    private Integer activityType;

    private String tracingCodeStr;


    public String getTracingCodeStr() {
        if (this.getTracingCode() != null && this.getTracingCode().intValue() == 1) {
            return "有";
        }
        return "没有";
    }

    public void setTracingCodeStr(String tracingCodeStr) {
        this.tracingCodeStr = tracingCodeStr;
    }

    public List<String> getImagesList() {
        return imagesList;
    }

    public void setImagesList(List<String> imagesList) {
        this.imagesList = imagesList;
    }

    public List<SkuImagesVideos> getImagesVideosList() {
        return imagesVideosList;
    }

    public void setImagesVideosList(List<SkuImagesVideos> imagesVideosList) {
        this.imagesVideosList = imagesVideosList;
    }

    public String getDrugClassificationImage() {
        return drugClassificationImage;
    }

    public void setDrugClassificationImage(String drugClassificationImage) {
        this.drugClassificationImage = drugClassificationImage;
    }

    public String getDrugClassificationText() {
        return drugClassificationText;
    }

    public void setDrugClassificationText(String drugClassificationText) {
        this.drugClassificationText = drugClassificationText;
    }

    public String getShelfLifeText() {
        return shelfLifeText;
    }

    public void setShelfLifeText(String shelfLifeText) {
        this.shelfLifeText = shelfLifeText;
    }

    public Integer getNearEffectiveFlag() {
        return nearEffectiveFlag;
    }

    public void setNearEffectiveFlag(Integer nearEffectiveFlag) {
        this.nearEffectiveFlag = nearEffectiveFlag;
    }

    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    @Override
    public String toString() {
        return "OrderTranscationSnapshotAppDto{" +
                "imagesList=" + imagesList +
                ", imagesVideosList=" + imagesVideosList +
                ", drugClassificationImage='" + drugClassificationImage + '\'' +
                ", drugClassificationText='" + drugClassificationText + '\'' +
                ", shelfLifeText='" + shelfLifeText + '\'' +
                "} " + super.toString();
    }
}
