package com.xyy.ec.pc.param;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ToString
@Getter
@Setter
public class PingAnReqParam implements Serializable {

    private Date payStartTime;

    private Date payEndTime;

    private Integer isArchive;

    private Integer page=1;

    private Integer pageSize = 10;

    private Integer offset = 0;

    private List<Integer> tradeCodeList;

    /**
     * 交易类型
     */
    private Integer tradeCode;

    /**
     * 1 全部  2 收入  3支出
     */
    private Integer pageType;

    /**
     *
     */
    private String accountId;

    private Long merchantId;

}
