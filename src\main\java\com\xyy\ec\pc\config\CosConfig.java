package com.xyy.ec.pc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "cos")
public class CosConfig {
    private String secretId;
    private String secretKey;
    private String bucketName;
    private String domain;
    private String region;
    private String dir;
    private Integer retryCount;
    private Boolean switchFlag;
}
