package com.xyy.ec.pc.controller.cgbchina;

import com.xyy.ec.order.business.dto.guangfa.CgbResponseBody;
import com.xyy.ec.order.business.utils.cgb.ProxyCgbKeyInitConfig;
import com.xyy.ec.pc.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.multipart.FilePart;
import org.apache.commons.httpclient.methods.multipart.MultipartRequestEntity;
import org.apache.commons.httpclient.methods.multipart.Part;
import org.apache.commons.httpclient.methods.multipart.StringPart;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;


@RestController
@RequestMapping("/bank/api")
@Slf4j
public class CgbController extends BaseController {
 
    @Autowired
    private ProxyCgbKeyInitConfig proxyCgbKeyInitConfig;

    @RequestMapping("/refreshKey")
    public String refreshKey(String path){
        try {
            proxyCgbKeyInitConfig.initKey(path);
        } catch (Exception e) {
            log.error("加载广发秘钥失败：{}",e);
        }
        return "ok";
    }

//    public String notifyLoanResult(HttpServletRequest request,HttpServletResponse response){
//        LOGGER.info("进入调广发回调接口入口");
//        String requestBody = null;
//        try {
//            requestBody = IOUtils.read(request.getReader());
//            LOGGER.info("接收报文：{}",requestBody);
//        } catch (IOException e) {
//            LOGGER.info("读取参数异常：{}",e);
//            return requestBody;
//        }
//        StringBuilder m4key = new StringBuilder();
//        //验证requerheard 返回
//        CgbResponseBody responseBody = new CgbResponseBody();
//        try {
//            requestBody = VerifyUtil.verifyRequestAndParam(request, requestBody, m4key::append);
//            LOGGER.info("解析报文：{}", requestBody);
//            CgbRequestBody cgbRequestBody = JSONObject.parseObject(requestBody, CgbRequestBody.class);
//            //验证header
//            VerifyUtil.verifyHeaderBean(cgbRequestBody.getHeader(),responseBody);
//            //验证body
//            VerifyUtil.verifyBodyBean(cgbRequestBody.getBody(),responseBody);
//            responseBody = guangfaBankBusinessApi.cgbCallBack(cgbRequestBody, responseBody);
//        } catch (Exception e) {
//            LOGGER.error(e.getMessage(),e);
//            responseBody.getBody().setErrorMsg(CgbCodeEnum.getName(e.getMessage())).setErrorCode(e.getMessage());
//
//        }
//        try {
//            String result = JSON.toJSONString(responseBody).replace("body","Body").replace("header","Header");
//            LOGGER.info("返回报文：{}",result);
//            //返回字符串加签
//            String resultSignature = CgbSMUtil.signature(CgbKeyEnum.OPEN_COM_PV_KEY, result, VerifyUtil.GBK);
//            LOGGER.info("返回报文sm2私钥加签：{}", resultSignature);
//            //返回字符串加密
//            String respStr = CgbSMUtil.SM4EncryptData(m4key.toString(), result, VerifyUtil.GBK);
//            LOGGER.info("返回报文SM4加密：{}", respStr);
//            response.addHeader("signature", resultSignature);
//            response.addHeader("signType","SM2");
//            response.addHeader("encryptType","SM4");
//            String encode = CgbSMUtil.encode(CgbKeyEnum.OPEN_CGB_PU_KEY, m4key.toString(), VerifyUtil.GBK);
//            LOGGER.info("返回报文encryptKey加密：{}", encode);
//            response.addHeader("encryptKey",encode);
//            return respStr;
//        } catch (Exception e) {
//            LOGGER.error("验签||加密失败：{}", e);
//        }
//        return "";
//    }

    @RequestMapping("/transferFile")
    public Object transferFile(HttpServletRequest request, HttpServletResponse response){
//        log.info("广发下载商户信息请求");
//        String requestBody = null;
//        try {
//            requestBody = IOUtils.read(request.getReader());
//            log.info("接收报文：{}",requestBody);
//        } catch (IOException e) {
//            log.info("读取参数异常：{}",e);
//            return requestBody;
//        }
//        StringBuilder m4key = new StringBuilder();
        //验证requerheard 返回
        CgbResponseBody responseBody = new CgbResponseBody();
        try {
//            requestBody = VerifyUtil.verifyRequestAndParam(request, requestBody, m4key::append);
//            log.info("解析报文：{}", requestBody);
//            CgbRequestBody cgbRequestBody = JSONObject.parseObject(requestBody, CgbRequestBody.class);
//            responseBody.getBody().setErrorMsg("成功").setErrorCode("0000");

//            String instId = request.getParameter("instId");
//            String fileType = request.getParameter("fileType");
//            String fileDate = request.getParameter("fileDate");
//            String signature = request.getParameter("signature");

//            //获取私钥
//            byte[] pvkBytes = CgbSMUtil.getPuvkBytes(CgbKeyEnum.OPEN_COM_PV_KEY);
//            if(pvkBytes.length>32) {
//                pvkBytes = SM2Util.getPrivateKey(pvkBytes);
//            }
//            //解出随机密钥
//            String key =  SM2SignUtil.decryptString(SM2SignUtil.buildPrivateKeyByPrivateByte(pvkBytes), encryptKey, null);
//            String transDate = SM4Util.SM4DecryptData(key, encryptTransDate, "GBK");
//            log.info("返回报文解密结果【随机密钥：{}, 文件业务日期:{}】", key, transDate);
            //验签
//            String signData = paramToStringForCgb(request);
////            String signature = SM4Util.SM4DecryptData(key, signData, "GBK");
//            boolean verifyResult=SM2SignUtil.verifySign(signData, signature);
            //传输文件()
            //根据文件类型和文件日期查询文件地址
            /*
             * 设置输出的格式
             */
            String fileName = "DrugStoreInfo_20201030.zip"; // 文件的默认保存名
            response.reset();//清除首部的空白行
            response.setContentType("bin");//是设置文件类型的，bin这个文件类型是不存在的，浏览器遇到不存在的文件类型就会出现一个下载提示。
            response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");//
//            response.addHeader("Content-length", "0");
            /*
             * 读取磁盘上的文件
             */
            InputStream inStream = new FileInputStream("http://upload.test.ybm100.com/cgb/zip/DrugStoreInfo_20201030.zip");// 文件的存放路径


            // 循环取出流中的数据
            byte[] b = new byte[100];
            int len;
                while ((len = inStream.read(b)) > 0)
                    response.getOutputStream().write(b, 0, len);//向客户端响应
                inStream.close();//关闭流
            //获取结果
            return this.addResult();
        }catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            log.error("加载广发秘钥失败：{}",e);
            return e.getMessage();
        }
        return null;
    }

    /**
     * http请求上传文件
     * @param url
     * @param map
     * @param file
     * @param head
     * @return
     */
    public static String doPostWithFile(String url, Map<String,String> map, File file, Map<String,String> head) {
        try
        {
            HttpClient client = new HttpClient();
            PostMethod post = new PostMethod(url);
            FilePart fp = new FilePart("file",file);
            fp.setCharSet("utf-8");
            StringPart stringPart;
            Part[] parts = new Part[map.size()+1];
            int i = 0;
            for(Map.Entry<String, String> m : map.entrySet()){
                stringPart = new StringPart(m.getKey(),m.getValue(),"UTF-8");
                parts[i] = stringPart;
                i++;
            }
            parts[parts.length-1]=fp;
            MultipartRequestEntity entity = new MultipartRequestEntity(parts, new HttpMethodParams());
            post.setRequestEntity(entity);

            for(Map.Entry<String, String> m : head.entrySet()){
                post.setRequestHeader(m.getKey(),m.getValue());
            }
            client.executeMethod(post);
            return post.getResponseBodyAsString();
        } catch (Exception e){
            log.error("doPostWithFile occur a exception", e);
            return null;
        }
    }
}
