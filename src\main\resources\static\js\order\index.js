/* 订单中心脚本 */
$(function(){

	getAllShops();

	//订单tab点击
	$(".order-tab-title-item").click(function(){
		var status=$(this).attr("status");
		$("#orderSource").empty();
		$("#orderNo").val("");
		// 保存当前页面状态供后续使用
		localStorage.setItem('orderCenterMenu', 'order');
		findOrders(status);
	});
	//查询点击事件
	$(".query-btn").click(function(){
		var status =  $(".order-tab-title-item-cur").attr("status");
		findOrders(status);
	});

	//查询点击事件
	$(".top-button-mk-reset").click(function(){
		$("#orderNo").val("");
		$("#searchShopBox").val("");
		$("#drugName").val("");
		headerButtonClick('');
	});
	//查询点击事件
	$(".top-button-mk").click(function(){
		var status =  $(".order-tab-title-item-cur").attr("status");
		findOrders(status);
	});



	//订单导出事件
	$(".export-btn").click(function(){
		var status =  $(".order-tab-title-item-cur").attr("status");
		exportOrders(status);
	});

	//订单明细导出事件
	$(".detail-export-btn").click(function(){
		var status =  $(".order-tab-title-item-cur").attr("status");
		exportOrderDetails(status);
	});

	$(".invoice-btn").click(function(){
		var startCreateTime = $("#startCreateTime").val();
		var endCreateTime = $("#endCreateTime").val();
		if (!startCreateTime || !endCreateTime) {
			$.alert({"title":"提示","body":"时间跨度最长不能超过1个月！"});
			return;
		}
		var isOkTime = completeDate(new Date(startCreateTime), new Date(endCreateTime), 1);
		if (startCreateTime && endCreateTime && isOkTime) {
			downloadAllFp();
		} else {
			$.alert({"title":"提示","body":"时间跨度最长不能超过1个月！"});
		}

	});

	//清空点击事件
	$(".clear-btn").click(function(){
		var status=$(".main-right ul li[class='active'] a").attr("status");
		$("#orderSource").empty();
		$("#orderNo").val("");
		$("#startCreateTime").val("");
		$("#endCreateTime").val("");
		findOrders(status);
	});
	//删除点击事件
	$(".oper-del-btn").click(function(){
		var ids="";
		var canDel=true;
		$("[name='check_order_tr']:checked").each(function(){
			ids+=$(this).val()+",";
			var st=$(this).attr("st");
			if(st!=4){
				canDel=false;
			}
		});
		if(!ids){
			$.alert("最少选择一项进行删除");
			return;
		}
		if(!canDel){
			$.alert({"title":"提示","body":"当前操作包含正在处理中的商品，为避免您的误操作行为，正在进行中的订单不能删除，请知悉！"});
			return;
		}
		$(".modal-body .scbox").html("你确定要删除选中订单?");
		$('#delModal').modal('show');
		$('#delModal').on('okHide', function (e) {
			var index=layer.load(2);
			location.href="/merchant/center/order/deleteOrder.htm?ids="+ids;
		});
	});
	/*全选*/
	$(".check-all-btn").click(function(event){
		event.preventDefault();
		var $checkbox = $(".checkbox-pretty").checkbox();
		if($(this).hasClass("checked")){
			$checkbox.checkbox("uncheck");
		}else{
			$checkbox.checkbox("check");
		}
	});

// 	/*控制是否全选*/
	$(".table-item .checkbox-pretty").click(function(event){
		event.preventDefault();
		var $checkbox = $(this).checkbox();
		var $checkbox_con = $(".check-all-btn").checkbox();
		if($(this).hasClass("checked")){
			$checkbox.checkbox("uncheck");
		}else{
			$checkbox.checkbox("check");
		};
		$(".table-item .checkbox-pretty").each(function(){
			if($(this).hasClass("checked")){
				$checkbox_con.checkbox("check");
			}else{
				$checkbox_con.checkbox("uncheck");
				return false;
			}
		});
	});

	/*物流样式切换*/
	if ($("#orderNo").val() != undefined) {
		$.ajax({
			url: "/orderDelivery/getOrderDeliveryByOrderNo?orderNo=" + $("#orderNo").val(),
			type: "POST",
			dataType: "html",
			traditional: true,
			async: false,
			data: {
				storeStatus: true
			},
			success: function (result) {
				$(".logistic ").html(result)
			}
		});
	}

	//tab切换
	$(".order-tab-title-item").click(function(){
		var inx = $(this).index();
		$(".order-tab-title-item").removeClass("order-tab-title-item-cur").eq(inx).addClass("order-tab-title-item-cur");
		$(".tab-cont").hide().eq(inx).show();
	});
	var alength = $('.wl-title .wl-cont a').length
	if(alength == 1){
		$('.wl-title').css('display','none')
	}else if(alength < 12){

		$('.wl-box').css('display','none')
		$('.wl-cont').css({
			'width':'100%',
			'padding-left':'36px',
			'border-left':'0',
			'border-right': '0'
		})
	}

	var flag =true;
	$('.wl-left').click(function(){
		if(flag){
			flag= false;
			var wl_distance = $('.order-tab-title').css('margin-left');
			if( !(parseInt(wl_distance)>= '0')){
				if(parseInt(wl_distance)== '-78'){
					$('.wl-left').css('color','rgba(0,0,0,0.25)')
				}else{
					$('.wl-left').css('color','#666666')
				}
				wl_distance =( parseInt(wl_distance) + 78) + 'px';
				$('.order-tab-title').animate({
					'margin-left':wl_distance
				})
			}else{
				$('.wl-left').css('color','rgba(0,0,0,0.25)')
			}
			setTimeout(function(){
				flag =true;
			},600)
		}

	})
	$('.wl-right').click(function(){
		if(flag){
			flag= false;
			$('.wl-left').css('color','#666666')
			var wl_distance = $('.order-tab-title').css('margin-left');
			wl_distance =( parseInt(wl_distance) - 78) + 'px';
			$('.order-tab-title').animate({
				'margin-left':wl_distance

			})
			setTimeout(function(){
				flag =true;
			},600)
		}
	})



	/*搜索弹窗响应键盘事件*/
	$("#searchShopBox").unbind("keyup");
	$("#searchShopBox").keyup(function (event) {
		var name = $("#searchShopBox").val();
		filterShopList(name);
	});
	$("#searchShopBox").focus(function (event) {
		var name = $("#searchShopBox").val();
		filterShopList(name);
	});
	/*隐藏搜索弹窗*/
	$("#searchShopBox").blur(function () {
		setTimeout(function () {
			$("#searchShopUl").hide();
		}, 200)
	});

	// 点击店铺名称下拉框
	$("ul#searchShopUl").on("click","li",function(e){
		e.stopPropagation();
		$("#searchShopBox").val($(this).text());
		var orgId = $(this).context.attributes['orgid'].value;
		if (orgId) {
			$("#searchShopBox").attr('orgid', orgId);
		}
		$(".searchShopUl").hide();
	})

	$("#startCreateTime").datepicker().on('changeDate', function(e) {
		var endDate = new Date(e.date.getFullYear() + 1, e.date.getMonth(), e.date.getDate());
		// console.log(4545454545, endDate);
		$("#endCreateTime").datepicker('setEndDate', endDate);
	});

	// 标记下单反区域是否显示
	let showRebateArea = false;

	$.ajax({
		url: '/marketing/rebateVoucher/consumption/startActInfo',
		type: 'get',
	data: {

	},
	success(data) {
		if(data.status=="success"){
			if(data.data.result.actResultList!=null||data.data.result.actResultList.length>0){
				var str = "";
				if(data.data.result.levelScene == 1){
					if(data.data.result.realAmount==0 && data.data.result.lastMonthExpectedAmount){
						str = '<div class="state-content">参与下单返活动，预估月均可返红包 <span>'+data.data.result.lastMonthExpectedAmount+'元</span></div>'
					}else {
						str = '<div class="state-content">参与下单返活动，预估月均可返红包 <span>'+data.data.result.actTags.maxReturnRedPackageAmount+'元</span></div>'
					}
				}else if(data.data.result.levelScene == 2){
					str = '<div class="state-content">仅差<span>'+data.data.result.nextLevelShortAmount+'元</span>参与返利活动，返利<span>'+data.data.result.nextLevelRate+'%</span>起</div>'
				}else if(data.data.result.levelScene == 3&&data.data.result.nextLevelShortAmount!=0){
					str = '<div class="state-content">已获得红包 <span>'+data.data.result.realAmount+'元</span>仅差<span>'+data.data.result.nextLevelShortAmount+'元</span>可返红包<span>'+data.data.result.nextLevelRedPacketAmount+'元</span></div>'
				}else if(data.data.result.levelScene == 3&&data.data.result.nextLevelShortAmount==0){
					str = '<div class="state-content">已获得红包<span class="amount-color">'+data.data.result.realAmount+'元</span></div>'
				}else{
					str = '<div class="state-content"> 已获得红包 <span>'+data.data.result.realAmount+'元</span>，多买多返最高返<span>'+data.data.result.actTags.maxReturnRedPackageAmount+'元</span></div>'
				}
				$("#rebateContent").html(str)
				$("#rebatePage").show()
				showRebateArea = true;
			}
		}
		initCarousel(showRebateArea);
	},
		error(err) {
			parent.layer.msg('网络异常');
			initCarousel(false);
		}
	})
	// 注意：rebatePage 的点击事件在 initCarousel 函数中统一处理，避免重复绑定

    // 数据埋点函数
    function trackCarouselExposure(itemType) {
        console.log('轮播组件曝光:', itemType);
        // 埋点
    }

    function trackCarouselClick(itemType) {
        console.log('轮播组件点击:', itemType);
        // 埋点
    }

    function trackButtonClick(buttonType, itemType) {
        console.log('按钮点击:', buttonType, '当前项:', itemType);
        // 埋点
    }

    // 轮播功能实现
    function initCarousel(showRebateArea = false) {
        const config = window.carouselConfig || {
            interval: 3000,
            showSaasAd: true,
            autoPlay: true,
            saasAdUrl: '',
            pauseOnHover: true
        };
        // 判断实际显示的区域
        const actualShowRebate = showRebateArea && $('#rebatePage').is(':visible');
        const actualShowSaas = config.showSaasAd;
		
        if (!actualShowRebate && !actualShowSaas) {
            // 情况：都不显示 - 隐藏整个轮播容器
            $('#carouselContainer').hide();
            return;
        }

        if (!actualShowSaas) {
            // 情况：只显示下单反 - 隐藏SaaS区域和箭头
            $('#saasAdPage').hide();
            $('.carousel-arrows').hide();
            $('#carouselContainer').show();
            // 确保下单反区域显示并触发埋点
            if (actualShowRebate) {
                trackCarouselExposure('rebate');

                // 绑定下单反区域点击事件
                $('#rebatePage').off('click').on('click', function(e) {
                    trackCarouselClick('rebate');
                    window.location.href = '/marketing/rebateVoucher/consumerRebate.htm';
                });

                // 绑定下单反按钮点击事件
                $('.rebate-look-btn').off('click').on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    trackButtonClick('rebate-btn', 'rebate');
                    window.location.href = '/marketing/rebateVoucher/consumerRebate.htm';
                    return false;
                });
            }
            return;
        }

        if (!actualShowRebate) {
            // 情况：只显示SaaS广告 - 隐藏下单反区域和箭头
            $('#rebatePage').hide();
            $('.carousel-arrows').hide();
            $('#saasAdPage').show().addClass('active');
            $('#carouselContainer').show();
            // 触发SaaS区域埋点
            trackCarouselExposure('saas');

            // 绑定SaaS广告点击事件
            $('#saasAdPage').off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                trackCarouselClick('saas');
                if (config.saasAdUrl) {
                    window.open(config.saasAdUrl, '_blank');
                } else {
                    console.log('SaaS广告被点击，但未配置跳转链接');
                }
                return false;
            });

            // 绑定SaaS按钮点击事件
            $('.saas-btn').off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                trackButtonClick('saas-btn', 'saas');
                if (config.saasAdUrl) {
                    window.open(config.saasAdUrl, '_blank');
                } else {
                    console.log('SaaS立即体验按钮被点击，但未配置跳转链接');
                }
                return false;
            });
            return;
        }

        // 情况：下单反 + SaaS广告都显示 - 启用完整轮播功能
        $('#carouselContainer').show();
        $('#saasAdPage').show();
        $('.carousel-arrows').show();

        let currentIndex = 0;
        const items = $('.carousel-item:visible'); // 只获取可见的轮播项
        const totalItems = items.length;
        let autoPlayTimer = null;
        let restartTimer = null; // 用于管理重启定时器

        // 创建显示项映射数组，用于正确的埋点
        const itemTypeMap = [];
        if (actualShowRebate) itemTypeMap.push('rebate');
        if (actualShowSaas) itemTypeMap.push('saas');

        // 获取当前显示项的类型
        function getCurrentItemType() {
            return itemTypeMap[currentIndex] || 'unknown';
        }

        // 切换到指定项目
        function switchToItem(index) {
            // 隐藏所有可见项目
            items.hide().removeClass('active');
            // 显示指定项目
            if (items[index]) {
                $(items[index]).show().addClass('active');
                currentIndex = index;

                // 触发曝光埋点
                const itemType = getCurrentItemType();
                trackCarouselExposure(itemType);
            }
        }

        // 下一项
        function nextItem() {
            if (totalItems > 1) {
                const nextIndex = (currentIndex + 1) % totalItems;
                switchToItem(nextIndex);
            }
        }

        // 上一项
        function prevItem() {
            if (totalItems > 1) {
                const prevIndex = (currentIndex - 1 + totalItems) % totalItems;
                switchToItem(prevIndex);
            }
        }

        // 开始自动播放
        function startAutoPlay() {
            if (autoPlayTimer) {
                clearInterval(autoPlayTimer);
                autoPlayTimer = null;
            }
            // 只有在配置允许且有多个项目时才启动自动播放
            if (config.autoPlay && totalItems > 1) {
                autoPlayTimer = setInterval(nextItem, config.interval);
            }
        }

        // 停止自动播放
        function stopAutoPlay() {
            // 清除自动播放定时器
            if (autoPlayTimer) {
                clearInterval(autoPlayTimer);
                autoPlayTimer = null;
            }
            // 清除重启定时器
            if (restartTimer) {
                clearTimeout(restartTimer);
                restartTimer = null;
            }
        }

        // 安全地重启自动播放
        function restartAutoPlayAfterDelay(delay) {
            stopAutoPlay();

            // 设置延迟重启定时器
            if (config.autoPlay && totalItems > 1) {
                restartTimer = setTimeout(function() {
                    restartTimer = null; // 清除重启定时器引用
                    startAutoPlay(); // 重新开始自动播放
                }, delay);
            }
        }

        // 绑定箭头点击事件，确保阻止事件冒泡
        $('.arrow-left').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            prevItem();
            restartAutoPlayAfterDelay(5000);
            return false;
        });

        $('.arrow-right').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            nextItem();
            restartAutoPlayAfterDelay(5000);
            return false;
        });

        // 阻止箭头区域的所有点击事件冒泡
        $('.carousel-arrows').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            return false;
        });

        // 鼠标悬停时停止自动播放
        if (config.pauseOnHover) {
            $('#carouselContainer').hover(
                function() {
                    // 鼠标进入时停止自动播放
                    stopAutoPlay();
                },
                function() {
                    // 鼠标离开时立即重新开始自动播放（不延迟）
                    if (config.autoPlay && totalItems > 1) {
                        startAutoPlay();
                    }
                }
            );
        }

        // SaaS广告点击事件，阻止冒泡到消费反区域
        $('#saasAdPage').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            trackCarouselClick('saas');

            if (config.saasAdUrl) {
                window.open(config.saasAdUrl, '_blank');
            } else {
                console.log('SaaS广告被点击，但未配置跳转链接');
            }
            return false;
        });

        // SaaS"立即体验"按钮点击事件
        $('.saas-btn').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            const currentItemType = getCurrentItemType();
            trackButtonClick('saas-btn', currentItemType);

            if (config.saasAdUrl) {
                window.open(config.saasAdUrl, '_blank');
            } else {
                console.log('SaaS立即体验按钮被点击，但未配置跳转链接');
            }
            return false;
        });

        // 消费反区域点击事件处理
        $('#rebatePage').off('click').on('click', function(e) {
            // 检查点击的目标是否是箭头或其子元素
            if ($(e.target).closest('.carousel-arrows').length > 0 ||
                $(e.target).closest('.arrow-left').length > 0 ||
                $(e.target).closest('.arrow-right').length > 0) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }

            trackCarouselClick('rebate');

            // 如果不是箭头，执行消费反跳转
            window.location.href = '/marketing/rebateVoucher/consumerRebate.htm';
        });

        // 消费反"立即查看"按钮点击事件
        $('.rebate-look-btn').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            // 在轮播场景下使用当前显示的模块类型
            const currentItemType = getCurrentItemType();
            trackButtonClick('rebate-btn', currentItemType);

            window.location.href = '/marketing/rebateVoucher/consumerRebate.htm';
            return false;
        });

        // 初始化显示第一个项目
        if (totalItems > 0) {
            switchToItem(0);
        }

        // 启动轮播（只有多个项目时才启动）
        if (totalItems > 1) {
            startAutoPlay();
        }
    }
});
//QT埋点三期
//点击再次购买
function action_sub_module_click_orderList(element, order_index, orderNo, statusName){
    try{
        console.log(element,'qiyu')
        if(!element.hasAttribute('data-qt')){
            // element.setAttribute('data-qt', '1');
            let tabs = tabSwitch(document.querySelector('.order-tab-title-item-cur a').text)
            let index = (Number($('#currentPage').val()) - 1)*10 + Number(order_index) + 1
            let spm_cnt = "1_4." + 'orderList_0-0_' + tabs +".orderList@9." + 'orderCard@' + index  + "_btn@2" + '.' +window.getSpmE(); //订单列表再次购买点击埋点
            let scm_cnt = "order.0.all_0." + "order-" + orderNo + '_orderStatus-' + statusName + '_text-再次购买.' +  window.addScmeV3(1);
			window.orderListQtSpm = spm_cnt;
			window.orderListQtScm = scm_cnt;
			aplus_queue.push({
                'action': 'aplus.record',
                'arguments': ['action_sub_module_click', 'CLK',
                {
                    "spm_cnt":spm_cnt,
                    "scm_cnt": scm_cnt
                }
                ]
            });
        }
    }catch(err){
        console.log(err,'qiyu1');
    }
}
var  headerStatus = '';

// 切换
function headerButtonClick(val) {
    headerStatus = '';
    // $("#table-header-buttons-one").removeClass('table-header-buttons-items-selected');
    // $("#table-header-buttons-two").removeClass('table-header-buttons-items-selected');
    // $("#table-header-buttons-three").removeClass('table-header-buttons-items-selected');
    // $("#table-header-buttons-four").removeClass('table-header-buttons-items-selected');
    if(val === ''){
        // $("#table-header-buttons-one").addClass('table-header-buttons-items-selected');
    }else if(val === 6){
        // $("#table-header-buttons-two").addClass('table-header-buttons-items-selected');
        headerStatus ="6";
    }else if(val === 0){
        // $("#table-header-buttons-three").addClass('table-header-buttons-items-selected');
        headerStatus ="0";
    }else{
        // $("#table-header-buttons-four").addClass('table-header-buttons-items-selected');
        headerStatus ="1";
    }
    $(".top-button-mk").click();
}

function clearOrderId() {
	var companyName = $("#searchShopBox").val();
	if (!companyName) {
		$("#searchShopBox").attr('orgid','');
	}

}
//查询全部订单
function findOrders(status){
    var queryStr="";
    if(status){
        queryStr+="&status="+status;
    }
    // // 添加center_menu参数，确保左侧菜单正确显示
    queryStr+="&center_menu=order";
    
    var orderSource = $("#orderSource").val();
    if(orderSource){
        queryStr+="&orderSource="+orderSource;
    }
    var orderNo = $("#orderNo").val();
    if(orderNo){
        queryStr+="&orderNo="+orderNo;
    }
    var drugName = $("#drugName").val();
    if(drugName){
        queryStr+="&name="+drugName;
    }
    var channelCode = $("#channelCode").val();
    if(channelCode){
        queryStr+="&channelCode="+channelCode;
    }
    var startCreateTime = $("#startCreateTime").val();
    if(startCreateTime){
        queryStr+="&startCreateTime="+startCreateTime + " 00:00:00";
    }
    var endCreateTime = $("#endCreateTime").val();
    if(endCreateTime){
        queryStr+="&endCreateTime="+endCreateTime + " 23:59:59";
    }
    var invoiceState = $("#invoiceState").val();
    if (invoiceState) {
        queryStr+="&invoiceState="+invoiceState;
    }

    // 如果是退货，那么这里添加afterSalesProcessState
    if(status == '89'){
        queryStr+='&afterSalesProcessState='+headerStatus;
    }

    var companyName = $("#searchShopBox").val();
    if (companyName) {
        queryStr+="&companyName="+companyName;
        var orgId = $("#searchShopBox").attr('orgid');
        if (orgId) {
            queryStr+="&orgId="+orgId;
        }
    }

    var index=layer.load(2);
    location.href="/merchant/center/order/index.htm"+(queryStr!=""?"?"+queryStr.substring(1):"");
}
// 判断时间跨度
function completeDate (time1, time2, m) {
	var year1 = time1.getFullYear()
	var year2 = time2.getFullYear()
	var month1 = time1.getMonth() + 1
	var month2 = time2.getMonth() + 1
	var day1 = time1.getDate()
	var day2 = time2.getDate()
	if (year2 === year1) { // 判断两个日期 是否是同年
		if (month2 - month1 > m) { // 相差是否 在m个月中
			return false
		} else if (month2 - month1 === m) { // 如果正好为 m月 判断天数
			if (day2 > day1) {
				return false
			}
		}
	} else { // 不同年
		if (year2 - year1 > 1) {
			return false
		}
		else if (year2 - year1 === 1) {
			if (month2 > m || month1 + m - month2 < 12) {
				return false
			}
			else if (month1 + m - month2 === 12) { // 正好为m月 判断天数
				if (day2 > day1) {
					return false
				}
			}
		}
	}
	return true
}

/**
 * 取消订单
 * @param id
 * @param status 一般没用,只是用来做配送中,取消订单的提示作用
 */
function getCancelReason(orderNo){
	$.ajax({
		type: "POST",
		url:  "/merchant/center/order/findRefundReason?orderStatus=4&orderNo="+orderNo,
		contentType: "application/json",
		dataType: "json",
		success: function (data) {
			if (data.status === "success") {
				var reasonList = data.reasonList;
				var reasonStr = '';
				if(reasonList && reasonList.length > 0){
					reasonList.forEach(function(item){
						reasonStr += '<option value="' + item.showText + '">' + item.showText + '</option>'
					})
					$(".reason-box").html(reasonStr);
				}
			}
		}
	});
}
var shopList = [];
// 查询店铺名称
function getAllShops() {
	var param= {
		"status" : $.trim(status),
		"orderNo" : $.trim($("#orderNo").val()),
		"orderSource" : $.trim($("#orderSource").val()),
		"startCreateTime" : $.trim($("#startCreateTime").val()),
		"endCreateTime" : $.trim($("#endCreateTime").val()),
		"orgId" : $.trim($("#searchShopBox").attr('orgid')),
		"invoiceState" : $.trim($("#invoiceState").val()),
		"name" : $.trim($("#drugName").val())
	};


	$.ajax({
		type: "POST",
		url:  "/merchant/center/order/queryOrderOrg?status"+$.trim(status)+"&orderNo="+$.trim($("#orderNo").val())+"&orderSource="+$.trim($("#orderSource").val())+"&startCreateTime="+$.trim($("#startCreateTime").val())+"&endCreateTime="+$.trim($("#endCreateTime").val())+"&orgId="+$.trim($("#searchShopBox").attr('orgid'))+"&invoiceState="+$.trim($("#invoiceState").val())+"&name="+$.trim($("#drugName").val()),
		contentType: "application/json",
		data: {},
		dataType: "json",
		success: function (data) {
			if (data.status === "success") {
				shopList = data.data;
			}else{
				$.alert({"title":"提示","body":"只允许查询下单时间区间在3个自然月内的订单"});
			}
		}
	});
}

function filterShopList(name) {
	var tempList = [];
	if (name) {
		tempList = shopList.filter(function(i){
			return i.companyName.indexOf(name) > -1;
		});
	} else {
		tempList = shopList
	}
	console.log('tempList', tempList)
	var html = '';
	$.each(tempList, function (index, item) {
		html += '<li orgid="' + item.orgId + '" companyName="' + item.companyName + '">' + item.companyName + '</li>';

		// html += "<li><span orgid=" + item.orgId + "companyName=" + item.companyName + ">" + item.companyName + "</span></li>";
	})
	if (tempList.length > 0) {
		$("#searchShopUl").html(html);
		$('#searchShopUl').css("display", "block");
	} else {
		$('#searchShopUl').css("display", "none");
	}
}


function closeCancelOrder(){
	$(".reason-box").val("");
	$(".reason-tip").hide();
	$("#cancelOrderDialog").modal("hide");
}
function closeReminderShipment(){
	$(".reason-box").val("");
	$(".reason-tip").hide();
	$("#reminderShipment").modal("hide");
	window.location.reload();
}
//提醒发货
function reminderShipment(orderNo,merchant,orgId,isThirdCompany,origName){
	$(".closeOrderBox1").click(closeReminderShipment);
	$.ajax({
		type: "POST",
		url:  "/merchant/center/order/reminder/submit?orderNo=" +orderNo,
		contentType: "application/json",
		dataType: "json",
		success: function (data) {
			if (data.status === "success") {
				$("#reminderShipmentBody").text(data.msg)
				$("#reminderShipmentB").off('click').on('click', function() {
					callKf(orderNo,merchant,orgId,isThirdCompany,origName);
					closeReminderShipment();
				});
				// let a = `<a  id="reminderShipmentA" href="javaScript:callKf('${orderNo}','${merchant}','${orgId}','${isThirdCompany}','${origName}');" style="color: black;">联系商家</a>`;
				// $("#reminderShipmentA").html(a);
				$("#reminderShipment").modal("show");
			}else{
				// $("#reminderShipmentBody").text(data.msg)
				// $("#reminderShipment").modal("show");
				layer.msg(data.msg, {
					time: 2000,
				});
				window.location.reload();
			}
		}
	});
}
function cancelOrder(obj,id,status, orderNo){
	if(status==2){
		$.alert({"title":"提示","body":"您的订单已在配送中哦，为了及时响应订单拦截与配送的及时性，请您及时联系官方客服进行操作，谢谢您的配合！"});
		return;
	}
	$("#cancelOrderDialog").modal("show");
	getCancelReason(orderNo);
	$(".reason-box").val("");
	$(".reason-tip").hide();
	$(".closeOrderBox").click(closeCancelOrder);
	$("#sure-btn").click(function(){
		var reason = $(".reason-box").val();
		if(reason){
			$.ajax({
				type: "POST",
				url:  "/merchant/center/order/cancelOrder.json?id=" + id + "&cancelReason=" + reason,
				contentType: "application/json",
				dataType: "json",
				success: function (data) {
					if (data.status === "success") {
						$("#cancelOrderDialog").modal("hide");
						$.alert({
							"title": "提示", "body": "取消成功！", okHidden: function () {
								window.location.reload();
							}
						});
					}else{
						$("#cancelOrderDialog").modal("hide");
						$.alert({
							"title": "提示", "body": data.errorMsg == null ? "取消失败！" : data.errorMsg, okHidden: function () {
								window.location.reload();
							}
						});
					}
				}
			});
		}else{
			$(".reason-tip").show();
		}
	});
	// $.confirm({
	// 	body: "确定取消订单?",
	// 	okBtn : '确认取消',
	// 	cancelBtn : '暂不取消',
	// 	okHidden:function(){
	// 		$.ajax({
	// 			type: "POST",
	// 			url:  "/merchant/center/order/cancelOrder.json?id=" + id,
	// 			contentType: "application/json",
	// 			dataType: "json",
	// 			success: function (data) {
	// 				if (data.status === "success") {
	// 					$.alert({
	// 						"title": "提示", "body": "取消成功！", okHidden: function () {
	// 							window.location.reload();
	// 						}
	// 					});
	// 				}else{
	// 					$.alert({
	// 						"title": "提示", "body": "取消失败！", okHidden: function () {
	// 							window.location.reload();
	// 						}
	// 					});
	// 				}
	// 			}
	// 		});
	// 	},
	// 	cancelHidden: function(){console.log('点击取消')}
	// })
}
/**
 * 申请退款
 * @param id
 * @param status 一般没用,只是用来做配送中,取消订单的提示作用
 */
function applyReturn(obj,id,status,payType,balanceStatus,isThirdCompany){
	if(status==3 && payType==2){
		$.alert({"title":"提示","body":"您的订单已完成完成，如需申请退款，请您及时联系官方客服进行操作，谢谢您的配合！"});
		return;
	}
	if(isThirdCompany == 0){
		if(status==3 && balanceStatus==1){
			$.alert('您已领取余额，已不能发起申请退款，请知悉！');
			return;
		}
		var flag = true;
		// 判断是否618订单
		$.ajax({
			type: "POST",
			url:  "/merchant/center/order/order618Prom/orderRefund?orderId=" + id,
			contentType: "application/json",
			async: false,
			dataType: "json",
			success: function (data) {
				if(data){
					if(data.status == 'failure' ){
						$.alert(data.errorMsg);
						flag = false;
					}
				}
			}
		});
		if(!flag){
			return;
		}
		//已经领取余额 申请退款弹框
		$.ajax({
			type: "POST",
			url:  "/merchant/center/order/getOrderInfo.json?orderId=" + id,
			contentType: "application/json",
			dataType: "json",
			success: function (data) {
				if(data){
					var order = data.order;

					if(order.status==3 && order.orderExtend.balanceStatus==1){
						$.alert('您已领取余额，已不能发起申请退款，请知悉！');
						return;
					}
				}
			}
		});

		//山西域7月25日前订单限制申请退款
		var flag = true;
		$.ajax({
			type: "POST",
			url:  "/merchant/center/order/limitedOrderRefund?orderId=" + id,
			contentType: "application/json",
			async: false,
			dataType: "json",
			success: function (data) {
				if(data){
					if(data.status == 'failure' ){
						$.alert(data.errorMsg);
						flag = false;
					}
				}
			}
		});
		if(!flag){
			return;
		}

		if(status==7){
			// 判断是否618订单
			$.ajax({
				type: "POST",
				url:  "/merchant/center/order/canRefund?orderId=" + id,
				contentType: "application/json",
				async: false,
				dataType: "json",
				success: function (data) {
					if(data){
						if(data.status === 'failure' ){
							$.alert(data.errorMsg);
							flag = false;
						}
					}
				}
			});
			if(!flag){
				return;
			}
			// $.confirm({"title":"提示","body":"您发起的退款订单目前只支持整单退款，若其中使用了优惠券，也将不予退还，请您确认是否提交退款"});
			$.confirm({
				body: "您发起的退款订单目前只支持整单退款，订单中的相关优惠活动将在退款发起后无法退还，请您确认是否提交退款。<br>" +
					"注：目前您的订单已在仓库拣货打包中，拦截订单有风险，若拦截失败，订单会正常发货，请您随时关注订单拦截情况，若有疑问可联系客服。",
				cancelBtn : '取消',
				okBtn : '确认',
				okHidden:function(){
					layer.load(2);
					location.href="/merchant/center/order/applyRefund.htm?id="+id;
					// location.href="/merchant/center/order/selectServiceType.htm?id="+id;
				}
				// ,
				// cancelHidden: function(){}
			});
			return;
		}else if(status == 1){
			// $.confirm({"title":"提示","body":"您发起的退款订单目前只支持整单退款，若其中使用了优惠券，也将不予退还，请您确认是否提交退款"});
			$.confirm({
				body: "若您申请的退款订单中使用了优惠券，部分优惠券可能无法返还，请您确认是否继续申请退款",
				cancelBtn : '取消',
				okBtn : '确认',
				okHidden:function(){
					layer.load(2);
					location.href="/merchant/center/order/applyRefund.htm?id="+id;
					// location.href="/merchant/center/order/selectServiceType.htm?id="+id;
				}
				// ,
				// cancelHidden: function(){}
			});
			return;
		}
	}
	var index=layer.load(2);
	location.href="/merchant/center/order/applyRefund.htm?id="+id;
	// location.href="/merchant/center/order/selectServiceType.htm?id="+id;
}
/**
 * 再次购买事件
 * @param id
 */
function nowBuy(id,dingdanCode){
	let index=layer.load(2);
	var sid = '',
		sptype = 5,
		spid = dingdanCode,
		direct = 4;
	//获取sid
	$.ajax({
		type: "GET",
		url:  "/merchant/center/order/getOrderStockState",
		contentType: "application/json",
		async: false,
		data:{
			orderNo:dingdanCode,
		},
		dataType: "json",
		success: function (data) {
			if(data){
				if(data.status == 'success'){
					layer.close(index);
					if(data.data.stockCode == 1){
						var paramsData={
							orderNo:dingdanCode,
							availableWareList:data.data.availableWareList,
						}
						if(window.orderListQtSpm&&window.orderListQtScm){
							paramsData.qtdata=JSON.stringify({
								spm_cnt:window.orderListQtSpm,
								scm_cnt:window.orderListQtScm,
							})
						}
						paramsData = JSON.stringify(paramsData);
						$.ajax({
							type: "POST",
							url:  "/merchant/center/order/cartAdd",
							contentType: "application/json",
							dataType: "json",
							data:paramsData,
							success: function (data) {
								if (data.status === "success") {
									location.href="/merchant/center/cart/index.htm";
								}else{
									$.alert({"title": "提示", "body": data.data.message});
								}
							}
						});
					}else if(data.data.stockCode == 3){
						if(data.data.noAvailableWareList.length > 1){
							$.confirm({
								title: '温馨提示',
								body: data.data.message,
								cancelBtn: false, // 隐藏取消按钮
								okHidden : function(e){
									location.href="/index.htm";
								},
							}); 
						}else{
							$.alert({"title":"温馨提示","body":data.data.message});
						}
					}else if(data.data.stockCode == 4){
						window.open(`/search/skuDetail/${data.data.availableWareList[0].skuId}.htm`)
					}else if(data.data.stockCode == 2){
						newBuyOrder(dingdanCode,data.data.availableWareList,data.data.noAvailableWareList)
					}
				}else if(data.status === "failure"){
					layer.close(index);
					$.alert({"title":"温馨提示","body":data.errorMsg});
					return;
				}
			}
		}
	});
}
/**再次购买弹窗打开关闭 */
function closeBuyOrder(){
	$("#buyAganDialog").modal("hide");
	$('body').css('overflow', '');
}
/**再次购买弹窗打开*/
function newBuyOrder(dingdanCode,availableWareList, noAvailableWareList){
	$("#buyAganDialog").modal("show")
	$(".closebuyBox").click(closeBuyOrder);
	$(".sui-modal-backdrop").click(closeBuyOrder);
	var productImageUrl = $("#productImageUrl").val();
	let htmlDom = '';
	noAvailableWareList.forEach((item,index)=>{
		let dom = `<div class="${index == 0 ? 'product-card-top' : 'product-card-bottom'}">
				<div class="img-boder">
					<img src="/static/images/product/shixiao.png" alt="" class="shixiao-image">
					<img src="${productImageUrl}/ybm/product/min/${item.imageUrl}" alt="${item.productName}" class="product-image">
				</div>
				<div style="text-align: left;">
					<div class="product-name">${item.productName}</div>
					<div class="product-effect">效期: ${item.effect}</div>
					<div style="display:flex;text-align: left;align-items: center;margin: 10px 0;">
						<div class="product-price">¥${item.productQuantity}元/${item.productUnit}</div>
						${item.productOriginPrice ? `<div class="product-origin-price">原价: ¥${item.productOriginPrice}</div>` : ''}
					</div>
				</div>
            </div>`;
		htmlDom += dom;
	})
	$('#buyAgan').html(htmlDom);
	$('body').css('overflow', 'hidden');
	$("#buy-sure-btn").click(function(){
		$('body').css('overflow', '');
		var paramsData = {
			orderNo:dingdanCode,
			availableWareList:availableWareList,
		};
		if(window.orderListQtSpm&&window.orderListQtScm){
			paramsData.qtdata=JSON.stringify({
				spm_cnt:window.orderListQtSpm,
				scm_cnt:window.orderListQtScm,
			})
		}
		paramsData = JSON.stringify(paramsData);
		$.ajax({
			type: "POST",
			url:  "/merchant/center/order/cartAdd",
			contentType: "application/json",
			dataType: "json",
			data:paramsData,
			success: function (data) {
				if (data.status === "success") {
					location.href="/merchant/center/cart/index.htm";
				}else{
					$.alert({"title": "提示", "body": data.data.message});
				}
			}
		});
	});
}
/**
 * 详情单条再次购买事件
 * @param id
 */
function nowBuyDetail(orderNo,skuId,productAmount){
	var index=layer.load(2);
	//获取sid
	$.ajax({
		type: "GET",
		url:  "/merchant/center/order/getOrderStockState",
		contentType: "application/json",
		async: false,
		data:{
			orderNo:orderNo,
			skuId:skuId,
			productQuantity:productAmount,
		},
		dataType: "json",
		success: function (data) {
			if(data){
				if(data.status == 'success'){
					layer.close(index);
					if(data.data.stockCode == 1){
						$.ajax({
							type: "POST",
							url:  "/merchant/center/order/cartAdd",
							contentType: "application/json",
							dataType: "json",
							data:JSON.stringify({
								orderNo:orderNo,
								availableWareList:[
									{
										skuId:skuId,
										productQuantity:productAmount,
									}
								]
							}),
							success: function (data) {
								if (data.status === "success") {
									location.href="/merchant/center/cart/index.htm";
								}else{
									layer.close(index);
									$.alert({"title": "提示", "body": data.data.errorMsg});
								}
							}
						});
					}else if(data.data.stockCode == 3){
						$.alert({"title":"温馨提示","body":data.data.message});
					}else if(data.data.stockCode == 4){
						window.open(`/search/skuDetail/${data.data.availableWareList[0].skuId}.htm`)
					}
				}else if(data.status === "failure"){
					layer.close(index);
					$.alert({"title":"温馨提示","body":data.errorMsg});
					return;
				}
			}
		}
	});
}

// //领取余额
function selectOrderBalance(orderId,balanceText){
	if (balanceText == ""){
		$.alert({"title":"提示","body":"订单退款中，无法领取余额！"});
		return false;
	}
	$.confirm({
		body:balanceText,
		okBtn : '同意并领取',
		cancelBtn : '暂不领取',
		okHidden:function(){
			$.ajax({
				type: "POST",
				url:  "/merchant/center/balanceJournal/addBalance.json?orderId=" + orderId,
				contentType: "application/json",
				dataType: "json",
				success: function (data) {
					$("#mol-money").text(data.BalanceJournal);
					if (data.status == "success") {
						$("#successModel").modal('show');
						setTimeout(function(){
							$("#successModel").modal('hide');
							if($(".query-btn")){
								$(".query-btn").click();
							}else{
								$(".top-button-mk").click();
							}
						},3000);
					}else{
						$.alert({"title":"提示","body":"订单退款中，无法领取余额！"});
					}
				}
			});
		},
		cancelHidden: function(){console.log('点击取消')}
	})
}

// //确认收货
function confirmOrderGoods(orderId){
	$.confirm({
		body:"请收到商品后，再确认收货，否则您将可能钱货两空！",
		cancelBtn : '暂未收货',
		okBtn : '我已收货',
		okHidden:function(){
			$.ajax({
				type: "POST",
				url:  "/merchant/center/order/confirmOrderGoods.json?orderId=" + orderId,
				contentType: "application/json",
				dataType: "json",
				success: function (data) {
					if (data.status == "success") {
						if (data.gainVoucher) {
							$.confirm({
								body: data.gainVoucher,
								cancelBtn : '取消',
								okBtn : '去查看',
								okHidden:function(){
									location.href =  "/merchant/center/voucher/findAllVoucherInfo.htm";
								}
							});
						} else {
							$.alert({
								"title": "提示", "body": "确认收货成功！", okHidden: function () {
									window.location.reload();
								}
							});
						}
					}else{
						$.alert({"title":"提示","body":"订单已确认收货或处于退款中,请先进行确认！", okHidden:function () {
								window.location.reload();
							}});

					}
				}
			});
		},
		cancelHidden: function(){console.log('点击取消')}
	})

}



//查看发票
function showInvoince(orderId,orderNo,billType, fromType){
	if (fromType === 'orderList') {
		webSdk.track('pc_action_purchaseOrder_viewInvoice', {
			'text' : '查看发票',
		});
	}

	var html = "";
	var html1 = "";

	var beginHtml01 = "<table class='fptable' id='fpTable'>";
	var beginHtml = "<tr class='head'>";
	beginHtml +=	"<td>发票类型</td>";
	beginHtml +=	"<td>开票日期</td>";
	beginHtml +=	"<td>发票号码</td>";
	beginHtml +=	"<td>税额</td>";
	beginHtml +=	"<td>价税合计</td>";
	beginHtml +=	"<td>发票状态</td>";
	beginHtml +=	"</tr>";

	var endHtml = "</table>";

	$.ajax({
		type : "post",
		url : "/merchant/center/order/showInvoince.json",
		data : {"orderId":orderId},
		async : false,
		dataType : "json",
		success : function(data,textStatus){
			var json = eval(data);
			var self = 1;
			if(json.invoinceInfo != null && json.invoinceInfo.result.length > 0){
				var i = 0;
				$.each(json.invoinceInfo.result,function(index,item){
					if( item.isThirdCompany == 0 || item.isFbp == 1){
						self =0;
						if (item.pdfurl) {
							item.pdfurl = item.pdfurl.replace(/^https?\:/i, '');
						}
						if(i%2==0){
							html += "<tr class='odd'>";
						}else{
							html += "<tr class='even'>";
						}
						html += "<td>"+item.billInfo+"</td>";
						html += "<td>"+item.createDate+"</td>";
						if(item.billType==1){
							html += "<td><a href='"+item.pdfurl+"' target='_blank'>"+item.fapiaohaoma+"</a></td>";
						}else{
							html += "<td>"+item.fapiaohaoma+"</td>";
						}
						html += "<td>"+item.shuie+"</td>";
						html += "<td>"+item.hanshuijine+"</td>";
						if(item.fapiaozhuangtai==0){
							html += "<td><span class='youxiao'>无效</span></td>";
						}else if(item.fapiaozhuangtai==1){
							html += "<td><span class='youxiao'>有效</span></td>";
						}else{
							html += "<td><span class='youxiao'>暂无</span></td>";
						}
						html += "</tr>";
					}else {
						html1+="<div class='divBox'><span> </span> <a href='"+item.pdfurl+"' target='_blank'> 发票"+(index+1)+"</a></div>"
					}
					$("#invoice-down-btn").show();
					$("#invoice-down-share").show();
				});
			}else{
				$("#invoice-down-btn").hide();
				$("#invoice-down-share").hide();

				html += "<tr class='odd'>";
				html += "<td colspan='6'>发票暂未生成，请稍后再试</td>";
				html +="</tr>";
			}
			// var showHtml = beginHtml01+beginHtml + html + endHtml;
			// $("#fapiaoDiv").html(showHtml);
			if(json.invoinceInfo != null && (json.invoinceInfo.isThirdCompany == 0 || self ==0)){
				var showHtml = beginHtml01+beginHtml + html + endHtml;
				$("#fapiaoDiv").html(showHtml);
			}else {
				$("#fapiaoDiv").html(beginHtml01+html+html1);
			}
		},
		error:function(XMLHttpRequest, textStatus, errorThrown){
			$("#invoice-down-btn").hide();
			$("#invoice-down-share").hide();
			html += "<tr class='odd'>";
			html += "<td colspan='5'>网络异常，请稍后再试...</td>";
			html +="</tr>";
			var showHtml = beginHtml + html + endHtml;
			$("#fapiaoDiv").html(showHtml);

		}
	});
	/* if (billType && billType === 3) {
		$("#invoice-down-btn").hide();
		$("#invoice-down-share").hide();
	} */
	$("#orderId").val(orderId);
	$("#showInvoiceOrder").html(orderNo);
	$("#invoiceOrderNo").val(orderNo);
	$("#fpxzTc").modal('show');
}
function showContract(name, url) {
	if (url) {
		//页面加载完曝光资质埋点
        webSdk.track('action_order_contract', {
            'pageName': window.document.title
        });
		window.open(url, "_blank");
		return false;
	} else {
		$.alert({"title":"温馨提示","body": "暂未生成购销合同，请稍后重试"});
	}
}
var canSend = false;
function checkEmail() {
	var emailUrl = $("#emailUrl").val();
	if(!/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test($.trim(emailUrl))) {
		$.alert({"title":"提示","body":"邮箱地址格式有误，请重新填写", okHidden:function () {
				canSend = false;
			}});
	} else {
		canSend = true;
	}
}
let emailLoading = false;
// 分享至邮箱
function sendEmail() {
	checkEmail();
	if (emailLoading) return;
	emailLoading = true;
	if (canSend) {

		$.ajax({
			type: "POST",
			// url: "/merchant/center/order/exportOrdersCount",
			url: "/merchant/center/order/sendInvoiceEmail",
			data: {
				"orderId" : $.trim($("#orderId").val()),
				"email" : $.trim($("#emailUrl").val())
			},
			dataType: "json",
			success: function (data) {
				emailLoading = false;
				if (data.status == "failure") {
					parent.layer.msg(data.errorMsg);
				} else if (data.status == "success") {
					webSdk.track('pc_action_purchaseOrder_invoice_downloadToMailbox', {
						'text' : '发送邮箱',
					});
					parent.layer.msg("发送成功");
				}
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				emailLoading = false;
				parent.layer.msg('网络异常');
				top.layer.close(index);
			}
		});
	}
}
function noThirdCompanydownloadZip(dataList, zipName){ // 自营
	const zip = new JSZip();  // 创建JSZip实例用于操作Zip文件
	const promises = [];  // 用于存储下载文件的Promise数组
	// if (type == 1) {
		dataList.forEach(item => {
			const promise = getFile(item.signatureImageUrl).then(data => {
				const suffix = item.signatureImageUrl.split(".")[item.signatureImageUrl.split(".").length - 1];
				zip.file(item.enclosureName + '.' + suffix, data, { binary: true });  // 将文件逐个添加到Zip文件
			});
			promises.push(promise);
		})
	// } else if(type == 2) {
	// 	dataList.forEach(item => {
	// 		item.value.forEach(res => {
	// 			const promise = getFile(res.url).then(data => {
	// 				const suffix = res.url.split(".")[res.url.split(".").length - 1];
	// 				zip.folder(item.name).file(res.name + '.' + suffix, data, { binary: true });  // 将文件逐个添加到Zip文件
	// 			});
	// 			promises.push(promise);
	// 		})
	// 	})
	// }
	 // 等待所有文件处理完成
	 Promise.all(promises).then(() => {
		zip.generateAsync({type: "blob"}).then(function (content) {
            let time = new Date() * 1;
			saveAs(content, `${zipName}.zip`);
			processBar = 100;
			$('#progress_rate').css('width',Math.floor(processBar)+"%");
			$('#progress_text').html(Math.floor(processBar)+"%");
			clearInterval(timerProgress);
			setTimeout(function(){
				$('#progressModel').modal('hide');
				$('#progress_rate').css('width',"0%");
				$('#progress_text').html("0%");
				processBar = 0;
			},1000)
		})
	})
}
var FunLib = {
	// 图片打包下载
	download: function (orderNo,images,downloadType) {
		FunLib.packageImages(orderNo,images,downloadType)
	},
	// 打包压缩图片
	packageImages: function (orderNo,imgs,downloadType) {
		var imgBase64 = {};
		var imageSuffix = []; // 图片后缀
		var zip = new JSZip();
		var zipName = "商品资质".concat(orderNo);
		var img = zip.folder(zipName);
		var convertCount = 0;
		$(imgs).each(function () {
			var src = this.signatureImageUrl
			var suffix = src.substring(src.lastIndexOf("."))
			imageSuffix.push(suffix)
			FunLib.getBase64(src, this.uniqueId).then(function (uniqueId, base64) {
				imgBase64[uniqueId] = base64.substring(22);
				convertCount++;
				if (imgs.length === convertCount) {
					for (var i = 0; i < imgs.length; i++) {
						// img.folder(imgs[i].brand.concat(" ").concat(imgs[i].productName)).file(imgs[i].enclosureName + imageSuffix[i], imgBase64[imgs[i].uniqueId], {base64: true})
						img.folder(imgs[i].brand.concat(" ").concat(imgs[i].productName) + "_" + imgs[i].oldProductCode).file(imgs[i].enclosureName + imageSuffix[i], imgBase64[imgs[i].uniqueId], {base64: true})
					}
					zip.generateAsync({type: "blob"}).then(function (content) {
						saveAs(content, zipName.concat(".zip"));
						//下载日志
						$.ajax({
							type: "POST",
							url: "/merchant/center/order/saveOrderDownloadLog",
							data: {
								"orderNo" : orderNo,
								"downloadType" : downloadType
							},
							dataType: "json",
							async: false,
							success: function (data) {
								if(data.status === "failure"){
									$.alert({"title":"温馨提示","body": data.errorMsg});
									return;
								}
							},
							// function (XMLHttpRequest, textStatus, errorThrown) {
							// 	parent.layer.msg('网络异常');
							// }
						});
						processBar = 100;
						$('#progress_rate').css('width',Math.floor(processBar)+"%");
						$('#progress_text').html(Math.floor(processBar)+"%");
						clearInterval(timerProgress);
						setTimeout(function(){
							$('#progressModel').modal('hide');
							$('#progress_rate').css('width',"0%");
							$('#progress_text').html("0%");
							processBar = 0;
						},1000)
					})
				}
			}, function (err) {
				console.log(err)
			})
		});
	},
	// 传入图片路径，返回base64
	getBase64: function (img, uniqueId) {
		var image = new Image();
		image.crossOrigin = 'Anonymous';
		image.src = img;
		var canvas = document.createElement('canvas');
		var img = document.createElement('img');
		var deferred = $.Deferred()
		if (img) {
			image.onload = function () {
				var canvas = document.createElement("canvas");
				canvas.width = image.width * 0.6;
				canvas.height = image.height * 0.6;
				var ctx = canvas.getContext("2d");
				ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
				var dataURL = canvas.toDataURL();
				deferred.resolve(uniqueId, dataURL);
			}
			return deferred.promise();
		}
	}
}
var processBar = 0;
var timerProgress;
// 商品资质下载
function getSignaturesDownload(orderNo, merchantId){
	if (time == 0) {
		time = 2; //设定间隔时间（秒）
		var timer = setInterval(function(){
			time--;
			if (time == 0) {
				clearInterval(timer);
			}
		}, 1000);
	}else{
		return;
	}
	var layer_msg = layer.msg('查询中，请稍等...',{
		time:0
	});
	// 根据订单号查询明细
	$.ajax({
		type: "POST",
		url: "/merchant/center/order/getSignaturesDownload",
		data: {
			"orderNo" : orderNo,
			"merchantId" : merchantId
		},
		dataType: "json",
		async: false,
		success: function (data) {
			layer.close(layer_msg);
			if(data.status === "failure" || !data.data){
				$.alert({"title":"温馨提示","body":"当前订单商品暂未上传相关资料，请联系客服人员进行咨询。客服电话：400-0505-111"});
				return;
			}
			// parent.layer.msg('正在为您下载商品资质文件，请稍候...');
			$('#progressModel').modal('show');
			processBar = 0;
			timerProgress = setInterval(function(){
				if(processBar < 90){
					processBar += 10;
					$('#progress_rate').css('width',Math.floor(processBar)+"%");
					$('#progress_text').html(Math.floor(processBar)+"%");
				}
			}, 1000);
			// miao()
			// FunLib.download(orderNo, data.data, 3);
			var signaturesZipName = "商品资质".concat(orderNo);
			noThirdCompanydownloadZip(data.data, signaturesZipName);
		},
		// function (XMLHttpRequest, textStatus, errorThrown) {
		// 	parent.layer.msg('网络异常');
		// }
	});
}
var processBarPOP = 0;
var timerProgressPOP;
// 自营商品资质下载
function getSignaturesDownloadPOP(orderNo, orgId,merchant,isThirdCompany,origName){
	if (time == 0) {
		time = 2; //设定间隔时间（秒）
		var timer = setInterval(function(){
			time--;
			if (time == 0) {
				clearInterval(timer);
			}
		}, 1000);
	}else{
		return;
	}
	var layer_msg = layer.msg('查询中，请稍等...',{
		time:0
	});
	// 根据订单号查询明细
	$.ajax({
		type: "POST",
		url: "/merchant/center/order/downPopFirstSaleQualification",
		data: {
			"orderNo" : orderNo,
			"merchantId" : merchant
		},
		dataType: "json",
		async: false,
		success: function (data) {
			layer.close(layer_msg);
			if(data.status === "failure" || !data.data){
				$.alert({"title":"温馨提示","body":data.errorMsg});
				return;
			}else{
				if(data.data.failCount >0 || data.data.successCount == 0){
					let isCancelClicked = true;
					$.confirm({
						title:'温馨提示',
						body: data.data.successCount > 0 ? `${data.data.failCount}个商品不支持下载资质文件，${data.data.successCount}个商品可下载资质文件，是否继续下载？`:'全部商品不支持下载资质文件。',
						okBtn : '确认',
						cancelBtn : `<a href="javaScript::void(0),);" id="cancelBtn">联系客服</a>`,
						okHidden:function(){
							if(data.data.successCount > 0){
								downloadZip(data.data,'商品资质',orderNo,data.data.failCount)
								$('#progressModel').modal('show');
								processBarPOP = 0;
								timerProgressPOP = setInterval(function(){
									if(processBarPOP < 90){
										processBarPOP += 10;
										$('#progress_rate').css('width',Math.floor(processBarPOP)+"%");
										$('#progress_text').html(Math.floor(processBarPOP)+"%");
									}
								}, 1000);
							}else{
								 // 关闭弹窗
								 $(this).closest('.modal').modal('hide'); // 或者 .remove()
							}
							// FunLib.download(orderNo, data.data, 3);
						},
						cancelHidden: function() {
						if (isCancelClicked) {
								// callKf(orderNo,merchant); // '${orderNo}','${merchant}'
								callKf(orderNo,merchant,orgId,isThirdCompany,origName);
							}
						},
						// 标记取消按钮点击
						onCancelClick: function() {
							isCancelClicked = true;
						}
					})
					$(document).on('click', '.modal-header .sui-close', function() {
						isCancelClicked = false;
					});
				}else{
					downloadZip(data.data,'商品资质',orderNo);
					$('#progressModel').modal('show');
					processBarPOP = 0;
					timerProgressPOP = setInterval(function(){
						if(processBarPOP < 90){
							processBarPOP += 10;
							$('#progress_rate').css('width',Math.floor(processBarPOP)+"%");
							$('#progress_text').html(Math.floor(processBarPOP)+"%");
						}
					}, 1000);
				}
			}
			// parent.layer.msg('正在为您下载商品资质文件，请稍候...');
			// miao()
		},
		// function (XMLHttpRequest, textStatus, errorThrown) {
		// 	parent.layer.msg('网络异常');
		// }
	});
}
function getFile(url){
	return new Promise((resolve, reject) => {
		const xhr = new XMLHttpRequest();
		xhr.open('GET', url, true);
		xhr.responseType = 'arraybuffer';
		
		xhr.onload = function() {
		  if (xhr.status === 200) {
			resolve(xhr.response);
		  } else {
			reject(`Error: ${xhr.status}`);
		  }
		};
	
		xhr.onerror = function() {
		  reject("Network Error");
		};
	
		xhr.send();
	});
};
/**
 * 
 * @param {*} data 
 * @param {*} failCount 
 */
function downloadZip(data,name,orderNo,failCount){
	const zip = new JSZip();  // 创建JSZip实例用于操作Zip文件
	var zipName = name.concat(orderNo);
	const cache = {};  // 缓存已下载文件内容
	const promises = [];  // 用于存储下载文件的Promise数组
	data.productInfoList.forEach(item => {
		if(item.cosImageSealUrlList && item.cosImageSealUrlList.length > 0){
			item.cosImageSealUrlList.forEach(it=>{
				const promise = getFile(it.url).then(data => {
					const fileName = it.name+'.'+it.url.substring(it.url.lastIndexOf('.') + 1);
					let batchCode = item.batchCode? ('_' + item.batchCode) : '';
					zip.folder(item.productName + '_' + item.barcode + batchCode ).file(fileName, data, { binary: true });  // 将文件逐个添加到Zip文件
					cache[fileName] = data;  // 缓存文件内容
				});
				promises.push(promise);
			})
		}
	})
	if(failCount && failCount > 0){
		let row = data.reasonExcel
		const promise = getFile(row.url).then(data => {
			const fileName = name == '药检报告' ?`药检报告异常商品-${orderNo}.xlsx`: `资质异常商品-${orderNo}.xlsx`;
			zip.file(fileName, data, { binary: true });  // 将文件逐个添加到Zip文件
			cache[fileName] = data;  // 缓存文件内容
		});
		promises.push(promise);
	}
	 // 等待所有文件处理完成
	 Promise.all(promises).then(() => {
		// 生成Zip文件并保存
		// zip.generateAsync({ type: 'blob' }).then(content => {
		//   FileSaver.saveAs(content, fileName);
		// });
		zip.generateAsync({type: "blob"}).then(function (content) {
			saveAs(content, zipName.concat(".zip"));
			//下载日志
			$.ajax({
				type: "POST",
				url: "/merchant/center/order/saveOrderDownloadLog",
				data: {
					"orderNo" : orderNo,
					"downloadType" : 3,
				},
				dataType: "json",
				async: false,
				success: function (data) {
					if(data.status === "failure"){
						$.alert({"title":"温馨提示","body": data.errorMsg});
						return;
					}
				},
				
				// function (XMLHttpRequest, textStatus, errorThrown) {
				// 	parent.layer.msg('网络异常');
				// }
			});
			processBarPOP = 100;
			$('#progress_rate').css('width',Math.floor(processBarPOP)+"%");
			$('#progress_text').html(Math.floor(processBarPOP)+"%");
			clearInterval(timerProgressPOP);
			setTimeout(function(){
				$('#progressModel').modal('hide');
				$('#progress_rate').css('width',"0%");
				$('#progress_text').html("0%");
				processBarPOP = 0;
			},1000)
		})
	})
}
/**
 *POP详情药检报告下载
 * @param {*} orderNo
 * @param {*} orgId
 * @param {*} merchant
 * @return {*} 
 */
function drugReportDownloadPOPde(orderNo, orgId,merchant,merchantId,newOrgId,isThirdCompany,origName){
	if (time == 0) {
		time = 2; //设定间隔时间（秒）
		var timer = setInterval(function(){
			time--;
			if (time == 0) {
				clearInterval(timer);
			}
		}, 1000);
	}else{
		return;
	}
	var layer_msg = layer.msg('查询中，请稍等...',{
		time:0
	});
	$.ajax({
		type: "POST",
		url: "/merchant/center/order/downPopDetailDrugReport",
		data: {
			"orderDetailId" : merchant,
		},
		dataType: "json",
		async: false,
		success: function (data) {
			layer.close(layer_msg);
			if(data.status === "failure" || !data.data){
				$.alert({"title":"温馨提示","body":data.errorMsg});
				return;
			}else{
				if(data.data.failCount >0 || data.data.successCount == 0){
					let isCancelClicked = true;
					$.confirm({
						title:'温馨提示',
						body: '不支持下载文件。',
						okBtn : '确认',
						cancelBtn : `<a href="javaScript::void(0),);" id="cancelBtn">联系客服</a>`,
						okHidden:function(){
							// 关闭弹窗
							$(this).closest('.modal').modal('hide'); // 或者 .remove()
						},
						cancelHidden: function() {
						if (isCancelClicked) {
								// callKf(orderNo,merchant); // '${orderNo}','${merchant}'
								// callKf(orderNo,merchant,order.orgId,order.isThirdCompany,order.origName);
								callKf(orderNo,merchantId,newOrgId,isThirdCompany,origName);
							}
						},
						// 标记取消按钮点击
						onCancelClick: function() {
							isCancelClicked = false;
						}
					})
					$(document).on('click', '.modal-header .sui-close', function() {
						isCancelClicked = false;
					});
				}else{
					downloadZip(data.data,'药检报告',orderNo);
					$('#progressModel').modal('show');
					processBarPOP = 0;
					timerProgressPOP = setInterval(function(){
						if(processBarPOP < 90){
							processBarPOP += 10;
							$('#progress_rate').css('width',Math.floor(processBarPOP)+"%");
							$('#progress_text').html(Math.floor(processBarPOP)+"%");
						}
					}, 1000);
				}
			}
		},
	});
}
/**
 *POP药检报告下载
 * @param {*} orderNo
 * @param {*} orgId
 * @param {*} merchant
 * @return {*} 
 */
function drugReportDownloadPOP(orderNo, orgId,merchant,isThirdCompany,origName){
	if (time == 0) {
		time = 2; //设定间隔时间（秒）
		var timer = setInterval(function(){
			time--;
			if (time == 0) {
				clearInterval(timer);
			}
		}, 1000);
	}else{
		return;
	}
	var layer_msg = layer.msg('查询中，请稍等...',{
		time:0
	});
	$.ajax({
		type: "POST",
		url: "/merchant/center/order/downPopDrugReport",
		data: {
			"orderNo" : orderNo,
			"merchantId" : merchant
		},
		dataType: "json",
		async: false,
		success: function (data) {
			layer.close(layer_msg);
			if(data.status === "failure" || !data.data){
				$.alert({"title":"温馨提示","body":data.errorMsg});
				return;
			}else{
				if(data.data.failCount >0 || data.data.successCount == 0){
					let isCancelClicked = true;
					$.confirm({
						title:'温馨提示',
						body: data.data.successCount > 0 ? `${data.data.failCount}个商品不支持下载药检文件，${data.data.successCount}个商品可下载药检文件，是否继续下载？`:'全部商品不支持下载药检文件。',
						okBtn : '确认',
						cancelBtn : `<a href="javaScript::void(0),);" id="cancelBtn">联系客服</a>`,
						okHidden:function(){
							if(data.data.successCount >0){
								downloadZip(data.data,'药检报告',orderNo,data.data.failCount);
								$('#progressModel').modal('show');
								processBarPOP = 0;
								timerProgressPOP = setInterval(function(){
									if(processBarPOP < 90){
										processBarPOP += 10;
										$('#progress_rate').css('width',Math.floor(processBarPOP)+"%");
										$('#progress_text').html(Math.floor(processBarPOP)+"%");
									}
								}, 1000);
							}else{
								 // 关闭弹窗
								 $(this).closest('.modal').modal('hide'); // 或者 .remove()
							}
							// FunLib.download(orderNo, data.data, 3);
						},
						cancelHidden: function() {
						if (isCancelClicked) {
								// callKf(orderNo,merchant); // '${orderNo}','${merchant}'
								callKf(orderNo,merchant,orgId,isThirdCompany,origName);
							}
						},
						// 标记取消按钮点击
						onCancelClick: function() {
							isCancelClicked = false;
						}
					})
					$(document).on('click', '.modal-header .sui-close', function() {
						isCancelClicked = false;
					});
				}else{
					downloadZip(data.data,'药检报告',orderNo);
					$('#progressModel').modal('show');
					processBarPOP = 0;
					timerProgressPOP = setInterval(function(){
						if(processBarPOP < 90){
							processBarPOP += 10;
							$('#progress_rate').css('width',Math.floor(processBarPOP)+"%");
							$('#progress_text').html(Math.floor(processBarPOP)+"%");
						}
					}, 1000);
				}
			}
		},
	});
}

// 下载全部发票
function downloadAllFp() {
	// 根据查询条件判断当前搜索结果是否存在订单
	var total = 0;
	var isReturn = 0;
	$.ajax({
		type: "POST",
		// url: "/merchant/center/order/exportOrdersCount",
		url: "/merchant/center/order/queryInvoiceCount",
		data: {
			"status" : $.trim(status),
			"orderNo" : $.trim($("#orderNo").val()),
			"orderSource" : $.trim($("#orderSource").val()),
			"startCreateTime" : $.trim($("#startCreateTime").val() + ' 00:00:00'),
			"endCreateTime" : $.trim($("#endCreateTime").val() + ' 23:59:59'),
			"orgId" : $.trim($("#searchShopBox").attr('orgid')),
			"invoiceState" : $.trim($("#invoiceState").val()),
			"name" : $.trim($("#drugName").val())
		},
		dataType: "json",
		async: false,
		success: function (data) {
			total = data.data;
			webSdk.track('pc_action_purchaseOrder_invoice_download', {
				'text' : '电子发票下载',
			});
			if (data.status == "failure") {
				parent.layer.msg(data.errorMsg);
				isReturn = 1;
			}
		},
		error: function (XMLHttpRequest, textStatus, errorThrown) {
			parent.layer.msg('网络异常');
			top.layer.close(index);
		}
	});
	if (isReturn == 1) {
		return;
	}
	if (total <= 0) {   //如果没数据
		layer.confirm("暂未生成发票，请稍后重试", {icon: 1, title: '提示'},
			function (index) {
				layer.close(index);

			})
		return;
	}

	layer.msg('正在为您下载，请稍后...',{
		icon: 16,
		shade: [0.1, '#fff']
	});
	$.ajax({
		type: "GET",
		// url: "/merchant/center/order/exportOrdersCount",
		url: "/merchant/center/order/batchDownloadInvoiceV2",
		data: {
			"status" : $.trim(status),
			"orderNo" : $.trim($("#orderNo").val()),
			"orderSource" : $.trim($("#orderSource").val()),
			"startCreateTime" : $.trim($("#startCreateTime").val() + ' 00:00:00'),
			"endCreateTime" : $.trim($("#endCreateTime").val() + ' 23:59:59'),
			"orgId" : $.trim($("#searchShopBox").attr('orgid')),
			"invoiceState" : $.trim($("#invoiceState").val()),
			"name" : $.trim($("#drugName").val())
		},
		async: false,
		success: function (data) {
			var list = data.data;
			webSdk.track('pc_action_purchaseOrder_invoice_download', {
				'text' : '电子发票下载',
			});
			if (data.status == "failure") {
				parent.layer.msg(data.errorMsg);
				isReturn = 1;
			} else if (list.length > 0) {
				const resultList = [];  //已开发票
				const unResultList = []; //未开发票
				list = list.filter(item => {
					if (item.rul) return true;
					unResultList.push([item.companyName, item.orderNo])
					return false;
				});
				list = list.map(item => {
					const result = { ...item };
					result.rul = item.rul.includes("baiwang.com") ? "/merchant/center/order/proxyDownload?targetUrl=" + item.rul : item.rul;
					return result;
				})
				const zip = new JSZip();
				batchDownloadFile(list, 10, 'rul', 'fileName', (res) => {
					res.forEach((item) => {
						const temp = item.fileName.split('.')[0].split('_');
						if (item.data) {
							zip.file(item.fileName, item.data);
							resultList.push([temp[0], temp[1], "成功", ""]);
						} else {
							resultList.push([temp[0], temp[1], "失败", "下载失败，请到该订单直接查看或下载"]);
						}
						/* if (item.data) {
							//下载成功
							zip.file(item.fileName, item.data);
							successList.push(item.fileName)
						} else {
							//下载失败
							failList.push(item.fileName);
						} */
					})
				}).then(() => {
					//已开发票xlsx
					const data = [['商家名称', '订单编号', '状态', '失败原因']];
					/* const max = Math.max(successList.length, failList.length);
					for (let i = 0; i < max; i++) {

						data.push([successList[i] || '', failList[i] || '']);
					} */
					data.push(...resultList)
					const workbook = XLSX.utils.book_new();
					const worksheet = XLSX.utils.aoa_to_sheet(data);
					XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet 1');

					// 导出为 Blob 并下载
					const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
					const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
					zip.file('已开发票订单清单.xlsx', blob, { binary: true });
					//未开发票xlsx
					const data1 = [['商家名称', '订单编号']];
					/* const max = Math.max(successList.length, failList.length);
					for (let i = 0; i < max; i++) {

						data.push([successList[i] || '', failList[i] || '']);
					} */
					data1.push(...unResultList);
					const workbook1 = XLSX.utils.book_new();
					const worksheet1 = XLSX.utils.aoa_to_sheet(data1);
					XLSX.utils.book_append_sheet(workbook1, worksheet1, 'Sheet 1');

					// 导出为 Blob 并下载
					const excelBuffer1 = XLSX.write(workbook1, { bookType: 'xlsx', type: 'array' });
					const blob1 = new Blob([excelBuffer1], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
					zip.file('未开发票订单清单.xlsx', blob1, { binary: true });
					zip.generateAsync({ type: "blob" }).then((content) => {
						saveAs(content, list[0].packageName);
					});
				})
			}
		},
		error: function (XMLHttpRequest, textStatus, errorThrown) {
			parent.layer.msg('网络异常');
			top.layer.close(index);
		}
	});
	/* window.location.href = '/merchant/center/order/batchDownloadInvoiceV2?orderNo='
		+ $.trim($("#orderNo").val())
		+ "&orderSource=" + $.trim($("#orderSource").val())
		+ "&startCreateTime=" + $.trim($("#startCreateTime").val() + ' 00:00:00')
		+ "&endCreateTime=" + $.trim($("#endCreateTime").val() + ' 23:59:59')
		+ "&status=" + $.trim(status)
		+ "&invoiceState=" + $.trim($("#invoiceState").val())
		+ "&orgId=" + $.trim($("#searchShopBox").attr('orgid'))
		+ "&name=" + $.trim($("#drugName").val()) */

}
//分量下载
/**
 * 
 * @param { Array } list 
 * @param { number } curIndex 
 * @param { number } bucket 
 * @param { string } urlKey 
 * @param { string } fileNameKey 
 * @param { (list: {data: ArrayBuffer,fileName: string,url: string}[]) => {} } run 
 * @returns 
 */
function batchDownloadFile(list, bucket, urlKey, fileNameKey, run) {
	return new Promise(function (resolve, reject) {
		const download = (start) => {
			//当前下载列表
			const curDownloadList = [];
			const end = Math.min(start + bucket, list.length);
			for (let i = start; i < end; i++) {
				curDownloadList.push(downloadFileByOne(list[i][urlKey], list[i][fileNameKey]));
			}
			Promise.all(curDownloadList).then(function (results) {
				if (end < list.length) {
					download(end);
				} else {
					resolve();
				}
				run(results);
			})
		}
		download(0);
	})
}
//下载文件
/**
 * 
 * @param {string} url 
 * @param {string} fileName 
 * @returns { Promise<{ data: ArrayBuffer, fileName: string, url: string }> }
 */
function downloadFileByOne(url, fileName) {
	return new Promise((resolve, reject) => {
		/* $.ajax({
			type: "GET",
			url: url,
			success: function (data) {	
				// 创建临时链接并触发下载
				resolve({ data, fileName })
			},
			error: function (xhr, status, error) {
				resolve({ data: null, fileName });
			}
		}) */
		const xhr = new XMLHttpRequest();
		xhr.open('GET', url, true);
		xhr.responseType = 'arraybuffer';
		
		xhr.onload = function() {
			if (xhr.status === 200) {
				resolve({ data: xhr.response, fileName, url: url});
			} else {
				resolve({ data: null, fileName, url: url })
			}
		};
	
		xhr.onerror = function() {
			resolve({ data: null, fileName, url: url });
		};
	
		xhr.send();
	})
}
//下载单个订单发票
function downloadSingleOrderInvoice() {
	layer.msg('正在为您下载，请稍后...',{
		icon: 16,
		shade: [0.1, '#fff']
	});

	/* window.location.href = '/merchant/center/order/downloadInvoice?orderNo='
		+ $.trim($("#invoiceOrderNo").val()) */
		//update: 后端返回下载路径，前端下载，下载成功则放到zip，不成功就通过a标签进行下载
		$.ajax({
			type: "GET",
			// url: "/merchant/center/order/exportOrdersCount",
			url: "/merchant/center/order/downloadInvoiceV2",
			data: {
				"orderNo" : $.trim($("#invoiceOrderNo").val()),
			},
			async: false,
			success: function (data) {
				var target = 0;
				var list = data.data;
				if (data.status == "failure") {
					parent.layer.msg(data.errorMsg);
					isReturn = 1;
				} else if (list.length > 0) {
					list = list.map(item => {
						const result = { ...item };
						result.rul = item.rul.includes("baiwang.com") ? "/merchant/center/order/proxyDownload?targetUrl=" + item.rul : item.rul;
						return result;
					})
					const zip = new JSZip();
					batchDownloadFile(list, 10, 'rul', 'fileName', (res) => {
						res.forEach((item) => {
							if (item.data) {
								zip.file(item.fileName, item.data);
								target++;
							} else {
								const aTab = document.createElement('a');
								aTab.href = item.url;
								aTab.download = item.fileName;
								aTab.target = "_blank";
								aTab.click();
							}
							/* if (item.data) {
								//下载成功
								zip.file(item.fileName, item.data);
								successList.push(item.fileName)
							} else {
								//下载失败
								failList.push(item.fileName);
							} */
						})
					}).then(() => {
						if (target == 0) return;
						zip.generateAsync({ type: "blob" }).then((content) => {
							saveAs(content, list[0].packageName);
						});
					})
				}
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				parent.layer.msg('网络异常');
				top.layer.close(index);
			}
		});	
}


// 获取仓库交接单、出库单下载地址
function getHandoverOrderUrl(orderNo,branchCode,docType) {
	//window.open('https://files.test.ybm100.com/B2BCOS/Pop/2284cd3afa8e4dea9b3192d84470c47a.pdf')
	var layer_msg = layer.msg('查询中，请稍等...',{
		time:0
	});
	$.ajax({
		type: "POST",
		url: "/merchant/center/order/queryOutOrderPdfUrl",
		data: {
			"orderNo" : orderNo,
			"branchCode" : branchCode,
			"docType" : docType,
		},
		dataType: "json",
		async: false,
		success: function (data) {
			layer.close(layer_msg);
			if((data.data || []).length){
				(data.data || []).forEach(function (pdfUrl, index) {
					window.open(pdfUrl, String(index));
				})
			} else {
				var str = docType === '1' ? '暂未生成电子出库单，请稍后重试' : '暂未生成仓库交接单，请稍后重试';
				$.alert({"title":"温馨提示","body": str});
			}
		},
		error: function () {
			parent.layer.msg('网络异常');
		},
	});
}
// 购物金代收款说明
function jumpShopingDescUrl(url) {
	if (url != null && url != 'null' && url != '') {
		window.open(url)
	}else {
		$.alert({
			"title": "提示", "body": "暂未生成代收款说明，请稍后重试", okHidden: function () {
			}
		});

	}
}



// //药检报告
var time = 0 ;
function drugReportDownload(orderNo,skuId,downloadType){
	if (time == 0) {
		time = 2; //设定间隔时间（秒）
		var timer = setInterval(function(){
			time--;
			if (time == 0) {
				clearInterval(timer);
			}
		}, 1000);
	}else{
		return ;
	}
	var layer_msg = layer.msg('查询中，请稍等...',{
		time:0
	});
	var uuid=guid();
	$.ajax({
		type: "POST",
		url:  '/merchant/center/order/judgeDownloadReport',
		dataType: "json",
		data: {
			orderNo: orderNo,
			skuId:skuId,
			downloadType: downloadType
		},
		success: function (data) {
			layer.close(layer_msg);
			if(data.status == "success") {
				var paramData = {
					"orderNo": orderNo,
					"skuId": skuId,
					"downloadType": downloadType,
					"progressId":uuid
				}
				if(data.msg == "illegal"){
					$.alert({"title":"提示","body":"下载路径不合法！"});
				}else if(data.msg == "false"){
					$.alert({"title":"温馨提示","body":"当前订单商品暂未上传相关资料，请联系客服人员进行咨询。客服电话：400-0505-111"});
				}else if(data.msg == "part"){
					var dataList = data.data;
					$("#J_addsuppliersDialog").modal('show');
					$("tr.tr_addsuppliers").remove();
					for(var i=0;i<dataList.length;i++){
						var dataRow = dataList[i];
						var j =i+1;
						var tables = '<tr class="tr_addsuppliers"><td style="text-align: center;"><span>'+ j +'</span></td>' +
							'<td style="text-align: center;"><span>'+dataRow.productName+'</span></td><td style="text-align: center;"><span>'+dataRow.spec+'</span></td>'+
							'<td style="text-align: center;"><span class="distributor-num">'+dataRow.manufacturer+'</span></td>'+
							'<td style="text-align: center;">'+	'<ul><li><span class='+(dataRow.isUpload==1?"sui-text-success":"sui-text-danger")+' >'+(dataRow.isUpload==1?'已上传':'未上传')+'</span></li> </ul></td></tr>';
						$("#J_addsuppliersDialog_tbody").append(tables);
					}
					//点击确定按钮时触发的事件
					$('#J_addsuppliersDialog').on('okHide', function(e) {
						//实现Ajax下载文件
						$.fileDownload("/merchant/center/order/downloadReport", {
							httpMethod : 'POST',
							data : $.param(paramData),
							prepareCallback : function(url) {
								$('#progressModel').modal('show');
							},
							successCallback : function(url) {
							},
							failCallback : function(html, url) {
								$.alert({"title":"提示","body":"下载失败，请联系客服人员进行咨询。客服电话：400-0505-111"});
								$('#progressModel').modal('hide');
								$('#progress_rate').css('width',"0%");
								$('#progress_text').html("0%");
							}
						});

						var timerProgress = setInterval(function(){
							$.ajax({
								type: "POST",
								url:  "/merchant/center/order/getDownloadProgress?progressId=" + uuid,
								contentType: "application/json",
								dataType: "json",
								success: function (data) {
									if (data.status == "success") {
										if(data.data == "" || data.data==null){
											data.data = 0.0;
										}else if(data.data == 0.95){
											clearInterval(timerProgress);
											data.data =1.0;
										}
										var num = data.data;
										$('#progress_rate').css('width',Math.floor(num * 100)+"%");
										$('#progress_text').html(Math.floor(num * 100)+"%");
										if(data.data*100 == 100){
											setTimeout(function(){
												$('#progressModel').modal('hide');
												$('#progress_rate').css('width',"0%");
												$('#progress_text').html("0%");
											},1000)
										}
									}
								}
							});
						}, 1000);

					});
				}else if(data.msg == "true") {
					//实现Ajax下载文件
					$.fileDownload("/merchant/center/order/downloadReport", {
						httpMethod : 'POST',
						data : $.param(paramData),
						prepareCallback : function(url) {
							$('#progressModel').modal('show');
						},
						successCallback : function(url) {
						},
						failCallback : function(html, url) {
							$.alert({"title":"提示","body":"下载失败，请联系客服人员进行咨询。客服电话：400-0505-111"});
						}
					});

					var timerProgress = setInterval(function(){
						$.ajax({
							type: "POST",
							url:  "/merchant/center/order/getDownloadProgress?progressId=" + uuid,
							contentType: "application/json",
							dataType: "json",
							success: function (data) {
								if (data.status == "success") {
									if(data.data == "" || data.data==null){
										data.data = 0.0;
									}else if(data.data == 0.95){
										clearInterval(timerProgress);
										data.data =1.0;
									}
									var num = data.data;
									$('#progress_rate').css('width',Math.floor(num * 100)+"%");
									$('#progress_text').html(Math.floor(num * 100)+"%");
									if(data.data*100 == 100){
										setTimeout(function(){
											$('#progressModel').modal('hide');
											$('#progress_rate').css('width',"0%");
											$('#progress_text').html("0%");
										},1000)
									}
								}
							}
						});
					}, 1000);

				}
			}else{
				$.alert({"title":"系统异常","body":"系统异常"});
			}
		}
	});
}

//首营资料下载 下载
var time = 0 ;
function firstCampDownload(orderNo,skuId,downloadType){
	if (time == 0) {
		time = 2; //设定间隔时间（秒）
		var timer = setInterval(function(){
			time--;
			if (time == 0) {
				clearInterval(timer);
			}
		}, 1000);
	}else{
		return ;
	}
	$.ajax({
		type: "POST",
		url:  '/merchant/center/order/judgeDownloadReport',
		dataType: "json",
		data: {
			orderNo: orderNo,
			skuId:skuId,
			downloadType: downloadType
		},
		success: function (data) {
			if (data.status == "success") {
				var paramData = {
					"orderNo": orderNo,
					"skuId": skuId,
					"downloadType": downloadType
				}
				if(data.msg == "false"){
					$.alert({"title":"温馨提示","body":"当前订单商品暂未上传相关资料，请联系客服人员进行咨询。客服电话：400-0505-111"});
				}else if(data.msg == "illegal"){
					$.alert({"title":"提示","body":"下载路径不合法！"});
				}else if(data.msg == "part"){
					var dataList = data.data;
					$("#J_addsuppliersDialog").modal('show');
					for(var i=0;i<dataList.length;i++){
						var dataRow = dataList[i];
						var j =i+1;
						var tables = '<tr class="tr_addsuppliers"><td style="text-align: center;"><span>'+ j +'</span></td>' +
							'<td style="text-align: center;"><span>'+dataRow.productName+'</span></td><td style="text-align: center;"><span>'+dataRow.spec+'</span></td>'+
							'<td style="text-align: center;"><span class="distributor-num">'+dataRow.manufacturer+'</span></td>'+
							'<td style="text-align: center;">'+	'<ul><li><span class='+(dataRow.isUpload==1?"sui-text-success":"sui-text-danger")+' >'+(dataRow.isUpload==1?'已上传':'未上传')+'</span></li> </ul></td></tr>';
						$("#J_addsuppliersDialog_tbody").append(tables);
					}
					//点击确定按钮时触发的事件
					$('#J_addsuppliersDialog').on('okHide', function(e) {
						layer.msg('正在为您下载资料，请耐心等待···', {
							time: 3500,
						});
						//实现Ajax下载文件
						$.fileDownload("/merchant/center/order/downloadReport", {
							httpMethod : 'POST',
							data : $.param(paramData),
							prepareCallback : function(url) {
								//用户无感知下载
							},
							successCallback : function(url) {
								//用户无感知下载
							},
							failCallback : function(html, url) {
								$.alert({"title":"提示","body":"下载失败，请联系客服人员进行咨询。客服电话：400-0505-111"});
							}
						});
					});
				}else if(data.msg == "true") {
					layer.msg('正在为您下载资料，请耐心等待···', {
						time: 3500,
					});
					$.fileDownload("/merchant/center/order/downloadReport", {
						httpMethod : 'POST',
						data : $.param(paramData),
						prepareCallback : function(url) {
						},
						successCallback : function(url) {
						},
						failCallback : function(html, url) {
							$.alert({"title":"提示","body":"下载失败，请联系客服人员进行咨询。客服电话：400-0505-111"});
						}
					});
				}
			}else{
				$.alert({"title":"系统异常","body":"系统异常"});
			}
		}
	});
}

function guid() {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		var r = Math.random() * 16 | 0,
			v = c == 'x' ? r : (r & 0x3 | 0x8);
		return v.toString(16);
	});
}

var time = 0;
function exportOrders(status) {
	// 根据查询条件判断当前搜索结果是否存在订单
	var total = 0;
	$.ajax({
		type: "POST",
		// url: "/merchant/center/order/exportOrdersCount",
		url: "/merchant/center/order/exportOrders",
		data: {
			"status" : $.trim(status),
			"orderNo" : $.trim($("#orderNo").val()),
			"orderSource" : $.trim($("#orderSource").val()),
			"startCreateTime" : $.trim($("#startCreateTime").val() + ' 00:00:00'),
			"endCreateTime" : $.trim($("#endCreateTime").val() + ' 23:59:59'),
			"channelCode" : $.trim($("#channelCode").val()),
			"invoiceState": $.trim($("#invoiceState").val())

		},
		dataType: "json",
		async: false,
		success: function (data) {
			total = data.data;
			if (data.status == "failure") {
            	parent.layer.msg(data.errorMsg);
            } else {
                if (total <= 0) {   //如果没数据
                    layer.confirm("未找到需要导出的订单", {icon: 1, title: '提示'},
                        function (index) {
                            layer.close(index);
                        })
                    return;
                }

                layer.msg('正在为您下载，请稍后...',{
                    icon: 16,
                    shade: [0.1, '#fff']
                });

                window.location.href = '/merchant/center/order/exportOrders?orderNo='
                        + $.trim($("#orderNo").val())
                        + "&orderSource=" + $.trim($("#orderSource").val())
                        + "&startCreateTime=" + $.trim($("#startCreateTime").val() + ' 00:00:00')
                        + "&endCreateTime=" + $.trim($("#endCreateTime").val() + ' 23:59:59')
                        + "&status=" + $.trim(status)
                        + "&type=" + $.trim(1)
                }
		}
	});

}

var time = 0;
function exportOrderDetails(status) {
	// 根据查询条件判断当前搜索结果是否存在订单
	var total = 0;
	$.ajax({
		type: "POST",
		// url: "/merchant/center/order/exportOrdersCount",
		url: "/merchant/center/order/exportOrderDetails",
		data: {
			"status" : $.trim(status),
			"orderNo" : $.trim($("#orderNo").val()),
			"orderSource" : $.trim($("#orderSource").val()),
			"startCreateTime" : $.trim($("#startCreateTime").val() + ' 00:00:00'),
			"endCreateTime" : $.trim($("#endCreateTime").val() + " 23:59:59"),
			"channelCode" : $.trim($("#channelCode").val())
		},
		dataType: "json",
		async: false,
		success: function (data) {
			total = data.data;
			if (data.status == "failure") {
				parent.layer.msg(data.errorMsg);
			} else {
                if (total <= 0) {   //如果没数据
                    layer.confirm("未找到需要导出的订单明细", {icon: 1, title: '提示'},
                        function (index) {
                            layer.close(index);

                        })
                    return;
                }

                layer.msg('正在为您下载，请稍后...',{
                    icon: 16,
                    shade: [0.1, '#fff']
                });

                window.location.href = '/merchant/center/order/exportOrderDetails?orderNo='
                    + $.trim($("#orderNo").val())
                    + "&orderSource=" + $.trim($("#orderSource").val())
                    + "&startCreateTime=" + $.trim($("#startCreateTime").val() + ' 00:00:00')
                    + "&endCreateTime=" + $.trim($("#endCreateTime").val() + ' 23:59:59')
                    + "&status=" + $.trim(status)
                    + "&type=" + $.trim(1)

            }
		},
		error: function (XMLHttpRequest, textStatus, errorThrown) {
			parent.layer.msg('网络异常');
			top.layer.close(index);
		}
	});

}

//下载资质
var time = 0 ;
function downloadQualification(orgId,phone){
	if (time == 0) {
		time = 3; //设定间隔时间（秒）
		var timer = setInterval(function(){
			time--;
			if (time == 0) {
				clearInterval(timer);
			}
		}, 1000);
	}else{
		return ;
	}

	$.ajax({
		type: "POST",
		url:  '/merchant/center/order/judgeQualification',
		dataType: "json",
		data:{ orgId: orgId },
		success: function (data) {
			if (data.status == "success") {
				if(data.msg == "false"){
					if(typeof(phone) == "undefined"){
						$.alert({"title":"温馨提示","body":"未找到商家相关资质信息，请联系商家进行咨询。"});
					}else{
						$.alert({"title":"温馨提示","body":"未找到商家相关资质信息，请联系商家进行咨询。商家电话："+phone});
					}
				}else if(data.msg == "illegal"){
					$.alert({"title":"提示","body":"下载路径不合法！"});
				}else if(data.msg == "true") {
					layer.msg('正在为您下载资料，请耐心等待···', {
						time: 2000,
					});
					$.fileDownload("/merchant/center/order/downloadQualification", {
						httpMethod : 'POST',
						data :  { orgId: orgId },
						prepareCallback : function(url) {
						},
						successCallback : function(url) {
						},
						failCallback : function(html, url) {
							$.alert({"title":"提示","body":"下载失败，请联系客服人员进行咨询。客服电话：400-0505-111"});
						}
					});
				}
			}else{
				$.alert({"title":"系统异常","body":"系统异常"});
			}
		}
	});



}

var scrollTimer = null;
var scrollTimeStart = '';
var scrollTimeEnd = '';
function handleScrollStart() {
	clearTimeout(scrollTimer);
	scrollTimer = setTimeout(handleScrollEnd, 100);
	scrollTimeStart = document.body.scrollTop;
};

function handleScrollEnd() {
	scrollTimeEnd = document.body.scrollTop;
	if (scrollTimeEnd === scrollTimeStart) {
		clearTimeout(scrollTimer);
		console.log("滚动停止", scrollTimeStart, scrollTimeEnd);
		exposureView();
	}
};

function exposureView() {
	var lists = $('[data-exceptionFlag="true"]');
	if (lists && lists.length) {
		for (var i = 0; i < lists.length; i++) {
			var item = lists[i];
			if (isElementInViewport(item)) {
				var hasTrack = $(item).attr('hastrack');
				if (hasTrack === "false" || !hasTrack) {
					var orderNo = $(item).data('orderno');
					var sysException = $(item).data('sysexception');
					var supplierException = $(item).data('supplierexception');
					var source = $(item).data('source');
					webSdk.track('Qualification_Exception_Reminder_Exposure', {
						user_id: window.merchantIdFromHeader,
						order_no: orderNo,
						page_source: source,
						system_reminder: sysException ? 1 : 0,
						business_reminder: supplierException ? 1 : 0,
					});
					$(item).attr('hastrack', true);
				}
			} else {
				$(item).attr('hastrack', false);
			}
		}
	}

	var lists1 = $('[data-checkMore="true"]');
	if (lists1 && lists1.length) {
		for (var i = 0; i < lists1.length; i++) {
			var item = lists1[i];
			if ($(item).height() === 18) continue;
			if (isElementInViewport(item)) {
				var hasTrack = $(item).attr('hastrack');
				if (hasTrack === "false" || !hasTrack) {
					var orderNo = $(item).data('orderno');
					var source = $(item).data('source');
					var type = $(item).data('type');
					webSdk.track('Business_Reminder_More_Exposure', {
						user_id: window.merchantIdFromHeader,
						order_no: orderNo,
						page_source: source,
						reminder_type: type,
						system_more: type === 'system' ? 1 : 0,
						business_more: type === 'business' ? 1: 0
					});
					$(item).attr('hastrack', true);
				}
			} else {
				$(item).attr('hastrack', false);
			}
		}
	}
};

function isElementInViewport(el) {
	var rect = el.getBoundingClientRect();
	var width_st = rect.width,
		height_st = rect.height;
	var innerHeight = window.innerHeight,
		innerWidth = window.innerWidth;
	if (
		(rect.top <= 0 && rect.height > innerHeight) ||
		(rect.left <= 0 && rect.width > innerWidth)
	) {
		return rect.left * rect.right <= 0 || rect.top * rect.bottom <= 0;
	};
	return (
		rect.height > 0 &&
		rect.width > 0 &&
		((rect.top >= 0 && rect.top <= innerHeight - height_st) ||
			(rect.bottom >= height_st && rect.bottom <= innerHeight)) &&
		((rect.left >= 0 && rect.left <= innerWidth - width_st) ||
			(rect.right >= width_st && rect.right <= innerWidth))
	);
};

$(document).on("click",".handleJumpLicense",function(e){
	var orderNo = $(this).data('orderno');
	var source = $(this).data('source');
	webSdk.track('Update_Qualification', {
		user_id: window.merchantIdFromHeader,
		order_no: orderNo,
		page_source: source,
	});

	window.open('/merchant/center/licenseAudit/findLicenseCategoryInfo.htm');
});

$(document).on("click",".checkMoreSupplierException",function(e){
	var orderNo = $(this).data('orderno');
	var source = $(this).data('source');
	webSdk.track('Business_Reminder_More_Detail', {
		user_id: window.merchantIdFromHeader,
		order_no: orderNo,
		page_source: source,
		reminder_type: 'business',
		system_more_click: 0,
		business_more_click: 1,
	});

	var str = $(this).data('supplierexception');
	str = str.split('\n').join('<br>');
	layer.confirm(str, {title: '资质异常提醒', btn: ['去更新','稍后更新资质']}, function (index) {

		webSdk.track('Update_Qualification', {
			user_id: window.merchantIdFromHeader,
			order_no: orderNo,
			page_source: 'reminder_popup',
		});

		window.open('/merchant/center/licenseAudit/findLicenseCategoryInfo.htm');
		layer.close(index);
	},function (index) {
		layer.close(index);
	});
});

$(document).on("click",".checkMoreSysException",function(e){

	var orderNo = $(this).data('orderno');
	var source = $(this).data('source');
	webSdk.track('Business_Reminder_More_Detail', {
		user_id: window.merchantIdFromHeader,
		order_no: orderNo,
		page_source: source,
		reminder_type: 'system',
		system_more_click: 1,
		business_more_click: 0,
	});

	var str = $(this).data('sysexception');
	str = str.split('\n').join('<br>');
	layer.confirm(str, {title: '资质异常提醒', btn: ['去更新','稍后更新资质']}, function (index) {

		webSdk.track('Update_Qualification', {
			user_id: window.merchantIdFromHeader,
			order_no: orderNo,
			page_source: 'reminder_popup',
		});

		window.open('/merchant/center/licenseAudit/findLicenseCategoryInfo.htm');
		layer.close(index);
	},function (index) {
		layer.close(index);
	});
});

window.addEventListener('message', function(e){
	if (e.data.message === 'webSdkIdentifyDown') {
		setTimeout(function() {
			exposureView();
			window.addEventListener("scroll", handleScrollStart);
		}, 1000);
	}
}, false);

// 在页面底部添加这段代码
$(document).ready(function() {
    // 检查左侧菜单是否存在
    if ($('.side-left .list').length === 0 || $('.side-left .list').css('display') === 'none') {
        // 如果左侧菜单不存在或不可见，手动加载并显示
        $.ajax({
            url: '/common/merchantCenter/left.ftl',
            type: 'GET',
            success: function(data) {
                $('.side-left').html(data);
                
                // 手动设置"我的订单"为激活状态
                $('.side-left .list a[href="/merchant/center/order/index.htm"]').addClass('active');
            },
            error: function() {
                console.error('Failed to load left menu');
            }
        });
    } else {
        // 确保"我的订单"菜单项处于激活状态
        //$('.side-left .list a[href="/merchant/center/order/index.htm"]').addClass('active');
    }
});