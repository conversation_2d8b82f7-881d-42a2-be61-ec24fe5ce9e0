package com.xyy.ec.pc.cms.service.complement.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * CMS：补足查询参数，秒杀商品列表
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsSeckillProductListComplementQueryParam implements Serializable {

    /**
     * 用户ID
     */
    private Long merchantId;
    /**
     * 商品的区域编码
     */
    private String productBranchCode;
    /**
     * 用户是否不能看易碎品
     */
    private Boolean merchantIsNotWatchFragileGoods;
    /**
     * 标签ID列表，商品组ID列表
     */
    private List<String> exhibitionIdStrList;
    /**
     * 活动状态列表
     */
    private List<Integer> activityStatusList;
    /**
     * 指定商品ID列表
     */
    private List<Long> specifiedCsuIds;
    /**
     * 期望数量
     */
    private Integer expectedNum;
    /**
     * 最大补足次数
     */
    private Integer maxComplementNum;
    /**
     * 排除商品ID列表
     */
    private Set<Long> excludeCsuIdsSet;
    /**
     * 是否做商品控销过滤
     */
    private Boolean isDoCsuControlFilter;

}
