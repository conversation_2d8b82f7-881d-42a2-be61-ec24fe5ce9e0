package com.xyy.ec.api.busi.place.register;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/15 14:17
 * @Description:
 */
@Data
public class PoiReq implements Serializable {

    private static final long serialVersionUID = 2910764190884691263L;
    private Double latitude;//维度
    private Double longitude;//经度
    private String shopName;//店铺名称
    @NotNull
    private Integer offset;
    @NotNull
    private Integer limit;
    private String provinceCode;
    private String cityCode;
    private String regionCode;
    private String streetCode;
}
