<!DOCTYPE HTML>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
	</head>
    <style>
        .suixinpin-container {
            padding: 10px;
            margin-top: 10px;
            background-image: linear-gradient(180deg, #FFEFED 0%, #FFFFFF 100%);
            border: 2px solid #FFFFFF;
        }
        .suixinpin-container .suixinpin-title {
            display: flex;
            justify-content: space-between;
        }
        .suixinpin-container .suixinpin-title .font {
            font-weight: 500;
            font-size: 16px;
            color: #222222;
            margin-left: 10px;
        }
        .suixinpin-container .suixinpin-title .btn {
            width: 80px;
            height: 26px;
            background: #fff;
            border-radius: 2px;
            cursor: pointer;
            font-weight: 400;
            font-size: 14px;
            color: #222222;
            line-height: 26px;
            text-align: center;
        }
        .suixinpin-container .suixinpin-list {
            padding: 10px;
            display: flex;
            flex-wrap: wrap;
            overflow: auto;
            justify-content: flex-start;
            gap: 10px;
        }
        .suixinpin-container .suixinpin-list .suixinpin-item {
            width: calc(24% - 8px);
            height: 100px;
            background: #FFFFFF;
            border: 1px solid #E0E0E0;
            border-radius: 4px;
            padding: 5px;
            display: flex;
        }
        .suixinpin-container .suixinpin-list .suixinpin-item .suixinpin-item-left {
            margin: 5px 5px 0 0;
            width: 80px;
            height: 80px;
        }
        .suixinpin-container .suixinpin-list .suixinpin-item .suixinpin-item-left img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .suixinpin-container .suixinpin-list .suixinpin-item .suixinpin-item-right {
            width: calc(100% - 90px);
            font-size: 14px; 
        }
        .suixinpin-container .suixinpin-list .suixinpin-item .suixinpin-item-right .suixinpin-item-title {
            color: #222222;
            min-height: 35px;
            text-align: left;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
        }
        .suixinpin-container .suixinpin-list .suixinpin-item .suixinpin-item-right .suixinpin-item-validity {
            height: 12px;
            line-height: 12px;
            color: #666666;
            font-size: 12px;
            font-weight: 400;
            margin: 3px 0;
        }
        .suixinpin-container .suixinpin-list .suixinpin-item .suixinpin-item-right .suixinpin-item-content {
            display: flex;
            justify-content: space-between;
            align-items: end;
        }
        .suixinpin-container .suixinpin-list .suixinpin-item .suixinpin-item-right .suixinpin-item-content .show-price {
            display: block;
            font-size: 14px;
            color: #FF0402;
            line-height: 14px;
            margin-top: 5px;
        }
        .suixinpin-container .suixinpin-list .suixinpin-item .suixinpin-item-right .suixinpin-item-content .abandoned-price {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 12px;
            text-decoration: line-through;
        }
        .suixinpin-container .suixinpin-list .suixinpin-item .suixinpin-item-right .suixinpin-item-content .suixinpin-item-btn-buy {
            width: 22px;
            height: 22px;
            background: #d8d8d800;
            cursor: pointer;
        }
        .suixinpin-item-btn-buy img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .suixinpin-container .suixinpin-footer {
            text-align: right;
            font-size: 14px;
            color: #333333;
        }
        .suixinpin-container .suixinpin-footer .summary-goods{
            margin-right: 30px;
        }
        .suixinpin-container .suixinpin-footer .red{
            color: #FF0402;
        }
        .suixinpin-item-btn-input{
            border: 1px solid #e0e0e0;
            width:90px;
            height: 25px;
            overflow: hidden;
        }
        .suixinpin-item-btn-input a.sub{
            display:block;
            width: 25px;
            height: 25px;
            line-height: 25px;
            border-right: 1px solid #e0e0e0;
            text-align: center;
            color: #333;
            font-size: 18px;
        }
        .suixinpin-item-btn-input input{
            display:block;
            width: 37px;
            height: 25px;
            line-height: 25px;
            margin: 0;
            padding: 0;
            border: none !important;
            text-align: center;
        }
        .suixinpin-item-btn-input input:focus {
            outline: none;
        }
        .suixinpin-item-btn-input a.add{
            display:block;
            width: 25px;
            height: 25px;
            line-height: 25px;
            border-left: 1px solid #e0e0e0;
            text-align: center;
            color: #333;
            font-size: 18px;
        }
        .showMarketingSolag {
            display: block;
            height: 12px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #C47102;
            letter-spacing: 0;
            line-height: 12px;
            margin-bottom: 5px;
        }
    </style>
	<body>
        <div class="suixinpin-container" id="suixinpin_container" style="display: ${(orderSettle.suiXinPinSkus?? && orderSettle.suiXinPinSkus.items?? && (orderSettle.suiXinPinSkus.items?size > 0))?string('block', 'none')};">
            <div class="suixinpin-title">
                <div  class="font">
                    <span id="suiTitle">随心拼</span>
                </div>
                <div class="btn" id="suixinpin_buyMore">购买更多</div>
            </div>
            <#if orderSettle.suiXinPinSkus ?? && orderSettle.suiXinPinSkus.items ?? &&(orderSettle.suiXinPinSkus.items?size>0)>
            <div class="suixinpin-list" id="suixinpin_list">
            <#assign itemIndex=0 />
            <#list orderSettle.suiXinPinSkus.items as item>
                <div id="suixinpin_item_type" data-item-type="${item.type}" style="display: none"></div>
                <div class="suixinpin-item" data-id="${item.skuId}" data-rank="${itemIndex + 1}" data-exp_id="${item.exp_id}">
                    <input type="hidden" class="sxpskuid" value="${item.skuId}">
                    <div class="suixinpin-item-left">
                        <img src="${productImageUrl}/ybm/product/min/${item.imageUrl}"
                            alt="${item.imageUrl}" onerror="this.src='/static/images/default-big.png'">
                    </div>
                    <div class="suixinpin-item-right">
                        <div class="suixinpin-item-title">${item.showName}</div>
                        <div class="suixinpin-item-validity">有效期至${item.nearEffect}</div>
                        <span class="showMarketingSolag">${item.promoTag!''}</span>
                        <div class="suixinpin-item-content">
                            <div class="suixinpin-item-price">
                                <span class="show-price">¥
                                        <#if item.price??>
                                            ${item.price}
                                        <#else>
                                            ${item.fob}
                                        </#if>
                                    /${item.productUnit}
                                    <#if item.type?? && item.type == 1>
                                        <span class="abandoned-price">
                                            <#if item.fob??>
                                            ¥${item.fob}
                                            </#if>
                                        </span>
                                    </#if>
                                </span>
                            </div>
                            <div class="suixinpin-item-btn">
                                <div class="suixinpin-item-btn-buy" onclick='suixinpin_sub_module_click("${item.skuId}","加购",1);showQuantityInput(this);'>
                                    <img src="/static/images/shoppingCar.png" alt="">
                                </div>
                                <div class="suixinpin-item-btn-input" style="display: none;" data-id="${item.skuId}">
                                    <#if item.qtData??>
                                        <div class="qtData" style="display: none">
                                            <div class="expId" data-expId="${item.qtData.expId!''}"></div>
                                            <div class="gtListData" data-gtListData='${item.qtData.qtListData!""}'></div>
                                            <div class="gtSkuData" data-gtSkuData='${item.qtData.qtSkuData!""}'></div>
                                            <div class="rank" data-rank="${itemIndex + 1}"></div>
                                            <div class="scmId" data-scmId="${item.qtData.scmId!''}"></div>
                                            <div class="productName" data-productName="${item.showName!''}"></div>
                                        </div>
                                    </#if>
                                    <a href="javascript:void(0);" class="sub fl" onclick='decreaseQuantity(this);'>-</a>
                                    <input 
                                        type="text" 
                                        value="0" 
                                        class="fl dataskustartnumv1"
                                        data-initial-value="${item.qty!''}"
                                        data-sku-start-num="<#if item.actPt??>${item.actPt.skuStartNum}<#elseif item.actPgpy>${item.actPgpy.skuStartNum}<#else>1</#if>"
                                        data-shop-name="${item.showName!''}"
                                        data-promo-tag="${item.promoTag!''}"
                                        data-mddata="${item.mddata!''}"
                                        data-id="${item.skuId}"
                                        data-mediumPackageNum="${item.mediumPackageNum!''}"
                                        data-isSplit="${item.isSplit}"
                                        data-act-value="0"
                                        data-type="<#if item.actPt??>3<#elseif item.actPgpy??>5<#else>1</#if>"
                                        data-rank="${itemIndex + 1}"
                                        onblur="handleCheckValue('${item.skuId}', this.getAttribute('data-sku-start-num'),this)"
                                        onclick='focus_input_click("${item.skuId}",2,this)'
                                        onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                        onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"
                                    >
                                    <a href="javascript:void(0);" class="add fl" onclick='suixinpin_sub_module_click("${item.skuId}","加",3);increaseQuantity(this);'>+</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <#assign itemIndex = itemIndex+1>
            </#list>
            </div>
            <input type="hidden" class="totalAmount" value="${orderSettle.suiXinPinSkus.totalAmount}">
            <input type="hidden" class="totalNum" value="${orderSettle.suiXinPinSkus.totalNum}">
            <input type="hidden" class="varietyNum" value="${orderSettle.suiXinPinSkus.varietyNum}">
            <#else>
                <div class="suixinpin-list" id="suixinpin_list"></div>
            </#if>
            <div class="suixinpin-footer" id="suixinpin_footer">
                <div class="suixinpin-summary">
                    <span class="summary-goods" id="summary_goods"></span>
                    <span>小计：<span class="red" id="summary_goods_red"></span></span>
                </div>
            </div>
           
        </div>
        <#include "/order/suixinpinModal.ftl">
    </body>
    <script type="text/javascript">
        $(function () {
            let scrollPosition = sessionStorage.getItem('scrollPosition');
            if (scrollPosition !== null) {
                $(window).scrollTop(scrollPosition);
                sessionStorage.removeItem('scrollPosition');
            }
            if(!getUrlParam('suiXinPinSkus')) {
                sessionStorage.removeItem("addDataNext")
            }
            let addData=sessionStorage.getItem("addDataNext")
                if(addData){
                    addData=JSON.parse(addData)
                    $(".suixinpin-item").each(function() {
                        let $item = $(this); // 当前遍历的suixinpin-item元素
                        let skuId = $item.find(".sxpskuid").val();
                        // 假设addData是对象数组，查找匹配的sku
                        let matchedData = addData.find(item => item.skuId == skuId);
                        if (matchedData) {
                            $item.find(".suixinpin-item-title").text(matchedData.showName);
                            $item.find(".dataskustartnumv1").attr("data-sku-start-num", matchedData.skuStartNum);
                            $item.find(".showMarketingSolag").text(matchedData.promoTag);
                        }
                    });
                              
            }
            const oldSuiXinPinSkus = sessionStorage.getItem("suiXinPinSkus");
            sessionStorage.removeItem("suiXinPinSkus");
            // 防抖函数
            function debounce(func, wait) {
                let timeout;
                return function() {
                    const context = this;
                    const args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(function() {
                        func.apply(context, args);
                    }, wait);
                };
            }
            // 从URL获取settleType参数
            window.settleType = getUrlParam('settleType', '1');
            window.initSkipTarget = function() {
                const shopInfoSxpListStr = sessionStorage.getItem("shopInfoSxpList");
                document.querySelectorAll('.suixinpin-item-btn-input input')
                const type = $("#suixinpin_item_type").attr("data-item-type") || 2
                if (type == 2) {
                    $('#suiTitle').text('顺手买一件')
                } else {
                    $('#suiTitle').text('随心拼')
                }
                getData(window.settleType, shopInfoSxpListStr);
            }
            
            window.initSkipTarget();
            
            function getData(settleType, shopInfoSxpList) {
                let pares = JSON.parse(shopInfoSxpList);
                let loadCount = getUrlParam('loadCount', '2');
                if(settleType == 2 && loadCount != 1) {
                    var index=layer.load(2);
                    // 清空现有列表
                    $('#suixinpin_list').empty();
                    $.ajax({
                        url: '/merchant/center/order/buySomethingCasuallyQuery',
                        type: 'GET',
                        data: {
                            merchantId: $('#merchantId').val(),
                            buySomethingCasuallyInfo: JSON.stringify({
                                shopInfoSxpList: pares,
                                buySomethingCasuallySkus: [],
                                isMore: false
                            })
                        },
                        success: function (data) {
                            if (data.code == 1000) {
                                // 处理返回的数据
                                if (data.data && data.data.length > 0) {
                                    let totalItems = 0;
                                    let totalAmount = 0;                             
                                    // 遍历返回的数组数据
                                    data.data.forEach(function(item, index) {
                                        // 构建商品项HTML
                                        var itemHtml = '<div class="suixinpin-item" data-query="yes" data-id="' + (item.id || '') + '" data-rank="' + (index + 1) + '" data-exp_id="' + (item.exp_id || '') + '">' +
                                            '<div class="suixinpin-item-left">' +
                                                '<img src="${productImageUrl}/ybm/product/min/'+ item.imageUrl +'" onerror="this.src=/static/images/default-big.png">' +
                                            '</div>' +
                                            '<div class="suixinpin-item-right">' +
                                                '<div class="suixinpin-item-title">' + (item.showName || '') + '</div>' +
                                                '<div class="suixinpin-item-validity">有效期至' + (item.nearEffect || '') + '</div>' +
                                                '<span class="showMarketingSolag">' + (item.promoTag || '') + '</span>' +
                                                '<div class="suixinpin-item-content">' +
                                                    '<div class="suixinpin-item-price">' +
                                                        '<span class="show-price">¥' + (item.price || item.fob || '0.00') + '/' + (item.productUnit || '件') + '</span>' +
                                                    '</div>' +
                                                    '<div class="suixinpin-item-btn">' +
                                                        '<div class="suixinpin-item-btn-buy" onclick="suixinpin_sub_module_click(' + item.id + ',\'加购\',1);showQuantityInput(this);">' +
                                                            '<img src="/static/images/shoppingCar.png" alt="">' +
                                                        '</div>' +
                                                        '<div class="suixinpin-item-btn-input" style="display: none;" data-id="' + (item.id || '') + '">' +
                                                            '<div class="qtData" style="display: none">' +
                                                                '<div class="expId" data-expId="' + (item.qtData ? item.qtData.expId || '' : '') + '"></div>' +
                                                                '<div class="gtListData" data-gtListData=\'' + (item.qtData ? (item.qtData.qtListData || '') : '') + '\'></div>' +
                                                                '<div class="gtSkuData" data-gtSkuData=\'' + (item.qtData ? (item.qtData.qtSkuData || '') : '') + '\'></div>' +
                                                                '<div class="rank" data-rank="' + (index + 1) + '"></div>' +
                                                                '<div class="scmId" data-scmId="' + (item.qtData ? (item.qtData.scmId || '') : '') + '"></div>' +
                                                                '<div class="productName" data-productName="' + (item.showName || '') + '"></div>' +
                                                            '</div>' +
                                                            '<a href="javascript:void(0);" class="sub fl" onclick="decreaseQuantity(this);">-</a>' +
                                                            '<input ' +
                                                                'type="text" ' +
                                                                'value="0" ' +
                                                                'class="fl" ' +
                                                                'data-initial-value="' + (item.quantity || '') + '" ' +
                                                                'data-id="' + (item.id || '') + '" ' +
                                                                'data-price="' + (item.price || item.fob) + '" ' +
                                                                'data-sku-start-num="' + ((item.actPt || {}).skuStartNum || (item.actPgby || {}).skuStartNum || '1') + '" ' +
                                                                'data-shop-name="' + (item.showName || '') + '" ' +
                                                                'data-promo-tag="' + (item.promoTag || '') + '" ' +
                                                                'data-mddata="' + (item.mddata ? item.mddata.replace(/"/g, '&quot;') : '') + '" ' +
                                                                'data-mediumPackageNum="' + (item.mediumPackageNum || '') + '" ' +
                                                                'data-isSplit="' + (item.isSplit) + '" ' +
                                                                'data-type="' + ((item.actPt) ? '3' : ((item.actPgby) ? '5' : '1')) + '" ' +
                                                                'onblur="handleCheckValue(\'' + (item.id || '') + '\', this.getAttribute(\'data-sku-start-num\'),this)" ' +
                                                                'onclick="focus_input_click(' + item.id + ',2,this)" ' +
                                                                'onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,\'\')}else{this.value=this.value.replace(/\\D/g,\'\')}" ' +
                                                                'onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,\'0\')}else{this.value=this.value.replace(/\\D/g,\'\')}" ' +
                                                            '>' +
                                                            '<a href="javascript:void(0);" class="add fl" onclick="suixinpin_sub_module_click(' + item.id + ',\'加\',3);increaseQuantity(this);">+</a>' +
                                                        '</div>' +
                                                    '</div>' +
                                                '</div>' +
                                            '</div>' +
                                        '</div>';
                                        // 添加到列表中
                                        $('#suixinpin_list').append(itemHtml);
                                    });
                                }
                                // 初始化数量输入框
                                initializeQuantityInputs();
                                layer.close(index);
                                $(".suixinpin-item").each(function(index, element) {
                                    const productId = $(this).attr("data-id");
                                    const rank = $(this).attr("data-rank");
                                    const exp_id = $(this).attr("data-exp_id");
                                    $('#suixinpin_container').css('display', 'block');
                                    window.productExposure(productId)
                                });
                            }
                        }
                    });
                    layer.close(index);
                } else {
                    initializeQuantityInputs();
                }
            }
            
            // 从地址栏获取参数的函数
            function getUrlParam(key, defaultValue = '') {
                // 优先级1：使用 URLSearchParams（现代浏览器）
                if (window.URLSearchParams) {
                    const params = new URLSearchParams(window.location.search);
                    const value = params.get(key);
                    return value !== null ? value : defaultValue;
                }
                // 优先级2：旧浏览器解析方案（兼容IE）
                const query = window.location.search.substring(1);
                const pairs = query.split('&');
                for (const pair of pairs) {
                    const [k, v] = pair.split('=');
                    if (decodeURIComponent(k) === key) {
                        return decodeURIComponent(v) || defaultValue;
                    }
                }
                return defaultValue;
            }
            
            // 创建防抖版本的更新数量函数
            window.updateQuantityDebounced = debounce(function(element) {
                updateQuantity(element);
            }, 300);
            
            // 更新数量函数
            window.updateQuantity = function(element) {
                let value = parseInt(element.value);
                if (isNaN(value) || value < 1) {
                    element.value = 1;
                    value = 1;
                }
                
                // 创建或更新 suiXinPinSkus 对象
                createSuiXinPinSkus();
                reloadPage()
            }
            
            // 增加数量
            window.increaseQuantity = function(element) {
                const input = element.parentNode.querySelector('input');
                const id = input.getAttribute('data-id');
                const type = input.getAttribute('data-type') || 1; // 获取type
                const value = addToCart(element);
                const $item = $("#suixinpin_item_type");
                if($item.length && $item.attr("data-item-type") == "1") {
                    input.value = value;
                    updateQuantityDebounced(input);
                }else {
                    window.ptChange(id, value, element, '加', type);
                }
            }
            
            // 减少数量
            window.decreaseQuantity = function(element) {
                const input = element.parentNode.querySelector('input');
                const skuStartNum = parseInt(input.getAttribute('data-sku-start-num')) || 1;
                const isSplit = input.getAttribute('data-isSplit');
                const skuId = input.getAttribute('data-id');
                const middpacking = parseInt(input.getAttribute('data-mediumPackageNum')) || 1;
                let value = parseInt(input.value) || 0;  
                // 确定步长
                let step = 1;
                if(isSplit == 0 && middpacking > 0){
                    step = middpacking;
                    if(value - step < middpacking) {
                        // 当数量小于等于起拼数时，设为0并恢复到购物车图标状态
                        const inputDiv = element.parentNode;
                        const buyBtn = inputDiv.parentNode.querySelector('.suixinpin-item-btn-buy');
                        input.value = 0;
                        inputDiv.style.display = 'none';
                        buyBtn.style.display = 'block';
                        removeFromSuiXinPinSkus(input.getAttribute('data-id'));
                        return
                    }
                }
                // 如果当前值小于等于起拼数，则不允许再减
                if (value <= skuStartNum && value - step < skuStartNum) {
                    // 当数量小于等于起拼数时，设为0并恢复到购物车图标状态
                    const inputDiv = element.parentNode;
                    const buyBtn = inputDiv.parentNode.querySelector('.suixinpin-item-btn-buy');
                    input.value = 0;
                    inputDiv.style.display = 'none';
                    buyBtn.style.display = 'block';
                    removeFromSuiXinPinSkus(input.getAttribute('data-id'));
                } else {
                    const type = input.getAttribute('data-type') || 1; // 获取type
                    const $item = $("#suixinpin_item_type");
                    if($item.length && $item.attr("data-item-type") == "1") {
                        input.value = value - step;
                        updateQuantityDebounced(input);
                    }else {
                        window.ptChange(skuId, value - step, input, '减', type)
                    }
                }
            }
            function addShowQuantityInput(element) {
                const inputDiv = element.parentNode.querySelector('.suixinpin-item-btn-input');
                const input = inputDiv.querySelector('input');
                const mediumPackageNum = parseInt(input.getAttribute('data-mediumPackageNum'));
                const skuId = input.getAttribute('data-id');
                const isSplit = input.getAttribute('data-isSplit');
                const skuStartNum = parseInt(input.getAttribute('data-sku-start-num')) || 1;
                let value = skuStartNum
                const $item = $("#suixinpin_item_type");
                if($item.length && $item.attr("data-item-type") == "1") {
                    value = mediumPackageNum;
                }else if (isSplit == 0 && mediumPackageNum > 1 && skuStartNum % mediumPackageNum !== 0) {
                    value = Math.ceil(skuStartNum / mediumPackageNum) * mediumPackageNum;
                }
                return value
            }
            // 显示数量输入框
            window.showQuantityInput = function(element) {
                const inputDiv = element.parentNode.querySelector('.suixinpin-item-btn-input');
                const input = inputDiv.querySelector('input');
                const skuId = input.getAttribute('data-id');
                const type = input.getAttribute('data-type') || 1; // 获取type
                const $item = $("#suixinpin_item_type");
                if($item.length && $item.attr("data-item-type") == "1") {
                    const value = addShowQuantityInput(element);
                    input.value = value;
                    updateQuantityDebounced(input);
                }else {
                    const value = addShowQuantityInput(element);
                    window.ptChange(skuId, value, element, '加购', type)
                }
            }
            
            // 更新总计
            function updateTotalSummary() {
                var totalAmount = 0 //商品总金额
                var totalNum = 0  //商品的总数量
                var varietyNum = 0 //商品品种
                let totalAmountDom = document.querySelectorAll(".totalAmount")
                totalAmountDom.forEach(function(item) {
                    totalAmount += parseFloat(item.value); 
                });
                let totalNumDom = document.querySelectorAll(".totalNum")
                totalNumDom.forEach(function(item) {
                    totalNum += parseFloat(item.value); 
                });
                let varietyNumDom = document.querySelectorAll(".varietyNum")
                varietyNumDom.forEach(function(item) {
                    varietyNum += parseFloat(item.value); 
                });
                // 更新底部汇总信息
                $('#summary_goods').text('共' + varietyNum + '种/' + totalNum + '件商品');
                $('#summary_goods_red').text('￥' + totalAmount.toFixed(2));
            }
            updateTotalSummary()
            
            // 创建 suiXinPinSkus 对象
            function createSuiXinPinSkus() {
                const suiXinPinSkus = [];
                const qtDataLocal = JSON.parse(sessionStorage.getItem("qtDataLocal")) || [];
                const mddataLocal = JSON.parse(sessionStorage.getItem("mddataLocal")) || [];
                // 获取所有显示的输入框
                const visibleInputs = document.querySelectorAll('.suixinpin-item-btn-input input');
                let newQtDataLocal = []
                visibleInputs.forEach(function(input) {
                    const quantity = parseInt(input.value) || 0;
                    // 检查是否已经有相同skuId的项
                    const skuId = parseInt(input.getAttribute('data-id')) || 0;
                    const existingIndex = suiXinPinSkus.findIndex(item => item?.skuId === skuId);
                    const qtDataElement = input.closest('.suixinpin-item-btn-input').querySelector('.qtData');
                    let qtData = null;
                    if (qtDataElement) {
                        qtData = {
                            expId: qtDataElement.querySelector('.expId')?.dataset?.expid || '',
                            qtListData: JSON.parse(qtDataElement.querySelector('.gtListData')?.dataset?.gtlistdata || '{}'),
                            qtSkuData: JSON.parse(qtDataElement.querySelector('.gtSkuData')?.dataset?.gtskudata || '{}'),
                            rank: qtDataElement.querySelector('.rank')?.dataset?.rank || '',
                            scmId: qtDataElement.querySelector('.scmId')?.dataset?.scmid || '',
                            productName: qtDataElement.querySelector('.productName')?.dataset?.productname || '',
                        };
                    } else {
                        //第二次进
                        //需要过滤页面离没有的数据，通过skuId判断
                        const index = input.getAttribute('data-rank')
                        let qtObj = qtDataLocal.filter(item => {
                            if (item && item.skuId === skuId) {
                                item.rank = index;
                                item.qtSkuData.rank = index;
                                return true;
                            } 
                            return false;
                        });
                        newQtDataLocal = [...newQtDataLocal, ...qtObj];
                    }
                    if(qtData) {
                        qtData.skuId = skuId;
                        const qtDataIndex = qtDataLocal.findIndex(item => item.skuId === skuId);
                        if (qtDataIndex >= 0) {
                            // 更新已存在的对象
                            qtDataLocal[qtDataIndex] = qtData;
                        } else {
                            // 添加新对象
                            qtDataLocal.push(qtData);
                        }
                    }
                    const mddata = JSON.parse(input.getAttribute('data-mddata') || '{}');
                    if(Object.keys(mddata).length > 0) {
                        mddata.skuId = skuId;
                        const mddataIndex = mddataLocal.findIndex(item => item.skuId === skuId);
                        if (mddataIndex >= 0) {
                            // 更新已存在的对象
                            mddataLocal[mddataIndex] = mddata;
                        } else {
                            mddataLocal.push(mddata);
                        }
                    }
                    if (existingIndex >= 0) {
                        // 更新已存在的项
                        suiXinPinSkus[existingIndex].quantity = quantity;
                    } else {
                        const $item = $("#suixinpin_item_type")
                        const type = $item.length && $item.attr("data-item-type") || 2
                        // 添加新项
                        suiXinPinSkus.push({
                            skuId: skuId,
                            quantity: quantity,
                            type: type, // 默认类型
                            skuStartNum: parseInt(input.getAttribute('data-sku-start-num')) || 1,
                            mddata: "",
                            showName: input.getAttribute('data-shop-name') || "",
                            promoTag: input.getAttribute('data-promo-tag') || "",
                            qtData: qtData ? qtData : input.getAttribute('data-qtData') || "",
                        });
                    }
                });
                if (addData) {
                    addData.forEach(item => {
                        suiXinPinSkus.some(it => {
                            if (it && item && it.skuId == item.skuId) {
                                it.showName = it.showName || item.showName;
                                it.skuStartNum = it.skuStartNum || item.skuStartNum;
                                it.promoTag = it.promoTag || item.promoTag;
                                return true;
                            }
                            return false;
                        })
                    })
                }
                sessionStorage.setItem("suiXinPinSkus", JSON.stringify(suiXinPinSkus));
                if (newQtDataLocal.length > 0) {
                    sessionStorage.setItem("qtDataLocal", JSON.stringify(newQtDataLocal));
                }else {
                    sessionStorage.setItem("qtDataLocal", JSON.stringify(qtDataLocal));
                }
                sessionStorage.setItem("mddataLocal", JSON.stringify(mddataLocal));
                return suiXinPinSkus;
            }
            
            // 从suiXinPinSkus中移除指定ID的商品
            function removeFromSuiXinPinSkus(skuId) {
                skuId = parseInt(skuId) || 0;
                if (skuId) {
                    // 获取当前的suiXinPinSkus
                    const suiXinPinSkusStr = sessionStorage.getItem("suiXinPinSkus");
                     if (suiXinPinSkusStr) {
                        let suiXinPinSkus = JSON.parse(suiXinPinSkusStr);
                        suiXinPinSkus = suiXinPinSkus.map(item => {
                            if (item.skuId === skuId) {
                                return {...item, quantity: 0};
                            }
                            return item;
                        });
                        sessionStorage.setItem("suiXinPinSkus", JSON.stringify(suiXinPinSkus));
                    }
                    reloadPage()
                }
            }
            
            // 初始化数量输入框
            function initializeQuantityInputs() {
                const items = document.querySelectorAll('.suixinpin-item');
                items.forEach(item => {
                    const input = item.querySelector('.suixinpin-item-btn-input input');
                    const buyBtn = item.querySelector('.suixinpin-item-btn-buy');
                    const inputDiv = item.querySelector('.suixinpin-item-btn-input');

                    const initialValue = input.getAttribute('data-initial-value');
                    if (initialValue && initialValue !== '' && initialValue !== '0') {
                        input.value = initialValue;
                        buyBtn.style.display = 'none';
                        inputDiv.style.display = 'flex';
                    }
                });
                createSuiXinPinSkus();
            }
            
            // 购买更多按钮点击事件
            const suixinpin_buyMore =  document.getElementById('suixinpin_buyMore').addEventListener('click', function() {
                if (window.initSuixinpinModal) {
                    const $item = $("#suixinpin_item_type")
                    const type = $item.length && $item.attr("data-item-type") || 2
                    window.initSuixinpinModal(type);
                    $('#suixinpin-modal').modal({backdrop: 'static', keyboard: false, show: true});
                }
                else alert("正在加载");
            });

            function getAllUrlParams() {
                const params = {};
                const queryString = window.location.search.substring(1);

                if (window.URLSearchParams) {
                    new URLSearchParams(window.location.search).forEach((value, key) => {
                        params[key] = value;
                    });
                    return params;
                }

                const pairs = queryString.split('&');
                for (const pair of pairs) {
                    if (!pair) continue; // 跳过空字符串
                    const [key, value] = pair.split('=');
                    const decodedKey = decodeURIComponent(key);
                    const decodedValue = decodeURIComponent(value || ''); // 处理无值的情况（如 ?foo）
                    params[decodedKey] = decodedValue;
                }

                return params;
            }
            // 重现加载页面
            function reloadPage() {
                sessionStorage.setItem('scrollPosition', $(window).scrollTop());
                const urlParams = getAllUrlParams();
                const suiXinPinSkusStr = sessionStorage.getItem("suiXinPinSkus");
                let skus = suiXinPinSkusStr ? JSON.parse(suiXinPinSkusStr) : [];
                skus = skus.map(({ mddata, qtData, ...rest }) => {
                    const result = { ...rest };
                    return result;
                });
                //存所有skus
                sessionStorage.setItem("addDataNext",JSON.stringify(skus))
                //然后过滤字段
                skus=skus.map(e=>{
                    return {
                        skuId:e.skuId,
                        quantity:e.quantity,
                        type:e.type
                    }
                })
                urlParams.loadCount = 1
                urlParams.suiXinPinSkus = JSON.stringify(skus);
                const currentUrl = window.location.href;
                const newUrl = currentUrl.split('?')[0] + '?' + new URLSearchParams(urlParams).toString();
                window.location.replace(newUrl)
            }
            // 封装数量加购
            function addToCart(element) {
                const input = element.parentNode.querySelector('input');
                const mediumPackageNum = parseInt(input.getAttribute('data-mediumPackageNum'));
                const skuStartNum = parseInt(input.getAttribute('data-sku-start-num'));
                const isSplit = input.getAttribute('data-isSplit');
                let value = parseInt(input.value);
                if(isNaN(value)) {
                    const $item = $("#suixinpin_item_type")
                    if($item.length && $item.attr("data-item-type") == "1") {
                        value = mediumPackageNum;
                    }else if (isSplit == 0 && mediumPackageNum > 1 && skuStartNum % mediumPackageNum !== 0) {
                        value = Math.ceil(skuStartNum / mediumPackageNum) * mediumPackageNum;
                    }else {
                        value = skuStartNum;
                    }
                }
                let countNum = 1;
                if(mediumPackageNum) {
                    value += mediumPackageNum;
                }else {
                    value += countNum;
                }
                return value; 
            }

            // 拼团加购动态更新拼团价
            window.ptChange = function (ptId, num, element, text, type) {
                // 如果没有传入type，则尝试从元素中获取
                if (!type && element) {
                    const input = (text === 'input') ? element : 
                                 (text === '加购') ? element.parentNode.querySelector('.suixinpin-item-btn-input input') : 
                                 element.parentNode.querySelector('input');
                    
                    if (input) {
                        // 获取商品类型
                        type = input.getAttribute('data-type') || 1; // 默认为3
                    } else {
                        type = 1;
                    }
                }
                var merchantId = $("#merchantId").val();
                $.ajax({
                    url: "/merchant/center/cart/group/changeCart",
                    type: "POST",
                    dataType: "json",
                    data: {
                        merchantId: merchantId,
                        quantity: num,
                        skuId: ptId,
                        ProductType: type,
                        bizScene: type, // 使用传入的type
                    },
                    success: function(res){
                        if(res.status == "success"){
                            if (res.data.message) {
                                $.alert({
                                    title: '提示',
                                    body: res.data.message
                                });
                                if(text == 'input') {
                                    if(!element.value) {
                                        updateQuantityDebounced(element);
                                    }else {
                                        let value = addShowQuantityInput(element);
                                        element.value = value;
                                        updateQuantityDebounced(element);
                                    }
                                }
                            }else {
                                let value
                                if(text == '加') {
                                    const input = element.parentNode.querySelector('input');
                                    value = addToCart(element);
                                    input.value = value;
                                    // 触发更新
                                    updateQuantityDebounced(input);
                                }else if (text == '加购') {
                                    value = addShowQuantityInput(element);
                                    element.style.display = 'none';
                                    const inputDiv = element.parentNode.querySelector('.suixinpin-item-btn-input');
                                    inputDiv.style.display = 'flex';
                                    const input2 = inputDiv.querySelector('input');
                                    input2.value = value;
                                    // 触发更新
                                    updateQuantityDebounced(input2);
                                }else if (text == 'input') {
                                    element.value = num;
                                    updateQuantityDebounced(element);
                                }else if(text == '减') {
                                    element.value = num;
                                    updateQuantityDebounced(element);
                                }
                            }
                        }else {
                            $.alert({
                                title: '提示',
                                body: res.errorMsg
                            });
                        }
                    },
                    error: function(){
                        $.alert({
                            title: '提示',
                            body: '加购异常'
                        });
                    }
                });
            }
            // 添加handleCheckValue函数
            window.handleCheckValue = function(id, skuStartNum,element) {
                const input = document.querySelector('input[data-id="'+id+'"]');
                if (!input) return;
                
                let num = parseFloat(input.value);
                if (isNaN(num)) num = 0;
                
                const isSplit = input.getAttribute('data-isSplit');
                const middpacking = parseInt(input.getAttribute('data-mediumPackageNum')) || 1;
                const type = input.getAttribute('data-type') || 1; // 获取type
                
                // 如果数量小于起拼数，则设为起拼数
                if (num < skuStartNum) {
                    num = skuStartNum;
                }
                
                // 如果不可拆零，确保数量是中包装的倍数
                if (isSplit == 0 && middpacking > 0) {
                    const remainder = num % middpacking;
                    if (remainder > 0) {
                        num = num - remainder;
                        // 如果结果为0，则至少保留一个中包装数量
                        if (num < middpacking) {
                            num = middpacking;
                        }
                    }
                }
                const $item = $("#suixinpin_item_type");
                if($item.length && $item.attr("data-item-type") == "1") {
                    element.value = num;
                    updateQuantityDebounced(element);
                }else {
                    window.ptChange(id, num, input, 'input', type);
                }
                
            }
        });
        $(document).ready(function() {
            function getSkuDataFromSession(skuId, isModal = 0) {
                const suiXinPinSkusStr = isModal == 0 ? sessionStorage.getItem("qtDataLocal") : sessionStorage.getItem("modelQtDataLocal");
                if (suiXinPinSkusStr) {
                    const skus = JSON.parse(suiXinPinSkusStr);
                    const target = skus.find(item => item.skuId == parseInt(skuId));
                    return target || { rank: '', expId: '',scmId: '',qtSkuData: {},qtListData: {},productName: '' };
                }
                return { rank: '', expId: '',scmId: '',qtSkuData: {},qtListData: {},productName: '' };
            }
            // 埋点逻辑，子模块点击
            window.suixinpin_sub_module_click = function (productId,text,position,isModal = 0) {
                try {
                    const { rank, expId, scmId,qtListData,qtSkuData,productName } = getSkuDataFromSession(productId, isModal);
                    let spm_c
                    let scm_b
                    if(window.settleType == '2') {
                        spm_c = "convenientList@7."
                        scm_b = expId
                    }else {
                        const $item = $("#suixinpin_item_type");
                        if($item.length && $item.attr("data-item-type") == "1") {
                            spm_c = "arbitraryList@7."
                            scm_b = "0"
                        }else {
                            spm_c = "convenientList@7."
                            scm_b = expId
                        }
                    }
                    let scmE = scmId + window.scmEShopDetail(6);
                    const qtdata = {
                        spm_cnt: "1_4.orderPendingConfirmation_0-0_0." + spm_c + (isModal === 1 ? "ftFloatBuyMore@Z_" : "") + "prod@" + rank + "_btn@" + position + '.' + window.getSpmE(),
                        scm_cnt:  "recommend."+scm_b+".all_0.prod-" + productId + "_text-" + text + "." + scmE,
                        qt_list_data: JSON.stringify(qtListData),
                        qt_sku_data: JSON.stringify(qtSkuData),
                        product_name: productName,
                        product_id: parseInt(productId)
                    }
                    aplus_queue.push({
                        'action': 'aplus.record',
                        'arguments': ['action_product_button_click', 'CLK',qtdata]
                    });
                    productClk(productId,isModal,scmE)
                }catch(e) {}
            }
            window.focus_input_click  = function (productId,position,element,isModal = 0) {
                const value = element.value;
                suixinpin_sub_module_click(productId,value,position,isModal)
            }
            // 埋点逻辑商品曝光
            window.productExposure = function (productId,isModal = 0) {
                try {
                    if(!productId) return
                    const { rank, expId, scmId,qtListData,qtSkuData,productName } = getSkuDataFromSession(productId,isModal);
                    let spm_c
                    let scm_b
                    if(window.settleType == '2') {
                        spm_c = "convenientList@7."
                        scm_b = expId
                    }else {
                        const $item = $("#suixinpin_item_type");
                        if($item.length && $item.attr("data-item-type") == "1") {
                            spm_c = "arbitraryList@7."
                            scm_b = "0"
                        }else {
                            spm_c = "convenientList@7."
                            scm_b = expId
                        }
                    }
                    const qtdata = {
                        spm_cnt: "1_4.orderPendingConfirmation_0-0_0." + spm_c + (isModal === 1 ? "ftFloatBuyMore@Z_" : "") + "prod@" + rank + '.' + window.getSpmE(),
                        scm_cnt: "recommend."+scm_b+".all_0.prod-" + productId + "." + scmId,
                        qt_list_data: JSON.stringify(qtListData),
                        qt_sku_data: JSON.stringify(qtSkuData),
                        product_name: productName,
                        product_id: parseInt(productId)
                    }
                    aplus_queue.push({
                        'action': 'aplus.record',
                        'arguments': ['page_list_product_exposure', 'EXP', qtdata]
                    })
                }catch(e) {
                    console.log(e)
                }
            }
            // 列表商品点击
            window.productClk  = function(productId,isModal = 0, scmE = '') {
                 try {
                    const { rank, expId, scmId,qtListData,qtSkuData,productName } = getSkuDataFromSession(productId,isModal);
                    let spm_c
                    let scm_b
                    if(window.settleType == '2') {
                        spm_c = "convenientList@7."
                        scm_b = expId
                    }else {
                        const $item = $("#suixinpin_item_type");
                        if($item.length && $item.attr("data-item-type") == "1") {
                            spm_c = "arbitraryList@7."
                            scm_b = "0"
                        }else {
                            spm_c = "convenientList@7."
                            scm_b = expId
                        }
                    }
                    let newScmE = scmE || scmId + window.scmEShopDetail(6);
                    const qtdata = {
                        spm_cnt: "1_4.orderPendingConfirmation_0-0_0." + spm_c + (isModal === 1 ? "ftFloatBuyMore@Z_" : "") + "prod@" + rank + '.' + window.getSpmE(),
                        scm_cnt:  "recommend."+scm_b+".all_0.prod-" + productId + "." + newScmE,
                        qt_list_data: JSON.stringify(qtListData),
                        qt_sku_data: JSON.stringify(qtSkuData),
                        product_name: productName,
                        product_id: parseInt(productId)
                    }
                    aplus_queue.push({
                        'action': 'aplus.record',
                        'arguments': ['action_list_product_click', 'CLK',qtdata]
                    });
                }catch(e) {}
            }
            $(".suixinpin-item").each(function(index, element) {
                const productId = $(this).attr("data-id");
                const rank = $(this).attr("data-rank");
                const exp_id = $(this).attr("data-exp_id");
                const query = $(this).attr("data-query") || null;
                if(!query) {
                    window.productExposure(productId)
                }
            });
        });
    </script>
</html>
