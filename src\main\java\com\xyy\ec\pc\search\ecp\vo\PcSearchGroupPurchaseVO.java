package com.xyy.ec.pc.search.ecp.vo;

import java.io.Serializable;
import java.util.List;

/**
 * 搜索商品卡片信息 VO
 *
 * <AUTHOR>
 */
public class PcSearchGroupPurchaseVO implements Serializable {

    /**
     * 主商品。
     */
    private PcSearchProductVO mainProduct;

    /**
     * 副品商品列表。
     */
    private List<PcSearchProductVO> subProducts;

    /**
     * 展示标题
     */
    private String title;

    /**
     * 轮播时间 单位S
     */
    private Integer carouselTime;

    public PcSearchProductVO getMainProduct() {
        return mainProduct;
    }

    public void setMainProduct(PcSearchProductVO mainProduct) {
        this.mainProduct = mainProduct;
    }

    public List<PcSearchProductVO> getSubProducts() {
        return subProducts;
    }

    public void setSubProducts(List<PcSearchProductVO> subProducts) {
        this.subProducts = subProducts;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getCarouselTime() {
        return carouselTime;
    }

    public void setCarouselTime(Integer carouselTime) {
        this.carouselTime = carouselTime;
    }
}
