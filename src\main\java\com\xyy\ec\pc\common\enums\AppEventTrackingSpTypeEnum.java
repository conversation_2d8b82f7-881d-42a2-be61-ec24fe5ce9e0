package com.xyy.ec.pc.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * APP埋点spType枚举。供于app项目埋点。type唯一。
 *
 * <AUTHOR>
 */
@Getter
public enum AppEventTrackingSpTypeEnum {

    /**
     * 搜索
     */
    SEARCH("3", "1", "搜索"),
    /**
     * 搜索无结果热销精选
     */
    SEARCH_NO_RESULT_HOT_SALE("4", "3", "搜索无结果热销精选"),
    SXP_SSM("5", "43", "随心拼中的顺手买"),
    ;
    /**
     * 枚举内部类型，唯一。
     */
    private String type;

    /**
     * 埋点spType
     */
    private String spType;

    private String name;

    AppEventTrackingSpTypeEnum(String type, String spType, String name) {
        this.type = type;
        this.spType = spType;
        this.name = name;
    }

    /**
     * 自定义 valueOf()方法，通过type转化
     *
     * @param type
     * @return
     */
    public static AppEventTrackingSpTypeEnum valueOfCustom(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        for (AppEventTrackingSpTypeEnum anEnum : values()) {
            if (Objects.equals(anEnum.getType(), type)) {
                return anEnum;
            }
        }
        return null;
    }

}
