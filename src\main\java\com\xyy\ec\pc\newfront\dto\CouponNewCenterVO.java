package com.xyy.ec.pc.newfront.dto;

import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponCsuImageDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CouponNewCenterVO {

    /**
     * 主键
     */
    private Long templateId;

    /**
     * 店铺来源，1ec,2店铺
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingChannelTypeEnum
     */
    private Integer shopType;


    /**
     * 券模板名称
     */
    private String templateName;

    /**
     * 券模板类型(1-通用券，2-厂家券，3-折扣券，4-礼品券,5-新人券，6-叠加券)
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum
     */
    private Integer voucherType;

    /**
     * 券模板类型(1-通用券，2-厂家券，3-折扣券，4-礼品券,5-新人券，6-叠加券)说明
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum
     */
    private String voucherTypeDesc;

    /**
     * 折扣类型 默认0。 1为折扣类型。
     * PS：优惠额度为折扣额度时，此为1。此字段根据discount判定得出。
     */
    private Integer voucherState = 0;

    /**
     * 券面文案
     */
    private String voucherTitle;

    /**
     * 券包含金额
     */
    private BigDecimal moneyInVoucher;

    /**
     * 起用券最低消费金额
     */
    private BigDecimal minMoneyToEnable;

    /**
     * 优惠券最高抵扣金额
     */
    private BigDecimal maxMoneyInVoucher;

    /**
     * 生效时间
     */
    private Date validDate;

    /**
     * 生效时间字符串 yyyy.MM.dd
     */
    private String validDateToString;

    /**
     * 失效时间
     */
    private Date expireDate;

    /**
     * 失效时间字符串 yyyy.MM.dd
     */
    private String expireDateToString;


    /**
     * 1:全部商品参与 2:指定商品参与 3:指定商品不参与
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.RelationTypeEnum
     */
    private Integer skuRelationType;

    /**
     * 商品券 相关商品ID
     */
    private List<Long> assignProductIds;

    /**
     * 券使用方式 0满，1每满，2阶梯
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherReduceTypeEnum
     */
    private Integer voucherUsageWay;

    /**
     * 折扣额度
     */
    private BigDecimal discount;

    /**
     * 店铺名称
     */
    private String shopName;
    private String shopLogoUrl;

    /**
     * 跨店券是否指定店铺
     */
    private Boolean isDesignateShop = Boolean.FALSE;

    /**
     * 描述
     */
    private String voucherInstructions;


    /**
     * 展示图片
     */
    private String describeUrl;



    /**
     * 是否已经领取
     */
    private Integer isLq;

    /**
     * 商品券上的商品图片集合
     */
    private List<CouponCsuImageDto> voucherSkuImages;

    /**
     * 券类型描述，与voucherTypeDesc一致
     */
    private String voucherTypeText;

    private String pcUrl;

    /**
     * 券最低使用描述
     */
    private String minMoneyToEnableDesc;

    /**
     * 优惠券最高抵扣金额描述
     */
    private String maxMoneyInVoucherDesc;

    /**
     * 发送次数，实际存储的是总库存量，如果是多段则存储当前段的库存数量
     */
    private Integer provideTotalCount;

    /**
     * 领取量
     */
    private Integer receiveTotalCount;

    /**
     * 开始时间戳
     */
    private Long startTimeStamp;

    /**
     * 结束时间戳
     */
    private Long endTimeStamp;

    /**
     * 使用状态（1、即将开始 2、立即领取 3、立即使用 4、已使用 5、已抢光），对应MarketingCouponActivityStatusEnum
     */
    private Integer activityState;


    /**
     * 有效时间（单位：天）
     */
    private Integer validDays;
    /**
     * 有效时间字符串（单位：天）
     */
    private String validDayStr;

    private String shopCode;

    /**
     * 优惠券剩余数量
     */
    private Integer couponSurplusCount;

    /**
     * 券展示开始时间
     */
    private Date showStartDate;
    /**
     * 券展示结束时间
     */
    private Date showEndDate;

    /**
     * 优惠券可领取时间
     */
    private Date receiveValidDate;
    private String receiveValidDateToString;

    /**
     * 优惠券领取结束时间
     */
    private Date receiveExpireDate;
    private String receiveExpireDateToString;

    /**
     * 当前时间
     */
    private Long currentDate;

}
