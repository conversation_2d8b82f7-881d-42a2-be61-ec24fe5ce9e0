package com.xyy.ec.pc.controller.layout;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.api.AppDialogBuinseseApi;
import com.xyy.ec.layout.buinese.api.FrameManageBusinessApi;
import com.xyy.ec.layout.buinese.dto.CmsDialogDto;
import com.xyy.ec.layout.buinese.dto.CmsDialogTypeDto;
import com.xyy.ec.layout.buinese.ecp.enums.CmsClientTypeEnum;
import com.xyy.ec.layout.buinese.ecp.enums.CmsDialogSceneTypeEnum;
import com.xyy.ec.layout.buinese.ecp.enums.CmsDialogTypeEnum;
import com.xyy.ec.marketing.hyperspace.api.VoucherForCmsApi;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponBaseDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

@Controller
@RequestMapping("/layout/frame")
public class FrameManagerController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(FrameManagerController.class);

    @Reference(version = "1.0.0")
    private FrameManageBusinessApi frameManageBusinessApi;
    @Reference(version = "1.0.0")
    private AppDialogBuinseseApi appDialogBuinseseApi;
    @Reference(version = "1.0.0")
    private VoucherForCmsApi voucherForCmsApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Autowired
    private Config config;

    @RequestMapping("/getFrame")
    @ResponseBody
    public Object getFrame(HttpServletRequest request, String branchCode) {
        return this.addResult();
        /*Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (null == merchant || 0 == merchant.getId()) {
                return this.addError("必要的参数不可为空");
            }
            merchantId = merchant.getId();
            branchCode = merchant.getRegisterCode();
            FrameManageBusinessDto frameManageBusinessDto = frameManageBusinessApi.getFrameManageByMerchantId(merchantId, branchCode);
            if (null == frameManageBusinessDto || CollectionUtils.isEmpty(frameManageBusinessDto.getFrameImageList())) {
                return this.addResult();
            }
            long frameId = frameManageBusinessDto.getId();
            //获取该弹框的图片配置
            List<FrameImageBusinessDTO> frameImageBusinessDTOList = frameManageBusinessDto.getFrameImageList();
            //过滤出属于pc的弹框图片
            List<FrameImageBusinessDTO> pcFrameImageList = frameImageBusinessDTOList.stream().filter(frameImageBusinessDTO -> frameImageBusinessDTO.getImageBelong() == LayoutConstants.FrameImageBelongEnum.BELONG_PC.getBelong()).collect(Collectors.toList());
            //过滤不为空的图片地址
            List<String> pcImageUrlList = pcFrameImageList.stream().filter(frameImageBusinessDTO -> StringUtils.isNotBlank(frameImageBusinessDTO.getImageUrl()))
                    .map(frameImageBusinessDTO -> config.getProductImagePathUrl().concat(frameImageBusinessDTO.getImageUrl())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(pcImageUrlList)) {
                return this.addResult();
            }
            Map<String, Object> resultMap = this.addResult();
            resultMap.put("frameId", frameId);
            resultMap.put("imageUrls", pcImageUrlList);
            resultMap.put("pcJumpUrl", frameManageBusinessDto.getPcJumpUrl());
            return resultMap;
        } catch (Exception e) {
            logger.error("用户: {} ,branchCode: {}获取弹窗信息异常", merchantId, branchCode, e);
            return this.addError("获取弹窗异常");
        }*/
    }

    @RequestMapping("/seeFrame")
    @ResponseBody
    public Object seeFrame(HttpServletRequest request, Long merchantId, Long frameId) {
        return this.addResult();
        /*try {
            if (null == merchantId || 0 == merchantId || null == frameId) {
                return this.addError("必要的参数不可为空");
            }
            frameManageBusinessApi.seeFrameByMerchantId(merchantId, frameId);
            return this.addResult();
        } catch (Exception e) {
            logger.error("记录用户: {},frameId: {}弹窗信息异常", merchantId, frameId, e);
            return this.addError("记录用户弹窗异常");
        }*/
    }

    /**
     * cms弹窗
     * @param sceneType
     * @return
     */
    @RequestMapping(value = "/getNewCmsDialog", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public XyyJsonResult getNewCmsDialog(Integer sceneType) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null || merchant.getId() == null) {
                return XyyJsonResult.createSuccess();
            }
            Long merchantId = merchant.getId();
            CmsDialogSceneTypeEnum cmsDialogSceneTypeEnum = CmsDialogSceneTypeEnum.valueOfCustom(sceneType);
            if (cmsDialogSceneTypeEnum == null) {
                return XyyJsonResult.createSuccess();
            }
            String branchCode = getBranchCodeByMerchantId(merchantId);
            // TODO
            ApiRPCResult<CmsDialogTypeDto> apiRPCResult = appDialogBuinseseApi.hasNewCmsDialog(merchantId,
                    branchCode, sceneType, CmsClientTypeEnum.PC.getType());
            if (apiRPCResult == null) {
                logger.info("【cms】新版首页弹窗，接口hasNewCmsDialog异常, merchantId：{}，sceneType：{}", merchantId, sceneType);
                return XyyJsonResult.createSuccess();
            }
            if (!apiRPCResult.isSuccess()) {
                logger.error("【cms】新版首页弹窗，接口hasNewCmsDialog异常, merchantId：{}，sceneType：{},msg:{} ",
                        merchantId, sceneType, apiRPCResult.getMsg());
                return XyyJsonResult.createSuccess();
            }
            CmsDialogTypeDto cmsDialogTypeDto = apiRPCResult.getData();
            if (cmsDialogTypeDto == null) {
                return XyyJsonResult.createSuccess();
            }
            String pageId = cmsDialogTypeDto.getPageId();
            ApiRPCResult<CmsDialogDto> detailApiRPCResult = appDialogBuinseseApi.getNewCmsDialog(branchCode, pageId);
            if (!detailApiRPCResult.isSuccess()) {
                logger.error("【cms】新版首页弹窗，接口getNewCmsDialog异常, merchantId：{}，sceneType：{},pageId:{},msg:{} ",
                        merchantId, sceneType, pageId, apiRPCResult.getMsg());
                return XyyJsonResult.createSuccess();
            }
            CmsDialogDto cmsDialogDto = detailApiRPCResult.getData();
            if (cmsDialogDto == null) {
                return XyyJsonResult.createSuccess();
            }
            List<Long> couponIds = cmsDialogDto.getCouponIds();
            if (CollectionUtils.isEmpty(couponIds) && Objects.equals(cmsDialogDto.getDialogType(), CmsDialogTypeEnum.COUPON.getType())) {
                return XyyJsonResult.createSuccess();
            }
            if (CollectionUtils.isNotEmpty(couponIds) && Objects.equals(cmsDialogDto.getDialogType(), CmsDialogTypeEnum.COUPON.getType())) {
                ApiRPCResult<List<CouponBaseDto>> couponApiRPCResult = voucherForCmsApi.listCouponByIdsWithFilter(merchantId, couponIds);
                if (!couponApiRPCResult.isSuccess()) {
                    logger.warn("【cms】弹窗listCouponByIds异常,merchantId:{},pageId:{},couponIds:{},msg:{}", merchantId, pageId, couponIds, apiRPCResult.getMsg());
                    return XyyJsonResult.createSuccess();
                }
                List<CouponBaseDto> data = couponApiRPCResult.getData();
                if (CollectionUtils.isEmpty(data)) {
                    return XyyJsonResult.createSuccess();
                }
                cmsDialogDto.setCouponDtos(data);
            }
            return XyyJsonResult.createSuccess().addResult("detail", cmsDialogDto);
        } catch (Exception e) {
            logger.error("cms新版首页弹窗异常， sceneType：{} ", sceneType, e);
        }
        return XyyJsonResult.createSuccess();
    }
}
