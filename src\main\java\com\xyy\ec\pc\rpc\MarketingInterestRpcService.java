package com.xyy.ec.pc.rpc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.interest.api.RedPacketApi;
import com.xyy.ec.marketing.interest.dto.redPacket.ConsumtionReturnRedPacketRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 */
@Slf4j
@Service
public class MarketingInterestRpcService {

    @Reference(version = "1.0.0")
    private RedPacketApi redPacketApi;

    public List<ConsumtionReturnRedPacketRecordDTO> getConsumptionReturnRedPacketRecord(Long templateId){
        List<ConsumtionReturnRedPacketRecordDTO> retList = Lists.newArrayList();
        try {
            if (log.isDebugEnabled()) {
                log.debug("MarketingInterestRpcService.getConsumtionReturnRedPacketRecord start, templateId:{}", templateId);
            }
            ApiRPCResult<List<ConsumtionReturnRedPacketRecordDTO>> result = redPacketApi.getConsumptionReturnRedPacketRecord(templateId);
            if (log.isDebugEnabled()) {
                log.debug("MarketingInterestRpcService.getConsumtionReturnRedPacketRecord success, templateId:{}, result:{}", templateId, JSONObject.toJSONString(result.getData()));
            }
            if (result.isFail()) {
                log.info("MarketingInterestRpcService.getConsumtionReturnRedPacketRecord fail, templateId:{}, result:{}", templateId, JSONObject.toJSONString(result.getData()));
            }
            retList = result.getData();
        } catch (Exception e) {
            log.error("查询领取红包记录入参:templateId:{}, msg:{}",templateId, e);
            throw e;
        }
        return retList;
    }

}
