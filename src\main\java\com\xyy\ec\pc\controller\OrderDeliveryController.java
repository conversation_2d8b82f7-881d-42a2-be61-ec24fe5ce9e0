package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Maps;
import com.xyy.ec.order.business.api.OrderDeliveryBusinessApi;
import com.xyy.ec.order.business.dto.OrderDeliveryBusinessDto;
import com.xyy.ec.pc.base.BaseController;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/orderDelivery")
public class OrderDeliveryController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(OrderDeliveryController.class);

    @Reference(version = "1.0.0")
    private OrderDeliveryBusinessApi orderDeliveryBusinessApi;


    /**
     * @param orderNo
     * @return org.springframework.web.servlet.ModelAndView
     * @throws
     * @Description: 查询订单物流
     * <AUTHOR>
     * @date 2019-01-07 14:12
     */
    @RequestMapping("/getOrderDeliveryByOrderNo")
    @ResponseBody
    public ModelAndView getOrderDeliveryByOrderNo(String orderNo) throws Exception {
        Map<String, Object> dataMap = Maps.newHashMap();
        try {
            if (StringUtils.isNotEmpty(orderNo)) {
                List<OrderDeliveryBusinessDto> orderDeliveryMessageList = orderDeliveryBusinessApi.getOrderDeliveryMessageList(orderNo);
                if (CollectionUtils.isNotEmpty(orderDeliveryMessageList)) {
                    dataMap.put("orderDeliveryMessageList", orderDeliveryMessageList);
                }
            }
        } catch (Exception e) {
            logger.error("订单物流记载失败：" + e.getMessage());
            throw e;
        }
        return new ModelAndView("/order/orderDelivery.ftl", dataMap);
    }

}


