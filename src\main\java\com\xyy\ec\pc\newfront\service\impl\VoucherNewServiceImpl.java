package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.api.HeavenlyVoucherBuineseApi;
import com.xyy.ec.marketing.hyperspace.api.VoucherForCmsApi;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponBaseDto;
import com.xyy.ec.marketing.insight.api.InsightChosenCustomerAdminApi;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupBaseInfoDTO;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.newfront.dto.CouponRespVO;
import com.xyy.ec.pc.newfront.service.VoucherNewService;
import com.xyy.ec.pc.newfront.vo.CmsCouponBaseVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ms.promotion.business.api.exp.VoucherForAdminBusinessExpApi;
import com.xyy.ms.promotion.business.api.pc.VoucherForPcBusinessApi;
import com.xyy.ms.promotion.business.common.ErrorCodeEum;
import com.xyy.ms.promotion.business.common.ResultDTO;
import com.xyy.ms.promotion.business.dto.voucher.ReceiveVoucherRequestDTO;
import com.xyy.ms.promotion.business.dto.voucher.VoucherExtendDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VoucherNewServiceImpl implements VoucherNewService {

    @Reference(version = "1.0.0")
    private HeavenlyVoucherBuineseApi heavenlyVoucherBuineseApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private VoucherForPcBusinessApi promotionVoucherApi;

    @Reference(version = "1.0.0")
    private VoucherForCmsApi voucherForCmsApi;

    @Reference(version = "1.0.0")
    private InsightChosenCustomerAdminApi insightChosenCustomerAdminApi;

    @Reference(version = "1.0.0")
    private VoucherForAdminBusinessExpApi promotionVoucherExpApi;



    @Override
    public AjaxResult<Integer> receive(List<Long> ids) {
        try {
            Integer count = 0;
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getId();
            if (merchantId == null) {
                throw new AppException("请先登录", XyyJsonResultCodeEnum.FAIL);
            }

            for (Long voucherTemplateId : ids) {
                // 构造请求并调用领取接口
                ReceiveVoucherRequestDTO voucherRequestDTO = new ReceiveVoucherRequestDTO();
                voucherRequestDTO.setMerchantId(merchant.getId());
                voucherRequestDTO.setVoucherTemplateId(voucherTemplateId);
                ResultDTO<VoucherExtendDTO> resultDTO = promotionVoucherApi.receiveVoucher(voucherRequestDTO);

                if(ErrorCodeEum.SUCCESS.getErrorCode() == resultDTO.getErrorCode()){
                    count ++;
                }
            }
            if (count > 0) {
                return AjaxResult.successResult(count);
            }
            return AjaxResult.errResult("您已领取此券！");
        }  catch (Exception e) {
            log.error("优惠券领取失败：", e);
            throw new AppException("优惠券领取失败！", XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public AjaxResult<List<CouponRespVO>> getCoupon(List<Long> couponIds) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Boolean isAdmin = false;
            Long merchantId = merchant.getId();
            // 会员未登录情况统一转为null。
            if (merchantId != null && merchantId <= 0L) {
                merchantId = null;
            }
            if (!BooleanUtils.isTrue(isAdmin) && merchantId == null) {
                // 非admin侧 且会员ID为null
                throw new AppException(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }
            if (CollectionUtils.isEmpty(couponIds)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR);
            }
            ApiRPCResult<List<CouponBaseDto>> apiRPCResult = voucherForCmsApi.listCouponByIds(merchantId, couponIds);
            if (!apiRPCResult.isSuccess()) {
                String message = MessageFormat.format("【cms】listCouponByIds，请求入参，isAdmin：{0}，merchantId：{1}，couponIds：{2}，msg:{3}",
                        isAdmin, String.valueOf(merchantId), JSONObject.toJSONString(couponIds), apiRPCResult.getMsg());
                log.error("【cms】listCouponByIds，请求入参，isAdmin：{}，merchantId：{}，couponIds：{}，msg:{}",
                        isAdmin, merchantId, JSONObject.toJSONString(couponIds), apiRPCResult.getMsg());
                throw new AppException(message, XyyJsonResultCodeEnum.CMS_COUPON_QUERY_ERROR, apiRPCResult.getMsg());
            }
            List<CouponBaseDto> couponBaseDtos = apiRPCResult.getData();
            if (CollectionUtils.isEmpty(couponBaseDtos)) {
                return AjaxResult.successResultNotData();
            }
            List<CmsCouponBaseVO> cmsCouponBaseVOS = creates(couponBaseDtos);
            if (BooleanUtils.isTrue(isAdmin)) {
                // 填充人群名称
                cmsCouponBaseVOS = this.fillCouponsCustomerGroupName(cmsCouponBaseVOS);
            }
            // List<CouponRespVO> couponRespVOs = new ArrayList<>();
            List<CouponRespVO> couponRespVOs = cmsCouponBaseVOS.stream()
                    .map(cms -> {
                        CouponRespVO vo = new CouponRespVO();
                        BeanUtils.copyProperties(cms,vo);
                        return vo;
                    })
                    .collect(Collectors.toList());
            return AjaxResult.successResult(couponRespVOs);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private List<CmsCouponBaseVO> fillCouponsCustomerGroupName(List<CmsCouponBaseVO> cmsCouponBaseVOS) {
        if (CollectionUtils.isEmpty(cmsCouponBaseVOS)) {
            return Lists.newArrayList();
        }
        List<Long> customerGroupIds = cmsCouponBaseVOS.stream().filter(Objects::nonNull).map(CmsCouponBaseVO::getCustomerGroupId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerGroupIds)) {
            return cmsCouponBaseVOS;
        }
        ApiRPCResult<List<MarketCustomerGroupBaseInfoDTO>> apiRPCResult = insightChosenCustomerAdminApi.mgetChoseCustomerBaseInfo(customerGroupIds);
        if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
            return cmsCouponBaseVOS;
        }
        List<MarketCustomerGroupBaseInfoDTO> marketCustomerGroupBaseInfoDTOS = apiRPCResult.getData();
        if (CollectionUtils.isEmpty(marketCustomerGroupBaseInfoDTOS)) {
            return cmsCouponBaseVOS;
        }
        Map<Long, String> idToNameMap = marketCustomerGroupBaseInfoDTOS.stream()
                .filter(item -> Objects.nonNull(item.getId()) && Objects.nonNull(item.getGroupName()))
                .collect(Collectors.toMap(MarketCustomerGroupBaseInfoDTO::getId, MarketCustomerGroupBaseInfoDTO::getGroupName));
        if (MapUtils.isEmpty(idToNameMap)) {
            return cmsCouponBaseVOS;
        }
        Long customerGroupId;
        String customerGroupName;
        for (CmsCouponBaseVO cmsCouponBaseVO : cmsCouponBaseVOS) {
            customerGroupId = cmsCouponBaseVO.getCustomerGroupId();
            if (Objects.isNull(customerGroupId)) {
                continue;
            }
            customerGroupName = idToNameMap.get(customerGroupId);
            cmsCouponBaseVO.setCustomerGroupName(customerGroupName);
        }
        return cmsCouponBaseVOS;
    }

    public static List<CmsCouponBaseVO> creates(List<CouponBaseDto> couponBaseDtos) {
        if (CollectionUtils.isEmpty(couponBaseDtos)) {
            return Lists.newArrayList();
        }
        return couponBaseDtos.stream().map(item -> create(item)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static CmsCouponBaseVO create(CouponBaseDto couponBaseDto) {
        if (Objects.isNull(couponBaseDto)) {
            return null;
        }
        CmsCouponBaseVO cmsCouponBaseVO = new CmsCouponBaseVO();
        BeanUtils.copyProperties(couponBaseDto, cmsCouponBaseVO);
        cmsCouponBaseVO.setCouponText(couponBaseDto.getVoucherText());
        return cmsCouponBaseVO;
    }

}
