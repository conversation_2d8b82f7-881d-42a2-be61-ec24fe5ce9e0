package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.api.HeavenlyVoucherBuineseApi;
import com.xyy.ec.marketing.client.api.query.CouponForReceiveCenterQueryService;
import com.xyy.ec.marketing.client.api.query.MyCouponSearchService;
import com.xyy.ec.marketing.client.dto.coupon.MyCouponDto;
import com.xyy.ec.marketing.client.dto.coupon.MyCouponPageDto;
import com.xyy.ec.marketing.common.dto.Page;
import com.xyy.ec.marketing.exception.MarketingBusinessException;
import com.xyy.ec.marketing.hyperspace.api.VoucherForCmsApi;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponBaseDto;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponCenterDto;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponCsuImageDto;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponReceivedNumPo;
import com.xyy.ec.marketing.insight.api.InsightChosenCustomerAdminApi;
import com.xyy.ec.marketing.insight.results.MarketCustomerGroupBaseInfoDTO;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.constants.ReceiveCenterCouponSortConstants;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.newfront.dto.*;
import com.xyy.ec.pc.newfront.service.VoucherNewService;
import com.xyy.ec.pc.newfront.vo.CheckCouponParamVO;
import com.xyy.ec.pc.newfront.vo.CmsCouponBaseVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.shop.service.ShopService;
import com.xyy.ec.pc.shop.vo.ShopStaticsVo;
import com.xyy.ec.product.business.ecp.vouchercenter.api.ProductVoucherCenterBusinessApi;
import com.xyy.ec.product.business.ecp.vouchercenter.dto.CsuImageVoucherDto;
import com.xyy.ec.product.business.ecp.vouchercenter.dto.VoucherCsuSearchDto;
import com.xyy.ms.promotion.business.api.exp.VoucherForAdminBusinessExpApi;
import com.xyy.ms.promotion.business.api.pc.VoucherForPcBusinessApi;
import com.xyy.ms.promotion.business.common.ErrorCodeEum;
import com.xyy.ms.promotion.business.common.ResultDTO;
import com.xyy.ms.promotion.business.common.constants.VoucherEnum;
import com.xyy.ms.promotion.business.dto.voucher.ReceiveVoucherRequestDTO;
import com.xyy.ms.promotion.business.dto.voucher.VoucherExtendDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VoucherNewServiceImpl implements VoucherNewService {

    @Reference(version = "1.0.0")
    private HeavenlyVoucherBuineseApi heavenlyVoucherBuineseApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private VoucherForPcBusinessApi promotionVoucherApi;

    @Reference(version = "1.0.0")
    private VoucherForCmsApi voucherForCmsApi;

    @Reference(version = "1.0.0")
    private InsightChosenCustomerAdminApi insightChosenCustomerAdminApi;

    @Reference(version = "1.0.0")
    private VoucherForAdminBusinessExpApi promotionVoucherExpApi;

    @Reference(version = "1.0.0")
    private CouponForReceiveCenterQueryService couponForReceiveCenterQueryService;

    @Autowired
    private ShopService shopService;

    // 跨店券店铺列表凑单页，搜索时shopCodeList最大长度
    @Value("${voucher.sku.recall.max.size}")
    private int voucherSkuRecallMaxSize;

    @Reference(version = "1.0.0")
    ProductVoucherCenterBusinessApi productVoucherCenterBusinessApi;

    @Reference(version = "1.0.0")
    private MyCouponSearchService myCouponSearchService;

    @Value("${config.product_image_path_url}")
    private String uploadPathUrl;


    @Value("${oss.url}")
    private String markerUrl;


    private final static String imgPath = "/ybm/product/min/";



    @Override
    public AjaxResult<Integer> receive(List<Long> ids) {
        try {
            Integer count = 0;
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getId();
            if (merchantId == null) {
                throw new AppException("请先登录", XyyJsonResultCodeEnum.FAIL);
            }

            for (Long voucherTemplateId : ids) {
                // 构造请求并调用领取接口
                ReceiveVoucherRequestDTO voucherRequestDTO = new ReceiveVoucherRequestDTO();
                voucherRequestDTO.setMerchantId(merchant.getId());
                voucherRequestDTO.setVoucherTemplateId(voucherTemplateId);
                ResultDTO<VoucherExtendDTO> resultDTO = promotionVoucherApi.receiveVoucher(voucherRequestDTO);

                if (ErrorCodeEum.SUCCESS.getErrorCode() == resultDTO.getErrorCode()) {
                    count++;
                }
            }
            if (count > 0) {
                return AjaxResult.successResult(count);
            }
            return AjaxResult.errResult("您已领取此券！");
        } catch (Exception e) {
            log.error("优惠券领取失败：", e);
            throw new AppException("优惠券领取失败！", XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public AjaxResult<List<CouponRespVO>> getCoupon(List<Long> couponIds) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Boolean isAdmin = false;
            Long merchantId = merchant.getId();
            // 会员未登录情况统一转为null。
            if (merchantId != null && merchantId <= 0L) {
                merchantId = null;
            }
            if (!BooleanUtils.isTrue(isAdmin) && merchantId == null) {
                // 非admin侧 且会员ID为null
                throw new AppException(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }
            if (CollectionUtils.isEmpty(couponIds)) {
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR);
            }
            ApiRPCResult<List<CouponBaseDto>> apiRPCResult = voucherForCmsApi.listCouponByIds(merchantId, couponIds);
            if (!apiRPCResult.isSuccess()) {
                String message = MessageFormat.format("【cms】listCouponByIds，请求入参，isAdmin：{0}，merchantId：{1}，couponIds：{2}，msg:{3}",
                        isAdmin, String.valueOf(merchantId), JSONObject.toJSONString(couponIds), apiRPCResult.getMsg());
                log.error("【cms】listCouponByIds，请求入参，isAdmin：{}，merchantId：{}，couponIds：{}，msg:{}",
                        isAdmin, merchantId, JSONObject.toJSONString(couponIds), apiRPCResult.getMsg());
                throw new AppException(message, XyyJsonResultCodeEnum.CMS_COUPON_QUERY_ERROR, apiRPCResult.getMsg());
            }
            List<CouponBaseDto> couponBaseDtos = apiRPCResult.getData();
            if (CollectionUtils.isEmpty(couponBaseDtos)) {
                return AjaxResult.successResultNotData();
            }
            List<CmsCouponBaseVO> cmsCouponBaseVOS = creates(couponBaseDtos);
            if (BooleanUtils.isTrue(isAdmin)) {
                // 填充人群名称
                cmsCouponBaseVOS = this.fillCouponsCustomerGroupName(cmsCouponBaseVOS);
            }
            // List<CouponRespVO> couponRespVOs = new ArrayList<>();
            List<CouponRespVO> couponRespVOs = cmsCouponBaseVOS.stream()
                    .map(cms -> {
                        CouponRespVO vo = new CouponRespVO();
                        BeanUtils.copyProperties(cms, vo);
                        return vo;
                    })
                    .collect(Collectors.toList());
            return AjaxResult.successResult(couponRespVOs);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public AjaxResult<ReceiveCouponRespVO> getReceiveCouponsInfo() {

        MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipalEaseEx();
        if (null == merchant) {
            log.info("receiveCenterIndex_用户未登录");
            return AjaxResult.errResult("用户未登录");
        }
        int queryVoucherState = VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState();
        try{
            MyCouponPageDto allMerchantVoucher = myCouponSearchService.getAllMerchantVoucher(merchant.getMerchantId(), queryVoucherState, true);
            List<MyCouponDto> couponDtoList = allMerchantVoucher.getCouponDtoList();

            if (CollectionUtils.isNotEmpty(couponDtoList)){
                ReceiveCouponRespVO vo = new ReceiveCouponRespVO();
                vo.setCount(couponDtoList.size());
                couponDtoList = couponDtoList.stream().limit(3).collect(Collectors.toList());
                for (int i = 0; i < couponDtoList.size(); i++) {
                    toReceived(couponDtoList.get(i));
                }
                // 组装数据
                vo.setCoupons(couponDtoList);
                return AjaxResult.successResult(vo);
            }
            return AjaxResult.successResultNotData();
        }catch (MarketingBusinessException e){
            log.error("获取商户优惠券失败,merchantId={},queryVoucherState={}",merchant.getMerchantId(),queryVoucherState, e);
            return AjaxResult.errResult("获取已领取优惠券异常");
        }
    }

    @Override
    public AjaxResult<VoucherNewRespVO> getPlatformCouponsInfo(CheckCouponParamVO couponParamVO) {
        MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipalEaseEx();
        if (null == merchant) {
            log.info("receiveCenterIndex_用户未登录");
            return AjaxResult.errResult("用户未登录");
        }

        Long merchantId = merchant.getId();
        Integer startNum = couponParamVO.getStartNum();
        Integer pageSize = (couponParamVO.getPageSize() == null || couponParamVO.getPageSize() > 20) ? 20 : couponParamVO.getPageSize();

        Page<CouponBaseDto> pageInfo = couponForReceiveCenterQueryService.getCouponShowInReceiveCenterPlatformCoupon(merchantId, startNum, pageSize);
        log.info("分页大小：{}, 页码：{},数组大小:{}", pageSize, startNum, pageInfo.getList().size());
        Map<String, ShopStaticsVo> shopInfoSupperVOMap = shopService.mgetShopStaticsByCode(pageInfo.getList().stream().map(CouponBaseDto::getShopCode).filter(s -> null != s && !"XS000000".equals(s)).distinct().collect(Collectors.toList()));


        List<CouponBaseDto> platformCoupon = pageInfo.getList().stream().sorted(ReceiveCenterCouponSortConstants.DISCOUNT_DESC_SORT).sorted(ReceiveCenterCouponSortConstants.COUPON_MAKE_FROM_SORT).collect(Collectors.toList());

        List<CouponCenterDto> couponCenterDto = toCouponCenterDto(platformCoupon, shopInfoSupperVOMap);

        return AjaxResult.successResult(combined(couponCenterDto, startNum, pageSize, pageInfo, merchantId));
    }

    @Override
    public AjaxResult<VoucherNewRespVO> getNewCustomerCouponsInfo(CheckCouponParamVO couponParamVO) {
        MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipalEaseEx();
        if (null == merchant) {
            log.info("receiveCenterIndex_用户未登录");
            return AjaxResult.errResult("用户未登录");
        }
        Long merchantId = merchant.getId();
        Integer startNum = couponParamVO.getStartNum();
        Integer pageSize = (couponParamVO.getPageSize() == null || couponParamVO.getPageSize() > 20) ? 20 : couponParamVO.getPageSize();

        Page<CouponBaseDto> pageInfo = couponForReceiveCenterQueryService.getCouponShowInReceiveCenterNewCustomerCoupon(merchantId, startNum, pageSize);
        Map<String, ShopStaticsVo> shopInfoSupperVOMap = shopService.mgetShopStaticsByCode(pageInfo.getList().stream().map(CouponBaseDto::getShopCode).filter(s -> null != s && !"XS000000".equals(s)).distinct().collect(Collectors.toList()));

        List<CouponBaseDto> newCustomerCoupon = pageInfo.getList().stream().sorted(ReceiveCenterCouponSortConstants.DISCOUNT_DESC_SORT).collect(Collectors.toList());

        List<CouponCenterDto> couponCenterDto = toCouponCenterDto(newCustomerCoupon, shopInfoSupperVOMap);


        return AjaxResult.successResult(combined(couponCenterDto, startNum, pageSize, pageInfo, merchantId));

    }

    @Override
    public AjaxResult<VoucherNewRespVO> getProductCouponsInfo(CheckCouponParamVO couponParamVO) {
        MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipalEaseEx();
        if (null == merchant) {
            log.info("receiveCenterIndex_用户未登录");
            return AjaxResult.errResult("用户未登录");
        }
        long merchantId = merchant.getId();
        int startNum = couponParamVO.getStartNum();
        int pageSize = (couponParamVO.getPageSize() == null || couponParamVO.getPageSize() > 20) ? 20 : couponParamVO.getPageSize().intValue();

        Page<CouponBaseDto> pageInfo = couponForReceiveCenterQueryService.getCouponShowInReceiveCenterProductCoupon(merchantId, startNum, pageSize);
        Map<String, ShopStaticsVo> shopInfoSupperVOMap = shopService.mgetShopStaticsByCode(pageInfo.getList().stream().map(CouponBaseDto::getShopCode).filter(s -> null != s && !"XS000000".equals(s)).distinct().collect(Collectors.toList()));

        List<CouponBaseDto> collect = pageInfo.getList().stream().sorted(ReceiveCenterCouponSortConstants.DISCOUNT_DESC_SORT).sorted(ReceiveCenterCouponSortConstants.COUPON_TYPE_SORT).collect(Collectors.toList());
        List<CouponCenterDto> list = setCsuImage(toCouponCenterDto(collect, shopInfoSupperVOMap), merchantId);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
            for (CouponCenterDto item : list) {
                if (Objects.equals(7, item.getVoucherType())) {
                    item.setVoucherType(2);
                    item.setVoucherTypeDesc("商品券");
                    item.setVoucherTypeText("商品券");
                }
            }
        }
        return AjaxResult.successResult(combined(list, startNum, pageSize, pageInfo, merchantId));
    }

    @Override
    public AjaxResult<VoucherNewRespVO> getShopCouponsInfo(CheckCouponParamVO couponParamVO) {
        MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipalEaseEx();
        if (null == merchant) {
            log.info("receiveCenterIndex_用户未登录");
            return AjaxResult.errResult("用户未登录");
        }
        long merchantId = merchant.getId();
        int startNum = couponParamVO.getStartNum();
        int pageSize = (couponParamVO.getPageSize() == null || couponParamVO.getPageSize() > 20) ? 20 : couponParamVO.getPageSize();

        //为空或者负数表示获取全部
        Integer bizType = couponParamVO.getBizType() == null ? -1 : couponParamVO.getBizType();

        Page<CouponBaseDto> pageInfo = couponForReceiveCenterQueryService.getCouponShowInReceiveCenterShopCoupon(merchantId, bizType, startNum, pageSize);
        Map<String, ShopStaticsVo> shopInfoSupperVOMap = shopService.mgetShopStaticsByCode(pageInfo.getList().stream().map(CouponBaseDto::getShopCode).filter(s -> null != s && !"XS000000".equals(s)).distinct().collect(Collectors.toList()));

        List<CouponBaseDto> collect = pageInfo.getList().stream().sorted(ReceiveCenterCouponSortConstants.DISCOUNT_DESC_SORT).collect(Collectors.toList());

        List<CouponCenterDto> couponCenterDto = toCouponCenterDto(collect, shopInfoSupperVOMap);

        return AjaxResult.successResult(combined(couponCenterDto, startNum, pageSize, pageInfo, merchantId));
    }

    @Override
    public AjaxResult<ReceiveVoucherRespVO> receiveOnlyVoucher(Long templateId) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getId();
            if (merchantId == null) {
                throw new AppException("请先登录", XyyJsonResultCodeEnum.FAIL);
            }
            ReceiveVoucherRequestDTO voucherRequestDTO = new ReceiveVoucherRequestDTO();
            voucherRequestDTO.setMerchantId(merchant.getId());
            voucherRequestDTO.setVoucherTemplateId(templateId);
            ResultDTO<VoucherExtendDTO> resultDTO = promotionVoucherApi.receiveVoucher(voucherRequestDTO);
            if(null == resultDTO){
                return AjaxResult.errResult("领取失败");
            }
            VoucherExtendDTO voucherExtendDTO = resultDTO.getData();
            if(ErrorCodeEum.SUCCESS.getErrorCode() != resultDTO.getErrorCode()){
                return AjaxResult.errResult(resultDTO.getErrorMsg());
            }

            ReceiveVoucherRespVO respVO = new ReceiveVoucherRespVO();

            respVO.setVoucherType(voucherExtendDTO.getVoucherType());
            respVO.setValidDate(voucherExtendDTO.getValidDate());
            respVO.setExpireDate(voucherExtendDTO.getExpireDate());
            respVO.setNoReceiveCount(0);
            respVO.setVoucherTemplateId(templateId);

            return AjaxResult.successResult(respVO);
        } catch (Exception e) {
            log.error("优惠券领取失败：", e);
            return AjaxResult.errResult("优惠券领取失败！");
        }
    }

    // 组装数据
    private VoucherNewRespVO combined(List<CouponCenterDto> couponBaseDto, Integer startNum, Integer pageSize, com.xyy.ec.marketing.common.dto.Page<CouponBaseDto> pageInfo, Long merchantId) {

        // 提取所有 templateId
        List<Long> templateIdList = couponBaseDto.stream().map(CouponCenterDto::getTemplateId).collect(Collectors.toList());

        // 批量查询已领取数量
        ApiRPCResult<List<CouponReceivedNumPo>> couponReceivedNumResult = voucherForCmsApi.getCouponReceivedNum(merchantId, templateIdList);


        // 构建 templateId -> receivedNum 映射
        Map<Long, Integer> receivedNumMap = couponReceivedNumResult.getData().stream()
                .collect(Collectors.toMap(
                        item -> Long.parseLong(item.getVoucherTemplateId()),
                        item -> Integer.valueOf(item.getReceivedNum()),
                        (oldValue, newValue) -> newValue)
                );

        // 转换 VO 并设置剩余数量
        List<CouponNewCenterVO> collect = couponBaseDto.stream()
                .map(item -> {
                    CouponNewCenterVO vo = new CouponNewCenterVO();
                    BeanUtils.copyProperties(item, vo);
                    Integer receivedNum = receivedNumMap.getOrDefault(item.getTemplateId(), 0);
                    vo.setCouponSurplusCount(item.getCountPerUser() - receivedNum);
                    vo.setCurrentDate(System.currentTimeMillis());
                    return vo;
                })
                .collect(Collectors.toList());

        VoucherNewRespVO voucherNewRespVO = new VoucherNewRespVO();
        voucherNewRespVO.setList(collect);
        voucherNewRespVO.setHasNext(pageInfo.isHasNext());
        voucherNewRespVO.setNextStartNum(pageInfo.getNextStartNum());
        voucherNewRespVO.setStartNum(startNum);

        return voucherNewRespVO;
    }

    private List<CouponCenterDto> setCsuImage(List<CouponCenterDto> couponCenterDtoList, Long merchantId) {
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(couponCenterDtoList)) {
            return couponCenterDtoList;
        }
        for (List<CouponCenterDto> part : Lists.partition(couponCenterDtoList, 1)) {
            doSetCsuImage(part, merchantId);
        }
        return couponCenterDtoList;
    }

    private void doSetCsuImage(List<CouponCenterDto> couponCenterDtoList, Long merchantId) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(couponCenterDtoList)) {
            return;
        }
        Map<Long, List<Long>> couponSkuMap = Maps.newHashMapWithExpectedSize(couponCenterDtoList.size());
        Set<Long> allSkuId = Sets.newHashSetWithExpectedSize(couponCenterDtoList.size() * 5);
        for (CouponCenterDto item : couponCenterDtoList) {
            List<Long> assignProductIds = item.getAssignProductIds();
            if (org.apache.commons.collections.CollectionUtils.isEmpty(assignProductIds)) {
                continue;
            }
            List<Long> searchCusIds;
            if (assignProductIds.size() <= voucherSkuRecallMaxSize) {
                searchCusIds = Lists.newArrayList(assignProductIds);
            } else {
                searchCusIds = assignProductIds.subList(0, voucherSkuRecallMaxSize - 1);
            }
            couponSkuMap.put(item.getTemplateId(), searchCusIds);
            allSkuId.addAll(searchCusIds);
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(allSkuId)) {
            return;
        }
        VoucherCsuSearchDto voucherCsuSearchDto = new VoucherCsuSearchDto();
        voucherCsuSearchDto.setCsuIdList(Lists.newArrayList(allSkuId));
        voucherCsuSearchDto.setMerchantId(merchantId);
        List<CsuImageVoucherDto> list = productVoucherCenterBusinessApi.searchCsuImageListByskuId(voucherCsuSearchDto);
        Map<Long, CsuImageVoucherDto> csuImageVoucherDtoMap = Maps.newHashMapWithExpectedSize(list.size());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
            for (CsuImageVoucherDto item : list) {
                csuImageVoucherDtoMap.put(item.getId(), item);
            }
        }
        for (CouponCenterDto item : couponCenterDtoList) {
            List<Long> searchSkuId = couponSkuMap.get(item.getTemplateId());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(searchSkuId)) {
                return;
            }
            List<CouponCsuImageDto> couponCsuImageDtos = Lists.newArrayList();
            int count = 0;
            for (Long skuId : searchSkuId) {
                CsuImageVoucherDto csuImageVoucherDto = csuImageVoucherDtoMap.get(skuId);
                if (null == csuImageVoucherDto || count > 3) {
                    continue;
                }
                ++count;
                CouponCsuImageDto couponCsuImageDto = CouponCsuImageDto
                        .builder()
                        .skuId(csuImageVoucherDto.getId())
                        // 路径拼接
                        .imageUrl(markerUrl + imgPath + csuImageVoucherDto.getImageUrl())
                        .sort(count)
                        .build();
                couponCsuImageDtos.add(couponCsuImageDto);
            }
            item.setVoucherSkuImages(couponCsuImageDtos);
        }
    }


    private List<CmsCouponBaseVO> fillCouponsCustomerGroupName(List<CmsCouponBaseVO> cmsCouponBaseVOS) {
        if (CollectionUtils.isEmpty(cmsCouponBaseVOS)) {
            return Lists.newArrayList();
        }
        List<Long> customerGroupIds = cmsCouponBaseVOS.stream().filter(Objects::nonNull).map(CmsCouponBaseVO::getCustomerGroupId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerGroupIds)) {
            return cmsCouponBaseVOS;
        }
        ApiRPCResult<List<MarketCustomerGroupBaseInfoDTO>> apiRPCResult = insightChosenCustomerAdminApi.mgetChoseCustomerBaseInfo(customerGroupIds);
        if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
            return cmsCouponBaseVOS;
        }
        List<MarketCustomerGroupBaseInfoDTO> marketCustomerGroupBaseInfoDTOS = apiRPCResult.getData();
        if (CollectionUtils.isEmpty(marketCustomerGroupBaseInfoDTOS)) {
            return cmsCouponBaseVOS;
        }
        Map<Long, String> idToNameMap = marketCustomerGroupBaseInfoDTOS.stream()
                .filter(item -> Objects.nonNull(item.getId()) && Objects.nonNull(item.getGroupName()))
                .collect(Collectors.toMap(MarketCustomerGroupBaseInfoDTO::getId, MarketCustomerGroupBaseInfoDTO::getGroupName));
        if (MapUtils.isEmpty(idToNameMap)) {
            return cmsCouponBaseVOS;
        }
        Long customerGroupId;
        String customerGroupName;
        for (CmsCouponBaseVO cmsCouponBaseVO : cmsCouponBaseVOS) {
            customerGroupId = cmsCouponBaseVO.getCustomerGroupId();
            if (Objects.isNull(customerGroupId)) {
                continue;
            }
            customerGroupName = idToNameMap.get(customerGroupId);
            cmsCouponBaseVO.setCustomerGroupName(customerGroupName);
        }
        return cmsCouponBaseVOS;
    }

    public static List<CmsCouponBaseVO> creates(List<CouponBaseDto> couponBaseDtos) {
        if (CollectionUtils.isEmpty(couponBaseDtos)) {
            return Lists.newArrayList();
        }
        return couponBaseDtos.stream().map(item -> create(item)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static CmsCouponBaseVO create(CouponBaseDto couponBaseDto) {
        if (Objects.isNull(couponBaseDto)) {
            return null;
        }
        CmsCouponBaseVO cmsCouponBaseVO = new CmsCouponBaseVO();
        BeanUtils.copyProperties(couponBaseDto, cmsCouponBaseVO);
        cmsCouponBaseVO.setCouponText(couponBaseDto.getVoucherText());
        return cmsCouponBaseVO;
    }

    private CouponCenterDto toCouponCenterDto(CouponBaseDto src, ShopStaticsVo shopInfoSupperVO) {
        String voucherInstructions = null;
        String shopName = "";
        Integer voucherType = src.getVoucherType();
        Integer voucherUsageWay = src.getVoucherUsageWay();
        BigDecimal minMoneyToEnable = src.getMinMoneyToEnable();
        BigDecimal maxMoneyInVoucher = src.getMaxMoneyInVoucher();
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.NEWMAN.getId())) {
            if (StringUtils.isNotBlank(shopName)) {
                voucherInstructions = shopName + "全部商品可用";
            } else {
                voucherInstructions = VoucherEnum.VoucherInstructionsEnum.XINREN_QUAN.getInstructionsText();
            }
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.COMMON.getId())) {
            if (StringUtils.isNotBlank(shopName)) {
                voucherInstructions = shopName + VoucherEnum.VoucherInstructionsEnum.TONGYOPNG_QUAN.getInstructionsText();
            } else {
                voucherInstructions = VoucherEnum.VoucherInstructionsEnum.SHOP_QUAN.getInstructionsText();
            }
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.OVERLYING.getId())
                && org.apache.commons.collections.CollectionUtils.isEmpty(src.getAssignProductIds()) && BooleanUtils.isNotTrue(src.getExhibitionGroupVoucher())) {
            voucherInstructions = VoucherEnum.VoucherInstructionsEnum.DIEJIA_QUAN.getInstructionsText();
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.CROSS_PLATFORM.getId())
                && (org.apache.commons.collections.CollectionUtils.isEmpty(src.getAssignProductIds()) || BooleanUtils.isNotTrue(src.getExhibitionGroupVoucher()))) {
            voucherInstructions = VoucherEnum.VoucherInstructionsEnum.DIEJIA_QUAN.getInstructionsText();
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.SPECIALTY.getId())
                && (org.apache.commons.collections.CollectionUtils.isEmpty(src.getAssignProductIds()) || BooleanUtils.isNotTrue(src.getExhibitionGroupVoucher()))) {
            voucherInstructions = VoucherEnum.VoucherInstructionsEnum.SHANGPIN_QUAN.getInstructionsText();
        }

        String minMoneyToEnableDesc = null;
        if (minMoneyToEnable != null
                && (Objects.equals(voucherType, 8) || Objects.equals(voucherType, 7) || Objects.equals(voucherType, 6) || Objects.equals(voucherType, 2) || Objects.equals(voucherType, 1) || Objects.equals(voucherType, 9))
                && Objects.equals(voucherUsageWay, 1)) {
            minMoneyToEnableDesc = StringUtils.join("每满", minMoneyToEnable.intValue(), "可用");
        } else if (minMoneyToEnable != null) {
            minMoneyToEnableDesc = StringUtils.join("满", minMoneyToEnable.intValue(), "可用");
        }

        String maxMoneyInVoucherDesc = null;
        if (maxMoneyInVoucher != null && maxMoneyInVoucher.compareTo(BigDecimal.ZERO) > 0) {
            maxMoneyInVoucherDesc = StringUtils.join("最高减", maxMoneyInVoucher.intValue());
        }

        boolean isDiscount = src.getDiscount() != null && src.getDiscount().compareTo(BigDecimal.ZERO) > 0;
        String validDayStr = (null != src.getValidDays() && src.getValidDays() > 0) ? "领取后" + src.getValidDays() + "天有效" : null;
        CouponCenterDto build = CouponCenterDto.builder().templateId(src.getTemplateId())
                .templateName(src.getTemplateName())
                .voucherType(voucherType)
                .voucherTypeDesc(com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.of(src.getVoucherType()).getValue())
                .voucherTypeText(com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.of(src.getVoucherType()).getValue())
                .voucherState(isDiscount ? 1 : 0)
                .isLq(null == src.getId() ? 0 : 1)
                .bizType(Objects.equals(src.getVoucherType(), 7) ? 3 : 1)
                .shopCode(src.getShopCode())
                .shopName(null == shopInfoSupperVO ? "" : shopInfoSupperVO.getShowName())
                .shopLogoUrl(null == shopInfoSupperVO ? "" : uploadPathUrl + shopInfoSupperVO.getAppLogo())
                .voucherInstructions(voucherInstructions)
                .voucherTitle(src.getVoucherTitle())
                .moneyInVoucher(isDiscount ? src.getDiscount() : src.getMoneyInVoucher())
                .minMoneyToEnable(src.getMinMoneyToEnable())
                .maxMoneyInVoucher(src.getMaxMoneyInVoucher())
                .discount(src.getDiscount())
                .expireDate(src.getExpireDate())
                .expireDateToString(src.getExpireDate() == null ? "" : DateFormatUtils.format(src.getExpireDate(), "yyyy.MM.dd"))
                .validDate(src.getValidDate())
                .validDateToString(src.getValidDate() == null ? "" : DateFormatUtils.format(src.getValidDate(), "yyyy.MM.dd"))
                .voucherUsageWay(src.getVoucherUsageWay() == null ? 0 : src.getVoucherUsageWay())
                .assignProductIds(src.getAssignProductIds())
                .skuRelationType(src.getSkuRelationType())
                .appUrl(src.getAppUrl())
                .pcUrl(src.getPcUrl())
                .describeUrl(src.getDescribeUrl())
                .minMoneyToEnableDesc(minMoneyToEnableDesc)
                .maxMoneyInVoucherDesc(maxMoneyInVoucherDesc)
                .sortNo(src.getSortNo())
                .validDays(src.getValidDays())
                .validDayStr(validDayStr)
                .countPerUser(src.getCountPerUser())
                .showStartDate(src.getShowStartDate())
                .showEndDate(src.getShowEndDate())
                .receiveValidDate(src.getReceiveValidDate())
                .receiveExpireDate(src.getReceiveExpireDate())
                .build();
        return build;
    }

    private List<CouponCenterDto> toCouponCenterDto(List<CouponBaseDto> src, Map<String, ShopStaticsVo> shopInfoSupperVOMap) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(src)) {
            return Lists.newArrayList();
        }
        List<CouponCenterDto> result = Lists.newArrayListWithExpectedSize(src.size());
        for (CouponBaseDto item : src) {
            if (null == item) {
                continue;
            }
            result.add(toCouponCenterDto(item, shopInfoSupperVOMap.get(item.getShopCode())));
        }
        return result;
    }

    private void toReceived(MyCouponDto src) {

        String voucherInstructions = null;
        String shopName = "";
        Integer voucherType = src.getVoucherType();
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.NEWMAN.getId())) {
            if (StringUtils.isNotBlank(shopName)) {
                voucherInstructions = shopName + "全部商品可用";
            } else {
                voucherInstructions = VoucherEnum.VoucherInstructionsEnum.XINREN_QUAN.getInstructionsText();
            }
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.COMMON.getId())) {
            if (StringUtils.isNotBlank(shopName)) {
                voucherInstructions = shopName + VoucherEnum.VoucherInstructionsEnum.TONGYOPNG_QUAN.getInstructionsText();
            } else {
                voucherInstructions = VoucherEnum.VoucherInstructionsEnum.SHOP_QUAN.getInstructionsText();
            }
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.OVERLYING.getId())
                && org.apache.commons.collections.CollectionUtils.isEmpty(src.getAssignProductIds()) && BooleanUtils.isNotTrue(src.getExhibitionGroupVoucher())) {
            voucherInstructions = VoucherEnum.VoucherInstructionsEnum.DIEJIA_QUAN.getInstructionsText();
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.CROSS_PLATFORM.getId())
                && (org.apache.commons.collections.CollectionUtils.isEmpty(src.getAssignProductIds()) || BooleanUtils.isNotTrue(src.getExhibitionGroupVoucher()))) {
            voucherInstructions = VoucherEnum.VoucherInstructionsEnum.DIEJIA_QUAN.getInstructionsText();
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.SPECIALTY.getId())
                && (org.apache.commons.collections.CollectionUtils.isEmpty(src.getAssignProductIds()) || BooleanUtils.isNotTrue(src.getExhibitionGroupVoucher()))) {
            voucherInstructions = VoucherEnum.VoucherInstructionsEnum.SHANGPIN_QUAN.getInstructionsText();
        }
        src.setVoucherInstructions(voucherInstructions);
    }

}
