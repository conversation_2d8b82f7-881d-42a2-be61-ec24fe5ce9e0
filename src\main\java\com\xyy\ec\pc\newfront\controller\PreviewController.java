package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.cms.dto.TokenGenDto;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/new-front/preview")
public class PreviewController {

    private final TokenService tokenService;
    private final XyyIndentityValidator xyyIndentityValidator;

    @SuppressWarnings("deprecation")
    @PostMapping("/gen-preview-k")
    public AjaxResult<String> genTokenForCmsPreview(@RequestBody TokenGenDto tokenGenDto) {
        try {
            if (tokenGenDto.getMerchantId() == null && StringUtils.isBlank(tokenGenDto.getKeyword())) {
                return AjaxResult.errResult("参数错误");
            }
            if (StringUtils.isBlank(tokenGenDto.getKeyword())) {
                tokenGenDto.setKeyword(String.valueOf(tokenGenDto.getMerchantId()));
            }
            String token = tokenService.genTokenForCmsPreview(tokenGenDto.getKeyword());
            return AjaxResult.successResult(token);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @CustomizeCmsResponse
    @GetMapping("/check-preview-k")
    public AjaxResult<MerchantBussinessDto> tokenCheck() {
        MerchantBussinessDto merchant;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return AjaxResult.successResult(merchant);
    }
}
