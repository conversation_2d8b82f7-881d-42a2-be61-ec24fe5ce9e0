package com.xyy.ec.pc.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.order.search.api.remote.dto.MyPcPurchaseQueryOrderDto;
import com.xyy.ec.order.search.api.remote.dto.MyPcPurchaseQueryRefundDto;
import com.xyy.ec.order.search.api.remote.dto.MyPcPurchaseQueryRefundingDto;
import com.xyy.ec.order.search.api.remote.dto.OrderFeeStatisticsQueryDto;
import com.xyy.ec.order.search.api.remote.result.*;

import java.util.List;

public interface OrderPurchaseStatisticService {
     /**
      * 我的账单
      * @param orderFeeStatisticsQueryDto
      * @return
      */
     StatisticPcByMerchantDto orderFeeStatisticPcByMerchant(OrderFeeStatisticsQueryDto orderFeeStatisticsQueryDto);

     /**
      * 采购总额
      * @param myPcPurchaseQueryOrderDto
      * @return
      */
     PageInfo<MyPcPurchaseOrderDto> orderFeePcByMerchant(MyPcPurchaseQueryOrderDto myPcPurchaseQueryOrderDto);

     /**
      * 采购总额-明细
      * @param OrderNo
      * @return
      */
     List<MyPcPurchaseOrderDetailDto> selectMyPurchaseOrderDetailByOrderNo(String OrderNo);

     /**
      * 查询退款明细
      * @param OrderNo
      * @return
      */
     List<MyPcPurchaseRefundDetailDto>  selectMyPurchaseRefundDetailByRefundNo(String OrderNo);

     /**
      * 查询退款中金额
      * @param queryDto
      * @return
      */
     PageInfo<MyPcPurchaseRefundingDto> selectMyPcPurchaseRefundingPageByMerchant(MyPcPurchaseQueryRefundingDto queryDto);


     /**
      * 查询已退款金额
      * @param queryDto
      * @return
      */
     PageInfo<MyPcPurchaseRefundDto> selectMyPcPurchaseRefundPageByMerchant(MyPcPurchaseQueryRefundDto queryDto);




}
