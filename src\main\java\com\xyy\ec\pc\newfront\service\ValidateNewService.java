package com.xyy.ec.pc.newfront.service;


import com.xyy.ec.pc.newfront.vo.CheckCodeParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.merchant.bussiness.dto.licence.LicenseValidDto;

public interface ValidateNewService {
    /**
     * 反爬账号发送验证码
     */
    AjaxResult<Object> sendCrawlerCode(String phone);

    /**
     * 反爬短信校验
     */
    AjaxResult<Object> checkCrawlerCode(CheckCodeParamVO codeParamVO);

    AjaxResult<LicenseValidDto> sendRegisterMsgCode(String mobileNumber, String code);

    /**
     * check验证码-注册
     */
    AjaxResult<Object> checkCode(String mobileNumber,String code);

    /**
     * 获取手机号验证码-修改密码
     */
    AjaxResult<Object> getValitionCode(String mobileNumber);
}
