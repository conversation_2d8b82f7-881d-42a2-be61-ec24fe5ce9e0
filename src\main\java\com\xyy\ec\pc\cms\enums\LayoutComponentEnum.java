package com.xyy.ec.pc.cms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum LayoutComponentEnum {

    /**
     * 商品流：commodity_flow
     */
    COMMODITY_FLOW(1, "商品流", true),
    /**
     * 平铺选项卡：noScrollTab
     */
    TILE_TAB(2, "平铺选项卡", true),
    /**
     * 滚动选项卡：tabs-page
     */
    SLIDE_TAB(3, "滚动选项卡", true),
    POSITIONING_TAB(4, "定位选项卡", true),
    PROTOCOL_AUTHORIZATION_TAB(5, "协议授权选项卡", true),

    GOLD_PIECE(6, "黄金单品", false),
    SLIDING_GOODS(7, "滑动商品", false),
    HORIZONTAL_COMMODITY_DISPLAY(8, "横向商品展示", false),
    VERTICAL_COMMODITY_DISPLAY(9, "竖向商品展示", false),
    /**
     * 控销的品类楼层：categoryFloor
     */
    CONTROL_CATEGORY_FLOOR(10, "品类楼层", false),

    /**
     * 店铺楼层（商品组）：control_new_groupBanner
     */
    SHOP_FLOOR_GROUP(11, "店铺楼层（商品组）", false),

    /**
     * 单品选项卡：item_tabControl
     */
    SINGLE_PRODUCT_TAB(12, "单品选项卡", false),

    /**
     * PC首页商品楼层
     */
    PC_INDEX_PRODUCT_FLOOR(13, "PC首页商品楼层", false),

    ;

    private Integer moduleSourceType;
    private String name;
    //是否走推荐逻辑
    private Boolean recommended;

    /**
     * 是否应用商品流兜底策略
     *
     * @param moduleSourceType 组来源
     * @return
     */
    public static boolean isUsingProductEndStrategy(Integer moduleSourceType) {
        if (Objects.isNull(moduleSourceType)) {
            return false;
        }
        return Arrays.stream(LayoutComponentEnum.values())
                .filter(com -> Objects.nonNull(com.getModuleSourceType()) && BooleanUtils.isTrue(com.getRecommended()))
                .mapToInt(LayoutComponentEnum::getModuleSourceType)
                .anyMatch(sourceType -> Objects.equals(moduleSourceType, sourceType));
    }
}
