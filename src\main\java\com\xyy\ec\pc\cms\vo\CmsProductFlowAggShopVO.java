package com.xyy.ec.pc.cms.vo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Setter
@Getter
@Builder
@ToString
public class CmsProductFlowAggShopVO implements Serializable {

    /**
     * 店铺编码
     */
    private String shopCode;
    /**
     * 显示名称
     */
    private String showName;

    /**
     * 域编码
     */
    private String branchCode;

    /**
     * 是否是自营本区域。0-否, 1-是
     */
    private Integer isThisSelfBranchCode;

    /**
     * 是否第三方 0-否, 1-是
     */
    private Integer isThreadCompany;

    /**
     * 包邮提示
     */
    private String packageTips;

    /**
     * 账户状态: 0-未开户，1-已开户
     */
    private Integer accountStatus;

    /**
     * 品质店铺: 0-非品质店铺，1-品质店铺
     */
    private Integer quality;

}
