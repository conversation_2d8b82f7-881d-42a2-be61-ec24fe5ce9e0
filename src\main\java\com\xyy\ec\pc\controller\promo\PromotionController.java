package com.xyy.ec.pc.controller.promo;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xyy.ec.marketing.hyperspace.api.dto.fullGive.PromoProductMainListDTO;
import com.xyy.ec.marketing.hyperspace.api.dto.fullGive.PromoWithMainProductDTO;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.dto.front.ShoppingCartFrontDto;
import com.xyy.ec.order.business.dto.front.ShoppingCartItemFrontDto;
import com.xyy.ec.order.business.dto.front.ShoppingGroupFrontDto;
import com.xyy.ec.order.business.dto.shop.CartBusinessDto;
import com.xyy.ec.order.business.dto.shop.CompanyBusinessDto;
import com.xyy.ec.order.business.dto.shop.ShopBusinessDto;
import com.xyy.ec.order.core.dto.cart.ShoppingCartDto;
import com.xyy.ec.order.dto.cart.GetCartDto;
import com.xyy.ec.order.dto.giftPool.CommonGiftPoolDto;
import com.xyy.ec.order.enums.BizSourceEnum;
import com.xyy.ec.order.enums.PlatformEnum;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.model.PromoMainProductSearchDTO;
import com.xyy.ec.pc.model.dto.PcPromoGiftPoolSkuVO;
import com.xyy.ec.pc.model.dto.PromotionMainProductListQueryParam;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.remote.ProductForLayoutRemoteService;
import com.xyy.ec.pc.rpc.HyperSpaceRpc;
import com.xyy.ec.pc.rpc.OrderServerRpcService;
import com.xyy.ec.pc.service.PromotionService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.EncodeUtil;
import com.xyy.ec.pc.util.JsonUtil;
import com.xyy.ec.pc.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/18 14:49
 */
@RestController
@RequestMapping("/promotion")
@Slf4j
public class PromotionController {
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Autowired
    private HyperSpaceRpc hyperSpaceRpc;
    @Autowired
    private PromotionService promotionService;
    @Autowired
    private ProductForLayoutRemoteService productForLayoutRemoteService;
    @Autowired
    private OrderServerRpcService orderServerRpcService;
    /**
     * 满赠凑单页面
     */
    @RequestMapping(value = "/promo/fullGive/mainProductList.htm", method = {RequestMethod.GET})
    public ModelAndView getMainProductList( @RequestParam(name = "categoryId", required = false) Long categoryId,
                                            @RequestParam(name = "keywords", required = false) String keywords,
                                            @RequestParam(name = "promoId", required = true) Long promoId,
                                            @RequestParam(name = "offset", required = false) Integer offset,
                                            @RequestParam(name = "pageSize", required = false) Integer pageSize,
                                            HttpServletRequest request) {
        try {
            PromotionMainProductListQueryParam queryParam = new PromotionMainProductListQueryParam();
            queryParam.setQueryWord(keywords);
            queryParam.setPromoId(promoId);
            if (Objects.nonNull(categoryId)) {
                queryParam.setCategoryId(categoryId);
            }
            queryParam.setPageNum(Optional.ofNullable(queryParam.getPageNum()).orElse(1));
            queryParam.setPageSize(Optional.ofNullable(queryParam.getPageSize()).orElse(20));
            if (log.isDebugEnabled()) {
                log.debug("满赠凑单页面查询参数:{}",JsonUtil.toJson(queryParam));
            }
            MerchantBussinessDto merchantBussinessDto = null;
            String productBranchCode = null;
            merchantBussinessDto = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            if (merchantBussinessDto == null) {
                log.error("getMainProductList, 用户未登录");
                return new ModelAndView(new RedirectView("/login/login.htm", true, false));
            }
            productBranchCode = merchantBussinessDto.getRegisterCode();
            // 用户id
            Long merchantId = merchantBussinessDto.getId();
            // 分页要用，将url传到前台
            String url = getRequestUrl(request);
            // 初始化返回结果 - 优雅返回
            Map<String, Object> model = new HashMap<>();
            model.put("merchantId", merchantId);
            model.put("mainSkuDTO", new PromoMainProductSearchDTO());
            model.put("skuList", new ArrayList<>());
            model.put("merchant", merchantBussinessDto);
            model.put("promoId", promoId);
            model.put("categoryList", Lists.newArrayList());
            // 调hyperspace 查询主品池
            PromoWithMainProductDTO promoWithMainProductDTO = hyperSpaceRpc.queryPromoMainProductList(promoId);
            if (Objects.isNull(promoWithMainProductDTO)) {
                log.info("promoId-{}, 活动主品查询结果为空", promoId);
                return new ModelAndView("/new_make_up_order.ftl", model);
            }
            String desc = promoWithMainProductDTO.getPromoDesc();
            List<PromoProductMainListDTO> mainList = promoWithMainProductDTO.getMainList();
            if (CollectionUtils.isEmpty(mainList)) {
                log.info("promoId-{}, 活动主品为空", promoId);
                model.put("promoDesc", desc);
                return new ModelAndView("/new_make_up_order.ftl", model);
            }
            List<Long> skuList = mainList.stream().map(PromoProductMainListDTO::getSkuId).collect(Collectors.toList());
            // 过控销
            skuList = productForLayoutRemoteService.controlFilterCsuIdsForIsVisible(merchantId, productBranchCode, skuList);
            queryParam.setSkuList(skuList);
            PromoMainProductSearchDTO mainProductSearchDTO = promotionService.queryMainProductList(queryParam, merchantId, productBranchCode);
            mainProductSearchDTO.setRequestUrl(url);
            mainProductSearchDTO.setCategoryId(categoryId);
            model.put("mainSkuDTO", mainProductSearchDTO);
            model.put("promoDesc", desc);
            model.put("categoryList", mainProductSearchDTO.getCategoryDtoList());
            model.put("skuList", JsonUtil.toJson(mainProductSearchDTO.getSkuList()));
            return new ModelAndView("/new_make_up_order.ftl", model);
        } catch (Exception e) {
            log.error("getMainProductList异常", e);
            return new ModelAndView("/error/500.ftl");
        }
    }



    @RequestMapping(value = "/promo/goGatherOrdersStatistics", method = {RequestMethod.POST, RequestMethod.GET})
    public XyyJsonResult goGatherOrdersStatistics(@RequestParam(name = "merchantId") Long merchantId,
                                                  @RequestParam(value = "terminalType", required = false) Integer terminalType,
                                                  @RequestParam(value = "version", required = false) Integer version,
                                                  Long promoId) {
        try {
            ShoppingCartDto cart =new ShoppingCartDto();
            cart.setAppVersion(version);
            cart.setMerchantId(merchantId);
            cart.setTerminalType(terminalType);
            AtomicReference<ShoppingGroupFrontDto> atomicReference=new AtomicReference<>();
            atomicReference.set(null);
            GetCartDto getCartDto = new GetCartDto();
            getCartDto.setBizSource(BizSourceEnum.B2B.getKey());
            getCartDto.setTerminalType(PlatformEnum.PC.getValue());
            getCartDto.setMerchantId(merchantId);
            getCartDto.setUseRedPacket(true);
            com.xyy.ec.order.dto.cart.CartBusinessDto cartBusinessDto = orderServerRpcService.getCart(getCartDto);

            if(cartBusinessDto != null &&  CollectionUtils.isNotEmpty(cartBusinessDto.getCompany())   )
            {
                List<com.xyy.ec.order.dto.cart.CompanyBusinessDto> lists=cartBusinessDto.getCompany();
                for ( int index=0;index< lists.size();index++){
                    CompanyBusinessDto company=   JSON.parseObject(JSON.toJSONString(lists.get(index)),CompanyBusinessDto.class)  ;
                    if(CollectionUtils.isNotEmpty(company.getShop()))
                    {
                        for ( int shopIndex=0;shopIndex<  company.getShop().size();shopIndex++){
                            ShopBusinessDto shop= company.getShop().get(shopIndex);
                            if(CollectionUtils.isNotEmpty(shop.getShoppingGroupFrontDtos())) {
                                shop.getShoppingGroupFrontDtos().stream().forEach(dto->{
                                    if(promoId.equals( dto.getPromoId())) {
                                        atomicReference.set(dto);
                                        return;
                                    }
                                });
                            }
                        }

                    }
                }

            }
            ShoppingGroupFrontDto  shoppingGroupFrontDto =atomicReference.get();
            BigDecimal subTotal=BigDecimal.ZERO;
            Integer countTotal=0;
            String title="";
            Integer productVarietyNum=0;

            if(shoppingGroupFrontDto != null) {
                title=  shoppingGroupFrontDto.getTitle();
                if(CollectionUtils.isNotEmpty(shoppingGroupFrontDto.getSorted())) {
                    List<ShoppingCartItemFrontDto>  list=  shoppingGroupFrontDto.getSorted();
                    for (ShoppingCartItemFrontDto dto:list) {
                        ShoppingCartFrontDto item= dto.getItem();
                        if(item != null && !item.isGift() && item.getStatus() ==1) {
                            productVarietyNum++;
                            Integer amount=item.getAmount()== null ? 0:item.getAmount();
                            BigDecimal price=item.getPrice()== null ? BigDecimal.ZERO:item.getPrice();
                            countTotal=countTotal+amount;
                            subTotal=   subTotal.add(price.multiply(new BigDecimal(amount))) ;
                        }
                    }
                }
            }
            XyyJsonResult xyyJsonResult=     XyyJsonResult.createSuccess()
                    .addResult("title", title)
                    .addResult("countTotal",countTotal)
                    .addResult("productVarietyNum",productVarietyNum)
                    .addResult("subTotal", subTotal.setScale(2, RoundingMode.HALF_UP));
            log.info("goGatherOrdersStatistics result:{}",JSON.toJSONString(xyyJsonResult) );

            return xyyJsonResult;
        } catch (Exception e) {
            log.error("goGatherOrdersStatistics,err",e);
            return XyyJsonResult.createFailure();
        }
    }


    /**
     * 拼接页面参数
     *
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    protected String getRequestUrl(HttpServletRequest request){
        String url = "";
        String requestUri = request.getRequestURI();
        String queryString = request.getQueryString();
        String qs = StringUtil.removeParameter(queryString, "offset");
        if (requestUri.contains("/xyy-ec-pc/")) {
            requestUri = requestUri.replace("/xyy-ex-pc/", "/");
        }
        if (StringUtil.isNotEmpty(qs)) {
            url = requestUri + "?" + EncodeUtil.urlDecode(qs,"UTF-8");
        } else {
            url = requestUri;
        }
        return url;
    }

    /**
     * 满赠凑单页面
     */
    @RequestMapping(value = "/promo/getGiftSkuPool", method = {RequestMethod.POST, RequestMethod.GET})
    public XyyJsonResult getGiftSkuPool(@RequestParam(name = "merchantId") Long merchantId,
                                        @RequestParam(value = "terminalType", required = false) Integer terminalType,
                                        @RequestParam(value = "version", required = false) Integer version,
                                        Long promoId,Integer bizSource, HttpServletRequest request) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("查询赠品池接口 ，merchantId：{}，promoId：{}", merchantId, promoId);
            }
            // 参数校验
            if(Objects.isNull(promoId)){
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "promoId 为空");
            }
            CommonGiftPoolDto commonGiftPoolDto =new CommonGiftPoolDto();
            commonGiftPoolDto.setMerchantId(merchantId);
            commonGiftPoolDto.setPromoId(promoId);
            commonGiftPoolDto.setBizSource(bizSource);
            Boolean  isResultGiveUp=  orderServerRpcService.queryAutoGiveUpActFlag( commonGiftPoolDto);
            List<PcPromoGiftPoolSkuVO> productList = promotionService.queryAndHandleGiftSkuPool(promoId, merchantId,bizSource);
            return XyyJsonResult.createSuccess()
                    .addResult("productList", productList).addResult("isGiveUpGift",isResultGiveUp);
        } catch (Exception e) {
            log.error("getGiftSkuPool异常", e);
            return XyyJsonResult.createFailure();
        }
    }
}
