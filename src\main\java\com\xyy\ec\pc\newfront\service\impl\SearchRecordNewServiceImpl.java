package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.newfront.service.SearchRecordNewService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.api.SearchRecordBusinessApi;
import com.xyy.ec.product.business.dto.SearchRecordBusinessDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class SearchRecordNewServiceImpl extends BaseController implements SearchRecordNewService {

    private final XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0",timeout = 10000)
    SearchRecordBusinessApi searchRecordBusinessApi;

    @Override
    public List<SearchRecordBusinessDTO> getList(HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                log.error("用户登录异常");
                throw new AppException("用户登陆异常", XyyJsonResultCodeEnum.FAIL);
            }
            String branchCode = this.getBranchCodeByMerchantId(request,merchant.getId());
            return searchRecordBusinessApi.findSearchRecordlist(merchant.getId(), branchCode);
        } catch (Exception e) {
            log.error("SearchRecordController-list.json获取用户的搜索记录列表异常",e);
            throw new AppException("获取用户的搜索记录列表异常",XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public void addRecord(HttpServletRequest request, String keyword) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                throw new AppException("用户登陆异常", XyyJsonResultCodeEnum.FAIL);
            }
            if(StringUtil.isBlank(keyword)||StringUtil.isEmpty(keyword)){
                throw new AppException("新增搜索记录无效",XyyJsonResultCodeEnum.FAIL);
            }
            String keywordname = URLDecoder.decode(keyword);
            String branchCode = this.getBranchCodeByMerchantId(request,merchant.getId());
            SearchRecordBusinessDTO searchRecordBusinessDTO=new SearchRecordBusinessDTO();
            searchRecordBusinessDTO.setBranchCode(branchCode);
            searchRecordBusinessDTO.setKeyword(keywordname);
            searchRecordBusinessDTO.setMerchantId(merchant.getId());
            int save = searchRecordBusinessApi.save(searchRecordBusinessDTO);
            if (save > 0){
                log.info("新增搜索记录成功！");
            }else {
                log.info("新增搜索记录失败！");
                throw new AppException("新增搜索记录失败",XyyJsonResultCodeEnum.FAIL);
            }
        } catch (Exception e) {
            log.error("新增搜索记录异常-add",e);
            throw new AppException("新增搜索记录失败",XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public void delRecord(HttpServletRequest request, String keyword) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                throw new AppException("用户登陆异常", XyyJsonResultCodeEnum.FAIL);
            }
            if(StringUtil.isBlank(keyword)||StringUtil.isEmpty(keyword)){
                throw new AppException("删除搜索记录无效",XyyJsonResultCodeEnum.FAIL);
            }
            String branchCode = this.getBranchCodeByMerchantId(request,merchant.getId());
            SearchRecordBusinessDTO searchRecordBusinessDTO=new SearchRecordBusinessDTO();
            searchRecordBusinessDTO.setBranchCode(branchCode);
            searchRecordBusinessDTO.setMerchantId(merchant.getId());
            searchRecordBusinessDTO.setKeyword(keyword);
            searchRecordBusinessApi.deleteSearchRecord(searchRecordBusinessDTO);
        } catch (Exception e) {
            log.error("删除搜索记录异常-del",e);
            throw new AppException("删除搜索记录失败",XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public void delBatchRecord(HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                throw new AppException("用户登陆异常", XyyJsonResultCodeEnum.FAIL);
            }
            String branchCode = this.getBranchCodeByMerchantId(request,merchant.getId());
            SearchRecordBusinessDTO searchRecordBusinessDTO=new SearchRecordBusinessDTO();
            searchRecordBusinessDTO.setBranchCode(branchCode);
            searchRecordBusinessDTO.setMerchantId(merchant.getId());
            searchRecordBusinessApi.batchdelHistorySearchRecord(searchRecordBusinessDTO);
        } catch (Exception e) {
            log.error("批量删除搜索记录异常-del",e);
            throw new AppException("批量删除搜索记录失败",XyyJsonResultCodeEnum.FAIL);
        }
    }
}
