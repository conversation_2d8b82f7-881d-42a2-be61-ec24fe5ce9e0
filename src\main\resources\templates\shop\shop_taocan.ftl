<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description"
          content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>${shopInfo.showName!"药帮忙"}</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link href="/favicon.ico" rel="shortcut icon">
    <link rel="stylesheet" type="text/css" href="/static/css/sui.min.css"/>
    <link rel="stylesheet" type="text/css" href="/static/css/sui-append.min.css"/>
    <link rel="stylesheet" type="text/css" href="/static/css/reset.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/headerAndFooter.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/common.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/lib.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/shop/shop.css?t=${t_v}"/>
    <link rel="stylesheet" href="/static/css/shop/shop-taocan.css?v=${t_v}"/>

    <script type="text/javascript" src="/static/js/jquery-1.11.3.min.js"></script>
    <script type="text/javascript" src="/static/js/sui.min.js"></script>
    <script type="text/javascript" src="/static/js/common.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/plugins/layer/layer.js"></script>
    <script type="text/javascript" src="/static/js/lib.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/util.js?t=${t_v}"></script>
    <script src="/static/js/plugins/jquery.md5.js" type="text/javascript"></script>
    <#--<script type="text/javascript" src="/static/js/sku_search.js?t=${t_v}"></script>-->

    <script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/toast.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/zhuge/zhugeio.js?t=${t_v}"></script>
    <#--<script type="text/javascript" src="/static/js/activityEvent/events-20171108-taocan.js"></script>-->
    <style>
        .main{
            background: #ffffff;
            margin-top: 20px;
        }
        .ss-title a {
            width: 105px;
        }
        .stock{
            margin-left: 10px;
        }
        .vipa .tcnbox .row3 .quan {
            background-color: #FFA500;
            color: #ffffff;
            border-radius: 4px;
            border: 1px solid #FFA500;
            /*float: right;*/
        }
        .jp-quality .timeBox {
            background: #FEDCD6;
            border-radius: 4px;
            height: 34px;
            line-height: 34px;
            width: 290px;
            margin: 0 auto;
            color: #ED6464;
            font-size: 14px;
            text-align: center;
        }

        .jp-quality span {
            color: #ED6464;
            font-size: 14px;
            letter-spacing: 0.78px;
        }
        .xstcn .vipa .tcnbox .row6 a.zizhi{display:block;width: 210px;height: 32px;line-height: 32px;color:#ffffff;background: #2f3844;text-align: center;}
    </style>
</head>
<div class="container">
    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
        <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->

    <!--店铺对用户不可见-->
    <#if (isVisible)>
    <!--主体部分开始-->
    <div class="main">
        <input type="hidden" id="shopCode" value="${shopCode}"/>
        <input type="hidden" id="modelType" value="${modelType}"/>
        <input type="hidden" id="hasStock" value="${hasStock}"/>
        <input type="hidden" id="totalSalesVolumeType" value="${totalSalesVolumeType}"/>
        <input type="hidden" id="offset" value="${offset}"/>
        <input type="hidden" id="limit" value="${limit}"/>
        <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
        <input type="hidden" id="sysTime" name="sysTime" value="<#if currentDate??> ${currentDate?string("yyyy/MM/dd HH:mm:ss")}</#if>"/>
        <!--店铺信息-->
        <div class="default">
            <div class="head-box">
                <div class="head-l-box fl">
                    <#if shopInfo.pcLogoUrl??  && shopInfo.pcLogoUrl != "">
                        <a href="#" class="shop-logo"><img src='${shopInfo.pcLogoUrl!""}' alt='${shopInfo.showName!"药帮忙"}'></a>
                    </#if>
                    <div class="shop-des-box">
                        <p>${shopInfo.showName}</p>
                        <#if shopInfo.shopPropertyCode == "self">
                            <span class="grn">${shopInfo.shopPropertyName}</span>
                        </#if>
                        <#if shopInfo.shopTags??  && shopInfo.shopTags != "">
                            <#list shopInfo.shopTags?split(",") as shopTag>
                                <span class="blu">${shopTag}</span>
                            </#list>
                        </#if>
                    </div>
                </div>
                <div class="head-r-box fr" style="position: relative;">
                    <a id="J_set" href="javascript:void(0)" data-placement="bottom" data-toggle="tooltip" data-trigger="hover" data-original-title="<p style=&quot;font-size:16px;text-align:center;margin:15px auto 50px;&quot;>请复制链接完成分享吧</p><div><input id=&quot;spUrl&quot; value=&quot;&quot; type=&quot;text&quot;><span class=&quot;btn&quot;>复制链接</span></div>">
                        <span class="grn">
                            <img src="/static/images/share-icon.png" alt="">分享店铺
                        </span>
                    </a>
                </div>
            </div>
        </div>
        <!--导航-->
        <div class="head-bar">
            <ul>
                <li class='tab-bar'>
                    <a href="/shop/${shopCode}.htm" class="item">首页</a>
                </li>
                <li class='tab-bar shangpin-tab'>
                    <a href="/shop/productList.htm?shopCode=${shopCode}&categoryCode=${firstCategory}" class="item">所有商品</a>
                    <div class="shaixuan-box">
                        <ul>
                            <#list categorys as category>
                                <li class="${((category_index+1) % 3 == 0)?string("three-n" , "")}">
                                    <div class="shaixuan-yiji">
                                        <a href="/shop/productList.htm?shopCode=${shopCode}&categoryCode=${category.categoryCode}">${category.categoryName}</a>
                                        <i class="sui-icon icon-tb-right"></i>
                                    </div>
                                    <div class="shaixuan-erji">
                                        <#list category.subCategorys as subCategory>
                                        <#--                                    <a href="/shop/productList.htm?shopCode=${shopCode}&categoryCode=${subCategory.categoryCode}" class="${(subCategory.categoryCode == categoryCode)?string("active" , "")}">${subCategory.categoryName}</a>-->
                                        <a href="/shop/productList.htm?shopCode=${shopCode}&categoryCode=${subCategory.categoryCode}">${subCategory.categoryName}</a>
                                        </#list>
                                    </div>
                                </li>
                            <#if ((category_index+1)?number) % 3 ==0><li style="clear:both;"></li></#if>
                            </#list>
                        </ul>
                    </div>
                </li>
                 <#if (hasAct??)>
                    <li class='tab-bar'>
                        <a href="/actshop/shop_activity.htm?shopCode=${shopCode}" class="item">活动</a>
                    </li>
                </#if>
                <li class='tab-bar active'>
                    <a href="/actshop/shop/activityPackage.htm?shopCode=${shopCode}" class="item">套餐</a>
                </li>
            </ul>
        </div>
        <!--内容开始-->
        <div class="xstcn">
            <!--循环套餐开始-->
            <#if packageList??&&(packageList?size>0)>
                <#list packageList as package>
                <div class="modelbox vipa">
                    <div class="tcnbox">
                        <div class="titlebox" id="md-link1"></div>
                        <#if package??>
                            <!--套餐头部开始-->
                            <#if package.status ==1>
                            <!--抢光了&ndash;&gt;-->
                            <div class="qgbox"><img src="/static/images/events/20170818/new_qiangguang.png"></div>
                            </#if>
                            <!--价格-->
                            <div class="pricebox">
                                <#if merchant != null>
                                    <#if merchant.licenseStatus ==1 || merchant.licenseStatus ==5>
                                        <span class="login_tit">套餐价：</span><span class="login_show">含税价认证资质后可见</span>
                                    <#else>
                                        <span class="tcprice-t">套餐价 : </span><span class="tcprice-f">¥${package.setMealMoney}</span>
                                        <span class="tcprice">(共${package.skuList?size}种)</span>
                                        <span class="yprice-t">原价 : ￥${package.totalPrice+package.discountPrice}</span>
                                    </#if>
                                <#else>
                                    <!--价格登录可见&ndash;&gt;-->
                                    <span class="login_tit">套餐价：</span><span class="login_show">价格登录可见</span>
                                </#if>
                                <#--<#if package.status !=1 && package.saleCountDown??-->
                                    <#--&& package.saleCountDown gt 0>-->
                                    <#--<div class="jp-quality">-->
                                        <#--<div class="timeBox"-->
                                             <#--data-times="${package.startSaleTime?string("yyyy/MM/dd,HH:mm:ss")}">-->
                                            <#--距离开抢还剩：-->
                                            <#--<span class="time date"> </span>-->
                                            <#--<span class="time hour"> </span> :-->
                                            <#--<span class="time minutes"> </span> :-->
                                            <#--<span class="time seconds"></span>-->
                                        <#--</div>-->
                                    <#--</div>-->
                                <#--</#if>-->
                            </div>
                            <#if package.status !=1>
                            <#if package.saleCountDown??&& package.saleCountDown gt 0>
                                <div class="jp-quality">
                                    <div class="timeBox"
                                         data-times="${package.startSaleTime?string("yyyy/MM/dd,HH:mm:ss")}">
                                        距离开抢还剩：
                                        <span class="time date"> </span>
                                        <span class="time hour"> </span> :
                                        <span class="time minutes"> </span> :
                                        <span class="time seconds"></span>
                                    </div>
                                </div>
                            <#else >
                            <#--<!--加入购物车&ndash;&gt;-->
                            <div class="resultbox">
                                <div class="row6">
                                    <#if merchant.licenseStatus ==1>
                                        <a class="zizhi" href="/merchant/center/license/findLicenseCategoryInfo.htm">资质认证</a>
                                    <#elseif merchant.licenseStatus ==5>
                                        <a class="zizhi" href="/merchant/center/license/findLicenseCategoryInfo.htm">资质审核中</a>
                                    <#else>
                                    <a href="javascript:void(0);" class="sub fl">-</a>
                                    <input class="fl" type="text" id="num${package.id}"
                                           value="<#if package.status==0>${package.packageCartCount!0}<#else>0</#if>"
                                           onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                           onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"/>
                                    <a href="javascript:void(0);" class="add fl" id="tc_add">+</a>
                                        <#if  merchant != null>
                                            <#if package.status==0||package.status==2>
                                            <a href="javascript:void(0);" class="buy fl"
                                               onclick="changeCartPackage(${package.id},event,this)">加入采购单</a>
                                            <#else>
                                            <!--灰色样式&ndash;&gt;-->
                                            <a href="javascript:void(0);" class="gary fl">加入采购单</a>
                                            </#if>
                                        <#--<#else>-->
                                        <#--<!--灰色样式&ndash;&gt;-->
                                        <#--<a href="javascript:void(0);" class="gary fl">加入采购单</a>-->
                                        </#if>
                                    </#if>

                                    <#--<!--灰色样式&ndash;&gt;-->
                                    <#--<!--<a href="javascript:void(0);" class="gary fl">加入采购单</a>&ndash;&gt;-->
                                </div>
                            </div>
                            </#if>
                            </#if>
                            <!--套餐头部结束-->

                            <!--套餐商品列表-->
                            <div class="tcwarp">
                                <!--循环套餐商品开始-->
                                <#list package.skuList as packageProduct>
                                    <#if (packageProduct_index != 0 && ((packageProduct_index+1) % 3 = 0))>
                                    <div class="itembox three-n">
                                    <#elseif !packageProduct_has_next>
                                    <div class="itembox last">
                                    <#else>
                                    <div class="itembox">
                                    </#if>
                                        <div class="row1">
                                            <a href="/search/skuDetail/${packageProduct.sku.id}.htm"><img
                                                src="${productImageUrl}/ybm/product/${packageProduct.sku.imageUrl}" alt=""
                                                onerror="this.src='/static/images/default-middle.png'"/></a>
                                            <#--<!--标签&ndash;&gt;-->
                                            <div class="bq-box">
                                            </div>
                                        </div>
                                        <div class="row2">
                                            <a href="/search/skuDetail/${packageProduct.sku.id}.htm">${packageProduct.sku.commonName}</a>
                                        </div>
                                        <div class="row4 text-overflow">
                                            ${packageProduct.sku.spec}
                                        </div>
                                        <div class="row5 text-overflow">
                                            ${packageProduct.sku.manufacturer}
                                        </div>
                                        <div class="row3">
                                        <#if  merchant != null>
                                            <!--正常显示价格样式&ndash;&gt;-->
                                            <#if merchant.licenseStatus ==1 || merchant.licenseStatus ==5>
                                                <span class="login_show">含税价认证资质后可见</span>
                                            </#if>
                                            <span class="meiyuan">￥</span><span
                                                    class="price">${packageProduct.sku.fob}
                                            </span>
                                            <span class="tcsj">x${packageProduct.productNumber}
                                            </span>
                                            <#if packageProduct.sku.nearEffectiveFlag == 1>
                                                <span class="quan">临期</span>
                                            </#if>
                                            <#if packageProduct.sku.nearEffectiveFlag == 2>
                                                <span class="quan">近效期</span>
                                            </#if>
                                            <#else>
                                            <!--价格登录可见样式&ndash;&gt;-->
                                            <span class="login_show">价格登录可见</span>
                                            <!--暂无购买权限样式&ndash;&gt;-->
                                            <!--<span class="noPermission">暂无购买权限</span>&ndash;&gt;-->
                                        </#if>

                                            <#--  TODO:  -->
                                        <br>
                                        <#if skuVO.unitPrice?? && skuVO.unitPrice != ''>
                                            <span class="unitPriceTag">${skuVO.unitPrice}</span>  
                                            <#else>
                                            <span style="color: #666;font-size: 12px;display: inline-block;background-color:#f2f2f2;border-radius: 15px;padding: 2px 8px;">约￥19.9/kg</span>
                                        </#if>
                                        </div>
                                        <#--<div class="row6-box showorno">-->
                                        <#--<div class="row6">-->
                                        <#--<a href="javascript:void(0);" class="sub fl">-</a>-->
                                        <#--<input class="fl" type="text" value="20"-->
                                        <#--onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"-->
                                        <#--onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"/>-->
                                        <#--<a href="javascript:void(0);" class="add fl">+</a>-->
                                        <#--<a href="javascript:void(0);" class="buy fl">加入采购单</a>-->
                                        <#--<!--灰色样式&ndash;&gt;-->
                                        <#--<!--<a href="javascript:void(0);" class="gary fl">加入采购单</a>&ndash;&gt;-->
                                        <#--</div>-->
                                        <#--</div>-->
                                        <#--<!--收藏 已收藏类名hasCollect  未收藏类名nopCollect&ndash;&gt;-->
                                        <#--<div class="w-collectZone nopCollect initial j-collectBtn">-->
                                        <#--<div class="zone-1">-->
                                        <#--<div class="top top-1">-->
                                        <#--<span class="w-icon-normal icon-normal-collectEpt"></span>-->
                                        <#--</div>-->
                                        <#--<div class="top top-2">-->
                                        <#--<span class="w-icon-normal icon-normal-collectFull"></span>-->
                                        <#--</div>-->
                                        <#--</div>-->
                                        <#--<div class="zone-2">-->
                                        <#--<div class="bottom bottom-1">-->
                                        <#--<p class="textOne">收藏</p>-->
                                        <#--</div>-->
                                        <#--<div class="bottom bottom-2">-->
                                        <#--<p class="textTwo">已收藏</p>-->
                                        <#--</div>-->
                                        <#--</div>-->
                                        <#--</div>-->
                                        <#--<!--角标begin&ndash;&gt;-->
                                        <#--<div class="jiaobiaobox">-->
                                        <#--<#if packageProduct.sku.markerUrl?? && packageProduct.sku.markerUrl!=''>-->
                                        <#--<div class="yaokuanghuan-pos">-->
                                        <#--<img src="${productImageUrl}/${packageProduct.sku.markerUrl}" alt="">-->
                                        <#--</div>-->
                                        <#--</#if>-->
                                        <#--<!--控销角标 默认隐藏 去掉noshow显示&ndash;&gt;-->
                                        <#--<div class="kongxiao-pos noshow">-->
                                        <#--<span>控销</span>-->
                                        <#--</div>-->
                                        <#--<!--限购角标 默认隐藏 去掉noshow显示&ndash;&gt;-->
                                        <#--<div class="xiangou-pos noshow">-->
                                        <#--<span>限购50件</span>-->
                                        <#--</div>-->
                                        <#--<!--满减角标 默认隐藏 去掉noshow显示&ndash;&gt;-->
                                        <#--<div class="manjian-pos noshow">-->
                                        <#--<span>满减</span>-->
                                        <#--</div>-->
                                        <#--</div>-->
                                        <#--<!--加号&ndash;&gt;-->
                                        <#--<div class="jia"><img src="/static/images/events/20170818/jia.png"></div>-->
                                    </div>
                                </#list>
                                <!--循环套餐商品结束-->
                            </div>
                        </#if>
                    </div>
                </div>
                </#list>
            </#if>
            <!--循环套餐结束-->
        </div>
         <!--内容结束-->
    </div>
    <!--主体部分结束-->
    <#else>
        <!--店铺对用户不可见-->
        <div class="main">
            <div class="nosearchgood" style="margin-bottom: 150px;">
                <div class="tpbox" style="margin-top:20px;text-align: center;">
                    <img src="${ctx}/static/images/nosearchresult.png" alt="" style="margin-top: 62px;width:290px;height:200px;">
                    <p style="color:#999;margin-top:38px;font-size:18px;">控销店铺暂不支持自由浏览</p>
                </div>
            </div>
        </div>
    </#if>
    <!--底部导航区域开始-->
    <div class="footer" id="footer">
        <#include "/common/footer.ftl" />
    </div>
    <!--底部导航区域结束-->
</div>
</div>
</body>
<script type="text/javascript">
    <!--复制链接-->
    $("#J_set").bind('mouseover',function(){
        var str = '<p style="font-size:16px;text-align:left;margin:15px auto 50px;">请复制链接完成分享吧</p><div><input id="spUrl" value="${shopInfo.pcLink}" type="text"><span onClick="copyUrl()" class="btn">复制链接</span></div>'
        $(".head-r-box #J_set").attr("data-original-title",str);
    })
    function copyUrl(){
        spUrl.select();
        document.execCommand("Copy");
        $.toast({
            title: '复制成功'
        });
    }
    $(function () {

        /*减操作*/
        $(".sub").click(function () {
            var step = 10;
            var me = $(this),
                    txt = me.next(":text");
            var val = parseFloat(txt.val());
            if (val <= step) {
                txt.val(0);
            } else {
                txt.val(val - step);
            }
        });
        /*加操作*/
        $(".add").click(function () {
            var step = 10;
            var me = $(this),
                    txt = me.prev(":text");
            var val = parseFloat(txt.val());
            txt.val(val + step);
        });

        $(function () {
            $(".timeBox").each(function () {
                var times = $(this).attr("data-times");//获得timeBox的data值，即结束时间
                var sysTime = $('#sysTime').val();
                var now = new Date();
                if(sysTime){
                    now = new Date(sysTime);
                }
                var endtime = new Date(times);//把data值转换成时间
                var endTimer = endtime.getTime() - now.getTime();
                var seconds = endTimer / 1000;
                if (seconds > 0) {
                    timer($(this), seconds);
                }
            });
        });

        //时分秒倒计时方法
        function timer(element, seconds) {
            if (element) {
                if (seconds > 0) {
                    var dd = parseInt(seconds / 3600 / 24, 10);
                    var hh = parseInt(seconds % (3600 * 24) / 3600, 10);
                    var mm = parseInt(seconds % 3600 / 60, 10);
                    var ss = parseInt(seconds % 3600 % 60, 10);
                    dd = dd < 10 ? ("0" + dd) : dd; //天
                    hh = hh < 10 ? ("0" + hh) : hh; //时
                    mm = mm < 10 ? ("0" + mm) : mm; //分
                    ss = ss < 10 ? ("0" + ss) : ss; //秒
                    if (dd && dd > 0) {
                        $(element).find(".date").text(dd + "天");
                    } else {
                        $(element).find(".date").hide();
                    }
                    $(element).find(".hour").text(hh);
                    $(element).find(".minutes").text(mm);
                    $(element).find(".seconds").text(ss);
                    setTimeout(function () {
                        timer(element, seconds - 1);
                    }, 1000);
                } else {
                    $(element).remove();
                    setTimeout(function () {
                        location.reload(true);
                    }, 1000);
                }
            }
        }
    });
    var cartUrlPrefix =  "/merchant/center/cart";

    //添加购物车接口
    function changeCartPackage(packageId, event, _this, type) {
        var packageAmount = $("#num" + packageId).val();
        if (packageAmount == 0) {
            $.alert("购物数量不能为0，请输入购买数量!");
            return;
        }
        $.ajax({
            url: cartUrlPrefix + "/changeCart.json",
            type: "POST",
            dataType: "json",
            async: true,
            data: {
                packageId: packageId,
                amount: packageAmount
            },
            success: function (result) {
                if (result.status == "success") {
                    var cartNum = getCartNum();
                    if (type == 1) {
                        packageFlyYY(event, _this, cartNum);
                    } else {
                        packageFly(event, _this, cartNum);
                    }

                    if (result.data.message != null && result.data.message != "") {
                        $.alert(result.data.message);
                    }
                    $("#num" + packageId).val(result.data.qty);
                } else {
                    $.alert(result.errorMsg);
                }
            }
        });
    }

    //销售套餐飞购物车效果~
    function packageFly(event, _this, cartNum) {
        //var offset = $(".pressCart").offset();
        var offset = $(".cycle2").offset();
        var scrollX = $(window).scrollTop(); //获取滚动条的距离。
        var addcar = $(_this);
        var img = addcar.parent().parent().parent().siblings(".tcwarp").find(".itembox").find(".row1").find("img").attr('src');
        //var img = "http://tesupload.ybm100.com/ybm/product/6926154730416.jpg?random=0.04261914503330155";
        /*var img = addcar.parent().parent().siblings(".row1").find("img").attr('src');*/
        var flyer = $('<img class="u-flyer" src="' + img + '">');
        flyer.fly({
            start: {
                left: event.pageX, //开始位置（必填）#fly元素会被设置成position: fixed
                top: event.pageY - scrollX  //开始位置（必填）
            },
            end: {
                left: offset.left + 10, //结束位置（必填）
                top: offset.top + 10 - scrollX, //结束位置（必填）
                width: 0, //结束时宽度
                height: 0 //结束时高度
            },
            onEnd: function () { //结束回调
                if (cartNum >0) {
                    $("#cartNumberLi").addClass("cycle");
                    $("#cartNumberDiv").addClass("topp");
                    $("#rigthCartNum").removeClass("cycle2 noshow");
                    $("#rigthCartNum").addClass("cycle2");
                    $("#rigthCartNum").addClass("topp");
                }
                $("#cartNumberLi").html(cartNum);
                $("#rigthCartNum").html(cartNum);
                $("#cartNumberDiv").html(cartNum);
            }
        });
    }

    //运营套餐飞购物车效果~
    function packageFlyYY(event, _this, cartNum) {
        //var offset = $(".pressCart").offset();
        var offset = $(".cycle2").offset();
        var scrollX = $(window).scrollTop(); //获取滚动条的距离。
        var addcar = $(_this);
        var img = addcar.parent().parent().parent().siblings(".itembox").find(".row1").find("img").attr('src');
        //var img = "http://tesupload.ybm100.com/ybm/product/6926154730416.jpg?random=0.04261914503330155";
        /*var img = addcar.parent().parent().siblings(".row1").find("img").attr('src');*/
        var flyer = $('<img class="u-flyer" src="' + img + '">');
        flyer.fly({
            start: {
                left: event.pageX, //开始位置（必填）#fly元素会被设置成position: fixed
                top: event.pageY - scrollX  //开始位置（必填）
            },
            end: {
                left: offset.left + 10, //结束位置（必填）
                top: offset.top + 10 - scrollX, //结束位置（必填）
                width: 0, //结束时宽度
                height: 0 //结束时高度
            },
            onEnd: function () { //结束回调
                if (cartNum == 1) {
                    $("#cartNumberLi").addClass("cycle");
                    $("#cartNumberDiv").addClass("topp");
                    $("#rigthCartNum").removeClass("cycle2 noshow");
                    $("#rigthCartNum").addClass("cycle2");
                    $("#rigthCartNum").addClass("topp");
                }
                $("#cartNumberLi").html(cartNum);
                $("#rigthCartNum").html(cartNum);
                $("#cartNumberDiv").html(cartNum);
            }
        });
    }
</script>
</html>


