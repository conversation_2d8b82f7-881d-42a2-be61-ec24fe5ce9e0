package com.xyy.ec.pc.cms.dto;

import com.xyy.ec.layout.buinese.ecp.enums.CmsEventTrackingSpTypeEnum;
import com.xyy.ec.layout.buinese.ecp.params.CmsEventTrackingSpIdGenerateParam;
import com.xyy.ec.pc.cms.enums.LayoutComponentEnum;
import com.xyy.ec.pc.cms.param.CmsRequestParam;
import com.xyy.ec.pc.util.SearchUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * CMS 请求参数DTO
 *
 * <AUTHOR>
 * @see CmsRequestParam
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CmsRequestDTO implements Serializable {

    private static final long serialVersionUID = -4826036724699604240L;

    /* 请求相关参数 */

    /**
     * @see LayoutComponentEnum
     */
    private Integer moduleSourceType;

    /**
     * 是否填充拼团活动数据
     */
    private Boolean isFillActPt;

    /**
     * 是否填充批购包邮活动数据
     */
    private Boolean isFillActPgby;

    /**
     * 期望的商品数量
     */
    private Integer expectedProductNum;

    /**
     * 商品组ID
     */
    private String exhibitionIdStr;

    /**
     * 商品组id
     */
    private String exhibitionId;

    private Long categoryId;

    /**
     * 店铺编码，选填
     */
    private String shopCodes;

    private Integer sortType;

    /**
     * 锚点商品Id列表
     */
    private String anchorCsuIds;

    /**
     * 分页
     */
    private Integer pageNum;

    /**
     * 分页
     */
    private Integer pageSize;

    private Boolean isEnd;

    /* 埋点相关参数 */
    /**
     * <pre>
     * 策略追踪id。相当于sid。
     * sid生成策略：日期（20200316）-用户ID-随机5个字符-平台。（1-Android；2-IOS；3-H5；4-PC）。
     * 例如：20200408-160537-YBMNB-1。
     * </pre>
     */
    private String nsid;
    /**
     * 当前已展示条数
     */
    private Integer currentCount;
    /**
     * 来源位置
     */
    private String pageSource;
    /**
     * 当前页面url
     */
    private String pageUrl;
    /**
     * 当前页currentPageSource
     */
    private String currentPageSource;
    /**
     * 页面id
     */
    private String pageId;
    /**
     * 页面标题
     */
    private String pageTitle;

    /**
     * <pre>
     * 埋点参数。
     * 埋点spType。
     * 首页-精选商品流时：“精选”为3，其他的选项卡为22；
     * 首页-定时活动组件时：20；
     * 首页-精选店铺组件时：21；
     * </pre>
     *
     * @see CmsEventTrackingSpTypeEnum
     */
    private String sptype;
    /**
     * <pre>
     * 埋点spId。
     * 首页-精选商品流时：选项卡位置-选项卡title名称-用户id；
     * 首页-定时活动组件时：商品坑位位置-商品ID-用户ID；
     * 首页-精选店铺组件时：店铺坑位位置-店铺编码-用户ID；
     * </pre>
     *
     * @see com.xyy.ec.layout.buinese.ecp.utils.CmsEventTrackingUtils#generateSpId(CmsEventTrackingSpIdGenerateParam)
     */
    private String spid;
    /**
     * <pre>
     * 策略追踪id。sid生成策略：日期（20200316）-用户ID-随机5个字符-平台。（1-Android；2-IOS；3-H5；4-PC）。
     * 例如：20200408-160537-YBMNB-1。
     * </pre>
     *
     * @see SearchUtils#generateSidData(Long, int)
     */
    private String sid;

    private String scmE;
}
