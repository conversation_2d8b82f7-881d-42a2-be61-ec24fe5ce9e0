package com.xyy.ec.pc.cms.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * CMS拼团查询类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum CmsGroupBuyingQueryTypeEnum {

    NOT_START(1, "未开始"),
    IN_PROGRESS(2, "进行中"),
    ;

    private Integer type;
    private String name;

    CmsGroupBuyingQueryTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param type
     * @return
     */
    public static CmsGroupBuyingQueryTypeEnum valueOfCustom(Integer type) {
        for (CmsGroupBuyingQueryTypeEnum anEnum : values()) {
            if (Objects.equals(anEnum.getType(), type)) {
                return anEnum;
            }
        }
        return null;
    }

}
