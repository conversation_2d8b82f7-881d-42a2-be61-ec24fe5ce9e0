<!DOCTYPE HTML>
<html>

<head>
    <#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="领券中心">
    <meta name="keywords" content="领券中心">
    <title>领券中心</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" type="text/css" href="/static/css/sui.min.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/sui-append.min.css?t=${t_v}"/>
    <link rel="stylesheet" type="text/css" href="/static/css/lib.css"/>
    <link rel="stylesheet" href="/static/css/reset.css?t=${t_v}"/>
    <link rel="stylesheet" href="/static/css/headerAndFooter.css?t=${t_v}"/>
    <link rel="stylesheet" href="http://upload.ybm100.com/ybm/pc/activitys/css/events-20171024.css?t=${t_v}"/>
    <link rel="stylesheet" href="/static/css/lib.css?t=${t_v}"/>
    <link rel="stylesheet" href="/static/css/activityEvent/events-20180605-lt-lqzx.css"/>
<#--<script type="text/javascript" src="/static/js/activityEvent/html5.js"></script>-->
    <script type="text/javascript" src="/static/js/activityEvent/canvas.js"></script>
    <script type="text/javascript">
        $(function () {
            var is806Style = $("#is806Style").val();
            if (is806Style) {
                $(".title").remove();
                $(".banner").css({
                    width: '100%',
                    height: '370px',
                    background: "url('/images/events/20180605-lt-lqzx/banner_02.jpg') no-repeat center top",
                    "min-width": '1200px',
                    "background-color": '#4e0ff6'
                });
                $(".main").css("background-color", "#b1eab8");
                $(".main-in").css("padding-top", "50px");
                $("ul").css("background-color", "#c0efc6;");
                $("ul").css("border-radius", "15px");
                $("ul").css("padding", "20px 0 10px 0;");
            }
            $("[id^=allTimer_]").each(function () {
                var index = $(this).attr("index");
                timer('allTimer_' + index, index);
            });
        });
        //时分秒倒计时方法
        function timer(eleId, eleId2) {
            var element = document.getElementById(eleId);
            if (element) {
                var endTimer = $("#" + eleId).attr("startTimeStamp");
                var ts = endTimer - 1000;
                $("#" + eleId).attr("startTimeStamp", ts);
                if (ts > 0) {
                    var hh = parseInt(ts / 1000 / 60 / 60, 10);
                    var mm = parseInt(ts / 1000 / 60 % 60, 10);
                    var ss = parseInt(ts / 1000 % 60, 10);
                    hh = hh < 10 ? ("0" + hh) : hh; //时
                    mm = mm < 10 ? ("0" + mm) : mm; //分
                    ss = ss < 10 ? ("0" + ss) : ss; //秒
                    $("#timer_h_" + eleId2).html(hh);
                    $("#timer_m_" + eleId2).html(mm);
                    $("#timer_s_" + eleId2).html(ss);
                    setTimeout(function () {
                        timer(eleId, eleId2);
                    }, 1000);
                } else {
                    //去领取
                    var merchantId = $("#" + eleId2).attr("merchantId");
                    var voucherId = $("#" + eleId2).attr("voucherId");
                    var receiveTotalCount = $("#" + eleId2).attr("receiveTotalCount");
                    var totalLimitQty = $("#" + eleId2).attr("totalLimitQty");
                    $("#" + eleId2).attr("onclick", 'receiveTemplate(' + eleId2 + ',' + merchantId + ',' + voucherId + ')');
                    $("#" + eleId2).html('<canvas width="62" height="51" class="canvas' + voucherId + '" receiveTotalCount="' + receiveTotalCount + '" totalLimitQty="' + totalLimitQty + '">您的浏览器不支持canvas，请换个浏览器</canvas>');
                    $("#" + eleId2).append('<a href="javascript:void(0);" id="' + voucherId + '" class="pick-up-now">立即领取 </a>');
                    new Drawpiechart(".canvas" + voucherId, receiveTotalCount, totalLimitQty);
                }
            }
        }
    </script>
    <style>
        .main-content .content-box{
            width:1126px;
            margin:0 auto;
            overflow: hidden;
        }
        .main-content .content-box .content-item{
            margin-bottom: 35px;
        }
        .main-content .content-box .content-item .content-item-title{
            font-size: 19px;
            border-left:3px solid #00DC82;
            color:#333;
            padding-left: 5px;
            font-weight:500;
            margin-bottom: 10px;
        }
        .main-content .content-box .content-item .quan-box{
            background:rgba(255,255,255,1);
            padding:26px 45px;
        }
        .main-content .content-box .content-item .quan-box .quan-list{
            overflow:hidden;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item{
            margin-right: 27px;
            margin-bottom: 10px;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan{
            width: 448px;
            height: 126px;
            padding-top: 10px;
            box-sizing: border-box;
            position:relative;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .shangpin-quan{
            width: 448px;
            height: 206px;
            padding-top: 10px;
            box-sizing: border-box;
            background: url(/static/images/events/20180605-lt-lqzx/shangpin_quan.png) no-repeat top center;
            background-size: 100%;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-1{
            background: url(/static/images/events/20180605-lt-lqzx/quan_1.png) no-repeat top center;
            background-size: 100%;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-2{
            background: url(/static/images/events/20180605-lt-lqzx/quan_2.png) no-repeat top center;
            background-size: 100%;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-3{
            background: url(/static/images/events/20180605-lt-lqzx/quan_3.png) no-repeat top center;
            background-size: 100%;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-4{
            background: url(/static/images/events/20180605-lt-lqzx/quan_4.png) no-repeat top center;
            background-size: 100%;
        }
        .quan-center-left{
            margin:0 25px;
            width:277px;
        }
        .quan-center-left .left-row1 .quan-quan{
            display: inline-block;
            width: 50px;
            height: 18px;
            font-size: 13px;
            text-align: center;
            line-height: 18px;
            color: #FFFFFF;
            background: rgba(255,66,68,1);
            /*background:linear-gradient(90deg,rgba(242,121,38,1) 0%,rgba(236,46,36,1) 100%);*/
            border-radius: 100px;
            margin-right: 3px;
            float: left;
        }
        .quan-center-left .left-row1 .quan-xin{
            background:linear-gradient(90deg,rgba(242,121,38,1) 0%,rgba(236,46,36,1) 100%);
        }
        .quan-center-left .left-row1 .quan-tong{
            background:linear-gradient(90deg,rgba(250,178,97,1) 0%,rgba(247,107,28,1) 100%);
        }
        .quan-center-left .left-row1 .quan-die{
            background:linear-gradient(103deg,rgba(255,107,113,1) 0%,rgba(224,46,36,1) 100%);
        }
        .quan-center-left .left-row1 .quan-title{
            color:#292933;
            display: inline-block;
            width: 217px;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            height: 19px;
        }
        .shangpin-quan .quan-title{
            width:340px;
        }
        .quan-title{
            color:#292933;
            font-size:14px;
            font-weight:500;
            font-family:PingFangSC-Medium,PingFang SC;
            overflow: hidden;
            text-overflow:ellipsis;
            white-space: nowrap;
            display: inline-block;
        }
        .quan-title nobr{
            overflow: hidden;
            text-overflow:ellipsis;
            white-space: nowrap;
        }
        .quan-center-left .left-row2{
            font-size:12px;
            color:#292933;
            font-weight:400;
            width:100%;
            margin-top: 10px;
            height:16px;
        }
        .quan-center-left .left-row3{
            font-size:12px;
            color:#9494A6;
            font-weight:400;
            width:100%;
            line-height:16px;
            margin-top: 10px;
        }
        .quan-1 .left-row3,.quan-2 .left-row3,.quan-3 .left-row3,.quan-4 .left-row3{
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }
        .quan-center-right{
            text-align: center;
            width:115px;
            height:95px;
        }
        .quan-center-right .right-row1{
            color:#FF4244;
            font-size:14px;
            font-weight:600;
            margin-top: 8px;
        }
        .quan-center-right .right-row2{
            /*height:17px;*/
            font-size:12px;
            line-height:15px;
            margin-top: 5px;
            color:#FF4244;
        }
        .quan-center-right .right-row3{
            width:84px;
            height:30px;
            background:linear-gradient(90deg,rgba(255,96,37,1) 0%,rgba(255,66,68,1) 100%);
            border-radius:16px;
            color: #ffffff;
            text-align:center;
            line-height:30px;
            margin:5px auto 0;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-2 .quan-center-right{
            position:relative;
            background: url("/static/images/events/20180605-lt-lqzx/quan_icon_2.png")  58px -5px no-repeat;
            background-size: 60px 60px;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-3 .quan-center-right{
           position:relative;
           background: url("/static/images/events/20180605-lt-lqzx/quan_icon_1.png")  58px -5px no-repeat;
            background-size: 60px 60px;
        }
        /*.main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-4 .quan-center-right{*/
            /*position:relative;*/
            /*background: url("/static/images/events/20180605-lt-lqzx/quan_icon_1.png")  58px -5px no-repeat;*/
            /*background-size: 60px 60px;*/
        /*}*/
        .quan-4 .right-row3{
            background:linear-gradient(90deg,rgba(255,96,37,1) 0%,rgba(255,66,68,1) 100%);
            opacity:0.5
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .shangpin-quan2 .status-bg{
            position:relative;
            background: url("/static/images/events/20180605-lt-lqzx/quan_icon_2.png")  382px -4px no-repeat;
            background-size: 60px 60px;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .shangpin-quan3 .status-bg{
            position:relative;
            background: url("/static/images/events/20180605-lt-lqzx/quan_icon_1.png")  382px -4px no-repeat;
            background-size: 60px 60px;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-2 .right-row3{
            border:1px solid #FF2121;
            background:transparent;
            color:#FF2121;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .shangpin-quan2 .right-row3{
            border:1px solid #FF2121;
            background:transparent;
            color:#FF2121;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-3 .right-row1{
            color:#999999;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-3 .right-row2{
            color:#999999;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-3 .right-row3{
            color:#999999;
            background: #F9F9F9;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .shangpin-quan3 .right-row1{
            color:#999999;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .shangpin-quan3 .right-row2{
            color:#999999;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .shangpin-quan3 .right-row3{
            color:#999999;
            background: #F9F9F9;
        }
        .main-content .content-box .content-item .quan-box .quan-list .quan-item .quan-2 .right-row3{
            border:1px solid #FF2121;
            background:transparent;
            color:#FF2121;
        }
        .right-row3 a{
            display: inline-block;
            width:100%;
            height:100%;
        }
        .quan-1 .right-row3 a{
            color: #ffffff;
        }
        .quan-2 .right-row3 a{
            color:#FF2121;
        }
        .shangpin-quan .shangpin-quan-row{
            margin:0 25px;
        }
        .shangpin-quan .shangpin-quan-row .quan-quan{
            display: inline-block;
            width: 50px;
            height: 18px;
            font-size: 13px;
            text-align: center;
            line-height: 18px;
            color: #FFFFFF;
            background: rgba(255,66,68,1);
            background:linear-gradient(129deg,rgba(242,212,195,1) 0%,rgba(215,165,138,1) 100%);
            border-radius: 100px;
            margin-right: 3px;
            float: left;
        }
        .shangpin-quan .shangpin-quan-row .quan-die{
            background:linear-gradient(103deg,rgba(255,107,113,1) 0%,rgba(224,46,36,1) 100%);
        }
        .shangpin-quan .shangpin-quan-row .quan-platform{
            background:linear-gradient(90deg,#ff5d5d 7%, #f61d1d 92%);
        }
        .shangpin-quan .quan-center-left{
            margin-right: 0;
            width:310px;
            position:relative;
            height:110px;
        }
        .shangpin-quan-content .left-row4{
            position: absolute;
            bottom:13px;
            left:3px;
            overflow: hidden;
        }
        .shangpin-quan-content .left-row4 .left-row4-one{
            color:#FF2121;
            font-size:14px;
            margin-right: 10px;
            margin-top: 2px;
        }
        .shangpin-quan-content .left-row4 .left-row4-two .time{
            color:#fff;
            width:20px;
            height:20px;
            background:linear-gradient(90deg,rgba(255,96,37,1) 0%,rgba(255,66,68,1) 100%);
            border-radius:1px;
            font-size:14px;
            text-align: center;
            line-height: 20px;
        }
        .shangpin-quan-content .left-row4 .left-row4-two .miao{
            color:#FF2121;
            margin:0 5px;
            font-size:14px;
        }
        .quan-4 .quan-center-left .left-row4{
            position: absolute;
            bottom:22px;
            left:25px;
            overflow: hidden;
        }
        .quan-4 .quan-center-left .left-row4 .left-row4-one{
            color:#FF2121;
            font-size:14px;
            margin-right: 10px;
            margin-top: 2px;
        }
        .quan-4 .quan-center-left .left-row4 .left-row4-two .time{
            color:#fff;
            width:20px;
            height:20px;
            background:linear-gradient(90deg,rgba(255,96,37,1) 0%,rgba(255,66,68,1) 100%);
            border-radius:1px;
            font-size:14px;
            text-align: center;
            line-height: 20px;
        }
        .quan-4 .quan-center-left .left-row4 .left-row4-two .miao{
            color:#FF2121;
            margin:0 5px;
            font-size:14px;
        }
        .shangpin-quan .quan-center-left .left-row2 {
            margin-top: 5px;
            height:16px;
        }
        .shangpin-quan .quan-center-left .left-row3{
            overflow: hidden;
            margin-top: 5px;
            height:81px;
        }
        .shangpin-quan .quan-center-left .left-row3 p{
            width:73px;
            height:79px;
            /*border:1px solid #ddd;*/
            margin-right: 29px;
            text-align: center;
            cursor:pointer;
        }
        .shangpin-quan .quan-center-right{
            width:90px;
        }
        .shangpin-quan-content{
            overflow: hidden;
            height:125px;
            clear:both;
        }
        .shangpin-quan-info{
            font-size:12px;
            color:#9494A6;
            margin:0 25px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }
        .shangpin-quan1 .right-row3 a{
            color: #ffffff;
        }
        .quan-4 .right-row3 a{
            color: #ffffff;
        }
        .shangpin-quan2 .right-row3 a{
            color: #FF4244;
        }
    </style>
</head>

<body>
<div class="container">

    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
    <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->
    <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
    <!--头部导航区域结束-->
    <div class="banner">

    </div>
    <!--主体部分开始-->
    <div class="main ">
        <#if voucherList?? && voucherList?size gt 0>
            <div class="title">
                —— 领券中心 ——
            </div>
            <input type="hidden" id="is806Style" value="${is806Style}">
            <!--领券中心新版开始-->
            <div class="main-content">
                <div class="content-box">
                    <div class="content-item">
                        <p class="content-item-title">指定商品券</p>
                        <div class="quan-box">
                            <ul class="quan-list">
                                <#list voucherList as voucher>
                                    <#--商品券，指定商品的叠加券，指定商品的平台券-->
                                    <#if voucher.voucherType==2 || (voucher.voucherType==6 && voucher.voucherSkuImages ??) || (voucher.voucherType==8 && voucher.voucherSkuImages ??)>
                                        <#if voucher.isLq == 0>
                                            <!--未开始-->
                                            <#if (voucher.startTimeStamp > 0)>
                                                <li class="quan-item fl">

                                                    <div class="quan shangpin-quan shangpin-quan1">
                                                        <div class="shangpin-quan-row">
                                                            <span class="quan-quan <#if voucher.voucherType==6>quan-die</#if><#if voucher.voucherType==8>quan-platform</#if>">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                        </div>
                                                        <div class="shangpin-quan-content">
                                                            <div class="quan-center-left fl">
                                                                <div class="left-row4">
                                                                    <span class="left-row4-one fl">
                                                                        距开抢还剩
                                                                    </span>
                                                                    <div class="left-row4-two fl" id="allTimer_${voucher_index}" index="${voucher_index}"
                                                                         startTimeStamp="${voucher.startTimeStamp}">
                                                                        <div class="time fl" id="timer_h_${voucher_index}"></div>
                                                                        <div class="miao fl">:</div>
                                                                        <div class="time fl" id="timer_m_${voucher_index}"></div>
                                                                        <div class="miao fl">:</div>
                                                                        <div class="time fl" id="timer_s_${voucher_index}"></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="quan-center-right fl">
                                                                <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                <div class="right-row2">
                                                                    <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                    <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                        ${voucher.maxMoneyInVoucherDesc}
                                                                    </#if>
                                                                </div>
                                                                <div class="right-row3" style="opacity: 0.5;background: linear-gradient(90deg,rgba(255,96,37,1) 0%,rgba(255,66,68,1) 100%);">立即领取
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="shangpin-quan-info">
                                                            <#--${voucher.voucherInstructions}-->
                                                        </div>
                                                    </div>
                                                </li>
                                            </#if>
                                            <!--未领取-->
                                            <#if (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>
                                                <#if voucher.provideTotalCount-voucher.receiveTotalCount gt 0 >
                                                <li class="quan-item fl">
                                                    <div class="quan shangpin-quan shangpin-quan1">
                                                        <div class="shangpin-quan-row">
                                                            <span class="quan-quan <#if voucher.voucherType==6>quan-die</#if><#if voucher.voucherType==8>quan-platform</#if>">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                        </div>
                                                        <div class="shangpin-quan-content">
                                                            <div class="quan-center-left fl">
                                                                <div class="left-row2"></div>
                                                                <div class="left-row3">
                                                                    <#list voucher.voucherSkuImages as skuImage>
                                                                        <p class="shangpin-img fl">
                                                                            <img src="${productImageUrl}/ybm/product/min/${skuImage.imageUrl}" height="73" width="79" alt=""  onclick="window.open('/search/skuDetail/${skuImage.skuId}.htm')">
                                                                        </p>
                                                                    </#list>
                                                                </div>
                                                            </div>
                                                            <div class="quan-center-right fl">
                                                                <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                <div class="right-row2">
                                                                    <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                    <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                        ${voucher.maxMoneyInVoucherDesc}
                                                                    </#if>
                                                                </div>
                                                                <div class="right-row3" id="${voucher_index}">
                                                                    <a href="javascript:void(0);" id="${voucher.templateId}" class="pick-up-now" onclick="receiveTemplate(${voucher_index}, ${merchantId},${voucher.templateId})">立即领取</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="shangpin-quan-info" title="${voucher.voucherInstructions}">
                                                            ${voucher.voucherInstructions}
                                                        </div>
                                                    </div>
                                                </li>
                                                </#if>
                                            </#if>
                                            <!--已抢光-->
                                            <#if (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>
                                                <#if voucher.provideTotalCount-voucher.receiveTotalCount <=0 >
                                                    <li class="quan-item fl">
                                                        <div class="quan shangpin-quan shangpin-quan3">
                                                            <div class="status-bg">
                                                                <div class="shangpin-quan-row">
                                                                    <span class="quan-quan <#if voucher.voucherType==6>quan-die</#if><#if voucher.voucherType==8>quan-platform</#if>">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                                </div>
                                                                <div class="shangpin-quan-content">
                                                                    <div class="quan-center-left fl">
                                                                        <div class="left-row2"></div>
                                                                        <div class="left-row3">
                                                                            <#list voucher.voucherSkuImages as skuImage>
                                                                                <p class="shangpin-img fl">
                                                                                    <img src="${productImageUrl}/ybm/product/min/${skuImage.imageUrl}" height="73" width="79" alt=""  onclick="window.open('/search/skuDetail/${skuImage.skuId}.htm')">
                                                                                </p>
                                                                            </#list>
                                                                        </div>
                                                                    </div>
                                                                    <div class="quan-center-right fl">
                                                                        <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                        <div class="right-row2">
                                                                            <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                            <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                                ${voucher.maxMoneyInVoucherDesc}
                                                                            </#if>
                                                                        </div>
                                                                        <div class="right-row3">立即领取</div>
                                                                    </div>
                                                                </div>
                                                                <div class="shangpin-quan-info" title="${voucher.voucherInstructions}">
                                                                    ${voucher.voucherInstructions}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </#if>
                                            </#if>
                                        <#else>
                                            <!--已领取-->
                                            <#if ( voucher.state == 3 || voucher.state == 4 || (voucher.expireDate && voucher.expireDate?datetime <= .now?datetime)) >

                                            <#else>
                                                <li class="quan-item fl">
                                                    <div class="quan shangpin-quan shangpin-quan2">
                                                        <div class="status-bg">
                                                            <div class="shangpin-quan-row">
                                                                <span class="quan-quan <#if voucher.voucherType==6>quan-die</#if><#if voucher.voucherType==8>quan-platform</#if>">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                            </div>
                                                            <div class="shangpin-quan-content">
                                                                <div class="quan-center-left fl">
                                                                    <div class="left-row2"></div>
                                                                    <div class="left-row3">
                                                                        <#list voucher.voucherSkuImages as skuImage>
                                                                            <p class="shangpin-img fl">
                                                                                <img src="${productImageUrl}/ybm/product/min/${skuImage.imageUrl}" height="73" width="79" alt=""  onclick="window.open('/search/skuDetail/${skuImage.skuId}.htm')">
                                                                            </p>
                                                                        </#list>
                                                                    </div>
                                                                </div>
                                                                <div class="quan-center-right fl" >
                                                                    <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                    <div class="right-row2">
                                                                        <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                        <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                            ${voucher.maxMoneyInVoucherDesc}
                                                                        </#if>
                                                                    </div>
                                                                    <div class="right-row3" id="quanRight_${voucher_index}">
                                                                        <!--已领取-->
                                                                        <a href="/voucher/centre/findVoucherSku.htm?voucherTemplateId=${voucher.templateId}&merchantId=${merchantId}&source=1" class="go-to-see">查看商品</a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="shangpin-quan-info" title="${voucher.voucherInstructions}">
                                                                ${voucher.voucherInstructions}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </#if>
                                        </#if>
                                    </#if>
                                </#list>
                            </ul>
                        </div>
                    </div>
                    <div class="content-item">
                        <p class="content-item-title">其他自营店铺券</p>
                        <div class="quan-box">
                            <ul class="quan-list">
                                <#list voucherList as voucher>
                                    <!--叠加券-->
                                    <#if voucher.voucherType==6 && !(voucher.voucherSkuImages ??)>
                                        <#if voucher.isLq == 0>
                                            <!--未开始-->
                                            <#if (voucher.startTimeStamp > 0)>
                                                <li class="quan-item fl">
                                                    <div class="quan quan-4">
                                                        <div class="quan-center-left fl">
                                                            <div class="left-row1">
                                                                <span class="quan-quan quan-die">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                            </div>
                                                        <#--<div class="left-row2">2019/11/11-2019/12/11</div>-->
                                                        <#--<div class="left-row3">券使用说明：${voucher.voucherInstructions}</div>-->
                                                            <div class="left-row4">
                                                                <span class="left-row4-one fl">
                                                                    距开抢还剩
                                                                </span>
                                                                <div class="left-row4-two fl" id="allTimer_${voucher_index}" index="${voucher_index}"
                                                                     startTimeStamp="${voucher.startTimeStamp}">
                                                                    <div class="time fl" id="timer_h_${voucher_index}">11</div>
                                                                    <div class="miao fl">:</div>
                                                                    <div class="time fl" id="timer_m_${voucher_index}">11</div>
                                                                    <div class="miao fl">:</div>
                                                                    <div class="time fl" id="timer_s_${voucher_index}">11</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="quan-center-right fl">
                                                            <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                            <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                            <div class="right-row3">立即领取</div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </#if>
                                            <!--未领取-->
                                            <#if (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>
                                                <#if voucher.provideTotalCount-voucher.receiveTotalCount gt 0 >
                                                    <li class="quan-item fl">
                                                        <div class="quan quan-1">
                                                            <div class="quan-center-left fl">
                                                                <div class="left-row1">
                                                                    <span class="quan-quan quan-die">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                                </div>
                                                                <div class="left-row2"></div>
                                                                <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                            </div>
                                                            <div class="quan-center-right fl">
                                                                <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                                <div class="right-row3" id="${voucher_index}">
                                                                    <a href="javascript:void(0);" id="${voucher.templateId}" class="pick-up-now" onclick="receiveTemplate(${voucher_index}, ${merchantId},${voucher.templateId})">
                                                                        立即领取
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </#if>
                                            </#if>
                                            <!--已抢光-->
                                            <#if (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>
                                                <#if voucher.provideTotalCount-voucher.receiveTotalCount <=0 >
                                                    <!--已抢光-->
                                                    <li class="quan-item fl">
                                                        <div class="quan quan-3">
                                                            <div class="quan-center-left fl">
                                                                <div class="left-row1">
                                                                    <span class="quan-quan quan-die">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                                </div>
                                                                <div class="left-row2"></div>
                                                                <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                            </div>
                                                            <div class="quan-center-right fl">
                                                                <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                                <div class="right-row3">立即领取</div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </#if>
                                            </#if>
                                        <#else>
                                            <!--已领取和已结束-->
                                            <#if ( voucher.state == 3 || voucher.state == 4 || (voucher.expireDate && voucher.expireDate?datetime <= .now?datetime)) >

                                            <#else>
                                                <!--已领取-->
                                                <li class="quan-item fl">
                                                    <div class="quan quan-2">
                                                        <div class="quan-center-left fl">
                                                            <div class="left-row1">
                                                                <span class="quan-quan quan-die">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                            </div>
                                                            <div class="left-row2"></div>
                                                            <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                        </div>
                                                        <div class="quan-center-right fl" >
                                                            <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                            <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                            <div class="right-row3" id="quanRight_${voucher_index}">
                                                                <!--已领取-->
                                                            <#--<div class="quan-right fl" id="quanRight_${voucher_index}">-->
                                                                    <#if voucher.validDate?? && .now?datetime lt voucher.validDate?datetime>
                                                                        <a href="/merchant/center/voucher/findAllVoucherInfoWithShop.htm"
                                                                           class="go-to-see">立即使用</a>
                                                                    <#else >

                                                                        <#if voucher.pcUrl?? && voucher.pcUrl?length gt 0>
                                                                            <a href="${voucher.pcUrl}" class="received">立即使用</a>
                                                                        <#elseif voucher.assignProductIds?? && voucher.assignProductIds?size gt 0>
                                                                            <a href="/voucher/centre/findVoucherSku.htm?voucherTemplateId=${voucher.templateId}&merchantId=${merchantId}&source=1" class="go-to-see">立即使用</a>
                                                                        <#else >
                                                                            <a href="/selfShop/center/shopSkuInfo.htm?orgId=&shopCode=${voucher.shopCode}" class="received">立即使用</a>
                                                                        </#if>
                                                                    </#if>
                                                            <#--</div>-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </#if>
                                        </#if>
                                    </#if>
                                    <!--通用券-->
                                    <#if voucher.voucherType==1>
                                        <#if voucher.isLq == 0>
                                            <!--未开始-->
                                            <#if (voucher.startTimeStamp > 0)>
                                            <li class="quan-item fl">
                                                <div class="quan quan-4">
                                                    <div class="quan-center-left fl">
                                                        <div class="left-row1">
                                                            <span class="quan-quan quan-tong">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                        </div>
                                                        <div class="left-row4">
                                                            <span class="left-row4-one fl">
                                                                距开抢还剩
                                                            </span>
                                                            <div class="left-row4-two fl" id="allTimer_${voucher_index}" index="${voucher_index}"
                                                                 startTimeStamp="${voucher.startTimeStamp}">
                                                                <div class="time fl" id="timer_h_${voucher_index}">11</div>
                                                                <div class="miao fl">:</div>
                                                                <div class="time fl" id="timer_m_${voucher_index}">11</div>
                                                                <div class="miao fl">:</div>
                                                                <div class="time fl" id="timer_s_${voucher_index}">11</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="quan-center-right fl">
                                                        <!--  如果是折扣券 -->
                                                        <#if voucher.activityState==5 || (voucher.isLq == 0 && voucher.endTimeStamp<=0) || (voucher.isLq == 1 && voucher.expireDate && voucher.expireDate?datetime<.now?datetime)>
                                                            <#if (voucher.voucherState==1)>
                                                                <div class="right-row1">
                                                                    <span class="zhekou" style="font-size:30px">${voucher.moneyInVoucher}</span>
                                                                    <span>折</span>
                                                                </div>
                                                            <#else>
                                                                <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                            </#if>
                                                            <div class="right-row2">
                                                                <#if (voucher.voucherState==1)>
                                                                    满${voucher.minMoneyToEnable}可用
                                                                <#else>
                                                                    <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                </#if>
                                                                <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                    ${voucher.maxMoneyInVoucherDesc}
                                                                </#if>
                                                            </div>
                                                        <#else>
                                                            <!--  如果是折扣券 -->
                                                            <#if (voucher.voucherState==1)>
                                                                <div class="right-row1">
                                                                    <span class="zhekou" style="font-size:30px">${voucher.moneyInVoucher}</span>
                                                                    <span>折</span>
                                                                </div>
                                                            <#else>
                                                                <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                            </#if>
                                                            <div class="right-row2">
                                                                <#if (voucher.voucherState==1)>
                                                                    满${voucher.minMoneyToEnable}可用
                                                                <#else>
                                                                    <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                </#if>
                                                                <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                    ${voucher.maxMoneyInVoucherDesc}
                                                                </#if>
                                                            </div>
                                                        </#if>
                                                        <div class="right-row3">
                                                            立即领取
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            </#if>
                                            <!--未领取-->
                                            <#if (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>
                                                <#if voucher.provideTotalCount-voucher.receiveTotalCount gt 0 >
                                                    <li class="quan-item fl">
                                                        <div class="quan quan-1">
                                                            <div class="quan-center-left fl">
                                                                <div class="left-row1">
                                                                    <span class="quan-quan quan-tong">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                                </div>
                                                                <div class="left-row2"></div>
                                                                <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                            </div>
                                                            <div class="quan-center-right fl">
                                                                <#if voucher.activityState==5 || (voucher.isLq == 0 && voucher.endTimeStamp<=0) || (voucher.isLq == 1 && voucher.expireDate && voucher.expireDate?datetime<.now?datetime)>
                                                                <!--  如果是折扣券 -->
                                                                <#--<#if (voucher.voucherState==1)>-->
                                                                <#--<div class="right-row1">-->
                                                                <#--<span style="font-size:16px">${voucher.moneyInVoucher}</span>-->
                                                                <#--<span style="font-size:16px">折</span>-->
                                                                <#--</div>-->
                                                                <#--<#else>-->
                                                                <#--<div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>-->
                                                                <#--</#if>-->
                                                                <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
                                                                <#--<div class="right-row2">每满${voucher.minMoneyToEnable}可用${voucher.maxMoneyInVoucherDesc}</div>-->
                                                                <#--<#else>-->
                                                                <#--<div class="right-row2">满${voucher.minMoneyToEnable}可用${voucher.moneyInVoucher}</div>-->
                                                                <#--</#if>-->
                                                                <#else>
                                                                    <!--  如果是折扣券 -->
                                                                    <#if (voucher.voucherState==1)>
                                                                        <div class="right-row1">
                                                                            <span class="zhekou" style="font-size:30px">${voucher.moneyInVoucher}</span>
                                                                            <span>折</span>
                                                                        </div>
                                                                    <#else>
                                                                        <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                    </#if>
                                                                    <div class="right-row2">
                                                                        <#if (voucher.voucherState==1)>
                                                                            满${voucher.minMoneyToEnable}可用
                                                                        <#else>
                                                                            <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                        </#if>
                                                                        <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                            ${voucher.maxMoneyInVoucherDesc}
                                                                        </#if>
                                                                    </div>
                                                                </#if>
                                                                <div class="right-row3" id="${voucher_index}"
                                                                     onclick="receiveTemplate(${voucher_index}, ${merchantId},${voucher.templateId})">
                                                                    <a href="javascript:void(0);" id="${voucher.templateId}" class="pick-up-now">立即领取</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </#if>
                                            </#if>

                                            <!--已抢光-->
                                            <#if (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>
                                                <#if voucher.provideTotalCount-voucher.receiveTotalCount <=0 >
                                                    <!--已抢光-->
                                                    <li class="quan-item fl">
                                                        <div class="quan quan-3">
                                                            <div class="quan-center-left fl">
                                                                <div class="left-row1">
                                                                    <span class="quan-quan quan-tong">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                                </div>
                                                                <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                            </div>
                                                            <div class="quan-center-right fl">
                                                                <!--  如果是折扣券 -->
                                                                <#if voucher.activityState==5 || (voucher.isLq == 0 && voucher.endTimeStamp<=0) || (voucher.isLq == 1 && voucher.expireDate && voucher.expireDate?datetime<.now?datetime)>
                                                                    <#if (voucher.voucherState==1)>
                                                                        <div class="right-row1">
                                                                            <span class="zhekou" style="font-size:30px">${voucher.moneyInVoucher}</span>
                                                                            <span>折</span>
                                                                        </div>
                                                                    <#else>
                                                                        <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                    </#if>
                                                                    <div class="right-row2">
                                                                        <#if (voucher.voucherState==1)>
                                                                            满${voucher.minMoneyToEnable}可用
                                                                        <#else>
                                                                            <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                        </#if>
                                                                        <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                            ${voucher.maxMoneyInVoucherDesc}
                                                                        </#if>
                                                                    </div>
                                                                <#else>
                                                                    <!--  如果是折扣券 -->
                                                                    <#if (voucher.voucherState==1)>
                                                                        <div class="right-row1">
                                                                            <span class="zhekou" style="font-size:30px">${voucher.moneyInVoucher}</span>
                                                                            <span>折</span>
                                                                        </div>
                                                                    <#else>
                                                                        <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                    </#if>
                                                                    <div class="right-row2">
                                                                        <#if (voucher.voucherState==1)>
                                                                            满${voucher.minMoneyToEnable}可用
                                                                        <#else>
                                                                            <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                        </#if>
                                                                        <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                            ${voucher.maxMoneyInVoucherDesc}
                                                                        </#if>
                                                                    </div>
                                                                </#if>
                                                                <div class="right-row3">
                                                                    <div class="right-row3">已抢光
                                                                        <!--已抢光-->

                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </#if>
                                            </#if>
                                        <#else>
                                            <!--已领取和已结束-->
                                            <#if ( voucher.state == 3 || voucher.state == 4 || (voucher.expireDate && voucher.expireDate?datetime <= .now?datetime)) >

                                            <#else>
                                                <!--已领取-->
                                                <li class="quan-item fl">
                                                    <div class="quan quan-2">
                                                        <div class="quan-center-left fl">
                                                            <div class="left-row1">
                                                                <span class="quan-quan quan-tong">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                            </div>
                                                            <div class="left-row2"></div>
                                                            <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                        </div>
                                                        <div class="quan-center-right fl">
                                                            <!--  如果是折扣券 -->
                                                            <#if voucher.activityState==5 || (voucher.isLq == 0 && voucher.endTimeStamp<=0) || (voucher.isLq == 1 && voucher.expireDate && voucher.expireDate?datetime<.now?datetime)>
                                                                <#if (voucher.voucherState==1)>
                                                                    <div class="right-row1">
                                                                        <span class="zhekou" style="font-size:30px">${voucher.moneyInVoucher}</span>
                                                                        <span>折</span>
                                                                    </div>
                                                                <#else>
                                                                    <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                </#if>
                                                                <div class="right-row2">
                                                                    <#if (voucher.voucherState==1)>
                                                                        满${voucher.minMoneyToEnable}可用
                                                                    <#else>
                                                                        <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                    </#if>
                                                                    <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                        ${voucher.maxMoneyInVoucherDesc}
                                                                    </#if>
                                                                </div>
                                                            <#else>
                                                                <!--  如果是折扣券 -->
                                                                <#if (voucher.voucherState==1)>
                                                                    <div class="right-row1">
                                                                        <span class="zhekou" style="font-size:30px">${voucher.moneyInVoucher}</span>
                                                                        <span>折</span>
                                                                    </div>
                                                                <#else>
                                                                    <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                </#if>
                                                                <div class="right-row2">
                                                                    <#if (voucher.voucherState==1)>
                                                                        满${voucher.minMoneyToEnable}可用
                                                                    <#else>
                                                                        <#if (voucher.voucherUsageWay ==1) >每满<#else>满</#if>${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}
                                                                    </#if>
                                                                    <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                        ${voucher.maxMoneyInVoucherDesc}
                                                                    </#if>
                                                                </div>
                                                            </#if>
                                                            <div class="right-row3" id="quanRight_${voucher_index}">
                                                                <!--已领取-->
                                                            <#--<div class="quan-right fl" >-->
                                                                    <#if voucher.validDate?? && .now?datetime lt voucher.validDate?datetime>
                                                                        <a href="/merchant/center/voucher/findAllVoucherInfoWithShop.htm"
                                                                           class="go-to-see">立即使用</a>
                                                                    <#else >
                                                                        <#if voucher.pcUrl?? && voucher.pcUrl?length gt 0>
                                                                            <a href="${voucher.pcUrl}" class="received"  class="pick-up-now">立即使用</a>
                                                                        <#else >
                                                                            <a href="/selfShop/center/shopSkuInfo.htm?orgId=&shopCode=${voucher.shopCode}" class="received"  class="pick-up-now">立即使用</a>
                                                                        </#if>
                                                                    </#if>
                                                            <#--</div>-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </#if>
                                        </#if>
                                    </#if>
                                    <!--新人券-->
                                    <#if voucher.voucherType==5>
                                        <#if voucher.isLq == 0>
                                            <!--未开始-->
                                            <#if (voucher.startTimeStamp > 0)>
                                                <li class="quan-item fl">
                                                    <div class="quan quan-4">
                                                        <div class="quan-center-left fl">
                                                            <div class="left-row1">
                                                                <span class="quan-quan quan-xin">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                            </div>
                                                            <div class="left-row4">
                                                                <span class="left-row4-one fl">
                                                                    距开抢还剩
                                                                </span>
                                                                <div class="left-row4-two fl" id="allTimer_${voucher_index}" index="${voucher_index}"
                                                                     startTimeStamp="${voucher.startTimeStamp}">
                                                                    <div class="time fl" id="timer_h_${voucher_index}">11</div>
                                                                    <div class="miao fl">:</div>
                                                                    <div class="time fl" id="timer_m_${voucher_index}">11</div>
                                                                    <div class="miao fl">:</div>
                                                                    <div class="time fl" id="timer_s_${voucher_index}">11</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="quan-center-right fl">
                                                            <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                            <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                                <div class="right-row3">立即领取</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </#if>
                                            <!--未领取-->
                                            <#if (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>
                                                <#if voucher.provideTotalCount-voucher.receiveTotalCount gt 0 >
                                                    <li class="quan-item fl">
                                                        <div class="quan quan-1">
                                                            <div class="quan-center-left fl">
                                                                <div class="left-row1">
                                                                    <span class="quan-quan quan-xin">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                                </div>
                                                                <div class="left-row2"></div>
                                                                <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                            </div>
                                                            <div class="quan-center-right fl">
                                                                <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                                <div class="right-row3" id="${voucher_index}" onclick="receiveTemplate(${voucher_index}, ${merchantId},${voucher.templateId})">
                                                                    <a href="javascript:void(0);" id="${voucher.templateId}" class="pick-up-now">立即领取</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </#if>
                                            </#if>

                                            <!--已抢光-->
                                            <#if (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>
                                                <#if voucher.provideTotalCount-voucher.receiveTotalCount <=0 >
                                                    <!--已抢光-->
                                                    <li class="quan-item fl">
                                                        <div class="quan quan-3">
                                                            <div class="quan-center-left fl">
                                                                <div class="left-row1">
                                                                    <span class="quan-quan quan-xin">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                                </div>
                                                                <div class="left-row2"></div>
                                                                <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                            </div>
                                                            <div class="quan-center-right fl">
                                                                <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                                <div class="right-row3">立即领取</div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </#if>
                                            </#if>
                                        <#else>
                                            <!--已领取和已结束-->
                                            <#if ( voucher.state == 3 || voucher.state == 4 || (voucher.expireDate && voucher.expireDate?datetime <= .now?datetime)) >
                                                <!--已结束-->
                                            <#else>
                                                <!--已领取-->
                                                <li class="quan-item fl">
                                                    <div class="quan quan-2">
                                                        <div class="quan-center-left fl">
                                                            <div class="left-row1">
                                                                <span class="quan-quan quan-xin">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                            </div>
                                                            <div class="left-row2"></div>
                                                            <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                        </div>
                                                        <div class="quan-center-right fl">
                                                            <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                            <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                            <div class="right-row3" id="quanRight_${voucher_index}">
                                                                <!--已领取-->
                                                                <#--<div class="quan-right fl" id="quanRight_${voucher_index}">-->
                                                                    <#if voucher.validDate?? && .now?datetime lt voucher.validDate?datetime>
                                                                        <a href="/merchant/center/voucher/findAllVoucherInfoWithShop.htm"
                                                                           class="go-to-see">立即使用</a>
                                                                    <#else >
                                                                        <#if voucher.voucherType?? && voucher.voucherType == 2>
                                                                            <a href="/merchant/center/voucher/findSkuInfo.htm?voucherTemplateId=${voucher.templateId}"
                                                                               class="received">立即使用</a>
                                                                        <#elseif voucher.pcUrl?? && voucher.pcUrl?length gt 0>
                                                                            <a href="${voucher.pcUrl}" class="received">立即使用</a>
                                                                        <#else >
                                                                            <a href="/selfShop/center/shopSkuInfo.htm?orgId=&shopCode=${voucher.shopCode}" class="received">立即使用</a>
                                                                        </#if>
                                                                    </#if>
                                                                <#--</div>-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </#if>
                                        </#if>
                                    </#if>
                                    <!--平台券-->
                                    <#if  voucher.voucherType==8 && !(voucher.voucherSkuImages ??)>
                                        <#if voucher.isLq == 0>
                                            <!--未开始-->
                                            <#if (voucher.startTimeStamp > 0)>
                                                <li class="quan-item fl">
                                                    <div class="quan quan-4">
                                                        <div class="quan-center-left fl">
                                                            <div class="left-row1">
                                                                <span class="quan-quan quan-platform">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                            </div>
                                                            <div class="left-row4">
                                                                <span class="left-row4-one fl">
                                                                    距开抢还剩
                                                                </span>
                                                                <div class="left-row4-two fl" id="allTimer_${voucher_index}" index="${voucher_index}"
                                                                     startTimeStamp="${voucher.startTimeStamp}">
                                                                    <div class="time fl" id="timer_h_${voucher_index}">11</div>
                                                                    <div class="miao fl">:</div>
                                                                    <div class="time fl" id="timer_m_${voucher_index}">11</div>
                                                                    <div class="miao fl">:</div>
                                                                    <div class="time fl" id="timer_s_${voucher_index}">11</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="quan-center-right fl">
                                                            <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                            <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                            <div class="right-row3">立即领取</div>
                                                        </div>
                                                    </div>
                                                    </div>
                                                </li>
                                            </#if>
                                            <!--未领取-->
                                            <#if (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>
                                                <#if voucher.provideTotalCount-voucher.receiveTotalCount gt 0 >
                                                    <li class="quan-item fl">
                                                        <div class="quan quan-1">
                                                            <div class="quan-center-left fl">
                                                                <div class="left-row1">
                                                                    <span class="quan-quan quan-platform">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                                </div>
                                                                <div class="left-row2"></div>
                                                                <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                            </div>
                                                            <div class="quan-center-right fl">
                                                                <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                                <div class="right-row3" id="${voucher_index}" onclick="receiveTemplate(${voucher_index}, ${merchantId},${voucher.templateId})">
                                                                    <a href="javascript:void(0);" id="${voucher.templateId}" class="pick-up-now">立即领取</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </#if>
                                            </#if>

                                            <!--已抢光-->
                                            <#if (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>
                                                <#if voucher.provideTotalCount-voucher.receiveTotalCount <=0 >
                                                    <!--已抢光-->
                                                    <li class="quan-item fl">
                                                        <div class="quan quan-3">
                                                            <div class="quan-center-left fl">
                                                                <div class="left-row1">
                                                                    <span class="quan-quan quan-platform">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                                </div>
                                                                <div class="left-row2"></div>
                                                                <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                            </div>
                                                            <div class="quan-center-right fl">
                                                                <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                                <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                                <div class="right-row3">立即领取</div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </#if>
                                            </#if>
                                        <#else>
                                            <!--已领取和已结束-->
                                            <#if ( voucher.state == 3 || voucher.state == 4 || (voucher.expireDate && voucher.expireDate?datetime <= .now?datetime)) >
                                                <!--已结束-->
                                            <#else>
                                                <!--已领取-->
                                                <li class="quan-item fl">
                                                    <div class="quan quan-2">
                                                        <div class="quan-center-left fl">
                                                            <div class="left-row1">
                                                                <span class="quan-quan quan-platform">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>
                                                            </div>
                                                            <div class="left-row2"></div>
                                                            <div class="left-row3" title="${voucher.voucherInstructions}">${voucher.voucherInstructions}</div>
                                                        </div>
                                                        <div class="quan-center-right fl">
                                                            <div class="right-row1">¥<span style="font-size:30px;">${voucher.moneyInVoucher}</span></div>
                                                            <div class="right-row2">满${voucher.minMoneyToEnable}减${voucher.moneyInVoucher}</div>
                                                            <div class="right-row3" id="quanRight_${voucher_index}">
                                                                <!--已领取-->
                                                            <#--<div class="quan-right fl" id="quanRight_${voucher_index}">-->
                                                                    <#if voucher.validDate?? && .now?datetime lt voucher.validDate?datetime>
                                                                        <a href="/merchant/center/voucher/findAllVoucherInfoWithShop.htm"
                                                                           class="go-to-see">立即使用</a>
                                                                    <#else >
                                                                        <#if voucher.voucherType?? && voucher.voucherType == 2>
                                                                            <a href="/merchant/center/voucher/findSkuInfo.htm?voucherTemplateId=${voucher.templateId}"
                                                                               class="received">立即使用</a>
                                                                        <#elseif voucher.pcUrl?? && voucher.pcUrl?length gt 0>
                                                                            <a href="${voucher.pcUrl}" class="received">立即使用</a>
                                                                        <#else >
                                                                            <a href="/selfShop/center/shopSkuInfo.htm?orgId=&shopCode=${voucher.shopCode}" class="received">立即使用</a>
                                                                        </#if>
                                                                    </#if>
                                                            <#--</div>-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </#if>
                                        </#if>
                                    </#if>
                                </#list>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!--领券中心结束-->
<#--	        <div class="main-in">-->
<#--	            <ul>-->
<#--	               <#list voucherList as voucher>-->
<#--	                    <li>-->
<#--	                        <div class="quan">-->
<#--	                            <div class="quan<#if voucher.voucherType!=2>1</#if>-left fl">-->
<#--	                                <#if voucher.skuFirstImg??>-->
<#--	                                    <img src="${productImageUrl}/ybm/product/min/${voucher.skuFirstImg}" alt="">-->
<#--	                                <#else >-->
<#--	                                    <img src="/static/images/events/20180605-lt-lqzx/beijing.png" alt="">-->
<#--								        <span>${voucher.voucherTypeText}</span>-->
<#--	                                </#if>-->
<#--	                            </div>-->
<#--	                            <div class="quan-center fl">-->
<#--	                                <div class="center-row1">-->
<#--	                                    <span class="quan-quan">${voucher.voucherTypeText}</span><span class="quan-title" title="${voucher.voucherTitle}"><nobr>${voucher.voucherTitle}</nobr></span>-->
<#--	                                </div>-->

<#--                                    <#if voucher.voucherType==2>-->
<#--                                        <div class="shangpin-quan-content">-->
<#--                                            <div class="quan-center-left fl">-->
<#--                                                <div class="left-row2">2019/11/11-2019/12/11</div>-->
<#--                                                <div class="left-row3">-->
<#--                                                    <#list voucher.voucherSkuImages as skuImage>-->
<#--                                                        <p class="shangpin-img fl">-->
<#--                                                            <img src="${skuImage.imageUrl}" height="73" width="79" alt="">-->
<#--                                                        </p>-->
<#--                                                    </#list>-->
<#--                                                </div>-->
<#--                                            </div>-->
<#--                                            <div class="quan-center-right fl">-->
<#--                                                <div class="right-row1" style="margin-top: 17px;">¥<span style="font-size:30px;">20</span></div>-->
<#--                                                <div class="right-row2">满200减20</div>-->
<#--                                                <div class="right-row3">立即领取</div>-->
<#--                                            </div>-->
<#--                                        </div>-->
<#--                                    </#if>-->
<#--                                    <div class="shangpin-quan-info">-->
<#--                                        ${voucher.voucherInstructions}-->
<#--                                    </div>-->

<#--	                                <div class="center-row2 clearfix">-->
<#--                                        <!-- 已抢光或已结束&ndash;&gt;-->
<#--                                        <#if voucher.activityState==5 || (voucher.isLq == 0 && voucher.endTimeStamp<=0) || (voucher.isLq == 1 && voucher.expireDate && voucher.expireDate?datetime<.now?datetime)>-->
<#--											&lt;#&ndash;  如果是折扣券 &ndash;&gt;-->
<#--											<#if (voucher.voucherState==1)>-->
<#--										       <div class="price fl ">-->
<#--												   <span style="font-size:16px">${voucher.moneyInVoucher}</span>-->
<#--												   <span style="font-size:16px">折</span>-->
<#--											   </div>-->
<#--											<#else>-->
<#--												<div class="price1 fl ">￥<span>${voucher.moneyInVoucher}</span></div>-->
<#--											</#if>-->
<#--                                            <#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
<#--                                                <div class="keyong1 fl"><span><em>每满${voucher.minMoneyToEnable}可用&nbsp;${voucher.maxMoneyInVoucherDesc }</em></span></div>-->
<#--                                            <#else>-->
<#--                                                <div class="keyong1 fl"><span><em>满${voucher.minMoneyToEnable}可用</em></span></div>-->
<#--                                            </#if>-->
<#--                                        <#else>-->
<#--										    &lt;#&ndash;  如果是折扣券 &ndash;&gt;-->
<#--                                            <#if (voucher.voucherState==1)>-->
<#--										       <div class="price fl ">-->
<#--												   <span style="font-size:16px">${voucher.moneyInVoucher}</span>-->
<#--												   <span style="font-size:16px">折</span>-->
<#--											   </div>-->
<#--											<#else>-->
<#--											   <div class="price fl ">￥<span>${voucher.moneyInVoucher}</span></div>-->
<#--											</#if>-->
<#--                                            <#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
<#--                                                <div class="keyong fl"><span><em>每满${voucher.minMoneyToEnable}可用&nbsp;${voucher.maxMoneyInVoucherDesc }</em></span></div>-->
<#--                                            <#else>-->
<#--                                                <div class="keyong fl"><span><em>满${voucher.minMoneyToEnable}可用</em></span></div>-->
<#--                                            </#if>-->
<#--                                        </#if>-->
<#--	                                </div>-->
<#--	                            </div>-->
<#--	                        <#if voucher.isLq == 0>-->
<#--	                            <#if (voucher.startTimeStamp > 0)>-->
<#--	                                <!--未开始&ndash;&gt;-->
<#--	                                <div class="quan-right fl" id="${voucher_index}" merchantId="${merchantId}"-->
<#--                                         voucherId="${voucher.templateId}"-->
<#--                                         receiveTotalCount="${voucher.receiveTotalCount}"-->
<#--                                         totalLimitQty="${voucher.provideTotalCount}">-->
<#--	                                    <div class="right-row1">-->
<#--	                                        距开抢还剩-->
<#--	                                    </div>-->
<#--	                                    <div class="right-row3" id="allTimer_${voucher_index}" index="${voucher_index}"-->
<#--	                                         startTimeStamp="${voucher.startTimeStamp}">-->
<#--	                                        <div class="time fl" id="timer_h_${voucher_index}"></div>-->
<#--	                                        <div class="miao fl">:</div>-->
<#--	                                        <div class="time fl" id="timer_m_${voucher_index}"></div>-->
<#--	                                        <div class="miao fl">:</div>-->
<#--	                                        <div class="time fl" id="timer_s_${voucher_index}"></div>-->
<#--	                                    </div>-->
<#--	                                </div>-->
<#--	                            <#elseif (voucher.startTimeStamp<=0 && voucher.endTimeStamp>0)>-->
<#--                                    <#if voucher.provideTotalCount-voucher.receiveTotalCount <=0 >-->
<#--	                                    <!--已抢光&ndash;&gt;-->
<#--	                                    <div class="quan-right fl">-->
<#--	                                        <div class="has-been-looted ">-->
<#--	-->
<#--	                                        </div>-->
<#--	                                    </div>-->
<#--	                                <#else>-->
<#--	                                    <!--未领取&ndash;&gt;-->
<#--	                                    <div class="quan-right fl" id="${voucher_index}"-->
<#--                                             onclick="receiveTemplate(${voucher_index}, ${merchantId},${voucher.templateId})">-->
<#--                                            <canvas width="62" height="51" class="canvas${voucher.templateId}"-->
<#--                                                    receiveTotalCount="${voucher.receiveTotalCount}"-->
<#--                                                    totalLimitQty="${voucher.provideTotalCount}"></canvas>-->
<#--                                            <a href="javascript:void(0);" id="${voucher.templateId}"-->
<#--                                               class="pick-up-now">-->
<#--	                                            立即领取-->
<#--	                                        </a>-->
<#--	                                    </div>-->
<#--	                                </#if>-->
<#--	                            <#elseif (voucher.startTimeStamp<=0 && voucher.endTimeStamp<=0)>-->
<#--	                                <!--已结束&ndash;&gt;-->
<#--	                                <div class="quan-right fl">-->
<#--	                                    <div class="over">-->
<#--	-->
<#--	                                    </div>-->
<#--	                                </div>-->
<#--	                            </#if>-->
<#--	                        <#else>-->
<#--                                <#if ( voucher.state == 3 || voucher.state == 4 || (voucher.expireDate && voucher.expireDate?datetime <= .now?datetime)) >-->
<#--	                                <!--已结束&ndash;&gt;-->
<#--	                                <div class="quan-right fl">-->
<#--	                                    <div class="over">-->
<#--	-->
<#--	                                    </div>-->
<#--	                                </div>-->
<#--	                            <#else>-->
<#--	                                <!--已领取&ndash;&gt;-->
<#--	                                <div class="quan-right fl" id="quanRight_${voucher_index}">-->
<#--	                                    <#if voucher.validDate?? && .now?datetime lt voucher.validDate?datetime>-->
<#--	                                        <a href="/merchant/center/voucher/findAllVoucherInfo.htm"-->
<#--	                                           class="go-to-see"></a>-->
<#--	                                    <#else >-->
<#--                                            <#if voucher.voucherType?? && voucher.voucherType == 2>-->
<#--                                                <a href="/merchant/center/voucher/findSkuInfo.htm?voucherTemplateId=${voucher.templateId}"-->
<#--                                                   class="received"></a>-->
<#--                                            <#elseif voucher.pcUrl?? && voucher.pcUrl?length gt 0>-->
<#--                                                <a href="${voucher.pcUrl}" class="received"></a>-->
<#--                                            <#else >-->
<#--                                                <a href="/search/skuInfoByCategory.htm?all=all" class="received"></a>-->
<#--                                            </#if>-->
<#--	                                    </#if>-->
<#--	                                </div>-->
<#--	                            </#if>-->
<#--	                        </#if>-->
<#--	                        </div>-->
<#--	                    </li>-->
<#--	               </#list>-->
<#--	                </li>-->
<#--	            </ul>-->
<#--	        </div>-->
        <#else>
	        <div class="no-quan">
	    		<img src="/static/images/events/20180605-lt-lqzx/Slice 4.png" alt="">
	    		<p>暂无可领取的优惠券哦...</p>
	    	</div>
        </#if>
    </div>
    <!--主体部分结束-->

    <!--底部导航区域开始-->
    <div class="footer" id="footer">
    <#include "/common/footer.ftl" />
    </div>
    <!--底部导航区域结束-->
</div>

</body>
<script type="text/javascript" src="/static/js/jquery-1.11.3.min.js"></script>
<script type="text/javascript" src="/static/js/sui.min.js"></script>
<script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/plugins/layer/layer.js"></script>
<script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>

<#--<script type="text/javascript" src="/static/js/activityEvent/events-20170401.js?t=${t_v}"></script>-->
<script type="text/javascript">
    // 領取優惠券
    function receiveTemplate(nodeId, merchantId, receiveTemplateId) {
        if (merchantId && merchantId > 0) {
            $.ajax({
                url:  "/merchant/center/voucher/receiveVoucher",
                type: "POST",
                dataType: "json",
                data: {
                    merchantId: merchantId,
                    voucherTemplateId: receiveTemplateId
                },
                success: function (result) {
                    if (result.status == "success") {
                        var noReceiveCount = result.noReceiveCount;
                        $("#" + nodeId).removeAttr("onclick");
                        var voucherType = result.voucherType;
                        var validDate = result.validDate;
                        var expireDate = result.expireDate;
                        var jumpUrl = result.jumpUrl;
                        var now = new Date();
                        // if (now >= validDate) {
                        //     //去使用
                        //     if (voucherType == 2) {
                        //         //商品券
                        //         $("#" + nodeId).html('<a href="/voucher/centre/findVoucherSku.htm?voucherTemplateId=' + receiveTemplateId + '&merchantId='+merchantId+'" class="received"></a>');
                        //     } else if (null != jumpUrl && jumpUrl != "") {
                        //         $("#" + nodeId).html('<a href="' + jumpUrl + '" class="received"></a>');
                        //     } else {
                        //         $("#" + nodeId).html('<a href="/search/skuInfoByCategory.htm?all=all" class="received"></a>');
                        //     }
                        // } else {
                        //     //去查看
                        //     $("#" + nodeId).html('<a href="/merchant/center/voucher/findAllVoucherInfo.htm" class="go-to-see"></a>');
                        // }
                        window.location.reload();
                    } else {
                        $.alert({
                            title: '提示',
                            body: result.errorMsg,
                            okHidden: function (e) {
                                location.reload();
                            }
                        });
                    }
                },
                error: function () {
                    $.alert({
                        title: '提示',
                        body: '因为某些原因导致优惠券领取异常哟!'
                    });
                }
            });
        } else {
            $.alert({
                title: '提示',
                body: '您还没有登录，请先登录!',
                okHidden: function (e) {
                    window.location.href = "/login/login.htm?redirectUrl=/";
                }
            });
        }
    }

    $(function () {
        /*加载头部*/
        // $("#headerBox").load("header.html");

        /*加载底部*/
        // $(".footer").load("footer.html");

        /*回到顶部*/
        $('.toTop').click(function () {
            $('html,body').animate({
                scrollTop: '0px'
            }, 'slow');
        });

        // $("[id^=quanRight_]").each(function (){
        //     $(this).css("margin-top","-3px");
        // });

        $('canvas').each(function () {

            var receiveTotalCount = $(this).attr("receiveTotalCount");
            var totalLimitQty = $(this).attr("totalLimitQty");

            new Drawpiechart("." + $(this).attr("class"), receiveTotalCount, totalLimitQty);
            $(this).parent().css("margin-top","-3px");
        });
        //折扣券展示
        $(".zhekou").each(function(){
            var val = $(this).html();
            var arr;
            if(val.indexOf(".")>0){
                arr = val.split(".");
                $(this).html("<span>"+arr[0]+"</span><span style=\"font-size:16px;\">."+arr[1]+"</span>")
            }
        })
    });



    /*
	*画进度条
	*/
    function Drawpiechart(ele, provideNum, totalNum) {
        this.$ele = ele,
                this.oC,//画布
                this.gd,//画笔
                this.crlwid = 30,//饼图圆心的X坐标
                this.crlhgt = 30,//饼图圆心的Y坐标
                this.crlrad = 25,//半径
                this.provideNum = provideNum == null || provideNum == "" ? 0 : provideNum,
                this.totalNum = totalNum,//总的销售量
                this.progress,//进度
                this.progressNum,//购买百分比
                this.progressBar = 140,//表示圆弧从140度开始画
                this.init();
    }

    Drawpiechart.prototype.init = function () {
        this.oC = document.querySelector(this.$ele);//获取画布
        this.gd = this.oC.getContext('2d'); //画笔
        this.gd.clearRect(0, 0, this.oC.width, this.oC.height);//清除画布;
        this.progress = this.provideNum / this.totalNum * 260 + 140;//将当前的进度计算成弧度值，260表示半圆总共是260度，140是进度条的起始位置
        this.progressNum = parseInt(this.provideNum / this.totalNum * 100)
        this.drawcircle();
    }
    Drawpiechart.prototype.drawcircle = function () {
        var timer = setInterval(function () {
            this.gd.clearRect(0, 0, this.oC.width, this.oC.height);
            this.gd.beginPath();
            this.gd.strokeStyle = '#ffb8b8';
            this.gd.lineWidth = 5;//画笔的粗细
            this.gd.lineCap = "round";
            this.gd.textAlign = "center";
            this.gd.textBaseline = "middle";
            this.gd.font = "12px Microsoft YaHei UI";
            this.gd.arc(this.crlwid, this.crlhgt, this.crlrad, this.d2a(140), this.d2a(400));//画下面的半圆，从140度，到400度
            this.gd.stroke();

            this.gd.beginPath();
            this.gd.strokeStyle = '#f04134';
            if (this.progressBar >= this.progress) {
                clearInterval(timer);
                this.progressBar = this.progress;
            }
            this.gd.arc(this.crlwid, this.crlhgt, this.crlrad, this.d2a(140), this.d2a(this.progressBar));
            this.gd.stroke();
            this.gd.fillStyle = "#F91440";//文字的颜色
            this.gd.fillText("已抢", this.crlwid, this.crlhgt - 6);
            this.gd.fillText(this.progressNum + "%", this.crlwid, this.crlhgt + 8);

            this.progressBar += 3;
        }.bind(this), 16)
    }
    Drawpiechart.prototype.d2a = function (n) {//计算弧度
        return n * Math.PI / 180;
    }
</script>

</html>
