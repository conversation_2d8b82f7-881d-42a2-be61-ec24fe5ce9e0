package com.xyy.ec.pc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.model.erp.ErpSignaturesResponse;
import com.xyy.ec.pc.model.erp.SignaturesInfo;
import com.xyy.ec.pc.service.DownloadProductTaskFactory;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.HttpClientUtil;
import com.xyy.framework.redis.autoconfigure.core.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping("/shop/product")
public class ProductController extends BaseController {

    @Autowired
    private DownloadProductTaskFactory downloadProductTaskFactory;
    @Value("${erp_host}")
    private String ERP_HOST;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Autowired
    private RedisClient redisClient;
    @Value("${ProductController.downloadSignature.repeat.time:3}")
    private Integer repeatTime;

    /**
     * 下载商品电子资质
     * @return
     */
    @ResponseBody
    @GetMapping("/downloadSignature")
    public Object downloadProductSignature(@RequestParam("code")String code, @RequestParam("branchCode") String branchCode) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                // 未登录 提示鉴权异常
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
            }
            Long merchantId = merchant.getMerchantId();
            String key = "REPEAT:ProductController_downloadProductSignature:"+merchantId;
            String setNxResultStr = redisClient.set(key, String.valueOf(System.currentTimeMillis()), "NX", "EX", repeatTime);
            if (StringUtils.isBlank(setNxResultStr) || !"OK".equals(setNxResultStr)) {
                return this.addError("请勿重复请求");
            }
            //调用ERP的http接口 https://erp.api.test.ybm100.com/api/ec/product/productFirstEnclosure
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("oldProductCodeList", Collections.singletonList(code));
            //共仓逻辑，共仓域修改为主仓域编码
            paramsMap.put("ecOrgCode", branchCode);
            log.info("调用ERP电子签章入参：{}", JSON.toJSONString(paramsMap));
            String erpResult = HttpClientUtil.doPost(ERP_HOST + "/api/ec/product/productFirstEnclosure", JSONObject.toJSONString(paramsMap),null);
            log.info("调用ERP电子签章出参：{}", erpResult);
            //erp返回值转换
            ErpSignaturesResponse erpSignaturesResponse = JSON.toJavaObject(JSON.parseObject(erpResult), ErpSignaturesResponse.class);
            if(erpSignaturesResponse == null || erpSignaturesResponse.getCode() == 1 || CollectionUtils.isEmpty(erpSignaturesResponse.getResult())){
                return this.addError("暂无数据");
            }
            List<SignaturesInfo> signaturesInfo = erpSignaturesResponse.getResult().stream().filter(e-> StringUtils.isNotBlank(e.getSignatureImageUrl())).collect(Collectors.toList());
            if(CollectionUtil.isEmpty(signaturesInfo)){
                return this.addError("暂无数据");
            }
            DownloadProductTaskFactory.DownloadProductSignatureTask task = downloadProductTaskFactory.newTask(signaturesInfo);
            CompletableFuture.runAsync(task);
            return this.addDataResult("taskId", task.getTaskId());
        } catch (Exception e) {
            log.error("OrderController.downLoadProductSignature error", e);
            return this.addError(e.getMessage());
        }
    }

    /**
     * 查询资质下载状态
     * @param taskId
     * @return
     */
    @ResponseBody
    @GetMapping("/downloadSignatureResult")
    public Object queryDownloadProductSignatureResult(@RequestParam("taskId") String taskId) {
        try {
            DownloadProductTaskFactory.TaskResult taskResult = downloadProductTaskFactory.getTaskResult(taskId);
            return this.addDataResult("taskResult", taskResult);
        } catch (Exception e) {
            log.error("OrderController.queryDownloadProductSignatureResult");
            return this.addError(e.getMessage());
        }
    }
}
