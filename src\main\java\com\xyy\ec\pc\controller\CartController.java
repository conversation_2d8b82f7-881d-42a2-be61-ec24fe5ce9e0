package com.xyy.ec.pc.controller;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.xyy.cat.util.CatUtil;
import com.xyy.ec.marketing.client.constants.CouponLevelEnum;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.dto.ShoppingCartBusinessDto;
import com.xyy.ec.order.core.dto.cart.Combined;
import com.xyy.ec.order.dto.cart.*;
import com.xyy.ec.order.dto.combined.CombinedRespDto;
import com.xyy.ec.order.dto.voucher.CartVoucherDto;
import com.xyy.ec.order.enums.BizSourceEnum;
import com.xyy.ec.order.enums.PlatformEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.rpc.OrderServerRpcService;
import com.xyy.ec.pc.service.CartService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: zhaoyun
 * @Date: 2018/8/27 16:32
 * @Description: 购物车控制器
 */
@Controller
@RequestMapping("/merchant/center/cart")
public class CartController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(CartController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Autowired
    private CartService cartService;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private OrderServerRpcService orderServerRpcService;

    @RequestMapping("/index.htm")
    public ModelAndView index(ModelMap modelMap, HttpServletRequest request){
        try{
            Transaction t1 = CatUtil.initTransaction("cartIndex", "cartIndex");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            modelMap.put("merchantId", merchant.getId());
            String errorMsg = request.getParameter("errorMsg");
            logger.error("cart errorMsg {},{}",merchant.getId(),errorMsg);
            if (StringUtil.isNotEmpty(errorMsg)) {
                modelMap.put("errorMsg", errorMsg);
            }
            CatUtil.successCat(t1);
        }catch (Exception e){
            logger.error("cartController index error",e);
        }

        return new ModelAndView("/cart/index.ftl", modelMap);

    }
    /**
     * 组合信息查询
     *
     * @param combined
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/combinedInfoQuery")
    public Object combinedInfoQuery(Combined combined) {
        try {
            combined.setAppVersion(1);
            combined.setTerminalType(4);
            CombinedRespDto combinedRespDto = cartService.getCombined(combined);
            return this.addResult("data", combinedRespDto);
        } catch (Exception e) {
            logger.error("combinedInfoQuery，combined：{}", JSON.toJSONString(combined), e);
            return addError("服务请求超时，请稍后重试");
        }
    }
    /**
     * 打开购物车
     * @param cart
     * @return
     */
    @RequestMapping("/list.htm")
    public ModelAndView list(ShoppingCartBusinessDto cart){
        try{
            Transaction t1 = CatUtil.initTransaction("cartlist", "cartlist");
            //终端机的类型
            cart.setTerminalType(4);
            cart.setAppVersion(1);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setMerchantId(merchant.getId());
            Map<String, Object> result = new HashMap<>();
//            if (orderServerRpcService.refactorGray(merchant.getRegisterCode(), OrderAbilityEnum.GET_CART.getKey(), merchant.getId())) {
                GetCartDto getCartDto = new GetCartDto();
                getCartDto.setBizSource(BizSourceEnum.B2B.getKey());
                getCartDto.setTerminalType(PlatformEnum.PC.getValue());
                getCartDto.setMerchantId(merchant.getId());
                getCartDto.setAccountRole(merchant.getAccountRole());
                getCartDto.setAccountId(merchant.getAccountId());
                getCartDto.setUseRedPacket(true);
                if (merchant.getAccountRole().equals(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId())) {
                    getCartDto.setUseRedPacket(false);
                    //TODO 购物车优惠券咋用的
                    getCartDto.setUseOptimal(false);
                }
                com.xyy.ec.order.dto.cart.CartBusinessDto cartBusinessDto = orderServerRpcService.getCart(getCartDto);

//                orderServerRpcService.checkGetCart(cart,cartBusinessDto,merchant);
                result.put("cartInfo", cartBusinessDto);
            List<FreightInfo> unsatisfiedStartPriceList = cartBusinessDto.getUnsatisfiedStartPriceList();
            if (CollectionUtils.isNotEmpty(unsatisfiedStartPriceList)) {
                String notSubmitOrderOrgIds = unsatisfiedStartPriceList.stream().map(p -> p.getOrgId())
                        .filter(x -> StringUtils.isNotBlank(x))
                        .collect(Collectors.joining(","));
                result.put("notSubmitOrderOrgIds", notSubmitOrderOrgIds);
            }

//            } else {
//                CartBusinessDto Mycart = cartService.getCartNew(cart, merchant.getRegisterCode());
//                try {
//                    String oldJson = JSON.toJSONString(Mycart);
//                    GetCartDto getCartDto = new GetCartDto();
//                    getCartDto.setBizSource(BizSourceEnum.B2B.getKey());
//                    getCartDto.setTerminalType(PlatformEnum.PC.getValue());
//                    getCartDto.setMerchantId(cart.getMerchantId());
//                    orderServerRpcService.checkGetCart(getCartDto,oldJson);
//                }catch (Exception e){
//                    logger.error("checkGetCart error. param:{}",cart,e);
//                }
//                result.put("cartInfo", Mycart);
//            }
            CatUtil.successCat(t1);
            return new ModelAndView("/cart/list.ftl",result);
        }catch (Exception e){
            logger.error("cartController list error",e);
            return new ModelAndView("/cart/list.ftl");
        }
    }

    @RequestMapping("/changeCart.json")
    @ResponseBody
    public Object changeCart(ShoppingCartBusinessDto cart) {
        try{
//            Transaction t1 = CatUtil.initTransaction("cartchangeCart", "cartchangeCart");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
//            ServiceResponse<Boolean> response= cartService.checkMerchantQualifications(cart.getPackageId(),cart.getSkuId(),merchant.getId(),merchant.getRegisterCode());
//            CatUtil.successCat(t1);
//            if(!response.getResult())
//            return this.addError(response.getMsg());
            cart.setMerchantId(merchant.getId());
            //终端机类型
            cart.setTerminalType(4);
            cart.setAppVersion(1);
            try {
                Transaction t2 = CatUtil.initTransaction("cartchangeCart", "cartchangeCart");
                String realIP = IPUtils.getClientIP(request);
                cart.setRealIP(realIP);
                logger.info("当前用户请求的IP地址为:"+realIP);

                Map<String, Object> dataMap = new HashMap<>();

//                if (orderServerRpcService.refactorGray(merchant.getRegisterCode(), OrderAbilityEnum.CHANGE_CART.getKey(), merchant.getId())) {
                ChangeCartDto changeCartDto = new ChangeCartDto();
                BeanUtils.copyProperties(cart, changeCartDto);
                if (Objects.isNull(changeCartDto.getBizSource())) {
                    changeCartDto.setBizSource(BizSourceEnum.B2B.getKey());
                }

                changeCartDto.setTerminalType(PlatformEnum.PC.getValue());
                changeCartDto.setQuantity(cart.getAmount());
                changeCartDto.setAccountId(merchant.getAccountId());
                changeCartDto.setAccountRole(merchant.getAccountRole());
                dataMap = orderServerRpcService.changeCart(changeCartDto);
//                    orderServerRpcService.checkChangeCart(cart, dataMap, merchant);
                CatUtil.successCat(t2);

                return this.addResult("data", dataMap);

//                } else {
//                    dataMap = cartService.changeCart(cart);
//                    try {
//                        String oldJson = JSON.toJSONString(dataMap);
//                        ChangeCartDto changeCartDto = new ChangeCartDto();
//                        BeanUtils.copyProperties(cart, changeCartDto);
//                        changeCartDto.setBizSource(BizSourceEnum.B2B.getKey());
//                        changeCartDto.setTerminalType(PlatformEnum.PC.getValue());
//                        changeCartDto.setQuantity(cart.getAmount());
//                        orderServerRpcService.checkChangeCart(changeCartDto, oldJson);
//                    }catch (Exception e){
//                        logger.error("checkChangeCart error. param:{}",cart,e);
//                    }
//                }

//                if (dataMap == null) {
//                    return this.addError("修改购物车失败");
//                }
//                return this.addResult("data", dataMap);
            } catch (IllegalArgumentException e) {
                return this.addError(e.getMessage());
            } catch (RuntimeException e) {
                logger.error("购物车改变数量异常",e);
                return this.addError(e.getMessage());
            }catch (Exception e) {
                logger.error("购物车改变数量异常",e);
                return this.addError("购物车改变数量异常");
            }

        }catch (Exception e){
            logger.error("changeCart error",e);
            return this.addError("系统打盹，请稍后再试");
        }
    }
    @RequestMapping("/group/changeCart")
    @ResponseBody
    public Object changeCartForGroup(ChangeCartDto cart,HttpServletRequest req) {
        logger.info("changeCartForGroup args ChangeCartDto:{}", cart);

        if (cart.getSkuId() == null || cart.getMerchantId() == null) {
            return addError("商品ID、客户ID不能为空");
        }
        if (cart.getQuantity() == null || cart.getQuantity() <= 0) {
            return addError("数量必须大于0");
        }
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setAccountId(merchant.getAccountId());
            cart.setAccountRole(merchant.getAccountRole());
            cart.setTerminalType(4);
            String realIp = IPUtils.getClientIP(req);
            cart.setRealIp(realIp);
            Map<String, Object> dataMap = cartService.changeCartForPromotion(cart);
            if (CollectionUtil.isEmpty(dataMap)) {
                return addError("修改拼团数量失败");
            }
            return this.addResult("data", dataMap);
        } catch (IllegalArgumentException e) {
            return addError(e.getMessage());
        } catch (Exception e) {
            logger.error("修改拼团数量异常", e);
            return addError("修改拼团数量异常");
        }
    }
    /**
     * 批量匹价加购
     * @param cart
     * @return
     */
    @RequestMapping("/matchPrice/changeCartBatch")
    @ResponseBody
    public Object matchPriceChangeCartBatch(@RequestBody ChangeCartDto cart) {
        try{
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setMerchantId(merchant.getId());
            //终端机类型
            cart.setTerminalType(4);
            cart.setAppVersion(1);
            try {
                Transaction t2 = CatUtil.initTransaction("cartchangeCart", "cartchangeCart");
                String realIP = IPUtils.getClientIP(request);
                cart.setRealIp(realIP);

                if (Objects.isNull(cart.getBizSource())) {
                    cart.setBizSource(BizSourceEnum.B2B.getKey());
                }

                cart.setTerminalType(PlatformEnum.PC.getValue());
                cart.setAccountId(merchant.getAccountId());
                cart.setAccountRole(merchant.getAccountRole());
                Map<String, Object> dataMap = orderServerRpcService.changeCart(cart);
                CatUtil.successCat(t2);

                return this.addResult("data", dataMap);

            } catch (IllegalArgumentException e) {
                return this.addError(e.getMessage());
            } catch (RuntimeException e) {
                logger.error("购物车改变数量异常",e);
                return this.addError(e.getMessage());
            }catch (Exception e) {
                logger.error("购物车改变数量异常",e);
                return this.addError("购物车改变数量异常");
            }

        }catch (Exception e){
            logger.error("changeCart error",e);
            return this.addError("系统打盹，请稍后再试");
        }
    }

    /**
     * 从采购单中删除单个商品
     * @param cart
     * @return
     */
    @RequestMapping("/removeProductFromCart.json")
    @ResponseBody
    public Object removeProductFromCart(ShoppingCartBusinessDto cart) {
        try{
            if (cart.getId() == null && cart.getPackageId() == null) {
                return this.addError("购物车ID不能为空");
            }
            Transaction t2 = CatUtil.initTransaction("removeProductFromCart", "removeProductFromCart");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setMerchantId(merchant.getId());
            Map<String, Object> dataMap = new HashMap<>();
            if (orderServerRpcService.commonIfaceGray("removeProductFromCart")) {
                DeleteCartDto deleteCartDto = new DeleteCartDto();
                deleteCartDto.setMerchantId(cart.getMerchantId());
                deleteCartDto.setSkuIdList(Arrays.asList(cart.getId()));
                deleteCartDto.setPackageIdList(Arrays.asList(cart.getPackageId()));
                deleteCartDto.setRealIp(cart.getRealIP());
                deleteCartDto.setRealIp(cart.getRealIP());
                deleteCartDto.setAccountRole(merchant.getAccountRole());
                deleteCartDto.setAccountId(merchant.getAccountId());
                orderServerRpcService.batchRemoveProduct(deleteCartDto);
            } else {
                dataMap = cartService.removeProductFromCart(cart);
            }
            CatUtil.successCat(t2);
            return this.addResult("data", dataMap);
        }catch (Exception e){
            logger.error("removeProductFromCart error",e);
            return this.addResult("data", null);
        }
    }

    @RequestMapping("/batchRemoveProductFromCart.json")
    @ResponseBody
    public Object batchRemoveProductFromCart(ShoppingCartBusinessDto cart) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setMerchantId(merchant.getId());
            if (StringUtil.isEmpty(cart.getIds()) && StringUtil.isEmpty(cart.getPackageIds())) {
                return this.addError("购物车ID不能为空");
            }
            if (StringUtil.isNotEmpty(cart.getIds())) {
                Long[] idsArray = StringUtil.splitStringToLong(cart.getIds(), ",");
                cart.setIdsArray(idsArray);
            }
            if (StringUtil.isNotEmpty(cart.getPackageIds())) {
                Long[] idsArray = StringUtil.splitStringToLong(cart.getPackageIds(), ",");
                //删除多项ID
                cart.setPackageIdsArray(idsArray);
            }
            Transaction t2 = CatUtil.initTransaction("batchRemoveProductFromCart", "batchRemoveProductFromCart");
            Map<String, Object> dataMap = new HashMap<>();
            if (orderServerRpcService.commonIfaceGray("batchRemoveProductFromCart")) {
                DeleteCartDto deleteCartDto = new DeleteCartDto();
                deleteCartDto.setMerchantId(cart.getMerchantId());

                List<Long> productIds = Lists.newArrayList();
                Long[] productIdArr = cart.getIdsArray();
                if (productIdArr != null) {
                    productIds = Stream.of(productIdArr).collect(Collectors.toList());
                }

                List<Long> packageIds = Lists.newArrayList();
                Long[] packageIdArr = cart.getPackageIdsArray();
                if (packageIdArr != null) {
                    packageIds = Stream.of(packageIdArr).collect(Collectors.toList());
                }
                deleteCartDto.setRealIp(cart.getRealIP());
                deleteCartDto.setSkuIdList(productIds);
                deleteCartDto.setPackageIdList(packageIds);
                deleteCartDto.setRealIp(cart.getRealIP());
                deleteCartDto.setAccountRole(merchant.getAccountRole());
                deleteCartDto.setAccountId(merchant.getAccountId());
                orderServerRpcService.batchRemoveProduct(deleteCartDto);
            } else {
                dataMap = cartService.batchRemoveProductFromCart(cart);
            }
            CatUtil.successCat(t2);
            return this.addResult("data", dataMap);
        } catch (Exception e) {
            logger.error("batchRemoveProductFromCart error", e);
            return this.addResult("data", null);
        }

    }

    /**
     * 获取购物车商品数量
     * @return
     */
    @RequestMapping("/getCartNum")
    @ResponseBody
    public Object getCartNum() {
        try {
            Transaction t2 = CatUtil.initTransaction("batchRemoveProductFromCart", "batchRemoveProductFromCart");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Integer cartNum = 0;
            logger.info("getCartNum merchant:{}", JSON.toJSONString(merchant));
            if (AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())){
                cartNum = orderServerRpcService.getSkuVarietyNumSubAccount(merchant.getAccountId(),merchant.getId());
            }else {
                if (orderServerRpcService.commonIfaceGray("getCartNum")){
                    cartNum = orderServerRpcService.getSkuVarietyNum(merchant.getId());
                } else {
                    cartNum = cartService.getCartNum(merchant.getId());
                }
            }
            CatUtil.successCat(t2);
            return this.addResult("num", cartNum);
        } catch (Exception e) {
            logger.error("getCartNum error",e);
            return this.addError("服务器异常");
        }

    }

    /**
     * 移至收藏夹
     * @param cart
     * @return
     */
    @RequestMapping("/followFavoriteForCart.json")
    @ResponseBody
    public Object followFavoriteForCart(ShoppingCartBusinessDto cart) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setMerchantId(merchant.getId());
            String ids = cart.getIds();
            if (StringUtil.isEmpty(ids)) {
                return this.addError("参数异常");
            }
            Long[] idsArray = StringUtil.splitStringToLong(cart.getIds(), ",");
            cart.setIdsArray(idsArray);
            cartService.addFavoriteForCart(cart);
            return this.addResult("收藏成功");
        } catch (IllegalArgumentException e) {
            logger.error("followFavoriteForCart error",e);
            return this.addError(e.getMessage());
        } catch (Exception e) {
            logger.error("followFavoriteForCart error",e);
            return this.addError("服务器异常");
        }

    }

    /**
     * 购物车-优惠券弹窗，已领券下方商品勾选/取消勾选
     * 购物车明细ID，merchantId
     * @param cart
     * @return
     */
    @RequestMapping("/voucher/changeCart")
    @ResponseBody
    public Object voucherProductSelectItem(ShoppingCartBusinessDto cart,Integer type) {
        if (cart.getMerchantId() == null) {
            return this.addError("商户ID不能为空");
        }
        if (cart.getSkuId() == null && cart.getPackageId() == null) {
            return this.addError("购物车商品ID不能为空");
        }
        if (cart.getSkuId() != null && cart.getPackageId() != null) {
            cart.setSkuId(null);
        }
        try {
            if(type==null){
                type = CouponLevelEnum.SHOP_COUPON.getCode();
            }
//            else {
//                type = type==2?CouponLevelEnum.SHOP_COUPON.getCode():CouponLevelEnum.PLATFORM_COUPON.getCode();
//            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (cart.getStatus() == 1) {//选中
                if (orderServerRpcService.commonIfaceGray("selectItem")) {
                    SelectCartDto selectCartDto = new SelectCartDto();
                    selectCartDto.setMerchantId(cart.getMerchantId());
                    selectCartDto.setSkuId(cart.getSkuId());
                    selectCartDto.setPackageId(cart.getPackageId());
                    selectCartDto.setAccountId(merchant.getAccountId());
                    selectCartDto.setAccountRole(merchant.getAccountRole());
                    orderServerRpcService.selectItem(selectCartDto);
                }else {
                    cartService.selectItem(cart);
                }
            } else if (cart.getStatus() == 0) {//未选中
                if (orderServerRpcService.commonIfaceGray("cancelItem")){
                    SelectCartDto selectCartDto = new SelectCartDto();
                    selectCartDto.setMerchantId(cart.getMerchantId());
                    selectCartDto.setSkuId(cart.getSkuId());
                    selectCartDto.setPackageId(cart.getPackageId());
                    selectCartDto.setAccountId(merchant.getAccountId());
                    selectCartDto.setAccountRole(merchant.getAccountRole());
                    orderServerRpcService.unSelectItem(selectCartDto);
                }else {
                    cartService.cancelItem(cart);
                }
            }
//            Map<String, List<CartVocherDto>> dataMap = cartService.selectCartVoucher(cart.getMerchantId(),cart.getShopCode());
            Map<String, List<CartVoucherDto>> dataMap = orderServerRpcService.selectCartVoucher(cart.getMerchantId(), cart.getShopCode(), type);
            return this.addResult("data", dataMap);
        } catch(IllegalArgumentException e){
            return this.addError(e.getMessage());
        } catch (Exception e) {
            logger.error("服务器异常", e);
            return this.addError("服务器异常");
        }
    }

    /**
     * 购物车勾选商品
     * 购物车明细ID，merchantId
     * @param cart
     * @return
     */
    @RequestMapping("/selectItem")
    @ResponseBody
    public Object selectItem(ShoppingCartBusinessDto cart) {
        try {
            if (cart.getSkuId() == null && cart.getPackageId() == null) {
                return this.addError("购物车ID不能为空");
            }
            Transaction t2 = CatUtil.initTransaction("selectItem", "selectItem");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setMerchantId(merchant.getId());
            if (orderServerRpcService.commonIfaceGray("selectItem")) {
                SelectCartDto selectCartDto = new SelectCartDto();
                selectCartDto.setMerchantId(cart.getMerchantId());
                selectCartDto.setSkuId(cart.getSkuId());
                selectCartDto.setPackageId(cart.getPackageId());
                selectCartDto.setAccountId(merchant.getAccountId());
                selectCartDto.setAccountRole(merchant.getAccountRole());
                orderServerRpcService.selectItem(selectCartDto);
            } else{
                Map<String, Object> dataMap = cartService.selectItem(cart);
            }
            CatUtil.successCat(t2);
            return this.addResult();
        } catch (Exception e) {
            logger.error("selectItem error",e);
            return this.addError(e.getMessage());
        }
    }

    /**
     * 购物车取消勾选商品
     * 购物车明细ID，merchantId
     * @param cart
     * @return
     */
    @RequestMapping("/cancelItem")
    @ResponseBody
    public Object cancelItem(ShoppingCartBusinessDto cart) {
        try{
            if (cart.getSkuId() == null && cart.getPackageId() == null) {
                return this.addError("购物车ID不能为空");
            }
            Map<String, Object> dataMap = new HashMap<>();
            Transaction t2 = CatUtil.initTransaction("cancelItem", "cancelItem");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setMerchantId(merchant.getId());
            if (orderServerRpcService.commonIfaceGray("cancelItem")) {
                SelectCartDto selectCartDto = new SelectCartDto();
                selectCartDto.setMerchantId(cart.getMerchantId());
                selectCartDto.setSkuId(cart.getSkuId());
                selectCartDto.setPackageId(cart.getPackageId());
                selectCartDto.setAccountId(merchant.getAccountId());
                selectCartDto.setAccountRole(merchant.getAccountRole());
                orderServerRpcService.unSelectItem(selectCartDto);
            }else {
               dataMap = cartService.cancelItem(cart);
            }
            CatUtil.successCat(t2);
            return this.addResult("data", dataMap);
        }catch (Exception e){
            logger.error("cancelItem errors",e);
            return this.addResult("data", null);
        }
    }

    /**
     * 购物车勾选全部商品
     * 购物车明细ID，merchantId
     * @param cart
     * @return
     */
    @RequestMapping("/selectAllItem")
    @ResponseBody
    public Object selectAllItem(ShoppingCartBusinessDto cart) {
        try {
            Transaction t2 = CatUtil.initTransaction("selectAllItem", "selectAllItem");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setMerchantId(merchant.getId());
            Map<String, Object> dataMap = new HashMap<>();
            if (orderServerRpcService.commonIfaceGray("selectAllItem")) {
                BatchSelectCartDto batchSelectCartDto = new BatchSelectCartDto();
                batchSelectCartDto.setMerchantId(cart.getMerchantId());
                batchSelectCartDto.setGroupId(cart.getOrgId());
                batchSelectCartDto.setIsThirdCompany(cart.getIsThirdCompany());
                batchSelectCartDto.setRealIp(cart.getRealIP());
                batchSelectCartDto.setAccountId(merchant.getAccountId());
                batchSelectCartDto.setAccountRole(merchant.getAccountRole());
                orderServerRpcService.batchSelectByGoupId(batchSelectCartDto);
            } else {
//            return shoppingCartBusinessApi.selectAllItem(shoppingCartDto);
                dataMap = cartService.selectAllItem(cart);
            }
            CatUtil.successCat(t2);
            return this.addResult("data", dataMap);
        } catch (Exception e) {
            logger.error("selectAllItem error", e);
            return this.addError("购物车全选异常");
        }

    }

    /**
     * 购物车取消勾选全部商品
     * 购物车明细ID，merchantId
     * @param cart
     * @return
     */
    @RequestMapping("/cancelAllItem")
    @ResponseBody
    public Object cancelAllItem(ShoppingCartBusinessDto cart) {
        try {
            Transaction t2 = CatUtil.initTransaction("cancelAllItem", "cancelAllItem");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setMerchantId(merchant.getId());
            Map<String, Object> dataMap = new HashMap<>();

            if (orderServerRpcService.commonIfaceGray("cancelAllItem")) {
                BatchSelectCartDto batchSelectCartDto = new BatchSelectCartDto();
                batchSelectCartDto.setMerchantId(cart.getMerchantId());
                batchSelectCartDto.setGroupId(cart.getOrgId());
                batchSelectCartDto.setIsThirdCompany(cart.getIsThirdCompany());
                batchSelectCartDto.setRealIp(cart.getRealIP());
                batchSelectCartDto.setAccountId(merchant.getAccountId());
                batchSelectCartDto.setAccountRole(merchant.getAccountRole());
                orderServerRpcService.batchUnSelectByGroupId(batchSelectCartDto);
            } else {
//            return shoppingCartBusinessApi.cancelAllItem(shoppingCartDto);
                dataMap = cartService.cancelAllItem(cart);
            }
            CatUtil.successCat(t2);
            return this.addResult("data", dataMap);
        }catch (Exception e){
            logger.error("cancelAllItem error",e);
            return this.addResult("data", null);
        }
    }

    /**
     * 获取采购单可用优惠券列表
     * @param cart
     * @return
     */
    @RequestMapping("/selectCartVoucher")
    @ResponseBody
    public Object selectCartVoucher(ShoppingCartBusinessDto cart,String shopCode,Integer type) {
        try {
            Transaction t2 = CatUtil.initTransaction("selectCartVoucher", "selectCartVoucher");
            if ((type ==null||type==1)&&StringUtils.isBlank(shopCode)) {
                return this.addError("shopCode不能为空");
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if(type==null){
               type = CouponLevelEnum.SHOP_COUPON.getCode();
            }
//            else {
//                type = type==2?CouponLevelEnum.SHOP_COUPON.getCode():CouponLevelEnum.PLATFORM_COUPON.getCode();
//            }
            Map<String, List<CartVoucherDto>> dataMap = orderServerRpcService.selectCartVoucher(merchant.getId(), shopCode, type);
//            Map<String, List<CartVocherDto>> dataMap = cartService.selectCartVoucher(merchant.getId(),shopCode);
            CatUtil.successCat(t2);
            if (CollectionUtil.isNotEmpty(dataMap)){
                return this.addResult("data", dataMap);
            }
        } catch (Exception e) {
            logger.error("selectCartVoucher error",e);
            return this.addError("网络异常");
        }
        return this.addError("无优惠券可用");
    }

    /**
     * 清空个人购物车
     * @return
     */
    @RequestMapping("/cleanCart")
    @ResponseBody
    public Object cleanCart(){
        try{
            Transaction t2 = CatUtil.initTransaction("cleanCart", "cleanCart");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())){
                orderServerRpcService.clearCartSubAccount(merchant.getAccountId(),merchant.getMerchantId());
            }else {
                if (orderServerRpcService.commonIfaceGray("cancelAllItem")) {
                    orderServerRpcService.clearCart(merchant.getId());
                }else {
                    cartService.cleanCart(merchant.getId());
                }
            }
            CatUtil.successCat(t2);
        }catch (Exception e){
            logger.error("cleanCart error",e);
            return this.addError("清空购物车异常");
        }
        return this.addResult("result","success");
    }

}
