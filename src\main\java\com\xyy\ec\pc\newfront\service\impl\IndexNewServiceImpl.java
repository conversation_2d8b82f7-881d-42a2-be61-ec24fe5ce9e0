package com.xyy.ec.pc.newfront.service.impl;

import cn.hutool.core.util.URLUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.exp.api.ExpOtherApi;
import com.xyy.ec.exp.params.ExpOtherParam;
import com.xyy.ec.exp.result.ExpOtherResult;
import com.xyy.ec.merchant.bussiness.api.LicensePoolBusinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantAuthenticationBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.api.ShoppingCartBusinessApi;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.enums.ProvinceBranchEnum;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.newfront.dto.HeaderRespVO;
import com.xyy.ec.pc.newfront.dto.VerifiedRespVO;
import com.xyy.ec.pc.newfront.service.IndexNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.IndexService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.product.business.module.CategoryVo;
import com.xyy.ec.system.business.dto.DicAreaBusinessDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndexNewServiceImpl extends BaseController implements IndexNewService {

    Logger LOGGER = LoggerFactory.getLogger(com.xyy.ec.pc.controller.IndexController.class);

    @Value("${new.index.open:false}")
    private boolean openNewIndex;

    @Reference(version = "1.0.0")
    private ExpOtherApi expApi;

    @Reference(version = "1.0.0")
    private ShoppingCartBusinessApi shoppingCartBusinessApi;

    private final XyyIndentityValidator xyyIndentityValidator;

    private final IndexService indexService;

    @Reference(version = "1.0.0")
    private MerchantAuthenticationBusinessApi merchantAuthenticationBusinessApi;

    @Reference(version = "1.0.0")
    private LicensePoolBusinessApi licensePoolBusinessApi;


    /**
     * AB实验首页
     * true 继续
     * false 说明非实验用户
     */
    @Override
    public boolean isNewIndex(Long merchantId) {
        // 未选择店铺的场景, 跳新首页, 前端会帮忙重定向到选择店铺页面
        if (merchantId == null || merchantId <= 0) {
            return true;
        }
        ExpOtherParam expParam = ExpOtherParam.builder().systemCode("xyy-ec-pc").bizCode("index").merchantId(merchantId).build();
        ApiRPCResult<ExpOtherResult> exp = expApi.getExp(expParam);
        if (exp == null ||  exp.getData() == null && exp.getData().getConfig() == null) {
            return false;
        }
        return JSONObject.parseObject(exp.getData().getConfig()).getBooleanValue("newIndex");
    }

    @Override
    public HeaderRespVO getHeaderData(HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("PC头部数据请求：{}", JSONObject.toJSONString(merchant));
            }
            //得到购物车总数量
            int merchantCartCount = 0;
            List<DicAreaBusinessDto> provinceList = new ArrayList<>();
            Long merchantId = 0L;
            if (Objects.equals(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId(), merchant.getAccountRole())){
                merchantCartCount = shoppingCartBusinessApi.getCartNumSubAccount(merchant.getId(),merchant.getAccountId());
            }else {
                merchantId = merchant.getId();
                //得到购物车总数量
                merchantCartCount = shoppingCartBusinessApi.getCartNum(merchantId);
            }
            String branchCode = this.getBranchCodeByMerchantId(request,merchantId);
            ProvinceBranchEnum branchEnum = ProvinceBranchEnum.getEnumByBranchCode(branchCode);

            HeaderRespVO headerRespVO = new HeaderRespVO();
            headerRespVO.setMerchantCartCount(merchantCartCount);
            headerRespVO.setMerchant(merchant);
            headerRespVO.setProvince(branchEnum.getProvince());
            headerRespVO.setBranchCode(branchCode);

            // 假设 SUCCESS 状态码为 200，可根据实际情况调整
            return headerRespVO;
        } catch (Exception e) {
            LOGGER.error("头部加载错误:" + ExceptionUtils.getStackTrace(e));
            String errMsg = "头部数据加载失败";
            throw new AppException(errMsg, XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public AjaxResult<List<CategoryVo>> categoryTree(HttpServletRequest request) throws Exception {;

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant == null) {
               return AjaxResult.errResult("请登录");
            }
            merchantId = merchant.getId();
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            List<CategoryVo> categoryTree = indexService.getCategoryTree(branchCode);


            return AjaxResult.successResult( categoryTree);

    }

    @Override
    public AjaxResult<VerifiedRespVO> getVerified(HttpServletRequest request) {
        try {
            VerifiedRespVO verifiedRespVO = new VerifiedRespVO();
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            if (Objects.nonNull(merchant)) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            // 实名认证首页弹窗
            Map<String, Object> map = merchantAuthenticationBusinessApi.getMerchantAuthenticationAlert(merchantId, branchCode, 1);
            verifiedRespVO.setAlertFlag((Integer) map.get("auth_alertFlag"));
            verifiedRespVO.setAlertFlag1((Boolean) map.get("change_alertFlag"));

            Long count =null;
            if(merchant != null){
                count = licensePoolBusinessApi.queryCountByMerchantId(merchant.getId());
            }
            verifiedRespVO.setCount(count);
            return AjaxResult.successResult(verifiedRespVO);
        } catch (Exception e) {
            return AjaxResult.errResult("获取认证弹窗失败");
        }
    }
}
