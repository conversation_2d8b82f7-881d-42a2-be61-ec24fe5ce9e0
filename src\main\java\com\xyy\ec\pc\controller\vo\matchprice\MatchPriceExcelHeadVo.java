package com.xyy.ec.pc.controller.vo.matchprice;

import lombok.Data;

import java.util.Set;

import static com.xyy.ec.pc.controller.vo.matchprice.MatchPriceExcelHeadVo.MatchType.UNMATCHED;

@Data
public class MatchPriceExcelHeadVo {

    /**
     * 匹配到的表头所在行index
     */
    private int headRowIndex;

    /**
     * 匹配类型
     */
    private MatchType accurateMatch = UNMATCHED;

    /**
     * 表格名称
     */
    private String name;

    /**
     * 行数
     */
    private Integer rowNum;

    /**
     * 表头名称
     */
    private Set<String> headerInformation;

    public enum MatchType {
        ACCURATE,
        VAGUE,
        UNMATCHED,
        ;
    }
}
