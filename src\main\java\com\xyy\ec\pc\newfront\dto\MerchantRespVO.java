package com.xyy.ec.pc.newfront.dto;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.dto.MyOrderBusinessDto;
import com.xyy.ec.pc.controller.vo.merchant.MerchantAccountRelatedMerchantInfoVO;
import com.xyy.ec.pc.model.Merchant;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class MerchantRespVO {
    // 商户信息
    private Merchant merchant;
    private Integer subAccount;
    //商品图片
    private String productImageUrl;
    //用户头像
    private String avatarUrl;

    // 订单统计信息
    private Object waitPayNum;     // 待付款订单数
    private Object waitShippingNum; // 待发货订单数
    private Object waitReceiveNum;     // 待收货订单数
    private Object waitAppraiseNum;       // 待评价订单数

    // 订单列表
    private List<MyOrderBusinessDto> orderList;

    // 优惠券数量
    private Integer voucherNum;

    // 积分信息
    private Integer pointCount;
    private String signDayArray;    // 签到日期数组（JSON字符串）

    // 余额
    private BigDecimal balanceMoney;

    //购物金金额
    private BigDecimal virtualGold;
    //账户状态
    private int accountState;

    // 其他字段
    private String centerMenu;      // 菜单标识
    private Byte status;         // 认证状态

    PageInfo<MerchantAccountRelatedMerchantInfoVO> merchantInfoPage;

    String mobile;
    //判断是否短信验证
    boolean isCrawler;

    /**
     * 资质状态 1-资质未提交,4:资质已通过,5表示首营资质审核中,6表示首营一审通过
     */
    private Integer licenseStatus;



    public void setSubAccount(int accountRole) {
        this.subAccount = AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(accountRole) ? 1 : 0;
    }

}
