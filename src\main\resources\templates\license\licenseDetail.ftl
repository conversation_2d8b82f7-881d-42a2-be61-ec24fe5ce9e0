<!DOCTYPE HTML>
<html>

<head>
    <#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description"
          content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="referrer" content="never">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>资质管理--资质单据</title>
<#--    <title>资质管理--资质变更</title>-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">

    <link rel="stylesheet" href="${ctx}/static/css/user.css?t=${t_v}"/>
    <script type="text/javascript">
        var ctx = "${ctx}";
    </script>

    <style>
        .myqual{
            width: 1260px;
        }

        .main-right.fr{
            border-width: 0px;
            background: #fff;
            /*min-height: 720px;*/
            padding: 20px 20px;
            width: 1010px;
        }

        .page-title-license{
            border-left-width: 0px;
            margin-bottom:20px;
            width: 990px;
            height: 20px;
           line-height: 20px;
            padding-left: 0px;
        }

        .shheXx{
            padding-bottom: 20px;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .shheXx div{
            font-size:14px;
            font-family:PingFangSC;
            font-weight:400;
            color:#333333;
            line-height:22px;
        }
        .shheXx div span,.shheXx div div{
            margin:0px;
            padding:0px;
        }
        .shheXx div span.spanOne,.shheXx div div.spanOne{
            color:#666666;
        }

        .shheXx div span.fail,.shheXx div div.fail{
            color:#FF7200;
        }

        .shheXx div span.succ,.shheXx div div.succ{
            color:#00C675;
        }

        .shheXx div div{
            width:930px;
        }
        .shheXx div div.spanOne{
            width:70px;
        }

        .zzhCon{
            padding-bottom: 20px;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;

        }

        .zzhCon .zzhConItem{
            width:49%;
           max-width:50%;
            margin-bottom: 30px;
            float: left;
            padding-right: 10px;
        }

        .zzhCon .zzhConItem .zzhConItemTitle{
            font-size:14px;
            font-family:PingFangSC;
            font-weight:400;
            color:#333333;
            line-height:14px;
            margin-bottom: 15px;
        }

        .zzhConItemTitleSee{
            font-size:12px;
            font-family:PingFangSC;
            font-weight:400;
            color:#6470B0;
            margin-left:15px;
            cursor: pointer;
        }

        .zzhConItemTitleDload{
            font-size:12px;
            font-family:PingFangSC;
            font-weight:400;
            color:#6470B0;
            margin-left:15px;
            cursor: pointer;
        }

        .zzhCon .zzhConItem .zzhConItemTitle .required{
            color:red;
        }

        .zzhConItemImgCon{
            position: relative;
        }
        .zzhConItemImgCon .yiguoqi{
            position: absolute;
            bottom:0px;
            left:0;
            text-align: center;
            display: inline-block;
            width: 136px;
            height: 28px;
            line-height:28px;
            background: #ff3024;
            font-size: 16px;
            color: #ffffff;
        }
        .zzhConItemImgCon .linqi{
            position: absolute;
            bottom:0px;
            left:0;
            text-align: center;
            display: inline-block;
            width: 136px;
            height: 28px;
            line-height:28px;
            background: #ff7200;
            font-size: 16px;
            color: #ffffff;
        }

        .zzhConItemImgConItem{
              position: relative;
              width: 135px;
              height:135px;
              margin-right: 16px;
              margin-bottom: 16px;
              border: 1px solid #ddd;
              border-radius: 3px;
        }

        .zzhConItemImgConItem .removeImg{
            position: absolute;
            top: -12px;
            right: -8px;
            font-size: 25px;
            z-index: 1;
        }

        .zzhConItemImgConItem img{
            width: 100%;
            max-width: 100%;
            display: block;
            margin: 0px auto;
            height: 100%;
            max-height: 100%;
        }

        .zzhConItemImgConItem input[type="file"]{
            position: absolute;
            top:0px;
            left:0px;
            z-index: 1;
            width: 135px;
            height:135px;
            opacity: 0;
            filter: alpha(opacity=0);
            cursor: pointer;
        }

        .zzhConItemImgCon img.zzhConItemImg{
            width: 135px;
            height:135px;
            /*margin-right: 16px;*/
            margin-bottom: 16px;
            border: 1px solid #fff;
        }

        .zzhCon .zzhConItem .zzhConItemRemark{
            font-size:12px;
            font-family:PingFangSC;
            font-weight:400;
            color:#8E8E93;
            line-height:17px;
            margin-bottom: 10px;
        }

        .wtsItem label div{
            width:113px ;
            text-align: right;
            font-size:12px;
            font-family:PingFangSC;
            font-weight:400;
            color:#666;
            line-height:18px;
            margin-right: 10px;
            float: left;
            height:32px;
            line-height: 32px;
        }

        .wtsItem{
            margin-bottom: 15px;
        }
        .wtsItem label input{
            width:270px;
            height:32px;
            background:rgba(255,255,255,1);
            border-radius:2px;
            border:1px solid rgba(238,238,238,1);
            float: left;
            padding:0px 10px;
        }
        .wtsItem label textarea{
            width:232px;
            height:107px;
            background:rgba(255,255,255,1);
            border-radius:2px;
            border:1px solid rgba(238,238,238,1);
            padding:10px;
        }

        .sui-btn.btn-bordered.subbtn{
            width:142px;
            height:40px;
            background:#00C675;
            border-radius:2px;
            font-size:14px;
            font-family:PingFangSC;
            font-weight:400;
            color:#fff;
            border: 0px solid #8c8c8c;
        }

        .shiliImg{
            width:100%;
            position: absolute;
            left:0px;
            bottom: 0px;
            height:25px;
            line-height: 25px;
            font-size: 15px;
            color:#fff;
            background: #c89e02a1;
            text-align: center;

        }

    </style>

</head>

<body>
<div class="container">

    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
        <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->
    <div class="main">
        <div class="myqual row">
            <!--面包屑-->
            <ul class="sui-breadcrumb">
                <li><a href="${ctx}/index.htm">首页</a>></li>
                <li><a href="${ctx}/merchant/center/index.htm">用户中心</a>></li>
                <li><a href="${ctx}/merchant/center/licenseAudit/queryLicenseAuditList">资质管理</a>></li>
                <li class="active">资质单据</li>
            </ul>
            <div class="myqual-content clear">
                <div class="side-left fl">
                    <#include "/common/merchantCenter/left.ftl" />
                </div>
                <div class="main-right fr">
                    <div class="page-title-license">
                        <span  id="licenseList" style="float:left;">资质单据</span>
                        <div style="clear:both;"></div>
                    </div>
                    <div style="clear:both;"></div>
                    <!--首营状态-->
                    <input type="hidden" id="licenseType" value="${licenseAudit.type}">
                    <!--机构编码-->
                    <input type="hidden" id="orgCode" value="${licenseAudit.ecOrgCode}">
                    <!--客户类型-->
                    <input type="hidden" id="licenseCustomerType" value="${licenseAudit.customerType}">
                    <!--单据编号-->
                    <input type="hidden" id="applicationNumber" value="${licenseAudit.applicationNumber}">
                    <div class="shheXx">
<#--                        <div>-->
<#--                            <span class="spanOne">机构名称：</span>-->
<#--                            <span>${licenseAudit.orgName!"-"}</span>-->
<#--                        </div>-->
                        <div>
                            <span class="spanOne">单据编号：</span>
                            <span>${licenseAudit.applicationNumber}</span>
                        </div>
                        <div>
                            <span class="spanOne">提交时间：</span>
                            <span><#if licenseAudit.createTime??>${licenseAudit.createTime ?string("yyyy-MM-dd HH:mm:ss")}</#if></span>
                        </div>
                        <div>
                            <span class="spanOne">审核状态：</span>
                            <input type="hidden" value="${licenseAudit.auditStatusName}">
                            <#if licenseAudit.auditStatus==10 || licenseAudit.auditStatus==20 || licenseAudit.auditStatus==30 || licenseAudit.auditStatus==40>
                                  <span style="color: red;">${licenseAudit.auditStatusName}</span>
                            <#elseif licenseAudit.auditStatus==11 || licenseAudit.auditStatus==21 || licenseAudit.auditStatus==31 || licenseAudit.auditStatus==3>
                                <span style="color: green;">${licenseAudit.auditStatusName}</span>
                            <#else>
                                  <span style="color: black;">${licenseAudit.auditStatusName}</span>
                            </#if>
                        </div>
                         <#--<#if (licenseAudit.auditStatus==10 || licenseAudit.auditStatus==20 || licenseAudit.auditStatus==30|| licenseAudit.auditStatus==40 || licenseAudit.paperRecyclStatus==10) && licenseAudit.remark!='' >-->
                         <#--<div >-->
                             <#--<div class="spanOne" style="float: left;">驳回原因：</div>-->
                             <#--<div style="float: left;">-->
                                 <#--${licenseAudit.remark}-->
                             <#--</div>-->
                             <#--<div style="clear:both;"></div>-->
                         <#--</div>-->
                         <#--</#if>-->
                    </div>
                    <div style="clear:both;"></div>


                    <div  class="zzhCon">
                         <#assign list_num="0" />
                    <#if necessaryLicenceList?? >
                             <#list necessaryLicenceList as licenseImg >
                                 <#assign list_num="${''+(list_num?number + 1)}" />
                                 <div class="zzhConItem">
                                    <div class="zzhConItemTitle">
                                        <span class="required">*</span>
                                        <span class="zzhConItemTitleName">${licenseImg.credentialName}</span>
                                    </div>
                                    <div class="zzhConItemImgCon" >
                                         <#if licenseImg.enclosureList?? >
                                             <#list licenseImg.enclosureList as lmgUrl >
                                                <div class="zzhConItemImgConItem" style="float: left;">
                                                    <img src="${lmgUrl.url}">
                                                    <#if licenseImg.status == 1 && lmgUrl_index == 0>
                                                    <span class="yiguoqi">已过期</span>
                                                    <#elseif licenseImg.status == 2 && lmgUrl_index == 0>
                                                    <span class="linqi">即将过期</span>
                                                    </#if>
                                                </div>
                                             </#list>
                                         <#else>
                                             <#if (licenseImg.licenseCode) == 'XYYWT'>
                                              <div class="zzhConItemImgConItem" style="float: left;">
                                                  <img src="/static/images/license/xyywt.png">
                                                  <div class="shiliImg">示例图片</div>
                                              </div>
                                             </#if>
                                         </#if>
                                        <div style="clear:both;"></div>
                                    </div>
                                    <div style="clear:both;"></div>
                                     <#if (licenseImg.licenseCode) == 'KPXX'>
                                        <p class="zzhConItemRemark">开具增值税专用发票，请完整、准确提供以下信息并加盖公章：名称、纳税人识别号、地址、电话、开户行、账号。</p>
                                     </#if>
                                     <#if (licenseImg.licenseCode) == 'XYYWT'>
                                     <div  class="wtsItem">
                                         <label>
                                             <div>小药药委托书有效期:</div>
                                             <input type="text" disabled class="input-medium input-date"
                                                    value="<#if licenseImg.xyyEntrusValidateTime??>${licenseImg.xyyEntrusValidateTime ?string("yyyy-MM-dd HH:mm:ss")}<#else >未知</#if>"/>
                                             <i style="clear:both;"></i>
                                         </label>
                                         <div style="clear:both;"></div>
                                     </div>
                                     <div style="clear:both;"></div>
                                     <div class="wtsItem">
                                         <label>
                                             <div>小药药委托书编号:</div>
                                             <input  value="<#if licenseImg.xyyEntrusCode?default("")?trim?length gt 1 >${licenseImg.xyyEntrusCode}<#else >未知</#if> " disabled />
                                             <i style="clear:both;"></i>
                                         </label>
                                         <div style="clear:both;"></div>
                                     </div>
                                    </#if>
                                </div>
                                 <#if ((list_num?number % 2)==0)>
                                     <div style="clear:both;"></div>
                                     <div style="float:left;"></div>
                                 </#if>
                             </#list>
                         </#if>
                        <#if optionalLicenceList?? >
                            <#list optionalLicenceList as licenseImg >
                                <#assign list_num="${''+(list_num?number + 1)}" />
                                 <div class="zzhConItem">
                                     <div class="zzhConItemTitle">
                                         <#--<span class="required">*</span>-->
                                         <span class="zzhConItemTitleName">${licenseImg.credentialName}</span>
                                         <#--<span class="zzhConItemTitleSee" data-url="http://t-upload.ybm100.com/ybm/product/min/6927947300229.jpg?random=0.2601158982142806">查看示例图片</span>-->
                                         <#--<span class="zzhConItemTitleDload">委托书模板下载</span>-->
                                     </div>
                                     <div class="zzhConItemImgCon" >
                                         <#if licenseImg.enclosureList?? >
                                             <#list licenseImg.enclosureList as lmgUrl >
                                                <div class="zzhConItemImgConItem" style="float: left;">
                                                    <img src="${lmgUrl.url}">
                                                    <#if licenseImg.status == 1 && lmgUrl_index == 0>
                                                    <span class="yiguoqi">已过期</span>
                                                    <#elseif licenseImg.status == 2 && lmgUrl_index == 0>
                                                    <span class="linqi">即将过期</span>
                                                    </#if>
                                                </div>
                                             </#list>
                                         <#else>
                                             <#if (licenseImg.licenseCode) == 'XYYWT'>
                                              <div class="zzhConItemImgConItem" style="float: left;">
                                                  <img src="/static/images/license/xyywt.png">
                                                  <div class="shiliImg">示例图片</div>
                                              </div>
                                             </#if>
                                         </#if>
                                         <div style="clear:both;"></div>
                                     </div>
                                     <div style="clear:both;"></div>
                                      <#if (licenseImg.licenseCode) == 'KPXX'>
                                        <p class="zzhConItemRemark">开具增值税专用发票，请完整、准确提供以下信息并加盖公章：名称、纳税人识别号、地址、电话、开户行、账号。</p>
                                      </#if>
                                      <#if (licenseImg.licenseCode) == 'XYYWT'>
                                     <div  class="wtsItem">
                                         <label>
                                             <div>小药药委托书有效期:</div>
                                             <input type="text" disabled  class="input-medium input-date"
                                                    value="<#if licenseImg.xyyEntrusValidateTime?? >${licenseImg.xyyEntrusValidateTime ?string("yyyy-MM-dd HH:mm:ss")}<#else>未知</#if>"/>
                                             <i style="clear:both;"></i>
                                         </label>
                                         <div style="clear:both;"></div>
                                     </div>
                                     <div style="clear:both;"></div>
                                     <div class="wtsItem">
                                         <label>
                                             <div>小药药委托书编号:</div>
                                             <input  value="<#if licenseImg.xyyEntrusCode?default("")?trim?length gt 1 >${licenseImg.xyyEntrusCode}<#else>未知</#if>" disabled />
                                             <i style="clear:both;"></i>
                                         </label>
                                         <div style="clear:both;"></div>
                                     </div>
                                    </#if>
                                 </div>
                                <#if ((list_num?number % 2)==0)>
                                     <div style="clear:both;"></div>
                                     <div style="float:left;"></div>
                                </#if>
                            </#list>
                        </#if>
                        <div style="clear:both;"></div>
                    </div>
                    <div style="clear:both;"></div>
                    <#--<#if tempRemark?? && tempRemark!=''>-->
                          <#--<div class="wtsItem">-->
                              <#--<label>-->
                                  <#--<div>备注:</div>-->
                                  <#--<textarea rows="10" disabled >${tempRemark}</textarea>-->
                                  <#--<i style="clear:both;"></i>-->
                              <#--</label>-->
                          <#--</div>-->
                    <#--</#if>-->
                    <#if isEdit==0>
                      <div style="text-align: center;">
                          <button class="sui-btn btn-bordered subbtn" id="sunbtn"  onclick="updateLicense()" >编辑</button>
                      </div>
                    <#elseif  licenseAudit.paperRecyclStatus==11>
                     <script>
                         $.alert("您的资质已经回收，如有疑问请联系您的专属销售");
                     </script>
                    <#elseif  licenseAudit.auditStatus==2>
                     <script>
                         $.alert("当前还有未处理完的资质，请联系您的专属销售。");
                     </script>
                    </#if>
                </div>
                <div id="jiucuoImgDialog" tabindex="-1" data-width="400" role="dialog"
                     class="sui-modal hide">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <button type="button" class="sui-close" id="jc_img_close" style="right: -50px;position: absolute;top: -35px;border: 2px solid #fff;border-radius: 30px;width: 35px;height: 35px;line-height: 30px;text-align: center;font-size: 30px;color: #fff;">×</button>
                            <div class="modal-body sui-form form-horizontal">
                                <div style="width:100%;height:400px;max-height:400px;line-height:100%;">
                                    <img src="" id="jiucuoImg"  style="display:block;margin:0px auto;height:100%;width:auto;max-width:100%;"  />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--底部导航区域开始-->
    <div class="footer" id="footer">
        <#include "/common/footer.ftl" />
    </div>
    <!--底部导航区域结束-->
    <!--客服入口开始-->
    <div class="kefu-box">
        <a href="javaScript:callKf('','${merchant.id}');">
            <img src="/static/images/kefu-online.png" alt="">
        </a>
    </div>
    <!--客服入口结束-->

</div>

</body>

<script>
    function updateLicense() {
        $.ajax({
            type: 'post',
            cache: false,
            url: '/merchant/center/licenseAudit/initBillDetailVaild?type='+$("#licenseType").val()+'&orgCode='+$("#orgCode").val(),
            dataType: 'json',
            success: function(data) {
                if (data.status == "success") {
                    if($("#licenseType").val() == 1){
                        var applicationNumber = $("#applicationNumber").val();
                        window.location.href='/merchant/center/licenseAudit/licenseAddMerchantInfo?type='+$("#licenseType").val()+'&applicationNumber='+applicationNumber+'&orgCode='+$("#orgCode").val();
                    }else{
                        window.location.href='/merchant/center/licenseAudit/initBillDetail?type='+$("#licenseType").val()+'&customerType='+$("#licenseCustomerType").val()+'&orgCode='+$("#orgCode").val();
                    }
                } else {
                    if (data.errorMsg) {
                        $.alert(data.errorMsg);
                    } else {
                        $.alert('保存失败');
                    }
                }
            },
            error: function(XMLHttpRequest, textStatus, errorThrown){
                $.alert('网络异常');
            }
        });
        return false;
    }
    //判断是否是IE
    function isIE() { //ie?
        if (!!window.ActiveXObject || "ActiveXObject" in window)
        { return true; }
        else
        { return false; }
    }

    Date.prototype.Format = function(fmt){
        var o = {
            "M+": this.getMonth()+1,
            "d+": this.getDate(),
            "H+": this.getHours(),
            "m+": this.getMinutes(),
            "s+": this.getSeconds(),
            "S+": this.getMilliseconds()
        };
        //因为date.getFullYear()出来的结果是number类型的,所以为了让结果变成字符串型，下面有两种方法：
        if(/(y+)/.test(fmt)){
            //第一种：利用字符串连接符“+”给date.getFullYear()+""，加一个空字符串便可以将number类型转换成字符串。
            fmt=fmt.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length));
        }
        for(var k in o){
            if (new RegExp("(" + k +")").test(fmt)){
                //第二种：使用String()类型进行强制数据类型转换String(date.getFullYear())，这种更容易理解。
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(String(o[k]).length)));
            }
        }
        return fmt;
    };

    $(function(){


        var endDate = new Date(new Date().valueOf() + (3 * 365 *  24 * 60 * 60 * 1000));
        //console.log(endDate,'------endDate----------');
        $(".input-date").datepicker({
            //startDate:new Date(),
            endDate:endDate,
            timepicker:true,
            "data-date-format": "yyyy-MM-dd HH:mm",
            //autoclose: false,
        });


        $('#jc_img_close').on('click',function(){
            $('#jiucuoImgDialog').modal('hide');
            return false;
        })


        $('.zzhConItemTitleSee').on('click',function(){
            var url = $(this).data('url');
            $("#jiucuoImg").attr('src',url);
            $('#jiucuoImgDialog').modal('show');
            return false;
        })
    })
</script>
</html>