package com.xyy.ec.pc.cms.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.ecp.api.PcModuleManageBuineseApi;
import com.xyy.ec.layout.buinese.ecp.params.LayoutPcIndexByPageIdQueryParam;
import com.xyy.ec.layout.buinese.ecp.results.CmsPcIndexLayoutModuleDTO;
import com.xyy.ec.pc.cms.service.CmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CmsServiceImpl implements CmsService {

    @Reference(version = "1.0.0")
    private PcModuleManageBuineseApi pcModuleManageBuineseApi;

    @Override
    public List<CmsPcIndexLayoutModuleDTO> listPcIndexLayoutModulesByPageIdForV2(Long merchantId, Long pageId) {
        LayoutPcIndexByPageIdQueryParam layoutPcIndexByPageIdQueryParam = LayoutPcIndexByPageIdQueryParam.builder().merchantId(merchantId).pageId(pageId).build();
        ApiRPCResult<List<CmsPcIndexLayoutModuleDTO>> apiRPCResult = pcModuleManageBuineseApi.listPcIndexLayoutModulesByPageIdForV2(layoutPcIndexByPageIdQueryParam);
        if (apiRPCResult.isFail() || Objects.isNull(apiRPCResult.getData())) {
            return Lists.newArrayList();
        }
        return apiRPCResult.getData();
    }

}
