package com.xyy.ec.pc.controller;

import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.CosConfig;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.util.CosUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;

/**
 * 文件上传下载服务类
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/fileTransfer")
@Slf4j
public class FileTransferController extends BaseController {
    @Autowired
    private CosConfig cosConfig;
    @Autowired
    private XyyConfig xyyConfig;


    /**
     * 从文件服务器下载文件
     *
     * @param response
     * @param fileName 文件名
     */
    @GetMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response, String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            log.warn("FileTransferController.downloadTemplate#下载文件名为空");
            return;
        }
        try (OutputStream out = response.getOutputStream()) {
            log.info("FileTransferController.downloadTemplate#fileName:{}", fileName);
            response.reset();
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), "iso-8859-1"));

            byte[] bytes = CosUtil.downloadFile(cosConfig.getDir() + xyyConfig.getTemplateDir() + fileName);
            out.write(bytes);
            log.info("FileTransferController.downloadTemplate#fileName:{} return 文件成功", fileName);
        } catch (Exception e) {
            log.error("FileTransferController.downloadTemplate#error.fileName:{}", fileName, e);
        }
    }

    /**
     * 下载指定路径下文件
     *
     * @param response
     * @param path
     * @param fileName
     */
    @GetMapping(value = "/downloadWithPath")
    public void downloadWithPath(HttpServletResponse response, String path, String fileName) {
        try {
            log.info("FileTransferController.downloadWithPath#path:{},fileName:{}", path, fileName);
            if (StringUtils.isEmpty(path) || StringUtils.isEmpty(fileName)) {
                return;
            }

            try (OutputStream out = response.getOutputStream()) {
                response.reset();
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), "iso-8859-1"));
                out.write(CosUtil.downloadFile(path));
            } catch (IOException e) {
                log.error("FileTransferController.downloadWithPath#IO异常. path:{},fileName:{}", path, fileName, e);
            }

            log.info("FileTransferController.downloadWithPath#path:{},fileName:{} 文件正常返回", path, fileName);
        } catch (Exception e) {
            log.error("FileTransferController.downloadWithPath#path:{},fileName:{} 异常", path, fileName, e);
        }
    }
}
