package com.xyy.ec.pc.controller;

import java.net.URLDecoder;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.api.SearchRecordBusinessApi;
import com.xyy.ec.product.business.dto.SearchRecordBusinessDTO;
/**
 * 搜索记录
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/searchRecord")
public class SearchRecordController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SearchRecordController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0",timeout = 10000)
    SearchRecordBusinessApi searchRecordBusinessApi;

    /**
     * 获取用户的搜索记录
     * @param request
     * @return
     */
    @RequestMapping("/list.json")
    @ResponseBody
    public Object list(HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return this.addError("用户登录异常");
            }
            String branchCode = this.getBranchCodeByMerchantId(request,merchant.getId());
            List<SearchRecordBusinessDTO> list = searchRecordBusinessApi.findSearchRecordlist(merchant.getId(), branchCode);
            return this.addResult("data", list);
        } catch (Exception e) {
            LOGGER.error("SearchRecordController-list.json获取用户的搜索记录列表异常",e);
            return this.addError("获取用户的搜索记录失败！");
        }
    }

    /**
     * 新增搜索记录
     * @param request
     * @param keyword
     * @return
     */
    @RequestMapping(value="/add.json", method = RequestMethod.POST)
    @ResponseBody
    public Object add(HttpServletRequest request,@RequestParam String keyword) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return this.addError("用户登录异常！");
            }
            if(StringUtil.isBlank(keyword)||StringUtil.isEmpty(keyword)){
                return this.addError("新增搜索记录无效！");
            }
            String keywordname = URLDecoder.decode(keyword);
            String branchCode = this.getBranchCodeByMerchantId(request,merchant.getId());
            SearchRecordBusinessDTO searchRecordBusinessDTO=new SearchRecordBusinessDTO();
            searchRecordBusinessDTO.setBranchCode(branchCode);
            searchRecordBusinessDTO.setKeyword(keywordname);
            searchRecordBusinessDTO.setMerchantId(merchant.getId());
            searchRecordBusinessApi.save(searchRecordBusinessDTO);
            return this.addResult();
        } catch (Exception e) {
            LOGGER.error("新增搜索记录异常-add",e);
            return this.addError("新增搜索记录失败！");
        }
    }

    /**
     * 删除搜索记录
     * @param request
     * @param keyword
     * @return
     */
    @RequestMapping(value="/del.json", method = RequestMethod.POST)
    @ResponseBody
    public Object del(HttpServletRequest request,@RequestParam String keyword) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return this.addError("用户登录异常！");
            }
            if(StringUtil.isBlank(keyword)||StringUtil.isEmpty(keyword)){
                return this.addError("删除搜索记录无效！");
            }
            String branchCode = this.getBranchCodeByMerchantId(request,merchant.getId());
            SearchRecordBusinessDTO searchRecordBusinessDTO=new SearchRecordBusinessDTO();
            searchRecordBusinessDTO.setBranchCode(branchCode);
            searchRecordBusinessDTO.setKeyword(keyword);
            searchRecordBusinessDTO.setMerchantId(merchant.getId());
            searchRecordBusinessApi.deleteSearchRecord(searchRecordBusinessDTO);
            return this.addResult();
        } catch (Exception e) {
            LOGGER.error("删除搜索记录异常-del",e);
            return this.addError("删除搜索记录失败！");
        }
    }


}
