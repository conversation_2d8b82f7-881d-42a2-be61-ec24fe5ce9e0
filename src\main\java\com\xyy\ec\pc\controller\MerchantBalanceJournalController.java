package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.enumcode.ApiResultExceptionTypeEum;
import com.xyy.ec.merchant.bussiness.api.BalanceBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBalanceJournalBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBankinfoBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.BalanceBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBalanceJournalBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBankinfoBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.enums.AuditingStatusEnum;
import com.xyy.ec.merchant.bussiness.excetion.XyyEcMemberBizNoneCheckRTException;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.constants.CashStatusEnum;
import com.xyy.ec.pc.constants.CodeItemConstants;
import com.xyy.ec.pc.constants.CodeMapConstants;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.system.business.dto.CodeitemBusinessDto;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Title: PC 提现相关
 * @Description:
 * @date 2018/4/13 11:15
 */
@Controller
@RequestMapping("/merchant/center/MerchantBalanceJournal")
public class MerchantBalanceJournalController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantBalanceJournalController.class);

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private MerchantBalanceJournalBussinessApi merchantBalanceJournalBussinessApi;
    @Reference(version = "1.0.0")
    private MerchantBankinfoBussinessApi merchantBankinfoBussinessApi;
    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;
    @Reference(version = "1.0.0")
    private BalanceBussinessApi  balanceBussinessApi;

    /**
     * @param merchantId  提现功能已关闭
     * @return java.lang.Object
     * @Description : 提现检查
     * @throws
     * <AUTHOR>
     * @date 2018/4/13 16:17
     */
    @RequestMapping(value = "/check.json", method = RequestMethod.GET)
    @ResponseBody
    public Object check(Long merchantId){
        Map<String,Object> model = new HashMap<>();
        if(merchantId == null){
            LOGGER.error("会员id不能为空");
            return this.addError("非法会员");
        }
        try {
            MerchantBussinessDto merchant = merchantBussinessApi.selectByPrimaryKey(merchantId);
            if(merchant == null){
                LOGGER.error("非法用户");
                throw new XyyEcMemberBizNoneCheckRTException(ApiResultExceptionTypeEum.UN_NEED_CHECK.getCode(),"非法用户",null);
            }
            //1: 查询资质和银行卡状态
//            MerchantBankinfoBussinessDto entity = new MerchantBankinfoBussinessDto();
//            entity.setMerchantId(merchantId);
//            List<MerchantBankinfoBussinessDto> merchantBankinfoList = merchantBankinfoBussinessApi.selectList(entity);

            //2: 判断资质
//            String branchCode = merchant.getRegisterCode();
//            Map<String, CodeitemBusinessDto> codeItem = codeItemServiceRpc.selectByCodemapRTMap(CodeMapConstants.APTITUDE_CONFIG,branchCode);

            //2.1用户资质未认证
//            if(CollectionUtils.isEmpty(merchantBankinfoList)){
//                model.put("result", CodeItemConstants.BEGIN_APTITUDE);
//                return model;
//            }

            //2.2用户资质未通过审核
//            MerchantBankinfoBussinessDto merchantBankinfo = merchantBankinfoList.get(0);
//            int status = merchantBankinfo.getStatus().intValue();
//            model.put("merchantBankinfoId", merchantBankinfo.getId());
//            if(AuditingStatusEnum.LICENSE_STATUS_AUDIT_DOES_NOT_PASS.getId() == status){
//                model.put("result", CodeItemConstants.AGAIN_APTITUDE);
//                return model;
//            }

            //2.3正在审核中
//            if(AuditingStatusEnum.LICENSE_STATUS_PENDING_AUDIT.getId() == status){
//                model.put("result", CodeItemConstants.WAITING_APTITUDE);
//                return model;
//            }

            //2.4 成功时的跳转
            model.put("result", "SUCCESS");
            return model;
        }catch (Exception e){
            model.put("result", "ERROR");
            LOGGER.error("提现检查异常,e="+e);
            return model;
        }
//
    }


    /**
     * @param merchantId 提现功能已关闭
     * @return java.lang.Object
     * @Description: 提现初始化
     * @throws
     * <AUTHOR>
     * @date 2018/4/16 14:40
     */
    @RequestMapping("/initMyCashwithdrawal")
    @ResponseBody
    public Object initMyCashwithdrawal(Long merchantId, HttpServletRequest request) {
//        Map<String,Object> model = new HashMap<>();
//        if(merchantId == null){
//            LOGGER.error("会员id不能为空");
//            return this.addError("非法会员");
//        }
        try {
//            MerchantBussinessDto merchant = merchantBussinessApi.selectByPrimaryKey(merchantId);
//            if(merchant == null){
//                LOGGER.error("非法用户");
//                throw new Exception("非法用户");
//            }
//            // 可用余额
//            BalanceBussinessDto balancetmp   = balanceBussinessApi.selectByMerchant(merchantId);
//            double balancemoney = 0;
//            if (balancetmp!=null) {
//                balancemoney = balancetmp.getBalance()==null?0:balancetmp.getBalance().doubleValue();
//            }
//
//            MerchantBankinfoBussinessDto entity = new MerchantBankinfoBussinessDto();
//            entity.setMerchantId(merchantId);
//            List<MerchantBankinfoBussinessDto>  merchantBankinfoList = merchantBankinfoBussinessApi.selectList(entity);
//            if(CollectionUtils.isEmpty(merchantBankinfoList)){
//                LOGGER.error("该用户没有银行卡信息");
//                throw new Exception();
//            }
//            MerchantBankinfoBussinessDto merchantBankinfo = merchantBankinfoList.get(0);
//            model.put("balancemoney", balancemoney);
//            model.put("merchantId", merchantId);
//            model.put("bankName", merchantBankinfo.getBankName());
//            model.put("cardNo", merchantBankinfo.getCardNo().subSequence(merchantBankinfo.getCardNo().length() - 4, merchantBankinfo.getCardNo().length()));
//            return new ModelAndView("/balanceJournal/myCashwithdrawal.ftl",model);
        return null;
        }catch (Exception e){
            LOGGER.error("提现初始化异常,e="+e);
            return this.addError("提现初始化异常");
        }
    }


    /**
     * 确认提现 - 提现关闭
     * @param merchantBalanceJournal
     * @return
     */
    @RequestMapping(value = "/submit.json", method = RequestMethod.POST)
    @ResponseBody
    public Object sumit(MerchantBalanceJournalBussinessDto merchantBalanceJournal, HttpServletRequest request) {
        Map<String,Object> model = new HashMap<>();
        Long merchantId = merchantBalanceJournal.getMerchantId();
        if(merchantId == null){
            model.put("result", "FAIL");
            model.put("msg", "会员id不能为空");
            return model;
        }
        BigDecimal balanceJournal = merchantBalanceJournal.getBalanceJournal();
        if(balanceJournal == null){
            model.put("result", "FAIL");
            model.put("msg", "提现金额不能为空");
            return model;
        }

        if(balanceJournal.compareTo(BigDecimal.ZERO) < 1){
            model.put("result", "FAIL");
            model.put("msg", "提现金额必须大于0");
            return model;
        }

        if(balanceJournal.doubleValue() > balanceJournal.intValue()){
            model.put("result", "FAIL");
            model.put("msg", "提现金额必须是整数");
            return model;
        }

        //让字符串进行Bigdecimal（安全）
        if(balanceJournal.compareTo(new BigDecimal(String.valueOf(Constants.IS100))) < 0){
            model.put("result", "FAIL");
            model.put("msg", "提现金额必须大于等于100");
            return model;
        }

        try {
            MerchantBussinessDto merchant = merchantBussinessApi.selectByPrimaryKey(merchantId);
            if(merchant == null){
                model.put("result", "FAIL");
                model.put("msg", "非法用户");
                return model;
            }
            //1: 判断资质和银行卡状态是否正确
            MerchantBankinfoBussinessDto entity = new MerchantBankinfoBussinessDto();
            entity.setMerchantId(merchantId);
            List<MerchantBankinfoBussinessDto>  merchantBankinfoList = merchantBankinfoBussinessApi.selectList(entity);
            if(CollectionUtils.isEmpty(merchantBankinfoList)){
                model.put("result", "FAIL");
                model.put("msg", "该用户没有银行卡信息");
                return model;
            }
            MerchantBankinfoBussinessDto merchantBankinfo = merchantBankinfoList.get(0);

            if(AuditingStatusEnum.LICENSE_STATUS_AUDIT_PASS.getId()
                    != merchantBankinfo.getStatus().intValue()){
                model.put("result", "FAIL");
                model.put("msg", "用户资质未通过审核");
                return model;
            }
            if(Constants.IS1.equals(merchantBankinfo.getBankCardStatus()) == false){
                model.put("result", "FAIL");
                model.put("errorType", "CARDNO_ERROR");
                model.put("msg", "您的银行卡信息有误，请前往修改");
                return model;
            }


            //2: 判断余额信息是否满足提现金额
//            BalanceBussinessDto balance = balanceBussinessApi.selectByMerchant(merchantId);
//
//            if(balance.getBalance().compareTo(balanceJournal) == -1){
//                model.put("result", "FAIL");
//                model.put("msg", "提现金额大于账户所剩余额，不能提现");
//                return model;
//            }
            //2: 插入 提现流水 和 扣减用用户余额
//            merchantBalanceJournal.setSysUserId(merchant.getMerchantId());
//            merchantBalanceJournal.setSysUserName(merchant.getMobile());
//            merchantBalanceJournalBussinessApi.insertAndUpdateBalance(merchantBankinfo,merchantBalanceJournal);
            model.put("result", "SUCCESS");
            return model;

        } catch (Exception e) {
            LOGGER.error("提交异常", e);
            model.put("msg", "提交异常");
            return model;
        }
    }


    @RequestMapping("/submitSuccess")
    @ResponseBody
    public Object submitSuccess() {
        return new ModelAndView("/balanceJournal/myconfirmsubmission.ftl");
    }

    /**
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @Description: 查询余额流水信息
     * @throws
     * <AUTHOR>
     * @date 2018/4/16 16:12
     */
    @RequestMapping(value = "/queryMerchantBalanceJournal.htm", method = RequestMethod.GET)
    public ModelAndView queryMerchantBalanceJournal(HttpServletRequest request) {
        Map<String,Object> model = new HashMap<>();
        try {
            Long merchantId = NumberUtils.toLong(request.getParameter("merchantId"));
            if(merchantId == null){
                LOGGER.error("会员id不能为空");
                return new ModelAndView("/balanceJournal/myWithdrawalsrecord.ftl",model);
            }

            Page page = new Page();
            page.setLimit(10);
            int offset;
            String offsetStr = request.getParameter("offset");
            if (StringUtil.isEmpty(offsetStr)) {
                offset = 0;
            } else {
                offset = NumberUtils.toInt(offsetStr);
            }
            page.setOffset(offset);
            page.setOffset(page.getOffset() > 0 ? (page.getOffset() - 1) : 0);
            //从缓存取
            MerchantBussinessDto merchant = merchantBussinessApi.findMerchantById(merchantId);
            if(merchant == null){
                LOGGER.error("非法用户");
                return new ModelAndView("/balanceJournal/myWithdrawalsrecord.ftl",model);
            }
            MerchantBalanceJournalBussinessDto entity = new MerchantBalanceJournalBussinessDto();
            entity.setMerchantId(merchantId);
            Page pageResult =new Page<>();
            PageInfo<MerchantBalanceJournalBussinessDto> pageInfo = merchantBalanceJournalBussinessApi.selectPageList(page.getOffset(), page.getLimit(), entity);
            setResultPage(pageResult,pageInfo);
            String requestUrl = this.getRequestUrl(request);
            pageResult.setRequestUrl(requestUrl);
            pageResult.setOffset(offset);
            if (CollectionUtil.isNotEmpty(pageResult.getRows())){
                pageResult.getRows().forEach(merchantBalanceJournal -> {
                    MerchantBalanceJournalBussinessDto tmp = (MerchantBalanceJournalBussinessDto)merchantBalanceJournal;
                    if (tmp.getStatus() == CashStatusEnum.HANDLING.getId()) {
                        tmp.setStatusName(CashStatusEnum.HANDLING.getValue());
                    } else if (tmp.getStatus() == CashStatusEnum.SUCCESS.getId()) {
                        tmp.setStatusName(CashStatusEnum.SUCCESS.getValue());
                    } else if (tmp.getStatus() == CashStatusEnum.FAIL.getId()) {
                        tmp.setStatusName(CashStatusEnum.FAIL.getValue());
                    }
                });
            }
            model.put("pager", pageResult);
            return new ModelAndView("/balanceJournal/myWithdrawalsrecord.ftl",model);
        } catch (Exception e) {
            LOGGER.error("PC查询余额流水明细异常");
        }
        return new ModelAndView("/balanceJournal/myWithdrawalsrecord.ftl",model);
    }

}
