package com.xyy.ec.pc.constants;

import java.util.HashMap;
import java.util.Map;

public enum CashStatusEnum {

    /*1-待打款，2-打款成功，3-打款失败*/
	HANDLING(1,"处理中"),
	SUCCESS(2,"提现成功"),
	FAIL(3,"提现失败");
	
    private int id;
    private  String value;

    CashStatusEnum(int id, String value){
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, CashStatusEnum> enumMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(CashStatusEnum e : CashStatusEnum.values()) {
        	enumMaps.put(e.getId(), e);
            maps.put(e.getId(),e.getValue());
        }
    }

    public static String get(int id) {
        return enumMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
    
}
