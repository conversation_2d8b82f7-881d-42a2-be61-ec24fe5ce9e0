<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.xyy.ec.pc</groupId>
	<artifactId>xyy-ec-pc</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>xyy-ec-pc</name>
	<description>Demo project for Spring Boot</description>


	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>1.5.10.RELEASE</version>
		<relativePath />
	</parent>


	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<dubbo.spring.boot.version>0.1.0</dubbo.spring.boot.version>
		<com.github.pagehelper.version>1.2.3</com.github.pagehelper.version>
		<zkclient.version>0.10</zkclient.version>
<!--		<poi.version>4.0.0</poi.version>-->
		<poi.version>3.9</poi.version>
		<xmlbeans.version>2.3.0</xmlbeans.version>
		<taobao.auto.sdk.version>20170210</taobao.auto.sdk.version>
		<xyy-ec-shop-server-api.version>1.0.0-SNAPSHOT</xyy-ec-shop-server-api.version>
		<rpc.user>1.0-SNAPSHOT</rpc.user>
		<rpc.order>1.0-SNAPSHOT</rpc.order>
		<rpc.hyperspace>1.2.0-SNAPSHOT</rpc.hyperspace>
		<dubbo.client.version>3.0</dubbo.client.version>
	</properties>


	<dependencies>

<!--		<dependency>-->
<!--			<artifactId>xyy-ec-pop-server-api</artifactId>-->
<!--			<groupId>xyy-ec-pop-server-api</groupId>-->
<!--			<version>1.0-SNAPSHOT</version>-->
<!--		</dependency>-->

		<dependency>
			<groupId>com.xyy.ec.exp.server</groupId>
			<artifactId>xyy-ec-exp-server-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.xyy.saas.payment.core</groupId>
			<artifactId>xyy-saas-payment-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.tencentcloudapi</groupId>
			<artifactId>tencentcloud-sdk-java</artifactId>
			<!-- go to https://search.maven.org/search?q=tencentcloud-sdk-java and get the latest version. -->
			<!-- 请到https://search.maven.org/search?q=tencentcloud-sdk-java查询最新版本 -->
			<version>3.0.93</version>
		</dependency>
		<dependency>
			<groupId>com.xyy.xtools</groupId>
			<artifactId>xkit</artifactId>
			<version>1.0.2-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.xyy.cat</groupId>
			<artifactId>xyy-common-filter</artifactId>
			<version>3.1.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>log4j-over-slf4j</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.6.2</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.3</version>
        </dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>1.8.13</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.8.13</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>dubbo-spring-boot-starter</artifactId>
			<version>${dubbo.spring.boot.version}</version>
		</dependency>
		<dependency>
			<groupId>com.101tec</groupId>
			<artifactId>zkclient</artifactId>
			<version>${zkclient.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>

		<!--电子资质平台-->
		<dependency>
			<groupId>com.xyy.electron.data.bussiness</groupId>
			<artifactId>xyy-electron-data-api</artifactId>
			<version>1.0-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>

        <!-- 会员 -->
        <dependency>
            <groupId>com.xyy.ec.merchant.bussiness</groupId>
            <artifactId>xyy-ec-merchant-bussiness-api</artifactId>
            <version>${rpc.user}</version>
        </dependency>
		<dependency>
			<groupId>com.xyy.ec.merchant.server</groupId>
			<artifactId>xyy-ec-merchant-server-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion></exclusions>
		</dependency>
        <!-- 系统服务 -->
        <dependency>
            <groupId>com.xyy.ec.system.business</groupId>
            <artifactId>xyy-ec-system-business-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!--订单服务-->
        <dependency>
            <groupId>com.xyy.ec.order.business</groupId>
            <artifactId>xyy-ec-order-business-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
		<dependency>
			<groupId>com.xyy.ec.order.server</groupId>
			<artifactId>xyy-ec-order-server-api</artifactId>
			<version>1.0-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>
		<!-- KA server -->
		<dependency>
			<groupId>com.xyy.ec.ka.server</groupId>
			<artifactId>xyy-ec-ka-server-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
        <!-- 系统服务 -->
        <dependency>
            <groupId>com.xyy.ec.product.business</groupId>
            <artifactId>xyy-ec-product-business-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- 活动 -->
        <dependency>
            <groupId>com.xyy.ec.promotion.business</groupId>
            <artifactId>xyy-ec-promotion-business-api</artifactId>
            <version>1.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>cn.afterturn</groupId>
					<artifactId>easypoi-web</artifactId>
				</exclusion>
				<exclusion>
					<groupId>cn.afterturn</groupId>
					<artifactId>easypoi-annotation</artifactId>
				</exclusion>
				<exclusion>
					<groupId>cn.afterturn</groupId>
					<artifactId>easypoi-base</artifactId>
				</exclusion>
			</exclusions>
        </dependency>
        <!--布局-->
        <dependency>
            <groupId>com.xyy.ec.layout.buinese</groupId>
            <artifactId>xyy-ec-layout-buinese-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
		<!--店铺-->
		<dependency>
			<groupId>com.xyy.ec.shop.server.business</groupId>
			<artifactId>xyy-ec-shop-server-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
			<scope>compile</scope>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.xyy.ec.pop</groupId>-->
<!--			<artifactId>xyy-ec-pop-server-api</artifactId>-->
<!--			<version>1.0-SNAPSHOT</version>-->
<!--		</dependency>-->

		<!--促销-->
		<dependency>
			<groupId>com.xyy.ec.marketing.hyperspace</groupId>
			<artifactId>xyy-ec-marketing-hyperspace-api</artifactId>
			<version>${rpc.hyperspace}</version>
		</dependency>
		<dependency>
			<groupId>com.xyy.ec.marketing-client</groupId>
			<artifactId>xyy-ec-marketing-client</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<!--推荐系统api -->
		<dependency>
			<groupId>com.xyy.recommend</groupId>
			<artifactId>xyy-ec-product-alsolike-api</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<artifactId>xyy-crm-operation-service-api</artifactId>
			<groupId>com.xyy</groupId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.3</version>
		</dependency>
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
			<version>0.4.5</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.4</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/commons-fileupload/commons-fileupload -->
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.3</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.apache.poi</groupId>-->
<!--			<artifactId>poi</artifactId>-->
<!--			<version>3.9</version>-->
<!--		</dependency>-->
				<!-- https://mvnrepository.com/artifact/org.jdom/jdom -->
		<dependency>
			<groupId>org.jdom</groupId>
			<artifactId>jdom</artifactId>
			<version>1.1.3</version>
		</dependency>
		<dependency>
			<groupId>com.xyy</groupId>
			<artifactId>qrCode</artifactId>
			<version>qrCode1.1</version>
		</dependency>
		<dependency>
			<groupId>com.unionpay.acp</groupId>
			<artifactId>core.api</artifactId>
			<version>1.0.0</version>
		</dependency>

		<!-- poi工具类 start -->
<!--		<dependency>-->
<!--			<groupId>org.apache.poi</groupId>-->
<!--			<artifactId>poi</artifactId>-->
<!--			<version>${poi.version}</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.poi</groupId>-->
<!--			<artifactId>poi-scratchpad</artifactId>-->
<!--			<version>${poi.version}</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.poi</groupId>-->
<!--			<artifactId>poi-ooxml</artifactId>-->
<!--			<version>${poi.version}</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.apache.xmlbeans</groupId>-->
<!--			<artifactId>xmlbeans</artifactId>-->
<!--			<version>${xmlbeans.version}</version>-->
<!--		</dependency>-->
		<!-- poi工具类 end -->

		<dependency>
			<groupId>net.sourceforge.jexcelapi</groupId>
			<artifactId>jxl</artifactId>
			<version>2.5.7</version>
		</dependency>
		<dependency>
			<groupId>net.sf.jxls</groupId>
			<artifactId>jxls-core</artifactId>
			<version>1.0.6</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- 即时通讯工具包(阿里百川) -->
		<dependency>
			<groupId>fakepath</groupId>
			<artifactId>taobao-sdk-java-auto</artifactId>
			<version>${taobao.auto.sdk.version}</version>
		</dependency>
		<!--第三方服务(时空、神农)-->
		<dependency>
			<groupId>com.xyy.ec.third</groupId>
			<artifactId>xyy-ec-third-api</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>net.oschina.zcx7878</groupId>
			<artifactId>fastdfs-client-java</artifactId>
			<version>1.27.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.4.2</version>
		</dependency>
		<dependency>
			<groupId>com.xyy.apollo</groupId>
			<artifactId>xyy-apollo-client</artifactId>
			<version>1.3.0</version>
		</dependency>
		<dependency>
			<groupId>com.xyy.ec.cs</groupId>
			<artifactId>xyy-ec-cs-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.10</version>
        </dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>com.xyy.framework</groupId>
			<artifactId>xyy-redis-spring-boot-starter</artifactId>
			<version>1.0.0-spring-data-SNAPSHOT</version>
		</dependency>

        <dependency>
            <groupId>com.xyy.ec.base.framework</groupId>
            <artifactId>xyy-ec-base-framework</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

	   <dependency>
			<groupId>com.xyy.ec.base.framework</groupId>
			<artifactId>xyy-ec-base-framework</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>

        <!-- 搜索api -->
        <dependency>
            <groupId>com.xyy.ec.search</groupId>
            <artifactId>xyy-ec-search-engine-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
		<dependency>
			<groupId>com.xyy.ec.marketing.common</groupId>
			<artifactId>xyy-ec-marketing-component</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
		</dependency>
		<dependency>
			<groupId>com.xyy.chaos</groupId>
			<artifactId>xyy-chaos-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

        <dependency>
            <groupId>com.xyy.ec.data</groupId>
            <artifactId>xyy-ec-data-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xyy.ec.marketing.nine.chapters</groupId>
            <artifactId>xyy-ec-marketing-nine-chapters-api</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>

        <!--后端埋点系统API-->
        <dependency>
            <groupId>com.xyy.ec.ai</groupId>
            <artifactId>snow-ground</artifactId>
            <version>1.5.10-SNAPSHOT-UAP</version>
        </dependency>

<!--		<dependency>-->
<!--			<groupId>com.xyy.ec.hack.server</groupId>-->
<!--			<artifactId>xyy-ec-hack-server-api</artifactId>-->
<!--			<version>1.0-SNAPSHOT</version>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<groupId>org.apache.poi</groupId>-->
<!--					<artifactId>poi-ooxml</artifactId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<groupId>org.apache.poi</groupId>-->
<!--					<artifactId>poi</artifactId>-->
<!--				</exclusion>-->
<!--				<exclusion>-->
<!--					<groupId>org.apache.poi</groupId>-->
<!--					<artifactId>poi-ooxml-schemas</artifactId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--		</dependency>-->
		<!--POP业务API-->
		<dependency>
			<groupId>com.xyy.ec.pop</groupId>
			<artifactId>xyy-ec-pop-server-api</artifactId>
			<version>1.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>easyexcel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>cn.afterturn</groupId>
					<artifactId>easypoi-annotation</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 平滑发布接入依赖dubbo-client-->
		<dependency>
			<groupId>com.xyy.common</groupId>
			<artifactId>xyy-common-dubbo-client</artifactId>
			<version>${dubbo.client.version}</version>
		</dependency>
		<!-- hutool 工具类 -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.7.22</version>
		</dependency>
		<dependency>
			<groupId>org.apache.skywalking</groupId>
			<artifactId>apm-toolkit-logback-1.x</artifactId>
			<version>8.9.0</version>
		</dependency>

		<dependency>
			<groupId>com.qcloud</groupId>
			<artifactId>cos_api</artifactId>
			<version>5.6.54</version>
		</dependency>
		<dependency>
			<groupId>com.xyy.ec.order.backend</groupId>
			<artifactId>xyy-ec-order-backend-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>easyexcel</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.0.5</version>
			<scope>compile</scope>
		</dependency>
		<!-- poi工具类 start -->
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-web</artifactId>
			<version>4.3.0</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml-schemas</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- poi工具类 end -->
		<!-- ec-common-filter -->
		<dependency>
			<groupId>com.xyy</groupId>
			<artifactId>ec-common-filter</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.googlecode.aviator</groupId>
			<artifactId>aviator</artifactId>
			<version>5.1.4</version>
		</dependency>
		<dependency>
			<groupId>com.xyy.ec.match.server</groupId>
			<artifactId>xyy-ec-match-server-api</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<!-- Token生成与解析-->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>0.9.1</version>
		</dependency>
		<!-- 解析客户端操作系统、浏览器等 -->
		<dependency>
			<groupId>eu.bitwalker</groupId>
			<artifactId>UserAgentUtils</artifactId>
			<version>1.21</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<!-- 接入Sentinel客户端依赖-->
		<dependency>
			<groupId>com.xyy.sentinel</groupId>
			<artifactId>xyy-sentinel-client</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<!-- 营销权益依赖 -->
		<dependency>
			<groupId>com.xyy.ec.marketing.interest</groupId>
			<artifactId>xyy-ec-marketing-interest-api</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<!-- 忽略datx文件编译，防止其损坏 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.0.2</version>
				<configuration>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>datx</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
	</build>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://maven.int.ybm100.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://maven.int.ybm100.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


</project>
