package com.xyy.ec.pc.newfront.service;

import com.xyy.ec.order.business.dto.ShoppingCartBusinessDto;
import com.xyy.ec.order.dto.cart.BatchSelectCartDto;
import com.xyy.ec.order.dto.cart.ChangeCartDto;
import com.xyy.ec.order.dto.cart.SelectCartDto;
import com.xyy.ec.pc.newfront.dto.CartRespVO;
import com.xyy.ec.pc.newfront.dto.ChangeCartRespVO;
import com.xyy.ec.pc.newfront.vo.CartParamVO;
import com.xyy.ec.pc.rest.AjaxResult;

import javax.servlet.http.HttpServletRequest;

public interface CartNewService {

    /**
     * 获取购物车信息
     * @param
     * @return AjaxResult
     */
    AjaxResult<CartRespVO> list();
    /**
     * 获取购物车商品数量
     *
     * @return AjaxResult
     */
    AjaxResult<Integer> getCartNum();

    /**
     * 修改购物车数量
     */
    AjaxResult<ChangeCartRespVO> changeCart(ShoppingCartBusinessDto cart);

    ChangeCartRespVO changeGroupCart(ChangeCartDto cart, HttpServletRequest  req);

    AjaxResult selectItem(SelectCartDto cart);

    AjaxResult cancelItem(SelectCartDto cart);

    AjaxResult selectAllItem(BatchSelectCartDto cart);

    AjaxResult cancelAllItem(BatchSelectCartDto cart);

    AjaxResult cleanCart();
}
