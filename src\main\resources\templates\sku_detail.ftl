<!DOCTYPE HTML>
<html>

	<head>
		<#include "/common/common.ftl" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<title>${detail.commonName}详情页</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
		<script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
		<script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
		<script type="text/javascript" src="/static/js/jquery.jqzoom.js"></script>
        <script type="text/javascript" src="/static/js/scroll.js"></script>
        <script type="text/javascript" src="/static/js/skuDetail.js?t=${t_v}"></script>
		<script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
		<script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>
		<script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/zhuge/zhugeio.js?t=${t_v}"></script>
			<script type="text/javascript" src="/static/js/qtShopDetail.js?t=${t_v}"></script>
		<link rel="stylesheet" href="/static/css/detail.css?t=${t_v}" />
		<script type="text/javascript">
            var ctx="${ctx}";

          function hasGoodsAlert(skuId,obj){
          	var merchantId = $('#merchantId').val();
			if(!merchantId || merchantId <=0 ){
				$.alert({
					title: '提示',
					body: '您还没有登录，请先登录!',
					okHidden : function(e){
						window.location.href="/login/login.htm?redirectUrl=/search/skuDetail/"+skuId+".htm";
					}
				});
			}
			else{
				$.ajax({
		        type: "POST",
		        url: "/merchant/merchantBusiness/subscribe/"+skuId+".json",
		        data: {},
		        dataType: "json",
		        success: function(result){
		        	 if(result.status == "success"){
		        		$.alert({
							title: '订阅成功',
							body: '<div style="text-align: center;line-height: 30px;color: #333; font-size: 14px;"><span class="sui-icon icon-tb-roundcheck" style="color: #3DBD7D;margin-right: 8px;font-size: 22px;vertical-align: bottom;"></span>设置到货提醒成功</div>\
							<p style="font-size: 14px;margin-top: 10px;line-height: 22px;">若该商品在45天内到货，药帮忙会提醒您！同时您可以在我的收藏夹查看您订阅过的所有商品</p>',
							okBtn : '确认',
							okHidden: function(e) {
								 window.location.reload(true);
							}
						});

		             }else {
		        		$.alert(result.errorMsg);
		             }
		         }
		     });
		  }
  		}
		</script>

		<style>


		</style>

		<!--[if IE 8]>

		<style>
			.typeItem.active::before{
                content: "";
			}
		</style>

		<![endif]-->

	</head>

	<body>
		<div class="container">
			<input type="hidden" id="productId" value="${detail.id}"/>
            <input type="hidden" id="branchCode" value="${detail.branchCode}"/>
			<input type="hidden" id="productShowName" value="${detail.showName}"/>
			<input type="hidden" id="detailFob" value="${detail.fob}"/>
			<input type="hidden" id="detailProductType" value="${detail.productType}"/>
			<input type="hidden" id="detailShopCode" value="${detail.shopCode}"/>
			<input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
            <input type="hidden" id="categoryId" name="categoryId" value="${detail.categoryId}"/>
            <input type="hidden" id="barcode" name="barcode" value="${detail.barcode}"/>
			<input type="hidden" id="sourceId" name="sourceId" value="${sourceId}"/>
            <input type="hidden" id="sptype" name="sptype" value="${sptype}">
            <input type="hidden" id="spid" name="spid" value="${spid}">
            <input type="hidden" id="sid" name="sid" value="${sid}"/>
			<input type="hidden" id="temp1" name="temp1" value="${detail.showAgree}">
            <input type="hidden" id="temp2" name="temp2" value="${detail.isOEM}">
            <input type="hidden" id="temp3" name="temp3" value="${detail.signStatus}"/>
            <input type="hidden" id="real" name="real" value="2"/>
			<input type="hidden" id="pigou" name="pigou" value="${isWholesale}"/>
			<input type="hidden" id="mediumPackage" name="mediumPackage" value="${detail.mediumPackage}"/>
			<input type="hidden" id="cnProductCode" name="cnProductCode" value="${detail.cnProductCode}"/>
			<input type="hidden" id="isThirdCompany" name="isThirdCompany" value="${detail.isThirdCompany}"/>
			<#if actPt??>
				<input type="hidden" id="supportSuiXinPin" name="supportSuiXinPin" value="${actPt.supportSuiXinPin}"/>
				<#elseif actPgby??>
				<input type="hidden" id="supportSuiXinPin" name="supportSuiXinPin" value="${actPgby.supportSuiXinPin}"/>
				<#else>
			</#if>
			
			<!--头部导航区域开始-->
			<div class="headerBox" id="headerBox">
				<#include "/common/header.ftl" />
			</div>
			<!--头部导航区域结束-->

			<!--主体部分开始-->
			<div class="main">

				<!--面包屑导航-->
				<div id ="productRelation" class="mb-box">
				</div>

				<!--商品图片-->
				<div class="d-mainbox">
					<div class="d-lbox fl">
						<div id="preview" class="spec-preview">
							<span class="jqzoom">
								<img id="detail_${detail.id}" class="sptp" jqimg="${productImageUrl}/ybm/product/${detail.imageUrl}" src="${productImageUrl}/ybm/product/${detail.imageUrl}" alt="" onerror="this.src='/static/images/default-big.png'">
								<!--标签-->
								<!--(detail.availableQty <= 0) || detail.status == 2 ||  (detail.isSplit == 0 && detail.availableQty - detail.mediumPackageNum lt 0)-->
								<div class="bq-box">
									<#if detail.status == 4>
										<img src="/static/images/product/bq-xiajia.png" alt="">
									<#elseif (detail.status == 2 || detail.availableQty <= 0)>
										<img src="/static/images/product/bq-shouqing.png" alt="">
									</#if>
								</div>
								<!-- 水印 -->
								<div class="xq-shuiyin ${detail.status} t">
									<img src="/static/images/shuiyin.png" alt="">
								</div>
								<div class="yaokuanghuan-pos ">
									<!--药狂欢角标 默认隐藏 去掉noshow显示-->
									<#if detail.markerUrl?length gt 5>
										<div class="yaokuanghuan-pos">
									<#else>
										<div class="yaokuanghuan-pos noshow">
									</#if>
									<#if (detail.markerUrl ? exists && detail.markerUrl!= '')>
			                        	<img src="${productImageUrl}/${detail.markerUrl}" alt="">
									</#if>

									</div>
								</div>
								<#if detail.activityTag != null && detail.activityTag.tagNoteBackGroupUrl != ''>
									<#if detail.activityTag.sourceType == 2>
										<div class="shop-activity-tag w290">
											<img src="${detail.activityTag.tagNoteBackGroupUrl}" alt="">
											<span class="top-box">${detail.activityTag.customTopNote}</span>
											<span class="time-box">${detail.activityTag.timeStr}</span>
											<div class="price-box">
												<#list detail.activityTag.skuTagNotes as skuTagNote>
													<span>${skuTagNote.text}</span>
												</#list>
											</div>
											<span class="bottom-box">${detail.activityTag.customBottomNote}</span>
										</div>
									<#else>
										<div class="yaokuanghuan-pos">
											<img src="${productImageUrl}/${detail.activityTag.tagNoteBackGroupUrl}" alt="">
											<div class="acTime" style="left: 7px;position: absolute;bottom: 82px;text-align: center;width:356px;height:26px;">
												<span style="color: #ffffff;font-size: 14px;">${detail.activityTag.timeStr}</span>
											</div>
											<div class="tejia806">
												<#list detail.activityTag.skuTagNotes as skuTagNote>
													<span class="price806" style="color: #${skuTagNote.textColor}">${skuTagNote.text}</span>
												</#list>
											</div>
										</div>
									</#if>
								</#if>

								<!--不参与返点提示-->
								<#--<#if (detail.blackProductText)!>-->
									<#--<div class="nofd">-->
										<#--${detail.blackProductText}-->
									<#--</div>-->
								<#--</#if>-->
								<!--end-->
							</span>
						</div>

						<!--缩图开始-->
						<#if detail.imagesList ?? && (detail.imagesList?size > 1) >
							<div class="spec-scroll"> <a class="prev"><img src="/static/images/d-left.png" ></a> <a class="next"><img src="/static/images/d-right.png" ></a>
								<div class="items">
									<ul>
										<#assign picIndex=0 />
										<#list detail.imagesList as imgUrl>
											<#if picIndex == 0 || (detail.isThirdCompany ?? && detail.isThirdCompany == 1)>
												<li><img  bimg="${productImageUrl}/ybm/product/${imgUrl}" src="${productImageUrl}/ybm/product/${imgUrl}" onmousemove="preview(this);"></li>
											<#else>
												<li><img  bimg="${productImageUrl}/ybm/product/pic/${imgUrl}" src="${productImageUrl}/ybm/product/pic/${imgUrl}" onmousemove="preview(this);"></li>
											</#if>
											 <#assign picIndex=picIndex+1 />
										</#list>
									</ul>
								</div>
							</div>
						</#if>
					</div>
					<div class="d-rbox fl">
                        <div class="title">
                            <span class="tit" style="display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;text-overflow: ellipsis;line-height:1.3">
								<#if detail.activityTag != null && detail.activityTag.tagUrl != ''>
									<div class="bq806">
										<img src="${productImageUrl}/${detail.activityTag.tagUrl}" alt="">
									</div>
								</#if>
								<#if detail.agent == 1>
									<span class="dujia">独家</span>
								</#if>
								<#--  <#if detail.isUsableMedicalStr ?? && detail.isUsableMedicalStr == 1>
									<span class="yibao">国家医保</span>
								</#if>
								<#if detail.drugClassification == 1>
									<span class="oc-icon otcr-icon"></span>
								<#elseif detail.drugClassification == 2>
									<span class="oc-icon otcg-icon"></span>
								<#elseif detail.drugClassification == 3>
									<span class="oc-icon rx-icon"></span>
								</#if>  -->
								${detail.showName}
							</span>
                        </div>
                        <div class="info">
								${detail.description}
							<#if detail.descriptions ?? && detail.descriptions != '' >
								<input type="hidden" id="isNewTab" value="${detail.isNewTab}">
                                <input type="hidden" id="descriptionPCUrl" value="${detail.descriptionPcUrl}">
								<a href="javascript:void(0);" onclick="jumpUrl()" class="gomore">${detail.descriptions}</a>
							</#if>
                        </div>
						<#if (detail.promotionEndTime?exists) && detail.promotionEndTime?datetime gt .now >
								<input type="hidden" id="endCxTime" value="${detail.promotionEndTime?string('yyyy-MM-dd HH:mm:ss')}" />
								<div class="xscxbox" id="timer" from="huo" data-timer="${detail.promotionEndTimeStamp}">
                                    <span class="xscxspan">限时促销</span>
                                    <span class="only">距离结束仅剩：</span>
                                    <span class="f24" id="timer_d">4</span>
                                    <span class="f18">天</span>
                                    <span class="f24" id="timer_h">3</span>
                                    <span class="f18">时</span>
                                    <span class="f24" id="timer_m">32</span>
                                    <span class="f18">分</span>
                                    <span class="f24" id="timer_s">58</span>
                                    <span class="f18">秒</span>
                                </div>
						</#if>
						<#if actSk??>
							<div class="xscxbox" id="timer" from="huo" data-timer="${actSk.surplusTime}">
								<span class="xscxspan">限时促销</span>
								<#if actSk.status==1>
									<span class="only">距离结束仅剩：</span>
									<#elseif actSk.status==0>
										<span class="only">距离开始仅剩：</span>
								</#if>
								<span class="f24" id="timer_d" style="display: none">4</span>
								<span class="f18" style="display: none">天</span>
								<span class="f24" id="timer_h">3</span>
								<span class="f18">时</span>
								<span class="f24" id="timer_m">32</span>
								<span class="f18">分</span>
								<span class="f24" id="timer_s">58</span>
								<span class="f18">秒</span>
							</div>
						</#if>
<#--						<#if detail.seckillEndTime?? && detail.seckillEndTime?datetime gt .now >-->
<#--							<div class="xscxbox" id="timer" from="huo" data-timer="${detail.seckillEndTime}">-->
<#--                                <span class="xscxspan">限时促销</span>-->
<#--                                <span class="only">距离结束仅剩：</span>-->
<#--                                <span class="f24" id="timer_d">4</span>-->
<#--                                <span class="f18">天</span>-->
<#--                                <span class="f24" id="timer_h">3</span>-->
<#--                                <span class="f18">时</span>-->
<#--                                <span class="f24" id="timer_m">32</span>-->
<#--                                <span class="f18">分</span>-->
<#--                                <span class="f24" id="timer_s">58</span>-->
<#--                                <span class="f18">秒</span>-->
<#--                            </div>-->
<#--						</#if>-->
						<#if detail.distanceEndTime?? && detail.distanceEndTime != 0 >
<#--							<input type="hidden" id="endCxTime" value="${detail.endTime?string('yyyy-MM-dd HH:mm:ss')}" />-->
							<div class="youxianbox" id="timer_you" from="you" data-timer="${detail.distanceEndTime}">
								<#if detail.merchantStatus?? && detail.merchantStatus == 1>
									<span class="xscxspan">智鹿用户专享时段</span>
								<#else>
									<span class="xscxspan">智鹿用户专享时段</span>
								</#if>
								<p class="timeP">
									<#if detail.merchantStatus?? && detail.merchantStatus == 1>
										<span class="only">距离结束</span>
									<#else>
										<span class="only">距离结束</span>
									</#if>
<#--									<span class="f24" id="timer_d">4</span>-->
<#--									<span class="f18">:</span>-->
									<span class="f24 ySpan" id="timer_h_you">00</span>
									<span class="f18">:</span>
									<span class="f24 ySpan" id="timer_m_you">00</span>
									<span class="f18">:</span>
									<span class="f24 ySpan" id="timer_s_you">00</span>
<#--									<span class="f18">:</span>-->
								</p>
							</div>
						</#if>
						<#--  加补  -->
						<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.endTime > 0)>
							<div class="addSubsidy">
								<div class="titlesDiv">
									<img class="titles" src="/static/img/events/addSubsidy.png"/>
								</div>
								<div class="timer" id="add_subsidy" from="you" data-timer="${detail.limitFullDiscountActInfo.endTime}">
									<p class="timeP">   
                                        <span class="f24 ySpan font" id="timer_h_you">00</span>
                                        <span class="f18">:</span>
                                        <span class="f24 ySpan font" id="timer_m_you">00</span>
                                        <span class="f18">:</span>
                                        <span class="f24 ySpan font" id="timer_s_you">00</span>
                                        <span class="only">后结束</span>
                                    </p>
								</div>
							</div>
						</#if>
						<input type="text" hidden value="${isAssemble}">
						<!--拼团-->
						<#if isAssemble == 1>
							<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.endTime > 0)>
								<div></div>
								<#else>
								<div class="pinGo" id="ptTimeShow" style="display: none;">
									<img class="titles" src="/static/img/events/pintuan.png"/>
									<div id="timer_you" class="ptEndTimestamp" style="display: inline-block;" from="you" data-timer="${actPt.surplusTime}">
										<p class="timeP">
											<#if actPt.assembleStatus == 1>
												<span class="only">距离结束仅剩</span>
											<#else>
												<span class="only">距离开始仅剩</span>
											</#if>
											<span class="f24" id="timer_d">4</span>
											<span class="f18" id="daySpan">天</span>
											<span class="f24 ySpan" id="timer_h_you">00</span>
											<span class="f18">:</span>
											<span class="f24 ySpan" id="timer_m_you">00</span>
											<span class="f18">:</span>
											<span class="f24 ySpan" id="timer_s_you">00</span>
											<#--									<span class="f18">:</span>-->
										</p>
									</div>
								</div>
							</#if>
                            
							<div class="section" style="background: #FFF9F2">
							<!--正常显示价格样式-->
							<#if (merchant ? exists)>
								<#if merchant.licenseStatus ==1 && detail.fob == '0'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">含税价认证资质后可见</span>
										</div>
									</div>
								<#elseif merchant.licenseStatus ==5 && detail.fob == '0'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">含税价认证资质后可见</span>
										</div>
									</div>
								<#elseif detail.controlType == '2'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">不在经营范围</span>
										</div>
									</div>
								<#elseif detail.agreementEffective?? && detail.agreementEffective =='3'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">协议已冻结，价格解冻后可见</span>
										</div>
									</div>
								<#elseif detail.isControl ==1 >
									<#if detail.isPurchase>
										<#if detail.priceType==1 >
											<div class="noqujian ">
												<div class="hprice">
													<span class="linfo">拼&nbsp;团&nbsp;&nbsp;价</span>
													<!--正常显示价格样式-->
													<span class="price" style="color: #FF0202;font-size: 28px;margin-left: 13px">
														<#if (actPt.assembleStatus?? && actPt.assembleStatus == 1) || (actPt.preheatShowPrice ?? && actPt.preheatShowPrice == 1)>
															<#-- 多阶梯拼团  -->
															<#if actPt.stepPriceStatus ?? && actPt.stepPriceStatus == 1>
																${actPt.minSkuPrice}
																<#else>
																	<#-- 单阶梯 -->
																	<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.limitFullDiscount > 0)>
																		${detail.limitFullDiscountActInfo.limitFullDiscount}
																	<#else>
																		${actPt.assemblePrice}
																	</#if>
															</#if>
														<#else>
															?
														</#if>

													</span>
													<!--限购提示-->
													<span style="color: #FF0202;font-size: 14px">
														<#if actPt.stepPriceStatus ?? && actPt.stepPriceStatus == 1>
															起
															<#else>
															元
														</#if>
														/${detail.productUnit}
													</span>
													<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.limitFullDiscount > 0)>
														<span style="margin-left: 2px;color: #F1081C;font-size: 12px">
															${detail.limitFullDiscountActInfo.text}
														</span>
													</#if>
													<#if detail.levelPriceDTO == null>
														<span class="zhehou-price-box">
															<span class="zhehou-price"></span>
															<a href="javascript:void(0)" class="get-intro" data-keyboard="false" style="display:none;"><img src="/static/images/zhehoujia-icon.png" alt="" style="top: -2px;position: relative;width: 14px;"></a>
														</span>
													</#if>
													<#if detail.unitPrice?? && detail.unitPrice != '' && ((actPt.assembleStatus?? && actPt.assembleStatus == 1) || (actPt.preheatShowPrice ?? && actPt.preheatShowPrice == 1))>
														<span style="color: #FF3535;font-size: 10px;display: inline-block;background-color: transparent;border: 1px solid #FF3535;padding: 0px 1px;border-radius: 3px;vertical-align: bottom;">${detail.unitPrice}</span>  
													</#if>
												</div>
											</div>
										<#else >
											<#if detail.skuPriceRangeList ??>
											<div class="hasqujian">
														<div class="hprice">
															<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
															<#list detail.skuPriceRangeList?reverse as priceReange>
																<!--正常显示价格样式-->
																<#if priceReange_has_next>
																	<span class="price qujian">￥${priceReange.price}<img src="/static/images/shuxian.png" class="shuxian"> </span>
																</#if>
																<#if !priceReange_has_next>
																	<span class="price qujian">￥${priceReange.price}</span>
																</#if>
															</#list>
														</div>
														<#if (detail.isControl ==1 && detail.isPurchase && detail.priceType==2) ||(detail.isControl !=1 && detail.priceType==2)>
															<div class="cgounum">
																<span class="linfo">采购量</span>
															<#list detail.skuPriceRangeList?reverse as priceReange>
																<#if !priceReange_has_next>
																	<span class="price"> ≥${priceReange.fromQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
																<#if priceReange_has_next>
																	<span class="price">${priceReange.fromQty}-${priceReange.toQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
															</#list>
															</div>
														</#if>
                                            </div>
											</#if>
										</#if>
									<#else>
										<div class="noqujian ">
											<div class="hprice">
												<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
												<!--正常显示价格样式-->
                                                <span class="noPermission">暂无购买权限</span>
											</div>
										</div>
									</#if>
								<#else>
									<#if detail.priceType==1 >
											<div class="noqujian ">
                                                <div class="hprice">
                                                    <span class="linfo">拼&nbsp;团&nbsp;&nbsp;价</span>
                                                    <!--正常显示价格样式-->
                                                    <span class="price" style="color: #FF0202;font-size: 28px;margin-left: 13px">
														<#if (actPt.assembleStatus?? && actPt.assembleStatus == 1) || (actPt.preheatShowPrice ?? && actPt.preheatShowPrice == 1)>
															<#-- 多阶梯拼团  -->
															<#if actPt.stepPriceStatus ?? && actPt.stepPriceStatus == 1>
																${actPt.minSkuPrice}
															<#else>
															<#-- 单阶梯 -->
																<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.limitFullDiscount > 0)>
																	${detail.limitFullDiscountActInfo.limitFullDiscount}
																<#else>
																	${actPt.assemblePrice}
																</#if>
															</#if>
														<#else>
															?
														</#if>
													</span>
													<span style="color: #FF0202;font-size: 14px">
														<#if actPt.stepPriceStatus ?? && actPt.stepPriceStatus == 1>
															起
														<#else>
															元
														</#if>
														/${detail.productUnit}
													</span>
													<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.limitFullDiscount > 0)>
														<span style="margin-left: 2px;color: #F1081C;font-size: 12px">
															${detail.limitFullDiscountActInfo.text}
														</span>
													</#if>
													<#if detail.levelPriceDTO == null>
														<span class="zhehou-price-box">
														<span class="zhehou-price"></span>
														<a href="javascript:void(0)" class="get-intro" data-keyboard="false" style="display:none;"><img src="/static/images/zhehoujia-icon.png" alt="" style="top: -2px;position: relative;width: 14px;"></a>
														</span>
                            </#if>
                            <#if detail.unitPrice?? && detail.unitPrice != '' && ((actPt.assembleStatus?? && actPt.assembleStatus == 1) || (actPt.preheatShowPrice ?? && actPt.preheatShowPrice == 1))>
                                <span style="color: #FF3535;font-size: 10px;display: inline-block;background-color: transparent;border: 1px solid #FF3535;padding: 0px 1px;border-radius: 3px;vertical-align: bottom;">${detail.unitPrice}</span>  
                            </#if>
                                                </div>                                            
                                            </div>
									<#else >
										<#if detail.skuPriceRangeList ??>
											<div class="hasqujian">
                                                <div class="hprice">
                                                    <span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
														<#list detail.skuPriceRangeList?reverse as priceReange>
																	<!--正常显示价格样式-->
															<#if priceReange_has_next>
																<span class="price qujian">￥${priceReange.price}<img src="/static/images/shuxian.png" class="shuxian"> </span>
															</#if>
															<#if !priceReange_has_next>
																<span class="price qujian">￥${priceReange.price}</span>
															</#if>
														</#list>
                                                </div>
												<#if (detail.isControl ==1 && detail.isPurchase && detail.priceType==2) ||(detail.isControl !=1 && detail.priceType==2)>
														<div class="cgounum">
                                                            <span class="linfo">采购量</span>
															<#list detail.skuPriceRangeList?reverse  as priceReange >
																<#if !priceReange_has_next>
																	<span class="price"> ≥${priceReange.fromQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
																<#if priceReange_has_next>
																	<span class="price">${priceReange.fromQty}-${priceReange.toQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
															</#list>
                                                        </div>
												</#if>
											</div>
										</#if>
									</#if>
								</#if>
							<#else>
								<div class="noqujian ">
                                    <div class="hprice">
                                        <span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
                                        <!--正常显示价格样式-->
                                        <span class="noPermission">价格登录可见</span>
                                    </div>
                                </div>
							</#if>




							<#if detail.actPurchaseTip ??>
								<div style="padding: 5px;border-radius: 5px;margin:3px 0 3px 92px;color:black;word-wrap:break-word;">${ detail.actPurchaseTip }</div>
							</#if>
							<#if detail.controlType == '5'>
								<p style="padding:3px;border: 1px solid #c76f15;border-radius:3px;color:#c76f15;margin:5px 0;">战略客户专享商品：协议签署即可采购，请联系区域业务经理</p>
							</#if>
                            <div class="yprice">
              <#if (merchant ? exists) && actPt.assembleStatus != 1>
								<#if merchant.licenseStatus ==1 && detail.retailPrice == '0'>

								<#elseif merchant.licenseStatus ==5 && detail.retailPrice == '0'>

								<#elseif detail.agreementEffective?? && detail.agreementEffective =='3'>

                                <#elseif detail.isOEM == '' || detail.isOEM == null || detail.isOEM != 'true' || detail.signStatus !='0'>
									<#if detail.showAgree !='0'>
										<#if detail.isControl ==1 >
											<#if detail.isPurchase>
												<#if (detail.uniformPrice ??) && (detail.uniformPrice != '')>
													<span class="ptit">控销零售价</span>
													<span class="pinfo" style="margin-left: 20px;">￥${detail.uniformPrice}</span>
												</#if>
												<#if (detail.suggestPrice ??) && (detail.suggestPrice != '') && (detail.suggestPrice > 0)>
													<#if detail.agreeSigneStatus == '' || detail.agreeSigneStatus == null||detail.agreeSigneStatus =='0'|| detail.agreeSigneStatus =='4'>
														<span class="linfo">建议零售价</span>
													<#else>
														<span class="linfo">统一零售价</span>
													</#if>
													<span class="pinfo" style="margin-left: 20px;">￥${detail.suggestPrice}</span>
												</#if>
											</#if>
										<#else>
											<#if (detail.uniformPrice ??) && (detail.uniformPrice != '')>
												<span class="ptit">控销零售价</span>
												<span class="pinfo" style="margin-left: 20px;">￥${detail.uniformPrice}</span>
											</#if>
											<#if (detail.suggestPrice ??) && (detail.suggestPrice != '') && (detail.suggestPrice > 0)>
												<#if detail.agreeSigneStatus == '' || detail.agreeSigneStatus == null||detail.agreeSigneStatus =='0'|| detail.agreeSigneStatus =='4'>
													<span class="linfo">建议零售价</span>
												<#else>
													<span class="linfo">统一零售价</span>
												</#if>
												<span class="pinfo" style="margin-left: 20px;">￥${detail.suggestPrice}</span>
											</#if>
										</#if>
									</#if>
                                </#if>
							</#if>
							<#if (merchant ? exists) && actPt.assembleStatus != 1>
                                <#if detail.isOEM == '' || detail.isOEM == null || detail.isOEM != 'true' || detail.signStatus !='0'>
									<#if detail.showAgree !='0'>
                                    	<#if detail.isControl ==1 >
                                        <#if detail.isPurchase>
                                            <#if (detail.grossMargin ??) && (detail.grossMargin != '')>
                                            <span class="ptit">毛利率</span>
                                            <span class="pinfo">${detail.grossMargin}</span>
                                            </#if>
                                        </#if>
                                    <#else>
                                        <#if (detail.grossMargin ??) && (detail.grossMargin != '')>
                                        <span class="ptit">毛利率</span>
                                        <span class="pinfo">${detail.grossMargin}</span>
                                        </#if>
                                    </#if>
									</#if>
								</#if>
							</#if>
							<#if detail.prescriptionPrice ?? && detail.prescriptionPrice != 0 && actPt.assembleStatus != 1>
                                <span class="ptit">医保价</span>
                                <span class="ptit" style="margin:0;">￥${detail.prescriptionPrice?string("0.00")}</span>
							</#if>
                            </div>
							<!--  次日达  -->
							<#if detail.nextDayServiceTagList ?? && (detail.nextDayServiceTagList?size >0) >
								<div class="cuxiaobox">
									<span class="linfo">配　　送</span>
									<#list detail.nextDayServiceTagList as item >
										<span class="next-day-service-tag" style="<#if item.bgColor??>background:${item.bgColor};</#if><#if item.borderColor??>border:1px solid ${item.borderColor};</#if>">
											<#if item.pcIcon??>
												<img src="${productImageUrl}${item.pcIcon}" 
													alt="${item.text!''}" 
													style="height: 12px; width: auto; object-fit: cover;display: inline-block;">
											</#if>
											<span style="<#if item.textColor??>color:${item.textColor};</#if>">
												${item.text!''}
											</span>
										</span>
										<span class="next-day-service-span" style="float: none !important">${item.description}</span>
									</#list>
								</div>
							</#if>
                            <!--优惠券-->
							<#if detail.isShowVoucher>
								<div class="youhuiquan test1">
									<span class="linfo">优&nbsp;惠&nbsp;&nbsp;券</span>
									<div class="quan-a-box">
										<#list detail.voucherTagList as vTag>
											<a href="javascript:void(0)" onclick="test()"  class="quan-a">${vTag}</a>
										</#list>
									</div>
								</div>
							</#if>
								<#if (merchant ? exists)>
								<#if detail.cxTagList ?? && (detail.cxTagList?size >0) >
									<div class="cuxiaobox">
										<span class="linfo">促　　销</span>
										<ul class="cuxiao-ul" id="cxUl">
											<#list detail.cxTagList as item >
												<#if detail.cxTagList?size == 1>
													<li>
														<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
														<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
														<#else>
															<#if item.name ?? && item.name == "整单包邮">
																<div class="synthesis">
																	<div class="synthesis-biao">
																		<span class="synthesis-biao-left">${item.name}</span>
																		<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																			<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																		</#if>
																		<#if item.description ?? && item.description != ''>
																			<span class="synthesis-biao-shuoming">${item.description}</span>
																		</#if>
																	</div>
																</div>
															<#elseif item.subType ?? && item.subType == 9>
																<span class="zhuanpin-biao 1">${item.name}</span>
															<#else>	
																<span class="biao 1">${item.name}</span>
															</#if>
														</#if>
														<#if item.name ?? && item.name != "整单包邮" && item.subType ?? && item.subType != 9>
															<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																<span class="shuoming">${item.cxtagDescription}</span>
															</#if>
															<#if item.description ?? && item.description != ''>
																<span class="shuoming">${item.description}</span>
															</#if>
														</#if>
														<#if item.pcURL ?? && item.pcURL != ''>
															<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
														</#if>
													</li>
												</#if>
												<#if detail.cxTagList?size == 2>
													<li id="desc${item_index}">
														<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
														<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
														<#else>
															<#if item.name ?? && item.name == "整单包邮">
																<div class="synthesis">
																	<div class="synthesis-biao">
																		<span class="synthesis-biao-left">${item.name}</span>
																		<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																			<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																		</#if>
																		<#if item.description ?? && item.description != ''>
																			<span class="synthesis-biao-shuoming">${item.description}</span>
																		</#if>
																	</div>
																</div>
															<#elseif item.subType ?? && item.subType == 9>
																<span class="zhuanpin-biao 2">${item.name}</span>
															<#else>	
																<span class="biao 2">${item.name}</span>
															</#if>
														</#if>
														<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
															<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																<span class="shuoming">${item.cxtagDescription}</span>
															</#if>
															<#if item.description ?? && item.description != ''>
																<span class="shuoming">${item.description}</span>
															</#if>
														</#if>
														<#if item.pcURL ?? && item.pcURL != ''>
															<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
														</#if>
													</li>
												</#if>
												<#if detail.cxTagList?size gt 2>
													<#if item_index == 0>
														<li class="text-overflow">
															<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
															<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
															<#else>
																<#if item.name ?? && item.name == "整单包邮">
																	<div class="synthesis">
																		<div class="synthesis-biao">
																			<span class="synthesis-biao-left">${item.name}</span>
																			<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																				<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																			</#if>
																			<#if item.description ?? && item.description != ''>
																				<span class="synthesis-biao-shuoming">${item.description}</span>
																			</#if>
																		</div>
																	</div>
																<#elseif item.subType ?? && item.subType == 9>
																	<span class="zhuanpin-biao 3">${item.name}</span>
																<#else>	
																	<span class="biao 3">${item.name}</span>
																</#if>
															</#if>
															<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
																<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																	<span class="shuoming">${item.cxtagDescription}</span>
																</#if>
																<#if item.description ?? && item.description != ''>
																	<span class="shuoming">${item.description}</span>
																</#if>
															</#if>
															<#if item.pcURL ?? && item.pcURL != ''>
																<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
															</#if>
														</li>
													</#if>
												</#if>
											</#list>
											<#if detail.cxTagList?size gt 2>
												<li class="sbzhan">
													<#list detail.cxTagList as item >
														<#if item_index gt 0>
															<#if (item.uiType == 4)>
                    											<span class="default">${item.name}</span>
															<#elseif (item.uiType == 999)>
																<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
															<#else>
																<#if item.subType ?? && item.subType == 9>
																	<span class="zhuanpin-biao z1">${item.name}</span>
																<#else>	
																	<span class="biao z1">${item.name}</span>
																</#if>
															</#if>
														</#if>
													</#list>
												</li>
												<#list detail.cxTagList as item >
													<#if item_index gt 0>
														<li>
															<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
															<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
															<#else>
																<#if item.name ?? && item.name == "整单包邮">
																	<div class="synthesis">
																		<div class="synthesis-biao">
																			<span class="synthesis-biao-left">${item.name}</span>
																			<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																				<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																			</#if>
																			<#if item.description ?? && item.description != ''>
																				<span class="synthesis-biao-shuoming">${item.description}</span>
																			</#if>
																		</div>
																	</div>
																<#elseif item.subType ?? && item.subType == 9>
																	<span class="zhuanpin-biao 4">${item.name}</span>
																<#else>	
																	<span class="biao 4">${item.name}</span>
																</#if>
															</#if>
															
															<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
																<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																	<span class="shuoming">${item.cxtagDescription}</span>
																</#if>
																<#if item.description ?? && item.description != ''>
																	<span class="shuoming">${item.description}</span>
																</#if>
															</#if>
															<#if item.pcURL ?? && item.pcURL != ''>
																<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
															</#if>
														</li>
													</#if>
												</#list>
											</#if>
										</ul>
										<#if detail.cxTagList?size gt 1>
											<!--促销记录大于2条时候显示展开按钮-->
											<a href="javascript:;" class="zhankai">展开</a>
										</#if>
									</div>
								</#if>
							</#if>

								<!--纠错-->
								<#if merchant ?exists>
								<#if detail.isThirdCompany == 0 || (detail.isThirdCompany == 1 && detail.branchCode != 'XS000000')>
								<div class="price-notice" id="jiucuo" style="right:180px;">
									<#else >
									<div class="price-notice" id="jiucuo" style="right:90px;">
										</#if>
										<a href="javascript:;"><img src="/static/images/jiucuo/jiucuo.png" alt=""><span>纠错</span></a>
									</div>
									</#if>

									<!--降价通知-->
									<#if detail.priceType !=2  && (detail.isOEM == '' || detail.isOEM == null || detail.isOEM != 'true' || detail.signStatus !='0') && detail.showAgree!='0'>
									<#if detail.isThirdCompany == 0 || (detail.isThirdCompany == 1 && detail.branchCode != 'XS000000')>
									<div class="price-notice" style="right:105px;">
										<#else >
										<div class="price-notice">
											</#if>
											<a href="javascript:;" onclick="priceNotify(${detail.id}, ${detail.fob})"><img src="/static/images/naozhong.png" alt=""><span>降价通知</span></a>
										</div>
										</#if>
										<#if (detail.isThirdCompany == 0 || (detail.isThirdCompany == 1 && detail.branchCode != 'XS000000')) && merchant != null && (merchant.licenseStatus == 6 || merchant.licenseStatus == 4 || merchant.licenseStatus == 2)>
											<div class="price-notice">
												<a href="javascript:;" onclick="getSignaturesDownload('${detail.barcode}', '${detail.branchCode}')"><span>商品资质下载</span></a>
											</div>
										</#if>
							</div>
						<!-- 批购包邮 -->
						<#elseif isWholesale == 1>
							<div class="section" style="background: #FFF9F2">
							<!--正常显示价格样式-->
							<#if (merchant ? exists)>
								<#if merchant.licenseStatus ==1 && detail.fob == '0'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">含税价认证资质后可见</span>
										</div>
									</div>
								<#elseif merchant.licenseStatus ==5 && detail.fob == '0'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">含税价认证资质后可见</span>
										</div>
									</div>
								<#elseif detail.controlType == '2'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">不在经营范围</span>
										</div>
									</div>
								<#elseif detail.agreementEffective?? && detail.agreementEffective =='3'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">协议已冻结，价格解冻后可见</span>
										</div>
									</div>
								<#elseif detail.isControl ==1 >
									<#if detail.isPurchase>
										<#if detail.priceType==1 >
											<div class="noqujian ">
												<div class="hprice">
													<span class="linfo">活&nbsp;动&nbsp;&nbsp;价</span>
													<!--正常显示价格样式-->
													<span class="price" style="color: #FF0202;font-size: 28px;margin-left: 13px">
														<#if (actPgby.assembleStatus?? && actPgby.assembleStatus == 1)>
															<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.limitFullDiscount > 0)>
																${detail.limitFullDiscountActInfo.limitFullDiscount}
															<#else>
																${actPgby.assemblePrice}
															</#if>
														<#else>
															?
														</#if>
													</span>
													<span style="color: #FF0202;font-size: 14px">元/${detail.productUnit}</span>
													<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.limitFullDiscount > 0)>
														<span style="margin-left: 2px;color: #F1081C;font-size: 12px">
															${detail.limitFullDiscountActInfo.text}
														</span>
													</#if>
													<#if detail.levelPriceDTO == null>
														<span class="zhehou-price-box">
														<span class="zhehou-price"></span>
														<a href="javascript:void(0)" class="get-intro" data-keyboard="false" style="display:none;"><img src="/static/images/zhehoujia-icon.png" alt="" style="top: -2px;position: relative;width: 14px;"></a>
														</span>
													</#if>
                          <#if detail.unitPrice?? && detail.unitPrice != '' && (actPgby.assembleStatus?? && actPgby.assembleStatus == 1)>
                              <span style="color: #FF3535;font-size: 10px;display: inline-block;background-color: transparent;border: 1px solid #FF3535;padding: 0px 1px;border-radius: 3px;vertical-align: bottom;">${detail.unitPrice}</span>  
                          </#if>
												</div>
											</div>
										<#else >
											<#if detail.skuPriceRangeList ??>
											<div class="hasqujian">
														<div class="hprice">
															<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
															<#list detail.skuPriceRangeList?reverse as priceReange>
																<!--正常显示价格样式-->
																<#if priceReange_has_next>
																	<span class="price qujian">￥${priceReange.price}<img src="/static/images/shuxian.png" class="shuxian"> </span>
																</#if>
																<#if !priceReange_has_next>
																	<span class="price qujian">￥${priceReange.price}</span>
																</#if>
															</#list>
														</div>
														<#if (detail.isControl ==1 && detail.isPurchase && detail.priceType==2) ||(detail.isControl !=1 && detail.priceType==2)>
															<div class="cgounum">
																<span class="linfo">采购量</span>
															<#list detail.skuPriceRangeList?reverse as priceReange>
																<#if !priceReange_has_next>
																	<span class="price"> ≥${priceReange.fromQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
																<#if priceReange_has_next>
																	<span class="price">${priceReange.fromQty}-${priceReange.toQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
															</#list>
															</div>
														</#if>
                                            </div>
											</#if>
										</#if>
									<#else>
										<div class="noqujian ">
											<div class="hprice">
												<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
												<!--正常显示价格样式-->
                                                <span class="noPermission">暂无购买权限</span>
											</div>
										</div>
									</#if>
								<#else>
									<#if detail.priceType==1 >
											<div class="noqujian ">
                                                <div class="hprice">
                                                    <span class="linfo">活&nbsp;动&nbsp;&nbsp;价</span>
                                                    <!--正常显示价格样式-->
                                                    <span class="price" style="color: #FF0202;font-size: 28px;margin-left: 13px">
														<#if (actPgby.assembleStatus?? && actPgby.assembleStatus == 1)>
															<#-- 单阶梯 -->
															<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.limitFullDiscount > 0)>
																${detail.limitFullDiscountActInfo.limitFullDiscount}
															<#else>
																${actPgby.assemblePrice}
															</#if>
														<#else>
															?
														</#if>
													</span>
													<span style="color: #FF0202;font-size: 14px">元/${detail.productUnit}</span>
													<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.limitFullDiscount > 0)>
														<span style="margin-left: 2px;color: #F1081C;font-size: 12px">
															${detail.limitFullDiscountActInfo.text}
														</span>
													</#if>
									                <#if detail.levelPriceDTO == null>
														<span class="zhehou-price-box">
														<span class="zhehou-price"></span>
														<a href="javascript:void(0)" class="get-intro" data-keyboard="false" style="display:none;"><img src="/static/images/zhehoujia-icon.png" alt="" style="top: -2px;position: relative;width: 14px;"></a>
														</span>
													</#if>

                          <#if detail.unitPrice?? && detail.unitPrice != '' && (actPgby.assembleStatus?? && actPgby.assembleStatus == 1)>
                              <span style="color: #FF3535;font-size: 10px;display: inline-block;background-color: transparent;border: 1px solid #FF3535;padding: 0px 1px;border-radius: 3px;vertical-align: bottom;">${detail.unitPrice}</span>  
                          </#if>
                                                </div>

                                            </div>
									<#else >
										<#if detail.skuPriceRangeList ??>
											<div class="hasqujian">
                                                <div class="hprice">
                                                    <span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
														<#list detail.skuPriceRangeList?reverse as priceReange>
																	<!--正常显示价格样式-->
															<#if priceReange_has_next>
																<span class="price qujian">￥${priceReange.price}<img src="/static/images/shuxian.png" class="shuxian"> </span>
															</#if>
															<#if !priceReange_has_next>
																<span class="price qujian">￥${priceReange.price}</span>
															</#if>
														</#list>
                                                </div>
												<#if (detail.isControl ==1 && detail.isPurchase && detail.priceType==2) ||(detail.isControl !=1 && detail.priceType==2)>
														<div class="cgounum">
                                                            <span class="linfo">采购量</span>
															<#list detail.skuPriceRangeList?reverse  as priceReange >
																<#if !priceReange_has_next>
																	<span class="price"> ≥${priceReange.fromQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
																<#if priceReange_has_next>
																	<span class="price">${priceReange.fromQty}-${priceReange.toQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
															</#list>
                                                        </div>
												</#if>
											</div>
										</#if>
									</#if>
								</#if>
							<#else>
								<div class="noqujian ">
                                    <div class="hprice">
                                        <span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
                                        <!--正常显示价格样式-->
                                        <span class="noPermission">价格登录可见</span>
                                    </div>
                                </div>
							</#if>
							<#if detail.controlType == '5'>
								<p style="padding:3px;border: 1px solid #c76f15;border-radius:3px;color:#c76f15;margin:5px 0;">战略客户专享商品：协议签署即可采购，请联系区域业务经理</p>
							</#if>
                            <div class="yprice">
							<#if (merchant ? exists) && actPgby.assembleStatus != 1>
								<#if merchant.licenseStatus ==1 && detail.retailPrice == '0'>

								<#elseif merchant.licenseStatus ==5 && detail.retailPrice == '0'>

								<#elseif detail.agreementEffective?? && detail.agreementEffective =='3'>

                                <#elseif detail.isOEM == '' || detail.isOEM == null || detail.isOEM != 'true' || detail.signStatus !='0'>
									<#if detail.showAgree !='0'>
										<#if detail.isControl ==1 >
											<#if detail.isPurchase>
												<#if (detail.uniformPrice ??) && (detail.uniformPrice != '')>
													<span class="ptit">控销零售价</span>
													<span class="pinfo" style="margin-left: 20px;">￥${detail.uniformPrice}</span>
												</#if>
												<#if (detail.suggestPrice ??) && (detail.suggestPrice != '') && (detail.suggestPrice > 0)>
													<#if detail.agreeSigneStatus == '' || detail.agreeSigneStatus == null||detail.agreeSigneStatus =='0'|| detail.agreeSigneStatus =='4'>
														<span class="linfo">建议零售价</span>
													<#else>
														<span class="linfo">统一零售价</span>
													</#if>
													<span class="pinfo" style="margin-left: 20px;">￥${detail.suggestPrice}</span>
												</#if>
											</#if>
										<#else>
											<#if (detail.uniformPrice ??) && (detail.uniformPrice != '')>
												<span class="ptit">控销零售价</span>
												<span class="pinfo" style="margin-left: 20px;">￥${detail.uniformPrice}</span>
											</#if>
											<#if (detail.suggestPrice ??) && (detail.suggestPrice != '') && (detail.suggestPrice > 0)>
												<#if detail.agreeSigneStatus == '' || detail.agreeSigneStatus == null||detail.agreeSigneStatus =='0'|| detail.agreeSigneStatus =='4'>
													<span class="linfo">建议零售价</span>
												<#else>
													<span class="linfo">统一零售价</span>
												</#if>
												<span class="pinfo" style="margin-left: 20px;">￥${detail.suggestPrice}</span>
											</#if>
										</#if>
									</#if>
                                </#if>
							</#if>
							<#if (merchant ? exists) && actPgby.assembleStatus != 1>
                                <#if detail.isOEM == '' || detail.isOEM == null || detail.isOEM != 'true' || detail.signStatus !='0'>
									<#if detail.showAgree !='0'>
                                    	<#if detail.isControl ==1 >
                                        <#if detail.isPurchase>
                                            <#if (detail.grossMargin ??) && (detail.grossMargin != '')>
                                            <span class="ptit">毛利率</span>
                                            <span class="pinfo">${detail.grossMargin}</span>
                                            </#if>
                                        </#if>
                                    <#else>
                                        <#if (detail.grossMargin ??) && (detail.grossMargin != '')>
                                        <span class="ptit">毛利率</span>
                                        <span class="pinfo">${detail.grossMargin}</span>
                                        </#if>
                                    </#if>
									</#if>
								</#if>
							</#if>
							<#if detail.prescriptionPrice ?? && detail.prescriptionPrice != 0 && actPgby.assembleStatus != 1>
                                <span class="ptit">医保价</span>
                                <span class="ptit" style="margin:0;">￥${detail.prescriptionPrice?string("0.00")}</span>
							</#if>
                            </div>
							<!--  次日达  -->
							<#if detail.nextDayServiceTagList ?? && (detail.nextDayServiceTagList?size >0) >
								<div class="cuxiaobox">
									<span class="linfo">配　　送</span>
									<#list detail.nextDayServiceTagList as item >
										<span class="next-day-service-tag" style="<#if item.bgColor??>background:${item.bgColor};</#if><#if item.borderColor??>border:1px solid ${item.borderColor};</#if>">
											<#if item.pcIcon??>
												<img src="${productImageUrl}${item.pcIcon}" 
													alt="${item.text!''}" 
													style="height: 12px; width: auto; object-fit: cover;display: inline-block;">
											</#if>
											<span style="<#if item.textColor??>color:${item.textColor};</#if>">
												${item.text!''}
											</span>
										</span>
										<span class="next-day-service-span" style="float: none !important">${item.description}</span>
									</#list>
								</div>
							</#if>
                            <!--优惠券-->
							<#if detail.isShowVoucher>
								<div class="youhuiquan test2">
									<span class="linfo">优&nbsp;惠&nbsp;&nbsp;券</span>
									<div class="quan-a-box">
										<#list detail.voucherTagList as vTag>
											<a href="javascript:void(0)" onclick="test()"  class="quan-a">${vTag}</a>
										</#list>
									</div>
								</div>
							</#if>
								<#if (merchant ? exists)>
								<#if detail.cxTagList ?? && (detail.cxTagList?size >0) >
									<div class="cuxiaobox">
										<span class="linfo">促　　销</span>
										<ul class="cuxiao-ul" id="cxUl">
											<#list detail.cxTagList as item >
												<#if detail.cxTagList?size == 1>
													<li>
														<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
														<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
														<#else>
															<#if item.name ?? && item.name == "整单包邮">
																<div class="synthesis">
																	<div class="synthesis-biao">
																		<span class="synthesis-biao-left">${item.name}</span>
																		<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																			<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																		</#if>
																		<#if item.description ?? && item.description != ''>
																			<span class="synthesis-biao-shuoming">${item.description}</span>
																		</#if>
																	</div>
																</div>
															<#elseif item.subType ?? && item.subType == 9>
																<span class="zhuanpin-biao 5">${item.name}</span>
															<#else>	
																<span class="biao 5">${item.name}</span>
															</#if>
														</#if>
														<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
															<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																<span class="shuoming">${item.cxtagDescription}</span>
															</#if>
															<#if item.description ?? && item.description != ''>
																<span class="shuoming">${item.description}</span>
															</#if>
														</#if>
														<#if item.pcURL ?? && item.pcURL != ''>
															<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
														</#if>
													</li>
												</#if>
												<#if detail.cxTagList?size == 2>
													<li id="desc${item_index}">
														<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
														<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
														<#else>
															<#if item.name ?? && item.name == "整单包邮">
																<div class="synthesis">
																	<div class="synthesis-biao">
																		<span class="synthesis-biao-left">${item.name}</span>
																		<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																			<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																		</#if>
																		<#if item.description ?? && item.description != ''>
																			<span class="synthesis-biao-shuoming">${item.description}</span>
																		</#if>
																	</div>
																</div>
															<#elseif item.subType ?? && item.subType == 9>
																<span class="zhuanpin-biao 6">${item.name}</span>
															<#else>	
																<span class="biao 6">${item.name}</span>
															</#if>
														</#if>
														<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
															<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																<span class="shuoming">${item.cxtagDescription}</span>
															</#if>
															<#if item.description ?? && item.description != ''>
																<span class="shuoming">${item.description}</span>
															</#if>
														</#if>
														<#if item.pcURL ?? && item.pcURL != ''>
															<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
														</#if>
													</li>
												</#if>
												<#if detail.cxTagList?size gt 2>
													<#if item_index == 0>
														<li class="text-overflow">
															<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
															<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
															<#else>
																<#if item.name ?? && item.name == "整单包邮">
																	<div class="synthesis">
																		<div class="synthesis-biao">
																			<span class="synthesis-biao-left">${item.name}</span>
																			<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																				<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																			</#if>
																			<#if item.description ?? && item.description != ''>
																				<span class="synthesis-biao-shuoming">${item.description}</span>
																			</#if>
																		</div>
																	</div>
																<#elseif item.subType ?? && item.subType == 9>
																	<span class="zhuanpin-biao 7">${item.name}</span>
																<#else>	
																	<span class="biao 7">${item.name}</span>
																</#if>
															</#if>
															<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
																<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																	<span class="shuoming">${item.cxtagDescription}</span>
																</#if>
																<#if item.description ?? && item.description != ''>
																	<span class="shuoming">${item.description}</span>
																</#if>
															</#if>
															<#if item.pcURL ?? && item.pcURL != ''>
																<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
															</#if>
														</li>
													</#if>
												</#if>
											</#list>
											<#if detail.cxTagList?size gt 2>
												<li class="sbzhan">
													<#list detail.cxTagList as item >
														<#if item_index gt 0>
															<#if (item.uiType == 4)>
                    											<span class="default">${item.name}</span>
															<#elseif (item.uiType == 999)>
																<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
															<#else>
																<#if item.subType ?? && item.subType == 9>
																	<span class="zhuanpin-biao z2">${item.name}</span>
																<#else>	
																	<span class="biao z2">${item.name}</span>
																</#if>
															</#if>
														</#if>
													</#list>
												</li>
												<#list detail.cxTagList as item >
													<#if item_index gt 0>
														<li>
															<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
															<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
															<#else>
																<#if item.name ?? && item.name == "整单包邮">
																	<div class="synthesis">
																		<div class="synthesis-biao">
																			<span class="synthesis-biao-left">${item.name}</span>
																			<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																				<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																			</#if>
																			<#if item.description ?? && item.description != ''>
																				<span class="synthesis-biao-shuoming">${item.description}</span>
																			</#if>
																		</div>
																	</div>
																<#elseif item.subType ?? && item.subType == 9>
																	<span class="zhuanpin-biao 8">${item.name}</span>
																<#else>	
																	<span class="biao 8">${item.name}</span>
																</#if>
															</#if>

															<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
																<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																	<span class="shuoming">${item.cxtagDescription}</span>
																</#if>
																<#if item.description ?? && item.description != ''>
																	<span class="shuoming">${item.description}</span>
																</#if>
															</#if>
															<#if item.pcURL ?? && item.pcURL != ''>
																<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
															</#if>
														</li>
													</#if>
												</#list>
											</#if>
										</ul>
										<#if detail.cxTagList?size gt 1>
											<!--促销记录大于2条时候显示展开按钮-->
											<a href="javascript:;" class="zhankai">展开</a>
										</#if>
									</div>
								</#if>
							</#if>

								<!--纠错-->
								<#if merchant ?exists>
								<#if detail.isThirdCompany == 0 || (detail.isThirdCompany == 1 && detail.branchCode != 'XS000000')>
								<div class="price-notice" id="jiucuo" style="right:180px;">
									<#else >
									<div class="price-notice" id="jiucuo" style="right:90px;">
										</#if>
										<a href="javascript:;"><img src="/static/images/jiucuo/jiucuo.png" alt=""><span>纠错</span></a>
									</div>
									</#if>

									<!--降价通知-->
									<#if detail.priceType !=2  && (detail.isOEM == '' || detail.isOEM == null || detail.isOEM != 'true' || detail.signStatus !='0') && detail.showAgree!='0'>
									<#if detail.isThirdCompany == 0 || (detail.isThirdCompany == 1 && detail.branchCode != 'XS000000')>
									<div class="price-notice" style="right:105px;">
										<#else >
										<div class="price-notice">
											</#if>
											<a href="javascript:;" onclick="priceNotify(${detail.id}, ${detail.fob})"><img src="/static/images/naozhong.png" alt=""><span>降价通知</span></a>
										</div>
										</#if>
										<#if (detail.isThirdCompany == 0 || (detail.isThirdCompany == 1 && detail.branchCode != 'XS000000')) && merchant != null && (merchant.licenseStatus == 6 || merchant.licenseStatus == 4 || merchant.licenseStatus == 2)>
											<div class="price-notice">
												<a href="javascript:;" onclick="getSignaturesDownload('${detail.barcode}', '${detail.branchCode}')"><span>商品资质下载</span></a>
											</div>
										</#if>
							</div>
						<!-- 普通 -->
						<#else >
							<div class="section">
							<!--正常显示价格样式-->
							<#if (merchant ? exists)>
								<#if merchant.licenseStatus ==1 && detail.fob == '0'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">含税价认证资质后可见</span>
										</div>
									</div>
								<#elseif merchant.licenseStatus ==5 && detail.fob == '0'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">含税价认证资质后可见</span>
										</div>
									</div>
								<#elseif detail.controlType == '2'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">不在经营范围</span>
										</div>
									</div>
								<#elseif detail.agreementEffective?? && detail.agreementEffective =='3'>
									<div class="noqujian ">
										<div class="hprice">
											<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
											<span class="noPermission">协议已冻结，价格解冻后可见</span>
										</div>
									</div>
								<#elseif detail.isControl ==1 >
									<#if detail.isPurchase>
										<#if detail.priceType==1 >
											<div class="noqujian ">
												<div class="hprice">
													<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
													<!--正常显示价格样式-->
													<#if detail.levelPriceDTO>
														<span class="price">
														  <#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.limitFullDiscount > 0)>
															${detail.limitFullDiscountActInfo.limitFullDiscount}
															<span style="margin-left: 2px;color: #F1081C;font-size: 12px">
																${detail.limitFullDiscountActInfo.text}
															</span>
														  <#else>
														  	${detail.levelPriceDTO.startPerUnitShowText}
														  </#if>
														</span>
													<#else>
														<span class="price">${detail.fob}</span><span style="margin: 0;color: #666666;font-size: 16px;padding-left: 3px;vertical-align: top">元/${detail.productUnit}</span>
													</#if>
													<#if detail.levelPriceDTO == null>
														<span class="zhehou-price-box">
														<span class="zhehou-price"></span>
														<a href="javascript:void(0)" class="get-intro" data-keyboard="false" style="display:none;"><img src="/static/images/zhehoujia-icon.png" alt="" style="top: -2px;position: relative;width: 14px;"></a>
														</span>
													</#if>
                          <#if detail.unitPrice?? && detail.unitPrice != ''>
                              <span style="color: #FF3535;font-size: 10px;display: inline-block;background-color: transparent;border: 1px solid #FF3535;padding: 0px 1px;border-radius: 3px;vertical-align: bottom;">${detail.unitPrice}</span>  
                          </#if>
												</div>
											</div>
										<#else >
											<#if detail.skuPriceRangeList ??>
											<div class="hasqujian">
														<div class="hprice">
															<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
															<#list detail.skuPriceRangeList?reverse as priceReange>
																<!--正常显示价格样式-->
																<#if priceReange_has_next>
																	<span class="price qujian">￥${priceReange.price}<img src="/static/images/shuxian.png" class="shuxian"> </span>
																</#if>
																<#if !priceReange_has_next>
																	<span class="price qujian">￥${priceReange.price}</span>
																</#if>
															</#list>
														</div>
														<#if (detail.isControl ==1 && detail.isPurchase && detail.priceType==2) ||(detail.isControl !=1 && detail.priceType==2)>
															<div class="cgounum">
																<span class="linfo">采购量</span>
															<#list detail.skuPriceRangeList?reverse as priceReange>
																<#if !priceReange_has_next>
																	<span class="price"> ≥${priceReange.fromQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
																<#if priceReange_has_next>
																	<span class="price">${priceReange.fromQty}-${priceReange.toQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
															</#list>
															</div>
														</#if>
                                            </div>
											</#if>
										</#if>
									<#else>
										<div class="noqujian ">
											<div class="hprice">
												<span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
												<!--正常显示价格样式-->
                                                <span class="noPermission">暂无购买权限</span>
											</div>
										</div>
									</#if>
								<#else>
									<#if detail.priceType==1 >
											<div class="noqujian ">
                                                <div class="hprice">
                                                    <span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
                                                    <!--正常显示价格样式-->
													<#if detail.levelPriceDTO>
														<span class="price">
															<#if detail.limitFullDiscountActInfo ?? && (detail.limitFullDiscountActInfo.limitFullDiscount > 0)>
																${detail.limitFullDiscountActInfo.limitFullDiscount}
																<span style="margin-left: 2px;color: #F1081C;font-size: 12px">
																	${detail.limitFullDiscountActInfo.text}
																</span>
															<#else>
																${detail.levelPriceDTO.startPerUnitShowText}
															</#if>
														</span>
													<#else>
														<span class="price">${detail.fob}</span><span style="margin: 0;color: #666666;font-size: 16px;padding-left: 3px;vertical-align: top">元/${detail.productUnit}</span>
													</#if>
													<span class="zhehou-price-box">
                                                    <span class="zhehou-price"></span>
                                                    <a href="javascript:void(0)" class="get-intro" data-keyboard="false" style="display:none;"><img src="/static/images/zhehoujia-icon.png" alt="" style="top: -2px;position: relative;width: 14px;"></a>
                                                    </span>
														<!--限购提示-->
                            <#if detail.unitPrice?? && detail.unitPrice != ''>
                                <span style="color: #FF3535;font-size: 10px;display: inline-block;background-color: transparent;border: 1px solid #FF3535;padding: 0px 1px;border-radius: 3px;vertical-align: bottom;">${detail.unitPrice}</span>  
                            </#if>
                                                </div>
                                            </div>
									<#else >
										<#if detail.skuPriceRangeList ??>
											<div class="hasqujian">
                                                <div class="hprice">
                                                    <span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
														<#list detail.skuPriceRangeList?reverse as priceReange>
																	<!--正常显示价格样式-->
															<#if priceReange_has_next>
																<span class="price qujian">￥${priceReange.price}<img src="/static/images/shuxian.png" class="shuxian"> </span>
															</#if>
															<#if !priceReange_has_next>
																<span class="price qujian">￥${priceReange.price}</span>
															</#if>
														</#list>
                                                </div>
												<#if (detail.isControl ==1 && detail.isPurchase && detail.priceType==2) ||(detail.isControl !=1 && detail.priceType==2)>
														<div class="cgounum">
                                                            <span class="linfo">采购量</span>
															<#list detail.skuPriceRangeList?reverse  as priceReange >
																<#if !priceReange_has_next>
																	<span class="price"> ≥${priceReange.fromQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
																<#if priceReange_has_next>
																	<span class="price">${priceReange.fromQty}-${priceReange.toQty}
																		<#if detail.productUnit ??  && detail.productUnit !="">
																			${detail.productUnit}
																		</#if>
																	</span>
																</#if>
															</#list>
                                                        </div>
												</#if>
											</div>
										</#if>
									</#if>
								</#if>
							<#else>
								<div class="noqujian ">
                                    <div class="hprice">
                                        <span class="linfo">含&nbsp;税&nbsp;&nbsp;价</span>
                                        <!--正常显示价格样式-->
                                        <span class="noPermission">价格登录可见</span>
                                    </div>
                                </div>
							</#if> 
							<#if detail.controlType == '5'>
								<p style="padding:3px;border: 1px solid #c76f15;border-radius:3px;color:#c76f15;margin:5px 0;">战略客户专享商品：协议签署即可采购，请联系区域业务经理</p>
							</#if>
                            <div class="yprice">
							<#if (merchant ? exists)>
								<#if merchant.licenseStatus ==1 && detail.retailPrice == '0'>

								<#elseif merchant.licenseStatus ==5 && detail.retailPrice == '0'>

								<#elseif detail.agreementEffective?? && detail.agreementEffective =='3'>

                                <#elseif detail.isOEM == '' || detail.isOEM == null || detail.isOEM != 'true' || detail.signStatus !='0'>
									<#if detail.showAgree !='0'>
										<#if detail.isControl ==1 >
											<#if detail.isPurchase>
												<#if (detail.uniformPrice ??) && (detail.uniformPrice != '')>
													<span class="ptit">控销零售价</span>
													<span class="pinfo" style="margin-left: 20px;">￥${detail.uniformPrice}</span>
												</#if>
												<#if (detail.suggestPrice ??) && (detail.suggestPrice != '') && (detail.suggestPrice > 0)>
													<#if detail.agreeSigneStatus == '' || detail.agreeSigneStatus == null||detail.agreeSigneStatus =='0'|| detail.agreeSigneStatus =='4'>
														<span class="linfo">建议零售价</span>
													<#else>
														<span class="linfo">统一零售价</span>
													</#if>
													<span class="pinfo" style="margin-left: 20px;">￥${detail.suggestPrice}</span>
												</#if>
											</#if>
										<#else>
											<#if (detail.uniformPrice ??) && (detail.uniformPrice != '')>
												<span class="ptit">控销零售价</span>
												<span class="pinfo" style="margin-left: 20px;">￥${detail.uniformPrice}</span>
											</#if>
											<#if (detail.suggestPrice ??) && (detail.suggestPrice != '') && (detail.suggestPrice > 0)>
												<#if detail.agreeSigneStatus == '' || detail.agreeSigneStatus == null||detail.agreeSigneStatus =='0'|| detail.agreeSigneStatus =='4'>
													<span class="linfo">建议零售价</span>
												<#else>
													<span class="linfo">统一零售价</span>
												</#if>
												<span class="pinfo" style="margin-left: 20px;">￥${detail.suggestPrice}</span>
											</#if>
										</#if>
									</#if>
                                </#if>
							</#if>
							<#if (merchant ? exists)>
                                <#if detail.isOEM == '' || detail.isOEM == null || detail.isOEM != 'true' || detail.signStatus !='0'>
									<#if detail.showAgree !='0'>
                                    	<#if detail.isControl ==1 >
                                        <#if detail.isPurchase>
                                            <#if (detail.grossMargin ??) && (detail.grossMargin != '')>
                                            <span class="ptit">毛利率</span>
                                            <span class="pinfo">${detail.grossMargin}</span>
                                            </#if>
                                        </#if>
                                    <#else>
                                        <#if (detail.grossMargin ??) && (detail.grossMargin != '')>
                                        <span class="ptit">毛利率</span>
                                        <span class="pinfo">${detail.grossMargin}</span>
                                        </#if>
                                    </#if>
									</#if>
								</#if>
							</#if>
							<#if detail.prescriptionPrice ?? && detail.prescriptionPrice != 0>
                                <span class="ptit">医保价</span>
                                <span class="ptit" style="margin:0;">￥${detail.prescriptionPrice?string("0.00")}</span>
							</#if>
                            </div>
							<!--  次日达  -->
							<#if detail.nextDayServiceTagList ?? && (detail.nextDayServiceTagList?size >0) >
								<div class="cuxiaobox">
									<span class="linfo">配　　送</span>
									<#list detail.nextDayServiceTagList as item >
										<span class="next-day-service-tag" style="<#if item.bgColor??>background:${item.bgColor};</#if><#if item.borderColor??>border:1px solid ${item.borderColor};</#if>">
											<#if item.pcIcon??>
												<img src="${productImageUrl}${item.pcIcon}" 
													alt="${item.text!''}" 
													style="height: 12px; width: auto; object-fit: cover;display: inline-block;">
											</#if>
											<span style="<#if item.textColor??>color:${item.textColor};</#if>">
												${item.text!''}
											</span>
										</span>
										<span class="next-day-service-span" style="float: none !important">${item.description}</span>
									</#list>
								</div>
							</#if>
                            <!--优惠券-->
							<#if detail.isShowVoucher>
								<div class="youhuiquan test3">
									<span class="linfo">优&nbsp;惠&nbsp;&nbsp;券</span>
									<div class="quan-a-box">
										<#list detail.voucherTagList as vTag>
											<a href="javascript:void(0)" onclick="test()"  class="quan-a">${vTag}</a>
										</#list>
									</div>
								</div>
							</#if>
                            <!--促销-->
							<#if (merchant ? exists)>
								<#if detail.cxTagList ?? && (detail.cxTagList?size >0) >
									<div class="cuxiaobox">
										<span class="linfo">促　　销</span>
										<ul class="cuxiao-ul" id="cxUl">
											<#list detail.cxTagList as item >
												<#if detail.cxTagList?size == 1>
													<li>
														<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
														<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
														<#else>
															<#if item.name ?? && item.name == "整单包邮">
																<div class="synthesis">
																	<div class="synthesis-biao">
																		<span class="synthesis-biao-left">${item.name}</span>
																		<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																			<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																		</#if>
																		<#if item.description ?? && item.description != ''>
																			<span class="synthesis-biao-shuoming">${item.description}</span>
																		</#if>
																	</div>
																</div>
															<#elseif item.subType ?? && item.subType == 9>
																<span class="zhuanpin-biao 9">${item.name}</span>
															<#else>	
																<span class="biao 9">${item.name}</span>
															</#if>
														</#if>
														<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
															<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																<span class="shuoming">${item.cxtagDescription}</span>
															</#if>
															<#if item.description ?? && item.description != ''>
																<span class="shuoming">${item.description}</span>
															</#if>
														</#if>
														<#if item.pcURL ?? && item.pcURL != ''>
															<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
														</#if>
													</li>
												</#if>
												<#if detail.cxTagList?size == 2>
													<li id="desc${item_index}">
														<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
														<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
														<#else>
															<#if item.name ?? && item.name == "整单包邮">
																<div class="synthesis">
																	<div class="synthesis-biao">
																		<span class="synthesis-biao-left">${item.name}</span>
																		<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																			<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																		</#if>
																		<#if item.description ?? && item.description != ''>
																			<span class="synthesis-biao-shuoming">${item.description}</span>
																		</#if>
																	</div>
																</div>
															<#elseif item.subType ?? && item.subType == 9>
																<span class="zhuanpin-biao 10">${item.name}</span>
															<#else>	
																<span class="biao 10">${item.name}</span>
															</#if>
														</#if>
														<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
															<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																<span class="shuoming">${item.cxtagDescription}</span>
															</#if>
															<#if item.description ?? && item.description != ''>
																<span class="shuoming">${item.description}</span>
															</#if>
														</#if>
														<#if item.pcURL ?? && item.pcURL != ''>
															<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
														</#if>
													</li>
												</#if>
												<#if detail.cxTagList?size gt 2>
													<#if item_index == 0>
														<li class="text-overflow">
															<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
															<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
															<#else>
																<#if item.name ?? && item.name == "整单包邮">
																	<div class="synthesis">
																		<div class="synthesis-biao">
																			<span class="synthesis-biao-left">${item.name}</span>
																			<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																				<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																			</#if>
																			<#if item.description ?? && item.description != ''>
																				<span class="synthesis-biao-shuoming">${item.description}</span>
																			</#if>
																		</div>
																	</div>
																<#elseif item.subType ?? && item.subType == 9>
																	<span class="zhuanpin-biao 11">${item.name}</span>
																<#else>	
																	<span class="biao 11">${item.name}</span>
																</#if>
															</#if>
															<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
																<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																	<span class="shuoming">${item.cxtagDescription}</span>
																</#if>
																<#if item.description ?? && item.description != ''>
																	<span class="shuoming">${item.description}</span>
																</#if>
															</#if>
															<#if item.pcURL ?? && item.pcURL != ''>
																<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
															</#if>
														</li>
													</#if>
												</#if>
											</#list>
											<#if detail.cxTagList?size gt 2>
												<li class="sbzhan">
													<#list detail.cxTagList as item >
														<#if item_index gt 0>
															<#if (item.uiType == 4)>
                    											<span class="default">${item.name}</span>
															<#elseif (item.uiType == 999)>
																<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
															<#else>
																<#if item.subType ?? && item.subType == 9>
																	<span class="zhuanpin-biao z3">${item.name}</span>
																<#else>	
																	<span class="biao z3">${item.name}</span>
																</#if>
															</#if>
														</#if>
													</#list>
												</li>
												<#list detail.cxTagList as item >
													<#if item_index gt 0>
														<li>
															<#if (item.uiType == 4)>
                    										<span class="default">${item.name}</span>
															<#elseif (item.uiType == 999)>
														<img src="${productImageUrl}${item.name}" style="width: 18px;width: 18px">
															<#else>
																<#if item.name ?? && item.name == "整单包邮">
																	<div class="synthesis">
																		<div class="synthesis-biao">
																			<span class="synthesis-biao-left">${item.name}</span>
																			<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																				<span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
																			</#if>
																			<#if item.description ?? && item.description != ''>
																				<span class="synthesis-biao-shuoming">${item.description}</span>
																			</#if>
																		</div>
																	</div>
																<#elseif item.subType ?? && item.subType == 9>
																	<span class="zhuanpin-biao 12">${item.name}</span>
																<#else>	
																	<span class="biao 12">${item.name}</span>
																</#if>
															</#if>

															<#if item.name ?? && item.name != "整单包邮" && item.subType != 9>
																<#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
																	<span class="shuoming">${item.cxtagDescription}</span>
																</#if>
																<#if item.description ?? && item.description != ''>
																	<span class="shuoming">${item.description}</span>
																</#if>
															</#if>
															<#if item.pcURL ?? && item.pcURL != ''>
																<a class="tiaozhuan" href="${item.pcURL}" <#if item.isNewTab == 1>target="_Blank"</#if>><#if item.urlName?? && item.urlName != ''>${item.urlName}<#else>查看更多活动商品>></#if></a>
															</#if>
														</li>
													</#if>
												</#list>
											</#if>
										</ul>
										<#if detail.cxTagList?size gt 1>
											<!--促销记录大于2条时候显示展开按钮-->
											<a href="javascript:;" class="zhankai">展开</a>
										</#if>
									</div>
								</#if>
							</#if>

                            <!--纠错-->
							<#if merchant ?exists>
								<#if detail.isThirdCompany == 0 || (detail.isThirdCompany == 1 && detail.branchCode != 'XS000000')>
									<div class="price-notice" id="jiucuo" style="right:180px;">
								<#else >
									<div class="price-notice" id="jiucuo" style="right:90px;">
								</#if>
								<a href="javascript:;"><img src="/static/images/jiucuo/jiucuo.png" alt=""><span>纠错</span></a>
							</div>
							</#if>

							<!--降价通知-->
							<#if detail.priceType !=2  && (detail.isOEM == '' || detail.isOEM == null || detail.isOEM != 'true' || detail.signStatus !='0') && detail.showAgree!='0'>
								<#if detail.isThirdCompany == 0 || (detail.isThirdCompany == 1 && detail.branchCode != 'XS000000')>
									<div class="price-notice" style="right:105px;">
								<#else >
									<div class="price-notice">
								</#if>
										<a href="javascript:;" onclick="priceNotify(${detail.id}, ${detail.fob})"><img src="/static/images/naozhong.png" alt=""><span>降价通知</span></a>
									</div>
                            </#if>
								<#if (detail.isThirdCompany == 0 || (detail.isThirdCompany == 1 && detail.branchCode != 'XS000000')) && merchant != null && (merchant.licenseStatus == 6 || merchant.licenseStatus == 4 || merchant.licenseStatus == 2)>
									<div class="price-notice">
										<a href="javascript:;" onclick="getSignaturesDownload('${detail.barcode}', '${detail.branchCode}')"><span>商品资质下载</span></a>
									</div>
								</#if>
								<#--下载资质-->
							</div>
						</#if>
						<#--<#if detail.prescriptionPrice ?? && detail.prescriptionPrice != 0>-->
							<#--<div class="tongyong-info">-->
								<#--<span class="linfo-col1">医保支付价</span>-->
								<#--<span class="rtext-co11">￥${detail.prescriptionPrice?string("0.00")}</span>-->
							<#--</div>-->
						<#--</#if>-->
						<div class="tag-list-container" style="margin: 10px 0 10px 15px;">
							<#if detail.firstTagList?? && (detail.firstTagList?size > 0)>
                                <#list detail.firstTagList as item>
                                    <#if item.uiStyle?? &&  item.uiStyle == 3>
                                        <img src="${productImageUrl}${item.pcIcon}" alt="${item.name!''}" style="height: 18px; width: auto;">
                                    <#else>
                                        <span style="height: 18px; background-color: ${item.bgColor!''};color: ${item.textColor!''};border: 1px solid ${item.borderColor!''}; border-radius: 2px; padding: 0 4px; vertical-align: middle; font-size: 12px;">${item.name}</span>
                                    </#if>
                                </#list>
                            </#if>
						</div>
                        <div class="tongyong-info">
							<#if detail.categoryFirstId ?? && detail.categoryFirstId == 100005>
								<span class="linfo-col1">型号、规格</span>
							<#else >
								<span class="linfo-col1">规　　格</span>
							</#if>
                            <span class="rtext-co11">${detail.spec}</span>
							<#if detail.categoryFirstId ?? && detail.categoryFirstId != 100004 && detail.categoryFirstId != 100010>
								<span class="linfo-col2">剂　　型</span>
								<span class="rtext-col2">${detail.dosageForm}</span>
							</#if>
							<#if detail.producer!= ''>
								<span class="linfo-col2">产　　地</span>
								<span class="rtext-col2">${detail.producer}</span>
							</#if>
                        </div>
						<div class="tongyong-info">
							<#if detail.skuExtAttrDTOS?? && detail.skuExtAttrDTOS?size gt 0>
								<#list detail.skuExtAttrDTOS as item>
									<#if item_index % 2 == 0>
										<div style="margin-top: 10px;"></div>
										<span class="linfo-col1">${item.attrTitle}</span>
                            			<span class="rtext-co11">${item.attrValue}</span>
									<#else>
										<span class="linfo-col2">${item.attrTitle}</span>
                            			<span class="rtext-col2">${item.attrValue}</span>
									</#if>
								</#list>
							</#if>
						</div>
						<#if detail.medicalInsuranceCode ?? && detail.medicalInsuranceCode != ''>
							<div class="tongyong-info">
                                <span class="linfo-col1">医保编码</span>
                                <span class="rtext-co11">${detail.medicalInsuranceCode}</span>
                            </div>
						</#if>
                        <div class="tongyong-info">
							<#if detail.categoryFirstId ?? && detail.categoryFirstId == 100004 || detail.categoryFirstId == 100010>
								<#if detail.categoryFirstId ?? && detail.categoryFirstId != 100004 && detail.categoryFirstId != 100010>
									<#if detail.categoryFirstId ?? && detail.categoryFirstId == 100005>
										<span class="linfo-col1">医疗器械注册证或备案凭证编号</span>
									<#elseif detail.skuCategory ?? && detail.skuCategory == '化妆品'>
										<span class="linfo-col1">化妆品备案编号/注册证号</span>
									<#else >
										<span class="linfo-col1">批准文号</span>
									</#if>
									<span class="rtext-co11">${detail.approvalNumber}</span>
								</#if>
							<#else>
								<#if detail.categoryFirstId ?? && detail.categoryFirstId != 100004 && detail.categoryFirstId != 100010>
									<#if detail.categoryFirstId ?? && detail.categoryFirstId == 100005>
										<span class="linfo-col1">医疗器械注册证或备案凭证编号</span>
									<#elseif detail.skuCategory ?? && detail.skuCategory == '化妆品'>
										<span class="linfo-col1">化妆品备案编号/注册证号</span>
									<#else >
										<span class="linfo-col1">批准文号</span>
									</#if>
									<span class="rtext-co11">${detail.approvalNumber}</span>
								</#if>
								
									<span class="linfo-col2">件&nbsp;装&nbsp;&nbsp;量</span>
									<#if detail.pieceLoading ?? && detail.pieceLoading != '' && detail.pieceLoading != 0>
										<span class="rtext-col2">${detail.pieceLoading}${detail.productUnit}</span>
									</#if>
							</#if>
                        </div>
                        <#--  <div class="tongyong-info">
							<input type="hidden" id="categoryFirstId" value="${detail.categoryFirstId}">
							<#if detail.categoryFirstId ?? && detail.categoryFirstId == 100004 || detail.categoryFirstId == 100010>
								<#if detail.skuCategory ?? && detail.skuCategory == '化妆品' && detail.filingsAuthor ?? && detail.filingsAuthor != ''>
									<span class="linfo-col1">化妆品备案人/注册人</span>
									<span class="rtext-col1">${detail.filingsAuthor}</span>
								</#if>
							<#else >
								<span class="linfo-col1">中&nbsp;包&nbsp;&nbsp;装</span>
								<span class="rtext-co11">${detail.mediumPackageNum}${detail.productUnit}
								<#if detail.isSplitTitle?? && detail.isSplitTitle != ''>
									<span class="bkcl">${detail.isSplitTitle}</span>
								</#if>
								</span>
								<#if detail.skuCategory ?? && detail.skuCategory == '化妆品' && detail.filingsAuthor ?? && detail.filingsAuthor != ''>
									<span class="linfo-col2">化妆品备案人/注册人</span>
									<span class="rtext-col2">${detail.filingsAuthor}</span>
								</#if>
							</#if>
                        </div>  -->
					<div class="tongyong-info">
						<span class="linfo-col1">中&nbsp;包&nbsp;&nbsp;装</span>
						<span class="rtext-co11">${detail.mediumPackageNum}${detail.productUnit}
						<#if detail.isSplitTitle?? && detail.isSplitTitle != ''>
							<span class="bkcl">${detail.isSplitTitle}</span>
						</#if>
						</span>
					</div>
					<div class="tongyong-info">
						<span class="linfo-col1">生产厂家</span>
						<span class="rtext-co11">${detail.manufacturer}</span>
						<#--  <#if detail.productionAddress?? && detail.productionAddress!= '' >
							<span class="linfo-col2">生产厂家地址</span>
							<span class="rtext-col2">${detail.productionAddress}</span>
						</#if>  -->
						<#if detail.cnProductCode?? && detail.cnProductCode!= '' >
							<span class="linfo-col2">医保代码</span>
							<span class="rtext-col2" style="overflow: visible;">${detail.cnProductCode}</span>
							<a href="javascript:void(0);" class="copy-btn" onclick="copyMedicalCode()" 
							style="margin-left: 60px; font-size: 10px; cursor: pointer; border: 1px solid #CDCDCD; color: #000000; padding: 0 4px; height: 16px; text-align: center; border-radius: 2px;">复制</a>
						</#if>
					</div>
						<#--  <#if detail.categoryFirstId ?? && (detail.categoryFirstId == 100002 || detail.categoryFirstId == 100009) && detail.marketAuthor ?? && detail.marketAuthor !=''>
						<div class="tongyong-info">
							<span class="linfo-col1">上市许可持有人</span>
							<span class="rtext-co11">${detail.marketAuthor}</span>
							<#if detail.marketAuthorAddress ?? && detail.marketAuthorAddress != '' >
								<span class="linfo-col2">上市许可持有人地址</span>
								<span class="rtext-col2">${detail.marketAuthorAddress}</span>
							</#if>
						</div>
						</#if>  -->
                        <div class="tongyong-info">
					<#if detail.validity?? && detail.validity != ''>
                        <span class="linfo-col1">有效期至</span>
                        <span class="rtext-co11 nolimit">${detail.validity}</span>
					<#elseif (detail.nearEffect?? && detail.nearEffect != '') || (detail.farEffect?? && detail.farEffect != '') || (detail.effectStr?? && detail.effectStr != '')>
							<span class="linfo-col1">效　　期</span>
						<span class="rtext-co11 ">${detail.effectStr}</span>
<#--						<#if detail.nearEffect?? && detail.nearEffect != ''>-->
<#--							<#if detail.farEffect?? && detail.farEffect != ''>-->
<#--								<#if detail.farEffect =="-" && detail.nearEffect =="-">-->
<#--                                       <span class="rtext-co11 ">-</span>-->
<#--								<#else>-->
<#--									<span class="rtext-co11">近至${detail.nearEffect} 远至${detail.farEffect}</span>-->
<#--								</#if>-->
<#--							<#else >-->
<#--                                    <span class="rtext-co11">近至${detail.nearEffect}</span>-->
<#--							</#if>-->
<#--						<#else >-->
<#--							<#if detail.farEffect?? && detail.farEffect != ''>-->
<#--                                    <span class="rtext-co11">远至${detail.farEffect}</span>-->
<#--							</#if>-->
<#--						</#if>-->
					</#if>
							<span class="linfo-col2">生产日期</span>
							<#if detail.manufactureDate?? && detail.manufactureDate != ''>
								<span class="rtext-co11" style="margin-left: 10px">${detail.manufactureDate}</span>
							</#if>
                        </div>
						<!-- <#if detail.producer?? && detail.producer != ''>
							<div class="tongyong-info">
								<span class="linfo-col1">产　　地</span>
								<span class="rtext-co11">${detail.producer}</span>
							</div>
						</#if> -->
						<!--运费-->
						<#if detail.freightTips??>
                        <div class="tongyong-info">
                            <span class="linfo-col1">运　　费</span>
                            <span class="rtext-co11 nolimit" style="white-space: pre-wrap;">${detail.freightTips}</span>
                        </div>
						</#if>
						<#--  <#if detail.tracingCode?? && (detail.tracingCode == 1)>
							<div class="tongyong-info">
								<span class="linfo-col1">追&nbsp;溯&nbsp;&nbsp;码</span>
								<span class="rtext-co11 nolimit">有</span>
							</div>
						</#if>  -->
						<#if detail.nearEffectiveFlag??&& (detail.nearEffectiveFlag == 1 || detail.nearEffectiveFlag == 2)>
							<div class="tongyong-info d-tishi">
								<span class="rtext-co11 nolimit">【温馨提示】该商品为近效期商品，非质量问题（包装破损）概不退换</span>
							</div>
						<#elseif detail.activityType ?? && detail.activityType == 99>
							<div class="tongyong-info d-tishi">
								<span class="rtext-co11 nolimit">【温馨提示】该商品当前正在参与特价、秒杀或直降活动，非质量问题（包装破损）概不退换</span>
							</div>
						</#if>
						<#if (detail.approvalNumber?index_of('卫食健')>=0) || (detail.approvalNumber?index_of('国食健')>=0) || (detail.approvalNumber?index_of('食健备')>=0)|| (detail.approvalNumber?index_of('食药健')>=0) || (detail.approvalNumber?index_of('进食健')>=0) || (detail.approvalNumber?index_of('食健进')>=0) >
							<div class="tongyong-info d-tishi">
								<span class="rtext-co11 nolimit noCtrl">*保健食品是食品，不是药物，不能代替药物治疗疾病</span>
							</div>
						</#if>
                        <#if detail.skuCategory?? &&  detail.skuCategory='化妆品'>
                            <div class="tongyong-info d-tishi">
                                <span class="rtext-co11 nolimit noCtrl">国家药监局提示您：化妆品是日用消费品，不得明示或暗示具有医疗作用。</span>
                            </div>
                        </#if>
						<#--<#if detail.isThirdCompany?? && detail.isThirdCompany != '' && detail.isThirdCompany == 1>-->
						    <#--<div class="tongyong-info">-->
                                <#--<span class="linfo-col1">第三方发货</span>-->
                                <#--<a href="/search/thirdSkuInfo.htm?orgId=${detail.orgId}&companyName=${detail.companyName}" target="_Blank" class="rtext-co11 dsffahuo">${detail.companyName}<i class="sui-icon icon-chevron-sign-right"></i></a>-->
                            <#--</div>-->
						<#--</#if>-->
						<#if merchant.licenseStatus ==1 && detail.fob == '0'>

						<#elseif merchant.licenseStatus ==5 && detail.fob == '0'>

						<#elseif detail.agreementEffective?? && detail.agreementEffective =='3'>

						<#else >
                        <div class="goumaibox">
                            <div class="goumaititle fl">
                                购买数量
                            </div>
                            <div class="suliang fl">
                                <a href="javascript:void(0);" class="sub fl">-</a>
								<#if isAssemble == 1 && actPt.assembleStatus == 1 >
									<input class="fl" type="text" value="${detail.leastPurchaseNum}" id="buyNumDl_${detail.id}" name="buyNumDl"
										   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
										   onblur="handleCheckValue(${detail.id},${actPt.skuStartNum})"
										   onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}" isSplit="${detail.isSplit}" middpacking="${detail.mediumPackageNum}" skuStartNum="${actPt.skuStartNum}"/>
								<#elseif isWholesale == 1 && actPgby.assembleStatus == 1>
									<input class="fl" type="text" value="${detail.leastPurchaseNum}" id="buyNumDl_${detail.id}" name="buyNumDl"
										   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
										   onblur="handleCheckValue(${detail.id},${actPgby.skuStartNum}, 3)"
										   onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}" isSplit="${detail.isSplit}" middpacking="${detail.mediumPackageNum}" skuStartNum="${actPt.skuStartNum}"/>
								<#else >
									<input class="fl" type="text" value="${detail.cartProductNum}" id="buyNumDl_${detail.id}" name="buyNumDl"
										   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
										   onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}" isSplit="${detail.isSplit}" middpacking="${detail.mediumPackageNum}"/>
								</#if>
                        		<a href="javascript:void(0);" class="add fl">+</a>
                            </div>
                            <div class="kucun fl">
                                <span style="display: none">普通库存库存：${detail.normalCacheQty} 展示库存:${detail.availableQty}</span>
                                <span>当前库存：</span>
                                <span>
									<#if detail.isSplit == 0 && detail.availableQty - detail.mediumPackageNum lt 0>
										0
									<#else >
										<#if (detail.availableQty > 100)>
											大于100
										<#else >
											${detail.availableQty}
										</#if>
									</#if>
                                </span>
								<#if detail.leastPurchaseNum?? && detail.leastPurchaseNum != 0>
									<span class="bkcl">
										${detail.leastPurchaseNum}${detail.productUnit}起购
									</span>
								</#if>
								<#if detail.promotionTag ??>
									<span class="bkcl">
										${detail.promotionTag}
									</span>
								</#if>
                            </div>
                        </div>
						</#if>
                        <div class="soucang">
							<#if merchant.licenseStatus ==1 && detail.fob == '0'>
							<div class="verifyBox">
								<a class="addbuy" href="/merchant/center/license/findLicenseCategoryInfo.htm">资质认证</a>
							</div>
							<#elseif merchant.licenseStatus ==5 && detail.fob == '0'>
								<div class="verifyBox">
									<a class="addbuy" href="/merchant/center/license/findLicenseCategoryInfo.htm">资质审核中</a>
								</div>
							<#elseif detail.agreementEffective?? && detail.agreementEffective =='3'>
<#--								<span class="price">协议已冻结，价格解冻后可见</span>-->
							<#else >
                         <#if merchantBusiness != null>
                        		<input id="businessType" value="${merchantBusiness.businessType}" hidden/>
                         </#if>

                        <#if (detail.status == 1 && detail.availableQty == 0) || detail.status == 2 || ((detail.status == 3 || detail.status == 5) && detail.promotionTotalQty == 0) || (detail.isSplit == 0 && detail.availableQty - detail.mediumPackageNum lt 0)>
                            <#if merchantBusiness == null>
								<a href="javascript:void(0);" onclick="hasGoodsAlert(${detail.id},this);"  class="addbuy reminder">有货提醒</a>
							<#else>
							   <#if merchantBusiness.businessType == 1>
							   		<a href="javascript:void(0);"  class="addbuy subscribed-stock-reminder">已订阅有货提醒</a>
							   <#else>
							      	<a href="javascript:void(0);" onclick="hasGoodsAlert(${detail.id},this);"  class="addbuy reminder">有货提醒</a>
							   </#if>

							</#if>
						<#else>
							<#if detail.isGive==1>
								<#if detail.canAddToCart ?? && detail.canAddToCart>
									<#if isAssemble == 1 && actPt.assembleStatus == 1>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(6,'加入采购单')">加入采购单</a>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(7,'立即参团')" >立即参团</a>
									<#elseif isAssemble == 1 && actPt.assembleStatus != 1 && actPt.preheatShowPrice != 1>
										<a href="javascript:void(0);" style="font-size: 14px"  class="disableBuy" onclick="action_sub_module_click(6,'开团')">${actPt.assembleStartTime?string('MM月dd日 HH:mm:ss')} 开团</a>
									<#elseif isWholesale == 1 && actPgby.assembleStatus == 1>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(6,'加入采购单')">加入采购单</a>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(7,'去抢购')">去抢购</a>
									</#if>
								<#else>
									<#if isAssemble == 1 && actPt.assembleStatus == 1>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(6,'立即参团')">立即参团</a>
									<#elseif isAssemble == 1 && actPt.assembleStatus != 1 && actPt.preheatShowPrice != 1>
										<a href="javascript:void(0);" style="font-size: 14px"  class="disableBuy" onclick="action_sub_module_click(6,'开团')">${actPt.assembleStartTime?string('MM月dd日 HH:mm:ss')} 开团</a>
									<#elseif isWholesale == 1 && actPgby.assembleStatus == 1>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(6,'去抢购')">去抢购</a>
									<#else >
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(6,'加入采购单')" >加入采购单</a>
									</#if>
								</#if>
								
							<#elseif detail.merchantStatus?? && detail.merchantStatus == 2>
								<#if detail.canAddToCart ?? && detail.canAddToCart>
									<#if isAssemble == 1 && actPt.assembleStatus == 1>
										<a href="javascript:void(0);"  class="disableBuy"  onclick="action_sub_module_click(6,'加入采购单')">加入采购单</a>
										<a href="javascript:void(0);"  class="disableBuy"  onclick="action_sub_module_click(7,'立即参团')">立即参团</a>
									<#elseif isAssemble == 1 && actPt.assembleStatus != 1 && actPt.preheatShowPrice != 1>
										<a href="javascript:void(0);" style="font-size: 14px"  class="disableBuy"  onclick="action_sub_module_click(6,'开团')">${actPt.assembleStartTime?string('MM月dd日 HH:mm:ss')} 开团</a>
									<#elseif isWholesale == 1 && actPgby.assembleStatus == 1>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(6,'加入采购单')">加入采购单</a>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(7,'去抢购')">去抢购</a>
									</#if>
								<#else>
									<#if isAssemble == 1 && actPt.assembleStatus == 1>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(6,'立即参团')" >立即参团</a>
									<#elseif isAssemble == 1 && actPt.assembleStatus != 1 && actPt.preheatShowPrice != 1>
										<a href="javascript:void(0);" style="font-size: 14px"  class="disableBuy" onclick="action_sub_module_click(6,'开团')">${actPt.assembleStartTime?string('MM月dd日 HH:mm:ss')} 开团</a>
									<#elseif isWholesale == 1 && actPgby.assembleStatus == 1>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(6,'去抢购')">去抢购</a>
									<#else>
										<a href="javascript:void(0);"  class="disableBuy" onclick="action_sub_module_click(6,'加入采购单')">加入采购单</a>
									</#if>
								</#if>
							<#elseif (sourceId)??>
								<#if detail.canAddToCart ?? && detail.canAddToCart>
									<#if isAssemble == 1 && actPt.assembleStatus == 1>
										<a href="javascript:void(0);"  class="addbuy" onclick="action_sub_module_click(6,'加入采购单');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,${sourceId}, '', { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_ADD_CART_${detail.id}">加入采购单</a>
										<a href="javascript:void(0);"  class="addbuy ptpgbyAddbuy" onclick="action_sub_module_click(7,'立即参团');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,${sourceId},7, { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_${detail.id}">立即参团</a>
									<#elseif isAssemble == 1 && actPt.assembleStatus != 1 && actPt.preheatShowPrice != 1>
										<a href="javascript:void(0);"  style="font-size: 14px" onclick="action_sub_module_click(6,开团)" class="addbuy" id="href_DETAIL_${detail.id}">${actPt.assembleStartTime?string('MM月dd日 HH:mm:ss')} 开团</a>
									<#elseif isWholesale == 1 && actPgby.assembleStatus == 1>
										<a href="javascript:void(0);"  class="addbuy ptpgbyAddbuy" onclick="action_sub_module_click(6,'加入采购单');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,${sourceId}, '', { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_ADD_CART_${detail.id}">加入采购单</a>
										<a href="javascript:void(0);" style="background-color:#FF6204;"  class="addbuy" onclick="action_sub_module_click(7,'去抢购');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,${sourceId},7, { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_${detail.id}">去抢购</a>
									</#if>
								<#else>
									<#if isAssemble == 1 && actPt.assembleStatus == 1>
										<a href="javascript:void(0);"  class="addbuy" onclick="action_sub_module_click(6,'立即参团');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,${sourceId},7,{ isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_${detail.id}">立即参团</a>
									<#elseif isAssemble == 1 && actPt.assembleStatus != 1 && actPt.preheatShowPrice != 1>
										<a href="javascript:void(0);"  style="font-size: 14px" class="addbuy" id="href_DETAIL_${detail.id}" onclick="action_sub_module_click(6,'开团')">${actPt.assembleStartTime?string('MM月dd日 HH:mm:ss')} 开团</a>
									<#elseif isWholesale == 1 && actPgby.assembleStatus == 1>
										<a href="javascript:void(0);" style="background-color:#FF6204;"  class="addbuy" onclick="action_sub_module_click(6,'去抢购');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,${sourceId},7,{ isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_${detail.id}">去抢购</a>
									<#else>
										<a href="javascript:void(0);"  class="addbuy" onclick="action_sub_module_click(6,'加入采购单');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,${sourceId}, '', { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_ADD_CART_${detail.id}" >加入采购单</a>
									</#if>
								</#if>
								
							<#else>
								<#if detail.canAddToCart ?? && detail.canAddToCart>
									<#if isAssemble == 1 && actPt.assembleStatus == 1>
										<a href="javascript:void(0);"  class="addbuy" onclick="action_sub_module_click(6,'加入采购单');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event, '', '', { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` })" id="href_DETAIL_ADD_CART_${detail.id}">加入采购单</a>
										<a href="javascript:void(0);"  class="addbuy ptpgbyAddbuy" onclick="action_sub_module_click(7,'立即参团');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,'',7, { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` })" id="href_DETAIL_${detail.id}">立即参团</a>
									<#elseif isAssemble == 1 && actPt.assembleStatus != 1 && actPt.preheatShowPrice != 1>
										<a href="javascript:void(0);" style="font-size: 14px"  class="addbuy" id="href_DETAIL_${detail.id}">${actPt.assembleStartTime?string('MM月dd日 HH:mm:ss')} 开团</a>
									<#elseif isWholesale == 1 && actPgby.assembleStatus == 1>
										<a href="javascript:void(0);"  class="addbuy" onclick="action_sub_module_click(6,'加入采购单');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event, '', '', { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_ADD_CART_${detail.id}">加入采购单</a>
										<a href="javascript:void(0);" style="background-color:#FF6204;"  class="addbuy ptpgbyAddbuy" onclick="action_sub_module_click(7,'去抢购');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,'',7, { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_${detail.id}">去抢购</a>
									</#if>
								<#else>
									<#if isAssemble == 1 && actPt.assembleStatus == 1>
										<a href="javascript:void(0);"  class="addbuy" onclick="action_sub_module_click(6,'立即参团');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,'',7, { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_${detail.id}">立即参团</a>
									<#elseif isAssemble == 1 && actPt.assembleStatus != 1 && actPt.preheatShowPrice != 1>
										<a href="javascript:void(0);" style="font-size: 14px"  class="addbuy" id="href_DETAIL_${detail.id}" onclick="action_sub_module_click(6,'开团')">${actPt.assembleStartTime?string('MM月dd日 HH:mm:ss')} 开团</a>
									<#elseif isWholesale == 1 && actPgby.assembleStatus == 1>
										<a href="javascript:void(0);" style="background-color:#FF6204;"  class="addbuy" onclick="action_sub_module_click(6,'去抢购');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event,'',7, { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_${detail.id}">去抢购</a>
									<#else>
										<a href="javascript:void(0);"  class="addbuy" onclick="action_sub_module_click(6,'加入采购单');addCartDetail(${detail.id},${detail.mediumPackageNum},${detail.isSplit},event, '', '', { isJgMd: 1, product_first: `${detail.categoryFirstId}`, product_present_price: `${detail.fob}`, product_type: `${detail.productType}`, product_shop_code: `${detail.shopCode}` });" id="href_DETAIL_ADD_CART_${detail.id}">加入采购单</a>
									</#if>
								</#if>
							</#if>
						</#if>
							</#if>
							<#if detail.favoriteStatus == 1 >
								<div class="w-collectZone_list hasCollect initial j-collectBtn fl" id="${detail.id}" onclick="removeSC(${detail.id},event,this)" >
									<div class="zone-1">
										<div class="top top-1">
											<span class="w-icon-normal icon-normal-collectEpt"></span>
										</div>
										<div class="top top-2">
											<span class="w-icon-normal icon-normal-collectFull"></span>
										</div>
										<span class="sc-wenzhi">收藏</span>
									</div>
									<div class="zone-2">
										<div class="bottom bottom-1">
											<#--<p class="textOne">收藏</p>-->
										</div>
										<div class="bottom bottom-2">
											<#--<p class="textTwo">已收藏</p>-->
										</div>
									</div>
								</div>
							<#else>
								<div class="w-collectZone_list nopCollect initial j-collectBtn fl" id="${detail.id}" onclick="addDTSX(${detail.id},event,this)" >
									<div class="zone-1">
										<div class="top top-1">
											<span class="w-icon-normal icon-normal-collectEpt"></span>
										</div>
										<div class="top top-2">
											<span class="w-icon-normal icon-normal-collectFull"></span>
										</div>
										<span class="sc-wenzhi">收藏</span>
									</div>
									<div class="zone-2">
										<div class="bottom bottom-1">
											<#--<p class="textOne">收藏</p>-->
										</div>
										<div class="bottom bottom-2">
											<#--<p class="textTwo">已收藏</p>-->
										</div>
									</div>

								</div>
							</#if>
                        </div>
						<#if detail.isEphedrine == 1>
							<div class="text-ma">*特殊管理药品需线下采购</div>
						</#if>
					</div>
				</div>

				<!--商品套餐-->
				<#if productPrckagelList??&&(productPrckagelList?size>0)>
				<div class="taocan-box">
					<div class="taocan-head">
						<span class="taocan-title">组合套餐</span>
						<#if productPrckagelList[0].descriptionPCUrl?? && productPrckagelList[0].descriptionPCUrl != null && productPrckagelList[0].descriptionPCUrl !="">
                        <a href="${productPrckagelList[0].descriptionPCUrl}">
                            <span class="taocan-more">更多</span>
                            <img src="/static/images/shou.png" alt="" class="more-icon">
						</a>
						</#if>
					</div>
                    <div class="xstcn">
                        <!--循环套餐开始-->
						<#list productPrckagelList as package>
						<div class="modelbox vipa">
							<div class="tcnbox">
								<div class="titlebox" id="md-link1"></div>
								<#if package??>
								<!--套餐头部开始-->
									<!--价格-->
									<div class="pricebox">
										<#if merchant != null>
											<#if merchant.licenseStatus ==1 || merchant.licenseStatus ==5>
												<span class="login_tit">套餐价：</span><span class="login_show">含税价认证资质后可见</span>
											<#else>
												<span class="tcprice-t">套餐价 : </span><span class="tcprice-f">¥${package.packagePrice}</span>
												<span class="tcprice">(共${package.skuNum}种)</span>
												<span class="yprice-t">原价 : ￥${package.subtotalPrice}</span>
											</#if>
										<#else>
											<!--价格登录可见&ndash;&gt;-->
											<span class="login_tit">套餐价：</span><span class="login_show">价格登录可见</span>
										</#if>
									</div>
									<#--<#if package.status !=1>-->
										<#--<#if package.saleCountDown??&& package.saleCountDown gt 0>-->
											<#--<div class="jp-quality">-->
												<#--<div class="timeBox"-->
													 <#--data-times="${package.startSaleTime?string("yyyy/MM/dd,HH:mm:ss")}">-->
													<#--距离开抢还剩：-->
													<#--<span class="time date"> </span>-->
													<#--<span class="time hour"> </span> :-->
													<#--<span class="time minutes"> </span> :-->
													<#--<span class="time seconds"></span>-->
												<#--</div>-->
											<#--</div>-->
										<#--<#else >-->
										<#--<!--加入购物车&ndash;&gt;-->
										<div class="resultbox">
											<div class="row6">
												<#if merchant.licenseStatus ==1>
													<a class="zizhi" href="/merchant/center/license/findLicenseCategoryInfo.htm">资质认证</a>
												<#elseif merchant.licenseStatus ==5>
													<a class="zizhi" href="/merchant/center/license/findLicenseCategoryInfo.htm">资质审核中</a>
												<#else>
													<#if package.canBuy == '1'>
														<a href="javascript:void(0);" class="subt fl">-</a>
														<input class="fl" type="text" id="num${package.packageId}"
															   value="${package.packageCartCount!0}"
															   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
															   onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"/>
														<a href="javascript:void(0);" class="addt fl" id="tc_add">+</a>
															<#if  merchant != null>
																<a href="javascript:void(0);" class="buy fl"
																   onclick="changeCartPackage(${package.packageId},event,this)">加入采购单</a>
															</#if>

													</#if>
												</#if>
											</div>
										</div>
										<#--</#if>-->
									<#--</#if>-->
									<!--套餐头部结束-->

									<!--套餐商品列表-->
									<div class="tcwarp">
										<!--循环套餐商品开始-->
										<#list package.skuList as packageProduct>
											<#--<#if (packageProduct_index != 0 && ((packageProduct_index+1) % 3 = 0))>-->
												<#--<div class="itembox three-n">-->
											<#--<#elseif !packageProduct_has_next>-->
												<#--<div class="itembox last">-->
											<#--<#else>-->
												<div class="itembox">
											<#--</#if>-->
													<div class="row1">
														<a href="/search/skuDetail/${packageProduct.skuId}.htm">
															<img src="${productImageUrl}/ybm/product/${packageProduct.imageUrl}" alt="" onerror="this.src='/static/images/default-middle.png'"/>
														</a>
														<#--<!--标签&ndash;&gt;-->
														<div class="bq-box"></div>
													</div>
													<div class="row2">
														<a href="/search/skuDetail/${packageProduct.skuId}.htm">${packageProduct.commonName}</a>
													</div>
													<div class="row4 text-overflow">
														${packageProduct.spec}
													</div>
<#--													<div class="row5 text-overflow">-->
<#--														${packageProduct.sku.manufacturer}-->
<#--													</div>-->
													<div class="row3">
													<#if  merchant != null>
														<!--正常显示价格样式&ndash;&gt;-->
														<#if merchant.licenseStatus ==1 || merchant.licenseStatus ==5>
															<span class="login_show">含税价认证资质后可见</span>
														<#elseif packageProduct.showAgree =='0'>
															<span class="login_show">价格签署协议可见</span>
														<#elseif packageProduct.isOEM?? && packageProduct.isOEM == 'true' && packageProduct.signStatus == '0'>
															<span class="login_show">价格签署协议可见</span>
														<#else>
															<span class="meiyuan">￥</span><span class="price">${packageProduct.fob}</span>
														</#if>
														<span class="tcsj">x${packageProduct.productNumber}</span>
<#--														<#if packageProduct.sku.nearEffectiveFlag == 1>-->
<#--														<span class="quan">临期</span>-->
<#--														</#if>-->
<#--														<#if packageProduct.sku.nearEffectiveFlag == 2>-->
<#--														<span class="quan">近效期</span>-->
<#--														</#if>-->
													<#else>
														<!--价格登录可见样式&ndash;&gt;-->
														<span class="login_show">价格登录可见</span>
													</#if>
													</div>
												</div>
											</#list>
										<!--循环套餐商品结束-->
									</div>
								</#if>
							</div>
						</div>
						</#list>
						</#if>
                        <!--循环套餐结束-->
                    </div>
				</div>
				<#--  组合购  -->
				<iframe id="combination" src="/newstatic/#/combination/index?id=${id}&merchantId=${merchantId}" height="280px" style="overflow: hidden;width: 100%;margin-top: 20px; border: none;display:none;" >组合购</iframe>
				<!--商品详情规格引入static里面的组件-->
				<iframe id="spuFather" src="/newstatic/#/productSpu?id=${id}&merchantId=${merchantId}&isVirtual=${shopInfo.shopPatternCode}&isShopDetail=1" width="100%" height="370px" style="margin-top: 20px; border: none;"></iframe>
				<!--content-->
			
				<div class="c-main">
					<!--热销商品-->
                    <!-- 店铺信息展示 -->
				<#if detail.isThirdCompany == 1>
                    <div class="xq-qy fl">
						<#if companyDetail != null>
                            <div class="qy-title">${companyDetail.orgName}</div>
                            <div class="qy-shuliang">
                                <div class="qy-zhong fl">
                                    <p class="qy-row1" id="upSkuNum">${upSkuNum}种</p>
                                    <p class="qy-row2">上架商品</p>
                                </div>
                                <div class="qy-center fl"></div>
                                <div class="qy-jian fl">
									<p class="qy-row1" id="saleNum">${saleNum}件</p>
                                    <p class="qy-row2">销量</p>
                                </div>
                            </div>

                            <div class="qy-dianpu">
                                <a href="/company/center/companyInfo/shopIndex.htm?orgId=${companyDetail.orgId}">
                                    进入店铺
                                </a>
								<#if  merchant != null>
                               <#if shopInfo.shopPatternCode != null && shopInfo.shopPatternCode == "virtual">
                               <#else >
                                <a href="javaScript:callKf('','${merchant.id}','${companyDetail.orgId}','${detail.isThirdCompany}','${companyDetail.orgName}');" style="background:#fff;color:#00c675;border:1px solid #00c675;">
                                    联系商家
                                </a>
                                </#if>
								</#if>
                            </div>

						<#else>

						</#if>
                    </div>
				<#else >
					<div class="shop-box">
						<div class="shop-row1">
							<img src="${shopInfo.shopLogoUrl}" onerror="this.src='/static/images/shop-default.png'" alt="">
						</div>
						<div class="shop-row2">
							<span title="${shopInfo.shopName}">${shopInfo.shopName}</span>
                           <#if shopInfo.shopPatternCode != null && shopInfo.shopPatternCode == "virtual">
                           <#else >
                                 <a href="javaScript:callKf('','${merchant.id}');">
								<img src="/static/images/kefu.png" alt="">
							     </a>
                            </#if>

						</div>
						<div class="shop-row3">
							<#list shopInfo.shopTag as tag>
								<#if tag == '自营'>
									<span class="grn">自营</span>
								<#else>
									<span class="blu">${tag}</span>
								</#if>
							</#list>
						</div>
						<div class="shop-row4">
							<a href="${shopInfo.shopHomeUrl}">
								<img src="/static/images/shop.png" alt="" style="margin-top: -2px;margin-right: 4px;">进店逛逛
							</a>
						</div>
					</div>
				</#if>



					<!--商品详情-->
					<div class="xq-box fr">
						<ul class="sptab">
							<li class="spxqfn cur">商品详情</li>
							<#if isAssemble == 1 && actPt.assembleStatus == 1>
								<li class="shwyfn">拼团信息</li>
							</#if>
							<li class="ybmfn">关于药帮忙</li>
						<#--<li class="shwyfn">售后无忧</li>-->
						</ul>
						<!--商品详情-->
						<div class="c-spxq">
                            <!--商品推荐语-->
							<#if (detail.sellingProposition1 ? exists && detail.sellingProposition1!= '') || (detail.sellingProposition2 ? exists && detail.sellingProposition2!= '') || (detail.sellingProposition3 ? exists && detail.sellingProposition3!= '')>
								<div class="rec-box">
                                    <h2>推荐理由：</h2>
                                    <ul>
										<#if (detail.sellingProposition1 ? exists && detail.sellingProposition1!= '')>
										    <li class="rec-icon1">${detail.sellingProposition1}</li>
										</#if>
										<#if (detail.sellingProposition2 ? exists && detail.sellingProposition2!= '')>
										    <li class="rec-icon2">${detail.sellingProposition2}</li>
										</#if>
										<#if (detail.sellingProposition3 ? exists && detail.sellingProposition3!= '')>
										    <li class="rec-icon3">${detail.sellingProposition3}</li>
										</#if>
                                    </ul>
                                </div>
							</#if>
							<!--视频-->
							<#if detail.videoUrl ??>
								<input type="hidden" id="videoUrl" value="${productImageUrl}/ybm/product/video/${detail.videoUrl}">
								<div class="video" id="video"></div>
							</#if>
							<!--商品说明书-->
							<div class="smsbox">
								<div class="bg_color">
									<div class="bg_color_weight">温馨提示</div>
									<div class="bg_color_two">1、如页面标注了“医保甲类”或“医保乙类”，表示为国家医保局的公示品种，与省医保局公示品种可能存在差异，请以当地医保局公示品种为准；2、部分商品包装更换频繁，如商品与图片不完全一致，如页面展示图片上的生产日期、批号、有效期等信息，请以收到的商品实物为准。</div>
								</div>
								<table>
									<tr class="first">
										<td class="td1 graybd">商品分类</td>
										<td class="td2" id="categorySecondName"></td>
										<td class="td3 graybd">生产厂家</td>
										<td class="td4">${detail.manufacturer}</td>
									</tr>
									<#if detail.categoryFirstId ?? && (detail.categoryFirstId == 100002 || detail.categoryFirstId == 100009) && detail.marketAuthor ?? && detail.marketAuthor !=''>
										<tr>
											<td class="td1 graybd">上市许可持有人</td>
											<td class="td2">${detail.marketAuthor}</td>
											<#if detail.marketAuthorAddress ?? && detail.marketAuthorAddress != '' >
												<td class="td3 graybd">上市许可持有人地址</td>
												<td class="td4">${detail.marketAuthorAddress}</td>
											</#if>
										</tr>
									</#if>
									<tr>
										<#if detail.categoryFirstId ?? && detail.categoryFirstId != 100004 && detail.categoryFirstId != 100010>
											<#if detail.categoryFirstId ?? && detail.categoryFirstId == 100005>
												<td class="td1 graybd">医疗器械注册证或备案凭证编号</td>
											<#elseif detail.skuCategory ?? && detail.skuCategory == '化妆品'>
												<td class="td1 graybd">化妆品备案编号/注册证号</td>
											<#else >
												<td class="td1 graybd">批准文号</td>
											</#if>
											<td class="td2">${detail.approvalNumber}</td>
										</#if>
											<#if (detail.categoryFirstId == 68)>
												<td class="td3 graybd">生产日期</td>
											<#else>
												<td class="td3 graybd">生产日期</td>
											</#if>
										<td class="td4">${detail.manufactureDate}</td>
										<#if detail.producer != ''>
											<td class="td3 graybd">产地</td>
											<td class="td4">${detail.producer}</td>
										</#if>
									</tr>
									<tr>
										<#if detail.categoryFirstId ?? && detail.categoryFirstId == 100005>
											<td class="td1 graybd">型号、规格</td>
										<#else >
											<td class="td1 graybd">规格</td>
										</#if>
										<td class="td2">${detail.spec}</td>
										<#if detail.productionAddress ?? && detail.productionAddress != ''>
											<td class="td3 graybd">生产厂家地址</td>
											<td class="td4">${detail.productionAddress}</td>
										</#if>										
<#--										<td class="td3 graybd">储存条件</td>-->
<#--										<td class="td4">${detail.storageCondition}</td>-->
									</tr>
									<#if detail.skuExtAttrDTOS?? && detail.skuExtAttrDTOS?size gt 0>
										<#assign skuExtAttrJson>
											[
											<#list detail.skuExtAttrDTOS as attr>
												{
													"attrTitle": "${(attr.attrTitle!'')?replace('"', '\\"')}",
													"attrValue": "${(attr.attrValue!'')?replace('"', '\\"')}"
												}<#if attr_has_next>,</#if>
											</#list>
											]
										</#assign>
										<input type="hidden" id="skuExtAttrDTOS" value='${skuExtAttrJson?trim}'/>
									<#else>
										<input type="hidden" id="skuExtAttrDTOS" value='[]'/>
									</#if>
										<#if detail.skuCategory ?? && detail.skuCategory == '化妆品' && detail.filingsAuthor ?? && detail.filingsAuthor != ''>
									<tr>
											<td class="td1 graybd">化妆品备案人/注册人</td>
											<td class="td2">${detail.filingsAuthor}</td>
									</tr>

										</#if>
									<tr>
										<td class="td1 graybd">中包装</td>
										<td class="td2">${detail.mediumPackageNum}${detail.productUnit}
											<span class="bkcl-td">${detail.isSplitTitle}</span>
										</td>
										<td class="td3 graybd">件装量</td>
										<td class="td4">${detail.pieceLoading}</td>
									</tr>
									<#if detail.categoryFirstId ?? && detail.categoryFirstId == 100005>
										<tr>
											<td class="td1 graybd">生产许可证号或备案凭证编号</td>
											<td class="td2">${detail.manufacturingLicenseNo}
											</td>
											<td class="td3 graybd">产品技术要求编号</td>
											<td class="td4">${detail.technicalRequirementNo}</td>
										</tr>
										<tr>
											<td class="td1 graybd">结构及组成</td>
											<td class="td2">${detail.component}</td>
<#--											<td class="td3 graybd">禁忌症</td>-->
<#--											<td class="td4">${detail.abstain}</td>-->
										</tr>
										<tr>
											<td class="td3 graybd">适用范围</td>
											<td class="tdspe3" colspan="3">${detail.indication}</td>
										</tr>
									<#else>
										<tr>
											<#if (detail.skuCategory!'') != '化妆品'>
												<td class="td1 graybd">适应症/功能主治</td>
											<#else >
												<td class="td1 graybd">功效宣称</td>
											</#if>
											<td class="tdspe3" colspan="3">${detail.indication}</td>
										</tr>
										<#if (detail.skuCategory!'') != '化妆品'>
											<tr>
												<td class="td1 graybd">用法与用量</td>
												<td class="tdspe3" colspan="3">${detail.usageAndDosage}</td>
											</tr>
											<tr>
												<td class="td1 graybd">注意事项</td>
												<td class="tdspe3" colspan="3">${detail.considerations}</td>
											</tr>
											<tr>
												<td class="td1 graybd">禁忌症</td>
												<td class="tdspe3" colspan="3">${detail.abstain}</td>
											</tr>
											<#if detail.categoryFirstId ?? && detail.categoryFirstId != 100004 && detail.categoryFirstId != 100010>
												<tr class="end">
													<td class="td1 graybd">不良反应</td>
													<td class="tdspe3" colspan="3">${detail.untowardEffect}</td>
												</tr>
											</#if>
										</#if>
										
									</#if>
								</table>
							</div>
							<!--图文说明-->
							<#--<div class="twsm-tit">-->
							<#--图文说明-->
							<#--</div>-->
							<div class="twsm-imgbox">
								<#list detail.skuInstructionImageList as descImg>
                                    <img src="${productImageUrl}/ybm/product/desc/${descImg.instrutionImageUrl}" alt="">
								</#list>
							</div>
						</div>
						<!--关于药帮忙-->
						<div class="c-ybm">
							<img src="/static/images/gyybm.png" alt="">
						</div>
						<!--售后无忧-->
						<div class="c-shwy">
<#--							<p class="topimg"><img src="/static/images/shwybg.jpg" alt=""></p>-->
<#--							<p class="jg60">为保证客户的权益，达到合作共赢，药帮忙平台特承诺以下售后无忧政策，请审阅：</p>-->
<#--							<p class="common"><span>签收货物温馨提示<img src="/static/images/yousj.png" ></span></p>-->
<#--							<p class="suojing jg20">客户收到货物后请仔细清点检查所订购的商品，如发现商品漏发、包装破损、受潮，商品与网站上图片不符等问题，请拍下照片取证，并于24个小时之内联系药帮忙平台客服申请退货或重发。</p>-->

<#--							<p class="common"><span>一、无忧售后服务内容：<img src="/static/images/yousj.png" ></span></p>-->
<#--							<p class="jg20"><span class="spespan">普药45天无忧退货</span>  自客户签收之日起45天内可享受无忧退货；由客户提出退货申请并附上商品名称和批号，由药帮忙平台核实确系我司所售出的商品，且内外包装、封签完好，在不影响二次销售并符合办理退货约束条件的，均可办理退货；因买家原因产生的退货费用由其自行承担。</p>-->
<#--							<p class="jg20"><span class="spespan">新品60天无忧退货</span>  自客户签收之日起60天内可享受无忧退货，新品定义：页面上标有“新品”角标的商品均为新品；由买家提出退货申请并附上商品名称和批号，由药帮忙平台核实确系我司所售出的商品，且内外包装、封签完好，在不影响二次销售并符合办理退货约束条件的，均可办理退货；因买家原因产生的退货费用由其自行承担。</p>-->
<#--							<p class="jg20">退货申请受理后，我司将在收到退货3个工作日内，完成退货退款处理。</p>-->

<#--							<p class="common"><span>二、退货方式：<img src="/static/images/yousj.png" ></span></p>-->
<#--							<p class="listbox"><span class="list"><span>1<img src="/static/images/yousj.png" ></span></span><span>自配区域，由我司工作人员上门取件；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>2<img src="/static/images/yousj.png" ></span></span><span>物流配送区域，需客户自行发快递退回，快递费用以实际支付为准，如需我司承担运费，则请客户先行垫付，我司同商品退款一同退回至客户账号（快递费最高不超过20元）。</span></p>-->


<#--							<p class="common"><span>三、退货费用：<img src="/static/images/yousj.png" ></span></p>-->
<#--							<p class="listbox">除商品本身质量或包裹破损问题产生的退货运费由药帮忙平台承担，其他情况一律由客户承担退货运费。</p>-->

<#--							<p class="common"><span>四、以下情况不列入退货范围：<img src="/static/images/yousj.png" ></span></p>-->
<#--							<p class="listbox"><span class="list"><span>1<img src="/static/images/yousj.png" ></span></span><span>页面明确标注不退换商品；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>2<img src="/static/images/yousj.png" ></span></span><span>非本公司销售的商品；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>3<img src="/static/images/yousj.png" ></span></span><span>需特殊保管储藏的商品（冷藏性生物制品、低温储藏商品、血液制品 等）、参茸制品、医用器械、成人用品，定制商品除外；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>4<img src="/static/images/yousj.png" ></span></span><span>未经客服同意，客户自行退回的商品；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>5<img src="/static/images/yousj.png" ></span></span><span>由于客户自身原因造成商品破损及损坏的（例如：因非正常使用和储藏而出现质量问题的、食物或液体溅落造成损坏、未经授权或任意的修理、改动而影响商品正常使用）；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>6<img src="/static/images/yousj.png" ></span></span><span>商品的正常磨损；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>7<img src="/static/images/yousj.png" ></span></span><span>超出售后退货时限的商品；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>8<img src="/static/images/yousj.png" ></span></span><span>所需退货商品的名称、批号、规格（型号）与售出时不符；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>9<img src="/static/images/yousj.png" ></span></span><span>不能提供我司销售出库复核单；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>10<img src="/static/images/yousj.png" ></span></span><span>退货商品不能小于我司销售的规格（例如我司售卖的是12s*6，退货需以此为单位）；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>11<img src="/static/images/yousj.png" ></span></span><span>人为下错商品；</span></p>-->
<#--							<p class="listbox"><span class="list"><span>12<img src="/static/images/yousj.png" ></span></span><span>影响二次销售的商品。</span></p>-->

<#--							<p class="common"><span>五、其他特殊约定<img src="/static/images/yousj.png" ></span></p>-->
<#--							<p class="listbox suojing">客户退货商品，药帮忙平台将依据出库单进行一一核查，退货商品如参加平台满减、返利或返劵等活动，药帮忙平台将自动取消相应权利。统一售后客服热线：400-0505-111-->
<#--							</p>-->
<#--							<p class="bottom">注：以上无忧售后政策最终解释权归本公司所有</p>-->

							<div style="height: 500px;position: relative">
								<#if actPt.subTitleNum ?? && (actPt.subTitleNum != 0)>
									<div class="infoDiv">
										<span class="infoTip">拼团中</span>
										<span class="infoText">
											${actPt.subTitle}
											<span style="color: #000;font-size: 14px;margin-left: 2px;vertical-align: text-bottom;" > /已拼${actPt.orderNum}${detail.productUnit}</span>
										</span>
									</div>
								</#if>
								<div class="scrollDiv">
                                    <ul id="slide">
									<#if actPt.subTitleNum ?? && (actPt.subTitleNum != 0)>
										<#list actPt.assembleOrderList as assemble>
                                            <li>
                                            												<span>
                                                                                                <span>${assemble.merchantName}</span>
                                            												    <span class="paddingSpan">${assemble.productAmount}</span>
                                                                                            </span>
                                                <span>${assemble.createTime}</span>
                                            </li>
                                        </#list>
									</#if>
                                    </ul>
                                </div>
							</div>

						</div>
					</div>

                    <div class="rx-box fr">
						<!--店铺信息-->
<#--						<#if isShowShop == 1>-->
<#--						<div class="shop-box">-->
<#--							<div class="shop-row1">-->
<#--                                <img src="${shopInfo.shopLogoUrl}" onerror="this.src='/static/images/shop-default.png'" alt="">-->
<#--							</div>-->
<#--                            <div class="shop-row2">-->
<#--								<span title="${shopInfo.shopName}">${shopInfo.shopName}</span>-->
<#--                                <a href="javaScript:callKf('','${merchant.id}');">-->
<#--                                    <img src="/static/images/kefu.png" alt="">-->
<#--								</a>-->
<#--                            </div>-->
<#--                            <div class="shop-row3">-->
<#--								<#list shopInfo.shopTag as tag>-->
<#--									<#if tag == '自营'>-->
<#--										<span class="grn">自营</span>-->
<#--									<#else>-->
<#--										<span class="blu">${tag}</span>-->
<#--									</#if>-->
<#--								</#list>-->
<#--                            </div>-->
<#--                            <div class="shop-row4">-->
<#--                                <a href="${shopInfo.shopHomeUrl}">-->
<#--									<img src="/static/images/shop.png" alt="" style="margin-top: -2px;margin-right: 4px;">进店逛逛-->
<#--								</a>-->
<#--                            </div>-->
<#--						</div>-->
<#--						</#if>-->
                        <div class="rxsp-title">
                            <div class="rxsp-title-l">热销商品</div>
                            <div class="rxsp-title-r" id="rxNew">换一批</div>
                        </div>
                        <input type="hidden" id="rxNum" value="1">
                        <ul class="mrth-new rxgoods" id="rexiao" style="width:236px;border-bottom:1px solid #E0E0E0;border-right:1px solid #E0E0E0;border-left:1px solid #E0E0E0;">

                        </ul>
                    </div>
					<!--商品纠错-->
					<div id="J_addsuppliersDialog" tabindex="-1" data-width="large" role="dialog"
						 class="sui-modal hide">
						<div class="modal-dialog">
							<div class="modal-content">
								<div class="modal-header">
									<button type="button" class="sui-close" id="jc_close">×</button>
									<h4 id="myModalLabel" class="modal-title">商品纠错</h4>
								</div>
								<div class="modal-body sui-form form-horizontal" style="padding:20px 50px;">
									<div>
										<div class="control-group noline" style="margin-bottom:0px;width:100%;">
											<label class="control-label v-top" style="width:100%;text-align: left;">
												<b style="color: #f00;">*</b>
												<span style="font-size: 18px;font-weight: 500; color:#333;">请选择您要纠错的内容</span>
											</label>
										</div>
										<div class="sui-row-fluid typeCon">
											<div class="span3 typeItem sui-icon" data-show="#typeConPrice,#typeConBecause,#typeConPriceSerjust,#typeConCompara,#typeConUpload,#typeConRemark"  data-hide="#typeConProDet,#typeConProDetCon">
												<div>
													<img src="/static/images/jiucuo/price.png">
													<span>价格</span>
												</div>
											</div>
											<div class="span1 noborder"  style="width:12.5%;">
												<div></div>
											</div>
											<div class="span3 typeItem sui-icon" data-show="#typeConProDet,#typeConProDetCon,#typeConUpload,#typeConRemark"  data-hide="#typeConPrice,#typeConBecause,#typeConPriceSerjust,#typeConCompara" >
												<div>
													<img src="/static/images/jiucuo/sku.png">
													<span>商品信息</span>
												</div>
											</div>
											<div class="span1 noborder" style="width:10.5%;">
												<div></div>
											</div>
											<div class="span3 typeItem sui-icon" data-show="#typeConUpload,#typeConRemark"  data-hide="#typeConPrice,#typeConBecause,#typeConProDet,#typeConProDetCon,#typeConPriceSerjust,#typeConCompara" >
												<div>
													<img src="/static/images/jiucuo/other.png">
													<span>其他</span>
												</div>
											</div>
										</div>
										<div id="jc_form" class="tooltip-only-arrow default bottom"
											 style="display:none;border:2px solid rgba(238,238,238,1);border-bottom-width:0px;border-left-width:0px;border-right-width:0px;">
<#--											<div class="tooltip-arrow"-->
<#--												 style="border-width: 0 6px 6px;border-bottom-color:rgba(238,238,238,1);left:12%;">-->
<#--												<div class="tooltip-arrow cover"></div>-->
<#--											</div>-->
											<div class="zsxCon">
												<img src="/static/images/zsx2.png" />
												<img src="/static/images/zsx3.png" />
												<img src="/static/images/zsx1.png" />
											</div>
											<div class="tooltip-inner" style="padding:0px 0px;">
												<div class="control-group noline" id="typeConPrice" style=";width:100%;">
													<label class="control-label v-top"
														   style="text-align:left;width:100%;line-height: 45px;height: 45px;background: rgba(247,247,248,1);padding-left: 15px;">
														<span style="font-size:14px;font-weight:400;color:rgba(51,51,51,1);">当前价格：</span>
														<span style="font-size:18px;font-weight:600;color:rgba(255,91,91,1);line-height:25px;">￥${detail.fob}</span>
													</label>
												</div>
												<div>
													<form class="sui-form form-horizontal" id="jiucuoForm">
														<input type="hidden" name="type" value=""/>
														<input type="hidden" name="productContent"  value=""/>
														<input type="hidden" name="skuId"  value=""/>
														<div class="control-group" id="typeConBecause">
															<label class="control-label v-top">
																<b style="color: #f00;">*</b>
																纠错原因
															</label>
															<div class="controls">
                                                        		<span class="sui-dropdown dropdown-bordered select">
                                                            <span class="dropdown-inner">
                                                                <a id="drop4" role="button" data-toggle="dropdown"
																   href="#" class="dropdown-toggle">
                                                                    <input value="1" name="priceAdjustType" type="hidden">
                                                                    <i class="caret"></i><span>售价偏高</span>
                                                                </a>
                                                                <ul id="menu4" role="menu" aria-labelledby="drop4"
																	class="sui-dropdown-menu">
                                                                    <li role="presentation">
                                                                        <a role="menuitem" tabindex="-1"
																		   href="javascript:void(0);"
																		   value="1">售价偏高</a>
                                                                    </li>
                                                                    <li role="presentation">
                                                                        <a role="menuitem" tabindex="-1"
																		   href="javascript:void(0);"
																		   value="2">建议零售价有误</a>
                                                                    </li>
                                                                    <li role="presentation">
                                                                        <a role="menuitem" tabindex="-1"
																		   href="javascript:void(0);"
																		   value="3">其他</a>
                                                                    </li>
                                                                </ul>
                                                            </span>
                                                        </span>
															</div>
														</div>
														<div class="control-group" id="typeConProDet" style="margin-bottom:0px;padding-top:0px;">
															<label class="control-label" style="width:100%;text-align: left;height:20px;line-height: 20px;">
																<b style="color: #f00;">*</b>
																(多选)请选择纠错的商品信息内容
															</label>
														</div>
														<div class="control-group noline" id="typeConProDetCon" style="padding-top:5px;">
															<div class="controls" style="line-height: 30px;">
																<div>
																	<label data-toggle="checkbox" class="checkbox-pretty inline">
																		<input type="checkbox" name="productContentNew" value="0"><span class="sui-icon icon-tb-check">图片有误</span>
																	</label>
																	<label data-toggle="checkbox" class="checkbox-pretty inline">
																		<input type="checkbox" name="productContentNew" value="1"><span class="sui-icon icon-tb-check">规格有误</span>
																	</label>
																	<label data-toggle="checkbox" class="checkbox-pretty inline">
																		<input type="checkbox" name="productContentNew" value="2"><span class="sui-icon icon-tb-check">生产厂家有误</span>
																	</label>
																	<label data-toggle="checkbox" class="checkbox-pretty inline">
																		<input type="checkbox" name="productContentNew" value="3"><span class="sui-icon icon-tb-check">中包装有误</span>
																	</label>
																	<label data-toggle="checkbox" class="checkbox-pretty inline">
																		<input type="checkbox" name="productContentNew" value="4"><span class="sui-icon icon-tb-check">处方类型有误</span>
																	</label>
																	<label data-toggle="checkbox" class="checkbox-pretty inline">
																		<input type="checkbox" name="productContentNew" value="5">
																		<#if detail.categoryFirstId ?? && detail.categoryFirstId == 100005>
																			<span class="sui-icon icon-tb-check">医疗器械注册证或备案凭证编号有误</span>
																		<#else >
																			<span class="sui-icon icon-tb-check">批准文号有误</span>
																		</#if>
																	</label>
																</div>
																<div>
																	<label data-toggle="checkbox" class="checkbox-pretty inline">
																		<input type="checkbox" name="productContentNew" value="6"><span class="sui-icon icon-tb-check">说明书有误</span>
																	</label>
																	<label data-toggle="checkbox" class="checkbox-pretty inline">
																		<input type="checkbox" name="productContentNew" value="7"><span class="sui-icon icon-tb-check">其他</span>
																	</label>
																</div>
															</div>
														</div>
														<div class="control-group noline" id="typeConPriceSerjust">
															<label class="control-label v-top">
																<b style="color: #f00;">*</b>
																建议价格
															</label>
															<div class="controls">
																<input type="number" value="" name="modifyPrice" class="input-xlarge"  onkedown="priceFamat(this);" onkeyup="priceFamat(this);" onpaste="priceFamat(this);" oncontextmenu = "priceFamat(this);" maxlength="11" />
															</div>
														</div>
														<div class="control-group noline" id="typeConCompara">
															<label class="control-label v-top">
																<b style="color: #f00;">*</b>
																参考平台
															</label>
															<div class="controls" >
																<input type="text" value="" name="refPlatform" class="input-xlarge">
															</div>
														</div>
														<div class="control-group" id="typeConUpload">
															<label class="control-label v-top">
																<b style="color: #f00;">*</b>
																上传图片
															</label>
															<div class="controls" id="upload_1">
																<div class="up_l_img" style="max-width:130px;width:130px;margin-right:20px;margin-bottom:10px;float: left;">
																	<img src="/static/images/jiucuo/upload.png"
																		 style="width:130px;"/>
																</div>
																<div style="clear:both;"></div>
																<p style="font-size:12px;font-family:PingFangSC-Regular;font-weight:400;color:rgba(148,148,166,1);line-height:20px;">
																	图片格式支持bmp、jpg、png，大小在500K以下</p>
															</div>
															<div class="controls" id="upload_2">
																<div class="up_l_img" style="max-width:130px;width:130px;margin-right:20px;margin-bottom:10px;float: left;">
																	<img src="/static/images/jiucuo/upload.png"
																		 style="width:130px;"/>
																</div>
																<div style="clear:both;"></div>
																<p style="font-size:12px;font-family:PingFangSC-Regular;font-weight:400;color:rgba(148,148,166,1);line-height:20px;">
																	图片格式支持bmp、jpg、png，大小在500K以下</p>
															</div>
															<div class="controls" id="upload_3">
																<div class="up_l_img" style="max-width:130px;width:130px;margin-right:20px;margin-bottom:10px;float: left;">
																	<img src="/static/images/jiucuo/upload.png"
																		 style="width:130px;"/>
																</div>
																<div style="clear:both;"></div>
																<p style="font-size:12px;font-family:PingFangSC-Regular;font-weight:400;color:rgba(148,148,166,1);line-height:20px;">
																	图片格式支持bmp、jpg、png，大小在500K以下</p>
															</div>
														</div>

														<div class="control-group" id="typeConRemark" style="padding-top:15px;">
															<label class="control-label v-top">
																<b style="color: #f00;">*</b>
																补充说明
															</label>
															<div class="controls" style="position: relative;    padding-right: 10px;">
                                                        		<textarea style="width:100%;max-width:100%;height: 120px;" name="supplementNote" class="input-xxlarge" maxlength="200"></textarea>
															    <span style="position: absolute;bottom:5px;right:15px;">
																	<span id="enterdNum" style="font-size:12px;color:#575766">0</span>
																	<span style="font-size:12px;color:#E4E4EB">/</span>
																	<span style="font-size:12px;color:#E4E4EB">200</span>
																</span>
															</div>
														</div>
														<div class="control-group noline" style="padding-top:15px;">
															<label class="control-label"></label>
															<div class="controls">
																<button type="button" class="sui-btn btn-primary" id="jcSub">提交</button>
<#--																<button type="reset" class="sui-btn">重置</button>-->
															</div>
														</div>
													</form>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div id="jiucuoImgDialog" tabindex="-1" data-width="400" role="dialog"
						 class="sui-modal hide">
						<div class="modal-dialog">
							<div class="modal-content">
								<button type="button" class="sui-close" id="jc_img_close" style="right: -50px;position: absolute;top: -35px;border: 2px solid #fff;border-radius: 30px;width: 35px;height: 35px;line-height: 30px;text-align: center;font-size: 30px;color: #fff;">×</button>
								<div class="modal-body sui-form form-horizontal">
									<div style="width:100%;height:400px;max-height:400px;line-height:100%;">
										<img src="" id="jiucuoImg"  style="display:block;margin:0px auto;height:100%;width:auto;max-width:100%;"  />
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
            <!--主体部分结束-->
            <!--底部导航区域开始-->
            <div class="footer" id="footer">
				<#include "/common/footer.ftl" />
            </div>
		</div>
        <!--优惠券弹窗-->
        <div id="fpxzTc" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">

                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                            <h4  class="modal-title">优惠券</h4>
                        </div>
                        <div class="modal-body">
                            <!--可领取优惠券-->
                            <div class="klqbox" id="klqb">
                                <div class="tishibox">
                                    可领取的优惠券
                                </div>
                                <ul class="yhq-common weishiyong" id="klq">
									<#list detail.voucherList as vo>
										<#if vo.state==1>
											<li>
												<div class="yhq-lb">
													<div class="yhq-lb-top">
														<#if vo.voucherState == 1>
															<span class="fuhao"></span><span class="price">${vo.discount}折</span>
														<#else>
															<span class="fuhao">￥</span><span class="price">${vo.moneyInVoucher}</span>
														</#if>
													</div>
													<div class="yhq-lb-foot">
														${vo.minMoneyToEnableDesc}
													</div>
													<#if (vo.maxMoneyInVoucherDesc?? && vo.maxMoneyInVoucherDesc!=null && vo.maxMoneyInVoucherDesc!="" )>
														<#--<div style="position: absolute; bottom: 20px;left:0px;">-->
                                                            <#--<span class="lastJian" style="font-size: 12px;font-family: PingFangSC;font-weight: 400;color: #F04134;line-height: 17px;">${vo.maxMoneyInVoucherDesc }</span>-->
                                                        <#--</div>-->
														<div class="yhq-lb-foot">
															${vo.maxMoneyInVoucherDesc }
														</div>
													</#if>
												</div>
												<div class="yhq-rb" id="v_${vo.voucherTemplateId}_v">
													<div class="yhq-rb-top">
														<span class="quan">${vo.voucherTypeDesc}</span><span class="info" title="${vo.voucherTitle}">${vo.voucherTitle}</span>
													</div>
                                                    <div style="height:30px;overflow:hidden;"></div>
													<#--<#if (vo.voucherUsageWay ==1 && vo.maxMoneyInVoucherDesc?? && vo.maxMoneyInVoucherDesc!=null && vo.maxMoneyInVoucherDesc!="" )>-->
														<#--<div style="position: absolute; bottom: 20px;left:0px;">-->
															<#--<span class="lastJian" style="font-size: 12px;font-family: PingFangSC;font-weight: 400;color: #F04134;line-height: 17px;">${vo.maxMoneyInVoucherDesc }</span>-->
														<#--</div>-->
													<#--</#if>-->
													<div class="yhq-rb-foot">
														<#if vo.flag==0 >
															<span>${vo.validDate?string('yyyy.MM.dd')} - ${vo.expireDate?string('yyyy.MM.dd')}</span>
														<#else >
															<span>${vo.validDays}</span>
														</#if>
														<a href="javascript:void(0);" id="v_${vo.templateId}" onclick="receiveTemplate(${merchantId},${vo.templateId})">立即领取</a>
													</div>
												</div>
											</li>
										</#if>
										<#if vo.state==8>
											<li>
                                                <div class="yhq-lb">
                                                    <div class="yhq-lb-top">
                                                        <span class="fuhao">￥</span><span class="price">${vo.moneyInVoucher}</span>
                                                    </div>
                                                    <div class="yhq-lb-foot">
														${vo.minMoneyToEnableDesc}
                                                    </div>
													<#if (vo.maxMoneyInVoucherDesc?? && vo.maxMoneyInVoucherDesc!=null && vo.maxMoneyInVoucherDesc!="" )>
													<#--<div style="position: absolute; bottom: 20px;left:0px;">-->
													<#--<span class="lastJian" style="font-size: 12px;font-family: PingFangSC;font-weight: 400;color: #F04134;line-height: 17px;">${vo.maxMoneyInVoucherDesc }</span>-->
													<#--</div>-->
														<div class="yhq-lb-foot">
															${vo.maxMoneyInVoucherDesc }
                                                        </div>
													</#if>
                                                </div>
                                                <div class="yhq-rb" id="v_${vo.voucherTemplateId}_v">
                                                    <div class="yhq-rb-top">
                                                        <span class="quan">${vo.voucherTypeDesc}</span><span class="info" title="${vo.voucherTitle}">${vo.voucherTitle}</span>
                                                    </div>
                                                    <div style="height:30px;overflow:hidden;"></div>
													<#--<#if (vo.voucherUsageWay ==1 && vo.maxMoneyInVoucherDesc?? && vo.maxMoneyInVoucherDesc!=null && vo.maxMoneyInVoucherDesc!="" )>-->
														<#--<div style="position: absolute; bottom: 20px;left:0px;">-->
															<#--<span class="lastJian" style="font-size: 12px;font-family: PingFangSC;font-weight: 400;color: #F04134;line-height: 17px;">${vo.maxMoneyInVoucherDesc }</span>-->
														<#--</div>-->
													<#--</#if>-->
                                                    <div class="yhq-rb-foot">
														<#if vo.flag==0 >
															<span>${vo.validDate?string('yyyy.MM.dd')} - ${vo.expireDate?string('yyyy.MM.dd')}</span>
														<#else >
															<span>${vo.validDays}</span>
														</#if>
                                                        <img src="/static/images/events/20180605-lt-lqzx/quan_icon_2.png"  class="pos-ysy">
                                                    </div>
                                                </div>
                                            </li>
										</#if>
									</#list>
                                </ul>
                            </div>

                            <!--已领取优惠券-->
                            <div class="klqbox" id="ylqb">
                                <div class="tishibox">
                                    已领取的优惠券
                                </div>
                                <ul class="yhq-common weishiyong" id="ylq">
									<#list detail.voucherList as vo>
										<#if vo.state==2>
											<li>
												<div class="yhq-lb">
													<div class="yhq-lb-top">
														<#if vo.voucherState == 1>
															<span class="fuhao"></span><span class="price">${vo.discount}折</span>
														<#else>
															<span class="fuhao">￥</span><span class="price">${vo.moneyInVoucher}</span>
														</#if>
													</div>
													<div class="yhq-lb-foot">
														${vo.minMoneyToEnableDesc}
													</div>
													<#if (vo.maxMoneyInVoucherDesc?? && vo.maxMoneyInVoucherDesc!=null && vo.maxMoneyInVoucherDesc!="" )>
														<#--<div style="position: absolute; bottom: 20px;left:0px;">-->
                                                            <#--<span class="lastJian" style="font-size: 12px;font-family: PingFangSC;font-weight: 400;color: #F04134;line-height: 17px;">${vo.maxMoneyInVoucherDesc }</span>-->
                                                        <#--</div>-->
														<div class="yhq-lb-foot">
															${vo.maxMoneyInVoucherDesc }
														</div>
													</#if>
												</div>
												<div class="yhq-rb">
													<div class="yhq-rb-top">
														<span class="quan">${vo.voucherTypeDesc}</span><span class="info" title="${vo.voucherTitle}">${vo.voucherTitle}</span>
													</div>
                                                    <div style="height:30px;overflow:hidden;"></div>
													<#--<#if (vo.voucherUsageWay ==1 && vo.maxMoneyInVoucherDesc?? && vo.maxMoneyInVoucherDesc!=null && vo.maxMoneyInVoucherDesc!="" )>-->
														<#--<div style="position: absolute; bottom: 20px;left:0px;">-->
															<#--<span class="lastJian" style="font-size: 12px;font-family: PingFangSC;font-weight: 400;color: #F04134;line-height: 17px;">${vo.maxMoneyInVoucherDesc }</span>-->
														<#--</div>-->
													<#--</#if>-->
													<div class="yhq-rb-foot">
														<#if vo.flag==0 >
															<span>${vo.validDate?string('yyyy.MM.dd')} - ${vo.expireDate?string('yyyy.MM.dd')}</span>
														<#else >
															<span>${vo.validDays}</span>
														</#if>
													</div>
													<img src="/static/images/events/20180605-lt-lqzx/quan_icon_2.png"  class="pos-ysy">
												</div>
											</li>
										</#if>
									</#list>
                                </ul>
                            </div>

                        </div>

                    </div>
                </div>
        </div>
        <!--折后价说明弹窗-->
        <div id="fuwu" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade" style="width: 500px;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4  class="modal-title" >折后价说明</h4>
                    </div>
                    <div class="modal-body" style="padding-top:12px;">
                        <div class="xzmain">
							<#--<p class="top-info">-->
                                <#--折后价是系统根据当前商品的促销活动及可领、可用优惠券，预估出的较优买入量及对应可享的优惠后单价，最终价格会根据您的买入量有所不同，实付价格以结算页为准。-->
							<#--</p>-->
							<#--<div class="content-info-box">-->
								<#--<div class="content-info">-->
									<#--<p class="content-header">-->
										<#--<span class="content-left">商品单价</span>-->
										<#--<span class="content-right">¥70.80</span>-->
									<#--</p>-->
								<#--</div>-->
								<#--<div class="content-info">-->
									<#--<p class="content-header">-->
										<#--<span class="content-left">优惠券</span>-->
										<#--<span class="content-right-red">-¥70.00</span>-->
									<#--</p>-->
									<#--<p class="content-item">-->
										<#--<span class="content-left-gray">通用券 满3000减50<span class="yiling">已领取</span></span>-->
										<#--<span class="content-right-gray">-¥50.00</span>-->
									<#--</p>-->
									<#--<p class="content-item">-->
										<#--<span class="content-left-gray">叠加券 满3000减20<span class="weiling">未领取</span></span>-->
										<#--<span class="content-right-gray">-¥20.00</span>-->
									<#--</p>-->
								<#--</div>-->
								<#--<div class="content-info">-->
									<#--<p class="content-header">-->
										<#--<span class="content-left">促销活动</span>-->
										<#--<span class="content-right-red">-¥20.00</span>-->
									<#--</p>-->
									<#--<p class="content-item">-->
										<#--<span class="content-left-gray">满减 满1000减20</span>-->
										<#--<span class="content-right-gray">-¥20.00</span>-->
									<#--</p>-->
								<#--</div>-->
								<#--<div class="total-price">-->
									<#--预估买入120件可享折后价约<span class="price-red">¥24.25</span>-->
								<#--</div>-->
								<#--<p class="tip-content">购买时，记得先领券哦</p>-->
                            <#--</div>-->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" data-dismiss="modal">确认</button>
                    </div>
                </div>
            </div>
        </div>
		<div id="progressModel" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade" height="32" style="border-radius: 10px;">
			<div style="background: #fff;padding:15px 10px;border-radius: 10px;">
				<p style="text-align: center;margin-bottom: 20px;">正在为您下载，请稍等…</p>
				<div class="sui-progress progress-success progress-striped active" style="height:32px;border-radius: 5px;">
					<div  id="progress_rate"  class="bar" style="border-radius: 5px;"></div>
					<div class="bar-text"  id="progress_text" style="height:32px;line-height: 32px;">0%</div>
				</div>
			</div>
		</div>

        <!--底部导航区域结束-->
		<script type="text/javascript" src="/static/js/plugins/jquery.form.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/ckplayer/ckplayer.js?t=${t_v}"></script>
        <script type="text/javascript">
			/**套餐**/
            $(function () {
				/* 临时 */
				const tempFunc = function() {
					const temp1 = $("#temp1").val();
					const temp2 = $("#temp2").val();
					const temp3 = $("#temp3").val();
					if (temp1 == '0' || (temp2 == 'true' && temp3 == '0')) {
						const id = $('#productId').val()
						const hrefDETAIL =  document.getElementById(`href_DETAIL_${id}`)
						const hrefAddCartDETAIL =  document.getElementById(`href_DETAIL_ADD_CART_${id}`)
						
						hrefDETAIL.onclick = () => {};
						hrefDETAIL.style.background = '#9b9b9b';
						hrefAddCartDETAIL.onclick = () => {};
						hrefAddCartDETAIL.style.background = '#9b9b9b';
					}
				}
				tempFunc();
				/* =================临时============ */
                /*减操作*/
                $(".subt").click(function () {
                    var step = 1;
                    var me = $(this),
                            txt = me.next(":text");
                    var val = parseFloat(txt.val());
                    if (val <= step) {
                        txt.val(0);
                    } else {
                        txt.val(val - step);
                    }
                });
                /*加操作*/
                $(".addt").click(function () {
                    var step = 1;
                    var me = $(this),
                            txt = me.prev(":text");
                    var val = parseFloat(txt.val());
                    txt.val(val + step);
                });

                $(function () {
                    $(".timeBox").each(function () {
                        var times = $(this).attr("data-times");//获得timeBox的data值，即结束时间
                        var sysTime = $('#sysTime').val();
                        var now = new Date();
                        if(sysTime){
                            now = new Date(sysTime);
                        }
                        var endtime = new Date(times);//把data值转换成时间
                        var endTimer = endtime.getTime() - now.getTime();
                        var seconds = endTimer / 1000;
                        if (seconds > 0) {
                            timer($(this), seconds);
                        }
                    });
                });

                //时分秒倒计时方法
                function timer(element, seconds) {
                    if (element) {
                        if (seconds > 0) {
                            var dd = parseInt(seconds / 3600 / 24, 10);
                            var hh = parseInt(seconds % (3600 * 24) / 3600, 10);
                            var mm = parseInt(seconds % 3600 / 60, 10);
                            var ss = parseInt(seconds % 3600 % 60, 10);
                            dd = dd < 10 ? ("0" + dd) : dd; //天
                            hh = hh < 10 ? ("0" + hh) : hh; //时
                            mm = mm < 10 ? ("0" + mm) : mm; //分
                            ss = ss < 10 ? ("0" + ss) : ss; //秒
                            if (dd && dd > 0) {
                                $(element).find(".date").text(dd + "天");
                            } else {
                                $(element).find(".date").hide();
                            }
                            $(element).find(".hour").text(hh);
                            $(element).find(".minutes").text(mm);
                            $(element).find(".seconds").text(ss);
                            setTimeout(function () {
                                timer(element, seconds - 1);
                            }, 1000);
                        } else {
                            $(element).remove();
                            setTimeout(function () {
                                location.reload(true);
                            }, 1000);
                        }
                    }
                }
            });
            var cartUrlPrefix =  "/merchant/center/cart";

            //添加购物车接口
            function changeCartPackage(packageId, event, _this, type) {
                var packageAmount = $("#num" + packageId).val();
                if (packageAmount == 0) {
                    $.alert("购物数量不能为0，请输入购买数量!");
                    return;
                }
                $.ajax({
                    url: cartUrlPrefix + "/changeCart.json",
                    type: "POST",
                    dataType: "json",
                    async: true,
                    data: {
                        packageId: packageId,
                        amount: packageAmount
                    },
                    success: function (result) {
                        if (result.status == "success") {
                            var cartNum = getCartNum();
                            if (type == 1) {
                                packageFlyYY(event, _this, cartNum);
                            } else {
                                packageFly(event, _this, cartNum);
                            }

                            if (result.data.message != null && result.data.message != "") {
                                $.alert(result.data.message);
                            }
                            $("#num" + packageId).val(result.data.qty);
                        } else {
                            $.alert(result.errorMsg);
                        }
                    }
                });
            }

            //销售套餐飞购物车效果~
            function packageFly(event, _this, cartNum) {
                //var offset = $(".pressCart").offset();
                var offset = $(".cycle2").offset();
                var scrollX = $(window).scrollTop(); //获取滚动条的距离。
                var addcar = $(_this);
                var img = addcar.parent().parent().siblings(".tcwarp").find(".itembox").find(".row1").find("img").attr('src');
                //var img = "http://tesupload.ybm100.com/ybm/product/6926154730416.jpg?random=0.04261914503330155";
                /*var img = addcar.parent().parent().siblings(".row1").find("img").attr('src');*/
                var flyer = $('<img class="u-flyer" src="' + img + '">');
                flyer.fly({
                    start: {
                        left: event.pageX, //开始位置（必填）#fly元素会被设置成position: fixed
                        top: event.pageY - scrollX  //开始位置（必填）
                    },
                    end: {
                        left: offset.left + 10, //结束位置（必填）
                        top: offset.top + 10 - scrollX, //结束位置（必填）
                        width: 0, //结束时宽度
                        height: 0 //结束时高度
                    },
                    onEnd: function () { //结束回调
                        if (cartNum >0) {
                            $("#cartNumberLi").addClass("cycle");
                            $("#cartNumberDiv").addClass("topp");
                            $("#rigthCartNum").removeClass("cycle2 noshow");
                            $("#rigthCartNum").addClass("cycle2");
                            $("#rigthCartNum").addClass("topp");
                        }
                        $("#cartNumberLi").html(cartNum);
                        $("#rigthCartNum").html(cartNum);
                        $("#cartNumberDiv").html(cartNum);
                    }
                });
            }

            //运营套餐飞购物车效果~
            function packageFlyYY(event, _this, cartNum) {
                //var offset = $(".pressCart").offset();
                var offset = $(".cycle2").offset();
                var scrollX = $(window).scrollTop(); //获取滚动条的距离。
                var addcar = $(_this);
                var img = addcar.parent().parent().parent().siblings(".itembox").find(".row1").find("img").attr('src');
                //var img = "http://tesupload.ybm100.com/ybm/product/6926154730416.jpg?random=0.04261914503330155";
                /*var img = addcar.parent().parent().siblings(".row1").find("img").attr('src');*/
                var flyer = $('<img class="u-flyer" src="' + img + '">');
                flyer.fly({
                    start: {
                        left: event.pageX, //开始位置（必填）#fly元素会被设置成position: fixed
                        top: event.pageY - scrollX  //开始位置（必填）
                    },
                    end: {
                        left: offset.left + 10, //结束位置（必填）
                        top: offset.top + 10 - scrollX, //结束位置（必填）
                        width: 0, //结束时宽度
                        height: 0 //结束时高度
                    },
                    onEnd: function () { //结束回调
                        if (cartNum == 1) {
                            $("#cartNumberLi").addClass("cycle");
                            $("#cartNumberDiv").addClass("topp");
                            $("#rigthCartNum").removeClass("cycle2 noshow");
                            $("#rigthCartNum").addClass("cycle2");
                            $("#rigthCartNum").addClass("topp");
                        }
                        $("#cartNumberLi").html(cartNum);
                        $("#rigthCartNum").html(cartNum);
                        $("#cartNumberDiv").html(cartNum);
                    }
                });
            }
			/**获取折后价**/
            var merchantId = $("#merchantId").val();
            var productId = $("#productId").val();
            var branchCode = $("#branchCode").val();
			function getPriceDetail(){
                $.ajax({
                    url: "/marketing/discount/satisfactoryInHandPrice",
                    type: "POST",
                    dataType: "json",
                    data: {
                        merchantId: merchantId,
                        skuIds: productId,
                        branchCode: branchCode
					},
                    success: function(result){
                        if(result){
                            if(result.data){
                                var data = result.data[0];
								const price = Number($(".hprice .price").text());
								const zhehou = data.price.split('￥');
                                if(data && data.price && (!isNaN(price) && price > zhehou[1])){
                                    var str = data.price
                                    $(".zhehou-price").html(str);
                                    $(".get-intro").css("display","inline");
								}
                            }
                        }else{
                            $.alert(result.errorMsg);
                        }
                    }
                })
			}
            /**折后价说明**/
            $(".get-intro").click(function(){
                var data = {
                    merchantId: merchantId,
                    skuId: productId,
                    branchCode: branchCode
				}
                $.ajax({
                    url: "/marketing/discount/satisfactoryInHandPriceWithDetail",
                    type: "POST",
                    dataType: "json",
                    data: data,
                    success: function(result){
                        if(result){
                            $("#fuwu").modal();
                            if(result.data){
                                var data = result.data;
                                var dataList = data.discountGroup;
                                var buyStr = '',contentStr = '',listStr = '',itemStr = '',noticeStr = '';
                                // buyStr += '<div class="total-price">预估买入'+data.buyQty+data.productUnit+'可享折后价约<span class="price-red">'+data.price+'</span></div>'
								buyStr += '<div class="total-price">'+data.priceDetailPrefix+'<span class="price-red">'+data.price+'</span></div>'
								noticeStr = '<p class="tip-content">' + data.notice + '</p>';
								if(dataList && dataList.length>0){
                                    dataList.forEach(function(item,index){
                                        if(item.discountDetails && item.discountDetails.length>0){
                                            itemStr = '';
                                            item.discountDetails.forEach(function(item1,index1){
												var status = item1.status == 2 ? '<span class="yiling">已领取</span>' : item1.status == 1 ? '<span class="weiling">未领取</span>' : '';
                                                itemStr += '<p class="content-item">' +
                                                        		'<span class="content-left-gray">'+ item1.typeTitle + ' ' + item1.discountDesc + status +'</span>' +
                                                        		'<span class="content-right-gray">'+ item1.discountPrice +'</span>' +
                                                        	'</p>'
											})
										}
                                        listStr += '<div class="content-info">' +
                                                		'<p class="content-header">' +
                                                			'<span class="content-left">'+ item.title +'</span>' +
                                                			'<span class="content-right-red">'+ item.discountPrice +'</span>' +
                                                		'</p>' + itemStr +
                                                	'</div>'
									})
								}
                                contentStr = '<p class="top-info">' + data.announcement +
											'</p>'+
											'<div class="content-info-box">' +
												'<div class="content-info">' +
													'<p class="content-header">' +
														'<span class="content-left">商品单价</span>' +
														'<span class="content-right">' + data.fob + '</span>' +
													'</p>' +
												'</div>' + listStr + buyStr + noticeStr +
											'</div>';
                                $(".xzmain").html(contentStr)
                            }
                        }else{
                            $.alert(result.errorMsg);
                        }
                    }
                })
            })
			var isCanSub = true;
			var jiucuoFormData={
				type1:{

				},
				type2:{

				},
				type3:{

				}
			}
			function checkoutRight(){
				var checkStatus = true;
				//获取表单数据
				var postDataArr = $('#jc_form').find('input,textarea,select');
				//console.log(postDataArr,'----postDataArr--');
				var postData={
					productContentNew:[]
				}

				//过滤表单数据
				for (var i = 0; i <postDataArr.length ; i++) {
					var name = postDataArr[i].name;
					var value = postDataArr[i].value;
					if(name == 'productContentNew'&& postDataArr[i].checked){
						postData[name].push(value);
					}else{
						if(name != 'productContentNew'){
							postData[name] = value;
						}
					}
				}

				$('input[name="productContent"]').val(postData['productContentNew'].join(','));

				$('input[name="skuId"]').val($('#productId').val());

				postData['supplementNote']= postData['supplementNote'].replace(/[\{\}\[\]#$%^&*!]/g, '').replace(/</g,'&lt;').replace(/>/g,'&gt;');

				//获取图片数据
				var uploadInput = $('#upload_' + $('input[name="type"]').val() +' .img-item input');
				var fileList = [];
				for (var i = 0; i <uploadInput.length ; i++) {
					if(uploadInput[i].value || (uploadInput[i] && uploadInput[i].files && uploadInput[i].files.length)){
						var fileObj = uploadInput[i].files && uploadInput[i].files.length?uploadInput[i].files[0]: uploadInput[i].value;
						fileList.push(fileObj);
					}
				}


				//console.log(postData,'---postData----',fileList);
				if(postData['type']){
					jiucuoFormData['type'+ postData['type']]=postData;
				}
				//console.log(jiucuoFormData,'----jiucuoFormData-----');


				if(postData.type==1){
					if(postData.priceAdjustType!=3){
						if(!postData.modifyPrice){
							checkStatus=false;
						}else if(!postData.refPlatform){
							checkStatus=false;
						}else{

						}
					}else{
						if(!fileList.length){
							checkStatus=false;
						}else if(!postData.supplementNote){
							checkStatus=false;
						}
					}
				}else if(postData.type==2){
					if(!postData.productContentNew.length){
						checkStatus=false;
					}else if(!fileList.length){
						checkStatus=false;
					}else if(!postData.supplementNote){
						checkStatus=false;
					}
				}else if(postData.type==3){
					if(!fileList.length){
						checkStatus=false;
					}else if(!postData.supplementNote){
						checkStatus=false;
					}
				}

				if(!checkStatus){
					$('#jcSub').css({background:"#ddd",'borderColor':"#ddd"});
				}else{
					$('#jcSub').css({background:"#00dc82",'borderColor':"#00dc82"});
				}
				return checkStatus;
			}
			function priceFamat(obj){
				if(!/(\.*)(\d+)(\.?)(\d{0,2}).*$/g.test(obj.value)){
					obj.value='';
				}else{
					if(obj.value && obj.value.length>=8){
						if(obj.value.indexOf('.')<0){
							if(obj.value && obj.value.length>=8){
								obj.value = obj.value.slice(0,8);
							}
						}else{
							obj.value=obj.value.replace(/^(\.*)(\d+)(\.?)(\d{0,2}).*$/g,'$2$3$4');
						}
					}else{
						obj.value=obj.value.replace(/^(\.*)(\d+)(\.?)(\d{0,2}).*$/g,'$2$3$4');
					}
				}
				checkoutRight();
			}
			$(function () {
                getPriceDetail();
				/*视频播放器配置*/
				if($('#videoUrl') && $('#videoUrl').lenght){
					var videoUrl = $('#videoUrl').val();
					var flashvars={
						f:videoUrl,
						c:0
					};
					var params={bgcolor:'#FFF',allowFullScreen:true,allowScriptAccess:'always',wmode:'transparent'};
					CKobject.embedSWF('/js/ckplayer/ckplayer.swf','video','ckplayer_a1','600','400',flashvars,params);
				}

				$('#jiucuo').on('click', function () {
					return $('#J_addsuppliersDialog').modal({
						backdrop:'static',
						bgcolor:'#000000e0',
						show:true
					});
					return false;
				})

				$('#jc_close').on('click', function () {
					$('#J_addsuppliersDialog').modal('hide');
					$('#jc_form').hide();
					$('.typeItem').removeClass('active').removeClass('icon-tb-check');
					$('#jc_form').find('input,textarea,select').each(function(){
						 //console.log($(this),$(this)[0].localName,$(this)[0].type);
						 var localName = $(this)[0].localName?$(this)[0].localName:$(this)[0].tagName?$(this)[0].tagName.toLowerCase():"";
						 var type = $(this)[0].type;

						 if(localName == 'input'){
							 if(type=="hidden" || type=="text" || type=="number"){
								 $(this).val('');
							 }else if(type=='checkbox'){
							 	var parentObj = $(this).parent('label').checkbox();
								 parentObj.checkbox('uncheck');
							 }
						 }else if(localName == 'textarea'){
                             $(this).val('');
						 }
					})

					$('input[name="priceAdjustType"]').val('1');
					$('input[name="priceAdjustType"]').parent().find('span').html('售价偏高');
					$('input[name="priceAdjustType"]').parent().parent().find('li').removeClass('active');
					$($('input[name="priceAdjustType"]').parent().parent().find('li')[0]).addClass('active');

					$('#typeConUpload').find('.controls').hide();

					jiucuoFormData={
						type1:{

						},
						type2:{

						},
						type3:{

						}
					}
					$('.img-item').remove();
					$('.up_l_img').show();
					$('#enterdNum').html('0');
					$('.zsxCon img').hide();
					$('#enterdNum').css('color','#575766');
					checkoutRight();
					return false;
				})

				$('#jc_img_close').on('click',function(){
					$('#jiucuoImgDialog').modal('hide');
					$('#J_addsuppliersDialog').modal('shadeOut');
					return false;
				})

				$('.typeItem').on('click', function () {
					var showItem = $(this).data('show');
					var hideItem = $(this).data('hide');
					if(showItem){
						$(showItem).show();
					}
					if(hideItem){
						$(hideItem).hide();
					}

					$('#jc_form').show();
					$('#J_addsuppliersDialog').css('marginTop',"-300px");
					$('.typeItem').removeClass('active').removeClass('icon-tb-check');
					$(this).addClass('icon-tb-check').addClass('active');
					var index = $(this).index();
					//$('input[name="type"]').val('' + index);



					//console.log(index);
					$('.zsxCon img').hide();
					if (index == 0) {//商品价格
						$($('.zsxCon img')[0]).show();
						$('.tooltip-arrow').css('left', '13%');
						$('input[name="type"]').val('' + 1);
						$('#typeConPrice').addClass('firstFormItem');
						$('#typeConProDet').removeClass('firstFormItem');
						$('#typeConUpload').removeClass('firstFormItem');
						$('#typeConUpload').find('.controls').hide();
						$($('#typeConUpload').find('.controls')[0]).show();
						var priceAdjustType = $('input[name="priceAdjustType"]').val();
						if(priceAdjustType=='3'){
							$('#typeConUpload b').show();
							$('#typeConRemark b').show();
						}else{
							$('#typeConUpload b').hide();
							$('#typeConRemark b').hide();
						}
					} else if (index == 2) {//商品内容
						$('#typeConProDet').addClass('firstFormItem');
						$('#typeConUpload').removeClass('firstFormItem');
						$('#typeConUpload').find('.controls').hide();
						$($('#typeConUpload').find('.controls')[1]).show();
						$($('.zsxCon img')[1]).show();
						$('.tooltip-arrow').css('left', '51%');
						$('input[name="type"]').val('' + 2);
						$('#typeConUpload b').show();
						$('#typeConRemark b').show();
					} else if (index == 4) {//其他
						$('#typeConUpload').addClass('firstFormItem');
						$('#typeConUpload').find('.controls').hide();
						$($('#typeConUpload').find('.controls')[2]).show();
						$($('.zsxCon img')[2]).show();
						$('.tooltip-arrow').css('left', '88%');
						$('input[name="type"]').val('' + 3);
						$('#typeConUpload b').show();
						$('#typeConRemark b').show();
					}


					var  formData = jiucuoFormData['type'+ $('input[name="type"]').val()];

					//console.log(formData,'----formData----');

					var formItemKeys = [];

					for(var key in formData){
						formItemKeys.push(key);
					}

					for (var i = 0; i < formItemKeys.length; i++) {
						//if(formData[formItemKeys[i]]){
							if(['priceAdjustType','modifyPrice','refPlatform','supplementNote','productContent'].join(',').indexOf(formItemKeys[i])>=0){
								if(formItemKeys[i]=='productContent'){

								}else if(formItemKeys[i]=='priceAdjustType'){
									// $('input[name="priceAdjustType"]').val(formData[formItemKeys[i]]);
									// $('input[name="priceAdjustType"]').parent().parent().find('li').removeClass('active');
									// if(formData[formItemKeys[i]]=='1'){
									// 	$('input[name="priceAdjustType"]').parent().find('span').html('售价偏高');
									// 	$($('input[name="priceAdjustType"]').parent().parent().find('li')[0]).addClass('active');
									// }else if(formData[formItemKeys[i]]=='2'){
									// 	$('input[name="priceAdjustType"]').parent().find('span').html('建议零售价有误');
									// 	$($('input[name="priceAdjustType"]').parent().parent().find('li')[1]).addClass('active');
									// }else if(formData[formItemKeys[i]]=='3'){
									// 	$('input[name="priceAdjustType"]').parent().find('span').html('其他');
									// 	$($('input[name="priceAdjustType"]').parent().parent().find('li')[2]).addClass('active');
									// }
								}else{
									//console.log(formItemKeys[i],formData[formItemKeys[i]]);
									if(formData[formItemKeys[i]]) {
										$('input[name="' + formItemKeys[i] + '"],textarea[name="' + formItemKeys[i] + '"]').val(formData[formItemKeys[i]]);
									}else{
										$('input[name="' + formItemKeys[i] + '"],textarea[name="' + formItemKeys[i] + '"]').val('');
									}
								}
							}
						//}
					}

					if(!formItemKeys.length){
						$('textarea[name="supplementNote"]').val('');
					}



					$('textarea[name="supplementNote"]').trigger('input');

					checkoutRight();
					return false;
				})

				$('input[name="priceAdjustType"]').on('change',function(){
                     var val = $(this).val();
					if(val=='1' || val=='2'){//售价偏高 || 建议零售价有误
						$('#typeConPriceSerjust').show();
						$('#typeConCompara').show();
						$('#typeConUpload b').hide();
						$('#typeConRemark b').hide();
					}else{
						$('#typeConPriceSerjust').hide();
						$('#typeConCompara').hide();
						$('#typeConUpload b').show();
						$('#typeConRemark b').show();
					}
					checkoutRight();
					return false;
				})

				//点击上传图片
				$('.up_l_img').each(function(){
					$(this).on('click', function () {
						if($('#upload_' + $('input[name="type"]').val() +' .img-item')&& $('#upload_' + $('input[name="type"]').val() +' .img-item').length){
							if(!$($('#upload_' + $('input[name="type"]').val() +' .img-item')[$('#upload_' + $('input[name="type"]').val() +' .img-item').length-1]).find('input').val()){
								// $.alert({
								// 	title: "提示",
								// 	body: '请先选择图片再添加!',
								// 	okBtn : '确定'
								// });
								// return;
								$($('#upload_' + $('input[name="type"]').val() +' .img-item')[$('#upload_' + $('input[name="type"]').val() +' .img-item').length-1]).remove();
							}
						}
						var itemid = new Date().valueOf();
						var imgItem = "<div id=\"item-" + itemid + "\" class=\"img-item\" style=\"position: relative;float: left;overflow: hidden;text-align:center;max-width:130px;width:130px;height:130px;line-height: 130px;border:1px solid #ddd;border-radius:10px;margin-right:20px;margin-bottom:10px;\">" +
								"<img src=\"\" style=\"width:130px;max-width:100%;height:100%;max-height:100%;display: none;\"/>" +
								"<input clase=\"img-url\" type=\"file\" name=\"" + new Date().valueOf() + "\" style=\" font-size: 100px;\n" +
								"  cursor: pointer;width:130px;height:130px;filter:alpha(opacity=0);position: absolute;z-index: 1;top:0px;left:0px; \n" +
								"      -moz-opacity:0;   \n" +
								"      -khtml-opacity: 0;   \n" +
								"      opacity: 0;\" />" +
								"<span class=\"del-img\" style=\"width:20px;height:20px;user-select: none;font-size:18px;line-height:19px;text-align:center;background:black;color:#fff;position:absolute;top:0px;right:0px;border-radius:15px;border:2px solid #fff;display:none;z-index:2;\">×</span>" +
								"<span class=\"selectimg\"  style=\"user-select: none;color:#ddd; font-size:18px; \">选择图片</span>"
						"</div>";
						$(this).before(imgItem);
						if (navigator.userAgent.indexOf('MSIE 8') <= 0){
							$("#item-" + itemid).hide();
							$("#item-" + itemid + " input").trigger('click');

						}else{
							$("#item-" + itemid).show();
							if ($('#upload_' + $('input[name="type"]').val() +' .img-item').length == 3){
								$(this).hide();
							}
						}

						checkoutRight();
						return false;
					})
				})

				//点击选择图片
				// $('#J_addsuppliersDialog').on('click', '.img-item .selectimg', function () {
				// 	$(this).parent('div.img-item').find('input').trigger('click');
				// 	return false;
				// })



				$('input').on('input',function(){
					checkoutRight();
					return false;
				})

				$('input[name="productContentNew"]').on('click',function(){
					checkoutRight();
					//return false;
				})

				// $('label[data-toggle="checkbox"]').on('click',function(){
				// 	checkoutRight();
				// 	return false;
				// })


				//点击预览图片
				$('#J_addsuppliersDialog').on('click', '.img-item img', function () {

					$('#jiucuoImg').attr('src',$(this).attr('src'));
					$('#J_addsuppliersDialog').modal('shadeIn');
					return  $('#jiucuoImgDialog').modal({
						hasfoot: false,
						backdrop: false,
						bgcolor:'rgba(0,0,0,1)',
						hide: function() {
							return $('#J_addsuppliersDialog').modal('shadeOut');
						},
						show:true
					});
				})

				function getObjectURL(file){//非ie 使用 blob协议文件路径预览图片
					var url=null
					if(window.createObjectURL!=undefined){ // basic
						url=window.createObjectURL(file)
					}else if(window.URL!=undefined){ // mozilla(firefox)
						url=window.URL.createObjectURL(file)
					} else if(window.webkitURL!=undefined){ // webkit or chrome
						url=window.webkitURL.createObjectURL(file)
					}
					return url  ;
				}

				function readBlobAsDataURL(blob, callback) {
					//console.log(blob,'---blob-');
					var a = new FileReader();
					a.onload = function(e) {
						//console.log(e);
					callback(e.target.result);
					};
					a.readAsDataURL(blob);
				}

				//文件输入框值改变的时候触发上传图片操作
				$('#J_addsuppliersDialog').on('change', '.img-item input', function (e) {//由于后端改变上传图片逻辑，次方法只是用来展示本地图片
					var filesList = $(this)[0].files;
					//console.log(filesList,'---filesList---');

					if( filesList && !filesList.length){
						$(this).parent('div.img-item').remove();
						return;
					}
					//console.log(e.target,$(this).val());
					var filePath = $(this).val();



					if (!(/.*?(jpg|png|bmp)/.test(filePath.toLowerCase())) || !(/.*?(jpg|png|bmp)/.test($(this).val().toLowerCase()))) {
						//不是图片 就跳出这一次循环
						$.alert({
							title: "提示",
							body:'不是图片！' ,
							okBtn : '确定'
						});
						$(this).val('');
						return;
					}


					if( filesList && filesList[0].size > 500 * 1000){
						//图片大小不能大于 500Kb
						$.alert({
							title: "提示",
							body:'图片大小不能大于 500Kb！' ,
							okBtn : '确定'
						});
						$(this).val('');
						return;
					}


					var that = this;
					$(that).parent('div.img-item').find('.selectimg').remove();
					$(that).parent('div.img-item').find('input').hide();
					if (navigator.userAgent.indexOf('MSIE 8') >= 0 || navigator.userAgent.indexOf('MSIE 9') >= 0) {//如果是 IE8 IE9
						$(this).parent('div.img-item').css("filter", "progid:DXImageTransform.Microsoft.AlphaImageLoader(enabled='true',src=\"" + $(this).val() + "\",sizingMethod=scale)");
						$(this).parent('div.img-item').find('.del-img').show();
					}else {
						var imgurl = getObjectURL(filesList[0]);
						$(this).parent('div.img-item').show();
						$(this).parent('div.img-item').find('.selectimg').remove();
						$(this).parent('div.img-item').find('img').attr("src",imgurl).show();
						$(this).parent('div.img-item').find('.del-img').show();
					}
					if ($('#upload_' + $('input[name="type"]').val() +' .img-item').length == 3){
						$('#upload_' + $('input[name="type"]').val()).find('.up_l_img').hide();
					}
					checkoutRight();
					return false;
				})

				//删除已经上传的图片
				$('#J_addsuppliersDialog').on('click', '.img-item .del-img', function () {
					$(this).parent('div.img-item').remove();
					if ($('#upload_' + $('input[name="type"]').val() +' .img-item').length >= 3) {
						$('#upload_' + $('input[name="type"]').val()).find('.up_l_img').hide();
					} else {
						$('#upload_' + $('input[name="type"]').val()).find('.up_l_img').show();
					}
					checkoutRight();
					return false;
				})

				//多行文本框
				$('textarea[name="supplementNote"]').on('input propertychange',function(){
					var val_length = $(this).val()?$(this).val().length:0;

					$('#enterdNum').html(""+ val_length);
					if(val_length>=200){
						$('#enterdNum').css('color','red');
						$(this).val($(this).val().substring(0,200));
					}else{
						$('#enterdNum').css('color','#575766');
					}
					checkoutRight();
					return false;
				})




				//提交表单

				$('#jcSub').on('click',function(){
					if(!checkoutRight()){
						return;
					}
                    if(!isCanSub){
                    	return false;
					}
					isCanSub =false;
					//获取表单数据
					var postDataArr = $('#jc_form').find('input,textarea,select');
					//console.log(postDataArr,'----postDataArr--');
					var postData={
						productContentNew:[]
					}

					//过滤表单数据
					for (var i = 0; i <postDataArr.length ; i++) {
						var name = postDataArr[i].name;
						var value = postDataArr[i].value;
						if(name == 'productContentNew'&& postDataArr[i].checked){
							postData[name].push(value);
						}else{
							if(name != 'productContentNew'){
								postData[name] = value;
							}
						}
					}

					$('input[name="productContent"]').val(postData['productContentNew'].join(','));

					$('input[name="skuId"]').val($('#productId').val());

					postData['supplementNote']= postData['supplementNote'].replace(/[\{\}\[\]#$%^&*!]/g, '').replace(/</g,'&lt;').replace(/>/g,'&gt;');

					//获取图片数据
					var uploadInput = $('#upload_' + postData['type'] +' .img-item input');
					var fileList = [];
					for (var i = 0; i <uploadInput.length ; i++) {
						if(uploadInput[i].value || (uploadInput[i] && uploadInput[i].files && uploadInput[i].files.length)){
							var fileObj = uploadInput[i].files && uploadInput[i].files.length?uploadInput[i].files[0]: uploadInput[i].value;
							fileList.push(fileObj);
						}
					}

					//console.log(postData,'---postData----');
                    //必填项数据校验
					if(postData.type==1){
						if(postData.priceAdjustType!=3){
							if(!postData.modifyPrice){
								$.alert({
									title: "提示",
									body: '建议价格为必填项!',
									okBtn : '确定',
									hide:function(){
										isCanSub = true;
									}
								});
								return false;
							}else if(!postData.refPlatform){
								$.alert({
									title: "提示",
									body: '参考平台为必填项!',
									okBtn : '确定',
									hide:function(){
										isCanSub = true;
									}
								});
								return false;
						    }else{

							}
						}else{
							if(!fileList.length){
								$.alert({
									title: "提示",
									body: '商品纠错图片信息为必填项!',
									okBtn : '确定',
									hide:function(){
										isCanSub = true;
									}
								});
								return false;
							}else if(!postData.supplementNote){
								$.alert({
									title: "提示",
									body: '商品纠错补充说明为必填项!',
									okBtn : '确定',
									hide:function(){
										isCanSub = true;
									}
								});
								return false;
							}
						}
					}else if(postData.type==2){

						if(!postData.productContentNew.length){
							$.alert({
								title: "提示",
								body: '请选择纠错的商品信息内容!',
								okBtn : '确定',
								hide:function(){
									isCanSub = true;
								}
							});
							return false;
						}else if(!fileList.length){
							$.alert({
								title: "提示",
								body: '商品纠错图片信息为必填项!',
								okBtn : '确定',
								hide:function(){
									isCanSub = true;
								}
							});
							return false;
						}else if(!postData.supplementNote){
							$.alert({
								title: "提示",
								body: '商品纠错补充说明为必填项!',
								okBtn : '确定',
								hide:function(){
									isCanSub = true;
								}
							});
							return false;
						}
					}else if(postData.type==3){
						if(!fileList.length){
							$.alert({
								title: "提示",
								body: '商品纠错图片信息为必填项!',
								okBtn : '确定',
								hide:function(){
									isCanSub = true;
								}
							});
							return false;
						}else if(!postData.supplementNote){
							$.alert({
								title: "提示",
								body: '商品纠错补充说明为必填项!',
								okBtn : '确定',
								hide:function(){
									isCanSub = true;
								}
							});
							return false;
						}
					}


					if(postData['type']=='1'){
						$('#upload_2,#upload_3').find('.img-item').remove();
					}else if(postData['type']=='2'){
						$('#upload_1,#upload_3').find('.img-item').remove();
					}else if(postData['type']=='3'){
						$('#upload_1,#upload_2').find('.img-item').remove();
					}else{

					}
					//IE上对 formData 支持有兼容问题  这里使用 jquery.form.js  进行 异步表单提交
					$('#jiucuoForm').ajaxSubmit({
						url: "/errorCollection/save",
						type: "post",
						// processData: false,
						//contentType: "multipart/form-data",
						dataType: "text",
						data: {},
						success: function(data) {
							//console.log(data,'-----data-------');
							if(data){
								var parseData = JSON.parse(data);
								if(parseData.status=='success'){
									$.alert({
										title: "提示",
										body: '提交成功!',
										okBtn : '确定',
										hide:function(){
											isCanSub = true;
											$('#jc_close').trigger('click');
										}
									});
								}else{
									$.alert({
										title: "提示",
										body:parseData.errorMsg || '提交失败!' ,
										okBtn : '确定',
										hide:function(){
											isCanSub = true;
										}
									});
								}
							}else{
								$.alert({
									title: "提示",
									body:'提交出错!' ,
									okBtn : '确定',
									hide:function(){
										isCanSub = true;
									}
								});
							}
						},
						error:function(err){
							//console.log(err,'---err----');
							$.alert({
								title: "提示",
								body:'提交出错!' ,
								okBtn : '确定',
								hide:function(){
									isCanSub = true;
								}
							});
						}
					});
					return false;
				})
                var isTh = '${detail.isThirdCompany}';
                var orgId = '${detail.orgId}';
                if(isTh == 1 && orgId){
                    $.ajax({
                        url:'/company/center/companyInfo/getPopCompanyDetail.json',
                        data:{orgId:orgId},
                        dataType:'json',
                        success:function (result) {
                            companyDetailData = result.data;
							var upSkuNum = companyDetailData.upSkuNum + "种";
							var test = companyDetailData.saleSkuNum;
                            var saleNum = parseInt(test)
                            if(saleNum >= 0 && saleNum < 10000){
								$("#saleNum").text(test.toString()+"件");
							}else if(saleNum === 10000){
								$("#saleNum").text((test/10000).toString()+"万件");
							}else if(saleNum > 10000 && saleNum < *********) {
								$("#saleNum").text((test / 10000).toFixed(1).toString() + "万件");
							}else if(saleNum > *********){
									$("#saleNum").text((test/*********).toFixed(1).toString()+"亿件");
							}
                            $("#upSkuNum").text(upSkuNum);
                        }
                    });
                }
                function initShopInfo() {

                }
			})
			function preDetailAction() {
				console.log("jinru");
				let jgInfo = JSON.parse(sessionStorage.getItem("jgInfo")) || {};
				let jgspid = sessionStorage.getItem("jgspid") || '';
				console.log(jgInfo, "jginfo11")
				console.log(jgspid, "jginfo11222")
				console.log(`${detail}`, "jginfo22")
		
				// 进入详情页曝光
				
			}
			console.log("走了");
			preDetailAction();

			function getPrice(item) {
				if (item.actPt) {
				if (item.actPt.stepPriceStatus == 1) {
					//阶梯\
					return item.actPt.minSkuPrice;
				} else if (item.actPt.stepPriceStatus == 2 && item.actPt.assembleStatus == 1) {
					return item.actPt.assemblePrice;
				}
				return ''
				} else if (item.actPgby) {
				return item.actPgby.assemblePrice;
				} else if (item.priceType == 2 && item.skuPriceRangeList) {
				return item.skuPriceRangeList[item.skuPriceRangeList.length -1].price;
				} else {
				return item.fob
				}
			}

			function copyMedicalCode() {
				var codeText = document.getElementById("cnProductCode").value;
				var textarea = document.createElement("textarea");
				textarea.value = codeText;
				document.body.appendChild(textarea);
				textarea.select();
				document.execCommand("Copy");
				document.body.removeChild(textarea);
				
				//$.alert({
					//title: '提示',
					//body: '复制成功!'
				//});
			}
		</script>
		<script type="text/javascript">
		//qt埋点
		//PC商品详情页-页面曝光
		$(function(){
		 aplus_queue.push({
				'action': 'aplus.record',
				'arguments': ['page_exposure', 'EXP', {
				"spm_cnt":"1_4.productDetail_"+ $('#productId').val()+"-0_0.0.0."+window.getSpmE()
				}]
			});
		})
		//qt埋点
		//PC商品详情页-按钮点击
		function action_sub_module_click(position,text){

			try{
				window.qtdata= {
				"spm_cnt":"1_4.productDetail_"+$('#productId').val()+"-<EMAIL>@"+position+"."+window.getSpmE(),
				"scm_cnt":"pcFE.0.all_0.text_"+text+'.'+window.scmEShopDetail(14),
				}
				aplus_queue.push({
				'action': 'aplus.record',
				'arguments': ['action_sub_module_click', 'CLK',window.qtdata]
				});
			}catch(e){}
		}
			
		</script>
   </body>
</html>
