package com.xyy.ec.pc.common.helpers;

import com.xyy.ec.merchant.bussiness.result.MerchantClerkLicenseAuditInfoDTO;
import com.xyy.ec.pc.controller.vo.merchant.MerchantClerkLicenseAuditImageVO;
import com.xyy.ec.pc.controller.vo.merchant.MerchantClerkLicenseAuditInfoVO;

import java.util.List;
import java.util.Objects;

public class MerchantClerkLicenseAuditInfoDTOHelper {

    public static MerchantClerkLicenseAuditInfoVO create(MerchantClerkLicenseAuditInfoDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        List<MerchantClerkLicenseAuditImageVO> images = MerchantClerkLicenseAuditImageDTOHelper.creates(dto.getImages());
        return MerchantClerkLicenseAuditInfoVO.builder()
                .merchantId(dto.getMerchantId())
                .accountId(dto.getAccountId())
                .images(images).build();
    }

}
