<!DOCTYPE html>
<html lang="en">
<head>
<#include "/common/common.ftl" />
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>店铺首页</title>
    <link rel="stylesheet" href="/static/css/user.css?t=${t_v}" />
    <script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
    <script src="/static/js/requestAnimationFrame.js"></script>
    <script src="/static/js/jquery.lazyload.min.js"></script>
    <script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/sku_search.js?t=${t_v}"></script>
    <link rel="stylesheet" href="/static/css/headerAndFooter1.css">
    <link rel="stylesheet" href="/static/css/setToast/toast.style.css">
    <script type="text/javascript" src="/static/js/setToast/toast.script.js"></script>
    <script type="text/javascript" src="/static/js/qtShopDetail.js?t=${t_v}"></script>
    <style>
        .title1,.title3{
            width: 1200px;
            margin:0 auto;
            margin-top: 15px;
            margin-bottom: 10px;
            font-size: 20px;
            color: #333333;
            font-weight: 700;
        }
        .title1 span,.title3 span{
            display: inline-block;
            width: 3px;
            height: 20px;
            background: #00DC82;
            margin-right: 10px;
            vertical-align: top;
        }
        .title1 p{
            display: inline-block;
            vertical-align: top;
            font-size: 20px;
            color: #333333;
            font-weight: 700;
        }
        .title1 .more{
            float: right;
        }
        .title1 .more a{
            color: #707bb6;
            font-size: 18px;
        }
        .banner .carousel-indicators{
            top: 90%;
            right: 50%;
        }
        .banner .carousel-inner>.item>img,.banner .carousel-inner>.item>a>img{
            width: 1200px;
            height: 450px;
        }
        .banner .carousel-inner{
            margin: 0 auto;
            width: 1200px;
        }
        .coupons-box{
            width: 1200px;
            margin: 0 auto;
            position: relative;
            display: none;
        }
        #coupons-ul{
            height: 126px;
            padding-top: 8px;
        }
        .look-more{
            position: absolute;
            top: 4px;
            right: 27px;
            width: 100px;
            height: 17px;
            font-size: 12px;
            font-family: PingFangSC, PingFangSC-Regular;
            font-weight: 400;
            text-align: left;
            color: #6470b0;
            line-height: 17px;
            cursor: pointer;
            display: none;
        }
        .look-more span{
            display: inline-block;
            width: 11px;
            height: 10px;
            background: url('/static/img/plan/downLine.png') no-repeat;
            background-size: 100% 100%;
        }
        .look-more span.down{
            background: url('/static/img/plan/upLine.png') no-repeat;
            background-size: 100% 100%;
        }
        .position-div{
            position: absolute;
            top: 37px;
            right: 0;
            width: 817px;
            max-height: 373px;
            background: #ffffff;
            border: 1px solid #eeeeee;
            box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.06);
            overflow-y: auto;
            padding-left: 30px;
            padding-top: 25px;
            display: none;
            z-index: 100;
        }
        .shopNotice{
            margin: 0 auto;
            width: 1200px;
        }
        .shopNotice h4{
            padding: 10px 0 5px 0;
            font-size: 18px;
            font-weight: bold;
        }
        .shopNotice p{
            padding: 5px 0;
        }
        .my-carousel-text{
            line-height: 1.5;
        }
        .lwq-fix-icon {
            font-family: 'icon-pc' !important;
        }
    </style>
</head>
<body>
<div class="container">
    <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
    <input type="hidden" id="merchant" name="merchant" value="${merchant}"/>
    <input type="hidden" id="orgId" value="${orgId}"/>
    <input type="hidden" id="real" name="real" value="0"/>
    <input type="hidden" id="shopCode" name="shopCode" value="${indexVo.shopCode}"/>
    <input type="hidden" id="pageName" name="pageName" value="companyShopIndex"/>
        <!--头部导航区域开始-->
        <div class="headerBox" id="headerBox">
        <#include "/common/company/header.ftl" />
        </div>
        <!--头部导航区域结束-->
        <div class="banner clearfix">
                <div id="myCarousel" data-ride="carousel" data-interval="3000" class="sui-carousel my-carousel-text slide">
                    <#if indexVo.shopNotice>
                        <div class="shopNotice">
                            <h4>店铺公告</h4>
                            <p>${indexVo.shopNotice}</p>
                        </div>
                    </#if>
                    <ol class="carousel-indicators">
                      <#list indexVo.adrVOS as adr>
                              <li data-target="#myCarousel" data-slide-to="${adr_index}" <#if adr_index == 0> class="active" </#if>></li>
                      </#list>
                    </ol>
                    <div class="carousel-inner">
                        <#list indexVo.adrVOS as adr>
                            <div class="<#if adr_index == 0>active </#if>item" ><a href="${adr.url}"><img src="${adr.imgUrl}""/></a></div>
                        </#list>
                    </div>
                    <!-- <a href="#myCarousel" data-slide="prev" class="carousel-control left">‹</a> -->
                    <!-- <a href="#myCarousel" data-slide="next" class="carousel-control right">›</a> -->
                </div>
            </div>
        <div class="main" style="min-height: 500px">
            <div class="coupons-box">
                <div class="title1">
                    <span></span>店铺优惠券
                </div>
                <ul class="yhq-common weishiyong" id="coupons-ul">

                </ul>
                <div class="position-div">
                    <ul class="yhq-common weishiyong" id="position-ul"></ul>
                </div>
                <div class="look-more" id="coupons-more" show="show">查看全部优惠券 <span></span></div>
            </div>
           <div id="company_goods">
           </div>
        </div>
    <!--底部导航区域开始-->
    <div class="footer" id="footer">
    <#include "/common/footer.ftl" />
    </div>
    <!--底部导航区域结束-->
</div>

</body>
<script>
    var voucherTemplateId = $("#orgId").val();
    var merchantIdval = $("#merchantId").val();
    $(function () {
        getCoupons(voucherTemplateId,merchantIdval);
        $.ajax({
            url: "/company/center/companyInfo/company-floor?orgId=" + voucherTemplateId,
            type:"GET",
            success: function (data) {
                $("#company_goods").html(data);
            },
        })
    });
    function getCoupons(voucherTemplateId,merchantIdval) {
        $.ajax({
            url: "/coupon/getPopCouponList",
            type: "GET",
            dataType: "json",
            async: true,
            data: {
                orgId: voucherTemplateId
            },
            success:function (res) {
                if(res.code == 1000) {
                    var dataList = res.data.list;
                    if (dataList && dataList.length > 0) {
                        if(dataList.length > 3){
                            $("#coupons-more").show();
                        }
                        $(".coupons-box").show();
                        var html = '';
                        var timestamp = new Date().getTime();
                        for (var i = 0; i < dataList.length; i++) {
                            var html1 = '';
                            var html2 = '';
                            var html3 = '';
                            var html4 = '';
                            var html5 = '';
                            if (dataList[i].discount)  {
                                var num = parseFloat(dataList[i].discount);
                                html1 = '<span class="fuhao" style="font-size:22px;">' + num + '</span><span class="price" style="font-size:17px;">折</span>'
                            } else {
                                html1 = '<span class="fuhao">￥</span><span class="price">' + dataList[i].moneyInVoucher + '</span>'
                            }
                            if (dataList[i].maxMoneyInVoucherDesc && dataList[i].maxMoneyInVoucherDesc != null && dataList[i].maxMoneyInVoucherDesc != "") {
                                html2 = '<div class="yhq-lb-foot">' + dataList[i].maxMoneyInVoucherDesc + '</div>'
                            }
                            if (dataList[i].voucherType == 2) {
                                html3 = '<span class="quan">' + dataList[i].voucherTypeDesc + '</span>'
                            } else if (dataList[i].voucherType == 1) {
                                html3 = '<span class="quan quan-tong">' + dataList[i].voucherTypeDesc + '</span>'
                            } else if (dataList[i].voucherType == 6) {
                                html3 = '<span class="quan quan-die">' + dataList[i].voucherTypeDesc + '</span>'
                            } else if (dataList[i].voucherType == 5) {
                                html3 = '<span class="quan quan-xin">' + dataList[i].voucherTypeDesc + '</span>'
                            } else if (dataList[i].voucherType == 7) {
                                html3 = '<span class="quan quan-shop">' + dataList[i].voucherTypeDesc + '</span>'
                            }
                            if(dataList[i].state == 1){
                                html4 = '<a href="javascript:(0)" onclick="toReceive('+ dataList[i].templateId +')">立即领取</a>'
                            }
                            if (dataList[i].state == 2) {
                                html4 = '<img src="/static/img/plan/yilingqu.png" class="pos-ysy">'
                            }
                            if (dataList[i].state == 3) {
                                html4 = '<a href="javascript:(0)" class="yishiyong">已使用</a>'
                            }
                            if (dataList[i].state == 4 || (dataList[i].validityType == 2 && dataList[i].state == 2 && timestamp > dataList[i].expireDate)) {
                                html4 = '<img src="/static/images/user/ygq.png" class="pos-ysy">'
                            }
                            if(dataList[i].validityType && dataList[i].validityType == 2){
                                html5 = '<div style="font-size:12px;color:#999999;">'+ getNowFormatDate(dataList[i].validDate)+'-'+getNowFormatDate(dataList[i].expireDate)+'</div>'
                            }else{
                                if(dataList[i].validDays){
                                    html5 = '<div style="font-size:12px;color:#999999;">领取后'+ dataList[i].validDays +'天内使用</div>'
                                }
                            }
                            html += '<li><div class="yhq-lb"><div class="yhq-lb-top" style="font-weight:600;">' + html1 + '</div><div class="yhq-lb-foot">' + dataList[i].minMoneyToEnableDesc + '</div>' + html2 + '</div><div class="yhq-rb"><div class="yhq-rb-top">' + html3 + '<span class="info" title="' + dataList[i].shopName + '">' + dataList[i].shopName + '</span></div><div style="height:10px;margin-top:5px;"><span style="white-space: nowrap;display:inline-block;width:240px;font-size: 12px;overflow: hidden;text-overflow: ellipsis;word-break: break-all;" title="' + dataList[i].voucherTitle + '">' + dataList[i].voucherTitle + '</span></div><div style="height:17px;overflow:hidden;"></div><div class="yhq-rb-foot"><span title="' + dataList[i].voucherInstructions + '">' + dataList[i].voucherInstructions + '</span>' + html4 + '</div>'+html5+'</div></li>'
                        }
                        $("#coupons-ul").html(html);
                        $("#position-ul").html(html);
                    }
                }
            }
        })
    }

    function toReceive(id) {
        if(id){
           $.ajax({
               url: "/merchant/center/voucher/receiveVoucher",
               type: "GET",
               dataType: "json",
               async: true,
               data: {
                   merchantId: merchantIdval,
                   voucherTemplateId:id
               },
               success:function (res) {
                   if(res.status == 'success'){
                       $.Toast('',res.msg,'info',{
                           timeout:2000,
                           position_class: 'toast-top-center',
                       });
                       getCoupons(voucherTemplateId,merchantIdval)
                   }else{
                       $.Toast('',res.errorMsg,'info',{
                           timeout:2000,
                           position_class: 'toast-top-center',
                       });
                   }
               }
           })
        }
    }

    function getNowFormatDate(date) {
        var date = new Date(date);
        var seperator1 = "/";
        var seperator2 = "/";
        var seperator3 = "/";
        var year = date.getFullYear();
        // year = year.toString().substr(2, 2);
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate2 = year +seperator3+ month + seperator1 + strDate;

        return currentdate2;
    }
    $("#coupons-more").click(function () {
        console.log($(this).attr('show'))
        if($(this).attr('show') == 'show'){
            $(this).find('span').addClass('down')
            $(this).attr('show','more')
            $('.position-div').show();
        }else{
            $(this).find('span').removeClass('down')
            $(this).attr('show','show')
            $('.position-div').hide();
        }
    })
</script>
<script>
 //埋点四期
    //PC店铺主页-页面曝光
    function shopIndexExposure(){
        try{
            let spm_cnt = "1_4." + "shopIndex_" +  ($("#shopCode").val()||0) + "-0_0." + "0." + "0." + window.getSpmE()
            let data = {
                spm_cnt: spm_cnt
            }
            aplus_queue.push({
                action: "aplus.record",
                arguments: ["page_exposure", "EXP", data]
            })
        }catch(e){
            console.log(e)
        }
    }
    $(document).ready(function(){
        shopIndexExposure()
    })
    function isElementInViewport($element) {
        try{
            const elementTop = $element.offset().top; // 元素距离顶部的偏移量
            const elementBottom = elementTop + $element.outerHeight(); // 元素底部距离顶部的偏移量
            const viewportTop = $(window).scrollTop(); // 视口顶部位置
            const viewportBottom = viewportTop + $(window).height(); // 视口底部位置

            return elementBottom > viewportTop && elementTop < viewportBottom;
        }catch(e){}
    };
    //商品曝光
    function exposeShop(){
        try{
            $(".sku-item-qt").each(function(){
                if(isElementInViewport($(this))){
                    let data=($(this).attr("qtData")||"").split(",");
                    if(data.length>=5){
                        aplus_queue.push({
                            'action': 'aplus.record',
                            'arguments': ['page_list_product_exposure', 'EXP',{
                                    spm_cnt:"1_4.shopIndex_"+($("#shopCode").val()||0)+"-0_0.shopSkuList@"+(Number(data[1])+6)+".prod@"+(Number(data[0])+1)+"."+window.getSpmE(),//yz
                                    scm_cnt:"shop.0.all_0.prod-"+data[2]+"."+data[4],//yz
                                    product_id:Number(data[2]),
                                    product_name:data[3]
                                }]
                        });      
                     $(this).removeClass("sku-item-qt");                                
                    }                             
                }     
            });
        }catch(e){

        }
    }
    $(document).ready(function(){
      try{
        document.addEventListener('scroll',exposeShop);  
       setTimeout(
        function(){
            exposeShop() 
        },1000
       )
        //商品点击
        $(".sku-item-qt-click").click(function(){
            let data=($(this).attr("qtData")||"").split(","); 
            if(data.length>=5){
                 aplus_queue.push({
                    'action': 'aplus.record',
                    'arguments': ['action_list_product_click', 'CLK',{
                            spm_cnt:"1_4.shopIndex_"+($("#shopCode").val()||0)+"-0_0.shopSkuList@"+(Number(data[1])+6)+".prod@"+(Number(data[0])+1)+"."+window.getSpmE(),//yz
                            scm_cnt:"shop.0.all_0.prod-"+data[2]+"."+data[4]+window.scmEShopDetail(6),//yz
                            product_id:Number(data[2]),
                            product_name:data[3]
                        }]
                });  
            }
        })
      }catch(e){
         console.log(e)
      }
    })
    //商品按钮点击
    window.companyShopBtnClick = function (el,event,index,name){
       try{
            event.stopPropagation();
                if($("#pageName").val()=="companyShopIndex"){
                    var ancestorElement = $(el).closest('[qtData]')  
                    if(ancestorElement.length==0){
                    return;
                    }
                    let data=(ancestorElement.attr("qtData")||"").split(","); 
                    if(data.length<5){
                        return;
                    }              
                    let spmE=window.getSpmE();
                    let scmE=data[4]+window.scmEShopDetail(6);
                    window.qtdata={
                        spm_cnt:"1_4.shopIndex_"+($("#shopCode").val()||0)+"-0_0.shopSkuList@"+(Number(data[1])+6)+".prod@"+(Number(data[0])+1)+"_btn@"+index+"."+spmE,//yz
                        scm_cnt:"shop.0.all_0.prod-"+data[2]+"_text-"+name+"."+scmE,//yz
                    }
                    //商品按钮点击
                    aplus_queue.push({
                        'action': 'aplus.record',
                        'arguments': ['action_product_button_click', 'CLK',{
                                spm_cnt:"1_4.shopIndex_"+($("#shopCode").val()||0)+"-0_0.shopSkuList@"+(Number(data[1])+6)+".prod@"+(Number(data[0])+1)+"_btn@"+index+"."+spmE,//yz
                                scm_cnt:"shop.0.all_0.prod-"+data[2]+"_text-"+name+"."+scmE,//yz
                                product_id:Number(data[2]),
                                product_name:data[3]
                            }]
                    });  
                    //商品点击
                    aplus_queue.push({
                        'action': 'aplus.record',
                        'arguments': ['action_list_product_click', 'CLK',{
                                spm_cnt:"1_4.shopIndex_"+($("#shopCode").val()||0)+"-0_0.shopSkuList@"+(Number(data[1])+6)+".prod@"+(Number(data[0])+1)+"."+spmE,//yz
                                scm_cnt:"shop.0.all_0.prod-"+data[2]+"."+scmE,//yz
                                product_id:Number(data[2]),
                                product_name:data[3]
                        }]
                    });  
                }
            }catch(e){
            console.log(e);
        } 
    }
</script>
</html>
