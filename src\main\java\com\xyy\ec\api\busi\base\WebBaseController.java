package com.xyy.ec.api.busi.base;

import com.xyy.ec.base.exception.BusiCommonException;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.model.dto.XyyIpAddressInfoDTO;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.XyyIpAddressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/9 17:58
 * @Description:公共controller基类
 */
@Slf4j
public class WebBaseController  {
    private static final String LOGIN_ERROR = "登录服务异常";
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private XyyIpAddressService xyyIpAddressService;

    protected HttpServletRequest getRequest() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return request;
    }

    protected  Long getUserId(){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant != null) {
                return merchant.getId();
            }
        } catch (Exception e) {
            throw new BusiCommonException(ApiResultCodeEum.NETWORK_ERROR.getCode(),ApiResultCodeEum.NETWORK_ERROR.getMsg(),LOGIN_ERROR,e);
        }
        return null;
    }
    protected  String getBranchCode(){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant != null) {
                return merchant.getRegisterCode();
            }else {
                // 未登录从ip中定位获取
                XyyIpAddressInfoDTO xyyIpAddressInfo = xyyIpAddressService.getXyyIpAddressInfo(getRequest());
                return xyyIpAddressInfo.getBranchCode();
            }
        } catch (Exception e) {
            throw new BusiCommonException(ApiResultCodeEum.NETWORK_ERROR.getCode(),ApiResultCodeEum.NETWORK_ERROR.getMsg(),LOGIN_ERROR,e);
        }
    }

}
