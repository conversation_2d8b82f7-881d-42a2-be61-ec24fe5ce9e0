package com.xyy.ec.pc.cms.helpers;

import com.xyy.ec.pc.cms.param.CmsSpecifiedProductQueryParam;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 */
public class CmsSpecifiedProductQueryParamHelper {

    /**
     * 校验
     *
     * @param cmsSpecifiedProductQueryParam
     * @return
     */
    public static Boolean validate(CmsSpecifiedProductQueryParam cmsSpecifiedProductQueryParam) {
        // 校验
        if (cmsSpecifiedProductQueryParam == null) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "商品查询");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (CollectionUtils.isEmpty(cmsSpecifiedProductQueryParam.getSkuIdList())) {
            String message = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "商品ID");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, message);
        }
        if (StringUtils.isEmpty(cmsSpecifiedProductQueryParam.getBranchCode())) {
            String message = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "分公司编码");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, message);
        }
        return true;
    }
}
