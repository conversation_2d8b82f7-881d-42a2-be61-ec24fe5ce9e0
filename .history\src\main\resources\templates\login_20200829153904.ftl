<!DOCTYPE HTML>
<html>

<head>
	<#include "common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>用户登录-药帮忙</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="/static/css/login-headerAndFooter.css"/>
    <link rel="stylesheet" href="/static/css/login.css"/>
    <script type="text/javascript" src="/static/js/login.js"></script>
    <script type="text/javascript" src="/static/js/util.js"></script>
    <script src="/static/js/plugins/jquery.md5.js" type="text/javascript"></script>
    <script src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
    <style>
        .sui-btnChenk{
            /*display: none;*/
            opacity: 0;
            height: 0;
        }
    </style>
</head>

<body>
<div class="container">

    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
    	<#include "login_header.ftl" />
    </div>
    <!--头部导航区域结束-->


    <!--主体部分开始-->
    <div class="main">
        <input type="hidden" value="${level}" id="level">
        <input type="hidden" value="${loginName}" id="loginName">
        <input type="hidden" value="${loginPass}" id="loginPass">

        <div class="loginmain">
            <div class="loginbox">
                <ul class="l-title">
                    <li class="phonelogin cur">账户登录</li>
                    <!-- <li class="smlogin">扫码登录</li>-->
                </ul>
                <!--错误提示-->
                <div class="err-box">
                <#if errorMsg??>
                    <input type="hidden" value="${errorMsg}" id="error">
                <div class="errinfo">
                <#else>
                    <div class="errinfo noshow">
                </#if>
                    <img src="/static/images/cuowutishi.png" class="errorimg"> <span class="wenantishi"></span>
                    </div>
                </div>
                    <!--账号登录-->
                    <div class="l-con">

                        <form class="sui-form form-horizontal sui-validate" action="/login/login.htm" id="loginForm" method="post" onsubmit="return check(this)">
                            <input type="hidden" name = "redirectUrl" id="redirectUrl" value="${redirectUrl }"/>
                            <input type="hidden" id="registerSource" name="registerSource" value="${registerSource}"/>
                            <div class="control-group">
                                <div class="controls">
                                    <div class="spediv">
                                        <img src="/static/images/ren.png" alt="">
                                        <input type="text" id="inputPhone" class="inputPhone" name="name" placeholder="请输入手机号" data-rules="">
                                    </div>
                                </div>
                            </div>
                            <div class="control-group jiange">
                                <div class="controls">
                                    <div class="spediv">
                                        <img src="/static/images/suo.png" alt="">
                                        <input type="password" id="inputPassword" class="inputPassword" name="password" placeholder="密码" data-rules="" title="密码">
                                    </div>

                                </div>
                            </div>
                            <div>
                                <!--点击此元素会自动激活验证码-->
                                <!--id : 元素的 ID (必须)-->
                                <!--data-appid : AppID(必须)-->
                                <!--data-cbfn : 回调函数名(必须)-->
                                <!--data-biz-state : 业务自定义透传参数(可选)-->
                                <button class="sui-btnChenk btn-primary"
                                        id="TencentCaptchaBtn"
                                        type="button"
                                        onclick="javascript:loginCheck();"
                                >验证</button>
                            </div>
                            <div class="control-group">
                                <div class="controls">
                                    <button type="button" onclick="login();" class="sui-btn btn-primary">登录</button>
                                </div>
                            </div>

                            <div class="wrapbox">
                                <label class="checkbox-pretty inline checked" id="remember">
                                    <input type="checkbox" checked="checked"><span>记住密码</span>
                                </label>
                                <a href="/login/forgetPassword.htm" class="fr">忘记密码？</a>
                            </div>
                            <div class="agreement-box">
                                <label class="checkbox-pretty inline checked" id="agreement">
                                    <input type="checkbox" name="agreement"  checked/>
                                    <span>同意</span>
                                </label>
                                <a target="_blank" href="/login/agreement.htm">《用户服务协议》</a>和<a target="_blank" href="/helpCenter/privacy.htm">《隐私政策》</a>
                            </div>

                        </form>
                        <div class="bot-info">
                            还没有账号，立即去
                            <a href="/login/register.htm?registerSource=${registerSource}">注册</a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!--主体部分结束-->

        <!--底部导航区域开始-->
        <div class="footer" id="footer">
    	    <#include "login_footer.ftl" />
        </div>
        <!--底部导航区域结束-->


    </div>

    <script>

        var loginName = $('#loginName').val();
        var loginPass = $('#loginPass').val();
        // console.log('loginName:',loginName);
        // console.log('loginPass:',loginPass)
        if (loginName!==''){
            $('#inputPhone').val(loginName);

        }if (loginPass !==""){
            $('#inputPassword').val(loginPass);
        }
        var level = $("#level").val();
        // console.log('level11111:',typeof level)
        if (level===0||level==="0"){
            // console.log('level22222:',level)
            $('#TencentCaptchaBtn').trigger("click");
        }

        function loginCheck() {
            var checkd = this.check($('#loginForm')[0]);
            // console.log('checkd',checkd)
            if (checkd){
                $(".l-title").css("display","block");
                $(".err-box").css("display","none");
            }else{
                return;
            }
            if(!$('#agreement').hasClass('checked')){
                $.alert({"title":"注意","body":"请阅读并同意服务条款","okBtn":"我知道了"});
                return;
            }
            var loginPhone = $('#inputPhone').val();
            var captcha1 = new TencentCaptcha('2033317447', function(res) {
                // res（用户主动关闭验证码）= {ret: 2, ticket: null}
                // res（验证成功） = {ret: 0, ticket: "String", randstr: "String"}
                if(res.ret === 0){
                    // console.log('res.ticket',res.ticket);  // 票据
                    // console.log('res.randstr',res.randstr);
                    var _this = this;
                    $.ajax({
                        url: '/validate/slideVerification.json',
                        type: 'get',
                        data: { mobile: loginPhone, ticket: res.ticket, randStr:res.randstr},
                        dataType: 'json',
                        success:  function(res){
                            if (res.status==='success'){
                                $("#level").val(999);
                                // console.log('$("#level").val(999):',$("#level").val())
                                _this.login();
                            }else {
                                // console.log('resres:',res)
                                $(".wenantishi").text(res.errorMsg);
                                $(".l-title").css("display","none");
                                $(".err-box").css("display","block");
                                return false;
                            }
                        }
                    })

                }},
                { bizState: '自定义透传参数' }
                );

            captcha1.show();

        }
        function login(){
            if(!$('#agreement').hasClass('checked')){
                $.alert({"title":"注意","body":"请阅读并同意服务条款","okBtn":"我知道了"});
                return;
            }

            var level = $("#level").val();
            // console.log('登录level:',level)
            if (level===0||level==="0"){
                // console.log('level444:',level)
                $('#TencentCaptchaBtn').trigger("click");
                return;
            }
            var loginPhone = $('#inputPhone').val();
            var loginPwd = $('#inputPassword').val();
            if(loginPhone != '' && loginPwd != ''){
                if($('#remember').hasClass("checked")){
                    if(!loginPwd.match(/^([a-fA-F0-9]{32})$/)){
                        var md5LoginPwd =  $.md5(loginPwd);
                        $('#inputPassword').val(md5LoginPwd);
                    }
                }else{
                    if(!loginPwd.match(/^([a-fA-F0-9]{32})$/)){
                        var md5LoginPwd =  $.md5(loginPwd);
                        $('#inputPassword').val(md5LoginPwd);
                    }
                }
            }
            $("#loginForm").submit();
        }

        var errorMsg = $("#error").val();
        if(errorMsg!=null){
            $(".l-title").css("display","none");
            $(".err-box").css("display","block");
            $(".wenantishi").text(errorMsg);
        }

        $(document).ready(function() {
            var pressKeyInt = 0;
            /*搜索弹窗响应键盘事件*/
            $("#inputPassword").keyup(function (event) {
                var e = event || window.event;
                var k = e.keyCode || e.which;

                switch (k) {
                    case 13:
                        login();
                        // console.log("回车");
                        break;

                }
            });
        });
    </script>
</body>

</html>