package com.xyy.ec.pc.cms.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.pc.cms.dto.CmsListProductDto;
import com.xyy.ec.pc.cms.param.CmsExpectProductQueryParam;
import com.xyy.ec.pc.cms.param.CmsProductQueryParam;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface CmsSkuService {

    /**
     * 根据会员ID、商品展示组ID查询期望数量的商品列表
     *
     * @param cmsExpectProductQueryParam
     * @return
     */
    List<ListProduct> listExpectProducts(CmsExpectProductQueryParam cmsExpectProductQueryParam);

    /**
     * 根据会员ID、商品展示组ID查询商品列表（带翻页功能）
     *
     * @param cmsProductQueryParam
     * @param pageNum
     * @param pageSize
     * @return
     */
    CmsListProductDto listProducts(CmsProductQueryParam cmsProductQueryParam, int pageNum, int pageSize);

    /**
     * 单品（非商品组，商品id集合请求）
     *
     * @param isAdmin
     * @param branchCode
     * @param skuIds
     * @param merchantId
     * @return
     */
    List<ListProduct> pagingUsingProducts(Boolean isAdmin, String branchCode, List<Long> skuIds, Long merchantId);

    /**
     * 获取为你推荐商品信息列表
     *
     * @param branchCode
     * @param merchantId
     * @param pageNum
     * @param pageSize
     * @param terminalType
     * @return
     */
    PageInfo<ListProduct> listRecommendToYouSkuInfos(String branchCode, Long merchantId, int pageNum, int pageSize, Integer terminalType);

    /**
     * 根据商品id查询拼团活动信息。
     * 按活动开始时间升序、活动ID降序排序。
     * 分页查询。
     *
     * @param skuIdList
     * @param statusList
     * @param merchantId
     * @return
     */
    Map<Long, GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdList(List<Long> skuIdList, List<Integer> statusList, Long merchantId);

    /**
     * 根据商品id查询拼团和批购包邮活动信息。
     * 按活动开始时间升序、活动ID降序排序。
     * 分页查询。
     *
     * @param skuIdList
     * @param statusList
     * @param merchantId
     * @param activityTypeSet
     * @return
     */
    Map<Long, GroupBuyingInfoDto> getMarketingActivityInfoBySkuIdList(List<Long> skuIdList, List<Integer> statusList, Long merchantId, Set<Integer> activityTypeSet, Set<Long> gaoMaoSkuIdSet);

    /**
     * 填充商品的营销拼团活动信息
     *
     * @param products
     * @return
     */
    List<CmsListProductVO> fillListProductsMarketingActivityInfo(List<CmsListProductVO> products, Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap);

    /**
     * 隐藏营销拼团活动价格信息
     *
     * @param products
     * @return
     */
    List<CmsListProductVO> hideListProductsMarketingGroupBuyingActivityPriceInfo(List<CmsListProductVO> products);

    /**
     * 填充商品的店铺信息
     *
     * @param products
     * @return
     */
    List<CmsListProductVO> fillListProductsShopInfo(List<CmsListProductVO> products);

    /**
     * 填充商品的数据标签信息
     *
     * @param merchantId
     * @param products
     * @param isQueryShopDataTags 可选
     * @return
     */
    List<CmsListProductVO> fillListProductsTagInfo(Long merchantId, List<CmsListProductVO> products, Boolean isQueryShopDataTags);


    /**
     * 判定商品是否在商品组中
     * @param exhibitionIdStrList
     * @param productIds
     * @return
     */
    List<Long> listUsingExhibitionProductIds(List<String> exhibitionIdStrList, List<Long> productIds);
}
