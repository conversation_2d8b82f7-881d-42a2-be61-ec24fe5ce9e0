<!DOCTYPE HTML>
<html>

<head>
	<#include "/common/common.ftl" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
	<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
	<title>用户注册-药帮忙</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="renderer" content="webkit">
	<link rel="stylesheet" href="/static/css/login-headerAndFooter.css?t=${t_v}"/>
	<link rel="stylesheet" href="/static/css/reg.css?t=${t_v}"/>
	<script type="text/javascript" src="/static/js/reg.js?t=${t_v}"></script>
	<script type="text/javascript">
	
		function mobileIdentify() {
		    var regMobile = /^1\d{10}$/;
		    var mobile = $("#mobile").val();
            if(mobile.length !=0 && !(regMobile.test(mobile))){
                $('#mobileIdentify').show();
            }else{
                $('#mobileIdentify').hide();
			}
        }
	</script>
</head>

<body>
	<div class="container">

		<!--头部导航区域开始-->
		<div class="headerBox" id="headerBox">
            <div class="login-nav">
                <div class="p-lbox fl">
                    <a href="/"><img src="/static/images/logo_login.png" alt="" /></a>
                </div>
                <#--<div class="p-rbox fr">
                    <img src="/static/images/buzou4.png" alt="">
                </div>-->
            </div>
		</div>
		<!--头部导航区域结束-->


		<!--主体部分开始-->
		<div class="main">
			<div class="regbox">
				<div class="reg-title">
                    <span class="gologin">已经有账号，立刻去<a href="/login/login.htm">登录</a> </span>
				</div>

				<div class="con-box">
					<form id="regForm" class="sui-form form-horizontal sui-validate" method="post">
						<input type="hidden" id="province_code" name="provinceCode" value="${provinceCode}"/>
						<input type="hidden" id="city_code" name="cityCode"/>
						<input type="hidden" id="area_code" name="areaCode"/>
						<input type="hidden" id="province" name="province"/>
						<input type="hidden" id="street_code" name="streetCode"/>
						<input type="hidden" id="city1" name="city"/>
						<input type="hidden" id="district" name="district"/>
						<input type="hidden" id="is_next_step" name="is_next_step"/>
						<input type="hidden" id="street" name="street"/>
                        <input type="hidden" id="registerSource" name="registerSource" value="${registerSource}"/>
                        <input type="hidden" id="organSign" name="organSign" value="${organSign}"/>
						<input type="hidden" name="poiId" />
						<input type="hidden" name="realName" />
						<input type="hidden" name="address" />
						<input type="hidden" name="latitude" />
						<input type="hidden" name="longitude" />
						<div class="control-group">
							<label for="mobile" class="control-label">手机号码：</label>
							<div class="controls">
								<input type="text" id="mobile" name="mobile" placeholder="请输入您的手机号码" value="${merchant.mobile}"
									data-rules="required" name="mobile" oninput="mobileIdentify()" maxlength="11" onfocus="$('#errorMsg').addClass('noshow');">
							</div>
                            <div id="mobileIdentify" class="sui-msg msg-error help-inline" style="display: none;">
                                <div class="msg-con">
                                    <span>请填写正确的手机号码</span>
                                </div>   <img src="/static/images/cuowutishi.png" class="errorimg">
                            </div>
                            <div class="err-box" >
                                <div class="errinfo noshow" id="errorMsg">
                                    <img src="/static/images/cuowutishi.png" class="errorimg" style="position:unset"> <span class="wenantishi"></span>
                                </div>
                            </div>
						</div>
                        <!-- 图片验证码-->
                        <div class="control-group">
                            <label for="yzcode" class="control-label">验证码：</label>
                            <div class="controls">
                                <input type="text" id="photoCode" placeholder="请输入您的验证码"  name="photoCode" data-rules="required">
                            </div>
                            <img alt="" src="/login/getCode.htm?merchantId=${merchant.id!0}" width="100" height="30" title="点击刷新" id="randomImg" onclick="refreshRandom();">
                            <a id="vcodeA" href="javascript:void(0);" class="switchvCode" tabindex="-1" onclick="refreshRandom();">看不清楚？换张图片</a>
                            <!--验证码错误提示-->
                            <div class="yzmerror sui-msg msg-error help-inline noshow">
                                <div class="msg-con">
                                    <span>验证码填写错误</span>
                                </div>
                                <i class="msg-icon"></i>
                            </div>

                        </div>
						<#if photomsg??>
							<div class="fberr">
								<div class="fberr-box">
									<img src="/static/images/cuowutishi.png"> ${photomsg}
								</div>
							</div>
						</#if>
                        <!--手机验证码-->
                        <div class="control-group specont">
                            <label for="yzcode" class="control-label">短信验证码：</label>

                            <div class="controls yzmwarp">
                                <input type="text" id="code" placeholder="请输入短信验证码"  name="code" data-rules="required" value="${code!""}" onblur="hiddenCode()">
                                <a href="javascript:;" class="yzmbox">
                                    获取短信验证码
                                </a>
                                <a href="javascript:;" class="yzmbox-repe">
                                    重新发送（<span class="datasub">60</span>）
                                </a>
                            </div>

                            <!--验证码错误提示-->
							<#if verification?exists>
                                <div class="yzmerror sui-msg msg-error help-inline">
                                    <div class="msg-con" id="yzcode">
                                        <img src="/static/images/cuowutishi.png"> <span>${msg}</span>
                                    </div>
                                </div>
							</#if>

                        </div>


						<div class="control-group">
							<label for="password" class="control-label">设置密码：</label>
							<div class="controls">
								<input type="password" id="password" name="password" placeholder="请设置密码"
									data-rules="required|minlength=8|maxlength=16" name="password" maxlength="16">
							</div>

							<!--密码规则提示 -->
							<div class="err-box">
								<#if checkPwd?exists>
									<div class="errinfo" id="realNameErrorMsg">
									<img src="/static/images/cuowutishi.png"> <span class="wenantishi">${msg}</span>
									</div>
								</#if>
							</div>
						</div>
						<span style="width: 240px;height: 38px;line-height: 38px;padding: 0;padding-left: 109px;">密码必须8-16个字符，至少含数字、大写和小写字母、符号两种组合，首位只能为字母！</span>
						<div class="control-group">
							<label for="repassword" class="control-label">确认密码：</label>
							<div class="controls">
								<input type="password" id="repassword" placeholder="请再次输入您的密码"
									data-rules="required|match=password" name="repassword">
							</div>
						</div>

						<div class="control-group">
							<label for="name" class="control-label">姓名：</label>
							<div class="controls">
								<input type="text" id="name" name="nickname" placeholder="请输入您的姓名"
									data-rules="required|minlength_xm=2|hanzi|maxlength_xm=8" name="name" value="${merchant.nickname}">
							</div>
						</div>

						<div class="control-group">
							<label for="name" class="control-label">关联药店：</label>
							<div class="controls">
								<div id="pharmacy-form" class="pharmacy-item pharmacy-form">
									<div class="pharmacy-empty">
										<span>请选择关联的店</span>
										<img class="right" src="/static/images/svg/right.svg" align="right">
									</div>
									<div class="pharmacy-name">
										<div class="pharmacy-name-text text-ellipsis">-</div>
										<img class="right" src="/static/images/svg/right.svg" align="right">
										<div class="clear"></div>
									</div>
									<div class="pharmacy-address text-ellipsis">-</div>
								</div>
							</div>
							<div class="err-box">
								<div class="errinfo" id="pharmacyMsg" style="display: none">
									<img src="/static/images/cuowutishi.png" class="error-info-img"> <span class="wenantishi">请选择</span>
								</div>
							</div>

						</div>

<#--						<div class="control-group">-->
<#--							<label for="name" class="control-label">店铺名称：</label>-->
<#--							<div class="controls">-->
<#--								<input type="text" id="shop" name="realName" placeholder="请输入您的店铺名称"-->
<#--									data-rules="required|minlength_dm=5|hanziAndzimu" name="shop" value="${merchant.realName}" onblur="hiddenRealName()">-->
<#--							</div>-->
<#--							<!--店铺重名错误 &ndash;&gt;-->
<#--                            <div class="err-box">-->
<#--								<#if status?exists>-->
<#--                                    <div class="errinfo" id="realNameErrorMsg">-->
<#--                                        <img src="/static/images/cuowutishi.png"> <span class="wenantishi">${msg}</span>-->
<#--                                    </div>-->
<#--								</#if>-->
<#--                            </div>-->
<#--						</div>-->

<#--						<div class="control-group">-->
<#--							<label for="mobile" class="control-label">收货地址：</label>-->
<#--							<div class="controls">-->
<#--								<div id="city_4">-->
<#--									<select class="prov" id="prov" name="provinces">-->
<#--									</select> -->
<#--									<select class="city" id="city" name="citys">-->
<#--									</select> -->
<#--									<select class="dist" id="dist" name="districts">-->
<#--									</select>-->
<#--									<select class="dist" id="street1" name="street1">-->
<#--									</select>-->
<#--								</div>-->
<#--								<div class="controls">-->
<#--									<input type="text" class="detailinp" name="address"-->
<#--										placeholder="请输入街道/门牌号　详细地址"-->
<#--										data-rules="required|minlength=2|maxlength=100" maxlength="100" id="address">-->
<#--								</div>-->
<#--							</div>-->
<#--						</div>-->
						<!--后台提示错误框 去掉类名noshow显示-->
						<#if exc?exists>
		                    <div class="fberr">
		                        <div class="fberr-box">
		                            <i class="sui-icon icon-touch-minus-circle-sign"></i> ${msg}
		                        </div>
		                    </div>
	                    </#if>
						<div class="wrapbox agreement-box" style="padding-bottom:20px;padding-left: 110px;">
							<label class="checkbox-pretty inline all" id="agreeId">
								<input type="checkbox"><span></span>
							</label>
							<span class="yundu">同意</span>
							<a href="/login/agreement.htm" class="" target="_blank">《用户服务协议》</a>和
							<a href="/helpCenter/privacy.htm" class="" target="_blank">《隐私政策》</a>
						</div>
						<div class="control-group">
							<label class="control-label"></label>
							<div class="controls">
								<button type="button" id="regNext" class="sui-btn btn-primary btn-large">注册</button>
							</div>
						</div>
					</form>
				</div>
			</div>


		</div>
		<!--主体部分结束-->
		<#include "/pharmacy.ftl" />
		<!--底部导航区域开始-->
		<div class="footer" id="footer">
			<#include "/login_footer.ftl" />
		</div>
		<!--底部导航区域结束-->


	</div>

	<script type="text/javascript">

    $(".sui-form").validate({
        messages: {
            repassword:["确认密码不能为空","确认密码要与密码一致"],
            name:["姓名不能为空","姓名必须大于一个字"],
            shop:["店名不能为空","店名必须不小于五个字"]
        }
    })


</script>
</body>

</html>