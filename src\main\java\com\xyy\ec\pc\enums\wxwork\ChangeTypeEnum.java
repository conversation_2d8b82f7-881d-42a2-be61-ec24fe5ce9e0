package com.xyy.ec.pc.enums.wxwork;

import com.xyy.ec.pop.server.api.Enum.BaseStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ChangeTypeEnum {

    ADD_EXTERNAL_CONTACT("add_external_contact", "change_external_contact", "添加企业客户事件"),
    ;

    private String code;
    private String event;
    private String name;

    public static ChangeTypeEnum getByCodeAndEvent(String code,String event) {
        for (ChangeTypeEnum changeTypeEnum : ChangeTypeEnum.values()) {
            if (Objects.equals(changeTypeEnum.code, code) && Objects.equals(changeTypeEnum.event, event)) {
                return changeTypeEnum;
            }
        }
        return null;
    }
}
