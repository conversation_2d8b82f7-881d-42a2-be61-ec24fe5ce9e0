package com.xyy.ec.pc.authentication.aop;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.merchant.server.dto.LoginAccountDto;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.base.PasswordVerifier;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @Date 2024/4/10 11:16
 * @File BeanSwitchAspect.class
 * @Software IntelliJ IDEA
 * @Description Bean切换切面类
 */
@Slf4j
@Aspect
@Component
public class BeanSwitchAspect {

    @Value("${jwt_open:false}")
    private Boolean openJwt;

    @Value("${jwt_grayscale_value:0}")
    private Integer jwtGrayscaleValue;

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    @Autowired
    private TokenService tokenService;

    @Around("execution(* (@com.xyy.ec.pc.authentication.aop.BeanSwitch *).*(..))")
    public Object switchAuth(ProceedingJoinPoint joinPoint) throws Throwable {

        return doSomeThing(joinPoint, joinPoint.getTarget().getClass().getAnnotation(BeanSwitch.class));
    }

    @Around("@annotation(beanSwitch)")
    public Object switchAuth(ProceedingJoinPoint joinPoint, BeanSwitch beanSwitch) throws Throwable {

        return doSomeThing(joinPoint, beanSwitch);
    }

    public Object doSomeThing(ProceedingJoinPoint joinPoint, BeanSwitch beanSwitch) throws Throwable {

        if (openJwt) {
            String token = tokenService.getToken();
            String xyyPrincipal = tokenService.getXyyPrincipal();
            // 如果cookie中有token，表示已采用新版登录方式登录，则使用新的实现类，如果cookie没有token和xyy_principal则是未登录状态，也走新版实现类
            if (StrUtil.isNotBlank(token) || (StrUtil.isBlank(token) && StrUtil.isBlank(xyyPrincipal))) {
                // 获取入参值
                Object[] args = joinPoint.getArgs();
                // 获取方法名、入参类型
                MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
                String methodName = methodSignature.getName();
                
                // TODO 判断是否进行灰度
                Long xyyPrincipalAccountId = tokenService.getXyyPrincipalAccountId();
                if ("login".equals(methodName)) {
                    PasswordVerifier passwordVerifier = (PasswordVerifier) args[0];
                    String mobile = passwordVerifier.getLoginName();
                    LoginAccountDto loginAccountDto = loginAccountApi.selectLoginAccountByMobile(mobile);
                    xyyPrincipalAccountId = loginAccountDto.getId();
                }
                if ("setPrincipalMerchant".equals(methodName)) {
                    xyyPrincipalAccountId = (Long) args[1];
                }
                if (!(xyyPrincipalAccountId != null && xyyPrincipalAccountId % 100 < jwtGrayscaleValue)) {
                    log.info("账号不进行灰度[{}]", xyyPrincipalAccountId);
                    return joinPoint.proceed();
                }
                // TODO 判断是否进行灰度
                log.info("使用JWT认证BeanSwitchAspect，{}", token);
                Class<?>[] parameterTypes = methodSignature.getParameterTypes();
                // 获取切换后的bean
                Class<?> clazz = beanSwitch.value();
                Object bean = SpringUtil.getBean(clazz);
                // bean存在，且方法存在
                if (bean != null && hasMethod(clazz, methodName, parameterTypes)) {
                    return invokeMethod(bean, methodName, parameterTypes, args);
                }
            }
        }
        return joinPoint.proceed();
    }

    /**
     * 判断方法是否存在
     */
    private static boolean hasMethod(Class<?> beanClass, String methodName, Class<?>... parameterTypes) {

        try {
            beanClass.getMethod(methodName, parameterTypes);
            return true;
        } catch (NoSuchMethodException e) {
            return false;
        }
    }

    /**
     * 反射调用方法
     */
    private static Object invokeMethod(Object bean, String methodName, Class<?>[] parameterTypes, Object[] args)
            throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException,
            InvocationTargetException {

        if (args != null && args.length > 0) {
            Method method = bean.getClass().getMethod(methodName, parameterTypes);
            return method.invoke(bean, args);
        }
        else {
            Method method = bean.getClass().getMethod(methodName);
            return method.invoke(bean);
        }
    }
}