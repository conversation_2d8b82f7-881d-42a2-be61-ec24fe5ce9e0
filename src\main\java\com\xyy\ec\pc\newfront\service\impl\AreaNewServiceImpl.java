package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.xyy.address.api.BaseRegionBusinessApi;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.address.dto.XyyRegionParams;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.newfront.service.AreaNewService;
import com.xyy.ec.system.business.api.BranchBusinessApi;
import com.xyy.ec.system.business.dto.BranchBusinessDto;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import static com.xyy.ec.pc.config.BranchEnum.*;

/**
 * <AUTHOR>
 * @date 2025-07-11 9:46
 */
@Service
@Slf4j
public class AreaNewServiceImpl implements AreaNewService {


    @Reference(version = "1.0.0")
    private BaseRegionBusinessApi baseRegionBusinessApi;

    @Reference(version = "1.0.0")
    private BranchBusinessApi branchBusinessApi;

    @Override
    public AjaxResult<Object> findAreaById(Integer parentId) {
        try {
            List<XyyRegionBusinessDto> dtos = null;
            List<XyyRegionBusinessDto> list = new ArrayList<>();
            if (parentId != null) {
                List<String> branchs = getEcAllBranchs();
                XyyRegionParams params = new XyyRegionParams();
                params.setParentCode(parentId);
                dtos = baseRegionBusinessApi.queryRegionByParentCode(params);
                for (XyyRegionBusinessDto dto : dtos) {
                    dto.setId(dto.getAreaCode());
                    if (branchs.contains(dto.getAreaCode().toString().substring(0, 2))) {
                        list.add(dto);
                    }
                }
            }
            return AjaxResult.successResult(list);
        } catch (Exception e) {
            log.error("区域获取异常", e);
            return AjaxResult.errResult("区域获取异常");
        }
    }


    private List<String> getEcAllBranchs() {
        List<String> branchs = new ArrayList<>();
        List<BranchBusinessDto> allBranchs = branchBusinessApi.getAllBranchs();
        for (BranchBusinessDto dto : allBranchs) {
            //过滤全国
            if (ALL_COUNTRY.getKey().equals(dto.getBranchCode())) {
                continue;
            }
            if (SHUIXING_COUNTRY.getKey().equals(dto.getBranchCode())) {
                continue;
            }
            if (HUOXING_COUNTRY.getKey().equals(dto.getBranchCode())) {
                continue;
            }
            String provinceCode = dto.getBranchCode().replace("XS", "").substring(0, 2);
            branchs.add(provinceCode);
        }
        return branchs;
    }
}
