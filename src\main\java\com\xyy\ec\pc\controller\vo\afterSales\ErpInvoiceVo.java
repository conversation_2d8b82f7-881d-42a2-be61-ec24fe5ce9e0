package com.xyy.ec.pc.controller.vo.afterSales;

import lombok.Data;

import java.io.Serializable;

/**
* <AUTHOR>
* @date  2020/11/24 20:37
* @table
*/
@Data
public class ErpInvoiceVo implements Serializable {
    private static final long serialVersionUID = -1056666179575003383L;
    /**
     * 发票类型 1、电子普通发票；2、增值税纸质专用发票； 3、纸质普通发票；4、增值税电子专用发票；
     */
    private Integer invoiceType;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 纳税人识别号
     */
    private String enterpriseRegistrationNo;
    /**
     * 开户行
     */
    private String bankName;
    /**
     * 开户账号
     */
    private String acct;
    /**
     * 营业执照注册地址
     */
    private String registerAddress;
}
