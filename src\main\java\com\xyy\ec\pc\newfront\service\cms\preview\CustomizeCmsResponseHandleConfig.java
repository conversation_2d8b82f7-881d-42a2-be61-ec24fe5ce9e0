package com.xyy.ec.pc.newfront.service.cms.preview;

import cn.hutool.core.map.MapUtil;
import com.xyy.ec.pc.newfront.service.HeaderNewService;
import com.xyy.ec.pc.newfront.service.VoucherNewService;
import com.xyy.ec.pc.newfront.service.cms.service.CustomizeCmsResponseService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.IndexService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static com.xyy.ec.pc.enums.ProvinceBranchEnum.HU_BEI;

@Slf4j
@SuppressWarnings("unchecked")
@RequiredArgsConstructor
@Service
public class CustomizeCmsResponseHandleConfig {

    private Map<String, Function<Object[], AjaxResult<Object>>> functionMap;

    private final CustomizeCmsResponseService cmsResponseService;
    private final IndexService indexService;
    private final HeaderNewService headerNewService;

    @PostConstruct
    public void init() {
        Map<String, Function<Object[], AjaxResult<Object>>> map = MapUtil.newHashMap();
        map.put("com.xyy.ec.pc.newfront.controller.CustomerGroupController.filterHitCustomerGroupIds", args -> AjaxResult.successResult(args[0]));
        map.put("com.xyy.ec.pc.newfront.controller.ProductGroupsController.listExpectProducts", args -> AjaxResult.successResult(cmsResponseService.listExpectProducts(args)));
        map.put("com.xyy.ec.pc.newfront.controller.ProductGroupsController.listProducts", args -> AjaxResult.successResult(cmsResponseService.listProducts(args)));
        map.put("com.xyy.ec.pc.newfront.controller.IndexNewController.categoryTree", args -> AjaxResult.successResult(indexService.getCategoryTree(HU_BEI.getBranchCode())));
        map.put("com.xyy.ec.pc.newfront.controller.HeaderNewController.getHeaderData", args -> AjaxResult.successResult(headerNewService.getHotWords()));
        map.put("com.xyy.ec.pc.newfront.controller.ShopQueryController.queryShopList", args -> AjaxResult.successResult(cmsResponseService.queryShopList(args)));
        map.put("com.xyy.ec.pc.newfront.controller.MerchantCenterNewController.selectUser", args -> AjaxResult.successResult(cmsResponseService.selectUser()));
        map.put("com.xyy.ec.pc.newfront.controller.VoucherNewController.getCouponBase", args -> AjaxResult.successResult(cmsResponseService.previewCouponsByTemplateIds(args)));
        functionMap = MapUtil.unmodifiable(map);
    }

    public AjaxResult<Object> apply(ProceedingJoinPoint joinPoint) {
        try {
            if (MapUtil.isEmpty(functionMap)) {
                throw new RuntimeException("未初始化完成:CustomizeCmsResponseService");
            }
            Signature signature = joinPoint.getSignature();
            String key = signature.getDeclaringTypeName() + "." + signature.getName();
            Object[] args = joinPoint.getArgs();
            if (functionMap.containsKey(key)) {
                return functionMap.get(key).apply(args);
            } else {
                return AjaxResult.successResultNotData();
            }
        } catch (Exception e) {
            log.info("CMS自定义response异常", e);
            return AjaxResult.successResultNotData();
        }
    }
}
