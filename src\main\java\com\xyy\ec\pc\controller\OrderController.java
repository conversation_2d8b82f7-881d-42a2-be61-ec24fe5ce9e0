package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.message.Transaction;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import com.xyy.cat.util.CatUtil;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.exception.XyyEcOrderBizNoneCheckRTException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.cs.api.order.CsOrderRefundApi;
import com.xyy.ec.merchant.bussiness.api.FindClientBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBalanceJournalBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.ShippingAddressBussinessApi;
import com.xyy.ec.merchant.bussiness.api.crm.InvoiceBussinessCrmApi;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.merchant.bussiness.dto.InvoiceTypeBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.licence.MerchantForLicense;
import com.xyy.ec.merchant.server.dto.AccountInfoDto;
import com.xyy.ec.merchant.server.dto.MerchatForAppDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.merchant.bussiness.enums.InvoiceTypeEnum;
import com.xyy.ec.order.api.pay.PingAnAccountApi;
import com.xyy.ec.order.business.api.*;
import com.xyy.ec.order.business.api.ecp.order.EcpOrderBusinessApi;
import com.xyy.ec.order.business.common.ResultDTO;
import com.xyy.ec.order.business.config.DownLoadEnum;
import com.xyy.ec.order.business.config.OperationEnum;
import com.xyy.ec.order.business.config.OrderEnum;
import com.xyy.ec.order.business.config.http.ResponseOrderInfo;
import com.xyy.ec.order.business.dto.*;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesListVo;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesQueryParam;
import com.xyy.ec.order.business.dto.afterSales.ReissueCredentialDto;
import com.xyy.ec.order.business.dto.invoice.OrderInvoiceDto;
import com.xyy.ec.order.business.dto.invoice.OrderInvoiceExtDto;
import com.xyy.ec.order.business.dto.shop.OrderStockStateDto;
import com.xyy.ec.order.business.dto.shop.RePurchaseDto;
import com.xyy.ec.order.business.dto.ykq.EcpOrderPayDto;
import com.xyy.ec.order.business.enums.SendEmailInfoTypeEnum;
import com.xyy.ec.order.business.enums.pay.PayChannelEnum;
import com.xyy.ec.order.business.exception.ServiceException;
import com.xyy.ec.order.business.model.ActivityPackageModel;
import com.xyy.ec.order.business.model.OrderPayRequest;
import com.xyy.ec.order.business.model.PayEvidenceVo;
import com.xyy.ec.order.business.model.ServiceResponse;
import com.xyy.ec.order.business.utils.BigDecimalUtils;
import com.xyy.ec.order.business.utils.OrderConvertUtil;
import com.xyy.ec.order.core.dto.Order;
import com.xyy.ec.order.core.dto.cart.CodeItemDto;
import com.xyy.ec.order.core.enums.OrderAdjustTypeEnum;
import com.xyy.ec.order.core.util.EntityConvert;
import com.xyy.ec.order.dto.cart.ShopInfoSxp;
import com.xyy.ec.order.dto.pay.CashierDto;
import com.xyy.ec.order.dto.pay.CashierQueryParamDto;
import com.xyy.ec.order.dto.pay.TradeCertificateResultDto;
import com.xyy.ec.order.dto.settle.SettleSkuDto;
import com.xyy.ec.order.dto.settle.SettleVO;
import com.xyy.ec.order.enums.*;
import com.xyy.ec.order.search.api.remote.dto.MyPcPurchaseQueryOrderDto;
import com.xyy.ec.order.search.api.remote.dto.MyPcPurchaseQueryRefundDto;
import com.xyy.ec.order.search.api.remote.dto.MyPcPurchaseQueryRefundingDto;
import com.xyy.ec.order.search.api.remote.dto.OrderFeeStatisticsQueryDto;
import com.xyy.ec.order.search.api.remote.enums.PopOrderStatusEnum;
import com.xyy.ec.order.search.api.remote.result.*;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.base.Sort;
import com.xyy.ec.pc.common.enums.AppEventTrackingSpTypeEnum;
import com.xyy.ec.pc.common.params.AppEventTrackingSpIdGenerateParam;
import com.xyy.ec.pc.common.utils.AppEventTrackingUtils;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.constants.CodeMapConstants;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.constants.RedisConstants;
import com.xyy.ec.pc.enums.OrderDetailEnum;
import com.xyy.ec.pc.enums.SxpTypeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.model.erp.ErpSignaturesResponse;
import com.xyy.ec.pc.model.erp.SignaturesInfo;
import com.xyy.ec.pc.model.invoice.InvoiceDownloadInfoVo;
import com.xyy.ec.pc.model.order.OrderSettleVo;
import com.xyy.ec.pc.model.req.ConsumeRebateDetailReq;
import com.xyy.ec.pc.model.search.BuySomethingCasuallyInfoParam;
import com.xyy.ec.pc.model.sxp.SettleSxpSkuVo;
import com.xyy.ec.pc.model.sxp.SuiXinPinTopVo;
import com.xyy.ec.pc.recommend.helpers.RecommendQuickTrackingDataHelper;
import com.xyy.ec.pc.remote.AccountProviderService;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.search.ecp.service.EcpPcSearchService;
import com.xyy.ec.pc.search.ecp.vo.PcSearchProductSSMVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchProductVO;
import com.xyy.ec.pc.search.service.SearchEngineService;
import com.xyy.ec.pc.service.*;
import com.xyy.ec.pc.service.impl.ReportInformationServiceImpl;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.order.OrderPurchaseStatisticV2Service;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.pc.util.excel.UtilOrganizeExcelData;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ec.pc.vo.order.ConfirmOrderVo;
import com.xyy.ec.pop.server.api.fdd.api.PlatformServiceAgreementApi;
import com.xyy.ec.pop.server.api.fdd.dto.TbXyyPopFddEnterpriseEmpowerDTO;
import com.xyy.ec.pop.server.api.order.api.PopOrderApi;
import com.xyy.ec.pop.server.api.order.dto.PopOrderConsignmentDetailDto;
import com.xyy.ec.pop.server.api.product.api.PopDrugReportApi;
import com.xyy.ec.pop.server.api.product.api.PopFirstSaleQualificationApi;
import com.xyy.ec.pop.server.api.product.dto.PopDrugDownDto;
import com.xyy.ec.pop.server.api.product.dto.PopFileResultDto;
import com.xyy.ec.product.business.api.ecp.skucategory.EcpCategoryRelationBusinessApi;
import com.xyy.ec.product.business.api.reportInformation.ReportInformationApi;
import com.xyy.ec.product.business.dto.SkuCategoryRelationBusinessDTO;
import com.xyy.ec.product.business.dto.reportInformation.ReportInformation;
import com.xyy.ec.product.business.dto.reportInformation.ReportInformationCondition;
import com.xyy.ec.system.business.api.RedisComponentApi;
import com.xyy.ec.system.business.dto.CodeitemBusinessDto;
import com.xyy.recommend.ecp.api.EcpRecommendOrderApi;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSceneEnum;
import com.xyy.recommend.ecp.params.EcpRecommendOrderQueryParam;
import com.xyy.recommend.ecp.params.EcpRecommendOrderShopDTO;
import com.xyy.recommend.ecp.result.EcpRecommendOrderResult;
import com.xyy.third.sn.api.ShenNongOrderApi;
import com.xyy.third.sn.dto.OrderTmpDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;

/**
 * @Author: zhaoyun
 * @Date: 2018/8/27 21:19
 * @Description: 订单控制器
 */
@Controller
@RequestMapping("/merchant/center/order")
public class OrderController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(OrderController.class);
    @Value("${sxpShopSwitch:true}")
    private Boolean sxpShopSwitch;
    @Value("${order.export.switch:false}")
    private Boolean orderExportSwitch;
    @Value("${credentialType}")
    private String credentialType;
    @Value("${orderList.query.limit.month:3}")
    public Integer orderListQueryListMonth;
    /***神农的域名*/
    @Value("${fastdfs.hostname}")
    public String host;
    /***神农的域名*/
    @Value("${wms.report.host}")
    public String reportHost;

    @Value("${noneShowJdCreditCertificateTime:2025-06-19 00:00:00}")
    public String noneShowJdCreditCertificateTime;

    @Value("${test_tip_accountid:**********}")
    public Long test_tip_accountid;

    @Value("${reportVersison:true}")
    public boolean reportVersison;

    @Value("${file_upload_isDelete:true}")
    public boolean isDelete;

    @Autowired
    private OrderPurchaseStatisticV2Service orderPurchaseStatisticV2Service;

    @Value("${pcStatisticsForMysqlSwitch:true}")
    private Boolean pcStatisticsForMysqlSwitch;

    @Value("${group.pre.settle.company.confirm.flag:1}")
    public Integer groupPreSettleCompanyConfirmFlag;
    @Value("${group.pre.settle.company.confirm.Msg:商品选择失败，请重新设置商品数量}")
    public String groupPreSettleCompanyConfirmMsg;
    @Reference(version = "1.0.0")
    private MerchantLicenseApi licenseApi;
    @Autowired
    private AppProperties appProperties;

    @Resource
    private ReportInformationServiceImpl reportInformationService;

    /**
     * linux下载文件的根目录
     **/
    public String reportFilePatch = "/tmp/reportDownload/";

    public static String invoiceFilePatch = "/tmp/invoiceFile/";

    @Reference(version = "1.0.0")
    private PingAnAccountApi pingAnAccountApi;

    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;
    @Reference(version = "1.0.0")
    EcpCategoryRelationBusinessApi categoryRelationBusinessApi;

    @Reference(version = "1.0.0")
    private OrderDetailBusinessApi orderDetailBusinessApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private OrderExtendBusinessApi orderExtendBusinessApi;

    @Reference(version = "1.0.0")
    private MyOrderDetailForMerchantApi myOrderDetailForMerchantApi;

    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;

    @Autowired
    private OrderPurchaseStatisticService orderPurchaseStatisticService;

    @Reference(version = "1.0.0")
    private InvoiceBusinessApi invoiceBusinessApi;

    @Reference(version = "1.0.0")
    private ShippingAddressBussinessApi shippingAddressBussinessApi;

    @Reference(version = "1.0.0")
    private FindClientBussinessApi findClientBussinessApi;

    @Reference(version = "1.0.0")
    private MerchantBalanceJournalBussinessApi merchantBalanceJournalBussinessApi;

    @Reference(version = "1.0.0")
    private CouponBusinessApi couponBusinessApi;

    @Reference(version = "1.0.0")
    private OrderRefundBusinessApi orderRefundBusinessApi;
    @Reference(version = "1.0.0")
    private OrderAdjustBusinessApi orderAdjustBusinessApi;
    @Reference(version = "1.0.0")
    private ShoppingCartBusinessApi shoppingCartBusinessApi;
    @Reference(version = "1.0.0")
    private ShenNongOrderApi shenNongOrderApi;

    @Reference(version = "1.0.0")
    private RedisComponentApi redisComponentApi;

    @Reference(version = "1.0.0")
    private PopDrugReportApi popDrugReportApi;

    @Reference(version = "1.0.0")
    private PopFirstSaleQualificationApi popFirstSaleQualificationApi;
    @Reference(version = "1.0.0")
    private PopOrderApi popOrderApi;
    @Reference(version = "1.0.0")
    private PlatformServiceAgreementApi platformServiceAgreementApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private OrderService orderService;

    @Resource
    private AfterSalesService afterSalesService;

    @Autowired
    private CodeItemService codeItemService;

    @Reference(version = "1.0.0", timeout = 60000)
    private ReportInformationApi reportInformationApi;

    @Autowired
    private HttpServletRequest request;

    @Reference(version = "1.0.0")
    private InvoiceBussinessCrmApi invoiceBussinessCrmApi;

    @Reference(version = "1.0.0")
    private OrderAuthenticationBusinessApi authenticationBusinessApi;

    @Reference(version = "1.0.0")
    private EcpOrderBusinessApi ecpOrderBusinessApi;


    @Reference(version = "1.0.0")
    private RedisBusinessApi redisBusinessApi;

    @Value("${erp_host}")
    private String ERP_HOST;
    @Value("${cosFlag:true}")
    private boolean cosFlag;

    @Reference(version = "1.0.0")
    private OrderDownloadLogBusinessApi orderDownloadLogBusinessApi;

    @Reference(version = "1.0.0")
    private OrderOperationLogBusinessApi orderOperationLogBusinessApi;
    @Reference(version = "1.0.0")
    private CsOrderRefundApi csOrderRefundApi;
    @Autowired
    private AccountProviderService accountProviderService;
            @Autowired
            private EcpPcSearchService ecpPcSearchService;
    @Value("#{'${shop.control.orgId}'.split(',')}")
    private List<String> controlSaleOrgIds;
    @Value("${shop.heye.orgId:**********}")
    private String heYeOrgId;
    @Value("${gwj.agreementInfo:查询《平台代收款说明》,https://oss-ec-test.ybm100.com/order/contract/代收款说明-平安.pdf}")
    private String agreementInfo;
    @Reference(version = "1.0.0")
    private EcpRecommendOrderApi ecpRecommendOrderApi;

    @Autowired
    private SearchEngineService searchEngineService;
    @Autowired
    private MarketingService marketingService;
    @RequestMapping("/queryOrderCredentials")
    @ResponseBody
    public Object queryOrderCredential(String orderNo) {
        logger.info("queryOrderCredential:{}", orderNo);
        if (StringUtils.isBlank(orderNo)) {
            return this.addError("订单号不允许为空！");
        }
        try {
            logger.info("queryOrderCredential request orderNo:{}", orderNo);
            ApiRPCResult<ReissueCredentialDto> apiRPCResult = orderBusinessApi.queryOrderCredentialV2(orderNo);
            logger.info("queryOrderCredential response orderNo:{} apiRPCResult:{}", orderNo, JSON.toJSONString(apiRPCResult));
            if (apiRPCResult == null || apiRPCResult.isFail()) {
                return this.addResult("data", null);
            }
            return this.addResult("data", apiRPCResult.getData());
        } catch (Exception e) {
            logger.error("queryOrderCredential:{}", orderNo);
            return this.addError(e.getMessage());
        }
    }
    @RequestMapping("/queryid")
    @ResponseBody
    public Object queryid(String orderNo) {
        logger.info("queryid:{}", orderNo);
        if (StringUtils.isBlank(orderNo)) {
            return this.addError("订单号不允许为空！");
        }
        try {
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(orderNo);
            return this.addResult("data", orderBusinessDto.getId());
        } catch (Exception e) {
            logger.error("queryid:{}", orderNo);
            return this.addError(e.getMessage());
        }
    }

    /**
     * 查询订单详情
     *
     * @param orderNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryOrderDetailList")
    public Object queryOrderDetailList(@RequestParam(name = "orderNo") String orderNo) {
        try {
            final List<OrderDetailBusinessDto> orderDetailBusinessDtos = orderBusinessApi.queryOrderDetailList(orderNo);
            return this.addResult("data", orderDetailBusinessDtos);
        } catch (Exception e) {
            logger.error("queryOrderDetailList，orderNo：{}", orderNo, e);
            return addError("服务请求超时，请稍后重试");
        }
    }


    /**
     *顺手买一件查询接口
     *
     * @param buySomethingCasuallyInfo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/buySomethingCasuallyQuery")
    public Object buySomethingCasuallyQuery(@RequestParam(name = "buySomethingCasuallyInfo") String buySomethingCasuallyInfo) {
        if(StringUtils.isEmpty(buySomethingCasuallyInfo)) {
            return this.addError("入参：buySomethingCasuallyInfo 是空");
        }
        logger.info("9528-ssm-buySomethingCasuallyInfo:{}",JSON.toJSONString(buySomethingCasuallyInfo));

        BuySomethingCasuallyInfoParam buySomethingCasuallyInfoParam=JSON.parseObject(buySomethingCasuallyInfo, BuySomethingCasuallyInfoParam.class);
        List<ShopInfoSxp> shopInfoSxpList=buySomethingCasuallyInfoParam.getShopInfoSxpList();
        shopInfoSxpList=CollectionUtils.isNotEmpty(shopInfoSxpList) ? shopInfoSxpList.stream().filter(x->CollectionUtils.isNotEmpty(x.getSkuids())).collect(Collectors.toList()):shopInfoSxpList;

        List<Long> buySomethingCasuallySkus=buySomethingCasuallyInfoParam.getBuySomethingCasuallySkus();
        Boolean isMore=buySomethingCasuallyInfoParam.getIsMore()== null? Boolean.TRUE: buySomethingCasuallyInfoParam.getIsMore();
        List<EcpRecommendOrderShopDTO> shops=new ArrayList<>();
        if(CollectionUtil.isNotEmpty(shopInfoSxpList)) {
            shopInfoSxpList.forEach(item->{
                shops.add(EcpRecommendOrderShopDTO.builder().shopCode(item.getShopCode())
                        .csuIds(item.getSkuids()).build());
            });
        }

        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (sxpShopSwitch) {
                if (merchant.getIsKa()) {
                    logger.info("buySomethingCasuallyQuery merchantid:{}",merchant.getId());
//                    isCloseShopCodeFilter = false;//单店铺;true-多店铺
                    return this.addResult("data", Lists.newArrayList());
                }
            }

            EcpRecommendOrderQueryParam queryParam=EcpRecommendOrderQueryParam.builder().merchantId(merchant.getId())
                    .terminalType(4)
                    .accountId(merchant.getAccountId())
                    .shops(shops)
                    .recommendScene(EcpRecommendSceneEnum.ORDER_TO_BE_CONFIRMED_PAGE)
                    .isCloseShopCodeFilter(Boolean.TRUE)
                    .build();
            ApiRPCResult<EcpRecommendOrderResult> res= ecpRecommendOrderApi.recommend(queryParam);
            if(res!= null && res.isSuccess() && res.getData() !=null) {
                List<Long> skuIds= res.getData().getCsuIds();
                if(CollectionUtils.isNotEmpty(skuIds) &&  CollectionUtils.isNotEmpty(buySomethingCasuallySkus)) {
                    skuIds.removeAll(buySomethingCasuallySkus);
                }

                if(CollectionUtils.isNotEmpty(skuIds)) {
                    //不是更多只获取前三个
                    skuIds= isMore ? skuIds: skuIds.subList(0, Math.min(8, skuIds.size()));
                    List<PcSearchProductVO>  result=   ecpPcSearchService.getProductInfoForSxp(skuIds, merchant.getMerchantId());
                    fillMddataInfo(result,res.getData(), merchant.getMerchantId(), 4);
                    List<PcSearchProductSSMVO> resSSM = RecommendQuickTrackingDataHelper.handleQuickTrackingDataForSSM(result, skuIds, res.getData().getRecommendStrategyCode());
                    return this.addResult("data", resSSM);

                }
            }
            return this.addResult();
        }catch (IllegalArgumentException e) {
            logger.error("购物车生成订单异常", e);
            String message = e.getMessage();
            if (StringUtils.isNotEmpty(message) && message.startsWith("您采购的部分商品因为库存不足")) {
                return this.addError(3, "您采购的部分商品因为库存不足、下架或超出经营范围，系统已经为您重置！");
            }
            return this.addError(1, e.getMessage());
        } catch (Exception e) {
            logger.error("购物车生成订单异常", e);
            return this.addError(1, "购物车生成订单异常");
        }

    }

    private void fillMddataInfo(List<PcSearchProductVO>  voList,EcpRecommendOrderResult ecpRecommendOrderResult,Long merchantId,Integer terminalType) {
        if(CollectionUtils.isNotEmpty(voList)) {
            String  spid= AppEventTrackingUtils.generateSpId(AppEventTrackingSpIdGenerateParam.builder().spTypeEnum(AppEventTrackingSpTypeEnum.SXP_SSM).merchantId(merchantId).build());
            String  nsid = SearchUtils.generateNewSidData(merchantId, terminalType);

            JsonObject mddataObject = new JsonObject();
            mddataObject.addProperty("sptype",spid);
            mddataObject.addProperty("sid",nsid);
            mddataObject.addProperty("search_sort_strategy_id",ecpRecommendOrderResult.getRecommendStrategyCode());
            //对于顺手买的埋点产品要求写死 5
            mddataObject.addProperty("direct",5);

            AtomicReference<Integer> count= new AtomicReference<>(1);
            voList.stream().forEach(item-> {
                mddataObject.addProperty("rank", count.getAndSet(count.get() + 1));
                item.setMddata(mddataObject.toString());
                if(CollectionUtils.isNotEmpty(item.getTagList())) {
                    item.setTagList(item.getTagList().stream().filter(x->"整单包邮".equals(x.getName())).collect(Collectors.toList()));
                }
            });
        }
    }

    /**
     * APP去结算，逻辑同settleNew
     *
     * @return
     */
    @RequestMapping(value = "/gotoSettle.json")
    @ResponseBody
    public Object gotoSettle(String notSubmitOrderOrgIds) {
        // 1. 拼接请求参数
        OrderSettleVo order = new OrderSettleVo();
        order.setOrderSource(4);
        order.setAppVersion(1);
        order.setRealIP(IPUtils.getClientIP(request));
        order.setNotSubmitOrderOrgIds(notSubmitOrderOrgIds);
        logger.info("当前用户请求的IP地址为:" + order.getRealIP());
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            order.setBranchCode(merchant.getRegisterCode());
            order.setAccountRole(merchant.getAccountRole());
            order.setAccountId(merchant.getAccountId());
            if (merchant.getAccountRole().equals(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId())) {
                order.setUseBalance(false);
                order.setUseRedPacket(false);
                order.setUseVirtualGold(false);
                order.setVoucherIds("");
            }
            // 2. 调用预结算方
            ApiRPCResult<SettleVO> resultDTO = orderService.preSettle(order);

            if(OrderApiResultCodeEnum.MERCHANT_REMOVAL.getCode() == resultDTO.getCode()){
                return addError(OrderApiResultCodeEnum.MERCHANT_REMOVAL.getCode(), resultDTO.getErrMsg());
            }

            if (resultDTO.isFail()) {
                return addError(0, resultDTO.getErrMsg());
            }
            SettleVO data = resultDTO.getData();
            Map<String, Object> result = this.addResult();
            if (data != null && data.getClaimVoucherNum() != null && data.getClaimVoucherNum() > 0) {
                result.put("claimVoucherNum", data.getClaimVoucherNum());
            }

            if (Objects.nonNull(data) && StringUtils.isNotEmpty(data.getLicenseError())) {
                result.put("dialogMsg", data.getLicenseError());
            }
            return result;

        } catch (IllegalArgumentException e) {
            logger.error("购物车生成订单异常", e);
            String message = e.getMessage();
            if (StringUtils.isNotEmpty(message) && message.startsWith("您采购的部分商品因为库存不足")) {
                return this.addError(3, "您采购的部分商品因为库存不足、下架或超出经营范围，系统已经为您重置！");
            }
            return this.addError(1, e.getMessage());
        } catch (Exception e) {
            logger.error("购物车生成订单异常", e);
            return this.addError(1, "购物车生成订单异常");
        }
    }

    /**
     * 拼团预结算
     *
     * @return
     */
    @RequestMapping(value = "/group/preSettle.json")
    @ResponseBody
    public Object groupPreSettle(OrderSettleVo order) {
        // 1. 拼接请求参数
        order.setOrderSource(4);
        order.setAppVersion(1);
        order.setRealIP(IPUtils.getClientIP(request));
        order.setBizSource(Objects.isNull(order.getIsPiGou()) || Objects.equals(order.getIsPiGou(), 0) ? BizSourceEnum.GROUP_PURCHASE.getKey() : BizSourceEnum.PIGOU.getKey());
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            logger.info("groupSettle MerchantId:{}&skuId:{}&bizSource:{}", merchant.getId(), order.getSkuId());
            merchantId = merchant.getId();
            order.setMerchantId(merchant.getId());
            order.setBranchCode(merchant.getRegisterCode());
            order.setAccountRole(merchant.getAccountRole());
            order.setAccountId(merchant.getAccountId());
            if (merchant.getAccountRole().equals(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId())) {
                order.setUseBalance(false);
                order.setUseRedPacket(false);
                order.setUseVirtualGold(false);
                order.setVoucherIds("");
            }
            // 2. 调用预结算方
            ApiRPCResult<SettleVO> resultDTO = orderService.preSettle(order);

            if (resultDTO.isFail()) {
                return addError(0, resultDTO.getErrMsg());
            }
            SettleVO data = resultDTO.getData();
            if (Objects.equals(groupPreSettleCompanyConfirmFlag, 1) && null != data && CollectionUtils.isEmpty(data.getCompanys())) {
                //商品分组数据为空 则认为有问题
                return addError(0, groupPreSettleCompanyConfirmMsg);
            }
            Map<String, Object> result = this.addResult();
            if (data != null && data.getClaimVoucherNum() != null && data.getClaimVoucherNum() > 0) {
                result.put("claimVoucherNum", data.getClaimVoucherNum());
            }
            if (Objects.nonNull(data) && StringUtils.isNotEmpty(data.getLicenseError())) {
                result.put("dialogMsg", data.getLicenseError());
            }
            return result;

        } catch (IllegalArgumentException e) {
            logger.error("拼团预结算异常: merchantId:{} skuId:{}&productQuantity:{}", merchantId, order.getSkuId(), order.getProductNum(), e);
            String message = e.getMessage();
            if (StringUtils.isNotEmpty(message) && message.startsWith("您采购的部分商品因为库存不足")) {
                return this.addError(3, "您采购的部分商品因为库存不足、下架或超出经营范围，系统已经为您重置！");
            }
            return this.addError(1, e.getMessage());
        } catch (Exception e) {
            logger.error("拼团预结算异常: merchantId:{} skuId:{}&productQuantity:{}", merchantId, order.getSkuId(), order.getProductNum(), e);
            return this.addError(1, "拼团预结算异常");
        }
    }

    /**
     * 购物车生成订单
     *
     * @param modelMap
     * @param voucherMonitor 优惠券提醒弹出框，用户点击查看的时候传入 1，其他情况下不用传
     *                       逻辑处理：传入1，表示该次刷新结算数据是从弹出框点击（则不清除优惠券弹出标记），而不是正常的购物车到结算（清除优惠券弹出标记），
     * @return String
     * @Title: pay
     */
    @RequestMapping("/settle.htm")
    public ModelAndView settle(ModelMap modelMap, RedirectAttributes attr, boolean storeStatus, String bizProducts,  Integer payType,  String suiXinPinSkus,String voucherMonitor, String notSubmitOrderOrgIds,
                               @RequestParam(required = false) Integer claimVoucherNum, @RequestParam(required = false) Integer bizScene) {
        // 1. 拼接请求参数
        OrderSettleVo order = new OrderSettleVo();
        order.setOrderSource(4);
        order.setAppVersion(1);
        order.setUseBalance(false);
        order.setVoucherMonitor(voucherMonitor);
        order.setPayType(payType);
        order.setBizScene(bizScene);
        order.setNotSubmitOrderOrgIds(notSubmitOrderOrgIds);
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            order.setBranchCode(merchant.getRegisterCode());
            order.setAccountRole(merchant.getAccountRole());
            order.setAccountId(merchant.getAccountId());
            if (merchant.getAccountRole().equals(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId())) {
                order.setUseBalance(false);
                order.setUseRedPacket(false);
                order.setUseVirtualGold(false);
                order.setVoucherIds("");
            }
            List<SettleSkuDto> lists=null;
            if(org.apache.commons.lang.StringUtils.isNotEmpty(bizProducts)) {
                lists = JSON.parseArray(bizProducts, SettleSkuDto.class);
            }



            order.setSuiXinPinSkus(suiXinPinSkus);
            // 2. 调用预结算方法
            ResultDTO<SettleVO> resultDTO = orderService.settleForRefactor(order,lists);
            if (!resultDTO.getIsSuccess()) {
                attr.addFlashAttribute("errorMsg", resultDTO.getErrorMsg());
                return new ModelAndView(new RedirectView("/merchant/center/cart/index.htm", true, false));
            }
            //pc对于null值处理不正确
            SettleVO data = resultDTO.getData();
            if (data.getIsGrayAccountPeriodPay() == null) {
                data.setIsGrayAccountPeriodPay(false);
            }
            logger.info("预约店铺:orderSettle IsGrayAccountPeriodPay: {}",data.getIsGrayAccountPeriodPay());

            // 3. 填充结算页数据
            orderService.setSettleDataForRefactor(modelMap, resultDTO.getData(), order.getBranchCode(), merchant);
            logger.info("预约店铺:orderSettle: {}",JSON.toJSONString(modelMap.get("orderSettle")));
            if (claimVoucherNum != null && claimVoucherNum > 0) {
                modelMap.put("claimVoucherMsg", String.format(Constants.CLAIM_VOUCHER_DIALOG, claimVoucherNum));
            }
            modelMap.put("notSubmitOrderOrgIds", notSubmitOrderOrgIds);
            modelMap.put("credentialTypeList", JSONObject.parseArray(credentialType));
            modelMap.put("accountRole", merchant.getAccountRole());

        } catch (IllegalArgumentException e) {
            logger.error("购物车生成订单异常", e);
            String message = e.getMessage();
            if (StringUtils.isNotEmpty(message) && message.startsWith("您采购的部分商品因为库存不足")) {
                attr.addFlashAttribute("errorMsg", "您采购的部分商品因为库存不足、下架或超出经营范围，系统已经为您重置！");
            } else {
                attr.addFlashAttribute("errorMsg", e.getMessage());
            }
            return new ModelAndView(new RedirectView("/merchant/center/cart/index.htm", true, false));
        } catch (Exception e) {
            logger.error("购物车生成订单异常", e);
            attr.addFlashAttribute("errorMsg", e.getMessage());
            return new ModelAndView(new RedirectView("/merchant/center/cart/index.htm", true, false));
        }
        return new ModelAndView("/order/settle.ftl");
    }

    @RequestMapping("/proxyDownload")
    @ResponseBody
    public ResponseEntity<byte[]> proxyDownload(@RequestParam String targetUrl) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet httpGet = new HttpGet(targetUrl);
        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    byte[] fileContent = EntityUtils.toByteArray(entity);
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                    headers.setContentLength(fileContent.length);
                    return new ResponseEntity<>(fileContent, headers, HttpStatus.OK);
                }
            }
            return new ResponseEntity<>(HttpStatus.valueOf(statusCode));
        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 拼团结算
     *
     * @return String
     * @Title: pay
     */
    @RequestMapping("/group/settle.htm")
    public ModelAndView groupSettle(ModelMap modelMap, RedirectAttributes attr, boolean storeStatus,Boolean isSupportOldSxp, Integer payType,  String shopCodes,String suiXinPinSkus,  Integer bizSource, Integer productNum, Long skuId, String voucherMonitor, @RequestParam(required = false) Integer claimVoucherNum, HttpServletRequest request) {
        // 1. 拼接请求参数
        OrderSettleVo order = new OrderSettleVo();
        order.setOrderSource(4);
        order.setAppVersion(1);
        order.setUseBalance(false);
        order.setUseRedPacket(true);
        order.setVoucherMonitor(voucherMonitor);
        order.setPayType(payType);
        //兼容历史，历史拼团不传这个值
        if (bizSource == null) {
            bizSource = BizSourceEnum.GROUP_PURCHASE.getKey();
        }

        order.setShopCode(shopCodes);
        order.setBizSource(bizSource);
        order.setProductNum(productNum);
        order.setSkuId(skuId);
        order.setSuiXinPinSkus(suiXinPinSkus);
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            logger.info("groupSettle MerchantId:{}&skuId:{}&bizSource:{}", merchant.getId(), skuId, bizSource);
            merchantId = merchant.getId();
            order.setMerchantId(merchant.getId());
            order.setBranchCode(merchant.getRegisterCode());
            order.setAccountId(merchant.getAccountId());
            order.setAccountRole(merchant.getAccountRole());
            if (merchant.getAccountRole().equals(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId())) {
                order.setUseBalance(false);
                order.setUseRedPacket(false);
                order.setUseVirtualGold(false);
                order.setVoucherIds("");
            }
            if (BizSourceEnum.GROUP_PURCHASE.getKey().equals(order.getBizSource()) || BizSourceEnum.PIGOU.getKey().equals(order.getBizSource())) {
                List<SuiXinPinTopVo> suiXinPinTopVoList = null;
                if (StringUtils.isEmpty(suiXinPinSkus) && StringUtils.isNotEmpty(order.getShopCode())) {
                   logger.info("groupSettle sxp merchantId:{} isKa:{}",merchant.getId(),merchant.getIsKa());
                    if (sxpShopSwitch && merchant.getIsKa()) {
//                        isSupportOldSxp = true;
                    } else {
                        suiXinPinTopVoList = searchEngineService.getAddPurchaseSuiXinPinList(order, 4, isSupportOldSxp);
                    }
                    if (com.xyy.ec.product.business.ecp.util.CollectionUtil.isNotEmpty(suiXinPinTopVoList)) {
                        Boolean finalIsSupportOldSxp = isSupportOldSxp;
                        List<SettleSxpSkuVo> settleSxpSkuVos = suiXinPinTopVoList.stream().map(suiXinPinTopVo -> {
                            SettleSxpSkuVo settleSxpSkuVo = new SettleSxpSkuVo();
                            settleSxpSkuVo.setSkuId(suiXinPinTopVo.getSkuId());
                            settleSxpSkuVo.setSkuStartNum(suiXinPinTopVo.getSkuStartNum());
                            settleSxpSkuVo.setShowName(suiXinPinTopVo.getShowName());
                            settleSxpSkuVo.setTag(suiXinPinTopVo.getTag());
                            settleSxpSkuVo.setQuantity(0);
                            settleSxpSkuVo.setQtData(suiXinPinTopVo.getQtData());
                            settleSxpSkuVo.setPromoTag(suiXinPinTopVo.getPromoTag());
                            settleSxpSkuVo.setType(BooleanUtils.isTrue(finalIsSupportOldSxp) ?
                                    SxpTypeEnum.NORMAL_SXP.getType() : SxpTypeEnum.SSM.getType() );
                            return settleSxpSkuVo;
                        }).collect(Collectors.toList());

                        order.setSuiXinPinSkus(JSONObject.toJSONString(settleSxpSkuVos));
                    }
                }}
            // 2. 调用预结算方法
            ResultDTO<SettleVO> resultDTO = orderService.settleForRefactor(order,null);

            if (!resultDTO.getIsSuccess()) {
                attr.addFlashAttribute("errorMsg", resultDTO.getErrorMsg());
                return new ModelAndView(new RedirectView("/search/skuDetail/" + skuId + ".htm", true, false));
            }

            //pc对于null值处理不正确
            SettleVO data = resultDTO.getData();
            if (data.getIsGrayAccountPeriodPay() == null) {
                data.setIsGrayAccountPeriodPay(false);
            }

            // 3. 填充结算页数据
            orderService.setSettleDataForRefactor(modelMap, resultDTO.getData(), order.getBranchCode(), merchant);
            if (claimVoucherNum != null && claimVoucherNum > 0) {
                modelMap.put("claimVoucherMsg", String.format(Constants.CLAIM_VOUCHER_DIALOG, claimVoucherNum));
            }
            modelMap.put("groupProductNum", productNum);
            modelMap.put("groupSkuId", skuId);
            modelMap.put("accountRole", merchant.getAccountRole());
            modelMap.put("credentialTypeList", JSONObject.parseArray(credentialType));
        } catch (IllegalArgumentException e) {
            logger.error("拼团去结算异常: merchantId:{} skuId:{}", merchantId, skuId, e);
            String message = e.getMessage();
            if (StringUtils.isNotEmpty(message) && message.startsWith("您采购的部分商品因为库存不足")) {
                attr.addFlashAttribute("errorMsg", "您采购的部分商品因为库存不足、下架或超出经营范围，系统已经为您重置！");
            } else {
                attr.addFlashAttribute("errorMsg", e.getMessage());
            }
            return new ModelAndView(new RedirectView("/search/skuDetail/" + skuId + ".htm", true, false));
        } catch (Exception e) {
            logger.error("拼团去结算异常: merchantId:{} skuId:{}&productQuantity:{}", merchantId, skuId, productNum, e);
            attr.addFlashAttribute("errorMsg", e.getMessage());
            return new ModelAndView(new RedirectView("/search/skuDetail/" + skuId + ".htm", true, false));
        }
        return new ModelAndView("/order/settle.ftl");
    }

    @RequestMapping("/matchPrice/preSettle")
    @ResponseBody
    public Object matchPricePreSettle(@RequestBody OrderSettleVo order, boolean storeStatus, String voucherMonitor, @RequestParam(required = false) Integer claimVoucherNum) {
        logger.info("matchPricePreSettle:{}", JSONObject.toJSONString(order));
        if (CollectionUtils.isEmpty(order.getSkuList())) {
            return this.addError("请选择要采购的商品");
        }
        // 1. 拼接请求参数
        order.setOrderSource(4);
        order.setAppVersion(1);
        order.setUseBalance(false);
        order.setVoucherMonitor(voucherMonitor);
        order.setBizScene(BizSceneEnum.MATCH_PRICE.getKey());
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            order.setBranchCode(merchant.getRegisterCode());
            // 2. 调用预结算方法
            orderService.matchPricePreSettle(order);
            return this.addResult("data", "");

        } catch (Exception e) {
            logger.error("matchPriceSettle:{}", JSONObject.toJSONString(order));
            return this.addError(e.getMessage());
        }

    }

    @RequestMapping("/matchPrice/settle.htm")
    public ModelAndView matchPriceSettle(ModelMap modelMap, RedirectAttributes attr, boolean storeStatus, String voucherMonitor, String notSubmitOrderOrgIds, @RequestParam(required = false) Integer claimVoucherNum) {

        OrderSettleVo order = new OrderSettleVo();
        // 1. 拼接请求参数
        order.setOrderSource(4);
        order.setAppVersion(1);
        order.setUseBalance(false);
        order.setVoucherMonitor(voucherMonitor);
        order.setNotSubmitOrderOrgIds(notSubmitOrderOrgIds);
        order.setBizScene(BizSceneEnum.MATCH_PRICE.getKey());
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            order.setBranchCode(merchant.getRegisterCode());
            merchantId = merchant.getId();
            // 2. 调用预结算方法
            ResultDTO<SettleVO> resultDTO = orderService.matchPriceSettle(order);

            if (!resultDTO.getIsSuccess()) {
                attr.addFlashAttribute("errorMsg", resultDTO.getErrorMsg());
                return new ModelAndView(new RedirectView("/matchPrice/batchPurchaseIndex.htm", true, false));
            }
            // 3. 填充结算页数据
            orderService.setSettleDataForRefactor(modelMap, resultDTO.getData(), merchant.getRegisterCode(), merchant);
            if (claimVoucherNum != null && claimVoucherNum > 0) {
                modelMap.put("claimVoucherMsg", String.format(Constants.CLAIM_VOUCHER_DIALOG, claimVoucherNum));
            }
            modelMap.put("notSubmitOrderOrgIds", resultDTO.getData().getNotSubmitOrderOrgIds());
            modelMap.put("bizScene", 1);
        } catch (IllegalArgumentException e) {
            logger.error("批量匹价去结算异常：merchantId:{}", merchantId, e);
            String message = e.getMessage();
            if (StringUtils.isNotEmpty(message) && message.startsWith("您采购的部分商品因为库存不足")) {
                attr.addFlashAttribute("errorMsg", "您采购的部分商品因为库存不足、下架或超出经营范围，系统已经为您重置！");
            } else {
                attr.addFlashAttribute("errorMsg", e.getMessage());
            }
        } catch (Exception e) {
            logger.error("批量匹价去结算异常:merchantId:{}", merchantId, e);
            attr.addFlashAttribute("errorMsg", e.getMessage());
            return new ModelAndView(new RedirectView("/matchPrice/batchPurchaseIndex.htm", true, false));
        }
        return new ModelAndView("/order/settle.ftl");
    }

    /**
     * 根据订单id 跳转到支付页面
     *
     * @param order
     * @param modelMap
     * @param redirectAttributes
     * @return
     */
    @RequestMapping("/queryConfirmOrder.htm")
    public ModelAndView queryConfirmOrder(OrderBusinessDto order, ModelMap modelMap, RedirectAttributes redirectAttributes, String token, String tranNo,
                                          @RequestParam(value = "rechargeType", required = false) String rechargeType,
                                          @RequestParam(value = "rechargeAmount", required = false) String rechargeAmount) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        try {
            logger.info("rechargeType:{}--rechargeAmount:{}-",rechargeType,rechargeAmount);
            //购物金充值
            if(StringUtils.isNotEmpty(rechargeType) && RechargeEnum.GOLD_RECHARGE.getCode().toString().equals(rechargeType)){
                logger.info("购物金充值-rechargeType:{}--rechargeAmount:{}-",rechargeType,rechargeAmount);
                CashierQueryParamDto param = new CashierQueryParamDto();
                param.setMerchantId(merchant.getMerchantId());
                param.setAccountId(merchant.getAccountId());
                param.setReqScene("cashier");
                param.setOrderPayAmount(rechargeAmount);
//                param.setCardId(cardId);
                param.setTerminalType(PlatformEnum.PC.getKey());
                param.setRechargeType(Integer.valueOf(rechargeType));
                param.setHasPop(false);
                order.setCashPayAmount(new BigDecimal(param.getOrderPayAmount()));
                CashierDto result = orderService.getCashierForRecharge(param);
                modelMap.put("cashier", result);   //支付类型信息
                modelMap.put("rechargeType", RechargeEnum.GOLD_RECHARGE.getCode());  // ORDER_PAY(1, "订单支付"), GOLD_RECHARGE(2, "购物金充值");
                modelMap.put("order", order);
                return new ModelAndView("/order/pay_order.ftl");
            }
            Long id = order.getId();
            if (id == null) {
                modelMap.put("errorMsg", "订单id不能为空");
                redirectAttributes.addFlashAttribute("errorMsg", "订单id不能为空");
                return new ModelAndView(new RedirectView("/merchant/center/cart/index.htm", true, false));
            }
            Transaction t2 = CatUtil.initTransaction("queryConfirmOrder", "queryConfirmOrder");
            order.setUseBalance(false);
            modelMap.put("evidenceImages", order.getEvidenceImages());

            List<OrderBusinessDto> orderList = orderService.selectWebServiceWithSubOrders(order.getId());
            OrderBusinessDto confirmOrder = orderList.stream().filter(o -> o.getId().equals(order.getId())).findFirst().get();
            confirmOrder.setAccountId(order.getAccountId());

            if (confirmOrder == null) {
                throw new RuntimeException("查询不到订单信息");
            }

            fillShopTransferType(modelMap, confirmOrder);

            order.setOrderSource(Constants.IS4);
            order.setAppVersion(1);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(confirmOrder.getCreateTime());
            Date payExpireTime = confirmOrder.getPayExpireTime();
            int payFinalHour = Long.valueOf(DateUtil.dateDiff(confirmOrder.getCreateTime(), payExpireTime, 2)).intValue();
            calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) + payFinalHour);
            payExpireTime = payExpireTime == null ? calendar.getTime() : payExpireTime;
            //这里已指定是在线业务，不用考虑订单过期时间按类型分别存储
            confirmOrder.setPayEndTime(new SimpleDateFormat("yyyyMMddHHmmss").format(payExpireTime));
            //发票相关
//            TODO 发票
            InvoiceTypeBussinessDto invoiceTypeBussinessDto = invoiceBussinessCrmApi
                    .queryInvoiceTypeById(merchant.getId());
            String invoinceTxt = invoiceTypeBussinessDto == null ? "电子普通发票" : invoiceTypeBussinessDto.getName();
            confirmOrder.setInvoinceText(invoinceTxt);
            confirmOrder.setBillInfo(invoinceTxt);
            confirmOrder.setMoney(confirmOrder.getCashPayAmount());
            modelMap.put("payFinalHour", DateFormatUtils.format(payExpireTime, "yyyy-MM-dd HH:mm:ss"));
            modelMap.put("order", confirmOrder);
            EcpOrderPayDto eecpOrderPayDto = ecpOrderBusinessApi.getEecpOrderPayDto(confirmOrder.getId());

            StringBuffer sb = new StringBuffer();
            for (String s1 : eecpOrderPayDto.getOrderNos()) {
                sb.append(s1).append(",");
            }
            modelMap.put("orderNos", sb.toString().substring(0, sb.length() - 1));
            modelMap.put("money", eecpOrderPayDto.getOrderMoney());
            modelMap.put("isList", "0");
            modelMap.put("tranNo", tranNo);
            modelMap.put("token", token);
            modelMap.put("rechargeType", RechargeEnum.ORDER_PAY.getCode());
            CatUtil.successCat(t2);

            //订单金额超过5万提示
            if (confirmOrder.getMoney().compareTo(new BigDecimal(50000)) >= 0) {
                String tips = "当前订单金额较大，建议您使用微信/支付宝进行支付";
                modelMap.put("tips", tips);
            }
            int payType = confirmOrder.getPayType();
            if (payType == OrderEnum.OrderPayType.ONLINEPAYMENT.getId() && confirmOrder.getCashPayAmount().compareTo(BigDecimal.ZERO) > 0) {
                modelMap.put("cashier", orderService.getCashierForPc(confirmOrder, orderList));
                return new ModelAndView("/order/pay_order.ftl");
            } else if (payType == OrderEnum.OrderPayType.OFFLINETRANSFER.getId()) {
                Map<String, String> transferInfo = orderBusinessApi.getTransferInfo(EntityConvert.convert(OrderBusinessDto.class, confirmOrder), confirmOrder.getBranchCode());
                return new ModelAndView("/order/offline_pay_ok.ftl", transferInfo);
            } else {
                // 1.构建下单支付参数
                OrderPayRequest orderPayRequest = OrderConvertUtil.buildOrderPayRequest(order, Constants.IS1);
                String gainVoucher = couponBusinessApi.couponPromotionToOrder(orderPayRequest);
                if (StringUtil.isNotEmpty(gainVoucher)) {
                    modelMap.put("gainVoucher", gainVoucher);
                }
                return new ModelAndView("/order/resultok.ftl");
            }

        } catch (Exception e) {
            logger.error("queryConfirmOrder查询生成的订单异常", e);
            //解决url后面拼接参数问题
            orderService.setErrorMsg(merchant.getId(), e.getMessage());
            redirectAttributes.addFlashAttribute("errorMsg", e.getMessage());
            return new ModelAndView(new RedirectView("/merchant/center/cart/index.htm", true, false));
        }
    }

    private void fillShopTransferType(ModelMap modelMap, OrderBusinessDto order) {
        // 店铺电汇类型：1-电汇商业;2-电汇平台
        Integer shopTransferType = null;
        if (order.getIsThirdCompany() == 0) {
            shopTransferType = 2;
        } else if (order.getIsThirdCompany() == 1) {//pop
            if (order.getIsFbp() == 0) {
                shopTransferType = 1;
            } else if (order.getIsFbp() == 1) {//fbp
                if (CollectionUtil.isNotEmpty(controlSaleOrgIds) && controlSaleOrgIds.contains(order.getOrgId())) {
                    shopTransferType = 2;//控销
                } else if (Objects.equals(heYeOrgId, order.getOrgId())) {//荷叶
                    shopTransferType = 1;
                }
            }
        }
        modelMap.put("shopTransferType", String.valueOf(shopTransferType));
    }

    /**
     * 跳转到支付页面
     *
     * @param order
     * @return String
     * @Title: settle
     */
    @RequestMapping("/confirmOrder.htm")
    public ModelAndView confirmOrder(ConfirmOrderVo order, ModelMap modelMap, String shoppingCartImgUUID, String bizSource, String token, RedirectAttributes redirectAttributes) {
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            //重楼test
            if (merchant.getAccountId().longValue() == test_tip_accountid.longValue()) {
                redirectAttributes.addFlashAttribute("errorMsg", "chonglou_test");
                RedirectView redirectView = new RedirectView("/merchant/center/cart/index.htm", true, false, false);
                return new ModelAndView(redirectView);
            }

            ModelAndView modelAndView = orderService.setConfimOrder(order, merchant, modelMap, request, bizSource, token);
            return modelAndView;
        } catch (Exception e) {
            logger.error("生成订单异常", e);
            //解决url后面拼接参数问题
            if (merchant != null)
                orderService.setErrorMsg(merchant.getId(), e.getMessage());
            redirectAttributes.addFlashAttribute("errorMsg", e.getMessage());
            if (order.getBizScene() != null && order.getBizScene().equals(BizSceneEnum.MATCH_PRICE.getKey())) {
                return new ModelAndView(new RedirectView("/matchPrice/batchPurchaseIndex.htm", true, false));
            }
            RedirectView redirectView = new RedirectView("/merchant/center/cart/index.htm", true, false, false);
            return new ModelAndView(redirectView);
        }
    }


    /**
     * 计算订单的优惠金额
     *
     * @param tempOrder void
     * @Title: calcOrderAdjust
     */
    private void calcOrderAdjust(OrderDiscountCalculator tempOrder) {
        //满减优惠
        BigDecimal fullDivPrice = new BigDecimal(0);

        BigDecimal voucherDivPrice = new BigDecimal(0);
        //订单优惠金额
        BigDecimal rebate = tempOrder.getDiscount();
        if (rebate != null && rebate.doubleValue() != 0.00) {
            OrderAdjustBusinessDto paramOrderAdjust = new OrderAdjustBusinessDto();
            //设置订单id
            paramOrderAdjust.setOrderNo(tempOrder.getOrderNo());
            //查询订单优惠券列表
            List<OrderAdjustBusinessDto> orderAdjustList = orderAdjustBusinessApi.selectList(paramOrderAdjust);

            for (OrderAdjustBusinessDto orderAdjust : orderAdjustList) {
                //判断优惠券的类型
                if (orderAdjust.getAdjustType() == OrderAdjustTypeEnum.ADJUST_TYPE_BUY_REWARD.getId()) {
                    //满减优惠
                    fullDivPrice = fullDivPrice.add(orderAdjust.getAmount());
                }
                if (orderAdjust.getAdjustType() == OrderAdjustTypeEnum.ADJUST_TYPE_VOUCHER.getId()) {
                    //使用优惠券记录
                    voucherDivPrice = voucherDivPrice.add(orderAdjust.getAmount());
                }
                // 返券的不需记录到rebate中
                if (orderAdjust.getAdjustType() == OrderAdjustTypeEnum.ADJUST_TYPE_RETURN_VOUCHER.getId()) {
                    continue;
                }
                //订单优惠金额-优惠券的数量
                rebate = rebate.subtract(orderAdjust.getAmount());
            }
        }
        //返利减去余额金额(因为订单拓展表的余额会减少,所以直接取最原始余额)
        OrderExtendBusinessDto orderExtendBusinessDto = orderExtendBusinessApi.selectByOrderNo(tempOrder.getOrderNo());
        if (orderExtendBusinessDto != null) {
            Double balanceUse = orderExtendBusinessDto.getSourceBalanceUse().doubleValue();
            if (balanceUse != null) {
                tempOrder.setBalanceAmount(new BigDecimal(balanceUse).setScale(2, RoundingMode.HALF_UP));
                rebate = rebate.subtract(new BigDecimal(balanceUse).setScale(2, RoundingMode.HALF_UP));
            }
        }
        tempOrder.setFullDivPrice(fullDivPrice);
        tempOrder.setVoucherDivPrice(voucherDivPrice);
        tempOrder.setRebate(rebate);

        if (tempOrder.getIsThirdCompany() == 0) {
            //扣除掉运费
            BigDecimal freightAmount = tempOrder.getFreightAmount();
            if (freightAmount == null) {
                freightAmount = BigDecimal.ZERO;
            }
            tempOrder.setTotalAmount(tempOrder.getTotalAmount().subtract(freightAmount).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private void addDataToModelMap(String branchCode, ModelMap modelMap) {
        try {
            Map<String, CodeitemBusinessDto> codeitemMap = codeItemServiceRpc.selectByCodemapRTMap("SETTLE_CONF", branchCode);
            CodeitemBusinessDto retryInterval = codeitemMap.get("RETRY_INTERVAL");
            modelMap.put("retryInterval", retryInterval.getName());

            CodeitemBusinessDto retryTimes = codeitemMap.get("RETRY_TIMES");
            modelMap.put("retryTimes", retryTimes.getName());
        } catch (Exception e) {
            logger.error("addDataToModelMap error", e);
        }
    }

    /**
     * 我的订单 8888
     *
     * @param order
     * @param page
     * @param sort
     * @param modelMap
     * @param request
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @RequestMapping("/index.htm")
    public String index(OrderBusinessDto order, Page page, Sort sort, ModelMap modelMap, HttpServletRequest request) throws Exception {
        Long start = System.currentTimeMillis();
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        //如果账号角色是子账号
        if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
            order.setAccountId(merchant.getAccountId());
        } else {
            order.setAccountId(null);
        }
        order.setMerchantId(merchant.getId());
        order.setBranchCode(merchant.getRegisterCode());
        //如果查询待配送，实际查待配送和出库中的数据
        if (order.getStatus() != null) {
            if (order.getStatus() == OrderEnum.OrderMiddleStatus.STATUS_FOR_THE_SHIPPING.getId()) {
                order.setStatuses(new Integer[]{1, 7});
                //售后退款91,包含90,91
            } else if (order.getStatus() == OrderEnum.OrderStatus.REFUNDPENDING.getId()) {
//                order.setStatus(null);
                order.setAuditStateArray(new Integer[]{-1, 0, 1});
                //查询配送中(配送中2和已签收)
            } else if (order.getStatus() == 2 || order.getStatus() == 3) {
//                order.setStatus(null);
                order.setStatuses(new Integer[]{order.getStatus()});

            }
        }
        Transaction t2 = CatUtil.initTransaction("order_index", "order_index");
        if (order.getOrderSource() != null && order.getOrderSource() == -1) {
            order.setOrderSourceArray(new Integer[]{-1, 1, 2, 3});
        }
        order.setVisibled(OrderBusinessDto.STATUS_VISIBLED);
        order.setIsQueryToBeConfirmedCount(true);
        if (order.getStartCreateTime() == null && order.getEndCreateTime() == null) {
            order.setStartCreateTime(DateUtil.monthToSubtract(LocalDate.now(), orderListQueryListMonth));
            order.setEndCreateTime(new Date());
        }

        //查询各个状态订单数量
        OrderBusinessDto tempOrder = new OrderBusinessDto();
        tempOrder.setMerchantId(merchant.getId());
        tempOrder.setTerminalType(4);
        tempOrder.setVisibled(OrderBusinessDto.STATUS_VISIBLED);
        tempOrder.setAccountRole(merchant.getAccountRole());
        //如果账号角色是子账号
        if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
            tempOrder.setAccountId(merchant.getAccountId());
        } else {
            tempOrder.setAccountId(null);
        }
        Map<String, Integer> resultMap = orderBusinessApi.findNumGroupByStatus(tempOrder);
        modelMap.put("waitPayNum", resultMap.get("waitPayNum"));
        modelMap.put("waitShippingNum", resultMap.get("waitShippingNum"));
        modelMap.put("shippingNum", resultMap.get("waitReceiveNum"));
        modelMap.put("refundNum", resultMap.get("refundNum"));
        modelMap.put("compareNum", resultMap.get("compareNum"));
        modelMap.put("afterSalesCount", resultMap.get("afterSalesCount"));

        //获取字典表
        PageInfo pageInfoParam = new PageInfo<>();
        pageInfoParam.setPageNum(page.getOffset());
        pageInfoParam.setPageSize(page.getLimit());

        if (order.getStatus() != null && order.getStatus() == 89) {
            AfterSalesQueryParam param = new AfterSalesQueryParam();
            param.setMerchantId(merchant.getId());
            //如果账号角色是子账号
            if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
                param.setAccountId(merchant.getAccountId());
            } else {
                param.setAccountId(null);
            }
            param.setPageSize(page.getLimit() == null ? 20 : page.getLimit());
            param.setPageNo(page.getOffset() == null ? 1 : page.getOffset());
            param.setOrderNo(order.getOrderNo());
            param.setAppQueryFlag(1);
            param.setOrgId(order.getOrgId());
            String afterSalesProcessState = request.getParameter("afterSalesProcessState");
            if (StringUtils.isNotBlank(afterSalesProcessState)) {
                param.setStatus(Integer.parseInt(afterSalesProcessState));
                modelMap.put("afterSalesProcessState", param.getStatus());
            } else {
                modelMap.put("afterSalesProcessState", -1);
            }
            param.setDocType(2);
            param.setMerchantId(merchant.getId());
            param.setCompanyName(order.getCompanyName());
            final com.xyy.ec.order.business.query.Page<AfterSalesListVo> result = afterSalesService.queryAfterSalesByPage(param);

            if (CollectionUtils.isNotEmpty(result.getRows())) {
                List<String> orgList = result.getRows().stream().map(AfterSalesListVo::getOrgId).distinct().collect(Collectors.toList());
                List<TbXyyPopFddEnterpriseEmpowerDTO> res = platformServiceAgreementApi.getFddEmpower(orgList);
                logger.info("platformServiceAgreementApi.getFddEmpower req:{},res:{}",JSON.toJSONString(orgList),JSON.toJSONString(res));
                if (CollectionUtils.isNotEmpty(res)) {
                    Map<String, List<TbXyyPopFddEnterpriseEmpowerDTO>> map = res.stream().collect(Collectors.groupingBy(TbXyyPopFddEnterpriseEmpowerDTO::getOrgId));
                    result.getRows().stream().forEach(x -> {
                        List<TbXyyPopFddEnterpriseEmpowerDTO> lists = map.get(x.getOrgId());
                        if (CollectionUtil.isNotEmpty(lists)) {
                            x.setSignaturesStatus(lists.get(0).getFddStatus());
                        }
                    });

                }
            }

            modelMap.put("paramOrder", order);

            String requestUrl = this.getRequestUrl(request);
            result.setRequestUrl(requestUrl);
            modelMap.put("pager", result);
            return "/order/index.ftl";
        }
        PageInfo<MyOrderBusinessDto> modelPageInfo = orderBusinessApi.findMerchantOrders(pageInfoParam, order);
        //v5.2.0需求，取消退款/驳回申请退款，需要展示‘查看退款’按钮,
        List<String> orderNoList = Lists.newArrayList();
        orderNoList = modelPageInfo.getList().stream().filter(o -> o.getRefundCount() <= 0).map(MyOrderBusinessDto::getOrderNo).collect(Collectors.toList());
        if (orderNoList.size() > 0) {
            List<OrderRefundBusinessDto> orderRefundBusinessDtoList = orderRefundBusinessApi.selectRefundOrderListByOrderNoList(orderNoList);
            Map<String, Long> refundBusinessDtoMap =
                    orderRefundBusinessDtoList.stream().collect(Collectors.groupingBy(OrderRefundBusinessDto::getOrderNo, Collectors.counting()));
            Map<String, List<String>> orderRefundNoMap = orderRefundBusinessDtoList.stream()
                    .filter(dto -> dto.getOrderNo() != null && dto.getRefundOrderNo() != null).collect(Collectors.groupingBy(
                            OrderRefundBusinessDto::getOrderNo,
                            Collectors.mapping(
                                    OrderRefundBusinessDto::getRefundOrderNo,
                                    Collectors.toList()
                            )
                    ));
            modelPageInfo.getList().stream().filter(o -> o.getRefundCount() <= 0).forEach(o -> {
                if (refundBusinessDtoMap.containsKey(o.getOrderNo())) {
                    o.setRefundCount(refundBusinessDtoMap.get(o.getOrderNo()).intValue());
                }
            });
        }
        Page<MyOrderBusinessDto> pageInfo = new Page<>();
        pageInfo.setRows(modelPageInfo.getList());
        if (CollectionUtils.isNotEmpty(modelPageInfo.getList())) {
            List<String> orgList =modelPageInfo.getList().stream().map(MyOrderBusinessDto::getOrgId).distinct().collect(Collectors.toList());
            List<TbXyyPopFddEnterpriseEmpowerDTO> res = platformServiceAgreementApi.getFddEmpower(orgList);
            logger.info("platformServiceAgreementApi.getFddEmpower req:{},res:{}",JSON.toJSONString(orgList),JSON.toJSONString(res));
            if (CollectionUtils.isNotEmpty(res)) {
                Map<String, List<TbXyyPopFddEnterpriseEmpowerDTO>> map = res.stream().collect(Collectors.groupingBy(TbXyyPopFddEnterpriseEmpowerDTO::getOrgId));
                modelPageInfo.getList().stream().forEach(x -> {
                    List<TbXyyPopFddEnterpriseEmpowerDTO> lists = map.get(x.getOrgId());
                    if (CollectionUtil.isNotEmpty(lists)) {
                        x.setSignaturesStatus(lists.get(0).getFddStatus());
                    }
                });

            }
        }
        pageInfo.setTotal(modelPageInfo.getTotal());
        pageInfo.setPageCount(modelPageInfo.getPages());
        String requestUrl = this.getRequestUrl(request);
        pageInfo.setRequestUrl(requestUrl);
        pageInfo.setOffset(page.getOffset());
        modelMap.put("pager", pageInfo);
        logger.info("orderExceptionList:{}", JSON.toJSONString(pageInfo));

        modelMap.put("paramOrder", order);
        CodeItemDto codeItem = null;
        String notice = "";
        try {
            codeItem = codeItemServiceRpc.getCodeItem(CodeMapConstants.GLOBAL_ACTIVITY_CONF, "CENTER_NOTICE", merchant.getRegisterCode());
        } catch (Exception e) {
            logger.error("字典获取个人中心活动注意事项异常", e);
        }

        if (codeItem != null) {
            notice = codeItem.getName();
        }
        modelMap.put("notice", notice);
        modelMap.put("center_menu", "order");
        modelMap.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
        modelMap.put("merchant", merchant);
        //电子资质下载灰度控制
        modelMap.put("signaturesdownload_switch", Boolean.TRUE);


        Long end = System.currentTimeMillis();
        CatUtil.successCat(t2);

        logger.info("#####################订单列表执行时间：{}", end - start);
        return "/order/index.ftl";
    }

    /**
     * 查看订单详情
     *
     * @param orderNo
     * @param modelMap
     * @return
     */
    @RequestMapping("/queryDetail")
    public String queryDetail(String orderNo, ModelMap modelMap) {
        MyOrderInfoBusinessDto order = null;
        try {
            final OrderBusinessDto orderBusinessDto = orderBusinessApi.queryByOrderNo(orderNo);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            modelMap.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            order = orderBusinessApi.selectOrderDetail(merchant.getId(), orderBusinessDto.getId(), OrderDetailEnum.DELIVERING.getCode());

            OrderDiscountCalculator calculator = new OrderDiscountCalculator(order);
            calcOrderAdjust(calculator); //计算订单优惠金额
            BeanUtils.copyProperties(calculator, order);

            String phone = null;
            //pop订单获取商家电话
            if (order.getIsThirdCompany() == Constants.IS1) {
                OrgInfoDto orgInfo = orderBusinessApi.getOrgInfo(order.getOrgId());
                if (orgInfo != null) {
                    phone = orgInfo.getPhone();
                }
            }
            modelMap.put("merchant", merchant);
            modelMap.put("phone", phone);
            List<OrderDetailBusinessDto> orderDetailBusinessDtos = order.getDetailList();
            List<Long> skuIds = orderDetailBusinessDtos.stream().map(OrderDetailBusinessDto::getSkuId).collect(Collectors.toList());
            List<SkuCategoryRelationBusinessDTO> skuCategoryRelationBusinessDTOS = categoryRelationBusinessApi.findSkuCategoryRelationBySkuIdList(skuIds);
            if (CollectionUtils.isNotEmpty(skuCategoryRelationBusinessDTOS)) {
                Map<Long, List<SkuCategoryRelationBusinessDTO>> skuCategoryRelationMap = skuCategoryRelationBusinessDTOS.stream().collect(Collectors.groupingBy(SkuCategoryRelationBusinessDTO::getSkuId));
                orderDetailBusinessDtos.forEach(orderDetailBusinessDto -> {
                    List<SkuCategoryRelationBusinessDTO> skuCategoryRelations = skuCategoryRelationMap.get(orderDetailBusinessDto.getSkuId());
                    if (CollectionUtils.isNotEmpty(skuCategoryRelations)) {
                        // 一般一个商品对应一个分类关系，但线上有大量商品对应多条一样的分类关系，所以这里取最新一条关系作为商品的分类
                        SkuCategoryRelationBusinessDTO skuCategoryRelationBusinessDTO = skuCategoryRelationBusinessDTOS.get(skuCategoryRelationBusinessDTOS.size() - 1);
                        orderDetailBusinessDto.setCategoryFirstId(skuCategoryRelationBusinessDTO.getCategoryFirstId());
                        orderDetailBusinessDto.setCategorySecondId(skuCategoryRelationBusinessDTO.getCategorySecondId());
                        orderDetailBusinessDto.setCategoryThirdId(skuCategoryRelationBusinessDTO.getCategoryThirdId());
                    }
                });
            }
            final List<TradeCertificateResultDto> tradeCertificateResultDtos = queryTradeCertificate(order);
            if (CollectionUtils.isNotEmpty(tradeCertificateResultDtos)) {
                final List<TradeCertificateResultDto> list = tradeCertificateResultDtos.stream().filter(x -> TransactionTypeEnum.PAY.getCode().equals(x.getTradeType())).collect(Collectors.toList());
                modelMap.put("payCertificateResult", list);
                modelMap.put("refundCertificateResult", tradeCertificateResultDtos.stream().filter(x -> TransactionTypeEnum.REFUND.getCode().equals(x.getTradeType())).collect(Collectors.toList()));

            }
            Set<String> specifyPopShopLicenseOrgIds = appProperties.getSpecifyPopShopLicenseOrgIds();
            boolean isSpecifyPopShop = CollectionUtils.isNotEmpty(specifyPopShopLicenseOrgIds) && specifyPopShopLicenseOrgIds.contains(order.getOrgId());
            if (isSpecifyPopShop) {
                order.setCompanyName(null);
            }
        } catch (Exception e) {
            logger.error("查看订单异常", e);
        }
        modelMap.put("order", order);
        modelMap.put("center_menu", "order");
        return "/order/detail.ftl";
    }

    /**
     * 查看订单详情
     *
     * @param id
     * @param modelMap
     * @return
     */
    @RequestMapping("/detail/{id}.htm")
    public String detail(@PathVariable Long id,
                         @RequestParam(value = "pageType", required = false) String pageType, ModelMap modelMap) {
        MyOrderInfoBusinessDto order = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            modelMap.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            order = orderBusinessApi.selectOrderDetail(merchant.getId(), id, OrderDetailEnum.DELIVERING.getCode());

            if(order.getIsThirdCompany() !=0 &&  order.getIsFbp() !=1)
            {
            List<String> orgIdList=new ArrayList<>();
            orgIdList.add(order.getOrgId());
            List<TbXyyPopFddEnterpriseEmpowerDTO> res = platformServiceAgreementApi.getFddEmpower(orgIdList);
           if(CollectionUtils.isNotEmpty(res))
               {
                   order.setSignaturesStatus(res.get(0).getFddStatus());
               }
            }

            OrderDiscountCalculator calculator = new OrderDiscountCalculator(order);
            calcOrderAdjust(calculator); //计算订单优惠金额
            BeanUtils.copyProperties(calculator, order);

            String phone = null;
            //pop订单获取商家电话
            if (order.getIsThirdCompany() == Constants.IS1) {
                OrgInfoDto orgInfo = orderBusinessApi.getOrgInfo(order.getOrgId());
                if (orgInfo != null) {
                    phone = orgInfo.getPhone();
                }
            }
            modelMap.put("merchant", merchant);
            modelMap.put("phone", phone);
            List<OrderDetailBusinessDto> orderDetailBusinessDtos = order.getDetailList();
            List<Long> skuIds = orderDetailBusinessDtos.stream().map(OrderDetailBusinessDto::getSkuId).collect(Collectors.toList());
            List<SkuCategoryRelationBusinessDTO> skuCategoryRelationBusinessDTOS = categoryRelationBusinessApi.findSkuCategoryRelationBySkuIdList(skuIds);
            if (CollectionUtils.isNotEmpty(skuCategoryRelationBusinessDTOS)) {
                Map<Long, List<SkuCategoryRelationBusinessDTO>> skuCategoryRelationMap = skuCategoryRelationBusinessDTOS.stream().collect(Collectors.groupingBy(SkuCategoryRelationBusinessDTO::getSkuId));
                orderDetailBusinessDtos.forEach(orderDetailBusinessDto -> {
                    List<SkuCategoryRelationBusinessDTO> skuCategoryRelations = skuCategoryRelationMap.get(orderDetailBusinessDto.getSkuId());
                    if (CollectionUtils.isNotEmpty(skuCategoryRelations)) {
                        // 一般一个商品对应一个分类关系，但线上有大量商品对应多条一样的分类关系，所以这里取最新一条关系作为商品的分类
                        SkuCategoryRelationBusinessDTO skuCategoryRelationBusinessDTO = skuCategoryRelationBusinessDTOS.get(skuCategoryRelationBusinessDTOS.size() - 1);
                        orderDetailBusinessDto.setCategoryFirstId(skuCategoryRelationBusinessDTO.getCategoryFirstId());
                        orderDetailBusinessDto.setCategorySecondId(skuCategoryRelationBusinessDTO.getCategorySecondId());
                        orderDetailBusinessDto.setCategoryThirdId(skuCategoryRelationBusinessDTO.getCategoryThirdId());
                    }
                });
            }
            final List<TradeCertificateResultDto> tradeCertificateResultDtos = queryTradeCertificate(order);
            if (CollectionUtils.isNotEmpty(tradeCertificateResultDtos)) {
                final List<TradeCertificateResultDto> list = tradeCertificateResultDtos.stream().filter(x -> TransactionTypeEnum.PAY.getCode().equals(x.getTradeType())).collect(Collectors.toList());
                modelMap.put("payCertificateResult", list);
                modelMap.put("refundCertificateResult", tradeCertificateResultDtos.stream().filter(x -> TransactionTypeEnum.REFUND.getCode().equals(x.getTradeType())).collect(Collectors.toList()));

            }
            Set<String> specifyPopShopLicenseOrgIds = appProperties.getSpecifyPopShopLicenseOrgIds();
            boolean isSpecifyPopShop = CollectionUtils.isNotEmpty(specifyPopShopLicenseOrgIds) && specifyPopShopLicenseOrgIds.contains(order.getOrgId());
            if (isSpecifyPopShop) {
                order.setCompanyName(null);
            }

            ApiRPCResult<MerchantForLicense> rpcResult = licenseApi.getMerchantBasicInfo(merchant.getId());
            if(rpcResult != null &&  rpcResult.getData() != null) {
                order.setInvoinceName(rpcResult.getData().getInvoiceName());
            }

            //设置子订单的剩余信息
            boolean isParent = order.getIsParent() != null && order.getIsParent().compareTo(1) == 0;
            if(isParent){
                BigDecimal totalAmount = BigDecimal.ZERO;
                BigDecimal rebate = BigDecimal.ZERO;
                BigDecimal voucherDivPrice = BigDecimal.ZERO;
                BigDecimal fullDivPrice = BigDecimal.ZERO;
                BigDecimal balanceAmount = BigDecimal.ZERO;
                for (ChildOrderDetailBusinessDto childOrder : order.getShopOrderList()) {
                    if(childOrder.getIsThirdCompany() !=0 &&  childOrder.getIsFbp() !=1)
                    {
                        List<String> orgIdList=new ArrayList<>();
                        orgIdList.add(childOrder.getOrgId());
                        List<TbXyyPopFddEnterpriseEmpowerDTO> res = platformServiceAgreementApi.getFddEmpower(orgIdList);
                        if(CollectionUtils.isNotEmpty(res))
                        {
                            childOrder.setSignaturesStatus(res.get(0).getFddStatus());
                        }
                    }
                    //计算订单优惠金额
                    OrderDiscountCalculator odc = new OrderDiscountCalculator(childOrder);
                    //做一下非空判断
                    odc.checkValues();
                    calcOrderAdjust(odc);
                    BeanUtils.copyProperties(odc, childOrder);
                    totalAmount = totalAmount.add(childOrder.getTotalAmount());
                    rebate = rebate.add(childOrder.getRebate());
                    voucherDivPrice = voucherDivPrice.add(childOrder.getVoucherDivPrice());
                    fullDivPrice = fullDivPrice.add(childOrder.getFullDivPrice());
                    balanceAmount = balanceAmount.add(childOrder.getBalanceAmount());
                    isSpecifyPopShop = CollectionUtils.isNotEmpty(specifyPopShopLicenseOrgIds) && specifyPopShopLicenseOrgIds.contains(childOrder.getOrgId());
                    if (isSpecifyPopShop) {
                        childOrder.setCompanyName(null);
                    }
                    ApiRPCResult<MerchantForLicense> rpc = licenseApi.getMerchantBasicInfo(merchant.getId());
                    if(rpc != null &&  rpc.getData() != null) {
                        childOrder.setInvoinceName(rpc.getData().getInvoiceName());
                    }
                }
                //外层再设置一下母单的订单优惠信息
                order.setBalanceAmount(balanceAmount);
                order.setVoucherDivPrice(voucherDivPrice);
                order.setFullDivPrice(fullDivPrice);
                order.setRebate(rebate);
                order.setTotalAmount(totalAmount);
            }
        } catch (Exception e) {
            logger.error("查看订单异常", e);
        }
        modelMap.put("order", order);
        if (pageType != null) {
            modelMap.put("pageType", pageType);
        }
        modelMap.put("center_menu", "order");
        return "/order/detail.ftl";
    }

    private List<TradeCertificateResultDto> queryTradeCertificate(MyOrderInfoBusinessDto order) {
        try {
            logger.info("queryTradeCertificate start {} PayTime:{}", order.getOrderNo(), order.getPayTime());

            if (Objects.equals(order.getPayChannel(), PayChannelEnum.JD_CREDIT.getValue())) {
                Date noneShowJdCreditCertificateDate = getNoneShowJdCreditCertificateDate(noneShowJdCreditCertificateTime);
                if (noneShowJdCreditCertificateDate != null && order.getPayTime() != null && order.getPayTime().before(noneShowJdCreditCertificateDate)) {
                    order.setShowCertificate(Boolean.FALSE);
                    return Lists.newArrayList();
                }
            }

            boolean orderPayChannelCondition = Objects.equals(order.getPayChannel(), PayChannelEnum.PINGAN_LOAD.getValue()) ||
                    Objects.equals(order.getPayChannel(), PayChannelEnum.XYD_LOAN.getValue()) ||
                    Objects.equals(order.getPayChannel(), PayChannelEnum.KING_DEE_LOAN.getValue()) ||
                    Objects.equals(order.getPayChannel(), PayChannelEnum.JD_CREDIT.getValue());
            if (order.getPayTime() != null && orderPayChannelCondition) {
                //当前时间>订单支付时间+2工作日
                Date beforeDay = DateUtils.getFrontDay(new Date(), 2);
                if (beforeDay.compareTo(order.getPayTime()) > 0) {

                    final ApiRPCResult<List<TradeCertificateResultDto>> result = pingAnAccountApi.queryTradeCertificateV2(order.getOrderNo(), order.getPayChannel());
                    return result.getData();
                } else {
                    logger.warn("queryTradeCertificate 当前时间-支付时间<2day {}", order.getOrderNo());
                }
            }

        } catch (Exception e) {
            logger.error("queryTradeCertificate error {}", order.getOrderNo(), e);
        }
        return Lists.newArrayList();
    }

    private Date getNoneShowJdCreditCertificateDate(String noneShowJdCreditCertificateTime) {
        try {
            return cn.hutool.core.date.DateUtil.parse(noneShowJdCreditCertificateTime, DateUtil.PATTERN_STANDARD);
        } catch (Exception e) {
            logger.error("getNoneShowJdCreditCertificateDate error, {}", noneShowJdCreditCertificateTime, e);
        }
        return null;
    }

    /**
     * 订单明细入库价页面
     *
     * @param id
     * @param modelMap
     * @return
     */
    @RequestMapping("/purchase/{id}.htm")
    public String purchase(@PathVariable Long id, ModelMap modelMap) {
        MyOrderInfoBusinessDto order = null;
        List<OrderDetailBusinessDto> orderDetailList = new ArrayList<OrderDetailBusinessDto>();
        try {
            Transaction t2 = CatUtil.initTransaction("order_purchase", "order_purchase");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            modelMap.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            //MerchantBussinessDto merchant = merchantBussinessApi.selectByPrimaryKey(26L);
            order = orderBusinessApi.selectOrderDetail(merchant.getId(), id, OrderDetailEnum.DELIVERING.getCode());
            if (order != null) {
                List<ActivityPackageModel> activityPackageList = order.getPackageList();
                if (CollectionUtil.isNotEmpty(activityPackageList)) {
                    for (ActivityPackageModel activityPackage : activityPackageList) {
                        List<OrderDetailBusinessDto> packageDetailList = activityPackage.getOrderDetailList();
                        if (CollectionUtil.isNotEmpty(packageDetailList)) {
                            orderDetailList.addAll(packageDetailList);
                        }
                    }
                }
                List<OrderDetailBusinessDto> detaiList = order.getDetailList();
                if (CollectionUtil.isNotEmpty(detaiList)) {
                    orderDetailList.addAll(detaiList);
                }
                for (OrderDetailBusinessDto orderDetail : orderDetailList) {
                    //优惠
                    BigDecimal discountAmountTmp = orderDetail.getDiscountAmount().divide(new BigDecimal(orderDetail.getProductAmount()), RoundingMode.HALF_UP);
                    orderDetail.setDiscountAmount(discountAmountTmp.setScale(2, RoundingMode.HALF_UP));
                    //余额抵扣
                    BigDecimal useBalanceAmountTmp = orderDetail.getUseBalanceAmount().divide(new BigDecimal(orderDetail.getProductAmount()), RoundingMode.HALF_UP);
                    orderDetail.setUseBalanceAmount(useBalanceAmountTmp.setScale(2, RoundingMode.HALF_UP));
                    //返利
                    BigDecimal balanceAmountTmp = orderDetail.getBalanceAmount().divide(new BigDecimal(orderDetail.getProductAmount()), RoundingMode.HALF_UP);
                    orderDetail.setBalanceAmount(balanceAmountTmp.setScale(2, RoundingMode.HALF_UP));
                    //成本价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）/数量 - 返利/数量；
                    BigDecimal productPrice = orderDetail.getProductPrice();
                    BigDecimal costPrice = productPrice.subtract(discountAmountTmp).subtract(balanceAmountTmp).setScale(4, BigDecimal.ROUND_HALF_UP);
                    orderDetail.setCostPrice(costPrice);
                    orderDetail.setPurchasePrice(orderDetail.getPurchasePrice().setScale(4, BigDecimal.ROUND_HALF_UP));
                }
            }
            CatUtil.successCat(t2);
        } catch (Exception e) {
            logger.error("查看订单异常", e);
        }
        modelMap.put("id", order.getId());
        modelMap.put("orderDetailList", orderDetailList);
        modelMap.put("center_menu", "order");
        int setShouldShowDialogAuto;
        boolean exists = redisBusinessApi.exists(RedisConstants.SHOW_TREASURY_PRICES + order.getMerchantId().toString());
        if (exists) {
            setShouldShowDialogAuto = 0;
        } else {
            redisBusinessApi.setStr(RedisConstants.SHOW_TREASURY_PRICES + order.getMerchantId().toString(), "1");
            setShouldShowDialogAuto = 1;
        }
        modelMap.put("setShouldShowDialogAuto", setShouldShowDialogAuto);
        return "/order/purchase.ftl";
    }

    /**
     * app 确认收货功能
     *
     * @param orderId 订单id
     * @return 返回操作结果对话框(待定)
     */
    @RequestMapping("/confirmOrderGoods.json")
    @ResponseBody
    public Object addBalance(Long orderId) throws Exception {
        try {
            Transaction t2 = CatUtil.initTransaction("order_addBalance", "order_addBalance");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            logger.info("confirmOrderGoods.json,orderId={},merchantId={}", new Object[]{orderId, merchant.getId()});
            orderBusinessApi.orderFinish(orderId, String.valueOf(merchant.getId()), 4);
            //不管你做何种优化： 这段代码逻辑不能去掉-----------------切记切记切记(生成发票改为出库操作调用)
            //invoiceBusinessApi.generateInvoce(orderId, EInvoiceHisTypeEnum.GENERATE.getId());
            //下单返券逻辑
            OrderBusinessDto order = orderBusinessApi.selectById(orderId);
            // 1.构建下单支付参数
            OrderPayRequest orderPayRequest = OrderConvertUtil.buildOrderPayRequest(order, Constants.IS2);
            String gainVoucher = couponBusinessApi.couponPromotionToOrder(orderPayRequest);
            CatUtil.successCat(t2);
            if (StringUtil.isNotEmpty(gainVoucher)) {
                Map<String, Object> result = this.addResult(gainVoucher);
                result.put("gainVoucher", gainVoucher);
                return result;
            } else {
                return this.addResult("收货成功！");
            }
        } catch (Exception e) {
            logger.error("confirmOrderGoods.json", e);
            return this.addError("取消订单异常");
        }
    }

    public File mkDir(File sourcePath, String name) {
        File newFile = new File(sourcePath + File.separator + name);
        if (!newFile.exists()) {
            newFile.mkdirs();
        }
        return newFile;
    }

    /**
     * 判断首营资料和药检报告是否有需要下载的url
     *
     * @param orderNo      订单编号
     * @param skuId        为空是批量下载   不为空是详情单个下载
     * @param downloadType 判断是首营质料还是药检报告
     * @param response
     * @param request
     * @return
     */
    @RequestMapping("/judgeDownloadReport")
    @ResponseBody
    public Object judgeDownloadReport(@RequestParam("orderNo") String orderNo, @RequestParam("skuId") String skuId, @RequestParam("downloadType") String downloadType,
                                      HttpServletResponse response, HttpServletRequest request) {
        Map<String, Object> result = this.addResult();
        List<ReportInformationCondition> reportInformationConditionList = getReportInformationConditions(orderNo, skuId, downloadType);
        List<ReportInformation> reportInformationList = null;
        //一个订单下的多个商品
        if (reportVersison) {
            reportInformationList = reportInformationService.getReportInformations(reportInformationConditionList);
        } else {
            reportInformationList = reportInformationApi.getReportInformations(reportInformationConditionList);
        }
        logger.info("judgeDownloadReport getReportInformations response param={}", JSONArray.toJSONString(reportInformationList));
        //是否部分上传了 药检报告/首营资料
        boolean isPartUpload = false;
        if (CollectionUtils.isNotEmpty(reportInformationList)) {
            for (ReportInformation reportInformation : reportInformationList) {
                if (!judgeUrl(result, reportInformation)) {
                    return result;
                }
                //是否上传图片 0：未上传 1：上传
                if (reportInformation.getIsUpload() == Constants.IS_UPLOAD && isPartUpload == false) {
                    isPartUpload = true;
                }
            }
        } else {
            //没有 药检报告/首营资料 可下载
            result.put("msg", "false");
            return result;
        }
        //药检报告/首营资料 不全的，前台页面列表展示所有数据
        if (isPartUpload) {
            reportInformationList.sort((a, b) -> a.getIsUpload() - b.getIsUpload());
            result.put("data", reportInformationList);
            result.put("msg", "part");
            return result;
        }
        result.put("msg", "true");
        return result;
    }

    @RequestMapping("/sendInvoiceEmail")
    @ResponseBody
    public Object sendInvoiceEmail(String email, Long orderId) {
        try {
            invoiceBusinessApi.sendInvoiceEmail(email, orderId);
            return this.addDataResult("data", null);
        } catch (Exception e) {
            logger.error("发送邮件异常：", e);
            return this.addError(null);
        }

    }

    /**
     * 批量下载发票
     */
    @RequestMapping("/queryOrderOrg")
    @ResponseBody
    public Object queryOrderOrg(OrderBusinessDto order) {
        try {
            Date startCreateTime = order.getStartCreateTime();
            Date endCreateTime = order.getEndCreateTime();
            if (endCreateTime == null && startCreateTime == null) {
                LocalDate now = LocalDate.now();
                order.setStartCreateTime(DateUtil.yearsToSubtract(now, 1l));
                order.setEndCreateTime(new Date());
            } else if (startCreateTime == null && endCreateTime != null) {
                // 区间不能超过2个月
                return this.addError("下载时间不能超过3个自然月");
            } else if (startCreateTime != null && endCreateTime == null) {
                long dateDiff = DateUtil.dateDiff(startCreateTime, new Date(), 5);
                if (dateDiff > 3) {
                    return this.addError("下载时间不能超过3个自然月");
                }
            } else if (startCreateTime != null && endCreateTime != null) {
                long dateDiff = DateUtil.dateDiff(startCreateTime, endCreateTime, 5);
                if (dateDiff > 3) {
                    return this.addError("下载时间不能超过3个自然月");
                }
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            order.setVisibled(OrderBusinessDto.STATUS_VISIBLED);
            List<OrderBusinessDto> invoiceDtos = orderBusinessApi.queryOrderOrg(order);
            return this.addResult("data", invoiceDtos);
        } catch (Exception e) {
            logger.error("queryOrderOrg {}", JSONObject.toJSONString(order), e);
            return this.addError("");
        }

    }

    /**
     * 批量下载发票
     */
    @RequestMapping("/queryInvoiceCount")
    @ResponseBody
    public Object queryInvoiceCount(OrderBusinessDto order) {
        try {
//            order.setStartCreateTime(DateUtil.string2Date("2022-08-01 00:10:01","yyyy-MM-dd HH:mm:ss"));
//            order.setEndCreateTime(DateUtil.string2Date("2022-08-26 00:10:03","yyyy-MM-dd HH:mm:ss"));
            Date startCreateTime = order.getStartCreateTime();
            Date endCreateTime = order.getEndCreateTime();
            if ((endCreateTime == null && startCreateTime == null) || (startCreateTime == null && endCreateTime != null)) {
                // 区间不能超过3个月
                return this.addError("下载时间不能超过3个自然月");
            } else if (startCreateTime != null && endCreateTime == null) {
                long dateDiff = DateUtil.dateDiff(startCreateTime, new Date(), 5);
                if (dateDiff > 3) {
                    return this.addError("下载时间不能超过3个自然月");
                }
            } else if (startCreateTime != null && endCreateTime != null) {
                long dateDiff = DateUtil.dateDiff(startCreateTime, endCreateTime, 5);
                if (dateDiff > 3) {
                    return this.addError("下载时间不能超过3个自然月");
                }
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            order.setVisibled(OrderBusinessDto.STATUS_VISIBLED);
            //如果账号角色是子账号
            if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
                order.setAccountId(merchant.getAccountId());
            } else {
                order.setAccountId(null);
            }
            Long count = orderBusinessApi.queryInvoiceCount(order);
            return this.addResult("data", count);
        } catch (Exception e) {
            logger.error("queryInvoiceCount {}", JSONObject.toJSONString(order), e);
            return this.addError("");
        }

    }



    /**
     * 批量下载发票
     */
    @RequestMapping("/batchDownloadInvoiceV2")
    @ResponseBody
    public Object batchDownloadInvoiceV2(OrderBusinessDto order, HttpServletResponse response, HttpServletRequest request) {
        try {
//            order.setStartCreateTime(DateUtil.string2Date("2022-08-01 00:10:01","yyyy-MM-dd HH:mm:ss"));
//            order.setEndCreateTime(DateUtil.string2Date("2022-08-26 00:10:03","yyyy-MM-dd HH:mm:ss"));
            Date startCreateTime = order.getStartCreateTime();
            Date endCreateTime = order.getEndCreateTime();
            if ((endCreateTime == null && startCreateTime == null) || (startCreateTime == null && endCreateTime != null)) {
                // 区间不能超过2个月
                return this.addError("下载时间不能超过2个自然月");
            } else if (startCreateTime != null && endCreateTime == null) {
                long dateDiff = DateUtil.dateDiff(startCreateTime, new Date(), 5);
                if (dateDiff > 2) {
                    return this.addError("下载时间不能超过2个自然月");
                }
            } else if (startCreateTime != null && endCreateTime != null) {
                long dateDiff = DateUtil.dateDiff(startCreateTime, endCreateTime, 5);
                if (dateDiff > 2) {
                    return this.addError("下载时间不能超过2个自然月");
                }
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            //如果账号角色是子账号
            if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
                order.setAccountId(merchant.getAccountId());
            } else {
                order.setAccountId(null);
            }
            List<OrderInvoiceDto> invoiceDtosRes = orderBusinessApi.queryInvoice(order);

            String startTime = DateUtil.date2String(order.getStartCreateTime(), "yyyyMMdd");
            String endTime = DateUtil.date2String(order.getEndCreateTime(), "yyyyMMdd");
             String packageName=   "发票" + startTime + "-" + endTime + ".zip";
            List<InvoiceDownloadInfoVo> invoiceDtos = new ArrayList<>();
            String fileType = ".pdf";
            for (OrderInvoiceDto dto : invoiceDtosRes) {
                try {
                    OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(dto.getOrderNo());
                    StringBuilder sb = new StringBuilder();
                    String companyName=orderBusinessDto.getCompanyName().replaceAll("【[^】]*】", "");
                    if(StringUtils.isEmpty(dto.getInvoiceUrl())) {
                        InvoiceDownloadInfoVo invoiceDownloadInfoVo=     InvoiceDownloadInfoVo.builder()
                                .orderNo(dto.getOrderNo())
                                .companyName(companyName)
                                .packageName(packageName)
                                .build();
                        invoiceDtos.add(invoiceDownloadInfoVo);
                       continue;
                    }
                    if (orderBusinessDto != null) {
                        String invoiceUrl = dto.getInvoiceUrl();
                        if (!invoiceUrl.contains("http")) {
                            invoiceUrl = "http://" + invoiceUrl;
                        }
                        String suffix = invoiceUrl.substring(invoiceUrl.lastIndexOf(".") + 1);
                        if (StringUtils.isNotBlank(suffix) && Arrays.asList("pdf", "ofd").contains(suffix)) {
                            fileType = "." + suffix;
                        } else {
                            Integer invoiceType = dto.getInvoiceType();
                            if (invoiceType != null && InvoiceTypeEnum.E_VAT_SPECIAL_TICKET.getId() == invoiceType) {
                                fileType = ".ofd";
                            } else {
                                fileType = ".pdf";
                            }
                        }

                        InvoiceDownloadInfoVo invoiceDownloadInfoVo=     InvoiceDownloadInfoVo.builder()
                                .orderNo(dto.getOrderNo())
                                .rul(dto.getInvoiceUrl())
                                .fileName(companyName+"_"+dto.getOrderNo()+fileType)
                                .companyName(companyName)
                                .packageName(packageName)
                                .build();
                        invoiceDtos.add(invoiceDownloadInfoVo);
                    }
                } catch (Exception e) {
                    // 可以根据实际情况记录日志或者进行其他处理
                    logger.error("batchDownloadInvoiceV2 {}", JSONObject.toJSONString(order), e);
                }
            }
            Map<String, Object> result = addResult();
            result.put(DATA, invoiceDtos);
            return result;

        } catch (Exception e) {
            logger.error("batchDownloadInvoice {}", JSONObject.toJSONString(order), e);
        }
        return addResult();
    }

    /**
     * 批量下载发票
     */
    @RequestMapping("/batchDownloadInvoice")
    @ResponseBody
    public Object batchDownloadInvoice(OrderBusinessDto order, HttpServletResponse response, HttpServletRequest request) {

        try {
//            order.setStartCreateTime(DateUtil.string2Date("2022-08-01 00:10:01","yyyy-MM-dd HH:mm:ss"));
//            order.setEndCreateTime(DateUtil.string2Date("2022-08-26 00:10:03","yyyy-MM-dd HH:mm:ss"));
            Date startCreateTime = order.getStartCreateTime();
            Date endCreateTime = order.getEndCreateTime();
            if ((endCreateTime == null && startCreateTime == null) || (startCreateTime == null && endCreateTime != null)) {
                // 区间不能超过2个月
                return this.addError("下载时间不能超过2个自然月");
            } else if (startCreateTime != null && endCreateTime == null) {
                long dateDiff = DateUtil.dateDiff(startCreateTime, new Date(), 5);
                if (dateDiff > 2) {
                    return this.addError("下载时间不能超过2个自然月");
                }
            } else if (startCreateTime != null && endCreateTime != null) {
                long dateDiff = DateUtil.dateDiff(startCreateTime, endCreateTime, 5);
                if (dateDiff > 2) {
                    return this.addError("下载时间不能超过2个自然月");
                }
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            //如果账号角色是子账号
            if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
                order.setAccountId(merchant.getAccountId());
            } else {
                order.setAccountId(null);
            }
            List<OrderInvoiceDto> invoiceDtosRes = orderBusinessApi.queryInvoice(order);


            List<OrderInvoiceExtDto> invoiceDtos = new ArrayList<>();
            for (OrderInvoiceDto dto : invoiceDtosRes) {
                OrderInvoiceExtDto orderInvoiceExtDto = new OrderInvoiceExtDto();
                BeanUtils.copyProperties(dto, orderInvoiceExtDto);
                try {
                    OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(dto.getOrderNo());
                    if (orderBusinessDto != null) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(orderBusinessDto.getCompanyName()).append("_").append(dto.getOrderNo());
                        orderInvoiceExtDto.setCombinedColumn(sb.toString().replaceAll("【[^】]*】", ""));

    //                    orderInvoiceExtDto.setCombinedColumn(orderBusinessDto.getCompanyName().replaceAll("【[^】]*】", ""));

                    }
                } catch (Exception e) {
                    // 可以根据实际情况记录日志或者进行其他处理
                    e.printStackTrace();
                }
                invoiceDtos.add(orderInvoiceExtDto);

            }

            List<OrderInvoiceExtDto> invoiceUrlList = invoiceDtos.stream().filter(x -> x.getInvoiceState() == 1)
                    .collect(Collectors.toList());

            //线上真实：//fp.baiwang.com/fp/d?d=2AF46F49138535B94308737F5E7BF42D5992F975F5735A36017D034FAACD0949
            String startTime = DateUtil.date2String(order.getStartCreateTime(), "yyyyMMdd");
            String endTime = DateUtil.date2String(order.getEndCreateTime(), "yyyyMMdd");
            String path = invoiceFilePatch + File.separator + "【电子发票】" + startTime + "-" + endTime;
//            String path ="D:"+invoiceFilePatch+ File.separator+"【电子发票】"+startTime+"-"+endTime;
            File file = new File(path);
            if (!file.exists()) {
                file.mkdirs();
            } else if (file.list().length > 0) {
                FileUtil.deleteDirectory(path);
            }

            String fileType = ".pdf";
            for (OrderInvoiceExtDto invoice : invoiceUrlList) {
                String invoiceUrl = invoice.getInvoiceUrl();
                if (!invoiceUrl.contains("http")) {
                    invoiceUrl = "http://" + invoiceUrl;
                }
                String suffix = invoiceUrl.substring(invoiceUrl.lastIndexOf(".") + 1);
                if (StringUtils.isNotBlank(suffix) && Arrays.asList("pdf", "ofd").contains(suffix)) {
                    fileType = "." + suffix;
                } else {
                    Integer invoiceType = invoice.getInvoiceType();
                    if (invoiceType != null && InvoiceTypeEnum.E_VAT_SPECIAL_TICKET.getId() == invoiceType) {
                        fileType = ".ofd";
                    } else {
                        fileType = ".pdf";
                    }
                }
                HttpUtil.downloadFile(invoiceUrl, path, invoice.getCombinedColumn() + fileType);
            }
            List<OrderInvoiceExtDto> noneInvoice = invoiceDtos.stream().filter(x -> x.getInvoiceState() == 0)
                    .collect(Collectors.toList());
            //创建Execl
            createExcelForInvoice(path, noneInvoice,"未开发票订单清单");

            createExcelForInvoice(path, invoiceUrlList,"已开发票订单清单");

            //发票YBM20220807203142158072.zip
//        File zipPathFile = mkDir(reportPatch, orderFileName+orderNo);

            ZipDownloadUtils.zip(path, invoiceFilePatch + "发票" + startTime + "-" + endTime + ".zip");

            ZipDownloadUtils.downloadFile(new File(invoiceFilePatch + "发票" + startTime + "-" + endTime + ".zip"), request, response, isDelete);

        } catch (Exception e) {
            logger.error("batchDownloadInvoice {}", JSONObject.toJSONString(order), e);
        }
        return this.addResult();
    }


    /**
     * 批量下载发票
     */
    @RequestMapping("/downloadInvoiceV2")
    @ResponseBody
    public Object downloadInvoiceV2(String orderNo, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (StringUtils.isEmpty(orderNo)) {
                return addResult();
            }
            OrderBusinessDto order = new OrderBusinessDto();
            order.setOrderNo(orderNo);
            List<OrderInvoiceDto> invoiceDtos = orderBusinessApi.queryInvoice(order);
            String fileType = ".pdf";
            List<InvoiceDownloadInfoVo> invoiceRes = new ArrayList<>();
            for (OrderInvoiceDto invoice : invoiceDtos) {
                OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(invoice.getOrderNo());
                StringBuilder sb = new StringBuilder();
                String companyName=orderBusinessDto.getCompanyName().replaceAll("【[^】]*】", "");
                String packageName=   "发票" + invoice.getOrderNo() + ".zip";
                String invoiceUrl = invoice.getInvoiceUrl();
                if (!invoiceUrl.contains("http")) {
                    invoiceUrl = "http://" + invoiceUrl;
                }
                Integer invoiceType = invoice.getInvoiceType();
                if (invoiceType != null && InvoiceTypeEnum.E_VAT_SPECIAL_TICKET.getId() == invoiceType) {
                    fileType = ".ofd";
                }

                InvoiceDownloadInfoVo invoiceDownloadInfoVo=     InvoiceDownloadInfoVo.builder()
                        .orderNo(invoice.getOrderNo())
                        .rul(invoice.getInvoiceUrl())
                        .fileName(companyName+"_"+invoice.getOrderNo()+fileType)
                        .companyName(companyName)
                        .packageName(packageName)
                        .build();
                invoiceRes.add(invoiceDownloadInfoVo);
            }
            Map<String, Object> result = addResult();
            result.put(DATA, invoiceRes);
            return result;

        } catch (Exception e) {
            logger.error("downloadInvoice {}", orderNo, e);
        }
        return addResult();
    }

    /**
     * 批量下载发票
     */
    @RequestMapping("/downloadInvoice")
    public void downloadInvoice(String orderNo, HttpServletResponse response, HttpServletRequest request) {
        downloadInvoiceV2(orderNo,response,request);
        try {
            if (StringUtils.isEmpty(orderNo)) {
                return;
            }
            OrderBusinessDto order = new OrderBusinessDto();
            order.setOrderNo(orderNo);
            List<OrderInvoiceDto> invoiceDtos = orderBusinessApi.queryInvoice(order);
//            List<OrderInvoiceDto> invoiceDtos = new ArrayList<>();
//            OrderInvoiceDto dto = new OrderInvoiceDto();
//            dto.setInvoiceState(1);
//            dto.setOrderNo("YBM20220807203142158072");
//            dto.setInvoiceUrl("fp.baiwang.com/fp/d?d=2AF46F49138535B94308737F5E7BF42D5992F975F5735A36017D034FAACD0949");
//            invoiceDtos.add(dto);
//
//            OrderInvoiceDto dto2 = new OrderInvoiceDto();
//            dto2.setInvoiceState(1);
//            dto2.setOrderNo("YBM202208072031427777777");
//            dto2.setInvoiceUrl("fp.baiwang.com/fp/d?d=2AF46F49138535B94308737F5E7BF42D5992F975F5735A36017D034FAACD0949");
//            invoiceDtos.add(dto2);

            //获取订单发票url
            List<OrderInvoiceDto> invoiceUrlList = invoiceDtos.stream().filter(x -> x.getInvoiceState() == 1)
                    .collect(Collectors.toList());
            //线上真实：//fp.baiwang.com/fp/d?d=2AF46F49138535B94308737F5E7BF42D5992F975F5735A36017D034FAACD0949
//            String path ="D:"+invoiceFilePatch+ File.separator+"发票"+orderNo;
            String path = invoiceFilePatch + File.separator + "发票" + orderNo;
            File file = new File(path);
            if (!file.exists()) {
                file.mkdirs();
            } else if (file.list().length > 0) {
                FileUtil.deleteDirectory(path);
            }
            String fileType = ".pdf";
            for (OrderInvoiceDto invoice : invoiceUrlList) {
                String invoiceUrl = invoice.getInvoiceUrl();
                if (!invoiceUrl.contains("http")) {
                    invoiceUrl = "http://" + invoiceUrl;
                }
                Integer invoiceType = invoice.getInvoiceType();
                if (invoiceType != null && InvoiceTypeEnum.E_VAT_SPECIAL_TICKET.getId() == invoiceType) {
                    fileType = ".ofd";
                }
                HttpUtil.downloadFile(invoiceUrl, path, invoice.getOrderNo() + fileType);
            }

            //订单文件夹的根目录  首营资料YBM20180820112432000001  药检报告YBM20180820112432000001
            //发票YBM20220807203142158072.zip
//        File zipPathFile = mkDir(reportPatch, orderFileName+orderNo);

            ZipDownloadUtils.zip(path, path + ".zip");

            ZipDownloadUtils.downloadFile(new File(path + ".zip"), request, response, isDelete);

        } catch (Exception e) {
            logger.error("downloadInvoice {}", orderNo, e);
        }
    }

    /**
     * 首营资料和药检报告下载
     * orderNo 订单号
     */
    @RequestMapping("/downloadReport")
    public String downloadReport(@RequestParam("orderNo") String orderNo, @RequestParam("skuId") String skuId, @RequestParam("downloadType") String downloadType,
                                 @RequestParam("progressId") String progressId, HttpServletResponse response, HttpServletRequest request) {
        Map<String, Object> map = new HashMap<>();
        map.put("mag", "fail");
        try {
            String docsPath = reportFilePatch;
            File reportPatch = new File(docsPath);
            if (!reportPatch.exists()) {
                reportPatch.mkdirs();
            }
            List<ReportInformation> reportInformationList = null;

            List<ReportInformationCondition> reportInformationConditionList = getReportInformationConditions(orderNo, skuId, downloadType);
            logger.info("downloadReport request docsPath={},orderNo={},detail={},downloadType={},param={}", new Object[]{docsPath, orderNo, skuId, downloadType, JSON.toJSONString(reportInformationConditionList)});
            //一个订单下的多个商品
            if (reportVersison) {
                reportInformationList = reportInformationService.getReportInformations(reportInformationConditionList);
            } else {
                reportInformationList = reportInformationApi.getReportInformations(reportInformationConditionList);
            }
            logger.info("downloadReport getReportInformations response param={}", JSONArray.toJSONString(reportInformationList));
            String orderFileName = "";
            //1:药检报告    2：首营资料
            int recordType = 0;
            if (downloadType.equals(Constants.DRUGREPORT)) {
                orderFileName = Constants.DRUG_CHECK_REPORT;
                recordType = DownLoadEnum.DOWNLOAD_DRUGREPORT.getType();
            } else {
                orderFileName = Constants.FIRST_CAMP_DATA;
                recordType = DownLoadEnum.DOWNLOAD_FIRSTCAMP.getType();
            }
            //订单文件夹的根目录  首营资料YBM20180820112432000001  药检报告YBM20180820112432000001
            File zipPathFile = mkDir(reportPatch, orderFileName + orderNo);

            //把药检报告/首营质料为空的放到List，然后在文件服务器创建Excel，放到药检报告的文件夹中，一起压缩然后下载到pc客户端
            List<ReportInformation> noUploadReportInformList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(reportInformationList)) {
                long start = System.currentTimeMillis();
                int j = 0;
                int k = 0;
                for (ReportInformation reportInformation : reportInformationList) {
                    k += 1;
                    //是否上传图片 0：未上传 1：上传   未上传的创建Excel
                    if (reportInformation.getIsUpload() == Constants.IS_UPLOAD) {
                        j += 1;
                        reportInformation.setOrderNumber(j);
                        noUploadReportInformList.add(reportInformation);
                    }
                    batchDownloadCosImage(zipPathFile, reportInformation);

                    //从Fastdfs下载到服务器这个过程定为占总下载的50%
                    Double divide = BigDecimalUtils.divide(k, reportInformationList.size());
                    BigDecimal progressValue = new BigDecimal(divide).divide(new BigDecimal(2), 2, BigDecimal.ROUND_HALF_UP);
                    //progressMap.put(progressId,progressValue.doubleValue());
                    redisComponentApi.set(progressId, progressValue.doubleValue());
                }
                long end = System.currentTimeMillis();
                logger.info("从FastDfs下载完成，耗时：" + (end - start) + " ms");
            }
            //progressMap.put(progressId,0.6);
            redisComponentApi.set(progressId, 0.6);
            //创建Execl
            createExcel(orderNo, zipPathFile, noUploadReportInformList);
            //创建Excel的进度定义为占总下载的10%
            //progressMap.put(progressId,0.7);
            redisComponentApi.set(progressId, 0.7);
            //从FASTDFS下载完药检报告/首营资料到服务器临时目录后，开始压缩下载到pc客户端
            long start = System.currentTimeMillis();
            ZipDownloadUtils.zip(zipPathFile.getPath(), docsPath + zipPathFile.getName() + ".zip");
            long end = System.currentTimeMillis();
            logger.info("zip压缩完成，耗时：" + (end - start) + " ms");
            //压缩成zip的进度定义为占总下载的20%
            //progressMap.put(progressId,0.95);
            redisComponentApi.set(progressId, 0.95);
            ZipDownloadUtils.downloadFile(new File(docsPath + zipPathFile.getName() + ".zip"), request, response, true);
            logger.info("下载完成:{}", zipPathFile.getName() + ".zip");
            map.put("msg", "success");
            saveOrderDownloadLog(orderNo, recordType);
        } catch (Exception e) {
            response.setHeader("Set-Cookie", "fileDownload=false; path=/");
            map.put("mag", "fail");
            logger.error("下载药检报告/首营资料异常", e);
        }
        return JSON.toJSONString(map);
    }

    /**
     * 下载药检报告和首营资料组装参数
     *
     * @param orderNo      订单编号
     * @param downloadType 判断是首营资料或药检报告
     * @return
     */
    private List<ReportInformationCondition> getReportInformationConditions(String orderNo, String skuId, String downloadType) {
        List<ReportInformationCondition> reportInformationConditionList = new ArrayList<>();
        ReportInformationCondition reportInformationCondition = new ReportInformationCondition();
        reportInformationCondition.setOrderNo(orderNo);
        //1:药检报告    2：首营资料
        if (downloadType.equals(Constants.DRUGREPORT)) {
            reportInformationCondition.setType(1);
        } else {
            reportInformationCondition.setType(2);
        }
        List<Long> skuIds = new ArrayList<Long>();
        List<OrderDetailBusinessDto> orderDetailBusinessDtos = null;
        //根据单个skuId查询
        if (StringUtil.isNotEmpty(skuId)) {
            skuIds.add(Long.valueOf(skuId));
        } else {
            //根据订单编号查询订单详情表 tb_order_detail 获取商品id
            //orderDetailBusinessDtos = orderDetailBusinessApi.getOrderDetailsByOrderNo(orderNo);
            orderDetailBusinessDtos = orderDetailBusinessApi.getSkuIdOrderDetailsByOrderNo(orderNo);
            for (OrderDetailBusinessDto orderDetailBusiness : orderDetailBusinessDtos) {
                //排除大礼包赠品的skuId,  giftId>0 是赠品
                if (orderDetailBusiness.getGiftId() > 0) {
                    continue;
                }
                skuIds.add(orderDetailBusiness.getSkuId());//商品id
            }
        }
       /* 赠品排除改为在商品接口里面判断
        *排除促销赠品的skuId  详情页面单个下载赠品的不展示下载按钮，所以单个下载不用再调接口判断
        if(StringUtil.isEmpty(skuId)){
            excludeGive(skuIds, orderDetailBusinessDtos.get(0).getBranchCode());
        }*/
        reportInformationCondition.setSkuIds(skuIds);
        reportInformationConditionList.add(reportInformationCondition);
        logger.info("调用下载药监报告和首营资料的参数" + JSON.toJSONString(reportInformationCondition));

        return reportInformationConditionList;
    }

    /**
     * 取消订单
     *
     * @param id
     * @return String
     * @Title: cancelOrder
     * <AUTHOR>
     * @date 2016-12-21 上午11:42:10
     */
    @RequestMapping("/cancelOrder.json")
    @ResponseBody
    public Object cancelOrder(Long id, Integer status, String cancelReason) {
//        if(StringUtils.isEmpty(cancelReason)){
//            return this.addError("取消原因必填！");
//        }
        try {
            //获取登录用户信息
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            //根据ID查询订单
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectById(id);
            //判断如果是当前用户下的订单可进行取消操作
            if (merchant != null && merchant.getId() != null && orderBusinessDto != null && orderBusinessDto.getMerchantId() != null && merchant.getId().equals(orderBusinessDto.getMerchantId())) {
                logger.info("cancelOrder orderNo:{}, merchantId:{}", orderBusinessDto.getOrderNo(), merchant.getId());
                Transaction t2 = CatUtil.initTransaction("order_cancelOrder", "order_cancelOrder");
                boolean result = orderBusinessApi.cancelOrderByCash(id, 1);
                if (result) {
                    //记录操作日志
                    orderOperationLogBusinessApi.saveOrderOperateLogForPCAndAPP(
                            Thread.currentThread().getStackTrace()[1],
                            merchant.getId(),
                            orderBusinessDto.getOrderNo(),
                            OperationEnum.PC_CANCEL, cancelReason);
                    return this.addResult("取消成功！");
                }
                CatUtil.successCat(t2);
            } else {
                logger.info("非法操作！");
            }
        } catch (Exception e) {
            logger.error("取消订单失败", e);

            if (e.getMessage().contains(Constants.XYD_LOAN_CHECK_HAS_PAY_SUCCESS_MSG)) {
                return addError(Constants.XYD_LOAN_CHECK_HAS_PAY_SUCCESS_MSG);
            }

            return this.addError("取消订单失败！");
        }
        return this.addError("取消订单失败！");
    }

    /**
     * 删除订单
     *
     * @param ids
     * @return Object
     * @Title: deleteOrder
     * <AUTHOR>
     * @date 2016-12-21 上午11:45:20
     */
    @RequestMapping("/deleteOrder.htm")
    public ModelAndView deleteOrder(String ids, Integer status) {
        try {
            Transaction t2 = CatUtil.initTransaction("order_deleteOrder", "order_deleteOrder");
            String[] idArray = StringUtil.splitStringToString(ids, ",");
            for (String id : idArray) {
                OrderBusinessDto paramOrder = new OrderBusinessDto();
                paramOrder.setId(Long.valueOf(id));
                //身份验证
                MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
                paramOrder.setMerchantId(merchant.getId());
                logger.error("/deleteOrder.htm,orderId={},merchantId={}", new Object[]{ids, merchant.getId()});
                orderBusinessApi.deleteOrder(paramOrder);
            }
            CatUtil.successCat(t2);
        } catch (Exception e) {
            logger.error("删除订单异常", e);
        }
        return new ModelAndView(new RedirectView("/merchant/center/order/index.htm", true, false));
    }


    /**
     * 订单再次购买
     *
     * @return Object
     * @Title: rebuyForOrder
     * <AUTHOR>
     * @date 2016-12-21 下午1:53:02
     */
    @RequestMapping("/rebuyForOrder.json")
    @ResponseBody
    public Object rebuyForOrder(Long id, String sptype, String spid, String sid, String direct) {
        try {
            Transaction t2 = CatUtil.initTransaction("order_rebuyForOrder", "order_rebuyForOrder");
            if (null == id) {
                throw new XyyEcOrderBizNoneCheckRTException("订单号异常");
            }
            OrderBusinessDto paramOrder = new OrderBusinessDto();
            paramOrder.setId(id);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            paramOrder.setMerchantId(merchant.getId());
            paramOrder.setAccountRole(merchant.getAccountRole());
            paramOrder.setAccountId(merchant.getAccountId());
            String realIP = IPUtils.getClientIP(request);
            paramOrder.setRealIP(realIP);
            logger.info("当前用户请求的IP地址为:" + realIP);
            paramOrder.setSptype(sptype);
            paramOrder.setSpid(spid);
            paramOrder.setSid(sid);
            paramOrder.setDirect(direct);
            ApiRPCResult apiRPCResult = orderBusinessApi.rebuyForOrder(paramOrder);
            if (ApiResultCodeEum.USER_QUALIFICATION_EXCEPTION.getCode() == apiRPCResult.getCode()) {
                return this.addError(apiRPCResult.getErrMsg());
            }
            logger.info("==PC rebuyForOrder,orderId={},operator={}", new Object[]{id, merchant.getId()});
            CatUtil.successCat(t2);
            return this.addResult();
        } catch (Exception e) {
            logger.error("订单再次购买异常", e);
            return this.addError("订单再次购买异常");
        }
    }
    /**
     * 再次购买调用
     * 获取商品是否可购买状态
     */
    @RequestMapping("/getOrderStockState")
    @ResponseBody
    public Object getOrderStockState(RePurchaseDto rePurchase) {
        try {
            ApiRPCResult orderStockState = orderBusinessApi.getOrderStockState(rePurchase);
            if (orderStockState!= null) {
                if (ApiResultCodeEum.SUCCESS.getCode() == orderStockState.getCode()) {
                    OrderStockStateDto data = (OrderStockStateDto) orderStockState.getData();
                    //pc 暂时没有找相似,则替换为 当前订单暂不支持再次购买
                    if (data.getStockCode()==3&&CollectionUtils.isNotEmpty(data.getNoAvailableWareList()) && data.getNoAvailableWareList().size()==1) {
                        return this.addError("当前订单暂不支持再次购买");
                    }
                    return this.addResult("data", orderStockState.getData());
                }
                return this.addError(orderStockState.getErrMsg());
            }
            return this.addError("再次购买异常");
        }catch (Exception e) {
            logger.error("订单再次购买异常", e);
            return this.addError("再次购买异常");
        }
    }

    /**
     * 加入购物车
     */
    @RequestMapping("/cartAdd")
    @ResponseBody
    public Object cartAdd(@RequestBody OrderStockStateDto orderStockStateDto) {
        try {
            ApiRPCResult result = orderBusinessApi.cartAdd(orderStockStateDto);
            if (result!= null) {
                if (ApiResultCodeEum.SUCCESS.getCode() == result.getCode()) {
                    return this.addResult();
                }
                return this.addError(result.getErrMsg());
            }
            return this.addError("加入购物车异常");
        }catch (Exception e) {
            logger.error("加入购物车异常", e);
            return this.addError("加入购物车异常");
        }
    }
    /**
     * 转账说明
     *
     * @return
     */
    @RequestMapping("/offline_instruction.htm")
    public ModelAndView offlineInstruction(Long id) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        String branchCode = merchant.getRegisterCode();
        OrderBusinessDto order = orderBusinessApi.selectById(id);
        Map<String, Object> resMap = new HashMap<>();
        Map<String, String> transferInfo = orderBusinessApi.getTransferInfo(order, order.getBranchCode());
        transferInfo.put("merchantId", String.valueOf(merchant.getId()));
        transferInfo.put("orderId", String.valueOf(id));
        transferInfo.put("payChannel", String.valueOf(order.getPayChannel()));
        resMap.putAll(transferInfo);
//        transferInfo.put("orgName", String.valueOf(order.getCompanyName()));
//        transferInfo.put("orgId", String.valueOf(order.getOrgId()));
//        transferInfo.put("orderNo", String.valueOf(order.getOrderNo()));
//        transferInfo.put("isThirdCompany", String.valueOf(order.getIsThirdCompany()));

        InvoiceTypeBussinessDto invoiceTypeBussinessDto = invoiceBussinessCrmApi
                .queryInvoiceTypeById(merchant.getId());
        String invoinceTxt = invoiceTypeBussinessDto == null ? "电子普通发票" : invoiceTypeBussinessDto.getName();
        resMap.put("billInfo", invoinceTxt);
        resMap.put("order", order);

        return new ModelAndView("/order/offline_instruction.ftl", resMap);
    }

    /**
     * 统计结算金额
     *
     * @return Object
     * @Title: calcSettleAmount
     */
    @RequestMapping("/calcSettleAmount.json")
    @ResponseBody
    public Object calcSettleAmount(OrderSettleVo order) {
        try {
            // 1. 拼接请求参数
            order.setOrderSource(4);
            order.setAppVersion(1);
            order.setUseBalance(false);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setBranchCode(merchant.getRegisterCode());
            order.setMerchantId(merchant.getId());
            order.setRecalculation(Boolean.TRUE);
            // 2. 调用预结算方法
//            ResultDTO<OrderSettleCommunicationDto> resultDTO = orderService.calcSettleAmount(order);
            ResultDTO<SettleVO> settleVOResultDTO = orderService.calcSettleAmountForRefactor(order);

            if (!settleVOResultDTO.getIsSuccess()) {
                return null;
            }

//            Map<String, Object> resultMap = orderService.setCalcSettleAmount(resultDTO.getData(), order.getBranchCode());
            return orderService.setCalcSettleAmount(settleVOResultDTO.getData(), order.getBranchCode());
        } catch (Exception se) {
            se.printStackTrace();
        }
        return null;
    }


    @RequestMapping("/showInvoince.json")
    @ResponseBody
    public Object showInvoince(Long orderId) {
        try {
            ModelMap modelMap = new ModelMap();
            Map<String, Object> map = invoiceBusinessApi.queryInvoceList(orderId);
            logger.info("showInvoince:{}", JSONObject.toJSONString(map));
            if (map != null && map.get("data") != null) {
                ResponseOrderInfo invoinceInfo = (ResponseOrderInfo) map.get("data");
                modelMap.put("invoinceInfo", invoinceInfo);
            }

            return modelMap;
        } catch (Exception e) {
            logger.error("系统异常", e);
            return this.addError("系统异常");
        }
    }

    /**
     * 支付页面检查订单
     *
     * @param orderNo
     * @return
     * @throws Exception
     */
    @RequestMapping("/checkOrderGenerate.json")
    @ResponseBody
    public Object checkOrderGenerate(String orderNo) throws Exception {
        if (StringUtils.isEmpty(orderNo)) {
            return this.addError("订单编号不能为空");
        }
        try {
            String success = orderBusinessApi.getGenerateOrderSuccess(orderNo);
            logger.info("checkOrderGenerate:{}",orderNo,success);
            if (success == null) {
                return this.addError("");
            }

            if ("1".equals(success)) {
                MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
                OrderBusinessDto order = orderBusinessApi.selectByOrderNo(orderNo);
                if (order != null && merchant.getId().equals(order.getMerchantId())) {
                    return this.addResult("id", order.getId());
                } else {
                    return this.addError("请前往我的订单查看");
                }
            } else {
                return this.addError("订单生成失败,请稍后重试");
            }
        } catch (Exception e) {
            logger.error("校验订单是否生成异常", e);
            return this.addError("校验生成订单异常");
        }
    }

    /**
     * 购销合同
     *
     * @return
     */
    @RequestMapping("/purchases_contract.html")
    public String purchasesContract(ModelMap modelMap, HttpServletRequest request) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        Long merchantId = 0L;
        if (merchant != null) {
            merchantId = merchant.getId();
        }
        String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
        modelMap.put("branchCode", branchCode);
        return "/order/purchases_contract.ftl";
    }


    /**
     * 入库价导出
     *
     * @param id
     * @param response
     */
    @RequestMapping("/exportPurchase")
    @ResponseBody
    public void exportPurchase(Long id, HttpServletResponse response) {
        MyOrderInfoBusinessDto order = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order = orderBusinessApi.selectOrderDetail(merchant.getId(), id, OrderDetailEnum.DELIVERING.getCode());
            List<OrderDetailBusinessDto> orderDetailList = new ArrayList<OrderDetailBusinessDto>();
            List<ActivityPackageModel> tempActivityPackageList = order.getPackageList();
            if (CollectionUtil.isNotEmpty(tempActivityPackageList)) {
                for (ActivityPackageModel activityPackage : tempActivityPackageList) {
                    orderDetailList.addAll(activityPackage.getOrderDetailList());
                }
            }

            List<OrderDetailBusinessDto> tempOrderDetailList = order.getDetailList();
            if (CollectionUtil.isNotEmpty(tempOrderDetailList)) {
                orderDetailList.addAll(tempOrderDetailList);
            }
            for (OrderDetailBusinessDto orderDetail : orderDetailList) {
                BigDecimal discountAmountTmp = orderDetail.getDiscountAmount().divide(new BigDecimal(orderDetail.getProductAmount()), RoundingMode.HALF_UP);
                orderDetail.setDiscountAmount(discountAmountTmp.setScale(2, RoundingMode.HALF_UP));

                BigDecimal useBalanceAmountTmp = orderDetail.getUseBalanceAmount().divide(new BigDecimal(orderDetail.getProductAmount()), RoundingMode.HALF_UP);
                orderDetail.setUseBalanceAmount(useBalanceAmountTmp.setScale(2, RoundingMode.HALF_UP));
                BigDecimal balanceAmountTmp = orderDetail.getBalanceAmount().divide(new BigDecimal(orderDetail.getProductAmount()), RoundingMode.HALF_UP);
                orderDetail.setBalanceAmount(balanceAmountTmp.setScale(2, RoundingMode.HALF_UP));
                //成本价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）/数量 - 返利/数量；
                BigDecimal productPrice = orderDetail.getProductPrice();
                BigDecimal costPrice = productPrice.subtract(discountAmountTmp).subtract(balanceAmountTmp).setScale(4, BigDecimal.ROUND_HALF_UP);
                orderDetail.setCostPrice(costPrice);
                orderDetail.setPurchasePrice(orderDetail.getPurchasePrice().setScale(6, BigDecimal.ROUND_HALF_UP));

            }
            String headers = "商品名称,商品规格,生产企业,购买数量,原单价,优惠,余额抵扣,返利金额,实付价,成本价";
            String dataIndexs = "productName,spec,manufacturer,productAmount,productPrice,discountAmount,useBalanceAmount,balanceAmount,purchasePrice,costPrice";
            String styles = "1,1,1,1,1,1,1,1,1,1";
            String alignStyles = "1,1,1,1,1,1,1,1,1,1";
            String widths = "10000,4000,10000,3000,3000,2000,3000,3000,2000,2000";
            UtilOrganizeExcelData.listToExcel(response, null, headers, dataIndexs, styles, alignStyles, widths, orderDetailList, order.getOrderNo() + "入库价");
        } catch (Exception e) {
            logger.error("导出入库明细异常", e);
        }
    }

    /**
     * 取消退款单操作
     *
     * @return
     */
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/cancelRefundOrder")
    @ResponseBody
    public Object cancelRefundOrder(@RequestParam(value = "refundOrderNo", required = false) String refundOrderNo,
                                    @RequestParam(value = "cancelChannel", required = false) Integer cancelChannel,
                                    @RequestParam(value = "refundOrderId", required = false) Long refundOrderId) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (refundOrderId == null && refundOrderNo == null) {
                return this.addError("请传入有效参数");
            }

            OrderRefundBusinessDto orderRefund;
            // 根据情况选择对应的退款单数据是根据退款单号还是退款单id进行查询
            if (StringUtil.isNotEmpty(refundOrderNo)) {
                orderRefund = orderRefundBusinessApi.selectByRefundOrderNo(refundOrderNo);
            } else {
                orderRefund = orderRefundBusinessApi.selectById(refundOrderId);
            }
            if (orderRefund.getInterceptStatus() == 1 && orderRefund.getRefundFlag() == 1) {
                return this.addError("订单已拦截成功，不支持取消退款");
            }
            if (Objects.nonNull(cancelChannel)){
                orderRefund.setCancelChannel(cancelChannel);
            }
            boolean flag = orderRefundBusinessApi.cancelRefundOrder(orderRefund, merchant.getId());
            // 神农交互,神农交互启用后这里进行释放
            if (flag) {
                // 对erp的订单进行解挂操作
                shenNongOrderApi.hangUpAndHangOut(orderRefund.getOrderNo(), -1);
                // 生成退款单之后将退款单推送至ERP
//                shenNongOrderApi.pushEcOrderRefundAndOrderRefundDetailToGodErp(orderRefund.getRefundOrderNo());
                shenNongOrderApi.updateErpRefundOrderAuditStatus(orderRefund.getRefundOrderNo(), -1);
                // 取消退款单之后将工单取消
                csOrderRefundApi.expireWorkOrderByRefundApply(orderRefund.getRefundOrderNo());
            }
            return this.addDataResult("msg", "取消退款成功");
        } catch (Exception e) {
            logger.error("取消退款单异常" + e.toString(), e);
            return this.addError("取消退款单失败,失败原因:" + e.getMessage(), e);
        }
    }

    /**
     * 查询订单状态
     *
     * @return
     */
    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/findOrderStatus")
    @ResponseBody
    public Object cancelRefundOrder(@RequestParam(value = "orderId", required = false) Long orderId) {
        try {
            if (orderId == null) {
                return this.addError("订单id不能为空");
            }
            OrderBusinessDto order = orderService.selectByPrimaryKey(orderId);
            if (order == null) {
                return this.addError("该订单不存在");
            }

            OrderTmpDto orderTmpDto = shenNongOrderApi.selectErpOrderByOrderNo(order.getOrderNo());
            if (null != orderTmpDto) {
                if (OrderTmpDto.ALREADY_PULL == Integer.parseInt(orderTmpDto.getState()) && order.getStatus() == Constants.IS1) {
                    return this.addError("当前订单审核中，不允许发起退款。如需退款请咨询客服热线400-0505-111，给您带来的不便尽情谅解；");
                }
            }
            return this.addResult("order", order);
        } catch (Exception e) {
            logger.error("取消退款单异常" + e.toString(), e);
            return this.addError("取消退款单失败,失败原因:" + e.getMessage(), e);
        }
    }

    @RequestMapping("/cacheGenerateOrder.htm")
    public Object cacheGenerateOrder(ConfirmOrderVo order, String token, String tranNo, ModelMap modelMap, RedirectAttributes redirectAttributes) throws Exception {
        String orderNo = order.getOrderNo();
        if (StringUtils.isEmpty(orderNo)) {
            modelMap.put("errorMsg", "订单编号不能为空");
            return new ModelAndView("/order/paywait.ftl");
        }

        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        final String branchCode = merchant.getRegisterCode();
        addDataToModelMap(branchCode, modelMap);
        modelMap.put("orderNo", orderNo);
        modelMap.put("jumpType", order.getJumpType());
        modelMap.put("tranNo", tranNo);
        modelMap.put("token", token);
        String flag = orderBusinessApi.getGenerateOrder(orderNo);
        if (flag == null) {
            modelMap.put("errorMsg", "订单编号不合法");
            return new ModelAndView("/order/paywait.ftl");
        } else if ("1".equals(flag)) {
            return new ModelAndView("/order/paywait.ftl");
        }

        Order dataOrder = orderService.getOrderByOrderNo(orderNo);
        //在刷新过程中，如果改订单号生成了订单，则跳转到订单列表
        if (dataOrder != null) {
            return new ModelAndView(new RedirectView("/merchant/center/order/index.htm", true, false));
        }

        return new ModelAndView("/order/paywait.ftl");
    }


    /**
     * 获取物流信息
     *
     * @return
     */
    @RequestMapping(value = "/getLogisticsInfo")
    @ResponseBody
    public Object getLogisticsInfo() {
        Map<Integer, String> map = new HashMap<>();
        try {
            List<ThirdLogisticsInfoDTO> thirdLogisticsInfos = orderDetailBusinessApi.getThirdLogisticsInfo();
            return thirdLogisticsInfos;
        } catch (Exception e) {
            logger.error("获取物流信息异常", e);
            return this.addError("邮寄信息回显异常");
        }
    }


    /**
     * 邮寄信息回显
     */
    @RequestMapping(value = "/getPostMailInfo")
    @ResponseBody
    public Object getPostMailInfoDTO(String orderNo) {
        try {
            if (StringUtil.isEmpty(orderNo)) {
                return this.addError("请传入有效参数");
            }
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(orderNo);
            if (null == orderBusinessDto) {
                return this.addError("订单编号不合法");
            }
            PostMailInfoDTO postMailInfo = orderDetailBusinessApi.getPostMailInfoDTO(orderBusinessDto.getOrgId(), orderBusinessDto.getMerchantId());
            if (null != postMailInfo) {
                postMailInfo.setAreaOrgId(orderBusinessDto.getOrgId());
                postMailInfo.setMerchantCode(String.valueOf(orderBusinessDto.getMerchantId()));//客户ID 用户id
                postMailInfo.setMerchantId(orderBusinessDto.getMerchantId());
            }
            logger.info("邮寄信息回显getPostMailInfoDTO{}", JSON.toJSONString(postMailInfo));
            return this.addResult("data", postMailInfo);
        } catch (Exception e) {
            logger.error("邮寄信息回显异常", e);
            return this.addError("邮寄信息回显异常");
        }
    }


    /***
     * 邮寄凭证资料保存接口
     */
    @RequestMapping(value = "/addPostMailInfo")
    @ResponseBody
    public Object addPostMailInfo(PostMailInfoDTO postMailInfo) {
        try {
            if (postMailInfo == null) {
                return this.addError("请传入有效参数");
            }
            if (StringUtil.isEmpty(postMailInfo.getOrderNo())) {
                return this.addError("订单编号不能为null");
            }
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(postMailInfo.getOrderNo());
            MerchantBussinessDto merchantBussinessDto = merchantBussinessApi.findMerchantBaseInfoForApp(orderBusinessDto.getMerchantId());
            postMailInfo.setType(merchantBussinessDto.getBusinessType().byteValue());//商户类型（业务类型） 客户类型
            postMailInfo.setMobile(orderBusinessDto.getMobile());
            postMailInfo.setContactUser(orderBusinessDto.getContactor());//客户名称
            postMailInfo.setMerchantName(orderBusinessDto.getMerchantName());
            postMailInfo.setBranchCode(merchantBussinessDto.getRegisterCode());
            postMailInfo.setMerchantId(orderBusinessDto.getMerchantId());
            postMailInfo.setOrgId(postMailInfo.getAreaOrgId());

            logger.info("调用邮寄凭证资料保存接口入参{}", JSON.toJSONString(postMailInfo));

            ApiRPCResult rpcResult = orderDetailBusinessApi.savePostMailInfoNew(postMailInfo);
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                logger.info("邮寄凭证资料保存接口返回错误信息" + this.addError(rpcResult.getMsg()));
                return this.addError("msg", rpcResult.getMsg());
            }
            return this.addResult("SUCCESS");

        } catch (Exception e) {
            logger.error("邮寄凭证资料保存异常", e);
            return this.addError(e.getMessage());
        }
    }


    // 页面中没有使用此接口
//    /**
//     * 判断商户是否开户
//     */
//    @RequestMapping(value = "/findAccountIsOpen")
//    @ResponseBody
//    public Object findAccountIsOpen(String orgId){
//        try{
//            if(StringUtil.isEmpty(orgId)){
//                this.addError("请传入有效参数");
//            }
//            PostMailInfoDTO postMailInfoDTO = orderDetailBusinessApi.findAccountIsOpen(orgId);
//            logger.info("判断商户是否开户findAccountIsOpen{}",JSON.toJSONString(postMailInfoDTO));
//            return this.addResult("data",postMailInfoDTO);
//        }catch(Exception e){
//            logger.error("判断商户是否开户异常", e);
//            return this.addError(e.getMessage());
//        }
//    }

    @RequestMapping("/cancelBigGiftPackage.json")
    @ResponseBody
    public Object cancelBigGiftPackage(Long merchantId, Long giftId) {
        logger.info("拒绝会员大礼包，merchantId：{}，giftId:{}", new Object[]{merchantId, giftId});
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Boolean falg = orderBusinessApi.updateBigGiftPackageStatus(giftId, merchant.getId(), 4, null, null);
            if (falg)
                return this.addResult();
            return this.addError("失败！");
        } catch (Exception e) {
            logger.error("系统异常", e);
            return this.addError("系统异常");
        }
    }

    /**
     * 验证url是否合法
     *
     * @param result
     * @param reportInformation
     * @return false  url不合法
     */
    private boolean judgeUrl(Map<String, Object> result, ReportInformation reportInformation) {
        //一个商品下的多个FastDFS文件路径
        List<String> imageUrlList = reportInformation.getImageUrlList();
        if (CollectionUtil.isNotEmpty(imageUrlList)) {
            //过滤url空串 例如：["https://files.3/4814.jpg", ""],
            imageUrlList.removeAll(Collections.singleton(""));
            for (String strUrl : imageUrlList) {
                if (!strUrl.startsWith(host) && !strUrl.startsWith(reportHost)) {
                    result.put("msg", "illegal");//url是否合法
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 批量下载Cos图片
     *
     * @param zipPathFile
     * @param reportInformation
     * @throws Exception
     */
    private void batchDownloadCosImage(File zipPathFile, ReportInformation reportInformation) throws Exception {
        //FastDFS文件路径
        List<String> imageUrlList = reportInformation.getImageUrlList();
        if (CollectionUtil.isNotEmpty(imageUrlList)) {
            //过滤url空串 例如：["https://files.3/4814.jpg", ""],
            imageUrlList.removeAll(Collections.singleton(""));
            //文件夹名称 药的名称如：六位地黄丸（10s-2版)
            String folderName = reportInformation.getFolderName();
            String regex = "[</:*?\">|\\\\]";
            folderName = folderName.replaceAll(regex, "-");
            File merchandiseFile = mkDir(zipPathFile, folderName);
            int i = 0;
            for (String url : imageUrlList) {
                i = i + 1;
                //获取文件后缀
                String filenameSuffix = CosUtil.getFilenameSuffix(url);
                //logger.info("文件临时目录path:{},absolutePath:{}",merchandiseFile.getPath(),merchandiseFile.getAbsolutePath());
                File downFile = new File(merchandiseFile.getPath() + File.separator + folderName + i + filenameSuffix);
                CosUtil.downloadFile(url, downFile);
            }
        }
    }

    /**
     * 创建Excel
     *
     * @param orderNo
     * @param zipPathFile
     * @param noUploadReportInformList
     * @throws ServiceException
     */
    private void createExcel(String orderNo, File zipPathFile, List<ReportInformation> noUploadReportInformList) throws ServiceException {
        if (CollectionUtil.isNotEmpty(noUploadReportInformList)) {
            String headers = "序号,商品名称,商品规格,生产厂家,批号";
            String dataIndexs = "orderNumber,productName,spec,manufacturer,batchNumber";
            String styles = "1,1,1,1,1,1,1,1,1";
            String alignStyles = "1,1,1,1,1,1,1,1,1";
            String widths = "10000,4000,10000,3000,3000,2000,3000,3000,2000";
            UtilOrganizeExcelData.createExcel(zipPathFile.getPath(), null, headers, dataIndexs, styles, alignStyles, widths, noUploadReportInformList, "未上传药检报告商品清单" + orderNo);
        }
    }

    private void createExcelForInvoice(String path, List<OrderInvoiceExtDto> invoiceDtos,String fileName) throws ServiceException {
        if (CollectionUtil.isEmpty(invoiceDtos)) {
            return;
        }
        File reportPatch = new File(path);
        if (!reportPatch.exists()) {
            reportPatch.mkdirs();
        }
        String headers = "商家名称_订单编号";
        String dataIndexs = "combinedColumn";
        String styles = "1";
        String alignStyles = "1";
        String widths = "20000";
        UtilOrganizeExcelData.createExcel(path, null, headers, dataIndexs, styles, alignStyles, widths, invoiceDtos, fileName);
    }

    /**
     * 实时获取下载进度
     *
     * @param progressId
     * @return
     */
    @RequestMapping(value = "/getDownloadProgress")
    @ResponseBody
    public Object getDownloadProgress(@RequestParam("progressId") String progressId) {
        Map<String, Object> result = this.addResult();
        if (redisComponentApi.get(progressId) == null) {
            result.put("msg", "false");
        } else {
            logger.info(progressId + "：百分之：" + redisComponentApi.get(progressId) + "%");
            result.put("data", redisComponentApi.get(progressId));
        }
        return result;
    }


    @RequestMapping("/exportOrders")
    @ResponseBody
    public Object exportOrders(OrderBusinessDto order, String type, HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> rs = null;
        try {
            if (orderExportSwitch) {
                return this.addError("系统临时限流暂不能导出");
            }
            if (order.getStartCreateTime() == null) {
                return this.addError("请选择起始下单时间");
            }
            if (order.getEndCreateTime() == null) {
                return this.addError("请选择截止下单时间");
            }
            long days = DateUtil.daysDiff(order.getStartCreateTime(), order.getEndCreateTime());
            if (days > 60) {
                return this.addError("订单导出时间不能超过两个月");
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

//            String key = String.format("exportOrders_%s", merchant.getId());
//            boolean lock = redisBusinessApi.redisLock(key, 0, 5);
//            if (!lock) {
//                logger.warn("exportOrders no get redisLock :{}", merchant.getId());
//                return this.addError("操作太频繁，请5秒后重试");
//            }
            order.setMerchantId(merchant.getId());
            order.setBranchCode(merchant.getRegisterCode());
            //如果账号角色是子账号
            if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
                order.setAccountId(merchant.getAccountId());
            } else {
                order.setAccountId(null);
            }
            //如果查询待配送，实际查待配送和出库中的数据
            if (order.getStatus() != null) {
                if (order.getStatus() == OrderEnum.OrderMiddleStatus.STATUS_FOR_THE_SHIPPING.getId()) {
                    order.setStatuses(new Integer[]{1, 7});
                    //售后退款91,包含90,91
                } else if (order.getStatus() == OrderEnum.OrderStatus.REFUNDPENDING.getId()) {
//                    order.setStatus(null);
                    order.setAuditStateArray(new Integer[]{-1, 0, 1});
                    //查询配送中(配送中2和已签收)
                } else if (order.getStatus() == Constants.IS2) {
//                    order.setStatus(null);
                    order.setStatuses(new Integer[]{2, 20});

                }
            }
            Transaction t2 = CatUtil.initTransaction("order_exportOrders", "order_exportOrders");
            if (order.getOrderSource() != null && order.getOrderSource() == -1) {
                order.setOrderSourceArray(new Integer[]{-1, 1, 2, 3});
            }
            order.setVisibled(OrderBusinessDto.STATUS_VISIBLED);

            rs = this.addResult();
            if (StringUtils.isNotEmpty(type) && type.equals("1")) {
                //查询数据
                List<MyOrderBusinessDto> myOrderBusinessDtos = new ArrayList<>();
                int pageNum = 1;
                order.setPageSize(100);
                do {
                    order.setPageNum(pageNum);
                    List<MyOrderBusinessDto> temp = orderBusinessApi.exportOrdersData(order);
                    logger.info("exportOrders:pageNum:{},{}",pageNum,temp.size());
                    pageNum ++;
                    if (CollectionUtils.isEmpty(temp)) {
                        break;
                    }
                    myOrderBusinessDtos.addAll(temp);
                } while (pageNum < 25);
                //子账号信息不为空赋值子账号数据
                List<AccountInfoDto> accountInfoList = accountProviderService.querySubAccountList(merchant.getId());
                logger.info("导出子账号信息 - accountInfoList:{}", JSON.toJSONString(accountInfoList));
                if (CollectionUtils.isNotEmpty(accountInfoList)) {
                    Map<Long, AccountInfoDto> subAccountInfoMap = accountInfoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AccountInfoDto::getAccountId, Function.identity(), (a, b) -> a));
                    for (MyOrderBusinessDto myOrderBusinessDto : myOrderBusinessDtos) {
                        if (Objects.nonNull(myOrderBusinessDto.getAccountId()) && Objects.nonNull(subAccountInfoMap.get(myOrderBusinessDto.getAccountId()))) {
                            AccountInfoDto accountInfoDto = subAccountInfoMap.get(myOrderBusinessDto.getAccountId());
                            myOrderBusinessDto.setSubAccountMobile(accountInfoDto.getMobile());
                            myOrderBusinessDto.setSubAccountName(accountInfoDto.getAccountName());
                        }
                    }
                }
                //如果有子账号 则导出子账号名称,子账号手机号，如果没有 不导出
                if (CollectionUtils.isNotEmpty(accountInfoList)) {
                    logger.info("导出子账号信息");
                    String headers = "订单编号,店铺名,渠道,下单时间,客户名称,收货地址,收货人,手机号,总金额,优惠金额,余额抵扣金额,实付金额,订单状态,支付方式,退款状态,退款金额,客户留言,现金实付金额,购物金实付金额,企业名称,子账号名称,子账号手机号,借据号";
                    String dataIndexs = "orderNo,companyName,channelCodeText,createDate,merchantName,address,contactor,mobile,totalAmount,discount,balanceUse,money,statusName,payTypeName,hasRefundText,refundAmount,remark,cashPayAmount,virtualGold,orgName,subAccountName,subAccountMobile,channelOrderNo";
                    String styles = "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1";
                    String alignStyles = "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1";
                    String widths = "7000,4000,4000,6000,6000,11000,4000,4000,4000,4000,4000,4000,4000,4000,4000,4000,10000,4000,4000,4000,4000,4000,5000";
                    UtilOrganizeExcelData.listDataToExcel(request, response, null, headers, dataIndexs, styles, alignStyles, widths, myOrderBusinessDtos, "药帮忙订单列表");
                } else {
                    logger.info("不导出子账号信息");
                    String headers = "订单编号,店铺名,渠道,下单时间,客户名称,收货地址,收货人,手机号,总金额,优惠金额,余额抵扣金额,实付金额,订单状态,支付方式,退款状态,退款金额,客户留言,现金实付金额,购物金实付金额,企业名称,借据号";
                    String dataIndexs = "orderNo,companyName,channelCodeText,createDate,merchantName,address,contactor,mobile,totalAmount,discount,balanceUse,money,statusName,payTypeName,hasRefundText,refundAmount,remark,cashPayAmount,virtualGold,orgName,channelOrderNo";
                    String styles = "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1";
                    String alignStyles = "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1";
                    String widths = "7000,4000,4000,6000,6000,11000,4000,4000,4000,4000,4000,4000,4000,4000,4000,4000,10000,4000,4000,4000,5000";
                    UtilOrganizeExcelData.listDataToExcel(request, response, null, headers, dataIndexs, styles, alignStyles, widths, myOrderBusinessDtos, "药帮忙订单列表");
                }
            } else {
                //查询数据条数
                Integer total = orderBusinessApi.exportOrdersDataCount(order);
                rs.put("data", total);
            }
        } catch (Exception e) {
            rs = this.addError("订单导出失败");
            logger.error("订单导出失败原因", e);
        }
        return rs;
    }

    @RequestMapping("/exportOrderDetails")
    @ResponseBody
    public Object exportOrderDetails(OrderBusinessDto order, String type, HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> rs = null;
        try {
            if (orderExportSwitch) {
                return this.addError("系统临时限流暂不能导出");
            }
            if (order.getStartCreateTime() == null) {
                return this.addError("请选择开始时间");
            }
            if (order.getEndCreateTime() == null) {
                return this.addError("请选择结束时间");
            }
            long days = DateUtil.daysDiff(order.getStartCreateTime(), order.getEndCreateTime());
            if (days > 60) {
                return this.addError("订单导出时间不能超过两个月");
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

//            String key = String.format("exportOrders_%s", merchant.getId());
//            boolean lock = redisBusinessApi.redisLock(key, 0, 5);
//            if (!lock) {
//                logger.warn("exportOrderDetail no get redisLock :{}", merchant.getId());
//                return this.addError("操作太频繁，请5秒后重试");
//            }
            order.setMerchantId(merchant.getId());
            order.setBranchCode(merchant.getRegisterCode());
            //如果账号角色是子账号
            if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
                order.setAccountId(merchant.getAccountId());
            } else {
                order.setAccountId(null);
            }
            //如果查询待配送，实际查待配送和出库中的数据
            if (order.getStatus() != null) {
                if (order.getStatus() == OrderEnum.OrderMiddleStatus.STATUS_FOR_THE_SHIPPING.getId()) {
                    order.setStatuses(new Integer[]{1, 7});
                    //售后退款91,包含90,91
                } else if (order.getStatus() == OrderEnum.OrderStatus.REFUNDPENDING.getId()) {
//                    order.setStatus(null);
                    order.setAuditStateArray(new Integer[]{-1, 0, 1});
                    //查询配送中(配送中2和已签收)
                } else if (order.getStatus() == Constants.IS2) {
//                    order.setStatus(null);
                    order.setStatuses(new Integer[]{2, 20});

                }
            }
            if (order.getOrderSource() != null && order.getOrderSource() == -1) {
                order.setOrderSourceArray(new Integer[]{-1, 1, 2, 3});
            }
            order.setVisibled(OrderBusinessDto.STATUS_VISIBLED);

            rs = this.addResult();
            if (StringUtils.isNotEmpty(type) && type.equals("1")) {
                //查询数据
                List<MyOrderDetailBusinessDto> myOrderBusinessDtos = new ArrayList<>();
                int pageNum = 1;
                order.setPageSize(100);
                do {
                    order.setPageNum(pageNum);
                    List<MyOrderDetailBusinessDto> temp = orderBusinessApi.exportOrderDetailsData(order);
                    logger.info("exportDetailOrders:pageNum:{},{}",pageNum,temp.size());
                    pageNum ++;
                    if (CollectionUtils.isEmpty(temp)) {
                        break;
                    }
                    myOrderBusinessDtos.addAll(temp);
                } while (pageNum < 25);

                //子账号信息不为空赋值子账号数据
                List<AccountInfoDto> accountInfoList = accountProviderService.querySubAccountList(merchant.getId());
                logger.info("导出子账号信息 - accountInfoList:{}", JSON.toJSONString(accountInfoList));
                if (CollectionUtils.isNotEmpty(accountInfoList)) {
                    Map<Long, AccountInfoDto> subAccountInfoMap = accountInfoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AccountInfoDto::getAccountId, Function.identity(), (a, b) -> a));
                    for (MyOrderDetailBusinessDto myOrderBusinessDto : myOrderBusinessDtos) {
                        if (Objects.nonNull(myOrderBusinessDto.getAccountId()) && Objects.nonNull(subAccountInfoMap.get(myOrderBusinessDto.getAccountId()))) {
                            AccountInfoDto accountInfoDto = subAccountInfoMap.get(myOrderBusinessDto.getAccountId());
                            myOrderBusinessDto.setSubAccountMobile(accountInfoDto.getMobile());
                            myOrderBusinessDto.setSubAccountName(accountInfoDto.getAccountName());
                        }
                    }
                }
                //如果有子账号 则导出子账号名称,子账号手机号，如果没有 不导出
                if (CollectionUtils.isNotEmpty(accountInfoList)) {
                    logger.info("导出子账号信息");
                    String headers = "订单编号,店铺名称,下单时间,支付时间,收货地址,收货人,状态,支付类型,支付渠道,发货时间,完成时间,商品编号,商品名称,生产厂家,规格,商品价格,商品数量,商品总额,总优惠金额,实付金额,生产许可证号或备案凭证编号,医疗器械注册证或备案凭证编号,结构及组成,产品技术要求编号,企业名称,子账号名称,子账号手机号,借据号";
                    String dataIndexs = "orderNo,companyName,createDate,payDate,address,contactor,statusName,payTypeName,payChannelName,shipDate,finishDate,barcode,productName,manufacturer,spec,productPrice,productAmount,subTotal,discountAmount,realPayAmount,manufacturingLicenseNo,approvalNumber,component,technicalRequirementNo,orgName,subAccountName,subAccountMobile,channelOrderNo";
                    String styles = "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1";
                    String alignStyles = "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1";
                    String widths = "8000,8000,6000,6000,9000,4000,4000,4000,4000,6000,6000,4000,6000,8000,8000,4000,4000,4000,4000,4000,4000,4000,4000,4000,4000,4000,4000,5000";
                    UtilOrganizeExcelData.listDataToExcel(request, response, null, headers, dataIndexs, styles, alignStyles, widths, myOrderBusinessDtos, "药帮忙订单明细列表");
                } else {
                    logger.info("不导出子账号信息");
                    String headers = "订单编号,店铺名称,下单时间,支付时间,收货地址,收货人,状态,支付类型,支付渠道,发货时间,完成时间,商品编号,商品名称,生产厂家,规格,商品价格,商品数量,商品总额,总优惠金额,实付金额,生产许可证号或备案凭证编号,医疗器械注册证或备案凭证编号,结构及组成,产品技术要求编号,企业名称,借据号";
                    String dataIndexs = "orderNo,companyName,createDate,payDate,address,contactor,statusName,payTypeName,payChannelName,shipDate,finishDate,barcode,productName,manufacturer,spec,productPrice,productAmount,subTotal,discountAmount,realPayAmount,manufacturingLicenseNo,approvalNumber,component,technicalRequirementNo,orgName,channelOrderNo";
                    String styles = "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1";
                    String alignStyles = "1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1";
                    String widths = "8000,8000,6000,6000,9000,4000,4000,4000,4000,6000,6000,4000,6000,8000,8000,4000,4000,4000,4000,4000,4000,4000,4000,4000,4000,5000";
                    UtilOrganizeExcelData.listDataToExcel(request, response, null, headers, dataIndexs, styles, alignStyles, widths, myOrderBusinessDtos, "药帮忙订单明细列表");
                }
            } else {
                //查询数据条数
                Integer total = orderBusinessApi.exportOrderDetailsDataCount(order);
                rs.put("data", total);
            }
        } catch (Exception e) {
            rs = this.addError("订单明细导出失败");
            logger.error("订单明细导出失败原因", e);
        }
        return rs;
    }

    @RequestMapping("/voucherMonitor.json")
    @ResponseBody
    public Object voucherMonitor(Order paramOrder) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            logger.info("优惠券检测，MerchantId:{}，voucherIds:{}", new Object[]{merchant.getId(), paramOrder.getVoucherIds()});
            paramOrder.setOrderSource(4);
            paramOrder.setAppVersion(1);
            paramOrder.setUseBalance(false);
            paramOrder.setMerchantId(merchant.getId());
            paramOrder.setBranchCode(merchant.getRegisterCode());
            ResultDTO<String> response = orderService.voucherMonitor(paramOrder, BizSourceEnum.B2B.getKey());

            if (!response.getIsSuccess()) {
                return ServiceResponse.failure(response.getErrorMsg());
            }

            return ServiceResponse.success();
        } catch (Exception e) {
            logger.error("com.xyy.ec.pc.controller.OrderController.voucherMonitor 异常:", e);
            return ServiceResponse.success();
        }
    }

    /**
     * @description: 实名认证->1. 判定该用户是否需要弹出认证窗口
     * @author: wcwanga
     * @create: 2019/8/21 9:57
     * @param: [orderId]    订单ID
     * @return: java.lang.Object
     **/
    @RequestMapping("/ra/authentication.json")
    @ResponseBody
    public Object authentication(Long orderId) {
        ServiceResponse response = ServiceResponse.success();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            logger.info("实名认证->判定该用户是否需要弹出认证窗口 入参，merchantId：{}，,orderId：{},ip：{}", merchant.getId(), orderId, IPUtils.getClientIP(request));
            response = authenticationBusinessApi.realNameAuthentication(merchant.getId(), IPUtils.getClientIP(request), null, Constants.IS4);
            logger.info("实名认证->判定该用户是否需要弹出认证窗口 返回，{}", response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("实名认证->判定该用户是否需要弹出认证窗口 异常，{}", e.getMessage());
        }
        return addResult("data", response);
    }

    /**
     * @description: 实名认证->2. 认证短信超时，重新发送认证短信
     * @author: wcwanga
     * @create: 2019/8/21 10:20
     * @param: [orderId]    订单ID
     * @return: java.lang.Object
     **/
    @RequestMapping("/ra/authenticationRepeat.json")
    @ResponseBody
    public Object authenticationRepeat(Long orderId) {
        ServiceResponse response;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            logger.info("实名认证->2. 认证短信超时，重新发送认证短信 入参，merchantId：{}，,orderId：{},ip：{}", merchant.getId(), orderId, IPUtils.getClientIP(request));
            response = authenticationBusinessApi.authSmsSend(merchant.getId(), null, null, null, Constants.IS4);
            logger.info("实名认证->2. 认证短信超时，重新发送认证短信 返回，{}", response);
            return addResult("data", response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("实名认证->2. 认证短信超时，重新发送认证短信 异常，{}", e);
            return addError("发送失败，请稍后重试");
        }
    }

    /**
     * @description: 实名认证->3. 验证发送的短信验证码
     * @author: wcwanga
     * @create: 2019/8/21 11:15
     * @param: [orderId,    订单ID
     * smsCode]    短信验证码
     * @return: java.lang.Object
     **/
    @RequestMapping("/ra/smsAuthentication")
    @ResponseBody
    public Object smsAuthentication(Long orderId, String smsCode) {
        ServiceResponse response = ServiceResponse.success();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            logger.info("实名认证->3. 验证发送的短信验证码 入参，merchantId：{},orderId：{}，ip：{}，smsCode：{}", merchant.getId(), orderId, IPUtils.getClientIP(request), smsCode);
            response = authenticationBusinessApi.smsAuthentication(merchant.getId(), null, smsCode, IPUtils.getClientIP(request), null, Constants.IS4);
            logger.info("实名认证->3. 验证发送的短信验证码 返回，{}", response);
        } catch (Exception e) {
            logger.error("实名认证->3. 验证发送的短信验证码 异常，{}", e);
        }
        return addResult("data", response);
    }

    /**
     * @description: 实名认证->4. 更换认证手机号，发送短信
     * @author: wcwanga
     * @create: 2019/8/21 11:50
     * @param: [orderId,    订单ID
     * password,   登录密码
     * phoneNum]  新手机号
     * @return: java.lang.Object
     **/
    @RequestMapping("/ra/changeMobileSmsSend.json")
    @ResponseBody
    public Object changeMobileSmsSend(Long orderId, String password, String phoneNum) {
        ServiceResponse response;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            logger.info("实名认证->4. 更换认证手机号，发送短信 入参，merchantId：{}，,orderId：{},ip：{}", merchant.getId(), orderId, IPUtils.getClientIP(request));
            response = authenticationBusinessApi.changeMobileSmsSend(merchant.getId(), password, phoneNum, null, Constants.IS4);
            logger.info("实名认证->4. 更换认证手机号，发送短信 返回，{}", response);
            return addResult("data", response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("实名认证->4. 更换认证手机号，发送短信 异常，{}", e);
            return addError("发送失败，请稍后重试");
        }
    }

    /**
     * @description: 实名认证->5. 更换认证手机号，提交更换
     * @author: wcwanga
     * @create: 2019/8/21 11:50
     * @param: [orderId,    订单ID
     * password,   登录密码
     * phoneNum,   新手机号
     * smsCode]    短信验证码
     * @return: java.lang.Object
     **/
    @RequestMapping("/ra/changeAuthenticationMobile.json")
    @ResponseBody
    public Object changeAuthenticationMobile(Long orderId, String password, String phoneNum, String smsCode) {
        ServiceResponse response;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            logger.info("实名认证->5. 更换认证手机号，提交更换 入参，merchantId：{}，,orderId：{},ip：{}", merchant.getId(), orderId, IPUtils.getClientIP(request));
            response = authenticationBusinessApi.changeAuthenticationMobile(merchant.getId(), password, phoneNum, smsCode, IPUtils.getClientIP(request), null, Constants.IS4);
            logger.info("实名认证->5. 更换认证手机号，提交更换 返回，{}", response);
            return addResult("data", response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("实名认证->5. 更换认证手机号，提交更换 异常，{}", e);
            return addError("验证失败，请稍后重试");
        }
    }

    /**
     * 判断资质是否能下载
     *
     * @param orgId
     * @return
     */
    @RequestMapping("/judgeQualification")
    @ResponseBody
    public Object judgeDownloadReport(String orgId) {
        Map<String, Object> result = addResult();
        try {
            logger.info("判断资质下载传参orgId:{}", orgId);
            List<QualificationInfoDto> qualificationList = orderBusinessApi.getCorporationQualification(orgId);
            logger.info("判断资质下载返回orgId:{},jsonString:{}", new Object[]{orgId, JSON.toJSONString(qualificationList)});

            //是否有部分资质,如果有，也可下载
            boolean isPartUpload = false;
            if (CollectionUtils.isNotEmpty(qualificationList)) {
                for (QualificationInfoDto information : qualificationList) {
                    //是否上传图片 0：未上传 1：上传
                    if (CollectionUtil.isNotEmpty(information.getUriList())) {
                        isPartUpload = true;
                        break;
                    }
                }
            } else {
                //没有资质可下载
                result.put("msg", "false");
                return result;
            }
            if (isPartUpload) {
                result.put("msg", "true");
                return result;
            } else {
                //没有资质可下载
                result.put("msg", "false");
                return result;
            }
        } catch (Exception e) {
            logger.error("判断资质下载报错", e);
            return this.addError("判断资质下载报错");
        }
    }

    /**
     * 下载卖家资质
     */
    @RequestMapping("/downloadQualification")
    public void downloadQualification(String orgId, HttpServletResponse response, HttpServletRequest request) {
        ZipOutputStream zipOutputStream = null;
        try {
            String companyName = orderBusinessApi.getCompanyNameByOrgId(orgId);
            String orderFileName = "资质文件" + ".zip";
            if (StringUtil.isNotEmpty(companyName)) {
                orderFileName = companyName + orderFileName;
            }
            zipOutputStream = getZipOutputStream(response, ZipDownloadUtils.getFileName(request, orderFileName));
            /**本地测试开始
             * zipOutputStream.putNextEntry(new ZipEntry("eeeeeeeeeeeeee.jpg"));
             fdfsClient.downloadFile(zipOutputStream, "G2/M00/05/99/Cgo01FzQ6ROACcvYAAP35LoXdZA079.jpg");
             本地测试结束**/
            List<QualificationInfoDto> corporationQualificationList = orderBusinessApi.getCorporationQualification(orgId);
            int i = 1;
            if (CollectionUtil.isNotEmpty(corporationQualificationList)) {
                List<String> fileNameList = new ArrayList<>();
                for (QualificationInfoDto qualificationInfoDto : corporationQualificationList) {
                    if (CollectionUtil.isNotEmpty(qualificationInfoDto.getUriList())) {
                        for (String uri : qualificationInfoDto.getUriList()) {
                            if (StringUtil.isNotEmpty(uri)) {
                                String suffix = CosUtil.getFilenameSuffix(uri);
                                String fileName = qualificationInfoDto.getName();
                                if (fileNameList.contains(fileName)) {
                                    fileName = fileName + i;
                                    i++;
                                }
                                fileNameList.add(fileName);
                                String fileNameRegex = getDirNameBySkuName(fileName);
                                String name = fileNameRegex + suffix;
                                zipOutputStream.putNextEntry(new ZipEntry(FileUtil.generatePath(name)));
                                //fdfsClient.downloadFile(zipOutputStream, uri);
                                CosUtil.downloadFile(uri, zipOutputStream);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            response.setHeader("Set-Cookie", "fileDownload=false; path=/");
            logger.error("下载卖家资质异常", e);
        } finally {
            try {
                if (zipOutputStream != null) {
                    zipOutputStream.finish();
                    zipOutputStream.close();
                }
            } catch (Exception e) {
                zipOutputStream = null;
            }

        }
    }

    private ZipOutputStream getZipOutputStream(HttpServletResponse response, String fileName) throws IOException {
        response.setHeader("Set-Cookie", "fileDownload=true; path=/");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + fileName);
        return new ZipOutputStream(response.getOutputStream());
    }

    private String getDirNameBySkuName(String skuName) {
        String regex = "[</:*?\">|\\\\]";
        return skuName.replaceAll(regex, "-");
    }


    /**
     * pop 的药检报告
     *
     * @param orderNo
     * @return
     */
    @RequestMapping("/downPopDrugReport")
    @ResponseBody
    public Object downPopDrugReport(@RequestParam("orderNo") String orderNo, Long merchantId) {
        try {

            List<PopDrugDownDto> dtoList = new ArrayList<>();
            //根据ID查询订单
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(orderNo);
            Long orderMerchantId = orderBusinessDto.getMerchantId();
            if (!permissionCheck(merchantId) || !orderMerchantId.equals(merchantId)) {
                return this.addError("暂无数据");
            }

            List<OrderDetailBusinessDto> orderDetails = orderDetailBusinessApi.getOrderDetailsByOrderNo(orderNo);
            if (CollectionUtils.isEmpty(orderDetails)) {
                logger.info("订单号：{}查询明细失败", orderNo);
                return this.addError("未查询到订单明细");
            }
            ApiRPCResult<List<PopOrderConsignmentDetailDto>> res=   popOrderApi.queryOrderConsignmentDetailList(orderNo);

            logger.info("popOrderApi.queryOrderConsignmentDetailList req:{},res:{}",JSON.toJSONString(orderNo),JSON.toJSONString(res));


            if(res != null &&  res.isSuccess() && CollectionUtils.isNotEmpty(res.getData()) ) {
                Map<Long, List<PopOrderConsignmentDetailDto>> map =   res.getData().stream().collect(Collectors.groupingBy(PopOrderConsignmentDetailDto::getOrderDetailId));
                orderDetails.stream().forEach(item -> {
                    PopDrugDownDto popDrugDownDto = new PopDrugDownDto();
                    popDrugDownDto.setBarcode(item.getBarcode());
                    List<PopOrderConsignmentDetailDto> lists=   map.get(item.getId());
                    if(CollectionUtil.isNotEmpty(lists)) {
                        popDrugDownDto.setBatchCode(lists.get(0).getBatchCode());
                    }
                    //    popOrderApi.queryOrderConsignmentDetailList(orderNo);
                    dtoList.add(popDrugDownDto);
                });
                PopFileResultDto data = popDrugReportApi.downloadFileWithReason(orderBusinessDto.getOrgId(), dtoList);
                logger.info("popDrugReportApi.downloadFileWithReason req:{},res:{}",JSON.toJSONString(dtoList),JSON.toJSONString(data));

                Map<String, Object> result = addResult();
                result.put(DATA, data);

                return result;
            }

            return this.addError("暂无数据");
        } catch (Exception e) {
            logger.error("下载药检报告异常", e);
            return this.addError("暂无数据");
        }
    }
    /**
     * pop 的药检报告
     * @return
     */
    @RequestMapping("/downPopDetailDrugReport")
    @ResponseBody
    public Object downPopDetailDrugReport(@RequestParam("orderDetailId") Long orderDetailId) {
        try {

            List<PopDrugDownDto> dtoList = new ArrayList<>();


            OrderDetailBusinessDto orderDetails = orderDetailBusinessApi.selectById(orderDetailId);


            if (orderDetails== null) {
                logger.info("订单明细号：{}查询明细失败", orderDetailId);
                return this.addError("未查询到订单明细");
            }
            ApiRPCResult<List<PopOrderConsignmentDetailDto>> res=   popOrderApi.queryOrderConsignmentDetailList(orderDetails.getOrderNo());
            if(res != null &&  res.isSuccess() && CollectionUtils.isNotEmpty(res.getData()) ) {
                Map<Long, List<PopOrderConsignmentDetailDto>> map =   res.getData().stream().collect(Collectors.groupingBy(PopOrderConsignmentDetailDto::getOrderDetailId));

                PopDrugDownDto popDrugDownDto = new PopDrugDownDto();
                popDrugDownDto.setBarcode(orderDetails.getBarcode());
                List<PopOrderConsignmentDetailDto> lists=   map.get(orderDetails.getId());
                if(CollectionUtil.isNotEmpty(lists)) {
                    popDrugDownDto.setBatchCode(lists.get(0).getBatchCode());
                }
                //    popOrderApi.queryOrderConsignmentDetailList(orderNo);
                dtoList.add(popDrugDownDto);

                PopFileResultDto data = popDrugReportApi.downloadFileWithReason(orderDetails.getOrgId(), dtoList);
                Map<String, Object> result = addResult();
                result.put(DATA, data);

                return result;
            }

            return this.addError("暂无数据");
        } catch (Exception e) {
            logger.error("下载药检报告异常", e);
            return this.addError("暂无数据");
        }
    }


    /**
     * pop 的首次资质报告下载
     *
     * @param orderNo
     * @return
     */
    @RequestMapping("/downPopFirstSaleQualification")
    @ResponseBody
    public Object downPopFirstSaleQualification(@RequestParam("orderNo") String orderNo, Long merchantId) {
        try {

            //根据ID查询订单
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(orderNo);
            Long orderMerchantId = orderBusinessDto.getMerchantId();
            if (!permissionCheck(merchantId) || !orderMerchantId.equals(merchantId)) {
                return this.addError("暂无数据");
            }
            List<OrderDetailBusinessDto> orderDetails = orderDetailBusinessApi.getOrderDetailsByOrderNo(orderNo);
            if (CollectionUtils.isEmpty(orderDetails)) {
                logger.info("订单号：{}查询明细失败", orderNo);
                return this.addError("未查询到订单明细");
            }
            List<String> barCodes = orderDetails.stream().map(OrderDetailBusinessDto::getBarcode).collect(Collectors.toList());
            PopFileResultDto data = popFirstSaleQualificationApi.downloadFileWithReason(orderBusinessDto.getOrgId(), barCodes);
            Map<String, Object> result = addResult();
            result.put(DATA, data);
            return result;
        } catch (Exception e) {
            logger.error("商品资质下载异常", e);
            return this.addError("商品资质下载");
        }
    }

    /**
     * @param orderNo
     * @return
     */
    @RequestMapping("/getSignaturesDownload")
    @ResponseBody
    public Object getSignaturesDownload(@RequestParam("orderNo") String orderNo, Long merchantId) {
        try {
            //根据ID查询订单
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(orderNo);
            Long orderMerchantId = orderBusinessDto.getMerchantId();
            if (!permissionCheck(merchantId) || !orderMerchantId.equals(merchantId)) {
                return this.addError("暂无数据");
            }

            List<OrderDetailBusinessDto> orderDetails = orderDetailBusinessApi.getOrderDetailsByOrderNo(orderNo);
            if (CollectionUtils.isEmpty(orderDetails)) {
                logger.info("订单号：{}查询明细失败", orderNo);
                return this.addError("未查询到订单明细");
            }
            List<String> barCodes = orderDetails.stream().map(OrderDetailBusinessDto::getBarcode).collect(Collectors.toList());
            //调用ERP的http接口 https://erp.api.test.ybm100.com/api/ec/product/productFirstEnclosure
            Map<String, Object> paramsMap = Maps.newHashMap();
            paramsMap.put("oldProductCodeList", barCodes);
            //共仓逻辑，共仓域修改为主仓域编码
            orderService.shareWarehouseLogic(orderBusinessDto);
            paramsMap.put("ecOrgCode", orderBusinessDto.getBranchCode());
            logger.info("用户【{}】,订单号【{}】调用ERP电子签章入参：{}", merchantId, orderNo, JSON.toJSONString(paramsMap));
            String erpResult = HttpClientUtil.doPost(ERP_HOST + "/api/ec/product/productFirstEnclosure", JSONObject.toJSONString(paramsMap), null);
            logger.info("用户【{}】,订单号【{}】调用ERP电子签章出参：{}", merchantId, orderNo, erpResult);
            Map<String, Object> resultData = new HashMap<>();
            //erp返回值转换
            ErpSignaturesResponse erpSignaturesResponse = JSON.toJavaObject(JSON.parseObject(erpResult), ErpSignaturesResponse.class);
            if (erpSignaturesResponse == null || erpSignaturesResponse.getCode() == 1 || CollectionUtils.isEmpty(erpSignaturesResponse.getResult())) {
                return this.addError("暂无数据");
            }
            List<SignaturesInfo> signaturesInfo = erpSignaturesResponse.getResult().stream().filter(e -> StringUtils.isNotBlank(e.getSignatureImageUrl())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(signaturesInfo)) {
                return this.addError("暂无数据");
            }
            logger.info("用户【{}】,订单号【{}】下载ERP电子签章过滤空地址后的数据：{}", merchantId, orderNo, JSON.toJSONString(signaturesInfo));
            signaturesInfo.forEach(e -> e.setUniqueId(String.valueOf(signaturesInfo.indexOf(e) + 1)));
            Map<String, Object> result = addResult();
            result.put(DATA, signaturesInfo);
            return result;
        } catch (Exception e) {
            logger.error("下载电子签章异常", e);
            return this.addError("暂无数据");
        }
    }

    /**
     * 权限校验
     */
    private Boolean permissionCheck(Long merchantId) {
        try {
            if (merchantId == null) {
                return Boolean.FALSE;
            }
            //权限校验，获取登录用户信息
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return Boolean.FALSE;
            }
            Long loginId = merchant.getId();
            if (loginId != null && loginId.equals(merchantId)) {
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }
        } catch (Exception e) {
            logger.error("权限校验异常，异常信息：{}", e);
            return Boolean.FALSE;
        }
    }

    @RequestMapping("/saveOrderDownloadLog")
    @ResponseBody
    public Object saveOrderDownloadLog(String orderNo, Integer downloadType) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            OrderDownloadLogBusinessDto orderDownloadLogBusinessDto = new OrderDownloadLogBusinessDto();
            orderDownloadLogBusinessDto.setCreateTime(new Date());
            orderDownloadLogBusinessDto.setMerchantId(merchant.getId());
            orderDownloadLogBusinessDto.setMerchantName(merchant.getRealName());
            orderDownloadLogBusinessDto.setOrderNo(orderNo);
            orderDownloadLogBusinessDto.setDownloadContent(downloadType);
            orderDownloadLogBusinessDto.setDownloadTime(new Date());
            orderDownloadLogBusinessApi.insertSelective(orderDownloadLogBusinessDto);
            return addResult();
        } catch (Exception e) {
            logger.error("保存下载日志异常", e);
            return this.addError("保存下载日志异常");
        }
    }
    @PostMapping("/queryConsumeRebateDetail")
    @ResponseBody
    public Object queryConsumeRebateDetail(ConsumeRebateDetailReq param) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            param.setMerchantId(merchant.getId());
            return this.addResultData(marketingService.getMarketingRebateConsumptionReturnInfo(param.getMerchantId(), param.getMoney()));
        } catch (Exception e) {
            logger.info("OrderController.queryConsumeRebateDetail error param {}", JSON.toJSONString(param), e);
            return this.addError(e instanceof AppException ? e.getMessage() : "消费返查询失败");
        }
    }
    @PostMapping("/pay/evidence/submit")
    @ResponseBody
    public Object submitPayEvidence(PayEvidenceVo param) {
        logger.info("OrderController.submitPayEvidence req params {}", JSON.toJSONString(param));
        if (param.getMerchantId() == null) {
            return this.addError("商户ID为空");
        }
        OrderBusinessDto order = orderBusinessApi.selectById(param.getOrderId());
        if (order == null) {
            return this.addError("订单查询失败");
        }
        param.setOrderNo(order.getOrderNo());
        if (CollectionUtils.isEmpty(param.getEvidenceImages())) {
            param.setEvidenceImages(Collections.emptyList());
//            return this.addError("凭证为空");
        }
        // 上传图片不能超过 3 张
        if (param.getEvidenceImages().size() > 3) {
            return this.addError("图片超过上限");
        }
        try {
            ApiRPCResult apiRPCResult = orderBusinessApi.submitPayEvidence(param);
            if (apiRPCResult != null && apiRPCResult.isSuccess()) {
                return this.addResult("success");
            }
        } catch (Exception e) {
            logger.error("OrderController.submitPayEvidence", e);
        }
        return this.addError("提交失败");
    }

    /**
     * 查询出库单PRD URL
     *
     * @param orderNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryOutOrderPdfUrl", method = RequestMethod.POST)
    public Object queryOutOrderPdfUrl(@RequestParam(name = "orderNo") String orderNo,
                                      @RequestParam(name = "branchCode") String branchCode,
                                      @RequestParam(name = "docType") String docType) {
        try {
            List<String> pdfUrl = orderService.queryOutOrderPdfUrl(orderNo, branchCode, docType);
            return this.addResult("data", pdfUrl);
        } catch (Exception e) {
            logger.error("queryOutOrderUrl，orderNo：{}", orderNo, e);
            return this.addError("");
        }
    }

    /**
     * 我的账单-下载对账单
     *
     * @param year
     * @param month
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "purchase/download", method = RequestMethod.POST)
    public Object purchaseOrderDownLoad(@RequestParam(name = "year") String year,
                                        @RequestParam(name = "month") String month,
                                        HttpServletResponse response, HttpServletRequest request) {

        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchantId = merchant.getId();
            //验证邮箱的合法性
            if (merchantId == null) {
                logger.error("merchantId is null");
                return addError("请重新登录");
            }
            if (BooleanUtils.isTrue(isCloseMyPurchaseOrder)) {
                logger.info("关闭我的账单，不返回数据，merchantId：{}", merchantId);
                return addError(CODE_ERROR, "功能维护中，请稍后重试");
            }
            //验证邮箱的合法性
            if (StringUtil.isEmpty(year)) {
                logger.error("purchaseOrderQuery year 为空");
                return addError("查询年为空");
            }
            if (StringUtil.isEmpty(month)) {
                logger.error("purchaseOrderQuery month 为空");
                return addError("查询月为空");
            }
            String key = String.format("purchaseOrderDownLoad_%s_%s_%s", merchantId, year, month);
            boolean lock = redisBusinessApi.redisLock(key, 0, 10);
            if (!lock) {
                logger.warn("purchaseOrderDownLoad no get redisLock :{} {} {}", merchantId, year, month);
                return this.addError("操作太频繁，请10秒后重试");
            }
            String fileName = year + "年" + month + "月采购账单";

            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            String start = year + "-" + month + "-" + "01";
            if ("12".equals(month)) {
                year = String.valueOf(Integer.valueOf(year).intValue() + 1);
            }
            month = Constants.nextMonth.get(month);
            String end = year + "-" + month + "-" + "01";
            Date startTime = df.parse(start);
            Date endTime = df.parse(end);
            orderFeeStatisticsQuerySendDto orderFeeStatisticsQueryDto = new orderFeeStatisticsQuerySendDto();
            orderFeeStatisticsQueryDto.setEndTime(endTime);
            orderFeeStatisticsQueryDto.setStartTime(startTime);
            orderFeeStatisticsQueryDto.setMerchantId(merchantId);
            orderFeeStatisticsQueryDto.setOrderStatusList(Arrays.asList(PopOrderStatusEnum.DELIVERYING.getCode(), PopOrderStatusEnum.SHIPPED.getCode(), PopOrderStatusEnum.STORAGE.getCode(),
                    PopOrderStatusEnum.REFUNDED.getCode(), PopOrderStatusEnum.WAIT_SHIP.getCode()));
            List<String> strings = myOrderDetailForMerchantApi.myOrderVoSendDownLoad(orderFeeStatisticsQueryDto, fileName);

            String extfilename = strings.get(0);
            logger.info("fileName={}", extfilename);
            response.setHeader("Content-type", "application/x-xls; charset=" + "UTF-8");
            extfilename = URLEncoder.encode(extfilename, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment; filename=" + extfilename);
            response.setContentType("application/msexcel");
            OutputStream out = response.getOutputStream();
            CosUtil.downloadFile(strings.get(1), out);
            return this.addResult("data", strings.get(1));
        } catch (Exception e) {
            logger.error("purchaseOrderDownLoad error", e);
            return this.addError("");
        }

    }


    /**
     * 我的账单-采购总额明细（列表）
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryMyPurchaseOrderFee", method = RequestMethod.POST)
    public Object queryMyPurchaseOrderFee(@RequestParam(name = "startTime") String startTime,
                                          @RequestParam(name = "endTime") String endTime,
                                          @RequestParam(name = "pageNo") Integer pageNo,
                                          @RequestParam(name = "pageSize") Integer pageSize,
                                          @RequestParam(name = "queryStr") String queryStr
    ) {
        try {
            logger.info("startTime={},endTime={}", startTime, endTime);
            logger.info("queryStr={}", queryStr);
            logger.info("pageNo={},pageSize={}", pageNo, pageSize);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getId();
            if (BooleanUtils.isTrue(isCloseMyPurchaseOrder)) {
                logger.info("关闭我的账单，不返回数据，merchantId：{}", merchantId);
                return this.addResult("data", new PageInfo<MyPcPurchaseOrderDto>(Lists.newArrayList()));
            }
            MyPcPurchaseQueryOrderDto myPcPurchaseQueryOrderDto = new MyPcPurchaseQueryOrderDto();
            myPcPurchaseQueryOrderDto.setMerchantId(merchantId);
            Date start = DateUtil.string2Date(startTime, DateUtil.PATTERN_STANDARD);
            myPcPurchaseQueryOrderDto.setStartTime(start);
            Date end = DateUtil.string2Date(endTime, DateUtil.PATTERN_STANDARD);
            myPcPurchaseQueryOrderDto.setEndTime(end);
            myPcPurchaseQueryOrderDto.setPageNo(pageNo);
            myPcPurchaseQueryOrderDto.setPageSize(pageSize);
            myPcPurchaseQueryOrderDto.setQueryStr(queryStr);
            PageInfo<MyPcPurchaseOrderDto> myPcPurchaseOrderDtoPageInfo = orderPurchaseStatisticService.orderFeePcByMerchant(myPcPurchaseQueryOrderDto);
            return this.addResult("data", myPcPurchaseOrderDtoPageInfo);
        } catch (Exception e) {
            logger.error("purchaseOrderDownLoad error", e);
            return this.addError("");
        }
    }


    /**
     * 我的账单-采购总额明细（列表-明细）
     *
     * @param orderNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryMyPurchaseOrderDetailFee", method = RequestMethod.POST)
    public Object queryMyPurchaseOrderDetailFee(@RequestParam(name = "orderNo") String orderNo) {
        try {
            List<MyPcPurchaseOrderDetailDto> myPcPurchaseOrderDetailDtoList = orderPurchaseStatisticService.selectMyPurchaseOrderDetailByOrderNo(orderNo);
            return this.addResult("data", myPcPurchaseOrderDetailDtoList);
        } catch (Exception e) {
            logger.error("purchaseOrderDownLoad error", e);
            return this.addError("");
        }
    }


    /**
     * 我的账单-实际退款（列表）
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryMyPurchaseRefundFee", method = RequestMethod.POST)
    public Object queryMyPurchaseRefundFee(@RequestParam(name = "startTime") String startTime,
                                           @RequestParam(name = "endTime") String endTime,
                                           @RequestParam(name = "pageNo") Integer pageNo,
                                           @RequestParam(name = "pageSize") Integer pageSize,
                                           @RequestParam(name = "queryStr") String queryStr) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getId();
            if (BooleanUtils.isTrue(isCloseMyPurchaseOrder)) {
                logger.info("关闭我的账单，不返回数据，merchantId：{}", merchantId);
                return this.addResult("data", new PageInfo<MyPcPurchaseRefundDto>(Lists.newArrayList()));
            }
            MyPcPurchaseQueryRefundDto myPcPurchaseQueryRefundDto = new MyPcPurchaseQueryRefundDto();
            myPcPurchaseQueryRefundDto.setMerchantId(merchantId);
            Date start = DateUtil.string2Date(startTime, DateUtil.PATTERN_STANDARD);
            myPcPurchaseQueryRefundDto.setStartTime(start);
            Date end = DateUtil.string2Date(endTime, DateUtil.PATTERN_STANDARD);
            myPcPurchaseQueryRefundDto.setEndTime(end);
            myPcPurchaseQueryRefundDto.setPageNo(pageNo);
            myPcPurchaseQueryRefundDto.setPageSize(pageSize);
            myPcPurchaseQueryRefundDto.setQueryStr(queryStr);
            PageInfo<MyPcPurchaseRefundDto> myPcPurchaseRefundDtoPageInfo = orderPurchaseStatisticService.selectMyPcPurchaseRefundPageByMerchant(myPcPurchaseQueryRefundDto);
            return this.addResult("data", myPcPurchaseRefundDtoPageInfo);
        } catch (Exception e) {
            logger.error("queryMyPurchaseRefundFee error", e);
            return this.addError("");
        }
    }

    /**
     * 我的账单-实际退款中（列表）
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryMyPurchaseRefundingFee", method = RequestMethod.POST)
    public Object queryMyPurchaseRefundingFee(@RequestParam(name = "startTime") String startTime,
                                              @RequestParam(name = "endTime") String endTime,
                                              @RequestParam(name = "pageNo") Integer pageNo,
                                              @RequestParam(name = "pageSize") Integer pageSize,
                                              @RequestParam(name = "queryStr") String queryStr) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getId();
            if (BooleanUtils.isTrue(isCloseMyPurchaseOrder)) {
                logger.info("关闭我的账单，不返回数据，merchantId：{}", merchantId);
                return this.addResult("data", new PageInfo<MyPcPurchaseRefundingDto>(Lists.newArrayList()));
            }
            MyPcPurchaseQueryRefundingDto myPcPurchaseQueryRefundingDto = new MyPcPurchaseQueryRefundingDto();
            myPcPurchaseQueryRefundingDto.setMerchantId(merchantId);
            Date start = DateUtil.string2Date(startTime, DateUtil.PATTERN_STANDARD);
            myPcPurchaseQueryRefundingDto.setStartTime(start);
            Date end = DateUtil.string2Date(endTime, DateUtil.PATTERN_STANDARD);
            myPcPurchaseQueryRefundingDto.setEndTime(end);
            myPcPurchaseQueryRefundingDto.setPageNo(pageNo);
            myPcPurchaseQueryRefundingDto.setPageSize(pageSize);
            myPcPurchaseQueryRefundingDto.setQueryStr(queryStr);
            PageInfo<MyPcPurchaseRefundingDto> myPcPurchaseRefundingDtoPageInfo = orderPurchaseStatisticService.selectMyPcPurchaseRefundingPageByMerchant(myPcPurchaseQueryRefundingDto);
            return this.addResult("data", myPcPurchaseRefundingDtoPageInfo);
        } catch (Exception e) {
            logger.error("queryMyPurchaseRefundingFee error", e);
            return this.addError("");
        }
    }

    /**
     * 我的账单-退款明细
     *
     * @param refundOrderNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryMyPurchaseRefunddetailFee", method = RequestMethod.POST)
    public Object queryMyPurchaseRefunddetailFee(@RequestParam(name = "refundOrderNo") String refundOrderNo) {
        try {
            List<MyPcPurchaseRefundDetailDto> myPcPurchaseRefundDetailDtoList = orderPurchaseStatisticService.selectMyPurchaseRefundDetailByRefundNo(refundOrderNo);
            return this.addResult("data", myPcPurchaseRefundDetailDtoList);
        } catch (Exception e) {
            logger.error("queryMyPurchaseRefunddetailFee error", e);
            return this.addError("");
        }
    }

    /**
     * 我的账单-退款中明细
     *
     * @param refundOrderNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryMyPurchaseRefundingdetailFee", method = RequestMethod.POST)
    public Object queryMyPurchaseRefundingdetailFee(@RequestParam(name = "refundOrderNo") String refundOrderNo) {
        try {
            List<MyPcPurchaseRefundDetailDto> myPcPurchaseRefundDetailDtoList = orderPurchaseStatisticService.selectMyPurchaseRefundDetailByRefundNo(refundOrderNo);
            return this.addResult("data", myPcPurchaseRefundDetailDtoList);
        } catch (Exception e) {
            logger.error("queryMyPurchaseRefundingdetailFee error", e);
            return this.addError("");
        }
    }

    @Value("${xyy.ec.isCloseMyPurchaseOrder:false}")
    private Boolean isCloseMyPurchaseOrder;

    /**
     * 采购对账单 我的账单
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryMyAccount", method = RequestMethod.POST)
    public Object queryMyAccount(@RequestParam(name = "startTime") String startTime,
                                 @RequestParam(name = "endTime") String endTime) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getId();
            if (BooleanUtils.isTrue(isCloseMyPurchaseOrder)) {
                logger.info("关闭我的账单，不返回数据，merchantId：{}", merchantId);
                return addError(CODE_ERROR, "功能维护中，请稍后重试");
            }
            OrderFeeStatisticsQueryDto orderFeeStatisticsQueryDto = new OrderFeeStatisticsQueryDto();
            orderFeeStatisticsQueryDto.setMerchantId(merchantId);
            Date start = DateUtil.string2Date(startTime, DateUtil.PATTERN_STANDARD);
            orderFeeStatisticsQueryDto.setStartTime(start);
            Date end = DateUtil.string2Date(endTime, DateUtil.PATTERN_STANDARD);
            orderFeeStatisticsQueryDto.setEndTime(end);

            StatisticPcByMerchantDto statisticPcByMerchantDto = null;
            if (pcStatisticsForMysqlSwitch) {
                //新版本查MySQL统计表
                statisticPcByMerchantDto = orderPurchaseStatisticV2Service.orderFeeStatisticPcByMerchantV2(orderFeeStatisticsQueryDto);
            } else {
                //老版本查ES
                statisticPcByMerchantDto = orderPurchaseStatisticService.orderFeeStatisticPcByMerchant(orderFeeStatisticsQueryDto);
            }
            return this.addResult("data", statisticPcByMerchantDto);
        } catch (Exception e) {
            logger.error("queryMyAccount，startTime：{}", startTime, e);
            return this.addError("");
        }
    }

    @RequestMapping("/bill.htm")
    public String bill(HttpServletRequest request) throws Exception {
        return "/order/bill.ftl";
    }


    @ResponseBody
    @RequestMapping(value = "/agreement/instructions", method = RequestMethod.POST)
    public Object agreementInstructions() {
        try {
            String [] arr= agreementInfo.split(",");
            if(arr != null && arr.length>1) {
                Map map = new HashMap();
                map.put("agreementName",arr[0]);
                map.put("agreementUrl",arr[1]);
                map.put("fileId",9999);
                map.put("fileType", SendEmailInfoTypeEnum.GOLD_COLLECTION_INSTRUCTION.getCode());
                return this.addResult("data", map);
            }
            return this.addError("协议地址配置错误");
        } catch (Exception e) {
            logger.error("agreementInstructions", e);
            return this.addError("");
        }
    }
}



