package com.xyy.ec.pc.cms.helpers;

import com.xyy.ec.layout.buinese.ecp.enums.CmsSelectProductStrategyTypeEnum;
import com.xyy.ec.pc.cms.param.CmsSeckillProductExpectQueryParam;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.List;
import java.util.Objects;

/**
 * {@link CmsSeckillProductExpectQueryParam} 帮助类
 *
 * <AUTHOR>
 */
public class CmsSeckillProductExpectQueryParamHelper {

    /**
     * 校验
     *
     * @param expectQueryParam
     * @return
     */
    public static Boolean validate(CmsSeckillProductExpectQueryParam expectQueryParam) {
        // 校验
        if (expectQueryParam == null) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "秒杀查询");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        // 用户侧
        if (expectQueryParam.getMerchantId() == null) {
            throw new AppException(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
        }
        String selectProductStrategyType = expectQueryParam.getSelectProductStrategyType();
        if (StringUtils.isEmpty(selectProductStrategyType)) {
            String message = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "选择商品策略类型");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, message);
        }
        CmsSelectProductStrategyTypeEnum cmsSelectProductStrategyTypeEnum = CmsSelectProductStrategyTypeEnum.valueOfCustom(selectProductStrategyType);
        if (Objects.isNull(cmsSelectProductStrategyTypeEnum)) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "非法的选择商品策略类型");
        }
        if (Objects.equals(cmsSelectProductStrategyTypeEnum, CmsSelectProductStrategyTypeEnum.APPOINT_PRODUCT)) {
            List<Long> specifiedCsuIds = expectQueryParam.getSpecifiedCsuIds();
            if (CollectionUtils.isEmpty(specifiedCsuIds)) {
                String message = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "指定商品ID");
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, message);
            }
        } else if (Objects.equals(cmsSelectProductStrategyTypeEnum, CmsSelectProductStrategyTypeEnum.APPOINT_PRODUCT_GROUP)) {
            String specifiedExhibitionIdStr = expectQueryParam.getSpecifiedExhibitionIdStr();
            if (StringUtils.isEmpty(specifiedExhibitionIdStr)) {
                String message = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "指定商品组");
                throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, message);
            }
        }
        Integer expectedNum = expectQueryParam.getExpectedNum();
        if (Objects.isNull(expectedNum) || expectedNum <= 0) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "不合法的期望数量");
        }
        return true;
    }

}
