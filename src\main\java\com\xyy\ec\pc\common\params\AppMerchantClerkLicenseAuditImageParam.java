package com.xyy.ec.pc.common.params;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppMerchantClerkLicenseAuditImageParam implements Serializable {

    /**
     * 名称
     */
    private String name;

    /**
     * 资质类型编码(ec)
     */
    private String licenseCode;

    /**
     * 资质图片URL列表
     */
    private String licenseImgUrls;

}
