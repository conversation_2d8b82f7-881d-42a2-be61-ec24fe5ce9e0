<!DOCTYPE HTML>
<html>

<head>
	<#include "common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>用户登录-药帮忙</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="/static/css/login-headerAndFooter.css"/>
    <link rel="stylesheet" href="/static/css/comparativePrice/login.css"/>
    <script type="text/javascript" src="/static/js/login.js"></script>
    <script type="text/javascript" src="/static/js/util.js"></script>
    <script type="text/javascript" src="/static/js/zhuge/zhugeio.js?t=${t_v}"></script>
    <script src="/static/js/plugins/jquery.md5.js" type="text/javascript"></script>
</head>

<body>
<div class="container">

    <!--头部导航区域开始-->
<#--<div class="headerBox" id="headerBox">-->
<#--<#include "login_header.ftl" />-->
<#--</div>-->
    <!--头部导航区域结束-->


    <!--主体部分开始-->
    <div class="main">
        <div class="loginmain">
            <div class="loginbox clearfix">
                <div class="login-left-box">
                    <img src="/static/img/comparativePrice/leftBg.png" class="left-bag" >
                </div>

                <div class="log-box"><img src="/static/img/comparativePrice/logo.png" class="w300"></div>
                <div class="l-title"></div>
                <!--错误提示-->
                <div class="err-box">

                <#if errorMsg??>
                    <input type="hidden" value="${errorMsg}" id="error">
                <div class="errinfo">
                <#else>
                    <div class="errinfo noshow">
                </#if>
                    <img src="/static/images/cuowutishi.png" class="errorimg"> <span class="wenantishi"></span>
                </div>
                </div>
                    <!--账号登录-->
                    <div class="l-con">


                        <form class="sui-form form-horizontal sui-validate" action="/login/purchase/login.htm" id="loginForm" method="post" onsubmit="return check(this)">
                            <input type="hidden" name = "redirectUrl" id="redirectUrl" value="${redirectUrl }"/>
                            <input type="hidden" id="registerSource" name="registerSource" value="${registerSource}"/>
                            <input type="hidden" id="hid" name="hid" value="${deviceId}"/>
                            <div class="control-group">
                                <div class="controls">
                                    <div class="spediv">
                                        <img src="/static/images/ren.png" alt="">
                                        <input type="text" id="inputPhone" class="inputPhone" name="name" placeholder="请输入手机号" data-rules="" value="${loginName}">
                                    </div>
                                </div>
                            </div>
                            <div class="control-group jiange">
                                <div class="controls">
                                    <div class="spediv">
                                        <img src="/static/images/suo.png" alt="">
                                        <input type="password" id="inputPassword" class="inputPassword" name="password" placeholder="密码" data-rules="" title="密码">
                                    </div>

                                </div>
                            </div>

                            <div class="control-group">
                                <div class="controls" style="padding: 40px 0px 0px 0px">
                                    <button type="button" onclick="javascript:login();" class="sui-btn btn-primary">登录</button>
                                </div>
                            </div>

                        <#--<div class="wrapbox">-->
                        <#--<label class="checkbox-pretty inline checked" id="remember">-->
                        <#--<input type="checkbox" checked="checked"><span>记住密码</span>-->
                        <#--</label>-->
                        <#--<a href="/login/forgetPassword.htm" class="fr">忘记密码？</a>-->
                        <#--</div>-->

                        </form>
                        <div class="bot-info">
                            还没有账号，立即去
                            <a href="/newstatic/#/register/index" >注册</a>
<#--                            <a href="/login/register.htm?registerSource=${registerSource}" target="_blank">注册</a>-->
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!--主体部分结束-->

        <!--底部导航区域开始-->
    <#--<div class="footer" id="footer">-->
    <#--<#include "login_footer.ftl" />-->
    <#--</div>-->
        <!--底部导航区域结束-->


    </div>

    <script>
        function login(){
            var loginPhone = $('#inputPhone').val();
            var loginPwd = $('#inputPassword').val();
            if(loginPhone != '' && loginPwd != ''){
                if($('#remember').hasClass("checked")){
                    if(!loginPwd.match(/^([a-fA-F0-9]{32})$/)){
                        var md5LoginPwd =  $.md5(loginPwd);
                        $('#inputPassword').val(md5LoginPwd);
                    }
                }else{
                    if(!loginPwd.match(/^([a-fA-F0-9]{32})$/)){
                        var md5LoginPwd =  $.md5(loginPwd);
                        $('#inputPassword').val(md5LoginPwd);
                    }
                }
            }
            $("#loginForm").submit();
        }

        var errorMsg = $("#error").val();
        if(errorMsg!=null){
            $(".l-title").css("display","none");
            $(".err-box").css("display","block");
            $(".wenantishi").text(errorMsg);
        }

        $(document).ready(function() {
            var pressKeyInt = 0;
            /*搜索弹窗响应键盘事件*/
            $("#inputPassword").keyup(function (event) {
                var e = event || window.event;
                var k = e.keyCode || e.which;

                switch (k) {
                    case 13:
                        login();
                        console.log("回车");
                        break;

                }
            });
        });
        $(function () {
            var storage;
            if(window.localStorage){
                storage=window.localStorage;
                storage.removeItem("comparativePrice");
            }
            //诸葛IO埋点
            function zhugeio() {
                if(merchantio){
                    var channelStr = '';
                    var merchatChannelList = merchantio.channelList;
                    if (merchatChannelList && Array.isArray(merchatChannelList)) {
                        var channelCodeNameMap = {'1':'B2B','2':'宜块钱'};
                        var channelCodeName;
                        for (var i = 0; i < merchatChannelList.length; i ++) {
                            channelCodeName = channelCodeNameMap[merchatChannelList[i]] || '';
                            if (channelCodeName) {
                                channelStr += channelCodeName + ',';
                            }
                        }
                        if (channelStr && channelStr.length > 0) {
                            channelStr = channelStr.substring(0, channelStr.length-1);
                        }
                    }
                    channelStr="CGB";
                    zhuge.setSuperProperty({
                        'channelCode': channelStr
                    });
                    zhuge.identify(merchantio.id,{
                        name:merchantio.nickname,
                        loginName :merchantio.loginName,
                        phoneNum : merchantio.mobile,
                        nickname :merchantio.nickname,
                        realName :merchantio.realName,
                        registeredDate:merchantio.createTime,
                        lastLoginTime:merchantio.lastLoginTime,
                        businessType:merchantio.businessType,
                        businessTypeName:merchantio.businessTypeName,
                        provinceId:merchantio.provinceCode,
                        provinceName:merchantio.province,
                        cityId:merchantio.cityCode,
                        cityName:merchantio.city,
                        districtId:merchantio.areaCode,
                        districtName:merchantio.district,
                        address:merchantio.address,
                        channelCode:channelStr
                    });
                }else {
                    //游客
                    zhuge.identify('0',{
                        name:"游客"
                    });
                }
            };
        })
    </script>
</body>

</html>