package com.xyy.ec.pc.newfront.vo;

import com.xyy.ec.shop.server.business.results.ShopActivityInfoDTO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ShotInfoVO {


    /**
     * 主键自增id
     */
    private Long id;
    /**
     * 店铺唯一编码
     */
    private String shopCode;

    /**
     * 运营后端店铺名称，默认值：shopCode-showName
     */
    private String name;

    /**
     * 店铺展示名称
     */
    private String showName;

    /**
     * 店铺logo
     */
    private String pcLogoUrl;

    /**
     * 店铺logo
     */
    private String appLogoUrl;


    /**
     * 运费信息
     */
    private String freightTips ;

    /**
     * 0-自营，1-pop商家
     */
    private Integer isThirdCompany;

    /**
     * orgId
     */
    private String orgId;

    /**
     * 营销信息
     */
    private List<ShopActivityInfoDTO> activityInfo ;



}
