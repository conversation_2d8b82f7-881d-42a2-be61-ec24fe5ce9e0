package com.xyy.ec.pc.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
public class XyyConfig {

    /**
     * @deprecated 过时，使用 {@link AppProperties#getBasePathUrl()} 代替。
     */
    @Deprecated
    @Value("${base_path_url}")
    public static String basePathUrl;

    @Value("${seckill_activity_category_id}")
    public static String seckillActivityCategoryId;

    @Value("${erp_url}")
    public static String erpUrl;

    @Value("${es_url}")
    public static String esUrl;

    @Value("${environment}")
    private static String environment;

    @Value("${temp_manageId}")
    private static int tempManageId;

    @Value("${crm.webserviceUrl}")
    private static String crmWebUrl;

    @Value("${xyy.ec.promotion.rewrite.package.price.ids:\"\"}")
    private String reWritePackagePriceIds;

    @Value("${template.dir:/template/}")
    private String templateDir;
    @Value("${excel.import.batch.size:500}")
    private Integer importBatchSize;
    @Value("${excel.export.batch.size:500}")
    private Integer exportBatchSize;
    //采购计划单导入文件最大大小，单位：M
    @Value("${excel.import.maxSize:2}")
    private Integer importMaxSize;
    //采购计划单导入文件最大行数
    @Value("${excel.import.maxRows:500}")
    private Integer importMaxRows;



    @Bean
    public CdnConfig getCdnConfig() {
        return new CdnConfig();
    }

    @Bean
    public JpushConfig getJpushConfig() {
        return new JpushConfig();
    }

    @Bean
    public AliyunSmsConfig getAliyunSmsConfig() {
        return new AliyunSmsConfig();
    }

    @Bean
    public SunNetClientConfig getSunNetClientConfig() {
        return new SunNetClientConfig();
    }

    @Bean
    public ZhugeConfig getZhugeConfig() {
        return new ZhugeConfig();
    }

    public static String getBasePathUrl() {
        return basePathUrl;
    }

    public static String getSeckillActivityCategoryId() {
        return seckillActivityCategoryId;
    }

    public static String getErpUrl() {
        return erpUrl;
    }

    public static String getEsUrl() {
        return esUrl;
    }

    public static String getEnvironment() {
        return environment;
    }

    public static int getTempManageId() {
        return tempManageId;
    }

    public static String getCrmWebUrl() {
        return crmWebUrl;
    }

    public String getReWritePackagePriceIds() {
        return reWritePackagePriceIds;
    }

    public class CdnConfig {
        @Value("${cdn_uploadPath}")
        private String cdnUploadPath;
        @Value("${cdn_hostname}")
        private String cdnHostname;
        @Value("${cdn_port}")
        private String cdnPort;
        @Value("${cdn_username}")
        private String cdnUsername;
        @Value("${cdn_password}")
        private String cdnPassword;

        private CdnConfig() {
        }

        public String getCdnUploadPath() {
            return cdnUploadPath;
        }

        public String getCdnHostname() {
            return cdnHostname;
        }

        public String getCdnPort() {
            return cdnPort;
        }

        public String getCdnUsername() {
            return cdnUsername;
        }

        public String getCdnPassword() {
            return cdnPassword;
        }
    }

    public class JpushConfig {
        @Value("${jpush.appKey.dev}")
        private String jpushAppKeyDev;

        @Value("${jpush.masterSecret.dev}")
        private String jpushMasterSecretDev;

        @Value("${jpush.appKey.prd}")
        private String jpushAppKeyPrd;

        @Value("${jpush.apnsProduction}")
        private boolean jpushApnsProduction;


        private JpushConfig() {
        }

        public String getJpushAppKeyDev() {
            return jpushAppKeyDev;
        }

        public String getJpushMasterSecretDev() {
            return jpushMasterSecretDev;
        }

        public String getJpushAppKeyPrd() {
            return jpushAppKeyPrd;
        }

        public boolean isJpushApnsProduction() {
            return jpushApnsProduction;
        }
    }

    public class AliyunSmsConfig {
        @Value("${aliyun_sms_access_key_id}")
        private String aliYunSmsAccessKeyId;
        @Value("${aliyun_sms_access_key_secret}")
        private String aliyunSmsAccessKeySecret;
        @Value("${aliyun_sms_sign_name}")
        private String aliyunSmsSignName;

        private AliyunSmsConfig() {
        }

        public String getAliYunSmsAccessKeyId() {
            return aliYunSmsAccessKeyId;
        }

        public String getAliyunSmsSignName() {
            return aliyunSmsSignName;
        }
    }

    public class SunNetClientConfig {
        @Value("${sun.net.client.defaultConnectTimeout}")
        private int defaultConnectTimeout;
        @Value("${sun.net.client.defaultReadTimeout}")
        private int defaultReadTimeout;

        private SunNetClientConfig() {
        }

        public int getDefaultConnectTimeout() {
            return defaultConnectTimeout;
        }

        public int getDefaultReadTimeout() {
            return defaultReadTimeout;
        }
    }

    public class ZhugeConfig {
        @Value("${zhuge.appKey}")
        private String zhugeAppKey;
        @Value("${zhuge.url}")
        private String zhugeAppUrl;

        public String getZhugeAppKey() {
            return zhugeAppKey;
        }

        public void setZhugeAppKey(String zhugeAppKey) {
            this.zhugeAppKey = zhugeAppKey;
        }

        public String getZhugeAppUrl() {
            return zhugeAppUrl;
        }

        public void setZhugeAppUrl(String zhugeAppUrl) {
            this.zhugeAppUrl = zhugeAppUrl;
        }

        public ZhugeConfig() {
        }
    }
}
