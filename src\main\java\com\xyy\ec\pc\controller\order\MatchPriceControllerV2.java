package com.xyy.ec.pc.controller.order;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.xyy.ec.marketing.client.api.query.query.CouponAvailableQuery;
import com.xyy.ec.marketing.client.api.query.resp.CouponsInCartView;
import com.xyy.ec.marketing.hyperspace.api.dto.ActCardInfoParamDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.match.api.*;
import com.xyy.ec.match.dto.MatchPriceResultDTO;
import com.xyy.ec.match.dto.MatchProgressDto;
import com.xyy.ec.match.dto.ProcureMatchConversionLog;
import com.xyy.ec.match.enums.ProductMatchStrategyEnum;
import com.xyy.ec.match.params.*;
import com.xyy.ec.match.vo.BigQuickMatchResultVo;
import com.xyy.ec.match.vo.MatchQuickSearchResultVo;
import com.xyy.ec.match.vo.ProcureMatchRuleVO;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.dto.settle.KaMatchPriceVO;
import com.xyy.ec.order.dto.settle.SettleMatchPriceDto;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.constants.RedisConstants;
import com.xyy.ec.pc.controller.vo.ImportPlanVO;
import com.xyy.ec.pc.controller.vo.MatchLineVO;
import com.xyy.ec.pc.controller.vo.matchprice.MatchPriceExcelHeadVo;
import com.xyy.ec.pc.controller.vo.matchprice.PurchaseExcelMappingVo;
import com.xyy.ec.pc.exception.XyyExcelException;
import com.xyy.ec.pc.rest.ResponseVo;
import com.xyy.ec.pc.rpc.OrderServerRpcService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.order.MatchPriceService;
import com.xyy.ec.pc.service.order.ThirdQueryService;
import com.xyy.ec.pc.service.order.excel.listener.MatchPriceHeadReadListener;
import com.xyy.ec.pc.service.order.excel.listener.MatchPriceImportPlanReadListener;
import com.xyy.ec.pc.service.order.excel.listener.MatchPriceImportReadListener;
import com.xyy.ec.pc.util.CosUtil;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.product.business.ecp.out.product.dto.ProductMatchResultDTO;
import com.xyy.ec.product.business.ecp.out.product.dto.ProductQueryDTO;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.framework.redis.autoconfigure.core.RedisClient;
import com.xyy.me.product.general.api.newer.dto.product.FuzzyMatchProductQueryDTO;
import com.xyy.me.product.general.api.newer.dto.product.FuzzyMatchProductResultDTO;
import com.xyy.ms.marketing.nine.chapters.api.estimation.dto.DiscountPrice;
import com.xyy.ms.marketing.nine.chapters.api.estimation.dto.KaDiscountPriceRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@RequiredArgsConstructor
@RequestMapping("/v2/matchPrice")
@RestController
public class MatchPriceControllerV2 {

    private final XyyConfig xyyConfig;
    private final XyyIndentityValidator xyyIndentityValidator;
    private final MatchPriceService matchPriceService;
    private final ThirdQueryService thirdQueryService;
    private final OrderServerRpcService orderServerRpcService;
    private final PcVersionUtils appVersionUtils;
    private final RedisClient redisClient;
    @Reference(version = "0.0.1")
    private MatchConversionLogApi matchConversionLogApi;
    @Reference(version = "0.0.1")
    private ProcureMatchRuleApi procureMatchRuleApi;
    @Reference(version = "0.0.1", timeout = 60000)
    private ProcureMatchResultApi procureMatchResultApi;
    @Reference(version = "1.0.0")
    ProcureMatchSubmitRemote submitRemote;
    @Reference(version = "0.0.1")
    TestManualApi testManualApi;


    @GetMapping("/limit-setting")
    public ResponseVo<Map<String, Integer>> getLimitSetting() {
        Map<String, Integer> limitSettingMap = new HashMap<>();
        limitSettingMap.put("importMaxSize", xyyConfig.getImportMaxSize());
        limitSettingMap.put("importMaxRows", xyyConfig.getImportMaxRows());
        return ResponseVo.successResult(limitSettingMap);
    }

    @PostMapping("/getTableInformation")
    public ResponseVo<MatchPriceExcelHeadVo> getTableInformation(@RequestParam("file") MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.endsWith(originalFilename, ".xlsx") && !StringUtils.endsWith(originalFilename, ".xls")) {
            return ResponseVo.errResult("请上传正确的文件格式.xlsx或.xsl！");
        }
        if (file.isEmpty() || file.getSize() > xyyConfig.getImportMaxSize() * (1024 * 1024)) {
            return ResponseVo.errResult("文件过大，文件大小不得超过" + xyyConfig.getImportMaxSize() + "M！");
        }
        MatchPriceExcelHeadVo matchPriceExcelHeadVo = new MatchPriceExcelHeadVo();
        matchPriceExcelHeadVo.setName(originalFilename);
        try {
            EasyExcel.read(file.getInputStream(), new MatchPriceHeadReadListener(matchPriceExcelHeadVo)).sheet().headRowNumber(0).doRead();
        } catch (XyyExcelException e) {
            log.error("MatchPriceController.importPurchasePlan#自定义异常.导入采购单失败", e);
            return ResponseVo.errResult(e.getMessage());
        } catch (Exception e) {
            log.error("MatchPriceController.importPurchasePlan#error.导入采购单失败", e);
            return ResponseVo.errResult("导入失败");
        }
        return ResponseVo.successResult(matchPriceExcelHeadVo);
    }

    @PostMapping("/import")
    public ResponseVo<ImportPlanVO> importPurchasePlan(@RequestParam("file") MultipartFile file,
                                                       @RequestParam("headRowIndex") int headRowIndex,
                                                       PurchaseExcelMappingVo purchaseExcelMappingVo) {
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            log.error("MatchPriceController.importPurchasePlan#error", e);
        }
        if (merchant == null) {
            return ResponseVo.errResult("请登陆后再试！");
        }
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.endsWith(originalFilename, ".xlsx") && !StringUtils.endsWith(originalFilename, ".xls")) {
            return ResponseVo.errResult("请上传正确的文件格式.xlsx或.xsl！");
        }
        if (file.isEmpty() || file.getSize() > xyyConfig.getImportMaxSize() * (1024 * 1024)) {
            return ResponseVo.errResult("文件过大，文件大小不得超过" + xyyConfig.getImportMaxSize() + "M！");
        }
        try {
            File tmp = saveFiles(merchant.getMerchantId(), file);
            Map<String, Object> map = BeanUtil.beanToMap(purchaseExcelMappingVo, false, true);
            if (map.get("csuId") != null && StringUtils.isNotBlank(map.get("csuId").toString())) {
                // 计划采购表格
                Map<String, String> headMap = map.entrySet().stream().collect(Collectors.toMap(x -> String.valueOf(x.getValue()), Map.Entry::getKey, (A, B) -> A));
                ImportPlanVO importPlanVo = new ImportPlanVO();
                MatchPriceImportPlanReadListener matchPriceImportReadListener = new MatchPriceImportPlanReadListener(merchant, headMap, matchPriceService, importPlanVo, xyyConfig.getImportBatchSize(), xyyConfig.getImportMaxRows());
                EasyExcel.read(new FileInputStream(tmp), matchPriceImportReadListener).sheet().headRowNumber(headRowIndex + 1).doRead();
                return ResponseVo.successResult(importPlanVo);
            } else {
                // 普通采购表格
                Map<String, String> headMap = map.entrySet().stream().collect(Collectors.toMap(x -> String.valueOf(x.getValue()), Map.Entry::getKey, (A, B) -> A));
                MatchPriceImportReadListener matchPriceImportReadListener = new MatchPriceImportReadListener(merchant, headMap, matchPriceService, xyyConfig.getImportBatchSize(), xyyConfig.getImportMaxRows());
                EasyExcel.read(new FileInputStream(tmp), matchPriceImportReadListener).sheet().headRowNumber(headRowIndex + 1).doRead();
                return ResponseVo.successResultNotData();
            }
        } catch (XyyExcelException e) {
            log.error("MatchPriceController.importPurchasePlan#自定义异常.导入采购单失败", e);
            return ResponseVo.errResult(e.getMessage());
        } catch (Exception e) {
            log.error("MatchPriceController.importPurchasePlan#error.导入采购单失败", e);
            return ResponseVo.errResult("导入失败");
        }
    }

    /**
     * 查询匹配进度
     */
    @RequestMapping("/match-progress")
    @ResponseBody
    public ResponseVo<MatchProgressDto> getMatchProgress(@RequestParam("requestId") String requestId) {
        try {
            MerchantBussinessDto merchantDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchantDto == null) {
                return ResponseVo.errResult("请登录后再试！");
            }
            return ResponseVo.successResult(procureMatchResultApi.getMatchProgress(merchantDto.getId(), requestId));
        } catch (Exception e) {
            log.error("查询匹配进度异常",e);
            return ResponseVo.errResult("查询匹配进度异常");
        }
    }


    /**
     * 快速搜索
     */
    @PostMapping("/quick-search-product")
    @ResponseBody
    public ResponseVo<MatchQuickSearchResultVo> quickSearchProduct(@RequestBody MatchQuickSearchParamDTO matchQuickSearchParamDto) {
        try {
            MerchantBussinessDto merchantDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchantDto == null || merchantDto.getId() == null) {
                return ResponseVo.errResult("请登陆后再试！");
            }
            if (matchQuickSearchParamDto == null) {
                return ResponseVo.errResult("参数错误！");
            }
            //匹价排序会用到，如果前端不传默认给1
            if (matchQuickSearchParamDto.getLineNum() == null) {
                matchQuickSearchParamDto.setLineNum(1);
            }
            log.info("MatchPriceController.quickSearchProductV2 param：{}", JSON.toJSONString(matchQuickSearchParamDto));
            //极速搜索
            matchQuickSearchParamDto.setMerchantId(merchantDto.getId());
            MatchQuickSearchResultVo matchQuickSearchResultVo = procureMatchResultApi.quickSearchProduct(matchQuickSearchParamDto);
            return ResponseVo.successResult(matchQuickSearchResultVo);
        } catch (Exception ex) {
            log.error("MatchPriceController.quickSearchProductV2#快速搜索失败 param：{}", JSON.toJSONString(matchQuickSearchParamDto), ex);
            return ResponseVo.errResult("快速搜索失败");
        }
    }


    /**
     * 使用es快速搜索商品
     */
    @PostMapping("/es-search-product")
    @ResponseBody
    public ResponseVo<BigQuickMatchResultVo> quickEsSearchProduct(@RequestBody BigQuickSearchParam param) {
        try {
            boolean invalid = param == null
                    || Strings.isNullOrEmpty(param.getGeneralName())
                    || param.getLineNum() == null
                    || param.getSort() == null;
            if (invalid) {
                return ResponseVo.errResult("参数错误！");
            }

            MerchantBussinessDto merchantDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchantDto == null || merchantDto.getId() == null) {
                return ResponseVo.errResult("请登陆后再试！");
            }
            log.info("MatchPriceController.quickSearchProductV2 param：{}", JSON.toJSONString(param));
            //极速搜索
            param.setMerchantId(merchantDto.getMerchantId());
            BigQuickMatchResultVo matchQuickSearchResultVo = procureMatchResultApi.quickUseEsSearchProduct(param);
            return ResponseVo.successResult(matchQuickSearchResultVo);
        } catch (Exception ex) {
            log.error("MatchPriceController.quickSearchProductV2#快速搜索失败 param：{}", JSON.toJSONString(param), ex);
            return ResponseVo.errResult("快速搜索失败");
        }
    }


    @PostMapping("/detail/update")
    public ResponseVo<String> update(@RequestBody List<MatchLineVO> matchLines) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return ResponseVo.errResult("请登陆后再试！");
            }
            matchPriceService.updateImportProduct(merchant.getId(), matchLines);
            return ResponseVo.successResult("success");
        } catch (Exception e) {
            log.error("MatchPriceController./detail/update#error", e);
			return ResponseVo.successResult("failed");
        }
    }

    @PostMapping("/replaceSku")
    public ResponseVo<String> replaceSku(@RequestBody ReplaceMatchParam param) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return ResponseVo.errResult("请登陆后再试！");
            }
            param.setMerchantId(merchant.getMerchantId());
            procureMatchResultApi.replaceMatchSku(param);
            return ResponseVo.successResult("success");
        } catch (Exception e) {
            log.error("替换sku信息失败:", e);
            return ResponseVo.successResult("failed");
        }
    }

    /**
     * 提交匹价
     */
    @PostMapping("/match-price-submit")
    public ResponseVo<Void> matchPriceSubmit(@RequestBody MatchParamVO matchParamVo) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return ResponseVo.errResult("请登录后再试！");
            }
            merchant = appVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            if(!merchant.getPriceDisplayFlag()){//资质判断
                return ResponseVo.errResult("请完成资质认证");
            }
            boolean isCurMatchEnd = procureMatchRuleApi.isCurMatchEnd(merchant.getId(), matchParamVo.getRequestId());
            if (isCurMatchEnd) {
                // requestId每次提交匹价应该是都是新的，有已完成的可以断定是requestId前端控制的不对
                return ResponseVo.errResult("出错了，请刷新页面后重新匹价");
            }
            boolean success = submitRemote.submitProcureTask(merchant.getId(), matchParamVo.getRequestId());
            return success ? ResponseVo.successResultNotresult("提交成功") : ResponseVo.errResult("匹配中，请稍候重试！");
        } catch (Exception e) {
            log.error("MatchPriceController./v2/matchPrice/match-price-submit#error", e);
            return ResponseVo.errResult("提交匹价失败");
        }
	}

    /**
     * 二次模糊匹价提交
     */
    @PostMapping("/secondMatchSubmit")
    public ResponseVo<Void> secondMatchSubmit(@RequestBody MultiMatchParam matchParamVo) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return ResponseVo.errResult("请登录后再试！");
            }
            merchant = appVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            if(!merchant.getPriceDisplayFlag()){//资质判断
                return ResponseVo.errResult("请完成资质认证");
            }
            boolean isCurMatchEnd = procureMatchRuleApi.isCurMatchEnd(merchant.getId(), matchParamVo.getRequestId());
            if (isCurMatchEnd) {
                // requestId每次提交匹价应该是都是新的，有已完成的可以断定是requestId前端控制的不对
                return ResponseVo.errResult("出错了，请刷新页面后重新匹价");
            }
            matchParamVo.setMatchStrategy(ProductMatchStrategyEnum.MATCH_3);
            matchParamVo.setMerchantId(merchant.getMerchantId());
            boolean success = submitRemote.submitFuzzyProcureTask(matchParamVo);
            return success ? ResponseVo.successResultNotresult("提交成功") : ResponseVo.errResult("匹配中，请稍候重试！");
        } catch (Exception e) {
            log.error("模糊匹价提交失败", e);
            return ResponseVo.errResult("模糊匹价提交失败");
        }
    }

    @PostMapping("/match-price-result")
    public ResponseVo<MatchPriceResultDTO> matchPriceResult(@RequestBody MatchParamVO matchParamVo) {
        try {
            MerchantBussinessDto merchantDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchantDto == null) {
                return ResponseVo.errResult("请登录后再试！");
            }
            merchantDto = appVersionUtils.getPriceDisplayFlagByMerchantId(merchantDto);
            if(!merchantDto.getPriceDisplayFlag()){//资质判断
                return ResponseVo.errResult("请完成资质认证");
            }

            String canBuyStr = matchParamVo.getMatchBaseParamVO().getCanBuyStr();
            if ("deficiency".equals(canBuyStr)) {
                return this.selectDeficiency(merchantDto.getId());
            } else {
                if ("canBuy".equals(canBuyStr)) {
                    matchParamVo.getMatchBaseParamVO().setCanBuyIs(true);
                } else if ("notCanBuy".equals(canBuyStr)) {
                    matchParamVo.getMatchBaseParamVO().setCanBuyIs(false);
                }
                MatchPriceResultDTO resultPage = procureMatchResultApi.getMatchResultPage(merchantDto.getId(), matchParamVo);
                return ResponseVo.successResult(resultPage);
            }
        } catch (Exception e) {
            log.error("MatchPriceController./v2/matchPrice/match-price-result#error", e);
            return ResponseVo.errResult("获取匹价结果失败");
        }
    }

    private ResponseVo<MatchPriceResultDTO> selectDeficiency(long merchantId) {
        try {
            MatchPriceResultDTO resultDTO = orderServerRpcService.selectImportDeficiencyProductV2(merchantId);
            resultDTO.setEnd(true);
            return ResponseVo.successResult(resultDTO);
        } catch (Exception e) {
            log.warn("selectDeficiency:", e);
            return ResponseVo.errResult("刷新匹配结果异常");
        }
    }

    @PostMapping("/conversion")
    public ResponseVo<Void> saveLogAndDetail(@RequestBody ProcureMatchConversionLog matchConversionLog) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return ResponseVo.errResult("请登陆后再试");
            }
            matchConversionLog.setMerchantId(merchant.getId());
            matchConversionLogApi.saveLogAndDetail(matchConversionLog);
            return ResponseVo.successResultNotresult("success");
        } catch (Exception e) {
            log.warn("saveLogAndDetail:", e);
            // 埋点请求失败，应该也是返回请求成功
            return ResponseVo.successResultNotresult("success");
        }
    }


	@GetMapping("/getProcureMatchRule")
	public ResponseVo<ProcureMatchRuleVO> getProcureMatchRule(){
		try {
			MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return ResponseVo.errResult("请登陆后再试");
			}
            ProcureMatchRuleVO procureMatchRuleVO =	procureMatchRuleApi.getMatchRule(merchantBussinessDto.getId());
			return ResponseVo.successResult(procureMatchRuleVO);
		} catch (Exception e) {
			log.error("MatchPriceController.getProcureMatchRule#异常", e);
			return ResponseVo.errResult("查询用户规则失败");
		}
	}


	@PostMapping("/saveMatchRule")
	public ResponseVo<String> saveMatchRule(@RequestBody MatchRuleParamVO matchRuleParamVo){
		try {
			MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchantBussinessDto == null) {
				return ResponseVo.errResult("请登陆后再试");
			}
            matchRuleParamVo.setMerchantId(merchantBussinessDto.getId());
            procureMatchRuleApi.saveMatchRule(matchRuleParamVo);
			return ResponseVo.successResultNotData();
		} catch (Exception e) {
			log.error("MatchPriceController.saveMatchRule#异常", e);
			return ResponseVo.errResult("保存规则失败");
		}
	}

    /**
     * 下载报价单
     * 按需下载（页面选择的数据）
     * @param matchSkuList
     * @param response
     * @return
     */
    @RequestMapping(value = "/downloadQuotation")
    @ResponseBody
    public void downloadQuotationV3(@RequestBody List<MatchLineVO> matchSkuList, HttpServletResponse response) {
        MerchantBussinessDto merchantBussinessDto = null;
        Long merchantId = null;
        try {
            merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchantBussinessDto == null) {
                throw new RuntimeException("请登录后再试！");
            }
            merchantId = merchantBussinessDto.getMerchantId();
            String result = redisClient.get(com.xyy.ec.match.base.Constants.getRedisMatchKey(merchantId));
            if("0".equalsIgnoreCase(result)){//匹配中
                throw new RuntimeException("匹配中，请稍候重试！");
            }
            redisClient.setEx(RedisConstants.getMatchProgressKey(merchantId), "0",RedisConstants.PRE_REMINDER_EXPIRE_ONLINE);

            String realName = merchantBussinessDto.getRealName();

            matchPriceService.downloadQuotationV3(merchantId,realName,matchSkuList, response);
            redisClient.del(RedisConstants.getMatchProgressKey(merchantId));
        }
        catch (XyyExcelException ex){
            if(merchantBussinessDto != null){
                redisClient.del(RedisConstants.getMatchProgressKey(merchantId));
            }
            throw new RuntimeException(ex.getMessage());
        }catch (Exception e) {
            if(merchantBussinessDto != null){
                redisClient.del(RedisConstants.getMatchProgressKey(merchantId));
            }
            log.error("执行失败:",e);
            throw new RuntimeException("报价单下载失败！");
        }
    }

    private File saveFiles(long merchantId,MultipartFile file) throws Exception{
        String tempDir = System.getProperty("java.io.tmpdir");
        tempDir += File.separator + UUID.randomUUID().toString().replaceAll("-","");
        File folder = new File(tempDir);
        if(!folder.exists()){
            folder.mkdirs();
        }
        File tmp = new File(tempDir + File.separator + file.getOriginalFilename());
        if(!tmp.exists()){
            tmp.createNewFile();
        }
        file.transferTo(tmp);

        String s = CosUtil.uploadAndGetFullPath(new FileInputStream(tmp), file.getOriginalFilename());
        // 文件保存7天
        redisClient.set(com.xyy.ec.match.base.Constants.getMerchantFileKey(merchantId),s,604800L);

        return tmp;
    }

    @PostMapping("/match")
    public MatchPriceResultDTO match(@RequestBody EcProductQueryParam param){
        return submitRemote.matchProcureProductList(param.getMerchantId());
    }
    @PostMapping("/submit")
    public boolean submit(@RequestBody EcProductQueryParam param){
        return submitRemote.submitProcureTask(param.getMerchantId(),param.getRequestId());
    }

    @PostMapping("/qualityShop")
    public Set<String> getQualityShop(@RequestBody Set<String> shopCodes){
        return thirdQueryService.getQualityShop(shopCodes);
    }
    @PostMapping("/shops")
    public List<ShopInfoDTO> getShops(@RequestBody Set<String> shopCodes){
        return thirdQueryService.getShops(shopCodes);
    }
    @PostMapping("/coupons")
    public CouponsInCartView getCoupons(@RequestBody CouponAvailableQuery query){
        return thirdQueryService.getCoupons(query);
    }
    @PostMapping("/discount")
    public List<DiscountPrice> getDiscountList(@RequestBody KaDiscountPriceRequest request){
        return thirdQueryService.getDiscountList(request);
    }
    @PostMapping("/me/product3")
    public List<FuzzyMatchProductResultDTO> matchMeProduct3(@RequestBody List<FuzzyMatchProductQueryDTO> products){
        return thirdQueryService.matchMeProduct3(products);
    }
    @PostMapping("/me/product4")
    public List<FuzzyMatchProductResultDTO> matchMeProduct4(@RequestBody List<FuzzyMatchProductQueryDTO> products){
        return thirdQueryService.matchMeProduct4(products);
    }
    @PostMapping("/csuId")
    public List<ProductMatchResultDTO> querySkuIdByStandardIds(@RequestBody ProductQueryDTO query){
        return thirdQueryService.querySkuIdByStandardIds(query);
    }
    @PostMapping("/products")
    public List<ProductDTO> getEcProductInfoById(@RequestBody EcProductQueryParam param){
        return thirdQueryService.getEcProductInfoById(param);
    }
    @PostMapping("/activity")
    public List<GroupBuyingInfoDto> activity(@RequestBody ActCardInfoParamDto param){
        return thirdQueryService.getActCardInfoBySkuIdList(param);
    }
    @PostMapping("/settle")
    public KaMatchPriceVO settle(@RequestBody SettleMatchPriceDto param){
        return thirdQueryService.settle(param);
    }

    @GetMapping("/refresh")
    public Boolean refresh(@RequestParam("id")Long id){
        testManualApi.refreshMatchRuleData(id);
        return true;
    }
}
