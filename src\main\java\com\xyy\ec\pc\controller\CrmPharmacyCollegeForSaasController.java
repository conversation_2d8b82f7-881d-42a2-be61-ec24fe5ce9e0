package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.model.dto.system.PharmacyCollegeDetailDto;
import com.xyy.ec.pc.model.dto.system.PharmacyCollegeDetailWrapperDto;
import com.xyy.ec.pc.model.dto.system.PharmacyCollegeHeaderInfoDto;
import com.xyy.ec.system.business.api.PharmacyCgAggregationBusinesApi;
import com.xyy.ec.system.business.dto.pharmacycollege.PharmacyCollegeDetailBusinessDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

@Slf4j
@Controller
@RequestMapping("/crm/saas/pharmacyCollege")
public class CrmPharmacyCollegeForSaasController extends BaseController {

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private Config config;

    @Reference(version = "1.0.0")
    private PharmacyCgAggregationBusinesApi pharmacyCgAggregationBusinesApi;

    @ResponseBody
    @RequestMapping("/listDetailInfos")
    public Map<String, Object> listDetailInfos() {
        try {
            String imageUrl = config.getProductImagePathUrl();
            Map<String, Object> result = addResult(true, "获取成功");
            PharmacyCollegeDetailWrapperDto pharmacyCollegeDetailWrapperDto = new PharmacyCollegeDetailWrapperDto();
            PharmacyCollegeHeaderInfoDto headerInfo = new PharmacyCollegeHeaderInfoDto();
            headerInfo.setIndexTitle(appProperties.getPharmacyCollege().getIndexTitleForSaas());
            String pcUrl = appProperties.getUrl().getPc();
            String indexApiForPc = appProperties.getPharmacyCollege().getIndexApiForPc();
            if (StringUtils.isNotEmpty(pcUrl) && StringUtils.isNotEmpty(indexApiForPc)) {
                headerInfo.setIndexUrlForPc(pcUrl + indexApiForPc);
            }
            try {
                List<PharmacyCollegeDetailBusinessDto> pharmacyCollegeDetailBusinessDtos = pharmacyCgAggregationBusinesApi
                        .listPharmacyCollegeDetailsForSass();
                List<PharmacyCollegeDetailDto> details = Lists.newArrayListWithCapacity(pharmacyCollegeDetailBusinessDtos.size());
                pharmacyCollegeDetailBusinessDtos.forEach(item -> {
                    PharmacyCollegeDetailDto temp = new PharmacyCollegeDetailDto();
                    BeanUtils.copyProperties(item, temp);
                    // 拼接预览图，绝对路径
                    if(StringUtils.isNotEmpty(temp.getViewImage())) {
                        temp.setViewImage(imageUrl + temp.getViewImage());
                    }
                    // 详情地址
                    if (temp.getRootCategoryId() != null && temp.getRootCategoryLevel() != null
                            && temp.getCategoryId() != null && temp.getId() != null) {
                        temp.setDetailUrlForPc(pcUrl + String.format(appProperties.getPharmacyCollege().getDetailApiForPc(),
                                temp.getRootCategoryId(), temp.getCategoryId(), temp.getId(), temp.getRootCategoryLevel()));
                    }
                    details.add(temp);
                });
                pharmacyCollegeDetailWrapperDto.setDetails(details);
            } catch (Exception e) {
                log.error("获取为saas提供的药学院详情异常", e);
                pharmacyCollegeDetailWrapperDto.setDetails(Lists.newArrayList());
            }
            pharmacyCollegeDetailWrapperDto.setHeaderInfo(headerInfo);
            result.put(DATA, pharmacyCollegeDetailWrapperDto);
            return result;
        } catch (Exception e) {
            log.error("获取为saas提供的药学院详情包装信息异常", e);
            return addResult(false, "获取失败");
        }

    }

}
