package com.xyy.ec.pc.vo.order;

import java.io.Serializable;

public class PayVo  implements Serializable {
    private static final long serialVersionUID = 8313594507467750928L;

    private String paymentKey;

    private String payTypeForFrontKey;
    //是否轮询支付结果：0-不轮询;1-轮询
    private Integer isQueryPay =1;

    /**
     * 农行链e贷区分核验状态使用
     *
     * @see com.xyy.ec.order.business.common.AbchinaLoanConstants
     */
    private String bizCode;

    /**
     * 购物金充值支付请求申请单号
     */
    private String payReqNo;

    private String orderNo;

    private String imgurl;

    public String getImgurl() {
        return imgurl;
    }

    public void setImgurl(String imgurl) {
        this.imgurl = imgurl;
    }

    public String getPaymentKey() {
        return paymentKey;
    }

    public void setPaymentKey(String paymentKey) {
        this.paymentKey = paymentKey;
    }

    public String getPayTypeForFrontKey() {
        return payTypeForFrontKey;
    }

    public void setPayTypeForFrontKey(String payTypeForFrontKey) {
        this.payTypeForFrontKey = payTypeForFrontKey;
    }

    public Integer getIsQueryPay() {
        return isQueryPay;
    }

    public void setIsQueryPay(Integer isQueryPay) {
        this.isQueryPay = isQueryPay;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getPayReqNo() {
        return payReqNo;
    }

    public void setPayReqNo(String payReqNo) {
        this.payReqNo = payReqNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
