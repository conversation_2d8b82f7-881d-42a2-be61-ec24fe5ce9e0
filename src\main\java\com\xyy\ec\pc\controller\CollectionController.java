package com.xyy.ec.pc.controller;

import cn.hutool.log.Log;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingSkuDto;
import com.xyy.ec.merchant.bussiness.api.FavoriteBussinessApi;
import com.xyy.ec.merchant.bussiness.api.ecp.ecpfavorite.EcpFavoriteBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.FavoriteBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.ProductFavoriteBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.api.OrderDetailBusinessApi;
import com.xyy.ec.order.business.api.ShoppingCartBusinessApi;
import com.xyy.ec.order.business.exception.ServiceException;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.base.Sort;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.controller.vo.PCShopListProductInfoVo;
import com.xyy.ec.pc.controller.vo.ProductFavoriteBussinessVo;
import com.xyy.ec.pc.rpc.ShopServiceRpc;
import com.xyy.ec.pc.search.vo.PinTuanActInfoVo;
import com.xyy.ec.pc.service.BuryingPointService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.marketing.dto.MarketingWholesaleActivityInfoDTO;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.api.ProductBusinessApi;
import com.xyy.ec.product.business.dto.ProductDto;
import com.xyy.ec.product.business.dto.product.ProductActivityTag;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 
 * <AUTHOR> 2016-12-27
 *
 */
@Controller
@RequestMapping("/merchant/center/collection")
public class CollectionController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(CollectionController.class);

	@Autowired
	private XyyIndentityValidator xyyIndentityValidator;

	@Reference(version = "1.0.0")
	private ProductBusinessApi productBusinessApi;

	@Reference(version = "1.0.0",timeout = 10000)
	private FavoriteBussinessApi favoriteBussinessApi;
	@Reference(version = "1.0.0",timeout = 10000)
	private EcpFavoriteBussinessApi ecpFavoriteBussinessApi;

	@Reference(version = "1.0.0")
	private ShoppingCartBusinessApi shoppingCartBusinessApi;

	@Reference(version = "1.0.0")
	private OrderDetailBusinessApi orderDetailBusinessApi;

	@Autowired
	private BuryingPointService buryingPointService;

	@Autowired
	private PcVersionUtils pcVersionUtils;

	@Autowired
    private ShopServiceRpc shopServiceRpc;

	@Value("${pc.shop.index.url}")
	private String pcShopUrl;

	@Autowired
	private MarketingService marketingService;


	/**
	 * 收藏夹和已购商品列表页面
	 *
	 * @param page
	 * @param sort
	 * @param product
	 * @param tab
	 *            1为已收藏 2为已购买
	 * @param modelType
	 *            1为列表模式 2为大图模式
	 * @return
	 * @throws UnsupportedEncodingException
	 * @throws ServiceException
	 */
	@RequestMapping(value = "findAttention.htm", method = RequestMethod.GET)
	public ModelAndView findAttention(
			Page page,
			Sort sort,
			ProductFavoriteBussinessDto product,
			HttpServletRequest request,
			@RequestParam(value = "tab", required = false) Integer tab,
			@RequestParam(value = "modelType", required = false) Integer modelType)
			throws Exception {
		Integer pageNum=1;
		Integer pageSize=12;
		if(page!=null) {
			pageNum=page.getOffset();
		}
		MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
		merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
		if (merchant == null) {
			return new ModelAndView(new RedirectView("/login/login.htm",true,false));
		}
		if (StringUtil.isNotEmpty(product.getCommonName())) {
			String name = URLDecoder.decode(product.getCommonName(), "UTF-8");
			product.setShowName(name);
			product.setCommonName(name);
		}
		if (modelType == null) {
			modelType = 2;
		}
		int oldOffset = page.getOffset();
		page.setLimit(12);
		page.setOffset((oldOffset > 0 ? (page.getOffset() - 1) : 0)
				* page.getLimit());

		product.setMerchantId(merchant.getId());
		Page<ProductFavoriteBussinessVo> result = new Page<>();

		try {
			if (tab == null || ArrayUtils.indexOf(new int[]{Constants.IS1.intValue(),Constants.IS3.intValue(),Constants.IS4.intValue(),Constants.IS5.intValue()},
					tab.intValue()) > -1 ) {
				PageInfo<ProductFavoriteBussinessDto> pageResult = ecpFavoriteBussinessApi.findFavoriteInfoForApp(pageNum, pageSize, product);
				if (pageResult != null && pageResult.getList() != null) {
					if (merchant.getPriceDisplayFlag() == false) {
						ProductActivityTag productActivityTagVO = new ProductActivityTag();
						productActivityTagVO.setTagUrl("");
						productActivityTagVO.setTagNoteBackGroupUrl("");
						productActivityTagVO.setSkuTagNotes(new ArrayList<>());

						List<ProductFavoriteBussinessDto> favoriteBussinessDtos = pageResult.getList();
						List<String> shopCodes = favoriteBussinessDtos.stream()
								.map(ProductFavoriteBussinessDto::getShopCode)
								.filter(x -> x != null)
								.distinct()
								.collect(Collectors.toList());
						Map<String, ShopInfoDTO> shopInfoToMap = shopServiceRpc.getShopInfoToMap(shopCodes);
						for (ProductFavoriteBussinessDto productFavorite : pageResult.getList()) {
							productFavorite.setFob(0d);
							productFavorite.setUnitPrice(null);
							productFavorite.setUnitPriceTag(null);
							productFavorite.setActivityTag(productActivityTagVO);
							//毛利
							productFavorite.setGrossMargin("");
							//建议零售价
							productFavorite.setSuggestPrice(BigDecimal.ZERO);
							//控销零售价
							productFavorite.setUniformPrice(BigDecimal.ZERO);
							//对比价
							productFavorite.setRetailPrice(0.0);
							ShopInfoDTO shopInfoDTO = shopInfoToMap.get(productFavorite.getShopCode());
							if (shopInfoDTO != null) {
								productFavorite.setShopName(shopInfoDTO.getShowName());
								productFavorite.setShopUrl(shopInfoDTO.getPcLink());
							}
							//兼容老逻辑，老商品需要单独拼装POP店铺跳转链接
							if (productFavorite.getIsThirdCompany() == 1) {
								productFavorite.setShopName(productFavorite.getCompanyName());
								productFavorite.setShopUrl(pcShopUrl + productFavorite.getOrgId());
							}
						}
					}
				}
				List<ProductFavoriteBussinessVo> productFavoriteBussinessVoList = Lists.newArrayList();
				// 拼团商品展示拼团和批购包邮样式
				if (pageResult != null && pageResult.getList() != null) {
				List<Long> csuIds = pageResult.getList().stream().map(ProductFavoriteBussinessDto::getId).collect(Collectors.toList());
				if (CollectionUtil.isNotEmpty(csuIds)) {
					//查询拼团活动信息
					Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap = marketingService.getActCardInfoBySkuIdList(csuIds,
							Lists.newArrayList(MarketingQueryStatusEnum.UN_START.getType(), MarketingQueryStatusEnum.STARTING.getType()), merchant.getId(),
							Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), null);
					productFavoriteBussinessVoList = pageResult.getList().stream().map(productFavoriteBussinessDto -> {
						ProductFavoriteBussinessVo productFavoriteBussinessVo = new ProductFavoriteBussinessVo();
						BeanUtils.copyProperties(productFavoriteBussinessDto, productFavoriteBussinessVo);
						productFavoriteBussinessVo.setTagList(productFavoriteBussinessDto.getProductTagList());
						setActPtInfo(groupBuyingInfoDtoMap, productFavoriteBussinessDto, productFavoriteBussinessVo);
						setActPgbyInfo(groupBuyingInfoDtoMap, productFavoriteBussinessDto, productFavoriteBussinessVo);
						//时间转换 Long转string
						if (Objects.nonNull(productFavoriteBussinessVo.getActPt()) && Objects.nonNull(productFavoriteBussinessVo.getActPt().getAssembleStartTime())) {
							Date date = new Date(productFavoriteBussinessVo.getActPt().getAssembleStartTime());
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
							productFavoriteBussinessVo.getActPt().setAssembleStartTimeStr(sdf.format(date));
						}
						return productFavoriteBussinessVo;
					}).collect(Collectors.toList());
				}
			}
				LOGGER.info("productFavoriteBussinessVoList:{}",JSON.toJSONString(productFavoriteBussinessVoList));
				result.setRows(productFavoriteBussinessVoList);
                result.setLimit(pageSize);
				result.setTotal((long) productFavoriteBussinessVoList.size());
			} else {
				// 目前没有用
//				pageInfo = skuService.findBuySkuListForShop(product, page, sort, merchant.getId();
//				tab = Constants.IS2.intValue();
			}
			if(tab==null){
                tab = Constants.IS1.intValue();
            }

		} catch (Exception e) {
			LOGGER.error("收藏夹列表查询出错,e="+ExceptionUtils.getStackTrace(e));
		}
		Map<String, Object> model = new HashMap<String, Object>();
		String requestUrl = this.getRequestUrl(request);
		result.setRequestUrl(requestUrl);
		result.setOffset(oldOffset);
		model.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);

		model.put("merchant",merchant);
		model.put("pager", result);
		model.put("tab", tab);
		model.put("modelType", modelType);
		model.put("businessType", product.getBusinessType());
		model.put("center_menu", "favorite");
		return new ModelAndView("/collection/collection.ftl", model);
	}


	private void setActPtInfo(Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap, ProductFavoriteBussinessDto productDTO, ProductFavoriteBussinessVo productInfoVo) {
		if (productDTO == null || productInfoVo == null || MapUtils.isEmpty(groupBuyingInfoDtoMap)) {
			return;
		}
		Optional.ofNullable(groupBuyingInfoDtoMap.get(productDTO.getId())).ifPresent(buyingInfoDto -> {
			if (!Objects.equals(buyingInfoDto.getActivityType(), MarketingEnum.PING_TUAN.getCode())) {
				return;
			}
			//活动信息
			GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtil.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList()) ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
			/** 拼团活动状态 1.未开始 ，2.拼团中，3.已结束 兼容老逻辑前端拼团活动状态 0-未开始 1-进行中 2-结束 **/
			Integer assembleStatus = buyingInfoDto.getStatus() == null ? 0 : buyingInfoDto.getStatus() - 1;

			PinTuanActInfoVo pinTuanActInfoVo = PinTuanActInfoVo.builder()
					.marketingId(buyingInfoDto.getMarketingId())
					.percentage(String.valueOf(buyingInfoDto.getPercentage().multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_UP))))
					.assembleStatus(assembleStatus)
					.assembleStartTime(buyingInfoDto.getStartTime().getTime())
					.assembleEndTime(buyingInfoDto.getEndTime().getTime())
					.surplusTime((buyingInfoDto.getEndTime().getTime() - System.currentTimeMillis()) / 1000)
					.orderNum(buyingInfoDto.getOrderNum())
					.skuStartNum(groupBuyingSkuDto.getSkuStartNum())
					.preheatShowPrice(buyingInfoDto.getPreheatShowPrice())
					// 拼团多阶梯价信息
					.stepPriceStatus(buyingInfoDto.getStepPriceStatus())
					.minSkuPrice(buyingInfoDto.getMinSkuPrice())
					.maxSkuPrice(buyingInfoDto.getMaxSkuPrice())
					.startingPriceShowText(buyingInfoDto.getStartingPriceShowText())
					.rangePriceShowText(buyingInfoDto.getRangePriceShowText())
					.stepPriceShowTexts(buyingInfoDto.generateStepPriceShowTexts(productInfoVo.getProductUnit()))
					.build();
			Optional.ofNullable(groupBuyingSkuDto).ifPresent(groupBuyingSkuDto1 -> {
				pinTuanActInfoVo.setSkuStartNum(groupBuyingSkuDto1.getSkuStartNum());
				pinTuanActInfoVo.setAssemblePrice(groupBuyingSkuDto1.getSkuPrice());
			});
			//调整拼团商品的showName
			StringBuilder sbShowName = new StringBuilder();
			if (SearchUtils.isShouTuiYouXuan(productDTO.getFirstChoose(), productDTO.getHighGross())) {
				sbShowName.append(Constants.SHOU_TUI_YOU_XUAN_TEXT);
			} else if (StringUtils.isNotEmpty(buyingInfoDto.getTopicPrefix())) {
				sbShowName.append(buyingInfoDto.getTopicPrefix());
			}
			if (groupBuyingSkuDto.getSkuStartNum() != null) {
				String productUnit = Optional.ofNullable(productInfoVo.getProductUnit()).orElse("");
				sbShowName.append(groupBuyingSkuDto.getSkuStartNum()).append(productUnit).append("包邮").append(" ").append(productInfoVo.getShowName());
			} else {
				sbShowName.append(productInfoVo.getShowName());
			}
			//追加规格显示
			if (StringUtils.isNotEmpty(productInfoVo.getSpec()) && !productInfoVo.getSpec().equals(Constants.LINE)) {
				sbShowName.append("/").append(productInfoVo.getSpec());
			}
			productInfoVo.setShowName(sbShowName.toString());
			productInfoVo.setActPt(pinTuanActInfoVo);
		});
	}


	private void setActPgbyInfo(Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap, ProductFavoriteBussinessDto productDTO, ProductFavoriteBussinessVo appSearchProductInfoVo) {
		if (productDTO == null || appSearchProductInfoVo == null || MapUtils.isEmpty(groupBuyingInfoDtoMap)) {
			return;
		}
		Optional.ofNullable(groupBuyingInfoDtoMap.get(productDTO.getId())).ifPresent(buyingInfoDto -> {
			if (!Objects.equals(buyingInfoDto.getActivityType(), MarketingEnum.PI_GOU_BAO_YOU.getCode())) {
				return;
			}
			// 活动信息
			GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtil.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList()) ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
			MarketingWholesaleActivityInfoDTO wholesaleActivityInfoDTO = MarketingWholesaleActivityInfoDTO.builder()
					.marketingId(buyingInfoDto.getMarketingId())
					.activityType(buyingInfoDto.getActivityType())
					.skuStartNum(groupBuyingSkuDto.getSkuStartNum())
					.build();
			Optional.ofNullable(groupBuyingSkuDto).ifPresent(tempGroupBuyingSkuDto -> {
				wholesaleActivityInfoDTO.setSkuStartNum(tempGroupBuyingSkuDto.getSkuStartNum());
				wholesaleActivityInfoDTO.setAssemblePrice(tempGroupBuyingSkuDto.getSkuPrice());
			});
			// 调整商品的showName
			StringBuilder sbShowName = new StringBuilder();
			if (groupBuyingSkuDto.getSkuStartNum() != null) {
				String productUnit = Optional.ofNullable(appSearchProductInfoVo.getProductUnit()).orElse("");
				sbShowName.append(groupBuyingSkuDto.getSkuStartNum()).append(productUnit).append("包邮").append(" ").append(appSearchProductInfoVo.getShowName());
			} else {
				sbShowName.append(appSearchProductInfoVo.getShowName());
			}
			// 追加规格显示
			if (StringUtils.isNotEmpty(appSearchProductInfoVo.getSpec()) && !Objects.equals(appSearchProductInfoVo.getSpec(), Constants.LINE)) {
				sbShowName.append("/").append(appSearchProductInfoVo.getSpec());
			}
			appSearchProductInfoVo.setShowName(sbShowName.toString());
			appSearchProductInfoVo.setActPgby(wholesaleActivityInfoDTO);
		});
	}
	
	
	/**
	 *
	 * 历史采购单
	 * @param page
	 * @param sort
	 * @param product
	 *            1为已收藏 2为已购买
	 * @param modelType
	 *            1为列表模式 2为大图模式
	 * @return
	 * @throws UnsupportedEncodingException
	 * @throws ServiceException
	 */
	@RequestMapping(value = "myOrder.htm", method = RequestMethod.GET)
	public ModelAndView myOrder(
			Page page,
			Sort sort,
			ProductDto product,
			HttpServletRequest request,
			@RequestParam(value = "modelType", required = false) Integer modelType)
			throws Exception {
		Map<String, Object> model = new HashMap<String, Object>();
		MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
		if (merchant == null) {
			return new ModelAndView(new RedirectView("/login/login.htm",true,false));
		}

		if (StringUtil.isNotEmpty(product.getCommonName())) {
			String name = URLDecoder.decode(product.getCommonName(), "UTF-8");
			product.setShowName(name);
			model.put("commonName",name);
		}
		if (modelType == null) {
			modelType = 2;
		}
		int oldOffset = page.getOffset();
		page.setLimit(12);
		page.setOffset(page.getOffset());
		Page<PCShopListProductInfoVo> productPage = new Page<>();
		PageInfo<ProductDto> pageInfo = new PageInfo();
		pageInfo.setPageNum(page.getOffset());
		pageInfo.setPageSize(page.getLimit());
		try{
			pageInfo = orderDetailBusinessApi.findHistoryBuyOrderDetailProductList(pageInfo, merchant.getId(), product.getShowName());
		}catch (Exception e){
			LOGGER.error("/app/sku/findSkuInfo-查询商品列表异常,e="+e);
		}
		List<ProductDto> productDtoList = pageInfo.getList();
		List<PCShopListProductInfoVo> pcShopListProductInfoVoList = new ArrayList<>();
		if(CollectionUtil.isNotEmpty(productDtoList)) {
			// 拼团商品展示拼团和批购包邮样式
			List<Long> csuIds = productDtoList.stream().map(ProductDto::getId).collect(Collectors.toList());
			//查询拼团活动信息
			Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap = marketingService.getActCardInfoBySkuIdList(csuIds,
					Lists.newArrayList(MarketingQueryStatusEnum.UN_START.getType(), MarketingQueryStatusEnum.STARTING.getType()), merchant.getId(),
					Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), null);
			pcShopListProductInfoVoList = productDtoList.stream().map(productDto -> {
				PCShopListProductInfoVo pcShopListProductInfoVo = new PCShopListProductInfoVo();
				BeanUtils.copyProperties(productDto, pcShopListProductInfoVo);
				setActPtInfo(groupBuyingInfoDtoMap, productDto, pcShopListProductInfoVo);
				setActPgbyInfo(groupBuyingInfoDtoMap, productDto, pcShopListProductInfoVo);
				return pcShopListProductInfoVo;
			}).collect(Collectors.toList());
		}
		String requestUrl = this.getRequestUrl(request);
		productPage.setRequestUrl(requestUrl);
		productPage.setOffset(oldOffset);
		productPage.setRows(pcShopListProductInfoVoList);
		productPage.setLimit(page.getLimit());
		productPage.setTotal(pageInfo.getTotal());
		productPage.setOffset(page.getOffset());
		model.put("merchant",merchant);
		model.put("pager", productPage);
		model.put("modelType", modelType);
		model.put("center_menu", "myOrder");
		model.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
		return new ModelAndView("/collection/myOrder.ftl", model);
	}



	private void setActPtInfo(Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap, ProductDto productDTO, PCShopListProductInfoVo productInfoVo) {
		if (productDTO == null || productInfoVo == null || MapUtils.isEmpty(groupBuyingInfoDtoMap)) {
			return;
		}
		Optional.ofNullable(groupBuyingInfoDtoMap.get(productDTO.getId())).ifPresent(buyingInfoDto -> {
			if (!Objects.equals(buyingInfoDto.getActivityType(), MarketingEnum.PING_TUAN.getCode())) {
				return;
			}
			//活动信息
			GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtil.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList()) ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
			/** 拼团活动状态 1.未开始 ，2.拼团中，3.已结束 兼容老逻辑前端拼团活动状态 0-未开始 1-进行中 2-结束 **/
			Integer assembleStatus = buyingInfoDto.getStatus() == null ? 0 : buyingInfoDto.getStatus() - 1;

			PinTuanActInfoVo pinTuanActInfoVo = PinTuanActInfoVo.builder()
					.marketingId(buyingInfoDto.getMarketingId())
					.percentage(String.valueOf(buyingInfoDto.getPercentage().multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_UP))))
					.assembleStatus(assembleStatus)
					.assembleStartTime(buyingInfoDto.getStartTime().getTime())
					.assembleEndTime(buyingInfoDto.getEndTime().getTime())
					.surplusTime((buyingInfoDto.getEndTime().getTime() - System.currentTimeMillis()) / 1000)
					.orderNum(buyingInfoDto.getOrderNum())
					.skuStartNum(groupBuyingSkuDto.getSkuStartNum())
					.preheatShowPrice(buyingInfoDto.getPreheatShowPrice())
					// 拼团多阶梯价信息
					.stepPriceStatus(buyingInfoDto.getStepPriceStatus())
					.minSkuPrice(buyingInfoDto.getMinSkuPrice())
					.maxSkuPrice(buyingInfoDto.getMaxSkuPrice())
					.startingPriceShowText(buyingInfoDto.getStartingPriceShowText())
					.rangePriceShowText(buyingInfoDto.getRangePriceShowText())
					.stepPriceShowTexts(buyingInfoDto.generateStepPriceShowTexts(productInfoVo.getProductUnit()))
					.build();
			Optional.ofNullable(groupBuyingSkuDto).ifPresent(groupBuyingSkuDto1 -> {
				pinTuanActInfoVo.setSkuStartNum(groupBuyingSkuDto1.getSkuStartNum());
				pinTuanActInfoVo.setAssemblePrice(groupBuyingSkuDto1.getSkuPrice());
			});
			//调整拼团商品的showName
			StringBuilder sbShowName = new StringBuilder();
			if (SearchUtils.isShouTuiYouXuan(productDTO.getFirstChoose(), productDTO.getHighGross())) {
				sbShowName.append(Constants.SHOU_TUI_YOU_XUAN_TEXT);
			} else if (StringUtils.isNotEmpty(buyingInfoDto.getTopicPrefix())) {
				sbShowName.append(buyingInfoDto.getTopicPrefix());
			}
			if (groupBuyingSkuDto.getSkuStartNum() != null) {
				String productUnit = Optional.ofNullable(productInfoVo.getProductUnit()).orElse("");
				sbShowName.append(groupBuyingSkuDto.getSkuStartNum()).append(productUnit).append("包邮").append(" ").append(productInfoVo.getShowName());
			} else {
				sbShowName.append(productInfoVo.getShowName());
			}
			//追加规格显示
			if (StringUtils.isNotEmpty(productInfoVo.getSpec()) && !productInfoVo.getSpec().equals(Constants.LINE)) {
				sbShowName.append("/").append(productInfoVo.getSpec());
			}
			productInfoVo.setShowName(sbShowName.toString());
			productInfoVo.setActPt(pinTuanActInfoVo);
		});
	}


	private void setActPgbyInfo(Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap, ProductDto productDTO, PCShopListProductInfoVo appSearchProductInfoVo) {
		if (productDTO == null || appSearchProductInfoVo == null || MapUtils.isEmpty(groupBuyingInfoDtoMap)) {
			return;
		}
		Optional.ofNullable(groupBuyingInfoDtoMap.get(productDTO.getId())).ifPresent(buyingInfoDto -> {
			if (!Objects.equals(buyingInfoDto.getActivityType(), MarketingEnum.PI_GOU_BAO_YOU.getCode())) {
				return;
			}
			// 活动信息
			GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtil.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList()) ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
			MarketingWholesaleActivityInfoDTO wholesaleActivityInfoDTO = MarketingWholesaleActivityInfoDTO.builder()
					.marketingId(buyingInfoDto.getMarketingId())
					.activityType(buyingInfoDto.getActivityType())
					.skuStartNum(groupBuyingSkuDto.getSkuStartNum())
					.build();
			Optional.ofNullable(groupBuyingSkuDto).ifPresent(tempGroupBuyingSkuDto -> {
				wholesaleActivityInfoDTO.setSkuStartNum(tempGroupBuyingSkuDto.getSkuStartNum());
				wholesaleActivityInfoDTO.setAssemblePrice(tempGroupBuyingSkuDto.getSkuPrice());
			});
			// 调整商品的showName
			StringBuilder sbShowName = new StringBuilder();
			if (groupBuyingSkuDto.getSkuStartNum() != null) {
				String productUnit = Optional.ofNullable(appSearchProductInfoVo.getProductUnit()).orElse("");
				sbShowName.append(groupBuyingSkuDto.getSkuStartNum()).append(productUnit).append("包邮").append(" ").append(appSearchProductInfoVo.getShowName());
			} else {
				sbShowName.append(appSearchProductInfoVo.getShowName());
			}
			// 追加规格显示
			if (StringUtils.isNotEmpty(appSearchProductInfoVo.getSpec()) && !Objects.equals(appSearchProductInfoVo.getSpec(), Constants.LINE)) {
				sbShowName.append("/").append(appSearchProductInfoVo.getSpec());
			}
			appSearchProductInfoVo.setShowName(sbShowName.toString());
			appSearchProductInfoVo.setActPgby(wholesaleActivityInfoDTO);
		});
	}

	/**
	 * 关注商品（商户收藏夹，状态：1）
	 *
	 * @param favorite
	 *            (productId，merchantId)
	 * @return
	 */
	@RequestMapping("attention.json")
	@ResponseBody
	public Object attention(FavoriteBussinessDto favorite) {
		try {
			MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			if (merchant == null) {
				return new ModelAndView(new RedirectView("/login/login.htm",true,false));
			}
			favorite.setMerchantId(merchant.getId());
			favoriteBussinessApi.attention(favorite);
			//埋点
//			buryingPointService.toggleAttention(merchant, FavoriteBussinessDto.STATUS_ATTENTION, Arrays.asList(new Long[]{favorite.getSkuId()}));
			return this.addResult("收藏成功!");
		} catch (Exception e) {
			LOGGER.error("收藏失败,e="+e);
			return this.addError("收藏失败!");
		}
	}

	/**
	 * 取消关注商品（商户收藏夹，状态：2）
	 *
	 * @param favorite
	 * @return
	 */
	@RequestMapping("cancelAttention.json")
	@ResponseBody
	public Object cancelAttention(FavoriteBussinessDto favorite) {
		try {
			MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			if (merchant == null) {
				return new ModelAndView(new RedirectView("/login/login.htm",true,false));
			}
			favorite.setMerchantId(merchant.getId());
			favoriteBussinessApi.cancelAttention(favorite);
			//埋点
//			buryingPointService.toggleAttention(merchant, FavoriteBussinessDto.STATUS_CANCEL_ATTENTION, Arrays.asList(new Long[]{favorite.getSkuId()}));
			return this.addResult("取消收藏成功!");
		} catch (IllegalArgumentException e) {
			return this.addError("取消收藏失败!");
		} catch (Exception e) {
			LOGGER.error("取消收藏异常,e="+e);
			return this.addError("取消收藏失败!");
		}
	}

	/**
	 * 批量取消关注商品（商户收藏夹，状态：2）
	 *
	 * @return
	 * @throws ServiceException
	 */
	@RequestMapping("cancelAttentionForShop.htm")
	public ModelAndView cancelAttentionForShop(
			@RequestParam(value = "ids", required = false) String ids,
			@RequestParam(value = "tab", required = false) Integer tab,
			@RequestParam(value = "modelType", required = false) Integer modelType,
			@RequestParam(value = "businessType", required = false) Integer businessType)  {
		try {
			MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			if (merchant == null) {
				return new ModelAndView(new RedirectView("/login/login.htm",true,false));
			}
			if (StringUtil.isNotEmpty(ids)) {
				Long[] idArray = StringUtil.splitStringToLong(ids, ",");
				List<Long> idList = new ArrayList<>();
				for (Long item : idArray) {
					idList.add(item);
				}
				FavoriteBussinessDto dto = new FavoriteBussinessDto();
				dto.setSkuIdList(idList);
				dto.setMerchantId(merchant.getId());
				favoriteBussinessApi.cancelAttentionBath(dto);
				//埋点
//				buryingPointService.toggleAttention(merchant, FavoriteBussinessDto.STATUS_CANCEL_ATTENTION,dto.getSkuIdList());
			}
			return new ModelAndView(new RedirectView("/merchant/center/collection/findAttention.htm?tab="+tab+"&modelType="+modelType+"&businessType="+businessType,true,false));
		} catch (Exception e) {
			LOGGER.error("取消收藏失败,e="+e);
			return new ModelAndView(new RedirectView("/merchant/center/collection/findAttention.htm?tab="+tab+"&modelType="+modelType+"&businessType="+businessType,true,false));
		}
	}

}
