package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.api.HeaderGuideBuineseApi;
import com.xyy.ec.layout.buinese.dto.HeaderGuideBuineseDto;
import com.xyy.ec.layout.buinese.ecp.api.PcHeaderGuideBuineseApi;
import com.xyy.ec.layout.buinese.ecp.results.CmsPcIndexDTO;
import com.xyy.ec.layout.buinese.ecp.results.CmsPcIndexLayoutModuleDTO;
import com.xyy.ec.layout.buinese.ecp.results.CmsPcIndexLayoutModuleItemDTO;
import com.xyy.ec.layout.buinese.utils.AppModuleCategoryEnum;
import com.xyy.ec.merchant.bussiness.api.LicensePoolBusinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantAuthenticationBusinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.api.ShoppingCartBusinessApi;
import com.xyy.ec.order.business.exception.ServiceException;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.cms.constants.CmsConstants;
import com.xyy.ec.pc.cms.utils.LayoutGraySwitchUtils;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.config.AppRocketMqFactory;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.enums.ProvinceBranchEnum;
import com.xyy.ec.pc.newfront.service.IndexNewService;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.service.CodeItemService;
import com.xyy.ec.pc.service.IndexService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.Base64Util;
import com.xyy.ec.product.business.api.HostSearchBusinessApi;
import com.xyy.ec.product.business.api.ecp.skucategory.EcpCategoryBusinessApi;
import com.xyy.ec.product.business.module.CategoryVo;
import com.xyy.ec.system.business.dto.DicAreaBusinessDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Controller
public class IndexController extends BaseController {

    Logger LOGGER = LoggerFactory.getLogger(IndexController.class);


    @Autowired
    private IndexService indexService;

    @Reference(version = "1.0.0")
    private HeaderGuideBuineseApi headerGuideBuineseApi;

    @Reference(version = "1.0.0")
    private PcHeaderGuideBuineseApi pcHeaderGuideBuineseApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;

    @Reference(version = "1.0.0")
    private HostSearchBusinessApi hostSearchBusinessApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private ShoppingCartBusinessApi shoppingCartBusinessApi;

    @Reference(version = "1.0.0")
    private LicensePoolBusinessApi licensePoolBusinessApi;
    @Reference(version = "1.0.0")
    private MerchantAuthenticationBusinessApi merchantAuthenticationBusinessApi;

    @Autowired
    private XyyConfig.ZhugeConfig zhugeConfig;

    @Autowired
    private LayoutGraySwitchUtils layoutGraySwitchUtils;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private CmsAppProperties cmsAppProperties;

    @Autowired
    private AppRocketMqFactory appRocketMqFactory;

    @Autowired
    private Config config;

    @Reference(version = "1.0.0",timeout = 10000)
    private EcpCategoryBusinessApi categoryBusinessApi;

    @Value("${new.index.open:false}")
    private boolean openNewIndex;

    @Value("${new.login.open:true}")
    private boolean newLoginOpen;

    @Autowired
    private IndexNewService indexNewService;

    @RequestMapping(value = {"image"})
    public String image(Map<String, Object> model) {
        return "image.ftl";
    }


    /**
     * @param modelMap
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @throws
     * @Description: 登录后跳转到首页（如果需要跳转到最后访问页面，需要将最后访问页面传过来）
     * <AUTHOR>
     * @date 2018/8/21 17:35
     */
    @RequestMapping(value = {"/", "/index.htm"})
    public ModelAndView index(ModelMap modelMap, HttpServletRequest request, RedirectAttributes redirectAttributes) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            if (Objects.nonNull(merchant)) {
                merchantId = merchant.getId();
            }
            if (newLoginOpen && merchant == null) {
                // 没登录的跳登录页
                String baseUrl = request.getHeader("x-forwarded-proto") + "://" + request.getHeader("host");
                return new ModelAndView("redirect:" + baseUrl + "/new/login");
            }
            if (openNewIndex && merchant != null && indexNewService.isNewIndex(merchantId)) {
                // 1. 将所有参数添加到 RedirectAttributes
                request.getParameterMap().forEach((key, values) -> {
                    for (String value : values) {
                        redirectAttributes.addAttribute(key, value);
                    }
                });
                // 新首页用户跳新首页
                String baseUrl = request.getHeader("x-forwarded-proto") + "://" + request.getHeader("host");
                return new ModelAndView("redirect:" + baseUrl + "/new/");
            }

            Boolean isApplyIndexV2 = cmsAppProperties.getIsApplyIndexV2();
            String cmsIndexV2Html = null;
            if (BooleanUtils.isTrue(isApplyIndexV2) && StringUtils.isNotEmpty(cmsAppProperties.getIndexV2PageUrl())) {
                try {
                    // 获取pc首页cms html 内容
                    String cmsIndexUrl = StringUtils.join(cmsAppProperties.getCmsAssetsDomain(), cmsAppProperties.getIndexV2PageUrl());
                    cmsIndexV2Html = IOUtils.toString(new URL(cmsIndexUrl), "UTF-8");
                } catch (Exception e) {
                    LOGGER.warn("获取首页V2 CMS html 异常，使用首页V1，merchantId：{}", merchantId, e);
                    isApplyIndexV2 = false;
                }
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("首页，merchantId：{}，isApplyIndexV2：{}，cmsIndexV2Html：{}", merchantId, isApplyIndexV2, cmsIndexV2Html);
            }
            if (BooleanUtils.isTrue(isApplyIndexV2) && StringUtils.isNotEmpty(cmsIndexV2Html)) {
                return this.indexV2(modelMap, request, merchant, merchantId, cmsIndexV2Html);
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            modelMap = indexService.getModelMap(modelMap, request, AppModuleCategoryEnum.AppModuleUp.UP_FALSE.getId(), branchCode);

            if (null != merchantId && merchantId>0){
                JSONObject event = new JSONObject();
                event.put("event", "APP_INDEX_EXPOSURE");
                event.put("merchantId", merchantId);
                event.put("time", System.currentTimeMillis());
                appRocketMqFactory.sendMsgShoppingTraceWatchByCrm(event);
            }

            //实名认证首页弹窗
            Map<String,Object> map = merchantAuthenticationBusinessApi.getMerchantAuthenticationAlert(merchantId, branchCode,1);
            modelMap.addAttribute("alertFlag",map.get("auth_alertFlag"));
            modelMap.addAttribute("alertFlag1",map.get("change_alertFlag"));

            Long count =null;
            if(merchant != null){
                count = licensePoolBusinessApi.queryCountByMerchantId(merchant.getId());
            }
            modelMap.addAttribute("count",count);
            return new ModelAndView("/index.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("PC布局加载异常:" + ExceptionUtils.getStackTrace(e),e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    private ModelAndView indexV2(ModelMap modelMap, HttpServletRequest request, MerchantBussinessDto merchant,
                                 Long merchantId, String cmsIndexHtml) {
        try {
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);

            List<CategoryVo> listCategory = categoryBusinessApi.getCategoryTreeForPcIndex(branchCode);
            modelMap.put("categorys", listCategory);
            modelMap.put("skuImageUrl", config.getProductImagePathUrl());
            modelMap.put("noticeDetails", indexService.listIndexNotices(branchCode));
            modelMap.put("merchantId", merchantId);
            modelMap.put("merchant", merchant);
            modelMap.put("branchCode", branchCode);

            // 获取当前使用的PC首页
            Long pageId = null;
            String pageVersion = "";
            // 指定首页（用于管理后台的PC预览场景）
            String pcVersion = request.getParameter("pcVersion");
            if (NumberUtils.toLong(pcVersion) > 0) {
                pageId = NumberUtils.toLong(pcVersion);
            }
            CmsPcIndexDTO cmsPcIndexDTO = indexService.listUsingPcIndexLayoutModulesForV2(merchantId, pageId);
            // 头图
            CmsPcIndexLayoutModuleDTO moduleHeader = null;
            // 轮播
            List<CmsPcIndexLayoutModuleItemDTO> moduleBanner = null;
            if (Objects.nonNull(cmsPcIndexDTO)) {
                pageId = cmsPcIndexDTO.getPageId();
                pageVersion = cmsPcIndexDTO.getPageVersion();
                List<CmsPcIndexLayoutModuleDTO> modules = cmsPcIndexDTO.getModules();
                if (CollectionUtils.isNotEmpty(modules)) {
                    for (CmsPcIndexLayoutModuleDTO module : modules) {
                        // 1021-头部广告和1001-轮播图
                        if (Objects.equals(module.getModuleType(), 1021)) {
                            moduleHeader = module;
                        } else if (Objects.equals(module.getModuleType(), 1001)) {
                            moduleBanner = module.getItems();
                        }
                    }
                }else{
                    log.error("modules is empty,pageId={}", pageId);
                }
            } else {
                pageId = CmsConstants.PC_INDEX_ID_NOT_EXISTS;
            }
            modelMap.put("pageId", pageId);
            modelMap.put("pageVersion", pageVersion);
            modelMap.put("moduleHeader", moduleHeader);
            modelMap.put("moduleBanner", moduleBanner);

            // 获取pc首页cms html 内容
            String cmsIndexHtmlPcMainCssName = this.getCmsIndexHtmlPcMainCssName(cmsIndexHtml);
            String cmsIndexHtmlContent = this.getCmsIndexHtmlBody(cmsIndexHtml);
            modelMap.put("assetsDomain", cmsAppProperties.getCmsAssetsDomain());
            modelMap.put("cmsIndexHtmlPcMainCssName", cmsIndexHtmlPcMainCssName);
            modelMap.put("cmsIndexHtmlContent", cmsIndexHtmlContent);

            if (Objects.nonNull(merchantId) && merchantId > 0) {
                JSONObject event = new JSONObject();
                event.put("event", "APP_INDEX_EXPOSURE");
                event.put("merchantId", merchantId);
                event.put("time", System.currentTimeMillis());
                appRocketMqFactory.sendMsgShoppingTraceWatchByCrm(event);
            }

            //实名认证首页弹窗
            Map<String, Object> map = merchantAuthenticationBusinessApi.getMerchantAuthenticationAlert(merchantId, branchCode, 1);
            modelMap.addAttribute("alertFlag", map.get("auth_alertFlag"));
            modelMap.addAttribute("alertFlag1", map.get("change_alertFlag"));

            Long count = null;
            if (Objects.nonNull(merchantId) && merchantId > 0) {
                count = licensePoolBusinessApi.queryCountByMerchantId(merchantId);
            }
            modelMap.addAttribute("count", count);
            return new ModelAndView("/indexV2.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("请求PC首页V2异常", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    private String getCmsIndexHtmlPcMainCssName(String cmsIndexHtml) {
        if (StringUtils.isEmpty(cmsIndexHtml)) {
            return null;
        }
        Pattern compile = Pattern.compile("pc-main\\.(.+)\\.css");
        Matcher matcher = compile.matcher(cmsIndexHtml);
        while (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private String getCmsIndexHtmlBody(String cmsIndexHtml) {
        if (StringUtils.isEmpty(cmsIndexHtml)) {
            return null;
        }
        int startIndex = cmsIndexHtml.indexOf(">", cmsIndexHtml.indexOf("<body"));
        int endIndex = cmsIndexHtml.lastIndexOf("</body>");
        return cmsIndexHtml.substring(startIndex + 1, endIndex);
    }

    @RequestMapping(value = "/categoryTree")
    public ModelAndView categoryTree(ModelMap modelMap,HttpServletRequest request){
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        Long merchantId = 0L;
        if (merchant != null) {
            merchantId = merchant.getId();
        }
        String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
        List<CategoryVo> categoryTree = indexService.getCategoryTree(branchCode);
        modelMap.put("categorys",categoryTree);
        return new ModelAndView("/categoryTree.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("PC分类加载异常:" + ExceptionUtils.getStackTrace(e),e);
            return new ModelAndView("/error/staticCategory.ftl");
        }
    }
    /**
     * @param modelMap
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @throws
     * @Description: 登录后跳转到新首页（如果需要跳转到最后访问页面，需要将最后访问页面传过来）
     * <AUTHOR>
     * @date 2018/8/21 17:35
     */
    @RequestMapping("/indexCat.htm")
    public ModelAndView indexCat(ModelMap modelMap, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            modelMap = indexService.getModelMap(modelMap, request, AppModuleCategoryEnum.AppModuleUp.UP_FALSE.getId(), branchCode);
            return new ModelAndView("/indexCat.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("PC布局加载异常:" + ExceptionUtils.getStackTrace(e),e);
            return new ModelAndView("/error/500.ftl");
        }
    }


    /**
     * @param modelMap
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @throws
     * @Description:登录后跳转到新首页（如果需要跳转到最后访问页面，需要将最后访问页面传过来）
     * <AUTHOR>
     * @date 2018/8/21 17:35
     */
    @RequestMapping("/indexList.htm")
    public ModelAndView list(ModelMap modelMap, HttpServletRequest request) throws Exception {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            modelMap = indexService.getModelMap(modelMap, request, AppModuleCategoryEnum.AppModuleUp.UP_TRUE.getId(), branchCode);
            return new ModelAndView("/indexList.ftl", modelMap);
        } catch (Exception e) {
            LOGGER.error("PC布局加载异常:" + ExceptionUtils.getStackTrace(e));
            return new ModelAndView("/error/500.ftl");
        }
    }


    /**
     * 得到头部数据
     *
     * @param modelMap
     * @param request
     * @return
     * @throws ServiceException
     */
    @RequestMapping(value = "/header_data.json", method = RequestMethod.GET)
    @ResponseBody
    public Object header_data(ModelMap modelMap, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("PC头部数据请求：{}",JSONObject.toJSONString(merchant));
            }
            //得到购物车总数量
            int merchantCartCount = 0;
            List<DicAreaBusinessDto> provinceList = new ArrayList<>();
            Long merchantId = 0L;
            if (Objects.equals(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId(), merchant.getAccountRole())){
                merchantCartCount = shoppingCartBusinessApi.getCartNumSubAccount(merchant.getId(),merchant.getAccountId());
            }else {
                merchantId = merchant.getId();
                //得到购物车总数量
                merchantCartCount = shoppingCartBusinessApi.getCartNum(merchantId);
            }
            String branchCode = this.getBranchCodeByMerchantId(request,merchantId);
            //得到热搜关键字
//            HotSearchBusinessDto hotSearch = new HotSearchBusinessDto();
//            hotSearch.setType(1);
//            hotSearch.setKeyword(null);
//            hotSearch.setBranchCode(branchCode);
//            List<HotSearchBusinessDto> listHostSearch = hostSearchBusinessApi.selectList(hotSearch);
            Map<String, Object> model = new HashMap<String, Object>();
//            model.put("listHostSearch", listHostSearch);
            model.put("merchantCartCount", merchantCartCount);
            model.put("merchant", merchant);
            ProvinceBranchEnum branchEnum = ProvinceBranchEnum.getEnumByBranchCode(branchCode);
            model.put("province",branchEnum.getProvince());
            model.put("branchCode",branchCode);
            Map<String, Object> map = addResult("header_data", model);
            return map;
        } catch (Exception e) {
            LOGGER.error("头部加载错误:" + ExceptionUtils.getStackTrace(e));
            return new ModelAndView("/error/500.ftl");
        }
    }


    /**
     * 得到导航数据
     *
     * @param modelMap
     * @param request
     * @return
     * @throws ServiceException
     */
    @RequestMapping(value = "/header_guide.json", method = RequestMethod.GET)
    @ResponseBody
    public Object header_guide(ModelMap modelMap, HttpServletRequest request) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        String thisPath = request.getParameter("thisPath");
        String requestUrl = "";
        if (StringUtils.isNotEmpty(thisPath)) {
            byte[] path = Base64Util.decode(thisPath);
            requestUrl = new String(path);
        }
        Long merchantId;
        if (merchant != null) {
            merchantId = merchant.getId();
        } else {
            merchantId = 0L;
        }
        /* 查询抬头数据 */
        String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
        //获取缓存
        List<HeaderGuideBuineseDto> headerGuideBuineseDtoList;
        if (layoutGraySwitchUtils.isOpenGrayByBranchForOnePiece(branchCode)) {
            ApiRPCResult<List<HeaderGuideBuineseDto>> apiRPCResult =
                    pcHeaderGuideBuineseApi.listUsingHeaderGuide(merchantId, appProperties.getPcHeaderShowNum());
            if (!apiRPCResult.isSuccess()) {
                LOGGER.error("【布局pc导航】header_guide.json请求失败", apiRPCResult.getMsg());
                headerGuideBuineseDtoList = Lists.newArrayList();
            } else {
                headerGuideBuineseDtoList = apiRPCResult.getData();
            }
        } else {
            headerGuideBuineseDtoList = headerGuideBuineseApi.getUseingFromDb(merchantId, branchCode);
        }
        /* 请求url如果和头部url一致的话,在原样式后增加 cur样式 表示选中 */
        if (StringUtils.isNotEmpty(requestUrl) && CollectionUtils.isNotEmpty(headerGuideBuineseDtoList)) {
            for (HeaderGuideBuineseDto headerGuideBuineseDto : headerGuideBuineseDtoList) {
                if (StringUtils.isNotEmpty(headerGuideBuineseDto.getUrl()) && requestUrl.equals(headerGuideBuineseDto.getUrl())) {
                    headerGuideBuineseDto.setClassName("cur");
                }
            }
        }
        modelMap.put("merchant", merchant);
        modelMap.put("headerGuideList", headerGuideBuineseDtoList);
        return addResult("header_guide", modelMap);
    }

    /**
     * @return
     * <AUTHOR>
     * @Description PC 获取易客通配置的H5聊天页面URL
     * @Date 2018/10/22 18:48
     * @Param
     **/
    @RequestMapping("/getIMPackUrl")
    @ResponseBody
    public Object getIMPackUrl() {
        try {
            Map<String, String> map = null;
            return this.addResult("data", map);
        } catch (Exception e) {
            LOGGER.error("获取易客通配置的H5聊天页面URL异常", e);
            return this.addError("获取易客通配置的H5聊天页面URL异常");
        }
    }

    /**
     * 获取诸葛配置信息
     *
     * @return
     */
    @RequestMapping("/getZhugeConfigInfo")
    @ResponseBody
    public Map<String, String> getZhugeConfigInfo() {
        String zhugeAppKey = zhugeConfig.getZhugeAppKey();
        String zhugeAppUrl = zhugeConfig.getZhugeAppUrl();
        Map<String, String> map = new HashMap<>();
        map.put("appKey", zhugeAppKey);
        map.put("appUrl", zhugeAppUrl);
        return map;
    }

    @RequestMapping(value = "/getAllCategory")
    @ResponseBody
    public Object getAllCategory(HttpServletRequest request){
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            List<CategoryVo> categoryTree = indexService.getCategoryTree(branchCode);
            return this.addResult("data", categoryTree);
        } catch (Exception e) {
            LOGGER.error("PC查询分类异常:" + ExceptionUtils.getStackTrace(e),e);
            return this.addError("PC查询分类异常");
        }
    }
}
