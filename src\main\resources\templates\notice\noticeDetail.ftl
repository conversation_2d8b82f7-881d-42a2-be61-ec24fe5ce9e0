<!DOCTYPE HTML>
<html>

	<head>
		<#include "/common/common.ftl" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<title>网站公告</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
        <script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/zhuge/zhugeio.js?t=${t_v}"></script>
        <link rel="stylesheet" href="/static/css/search.css?t=${t_v}" />
		<style type="text/css">
			.sui-breadcrumb a{
				color: #666;
			}
			.sui-breadcrumb > .active {
			    color: #31cb96;
			}
			.sui-breadcrumb>li+li:before {
			    content: " ";
			    padding: 0;
			}
			.notice-detail{
				border-top: 1px solid #ccc;
			}
			.notice-detail-title{
				height: 100px;
				line-height: 100px;
				text-align: center;
				font-size: 30px;
				color: #333;
			}
			.notice-detail-info{
				height: 30px;
				line-height: 30px;
				padding: 0 20px;
				background: #f5f5f5;
				color: #999;
			}
			.notice-detail-text{
				padding: 20px 0;
			}
			.notice-detail-text p{
				padding: 5px 0;
				line-height: 26px;
				color: #333;
				text-indent: 2em;
			}
			.notice-detail-bot{
				margin: 20px 0 100px 0;
			}
			.notice-detail-bot a{
				color: #31cb96;
				display: inline-block;
				width: 40%;
			}
			.notice-detail-bot a:hover{
				color: #f39800;
			}
			.notice-detail-bot .next-btn{
				float: right;
				text-align: right;
			}
			.notice-detail-bot .gray-btn{
				color: #999;
			}
			

		</style>

	</head>

	<body>
		<div class="container">
            <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
            <input type="hidden" id="noticeTitle" name="noticeTitle" value="${noticeDetail.noticeTitle}"/>
            <input type="hidden" id="id" name="id" value="${noticeDetail.id}"/>
            <!--头部导航区域开始-->
            <div class="headerBox" id="headerBox">
			<#include "/common/header.ftl" />
            </div>
            <!--头部导航区域结束-->

			<!--主体部分开始-->
			<div class="main">
				<div class="notice row">
					<!--面包屑-->
					<ul class="sui-breadcrumb">
					  <li><a href="/">首页</a>></li>
					  <li><a href="/notice/noticeList">网站公告</a>></li>
					  <li class="active"><#if noticeDetail.noticeTitle??>${noticeDetail.noticeTitle?replace('&nbsp;','','i')}</#if></li>
					</ul>
					<div class="notice-detail">
						<h1 class="notice-detail-title">${noticeDetail.noticeTitle}</h1>
						<div class="notice-detail-info">发布时间：<#if noticeDetail.createTime??>${noticeDetail.createTime?string('yyyy-MM-dd HH:mm:ss')}</#if> 作者：${noticeDetail.example1}</div>
						<div class="notice-detail-text">
							${noticeDetail.noticeText}
						</div>

						<#if !previousId?? && nextId??>
                            <div class="notice-detail-bot clear">
                                <span class="pre-btn gray-btn">没有上一条</span>
                                <a href="/notice/noticeDetail?id=${nextId}" class="next-btn text-overflow">[下一条]</a>
                            </div>
						</#if>

						<#if previousId?? && nextId??>
							<div class="notice-detail-bot clear">
                                <a href="/notice/noticeDetail?id=${previousId}" class="pre-btn text-overflow">[上一条]</a>
								<a href="/notice/noticeDetail?id=${nextId}" class="next-btn text-overflow">[下一条]</a>
							</div>
						</#if>

						<#if previousId?? && !nextId??>
                            <div class="notice-detail-bot clear">
                                <a href="/notice/noticeDetail?id=${previousId}" class="pre-btn text-overflow">[上一条]</a>
                                <span href="#" class="next-btn gray-btn">没有下一条</span>
                            </div>
						</#if>

						<#if !previousId?? && !nextId??>
							<div class="notice-detail-bot clear">
                                <span class="pre-btn gray-btn">没有上一条</span>
                                <span href="#" class="next-btn gray-btn">没有下一条</span>
							</div>
						</#if>
						<#--<div class="notice-detail-bot clear">-->
							<#--<a href="#" class="pre-btn text-overflow">[上一条]上一条信息</a>-->
							<#--<a href="#" class="next-btn text-overflow">下一条信息下一条信息[下一条]</a>-->
						<#--</div>-->
						<#--<!--无上下页&ndash;&gt;-->
						<#--<div class="notice-detail-bot clear">-->
							<#--<span class="pre-btn gray-btn">[上一条]没有上一条</span>-->
							<#--<span href="#" class="next-btn gray-btn">没有下一条[下一条]</span>-->
						<#--</div>-->
					</div>
				</div>
			</div>
			<!--主体部分结束-->

            <!--底部导航区域开始-->
            <div class="footer" id="footer">
			<#include "/common/footer.ftl" />
            </div>
            <!--底部导航区域结束-->
			
		</div>

		<script type="text/javascript" src="/static/js/jquery-1.11.3.min.js"></script>
		<script type="text/javascript" src="/static/js/sui.min.js"></script>
		<script type="text/javascript" src="/static/js/common.js"></script>
		<script type="text/javascript" src="/static/js/help.js"></script>
		<script type="text/javascript">
			$(function(){
				$(".side-help .link-about").addClass("active");
                // zhuge.track('pc_action_Home_Notice', {
                //     'merchantId' : $("#merchantId").val(),
                //     'noticeID' : $("#id").val(),
                //     'noticeName' : $("#noticeTitle").val()
                // });
                webSdk.track('pc_action_Home_Notice', {
                    'merchantId' : $("#merchantId").val(),
                    'noticeID' : $("#id").val(),
                    'noticeName' : $("#noticeTitle").val()
                });
			})
		</script>
	</body>

</html>