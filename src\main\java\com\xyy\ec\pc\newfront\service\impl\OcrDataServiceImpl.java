package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import com.xyy.ec.pc.newfront.dto.OcrAnalysisResultRespVO;
import com.xyy.ec.pc.newfront.dto.OcrDTO;
import com.xyy.ec.pc.newfront.dto.OcrParseRespVO;
import com.xyy.ec.pc.newfront.enums.TypeEnum;
import com.xyy.ec.pc.newfront.service.OcrDataService;
import com.xyy.ec.pc.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-06-19 17:43
 */
@Service
@Slf4j
public class OcrDataServiceImpl implements OcrDataService {

    /**
     * 测试环境域名 //https://xyy-ocr.test.ybm100.com
     * 环境域名 //https://xyy-ocr.ybm100.com
     */
   @Value("${xyy.erp.ocr.urlBase}")
   private String xyyErpOcrUrlBase;

    /**
     * asyncOcr/identify
     */
   @Value("${identify.url}")
   private String identifyUrl;

    /**
     * asyncOcr/getData
     */
    @Value("${getData.url}")
    private String getDataUrl;

    /**
     * 店铺类型
     * 4, 8, 9, 10, 14, 15, 6, 7 为医疗
     */
    static List<Integer> shopType= Arrays.asList(4, 8, 9, 10, 14, 15, 6, 7);

    @Override
    public OcrAnalysisResultRespVO identify(Integer type, String imgUrl) {
        log.info("ocr identify type:{},imgUrl:{}",type,imgUrl);
        if (StringUtils.isBlank(imgUrl)){
            throw new RuntimeException("imgUrl can not be empty");
        }
        TypeEnum ocrType= getType(type);
        OcrDTO.OcrDTOBuilder OcrDTOBuilder = OcrDTO.builder().imgUrl(imgUrl).type(ocrType);
        String param = JSONObject.toJSONString(OcrDTOBuilder.build());
        try {
            String url = xyyErpOcrUrlBase + identifyUrl;
            String data = HttpClientUtil.doPost(url, param,null);
            log.info("ocr identify url:{},param:{},data:{}", url, OcrDTOBuilder.build(), data);
            Map<String, Object> httpMap = JSON.parseObject(data, new TypeReference<Map<String, Object>>(){});

            OcrAnalysisResultRespVO ocrAnalysisResultRespVO = new OcrAnalysisResultRespVO();
            ocrAnalysisResultRespVO.setDataKey( httpMap.get("result").toString());
            ocrAnalysisResultRespVO.setType(ocrType.getType());
            return ocrAnalysisResultRespVO;
        } catch (IOException e) {
            log.error("ocr identify error",e);
            throw new RuntimeException("ocr identify error");
        }

    }


    @Override
    public OcrParseRespVO getData(String key, String type) {
        OcrParseRespVO ocrParseRespVO = new OcrParseRespVO();
        ocrParseRespVO.setStatus(1);
        if (StringUtils.isBlank(key)){
            throw new RuntimeException("key can not be empty");
        }
        OcrDTO  ocrDTO = OcrDTO.builder().uuid(key).build();
        try {
            String url=xyyErpOcrUrlBase+getDataUrl;
            String data = HttpClientUtil.doPost(url,JSONObject.toJSONString(ocrDTO),null);
            log.info("OcrDataServiceImpl getData data:{}",data);
            Map<String, Object> map = JSON.parseObject(data, new TypeReference<Map<String, Object>>(){});
            Integer code =(Integer)map.get("code");
            Integer status =statusMapping(code);
            ocrParseRespVO.setStatus(status);
            ocrParseRespVO.setTitle(TypeEnum.ofType(type).getDesc());
            if (StringUtils.equals(TypeEnum.LICENSE.getType(), type)) {
                return getLicenseOcrDTO(map.get("result"),ocrParseRespVO);
            } else {
                return getGeneralStructureOcrDTO(map.get("result"),ocrParseRespVO);
            }
        } catch (Exception e) {
            log.error("OcrDataServiceImpl getData error",e);
        }
        return ocrParseRespVO;
    }

    private TypeEnum getType(Integer type) {
        return   shopType.contains(type)?TypeEnum.GENERAL_STRUCTURE:TypeEnum.LICENSE;
    }

    /**
     * code  100 -> status 3
     * code  200 -> status 2
     * code  500 -> status 1
     * 解析状态 (1、解析失败，2、解析成功，3、正在解析
     */
    private Integer statusMapping(Integer code) {
        if (Objects.equals(100, code)) {
            return 3;
        } else if (Objects.equals(200, code)) {
            return 2;
        }
        return 1;
    }

    private Map<String, Object> getResultMap(Object result) {
        return JSON.parseObject(JSON.toJSONString(result), new TypeReference<Map<String, Object>>(){});
    }

    private OcrParseRespVO getLicenseOcrDTO(Object result,OcrParseRespVO ocrParseRespVO) throws ParseException {
        Map<String, Object> resultMap= getResultMap(result);
        Object validFromDate = resultMap.get("validFromDate");
        try {
            if (Objects.nonNull(validFromDate)&&!Objects.equals("",validFromDate)){
                SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
                ocrParseRespVO.setBeginTime(sdf1.parse(validFromDate.toString()));
            }
            Object validToDate = resultMap.get("validToDate");
            if (Objects.nonNull(validToDate)&&!Objects.equals("",validToDate)){
                SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
                ocrParseRespVO.setEndTime(sdf2.parse(validToDate.toString()));
            }
        } catch (ParseException e) {
            log.error("parse time error",e);
        }

        ocrParseRespVO.setBusinessAddress(resultMap.get("businessAddress").toString());
        ocrParseRespVO.setCompanyName(resultMap.get("companyName").toString());
        ocrParseRespVO.setCreditCode(resultMap.get("creditCode").toString());
        return ocrParseRespVO;

    }

    static List<String> key= Arrays.asList("备案编号","登记号","IC号");

    static List<String> key1= Arrays.asList("企业名称","单位","机构全称","企业名称(名称)","企业名称（名称）","医疗机构名称","诊所名称","机构名称","机构名","名称");
    private OcrParseRespVO getGeneralStructureOcrDTO(Object result,OcrParseRespVO ocrParseRespVO) throws ParseException {
        Map<String, Object> resultMap= getResultMap(result);
        Map<String,String> data = JSON.parseObject(JSON.toJSONString(resultMap.get("Data")), new TypeReference<Map<String, String>>(){});
        if (Objects.isNull(data)||data.size()<6){
            return ocrParseRespVO;
        }
        for (String s : key) {
            if (StringUtils.isNotBlank(data.get(s))){
                ocrParseRespVO.setCreditCode(data.get(s));
                break;
            }
        }
        for (String s : key1) {
            if (StringUtils.isNotBlank(data.get(s))){
                ocrParseRespVO.setCompanyName(data.get(s));
                break;
            }
        }
        String businessAddress = data.get("地址");
        if (StringUtils.isNotBlank(ocrParseRespVO.getCompanyName())){
            ocrParseRespVO.setBusinessAddress(businessAddress);
        }
        try {
            String time = data.get("有效期限");
            if (StringUtils.isNotBlank(time)){
                String[] split = time.replace("自","").split("至");
                if (2==split.length){
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
                    ocrParseRespVO.setBeginTime(simpleDateFormat.parse(split[0]));
                    ocrParseRespVO.setEndTime(simpleDateFormat.parse(split[1]));
                }
            }
        } catch (ParseException e) {
            log.error("parse time error",e);
        }
        return ocrParseRespVO;
    }


}
