package com.xyy.ec.api.rpc.user;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.exception.BusiCommonException;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.MerchantPoiBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantPoiBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.PoiRequestBussinessDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/15 09:59
 * @Description:用户接口
 */
@Component
@Slf4j
public class UserRpcService {
    @Reference(version = "1.0.0")
    MerchantPoiBussinessApi merchantPoiBussinessApi;
    private static final String USER_ERROR = "会员服务异常";

    /**
     * 搜索地址
     * @param poiDto
     * @param pageNum
     * @param pageSize
     * @return
     */
    public PageInfo<MerchantPoiBusinessDto> findPageMerchantInfoList(PoiRequestBussinessDto poiDto, Integer pageNum, Integer pageSize){
        try {
            return merchantPoiBussinessApi.findPageMerchantInfoList(poiDto,pageNum,pageSize);
        } catch (Exception e) {
            throw new BusiCommonException(ApiResultCodeEum.NETWORK_ERROR.getCode(),ApiResultCodeEum.NETWORK_ERROR.getMsg(),USER_ERROR,e);
        }
    }

    /**
     *检测新增店铺名称是否重复
     * @return
     */
    public ApiRPCResult checkPoiRelationAndExist(Integer poiId, String shopName){
        try {
            return merchantPoiBussinessApi.checkPoiRelationAndExist(poiId, shopName);
//            throw new BusiCommonException(ApiResultCodeEum.NETWORK_ERROR.getCode(),ApiResultCodeEum.NETWORK_ERROR.getMsg(),USER_ERROR);
        } catch (Exception e) {
            throw new BusiCommonException(ApiResultCodeEum.NETWORK_ERROR.getCode(),ApiResultCodeEum.NETWORK_ERROR.getMsg(),USER_ERROR,e);
        }
    }
}
