package com.xyy.ec.pc.remote.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.order.backend.order.api.OrderApi;
import com.xyy.ec.order.backend.order.dto.MerchantSkuSumDto;
import com.xyy.ec.pc.remote.OrderBackendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderBackendServiceImpl implements OrderBackendService {

    @Value("${order.sku.promoTag.ratio:20}")
    public Integer promoTagRatio;
    @Reference(version = "1.0.0")
    private OrderApi orderBackendApi;

    @Override
    public Map<Long, String> queryPromoTag(List<Long> skuIds) {
        if (CollUtil.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        Map<Long, String> skuId2promoTag = new HashMap<>(skuIds.size());

        try {
            ApiRPCResult<List<MerchantSkuSumDto>> rpcResult = orderBackendApi.queryMerchantSkuSum(skuIds);
            if (rpcResult.isSuccess() && rpcResult.getData() != null) {
                for (MerchantSkuSumDto datum : rpcResult.getData()) {
                    skuId2promoTag.put(datum.getSkuId(), orderSumToPromoTag(datum.getMnCount()));
                }
            }
            log.info("queryPromoTag skuIds:{} rpcResult:{}", JSON.toJSONString(skuIds), JSON.toJSONString(rpcResult));
        } catch (Exception e){
            log.error("queryMerchantSkuSum error:{}", JSON.toJSONString(skuIds), e);
        }

        return skuId2promoTag;
    }

    private String orderSumToPromoTag(Long orderSum) {
        if (orderSum == null) {
            return null;
        }
        Long resizeOrderSum = orderSum * promoTagRatio;
        String promoTag;
        if (resizeOrderSum < 1000) {
            promoTag = null;
        } else if (resizeOrderSum < 5000) {
            promoTag = "已有1000+人选择下单";
        } else if (resizeOrderSum < 10000) {
            promoTag = "已有5000+人选择下单";
        } else if (resizeOrderSum < 50000) {
            promoTag = "已有1万+人选择下单";
        } else if (resizeOrderSum < 100000) {
            promoTag = "已有5万+人选择下单";
        } else {
            promoTag = "已有10万+人选择下单";
        }
        return promoTag;
    }

}
