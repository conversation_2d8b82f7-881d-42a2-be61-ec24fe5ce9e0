package com.xyy.ec.api.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.api.rpc.hyperspace.HyperspaceRpc;
import com.xyy.ec.api.service.pager.RAMPager;
import com.xyy.ec.marketing.common.domain.ActivityInfo;
import com.xyy.ec.marketing.common.domain.ActivitySkuInfo;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.shop.server.business.params.ShopProductQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/21 20:40
 * @Description:
 */
@Slf4j
@Service
public class ActService {

    private static final int SOLD_OUT_STATUS = 2;//已售罄
    @Autowired
    private   ProductService productService;
    @Autowired
    private HyperspaceRpc hyperspaceRpc;

    public PageInfo<ProductDTO> pagingShopProducts(ShopProductQueryParam param , Integer realPageNum, Integer realPageSize,Long actId) {
        List<Long> shopCsuIds = new ArrayList<>();
        ActivityInfo activityInfo = hyperspaceRpc.getShopMarketingActivityById(actId);
        if (activityInfo==null || activityInfo.getActivitySkuInfoList()==null){
            return new PageInfo<>();
        }
        shopCsuIds = activityInfo.getActivitySkuInfoList().stream().map(ActivitySkuInfo::getSkuId).collect(Collectors.toList());
//        shopCsuIds.add(243879L);
//        shopCsuIds.add(407630L);

        //优化控销逻辑
        shopCsuIds =  productService.controlFilterCsuIdsForIsNotPurchase(param.getMerchantId(), param.getBranchCode(), shopCsuIds);

        //分页处理
        RAMPager<Long> pager = new RAMPager<>(shopCsuIds,realPageSize);
        List<Long> csuIdsForPage = pager.page(realPageNum);

        //查询商品信息
        List<ProductDTO> productDTOS = productService.fillProductInfo(csuIdsForPage, param.getMerchantId(), param.getBranchCode());
        /**
         * 1、已售罄置底
         * 2、ID升序排序
         */
        List<ProductDTO> realProducts = productDTOS.stream()
                .sorted(Comparator.comparing(ProductDTO::getStatus,(x, y)->{
                    if (y.equals(SOLD_OUT_STATUS)) return -1;
                    return x.compareTo(y);
                }).thenComparing(ProductDTO::getStatus))
                .collect(Collectors.toList());

        PageInfo<ProductDTO> pageInfo = new PageInfo(realProducts);
        pageInfo.setTotal(pager.getTotal());
        pageInfo.setPages(pager.getPageCount());
        pageInfo.setPageNum(realPageNum);
        pageInfo.setPageSize(realPageSize);
        return pageInfo;
    }
}
