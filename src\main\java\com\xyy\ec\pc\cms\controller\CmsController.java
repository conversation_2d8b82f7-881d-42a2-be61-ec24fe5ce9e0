package com.xyy.ec.pc.cms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.layout.buinese.ecp.enums.CmsEventTrackingSpTypeEnum;
import com.xyy.ec.layout.buinese.ecp.enums.CmsSelectProductStrategyTypeEnum;
import com.xyy.ec.layout.buinese.ecp.params.CmsEventTrackingSpIdGenerateParam;
import com.xyy.ec.layout.buinese.ecp.results.CmsPcIndexLayoutModuleDTO;
import com.xyy.ec.layout.buinese.ecp.utils.CmsEventTrackingUtils;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.cms.constants.CmsConstants;
import com.xyy.ec.pc.cms.dto.CmsRequestDTO;
import com.xyy.ec.pc.cms.enums.LayoutComponentEnum;
import com.xyy.ec.pc.cms.helpers.CmsListProductVOHelper;
import com.xyy.ec.pc.cms.helpers.CmsRequestHelper;
import com.xyy.ec.pc.cms.param.CmsExpectProductQueryParam;
import com.xyy.ec.pc.cms.param.CmsRequestParam;
import com.xyy.ec.pc.cms.param.CmsSeckillProductExpectQueryParam;
import com.xyy.ec.pc.cms.service.CmsService;
import com.xyy.ec.pc.cms.service.CmsSkuService;
import com.xyy.ec.pc.cms.service.complement.ComplementQueryService;
import com.xyy.ec.pc.cms.utils.LayoutBranchUtils;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.enums.LayoutMerchantStatusEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.helper.ProductDataFilterHelper;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.remote.MerchantBusinessRemoteService;
import com.xyy.ec.pc.remote.ProductExhibitionGroupBusinessAdminRemoteService;
import com.xyy.ec.pc.remote.ProductForLayoutRemoteService;
import com.xyy.ec.pc.remote.ShopQueryRemoteService;
import com.xyy.ec.pc.rpc.ProductServiceRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.layout.LayoutBaseService;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.search.engine.enums.PlatformEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/cms")
public class CmsController extends BaseController {

    @Autowired
    private CmsSkuService cmsSkuService;

    @Autowired
    private LayoutBaseService layoutBaseService;

    @Autowired
    private LayoutBranchUtils layoutBranchUtils;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private CmsAppProperties cmsAppProperties;

    @Autowired
    private MerchantBusinessRemoteService merchantBusinessRemoteService;

    @Autowired
    private ProductForLayoutRemoteService productForLayoutRemoteService;

    @Autowired
    private ShopQueryRemoteService shopQueryRemoteService;

    @Autowired
    private PcVersionUtils pcVersionUtils;

    @Autowired
    private ProductExhibitionGroupBusinessAdminRemoteService productExhibitionGroupBusinessAdminRemoteService;

    @Autowired
    private CmsService cmsService;

    @Autowired
    private ComplementQueryService complementQueryService;

    @Autowired
    private ProductServiceRpc productServiceRpc;

    @Autowired
    private AppProperties appProperties;

    /**
     * 获取pc首页配置信息
     *
     * @param terminalType
     * @param cmsRequestParam
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/index/v2/getConfigInfo", method = {RequestMethod.GET, RequestMethod.POST})
    public XyyJsonResult getIndexV2ConfigInfo(@RequestHeader(name = "terminalType", required = false) Integer terminalType,
                                              CmsRequestParam cmsRequestParam, HttpServletRequest request) {
        try {
            CmsRequestDTO cmsRequestDTO = CmsRequestHelper.createDTO(cmsRequestParam);
            if (cmsRequestDTO == null) {
                cmsRequestDTO = new CmsRequestDTO();
            }
            String pageIdStr = cmsRequestDTO.getPageId();
            if (StringUtils.isEmpty(pageIdStr)) {
                return XyyJsonResult.createFailure().msg(CmsConstants.MSG_ERROR);
            }
            Long pageId = null;
            try {
                pageId = Long.parseLong(pageIdStr);
            } catch (Exception e) {
            }
            if (Objects.isNull(pageId)) {
                return XyyJsonResult.createFailure().msg(CmsConstants.MSG_ERROR);
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = null;
            if (Objects.nonNull(merchant)) {
                merchantId = merchant.getId();
            }

            // 获取首页配置信息
            List<CmsPcIndexLayoutModuleDTO> modules;

            if (Objects.equals(pageId, CmsConstants.PC_INDEX_ID_NOT_EXISTS)) {
                modules = Lists.newArrayList();
            } else {
                modules = cmsService.listPcIndexLayoutModulesByPageIdForV2(merchantId, pageId);
            }

            // 埋点
            CmsEventTrackingSpTypeEnum spTypeEnum = CmsEventTrackingSpTypeEnum.PC_INDEX;
            String spType = spTypeEnum.getSpType();
            if (StringUtils.isEmpty(cmsRequestDTO.getSptype())) {
                cmsRequestDTO.setSptype(spType);
            }
            if (StringUtils.isEmpty(cmsRequestDTO.getSpid())) {
                String spId = CmsEventTrackingUtils.generateSpId(CmsEventTrackingSpIdGenerateParam.builder()
                        .spTypeEnum(spTypeEnum).build());
                cmsRequestDTO.setSpid(spId);
            }
            if (StringUtils.isEmpty(cmsRequestDTO.getSid())) {
                String sid = SearchUtils.generateNewSidData(merchantId, PlatformEnum.WEB.getValue());
                cmsRequestDTO.setSid(sid);
            }
            CmsRequestParam resultCmsRequestParam = CmsRequestHelper.createParam(cmsRequestDTO);

            // 响应
            XyyJsonResult xyyJsonResult = XyyJsonResult.createSuccess()
                    .addResult("rows", modules)
                    .addResult("merchantId", merchant == null ? null : merchant.getId())
                    .addResult("cmsRequestParam", resultCmsRequestParam);
            return xyyJsonResult;
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("获取pc首页配置信息失败，terminalType：{}，cmsRequestParam：{}，msg：{}",
                        terminalType, JSONObject.toJSONString(cmsRequestParam), e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("获取pc首页配置信息异常，terminalType：{}，cmsRequestParam：{}", terminalType, JSONObject.toJSONString(cmsRequestParam), e);
            return XyyJsonResult.createFailure().msg(CmsConstants.MSG_ERROR);
        }
    }

    /**
     * 获取期望数量的商品流
     *
     * @param terminalType
     * @param cmsRequestParam
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/listExpectProducts", method = {RequestMethod.GET, RequestMethod.POST})
    public XyyJsonResult listExpectProducts(@RequestHeader(name = "terminalType", required = false) Integer terminalType,
                                            CmsRequestParam cmsRequestParam, HttpServletRequest request) {
        try {
            CmsRequestDTO cmsRequestDTO = CmsRequestHelper.createDTO(cmsRequestParam);
            if (cmsRequestDTO == null) {
                cmsRequestDTO = new CmsRequestDTO();
            }
            Integer expectedProductNum = cmsRequestDTO.getExpectedProductNum();
            String exhibitionIdStr = cmsRequestDTO.getExhibitionIdStr();
            Boolean isFillActPt = cmsRequestDTO.getIsFillActPt();
            Boolean isFillActPgby = cmsRequestDTO.getIsFillActPgby();
            if (Objects.isNull(expectedProductNum) || expectedProductNum <= 0 || StringUtils.isEmpty(exhibitionIdStr)) {
                return XyyJsonResult.createFailure().msg(CmsConstants.MSG_ERROR);
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = null;
            if (Objects.nonNull(merchant)) {
                merchantId = merchant.getId();
            }
            String realBranchCode = getBranchCodeByMerchantId(request, merchantId);

            CmsExpectProductQueryParam cmsExpectProductQueryParam = CmsExpectProductQueryParam.builder()
                    .merchantId(merchantId).exhibitionId(exhibitionIdStr).branchCode(realBranchCode)
                    .terminalType(terminalType).expectNum(expectedProductNum).build();
            List<ListProduct> products = cmsSkuService.listExpectProducts(cmsExpectProductQueryParam);
            // 商品信息根据资质状态过滤
            Integer licenseStatus;
            LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
            licenseStatus = layoutBaseService.getLicenseStatus(merchant);
            if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                products = layoutBaseService.setProductProperties(products, statusEnum, request);
            }
            {
                // 处方药商品默认图处理
                if (log.isDebugEnabled()) {
                    log.debug("【处方药商品默认图处理】merchantId：{}，原商品信息：{}", merchantId, JSONArray.toJSONString(products));
                }
                if ((Objects.isNull(cmsRequestParam.getModuleSourceType()) || Objects.equals(cmsRequestParam.getModuleSourceType(), LayoutComponentEnum.PC_INDEX_PRODUCT_FLOOR.getModuleSourceType()))
                        && BooleanUtils.isTrue(cmsAppProperties.getIsOpenIndexProductDefaultImageFeature())) {
                    String defaultImageUrl = cmsAppProperties.getProductDefaultImageUrl();
                    products.stream().filter(item -> Objects.equals(item.getDrugClassification(), 3))
                            .forEach(item -> item.setImageUrl(defaultImageUrl));
                    if (log.isDebugEnabled()) {
                        log.debug("【处方药商品默认图处理】merchantId：{}，处理后商品信息：{}", merchantId, JSONArray.toJSONString(products));
                    }
                }
            }
            List<CmsListProductVO> cmsListProductVOS = CmsListProductVOHelper.creates(products);
            cmsListProductVOS = cmsSkuService.fillListProductsShopInfo(cmsListProductVOS);
            cmsListProductVOS = cmsSkuService.fillListProductsTagInfo(merchantId, cmsListProductVOS, true);
            //填充库存信息
            cmsListProductVOS = productServiceRpc.fillCmsActTotalSurplusQtyToAvailableQty(cmsListProductVOS);

            if ((BooleanUtils.isTrue(isFillActPt) || BooleanUtils.isTrue(isFillActPgby)) && CollectionUtils.isNotEmpty(products)) {
                // 尝试填充拼团活动信息
                List<Long> skuIdList = products.stream().map(ListProduct::getId).distinct().collect(Collectors.toList());
                Set<Long> gaoMaoSkuIdSet = Sets.newHashSet();
                gaoMaoSkuIdSet = cmsListProductVOS.stream().filter(productDto -> {
                    if(null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                            || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))){
                        return true;
                    }
                    return false;
                }).map(CmsListProductVO :: getId).collect(Collectors.toSet());
                Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap = cmsSkuService.getMarketingActivityInfoBySkuIdList(skuIdList,
                        Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()), merchantId,
                        Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
                cmsListProductVOS = cmsSkuService.fillListProductsMarketingActivityInfo(cmsListProductVOS, csuIdToGroupBuyingInfoMap);
                if (Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NO_LOGIN)) {
                    cmsListProductVOS = cmsSkuService.hideListProductsMarketingGroupBuyingActivityPriceInfo(cmsListProductVOS);
                }
            }
            //受托生产厂家处理
            if(CollectionUtils.isNotEmpty(cmsListProductVOS)){
                for (CmsListProductVO productVO : cmsListProductVOS) {
                    if(StringUtils.isNotBlank(productVO.getEntrustedManufacturer())){
                        productVO.setManufacturer(ProductMangeUtils.getManufacturer(productVO.getMarketAuthor(),productVO.getManufacturer(),productVO.getEntrustedManufacturer()));
                    }
                }
            }

            // 埋点
            CmsEventTrackingSpTypeEnum spTypeEnum = CmsEventTrackingSpTypeEnum.H5_SUBJECT;
            String spType = spTypeEnum.getSpType();
            if (StringUtils.isEmpty(cmsRequestDTO.getSptype())) {
                cmsRequestDTO.setSptype(spType);
            }
            if (StringUtils.isEmpty(cmsRequestDTO.getSpid())) {
                String spId = CmsEventTrackingUtils.generateSpId(CmsEventTrackingSpIdGenerateParam.builder()
                        .spTypeEnum(spTypeEnum).pageId(cmsRequestDTO.getPageId())
                        .pageTitle(cmsRequestDTO.getPageTitle()).build());
                cmsRequestDTO.setSpid(spId);
            }
            if (StringUtils.isEmpty(cmsRequestDTO.getSid())) {
                String sid = SearchUtils.generateNewSidData(merchantId, PlatformEnum.WEB.getValue());
                cmsRequestDTO.setSid(sid);
            }
            CmsRequestParam resultCmsRequestParam = CmsRequestHelper.createParam(cmsRequestDTO);
            ProductDataFilterHelper.fillBlankCms(cmsListProductVOS);
            return XyyJsonResult.createSuccess()
                    .addResult("rows", cmsListProductVOS)
                    .addResult("licenseStatus", licenseStatus)
                    .addResult("merchantId", merchant == null ? null : merchant.getId())
                    .addResult("cmsRequestParam", resultCmsRequestParam)
                    .addResult("scmE", RandomUtil.nanoId(8));
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("获取期望数量的商品流失败，terminalType：{}，cmsRequestParam：{}，msg：{}",
                        terminalType, JSONObject.toJSONString(cmsRequestParam), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("获取期望数量的商品流异常，terminalType：{}，cmsRequestParam：{}",
                    terminalType, JSONObject.toJSONString(cmsRequestParam), e);
            return XyyJsonResult.createFailure().msg(CmsConstants.MSG_ERROR);
        }
    }

    /**
     * 获取期望数量的秒杀商品流
     *
     * @param terminalType
     * @param cmsRequestParam
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/listExpectSeckillProducts", method = {RequestMethod.GET, RequestMethod.POST})
    public XyyJsonResult listExpectSeckillProducts(@RequestHeader(name = "terminalType", required = false) Integer terminalType,
                                                   CmsRequestParam cmsRequestParam, HttpServletRequest request) {
        try {
            CmsRequestDTO cmsRequestDTO = CmsRequestHelper.createDTO(cmsRequestParam);
            if (cmsRequestDTO == null) {
                cmsRequestDTO = new CmsRequestDTO();
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = null;
            if (Objects.nonNull(merchant)) {
                merchantId = merchant.getId();
            }
            boolean isKa = false;
            if (Objects.nonNull(merchant)) {
                isKa = merchant.getIsKa();
            }
            Integer licenseStatus = layoutBaseService.getLicenseStatus(merchant);

            int expectedNum = 10;
            List<CmsListProductVO> cmsSeckillProducts = Lists.newArrayListWithExpectedSize(expectedNum);
            if (Objects.nonNull(merchantId) && merchantId > 0L && BooleanUtils.isNotTrue(isKa)) {
                /* 查询 */
                String selectProductStrategyType = CmsSelectProductStrategyTypeEnum.SYSTEM_AUTO.getType();
                List<Long> specifiedCsuIds = null;
                String specifiedExhibitionIdStr = null;
                CmsSeckillProductExpectQueryParam cmsSeckillProductExpectQueryParam = CmsSeckillProductExpectQueryParam.builder()
                        .merchantId(merchantId)
                        .selectProductStrategyType(selectProductStrategyType)
                        .specifiedCsuIds(specifiedCsuIds)
                        .specifiedExhibitionIdStr(specifiedExhibitionIdStr)
                        .expectedNum(expectedNum)
                        .build();
                cmsSeckillProducts = complementQueryService.listExpectCmsSeckillProducts(cmsSeckillProductExpectQueryParam);
                if (log.isDebugEnabled()) {
                    log.debug("获取期望数量的秒杀商品流，入参：{}，出参：{}", JSONObject.toJSONString(cmsSeckillProductExpectQueryParam),
                            JSONArray.toJSONString(cmsSeckillProducts));
                }
                if (CollectionUtils.isEmpty(cmsSeckillProducts) || cmsSeckillProducts.size() < 5) {
                    cmsSeckillProducts = Lists.newArrayList();
                }
                {
                    // 处方药商品默认图处理
                    if (log.isDebugEnabled()) {
                        log.debug("【处方药商品默认图处理】merchantId：{}，原商品信息：{}", merchantId, JSONArray.toJSONString(cmsSeckillProducts));
                    }
                    if (BooleanUtils.isTrue(cmsAppProperties.getIsOpenIndexProductDefaultImageFeature())) {
                        String defaultImageUrl = cmsAppProperties.getProductDefaultImageUrl();
                        cmsSeckillProducts.stream().filter(item -> Objects.nonNull(item) && Objects.equals(item.getDrugClassification(), 3))
                                .forEach(item -> item.setImageUrl(defaultImageUrl));
                        if (log.isDebugEnabled()) {
                            log.debug("【处方药商品默认图处理】merchantId：{}，处理后商品信息：{}", merchantId, JSONArray.toJSONString(cmsSeckillProducts));
                        }
                    }
                }
            }

            // 埋点
            CmsEventTrackingSpTypeEnum spTypeEnum = CmsEventTrackingSpTypeEnum.H5_SUBJECT;
            String spType = spTypeEnum.getSpType();
            if (StringUtils.isEmpty(cmsRequestDTO.getSptype())) {
                cmsRequestDTO.setSptype(spType);
            }
            if (StringUtils.isEmpty(cmsRequestDTO.getSpid())) {
                String spId = CmsEventTrackingUtils.generateSpId(CmsEventTrackingSpIdGenerateParam.builder()
                        .spTypeEnum(spTypeEnum).pageId(cmsRequestDTO.getPageId())
                        .pageTitle(cmsRequestDTO.getPageTitle()).build());
                cmsRequestDTO.setSpid(spId);
            }
            if (StringUtils.isEmpty(cmsRequestDTO.getSid())) {
                String sid = SearchUtils.generateNewSidData(merchantId, PlatformEnum.WEB.getValue());
                cmsRequestDTO.setSid(sid);
            }
            CmsRequestParam resultCmsRequestParam = CmsRequestHelper.createParam(cmsRequestDTO);

            return XyyJsonResult.createSuccess()
                    .addResult("rows", cmsSeckillProducts)
                    .addResult("licenseStatus", licenseStatus)
                    .addResult("merchantId", merchant == null ? null : merchant.getId())
                    .addResult("cmsRequestParam", resultCmsRequestParam);
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("获取期望数量的秒杀商品流失败，terminalType：{}，cmsRequestParam：{}，msg：{}",
                        terminalType, JSONObject.toJSONString(cmsRequestParam), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            log.error("获取期望数量的秒杀商品流异常，terminalType：{}，cmsRequestParam：{}",
                    terminalType, JSONObject.toJSONString(cmsRequestParam), e);
            return XyyJsonResult.createFailure().msg(CmsConstants.MSG_ERROR);
        }
    }

}
