package com.xyy.ec.pc.newfront.service;

import com.xyy.ec.pc.newfront.dto.RecordRespVO;
import com.xyy.ec.product.business.dto.SearchRecordBusinessDTO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface SearchRecordNewService {

    /**
     * 获取用户搜索记录
     */
    List<SearchRecordBusinessDTO> getList(HttpServletRequest httpServletRequest);

    /**
     * 添加用户搜索记录
     */
    void addRecord(HttpServletRequest httpServletRequest, String keyword);

    /**
     * 删除用户搜索记录
     */
    void delRecord(HttpServletRequest httpServletRequest, String keyword);

    /**
     * 批量删除用户搜索记录
     */
    void delBatchRecord(HttpServletRequest httpServletRequest);
}
