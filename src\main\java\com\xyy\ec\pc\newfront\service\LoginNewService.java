package com.xyy.ec.pc.newfront.service;

import com.xyy.ec.pc.controller.vo.AccountRegisterVO;
import com.xyy.ec.pc.newfront.dto.ForgetPassLastRespVO;
import com.xyy.ec.pc.newfront.dto.LoginRespVO;
import com.xyy.ec.pc.newfront.dto.RegisterRespVO;
import com.xyy.ec.pc.newfront.dto.WxLoginRespVO;
import com.xyy.ec.pc.newfront.vo.*;
import com.xyy.ec.pc.rest.AjaxResult;
import org.aspectj.weaver.loadtime.Aj;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface LoginNewService {

    /**
     * 登录
     *
     * @param loginParamVO
     * @param request
     * @return
     */
    AjaxResult<LoginRespVO> login(LoginParamVO loginParamVO, HttpServletRequest request);

    /**
     * 登出
     *
     * @return
     */
    AjaxResult<LoginRespVO> logOut();

    /**
     * 注册
     *
     * @param registerParamVO
     * @param request
     * @return
     */
    AjaxResult<RegisterRespVO> registerLast(RegisterParamVO registerParamVO, HttpServletRequest request) throws Exception;

    /**
     * 忘记密码
     *
     * @param param
     * @return
     */
    AjaxResult<ForgetPassLastRespVO> forgetPassLast(ForgetPassLastParamVO param) throws Exception;

    /**
     * 获取验证码code

     */
    AjaxResult<String> getCode(HttpServletRequest request, HttpServletResponse response)throws Exception;

    /**
     * 验证码校验

     */
    AjaxResult<String> checkPhoCode(HttpServletRequest request, HttpServletResponse response)throws Exception;

    /**
     * wx登录
     */
    AjaxResult<WxLoginRespVO> wxLogin(WxLoginParamVO wxLoginParamVO, HttpServletRequest request);

    /**
     * wx绑定
     */
    AjaxResult<Object> wxBind(WxBindParamVO wxBindParamVO);

    /**
     * 发送绑定验证码
     */
    AjaxResult<Object> sendBindCode(String phone);

    /**
     * 账号绑定微信
     */
    AjaxResult<Object> wxBindRegister(WxBindParamVO wxBindParamVO);


    AjaxResult<Object> registerNext(AccountRegisterVO accountRegisterVO);




}
