package com.xyy.ec.pc.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import redis.clients.jedis.*;



/**
 * 描述: redis 基础操作 包括增删改查
 *
 */
@Component
public class BaseRedisUtil {
    private static final Logger logger = LoggerFactory.getLogger(BaseRedisUtil.class);

    private static JedisPool jedisPool;


    public static JedisPool getJedisPool() {
        return jedisPool;
    }

    public static void setJedisPool(JedisPool pool) {
        jedisPool = pool;
    }

    public static String get(String key, int indexDB) {
        Jedis jedis = null;
        String value = null;
        try {
            if (jedisPool == null) {
                return value;
            }
            jedis = jedisPool.getResource();
            jedis.select(indexDB);
            value = jedis.get(key);
        } catch (Exception e) {
            logger.error("链接redis失败:", e);
        } finally {
            returnResource(jedis);
        }
        return value;
    }

    /**
     * 存储缓存
     *
     * @param key
     * @param value
     * @param indexDB
     * @param expireTime 过期时间，单位秒
     */
    public static void set(String key, String value, int indexDB, int expireTime) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(indexDB);
            jedis.setex(key, expireTime, value);
        } catch (Exception e) {
            logger.error("链接redis失败:", e);
        } finally {
            returnResource(jedis);
        }
    }

    /** 按照 offset 来更新value
     * offset = 0L ,只更新value，不更新过期时间
     *
     * @param key
     * @param value
     * @param indexDB
     */
    public static void setRangeNew(String key, String value, int indexDB, long offset ) {
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(indexDB);
            jedis.setrange(key,offset,value);
        } catch (Exception e) {
            logger.error("链接redis失败:", e);
        } finally {
            returnResource(jedis);
        }
    }

    /** 查询ttl
     *
     * @param key
     * @param indexDB
     * @return
     */
    public static Long getTTL(String key,int indexDB) {
        Jedis jedis = null;
        Long ttlTime = 0L;
        try {
            jedis = jedisPool.getResource();
            jedis.select(indexDB);
            ttlTime = jedis.ttl(key);
        } catch (Exception e) {
            logger.error("链接redis失败:", e);
        } finally {
            returnResource(jedis);
        }
        return ttlTime;
    }


    /**
     * 关闭jedis连接
     *
     * @param jedis
     */
    public static void returnResource(Jedis jedis) {
        if (jedis != null) {
            jedis.close();
        }
    }




}
