package com.xyy.ec.pc.config;

import java.util.HashMap;
import java.util.Map;


/**
 * 
 * <AUTHOR>
 *
 */
public enum IsSuccessEnum {


    SUCCESS(0,"成功"),
    FAIL(1,"失败");

    private int id;
    private  String value;

    IsSuccessEnum(int id, String value){
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, IsSuccessEnum> successMaps = new HashMap<>();
    public static Map<Integer,String> maps = new HashMap<>();
    static {
        for(IsSuccessEnum control : IsSuccessEnum.values()) {
        	successMaps.put(control.getId(), control);
            maps.put(control.getId(),control.getValue());
        }
    }

    public static String get(int id) {
        return successMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }
}
