package com.xyy.ec.pc.recommend.service;

import com.xyy.ec.pc.recommend.vo.PcRecommendCardVO;
import com.xyy.recommend.ecp.result.EcpRecommendBaseCardDTO;

import java.util.List;

public interface EcpPcRecommendService {

    /**
     * 转换和填充搜索结果卡片信息
     *
     * @param merchantId
     * @param branchCode
     * @param priceDisplayFlag
     * @param cardDTOS
     * @return
     */
    List<PcRecommendCardVO> convertAndFillRecommendCards(Long merchantId, String branchCode, Boolean priceDisplayFlag, boolean userOneClickReplenishment, List<EcpRecommendBaseCardDTO> cardDTOS);

    /**
     * 转换和填充搜索结果卡片信息  无用户信息
     *
     * @param cardDTOS
     * @return
     */
    List<PcRecommendCardVO> convertAndFillRecommendCards(String saasOrganSign, List<EcpRecommendBaseCardDTO> cardDTOS);
}
