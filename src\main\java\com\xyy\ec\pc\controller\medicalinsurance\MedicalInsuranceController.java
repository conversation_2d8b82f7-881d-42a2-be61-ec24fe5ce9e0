package com.xyy.ec.pc.controller.medicalinsurance;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.product.business.ecp.out.app.ProductForAppApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;


@Controller
@RequestMapping("/medicalInsurance")
public class MedicalInsuranceController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(MedicalInsuranceController.class);

    @Reference(version = "1.0.0")
    ProductForAppApi productForAppApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    /**
     * 医保目录搜索
     * @param keyword
     * @return
     */
    @RequestMapping("/search")
    @ResponseBody
    public Object search(@RequestParam("keyword") String keyword, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            return this.addResult("data", productForAppApi.selectMedicalInsuranceCatalogByName(keyword,merchantId,branchCode));
        }catch (Exception e){
            LOGGER.error("/app/medicalInsurance/search-医保目录搜索：",e);
            return this.addError("获取医保目录失败！");
        }
    }

}
