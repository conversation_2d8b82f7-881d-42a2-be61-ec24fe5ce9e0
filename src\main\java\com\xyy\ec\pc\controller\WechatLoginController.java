package com.xyy.ec.pc.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

@Controller
@RequestMapping
public class WechatLoginController {

    @RequestMapping(value = "/wechat-test/wechat/login/callback", method = RequestMethod.GET)
    public ModelAndView wechatTestLogin() {

        return new ModelAndView("/callback.ftl");
    }

    @RequestMapping(value = "/wechat-stage/wechat/login/callback", method = RequestMethod.GET)
    public ModelAndView wechatLoginState() {

        return new ModelAndView("/callback.ftl");
    }

    @RequestMapping(value = "/wechat/login/callback", method = RequestMethod.GET)
    public ModelAndView wechatLogin() {

        return new ModelAndView("/callback.ftl");
    }
}
