package com.xyy.ec.pc.authentication.consts;

/**
 * <AUTHOR>
 * @Date 2024/4/10 14:26
 * @File Constants.class
 * @Software IntelliJ IDEA
 * @Description
 */
public class Constants
{
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * www主域
     */
    public static final String WWW = "www.";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";


    /**
     * redis token key
     */
    public static final String JWT_LOGIN_USER_KEY = "login_user_key";

    /**
     * 账号ID
     */
    public static final String JWT_ACCOUNT_ID = "account_id";

    /**
     * 店铺ID
     */
    public static final String JWT_MERCHANT_ID = "merchant_id";

    /**
     * 登录时间
     */
    public static final String JWT_LOGIN_TIME = "login_time";

    /**
     * 登录IP、地址
     */
    public static final String JWT_IP_ADDR = "ip_addr";

    /**
     * 登录设备ID
     */
    public static final String JWT_LOGIN_DEVICE_ID = "device_id";

    /**
     * 登录版本号
     */
    public static final String JWT_LOGIN_VERSION = "version";

    /**
     *  登录操作系统
     */
    public static final String JWT_LOGIN_OS = "os";

    /**
     * 登录浏览器
     */
    public static final String JWT_LOGIN_BROWSER = "browser";

    /** 持久cookie 超时时间(以秒为单位) */
    public static final int COOKIE_MAX_AGE = 3600 * 24 * 30;
    /**cookie会话信息**/
    public static final String PRINCIPAL_COOKIE_NAME  = "xyy_principal";
    /**token**/
    public static final String TOKEN_NAME = "xyy_token";
    /**访客名**/
    public static final String VISITOR_COOKIE_NAME    = "xyy_visitor";
    /**最后登录时间**/
    public static final String LAST_LOGIN_TIME       = "xyy_last_login_time";
    /****/
    public static final String BIZ_LAST_LOGIN_TIME   = "xyy_biz_last_login_time";
    /**域编码**/
    public static final String BRANCH_ID_COOKIE_NAME = "BRANCH_ID";
    /**设备Id*/
    public static final String DEVICE_ID = "device_id";
    /** encode */
    public static final String ENCODE = "utf-8";
    /** 分割符 */
    public static final String SPLIT = "&";
    /** */
    public static final String AUTO_LOGIN_COOKIE_NAME  = "xyy";

    public static final String QT_SESSION_KET  = "qt_session";
}