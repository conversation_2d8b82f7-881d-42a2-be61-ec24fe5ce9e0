package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.merchant.bussiness.dto.licence.MerchantForLicense;
import com.xyy.ec.pc.newfront.service.LicenseNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025-07-25 16:25
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/new-front/merchant/center/license")
public class LicenseNewController {

    private final LicenseNewService licenseNewService;

    /**
     * 客户首营认证
     * @param merchantForLicense
     * @return
     */
    @PostMapping(value = "/authentication")
    public AjaxResult<Object> getMerchantInfoToAuthentication(@RequestBody MerchantForLicense merchantForLicense) {
        return licenseNewService.getMerchantInfoToAuthentication(merchantForLicense);
    }
}
