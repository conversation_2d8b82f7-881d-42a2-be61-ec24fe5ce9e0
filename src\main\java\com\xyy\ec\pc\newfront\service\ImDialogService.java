package com.xyy.ec.pc.newfront.service;

import com.alibaba.fastjson.JSON;
import com.xyy.ec.system.business.dto.MessageCenterBusinessDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Slf4j
@Service
public class ImDialogService {

    @Value("${im-base-url}")
    private String imBaseUrl;

    @Autowired
    private RestTemplate restTemplate;


    public MessageCenterBusinessDto findLatestDialogByUid(Long merchantId) {
        try {
            if (merchantId == null) {
                return null;
            }
            String uri = String.format("/messageCenter/findLatestDialogByUid?merchantId=%d", merchantId);
            String url = imBaseUrl + uri;
            Map<String, Object> response = restTemplate.getForObject(url, Map.class);
            int code = Integer.parseInt(response.get("code").toString());
            if (code == 0) {
                log.info("查询客户与自营客服会话失败: {}", response.get("msg"));
                return null;
            }
            MessageCenterBusinessDto businessDto = JSON.parseObject(JSON.toJSONString(response.get("data")), MessageCenterBusinessDto.class);
            return businessDto;
        } catch (Exception e) {
            log.error("查询客户与自营客服会话", e);
            return null;
        }
    }

}
