package com.xyy.ec.pc.controller;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Maps;
import com.xyy.crm.query.model.R;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.cs.api.dto.HolidayDTO;
import com.xyy.ec.cs.api.dto.PlatformInAddNodeDTO;
import com.xyy.ec.cs.api.dto.PlatformInWorkorderDetailDTO;
import com.xyy.ec.cs.api.order.CsOrderRefundApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.OrderInvoiceTitleBussinessDto;
import com.xyy.ec.order.business.api.OrderDetailBusinessApi;
import com.xyy.ec.order.business.api.OrderRefundBusinessApi;
import com.xyy.ec.order.business.api.OrderRefundDetailBusinessApi;
import com.xyy.ec.order.business.dto.OrderDetailBusinessDto;
import com.xyy.ec.order.business.dto.OrderRefundBusinessDto;
import com.xyy.ec.order.business.dto.OrderRefundDetailBusinessDto;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesDetailVo;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesQueryParam;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.cms.vo.InterventionInvoiceVO;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.constants.CodeMapConstants;
import com.xyy.ec.pc.controller.vo.platformin.PlatformInWorkOrderVO;
import com.xyy.ec.pc.model.SupplementaryDescriptionDTO;
import com.xyy.ec.pc.model.WorkOrderDTO;
import com.xyy.ec.pc.model.WorkerIdDTO;
import com.xyy.ec.pc.remote.InterventionService;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.search.ecp.vo.RefundImageInformationVo;
import com.xyy.ec.pc.search.ecp.vo.WorkOrderVo;
import com.xyy.ec.pc.service.AfterSalesService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.system.business.dto.CodeitemBusinessDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.*;
import java.util.*;

import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工单管理
 */
@Controller
@RequestMapping("/intervention")
@Slf4j
public class InterventionController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(InterventionController.class);


    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Reference(version = "1.0.0")
    private CsOrderRefundApi csOrderRefundApi;
    @Autowired
    private InterventionService interventionService;
    @Autowired
    private AfterSalesService afterSalesService;
    @Autowired
    private XyyConfig.CdnConfig cdnConfig;
    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;
    @Reference(version = "1.0.0", timeout = 60000, retries = -1)
    private OrderRefundBusinessApi orderRefundBusinessApi;
    @Reference(version = "1.0.0")
    private OrderRefundDetailBusinessApi orderRefundDetailBusinessApi;
    @Reference(version = "1.0.0")
    private OrderDetailBusinessApi orderDetailBusinessApi;
    @Autowired
    private ImUrlBuilder imUrlBuilder;

    @Value("${ossDomain:https://oss-ec-test.ybm100.com/}")
    public String ossDomain;
    @Value("${base_path_url}")
    private String basePathUrl;

    @Value("${refund.order.platformIn.check.switch:false}")
    private Boolean refundOrderPlatformInCheckSwitch;



    /**
     * 新增工单页面
     */
    @RequestMapping(value = "/create.htm",method = RequestMethod.GET)
    public Object create(HttpServletRequest request,String afsNo,String orderNo,String refundNo,Integer platforminType,@RequestParam(required = false) Long dialogId){
        Map<String, Object> model = new HashMap<>();
        MerchantPrincipal merchant = null;
        try {
            merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            LOGGER.error("获取会员信息异常:",e);
        }
        if (merchant == null) {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }
        model.put("afsNo",afsNo);
        model.put("orderNo",orderNo);
        model.put("refundNo",refundNo);
        model.put("platforminType",platforminType);
        model.put("dialogId",dialogId);
        return new ModelAndView("/order/createInter.ftl", model);
    }

    /**
     * 详情工单页面
     */
    @RequestMapping(value = "/detail.htm",method = RequestMethod.GET)
    public Object detail(HttpServletRequest request,Long workOrderId){
        Map<String, Object> model = new HashMap<>();
        MerchantPrincipal merchant = null;
        try {
            merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            LOGGER.error("获取会员信息异常:",e);
        }
        if (merchant == null) {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }
        model.put("workOrderId",workOrderId);
        return new ModelAndView("/order/interDetaill.ftl", model);
    }

    /**
     * 新增工单
     */
    @RequestMapping(value = "/addWorkOrder",method = RequestMethod.POST)
    @ResponseBody
    public R<Long> addWorkOrder(@RequestBody WorkOrderDTO param){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto)xyyIndentityValidator.currentPrincipal();
            if (merchant != null){
                Long merchantId = merchant.getMerchantId();
                param.setMerchantId(merchantId);
            }
            if (param == null){
                return R.errorResult("参数为空");
            }
            if (StringUtil.isEmpty(param.getAfsNo()) && StringUtil.isEmpty(param.getRefundNo())){
                return R.errorResult("afsNo和refundNo都为空");
            }
            if (StringUtil.isNotEmpty(param.getImgsUrl()))
            {
            String[] arryImagesList = param.getImgsUrl().split(";");
            List<String> imagesListArry = new ArrayList<>();
            for (String skuImages: arryImagesList) {
                if (StringUtil.isNotEmpty(skuImages)) {
                    imagesListArry.add(skuImages.trim());
                }
            }
                param.setImgUrls(imagesListArry);
            }
            com.xyy.ec.cs.api.dto.PlatformInWorkorderCreateDTO dto = new com.xyy.ec.cs.api.dto.PlatformInWorkorderCreateDTO();
            BeanUtils.copyProperties(param, dto);
            log.info("新增工单参数:dto为{}",dto);
            ApiRPCResult<Long> longApiRPCResult = csOrderRefundApi.create(dto);
            if (longApiRPCResult.isFail()){
                return R.errorResult(longApiRPCResult.getErrMsg());
            }
            log.info("新增工单成功,工单id为{}",longApiRPCResult.getData());
            return R.successResult(longApiRPCResult.getData());

        } catch (Exception e) {
            LOGGER.error("新增工单异常",e);
            return R.errorResult("新增工单异常");
        }
    }

    /**
     * 查询工单
     * app端比pc多两个参数注意分别
     */
    @RequestMapping(value = "/queryWorkOrder/{id}",method = RequestMethod.GET)
    @ResponseBody
    public Object queryWorkOrder(@PathVariable Long id){
        LOGGER.info("查询工单开始");
        try {
            //判断是否为空
            if (id == null){
                return R.errorResult("请选择查询工单");
            }
            PlatformInWorkOrderVO vo = new PlatformInWorkOrderVO();
            ApiRPCResult<PlatformInWorkorderDetailDTO> apiRPCResult = csOrderRefundApi.detailAndNodeByPersonType(id,1);
            if (apiRPCResult.isFail()){
                return this.addError(apiRPCResult.getErrMsg());
            }

            if (apiRPCResult.getData() == null){
                return R.errorResult("工单不存在");
            }
            PlatformInWorkorderDetailDTO detailDTO = apiRPCResult.getData();
            BeanUtils.copyProperties(detailDTO,vo);
            vo.setRefundInfo(detailDTO.getRefundInfo());
            vo.setAfsInfo(detailDTO.getAfsInfo());
            vo.setNodeInfo(detailDTO.getNodeInfo());
            vo.setImUrl(imUrlBuilder.buildImUrl(detailDTO.getMerchantId()));
            vo.setPopImUrl(imUrlBuilder.buildImPopUrl(detailDTO.getId() ,detailDTO.getMerchantId(),detailDTO.getOrgId(),detailDTO.getShopName()));
            return this.addResult("data", vo);
        } catch (Exception e) {
            LOGGER.error("查询工单异常",e);
            return R.errorResult("查询工单异常");
        }

    }
    /**
     * 撤销工单
     */
    @RequestMapping(value = "/cancelWorkOrder",method = RequestMethod.GET)
    @ResponseBody
    public R<Boolean> updateWorkOrder(Long id){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto)xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getMerchantId();
            if (merchantId == null){
                return R.errorResult("请登录");
            }

            //判断是否为空
            if (id == null){
                return R.errorResult("请选择工单");
            }
            //调用工单系统撤销工单
            PlatformInAddNodeDTO dto = new PlatformInAddNodeDTO();
            dto.setCreatePersonId(merchantId.toString());
            dto.setWorkOrderId(id);
            dto.setCreatePersonType(1);
            Boolean b = interventionService.updateWorkOrder(dto);
            if (!b){
                return R.errorResult("撤销工单失败");
            }
           return R.successResult(true);
        } catch (Exception e) {
            LOGGER.error("撤销工单异常",e);
            return R.errorResult("撤销工单异常");
        }
    }

    /**
     * 查询平台介入申请
     */
    @RequestMapping(value = "/getRedirectUrl",method = RequestMethod.GET)
    @ResponseBody
    public Object getRedirectUrl(String afsNo,String orderNo){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto)xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getMerchantId();
            if (merchantId == null){
                return this.addError("请登录");
            }
            if (StringUtil.isEmpty(afsNo)){
                return this.addError("请选择退款/售后单");
            }

            if (StringUtil.isEmpty(orderNo)){
                return this.addError("请选择订单");
            }
            String refundNo = "";
            if (afsNo.startsWith("RS")){
                refundNo = afsNo;
                OrderRefundBusinessDto orderRefundBusinessDto = orderRefundBusinessApi.selectByRefundOrderNo(refundNo);
                Integer auditState = orderRefundBusinessDto.getAuditState();
                Date refundCreateTime = orderRefundBusinessDto.getRefundCreateTime();

                Date now = new Date();
                Instant instant1 = refundCreateTime.toInstant();
                Instant instant2 = now.toInstant();
                Duration duration = Duration.between(instant1, instant2).abs();

                long minus = 0;
                ApiRPCResult<List<HolidayDTO>> result = csOrderRefundApi.get15Holiday(refundCreateTime);
                if (result.isSuccess()){
                    List<HolidayDTO> holidays = result.getData();
                    Duration duration1 = HolidayUtil.calculateHolidayDuration(instant1, instant2, holidays);
                    minus = duration1.toHours();
                }

                duration = duration.minusHours(minus);
                if (auditState != -1 && duration.toHours() <= 48){
                    return this.addError("亲，若卖家拒绝您的申请，或者超2个工作日未处理，您可以申请“平台介入”");
                }
            }

            if (afsNo.startsWith("AS")){
                AfterSalesQueryParam afterSalesQueryParam = new AfterSalesQueryParam();
                afterSalesQueryParam.setAfterSalesNo(afsNo);
                ApiRPCResult<AfterSalesDetailVo> afterSalesDetailVoApiRPCResult = orderRefundBusinessApi.queryAfterSalesDetail(afterSalesQueryParam);
                if (afterSalesDetailVoApiRPCResult.isFail()){
                    return this.addError(afterSalesDetailVoApiRPCResult.getErrMsg());
                }
                AfterSalesDetailVo afterSalesDetailVo = afterSalesDetailVoApiRPCResult.getData();
                Integer auditProcessState = afterSalesDetailVo.getAuditProcessState();

                Date createTime = afterSalesDetailVo.getCreateTime();
                Date now = new Date();
                Instant instant1 = createTime.toInstant();
                Instant instant2 = now.toInstant();
                Duration duration = Duration.between(instant1, instant2).abs();

                long minus = 0;
                ApiRPCResult<List<HolidayDTO>> result = csOrderRefundApi.get15Holiday(createTime);
                if (result.isSuccess()){
                    List<HolidayDTO> holidays = result.getData();
                    Duration duration1 = HolidayUtil.calculateHolidayDuration(instant1, instant2, holidays);
                    minus = duration1.toHours();
                }

                duration = duration.minusHours(minus);
                if (auditProcessState != 4 && duration.toHours() <= 48){
                    return this.addError("亲，若卖家拒绝您的申请，或者超2个工作日未处理，您可以申请“平台介入”");
                }
            }

            ApiRPCResult<List<PlatformInWorkorderDetailDTO>> listApiRPCResult = csOrderRefundApi.detailByRefundNoOrAfsNo(afsNo);
            if (listApiRPCResult.isFail()){
                return this.addError(listApiRPCResult.getErrMsg());
            }
            String platforminType = "2";
            List<PlatformInWorkorderDetailDTO> list = listApiRPCResult.getData();
            if (CollectionUtil.isEmpty(list)){
                if (StringUtil.isEmpty(refundNo)){
                    AfterSalesQueryParam afterSalesQueryParam = new AfterSalesQueryParam();
                    afterSalesQueryParam.setAfterSalesNo(afsNo);
                    AfterSalesDetailVo afterSalesDetailVo = afterSalesService.queryAfterSalesDetail(afterSalesQueryParam);

                    if (Objects.nonNull(afterSalesDetailVo)){
                        if (afterSalesDetailVo.getAfterSalesType() == 1){
                            platforminType = "2";
                        }
                        if (afterSalesDetailVo.getAfterSalesType() == 2){
                            platforminType = "3";
                        }
                    }
                    return this.addDataResult("data",String.format(basePathUrl+"/intervention/create.htm?orderNo=%s&afsNo=%s&platforminType=%s",orderNo,afsNo,platforminType));
                }
                return this.addDataResult("data",String.format(basePathUrl+"/intervention/create.htm?orderNo=%s&refundNo=%s&platforminType=1",orderNo,refundNo));
            }

            PlatformInWorkorderDetailDTO platformInWorkorderDetailDTO = list.stream()
                    .filter(dto -> dto.getState() != 3)
                    .max(Comparator.comparingLong(PlatformInWorkorderDetailDTO::getId))
                    .orElse(null);
            if (StringUtil.isNotEmpty(refundNo)){
                PlatformInWorkorderDetailDTO dto = list.stream()
                        .max(Comparator.comparingLong(PlatformInWorkorderDetailDTO::getId))
                        .orElse(null);
                if (!Objects.isNull(dto)){
                    Long workOrderId = dto.getId();
                    return this.addDataResult("data",basePathUrl+"/intervention/detail.htm?workOrderId="+workOrderId);
                }
            }
            if(Objects.isNull(platformInWorkorderDetailDTO)){
                if (StringUtil.isEmpty(refundNo)){
                    AfterSalesQueryParam afterSalesQueryParam = new AfterSalesQueryParam();
                    afterSalesQueryParam.setAfterSalesNo(afsNo);
                    AfterSalesDetailVo afterSalesDetailVo = afterSalesService.queryAfterSalesDetail(afterSalesQueryParam);

                    if (Objects.nonNull(afterSalesDetailVo)){
                        if (afterSalesDetailVo.getAfterSalesType() == 1){
                            platforminType = "2";
                        }
                        if (afterSalesDetailVo.getAfterSalesType() == 2){
                            platforminType = "3";
                        }
                    }
                    return this.addDataResult("data",String.format(basePathUrl+"/intervention/create.htm?orderNo=%s&afsNo=%s&platforminType=%s",orderNo,afsNo,platforminType));
                }
                return this.addDataResult("data",String.format(basePathUrl+"/intervention/create.htm?orderNo=%s&refundNo=%s&platforminType=1",orderNo,refundNo));
            }
            Long workOrderId = platformInWorkorderDetailDTO.getId();
            return this.addDataResult("data",basePathUrl+"/intervention/detail.htm?workOrderId="+workOrderId);
        } catch (Exception e) {
            LOGGER.error("查询平台介入申请异常",e);
            return this.addError("查询平台介入申请异常");
        }
    }
    /**
     * 催处理订单
     * 查询工单获取时间戳
     */
    @RequestMapping(value = "/urgeProcessing",method = RequestMethod.GET)
    @ResponseBody
    public R<Long> urgeWorkOrder(Long id){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto)xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getMerchantId();
            if (merchantId == null){
                return R.errorResult("请登录");
            }
            //判断是否为空
            if (id == null){
                return R.errorResult("请选择工单");
            }
            PlatformInAddNodeDTO dto = new PlatformInAddNodeDTO();
            dto.setCreatePersonId(merchantId.toString());
            dto.setWorkOrderId(id);
            dto.setCreatePersonType(1);
            //调用工单系统获取时间戳
            ApiRPCResult<Long> result = csOrderRefundApi.urge(dto);
            if (result.isFail()){
                return R.errorResult(result.getErrMsg());
            }
            //返回前端
            return R.successResult(result.getData());
        } catch (Exception e) {
            LOGGER.error("催处理订单异常",e);
            return R.errorResult("催处理订单异常");
        }
    }
    /**
     * 补充描述
     */
    @RequestMapping(value = "/supplementaryDescription",method = RequestMethod.POST)
    @ResponseBody
    public  R<Boolean> addWorkOrderNode(@RequestBody SupplementaryDescriptionDTO dto){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto)xyyIndentityValidator.currentPrincipal();
            LOGGER.info("补充描述的dto参数为:{}",dto);
            //判断是否为空
            if (BeanUtil.isEmpty(dto.getId())){
                return R.errorResult("请选择工单");
            }
            //转化
            PlatformInAddNodeDTO param = new PlatformInAddNodeDTO();
            param.setWorkOrderId(dto.getId());
            param.setContent(dto.getProblemTypeStr());
            param.setCreatePersonType(1);
            param.setCreatePersonId(merchant.getMerchantId().toString());
            if (com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(dto.getImgsUrl())){
                String[] split = dto.getImgsUrl().split(";");
                dto.setImgList(Arrays.asList(split));
                param.setFiles(dto.getImgList());
            }
            Boolean b = interventionService.addWorkOrderNode(param);
            if (!b){
                return R.errorResult("补充描述失败");
            }else {
                return R.successResult(true);
            }

        } catch (Exception e) {
            LOGGER.error("新增节点异常",e);
            return R.errorResult("新增节点异常");
        }
    }
    /**
     * 意见提交
     */
    @RequestMapping(value = "/opinionSubmit",method = RequestMethod.POST)
    @ResponseBody
    public R<Boolean> opinionSubmit(@RequestBody SupplementaryDescriptionDTO dto){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto)xyyIndentityValidator.currentPrincipal();
            LOGGER.info("补充描述的dto参数为:{}",dto);
            //判断是否为空
            if (BeanUtil.isEmpty(dto.getId())){
                return R.errorResult("请选择工单");
            }
            //转化
            PlatformInAddNodeDTO param = new PlatformInAddNodeDTO();
            param.setWorkOrderId(dto.getId());
            param.setContent(dto.getProblemTypeStr());
            param.setCreatePersonType(1);
            param.setCreatePersonId(merchant.getMerchantId().toString());
            param.setMerchantOrPop(1);
            if (com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(dto.getImgsUrl())){
                String[] split = dto.getImgsUrl().split(";");
                dto.setImgList(Arrays.asList(split));
                param.setFiles(dto.getImgList());
            }
            //根据类型判断是补充描述还是意见征询 type为1是意见征询  2是补充描述
            Boolean b = interventionService.opinionSubmit(param);
            if (!b){
                return R.errorResult("补充描述失败");
            }else {
                return R.successResult(true);
            }

        } catch (Exception e) {
            LOGGER.error("新增节点异常",e);
            return R.errorResult("新增节点异常");
        }
    }
    /**
     * 查看发票信息
     */
    @RequestMapping(value = "/queryInvoiceInfo",method = RequestMethod.GET)
    @ResponseBody
    public Object queryInvoiceInfo(Long merchantId){
        try {
            OrderInvoiceTitleBussinessDto orderInvoiceTitle = merchantBussinessApi.getMerchantInvoiceType(merchantId);
            if (orderInvoiceTitle==null){
                return R.errorResult("未查询到发票信息");
            }
            String branchCodeByMerchantId = super.getBranchCodeByMerchantId(merchantId);
            Map<String, CodeitemBusinessDto> stringCodeitemBusinessDtoMap = codeItemServiceRpc.selectByCodemapRTMap(CodeMapConstants.MERCHANT_INVOICE, branchCodeByMerchantId);
            String fpGs = stringCodeitemBusinessDtoMap.get("FFTT_GS").getName();
            String fpTitle = stringCodeitemBusinessDtoMap.get("FFTT").getName();
            String fpYdmc = stringCodeitemBusinessDtoMap.get("FFTT_YDMC").getName();
            String fpNsrsbh = stringCodeitemBusinessDtoMap.get("FFTT_NSRSBH").getName();

            //将字典中的四个值以及发票信息组装到InterventionInvoiceVO返回给前端
            InterventionInvoiceVO interventionInvoiceVO = new InterventionInvoiceVO();
            interventionInvoiceVO.setFpGs(fpGs);
            interventionInvoiceVO.setFpTitle(fpTitle);
            interventionInvoiceVO.setFpYdmc(fpYdmc);
            interventionInvoiceVO.setFpNsrsbh(fpNsrsbh);
            interventionInvoiceVO.setRealName(orderInvoiceTitle.getRealName());
            interventionInvoiceVO.setTaxNum(orderInvoiceTitle.getTaxNum());


            return R.successResult(interventionInvoiceVO);
        } catch (Exception e) {
            LOGGER.error("查询发票信息异常",e);
            return R.errorResult("查询发票信息异常");
        }
    }

    /**
     * 上传文件
     *
     * @param request 请求request
     * @return
     */
    @ResponseBody
    @RequestMapping("/uploadFile")
    public Object uploadImg(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 创建一个通用的多部分解析器
            CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                    request.getSession().getServletContext());
            // 判断 request 是否有文件上传,即多部分请求
            if (multipartResolver.isMultipart(request)) {
                // 转换成多部分request
                MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
                // 取得request中的所有文件名
                Iterator<String> iter = multiRequest.getFileNames();
                while (iter.hasNext()) {
                    // 取得上传文件
                    List<MultipartFile> files = multiRequest.getFiles(iter.next());
                    if (!org.springframework.util.CollectionUtils.isEmpty(files)) {
                        for (MultipartFile file : files) {
                            if (file != null) {
                                // 取得当前上传文件的文件名称
                                String myFileName = file.getOriginalFilename();
                                //验证文件名称
                                if (checkFile(myFileName) == false) {
                                    log.info("上传图片,文件名称不正确！myFileName is {}", myFileName);
                                    return JsonUtil.wapperObjToString(this.addError("上传文件格式不对!"));
                                }
                                //验证文件内容
//                                if (FileTypeUtil.isImage(file.getInputStream()) == false) {
//                                    log.info("上传资质图片,文件内容不正确！");
//                                    return JsonUtil.wapperObjToString(this.addError("上传文件格式不对!"));
//                                }
                                if (!StringUtils.isEmpty(myFileName) && file.getSize() > (1024 * 1024 * 500)) {
                                    return JsonUtil.wapperObjToString(this.addError("上传图片不能超过500M"));
                                }
                            }
                        }
                    }
                }
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (merchant.getId() == null){
                log.info("商户信息为空，无法上传文件");
                return JsonUtil.wapperObjToString(this.addError("请先登录!"));
            }
            String uploadPath = null;
            if (merchant.getId() != null) {
                uploadPath = "ybm/intervention/" + merchant.getId() + "/";
            } else {
                uploadPath = "ybm/intervention/account/" + merchant.getAccountId() + "/";
            }
            Map<String, Object> resultMap = FileUploadUtil.fileUpload(request, uploadPath, cdnConfig, null, null);
            List<String> fileNameList = (List<String>) resultMap.get("fileName");
            List<String> newFileNameList = new ArrayList<>();
            if (CollectionUtil.isEmpty(fileNameList)) {
                return JsonUtil.wapperObjToString(this.addError("文件上传异常"));
            }
            for (String fileName : fileNameList) {
                newFileNameList.add(uploadPath + fileName);
            }
            resultMap.put("fileName", newFileNameList.stream()
                    .map(filename -> ossDomain + filename)  // 每个文件名前加上域名
                    .collect(Collectors.joining(",")));
            return JsonUtil.wapperObjToString(resultMap);
        } catch (Exception e) {
            log.error("文件上传异常：", e);
            try {
                return JsonUtil.wapperObjToString(this.addError("文件上传异常"));
            } catch (JsonProcessingException ex) {
                log.error("文件上传JSON转换异常：", ex);
                return "";
            }
        }
    }
    private boolean checkFile(String fileName) {
        if (null == fileName) {
            return false;
        }
        //设置允许上传文件类型
        String suffixList = "gif,bmp,jpg,jpeg,png,pdf,zip,rar,mp4,avi,doc,docx,xls,xlsx,ppt,pptx,txt";
        // 获取文件后缀
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
        if (suffixList.contains(suffix.trim().toLowerCase())) {
            return true;
        }
        return false;
    }

    /**
     * 资质异常
     * @param dto
     * @return
     */
    @RequestMapping(value = "/qualificationException",method = RequestMethod.POST)
    @ResponseBody
    public R<Boolean> qualificationException(@RequestBody WorkerIdDTO dto) {
        try {
            Long workerId = dto.getWorkerId();
            MerchantBussinessDto merchant = (MerchantBussinessDto)xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getMerchantId();
            //判断是否为空
            if (BeanUtil.isEmpty(workerId)){
                return R.errorResult("workerId为null");
            }
            PlatformInAddNodeDTO platformInAddNodeDTO = new PlatformInAddNodeDTO();
            platformInAddNodeDTO.setWorkOrderId(workerId);
            platformInAddNodeDTO.setCreatePersonType(1);
            platformInAddNodeDTO.setCreatePersonId(merchantId.toString());
            Boolean b = interventionService.abnormalQualification(platformInAddNodeDTO);
            if (!b){
                return R.errorResult("资质异常处理失败");
            }
            return R.successResult(true);
        } catch (Exception e) {
            LOGGER.error("资质异常处理失败", e);
            return R.errorResult("资质异常处理失败");
        }
    }
    /**
     * 确认无误
     */
    @RequestMapping(value = "/confirmNoError",method = RequestMethod.POST)
    @ResponseBody
    public R<Boolean> confirmNoError(@RequestBody WorkerIdDTO dto) {
        try {
            Long workerId = dto.getWorkerId();
            MerchantBussinessDto merchant = (MerchantBussinessDto)xyyIndentityValidator.currentPrincipal();
            Long merchantId = merchant.getMerchantId();
            //判断是否为空
            if (BeanUtil.isEmpty(workerId)){
                return R.errorResult("workerId为null");
            }
            PlatformInAddNodeDTO platformInAddNodeDTO = new PlatformInAddNodeDTO();
            platformInAddNodeDTO.setWorkOrderId(workerId);
            platformInAddNodeDTO.setCreatePersonType(1);
            platformInAddNodeDTO.setCreatePersonId(merchantId.toString());
            Boolean b = interventionService.confirmAccuracy(platformInAddNodeDTO);
            if(!b){
                return R.errorResult("确认无误处理失败");
            }
            return R.successResult(true);
        } catch (Exception e) {
            LOGGER.error("确认无误处理失败", e);
            return R.errorResult("确认无误处理失败");
        }
    }

    /**
     * 提交时校验
     */
    @RequestMapping(value = "/checkApplyPlatformInWithRefund", method = RequestMethod.POST)
    @ResponseBody
    public Object checkApplyPlatformInWithRefund(@RequestParam(value = "refundOrderNo") String refundOrderNo,
                                                 @RequestParam(value = "orderNo") String orderNo){

        if (!refundOrderPlatformInCheckSwitch){
            return this.addResult();
        }
        if (StringUtil.isEmpty(refundOrderNo) || StringUtil.isEmpty(orderNo)){
            return this.addError("单号不能为空");
        }
        if (refundOrderNo.startsWith("RS")){
            OrderRefundBusinessDto orderRefundBusinessDto = orderRefundBusinessApi.selectByRefundOrderNo(refundOrderNo);
            if (Objects.nonNull(orderRefundBusinessDto)){
                Integer auditState = orderRefundBusinessDto.getAuditState();
                Integer refundType = orderRefundBusinessDto.getRefundType();
                if (auditState != -1){
                    return this.addResult();
                }
                //整单退款
                if (refundType == 2){
                    List<OrderRefundBusinessDto> refundOrders= orderRefundBusinessApi.selectRefundOrderListByOrderNo(orderNo);
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(refundOrders)){
                        List<OrderRefundBusinessDto> workingRefundOrders = refundOrders.stream().filter(e -> e.getAuditState() != -1 && !e.getRefundOrderNo().equals(refundOrderNo)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(workingRefundOrders)){
                            return this.addError("该订单存在退款中的退款单，退款单号"+workingRefundOrders.get(0).getRefundOrderNo()+"，暂不支持再次发起平台介入");
                        }
                    }
                }else if (refundType == 1){
                    List<OrderRefundDetailBusinessDto> customOrderDetails = orderRefundDetailBusinessApi.selectDetailOnlyByOrderNo(refundOrderNo);
                    List<OrderDetailBusinessDto> orderDetailList = orderDetailBusinessApi.getOrderDetailsAndRefundDetailForAdmin(orderNo);
                    if (org.springframework.util.CollectionUtils.isEmpty(customOrderDetails)) {
                        customOrderDetails = new ArrayList<>();
                    }
                    Map<Long,OrderDetailBusinessDto> orderDetailMap = orderDetailDtoListToMap(orderDetailList);
                    for (OrderRefundDetailBusinessDto customOrderDetail : customOrderDetails) {
                        if(customOrderDetail.getOrderDetailId()!=null){
                            Long key = customOrderDetail.getOrderDetailId();
                            OrderDetailBusinessDto orderDetailBusinessDto = orderDetailMap.get(key);
                            String productName = StringUtil.isEmpty(customOrderDetail.getProductName()) ? "" : customOrderDetail.getProductName();
                            String customOrderDetailRefundOrderNo = customOrderDetail.getRefundOrderNo();
                            if (orderDetailBusinessDto!= null && orderDetailBusinessDto.getCanRefundQuantity().compareTo(BigDecimal.ZERO)>0) {
                                if (customOrderDetail.getProductQuantity().compareTo(orderDetailBusinessDto.getCanRefundQuantity()) > 0) {
                                    return this.addError("系统识别到"+productName+"商品，存在另外一笔退款单,退款单号"+customOrderDetailRefundOrderNo+"，暂不支持再次发起平台介入");
                                }
                            }else {
                                return this.addError("系统识别到"+productName+"商品，存在另外一笔退款单,退款单号"+customOrderDetailRefundOrderNo+"，暂不支持再次发起平台介入");
                            }
                        }
                    }
                    return this.addResult();
                }

            }

        }
        return this.addResult();
    }
    public Map<Long, OrderDetailBusinessDto> orderDetailDtoListToMap(List<OrderDetailBusinessDto> orderDetailList) {
        Map<Long, OrderDetailBusinessDto> detailDtoMap = Maps.newHashMap();
        for (OrderDetailBusinessDto orderDetailDto : orderDetailList) {
            detailDtoMap.put(orderDetailDto.getId(), orderDetailDto);
        }
        return detailDtoMap;
    }
}
