package com.xyy.ec.pc.controller.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProcureMatchRuleVO {
    /**
     * 自营店铺
     */
    private List<MatchRuleShopVO> shopList;
    /**
     * pop店铺
     */
    private List<MatchRuleShopVO> popShopList;

    //是否满足采购库存
    private Boolean isCheckStock;

    //是否满足限购采购数量
    private Boolean isCheckLimit;

    //是否满足起订采购数量
    private Boolean isCheckMinOrder;

    /** 近效期大于多少天 */
    private Integer nearEffectDay;

    /**
     * 比价类型: 1:折后价，2:连锁指导价
     */
    private Integer matchPriceType;
}
