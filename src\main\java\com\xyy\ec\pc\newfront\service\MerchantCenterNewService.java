package com.xyy.ec.pc.newfront.service;

import com.xyy.ec.merchant.bussiness.dto.licence.LicenseValidDto;
import com.xyy.ec.merchant.bussiness.result.MerchantRelateShopResult;
import com.xyy.ec.pc.controller.vo.merchant.MerchantAccountInfoVO;
import com.xyy.ec.pc.enums.TerminalTypeEnum;
import com.xyy.ec.pc.newfront.dto.MerchantRespVO;
import com.xyy.ec.pc.newfront.vo.AddMerchantParamVO;
import com.xyy.ec.pc.newfront.vo.MerchantParamVO;
import com.xyy.ec.pc.newfront.vo.MerchantStatusSearchParamVO;
import com.xyy.ec.pc.rest.AjaxResult;

import javax.servlet.http.HttpServletRequest;

public interface MerchantCenterNewService {

    /**
     * 获取资质提醒  type有值说明是主页弹窗，一天弹一次
     */
    AjaxResult<LicenseValidDto> validityRemind(TerminalTypeEnum terminalType) throws Exception;

    /**
     * 切换店铺
     * @param merchantParamVO
     * @param request
     * @return
     */
    AjaxResult<MerchantRespVO> selectMerchant(HttpServletRequest request, MerchantParamVO merchantParamVO) throws Exception;
    /**
     * 分页查询账号关联的店铺
     * @param merchantParamVO
     * @return AjaxResult
     */
    AjaxResult<MerchantRespVO> listAccountRelatedMerchants(MerchantParamVO merchantParamVO) throws Exception;

    /**
     * 个人中心首页入口
     * @return AjaxResult
     */
    AjaxResult<MerchantRespVO> selectUser() throws Exception;

    /**
     * 验证资质有效性
     */
    AjaxResult<Integer> validity();

    /**
     * 添加店铺
     * @param vo
     * @return
     */
    AjaxResult<Object> addMerchant(AddMerchantParamVO vo);

    AjaxResult<Object> getBusinessInfo(HttpServletRequest request,AddMerchantParamVO vo);

    AjaxResult<Object> getMerchantStatus(HttpServletRequest request, MerchantStatusSearchParamVO vo);

    /**
     * 获取当前登录的账号信息
     * @return
     */
    AjaxResult<MerchantAccountInfoVO> getAccountInfo();

    /**
     * 关联店铺
     * @param request
     * @param poiIdStr
     * @return
     */
    AjaxResult<MerchantRelateShopResult> relateShop(HttpServletRequest request, String poiIdStr);

    AjaxResult<Object> addClerkLicenseAudit(String targetMerchantIdStr, String licenseAuditImagesJsonStr);

    AjaxResult<Integer> getLicenseStatus(Long merchantId);

    /**
     * 查询店铺信息
     */
    AjaxResult<Object> getSimpleMerchantInfo(Long merchantId);
}
