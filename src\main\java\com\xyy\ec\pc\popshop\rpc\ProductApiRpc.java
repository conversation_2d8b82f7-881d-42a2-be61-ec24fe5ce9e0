package com.xyy.ec.pc.popshop.rpc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pc.cms.utils.SkuThreadPoolExecutorSingleton;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import com.xyy.ec.product.business.ecp.out.pop.api.ProductForPopApi;
import com.xyy.ec.product.business.ecp.out.product.dto.ShopSkuCountDTO;
import com.xyy.ec.product.business.ecp.out.shop.ProductForShopApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> lizhiwei
 * @description: 商品api
 * create at:  2021/3/17  11:57
 **/
@Slf4j
@Component
public class ProductApiRpc {
    @Reference(version = "1.0.0")
    private ProductForPopApi productForPopApi;
    @Reference(version = "1.0.0")
    private ProductForShopApi productForShopApi;
    @Autowired
    private AppProperties appProperties;

   public List<ShopSkuCountDTO>  getCompanySkuCount(List<String> shopCodes){
        log.info("#ProductApiRpc.getCompanySkuCount#info,参数：shopCodes:{}",shopCodes);
        try {
            ApiRPCResult<List<ShopSkuCountDTO>> rpcResult = productForShopApi.getSkuUpCountByShopCodes(shopCodes);
            log.info("#ProductApiRpc.getCompanySkuCount#info,参数：shopCodes:{},rpcResult:{}",shopCodes, JSON.toJSONString(rpcResult));
         return rpcResult.isSuccess()?rpcResult.getData(): Lists.newArrayList();
        } catch (Exception e) {
            log.error("#ProductApiRpc.getCompanySkuCount#error,参数：shopCodes:{}",shopCodes,e);
           return Lists.newArrayList();
        }
    }
   public List<ListProduct> findOnSaleProductBySkuIdList(List<Long> skuIds, Long merchantId, String branchCode){
       log.info("#ProductApiRpc.findOnSaleProductBySkuIdList#info,参数：skuIds:{},merchantId:{},branchCode:{}",skuIds,merchantId,branchCode);
       try {
           ApiRPCResult<ListSkuSearchData> rpcResult = productForPopApi.findOnSaleProductBySkuIdList(skuIds,merchantId,branchCode);
           log.info("#ProductApiRpc.findOnSaleProductBySkuIdList#info,参数：skuIds:{},merchantId:{},branchCode:{}，rpcResult:{}",skuIds,merchantId,branchCode,JSON.toJSONString(rpcResult));
           if (rpcResult.isFail()){
               log.error("#ProductApiRpc.findOnSaleProductBySkuIdList#error,RPC查询失败,参数：skuIds:{},merchantId:{},branchCode:{},rpcResult:{}",skuIds,merchantId,branchCode,JSON.toJSONString(rpcResult));
          return Lists.newArrayList();
           }
           ListSkuSearchData listSkuSearchData = rpcResult.getData();
           if(listSkuSearchData==null){
               return Lists.newArrayList();
           }
           return listSkuSearchData.getSkuDtoList();
       } catch (Exception e) {
           log.error("#ProductApiRpc.findOnSaleProductBySkuIdList#error,参数：skuIds:{},merchantId:{},branchCode:{}",skuIds,merchantId,branchCode,e);
           return Lists.newArrayList();
       }
   }
   public Map<Long, ListProduct> findOnSaleProductMapBySkuIdList(List<Long> skuIds, Long merchantId, String branchCode, boolean isVirtual){
       if(CollectionUtils.isEmpty(skuIds)){
           return Maps.newHashMap();
       }

       List<ListProduct> productList = new ArrayList<>();
       List<List<Long>> lists = Lists.partition(skuIds, 200);
       for (List<Long> list : lists) {
           List<ListProduct> listProducts = findOnSaleProductBySkuIdList(list, merchantId, branchCode);
           if(CollectionUtils.isEmpty(listProducts)){
               continue;
           }
           productList.addAll(listProducts);
       }

       if (CollectionUtils.isEmpty(productList)) {
           return Maps.newHashMap();
       }
       Map<Long, ListProduct> skuVOMap = new HashMap<>(skuIds.size());
       productList.stream().forEach(skuVO -> {
           int status = skuVO.getStatus();
           Integer purchaseType = skuVO.getPurchaseType();
           //只展示 销售中，特惠中，售罄的商品
           boolean boolStatus= (status == ProductEnumDTO.SkuStatusEnum.ONSALE.getId() ||
                   status == ProductEnumDTO.SkuStatusEnum.PRICEOFF.getId() ||
                   status == ProductEnumDTO.SkuStatusEnum.SOLDOUT.getId());
           //非空销
           boolean boolPurchaseType = (purchaseType != 2);

           boolean virtualSkuFlag = skuVO.getIsVirtualSupplier() == null?false:skuVO.getIsVirtualSupplier();
           if(boolStatus && boolPurchaseType &&
                   ((isVirtual && virtualSkuFlag)||(!isVirtual&&!virtualSkuFlag))){
               skuVOMap.put(skuVO.getId(), skuVO);
           }
       });
       return skuVOMap;
   }

    public Map<Long, ListProduct> findOnSaleProductMapBySkuIdListConcurrently(List<Long> skuIds, Long merchantId, String branchCode, boolean isVirtual){
        if(CollectionUtils.isEmpty(skuIds)){
            return Maps.newHashMap();
        }
        if(BooleanUtils.isNotTrue(appProperties.isShopIndexGetSkuConcurrentFlag())){
            return findOnSaleProductMapBySkuIdList(skuIds, merchantId, branchCode, isVirtual);
        }

        int shopIndexGetSkuPageSize = appProperties.getShopIndexGetSkuPageSize();
        // 分组
        List<List<Long>> csuIdsParts = Lists.partition(skuIds, shopIndexGetSkuPageSize);

        ThreadPoolExecutor threadPoolExecutor = SkuThreadPoolExecutorSingleton.getSkuByCsuIds();
        List<Future<Map<Long, ListProduct>>> futures = Lists.newArrayListWithExpectedSize(csuIdsParts.size());
        Future<Map<Long, ListProduct>> future;
        for (List<Long> part : csuIdsParts) {
            try {
                future = threadPoolExecutor.submit(() -> {
                    Map<Long, ListProduct> skuMap = findOnSaleProductMapBySkuIdList(part, merchantId, branchCode, isVirtual);
                    if (MapUtils.isEmpty(skuMap)) {
                        return Maps.newHashMap();
                    }
                    return skuMap;
                });
                futures.add(future);
            } catch (Exception e) {
                log.error("首页查询商品信息滤线程池并发；branchCode：{}，merchantId：{}，csuIds：{}", branchCode, merchantId, JSONArray.toJSONString(part), e);
            }
        }
        // 获取结果
        Map<Long, ListProduct> result = Maps.newHashMap();
        for (Future<Map<Long, ListProduct>> f : futures) {
            try {
                Map<Long, ListProduct> skuMap = f.get(appProperties.getShopIndexGetSkuConcurrentTimeout(), TimeUnit.MILLISECONDS);
                if (MapUtils.isNotEmpty(skuMap)) {
                    result.putAll(skuMap);
                }
            } catch (Exception e) {
                log.error("首页查询商品信息线程池并发；branchCode：{}，merchantId：{}", branchCode, merchantId, e);
            }
        }
        return result;
    }
}
