package com.xyy.ec.pc.cms.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 各类灰度开关工具类
 *
 * <AUTHOR>
 */
@Component
public class LayoutGraySwitchUtils {

    /**
     * 灰度开关：一品卖全国的开启灰度的区域编码
     */
    @Value("#{'${layout.open.gray.one.piece.branch:}'.split(',')}")
    private Set<String> openGrayBranchForOnePiece;

    /**
     * 是否开启灰度
     *
     * @param branchCode
     * @return
     */
    public Boolean isOpenGrayByBranchForOnePiece(String branchCode) {
        return true;
//        return openGrayBranchForOnePiece.contains(branchCode);
    }

}
