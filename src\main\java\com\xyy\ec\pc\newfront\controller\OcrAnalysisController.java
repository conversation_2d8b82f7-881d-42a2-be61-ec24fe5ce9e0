package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.dto.OcrAnalysisResultRespVO;
import com.xyy.ec.pc.newfront.dto.OcrParseRespVO;
import com.xyy.ec.pc.newfront.service.OcrDataService;
import com.xyy.ec.pc.newfront.vo.OcrAnalysisDataParamVO;
import com.xyy.ec.pc.newfront.vo.OcrAnalysisResultParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025-07-10 10:42
 */

@CustomizeCmsResponse
@RestController
@RequiredArgsConstructor
@RequestMapping("/new-front/newRegisterData")
@Slf4j
public class OcrAnalysisController {


    private final OcrDataService ocrDataService;

    /**
     * ocr识别
     * @param vo
     * @return
     */
    @PostMapping("/getBusinessAndGeneral")
    public AjaxResult<OcrAnalysisResultRespVO> identifyBusinessAndGeneral(@RequestBody OcrAnalysisResultParamVO vo) {
        log.info("OcrDataController getBusinessAndGeneral param imgUrl:{},type:{}",vo.getImgUrl(),vo.getType());
        try {
            return AjaxResult.successResult(ocrDataService.identify(vo.getType(), vo.getImgUrl()));
        } catch (Exception e) {
            log.error("OcrDataController getBusinessAndGeneral error",e);
            return AjaxResult.errResult("identifyError");
        }

    }

    /**
     * ocr识别数据获取
     * @param vo
     * @return
     */
    @PostMapping("/getBusinessAndGeneralData")
    public AjaxResult<OcrParseRespVO> getBusinessAndGeneralData(@RequestBody OcrAnalysisDataParamVO vo) {
        log.info("OcrDataController getBusinessAndGeneralData param dataKey:{},type:{}",vo.getDataKey(),vo.getType());
        try {
            return  AjaxResult.successResult(ocrDataService.getData(vo.getDataKey(),vo.getType()));
        } catch (Exception e) {
            log.error("OcrDataController getBusinessAndGeneralData error",e);
            return AjaxResult.errResult("getDataError");
        }
    }


}
