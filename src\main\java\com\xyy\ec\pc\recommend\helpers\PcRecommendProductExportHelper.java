package com.xyy.ec.pc.recommend.helpers;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.xyy.ec.pc.recommend.vo.PcRecommendCardVO;
import com.xyy.ec.pc.recommend.vo.PcRecommendProductExportVO;
import com.xyy.ec.pc.recommend.vo.PcRecommendProductVO;
import com.xyy.ec.pc.service.marketing.dto.MarketingGroupBuyingActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingWholesaleActivityInfoDTO;

import java.math.BigDecimal;
import java.util.List;

public class PcRecommendProductExportHelper {

    /**
     * 创建
     *
     * @param product
     * @return
     */
    public static List<PcRecommendProductExportVO> create(List<PcRecommendCardVO> allRows) {
        if (CollectionUtils.isEmpty(allRows)) {
            return Lists.newArrayList();
        }
        List<PcRecommendProductExportVO> productExports = Lists.newArrayList();
        for (PcRecommendCardVO allRow : allRows) {
            PcRecommendProductVO productInfo = allRow.getProductInfo();
            if (productInfo == null) {
                continue;
            }
            BigDecimal price = getPrice(productInfo);
            productExports.add(PcRecommendProductExportVO.builder()
                    .shopName(productInfo.getShowName())
                    .spec(productInfo.getSpec())
                    .manufacturer(productInfo.getManufacturer())
                    .price(price).build());
        }
        return productExports;
    }

    private static BigDecimal getPrice(PcRecommendProductVO productInfo) {
        if (productInfo == null) {
            return null;
        }

        BigDecimal price = productInfo.getFob();

        // 拼团价
        MarketingGroupBuyingActivityInfoDTO actPt = productInfo.getActPt();
        if (actPt != null && actPt.getAssemblePrice() != null) {
            price = actPt.getAssemblePrice();
        }

        // 批购包邮价
        MarketingWholesaleActivityInfoDTO actPgby = productInfo.getActPgby();
        if (actPgby != null && actPgby.getAssemblePrice() != null) {
            price = actPgby.getAssemblePrice();
        }

        // 秒杀价
        MarketingSeckillActivityInfoDTO actSk = productInfo.getActSk();
        if (actSk != null && actSk.getSkPrice() != null) {
            price = actSk.getSkPrice();
        }

        //折后价
        if (productInfo.getCouponPrice() != null) {
            price = productInfo.getCouponPrice();
        }

        return price;
    }
}
