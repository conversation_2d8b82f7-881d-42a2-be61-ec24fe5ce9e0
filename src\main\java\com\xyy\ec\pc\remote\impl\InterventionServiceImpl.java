package com.xyy.ec.pc.remote.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.cs.api.dto.PlatformInAddNodeDTO;
import com.xyy.ec.cs.api.dto.PlatformInWorkorderDetailDTO;
import com.xyy.ec.cs.api.merchant.CsWorkOrderForMerchantApi;
import com.xyy.ec.cs.api.dto.PlatformInWorkorderCreateDTO;
import com.xyy.ec.cs.api.order.CsOrderRefundApi;
import com.xyy.ec.order.business.api.OrderRefundBusinessApi;
import com.xyy.ec.order.business.api.OrderRefundDetailBusinessApi;
import com.xyy.ec.order.business.dto.OrderRefundBusinessDto;
import com.xyy.ec.order.business.dto.OrderRefundDetailBusinessDto;
import com.xyy.ec.pc.model.SupplementaryDescriptionDTO;
import com.xyy.ec.pc.remote.InterventionService;
import com.xyy.ec.pc.search.ecp.vo.RefundImageInformationVo;
import com.xyy.ec.pc.search.ecp.vo.RefundProductInformation;
import com.xyy.ec.pc.search.ecp.vo.WorkOrderVo;
import com.xyy.ec.pc.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class InterventionServiceImpl implements InterventionService {
    @Reference(version = "1.0.0")
    private OrderRefundBusinessApi orderRefundBusinessApi;

    @Reference(version = "1.0.0")
    private OrderRefundDetailBusinessApi orderRefundDetailBusinessApi;

    @Reference(version = "1.0.0")
    private CsOrderRefundApi csOrderRefundApi;

    /**
     * 创建工单
     */
    @Override
    public Long create(PlatformInWorkorderCreateDTO param) {
        log.info("创建工单开始参数为 WorkOrderDTO:{}" , param);
        try {
            ApiRPCResult apiRPCResult = csOrderRefundApi.create(param);
            Long id = (Long) apiRPCResult.getData();
            if (id == null){
                return null;
            }
            return id;
        } catch (Exception e) {
            log.error("创建工单异常",e);
            throw new RuntimeException(e);
        }

    }


    /**
     * 获取工单倒计时时间
     * @param workOrderId
     * @return
     */
    @Override
    public Long getCountdownTime(PlatformInAddNodeDTO workOrderId) {
        ApiRPCResult countdownTime = csOrderRefundApi.urge(workOrderId);
        Long data = (Long) countdownTime.getData();
        return data;
    }

    @Override
    public void refundDetail(String refundNo) {
        OrderRefundBusinessDto orderRefundBusinessDto = orderRefundBusinessApi.selectByRefundOrderNo(refundNo);
        log.info("查询退单详情参数为:{}",orderRefundBusinessDto);
        WorkOrderVo vo = new WorkOrderVo();
        vo.setRefundMoney(orderRefundBusinessDto.getRefundMoney());
        vo.setRefundCreateTime(orderRefundBusinessDto.getRefundCreateTime());
        vo.setOrderNo(orderRefundBusinessDto.getOrderNo());
        vo.setRefundType(orderRefundBusinessDto.getRefundType());
        vo.setRefundExplain(orderRefundBusinessDto.getRefundExplain());
        vo.setMerchantName(orderRefundBusinessDto.getMerchantName());
        vo.setRefundReason(orderRefundBusinessDto.getRefundReason());
        vo.setAuditState(orderRefundBusinessDto.getAuditState());
        vo.setAuditProcessState(orderRefundBusinessDto.getAuditProcessState());

        OrderRefundDetailBusinessDto orderRefundDetailBusinessDto = new OrderRefundDetailBusinessDto();
        orderRefundDetailBusinessDto.setOrderNo(orderRefundBusinessDto.getOrderNo());
        orderRefundDetailBusinessDto.setRefundOrderNo(orderRefundBusinessDto.getRefundOrderNo());
        List<OrderRefundDetailBusinessDto> orderRefundDetailBusinessDtos = orderRefundDetailBusinessApi.selectList(orderRefundDetailBusinessDto);
        for (OrderRefundDetailBusinessDto orderRefundDetailBusinessDto1 : orderRefundDetailBusinessDtos) {
            RefundProductInformation refundProductInformation = new RefundProductInformation();
            refundProductInformation.setImgUrl(orderRefundDetailBusinessDto1.getImageUrl());
            refundProductInformation.setSpecificationName(orderRefundDetailBusinessDto1.getSpec());
            refundProductInformation.setSupplier(orderRefundDetailBusinessDto1.getManufacturer());
            refundProductInformation.setRefundNum(orderRefundDetailBusinessDto1.getProductAmount());
            vo.getRefundProductInfoList().add(refundProductInformation);
        }
    }

    /**
     * 撤销工单
     * @param
     * @return
     */
    @Override
    public Boolean updateWorkOrder(PlatformInAddNodeDTO dto) {
        log.info("撤销工单参数为:{}",dto);
        ApiRPCResult revoke = csOrderRefundApi.revoke(dto);
        if (revoke.isSuccess()) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean addWorkOrderNode(PlatformInAddNodeDTO dto) {
        log.info("补充描述的参数为:{}",dto);
        try {
            ApiRPCResult supplement = csOrderRefundApi.supplement(dto);
            if (supplement.isSuccess()) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("意见征询异常",e);
            return false;
        }
    }

    @Override
    public Boolean opinionSubmit(PlatformInAddNodeDTO dto) {
        log.info("意见征询的节点参数:{}",dto);
        try {
            ApiRPCResult supplement = csOrderRefundApi.supplement(dto);
            if (supplement.isSuccess()) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("意见征询异常",e);
            return false;
        }
    }
    /**
     * 资质异常
     * @param
     * @return
     */
    @Override
    public Boolean abnormalQualification(PlatformInAddNodeDTO addNodeDTO) {
        log.info("资质异常的参数:{}",addNodeDTO);
        try {
            ApiRPCResult apiRPCResult = csOrderRefundApi.qualificationIssues(addNodeDTO);
            if (apiRPCResult.isSuccess()){
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("资质异常异常InterventionServiceImpl",e);
            return false;
        }
    }

    /**
     * 确认无误
     * @param
     * @return
     */
    @Override
    public Boolean confirmAccuracy(PlatformInAddNodeDTO addNodeDTO) {
        log.info("确认无误的参数:{}",addNodeDTO);
        try {
            ApiRPCResult apiRPCResult = csOrderRefundApi.confirm(addNodeDTO);
            if (apiRPCResult.isSuccess()){
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("确认无误异常InterventionServiceImpl",e);
            return false;
        }
    }

}

