/**
 * @(#)KeepVerifier.java Copyright 2011 jointown, Inc. All rights reserved.
 */
package com.xyy.ec.pc.base;

import com.xyy.ec.pc.util.Verifier;

/**
 * 提供保持登录状态的校验器
 * 
 * 
 * @version 1.0,2011-7-7
 */
public abstract class KeepLoginStatusVerifier implements Verifier {

    /**
     * 默认保持登录状态的天数
     */
    protected static final int DEFAULT_KEEP_LOGIN_DAY = 14;
    private boolean            keepLoginStatus;
    private int                keepLoginMaxTime;

    protected KeepLoginStatusVerifier(boolean keepLoginStatus, int keepLoginDay){
        this.keepLoginStatus = keepLoginStatus;
        this.keepLoginMaxTime = keepLoginDay * 3600 * 24;
    }

    public static int getDefaultKeepLoginDay() {
        return DEFAULT_KEEP_LOGIN_DAY;
    }

    public boolean isKeepLoginStatus() {
        return keepLoginStatus;
    }

    public int getKeepLoginMaxTime() {
        return keepLoginMaxTime;
    }

}
