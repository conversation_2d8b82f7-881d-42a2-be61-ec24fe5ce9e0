package com.xyy.ec.pc.rpc;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.pc.util.JsonUtil;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.user.center.api.client.TokenApi;
import com.xyy.saas.user.center.api.client.YbmAccountApi;
import com.xyy.saas.user.center.api.pojo.request.QueryOneYbmAccountDTO;
import com.xyy.saas.user.center.api.pojo.response.BaseYbmAccountDTO;
import com.xyy.saas.user.center.api.pojo.response.TokenResponse;
import com.xyy.user.center.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description
 * @Date 2025/7/30 16:47
 */
@Slf4j
@Service
public class SaasServiceRpc {

    @Reference(version = "1.0.0", registry = "saas", validation = "false")
    private TokenApi tokenApi;

    @Reference(version = "1.0.0", registry = "saas", validation = "false")
    private YbmAccountApi ybmAccountApi;

    public BaseYbmAccountDTO getEcSyncNoBySaasOrganSign(QueryOneYbmAccountDTO param) {
        if (log.isDebugEnabled()) {
            log.debug("查询绑定药帮忙账号getYbmAccount, param：{}", JsonUtil.toJson(param));
        }
        try {

            BaseYbmAccountDTO result = null;
            Result<BaseYbmAccountDTO> resultDTO = ybmAccountApi.getYbmAccount(param);
            if (ObjectUtil.isNotNull(resultDTO) && resultDTO.isSuccess()) {
                result = resultDTO.getResult();
            }
            if (log.isDebugEnabled()) {
                log.debug("查询绑定药帮忙账号 result：{}", JsonUtil.toJson(result));
            }
            return result;
        } catch (Exception e) {
            log.debug("查询绑定药帮忙账号异常 param：{}", param, e);
            return null;
        }
    }

    public TokenResponse validateAccessToken(String accessToken) {
        if (log.isDebugEnabled()) {
            log.debug("校验accessToken是否有效, accessToken：{}", accessToken);
        }
        try {
            TokenResponse tokenResponse = tokenApi.validateAccessToken(accessToken);
            if (log.isDebugEnabled()) {
                log.debug("校验accessToken是否有效, tokenResponse：{}", JsonUtil.toJson(tokenResponse));
            }
            return tokenResponse;
        } catch (Exception e) {
            log.debug("校验accessToken是否有效异常 accessToken：{}", accessToken, e);
            return null;
        }
    }

    public TokenResponse getOrganSignFromToken(String organSign) {

        ResultVO<?> resultVO = null;
        try {
            resultVO = tokenApi.generateAccessToken(organSign);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (TokenResponse) resultVO.getResult();
    }

}
