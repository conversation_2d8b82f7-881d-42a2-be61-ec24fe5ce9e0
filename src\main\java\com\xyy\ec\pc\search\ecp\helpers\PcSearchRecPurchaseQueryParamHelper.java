package com.xyy.ec.pc.search.ecp.helpers;

import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.search.ecp.params.PcSearchRecPurchaseQueryParam;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchRecPurchaseTypeEnum;

import java.util.Objects;

/**
 * {@link PcSearchRecPurchaseQueryParam } 帮助类
 *
 * <AUTHOR>
 */
public class PcSearchRecPurchaseQueryParamHelper {

    /**
     * 查询校验
     *
     * @param queryParam
     * @return
     */
    public static boolean validate(PcSearchRecPurchaseQueryParam queryParam) {
        if (Objects.isNull(queryParam)) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "查询参数必填");
        }
        Long mainCsuId = queryParam.getMainCsuId();
        if (Objects.isNull(mainCsuId) || mainCsuId <= 0L) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "mainCsuId参数必填");
        }
        Integer showRecPurchaseType = queryParam.getShowRecPurchaseType();
        if (Objects.isNull(EcpSearchRecPurchaseTypeEnum.valueOfCustom(showRecPurchaseType))) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "showRecPurchaseType参数非法");
        }
        return true;
    }

}
