package com.xyy.ec.pc.popshop.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xyy.ec.marketing.client.api.MarketingQueryApi;
import com.xyy.ec.marketing.client.constants.ProductOwnerBizTypeEnum;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingSkuDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingStepPriceDto;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.cms.enums.LayoutComponentEnum;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.controller.vo.ShopGoodsVo;
import com.xyy.ec.pc.popshop.helper.CompanyHelper;
import com.xyy.ec.pc.popshop.helper.ShopIndexHelper;
import com.xyy.ec.pc.popshop.rpc.ClientAccountServerRpc;
import com.xyy.ec.pc.popshop.rpc.OrderBusinessApiRpc;
import com.xyy.ec.pc.popshop.rpc.PopCorporationServiceRpc;
import com.xyy.ec.pc.popshop.rpc.ProductApiRpc;
import com.xyy.ec.pc.popshop.service.PopShopService;
import com.xyy.ec.pc.popshop.vo.*;
import com.xyy.ec.pc.popshop.vo.account.AboutUsVo;
import com.xyy.ec.pc.popshop.vo.account.AccountResourceVo;
import com.xyy.ec.pc.popshop.vo.account.OpenAccountVo;
import com.xyy.ec.pc.rpc.HyperSpaceRpc;
import com.xyy.ec.pc.rpc.ShopServiceRpc;
import com.xyy.ec.pc.search.vo.PinTuanActInfoVo;
import com.xyy.ec.pc.service.ShopConfuseService;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.marketing.dto.MarketingCsuResultDto;
import com.xyy.ec.pc.service.marketing.dto.MarketingWholesaleActivityInfoDTO;
import com.xyy.ec.pc.util.ProductMangeUtils;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.pop.server.api.account.dto.ClientAccountInfoExDto;
import com.xyy.ec.pop.server.api.account.dto.ClientExplanationDto;
import com.xyy.ec.pop.server.api.account.dto.ClientResourcesDto;
import com.xyy.ec.pop.server.api.advertise.dto.PopAdvertisementDto;
import com.xyy.ec.pop.server.api.external.app.dto.AppFloorDto;
import com.xyy.ec.pop.server.api.external.app.dto.AppFloorGoodsDto;
import com.xyy.ec.pop.server.api.external.app.dto.FloorGroupInfoForPcVo;
import com.xyy.ec.pop.server.api.external.enums.AccountWayEnum;
import com.xyy.ec.pop.server.api.external.enums.FloorTypeEnum;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationBusinessDto;
import com.xyy.ec.pop.server.api.merchant.dto.CorporationQualificationDto;
import com.xyy.ec.pop.server.api.merchant.dto.ShippingDto;
import com.xyy.ec.pop.server.api.seller.dto.CorporationBaseInfoDto;
import com.xyy.ec.pop.server.api.seller.dto.PopCorporationDto;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.ecp.out.product.dto.ShopSkuCountDTO;
import com.xyy.ec.search.engine.enums.CsuOrder;
import com.xyy.ec.search.engine.pagination.Page;
import com.xyy.ec.shop.server.business.enums.ShopPatternEnum;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.ec.system.business.dto.CompanyBranchBusinessDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lizhiwei
 * @description: POP店铺服务
 * create at:  2021/3/16  16:43
 **/
@Slf4j
@Service
public class PopShopServiceImpl implements PopShopService {
    /**
     * 企业默认logo
     */
    private static final String ORG_DEFAULT_LOGO = "http://upload.ybm100.com/pop/org_default_logo.png";
    @Value("${pc.shop.index.url:www.ybm100.com/company/center/companyInfo/shopIndex.htm?orgId=}")
    private String SHOP_INDEX_URL;
   @Autowired
   private PopCorporationServiceRpc popCorporationServiceRpc;
   @Autowired
   private OrderBusinessApiRpc orderBusinessApiRpc;
   @Autowired
   private ClientAccountServerRpc clientAccountServerRpc;

   @Autowired
   private ProductApiRpc productApiRpc;

   @Reference(version = "1.0.0")
   private OrderBusinessApi orderBusinessApi;

    @Reference(version = "1.0.0")
    private MarketingQueryApi marketingQueryApi;

    @Autowired
    private HyperSpaceRpc hyperSpaceRpc;

    @Autowired
    private MarketingService marketingService;

    @Resource
    private ShopServiceRpc shopServiceRpc;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private ShopConfuseService shopConfuseService;

    @Autowired
    private CmsAppProperties cmsAppProperties;

    @Value("${pc.shop.index.search.page.size:400}")
    private int shopIndexSearchPageSize;

    @Value("${pc.shop.index.act.sku.limit:30}")
    private int shopIndexActSkuLimit;

    @Override
    public ShopIndexVo shopIndexView(String orgId, Long merchantId, String branchCode) {
        ShopIndexVo companyIndexVO = new ShopIndexVo();
        if (BooleanUtils.isTrue(shopConfuseService.isCantShowShop(orgId))) {
            log.warn("PopShopServiceImpl.shopIndexView，Pop店铺首页数据，不显示店铺，orgId：{}，merchantId：{}，branchCode：{}", orgId, merchantId, branchCode);
            companyIndexVO.setAdrVOS(Lists.newArrayList());
            companyIndexVO.setFloorVOS(Lists.newArrayList());
            return companyIndexVO;
        }
        List<CompanyShopFloorVo> floorVOS = new ArrayList<>();

        //请求楼层商品信息
        Future<List<AppFloorDto>> floorFuture = CompletableFuture.supplyAsync(() -> popCorporationServiceRpc.listFloor(orgId,branchCode));
        //请求广告
        Future<List<PopAdvertisementDto>> adrFuture = CompletableFuture.supplyAsync(() -> popCorporationServiceRpc.getAdvList4Shop(orgId,branchCode));
        //拿到楼层数据后，获取楼层商品信息
        List<AppFloorDto> popfloorInfos = getPopFloorInfoByFuture(floorFuture);
        //楼层商品返回结果集合
        List<AppFloorDto> floorInfos = new ArrayList<>();

        ShopInfoDTO shopInfoDTO = shopServiceRpc.queryShopByOrgId(orgId);
        boolean isVirtual = shopInfoDTO ==null?false:ShopPatternEnum.VIRTUAL.getCode().equalsIgnoreCase(shopInfoDTO.getShopPatternCode());

        //组装查询拼团和特价
        getActivityFloor(orgId, merchantId, floorInfos, isVirtual);

        floorInfos.addAll(popfloorInfos);

        List<Long> skuIds = CompanyHelper.getPopFloorSkuIds(floorInfos);
        Map<Long, ListProduct> skuVOMap = productApiRpc.findOnSaleProductMapBySkuIdListConcurrently(skuIds, merchantId, branchCode, isVirtual);
        log.info("PopShopServiceImpl.shopIndexView, skuIds:{}, merchantId:{}, branchCode:{}, skuVOMap:{}",
                JSON.toJSONString(skuIds), merchantId, branchCode, JSON.toJSONString(skuVOMap));
        {
            // 处方药商品默认图处理
            if (log.isDebugEnabled()) {
                log.debug("【处方药商品默认图处理】merchantId：{}，orgId：{}，原商品信息：{}", merchantId, orgId, JSONObject.toJSONString(skuVOMap));
            }
            if (BooleanUtils.isTrue(cmsAppProperties.getIsOpenIndexProductDefaultImageFeature())) {
                String defaultImageUrl = cmsAppProperties.getProductDefaultImageUrl();
                skuVOMap.values().stream().filter(item -> Objects.nonNull(item) && Objects.equals(item.getDrugClassification(), 3))
                        .forEach(item -> item.setImageUrl(defaultImageUrl));
                if (log.isDebugEnabled()) {
                    log.debug("【处方药商品默认图处理】merchantId：{}，orgId：{}，处理后商品信息：{}", merchantId, orgId, JSONObject.toJSONString(skuVOMap));
                }
            }
        }
        //组装广告信息
        List<PopAdvertisementDto> adrInfos = getPopAdvertiseByFuture(adrFuture);
        if(CollectionUtils.isNotEmpty(adrInfos)){
            List<CompanyIndexAdrVo> adrVOS = adrInfos.stream().map(adrInfo -> CompanyHelper.parsePopAdr(adrInfo)).collect(Collectors.toList());
            companyIndexVO.setAdrVOS(adrVOS);
        }

        if(BooleanUtils.isTrue(appProperties.isShopIndexGetSkuIdNewFlag())){
            setFloorActGoods(floorInfos, skuVOMap, merchantId);
        }

        //组装楼层商品信息
        setFloorGoods(floorVOS, floorInfos, skuVOMap);
        log.info("PopShopServiceImpl.setFloorGoods-after参数# floorVOS:{}", JSON.toJSONString(floorVOS));
        if(CollectionUtils.isNotEmpty(floorVOS)){
            popGroup(merchantId, floorVOS);
        }
        companyIndexVO.setFloorVOS(floorVOS);
        fillScmId(floorVOS);
        // 店铺公告
        String shopNoticeByOrgId = popCorporationServiceRpc.getShopNoticeByOrgId(orgId);
        companyIndexVO.setShopNotice(shopNoticeByOrgId);
        if (shopInfoDTO != null) {
            companyIndexVO.setShopCode(shopInfoDTO.getShopCode());
        }
        return companyIndexVO;
    }

    private void fillScmId(List<CompanyShopFloorVo> floorVOS) {
        floorVOS.forEach(floorVO -> {
            if (CollectionUtils.isNotEmpty(floorVO.getListProductVos())) {
                String alphanumeric = RandomStringUtils.randomAlphanumeric(8);
                floorVO.getListProductVos().forEach(product -> {
                    product.setScmId(alphanumeric);
                });
            }
        });
    }

    private void popGroup(Long merchantId, List<CompanyShopFloorVo> floorVOS) {
        //组装普通pop商品楼层
        List<ShopListProductVo> popListProductVos = floorVOS.stream().filter(vo -> vo.getFloorType() == FloorTypeEnum.POP_FLOOR.getCode())
                .map(CompanyShopFloorVo::getListProductVos).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(popListProductVos)){
            return;
        }
        List<Long> popFloorskuIds = popListProductVos.stream().map(ShopListProductVo::getId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(popFloorskuIds)) {
            //查询拼团和批购包邮信息
            Map<Long, GroupBuyingInfoDto> skuIdToInProgressGroupBuyingInfoMap = marketingService.getActCardInfoBySkuIdList(popFloorskuIds,
                    Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()), merchantId,
                    Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), null);
            if (MapUtils.isNotEmpty(skuIdToInProgressGroupBuyingInfoMap)) {
                for (ShopListProductVo productInfoVO : popListProductVos) {
                    GroupBuyingInfoDto groupBuyingInfoDto = skuIdToInProgressGroupBuyingInfoMap.get(productInfoVO.getId());
                    if (Objects.nonNull(groupBuyingInfoDto)) {
                        FloorGroupInfoForPcVo groupInfoForPcVo = getGroupInfoForPcVo(groupBuyingInfoDto);
                        if (Objects.equals(groupBuyingInfoDto.getActivityType(), MarketingEnum.PING_TUAN.getCode())) {
                            findActPt(productInfoVO, groupInfoForPcVo);
                        } else if (Objects.equals(groupBuyingInfoDto.getActivityType(), MarketingEnum.PI_GOU_BAO_YOU.getCode())) {
                            findActPg(productInfoVO, groupInfoForPcVo);
                        }
                    }
                }
            }
        }
    }

    public static ShopListProductVo findActPg(ShopListProductVo vo, FloorGroupInfoForPcVo actPgVo) {
        if (Objects.isNull(vo) || Objects.isNull(actPgVo)) {
            return vo;
        }
        /* 调整商品的showName */
        String productUnit = Optional.ofNullable(vo.getProductUnit()).orElse("");
        String showName = Optional.ofNullable(vo.getShowName()).orElse("");
        StringBuilder sbShowName = new StringBuilder();
        if (actPgVo.getSkuStartNum() != null) {
            sbShowName.append(actPgVo.getSkuStartNum())
                    .append(productUnit).append("包邮")
                    .append(" ").append(showName);
        } else {
            sbShowName.append(showName);
        }
        // 追加规格显示
        if (StringUtils.isNotEmpty(vo.getSpec()) && !Objects.equals(vo.getSpec(), Constants.LINE)) {
            sbShowName.append("/").append(vo.getSpec());
        }
        vo.setShowName(sbShowName.toString());
        /* 设置批购包邮活动信息 */
        return vo;
    }

    public ShopListProductVo findActPt(ShopListProductVo vo,FloorGroupInfoForPcVo actPtVo){
        if(Objects.isNull(actPtVo)) {
            return vo;
        }
            //拼团商品名称拼接
            String productUnit = Optional.ofNullable(vo.getProductUnit()).orElse("");
            String productSpec = Optional.ofNullable(vo.getSpec()).orElse("");
            StringBuilder sb = new StringBuilder();
            //校验商品属性 修改文案 拼团-》首推
            String tagName = "";
            if(Constants.IS1 == vo.getFirstChoose() && Constants.highGross.contains(vo.getHighGross())){
                tagName = "【首推优选】";
            } else if (StringUtils.isNotEmpty(actPtVo.getTopicPrefix())){
                tagName = actPtVo.getTopicPrefix();
            }
            sb.append(tagName).append(actPtVo.getSkuStartNum()).append(productUnit).append("包邮").append(" ")
                    .append(vo.getShowName()).append("/").append(productSpec);
            vo.setShowName(sb.toString());
            // 拼团多阶梯价信息
            String stepPricesJson = actPtVo.getStepPricesJson();
            if (StringUtils.isNotEmpty(stepPricesJson)) {
                List<GroupBuyingStepPriceDto> groupBuyingStepPriceDtos = JSONArray.parseArray(stepPricesJson, GroupBuyingStepPriceDto.class);
                List<String> stepPriceShowTexts = GroupBuyingInfoDto.generateStepPriceShowTexts(groupBuyingStepPriceDtos, vo.getProductUnit());
                actPtVo.setStepPriceShowTexts(stepPriceShowTexts);
            }
            vo.setActPt(actPtVo);
            vo.setPricePrefix("拼团价");

        return vo;
    }

    private void setFloorGoods(List<CompanyShopFloorVo> floorVOS, List<AppFloorDto> floorInfos, Map<Long, ListProduct> skuVOMap) {
        floorInfos.stream().forEach(popOrgFloorInfo -> {
            CompanyShopFloorVo companyShopFloorVO = CompanyHelper.parsePopFloor(popOrgFloorInfo);
            List<AppFloorGoodsDto> popOrgFloorSkuDetails = popOrgFloorInfo.getFloorGoodsPo();
            if(CollectionUtils.isEmpty(popOrgFloorSkuDetails)){
                companyShopFloorVO.setListProductVos(new ArrayList<>());
                companyShopFloorVO.setMore(Boolean.FALSE);
                floorVOS.add(companyShopFloorVO);
                return;
            }

            Set<Long> showSkuIds = skuVOMap.keySet();
            popOrgFloorSkuDetails = popOrgFloorSkuDetails.stream().filter(good -> showSkuIds.contains(good.getGoodsId().longValue())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(popOrgFloorSkuDetails)){
                companyShopFloorVO.setListProductVos(new ArrayList<>());
                companyShopFloorVO.setMore(Boolean.FALSE);
                floorVOS.add(companyShopFloorVO);
                return;
            }

            int length;
            int size = popOrgFloorSkuDetails.size();
            if(size > 10){
                length = 10;
                companyShopFloorVO.setMore(Boolean.TRUE);
            }else{
                length = size;
                companyShopFloorVO.setMore(Boolean.FALSE);
            }
            //pop楼层排序
            if(companyShopFloorVO.getFloorType() == FloorTypeEnum.POP_FLOOR.getCode()){
                popOrgFloorSkuDetails = popOrgFloorSkuDetails.stream().sorted(Comparator.comparingInt(AppFloorGoodsDto::getSort)
                        .thenComparingInt(AppFloorGoodsDto::getGoodsId)).collect(Collectors.toList());
            }

            List<ShopListProductVo> skuVOS = new ArrayList<>();
            for (int i = 0; i < length; i++) {
                AppFloorGoodsDto goodsDto = popOrgFloorSkuDetails.get(i);
                ListProduct skuVO = skuVOMap.get((Long) goodsDto.getGoodsId().longValue());
                if (skuVO != null) {
                    ShopListProductVo listProductVo = ShopIndexHelper.covertToVo(skuVO);
                    //拼团
                    if(popOrgFloorInfo.getFloorType() == FloorTypeEnum.GROUP_WORK.getCode()){
                        FloorGroupInfoForPcVo groupInfoForPcVo = goodsDto.getGroupInfoForPcVo();
                        if (Objects.nonNull(groupInfoForPcVo)) {
                            // 拼团多阶梯价信息
                            String stepPricesJson = groupInfoForPcVo.getStepPricesJson();
                            if (StringUtils.isNotEmpty(stepPricesJson)) {
                                List<GroupBuyingStepPriceDto> groupBuyingStepPriceDtos = JSONArray.parseArray(stepPricesJson, GroupBuyingStepPriceDto.class);
                                List<String> stepPriceShowTexts = GroupBuyingInfoDto.generateStepPriceShowTexts(groupBuyingStepPriceDtos, listProductVo.getProductUnit());
                                groupInfoForPcVo.setStepPriceShowTexts(stepPriceShowTexts);
                            }
                        }
                        listProductVo.setActPt(groupInfoForPcVo);
                    }


                    //批购包邮
                    if(popOrgFloorInfo.getFloorType() == FloorTypeEnum.PG_WORK.getCode()){
                        FloorGroupInfoForPcVo groupInfoForPcVo = goodsDto.getPgForPcVo();
                        if (Objects.nonNull(groupInfoForPcVo)) {
                            /* 调整商品的showName */
                            String productUnit = Optional.ofNullable(listProductVo.getProductUnit()).orElse("");
                            String showName = Optional.ofNullable(listProductVo.getShowName()).orElse("");
                            StringBuilder sbShowName = new StringBuilder();
                            if (groupInfoForPcVo.getSkuStartNum() != null) {
                                sbShowName.append(groupInfoForPcVo.getSkuStartNum())
                                        .append(productUnit).append("包邮")
                                        .append(" ").append(showName);
                            } else {
                                sbShowName.append(showName);
                            }
                            // 追加规格显示
                            if (StringUtils.isNotEmpty(listProductVo.getSpec()) && !Objects.equals(listProductVo.getSpec(), Constants.LINE)) {
                                sbShowName.append("/").append(listProductVo.getSpec());
                            }
                            listProductVo.setShowName(sbShowName.toString());
                            listProductVo.setActPgby(groupInfoForPcVo);
                        }
                    }

                    //添加受托生产厂家
                    if(StringUtils.isNotBlank(skuVO.getEntrustedManufacturer())){
                        listProductVo.setManufacturer(ProductMangeUtils.getManufacturer(skuVO.getMarketAuthor(),skuVO.getManufacturer(),skuVO.getEntrustedManufacturer()));
                    }

                    skuVOS.add(listProductVo);
                }
            }
            companyShopFloorVO.setListProductVos(skuVOS);
            floorVOS.add(companyShopFloorVO);
        });
    }

    private void getActivityFloor(String orgId, Long merchantId, List<AppFloorDto> floorInfos, Boolean isVirtual) {
        if(merchantId == null || StringUtils.isEmpty(orgId)){
            log.error("PopShopServiceImpl.shopIndexView获取特价商品入参orgId和merchantId不能为空：orgId={},merchantId={},isVirtual={}", orgId, merchantId, isVirtual);
            return;
        }

        //获取shopCode
        String shopCode = popCorporationServiceRpc.getShopCodeByOrgId(orgId);
        if(StringUtils.isEmpty(shopCode)){
            log.error("PopShopServiceImpl.shopIndexView获取特价商品入参shopCode为空: orgId={}", orgId);
            return;
        }

        Integer bizType = ProductOwnerBizTypeEnum.POP.getCode();
        if(isVirtual){
            bizType = ProductOwnerBizTypeEnum.VIRTUAL_SHOP.getCode();
        }

        List<MarketingEnum> extFloors = hyperSpaceRpc.getFloorList(merchantId, bizType, shopCode);
        log.info("PopShopServiceImpl.shopIndexView.getFloorList获取特价商品参数: orgId={},bizType={},bizCode={},result={}", orgId,
                bizType, shopCode, JSON.toJSONString(extFloors));
        if(CollectionUtils.isEmpty(extFloors)){
            log.info("PopShopServiceImpl.shopIndexView没有特价楼层和拼团楼层");
            return;
        }

        //需要按照顺序展示楼层
        MarketingEnum pintuanFloor = extFloors.stream().filter(floor -> MarketingEnum.PING_TUAN.getCode() == floor.getCode()).findFirst().orElse(null);
        if(Objects.nonNull(pintuanFloor)){
            if(BooleanUtils.isNotTrue(appProperties.isShopIndexGetSkuIdNewFlag())){
                floorInfos.add(getGroupFloorGoods(orgId, merchantId, shopCode, MarketingEnum.PING_TUAN,
                        FloorTypeEnum.GROUP_WORK.getCode()));
            }else {
                floorInfos.add(getFloorGoodsV2(orgId, merchantId, shopCode, MarketingEnum.PING_TUAN,
                        FloorTypeEnum.GROUP_WORK.getCode()));
            }
        }

        MarketingEnum piGouFloor = extFloors.stream().filter(floor -> MarketingEnum.PI_GOU_BAO_YOU.getCode() == floor.getCode()).findFirst().orElse(null);
        if(Objects.nonNull(piGouFloor)){
            if(BooleanUtils.isNotTrue(appProperties.isShopIndexGetSkuIdNewFlag())){
                floorInfos.add(getGroupFloorGoods(orgId, merchantId, shopCode, MarketingEnum.PI_GOU_BAO_YOU,
                        FloorTypeEnum.PG_WORK.getCode()));
            }else {
                floorInfos.add(getFloorGoodsV2(orgId, merchantId, shopCode, MarketingEnum.PI_GOU_BAO_YOU,
                        FloorTypeEnum.PG_WORK.getCode()));
            }
        }

        MarketingEnum specialFloor = extFloors.stream().filter(floor -> MarketingEnum.TE_JIA.getCode() == floor.getCode()).findFirst().orElse(null);
        if(Objects.nonNull(specialFloor)){
            if(BooleanUtils.isNotTrue(appProperties.isShopIndexGetSkuIdNewFlag())){
                floorInfos.add(getSpecialOfferFloorGoods(orgId, merchantId, shopCode, MarketingEnum.TE_JIA,
                        FloorTypeEnum.SPECIAL_OFFER.getCode()));
            }else {
                floorInfos.add(getFloorGoodsV2(orgId, merchantId, shopCode, MarketingEnum.TE_JIA,
                        FloorTypeEnum.SPECIAL_OFFER.getCode()));
            }

        }
    }

    private AppFloorDto getSpecialOfferFloorGoods(String orgId,Long merchantId, String shopCode, MarketingEnum marketingEnum, Integer floorType) {
        CsuOrder csuOrder = CsuOrder.DEFAULT;
        if (Objects.equals(marketingEnum, MarketingEnum.PING_TUAN)) {
            csuOrder = CsuOrder.PT_DEFAULT;
        } else if (Objects.equals(marketingEnum, MarketingEnum.MIAO_SHA)) {
            csuOrder = CsuOrder.SK_DEFAULT;
        }
        Page<MarketingCsuResultDto> ptResultDtoPage = marketingService.getMarketingShopIndex(0, 50, merchantId,
                shopCode, csuOrder, marketingEnum);
        log.info("PopShopServiceImpl.shopIndexView.getSpecialOfferFloorGoods# merchantId:{}, shopCode:{}, marketingEnum:{}, ptResultDtoPage:{}",
                merchantId, shopCode, marketingEnum, JSON.toJSONString(ptResultDtoPage));
        AppFloorDto floorDto = new AppFloorDto();
        floorDto.setFloorId(0);
        floorDto.setFloorName(marketingEnum.getTitle());
        floorDto.setFloorType(floorType);
        List<MarketingCsuResultDto> recordList = ptResultDtoPage.getRecordList();
        if(CollectionUtils.isEmpty(recordList)){
            return floorDto;
        }
        List<AppFloorGoodsDto> floorGoodsDtos = new ArrayList<>();
        for (MarketingCsuResultDto dto : recordList) {
            AppFloorGoodsDto goodsDto = new AppFloorGoodsDto();
            goodsDto.setOrgId(orgId);
            goodsDto.setFloorId(floorDto.getFloorId());
            goodsDto.setGoodsId(dto.getCsuId().intValue());
            //拼团 批购
            FloorGroupInfoForPcVo actPt = getGroupInfoForPcVo(dto.getGroupBuyingInfoDto());
            goodsDto.setGroupInfoForPcVo(actPt);

            FloorGroupInfoForPcVo piGou = getGroupInfoForPcVo(dto.getPiGouInfoDto());
            goodsDto.setPgForPcVo(piGou);
            floorGoodsDtos.add(goodsDto);
        }
        floorDto.setFloorGoodsPo(floorGoodsDtos);
        return floorDto;
    }


    private AppFloorDto getGroupFloorGoods(String orgId,Long merchantId, String shopCode, MarketingEnum marketingEnum, Integer floorType) {
        CsuOrder csuOrder = CsuOrder.DEFAULT;
        if (Objects.equals(marketingEnum, MarketingEnum.PING_TUAN)) {
            csuOrder = CsuOrder.PT_DEFAULT;
        } else if (Objects.equals(marketingEnum, MarketingEnum.MIAO_SHA)) {
            csuOrder = CsuOrder.SK_DEFAULT;
        }
        Page<MarketingCsuResultDto> ptResultDtoPage = marketingService.getMarketingShopIndex(0, 50, merchantId,
                shopCode, csuOrder, marketingEnum);
        log.info("PopShopServiceImpl.shopIndexView.getActivityFloorGoods# merchantId:{}, shopCode:{}, marketingEnum:{}, ptResultDtoPage:{}",
                merchantId, shopCode, marketingEnum, JSON.toJSONString(ptResultDtoPage));
        AppFloorDto floorDto = new AppFloorDto();
        floorDto.setFloorId(0);
        if (Objects.equals(marketingEnum, MarketingEnum.PI_GOU_BAO_YOU)) {
            floorDto.setFloorName("包邮");
        } else {
            floorDto.setFloorName(marketingEnum.getTitle());
        }
        floorDto.setFloorType(floorType);
        List<MarketingCsuResultDto> recordList = ptResultDtoPage.getRecordList();
        if(CollectionUtils.isEmpty(recordList)){
            return floorDto;
        }
        List<AppFloorGoodsDto> floorGoodsDtos = new ArrayList<>();
        for (MarketingCsuResultDto dto : recordList) {
            AppFloorGoodsDto goodsDto = new AppFloorGoodsDto();
            goodsDto.setOrgId(orgId);
            goodsDto.setFloorId(floorDto.getFloorId());
            goodsDto.setGoodsId(dto.getCsuId().intValue());
            //拼团
            FloorGroupInfoForPcVo actPt = getGroupInfoForPcVo(dto.getGroupBuyingInfoDto());
            goodsDto.setGroupInfoForPcVo(actPt);

            //批购
            FloorGroupInfoForPcVo piGou = getGroupInfoForPcVo(dto.getPiGouInfoDto());
            goodsDto.setPgForPcVo(piGou);
            floorGoodsDtos.add(goodsDto);
        }
        floorDto.setFloorGoodsPo(floorGoodsDtos);
        return floorDto;
    }

    private AppFloorDto getFloorGoodsV2(String orgId,Long merchantId, String shopCode, MarketingEnum marketingEnum, Integer floorType) {
        CsuOrder csuOrder = CsuOrder.DEFAULT;
        if (Objects.equals(marketingEnum, MarketingEnum.PING_TUAN)) {
            csuOrder = CsuOrder.PT_DEFAULT;
        } else if (Objects.equals(marketingEnum, MarketingEnum.MIAO_SHA)) {
            csuOrder = CsuOrder.SK_DEFAULT;
        }
        List<Long> skuIdList = marketingService.getSkuIdListForShopIndex(0, shopIndexSearchPageSize, merchantId,
                shopCode, csuOrder, marketingEnum);
        log.info("PopShopServiceImpl.shopIndexView.getActivityFloorGoods# merchantId:{}, shopCode:{}, marketingEnum:{}, skuIdList:{}",
                merchantId, shopCode, marketingEnum, JSON.toJSONString(skuIdList));
        AppFloorDto floorDto = new AppFloorDto();
        floorDto.setFloorId(0);
        if (Objects.equals(marketingEnum, MarketingEnum.PI_GOU_BAO_YOU)) {
            floorDto.setFloorName("包邮");
        } else {
            floorDto.setFloorName(marketingEnum.getTitle());
        }
        floorDto.setFloorType(floorType);
        if(CollectionUtils.isEmpty(skuIdList)){
            return floorDto;
        }
        if(skuIdList.size() > shopIndexActSkuLimit){
            skuIdList = skuIdList.subList(0, shopIndexActSkuLimit);
        }
        List<AppFloorGoodsDto> floorGoodsDtos = new ArrayList<>();
        for (Long skuId : skuIdList) {
            AppFloorGoodsDto goodsDto = new AppFloorGoodsDto();
            goodsDto.setOrgId(orgId);
            goodsDto.setFloorId(floorDto.getFloorId());
            goodsDto.setGoodsId(skuId.intValue());
            floorGoodsDtos.add(goodsDto);
        }
        floorDto.setFloorGoodsPo(floorGoodsDtos);
        return floorDto;
    }

    private FloorGroupInfoForPcVo getGroupInfoForPcVo( GroupBuyingInfoDto infoDto) {
        if(Objects.isNull(infoDto) || CollectionUtils.isEmpty(infoDto.getGroupBuyingSkuDtoList())){
            return null;
        }
        FloorGroupInfoForPcVo actPt = new FloorGroupInfoForPcVo();
        GroupBuyingSkuDto skuDto = infoDto.getGroupBuyingSkuDtoList().get(0);
        actPt.setGoodId(skuDto.getSkuId());
        actPt.setAssemblePrice(skuDto.getSkuPrice());
        actPt.setPercentage(infoDto.getPercentage().multiply(new BigDecimal(100).setScale( 0, BigDecimal.ROUND_UP )));
        actPt.setSkuStartNum(skuDto.getSkuStartNum());
        actPt.setOrderNum(infoDto.getOrderNum()==null?0:infoDto.getOrderNum().intValue());
        if(Objects.nonNull(infoDto.getStartTime())){
            actPt.setAssembleStartTime(infoDto.getStartTime());
        }
        if(Objects.nonNull(infoDto.getEndTime())){
            actPt.setAssembleEndTime(infoDto.getEndTime());
        }
        if(infoDto.getMarketingId() !=null){
            actPt.setMarketingId(infoDto.getMarketingId().intValue());
        }

        if (infoDto.getStartTime().getTime()>System.currentTimeMillis()){//未开始
            actPt.setAssembleStatus(0);
        }else  if (infoDto.getEndTime().getTime()<System.currentTimeMillis()){//结束
            actPt.setAssembleStatus(2);
        }else{
            actPt.setAssembleStatus(1);
        }
        actPt.setSurplusTime((infoDto.getEndTime().getTime()-System.currentTimeMillis())/1000);
        actPt.setPreheatShowPrice(infoDto.getPreheatShowPrice());
        actPt.setStepPriceStatus(infoDto.getStepPriceStatus());
        actPt.setMinSkuPrice(infoDto.getMinSkuPrice());
        actPt.setMaxSkuPrice(infoDto.getMaxSkuPrice());
        actPt.setStepPricesJson(JSONArray.toJSONString(infoDto.getStepPrices()));
        actPt.setStartingPriceShowText(infoDto.getStartingPriceShowText());
        actPt.setRangePriceShowText(infoDto.getRangePriceShowText());
        // 默认值，没有商品单位的多阶梯文本列表
        actPt.setStepPriceShowTexts(infoDto.getStepPriceShowTexts());
        actPt.setTopicPrefix(infoDto.getTopicPrefix());
        return actPt;
    }

    /**
     * pop商家店铺首页展示
     *
     * @param orgId
     * @param merchantId
     * @return
     */
    @Override
    public List<ShopListProductVo> shopGood(String orgId, Long merchantId, Integer floorId, Integer floorType, String branchCode) {
        List<ShopListProductVo> floorVOS = new ArrayList<>();
        List<AppFloorGoodsDto> appFloorGoodsDtos = null;
        if(floorType == FloorTypeEnum.POP_FLOOR.getCode()){//pop
            appFloorGoodsDtos = popCorporationServiceRpc.floorGoods(floorId);
            log.info("PopShopServiceImpl.shopGood.popCorporationServiceRpc.floorGoods# floorId:{}, appFloorGoodsDtos:{}",
                    floorId, JSON.toJSONString(appFloorGoodsDtos));
            if(CollectionUtils.isNotEmpty(appFloorGoodsDtos)){
                popGroupInfo(merchantId, appFloorGoodsDtos);
            }
        }else if(floorType == FloorTypeEnum.GROUP_WORK.getCode() || floorType == FloorTypeEnum.SPECIAL_OFFER.getCode() || floorType == FloorTypeEnum.PG_WORK.getCode()){//特价、拼团
            //获取shopCode
            String shopCode = popCorporationServiceRpc.getShopCodeByOrgId(orgId);
            if(StringUtils.isEmpty(shopCode)){
                log.error("PopShopServiceImpl.shopIndexView获取特价商品入参shopCode为空");
                return new ArrayList<>();
            }

            if(floorType == FloorTypeEnum.GROUP_WORK.getCode()){
                AppFloorDto floorDto = getSpecialOfferFloorGoods(orgId, merchantId, shopCode, MarketingEnum.PING_TUAN, floorType);
                appFloorGoodsDtos = floorDto.getFloorGoodsPo();
            }else if(floorType == FloorTypeEnum.SPECIAL_OFFER.getCode()){
                AppFloorDto floorDto = getGroupFloorGoods(orgId, merchantId, shopCode, MarketingEnum.TE_JIA, floorType);
                appFloorGoodsDtos = floorDto.getFloorGoodsPo();
            }else if(floorType == FloorTypeEnum.PG_WORK.getCode()){
                AppFloorDto floorDto = getGroupFloorGoods(orgId, merchantId, shopCode, MarketingEnum.PI_GOU_BAO_YOU, floorType);
                appFloorGoodsDtos = floorDto.getFloorGoodsPo();
            }
        }

        if(CollectionUtils.isEmpty(appFloorGoodsDtos)){
            return Lists.newArrayList();
        }

        List<Long> skuIds = appFloorGoodsDtos.stream().map(AppFloorGoodsDto::getGoodsId).map(Long::new).collect(Collectors.toList());
        //拼团使用
        Map<String, AppFloorGoodsDto> infoVoMap = appFloorGoodsDtos.stream().collect(Collectors.toMap(dto -> String.valueOf(dto.getGoodsId()), Function.identity(), (o, n) -> o));
        List<ListProduct> skuIdList = productApiRpc.findOnSaleProductBySkuIdList(skuIds, merchantId, branchCode);
        if(CollectionUtils.isEmpty(skuIdList)){
            return Lists.newArrayList();
        }

        ShopInfoDTO shopInfoDTO = shopServiceRpc.queryShopByOrgId(orgId);
        boolean isVirtual = shopInfoDTO ==null?false:ShopPatternEnum.VIRTUAL.getCode().equalsIgnoreCase(shopInfoDTO.getShopPatternCode());


        List<ShopListProductVo> productVoList = skuIdList.stream().filter(product -> filterProduct(product,isVirtual))
                .map(product -> getShopListProductVo(product, floorType, infoVoMap)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(productVoList)){
            return Lists.newArrayList();
        }
        //排序
        if(floorType == FloorTypeEnum.POP_FLOOR.getCode()){
            return productVoList.stream().sorted(Comparator.comparingInt(ShopListProductVo::getSort).thenComparingLong(ShopListProductVo::getId))
                    .collect(Collectors.toList());
        }
        return productVoList;
    }

    private void popGroupInfo(Long merchantId, List<AppFloorGoodsDto> appFloorGoodsDtos) {
        List<Long> skuIds = appFloorGoodsDtos.stream().map(AppFloorGoodsDto::getGoodsId).map(Long::new).collect(Collectors.toList());
        Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap = marketingService.getGroupBuyingInfoBySkuIdList(skuIds,
                Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()), merchantId);
        log.info("PopShopServiceImpl.popGroupInfo.getGroupBuyingInfoBySkuIdList# param skuIds:{}, merchantId:{}, csuIdToGroupBuyingInfoMap:{}",
                JSON.toJSONString(skuIds), merchantId, JSON.toJSONString(csuIdToGroupBuyingInfoMap));
        if(MapUtils.isEmpty(csuIdToGroupBuyingInfoMap)){
            return;
        }
        Map<String, FloorGroupInfoForPcVo> actPtMap = new HashMap<>();
        for (GroupBuyingInfoDto infoDto : csuIdToGroupBuyingInfoMap.values()) {
            FloorGroupInfoForPcVo infoForPcVo = getGroupInfoForPcVo(infoDto);
            if(Objects.nonNull(infoForPcVo)){
                actPtMap.put(String.valueOf(infoForPcVo.getGoodId()), infoForPcVo);
            }
        }
        for (AppFloorGoodsDto goodsDto : appFloorGoodsDtos) {
            FloorGroupInfoForPcVo forPcVo = actPtMap.get(String.valueOf(goodsDto.getGoodsId()));
            if(Objects.nonNull(forPcVo)){
                goodsDto.setGroupInfoForPcVo(forPcVo);
            }
        }
    }

    public ShopListProductVo getShopListProductVo(ListProduct listProduct, Integer floorType,Map<String, AppFloorGoodsDto> infoVoMap){
        ShopListProductVo listProductVo = ShopIndexHelper.covertToVo(listProduct);
        if(floorType == FloorTypeEnum.POP_FLOOR.getCode()){
            AppFloorGoodsDto goodsDto = infoVoMap.get(String.valueOf(listProductVo.getId()));
            if(Objects.isNull(goodsDto) || Objects.isNull(goodsDto.getSort())){
                listProductVo.setSort(0);
            }else{
                listProductVo.setSort(goodsDto.getSort());
            }
        }

        if(floorType == FloorTypeEnum.GROUP_WORK.getCode()){
            AppFloorGoodsDto floorGoodsDto = infoVoMap.get(String.valueOf(listProduct.getId()));
            FloorGroupInfoForPcVo groupInfoForPcVo = floorGoodsDto.getGroupInfoForPcVo();
            if(Objects.nonNull(groupInfoForPcVo)){
                //拼团商品名称拼接
                StringBuilder sb = new StringBuilder();
                String productUnit = Optional.ofNullable(listProductVo.getProductUnit()).orElse("");
                String productSpec = Optional.ofNullable(listProductVo.getSpec()).orElse("");
                String skuNamePrefix = "";
                if (StringUtils.isNotEmpty(groupInfoForPcVo.getTopicPrefix())){
                    skuNamePrefix = groupInfoForPcVo.getTopicPrefix();
                }
                sb.append(skuNamePrefix).append(groupInfoForPcVo.getSkuStartNum()).append(productUnit).append("包邮").append(" ")
                        .append(listProductVo.getShowName()).append("/").append(productSpec);
                listProductVo.setShowName(sb.toString());
                // 拼团多阶梯价信息
                String stepPricesJson = groupInfoForPcVo.getStepPricesJson();
                if (StringUtils.isNotEmpty(stepPricesJson)) {
                    List<GroupBuyingStepPriceDto> groupBuyingStepPriceDtos = JSONArray.parseArray(stepPricesJson, GroupBuyingStepPriceDto.class);
                    List<String> stepPriceShowTexts = GroupBuyingInfoDto.generateStepPriceShowTexts(groupBuyingStepPriceDtos, listProductVo.getProductUnit());
                    groupInfoForPcVo.setStepPriceShowTexts(stepPriceShowTexts);
                }
                listProductVo.setActPt(groupInfoForPcVo);
                listProductVo.setPricePrefix("拼团价");
            }
        }

        if(floorType == FloorTypeEnum.PG_WORK.getCode()){
            AppFloorGoodsDto floorGoodsDto = infoVoMap.get(String.valueOf(listProduct.getId()));
            FloorGroupInfoForPcVo groupInfoForPcVo = floorGoodsDto.getPgForPcVo();
            if(Objects.nonNull(groupInfoForPcVo)){

                /* 调整商品的showName */
                String productUnit = Optional.ofNullable(listProductVo.getProductUnit()).orElse("");
                String showName = Optional.ofNullable(listProductVo.getShowName()).orElse("");
                StringBuilder sbShowName = new StringBuilder();
                if (groupInfoForPcVo.getSkuStartNum() != null) {
                    sbShowName.append(groupInfoForPcVo.getSkuStartNum())
                            .append(productUnit).append("包邮")
                            .append(" ").append(showName);
                } else {
                    sbShowName.append(showName);
                }
                // 追加规格显示
                if (StringUtils.isNotEmpty(listProductVo.getSpec()) && !Objects.equals(listProductVo.getSpec(), Constants.LINE)) {
                    sbShowName.append("/").append(listProductVo.getSpec());
                }
                listProductVo.setShowName(sbShowName.toString());
                listProductVo.setActPgby(groupInfoForPcVo);
            }
        }
        
        return listProductVo;
    }

    public Boolean filterProduct(ListProduct product, boolean isVirtual){
        //只展示特惠中、销售中、售罄。
        Integer status = product.getStatus();
        Boolean boolProductStatus = (status == ProductEnumDTO.SkuStatusEnum.ONSALE.getId() ||
                status == ProductEnumDTO.SkuStatusEnum.PRICEOFF.getId() ||
                status == ProductEnumDTO.SkuStatusEnum.SOLDOUT.getId());

        //控销： /** 0:白名单1:可见不可买2:不可见*/
        Integer purchaseType = product.getPurchaseType();
        Boolean boolPurchaseType = (purchaseType != 2);

        //只展示特惠中、销售中、售罄非控销的品
        if(boolProductStatus && boolPurchaseType){
            return true;
        }

        //虚拟供应商不用判断是否是虚拟商品
        if(isVirtual){
            return true;
        }
        //过了虚拟供应商商品
        if(!product.getIsVirtualSupplier()){
            return true;
        }

        return false;
    }

    @Override
    public CompanyDetailVo getPopCompanyDetail(String orgId, String branchCode) {
        Future<PopCorporationDto> corporationDtoFuture = CompletableFuture.supplyAsync(() -> popCorporationServiceRpc.getPopCompanyInfo(orgId));
        Future<ShippingDto> freightDtoFuture = CompletableFuture.supplyAsync(() -> popCorporationServiceRpc.getShippingInfo(orgId,branchCode));
        PopCorporationDto corporationDto = getPopCompanyInfoByFuture(corporationDtoFuture);

        CompanyDetailVo  companyDetailVo =  CompanyHelper.parsePopCompanyInfo(corporationDto);
        if(companyDetailVo==null){
            return null;
        }

        companyDetailVo.setWebUrl(String.format("%s%s",SHOP_INDEX_URL,orgId));
        if (StringUtils.isEmpty(companyDetailVo.getOrgLogo())) {
            companyDetailVo.setOrgLogo(ORG_DEFAULT_LOGO);
        }

        boolean isVirtual = isVirtual(corporationDto.getShopCode());
        //虚拟供应商特殊逻辑
        if (isVirtual) {
            log.info("#虚拟供应商返回,orgId:{}#",orgId);
            companyDetailVo.setUpSkuNum(9999l);
            companyDetailVo.setSaleSkuNum(9999l);
            companyDetailVo.setFreightInfo("");
            return companyDetailVo;
        }

        //查询统计信息
        Long upSkuNum = 0L;
        if (corporationDto != null) {
            List<ShopSkuCountDTO> skuCountDTOList = productApiRpc.getCompanySkuCount(Collections.singletonList(corporationDto.getShopCode()));
            if (CollectionUtils.isNotEmpty(skuCountDTOList)) {
                ShopSkuCountDTO countDTO = skuCountDTOList.get(0);
                upSkuNum=countDTO!=null?countDTO.getSkuNum():0L;
            }
        }

        ShippingDto shippingDto = getPopShippingInfoByFuture(freightDtoFuture);

        //调用店铺查询销量
        Long sumSaleNum = orderBusinessApiRpc.countSaleNumByShopCode(corporationDto.getShopCode());

        companyDetailVo.setUpSkuNum(upSkuNum);
        companyDetailVo.setSaleSkuNum(sumSaleNum);

        if(null!=shippingDto&&null!=shippingDto.getTemplateType()){
            if(shippingDto.getTemplateType()==1){
                companyDetailVo.setFreightInfo(CompanyHelper.getCompanyFreightInfo(shippingDto));
            }else{
                companyDetailVo.setFreightInfo("商家承担运费 ");
            }
        }

        return companyDetailVo;
    }

    private boolean isVirtual(String shopCode){
        //查询店铺信息
        ShopInfoDTO shopInfoDTO = shopServiceRpc.queryShopByShopCode(shopCode);
        if(null == shopInfoDTO){
            return false;
        }
        //判断是否是虚拟店铺
        return ShopPatternEnum.VIRTUAL.getCode().equalsIgnoreCase(shopInfoDTO.getShopPatternCode());
    }

    @Override
    public PageInfo<CompanyBranchBusinessDto> pageQueryCompany(Long merchantId, String orgName, int pageNum, int pageSize) {
        PageInfo<CompanyBranchBusinessDto> pageResult = new PageInfo<>();
        List<String> orgIdList =  orderBusinessApi.findOrgIdByMerchantId(merchantId);

        if(CollectionUtils.isEmpty(orgIdList)){
            log.info("没有第三方商家");
            return pageResult;
        }

        PageInfo<CorporationBaseInfoDto> baseInfoDtoPageInfo =popCorporationServiceRpc.selectCorporationsByOrgIdsAndName(orgIdList,orgName,pageNum,pageSize);
        if (baseInfoDtoPageInfo == null) {
            log.info("没有第三方商家");
            return pageResult;
        }

        pageResult.setPageNum(baseInfoDtoPageInfo.getPageNum());
        pageResult.setPageSize(baseInfoDtoPageInfo.getPageSize());
        pageResult.setTotal(baseInfoDtoPageInfo.getTotal());
        pageResult.setPages(baseInfoDtoPageInfo.getPages());
        pageResult.setList(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(baseInfoDtoPageInfo.getList())) {
            baseInfoDtoPageInfo.getList().stream().forEach(companyBranchCoreDto1 -> {
                CompanyBranchBusinessDto companyBranchBusinessDto = new CompanyBranchBusinessDto();
                BeanUtils.copyProperties(companyBranchCoreDto1, companyBranchBusinessDto);
                companyBranchBusinessDto.setCompanyName(companyBranchCoreDto1.getName());
                pageResult.getList().add(companyBranchBusinessDto);
            });
        }
        return pageResult;
    }

    private PopCorporationDto getPopCompanyInfoByFuture(Future<PopCorporationDto> future){
        try {
            return future.get();
        }
        catch (InterruptedException e) {
            log.error("pop商家信息查询", e);
        }
        catch (ExecutionException e) {
            log.error("pop商家信息查询", e);
        }
        return null;
    }
    private ShippingDto getPopShippingInfoByFuture(Future<ShippingDto> future){
        try {
            return future.get();
        }
        catch (InterruptedException e) {
            log.error("pop商家运费模板信息查询", e);
        }
        catch (ExecutionException e) {
            log.error("pop商家运费模板信息查询", e);
        }
        return null;
    }
    private List<AppFloorDto> getPopFloorInfoByFuture(Future<List<AppFloorDto>>  future){
        try {
            return future.get();
        }
        catch (InterruptedException e) {
            log.error("pop商家楼层信息查询", e);
        }
        catch (ExecutionException e) {
            log.error("pop商家楼层信息查询", e);
        }
        return null;
    }
    private List<PopAdvertisementDto> getPopAdvertiseByFuture(Future<List<PopAdvertisementDto>>  future){
        try {
            return future.get();
        }
        catch (InterruptedException e) {
            log.error("pop店铺广告查询", e);
        }
        catch (ExecutionException e) {
            log.error("pop店铺广告查询", e);
        }
        return null;
    }
    @Value("#{'${ec.pop.shop.qualification.show.code.names:第二类医疗器械经营备案凭证,医疗器械经营许可证,医疗器械网络销售备案凭证,第一类医疗器械生产备案凭证,医疗器械生产许可证}'.split(',')}")
    private List<String> qualificationShowCodeNames;
    @Override
    public List<ShopQualificationVO> getCorporationQualification(String orgId) {
        List<CorporationQualificationDto> shopQualification = popCorporationServiceRpc.getShopQualification(orgId);
        if (CollectionUtils.isEmpty(shopQualification)){
            return Lists.newArrayList();
        }
        return shopQualification.stream().map(item->ShopIndexHelper.covertQualificationToVo(item,qualificationShowCodeNames)).collect(Collectors.toList());
    }

    @Override
    public ShopOpenAccountProcessVo getClientAccountInfoByOrgId(String orgId) {
        Map<String, Object> map = new HashMap<>();
        ShopOpenAccountProcessVo shopOpenAccountProcessVo = new ShopOpenAccountProcessVo();
        PopCorporationDto popCompanyInfo = popCorporationServiceRpc.getPopCompanyInfo(orgId);
        log.info("#PopShopServiceImpl.getClientAccountInfoByOrgId#info,参数：orgId:{}, popCompanyInfo:{}", orgId, JSON.toJSONString(popCompanyInfo));
        if (popCompanyInfo == null){
            return shopOpenAccountProcessVo;
        }
        AboutUsVo aboutUsVo = CompanyHelper.covertCompanyToVo(popCompanyInfo);
        Set<String> specifyPopShopLicenseOrgIds = appProperties.getSpecifyPopShopLicenseOrgIds();
        boolean isSpecifyPopShop = CollectionUtils.isNotEmpty(specifyPopShopLicenseOrgIds) && specifyPopShopLicenseOrgIds.contains(orgId);
        shopOpenAccountProcessVo.setAboutUsVo(aboutUsVo);
        List<CorporationBusinessDto> corporationBusinessDto = popCorporationServiceRpc.getCorporationBusinessDto(orgId);
        if (CollectionUtils.isNotEmpty(corporationBusinessDto)){
            String collect = corporationBusinessDto.stream().map(CorporationBusinessDto::getName).collect(Collectors.joining("；"));
            aboutUsVo.setBusinessScope(collect);
            shopOpenAccountProcessVo.setAboutUsVo(aboutUsVo);
        }
        if (isSpecifyPopShop && Objects.nonNull(shopOpenAccountProcessVo.getAboutUsVo())) {
            shopOpenAccountProcessVo.getAboutUsVo().setCompanyName(desensitizeCorporationName(shopOpenAccountProcessVo.getAboutUsVo().getCompanyName()));
        }

        ClientAccountInfoExDto accountInfoExDto = clientAccountServerRpc.getClientAccountInfoByOrgId(orgId);
        log.info("#PopShopServiceImpl.getClientAccountInfoByOrgId#info,参数：orgId:{}, accountInfoExDto:{}", orgId, JSON.toJSONString(accountInfoExDto));
        if(accountInfoExDto == null){
            return shopOpenAccountProcessVo;
        }
        OpenAccountVo accountInfoVo = new OpenAccountVo();
        accountInfoVo.setId(accountInfoExDto.getId());
        if(NumberUtils.INTEGER_ONE.equals(accountInfoExDto.getElectronicType())){
            accountInfoVo.setElectronicType(accountInfoExDto.getElectronicType());
            accountInfoVo.setElectronicName(AccountWayEnum.ELECTRONIC.getName());
            accountInfoVo.setElectronicExplain(AccountWayEnum.ELECTRONIC.getDesc());
        }

        if(NumberUtils.INTEGER_ONE.equals(accountInfoExDto.getPagerType())){
            accountInfoVo.setPagerName(AccountWayEnum.PAGER.getName());
            accountInfoVo.setPagerType(accountInfoExDto.getPagerType());
            accountInfoVo.setPagerExplain(AccountWayEnum.PAGER.getDesc());
            accountInfoVo.setReceiver(accountInfoExDto.getReceiver());
            accountInfoVo.setPhone(accountInfoExDto.getPhone());
            String addressSb = Optional.ofNullable(accountInfoExDto.getProvinceName()).orElse("") + Optional.ofNullable(accountInfoExDto.getCityName()).orElse("") +
                    Optional.ofNullable(accountInfoExDto.getAreaName()).orElse("") + Optional.ofNullable(accountInfoExDto.getAddress()).orElse("");
            accountInfoVo.setAddress(addressSb);
            accountInfoVo.setRemark(accountInfoExDto.getRemark());
        }
        ClientExplanationDto clientExplanation = accountInfoExDto.getClientExplanation();
        if(Objects.nonNull(clientExplanation)){
            accountInfoVo.setExplanationContent(clientExplanation.getExplanationContent().replace("\n", "<br />").replace("*", "</i><span style=\"color: red\" class=\"xing\">*</span>"));
        }
        shopOpenAccountProcessVo.setOpenAccountVo(accountInfoVo);

        List<ClientResourcesDto> resourcesDtoList = accountInfoExDto.getClientResourcesList();
        if(CollectionUtils.isEmpty(resourcesDtoList)){
            return shopOpenAccountProcessVo;
        }

        List<AccountResourceVo> clientResourcesVos =  ShopIndexHelper.clientResourcesDtosToVos(resourcesDtoList);
        shopOpenAccountProcessVo.setResourceVos(clientResourcesVos);
        return shopOpenAccountProcessVo;
    }

    /**
     * 对企业名称进行脱敏
     *
     * @param corporationName
     * @return
     */
    private String desensitizeCorporationName(String corporationName) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(corporationName)) {
            return corporationName;
        }
        if (corporationName.length() <= 4) {
            return corporationName;
        }
        corporationName = corporationName.replaceAll("(.{2}).*(.{2})", "$1****$2");
        return corporationName;
    }

    @Override
    public String getReturnNoticeByOrgId(String orgId) throws Exception {
        return popCorporationServiceRpc.getReturnNoticeByOrgId(orgId);
    }

    @Override
    public String compressionCorporationQualification(String orgId, Long id) {
        return popCorporationServiceRpc.compressionCorporationQualification(orgId, id);
    }

    @Override
    public ShopGoodsVo shopGoodV2(String orgId, Long merchantId, Integer floorId, Integer floorType, String branchCode) {
        List<AppFloorGoodsDto> appFloorGoodsDtos = null;
        String shopCode = popCorporationServiceRpc.getShopCodeByOrgId(orgId);

        if(floorType == FloorTypeEnum.POP_FLOOR.getCode()){//pop
            appFloorGoodsDtos = popCorporationServiceRpc.floorGoods(floorId);
            log.info("PopShopServiceImpl.shopGood.popCorporationServiceRpc.floorGoods# floorId:{}, appFloorGoodsDtos:{}",
                    floorId, JSON.toJSONString(appFloorGoodsDtos));
            if(CollectionUtils.isNotEmpty(appFloorGoodsDtos)){
                popGroupInfo(merchantId, appFloorGoodsDtos);
            }
        }else if(floorType == FloorTypeEnum.GROUP_WORK.getCode() || floorType == FloorTypeEnum.SPECIAL_OFFER.getCode() || floorType == FloorTypeEnum.PG_WORK.getCode()){//特价、拼团
            //获取shopCode
            if(StringUtils.isEmpty(shopCode)){
                log.error("PopShopServiceImpl.shopIndexView获取特价商品入参shopCode为空");
                return ShopGoodsVo.builder().shopListProductVos(Lists.newArrayList()).build();
            }

            if(floorType == FloorTypeEnum.GROUP_WORK.getCode()){
                AppFloorDto floorDto = getSpecialOfferFloorGoods(orgId, merchantId, shopCode, MarketingEnum.PING_TUAN, floorType);
                appFloorGoodsDtos = floorDto.getFloorGoodsPo();
            }else if(floorType == FloorTypeEnum.SPECIAL_OFFER.getCode()){
                AppFloorDto floorDto = getGroupFloorGoods(orgId, merchantId, shopCode, MarketingEnum.TE_JIA, floorType);
                appFloorGoodsDtos = floorDto.getFloorGoodsPo();
            }else if(floorType == FloorTypeEnum.PG_WORK.getCode()){
                AppFloorDto floorDto = getGroupFloorGoods(orgId, merchantId, shopCode, MarketingEnum.PI_GOU_BAO_YOU, floorType);
                appFloorGoodsDtos = floorDto.getFloorGoodsPo();
            }
        }
        if(CollectionUtils.isEmpty(appFloorGoodsDtos)){
            return ShopGoodsVo.builder().shopListProductVos(Lists.newArrayList()).shopCode(shopCode).build();
        }

        List<Long> skuIds = appFloorGoodsDtos.stream().map(AppFloorGoodsDto::getGoodsId).map(Long::new).collect(Collectors.toList());
        //拼团使用
        Map<String, AppFloorGoodsDto> infoVoMap = appFloorGoodsDtos.stream().collect(Collectors.toMap(dto -> String.valueOf(dto.getGoodsId()), Function.identity(), (o, n) -> o));
        List<ListProduct> skuIdList = productApiRpc.findOnSaleProductBySkuIdList(skuIds, merchantId, branchCode);
        if(CollectionUtils.isEmpty(skuIdList)){
            return ShopGoodsVo.builder().shopListProductVos(Lists.newArrayList()).shopCode(shopCode).build();
        }
        ShopInfoDTO shopInfoDTO = shopServiceRpc.queryShopByOrgId(orgId);

        boolean isVirtual = shopInfoDTO != null && ShopPatternEnum.VIRTUAL.getCode().equalsIgnoreCase(shopInfoDTO.getShopPatternCode());

        List<ShopListProductVo> productVoList = skuIdList.stream().filter(product -> filterProduct(product,isVirtual))
                .map(product -> getShopListProductVo(product, floorType, infoVoMap)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(productVoList)){
            return ShopGoodsVo.builder().shopListProductVos(Lists.newArrayList()).shopCode(shopCode).build();
        }
        //排序
        if(floorType == FloorTypeEnum.POP_FLOOR.getCode()){
            List<ShopListProductVo> collected = productVoList.stream().sorted(Comparator.comparingInt(ShopListProductVo::getSort).thenComparingLong(ShopListProductVo::getId))
                    .collect(Collectors.toList());
            fillGoodsScmId(collected);
            return ShopGoodsVo.builder().shopListProductVos(collected).shopCode(shopCode).build();
        }
        fillGoodsScmId(productVoList);
        return ShopGoodsVo.builder().shopListProductVos(productVoList).shopCode(shopCode).build();
    }

    private void fillGoodsScmId(List<ShopListProductVo> floorVOS) {
        String alphanumeric = RandomStringUtils.randomAlphanumeric(8);
        floorVOS.forEach(product -> {
            product.setScmId(alphanumeric);
        });
    }

    private List<Long> getFloorSkuIds(List<AppFloorDto> floorInfos, FloorTypeEnum floorType) {
        List<Long> skuIds = floorInfos.stream().filter(floorInfo -> floorInfo.getFloorType().equals(floorType.getCode()))
                .map(AppFloorDto::getFloorGoodsPo).filter(Objects::nonNull)
                .flatMap(List::stream).map(AppFloorGoodsDto::getGoodsId).map(Long::new).collect(Collectors.toList());

        return skuIds;
    }

    private void setFloorActGoods(List<AppFloorDto> floorInfos, Map<Long, ListProduct> skuVOMap, Long merchantId) {
        //拼团   在原来基础上改造
        List<Long> ptSkuIds = getFloorSkuIds(floorInfos, FloorTypeEnum.GROUP_WORK);
        Map<Long, GroupBuyingInfoDto> ptActMap = marketingService.getGroupBuyingInfo(ptSkuIds, merchantId);
        if(log.isDebugEnabled()){
            log.debug("setFloorActGoods  ptActMap:{}", JSONObject.toJSONString(ptActMap));
        }

        //批购包邮   在原来基础上改造
        List<Long> pgSkuIds = getFloorSkuIds(floorInfos, FloorTypeEnum.PG_WORK);
        Map<Long, GroupBuyingInfoDto> pgActMap = marketingService.getPiGouInfo(pgSkuIds, merchantId);
        if(log.isDebugEnabled()){
            log.debug("setFloorActGoods  pgActMap:{}", JSONObject.toJSONString(pgActMap));
        }
//
//        //特价   在原来基础上改造
//        List<Long> tjSkuIds = getFloorSkuIds(floorInfos, FloorTypeEnum.SPECIAL_OFFER);
//        Map<Long, MarketingSeckillActivityInfoDTO>  tjActMap = marketingService.getCsuMarketingSeckillActivityInfoForShopFloor(merchantId, tjSkuIds);

        for(AppFloorDto floorInfo : floorInfos){
            if(log.isDebugEnabled()){
                log.debug("setFloorActGoods  floorInfo :{}", JSONObject.toJSONString(floorInfo));
            }
            if(!floorInfo.getFloorType().equals(FloorTypeEnum.GROUP_WORK.getCode())
                    && !floorInfo.getFloorType().equals(FloorTypeEnum.PG_WORK.getCode())){
                continue;
            }
            List<AppFloorGoodsDto> floorGoods = floorInfo.getFloorGoodsPo();
            if(CollectionUtils.isEmpty(floorGoods)){
                continue;
            }
            for(AppFloorGoodsDto floorGood : floorGoods){
                if(null == floorGood.getGoodsId()){
                    continue;
                }
                Long skuId = floorGood.getGoodsId().longValue();
                //拼团
                FloorGroupInfoForPcVo actPt = getGroupInfoForPcVo(ptActMap.get(skuId));
                floorGood.setGroupInfoForPcVo(actPt);

                //批购
                FloorGroupInfoForPcVo piGou = getGroupInfoForPcVo(pgActMap.get(skuId));
                floorGood.setPgForPcVo(piGou);
            }
        }
    }
}
