$(function () {
  var urlParams = new URLSearchParams(window.location.search);
  var code = urlParams.get("code");

  var openid = "";
  var access_token = "";
  var unionid = ""
  var checkTime = localStorage.getItem("checkTime");
  var agreements = localStorage.getItem("agreements");
  if (code) {
    //二维码接口
    $.ajax({
      url: "/login/wechat/accessToken",
      type: "POST",
      dataType: "json",
      data: {
        code: code,
      },
      success: function (res) {
        console.log(res);
        if (res.code == 1000) {
          access_token = res.data.access_token;
          openid = res.data.openid;
          unionid = res.data.unionid;
          var url = "/";
          console.log("checkTime" + checkTime, "agreements" + agreements);
            //登录
          $.ajax({
            url: "/login/wechat/login",
            type: "POST",
            dataType: "json",
            data: {
              openid: openid,
              unionid: unionid,
              accessToken: access_token,
              redirectUrl: url,
              agreements: agreements,
              checkTime: checkTime,
            },
            success: function (res) {
              if (res.code == 1000) {
                if (res.data.status) {
                  window.location.href = res.data.redirectUrl;
                } else {
                  $(".container").css("display", "block");
                }
              } else {
                $.alert(res.errorMsg);
              }
            },
          });
        } else {
          $.alert({
            title: "提示",
            body: res.errorMsg,
          });
        }
      },
      error: function () {
        $.alert({
          title: "提示",
          body: "网络异常，请稍后再试！",
        });
      },
    });
  } else {
    console.error("未获取到 code");
  }

  /*发送验证码*/
  $(".yzmbox").click(function () {
    var mobile = $("#mobile").val();
    if (mobile == null || mobile == "") {
      $(".err-box").css("display", "block");
      $(".wenantishi").text("手机号不能为空");
      return;
    }
    if (!/^1\d{10}$/.test(mobile)) {
      $(".err-box").css("display", "block");
      $(".wenantishi").text("请填写正确的手机号码！");
      return false;
    }
    var shuju = {
      //photoCode:photoCode,
      phone: mobile,
    };

    $.ajax({
      url: "/login/wechat/sendBindValidCode",
      data: shuju,
      type: "POST",
      dataType: "JSON",
      success: function (data) {
        if (data.status == "success") {
          if (data.data.status) {
            $(this).css("display", "none");
            $(".yzmbox-repe").css("display", "block");
            var initData = $(".datasub").text();
            var storeData = initData;
            var sh = setInterval(function () {
              if (initData > 0) {
                $(".datasub").text(--initData);
              } else {
                clearInterval(sh);
                $(".yzmbox").css("display", "block");
                $(".yzmbox-repe").css("display", "none");
                initData = storeData;
                $(".datasub").text(initData);
              }
            }, 1000);
            console.log(initData);
          } else {
            $.confirm({
                body: "手机号未注册账号",
                okBtn: "去注册",
                cancelBtn: "取消",
                okHidden: function () {
                    location.href = `/newstatic/#/register/index?openId=${openid}&accessToken=${access_token}`;
                },
            });
          }
        } else {
          $.alert(data.errorMsg);
        }
      },
    });
  });

  $(".login-nav .p-lbox .title").text("绑定帐号");

  $("#regNext").click(function () {
     console.log('确定按钮')
      var mobile = $("#mobile").val();
      var code = $("#code").val();

      if (mobile == null || mobile == "") {
        $(".err-box").css("display", "block");
        $(".wenantishi").text("手机号不能为空！");
        return;
      }
      if (!/^1\d{10}$/.test(mobile)) {
        $('#mobileIdentify').show();
        return false;
      }
      if (code == null || code == "") {
        $(".code-box").css("display", "block");
        $(".codetishi").text("验证码不能为空！");
        return;
      }
      //绑定接口
      $.ajax({
        url: "/login/wechat/bind",
        type: "POST",
        dataType: "json",
        data: {
          phone: mobile,
          code: code,
          openid: openid,
          accessToken: access_token,
        },
        success: function (res) {
          if (res.code == 1000) {
            if (res.data.status) {
              $.alert(res.data.msg);
              $.ajax({
                    url: "/login/wechat/login",
                    type: "POST",
                    dataType: "json",
                    data: {
                    openid: openid,
                    unionid: unionid,
                    accessToken: access_token,
                    redirectUrl: '/',
                    agreements: agreements,
                    checkTime: checkTime,
                    },
                    success: function (res) {
                    if (res.code == 1000) {
                        if (res.data.status) {
                        window.location.href = res.data.redirectUrl;
                        } else {
                        $(".container").css("display", "block");
                        }
                    } else {
                        $.alert(res.errorMsg);
                    }
                    },
                });
            } else {
              $.confirm({
                body: "手机号未注册账号",
                cancelBtn: "取消",
                okBtn: "去注册",
                okHidden: function () {
                  location.href = `/newstatic/#/register/index?openId=${openid}&accessToken=${access_token}`;
                },
              });
            }
          } else {
            $.alert(res.errorMsg);
          }
        },
      });
  });
});
function hiddenCode() {
  $("#yzcode").addClass("noshow");
}

function hiddenRealName() {
  $("#realNameErrorMsg").addClass("noshow");
}
