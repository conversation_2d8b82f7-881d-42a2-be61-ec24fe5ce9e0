package com.xyy.ec.pc.constants;

import java.util.HashMap;
import java.util.Map;

public enum MatchErrorEnum {
    TYPE_1(1,"客户经营范围不包括{}"),
    TYPE_2(2,"商品库存不足"),
    TYPE_3(3,"商品限购"),
    TYPE_4(4,"请签署协议"),
    TYPE_5(5,"易碎品不可买"),
    TYPE_6(6,"商品缺货/下架/控销/超经营范围"),
    TYPE_7(7,"商品下架"),
    TYPE_8(8,"匹配失败"),
    TYPE_9(9,"特殊管理药品需单独下单"),
    TYPE_10(10,"限购不可买"),
    TYPE_11(11,"连锁指导价为空"),
    TYPE_12(12,"效期不满足"),
    TYPE_13(13,"控销不可买"),
    TYPE_14(14,"匹配到重复商品，已将其合并到同一商品中（见序号行：%s）");


    MatchErrorEnum(Integer type, String detail) {
        this.type = type;
        this.detail = detail;
    }

    private Integer type;
    private String detail;

    private static final Map<Integer, MatchErrorEnum> map = new HashMap<>();

    static {
        for (MatchErrorEnum matchErrorEnum : MatchErrorEnum.values()) {
            map.put(matchErrorEnum.type, matchErrorEnum);
        }
    }

    public static MatchErrorEnum valueOf(Integer type) {
        return map.get(type);
    }

    public Integer getType() {
        return type;
    }

    public String getDetail() {
        return detail;
    }
}
