/*主体部分*/
.checkbox-pretty span:before, .radio-pretty span:before{font-size: 170%;font-size: 80%\0; position: relative;top:2px;}
.checkbox-pretty.checked>span:before, .radio-pretty.checked>span:before{color: #333333;}
.checkbox-pretty:hover span:before, .radio-pretty:hover span:before{color: #333333;}

.radio-pretty span:before{font-size: 170%;position: relative;top:1px;}
.radio-pretty.checked>span:before{color: #00dc82 ;}
.radio-pretty:hover span:before{color: #00dc82;}

div.footer div.foot_top{background-color: #f2f2f2;padding-top: 57px;}
div.footer .footboot{background: #f2f2f2;}
/*屏蔽搜索栏和商品分类*/
.searcherBox{display: none;}
.goodsmain{display: none;}

/*结算页头部导航*/
.topbzbox{height: 78px;line-height: 78px;background-color: #ffffff;border-bottom: 1px solid #e6e6e6;}
.topbzbox .warp{width: 1200px;margin: 0 auto;overflow: hidden;}
.topbzbox .bzcol1{float: left;}
.topbzbox .bzcol1 img{width: 160px;height: 60px;}
.topbzbox .bzcol2{float: left;width: 0px;height: 20px;border: 1px solid #ededed;margin-top: 34px;margin-left: 7px;margin-right: 7px;}
.topbzbox .bzcol3{float: left;font-size: 21px;color: #333333;margin-top: 5px;}
.topbzbox .bzcol4{float: right;}
.topbzbox .bzcol4 img{width: 753px;height: 28px;}



/*收货人信息*/
.recebox{border-bottom: 1px dashed #e0e0e0;}
.recebox .topbox{padding: 15px 20px;overflow: hidden;border-bottom: 1px solid #eee;}
.recebox .topbox span{display: inline-block;float: left;color: #333;}
.recebox .topbox a{display: inline-block;float: right;color: #00dc82;text-decoration: underline;}
/*地址列表*/
.recebox .address{overflow: hidden;padding-bottom: 0px;padding-left: 20px;margin-top: 20px;max-height: 123px;}
.recebox .noshow{display: none;visibility: inherit;}

/*配送时间*/
.pssj{border-bottom: 1px dashed #e0e0e0;padding: 20px 0;}
.pssj .info{color: #333;}
.pssj ul{overflow: hidden;margin-top: 15px;}
.pssj ul li{float: left;border: 1px solid #00dc82;padding: 0px 6px;color: #00dc82;margin-right: 16px;height: 30px;line-height: 30px;}
/*支付方式*/
.zffs{border-bottom: 1px dashed #e0e0e0;padding: 20px 0;}
.zffs .info{color: #333;}
.zffs ul{height: 32px;margin-top: 15px;}
.zffs ul li{padding: 4px 0;float: left;border: 1px solid #e0e0e0;width:110px;text-align: center; color: #333;margin-right: 16px;min-height: 30px;}
.zffs ul li .payTypeTip {color:#666666;font-size: 12px;}
.zffs ul li.cur{border: 2px solid #00dc82 !important; width: 108px;background: url(../../images/zf-icon.png) right bottom no-repeat; }
.zffs ul li:hover{cursor: pointer;color: #f39800;border: 1px solid #f39800;}
.zffs .noshow{display: none;}
.zffs .hdfk-box{margin-top: 20px;}
.zffs .hdfk-box span{color: #ff2400;font-weight: bold;}
.zffs .grayBtn {background: #F9F9F9;border: 1px solid #DDDDDD;color:#666;}
.zffs .grayBtn:hover{cursor: pointer;border: 1px solid #DDDDDD;}
.zffs .zq-li { position: relative }
.zffs .zqTip { position: absolute;display: none;top: -114px;width: 300px;text-align: left;line-height: 18px; background: rgba(0,0,0,0.6);border-radius:5px; color: #fff; padding: 10px;}
.zffs .zq-li:hover .zqTip {display: block;}
.zffs .zq-li .tyqiIcon {position:absolute; width: 56px;height: 18px;top:-19px;right:-1px; background: url(../../images/yuqi.png) no-repeat;background-size: 100% 100%;}

/*发票类型*/
.fplx{box-sizing: border-box;border-bottom: 1px dashed #e0e0e0;padding: 20px 0 20px 40px;background:#ffffff;width:1198px;margin:10px auto 0;}
.fplx .info{color: #333;}
.fplx .fplx-a{font-size: 14px;color: #6493d4;padding-left: 3px;}
.fplx .fplx-a .sui-icon{font-size: 14px;}
.fplx ul{overflow: hidden;margin-top: 15px;}
.fplx ul li{float: left;border: 1px solid #e0e0e0;width:110px;text-align: center; color: #333;margin-right: 16px;height: 30px;line-height: 30px;}
.fplx ul li.cur{border: 1px solid #00dc82 !important;width: 108px;height: 28px; background: url(../../images/zf-icon.png) right bottom no-repeat;}
.fplx ul li:hover{cursor: pointer;color: #f39800;border: 1px solid #f39800;}
/*优惠券*/
.youhuiquan{border-bottom: 1px dashed #e0e0e0;padding: 20px 0;max-height: 214px;overflow: hidden;}
.youhuiquan .address-top-title{overflow: hidden;margin-bottom: 20px;}
.youhuiquan .address-top-title .yhq-l-box{float: left;}
.youhuiquan .address-top-title .yhq-l-box a{display: inline-block;font-family: PingFangSC-Semibold;font-size: 14px;color: #434343;letter-spacing: 0.78px;margin-right: 32px;padding-bottom: 5px;}
.youhuiquan .address-top-title .yhq-l-box a.cur{color: #00DC7D;border-bottom: 2px solid #00dc7d;}
.youhuiquan .address-top-title .yhq-l-box a.ky-yhq{}
.youhuiquan .address-top-title .yhq-l-box a.bky-yhq{}
.youhuiquan .address-top-title .yhq-r-box{float: right;}
.youhuiquan .address-top-title .yhq-r-box a{display: inline-block;}
.youhuiquan .address-top-title .yhq-r-box a.xiala{}
.youhuiquan .address-top-title .yhq-r-box a.shouqi{display: none;}
.youhuiquan .address-top-title .yhq-r-box a img{margin-right: 3px;}
.youhuiquan .address-top-title .yhq-r-box a span{font-size: 12px; color: #6493D4;}
.youhuiquan .bky{display: none;}

.youhuiquan .info{color: #333;}
.youhuiquan ul{margin-top: 10px;}
.youhuiquan ul li{float: left;color: #333;margin-right: 34px;position: relative;margin-bottom: 20px;}
.youhuiquan ul li.three-3n{margin-right: 0px;}
.coupon-list{ width: 322px;height: 85px;background:url(../../images/couponbg.png) no-repeat; padding: 10px 15px;}
.coupon-item{  display: block;  width: 322px;  height: 85px; cursor:default;}
.coupon-item-top{  height: 54px;  width: 322px;  overflow: hidden;  }
.coupon-item-top-left{  float: left;  width: 210px;  }
.coupon-item-top-left .coupon-name{  font-size: 16px;}
.coupon-item-top-left .coupon-time{font-size: 12px;color: #999;margin-top: 8px; text-overflow:ellipsis; white-space:nowrap; overflow:hidden; }
.coupon-item-top-right{width: 110px;float: right;text-align: right;color: #00dc7d;font-size: 36px;margin-top: 9px;  }
.coupon-item-top-right .rmb-icon{font-size: 14px;}
.coupon-item-bot{ padding-top: 13px;  }
.coupon-item-bot-left{width: 100px;float: left;font-size: 12px;  }
.coupon-item-bot-right{float: right;width: 110px;height: 18px;line-height: 18px;text-align: center;background: #e6eff8;color: #6d7881;font-size: 12px;display: inline-block;border-radius: 9px;margin-right: 3px;}
.youhuiquan .first-spe{float: right;}
.youhuiquan .first-spe span{font-size: 12px;color: #6F7A87;display: inline-block;}
.youhuiquan .first-spe span.wmk{background: #E6EEF8; border-radius: 10px;padding: 0px 5px;margin-right: 13px;}
.youhuiquan .first-spe span.chakan{background: #00DC82;  border-radius: 10px;padding: 0px 5px;color: #ffffff;}

/*.youhuiquan ul li.cur {background: url(../../images/couponbg_cur.png) no-repeat; }*/
/*.youhuiquan ul li.cur .xuanzhong{display:block;width: 29px;height: 30px;background: url(../img/coupon-cion.png) no-repeat;position: absolute;bottom: 0;right: 0;  }*/

/*复选框样式*/
.youhuiquan .checkbox-pretty span:before, .radio-pretty span:before{font-size: 150%;font-size: 120%\0;position: relative;top:0px;color: #D2D2D2;}
.youhuiquan .checkbox-pretty.checked>span:before, .radio-pretty.checked>span:before{color: #50E3C2;}
.youhuiquan .checkbox-pretty:hover span:before, .radio-pretty:hover span:before{color: #50E3C2;}

/*优惠券灰色样式*/
.gray{ width: 322px;height: 85px;background:url(../../images/couponbg_gray.png) no-repeat; padding: 10px 15px;}
.gray .coupon-item-top-left .coupon-name{color: #cccccc;}
.gray .coupon-item-top-left .coupon-time{color: #cccccc;}
.gray .coupon-item-bot-left{color:#6F7A87; }
.gray .coupon-item-top-right{color: #cccccc;}
.gray .coupon-item-bot-right{color: #ffffff;background-color: #cccccc;}

/*我的余额*/
.my-balance{box-sizing: border-box;width:1198px;margin:0 auto;padding: 20px 40px;border-bottom: 1px dashed #e6e6e6;background: #ffffff;}
.my-balance .infobox{overflow: hidden;margin-top: 15px;}
.my-balance .infobox span.redcolor{color: #ff2400;}
.my-balance .infobox span.dikou{margin-left: 40px;}
.my-balance input.inputprice{width: 128px;height:28px;border: 1px solid #cccccc;padding-left: 5px; margin: 0 12px;}
.my-balance a.rightbox{color: #6493d4;}
.my-balance a.rightbox .sui-icon{
    font-size: 14px;
}
.my-balance .infobox .tishi{padding-left: 30px;}
.infobox-top{
    font-size: 14px;
}
.infobox-bot{
    margin-top: 10px;
    font-size: 14px;
}
.infobox-bot .allprice{
    padding: 0 5px;
}


/*备注留言*/
.bzly{border-bottom: 1px dashed #e0e0e0;padding: 20px 0;}
.bzly .info{color: #333;}
.bzly input{margin-top:0px;height: 33px;line-height: 33px;border: 1px solid #e0e0e0;padding-left: 15px;width: 574px;}

/*送货清单*/
.shqd{margin: 0 auto;width: 1200px;}
.shqdtitle{font-size: 16px;color: #333333;width: 1199px;box-sizing: border-box; margin: 0 auto;height:60px;line-height: 60px;background:#ffffff;padding-left: 25px;}

/*套餐商品*/
.taocanbox{border-bottom: 1px solid #e0e0e0;}
.taocanbox .taocantitle{height: 35px;line-height: 35px;border: 1px solid #e0e0e0;padding-left: 60px;}
.taocanbox .taocanmain{position: relative;margin-bottom: 35px;}
.taocanbox .tcprice{position: absolute;top:50%;right: 100px;font-size: 14px;color: #ff2400;margin-top: -7px;}

.defaultbox-all{display: none;}

/*列表模式*/
.listmode{width:1198px;margin: 0 auto;border: 1px solid #e0e0e0;margin-bottom: 0px;border-bottom: none;}
/*表头*/
.listmode .headbox{overflow: hidden;background: #353a42;border-top-left-radius: 4px;border-top-right-radius: 4px;}
.listmode .headbox  ul{overflow: hidden;}
.listmode .headbox  li{float:left;height: 50px;line-height: 50px;color: #c0c0c0;}
.listmode .headbox .li1{width: 450px;text-align: left;}
.listmode .headbox .li1 span{padding-left: 26px;}
.listmode .headbox .li1 label{margin-left: 26px;margin-right: 105px;}
.listmode .headbox .li1 .checkbox-pretty span:before, .listmode .headbox .li1 .radio-pretty span:before{margin-right: 29px;}
.listmode  .li_mingxi{width: 350px;}
.listmode .headbox .li2{width: 240px;}
.listmode .headbox .li3{width:286px;width: 156px;}
.listmode .headbox .li4{width: 140px;width: 120px;}
.listmode .headbox .li5{width: 210px;width: 110px;}
.listmode .headbox .li6{width: 98px;}
/*表体*/
.listmode .bodybox{overflow: hidden;border-bottom: 1px solid #e0e0e0;background-color: #ffffff;}
.listmode .bodybox.cur{background: #f4fffa;}
/*套餐*/
.listmode  .taocanbox{height: 42px;line-height: 42px;border-bottom: 1px solid #e0e0e0;background-color: #eeeeee;}
.listmode  .taocanbox .title{font-size: 14px;display: inline-block;background: #df3c3a;color: #fff;height: 17px;line-height: 17px;padding: 2px 5px;margin-left: 20px;margin-right: 20px;}
.listmode  .taocanbox .info{font-size: 12px;color: #df3c3a;}
.listmode  .taocanbox .price{font-size: 12px;color: #df3c3a;margin-right: 20px;}
.listmode  .taocanbox .new{font-size: 12px;color: #666666;padding-left: 40px;}

/*套餐选中*/
.tccheckb{position: absolute;top:50%;left: 29px;margin-top: -20px;}
/*套餐中的其它商品*/
.taocanbox-onther{height: 14px;line-height: 14px;border-bottom: 1px solid #e0e0e0;background-color: #eeeeee;}
.taocanbox-onther .new{font-size: 12px;color: #666666;padding-left: 40px;}
.onther-tcbox{margin-top: 0px;}

.taocanspe{position: relative;}
.taocanspe .taocanjj{position: absolute;top:50%;right: 290px;margin-top: -20px;}
.taocanspe .taocanjj .suliang{border: 1px solid #e0e0e0;width:120px;height: 32px;overflow: hidden;margin: 0 auto;background: #ffffff;}
.taocanspe .taocanjj .suliang a.sub{display:block;width: 30px;height: 32px;line-height: 32px;border-right: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.taocanspe .taocanjj .suliang input{display:block;width: 54px;height: 32px;line-height: 32px;margin: 0;padding: 0;border: none;text-align: center;}
.taocanspe .taocanjj .suliang a.add{display:block;width: 30px;height: 32px;line-height: 32px;border-left: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}

.taocanspe .taocanxj{position: absolute;top:50%;right: 84px;margin-top: -30px;padding-top: 20px;}
.taocanspe .taocanxj .zkj{color: #ff2400;padding-bottom: 15px;}
.taocanspe .taocanxj .sjj{color: #999;}
.taocanspe .taocanxj .sjj span{font-size: 12px;}

.taocanspe .deltc{position: absolute;top:50%;right: 88px;margin-top: -12px;}
.taocanspe .deltc a.addbuy{color: #999999;font-size: 12px;}
.lib3 .tcsj{color: #999999;font-size: 12px;margin-left: 7px;}
/*满减*/
.listmode  .manjianbox{height: 36px;line-height: 36px;border-bottom: 1px solid #e0e0e0;background-color: #fff2cf;}
.listmode  .manjianbox .title{font-size: 14px;display: inline-block;background: #df3c3a;color: #fff;height: 17px;line-height: 17px;padding: 2px 5px;margin-left: 40px;margin-right: 20px;}
.listmode  .manjianbox .info{font-size: 12px;color: #df3c3a;}
.manjianbox .title_hui{border:1px solid #FF5B5B;color: #FF5B5B;font-size: 12px;padding:2px 4px;}
.manjianbox .info_hui{float: right;font-size: 12px;color: #999;}
.manjianbox .info_hui{float: right;margin-right:58px;font-size:12px;color:#999;border:1px solid #d4d4d4;width:50px;  height:25px;line-height:25px;text-align:center;margin-top:5px;}
/*失效*/
.listmode  .shixiaotitle{margin-top: 51px;margin-bottom: 20px;}
.listmode  .shixiaotitle .info{font-size: 12px;color: #df3c3a;padding-left: 5px;}
.listmode  .shixiaotitle .clear-goods{color: #333333;padding-left: 103px;padding-right: 60px;font-weight: bold;}
.listmode  .shixiaotitle .clear-goods:hover{text-decoration: underline;}
.listmode  .shixiaotitle .move-goods{color: #333333;font-weight: bold;}
.listmode  .shixiaotitle .move-goods:hover{text-decoration: underline;}

/*列表*/
.listmode .pro-box{background:#ffffff;margin-bottom: 8px;}
.listmode .bodybox  ul{overflow: hidden;}
.listmode .bodybox  li{float:left;padding-bottom: 20px;}
.listmode .bodybox .lib1{width: 450px;}
.listmode .bodybox .lib3{width:286px;width: 156px;}
.listmode .bodybox .lib4{width: 140px;width: 120px;}
.listmode .bodybox .lib5{width: 210px;width: 110px;}
.listmode .bodybox .lib6{/*width: 168px;*/width: 98px;text-align: left;}


.lib1 .checkb{width:10px;margin-top: 52px; padding-left: 26px;padding-right: 20px;}
.lib1 .shixiaotext{width:24px;margin-top: 52px; padding-left: 26px;padding-right: 8px;}
.lib1 .shixiaotext span{background-color: #dadada;}
.lib1 .shixiao{width: 28px;height:100px;line-height: 100px;padding-left: 5px;font-weight: bold;}
.lib1 .l-box{width: 110px;height: 110px;border: 1px solid #e0e0e0;overflow: hidden;margin-left: 40px;margin-top: 20px;margin-right: 15px;}
.lib1 .l-box img{width: 110px;height: 110px;vertical-align: middle;}
.lib1 .r-box {width: 275px;padding-top: 20px;}
.lib1 .r-box span{font-size: 12px;}
.lib1 .r-box span.title{color: #999;}
.lib1 .r-box span.info{color: #888888;}
.lib1 .r-box .lib1-row1 span{color: #333;}
.lib1 .r-box .lib1-row1 a{color:#333333; }
.lib1 .r-box .lib1-row1 a:hover{color:#f39800; }
/* .lib1 .r-box .lib1-row2{margin-top: 34px;} */
.lib1 .r-box .lib1-row3{margin-top: 5px;}
.lib1 .r-box .lib1-row4{margin-top: 2px;}

.lib2 span{font-size: 12px;}
.lib2 .huodong{margin-top: 20px;padding-left: 25px;}
.lib2 .huodong span{display: inline-block;background: #ff2400;color: #fff;padding: 2px 7px; }
.lib2 .time{margin-top: 15px;padding-left: 25px;}
.lib2 .time span{color: #ff2400;}

.lib3{font-size: 12px;}
.lib3 .zkj{color: #ff2400;margin-top: 45px;font-size: 12px;}
.lib3 .zkj span.manjian{background-color: #ffa200;color: #ffffff;margin-left: 5px; padding: 0 2px;}
.lib3 .zkj span.sanfang{background-color: #ff2400;color: #ffffff;margin-left: 5px; padding: 0 2px;}
.lib3 .sjj{margin-top: 0px;}
.lib3 .sjj span{text-decoration: line-through;color: #808080;font-size: 12px;}
.lib3 .loginshow{color: #f39800;margin-top: 20px;}
.lib3 .notbug{color: #ff2400;margin-top: 20px;}

.lib4{padding-top: 0px;}
.lib4 .zkj{color: #ff2400;padding-bottom: 15px;margin-top: 50px;}
.lib4 .sjj{color: #999;}
.lib4 .sjj span{font-size: 12px;}


.lib5{padding-top: 0px;}
.lib5 span{font-size: 12px;padding-top: 45px;display: block;}
.lib5 .suliang{border: 1px solid #e0e0e0;width:120px;height: 32px;overflow: hidden;margin-top:40px;background: #fffff;}
.lib5 .suliang a.sub{display:block;width: 30px;height: 32px;line-height: 32px;border-right: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.lib5 .suliang input{display:block;width: 54px;height: 32px;line-height: 32px;margin: 0;padding: 0;border: none;text-align: center;}
.lib5 .suliang a.add{display:block;width: 30px;height: 32px;line-height: 32px;border-left: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.lib6{}
.lib6 a{display:block;width: 120px;height: 30px;line-height: 30px;border-radius: 3px;color: #999999;font-size: 12px;}
.lib6 a.addbuy{margin-top: 25px;}
.lib6 a.souc{margin-top: 8px;}

/*查看更多*/
a.more{color: #666;display: block;margin: 0 auto;text-align: center;width: 120px;color: #999;text-decoration: underline;height: 52px;line-height: 52px;}
a.no-more{color: #666;display: none;margin: 0 auto;text-align: center;width: 120px;color: #999;text-decoration: underline;height: 52px;line-height: 52px;}

/*控销毛利率*/
.lib3 .row-last{margin-top: 10px;;overflow: hidden;}
.lib3 .row-last span{font-size: 12px;color: #333333;}
.lib3 .row-last span.jg{margin-left: 3px;}
.lib3 .row-last span.s-kx{color: #ffffff;background: #21a2e8;border-radius: 7px;padding: 0px 7px;}
.lib3 .row-last span.s-ml{color: #ffffff;background: #ff0000;border-radius: 7px;padding: 0px 7px;margin-left: 10px;}
/*总计*/
.totalbox{width: 1120px;margin: 0 auto;background: #ffffff;padding: 15px 40px 15px 5px;/*margin-bottom: 40px;*/clear: both;}
.totalbox .rbox-total{}
.totalbox .rbox-total .spzjbox{text-align: right;font-size: 14px;color: #666;margin-bottom: 20px;}
.totalbox .rbox-total .spzjbox span{font-size: 14px;color: #666;}
.totalbox .rbox-total .spzjbox span.zhongshu{padding: 0 5px;}
.totalbox .rbox-total .spzjbox span.zongjianshu{padding: 0 25px 0 5px;}

.totalbox .rbox-total .fd-warp{overflow: hidden;}
.totalbox .rbox-total .fd-warp .left-fd-box{float: left;padding-right: 40px;}
.totalbox .rbox-total .fd-warp .mid-line{float: left;height: 140px;border-right: 1px solid #ececec;margin-top: 5px;}
.totalbox .rbox-total .fd-warp .right-fd-box{float: right;}
.fd-warp .list-item{margin-bottom: 14px;}
.fd-warp .list-item span{display: inline-block;font-size: 12px;color: #666;}
.fd-warp .list-item span.spec{color: #666666;width: 90px;text-align: right;margin-right: 15px;}
.fd-warp .list-item span.red{color: #e73734;}



/*销售协议*/
.wrapbox{ font-size: 12px;color: #999;margin-bottom: 20px;margin-top: 10px;display: inline-block;margin-right: 14px;}
.wrapbox a{color: #6493d4;}
.noshow{visibility: hidden;}
/*提示领取余额*/
.ts-getyue{width:1190px;padding-right: 10px; margin:0 auto;text-align:right; margin-bottom: 15px;font-size: 18px;color: #666666;margin-bottom: 40px;}
.ts-getyue a{color: #00dc82;font-weight: bold;text-decoration: underline;}

/*消费返*/
.reward-banner{
    display: none;
    height: 84px;
    margin: 0 auto;
    width: 1158px;
    background-image: linear-gradient(-87deg, #FF2222 40%, #FE510F 100%);
    padding: 10px 20px 0 20px;
    cursor:pointer;
}
.banner-head{
    display: flex;justify-content: space-between;
}
.banner-head img{
    height: 20px;
}
.banner-head .countdown{
    color: #fff;font-size: 16px;
}
.banner-head .countdown div{
    display: inline-block;
    color: #fff;
    font-size: 16px;
}
.banner-content{
    width: 1150px;
    height: 32px;
    line-height: 32px;
    background: #FFFFFF;
    border-radius: 4px;
    margin: 0 auto;
    padding-left: 10px;
    color: #333;
    font-size: 16px;
    margin-top: 10px;
}
.banner-content span{
    color:#FF0000;
}
.state-color{
    color:#000;
}
/*消费返弹窗*/
.consumer-rebate{
    position: fixed;top: 0;left: 0;width: 100%;height: 100%;background-color: rgba(0,0,0,0.5);display: none;z-index: 2222;
}
.consumer-rebate .rebate-content{
    position: fixed;
    top: 15%;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
}
.consumer-rebate .rebate-content .tip-content{
    position: relative;
}
.guide{
    width: 100%;
} 
#data-table{
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
}
#data-table th{
    font-weight: bold;
    background-color: #f55207;
    color: #fff;
}
#data-table thead{
        border: 1px solid #f55207;
}
#data-table tbody{
        border: 1px solid #f55207;
}
table {
    border-collapse: collapse;
    width: 100%;
    font-family: Arial, sans-serif;
}
th, td {
    padding: 12px;
    text-align: left;
}
.rate-cell {
    position: relative;
}
.extra-rate {
    color: #666;
    font-size: 0.9em;
    margin-top: 5px;
    display: none;
}
.table-th div{
    color: #fff;
}
.add-ratio{
    width: 37px;
    height: 13px;
    line-height: 13px;
    background: #FF0000;
    border-radius: 4px;
    font-size: 12px;
    color: #fff;
    display: inline-block;
    margin-left: 10px;
    padding: 0 2px;
}
.limited-time-offer{
    border-radius: 4px 0 0 0 4px 4px 4px;
    height: 17px;
    line-height: 17px;
    border-radius: 4px 0 0 0 4px 4px 4px;
    position: relative;
}
.limited-label{
    color: #FF0000;
    font-size: 12px;
    width: 188px;
    position: absolute;
    left: 0;
    bottom: -7px;
    border:1px solid #ff0909;
    border-radius:4px;
    padding-left:3px;
    //background: #FFE4DA;
}
.guide tbody tr:nth-child(2n){
     background: #FFECE1;
}
.limited-label:before,.limited-label:after{
    content: "";
    display: block;
    border-width: 8px;
    position: absolute;
    top: -17px;
    left: 4px;
    border-style: dashed dashed solid;
    border-color: transparent transparent #ff0909;
    font-size: 0;
    line-height: 0;
}
.limited-label:after{
    top:-16px;
    border-color: transparent transparent #FFF;
}
.order-state{
    width: 546px;
    height: auto;
    background: #FF4A27;
    border-radius: 24.31px;
    padding: 10px 22px 16px 22px;
}
.order-table-box{
    position: relative;
    height: auto;
    background: #FFFFFF;
    border-radius: 16.38px;padding: 23px 58px 10px;box-sizing: border-box;
}
.order-table-box img{
    height: 30px;
    width: 87px;
    position: absolute;
    right: 11px;
    top: -30px;
}
.head-container{
    width:100%;
}
.head-container .rebate-explain-p1{
    text-align: center;font-size: 16px;color: #000000;
    
}
.head-container .rebate-explain-p2{
    text-align: center;font-size: 18px;margin-top: 10px;margin-bottom: 10px;
}
.head-container .text-style{
    text-align: center;margin-top: 42px;height: 50px;display: flex;justify-content: center;align-items: baseline;
}
.head-container .text-style .text-span-1{
    font-weight: 600;font-size: 100px;color:#FF0000;
}
.text-span-2{
    font-weight: 600;
    font-size: 40px;
    color: #FF0000;
}
.head-container .closeRebateBtn{
    width: 150px;
    height: 48px;
    background-image: linear-gradient(-80deg, #FF2222 40%, #FE510F 100%);
    border: 3px solid #FFDFA2;
    border-radius: 27.44px;
    padding: 5px 15px;
    font-size: 16px;
    color: #fff;
    cursor: pointer;
    margin-top: 15px;
    margin-left: 140px;
}
/*提交订单*/
.orderfix{position: fixed;margin:auto;left:0; right:0; bottom:0;background: #fff;background-color: #e5e5e5;}
.applybox{width: 1198px;margin:0 auto;border: 0px solid #e0e0e0;background-color: #fff;}

.applybox .acol5{margin-right: 60px;text-align: right;margin-top: 20px;}
.applybox .acol5 .tinfo{font-size: 12px;color: #9b9999;}
.applybox .acol5 .money{color: #e73736;font-size: 16px;text-align: right;font-weight: bold;}

.applybox .acol6{text-align: right;margin-right: 60px;margin-top: 20px;position: relative;}
.applybox .acol6 a.tjbtn{display: inline-block;width: 142px;height: 46px;line-height: 46px;text-align: center;background: #f39800;color: #fff;font-size: 16px;font-weight: bold;text-decoration: none;border-radius: 2px;}
.applybox .acol6 a.tjbtn-gray{display: inline-block;width: 142px;height: 46px;line-height: 46px;text-align: center;background: gray;color: #fff;font-size: 16px;font-weight: bold;text-decoration: none;border-radius: 2px;}
.applybox .acol6 a.gray{background: #ededed;padding: 0;}
.applybox .acol6 .tishiwa{font-size: 12px;color: #df3c3a;position: absolute;top:-38px;right:240px;}

/*新增收货地址弹窗*/
#addModal,#editModal{  width: 700px;  }
.add-address-modal .sui-form.form-horizontal .control-label{display: inline-block;  }
.add-address-modal .sui-form input[type="text"]{  height: 24px;  line-height: 24px;  }
.add-address-modal .detailinp{  width: 395px;}
.add-address-modal .prov,.add-address-modal .city,.add-address-modal .dist{  width: 110px;  height: 28px;  border: 1px solid #e8e8e8;  margin-right: 5px;}
.add-address-modal .modal-bot{ text-align: right; margin-top: 10px;border-top: 1px solid #eee; padding-top: 20px; }
.add-address-modal .modal-bot button{  width: 98px;  height: 30px;  margin-right: 10px;  }
.add-address-modal .btn-primary {  background: #00dc82;  border: 1px solid #00dc82;}
.sui-modal .modal-header .modal-title{  font-size: 20px;  color: #333;  font-weight: bold;  }
#editModal textarea{height: 67px;width: 353px;}
#editModal .spe{position: relative;top:-25px;}
img.errorimg{position: absolute;left: 3px;top: 4px;}

/*支付弹窗*/
#payModal{width: 600px;height: 360px;overflow: hidden;}
#payModal .modal-content{overflow: hidden;}
#payModal .titleinfo{text-align: center;font-size: 20px;font-weight: bold;margin-top: 70px;margin-bottom: 56px;}
#payModal .titleinfo .red{color: #F29700;}
#payModal  .zf-lbox{width: 235px;height: 100px;border-right: 1px solid #e0e0e0;margin-left: 66px;}
#payModal  .zf-lbox .title{font-size: 16px;text-align: center;margin-top: 10px;}
#payModal  .zf-lbox a{font-weight: bold;font-size: 16px;display: block;width: 120px;height: 40px;line-height: 40px;text-align: center;background: #f5f5f5;color: #666;margin: 0 auto;margin-top: 20px;border: 1px solid #e0e0e0;border-radius: 5px;}
#payModal  .zf-rbox{width: 235px;height: 100px;}
#payModal  .zf-rbox .title{font-size: 16px;text-align: center;margin-top: 10px;}
#payModal  .zf-rbox a{font-weight: bold;font-size: 16px;display: block;width: 120px;height: 40px;line-height: 40px;text-align: center;background: #fff;color: #F29700;margin: 0 auto;margin-top: 20px;border: 1px solid #F29700;border-radius: 5px;}
#payModal .bottom1{text-align: center;margin-top: 30px;}
#payModal .bottom2{text-align: center;margin-top: 15px;}
#payModal .bottom2 a{border: 1px solid #e0e0e0;padding: 5px 5px;color: #000;margin-left: 10px;}

/*发票须知弹窗*/
#fpxzTc{width: 520px;border-radius: 5px;}
#fpxzTc .xzmain {padding: 0 20px 20px 20px;}
#fpxzTc .xzmain p{font-size: 12px;}
#fpxzTc .xzmain p.xz-title{font-weight: bold;margin-top: 20px;}
#fpxzTc .xzmain p.xz-info{}
#fpxzTc .modal-body{line-height: 17px;padding: 0;padding-bottom: 20px;}
#fpxzTc .modal-header{border-radius: 5px;}

/*弹框公用样式*/
.sui-modal{  border: none }
.sui-modal .modal-header {  padding: 0;  margin: 0;  border:none;  height: 40px;  background: #f5f5f5;  }
.sui-close{  margin: 10px;  }
.sui-modal .modal-header .modal-title {  font-size: 20px;  color: #333;  font-weight: normal;  height: 40px;  line-height: 40px;  padding-left: 15px;  }
.sui-modal .modal-footer{  background: none;  padding-bottom: 20px;  }
.btn-primary:hover, .btn-primary:focus {  color: #fff;  background-color: #00dc82;  border: 1px solid #00dc82;  }
.btn-primary {  color: #fff;  background-color: #00dc82;  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);  border: 1px solid #00dc82;  }

/*删除弹窗*/
.scbox{width: 300px;margin: 0 auto;padding: 0; padding-top: 10px;padding-bottom:10px;font-size: 18px;}

/*2017-06-22 涂洋*/
.main{
    width:100%;
    background:#f2f2f2;
    border: none !important;
    overflow: hidden;
}
.pay-content{
    width: 1148px;
    margin: 30px auto;
    /*border: 1px solid #e6e6e6;*/
    background: #fff;
    padding: 25px 25px 0;
}
.address-top-title{
    color: #333;
    font-size: 14px;
}
.gspadres{
    margin-top: 20px;
    padding-bottom: 15px;
}
.gspadres .adres-row{
    display: inline-block;
    float: left;
    font-size: 12px;
}
.adres-row-new{width: 50px;}
.adres-row1{
    width: 130px;
}
.adres-row2{
    width: 585px;
}
.adres-row2 p{
    font-size: 12px;
}
.adres-row3{
    width: 250px;
    padding-left: 60px;
}
.adres-row4{
    width: 60px;
}
.sec-ades{
    color: #999;
}
.adres-row4 .sui-icon{
    font-size: 14px;
    padding-right: 5px;
    position: relative;
    top: 2px;
}
.adres-row4 .res-btn{
    color: #6493d4;
}
/*弹窗*/
.adres-text{
    text-align: left;
}

/*弹窗样式*/
.sui-modal .modal-body{text-align: left;line-height: 23px;}

/*319优惠券*/
.youhuiquan{border-bottom: none;padding: 20px 23px 0 25px;max-height: 270px;overflow: hidden;background:#ffffff;}
ul.yhq-common li{margin-left: 30px;margin-right: 34px;}


.youhuiquan .yhq-checkb{position: absolute;top: 32px;left: -28px;}

/*查看优惠券样式*/
#yhqModal{width: 730px;overflow-y: auto;margin-left: -365px;}
#yhqModal ul{margin-top: 10px;width: 700px;}
#yhqModal ul li{float: left;color: #333;position: relative;margin-bottom: 20px;}

/*商品明细*/
.li_mingxi .mxrow1{margin-top: 45px;}
.li_mingxi .mxrow2{margin-top: 10px;}
.li_mingxi span{font-size: 12px;display: inline-block;}
.li_mingxi .mxrow_tit{width: 60px;}
.li_mingxi .mxrow_info{width: 80px;}
.li_mingxi .head-tit{font-size: 14px;padding-left: 100px;}


/* 标签 */
.lib1 .r-box .lib1-row1 span.dujia {
    background-color: #27ADFF;
    border-radius: 2px;
    color: #ffffff;
    font-size: 12px;
    padding: 2px 4px;
    margin-right: 5px;
}
.row-biaoqian {
    font-size: 12px;
}
.row-biaoqian span {
    padding: 1px 3px;
    border-radius: 4px;
    color: #ff2727;
    border: 1px solid #ff2727;
}
.row-biaoqian span.linqi {
    background-color: #FF8E00;
    color: #ffffff;
    border: 1px solid #FF8E00;
}
.row-biaoqian span.quan {
    background-color: #FF0000;
    color: #ffffff;
    border: 1px solid #FF0000;
}
.row-biaoqian span.manjian {
    background-color: #ffffff;
}
.row-biaoqian span.default {
    background-color: #E4E4E4;
    color: #333333;
    border: 1px solid #E4E4E4;
}
.synthesis {display: inline-block;}
.synthesis-biao {display: flex;border: 1px solid #FF2121;border-radius: 2px;overflow: hidden;}
.synthesis-biao .synthesis-biao-left {padding: 0px 5px;box-sizing: border-box; PingFangSC-Regular;font-weight: 400;font-size: 10px;color: #FFFFFF;background: #FF2121;border-radius: 0;}
.synthesis-biao .synthesis-biao-shuoming, .synthesis-biao-left-single {padding: 0px 5px;box-sizing: border-box;font-family: PingFangSC-Regular;font-weight: 400;font-size: 10px;color: #FF2121;border: 0;}
.tag-title {
    background-image: linear-gradient(90deg, #FC6C41 0%, #F62626 100%);
    border-radius: 4px;
    padding: 4px 6px;
    box-sizing: border-box;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 12px;
    color: #FFFFFF !important;
    display: inline-block;
    text-align: center;
    line-height: 1;
    height: 100%;
}
.lib1-row5{
    margin-bottom: 5px;
}
.listmode .bodybox ul .lib1 .l-box .bq-box{
    top: 30px;
    left: 30px;
}
/*控销毛利率*/
.row-last{margin-top: 5px;overflow: hidden;}
.row-last .kongxiao-box{float: left;border: 1px solid #404C59; border-radius: 4px;margin-right: 7px;}
.row-last .maoli-box{float: left;border: 1px solid #404C59; border-radius: 4px;}
.row-last span{font-size: 12px;color: #333333;display: inline-block;float: left;}
.row-last span.jg{padding: 0px 5px}
.row-last span.s-kx{color: #ffffff;background: #475462 ;padding: 0px 6px;}
.row-last span.s-ml{color: #ffffff;background: #475462;padding: 0px 6px;}
/* 自营企业 */
.listmode .ziying{
    background-color: #00DC82;
    border-radius: 2px;
    color: #ffffff;
    font-size: 12px;
    padding: 2px 4px;
    margin-right: 5px;
}
.listmode .cgd-qy{
    height: 40px;
    line-height: 40px;
    /*background: #fff;*/
    /*border-bottom: 1px solid #e0e0e0;*/
    padding-left: 20px;
}
.listmode .cgd-qy1{
    padding-left: 30px;
    border-bottom: 1px solid #f1f1f1;
}
.listmode .cgd-qy .qy-title{
    font-size: 14px;
    color: #333333;
    font-weight: 600;
}
.listmode .cgd-qy1 .qy-title{
    font-size: 14px;
    color: #333333;
    font-weight: 400;
}
.applybox{
    /*margin-top: 10px;*/
    border-radius: 1px;
}
.js-order{
    overflow: hidden;
    background: #FFFDF4;
    padding-bottom: 15px;
    clear: both;
}
.js-total {
    /*overflow: hidden;*/
    padding:20px 40px 10px 0;
    background: #fff;
}
.applybox .fd-warp .list-item span.spec{
    color: #666;
}
.wrapbox p span{
    color: #FF5B5B;
}
.wrapbox p .money{
    font-size: 16px;
}
.wrapbox{ font-size: 12px;color: #999;margin-bottom: 20px;display: inline-block;margin-right: 14px;position: absolute;right: 140px;top: -14px;}
.wrapbox p{
    color: #9B9999;
}
.wrapbox p label span{
    color: #9B9999;
}
/* 电子发票优化 */
.fplx .fptx{
    margin-top: 18px;
}
.fptx .checkbox-pretty:hover span:before,.fptx .radio-pretty:hover span:before{
    color: #50E3C2;
    font-size: 150%;
}
.fptx .checkbox-pretty.checked>span:before,.fptx .radio-pretty.checked>span:before{
    color: #50E3C2;
}
.fptx .checkbox-pretty span:before,.fptx .radio-pretty span:before{
    color: #D2D2D2;
    font-size: 150%;
    top: 1px;
}
.fplx .fptx span i{
    margin-left: 8px;
    font-style: normal;
    display: inline-block;
    padding: 3px 6px;
    background-color: #FFFAE0;
    font-size: 12px;
    color: #F39800;
}

.ti_box, .ti_box_v{width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.50);
    z-index: 99;
    position: fixed;
    top: 0;display: none;}
.ti_shi, .ti_shi_v{width: 416px;margin:150px auto;background: #fff;height: 184px;border-radius: 4px;overflow: hidden;}
.ti_shi .shi_tou,.ti_shi_v .shi_tou_v{height: 50px;background: #f5f5f5;line-height: 50px;}
.ti_shi .shi_tou .icon-tb-infofill,.ti_shi_v .shi_tou_v .icon-tb-infofill_v{color:#FAAD14 ;font-size: 20px;padding-top: 15px;display: inline-block;padding-left:16px;}
.ti_shi .shi_tou span{font-size: 20px;color: rgba(0,0,0,0.75);}
.ti_shi .shi_tou .icon-tb-close,.ti_shi_v .shi_tou_v .icon-tb-close_v{float: right;margin-top: 15px;margin-right: 20px;font-size: 16px;}
.shi_xia p,.shi_xia_v p{columns: #333333;font-size: 14px;padding-left: 18px;padding-top:10px;}
.shi_qu,.shi_qu_v{padding:6px 25px;border:1px solid #d9d9d9;border-radius: 4px;color: rgba(0,0,0,0.65);font-size: 14px;float: right;margin-top:60px;}
.shi_que,.shi_que_v{padding:6px 25px;border-radius: 4px;color: #ffffff;font-size: 14px;background: #00C675;float: right;margin-right:16px;margin-left: 8px;margin-top:60px;}

.def-biaoqian {
    font-size: 12px;
}
.def-biaoqian span {
    padding: 1px 3px;
    border-radius: 4px;
    color: #ff2727;
    border: 1px solid #ff2727;
}
.def-biaoqian span.default {
    background-color: #FF5F59;
    color: #fff;
    border: 1px solid #FF5F59;
}