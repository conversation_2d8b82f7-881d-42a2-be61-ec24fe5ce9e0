package com.xyy.ec.pc.aspect;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.PlanningScheduleBussinessApi;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.system.business.api.BranchBusinessApi;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 
 * <AUTHOR>
 *
 */
@Aspect
@Component
public class PlanningScheduleAspect {
	private static final Logger LOGGER = LoggerFactory.getLogger(PlanningScheduleAspect.class);

	@Reference(version = "1.0.0")
	private MerchantBussinessApi merchantBussinessApi;

	@Reference(version = "1.0.0")
	private PlanningScheduleBussinessApi planningScheduleBussinessApi;

	@Autowired
	private XyyIndentityValidator xyyIndentityValidator;

	@Reference(version = "1.0.0")
	private BranchBusinessApi branchBusinessApi;
	
	@Pointcut("@annotation(com.xyy.ec.pc.aspect.PlanningScheduleFlag)")
	public void pointCut(){};
    
	@Before("pointCut()")
    public void before(JoinPoint point) {
		try{
			Object[] args = point.getArgs();
			if(args == null || args.length == 0){
				LOGGER.error("动态生成电子计划单失败没有参数传递过来");
				return;
			}
			MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			Long planningScheduleId = (Long)args[0];;
			String BranchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchant.getId());
//			BranchCode = branchBusinessApi.convertCommon(BranchCode);
			planningScheduleBussinessApi.batchCopy(planningScheduleId,BranchCode);
		}
		catch(Exception e){
			LOGGER.error("动态生成电子计划单失败",e);
			//return super.addError("动态生成电子计划单失败"+e);
		}
    }
}
