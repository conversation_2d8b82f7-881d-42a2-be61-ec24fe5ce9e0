<!DOCTYPE HTML>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
	</head>
    <style>
        .suixinpin-modal-body .suixinpin-list {
            padding: 10px;
            display: flex;
            flex-wrap: wrap;
            overflow: auto;
            justify-content: flex-start;
            gap: 10px;
        }
        .suixinpin-modal-body .suixinpin-list .suixinpin-modal-item{
            /* max-width: 285px; */
            width: calc(25% - 20px);
            height: 100px;
            background: #FFFFFF;
            border: 1px solid #E0E0E0;
            border-radius: 4px;
            padding: 5px;
            display: flex;
        }
        .suixinpin-modal-body .suixinpin-list .suixinpin-modal-item .suixinpin-item-left {
            margin: 5px 5px 0 0;
            width: 80px;
            height: 80px;
        }
        .suixinpin-modal-body .suixinpin-list .suixinpin-modal-item .suixinpin-item-left img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .suixinpin-modal-body .suixinpin-list .suixinpin-modal-item .suixinpin-item-right {
            width: calc(100% - 90px);
            font-size: 14px;
        }
        .suixinpin-modal-body .suixinpin-list .suixinpin-modal-item .suixinpin-item-right .modal-suixinpin-item-title {
            color: #222222;
            min-height: 35px;
            text-align: left;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
        }
        .suixinpin-modal-body .suixinpin-list .suixinpin-modal-item .suixinpin-item-right .suixinpin-item-validity {
            height: 12px;
            line-height: 12px;
            color: #666666;
            font-size: 12px;
            font-weight: 400;
            margin: 3px 0;
        }
        .suixinpin-modal-body .suixinpin-list .suixinpin-modal-item .suixinpin-item-right .suixinpin-item-content {
            display: flex;
            justify-content: space-between;
            align-items: end;
        }
        .suixinpin-modal-body .suixinpin-list .suixinpin-modal-item .suixinpin-item-right .suixinpin-item-content .show-price {
            display: block;
            font-size: 14px;
            color: #FF0402;
            line-height: 14px;
        }
        .suixinpin-modal-body .suixinpin-list .suixinpin-modal-item .suixinpin-item-right .suixinpin-item-content .abandoned-price {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 12px;
            text-decoration: line-through;
        }
        .suixinpin-modal-body .suixinpin-list .suixinpin-modal-item .suixinpin-item-right .suixinpin-item-content .suixinpin-item-btn-buy-modal {
            width: 22px;
            height: 22px;
            background: #d8d8d800;
            cursor: pointer;
        }
        .suixinpin-item-btn-buy-modal img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .suixinpin-item-btn-input-modal {
            border: 1px solid #e0e0e0;
            width:90px;
            height: 25px;
            overflow: hidden;
        }
        .suixinpin-item-btn-input-modal a.sub{
            display:block;
            width: 25px;
            height: 25px;
            line-height: 25px;
            border-right: 1px solid #e0e0e0;
            text-align: center;
            color: #333;
            font-size: 18px;
        }
        .suixinpin-item-btn-input-modal input{
            display:block;
            width: 37px;
            height: 25px;
            line-height: 25px;
            margin: 0;
            padding: 0;
            border: none !important;
            text-align: center;
        }
        .suixinpin-item-btn-input-modal input:focus {
            outline: none;
        }
        .suixinpin-item-btn-input-modal a.add{
            display:block;
            width: 25px;
            height: 25px;
            line-height: 25px;
            border-left: 1px solid #e0e0e0;
            text-align: center;
            color: #333;
            font-size: 18px;
        }
        #suixinpin-modal {
            width: 1220px;
            height: 540px;
            background: #fff;
            border-radius: 8px;
            margin-left: -605px;
        }
        .suixinpin-modal-header {
            padding-left: 24px;
            padding-top: 24px;
            position: relative;
            padding-right: 14px;
        }

        .suixinpin-modal-title {
            width: 48px;
            height: 16px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            letter-spacing: 0;
            line-height: 16px;
        }

        .suixinpin-modal-search {
            position: absolute;
            left: 370px;
            top: 16px;
            display: flex;
            align-items: center;
            width: 480px;
        }

        .suixinpin-modal-search-inner {
            position: relative;
            width: 100%;
        }

        .suixinpin-modal-search-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            fill: #999;
        }

        .suixinpin-modal-search-input {
            width: 100%;
            height: 32px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            letter-spacing: 0;
            line-height: 14px;
            background: #ededed;
            border-radius: 4px;
            border: none;
            padding-left: 36px;
        }

        .suixinpin-modal-close {
            position: absolute;
            right: 26.2px;
            top: 26.2px;
            width: 24px;
            height: 24px;
            background: transparent;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .suixinpin-modal-body {
            position: absolute;
            top: 64px;
            bottom: 76px;
            left: 24px;
            right: 24px;
            overflow: auto;
        }

        .suixinpin-modal-footer {
            position: absolute;
            bottom: 20px;
            right: 24px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 100%;
        }

        .suixinpin-modal-footer-summary {
            margin-right: 20px;
            width: 102px;
            height: 14px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            letter-spacing: 0;
            line-height: 14px;
        }

        .suixinpin-modal-footer-total {
            margin-right: 24px;
            font-family: PingFangSC-Regular;
            font-size: 16px;
            color: #ff0402;
            letter-spacing: 0;
            line-height: 1;
            font-weight: 400;
        }

        .suixinpin-modal-footer-subtotal {
            width: 42px;
            height: 14px;
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            letter-spacing: 0;
            line-height: 14px;
        }

        .suixinpin-modal-footer-btn {
            border: none;
            width: 96px;
            height: 36px;
            border-radius: 4px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: 16px;
            letter-spacing: 0;
            line-height: 36px;
            text-align: center;
            cursor: pointer;
            background: #00b955;
            color: #ffffff;
        }

        .suixinpin-modal-body {
            overflow: auto;
        }

        /* 滚动条滑块 */
        .suixinpin-modal-body::-webkit-scrollbar-thumb {
            width: 6px;
            background: #d3d5d8;
            border-radius: 3px;
        }

        /* 滚动条轨道 */
        .suixinpin-modal-body::-webkit-scrollbar-track {
            background: #ffffff;
        }
    </style>
	<body>
        <div id="suixinpin-modal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
            <div class="modal-dialog">
                <div class="suixinpin-modal-header">
                    <span class="suixinpin-modal-title">
                        <span id="suiTitleModal">随心拼</span>
                    </span>
                    <span class="suixinpin-modal-search">
                    <div class="suixinpin-modal-search-inner">
                        <svg class="suixinpin-modal-search-icon" width="18" height="18" viewBox="0 0 1024 1024">
                        <path d="M832 832a32 32 0 0 1-22.624-9.376l-144.064-144.064A319.36 319.36 0 0 1 512 832C300.288 832 128 659.712 128 448S300.288 64 512 64s384 172.288 384 384c0 84.608-27.136 163.072-73.472 226.688l144.064 144.064A32 32 0 0 1 832 832zM512 128C335.36 128 192 271.36 192 448s143.36 320 320 320 320-143.36 320-320S688.64 128 512 128z" />
                        </svg>
                        <input type="text" placeholder="请输入商品名称" class="suixinpin-modal-search-input" />
                    </div>
                    </span>
                    <div class="suixinpin-modal-close">
                    <svg width="16" height="16" viewBox="0 0 18 18" fill="none">
                        <path d="M3 3L15 15M15 3L3 15" stroke="#666" stroke-width="2" stroke-linecap="round" />
                    </svg>
                    </div>
                </div>
                <div class="suixinpin-modal-body">
                    <div class="suixinpin-list" id="suixinpin_modal_list">

                    </div>
                </div>
                <input type="hidden" class="totalAmount_modal" value="${orderSettle.suiXinPinSkus.totalAmount}">
                <input type="hidden" class="totalNum_modal" value="${orderSettle.suiXinPinSkus.totalNum}">
                <input type="hidden" class="varietyNum_modal" value="${orderSettle.suiXinPinSkus.varietyNum}">

                <div class="suixinpin-modal-footer">
                    <span class="suixinpin-modal-footer-summary" id="modal_summary_goods">共0种/0件商品</span>
                    <span class="suixinpin-modal-footer-subtotal">小计：</span>
                    <span class="suixinpin-modal-footer-total" id="modal_summary_goods_red">￥0.00</span>
                    <div class="suixinpin-modal-footer-btn">选好了</div>
                </div>
            </div>
        </div>
    </body>
    <script type="text/javascript">
        $(function () {
            // 防抖函数
            function debounce(func, wait) {
                let timeout;
                return function() {
                    const context = this;
                    const args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(function() {
                        func.apply(context, args);
                    }, wait);
                };
            }
            const settleType = getUrlParam('settleType', '1');
            // const loadCount = getUrlParam('loadCount', '2');


            var oldSuiXinPinSkus = '[]';
            window.initSuixinpinModal = function(type) {
                $('.suixinpin-modal-search-input').val('');
                $('.suixinpin-modal-search-input').trigger('input');
                oldSuiXinPinSkus = sessionStorage.getItem("suiXinPinSkus");
                let shopInfoSxpList = ''
                if(settleType == 2) {
                    shopInfoSxpList = sessionStorage.getItem("shopInfoSxpList");
                }else {
                    const shopCode = getUrlParam('shopCodes',"00");
                    const skuId = getUrlParam('skuId',"");
                    shopInfoSxpList = JSON.stringify([{shopCode:shopCode,skuids:[parseInt(skuId) || 0]}])
                }
                
                currentPage = 1;
                hasMore = true;
                isLoading = false;
                keyword = '';

                getData(shopInfoSxpList,type);
            }

            // ====== 分页相关变量提升到全局作用域 ======
            var currentPage = 1;
            var pageSize = 20;
            var isLoading = false;
            var hasMore = true;
            var keyword = '';

            function getData(shopInfoSxpList,type) {
                let pares = JSON.parse(shopInfoSxpList);
                let suiXinPinSkusPares = JSON.parse(oldSuiXinPinSkus || "[]");
                // 过滤掉数量为0的项
                let filteredSkus = suiXinPinSkusPares.filter(item => item.quantity > 0);

                filteredSkus = filteredSkus.map(item=> item.skuId);
                if(type == 2) {
                    $('#suiTitleModal').text('顺手买一件')
                    var index=layer.load(2);
                    $('#suixinpin_modal_list').empty();
                    $.ajax({
                        url: '/merchant/center/order/buySomethingCasuallyQuery',
                        type: 'GET',
                        data: {
                            merchantId: $('#merchantId').val(),
                            buySomethingCasuallyInfo: JSON.stringify({
                                shopInfoSxpList: pares,
                                buySomethingCasuallySkus: filteredSkus, // 只传数量大于0的
                                isMore: true
                            })
                        },
                        success: function (data) {
                            if (data.code == 1000){
                                if (data.data && data.data.length > 0) {
                                    data.data.forEach(function(item, index) {
                                        var itemHtml = '<div class="suixinpin-modal-item suixinpin_modal_item" data-query="yes" data-id="' + (item.id || '') + '" data-rank="' + (index + 1) + '" data-exp_id="' + (item.exp_id || '') + '">' +
                                            '<div class="suixinpin-item-left">' +
                                                '<img src="${productImageUrl}/ybm/product/min/' + (item.imageUrl || '') + '" onerror="this.src=/static/images/default-big.png">' +
                                            '</div>' +
                                            '<div class="suixinpin-item-right">' +
                                                '<div class="modal-suixinpin-item-title">' + (item.showName || '') + '</div>' +
                                                '<div class="suixinpin-item-validity">有效期至' + (item.nearEffect || '') + '</div>' +
                                                '<span class="showMarketingSolag">' + (item.promoTag || '') + '</span>' +
                                                '<div class="suixinpin-item-content">' +
                                                    '<div class="suixinpin-item-price">' +
                                                        '<span class="show-price">¥' + (item.price || item.fob || '0.00') + '/' + (item.productUnit || '件') + '</span>' +
                                                    '</div>' +
                                                    '<div class="suixinpin-item-btn">' +
                                                        '<div class="suixinpin-item-btn-buy-modal" onclick="modalShowQuantityInput(this);window.suixinpin_sub_module_click(' + item.id + ',\'加购\',1,1)">' +
                                                            '<img src="/static/images/shoppingCar.png" alt="">' +
                                                        '</div>' +
                                                        '<div class="suixinpin-item-btn-input-modal" style="display: none;" data-id="' + (item.id || '') + '">' +
                                                            '<div class="qtData" style="display: none">' +
                                                                '<div class="expId" data-expId="' + (item.qtData ? item.qtData.expId || '' : '') + '"></div>' +
                                                                '<div class="gtListData" data-gtListData=\'' + (item.qtData ? (item.qtData.qtListData || '') : '') + '\'></div>' +
                                                                '<div class="gtSkuData" data-gtSkuData=\'' + (item.qtData ? (item.qtData.qtSkuData || '') : '') + '\'></div>' +
                                                                '<div class="rank" data-rank="' + (index + 1) + '"></div>' +
                                                                '<div class="scmId" data-scmId="' + (item.qtData ? (item.qtData.scmId || '') : '') + '"></div>' +
                                                                '<div class="productName" data-productName="' + (item.showName || '') + '"></div>' +
                                                            '</div>' +
                                                            '<a href="javascript:void(0);" class="sub fl" onclick="modalDecreaseQuantity(this)">-</a>' +
                                                            '<input ' +
                                                                'type="text" ' +
                                                                'value="0" ' +
                                                                'class="fl" ' +
                                                                'data-initial-value="' + (item.quantity || '') + '" ' +
                                                                'data-id="' + (item.id || '') + '" ' +
                                                                'data-price="' + (item.price || item.fob) + '" ' +
                                                                'data-sku-start-num="' + ((item.actPt || {}).skuStartNum || (item.actPgby || {}).skuStartNum || '1') + '" ' +
                                                                'data-show-name="' + (item.showName || '') + '" ' +
                                                                'data-promo-tag="' + (item.promoTag || '') + '" ' +
                                                                'data-mddata="' + (item.mddata ? item.mddata.replace(/"/g, '&quot;') : '') + '" ' +
                                                                'data-qtData="' + (item.qtData ? JSON.stringify(item.qtData).replace(/"/g, '&quot;') : '') + '" ' +
                                                                'data-mediumPackageNum="' + (item.mediumPackageNum || '') + '" ' +
                                                                'onchange="modalUpdateQuantityDebounced(this)" ' +
                                                                'data-type="' + ((item.actPt) ? '3' : ((item.actPgby) ? '5' : '1')) + '" ' +
                                                                'data-item-type="' + (item.type || '') + '" ' +
                                                                'data-isSplit="' + (item.isSplit) + '" ' +
                                                                'onblur="handleCheckValueModel(\'' + (item.id || '') + '\', this.getAttribute(\'data-sku-start-num\'),this)" ' +
                                                                'onclick="window.focus_input_click(' + item.id + ',2,this,1)" ' +
                                                                'onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,\'\')}else{this.value=this.value.replace(/\\D/g,\'\')}" ' +
                                                                'onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,\'0\')}else{this.value=this.value.replace(/\\D/g,\'\')}" ' +
                                                            '>' +
                                                            '<a href="javascript:void(0);" class="add fl" onclick="modalIncreaseQuantity(this);window.suixinpin_sub_module_click(' + item.id + ',\'加\',3,1)">+</a>' +
                                                        '</div>' +
                                                    '</div>' +
                                                '</div>' +
                                            '</div>' +
                                        '</div>';
                                        $('#suixinpin_modal_list').append(itemHtml);
                                    });
                                }
                                initializeModalQuantityInputs();
                                updateTotalSummary();
                            }
                            isLoading = false;
                            layer.close(index);
                            setTimeout(function () {
                                observeModalProductExposure();
                            }, 100);
                        }
                    });
                } else {
                    $('#suiTitleModal').text('随心拼')
                    var shopCodes = getUrlParam('shopCodes');
                    var isThirdCompany = getUrlParam('isThirdCompany');
                    function loadPage(page, keyword) {
                        if (isLoading || !hasMore) return;
                        isLoading = true;
                        var index = layer.load(2);
                        let data = {
                            tagList: "YBM_ACT_SUI_XIN_PIN",
                            spFrom: 8,
                            shopCodes: shopCodes,
                            excludeIds: filteredSkus.join(','),
                            pageNum: page,
                            pageSize: pageSize,
                            keyword: keyword || '',
                            isThirdCompany: isThirdCompany || ''
                        }
                        if(page > 1) {
                            let params = JSON.parse(sessionStorage.getItem("modelQtDataLocal"));
                            if(params.length > 0) {
                                data.scmId = params[0].scmId;
                            }
                        }
                        $.ajax({
                            url: '/pc/search/v1/productList',
                            type: 'POST',
                            contentType: 'application/x-www-form-urlencoded',
                            data: data,
                            success: function (data) {
                                if (data.code == 1000 && data.data && data.data.rows && data.data.rows.length > 0) {
                                    const curLength = $('#suixinpin_modal_list .suixinpin_modal_item').length;
                                    data.data.rows.forEach(function(item, index) {
                                        var itemHtml = '<div class="suixinpin-modal-item suixinpin_modal_item" data-query="yes" data-id="' + (item.id || '') + '" data-rank="' + (curLength +index + 1) + '" data-exp_id="' + (item.exp_id || '') + '">' +
                                            '<div class="suixinpin-item-left">' +
                                                '<img src="${productImageUrl}/ybm/product/min/' + (item.imageUrl || '') + '" onerror="this.src=/static/images/default-big.png">' +
                                            '</div>' +
                                            '<div class="suixinpin-item-right">' +
                                                '<div class="modal-suixinpin-item-title">' + (item.showName || '') + '</div>' +
                                                '<div class="suixinpin-item-validity">有效期至' + (item.effectStr || '') + '</div>' +
                                                '<span class="showMarketingSolag">' + (item.promoTag || '') + '</span>' +
                                                '<div class="suixinpin-item-content">' +
                                                    '<div class="suixinpin-item-price">' +
                                                        '<span class="show-price">¥' + ((item.actSuiXinPin && item.actSuiXinPin.suiXinPinPrice) || item.fob || '0.00') + '/' + (item.productUnit || '件') + '<span class="abandoned-price">¥' + (item.fob) + '</span>' +'</span>' +
                                                    '</div>' +
                                                    '<div class="suixinpin-item-btn">' +
                                                        '<div class="suixinpin-item-btn-buy-modal" onclick="modalShowQuantityInput(this);window.suixinpin_sub_module_click(' + item.id + ',\'加购\',1,1)">' +
                                                            '<img src="/static/images/shoppingCar.png" alt="">' +
                                                        '</div>' +
                                                        '<div class="suixinpin-item-btn-input-modal" style="display: none;" data-id="' + (item.id || '') + '">' +
                                                            '<div class="qtData" style="display: none">' +
                                                                '<div class="expId" data-expId="' + (item.qtData ? item.qtData.expId || '' : '') + '"></div>' +
                                                                '<div class="gtListData" data-gtListData=\'' + (item.qtData ? (item.qtData.qtListData || '') : '') + '\'></div>' +
                                                                '<div class="gtSkuData" data-gtSkuData=\'' + (item.qtData ? (item.qtData.qtSkuData || '') : '') + '\'></div>' +
                                                                '<div class="rank" data-rank="' + (curLength + index + 1) + '"></div>' +
                                                                '<div class="scmId" data-scmId="' + (item.qtData ? (item.qtData.scmId || '') : '') + '"></div>' +
                                                                '<div class="productName" data-productName="' + (item.showName || '') + '"></div>' +
                                                            '</div>' +
                                                            '<a href="javascript:void(0);" class="sub fl" onclick="modalDecreaseQuantity(this)">-</a>' +
                                                            '<input ' +
                                                                'type="text" ' +
                                                                'value="0" ' +
                                                                'class="fl" ' +
                                                                'data-initial-value="' + (item.quantity || '') + '" ' +
                                                                'data-id="' + (item.id || '') + '" ' +
                                                                'data-price="' + ((item.actSuiXinPin && item.actSuiXinPin.suiXinPinPrice) || item.fob) + '" ' +
                                                                'data-sku-start-num="' + ((item.actPt || {}).skuStartNum || (item.actPgby || {}).skuStartNum || '1') + '" ' +
                                                                'data-show-name="' + (item.showName || '') + '" ' +
                                                                'data-promo-tag="' + (item.promoTag || '') + '" ' +
                                                                'data-mddata="' + (item.mddata ? item.mddata.replace(/"/g, '&quot;') : '') + '" ' +
                                                                'data-mediumPackageNum="' + (item.mediumPackageNum || '') + '" ' +
                                                                'data-type="' + ((item.actPt) ? '3' : ((item.actPgby) ? '5' : '1')) + '" ' +
                                                                'data-item-type="' + (item.type || '') + '" ' +
                                                                'data-isSplit="' + (item.isSplit) + '" ' +
                                                                'onchange="modalUpdateQuantityDebounced(this)" ' +
                                                                'onclick="window.focus_input_click(' + item.id + ',2,this,1)" ' +
                                                                'onblur="handleCheckValueModel(\'' + (item.id || '') + '\', this.getAttribute(\'data-sku-start-num\'),this)" ' +
                                                                'onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,\'\')}else{this.value=this.value.replace(/\\D/g,\'\')}" ' +
                                                                'onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,\'0\')}else{this.value=this.value.replace(/\\D/g,\'\')}" ' +
                                                            '>' +
                                                            '<a href="javascript:void(0);" class="add fl" onclick="modalIncreaseQuantity(this);window.suixinpin_sub_module_click(' + item.id + ',\'加\',3,1)">+</a>' +
                                                        '</div>' +
                                                    '</div>' +
                                                '</div>' +
                                            '</div>' +
                                        '</div>';
                                        $('#suixinpin_modal_list').append(itemHtml);
                                    });
                                    // 用isEnd判断是否还有下一页
                                    if (data.data.isEnd === true) {
                                        hasMore = false;
                                    }
                                } else {
                                    hasMore = false;
                                }
                                isLoading = false;
                                layer.close(index);
                                initializeModalQuantityInputs();
                                updateTotalSummary();
                                setTimeout(function () {
                                    observeModalProductExposure();
                                }, 100);
                            },
                            error: function() {
                                isLoading = false;
                                layer.close(index);
                            }
                        });
                    }

                    // ====== 首次加载第一页 ======
                    loadPage(currentPage, keyword);

                    // ====== 监听下滑到底部自动加载下一页（无debounce，绑定前先解绑） ======
                    $('.suixinpin-modal-body').off('scroll').on('scroll', function() {
                        var $body = $(this)[0];
                        if ($body.scrollTop + $body.clientHeight >= $body.scrollHeight - 10) {
                            if (!isLoading && hasMore) {
                                currentPage++;
                                loadPage(currentPage, keyword);
                                console.log('滚动到底部，加载第 ' + currentPage + ' 页');
                            }
                        }
                    });

                    // ====== 监听搜索输入 ======
                    function handleSearch() {
                        keyword = $('.suixinpin-modal-search-input').val().trim();
                        currentPage = 1;
                        hasMore = true;
                        $('#suixinpin_modal_list').empty();
                        loadPage(currentPage, keyword);
                    }
                    $('.suixinpin-modal-search-input')
                    .off('input')
                    .on('keyup', function(e) {
                        if (e.key === 'Enter') handleSearch();
                    });
                }
            };
            // 用于记录已曝光的商品ID，避免重复曝光
            var exposedSet = new Set();
            var exposureQueue = [];
            var isReporting = false;
            function throttleExposureReport() {
                if (isReporting || exposureQueue.length === 0) return;
                isReporting = true;
                var productId = exposureQueue.shift();
                if (typeof window.productExposure === 'function') {
                    window.productExposure(productId, 1);
                    console.log('Modal product exposed:', productId);
                }
                setTimeout(function() {
                    isReporting = false;
                    throttleExposureReport(); // 继续处理队列
                }, 400); // 每200ms上报一次
            }
            // 商品曝光埋点：IntersectionObserver 方式
            function observeModalProductExposure() {
                // 检查浏览器是否支持IntersectionObserver
                if (!window.IntersectionObserver) {
                    // 如果不支持，使用备用方法进行曝光统计
                    fallbackExposureTracking();
                    return;
                }
                // 获取所有需要观察的商品项
                var items = document.querySelectorAll('.suixinpin-modal-item');
                if (!items || items.length === 0) return;
                // 创建IntersectionObserver实例
                var observer = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            var el = entry.target;
                            var productId = el.getAttribute('data-id');
                            // 确保productId存在且未被曝光过
                            if (productId && !exposedSet.has(productId)) {
                                exposedSet.add(productId);
                                exposureQueue.push(productId);
                                throttleExposureReport();
                                observer.unobserve(el);
                            }
                        }
                    });
                }, {
                    threshold: 0.1, // 10%可见即曝光
                    root: document.querySelector('.suixinpin-modal-body') // 指定滚动容器
                });
                // 开始观察所有商品项
                items.forEach(function(item) {
                    observer.observe(item);
                });
            }

            // 备用曝光跟踪方法（用于不支持IntersectionObserver的浏览器）
            function fallbackExposureTracking() {
                // 获取滚动容器
                var scrollContainer = document.querySelector('.suixinpin-modal-body');
                if (!scrollContainer) return;

                // 已曝光商品ID集合
                var exposedIds = new Set();

                // 检查元素是否在视口内
                function isElementInViewport(el) {
                    var rect = el.getBoundingClientRect();
                    var containerRect = scrollContainer.getBoundingClientRect();

                    return (
                        rect.top <= containerRect.bottom &&
                        rect.bottom >= containerRect.top &&
                        rect.left <= containerRect.right &&
                        rect.right >= containerRect.left
                    );
                }

                // 检查所有商品是否在视口内
                function checkVisibility() {
                    var items = document.querySelectorAll('.suixinpin-modal-item');
                    items.forEach(function(item) {
                        var productId = item.getAttribute('data-id');
                        if (productId && !exposedIds.has(productId) && isElementInViewport(item)) {
                            exposedIds.add(productId);
                            if (typeof window.productExposure === 'function') {
                                setTimeout(function() {
                                    window.productExposure(productId, 1); // isModal=1表示在弹窗中
                                    console.log('Modal product exposed:', productId);
                                }, 400); // 延时400ms以确保元素已渲染
                            }
                        }
                    });
                }

                // 初始检查
                checkVisibility();

                // 监听滚动事件
                scrollContainer.addEventListener('scroll', debounce(checkVisibility, 200));
            }

            var currentPage = 1;
            var pageSize = 20;
            var isLoading = false;
            var hasMore = true;
            var keyword = '';

            // 从地址栏获取参数的函数
            function getUrlParam(key, defaultValue = '') {
                // 优先级1：使用 URLSearchParams（现代浏览器）
                if (window.URLSearchParams) {
                    const params = new URLSearchParams(window.location.search);
                    const value = params.get(key);
                    return value !== null ? value : defaultValue;
                }
                // 优先级2：旧浏览器解析方案（兼容IE）
                const query = window.location.search.substring(1);
                const pairs = query.split('&');
                for (const pair of pairs) {
                    const [k, v] = pair.split('=');
                    if (decodeURIComponent(k) === key) {
                        return decodeURIComponent(v) || defaultValue;
                    }
                }
                return defaultValue;
            }

            // 创建防抖版本的更新数量函数
            window.modalUpdateQuantityDebounced = debounce(function(element) {
                modalUpdateQuantity(element);
            }, 300);

            // 更新数量函数
            window.modalUpdateQuantity = function (element) {
                let value = parseInt(element.value);
                if (isNaN(value) || value < 1) {
                    element.value = 1;
                    value = 1;
                }
                updateTotalSummary();
            }

            // 增加数量
            window.modalIncreaseQuantity = function(element) {
                const input = element.parentNode.querySelector('input');
                const id = input.getAttribute('data-id');
                const type = input.getAttribute('data-type') || 1; // 获取type
                const itemType = input.getAttribute('data-item-type');
                const value = modalAddToCart(element)
                if(type == "1") {
                    input.value = value;
                    modalUpdateQuantityDebounced(input);
                }else {
                    window.modalPtChange(id, value, element, '加', type);
                }
            }
            //
            // 减少数量
            window.modalDecreaseQuantity = function(element) {
                const input = element.parentNode.querySelector('input');
                const skuStartNum = parseInt(input.getAttribute('data-sku-start-num')) || 1;
                const isSplit = input.getAttribute('data-isSplit');
                const skuId = input.getAttribute('data-id');
                const middpacking = parseInt(input.getAttribute('data-mediumPackageNum')) || 1;
                let value = parseInt(input.value) || 0;
                // 确定步长
                let step = 1;
                if(isSplit == 0 && middpacking > 0){
                    step = middpacking;
                    if(value - step < middpacking) {
                        // 当数量小于等于起拼数时，设为0并恢复到购物车图标状态
                        const inputDiv = element.parentNode;
                        const buyBtn = inputDiv.parentNode.querySelector('.suixinpin-item-btn-buy-modal');
                        input.value = 0;
                        inputDiv.style.display = 'none';
                        buyBtn.style.display = 'block';
                        updateTotalSummary();
                        return
                    }
                }
                // 如果当前值小于等于起拼数，则不允许再减
                if (value <= skuStartNum && value - step < skuStartNum) {
                    // 当数量小于等于起拼数时，设为0并恢复到购物车图标状态
                    const inputDiv = element.parentNode;
                    const buyBtn = inputDiv.parentNode.querySelector('.suixinpin-item-btn-buy-modal');
                    input.value = 0;
                    inputDiv.style.display = 'none';
                    buyBtn.style.display = 'block';
                    updateTotalSummary();
                } else {
                    // 正常减少
                     const type = input.getAttribute('data-type') || 1;
                    if(type != "1") {
                        window.modalPtChange(skuId, value - step, input, '减', type)
                    }else {
                        input.value = value - step;
                        modalUpdateQuantityDebounced(input);
                        updateTotalSummary();
                    }
                }
            }
            function addShowQuantityInput(element) {
                const inputDiv = element.parentNode.querySelector('.suixinpin-item-btn-input-modal');
                const input = inputDiv.querySelector('input');
                const mediumPackageNum = parseInt(input.getAttribute('data-mediumPackageNum'));
                const skuId = input.getAttribute('data-id');
                const isSplit = input.getAttribute('data-isSplit');
                const skuStartNum = parseInt(input.getAttribute('data-sku-start-num')) || 1;
                const type = input.getAttribute('data-type') || 1;
                let value = skuStartNum
                if(type == 1) {
                    value = mediumPackageNum
                }else if (isSplit == 0 && mediumPackageNum > 1 && skuStartNum % mediumPackageNum !== 0) {
                    value = Math.ceil(skuStartNum / mediumPackageNum) * mediumPackageNum;
                }
                return value
            }
            // 更改数量输入框
            window.modalShowQuantityInput = function(element) {
                const inputDiv = element.parentNode.querySelector('.suixinpin-item-btn-input-modal');
                const input = inputDiv.querySelector('input');
                const skuId = input.getAttribute('data-id');
                const type = input.getAttribute('data-type') || 1; // 获取type
                const itemType = input.getAttribute('data-item-type');
                if(type == 1) {
                    element.style.display = 'none';
                    inputDiv.style.display = 'flex';
                    const value = addShowQuantityInput(element);
                    input.value = value;
                    modalUpdateQuantityDebounced(input);
                }else {
                    const value = addShowQuantityInput(element);
                    window.modalPtChange(skuId, value, element, '加购', type)
                }
            };
            // 添加handleCheckValue函数
            window.handleCheckValueModel = function(id, skuStartNum,element) {
                const input = document.querySelector('input[data-id="'+id+'"]');
                if (!input) return;

                let num = parseFloat(input.value);
                if (isNaN(num)) num = 0;

                const isSplit = input.getAttribute('data-isSplit');
                const middpacking = parseInt(input.getAttribute('data-mediumPackageNum')) || 1;
                const type = input.getAttribute('data-type') || 1; // 获取type

                // 如果数量小于起拼数，则设为起拼数
                if (num < skuStartNum) {
                    num = skuStartNum;
                }

                // 如果不可拆零，确保数量是中包装的倍数
                if (isSplit == 0 && middpacking > 0) {
                    const remainder = num % middpacking;
                    if (remainder > 0) {
                        num = num - remainder;
                        // 如果结果为0，则至少保留一个中包装数量
                        if (num < middpacking) {
                            num = middpacking;
                        }
                    }
                }
                if(type == 1) {
                    element.value = num;
                    modalUpdateQuantityDebounced(element);
                }else {
                    window.modalPtChange(id, num, input, 'input', type);
                }
            }

            // 更新总计
            function updateTotalSummary() {
                var totalAmount = 0; // 商品总金额
                var totalNum = 0;    // 商品总数量
                var varietyNum = 0;  // 商品品种数

                // 只统计弹窗内所有已显示的输入框
                var $inputs = $('.suixinpin-item-btn-input-modal:visible input');
                $inputs.each(function() {
                    var quantity = parseInt($(this).val(), 10) || 0;
                    var price = parseFloat($(this).attr('data-price')) || 0;
                    if (quantity > 0) {
                        totalNum += quantity;
                        totalAmount += price * quantity;
                        varietyNum += 1;
                    }
                });

                $('#modal_summary_goods').text('共' + varietyNum + '种/' + totalNum + '件商品');
                $('#modal_summary_goods_red').text('￥' + totalAmount.toFixed(2));
            }



            // 创建 suiXinPinSkus 对象
            function createSuiXinPinSkus() {
                const suiXinPinSkus = [];
                const qtDataLocal = JSON.parse(sessionStorage.getItem("modelQtDataLocal")) || [];
                const mddataLocal = JSON.parse(sessionStorage.getItem("mddataLocal")) || [];
                // 获取所有显示的输入框
                const visibleInputs = document.querySelectorAll('.suixinpin-item-btn-input-modal input');

                visibleInputs.forEach(function(input) {
                    const quantity = parseInt(input.value) || 0;
                    // 检查是否已经有相同skuId的项
                    const skuId = parseInt(input.getAttribute('data-id')) || 0;
                    const existingIndex = suiXinPinSkus.findIndex(item => item?.skuId === skuId);
                    const qtDataElement = input.closest('.suixinpin-item-btn-input-modal').querySelector('.qtData');
                    let qtData = null;
                    if (qtDataElement) {
                        let params = JSON.parse(qtDataElement.querySelector('.gtSkuData')?.dataset?.gtskudata || '{}');
                        params.rank = qtDataElement.querySelector('.rank')?.dataset?.rank || '';
                        qtData = {
                            expId: qtDataElement.querySelector('.expId')?.dataset?.expid || '',
                            qtListData: JSON.parse(qtDataElement.querySelector('.gtListData')?.dataset?.gtlistdata || '{}'),
                            qtSkuData: params,
                            rank: qtDataElement.querySelector('.rank')?.dataset?.rank || '',
                            scmId: qtDataElement.querySelector('.scmId')?.dataset?.scmid || '',
                            productName: qtDataElement.querySelector('.productName')?.dataset?.productname || '',
                        };
                    }
                    if(qtData) {
                        qtData.skuId = skuId;
                        const qtDataIndex = qtDataLocal.findIndex(item => item.skuId === skuId);
                        if (qtDataIndex >= 0) {
                            // 更新已存在的对象
                            qtDataLocal[qtDataIndex] = qtData;
                        } else {
                            // 添加新对象
                            qtDataLocal.push(qtData);
                        }
                    }
                    const mddata = JSON.parse(input.getAttribute('data-mddata') || '{}');
                    if(Object.keys(mddata).length > 0) {
                        mddata.skuId = skuId;
                        const mddataIndex = mddataLocal.findIndex(item => item.skuId === skuId);
                        if (mddataIndex >= 0) {
                            // 更新已存在的对象
                            mddataLocal[mddataIndex] = mddata;
                        } else {
                            mddataLocal.push(mddata);
                        }
                    }
                    if (existingIndex >= 0) {
                        // 更新已存在的项
                        suiXinPinSkus[existingIndex].quantity = quantity;
                    } else if (quantity > 0) {
                        // 只有当quantity > 0时才添加新项
                        suiXinPinSkus.push({
                            skuId: skuId,
                            quantity: quantity,
                            type: settleType, // 默认类型
                            skuStartNum: parseInt(input.getAttribute('data-sku-start-num')) || 1,
                            mddata: "",
                            showName: input.getAttribute('data-show-name') || "",
                            promoTag: input.getAttribute('data-promo-tag') || "",
                            qtData: qtData ? qtData : input.getAttribute('data-qtData') || "",
                        });
                    }
                });

                // 将对象存储到 sessionStorage 中
                sessionStorage.setItem("modelQtDataLocal", JSON.stringify(qtDataLocal));
                sessionStorage.setItem("mddataLocal", JSON.stringify(mddataLocal));
                return suiXinPinSkus;
            }

            var respondSuiXinPinSkus = '[]';

            // 初始化数量输入框
            function initializeModalQuantityInputs() {
                const items = document.querySelectorAll('.suixinpin-modal-item');
                items.forEach(item => {
                    const input = item.querySelector('.suixinpin-item-btn-input-modal input');
                    const buyBtn = item.querySelector('.suixinpin-item-btn-buy-modal');
                    const inputDiv = item.querySelector('.suixinpin-item-btn-input-modal');

                    const initialValue = input.getAttribute('data-initial-value');
                    if (initialValue && initialValue !== '') {
                        input.value = initialValue;
                        buyBtn.style.display = 'none';
                        inputDiv.style.display = 'flex';
                    }
                });
                respondSuiXinPinSkus = createSuiXinPinSkus();
                // observeProductExposure(); // 新增：初始化时绑定曝光埋点
            }

            function getAllUrlParams() {
                const params = {};
                const queryString = window.location.search.substring(1);

                if (window.URLSearchParams) {
                    new URLSearchParams(window.location.search).forEach((value, key) => {
                        params[key] = value;
                    });
                    return params;
                }

                const pairs = queryString.split('&');
                for (const pair of pairs) {
                    if (!pair) continue; // 跳过空字符串
                    const [key, value] = pair.split('=');
                    const decodedKey = decodeURIComponent(key);
                    const decodedValue = decodeURIComponent(value || '');
                    params[decodedKey] = decodedValue;
                }

                return params;
            }
            // 重加载页面
            function reloadPage() {
                sessionStorage.setItem('scrollPosition', $(window).scrollTop());
                const urlParams = getAllUrlParams();
                const suiXinPinSkusStr = sessionStorage.getItem("suiXinPinSkus");
                let skus = suiXinPinSkusStr ? JSON.parse(suiXinPinSkusStr) : [];
                sessionStorage.setItem("addDataNext",JSON.stringify(skus))
                skus = skus.map(({ mddata, qtData, skuId, quantity, type }) => ({
                    skuId,
                    quantity,
                    type
                }));
                urlParams.loadCount = 1
                urlParams.suiXinPinSkus = JSON.stringify(skus);
                const currentUrl = window.location.href;
                const newUrl = currentUrl.split('?')[0] + '?' + new URLSearchParams(urlParams).toString();
                window.location.replace(newUrl)
            }

            // 搜索防抖包装
            var handleModalSearch = debounce(function() {
                var keyword = $('.suixinpin-modal-search-input').val().trim().toLowerCase();
                // 遍历所有商品项，按关键字过滤显示
                $('.suixinpin_modal_item').each(function() {
                    var showName = $(this).find('.modal-suixinpin-item-title').text().toLowerCase();
                    if (!keyword || showName.indexOf(keyword) > -1) {
                        $(this).css('display', 'flex');
                    } else {
                        $(this).css('display', 'none');
                    }
                });
                updateTotalSummary();
            }, 200);

            if(settleType == 2){
                $('.suixinpin-modal-search-input').on('input', handleModalSearch);
            }


            // 确定按钮点击事件
            $('.suixinpin-modal-footer-btn').on('click', function() {
                $('#suixinpin-modal').modal("hide");
                exposedSet = new Set();
                let qtDataLocal = JSON.parse(sessionStorage.getItem("qtDataLocal")) || [];
                let modelQtDataLocal = JSON.parse(sessionStorage.getItem("modelQtDataLocal")) || [];
                var resultSuiXinPinSkus = createSuiXinPinSkus();

                // 过滤掉quantity为0或null的项
                resultSuiXinPinSkus = resultSuiXinPinSkus.filter(item => item && item.quantity > 0);
                //选中的
                const temp = modelQtDataLocal.filter((item) => resultSuiXinPinSkus.some(it => it.skuId == item.skuId));
                temp.forEach(it => {
                    if (qtDataLocal.every(item => item.skuId != it.skuId)) {
                        qtDataLocal.push(it);
                    }
                    qtDataLocal.forEach((item, idx) => {
                        if(item.skuId == it.skuId) {
                            qtDataLocal[idx] = it;
                        }
                    })
                })

                sessionStorage.setItem("qtDataLocal", JSON.stringify(qtDataLocal));

                if (JSON.stringify(resultSuiXinPinSkus) !== JSON.stringify(respondSuiXinPinSkus)) {
                    // 只保存数量大于0的商品到sessionStorage
                    var validSkus = resultSuiXinPinSkus.filter(function(item) {
                        return item && item.quantity > 0;
                    });

                    // 合并oldSuiXinPinSkus中validSkus没有且数量大于0的项
                    try {
                        var oldArr = [];
                        if (oldSuiXinPinSkus) {
                            oldArr = JSON.parse(oldSuiXinPinSkus);
                        }
                        oldArr.forEach(function(oldItem) {
                            if (oldItem && oldItem.quantity > 0 && !validSkus.some(function(v) { return v.skuId === oldItem.skuId; })) {
                                validSkus.push(oldItem);
                            }
                        });
                    } catch (e) { /* ignore */ }

                    sessionStorage.setItem("suiXinPinSkus", JSON.stringify(validSkus));
                }
                reloadPage();
            });

            // 关闭弹窗
            $('.suixinpin-modal-close').on('click', function() {
                if (confirm('关闭弹框后，已选商品不会保留，确定要关闭吗？')) {
                    $('#suixinpin-modal').modal("hide");
                    $('#suixinpin_modal_list').empty();
                    exposedSet = new Set();
                }
                $('.suixinpin-modal-search-input').off('input');
                $('.suixinpin-modal-body').off('scroll');
            });

            // 封装数量加购
            function modalAddToCart(element) {
                const input = element.parentNode.querySelector('input');
                const mediumPackageNum = parseInt(input.getAttribute('data-mediumPackageNum'));
                const skuStartNum = parseInt(input.getAttribute('data-sku-start-num'));
                let value = parseInt(input.value);
                if(isNaN(value)) {
                    if(input.getAttribute('data-item-type') == "1") {
                        value = mediumPackageNum;
                    }else if (isSplit == 0 && mediumPackageNum > 1 && skuStartNum % mediumPackageNum !== 0) {
                        value = Math.ceil(skuStartNum / mediumPackageNum) * mediumPackageNum;
                    }else {
                        value = skuStartNum;
                    }
                }
                let countNum = 1;
                if(mediumPackageNum) {
                    value += mediumPackageNum;
                }else {
                    value += countNum;
                }
                return value;
            }

            // 拼团加购动态更新拼团价
            window.modalPtChange = function (ptId, num,element,text,type = 3) {
                var merchantId = $("#merchantId").val();
                $.ajax({
                    url: "/merchant/center/cart/group/changeCart",
                    type: "POST",
                    dataType: "json",
                    data: {
                        merchantId: merchantId,
                        quantity: num,
                        skuId: ptId,
                        bizType: type
                    },
                    success: function(res){
                        if(res.status == "success"){
                            if (res.data.message) {
                                $.alert({
                                    title: '提示',
                                    body: res.data.message
                                });
                                if(text == 'input') {
                                    if(!element.value) {
                                        modalUpdateQuantityDebounced(element);
                                    }else {
                                        let value = addShowQuantityInput(element);
                                        element.value = value;
                                        modalUpdateQuantityDebounced(element);
                                    }
                                }
                            }else {
                                let value
                                if(text == '加') {
                                    const input = element.parentNode.querySelector('input');
                                    const value = modalAddToCart(element);
                                    input.value = value;
                                    // 触发更新
                                    modalUpdateQuantityDebounced(input);
                                }else if (text == '加购') {
                                    value = addShowQuantityInput(element);
                                    element.style.display = 'none';
                                    const inputDiv = element.parentNode.querySelector('.suixinpin-item-btn-input-modal');
                                    inputDiv.style.display = 'flex';
                                    const input2 = inputDiv.querySelector('input');
                                    input2.value = value;
                                    // 触发更新
                                    modalUpdateQuantityDebounced(input2);
                                }else if (text == 'input') {
                                    element.value = num;
                                    modalUpdateQuantityDebounced(element);
                                }else if(text == '减') {
                                    element.value = num;
                                    modalUpdateQuantityDebounced(element);
                                    updateTotalSummary();
                                }
                            }
                        }else {
                            $.alert({
                                title: '提示',
                                body: res.errorMsg
                            });
                        }
                    },
                    error: function(){
                        $.alert({
                            title: '提示',
                            body: '加购异常'
                        });
                    }
                });
            }
        });
    </script>
</html>
