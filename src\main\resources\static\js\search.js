$(function() {
	var changjZhan = $('#cjZhan').val();
	//当选中的厂家是展开状态下时，厂家默认展开
	if (parseInt(changjZhan) == 1){
		$(".zhan").css("display","none");
		$(".shou").css("display","inline-block");
		$(".default-more").css("display","block");
		$(".default").css("display","none");
	}

	//商家的是否展开状态
	var merchantZhanValue = $('#merchantZhan').val();
	if (parseInt(merchantZhanValue) == 1){
		$(".zhan-mer").css("display","none");
		$(".shou-mer").css("display","inline-block");
		$(".default-more-mer").css("display","block");
		$(".default-mer").css("display","none");
	}

	//规格的是否展开状态
	var specZhanValue = $('#specZhan').val();
	if (parseInt(specZhanValue) == 1){
		$(".zhan-spec").css("display","none");
		$(".shou-spec").css("display","inline-block");
		$(".default-more-spec").css("display","block");
		$(".default-spec").css("display","none");
	}

	var erjZhan = $('#ejZhan').val();
	//当选中的二级分类是展开状态下时，二级类目默认展开
	if (parseInt(erjZhan) == 1){
		$(".zhan-er").css("display","none");
		$(".shou-er").css("display","inline-block");
		$(".default-more-ejlm").css("display","block");
		$(".default-ejlm").css("display","none");
	}
	var sanjZhan = $('#sjZhan').val();
	//当选中的三级分类是展开状态下时，三级类目默认展开
	if (parseInt(sanjZhan) == 1){
		$(".zhan-san").css("display","none");
		$(".shou-san").css("display","inline-block");
		$(".default-more-sjlm").css("display","block");
		$(".default-sjlm").css("display","none");
	}

	/* 显示收藏*/
	$(".mrth li").hover(
		function() {
			$(this).find(".showorno").css("display", "block");
		},
		function() {
			$(this).find(".showorno").css("display", "none");
		}
	);



	/*滚动置顶*/
	$(window).scroll(function() {
		var a = document.getElementById("searchBox").offsetTop;
		if(a >= $(window).scrollTop() && a < ($(window).scrollTop() + $(window).height())) {
			$(".topsearcher").css("display", "none")
		} else {
			$(".topsearcher").css("display", "block")
		}
	});

	/*减操作*/
	$(".sub").click(function() {
		var step = 1;
		var me = $(this),
			txt = me.next(":text");
		var val = parseFloat(txt.val());
		var isSplit = txt.attr("isSplit");
		var middpacking = txt.attr("middpacking");
		var skuStartNum = txt.attr("skuStartNum");
		if(isSplit == 0){
			step = parseFloat(middpacking);
		}
		var num = 0;
		if(!isNaN(val)){
			num = val;
		}
		// 加购数<=拼团起拼数
		if (skuStartNum && (val < skuStartNum || val == skuStartNum)) {
			return;
		}
		if(num <= step) {
			txt.val(0);
		} else {
			txt.val(num - step);
		}
		var ptId = me.attr('is-pt-id');
		if (ptId) {
			ptChange(ptId, num - step);
		}
	});
	/*加操作:
	 *不管是否中包装： 都按照中包装数量去加
	 */
	setTimeout(function () {
        $(".add").unbind("click");
        $(".add").click(addClickEvent);
    },40);
    function addClickEvent(){
        var step = 10;
        var me = $(this),
            txt = me.prev(":text");
        var val = parseFloat(txt.val());
        var isSplit = txt.attr("isSplit");
        var middpacking = txt.attr("middpacking");
        step = parseFloat(middpacking);
        var num = 0;
        if(!isNaN(val)){
            num = val;
        }
        txt.val(num + step);
		var ptId = me.attr('is-pt-id');
		if (ptId) {
			ptChange(ptId, num + step);
		}
    };
    $(".add").click(addClickEvent);

	/*展开收起*/
	$(".zhan").click(function () {
		$(this).css("display","none");
		$(".shou").css("display","inline-block");
		$(".default-more").css("display","block");
		$(".default").css("display","none");
	});
	$(".shou").click(function () {
		$(this).css("display","none");
		$(".zhan").css("display","inline-block");
		$(".default-more").css("display","none");
		$(".default").css("display","block");
	});

	$(".zhan-mer").click(function () {
		$(this).css("display","none");
		$(".shou-mer").css("display","inline-block");
		$(".default-more-mer").css("display","block");
		$(".default-mer").css("display","none");
	});
	$(".shou-mer").click(function () {
		$(this).css("display","none");
		$(".zhan-mer").css("display","inline-block");
		$(".default-more-mer").css("display","none");
		$(".default-mer").css("display","block");
	});

	$(".zhan-spec").click(function () {
		$(this).css("display","none");
		$(".shou-spec").css("display","inline-block");
		$(".default-more-spec").css("display","block");
		$(".default-spec").css("display","none");
	});
	$(".shou-spec").click(function () {
		$(this).css("display","none");
		$(".zhan-spec").css("display","inline-block");
		$(".default-more-spec").css("display","none");
		$(".default-spec").css("display","block");
	});

	/*多选*/
	$(".duoxuan").click(function () {
		$(".mulchoose").css("display","block");
		$(".default").css("display","none");
		$(".default-more").css("display","none");
		$(".duoxuan").css("display","none");
		$(".zhan").css("display","none");
		$(".shou").css("display","none");
	});

	/*提交*/
	$(".tj").click(function () {
		$(".mulchoose").css("display","none");
		$(".default").css("display","block");

		$(".duoxuan").css("display","inline-block");
		$(".zhan").css("display","inline-block");
		$(".shou").css("display","none");
	});

	/*取消*/
	$(".qx").click(function () {
		$(".mulchoose").css("display","none");
		$(".default").css("display","block");

		$(".duoxuan").css("display","inline-block");
		$(".zhan").css("display","inline-block");
		$(".shou").css("display","none");
	});

	/*二级类目展开收起*/
	/*展开收起*/
	$(".zhan-er").click(function () {
		$(this).css("display","none");
		$(".shou-er").css("display","inline-block");
		$(".default-more-ejlm").css("display","block");
		$(".default-ejlm").css("display","none");
	});
	$(".shou-er").click(function () {
		$(this).css("display","none");
		$(".zhan-er").css("display","inline-block");
		$(".default-more-ejlm").css("display","none");
		$(".default-ejlm").css("display","block");
	});



	/*三级类目展开收起*/
	/*展开收起*/
	$(".zhan-san").click(function () {
		$(this).css("display","none");
		$(".shou-san").css("display","inline-block");
		$(".default-more-sjlm").css("display","block");
		$(".default-sjlm").css("display","none");
	});
	$(".shou-san").click(function () {
		$(this).css("display","none");
		$(".zhan-san").css("display","inline-block");
		$(".default-more-sjlm").css("display","none");
		$(".default-sjlm").css("display","block");
	});

	// /*激活列表模式*/
	// $(".lbfn").click(function () {
	// 	$(this).addClass("active");
	// 	$(".dtfn").removeClass("active");
	// 	$(".listmode").css("display","block");
	// 	$(".mrth-new").css("display","none");
	// });
    //
	// /*激活大图模式*/
	// $(".dtfn").click(function () {
	// 	$(this).addClass("active");
	// 	$(".lbfn").removeClass("active");
	// 	$(".listmode").css("display","none");
	// 	$(".mrth-new").css("display","block");
	// });

	showCollect();

	/*价格区间*/
	$('.ss-price').mouseover(function(){
		$('.ss-range').css('display','block')
	});
	$('.ss-range').mouseleave(function(){
		$('.ss-range').css('display','none')
	});

	//价格区间确认按钮点击事件
	$("#ss-confirm").click(function () {
		var minPrice = $("#minPrice").val();
		var maxPrice = $("#maxPrice").val();
		if (minPrice == ''){
            minPrice=0;
        }
        if (maxPrice != ''){
            //当最小价格大于最大价格时，最小价格和最大价格交换数值
            if (parseInt(minPrice)>parseInt(maxPrice)){
                var a = minPrice;
                minPrice = maxPrice;
                maxPrice = a;
            }
        }else{
			maxPrice=9999;
		}
        var path = urlPath;
		path = path.replace("minPrice="+minPrice,"");
		path = path.replace("maxPrice="+maxPrice,"");
		//上一步执行替换操作是为了防止下一步操作时有相同值直接设置为空了
        path = appendParamToUrl(path,"minPrice",minPrice);
        path = appendParamToUrl(path,"maxPrice",maxPrice);
        window.location.href=path;
	});
	//价格区间清空按钮点击事件
	$("#ss-clear").click(function () {
		$("#minPrice").val('');
		$("#maxPrice").val('');
	});

});
// 拼团加购动态更新拼团价
function ptChange(ptId, num, type=4) {
	var merchantId = $("#merchantId").val();
	$.ajax({
		url: "/merchant/center/cart/group/changeCart",
		type: "POST",
		dataType: "json",
		data: {
			merchantId: merchantId,
			quantity: num,
			skuId: ptId,
			bizScene: type, //批购3 拼团4
		},
		success: function(res){
			if(res.status == "success"){
				$("#ptPrice_"+ptId).html(res.data.price);
				$("#buyNumDl_"+ptId).val(res.data.qty);
				if (res.data.message) {
					$.alert({
						title: '提示',
						body: res.data.message
					});
				}
			}else {
				$.alert({
					title: '提示',
					body: res.errorMsg
				});
			}
		},
		error: function(){
			$.alert({
				title: '提示',
				body: '加购异常'
			});
		}
	});
}
// 加购数输入框失焦时校验
function handleCheckValue(id, skuStartNum, type=4) {
	var me = $(this),
		txt = me.prev(":text");
	var num = parseFloat($("#buyNumDl_"+id).val());
	var isSplit = txt.attr("isSplit");
	var middpacking = txt.attr("middpacking");
	if (num < skuStartNum) {
		num = skuStartNum;
		// return false;
	}
	if (isSplit == 0) {
		var remainder = num % middpacking;
		if (remainder > 0) {
			num = num - remainder;
		}
	}
	$("#buyNumDl_"+id).val(num);
	ptChange(id, num, type);

}
/*点击厂家*/
function searchProdctByCJ(name){
	var cjZhan = 2;
	var path = urlPath;
	path = appendParamToUrl(path,"manufacturer",name);
	path = appendParamToUrl(path,"cjZhan",cjZhan);
	window.location.href=path;
}
/*点击展开的厂家*/
function searchProdctByCJ1(name){
	var cjZhan = 1;
	var path = urlPath;
	path = path.replace("cjZhan=1","");
	path = appendParamToUrl(path,"manufacturer",name);
	path = appendParamToUrl(path,"cjZhan",cjZhan);
	window.location.href=path;
}

function getUrlParam(name) {
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
	var r = window.location.search.substr(1).match(reg);  //匹配目标参数
	if (r != null) return decodeURI(r[2]); return null; //返回参数值
}

/*点击商家*/
function searchProdctByMerchant(shopCode){
	var shopCodes = getUrlParam('shopCodes')
	var list = []
	if (shopCodes) {
		list = shopCodes.split(',')
		if (list && list.length) {
			var index = list.indexOf(shopCode)
			if (index !== -1) {
				list.splice(index, 1)
			} else {
				list.push(shopCode)
			}
		}
	}
	var merchantZhan = 2;
	var path = urlPath;
	var isThirdCompany = getUrlParam('isThirdCompany');
	if (isThirdCompany) {
		path = path.replace((path.indexOf(".htm?") > 0 ? '&' : '?') + 'isThirdCompany=' + isThirdCompany, '')
	}
	path = appendParamToUrl(path,"shopCodes", shopCodes && list.length ? list.join(',') : shopCode);
	path = appendParamToUrl(path,"merchantZhan", merchantZhan);
	window.location.href=path;
}
/*点击展开的商家*/
function searchProdctByMerchant1(shopCode){
	var shopCodes = getUrlParam('shopCodes')
	var list = []
	if (shopCodes) {
		list = shopCodes.split(',')
		if (list && list.length) {
			var index = list.indexOf(shopCode)
			if (index !== -1) {
				list.splice(index, 1)
			} else {
				list.push(shopCode)
			}
		}
	}
	var merchantZhan = 1;
	var path = urlPath;
	var isThirdCompany = getUrlParam('isThirdCompany');
	if (isThirdCompany) {
		path = path.replace((path.indexOf(".htm?") > 0 ? '&' : '?') + 'isThirdCompany=' + isThirdCompany, '')
	}
	path = path.replace("merchantZhan=1","");
	path = appendParamToUrl(path,"shopCodes", shopCodes && list.length ? list.join(',') : shopCode);
	path = appendParamToUrl(path,"merchantZhan", merchantZhan);
	window.location.href=path;
}
/*点击规格*/
function searchProdctBySpec(name) {
	var specs = getUrlParam('currentSpec');
	// specs = decodeURI(specs)
	var list = [];
	if (specs) {
		specs = specs.replace(/\%/g, '%25');
		specs = decodeURIComponent(specs)
		list = specs.split(',')
		if (list && list.length) {
			var index = list.indexOf(name)
			if (index !== -1) {
				list.splice(index, 1)
			} else {
				list.push(name)
			}
		}
	}
	var path = urlPath;
	var specZhan = 2;
	// path = appendParamToUrl(path,"currentSpec", name);
	path = appendParamToUrl(path,"currentSpec", specs && list.length ? list.join(',') : name);
	path = appendParamToUrl(path,"specZhan", specZhan);
	// path = encodeURI(path)
	window.location.href=path;
}

function searchProdctBySpec1(name) {
	var specs = getUrlParam('currentSpec');
	// specs = decodeURI(specs)
	var list = [];
	if (specs) {
		specs = specs.replace(/\%/g, '%25');
		specs = decodeURIComponent(specs)
		list = specs.split(',')
		if (list && list.length) {
			var index = list.indexOf(name)
			if (index !== -1) {
				list.splice(index, 1)
			} else {
				list.push(name)
			}
		}
	}
	var path = urlPath;
	var specZhan = 1;
	// path = appendParamToUrl(path,"currentSpec", name);
	path = appendParamToUrl(path,"currentSpec", specs && list.length ? list.join(',') : name);
	path = appendParamToUrl(path,"specZhan", specZhan);
	// path = encodeURI(path)
	window.location.href=path;
}


/*点击一级分类*/
function searchProdctByYj(id){
	var path = urlPath;
	//当选择分类时，清空其他筛选条件
	var manufacturer = $("#manufacturer").val();
	var drugClassification = $("#drugClassification").val();
	if (drugClassification != null || drugClassification != ''){
		path = path.replace("drugClassification="+drugClassification,"");
	}
	if (manufacturer != null || manufacturer != ''){
		path = path.replace("manufacturer="+manufacturer,"");
	}
	var minPrice = $("#minPrice").val();
	var maxPrice = $("#maxPrice").val();
	if (minPrice == '' || minPrice == null){
		minPrice=0;
	}
	if (maxPrice == '' || maxPrice == null){
		maxPrice=9999;
	}
	path = path.replace("minPrice="+minPrice,"");
	path = path.replace("maxPrice="+maxPrice,"");
	path = path.replace("has_stock=1","");
	path = path.replace("has_stock=2","");
	path = path.replace("is_control=1","");
	path = path.replace("is_control=2","");
	//清空条件结束---
	path = appendParamToUrl(path,"categoryFirstId",id);
	window.location.href=path;
}

/*点击二级分类*/
function searchProdctByEJ(id){
	var ejZhan = 2;
	var path = urlPath;
	//当选择分类时，清空其他筛选条件
	var manufacturer = $("#manufacturer").val();
	var drugClassification = $("#drugClassification").val();
	if (drugClassification != null || drugClassification != ''){
		path = path.replace("drugClassification="+drugClassification,"");
	}
	if (manufacturer != null || manufacturer != ''){
		path = path.replace("manufacturer="+manufacturer,"");
	}
	var minPrice = $("#minPrice").val();
	var maxPrice = $("#maxPrice").val();
	if (minPrice == '' || minPrice == null){
		minPrice=0;
	}
	if (maxPrice == '' || maxPrice == null){
		maxPrice=9999;
	}
	path = path.replace("minPrice="+minPrice,"");
	path = path.replace("maxPrice="+maxPrice,"");
	path = path.replace("has_stock=1","");
	path = path.replace("has_stock=2","");
	path = path.replace("is_control=1","");
	path = path.replace("is_control=2","");
	//清空条件结束---
	path = path.replace("categoryFirstId=99999","");
	path = appendParamToUrl(path,"categorySecondId",id);
	path = appendParamToUrl(path,"ejZhan",ejZhan);
	window.location.href=path;
}
/*点击展开的二级分类*/
function searchProdctByEJ1(id){
	var ejZhan = 1;
	var path = urlPath;
	//当选择分类时，清空其他筛选条件
	var manufacturer = $("#manufacturer").val();
	var drugClassification = $("#drugClassification").val();
	if (drugClassification != null || drugClassification != ''){
		path = path.replace("drugClassification="+drugClassification,"");
	}
	if (manufacturer != null || manufacturer != ''){
		path = path.replace("manufacturer="+manufacturer,"");
	}
	var minPrice = $("#minPrice").val();
	var maxPrice = $("#maxPrice").val();
	if (minPrice == '' || minPrice == null){
		minPrice=0;
	}
	if (maxPrice == '' || maxPrice == null){
		maxPrice=9999;
	}
	path = path.replace("minPrice="+minPrice,"");
	path = path.replace("maxPrice="+maxPrice,"");
	path = path.replace("has_stock=1","");
	path = path.replace("has_stock=2","");
	path = path.replace("is_control=1","");
	path = path.replace("is_control=2","");
	//清空条件结束---
	path = path.replace("ejZhan=1","");
	path = path.replace("categoryFirstId=99999","");
	path = appendParamToUrl(path,"categorySecondId",id);
	path = appendParamToUrl(path,"ejZhan",ejZhan);
	window.location.href=path;
}

/*点击三级分类*/
function searchProdctBySJ(id){
	var sjZhan = 2;
	var path = urlPath;
	//当选择分类时，清空其他筛选条件
	var manufacturer = $("#manufacturer").val();
	var drugClassification = $("#drugClassification").val();
	if (drugClassification != null || drugClassification != ''){
		path = path.replace("drugClassification="+drugClassification,"");
	}
	if (manufacturer != null || manufacturer != ''){
		path = path.replace("manufacturer="+manufacturer,"");
	}
	var minPrice = $("#minPrice").val();
	var maxPrice = $("#maxPrice").val();
	if (minPrice == '' || minPrice == null){
		minPrice=0;
	}
	if (maxPrice == '' || maxPrice == null){
		maxPrice=9999;
	}
	path = path.replace("minPrice="+minPrice,"");
	path = path.replace("maxPrice="+maxPrice,"");
	path = path.replace("has_stock=1","");
	path = path.replace("has_stock=2","");
	path = path.replace("is_control=1","");
	path = path.replace("is_control=2","");
	//清空条件结束---
	path = path.replace("categoryFirstId=99999","");
	path = appendParamToUrl(path,"categoryThirdId",id);
	path = appendParamToUrl(path,"sjZhan",sjZhan);
	window.location.href=path;
}
/*点击展开的三级分类*/
function searchProdctBySJ1(id){
	var sjZhan = 1;
	var path = urlPath;
	//当选择分类时，清空其他筛选条件
	var manufacturer = $("#manufacturer").val();
	var drugClassification = $("#drugClassification").val();
	if (drugClassification != null || drugClassification != ''){
		path = path.replace("drugClassification="+drugClassification,"");
	}
	if (manufacturer != null || manufacturer != ''){
		path = path.replace("manufacturer="+manufacturer,"");
	}
	var minPrice = $("#minPrice").val();
	var maxPrice = $("#maxPrice").val();
	if (minPrice == '' || minPrice == null){
		minPrice=0;
	}
	if (maxPrice == '' || maxPrice == null){
		maxPrice=9999;
	}
	path = path.replace("minPrice="+minPrice,"");
	path = path.replace("maxPrice="+maxPrice,"");
	path = path.replace("has_stock=1","");
	path = path.replace("has_stock=2","");
	path = path.replace("is_control=1","");
	path = path.replace("is_control=2","");
	//清空条件结束---
	path = path.replace("sjZhan=1","");
	path = path.replace("categoryFirstId=99999","");
	path = appendParamToUrl(path,"categoryThirdId",id);
	path = appendParamToUrl(path,"sjZhan",sjZhan);
	window.location.href=path;
}

function searchProdctByCx(type){
	var path = urlPath;
	path = appendParamToUrl(path,"cxType",type);
	window.location.href=path;
}

function searchProdctByJylx(drugClassification){
	if (drugClassification == 'all'){
        drugClassification = 5;
	}
	var path = urlPath;
	path = appendParamToUrl(path,"drugClassification",drugClassification);
	window.location.href=path;
}

function searchByNames(){
	var names = "";
	var obj = document.getElementsByName('manname'); 
	for(var i=0; i<obj.length; i++){ 
		if(obj[i].checked){
			names += obj[i].value+",";
		}
	}
	if(names == null || names ==""){
	}else{
		names = names.substring(0,name.length-1);
		var path = urlPath;
		path = appendParamToUrl(path,"manufacturer",names);
	}
}

/*大图列表 加入购物车成功后的动画效果*/
function cart_fly(event,carNum,flag,id){
	// var offset = $(".pressCart").offset();
	var offset = $(".cycle2").offset();
	var scrollX=$(window).scrollTop(); //获取滚动条的距离。
	var addcar = $(this);
	var img="http://upload.ybm100.com/ybm/product/6936292110209.jpg";
	var flyer = "";
	if(flag == 1){
		 //img = addcar.siblings(".row1").find("img").attr('src');
		 img = $("#dt_"+id)[0].src;
	}else if(flag ==2){
		 img = $("#lb_"+id)[0].src;
	}else{
		img = $("#detail_"+id)[0].src;
	}
	flyer = $('<img class="u-flyer" src="'+img+'">');
	//var flyer = $('<img class="u-flyer" src="http://upload.ybm100.com/ybm/product/6936292110209.jpg">');
	var start_left = 1;
	var start_top = 1;
	if(typeof(event)=="undefined" || typeof(event.pageX)=="undefined" || typeof(event.pageX)==undefined){
		if(flag == 1){
			start_left = $('#href_DT_'+id).offset().left; // X坐标
			start_top = $('#href_DT_'+id).offset().top-scrollX;  // Y坐标
		}else if(flag ==2){
			start_left = $('#href_LB_'+id).offset().left; // X坐标
			start_top = $('#href_LB_'+id).offset().top-scrollX;  // Y坐标
		}else{
			start_left = $('#href_DETAIL_'+id).offset().left; // X坐标
			start_top = $('#href_DETAIL_'+id).offset().top-scrollX;  // Y坐标
		}
		
	}else{
		start_left = event.pageX;
		start_top = event.pageY-scrollX;
	}
	flyer.fly({
		start: {
			left: start_left, //开始位置（必填）#fly元素会被设置成position: fixed
			top: start_top  //开始位置（必填）
		},
		end: {
			left: offset.left+10, //结束位置（必填）
			top: offset.top+10-scrollX, //结束位置（必填）
			width: 0, //结束时宽度
			height: 0 //结束时高度
		},
		onEnd: function(){ //结束回调
				if(carNum >=1){
					$("#cartNumberLi").addClass("cycle");
					$("#cartNumberDiv").addClass("topp");
                    $("#cartNumberDiv").removeClass("noshow");
					$("#rigthCartNum").removeClass("cycle2 noshow");
					$("#rigthCartNum").addClass("cycle2");
					$("#rigthCartNum").addClass("topp");
					$("#firstCartNumberDiv").addClass("topp");
				}
				$("#cartNumberLi").html(carNum);
				$("#rigthCartNum").html(carNum);
				$("#cartNumberDiv").html(carNum);
				$("#firstCartNumberDiv").html(carNum);
				
			
			
			/*$("#msg").show().animate({width: '250px'}, 200).fadeOut(1000); //提示信息*/
			/*addcar.css("cursor","default").removeClass('orange').unbind('click');*/
			/*this.destory();*/ //移除dom
		}
	});
}

/*大图/列表收藏成功后的动画效果*/
function shouc_fly(event,flag,id){
	var offset = $(".r-souchang").offset();
	var scrollX=$(window).scrollTop(); //获取滚动条的距离。
	var addcar = $(this);
	var img = "http://upload.ybm100.com/ybm/product/6936292110209.jpg";
	var flyer = "";
	if(flag == 1){
		 img = $("#dt_"+id)[0].src;
	}else if(flag == 2){
		 img = $("#lb_"+id)[0].src;
	}else{
		 img = $("#detail_"+id)[0].src;
	}
	 flyer = $('<img class="u-flyer" src="'+img+'">');
	 
	 	var start_left = 1;
		var start_top = 1;
		if(typeof(event.pageX)=="undefined" || typeof(event.pageX)==undefined){
			if(flag == 1){
				start_left = $('#dt_'+id).offset().left; // X坐标
				start_top = $('#dt_'+id).offset().top-scrollX;  // Y坐标
			}else if(flag ==2){
				start_left = $('#lb_'+id).offset().left; // X坐标
				start_top = $('#lb_'+id).offset().top-scrollX;  // Y坐标
			}else{
				start_left = $('#detail_'+id).offset().left; // X坐标
				start_top = $('#detail_'+id).offset().top-scrollX;  // Y坐标
			}
			
		}else{
			start_left = event.pageX;
			start_top = event.pageY-scrollX;
		}
	
	flyer.fly({
		start: {
			left: start_left, //开始位置（必填）#fly元素会被设置成position: fixed
			top: start_top    //开始位置（必填）
		},
		end: {
			left: offset.left+10, //结束位置（必填）
			top: offset.top+10-scrollX, //结束位置（必填）
			width: 0, //结束时宽度
			height: 0 //结束时高度
		},
		onEnd: function(){ //结束回调
			/*$("#msg").show().animate({width: '250px'}, 200).fadeOut(1000); //提示信息*/
			/*addcar.css("cursor","default").removeClass('orange').unbind('click');*/
			/*this.destory();*/ //移除dom
		}
	});
}




/*大图添加购物车js*/
//real: 1 默认 非详情页加购 2 详情页加购
window.addListCart=function(id,middpacking,isSplit,event,that){
	var merchantId = $("#merchantId").val();
    var cartNum;
    var shurukuangobj;
    var real = $('#real').val(),
        productName = $(that).parent(".row6").parent(".row6-box").parent("li").find("#skuName").val(),
        barcode = $(that).parent(".row6").parent(".row6-box").parent("li").find("#barcode").val(),
        categoryId = $(that).parent(".row6").parent(".row6-box").parent("li").find("#categoryId").val(),
        sptype = $('#sptype').val(),
        spid = $('#spid').val(),
        sid = $('#sid').val(),
    	sptype2 = $('#sptype_2').val(),
        spid2 = $('#spid_2').val(),
        sid2 = $('#sid_2').val();
    //商品详情上报
    // webSdk.track('pc_action_CommodityDetails', {
    //     'sptype': $('#sptype').val(),
    //     'spid': $('#spid').val(),
    //     'sid': $('#sid').val(),
		// 'real': real,
		// 'commodityId':id,
		// 'commodityName':name,
		// 'commodityCategory':barcode,
		// 'commodityCode':categoryId
    // });

	if(merchantId>0){
        cartNum=$(that).parent().find("input[name^='buyNum']").val();
        shurukuangobj=$(that).parent().find("input[name^='buyNum']");
        // if (!!window.ActiveXObject || "ActiveXObject" in window){
         //     eve = event || window.event; //获取事件对象
         //     objEle = eve.target || eve.srcElement; //获取document 对象的引用
         //    cartNum=$(that).parent().find("input[name^='buyNum']").val();
         //    shurukuangobj=$(that).parent().find("input[name^='buyNum']");
		// }else{
         //    cartNum=$(event.target).parent().find("input[name^='buyNum']").val();
         //    shurukuangobj=$(event.target).parent().find("input[name^='buyNum']");
		// }
		if(cartNum == 0){
			$.alert({
				title: '提示',
				body: '请输入购买数量!'
			});
			return;
		}else{
// 			if(isSplit == 0){
// 				if(cartNum<middpacking){
// 					showBoxTC(middpacking);
// 					//$.alert({"title":"提示","body":"购买数量必须是中包装的倍数！"});
// 					cartNum = middpacking;
// 				}else{
// 					var beishu = cartNum % middpacking;
// 					if(beishu != 0){
// //						$.alert({"title":"提示","body":"购买数量必须是中包装的倍数！"});
// 						showBoxTC(middpacking);
// 						var yushu = Math.floor(cartNum/middpacking);
// 						cartNum = yushu*middpacking;
// 					}
// 				}
// 				$("#buyNum_"+id).val(cartNum);
// 			}
			if(sptype2){
                changeCart(id,0,cartNum,addsuccesCart,addfailCart,adderrorCart,event,shurukuangobj,middpacking,isSplit,undefined,real,productName,barcode,categoryId,sptype2,spid2,sid2);
			}else{
                changeCart(id,0,cartNum,addsuccesCart,addfailCart,adderrorCart,event,shurukuangobj,middpacking,isSplit,undefined,real,productName,barcode,categoryId,sptype,spid,sid);
			}

		}

	}else{
		$.alert({
			title: '提示',
			body: '您还没有登录，请先登录!',
			okHidden : function(e){
				window.location.href="/login/login.htm?redirectUrl=/search/skuDetail/"+id+".htm";
			}
		});

	}
}
/*列表添加购物车js*/
function addCartLb(id,middpacking,isSplit,event,that){
	middpacking = parseInt(middpacking);
	cartNum = parseInt(cartNum);
	var merchantId = $("#merchantId").val();
    var real = $('#real').val(),
        productName = $(that).parent(".lib5").parent("ul").find("#skuName").val(),
        barcode = $(that).parent(".lib5").parent("ul").find("#barcode").val(),
        categoryId = $(that).parent(".lib5").parent("ul").find("#categoryId").val(),
        sptype = $('#sptype').val(),
		spid = $('#spid').val(),
        sid = $('#sid').val(),
        sptype2 = $('#sptype_2').val(),
        spid2 = $('#spid_2').val(),
        sid2 = $('#sid_2').val();
	if(merchantId>0){
		var cartNum = $("#buyNumlb_"+id).val();
		var shurukuangobj = $("#buyNumlb_"+id);
		if(cartNum == 0){
			$.alert({
				title: '提示',
				body: '请输入购买数量!'
			});
			return;
		}else{
// 			if(isSplit == 0){
//
// 				if(cartNum<middpacking){
// //					$.alert({"title":"提示","body":"购买数量必须是中包装的倍数！"});
// 					showBoxTC(middpacking);
// 					cartNum = middpacking;
// 				}else{
// 					var beishu = cartNum % middpacking;
// 					if(beishu != 0){
// //						$.alert({"title":"提示","body":"购买数量必须是中包装的倍数！"});
// 						showBoxTC(middpacking);
// 						var yushu = Math.floor(cartNum/middpacking);
// 						cartNum = yushu*middpacking;
// 					}
// 				}
// 				$("#buyNumlb_"+id).val(cartNum);
// 			}
            if(sptype2){
                changeCart(id,0,cartNum,addsuccesCart,addfailCart,adderrorCart,event,shurukuangobj,middpacking,isSplit,undefined,real,productName,barcode,categoryId,sptype2,spid2,sid2);
            }else{
                changeCart(id,0,cartNum,addsuccesCart,addfailCart,adderrorCart,event,shurukuangobj,middpacking,isSplit,undefined,real,productName,barcode,categoryId,sptype,spid,sid);
            }
		}
		
	}else{
		$.alert({
			title: '提示',
			body: '您还没有登录，请先登录!',
			okHidden : function(e){
				window.location.href="/login/login.htm";
			}
		});
	}
}
function formatDate(date, fmt) {
	if(!date) return '';
	date = typeof date === "string" ? date.replace(/-/g,'/') : date;
	date = new Date(date);
	if (/(y+)/.test(fmt)) {
	  fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substring(4 - RegExp.$1.length));
	}
	let o = {
	  'M+': date.getMonth() + 1,
	  'd+': date.getDate(),
	  'h+': date.getHours(),
	  'm+': date.getMinutes(),
	  's+': date.getSeconds()
	};
	for (let k in o) {
	  if (new RegExp(`(${k})`).test(fmt)) {
		let str = o[k] + '';
		fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : padLeftZero(str));
	  }
	}
	return fmt;
};
function padLeftZero(str) {
	return ('00' + str).substring(str.length);
};
/*详情页加入购物js*/
function addCartDetail(id,middpacking,isSplit,event,sourceId,bizSource, jgmdObj={}){
	console.log(id,middpacking,isSplit,event,sourceId,bizSource)
	
	middpacking = parseInt(middpacking);
	cartNum = parseInt(cartNum);
	var merchantId = $("#merchantId").val();
    var real = $('#real').val(),
        productName = $('#productShowName').val(),
        barcode = $('#barcode').val(),
        categoryId = $('#categoryId').val(),
        sptype = $('#sptype').val(),
        spid = $('#spid').val(),
        sid = $('#sid').val(),
		isWholesale = $('#pigou').val();

	
	if (jgmdObj && jgmdObj.isJgMd == 1) {
		var cartNum = $("#buyNumDl_"+id).val();

		var btn_text = event.target.innerText || "";
		let jgInfo = JSON.parse(sessionStorage.getItem("jgInfo")) || {};
		let jgspid = sessionStorage.getItem("jgspid") || '';
		// 进入详情页曝光
		jgInfo.direct = "2";
		console.log("存direct");
		sessionStorage.setItem("jgInfo", JSON.stringify(jgInfo));
	}
	
	if(merchantId>0){
		var cartNum = $("#buyNumDl_"+id).val();
        var shurukuangobj = $("#buyNumDl_"+id);
        var sourceIdNum = '';
		if(cartNum == 0){
			$.alert({
				title: '提示',
				body: '请输入购买数量!'
			});
			return;
		}else{
			// if(isSplit == 0){
			// 	if(cartNum<middpacking){
			// 		showBoxTC(middpacking);
			// 		cartNum = middpacking;
			// 	}else{
			// 		var beishu = cartNum % middpacking;
			// 		if(beishu != 0){
			// 			showBoxTC(middpacking);
			// 			var yushu = Math.floor(cartNum/middpacking);
			// 			cartNum = yushu*middpacking;
			// 		}
			// 	}
			// 	$("#buyNumDl_"+id).val(cartNum);
			// }
			if(sourceId){
				sourceIdNum = sourceId;
			}else {
				sourceIdNum = '';
			}

			if (bizSource) {
				let shopCode = $('#detailShopCode').val()
				let supportSuiXinPin = $('#supportSuiXinPin').val()
				let isThirdCompany = $('#isThirdCompany').val()
				groupSettle(cartNum,id, isWholesale,shopCode,supportSuiXinPin,isThirdCompany);
			} else {
				changeCart(id,0,cartNum,addDetailsuccesCart,addfailCart,adderrorCart,event,shurukuangobj,middpacking,isSplit,sourceIdNum,real,productName,barcode,categoryId,sptype,spid,sid,bizSource);
			}
		}
		
	}else{
		$.alert({
			title: '提示',
			body: '您还没有登录，请先登录!',
			okHidden : function(e){
				window.location.href="/login/login.htm?redirectUrl=/search/skuDetail/"+id+".htm";
			}
		});
	}
}

function addsuccesCart(result,event){
	var status = result.status;
	if(status =="success"){
	var num = getCartNum();
		var id = result.data.skuId;
		cart_fly(event,num,1,id);
		if(result.data.message != null && result.data.message !=""){
			$.alert(result.data.message);
		}
		
		var count = result.data.qty;
		$("#buyNum_"+id).val(count);
	}
}


function addLbsuccesCart(result,event){
	var status = result.status;
	if(status =="success"){
	var num = getCartNum();
		var id = result.data.skuId;
		cart_fly(event,num,2,id);
		if(result.data.message != null && result.data.message !=""){
			$.alert(result.data.message);
		}
		
		var count = result.data.qty;
		$("#buyNumlb_"+id).val(count);
	}
}
function addDetailsuccesCart(result,event){
	var status = result.status;
	if(status =="success"){
	var num = getCartNum();
	var id = result.data.skuId;
		cart_fly(event,num,3,id);
		if(result.data.message != null && result.data.message !=""){
			$.alert(result.data.message);
		}
		
		var count = result.data.qty;
		$("#buyNumDl_"+id).val(count);
	}
}

function addfailCart(result){
	var status = result.status;
	if(status=="failure"){
		$.alert(result.errorMsg);
	}
}
function adderrorCart(){
}

/*大图列表收藏*/
function addDTSX(id,event,_this){

	var merchantId = $("#merchantId").val();
	if(merchantId>0){
		addCollection(id, addDTsuccesC, addDTfailC, addDTerrorC,event,_this);
	}else{
		$.alert({
			title: '提示',
			body: '您还没有登录，请先登录!',
			okHidden : function(e){
				window.location.href="/login/login.htm";
			}
		});
		
	}
}

function addDTsuccesC(result,event,_this){
	var status = result.status;
	if(status =="success"){
		//shouc_fly(event,1,_this.id);
		//$(_this).removeClass("soucang_gary").addClass("soucang");
		$(_this).removeAttr("onclick");
		$(_this).attr("onclick","removeSC("+_this.id+",event,this)");
		$(_this).removeClass("nopCollect").addClass("hasCollect");
	}
}
function addLBsuccesC(result,event,_this){
	var status = result.status;
	if(status =="success"){
		//shouc_fly(event,2,_this.id);
		//$(_this).removeClass("souc").addClass("active");
		$(_this).removeAttr("onclick");
		$(_this).attr("onclick","rmCollectionLb("+_this.id+",event,this)");
		/*添加收藏*/
		$(_this).removeClass("nopCollect").addClass("hasCollect");
	}
}
function addDetailsuccesC(result,event,_this){
	var status = result.status;
	if(status =="success"){
		//shouc_fly(event,3,_this.id);
		$(_this).removeClass("addsc gray").addClass("addsc");
		$(_this).removeAttr("onclick");
		$(_this).attr("onclick","rmCollectionDetail("+_this.id+",event,this)");
	}
}

function addDTfailC(result){
	var status = result.status;
	if(status=="failure"){
		$.alert({
			title: '提示',
			body: '添加失败!'
		});
	}
	
}
function addDTerrorC(){
}

/*列表收藏*/
function addCollectionLb(id,event,_this){
	var merchantId = $("#merchantId").val();
	if(merchantId>0){
		addCollection(id, addLBsuccesC, addDTfailC, addDTerrorC,event,_this);
	}else{
		$.alert({
			title: '提示',
			body: '您还没有登录，请先登录!',
			okHidden : function(e){
				window.location.href="/login/login.htm";
			}
		});
	}
}

/*详情页收藏*/
function addDetailConllect(id,event,_this){
	var merchantId = $("#merchantId").val();
	if(merchantId>0){
		addCollection(id, addDetailsuccesC, addDTfailC, addDTerrorC,event,_this);
		$("#"+id).find("span").html("取消收藏");
	}else{
		$.alert({
			title: '提示',
			body: '您还没有登录，请先登录!',
			okHidden : function(e){
				window.location.href="/login/login.htm?redirectUrl=/search/skuDetail/"+id+".htm";
			}
		});
	}
}

/*大图列表取消收藏*/
function removeSC(id,event,_this){
	var merchantId = $("#merchantId").val();
	if(merchantId>0){
		//有货提醒和原取消收藏分开
		 if($('#businessType') && $('#businessType').length >0){
			 var alertMsg ="";
			 var businessType = $('#businessType').val();
			 if(businessType == 1){
				 alertMsg = "取消收藏后，该商品有货将不会继续通知您，确认取消收藏吗？";
			 }

			 if(businessType == 2){
				 alertMsg = "取消收藏以后，该商品降价将不会继续通知您，确定取消收藏吗？";
			 }
			 $.confirm({
					title: '提示',
					body: alertMsg,
					okHidden : function(e){
						delCollection(id, null, rmDTfailC, rmDTerrorC,event,_this);
						window.location.reload(true);
					},
					cancelHidden:function(e){
						return false;
					}
				}); 
		 }
		 else{
			 delCollection(id, rmDTsuccesC, rmDTfailC, rmDTerrorC,event,_this);	
		 }
			
	}else{
		$.alert({
			title: '提示',
			body: '您还没有登录，请先登录!',
			okHidden : function(e){
				window.location.href="/login/login.htm";
			}
		});
	}
}

function rmDTsuccesC(result,event,_this){
	var status = result.status;
	if(status =="success"){
		//$(_this).removeClass("soucang").addClass("soucang_gary");
		$(_this).removeAttr("onclick");
		$(_this).attr("onclick","addDTSX("+_this.id+",event,this)");
		/*取消收藏*/
		$(_this).removeClass("hasCollect").addClass("nopCollect");
		/*弹窗提示 2秒后消失*/
		showCollectTC();
	}
}
function rmDTfailC(result){
	var status = result.status;
	if(status=="failure"){
		$.alert({
			title: '提示',
			body: '取消收藏失败!'
		});
	}
	
}
function rmDTerrorC(){
}
/*列表取消收藏*/
function rmCollectionLb(id,event,_this){
	var merchantId = $("#merchantId").val();
	if(merchantId>0){
		delCollection(id, rmSuccsscollect, rmfailCollect, rmerrorCollect,event,_this);
	}else{
		$.alert({
			title: '提示',
			body: '您还没有登录，请先登录!',
			okHidden : function(e){
				window.location.href="/login/login.htm";
			}
		});
	}
}

function rmSuccsscollect(result,event,_this){
	var status = result.status;
	if(status =="success"){
		//$(_this).removeClass("active").addClass("souc");
		$(_this).removeAttr("onclick");
		$(_this).attr("onclick","addCollectionLb("+_this.id+",event,this)");
		/*取消收藏*/
		$(_this).removeClass("hasCollect").addClass("nopCollect");
		/*弹窗提示 2秒后消失*/
		showCollectTC();
	}
}
function rmfailCollect(result){
	var status = result.status;
	if(status=="failure"){
		$.alert({
			title: '提示',
			body: '取消收藏失败!'
		});
	}
	
}
function rmerrorCollect(){
}

/*详情页取消收藏*/
function rmCollectionDetail(id,event,_this){
	var merchantId = $("#merchantId").val();
	if(merchantId>0){
		delCollection(id, rmSuccsscollectDetail, rmfailCollectDetail, rmerrorCollectDetail,event,_this);
		$("#"+id).find("span").html("加入收藏");
	}else{
		$.alert({
			title: '提示',
			body: '您还没有登录，请先登录!',
			okHidden : function(e){
				window.location.href="/login/login.htm?redirectUrl=/search/skuDetail/"+id+".htm";
			}
		});
	}
}

function rmSuccsscollectDetail(result,event,_this){
	var status = result.status;
	if(status =="success"){
		$(_this).removeAttr("onclick");
		$(_this).attr("onclick","addDetailConllect("+_this.id+",event,this)");
		$(_this).removeClass("hasCollect").addClass("nopCollect");
	}
}
function rmfailCollectDetail(result){
	var status = result.status;
	if(status=="failure"){
		$.alert({
			title: '提示',
			body: '取消收藏失败!'
		});
	}
	
}
function rmerrorCollectDetail(){
}
/*排序*/
$(".paixu").click(function(){
	if($(this).hasClass("con-up")){
		/*降序*/
		$(this).removeClass("con-up").addClass("con-down");
	}else{
		/*升序*/
		$(this).removeClass("con-down").addClass("con-up");
	}
})

///*点击收藏*/
setTimeout(function(){ $(".w-collectZone_list").removeClass("initial");},1000);
/*取消收藏弹窗*/
var showCollectTC=function(){
	$("#nopCollect").modal('show');
	setTimeout(function(){$("#nopCollect").modal('hide');},2000)
}

/*列表显示收藏和采购模块*/
var showCollect=function(){
	$(".mrth-new li").hover(
			function() {
				$(this).find(".showorno").css("display", "block");
				$(this).find(".showorno").css("display", "block");
				setTimeout(function(){ $(".w-collectZone").removeClass("initial");},1000);
				$(this).find(".w-collectZone").css("display", "block");
			},
			function() {
				$(this).find(".showorno").css("display", "none");
				$(this).find(".w-collectZone").css("display", "none");
			}
	);
}

/*活动未开始弹窗*/
var showMsTC=function(){
    // $("#msfc").modal('show');
    // setTimeout(function(){$("#msfc").modal('hide');},1000)
    $.alert({
        title: '提示',
        body: '活动尚未开始,敬请期待'
    });
}

