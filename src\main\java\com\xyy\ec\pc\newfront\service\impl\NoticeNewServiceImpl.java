package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.layout.buinese.api.NoticeDetailBuineseApi;
import com.xyy.ec.layout.buinese.dto.NoticeDetailBuineseDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.newfront.dto.NoticeDetailRespVO;
import com.xyy.ec.pc.newfront.dto.NoticeRowRespVO;
import com.xyy.ec.pc.newfront.service.NoticeNewService;
import com.xyy.ec.pc.newfront.vo.NoticeDetailParamVO;
import com.xyy.ec.pc.newfront.vo.NoticeParamVO;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class NoticeNewServiceImpl extends BaseController implements NoticeNewService   {

    private final XyyIndentityValidator xyyIndentityValidator;


    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private NoticeDetailBuineseApi noticeDetailBuineseApi;

    @Override
    public PageInfo<NoticeRowRespVO> getNoticeList(NoticeParamVO paramVO, HttpServletRequest request) {
        try{
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            }else{
                merchantId = 0L;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);

            // 分页要用，将url传到前台
            String url = this.getRequestUrl(request);

//            int offset = NumberUtils.toInt(request.getParameter("offset"));
//            if (offset < 1) {
//                offset = 1;
//            }
            /** 初始化页面数据Page */
            NoticeDetailBuineseDto noticeDetail = new NoticeDetailBuineseDto();
            noticeDetail.setBranchCode(branchCode);

            PageInfo<NoticeDetailBuineseDto> pageInfo = noticeDetailBuineseApi.getUseingToRedis(branchCode, paramVO.getPageNum(), paramVO.getPageSize());
            List<NoticeDetailBuineseDto> list = pageInfo.getList();
            List<NoticeRowRespVO> rows = new ArrayList<>();

            for (NoticeDetailBuineseDto dto : list) {
                NoticeRowRespVO vo = new NoticeRowRespVO();
                BeanUtils.copyProperties(vo, dto); // 注意参数顺序：目标在前
                rows.add(vo);
            }

            Page<NoticeRowRespVO> noticePage = new Page<>(paramVO.getPageNum(), paramVO.getPageSize());
//            noticePage.setOffset(offset);
            noticePage.setPageNum(paramVO.getPageNum());
            noticePage.addAll(rows);
            noticePage.setTotal(pageInfo.getTotal());
            noticePage.setPages(pageInfo.getPages());

            // noticeRespVO.setPager(noticePage);
            // 商户id
            // noticeRespVO.setMerchantId(merchantId);
            // 商户信息
            // noticeRespVO.setMerchant(merchant);
            return PageInfo.of(noticePage);
        } catch(Exception e){
            log.error("公告加载异常:", e);
            String errMsg = "公告加载异常";
            throw new AppException(errMsg, XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public NoticeDetailRespVO getNoticeDetail(NoticeDetailParamVO paramVO, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);

            NoticeDetailRespVO noticeDetailRespVO = new NoticeDetailRespVO();
            NoticeDetailBuineseDto noticeDetail1 = noticeDetailBuineseApi.selectByPrimaryKey(paramVO.getId());
            noticeDetailRespVO.setNoticeDetail(noticeDetail1);
            noticeDetailRespVO.setMerchantId(merchantId);
            noticeDetailRespVO.setPreviousId(noticeDetailBuineseApi.selectPreviousId(paramVO.getId(), branchCode));
            noticeDetailRespVO.setNextId(noticeDetailBuineseApi.selectNextId(paramVO.getId(), branchCode));
            noticeDetailRespVO.setMerchant(merchant);

            return noticeDetailRespVO;
        } catch (Exception e) {
            log.error("公告详情加载异常:", e);
            String errMsg = "公告详情加载异常";

            throw new AppException(errMsg, XyyJsonResultCodeEnum.FAIL);
        }
    }
}
