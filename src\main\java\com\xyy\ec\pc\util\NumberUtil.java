package com.xyy.ec.pc.util;

/**
 * @Description
 * @Date 2025/7/1 14:18
 */
public class NumberUtil {

    /**
     * 格式化数字，保留最高位数字，后面补0
     * 例如：23456 → 20000
     *
     * @param num 要格式化的数字
     * @return 格式化后的整数
     */
    public static int formatNumber(int num) {
        if (num < 10) return num;

        // 计算数字位数 (如345的位数是3)
        int digits = (int) (Math.log10(num) + 1);

        // 计算最高位的权重因子 (3位数的因子=100)
        int factor = (int) Math.pow(10, digits - 1);

        // (345/100)=3 → 3*100=300
        return (num / factor) * factor;
    }
}
