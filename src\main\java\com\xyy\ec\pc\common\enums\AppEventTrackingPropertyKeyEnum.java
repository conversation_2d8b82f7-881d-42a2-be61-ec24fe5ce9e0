package com.xyy.ec.pc.common.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * App埋点属性key枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AppEventTrackingPropertyKeyEnum {

    /**
     * sptype
     */
    SPTYPE("sptype", "sptype"),

    /**
     * spid
     */
    SPID("spid", "spid"),

    /**
     * sid
     */
    SID("sid", "sid"),

    /**
     * sid
     */
    JGSPID("jgspid", "jgspid"),

    /**
     * scmId
     */
    SCMID("scmId", "scmId"),
    ;

    private String propertyKey;
    private String propertyName;

    AppEventTrackingPropertyKeyEnum(String propertyKey, String propertyName) {
        this.propertyKey = propertyKey;
        this.propertyName = propertyName;
    }

    /**
     * 自定义 valueOf()方法
     *
     * @param propertyKey
     * @return
     */
    public static AppEventTrackingPropertyKeyEnum valueOfCustom(String propertyKey) {
        if (StringUtils.isEmpty(propertyKey)) {
            return null;
        }
        for (AppEventTrackingPropertyKeyEnum anEnum : values()) {
            if (Objects.equals(anEnum.getPropertyKey(), propertyKey)) {
                return anEnum;
            }
        }
        return null;
    }

}
