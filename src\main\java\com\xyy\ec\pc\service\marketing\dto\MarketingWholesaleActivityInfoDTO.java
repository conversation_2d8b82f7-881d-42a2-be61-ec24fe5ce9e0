package com.xyy.ec.pc.service.marketing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 营销批购包邮活动信息DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketingWholesaleActivityInfoDTO implements Serializable {

    private static final long serialVersionUID = -4334237673176329599L;
    /**
     * 活动ID
     */
    private Long marketingId;

    /**
     * 活动类型
     **/
    private Integer activityType;

    /**
     * 活动价格
     */
    private BigDecimal assemblePrice;

    /**
     * 起拼数量
     */
    private Integer skuStartNum;

    /**
     * 是否支持随心拼
     */
    private Boolean supportSuiXinPin;

    /**
     * 随心拼按钮文案（商品详情页）
     */
    private String suiXinPinButtonText;

    /**
     * 随心拼按钮上气泡文案
     */
    private String suiXinPinButtonBubbleText;
    /**
     * 是否使用在列表页上弹出动态面板的操作方式
     */
    private Boolean isApplyListShowType;
}
