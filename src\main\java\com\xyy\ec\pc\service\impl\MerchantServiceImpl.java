package com.xyy.ec.pc.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.MerchantBalanceJournalBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBalanceJournalBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.account.LoginAccountDto;
import com.xyy.ec.merchant.bussiness.params.*;
import com.xyy.ec.merchant.bussiness.result.MerchantAccountRelatedMerchantInfoDTO;
import com.xyy.ec.merchant.bussiness.result.MerchantClerkInfoDTO;
import com.xyy.ec.merchant.bussiness.result.MerchantClerkLicenseAuditInfoDTO;
import com.xyy.ec.merchant.bussiness.result.MerchantRelateShopResult;
import com.xyy.ec.merchant.server.api.VirtualGoldApi;
import com.xyy.ec.merchant.server.dto.VirtualGoldDto;
import com.xyy.ec.merchant.server.dto.VirtualGoldLogDto;
import com.xyy.ec.merchant.server.dto.VirtualGoldParamDto;
import com.xyy.ec.merchant.server.enums.VirtualGoldChangeTypeEnum;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderRefundBusinessApi;
import com.xyy.ec.order.business.dto.OrderRefundBusinessDto;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.common.helpers.MerchantAccountRelatedMerchantInfoVOHelper;
import com.xyy.ec.pc.common.helpers.MerchantClerkInfoVOHelper;
import com.xyy.ec.pc.common.helpers.MerchantClerkLicenseAuditInfoDTOHelper;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.controller.order.OrderRefundVO;
import com.xyy.ec.pc.controller.vo.merchant.MerchantAccountRelatedMerchantInfoVO;
import com.xyy.ec.pc.controller.vo.merchant.MerchantClerkInfoVO;
import com.xyy.ec.pc.controller.vo.merchant.MerchantClerkLicenseAuditInfoVO;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.param.VirtualGoldReqDto;
import com.xyy.ec.pc.service.MerchantService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.vo.VirtualGoldLogVO;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zhaoyun
 * @Date: 2018/8/28 10:20
 * @Description: 用户服务实现类
 */
@Component
public class MerchantServiceImpl implements MerchantService {

    private static final Logger logger = LoggerFactory.getLogger(MerchantServiceImpl.class);

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private MerchantBalanceJournalBussinessApi merchantBalanceJournalBussinessApi;

    @Reference(version = "1.0.0")
    private VirtualGoldApi virtualGoldApi;


    @Reference(version = "1.0.0", timeout = 2000)
    private OrderRefundBusinessApi orderRefundBusinessApi;

    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;

    @Resource
    private Config config;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private MerchantLicenseApi merchantLicenseApi;

    /**
     * 获取用户
     * @param merchantId
     * @return
     */
    @Override
    public MerchantBussinessDto getMerchant(Long merchantId) {
        MerchantBussinessDto merchantBussinessDto = new MerchantBussinessDto();
        try{
            merchantBussinessDto = merchantBussinessApi.findMerchantById(merchantId);
        }catch (Exception e){
            logger.error("getMerchant error",e);
        }
        return merchantBussinessDto;
    }

    /**
     * 查询余额
     * @param orderNo
     * @return
     */
    @Override
    public MerchantBalanceJournalBussinessDto getSourceUseBanalce(String orderNo) {
        List<MerchantBalanceJournalBussinessDto> result = Lists.newArrayList();
        try{
            MerchantBalanceJournalBussinessDto queryBean = new MerchantBalanceJournalBussinessDto();
            //todo 要改成orderNo
            queryBean.setCardNo(orderNo);
            result = merchantBalanceJournalBussinessApi.selectMerchantBalanceJournalList(queryBean);
        }catch (Exception e){
            logger.error("getSourceUseBanalce error",e);
        }
        if(CollectionUtils.isEmpty(result)) return null;
        return result.get(0);
    }

    @Override
    public MerchantBussinessDto getMerchantBySync(String syncNO) throws Exception{
        return merchantBussinessApi.findMerchantBySync(syncNO);
    }

    @Override
    public MerchantBussinessDto findMerchantByMobile(String mobile) throws Exception {
        return merchantBussinessApi.findMerchantByMobile(mobile);
    }

    @Override
    public List<MerchantBussinessDto> findMerchantDetailsByMobile(String mobile) {
        return merchantBussinessApi.findMerchantDetailsByMobile(mobile);
    }

    @Override
    public LoginAccountDto findAccountInfoByMobile(String mobile) {
        return merchantBussinessApi.findAccountInfoByMobile(mobile);
    }

//    @Override
//    public Long doPostOa(String tgc) {
//        logger.info("hacker 调用通行证验票,tgc:{}",tgc);
//        try {
//            if (StringUtils.isBlank(tgc)){
//                return null;
//            }
//            String url = config.getPassportUrl().concat(config.getTicketUrl());
//            Map<String,String> map = new HashMap<>();
//            map.put("tgc",tgc);
//            map.put("systemCode",config.getSystemCode());
//            String httpRs = HttpUtil.post(url,JSONObject.toJSONString(map),"application/json");
//            logger.info("hacker 调用通行证验票,结果,tgc:{},rs:{}",tgc,httpRs);
//            if (StringUtils.isBlank(httpRs)){
//                return null;
//            }
//            JSONObject jso = JSON.parseObject(httpRs);
//            if (jso==null){
//                return null;
//            }
//
//            if (!Objects.equals(jso.getInteger("code"),ApiResultCodeEum.SUCCESS.getCode()) ){
//                return null;
//            }
//            //取data
//            JSONObject data = jso.getJSONObject("result");
//            if (data==null){
//                return null;
//            }
//            //2. 根据userId 查询merchantId(等待接口)
//            Integer userId = data.getInteger("userId");
//            if (userId==null){
//                return null;
//            }
//            Long merchantId = hackUserRelationApi.getChannelUserId(userId,ChannelEnum.EC.getValue());
//            //3. 判断是否是EC客户
//            return merchantId;
//        }catch (Exception e){
//            logger.error("hacker 调用通行证验票,异常,tgc:{}",tgc,e);
//            return null;
//        }
//    }

    @Override
    public MerchantRelateShopResult relateShop(MerchantRelateShopParam param) {
        ApiRPCResult<MerchantRelateShopResult> apiRPCResult = merchantBussinessApi.relateShop(param);
        if (apiRPCResult.isFail()) {
            logger.error("关联店铺失败，入参：{}，出参：{}", JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
            throw new AppException(XyyJsonResultCodeEnum.FAIL, apiRPCResult.getMsg());
        }
        return apiRPCResult.getData();
    }

    @Override
    public void cancelShopRelation(MerchantCancelShopRelationParam param) {
        ApiRPCResult<Boolean> apiRPCResult = merchantBussinessApi.cancelShopRelation(param);
        if (apiRPCResult.isFail() || BooleanUtils.isNotTrue(apiRPCResult.getData())) {
            logger.error("取消店铺关联失败，入参：{}，出参：{}", JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
            throw new AppException(XyyJsonResultCodeEnum.FAIL, apiRPCResult.getMsg());
        }
    }

    @Override
    public void addClerkLicenseAudit(MerchantAddClerkLicenseAuditParam param) {
        ApiRPCResult<Boolean> apiRPCResult = merchantLicenseApi.addClerkLicenseAudit(param);
        if (apiRPCResult.isFail() || BooleanUtils.isNotTrue(apiRPCResult.getData())) {
            logger.error("添加店员资质审核信息失败，入参：{}，出参：{}", JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
            throw new AppException(XyyJsonResultCodeEnum.FAIL, apiRPCResult.getMsg());
        }
    }

    @Override
    public MerchantClerkLicenseAuditInfoVO getClerkLicenseAuditInfo(MerchantGetClerkLicenseAuditInfoParam param) {
        ApiRPCResult<MerchantClerkLicenseAuditInfoDTO> apiRPCResult = merchantLicenseApi.getClerkLicenseAuditInfo(param);
        if (apiRPCResult.isFail()) {
            logger.error("获取店铺资质审核信息失败，入参：{}，出参：{}", JSONObject.toJSONString(param), JSONObject.toJSONString(apiRPCResult));
            throw new AppException(XyyJsonResultCodeEnum.FAIL, apiRPCResult.getMsg());
        }
        return MerchantClerkLicenseAuditInfoDTOHelper.create(apiRPCResult.getData());
    }

    @Override
    public PageInfo<MerchantAccountRelatedMerchantInfoVO> listAccountRelatedMerchants(MerchantListAccountRelatedMerchantsQueryParam queryParam) {
        ApiRPCResult<PageInfo<MerchantAccountRelatedMerchantInfoDTO>> apiRPCResult = merchantBussinessApi.listAccountRelatedMerchants(queryParam);
        if (apiRPCResult.isFail()) {
            logger.error("分页查询账号关联的店铺失败，入参：{}，出参：{}", JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
            throw new AppException(XyyJsonResultCodeEnum.FAIL, apiRPCResult.getMsg());
        }
        PageInfo<MerchantAccountRelatedMerchantInfoDTO> pageInfo = apiRPCResult.getData();
        List<MerchantAccountRelatedMerchantInfoDTO> dtos = pageInfo.getList();
        List<MerchantAccountRelatedMerchantInfoVO> voList = MerchantAccountRelatedMerchantInfoVOHelper.creates(dtos);
        PageInfo<MerchantAccountRelatedMerchantInfoVO> result = new PageInfo<>();
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());
        result.setList(voList);
        return result;
    }

    @Override
    public PageInfo<MerchantClerkInfoVO> listMerchantClerks(MerchantListMerchantClerksQueryParam queryParam) {
        ApiRPCResult<PageInfo<MerchantClerkInfoDTO>> apiRPCResult = merchantBussinessApi.listMerchantClerks(queryParam);
        if (apiRPCResult.isFail()) {
            logger.error("分页查询店铺的店员信息失败，入参：{}，出参：{}", JSONObject.toJSONString(queryParam), JSONObject.toJSONString(apiRPCResult));
            throw new AppException(XyyJsonResultCodeEnum.FAIL, apiRPCResult.getMsg());
        }
        PageInfo<MerchantClerkInfoDTO> pageInfo = apiRPCResult.getData();
        List<MerchantClerkInfoDTO> dtos = pageInfo.getList();
        List<MerchantClerkInfoVO> voList = MerchantClerkInfoVOHelper.creates(dtos);
        PageInfo<MerchantClerkInfoVO> result = new PageInfo<>();
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        result.setPages(pageInfo.getPages());
        result.setList(voList);
        return result;
    }

    @Override
    public List<MerchantBussinessDto> findMerchantForSaasByAccountId(Long accountId) {
        try{
            if(accountId == null){
                return Lists.newArrayList();
            }
            ApiRPCResult<List<MerchantBussinessDto>> result = merchantBussinessApi.findMerchantForSaasByAccountId(accountId);
            logger.info("merchantBussinessApi.findMerchantForSaasByIds res : {}",JSON.toJSONString(result));
            if(result.isSuccess()){
                return result.getData();
            }
        }catch (Exception e){
            logger.error("merchantBussinessApi.findMerchantForSaasByIds error",e);
        }
        return null;
    }


    @Override
    public BigDecimal queryVirtualGold(Long merchantId) {
        try{
            ApiRPCResult<VirtualGoldDto> result = virtualGoldApi.queryByMerchantId(merchantId);
            if (result.isSuccess() && result.getData() != null) {
                return result.getData().getAvailableVirtualGold();
            }
        }catch (Exception e){
            logger.error("MerchantServiceImpl getMerchant error",e);
        }
        return null;
    }

    @Override
    public Page<VirtualGoldLogVO> queryVirtualGoldLogList(VirtualGoldReqDto reqDto) {
        try{
            VirtualGoldParamDto param = new VirtualGoldParamDto();
            param.setMerchantId(reqDto.getMerchantId());
            param.setPageNum(reqDto.getPageNum());
            param.setPageSize(reqDto.getPageSize());
            param.setChangeType(reqDto.getTradeCode());
            param.setPayStartTime(reqDto.getPayStartTime());
            param.setPayEndTime(reqDto.getPayEndTime());
            if (reqDto.getVirtualGoldType() == 2) {
                param.setChangeTypeList(Arrays.asList(VirtualGoldChangeTypeEnum.deposit.getId(),VirtualGoldChangeTypeEnum.returnOfRefund.getId(),VirtualGoldChangeTypeEnum.returnOfSaveOrderFail.getId(),VirtualGoldChangeTypeEnum.returnOfCancel.getId()
                        ,VirtualGoldChangeTypeEnum.smallAmount.getId(),VirtualGoldChangeTypeEnum.returnOfPlatform.getId(),VirtualGoldChangeTypeEnum.PA_ACCOUNT_IN_VIRTUAL.getId(),VirtualGoldChangeTypeEnum.RECHARGE_ONLINE.getId()));
            } else if (reqDto.getVirtualGoldType() == 3) {
                param.setChangeTypeList(Arrays.asList(VirtualGoldChangeTypeEnum.reduce.getId(),VirtualGoldChangeTypeEnum.VIRTUAL_IN_PA_ACCOUNT.getId()));
            }
            logger.info("##virtualGoldApi.queryVirtualGoldLogByPage:入参：{}",param.toString());
            ApiRPCResult<com.xyy.ec.merchant.server.common.Page<VirtualGoldLogDto>> result = virtualGoldApi.queryVirtualGoldLogByPage(param);
            logger.info("##virtualGoldApi.queryVirtualGoldLogByPage:结果：{}",JSONObject.toJSONString(result));

            if (result.isSuccess() && result.getData() != null) {
                com.xyy.ec.merchant.server.common.Page<VirtualGoldLogDto> pageInfo = result.getData();
                List<VirtualGoldLogDto> list = pageInfo.getList();
                List<VirtualGoldLogVO> volist = new ArrayList<>();

                Map<String,Long> orderMap = getOrderIdMap(list);
                Map<String,Long> refundMap = getRefundIdMap(list);

                for (VirtualGoldLogDto virtualGoldLogDto : list) {
                    VirtualGoldLogVO vo = new VirtualGoldLogVO();
                    BeanUtils.copyProperties(virtualGoldLogDto,vo);

                    //RequestFlag:用于标识APP端点击流水往哪个详情页跳，1：订单详情;2：退款详情
                    if (virtualGoldLogDto.getChangeType() == VirtualGoldChangeTypeEnum.reduce.getId().intValue() || virtualGoldLogDto.getChangeType() == VirtualGoldChangeTypeEnum.returnOfCancel.getId().intValue()) {
                        vo.setOrderId(orderMap.get(virtualGoldLogDto.getTranNo()));
                        vo.setRequestFlag(1);
                    } else if (virtualGoldLogDto.getChangeType() == VirtualGoldChangeTypeEnum.returnOfRefund.getId().intValue()) {
                        vo.setOrderId(refundMap.get(virtualGoldLogDto.getTranNo()));
                        vo.setRequestFlag(2);
                    }
                    vo.setChangeDesc(VirtualGoldChangeTypeEnum.maps.get(virtualGoldLogDto.getChangeType().intValue()));
                    if (virtualGoldLogDto.getAfterChange().compareTo(virtualGoldLogDto.getBeforeChange())>0) {
                        vo.setChangeType((byte) 2);
                        if (virtualGoldLogDto.getChangeType() == VirtualGoldChangeTypeEnum.deposit.getId().intValue()) {
                            vo.setChangeDesc("充值");
                        } else if (virtualGoldLogDto.getChangeType() == VirtualGoldChangeTypeEnum.returnOfPlatform.getId().intValue() && StringUtil.isNotBlank(virtualGoldLogDto.getRemark())) {
                            vo.setChangeDesc(VirtualGoldChangeTypeEnum.returnOfPlatform.getDesc()+"-"+virtualGoldLogDto.getRemark());
                        }
                    } else {
                        vo.setChangeType((byte) 3);
                    }
                    if (virtualGoldLogDto.getChangeType() == VirtualGoldChangeTypeEnum.PA_ACCOUNT_IN_VIRTUAL.getId().intValue()||virtualGoldLogDto.getChangeType() == VirtualGoldChangeTypeEnum.VIRTUAL_IN_PA_ACCOUNT.getId().intValue()) {
                        vo.setTranNo("");
                    }else{
                        //兼容扣罚
                        vo.setTranNo(vo.getTranNo().split("&")[0]);
                    }
                    // 下单扣减、购物金转平安银行商户类型是支出情况变动金额为负数、否则是收入情况变动金额为正数
                    if (virtualGoldLogDto.getChangeType() == VirtualGoldChangeTypeEnum.reduce.getId().intValue() || virtualGoldLogDto.getChangeType() ==VirtualGoldChangeTypeEnum.VIRTUAL_IN_PA_ACCOUNT.getId().intValue()){
                          vo.setVirtualGold(virtualGoldLogDto.getVirtualGold()==null  ? BigDecimal.ZERO : virtualGoldLogDto.getVirtualGold().negate());
                    }else{
                          vo.setVirtualGold(virtualGoldLogDto.getVirtualGold());
                    }
                    volist.add(vo);
                }
                Page<VirtualGoldLogVO> orderPage = new Page<>();
                orderPage.setLimit(pageInfo.getPageSize());
                orderPage.setOffset(pageInfo.getPageNum());
                orderPage.setTotal(pageInfo.getTotal());
                orderPage.setRows(volist);
                orderPage.setPageCount(pageInfo.getPages());
                return orderPage;
            }
        }catch (Exception e){
            logger.error("queryVirtualGoldLogList error",e);
        }
        return null;
    }



    private Map<String, Long> getOrderIdMap(List<VirtualGoldLogDto> list) {
        List<String> orderNoList = list.stream()
                .filter(x -> StringUtils.isNoneEmpty(x.getTranNo()) && (x.getChangeType() == 2 || x.getChangeType() == 4))
                .map(k -> k.getTranNo()).collect(Collectors.toList());
        ApiRPCResult<Map<String, Long>> orderResult = orderBusinessApi.queryOrderIdByOrderNo(orderNoList);
        if (orderResult.isSuccess()) {
            return orderResult.getData();
        }
        return Maps.newHashMap();
    }

    private Map<String, Long> getRefundIdMap(List<VirtualGoldLogDto> list) {
        if(CollectionUtils.isEmpty(list)){
            return Maps.newHashMap();
        }
        List<OrderRefundVO> resultList = new ArrayList<>();
        List<String> refundOrderNoList = list.stream()
                .filter(x -> StringUtils.isNoneEmpty(x.getTranNo()) && (x.getChangeType() == 3))
                .map(k -> k.getTranNo()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(refundOrderNoList)){
             return Maps.newHashMap();
        }
        ApiRPCResult<List<OrderRefundBusinessDto>> result = orderRefundBusinessApi.queryByRefundOrderNo(refundOrderNoList);
        List<String> orderNoList = result.getData().stream().map(OrderRefundBusinessDto::getOrderNo).collect(Collectors.toList());
        ApiRPCResult<Map<String, Long>> orderResult = orderBusinessApi.queryOrderIdByOrderNo(orderNoList);
        if (result.isSuccess()) {
            result.getData().stream().forEach(v->{
                OrderRefundVO dto = new OrderRefundVO();
                dto.setOrderNo( v.getOrderNo());
                dto.setRefundOrderNo(  v.getRefundOrderNo());
                dto.setRefundOrderId(v.getId());
                dto.setOrderId(orderResult.getData().get(v.getOrderNo()));
                resultList.add(dto);
            });
           return resultList.stream().collect(Collectors.toMap(OrderRefundVO::getRefundOrderNo, OrderRefundVO::getOrderId));
        }
        return Maps.newHashMap();
    }

}
