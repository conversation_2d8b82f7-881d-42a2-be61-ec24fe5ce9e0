package com.xyy.ec.pc.newfront.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.merchant.bussiness.dto.licence.LicenseAuditParams;
import com.xyy.ec.pc.controller.LicenseAuditController;
import com.xyy.ec.pc.newfront.service.LicenseAuditNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2025-07-23 15:19
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/new-front/merchant/center/licenseAudit")
public class LicenseAuditNewController {


    private final LicenseAuditNewService licenseAuditNewService;

    private final LicenseAuditController licenseAuditController;

    @Value("${config.product_image_path_url}")
    private String uploadBasePathUrl;


    /**
     * 上传资质图片
     *
     * @param request 请求request
     * @return
     */
    @PostMapping("/uploadImg")
    public AjaxResult<Object> uploadImgNew(HttpServletRequest request, HttpServletResponse response) {
        String o = (String) licenseAuditController.uploadImg(request, response);
        log.info("uploadImgNew uploadImg result:{}", o);
        JSONObject json;
        try {
            json = JSONObject.parseObject(o);
        } catch (Exception e) {
            log.error("uploadImgNew error", e);
            return AjaxResult.errResult(o);
        }
        JSONArray fileNames = json.getJSONArray("fileName");
        if (CollectionUtil.isEmpty(fileNames)) {
            return AjaxResult.errResult(o);
        }
        ArrayList<String> fullPath = new ArrayList<>();
        for (Object fileName : fileNames) {
            fullPath.add(uploadBasePathUrl + fileName);
        }
        json.put("fileName", fullPath);
        return AjaxResult.successResult(json);
    }

    /**
     * 新增资质审核保存
     *
     * @param licenseAudit 资质审核对象
     * @return
     */
    @PostMapping("/addLicenseAudit")
    public AjaxResult<Object> addLicenseAuditNew(@RequestBody LicenseAuditParams licenseAudit) {
        return licenseAuditNewService.addLicenseAudit(licenseAudit);
    }


    /**
     * 资质更新保存
     *
     * @param licenseAudit 资质审核对象
     * @return
     */
    @PostMapping(value = "/updateLicenseAudit")
    public AjaxResult<Object> updateLicenseAuditNew(@RequestBody LicenseAuditParams licenseAudit) {
        return licenseAuditNewService.updateLicenseAudit(licenseAudit);
    }


    /**
     * 点击添加“添加首营资质/资质变更”按钮之前的效验
     *
     * @param type 1首营 2变更
     * @return
     */
    @GetMapping(value = "/initLicenseAuditDetailVaild")
    public AjaxResult<Object> initLicenseAuditDetailVaildNew(Integer type) {
        return licenseAuditNewService.initLicenseAuditDetailVaild(type);
    }

    /**
     * 点击添加“添加首营资质/资质变更”按钮
     *
     * @param customerType
     * @return
     */
    @GetMapping(value = "/initLicenseAuditDetail")
    public AjaxResult<Object> initLicenseAuditDetailNew(Integer customerType, Boolean remark, Integer invoiceType) {
        return licenseAuditNewService.initLicenseAuditDetail(customerType, remark, invoiceType);
    }

    /**
     * 点击修改资质按钮之前的效验
     *
     * @param type
     * @return
     */
    @GetMapping(value = "/initBillDetailVaild")
    public AjaxResult<Object> initBillDetailVaildNew(int type, @RequestParam("orgCode") String orgCode) {
        return licenseAuditNewService.initBillDetailVaild(type, orgCode);
    }

    /**
     * 点击修改资质按钮
     *
     * @param type
     * @param customerType
     * @return
     */
    @GetMapping(value = "/initBillDetail")
    public AjaxResult<Object> initBillDetail(int type, int customerType, @RequestParam("orgCode") String orgCode) {
        return licenseAuditNewService.initBillDetail(type, customerType, orgCode);
    }


    /**
     * 缓存首营资质添加的客户信息
     *
     * @param licenseAudit 资质审核对象
     * @return
     */
    @PostMapping(value = "/cacheMerchantInfo")
    public AjaxResult<Object> cacheMerchantInfo(@RequestBody LicenseAuditParams licenseAudit) {
        return licenseAuditNewService.cacheMerchantInfo(licenseAudit);
    }


}
