<!DOCTYPE HTML>
<html>

<head>
    <#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description"
          content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="referrer" content="never">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>资质管理--资质单据</title>
<#--    <title>资质管理--资质变更</title>-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">

    <link rel="stylesheet" href="${ctx}/static/css/user.css?t=${t_v}"/>
    <script type="text/javascript">
        var ctx = "${ctx}";
        var imageUrl='${imageUrl}';
        var msg ='${msg}';
    </script>

    <style>
        .myqual{
            width: 1260px;
        }

        .main-right.fr{
            border-width: 0px;
            background: #fff;
            /*min-height: 720px;*/
            padding: 20px 20px;
            width: 1010px;
        }

        .page-title-license{
            border-left-width: 0px;
            margin-bottom:20px;
            width: 990px;
            height: 20px;
            line-height: 20px;
            padding-left: 0px;
        }

        .shheXx{
            padding-bottom: 20px;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .shheXx div{
            font-size:14px;
            font-family:PingFangSC;
            font-weight:400;
            color:#333333;
            line-height:22px;
        }
        .shheXx div span,.shheXx div div{
            margin:0px;
            padding:0px;
        }
        .shheXx div span.spanOne,.shheXx div div.spanOne{
            color:#666666;
        }

        .shheXx div span.fail,.shheXx div div.fail{
            color:#FF7200;
        }

        .shheXx div span.succ,.shheXx div div.succ{
            color:#00C675;
        }

        .shheXx div div{
            width:930px;
        }
        .shheXx div div.spanOne{
            width:70px;
        }

        .zzhCon{
            padding-bottom: 20px;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;

        }

        .zzhCon .zzhConItem{
            width:49%;
            max-width:50%;
            margin-bottom: 30px;
            float: left;
            padding-right: 10px;
        }

        .zzhCon .zzhConItem .zzhConItemTitle{
            font-size:14px;
            font-family:PingFangSC;
            font-weight:400;
            color:#333333;
            line-height:14px;
            margin-bottom: 15px;
        }

        .zzhConItemTitleSee{
            font-size:12px;
            font-family:PingFangSC;
            font-weight:400;
            color:#6470B0;
            margin-left:15px;
            cursor: pointer;
        }

        .zzhConItemTitleSee_kpxx{
            font-size:12px;
            font-family:PingFangSC;
            font-weight:400;
            color:#6470B0;
            margin-left:15px;
            cursor: pointer;
        }

        .zzhConItemTitleDload{
            font-size:12px;
            font-family:PingFangSC;
            font-weight:400;
            color:#6470B0;
            margin-left:15px;
            cursor: pointer;
        }

        .zzhCon .zzhConItem .zzhConItemTitle .required{
            color:red;
        }

        .zzhConItemImgCon{

        }

        .zzhConItemImgConItem{
            position: relative;
            width: 135px;
            height:135px;
            margin-right: 16px;
            margin-bottom: 16px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        .zzhConItemImgConItem .removeImg{
            position: absolute;
            top: -12px;
            right: -8px;
            font-size: 25px;
            z-index: 1;
        }

        .zzhConItemImgConItem img{
            width: 100%;
            max-width: 100%;
            display: block;
            margin: 0px auto;
            height: 100%;
            max-height: 100%;
        }

        .zzhConItemImgConItem input[type="file"]{
            position: absolute;
            top:0px;
            left:0px;
            z-index: 1;
            width: 135px;
            height:135px;
            opacity: 0;
            filter: alpha(opacity=0);
            cursor: pointer;
            font-size:135px;
        }

        .zzhConItemImgCon img.zzhConItemImg{
            width: 135px;
            height:135px;
            /*margin-right: 16px;*/
            margin-bottom: 16px;
            border: 1px solid #fff;
        }

        .zzhCon .zzhConItem .zzhConItemRemark{
            font-size:12px;
            font-family:PingFangSC;
            font-weight:400;
            color:#8E8E93;
            line-height:17px;
            margin-bottom: 10px;
        }

        .wtsItem label div{
            width:113px ;
            text-align: right;
            font-size:12px;
            font-family:PingFangSC;
            font-weight:400;
            color:#666;
            line-height:18px;
            margin-right: 10px;
            float: left;
            height:32px;
            line-height: 32px;
        }

        .wtsItem{
            margin-bottom: 15px;
        }
        .wtsItem label input{
            width:232px;
            height:32px;
            background:rgba(255,255,255,1);
            border-radius:2px;
            border:1px solid rgba(238,238,238,1);
            float: left;
            padding:0px 10px;
        }
        .wtsItem label textarea{
            width:232px;
            height:107px;
            background:rgba(255,255,255,1);
            border-radius:2px;
            border:1px solid rgba(238,238,238,1);
            padding:10px;
        }

        .sui-btn.btn-bordered.subbtn{
            width:142px;
            height:40px;
            background:#00C675;
            border-radius:2px;
            font-size:16px;
            font-family:PingFangSC;
            font-weight:400;
            color:#fff;
            border-radius: 4px;
            border: 0px solid #8c8c8c;
        }
        .top-tip{
            padding:16px 20px 12px;
            border: 1px solid #faf1d7;
            border-radius: 4px 4px 0px 0px;
            font-size: 14px;
            font-weight: 400;
            text-align: left;
            color: #ff9500;
            background: #fffae0;
        }
        .step-header{
            padding:30px 0 30px 180px;
            background: #ffffff;
            border-bottom: 1px solid #eeeeee;
        }
        .step-content{
            margin:0 auto;
        }
        .step-content .step-circle{
            width: 30px;
            height: 30px;
            border-radius: 30px;
            border: 1px solid #bdbdbd;
            font-size:18px;
            line-height:30px;
            text-align: center;
            display: inline-block;
            color: #bdbdbd;
            font-weight: 400;
        }
        .step-content .circle-fill{
            background: #00c675;
            border: 1px solid #00c675;
            color:#ffffff;
        }
        .step-content .step-text{
            font-size: 14px;
            font-weight: 400;
            color: #999999;
            margin:0 5px;
        }
        .step-content .text-online{
            color: #333333;
        }
        .step-content .step-line{
            display: inline-block;
            width: 60px;
            height: 2px;
            background: #bdbdbd;
            border-radius: 4px;
            margin-right: 8px;
            position: relative;
            top:-3px;
        }
        .step-content .line-fill{
            background: #00c675;
        }
        .right-box{
            width: 1050px;
        }
        /*基本信息*/
        .con-box{
            padding-left:180px;
        }
        .sui-form input[type="text"] {
            width: 330px;
            height: 30px;
            font-size: 14px;
            color: #666666;
        }
        .sui-form select {
            width: 340px;
            height: 36px;
            font-size: 14px;
            color: #666666;
        }
        .control-label {
            font-size: 14px;
            color: #666;
            width: 106px;
        }
        .basic-title{
            font-size: 18px;
            font-weight: 500;
            text-align: left;
            color: #333333;
            margin-bottom: 20px;
            padding-left: 25px;
            padding-top: 10px;
        }
        .next-step{
            border-radius: 4px;
            margin-top: 20px;
            font-size: 16px;
        }
        .err-box{
            padding-left: 97px;
        }
        .err-box .wenantishi{
            color:#f00;
        }
        /** 地址选择 **/
        .area-select {
            width: 340px;
            height: 36px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 14px;
            color: #999;
            border-radius: 2px;
            border: 1px solid #ccc;
            padding: 10px;
            box-sizing: border-box;
            cursor: pointer;
        }
        .area-select .area-select-text {
            width: 300px;
            display: inline-block;
        }
        .text-ellipsis {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .area-select .right {
            vertical-align: middle;
        }
        .area-form {
            position: relative;
        }
        .area-form .area-pop {
            box-shadow:2px 2px 4px 0px rgba(226,226,226,0.5),-2px 0px 4px 0px rgba(226,226,226,1);
            border-radius:2px;
            background-color: #fff;
            position: absolute;
            z-index: 100;
            left: 0;
            top: 4px;
            height: 260px;
            white-space: nowrap;
        }
        .area-pop .area-list {
            height: 260px;
            display: inline-block;
            overflow-y: auto;
        }
        .area-pop .area-item {
            width: 120px;
            box-sizing: border-box;
            padding: 10px 19px 10px 11px;
            cursor: pointer;
            font-size: 14px;
        }
        .area-pop .area-item .area-name{
            display: inline-block;
            width: 80px;
        }
        .area-pop .area-item.active .area-name{
            color: #00C675;
        }
        select{
            outline: none;
        }
        .entype-select {
            width: 340px;
            height: 36px;
            display: inline-block;
            vertical-align: middle;
            margin-right: 14px;
            color: #999;
            border-radius: 2px;
            border: 1px solid #ccc;
            padding: 10px;
            box-sizing: border-box;
            cursor: pointer;
            outline: none;
            /*清除默认样式*/
            appearance:none;
            -moz-appearance:none;
            -webkit-appearance:none;
            background:url("/static/images/svg/right.svg") no-repeat 320px 10px;
        }
        /*清除ie下面的默认样式*/
        select::-ms-expand{display:none;}
        .btnTip{
            font-size: 14px;
            color: #ffb100;
            margin-bottom: 15px;
            margin-top: 15px;
        }
        .sui-form.form-horizontal label.control-label{
            width: 110px;
        }
        #stepBox{
            display: none;
        }
    </style>


</head>

<body>
<div class="container">

    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
        <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->
    <div class="main">
        <div class="myqual row">
            <!--面包屑-->
            <ul class="sui-breadcrumb">
                <li><a href="${ctx}/index.htm">首页</a>></li>
                <li><a href="${ctx}/merchant/center/index.htm">用户中心</a>></li>
                <li><a href="${ctx}/merchant/center/licenseAudit/queryLicenseAuditList">资质管理</a>></li>
                <li class="active">资质单据</li>
            <#--                <li class="active">资质变更</li>-->
            </ul>
            <div class="myqual-content clear">
                <div class="side-left fl">
                    <#include "/common/merchantCenter/left.ftl" />
                </div>
                <!--资质类型-->
                <input type="hidden" id="txt_firstLicenseType" value="${info.customerType}">
                <input type="hidden" id="" value="${info}">
                <input type="hidden" id="txt_msg" value="${msg}">
                <input type="hidden" id="licenseStatus" value="${info.type}">
                <#--单据编号-->
                <input type="hidden" id="applicationNumber" value="${applicationNumber}">
                <input type="hidden" id="orgCode" value="${orgCode}">
                <div class="right-box fr first-step">
                    <div class="top-tip">
                        为严格执行《药品管理法》及《药品经营质量管理规范》的相关规定，企业名称请填写《营业执照》中的企业名称，收货地址请在《药品经营许可证》中仓库地址的基础上进行完善，以便物流能准确的配送
                    </div>
                    <div class="step-header">
                        <div class="step-content">
                            <span class="step-circle circle-fill">1</span>
                            <span class="step-text text-online">信息认证</span>
                            <span class="step-line"></span>
                            <span class="step-circle" id="stepTwo">2</span>
                            <span class="step-text" id="stepTwoText">确认信息</span>
                            <span class="step-line"></span>
                            <span class="step-circle">3</span>
                            <span class="step-text">上传资质照片</span>
                        </div>
                    </div>
                    <div class="main-right fr">
                        <div class="con-box">
                            <form id="regForm" class="sui-form form-horizontal sui-validate" method="post" novalidate="novalidate">
                                <p class="basic-title">企业信息</p>
                                <div id="stepBoxOne">
                                    <div class="control-group">
                                        <label for="repassword" class="control-label">企业类型：</label>
                                        <div class="controls">
                                            <input type="hidden" value="${info.customerType}" id="selectValue">
                                            <select class="entype-select" id="entype" name="entype" value="${info.customerType}">
                                                <option value="1">单体药店</option>
                                                <option value="21">连锁总部</option>
                                                <option value="3">连锁直营</option>
                                                <option value="2">连锁加盟</option>
                                                <option value="4">诊所</option>
                                                <option value="14">门诊部</option>
                                                <option value="10">卫生室</option>
                                                <option value="9">社区卫生服务站</option>
                                                <option value="8">卫生院</option>
                                                <option value="11" style="display:none">医疗机构</option>
                                                <option value="13" style="display:none">基层医疗机构</option>
                                                <option value="15">其他医疗机构</option>
                                                <option value="5">药品批发</option>
                                                <option value="19">非药类经营</option>
                                                <option value="6">民营医院</option>
                                                <option value="7">公立医院</option>
                                                <option value="17">非药类生产</option>
                                                <option value="16">药品生产</option>
                                                <option value="12" style="display:none">终端</option>
                                                <option value="20">其他企业</option>
                                                <option value="18">境外企业</option>
                                                <option value="22">小连锁总部</option>
                                                <option value="23">批发(商业)</option>
                                            </select>
                                            <#--</#if>-->
                                        </div>
                                    </div>
                                    <div class="control-group" style="margin: 0">
                                        <label for="mobile" class="control-label"><span style="color: #ff0000">*</span><span id="codeTitle">
                                            <#if (info.customerType == 4 || info.customerType == 14 || info.customerType == 10 || info.customerType == 9 || info.customerType == 8 || info.customerType == 15 || info.customerType == 6 || info.customerType == 7)>
                                                医疗机构执业许可证编码：
                                            <#else >
                                                营业执照编码：
                                            </#if>
                                        </span></label>
                                        <div class="controls">
                                            <input type="text" id="encode" name="encode" maxlength="50" placeholder="请输入" value="${info.code}" onkeyup="value=value.replace(/[\W]/g,'') " data-rules="required">
                                        </div>
                                    </div>
                                    <div class="err-box">
                                        <div class="errinfo" id="pharmacyMsg" style="display: none;margin-top: 5px" >
                                            <img src="/static/images/cuowutishi.png" class="error-info-img"> <span class="wenantishi">证照编号不能为空</span>
                                        </div>
                                    </div>
                                    <div class="control-group">
                                        <label class="control-label"></label>
                                        <div class="controls">
                                            <p class="btnTip">为保证结果准确性，请保证企业类型和证照编码与实际相符</p>
                                            <button type="button" id="regInfo" class="sui-btn btn-bordered subbtn next-step" style="margin: 0">信息认证</button>
                                        </div>
                                    </div>
                                </div>
<#--                                <#if invoiceList??&&(invoiceList?size>0)>-->
<#--                                -->
<#--                                </#if>-->
                                <div class="stepBox" id="stepBox">
                                    <div class="control-group">
                                        <label for="mobile" class="control-label">企业名称：</label>
                                        <div class="controls">
                                            <input type="text" id="enname" name="enname" maxlength="50" placeholder="请填写营业执照对应的企业名称" value="${info.realName}" data-rules="required">
                                        </div>
                                    </div>
                                    <div class="control-group">
                                        <label for="name" class="control-label">所在地区：</label>
                                        <div class="controls select-box">
                                            <div class="area-select" >
                                                <input type="hidden" value="${info.provinceId}" id="provinceId">
                                                <input type="hidden" value="${info.cityId}" id="cityId">
                                                <input type="hidden" value="${info.districtId}" id="regionId">
                                                <input type="hidden" value="${info.streetId}" id="streetId">
                                                <div class="area-select-text text-ellipsis"><span class="area-init"></span></div>
                                                <img src="/static/images/svg/right.svg" class="right" align="right">
                                            </div>
                                            <div id="area" class="area-form"></div>
                                        </div>
                                    </div>

                                    <div class="control-group">
                                        <label for="name" class="control-label">详细地址：</label>
                                        <div class="controls">
                                            <input type="text" id="ad-detail" name="ad-detail" placeholder="请填写道路、门牌号等" value="">
                                        </div>
                                    </div>
                                    <#if invoiceList??&&(invoiceList?size>0)>
                                        <div class="control-group">
                                            <label for="repassword" class="control-label">发票类型：</label>
                                            <div class="controls">
                                                <select class="entype-select" id="billtype" name="billtype" value="${info.invoiceType}" <#if info.type!=1>disabled</#if>>
                                                    <#list invoiceList as v >
                                                        <option value="${v.id}">${v.name}</option>
                                                    </#list>
                                                </select>
                                            </div>
                                        </div>
                                    </#if>
                                    <div class="control-group">
                                        <label class="control-label"></label>
                                        <div class="controls">
                                            <button type="button" id="resetNext" class="sui-btn btn-bordered subbtn next-step">上一步</button>
                                            <button type="button" id="regNext" class="sui-btn btn-bordered subbtn next-step">下一步</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
        </div>
    </div>
    <!--底部导航区域开始-->
    <div class="footer" id="footer">
        <#include "/common/footer.ftl" />
    </div>
    <!--底部导航区域结束-->
    <!--客服入口开始-->
    <div class="kefu-box">
        <a href="javaScript:callKf('','${merchant.id}');">
            <img src="/static/images/kefu-online.png" alt="">
        </a>
    </div>
    <!--客服入口结束-->

</div>
</body>
<script src="${ctx}/static/js/ajaxfileupload.js"></script>
<script type="text/javascript" src="/static/js/pharmacy-list/event-dispatcher.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/license/pharmacy.class.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/license/main.js?t=${t_v}"></script>

<script>
    /**下拉框**/
    // var _entypePop = $(".entype-pop");
    // _entypePop.hide();
    // $(".entype-select").click(function(){
    //     // $(".entype-pop").show();
    //     if(_entypePop.is(':hidden')) _entypePop.show();
    //     else _entypePop.hide();
    // })
    var selectValue = $("#selectValue").val();
    if(selectValue){
        $("#entype").val(selectValue);
    }
    //校验必填项
    // $("#regForm").validate({
    //     messages: {
    //         encode: ["证照编号不能为空"]
    //     }
    // });
    $("#regNext").click(function(){
        if(msg){
            $.alert({"title":"提示",
                "body":msg,
                hide:function(){
                    window.location.href='/merchant/center/licenseAudit/queryLicenseAuditList';
                }});
        }else{
            // var realName = $('#regForm input[name="enname"]').val();
            // if(realName == '') {
            //     $('#pharmacyMsg').show();
            //     return;
            // }else{
                var firstLicenseType = $("#entype").val(); //资质类型
                var txt_firstLicenseType =$("#txt_firstLicenseType").val(); //页面返回的资质类型
                var ename = $("#enname").val();
                var etype = $("#entype").val();
                var address = $("#ad-detail").val();
                var provinceId = $("#provinceId").val();
                var cityId = $("#cityId").val();
                var regionId = $("#regionId").val();
                var streetId = $("#streetId").val();
                var billtype = $("#billtype").val();
                var basicInfoParams = {
                    customerName : ename,
                    customerType : etype,
                    deliveryProvinceId : provinceId,
                    deliveryCityId : cityId,
                    deliveryDistrictId : regionId,
                    deliveryStreetId : streetId,
                    deliveryAddress : address,
                    invoiceType : billtype,
                    type: 1
                }
                if(txt_firstLicenseType != '' && txt_firstLicenseType != firstLicenseType){
                    layer.confirm("变更资质类型将替换原资质，是否替换？", {icon: 3, title: '提示'}, function (index) {
                        updateLicense(basicInfoParams);
                        layer.close(index);
                    },function (index) {

                    });
                }else {
                    updateLicense(basicInfoParams);
                }
            // }
        }
    });
    $("#resetNext").click(function(){
        $("#stepBox").hide();
        $("#stepBoxOne").show();
        $("#stepTwo").removeClass('circle-fill');
        $("#stepTwoText").removeClass('text-online')
    })
    $("#regInfo").click(function(){
        $("#stepBox").hide()
        var realName = $('#regForm input[name="encode"]').val();
        if(!realName) {
            console.log(realName,'ppp')
            $('#pharmacyMsg').show();
            return;
        }
        var paramstemp =  {
            id: ${info.id},
            customerType: $("#entype").val(),
                code: $("#encode").val(),
        };
        $.ajax({
            type: "POST",
            url: "/merchant/center/license/authentication.json",
            data: paramstemp,
            contentType: "application/x-www-form-urlencoded",
            success: function (data) {
                console.log(data)
                if(data.status == 'success'){
                    if(data.data.occupyMobile && data.data.occupyRealName){
                        var body = '资质编码已被<span style="color: #0BB9D6">'+data.data.occupyRealName+'</span>占用，请联系客服找回客服电话：400-0505-111';
                        $.alert({
                            title: '提示',
                            body: body
                        });
                    }else {
                        $("#stepBox").show();
                        $("#stepBoxOne").hide();
                        $("#stepTwo").addClass('circle-fill');
                        $("#stepTwoText").addClass('text-online')
                    }
                }else {
                    $.alert({
                        title: '提示',
                        body: data.errorMsg
                    });
                }
            }
        })
    });
    $("#encode").on('input',function () {
        $('#pharmacyMsg').hide();
    });
    $("#entype").change(function(){
        var valStr = $(this).val();
        if(valStr == 4 || valStr == 14 || valStr == 10 || valStr == 9 || valStr == 8 || valStr == 15 || valStr == 6 || valStr == 7){
            $("#codeTitle").html('医疗机构执业许可证编码：')
        }else {
            $("#codeTitle").html('营业执照编码：')
        }
    });
    function updateLicense(param){
        // $.ajax({
        //     type: "POST",
        //     url:  "/merchant/center/licenseAudit/initBillDetailVaild?customerType="+param.customerType+"&type=2",
        //     contentType: "application/json",
        //     success: function(data){
        //         if(data.status == 'success'){
        //             // window.location.href='/merchant/center/licenseAudit/initLicenseAuditDetail?customerType='+param.customerType
        //         }
        //     }
        // })
        $.ajax({
            type: "POST",
            url:  "/merchant/center/licenseAudit/cacheMerchantInfo",
            data: JSON.stringify(param),
            contentType: "application/json",
            success: function(data){
                if(data.status == 'success'){
                    var applicationNumber = $("#applicationNumber").val();
                    var orgCode = $("#orgCode").val();
                    window.location.href='/merchant/center/licenseAudit/initLicenseAuditDetail?customerType='+param.customerType+'&remark=true&applicationNumber='+applicationNumber+'&orgCode='+orgCode+'&invoiceType='+param.invoiceType
                }
            }
        })
    }
</script>
</html>