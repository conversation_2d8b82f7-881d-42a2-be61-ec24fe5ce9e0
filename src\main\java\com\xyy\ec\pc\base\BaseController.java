package com.xyy.ec.pc.base;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.model.dto.XyyIpAddressInfoDTO;
import com.xyy.ec.pc.rpc.ThirdServiceSdk;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.XyyIpAddressService;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.EncodeUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.*;

public class BaseController {


	private static final Logger LOGGER = LoggerFactory.getLogger(BaseController.class);

	@Reference(version = "1.0.0",timeout = 60000)
	private MerchantBussinessApi merchantBussinessApi;
	@Autowired
	private ThirdServiceSdk thirdServiceSdk;
	@Autowired
	private XyyIpAddressService xyyIpAddressService;

	@Autowired
	private XyyIndentityValidator xyyIndentityValidator;

	protected static final String RESULT_STATUS = "status";
	protected static final String RESULT_ERRORMSG = "errorMsg";
	protected static final String RESULT_ERRORCODE = "errorCode";
	protected static final String RESULT_MSG = "msg";
	protected static final String RESULT_SUCCESS = "success";
	protected static final String RESULT_FAILURE = "failure";
	protected static final String CODE = "code";
	protected static final String DATA = "data";
	/** ip测试请求头 */
	private static final String IP_TEST_HEADER_NAME = "testIP";
	/** 成功:1000 */
	protected static final Integer CODE_SUCCESS = 1000;

    /** 失败:9999 */
	protected static final Integer CODE_ERROR = 9999;

    /** 强制返回到登录界面:90000 */
    protected static final Integer CODE_RETURN_TO_LOGIN = 90000;
	/**
	 * 
	 * 添加返回结果
	 * 
	 * @param name
	 *            返回对象的名称
	 * @param value
	 *            需要返回的对象
	 * @return
	 * <AUTHOR>
	 */
	protected Map<String, Object> addResult(String name, Object value) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		responseData.put(CODE, CODE_SUCCESS);
		responseData.put(name, value);
		return responseData;
	}

	protected Map<String, Object> addResult(Map<String, Object> data) {
		Map<String, Object> responseData = new HashMap<>(data);
		responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		responseData.put(CODE, CODE_SUCCESS);
		return responseData;
	}

	protected Map<String, Object> addResultData(Object data) {
		Map<String, Object> responseData = new HashMap<>();
		responseData.put(DATA, data);
		responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		responseData.put(CODE, CODE_SUCCESS);
		return responseData;
	}

	public static Map<String, Object> addResult(String[] names, Object[] values) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		for (int i = 0; i < names.length; i++) {
			responseData.put(names[i], values[i]);
		}
		return responseData;
	}

	public static Map<String, Object> addResult() {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		responseData.put(CODE, CODE_SUCCESS);
		return responseData;
	}

	protected Map<String, Object> addResult(String msg) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		responseData.put(CODE, CODE_SUCCESS);
		responseData.put(RESULT_MSG, msg);
		return responseData;
	}

	/**
	 * 返回响应数据。旨在成功与失败时，响应的数据格式一致。
	 *
	 * @param isSuccess 是否成功，若不填，默认为false，表示失败。
	 * @param msg       信息
	 * @return
	 */
	protected Map<String, Object> addResult(Boolean isSuccess, String msg) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		if (isSuccess != null && isSuccess) {
			responseData.put(RESULT_STATUS, RESULT_SUCCESS);
		} else {
			responseData.put(RESULT_STATUS, RESULT_FAILURE);
		}
		responseData.put(RESULT_MSG, msg);
		return responseData;
	}

	/**
    *
    * 添加返回结果
    *
    * @param name 返回对象的名称
    * @param value 需要返回的对象
    * @return
    * <AUTHOR>
    */
   protected Map<String, Object> addDataResult(String name, Object value) {
       Map<String, Object> responseData = new HashMap<String, Object>();
       responseData.put(RESULT_STATUS, RESULT_SUCCESS);
       Map<String, Object> dataMap = Maps.newHashMap();
       dataMap.put(name, value);
       responseData.put("data", dataMap);
       return responseData;
   }

   /**
   * @Description: 添加返回结果
   * @Param: [names, values]
   * @return: java.util.Map<java.lang.String,java.lang.Object>
   * @Author: xh.zhang
   * @Date: 2018/8/25
   */
   protected Map<String, Object> addDataResult(String[] names, Object[] values) {
       Map<String, Object> responseData = new HashMap<String, Object>();
       responseData.put(RESULT_STATUS, RESULT_SUCCESS);
       responseData.put(CODE, CODE_SUCCESS);
       Map<String, Object> dataMap = Maps.newHashMap();
       for (int i = 0; i < names.length; i++) {
           dataMap.put(names[i], values[i]);
       }
       responseData.put(DATA, dataMap);
       return responseData;
   }
	/**
	 * 
	 * 添加错误信息
	 * 
	 * @param errorMsg
	 *            错误信息
	 * @return
	 * <AUTHOR>
	 */
	protected Map<String, Object> addError(String errorMsg) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		responseData.put(RESULT_ERRORMSG, errorMsg);
		responseData.put(CODE, CODE_ERROR);
		return responseData;
	}

	/**
	 * 添加错误信息
	 * 
	 * @param errorCode
	 *            错误编码
	 * @param errorMsg
	 *            错误信息
	 * @return
	 */
	protected Map<String, Object> addError(int errorCode, String errorMsg) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		responseData.put(RESULT_ERRORCODE, errorCode);
		responseData.put(CODE, errorCode);
		responseData.put(RESULT_ERRORMSG, errorMsg);
		return responseData;
	}
	protected Map<String, Object> getErrorResult(int errorCode, String errorMsg) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		responseData.put(CODE, errorCode);
		responseData.put(RESULT_MSG, errorMsg);
		return responseData;
	}
	public static Map<String, Object> getErrorResult(String errorMsg) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		responseData.put(CODE, CODE_ERROR);
		responseData.put(RESULT_MSG, errorMsg);
		return responseData;
	}
	protected Map<String, Object> addError(String name, Object value) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		responseData.put(name, value);
		return responseData;
	}

	protected Map<String, Object> addError(String[] names, Object[] values) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		for (int i = 0; i < names.length; i++) {
			responseData.put(names[i], values[i]);
		}
		return responseData;
	}

	protected Map<String, Object> addError(String errorMsg, Map<String, Object> data) {
		Map<String, Object> responseData = new HashMap<String, Object>();
		responseData.put(RESULT_STATUS, RESULT_FAILURE);
		responseData.put(CODE, CODE_ERROR);
		responseData.put(RESULT_MSG, errorMsg);
		responseData.putAll(data);
		return responseData;
	}

	/**
	 * 拼接页面参数
	 * 
	 * @param request
	 * @return
	 * @throws UnsupportedEncodingException 
	 */
	protected String getRequestUrl(HttpServletRequest request){
		String url = "";
		String requestUri = request.getRequestURI();
		String queryString = request.getQueryString();
		String qs = StringUtil.removeParameter(queryString, "offset");
        if (requestUri.contains("/xyy-ec-pc/")) {
            requestUri = requestUri.replace("/xyy-ex-pc/", "/");
		}
		if (StringUtil.isNotEmpty(qs)) {
			url = requestUri + "?" + EncodeUtil.urlDecode(qs,"UTF-8");
		} else {
			url = requestUri;
		}
		return url;
	}
	
	/**
     * 得到请求参数的字符串表现形式
     * 
     * @return
     */
	protected String toParameterString(HttpServletRequest request) {
		String URI = request.getRequestURI();
		Map<String, String[]> parameters = request.getParameterMap(); 
        StringBuilder sb = new StringBuilder(URI);
        if (CollectionUtil.isEmpty(parameters)) return sb.toString();
        sb.append("?");
        for (String key : parameters.keySet()) {
            String[] values = getStringArray(key,parameters);
            for (String value : values) {
            	if(sb.toString().contains(key+"=")){
            		String qs = StringUtil.removeParameter(sb.toString(),key);
            	}else{
            		sb.append(key).append("=").append(value).append("&");
            	}
                
            }
        }
        return sb.toString();
    }
	
	public String[] getStringArray(String key,Map<String,String[]> parameters) {
        List<String> values = new ArrayList<String>();
        String[] params = parameters.get(key);
        if (CollectionUtil.isEmpty(params)) return values.toArray(new String[] {});
        for (String param : params) {
            if (StringUtil.isEmpty(param)) continue;
            values.add(param);
        }
        return values.toArray(new String[] {});
    }


	protected void setResultPage(Page retPage, PageInfo merchantForBaseByPage) {
		retPage.setTotal(merchantForBaseByPage.getTotal());
		retPage.setCurrentPage(merchantForBaseByPage.getPageNum());
		retPage.setRows(merchantForBaseByPage.getList());
	}

	protected com.github.pagehelper.Page initPageParam(Page page) {
		if(page==null){
			page=new Page();
		}
		com.github.pagehelper.Page requestPage=new com.github.pagehelper.Page();
		requestPage.setPageNum(page.getOffset());
		requestPage.setPages(page.getLimit());
		return requestPage;
	}

	protected PageInfo buildOnePage(){
		PageInfo pageInfo = new PageInfo();
		pageInfo.setPageNum(1);
		pageInfo.setPageSize(1);
		return pageInfo;
	}

	/**
	 * 请求参数处理
	 * @Title: paramToString
	 * @param request
	 * @return
	 * String
	 * <AUTHOR>
	 * @date 2017-1-15 上午1:51:49
	 */
	protected  String paramToString(HttpServletRequest request) {
		StringBuilder str = new StringBuilder();
		Enumeration<String> e = request.getParameterNames();
		String paramName;
		while (e.hasMoreElements()) {
			paramName = e.nextElement();
			str.append('&').append(paramName).append("=").append(request.getParameter(paramName));
		}
		if (str.length() > 0) {
			str.deleteCharAt(0);
		}
		return str.toString();
	}
	
//	@InitBinder
//	protected void initBinder(ServletRequestDataBinder binder) throws Exception {
//		binder.registerCustomEditor(Integer.class, new IntegerEditor());
//		binder.registerCustomEditor(Long.class, new LongEditor());
//		binder.registerCustomEditor(Double.class, new DoubleEditor());
//		binder.registerCustomEditor(Date.class, new DateEditor());
//	}
//

	/**
	 * 通过用户id获取用户所在的域编码
	 * @param merchantId
	 * @return
	 */
	protected String getBranchCodeByMerchantId(Long merchantId){
		String branchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchantId);
		if(StringUtil.isEmpty(branchCode)){
			branchCode = BranchEnum.HUBEI_COUNTRY.getKey();
		}

		return branchCode;
	}

	/**
	 * 通过用户id获取用户所在的域编码(Long类型) 未登录时按请求ip获取域编码
	 * @param request
	 * @param  merchantId
	 * @return
	 */
	protected String getBranchCodeByMerchantId(HttpServletRequest request, Long merchantId){
		if(merchantId == null || merchantId == 0L){
			XyyIpAddressInfoDTO xyyIpAddressInfo = xyyIpAddressService.getXyyIpAddressInfo(request);
			return xyyIpAddressInfo.getBranchCode();
		}else{
			return getBranchCodeByMerchantId(merchantId);
		}
	}
	protected String getBranchCode(HttpServletRequest request){
		try {
			MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			Long merchantId = 0L;

			if (merchant != null || merchant.getId() != null) {
				merchantId = merchant.getId();
			}
			String branchCode = this.getBranchCodeByMerchantId(request,merchantId);
			return branchCode;
		} catch (Exception e){
			LOGGER.error("#getBranchCode,获取区域编码失败");
			return BranchEnum.HUBEI_COUNTRY.getKey();
		}

	}
}
