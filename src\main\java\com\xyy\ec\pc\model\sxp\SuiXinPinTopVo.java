package com.xyy.ec.pc.model.sxp;

import com.xyy.ec.pc.search.vo.SxpQtDataVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 随心拼加购推荐
 * @date 2022/9/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SuiXinPinTopVo implements Serializable {
    private static final long serialVersionUID = -646969370743850744L;

    /**
     * 商品ID
     */
    private Long skuId;
    /**
     * 已有XXX人 下单
     */
    private String promoTag;

    /**
     * 数据类型
     */
    private Integer sourceType;
    /**
     * 起步数量
     */
    private Integer skuStartNum;
    /**
     * 标签
     */
    private String tag;
    /**
     * 显示名字
     */
    private String showName;

    /**
     *  埋点数据
     */
    private SxpQtDataVo qtData;
}
