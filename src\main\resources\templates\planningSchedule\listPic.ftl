<!DOCTYPE HTML>
<html>
<head>
<#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>我的采购单图片</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="/static/css/user.css?t=${t_v}" />
    <link rel="stylesheet" href="/static/css/myplan.css?t=${t_v}" />
    <script type="text/javascript" src="/static/js/planningSchedule/index-pic.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/planningSchedule/myplan-pic.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/planningSchedule/myplan-add.js?t=${t_v}"></script>
    <script src="/static/js/ajaxfileupload.js"></script>
    <script type="text/javascript">
        var ctx="${ctx}";
    </script>
</head>

<body>
<#include "/planningSchedule/pic.ftl">
<#include "/planningSchedule/picDetails.ftl">
<div class="container">

    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
	<#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->

    <!--主体部分开始-->
    <div class="main">
        <div class="myplan row">
            <!--面包屑-->
            <ul class="sui-breadcrumb">
                <li><a href="/">首页</a>></li>
                <li><a href="/merchant/center/index.htm">用户中心</a>></li>
                <li class="active">我的采购单图片</li>
            </ul>

            <div class="myqual-content clear">
                <div class="side-left fl">
				<#include "/common/merchantCenter/left.ftl" />
                </div>

                <div class="main-right fr">
                    <div class="myplan-buttons clearfix tclearfix">
                        <div class="newplan fl">
                        </div>
                        <div class="plan-anniu tfr">
                            <button class="electronic-Plan"  id="addEcPlanOrder">新建电子计划单</button>
                            <button class="electronic-Plan newplan1" id="btn_add">上传采购单图片</button>
                        </div>
                    </div>
                    <ul class="sui-nav nav-tabs mycoupon-tabs crumbs">
                        <li ><a href="/merchant/center/planningSchedule/index.htm" >我的电子计划单</a></li>
                        <li  class="active" ><a href="javascript:void(0);" >我的采购单图片</a></li>
                    </ul>
                    <div class="myneworder-search clear">
                        <div class="fl">
                            <label>采购单名称：</label>
                            <input type="text" class="inp-num" name="purchaseName" maxlenght="255" id="purchaseName" placeholder="请输入" <#if paramOrder.purchaseName??>value="${paramOrder.purchaseName}"</#if>>
                            <div class="sui-form form-horizontal fr">
                                <div data-toggle="datepicker" class="control-group input-daterange">
                                    <label class="padding-left">提交日期：</label>
                                    <div class="controls">
                                        <input type="text" id="startCreateTime" class="input-medium input-date" <#if paramOrder.startTime??>value="${paramOrder.startTime}" </#if>><span> 到 </span>
                                        <input type="text" id="endCreateTime" class="input-medium input-date" <#if paramOrder.endTime??>value="${paramOrder.endTime}"</#if>>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="myplan-buttons clearfix">
                        <div class="plan-anniu fr">
                            <button class="plan-empty clear-btn fl">清空</button>
                            <button class="search-for query-btn fl">搜索</button>
                        </div>
                    </div>
                    <div class="user-myplan">
                    <table>
                        <thead>
							<th class="myorder-row1">采购单名称</th>
							<th class="myorder-row2">提交日期</th>
							<th class="myorder-row3">图片数量</th>
							<th class="myorder-row4">状态</th>
							<th class="myorder-row4">计划单标题</th>
                        </thead>
						<tbody>
						<#if pager?? && pager.rows??&&(pager.rows?size>0)>
							<#list pager.rows as order>
							<tr class="">

                                <#--data-target="#detailModal" data-toggle="modal"-->
								<td><a href="javascript:void(0)" style="color: #0d77fe" onmouseover="this.style.color='#f39800';" onmouseleave="this.style.color='#0d77fe';" class="adress"  onclick="showDetails('${order.id}')">${order.purchaseName}</a></td>
								<td>${order.subTime?string("yyyy-MM-dd HH:mm:ss")}</td>
								<td>${order.picNum}张</td>
								<td>
                                    <#if (order.planStatus==1)>
                                        已创建电子计划单
                                    <#else> 未创建电子计划单
                                    </#if>
                                </td>
								<td>
                                <#if order.electronicPlanNo??>
                                    <a href="/merchant/center/planningScheduleProduct/index/${order.planningScheduleId}.htm" target="_blank">
                                        ${order.electronicPlanName}
                                    </a>
                                    <#else> -
                                    </#if>
                                </td>
							</tr>
							</#list>
						</tbody>
                    </table>
					<#else>
                        </table>
                        <#--<!--没有订单:   加样式和图片以及事件&ndash;&gt;-->
                        <div class="noplan">
                            <img src="/static/images/user/noplan.png" alt="">
							<#if total == 0>
                                <p>还没任何采购图片</p>
							<#else>
                                <p>暂无记录</p>
							</#if>  -->
                        </div>
					</#if>
                    </div>
                    <!--分页器-->
                    <div class="page">
					<#import "/common/pager.ftl" as p>
					 <@p.pager currentPage=pager.currentPage limit=pager.limit total=pager.total pageCount=pager.pageCount toURL=pager.requestUrl method="get"/>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<!--主体部分结束-->
<!--弹窗提示-->
<div id="delModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">提示</h4>
            </div>
            <div class="modal-body">
                <div class="scbox"></div>
            </div>
            <div class="modal-footer">
                <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large" >确定</button>
                <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
            </div>
        </div>
    </div>
</div>

<div id="confirmOrderGoodsModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">提示</h4>
            </div>
            <div class="modal-body">
                <div class="scbox">请收到商品后，再确认收货，否则您将可能钱货两空！</div>
            </div>
            <div class="modal-footer">
                <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large" >确定</button>
                <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
            </div>
        </div>
    </div>
</div>

<!--底部导航区域开始-->
<div class="footer" id="footer">
<#include "/common/footer.ftl" />
</div>
<!--底部导航区域结束-->
</div>

<div id="successModel" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade">
    <b style="color: #31cb96" id="mol-money" ></b>元余额已到账，请确认查收
</div>
</body>
</html>