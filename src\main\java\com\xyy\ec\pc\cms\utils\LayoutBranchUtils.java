package com.xyy.ec.pc.cms.utils;

import com.xyy.ec.pc.cms.constants.CmsConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 区域工具类
 *
 * <AUTHOR>
 */
@Component
public class LayoutBranchUtils {

    @Autowired
    private LayoutGraySwitchUtils layoutGraySwitchUtils;

    /**
     * 获取真实的区域编码。
     * 若此区域开启一品卖全国灰度开关，则使用全国区域编码，否则使用原区域编码。
     *
     * @param branchCode
     * @return
     */
    public String getRealBranchCode(String branchCode) {
        return CmsConstants.GLOBAL_BRANCH_CODE;
//        if(StringUtils.isEmpty(branchCode)) {
//            return UNIFORM_BRANCH_CODE;
//        }
//        Boolean isOpenGrayByBranchForOnePiece = layoutGraySwitchUtils.isOpenGrayByBranchForOnePiece(branchCode);
//        if(BooleanUtils.isTrue(isOpenGrayByBranchForOnePiece)) {
//            return UNIFORM_BRANCH_CODE;
//        }
//        return branchCode;
    }
}
