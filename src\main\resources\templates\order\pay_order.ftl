<!DOCTYPE HTML>
<html>
<head>
        <#include "/common/common.ftl" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<title>立即支付</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
        <!--
        <link rel="stylesheet" href="/static/css/order/headerAndFooter.css?t=${t_v}" />
        -->
        <link rel="stylesheet" href="/static/css/order/paynext.css?t=${t_v}" />
        <style type="text/css">
            .zffs ul{margin-top: 10px; overflow: inherit;}
            .zffs ul .instantDiscount {
                background: url("//upload.ybm100.com/ybm/app/layout/cmsimages/2023-5/2dd5dbc59af8130ad4f67622c04365bc.png") no-repeat;
                background-size: 100% 100%;
                /* width: 138px; */
                height: 22px;
                font-size: 12px;
                color: #FFFFFF;
                line-height: 22px;
                text-align: center;
                position: absolute;
                right: -2px;
                top: -11px;
                padding-left: 9px;
                padding-right: 6px;
            }
            .bankCardPayment {
                width: 960px;
                border-radius: 2px;
                box-sizing: border-box;
                margin-left: 26px;
                margin-top: 10px;
                border: 2px solid #E2E2E2;
                min-height: 36px;
                visibility: hidden;
            }
            .bankCardPayment:hover {
                border-color: #00dc7d;
            }
            .onBorder {
                border-color: #00dc7d;
            }
            .bankCardPayment .title {
                padding-left: 8px;
                background: #FAFAFA;
            }
            .bankCardPayment .title p {
                height: 36px;
                background: #FAFAFA;
                line-height: 36px;
                font-size: 14px;
                color: #222222;
                display: inline-block;
            }
            .bankCardPayment .title span {
                background: url("//upload.ybm100.com/ybm/app/layout/cmsimages/2023-5/2dd5dbc59af8130ad4f67622c04365bc.png") no-repeat;
                background-size: cover;
                height: 22px;
                font-size: 12px;
                color: #FFFFFF;
                line-height: 22px;
                text-align: center;
                margin-left: 6px;
                padding-left: 9px;
                padding-right: 6px;
                display: inline-block;
            }
            .bankCardPayment .list_item {
                padding: 0 20px;
                padding-right: 0;
            }
            .bankCardPayment .list_item .showMoreDiv {
                overflow: hidden;
            }
            .bankCardPayment .list_item .list_item_one {
                padding: 11px 0;
                border-bottom: 1px #eee dashed;
                height: 46px;
                box-sizing: border-box;
            }
            .bankCardPayment .list_item .list_item_one:last-child {
                border-bottom: none;
            }
            .bankCardPayment .list_item .list_item_one .cardCheckbox {
                width: 16px;
                height: 16px;
                float: left;
                /* vertical-align: middle; */
                margin-top: 4px;
            }
            .bankCardPayment .list_item .list_item_one .cardCheckbox.checked>span:before {
                content: "\e607";
                color: #00DC82;
            }
            .bankCardPayment .list_item .list_item_one img {
                width: 24px;
                height: 24px;
                display: block;
                margin-left: 10px;
                float: left;
            }
            .bankCardPayment .list_item .list_item_one p {
                font-weight: 700;
                font-size: 14px;
                color: #222222;
                letter-spacing: 0;
                line-height: 16px;
                margin-left: 10px;
                min-width: 140px;
                float: left;
                margin-top: 4px;
            }
            .bankCardPayment .list_item .list_item_one .cardType {
                font-size: 14px;
                color: #999;
                letter-spacing: 0;
                line-height: 14px;
                float: left;
                margin-top: 4px;
            }
            .dialog_content {
                width: 246px;
                margin: 0 auto;
            }
            .dialog_content p {
                font-size: 12px;
                color: #999999;
                line-height: 17px;
                justify-content: space-between;
                margin-bottom: 6px;
            }
            .dialog_content .pay {
                width: 246px;
                height: 46px;
                background: #F39800;
                border-radius: 2px;
                margin-top: 12px;
            }
            .dialog_content .tips {
                font-size: 12px;
                color: #E62E2E;
                text-align: center;
                line-height: 16px;
                text-align: center;
                padding: 10px 18px 0 18px;
            }
            .showMore {
                display: -webkit-flex; /* Safari, Chrome, Opera */
                display: -moz-flex; /* Firefox */
                display: -ms-flexbox; /* IE 10 */
                display: flex; /* Standard syntax */
                align-items: center;
                padding: 12px 30px 12px 0px;
                font-size: 14px;
                color: #888888;
                line-height: 14px;
                border-top: 1px #eee dashed;
                cursor: pointer;
            }
            .showMore i {
                border: solid #888888;
                border-width: 0 1px 1px 0;
                display: inline-block;
                padding: 3px;
                margin-left: 5px;
            }
            .showMore .up {
                transform: rotate(-135deg);
                -webkit-transform: rotate(-135deg);
                margin-top: 5px;
            }
            .showMore .down {
                transform: rotate(45deg);
                -webkit-transform: rotate(45deg);
                margin-top: -2px;
            }
            
            .redStr {
                color: red;
                padding: 0 5px;
            }

            .checkboxLabel {
                position: relative;
                cursor: pointer;
            }

            .virtualGoldInput {
                cursor: pointer;
            }

            .virtualGoldInput:checked+.virtualGoldCheckbox {
                background-color: #00dc82;
            }
            .virtualGoldInput:checked+.virtualGoldCheckboxDisable {
                background-color: #00dc82;
            }

            .virtualGoldCheckbox {
                position: absolute;
                top: 2px;
                left: 0;
                width: 16px;
                height: 16px;
                border-radius: 16px;
                border: 1px solid #d8d8d8;
                background: white;
            }

            .virtualGoldCheckbox:before {
                content: '';
                position: absolute;
                top: 2px;
                left: 6px;
                width: 3px;
                height: 8px;
                border: solid white;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
            }
            .virtualGoldCheckboxDisable {
                position: absolute;
                top: 2px;
                left: 0;
                width: 16px;
                height: 16px;
                border-radius: 16px;
                border: 1px solid #d8d8d8;
                background: #e8e8e8;
            }

            .virtualGoldCheckboxDisable:before {
                content: '';
                position: absolute;
                top: 2px;
                left: 6px;
                width: 3px;
                height: 8px;
                border: solid #e8e8e8;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
            }
            #xx-pay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: #27292b8a;
                z-index: -10;
                opacity: 0;
                transition: all 0.3s;
            }
            #xx-pay .xx-input-box {
                position: relative;
                width: max-content;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50px);
                background: white;
                border-radius: 5px;
                padding: 20px;
            }
            #xx-pay .xx-title {
                display: flex;
                justify-content: space-between;
                margin: 10px 0;
                align-items: center;
            }
            #xx-pay .xx-input-box .xx-input {
                outline: none;
                text-align: center;
                border-radius: 5px;
                border: 1px #cdcdcd solid;
                font-size: 30px;
                padding: 0;
                width: 50px;
                height: 50px;
                transition: all 0.1s;
            }
            #xx-pay-submit {
                width: max-content;
                padding: 10px 40px;
                margin: 0 auto;
                background: #2bd975;
                color: white;
                border-radius: 5px;
                cursor: pointer;
            }
        </style>
        <script type="text/javascript" src="/static/js/order/payorder.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/order/paycheck20200326.js?v=${t_v}"></script>
        <script type="text/javascript" src="/static/js/qtShopDetail.js?t=${t_v}"></script>
        <#--  <script type="text/javascript" src="/static/js/toast.js?t=${t_v}"></script>  -->
        <script type="text/javascript">
           var ctx="/static";
        </script>
	</head>
	<body>
		<div class="container">
            <input type="hidden" id="orderno" name="orderno" value="${order.orderNo}">
            <input type="hidden" id="orderNos" name="orderNos" value="${orderNos}">
            <input type="hidden" id="token" name="token" value="${token}" />
            <input type="hidden" id="tranNo" name="tranNo" value="${tranNo}" />
            <input type="hidden" id="useVirtualGold" name="useVirtualGold" value="${useVirtualGold}" />
            <input type="hidden" id="rechargeType" name="rechargeType" value="${rechargeType}" />
            <input type="hidden" id="cashPayAmount" name="cashPayAmount" value="${order.cashPayAmount}" />
			<!--头部导航区域开始-->
			<div class="headerBox" id="headerBox">
			<#include "/common/header.ftl" />
			</div>
			<!--头部导航区域结束-->
            <!--头部步骤条-->
            <div class="topbzbox">
                <div class="warp">
                    <div class="bzcol1"><a href="/"><img src="/static/images/logo_login.png" ></a></div>
                    <div class="bzcol2"></div>
                    <div class="bzcol3">收银台</div>
                    <div class="bzcol4"><img src="/static/images/buzhou3.png" ></div>
                </div>
            </div>

			<!--主体部分开始-->
            <div class="main">
                    <!--立即支付-->
                    <div class="con-box">
                        <div class="infotitle">
                            <i class="sui-icon icon-tb-infofill"></i>您的订单已生成，请尽快完成付款！
                        </div>
                        <div class="warp">
                            <div class="row1">
                                <div class="row1-left">
                                    <#if ((order.orderNo??) || (orderNos??))>
                                        <span class="spew">订单编号：</span>
                                    </#if>
                                    <input type="hidden" id="isList" value="${isList}">
                                    <#if isList = 1>
                                        <span>${order.orderNo }</span>
                                    <#else>
                                        <span>${orderNos }</span>
                                    </#if>
                                </div>
                                <div class="row1-right">
                                    <#if order.createTime??>
                                        <span class="spew">下单时间：</span>
                                        <span>${order.createTime?string('yyyy-MM-dd HH:mm:ss')}</span>
                                    </#if>  
                                </div>
                            </div>
                            <#if order.billInfo??>
                                <div class="row-fapiao">
                                    <span class="spew">发票信息：</span>
                                    <span>${order.billInfo}</span>
                                </div>
                            </#if>
                            <#if  rechargeType?? && rechargeType == 2>
                                <div class="row-fapiao">
                                        <span class="spew">支付项目：</span>
                                        <span>购物金充值</span>
                                </div>
                            </#if>
                            <div class="row2">
                                <#if rechargeType?? && rechargeType == 2>
                                    <span>充值金额：</span>
                                <#else>
                                    <span>实付金额：</span>
                                </#if>
                                <span class="price">￥</span>
                                <#if order.cashPayAmount??>
                                    <#if isList = 1>
                                        <span class="price" id="cashPayAmountDom">${order.cashPayAmount?string("0.00")}</span>
                                    <#else>
                                        <span class="price" id="cashPayAmountDom">${order.cashPayAmount?string("0.00")}</span>
                                    </#if>
                                </#if>
                            </div>
                            <div class="row3">支付方式：</div>
                            <div class="bankCardPayment" id="bankCardPayment">
                                <#if cashier??>
                                <input type="hidden" id="defaultCard" value="${cashier.selectCardId}">
                                    <#list cashier.paymentlist as pl>
                                        <#if pl.paycode == 'jdCardPay'>
                                            <div class="title">
                                                <p>银行卡支付</p>
                                                <#if pl.mktTip != ''>
                                                    <span>${pl.mktTip}</span>
                                                </#if>
                                            </div>
                                        </#if>
                                    </#list>
                                </#if>
                                <div id="vueRender"></div>
                                <div id="dialog" style="position: fixed;width: 100%;height: 100%;
                                top: 0;left: 0;z-index: 100">
                                    <div style="width: 100%;height: 100%;background: #000;opacity: 0.5;"></div>
                                    <div class="dialog_content" style="width: 400px;background: #fff;position:absolute;top: 150px;left: 50%;margin-left: -200px;">
                                        <div style="height: 51px;background: #F5F5F5; text-align: center;line-height: 51px;font-weight: 500;font-size: 20px;position: relative;">请输入支付密码<span id="closeDialog" style="position:absolute; font-size: 18px; top: 0;right: 18px;cursor: pointer;">x</span></div>
                                        <div style="width: 246px;padding: 60px 0 60px 0; margin: auto;">
                                            <p><span>请输入6位支付密码</span><span id="showTips" style="cursor: pointer; float:right">忘记密码？</span></p>
                                            <input id="passInput" maxlength="6" type="password" style="clear: both;width: 100%; height: 48px;" oninput="value=value.replace(/[^\d]/g,'')" onpaste="return false" />
                                            <button id="goPay" style="border: 0;font-size: 18px;color: #FFFFFF;" class="pay">立即支付</button>
                                            <div id="tips" class="tips"></div>
                                        </div>
                                    </div>
                                    <#--  <el-dialog title="请输入支付密码" :visible="showVis" width="400" @close="handleClose"><div class="dialog_content"><p><span>请输入6位支付密码</span><span @click="showTips" style="cursor: pointer">忘记密码？</span></p><el-input v-model="pass" maxlength="6" type="password" style="width: 100%; height: 48px;" @paste.native.capture.prevent="function(){}" @input="handleInput" /><el-button :loading="loading" type="warning" class="pay" @click="handleGoPay">立即支付</el-button><div class="tips">{{ tips }}</div></div></el-dialog>  -->
                                </div>
                            </div>
                            <div class="row4">
                                <!--支付方式-->
                                <div class="zffs">
                                    <ul style="display:inline-block;">
                                        <#if cashier??>
                                            <#list cashier.paymentlist as pl>
                                                <#if pl.isSelected == 1>
                                                    <input id='isSelectedPayment' value="${pl_index}" paycode='${pl.paycode}' type="hidden">
                                                 </#if>
                                                <#if pl.paycode == 'alipay'>
                                                    <li apply-code="1" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                        <#if pl.mktTip != ''>
                                                            <p class="instantDiscount">${pl.mktTip}</p>
                                                        </#if>
                                                        <img src="/static/images/zfb-new.png"  alt="支付宝">
                                                    </li>
                                                </#if>
                                                <#if pl.paycode == 'pcredit'>
                                                    <li apply-code="1" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                        <#if pl.mktTip != ''>
                                                            <p class="instantDiscount">${pl.mktTip}</p>
                                                        </#if>
                                                        <img src="/static/images/huabei-new.png" alt="">
                                                    </li>
                                                </#if>
                                                <#if pl.paycode == 'unionpay'>
                                                    <li apply-code="3" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                        <#if pl.mktTip != ''>
                                                            <p class="instantDiscount">${pl.mktTip}</p>
                                                        </#if>
                                                        <img src="/static/images/yinlian-new.png" alt="">
                                                    </li>
                                                </#if>
                                                <#if pl.paycode == 'weixin'>
                                                    <li apply-code="2" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                        <#if pl.mktTip != ''>
                                                            <p class="instantDiscount">${pl.mktTip}</p>
                                                        </#if>
                                                        <img src="/static/images/weixin-new.png" alt="">
                                                    </li>
                                                </#if>
                                                <#if pl.paycode == 'pingancredit'>
                                                    <#if pl.canUse == 'yes'>
                                                        <li apply-code="4" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <img src="/static/images/pingan.png" alt="">
                                                            <p style="line-height: 30px; text-align:left; position: absolute">当前可用余额：¥${pl.availableAmount }</p>
                                                        </li>
                                                    <#else>
                                                        <div class="wli" style="position:relative">
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <div class="li">
                                                                <img src="/static/images/pingan.png" alt="">
                                                            </div>
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                            <#if pl.tips>
                                                                <p style="line-height: 30px; text-align:left">
                                                                    ${pl.tips }
                                                                    <#if pl.message>
                                                                        <span style="color:#00dc7d;cursor:poninter;margin-left:5px;" onclick="messageDialog('${pl.message}')">查看</span>
                                                                    </#if>
                                                                </p>
                                                            </#if> 
                                                        </div>
                                                    </#if>
                                                </#if>
                                                <#if pl.paycode == 'jdCredit'>
                                                    <#if pl.canUse == 'yes'>
                                                        <li apply-code="12" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <img src="/static/images/jdcredit.png" alt="">
                                                            <p style="line-height: 30px; text-align:left; position: absolute">当前可用余额：¥${pl.availableAmount }</p>
                                                        </li>
                                                    <#else>
                                                        <div class="wli" style="position:relative">
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <div class="li">
                                                                <img src="/static/images/jdcredit.png" alt="">
                                                            </div>
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                            <#if pl.tips>
                                                                <p style="line-height: 30px; text-align:left">
                                                                    ${pl.tips }
                                                                    <#if pl.message>
                                                                        <span style="color:#00dc7d;cursor:poninter;margin-left:5px;" onclick="messageDialog('${pl.message}')">查看</span>
                                                                    </#if>    
                                                                </p>
                                                            </#if> 
                                                        </div>
                                                    </#if>
                                                </#if>
                                                <#if pl.paycode == 'xydLoan'>
                                                    <#if pl.canUse == 'yes'>
                                                        <li apply-code="14" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <img src="/static/images/xydcredit.png" alt="">
                                                            <p style="line-height: 30px; text-align:left; position: absolute">当前可用余额：¥${pl.availableAmount }</p>
                                                        </li>
                                                    <#else>
                                                        <div class="wli" style="position:relative">
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <div class="li">
                                                                <img src="/static/images/xydcredit.png" alt="">
                                                            </div>
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                            <#if pl.tips>
                                                                <p style="line-height: 30px; text-align:left">
                                                                    ${pl.tips }
                                                                    <#if pl.message>
                                                                        <span style="color:#00dc7d;cursor:poninter;margin-left:5px;" onclick="messageDialog('${pl.message}')">查看</span>
                                                                    </#if>
                                                                </p>
                                                            </#if>
                                                        </div>
                                                    </#if>
                                                </#if>
                                                 <#if pl.paycode == 'kingDee'>
                                                    <#if pl.canUse == 'yes'>
                                                        <li apply-code="15" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <img src="/static/images/jindie.png" alt="">
                                                            <p style="line-height: 30px; text-align:left; position: absolute">当前可用余额：¥${pl.availableAmount }</p>
                                                        </li>
                                                    <#else>
                                                        <div class="wli" style="position:relative">
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <div class="li">
                                                                <img src="/static/images/jindie.png" alt="">
                                                            </div>
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                            <#if pl.tips>
                                                                <p style="line-height: 30px; text-align:left">
                                                                    ${pl.tips }
                                                                    <#if pl.message>
                                                                        <span style="color:#00dc7d;cursor:poninter;margin-left:5px;" onclick="messageDialog('${pl.message}')">查看</span>
                                                                    </#if>
                                                                </p>
                                                            </#if>
                                                        </div>
                                                    </#if>
                                                </#if>
                                            </#list>
                                            <#list cashier.extendPaymentList as pl>
                                                 <#if pl.isSelected == 1>
                                                    <input id='isSelectedPayment' value="${pl_index}" paycode='${pl.paycode}' type="hidden">
                                                 </#if>
                                                <#if pl.paycode == 'alipay'>
                                                    <li apply-code="1" data-hide="true" style="display: none; position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                        <#if pl.mktTip != ''>
                                                            <p class="instantDiscount">${pl.mktTip}</p>
                                                        </#if>
                                                        <img src="/static/images/zfb-new.png"  alt="支付宝">
                                                    </li>
                                                </#if>
                                                <#if pl.paycode == 'pcredit'>
                                                    <li apply-code="1" data-hide="true" style="display: none; position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                        <#if pl.mktTip != ''>
                                                            <p class="instantDiscount">${pl.mktTip}</p>
                                                        </#if>
                                                        <img src="/static/images/huabei-new.png" alt="">
                                                    </li>
                                                </#if>
                                                <#if pl.paycode == 'unionpay'>
                                                    <li apply-code="3" data-hide="true" style="display: none; position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                        <#if pl.mktTip != ''>
                                                            <p class="instantDiscount">${pl.mktTip}</p>
                                                        </#if>
                                                        <img src="/static/images/yinlian-new.png" alt="">
                                                    </li>
                                                </#if>
                                                <#if pl.paycode == 'weixin'>
                                                    <li apply-code="2" data-hide="true" style="display: none;position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                        <#if pl.mktTip != ''>
                                                            <p class="instantDiscount">${pl.mktTip}</p>
                                                        </#if>
                                                        <img src="/static/images/weixin-new.png" alt="">
                                                    </li>
                                                </#if>
                                                <#if pl.paycode == 'pingancredit'>
                                                    <#if pl.canUse == 'yes'>
                                                        <li apply-code="4" data-hide="true" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <img src="/static/images/pingan.png" alt="">
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                        </li>
                                                    <#else>
                                                         <div class="wli" style="position:relative">
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <div class="li">
                                                                <img src="/static/images/pingan.png" alt="">
                                                            </div>
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                            <#if pl.tips>
                                                                <p style="line-height: 30px; text-align:left">
                                                                    ${pl.tips }
                                                                    <#if pl.message>
                                                                        <span style="color:#00dc7d;cursor:poninter;margin-left:5px;" onclick="messageDialog('${pl.message}')">查看</span>
                                                                    </#if>
                                                                </p>
                                                            </#if> 
                                                        </div>
                                                    </#if>
                                                </#if>
                                                 <#if pl.paycode == 'jdCredit'>
                                                    <#if pl.canUse == 'yes'>
                                                        <li apply-code="12" data-hide="true" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <img src="/static/images/jdcredit.png" alt="">
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                        </li>
                                                    <#else>
                                                         <div class="wli" style="position:relative">
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <div class="li">
                                                                <img src="/static/images/jdcredit.png" alt="">
                                                            </div>
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                            <#if pl.tips>
                                                                <p style="line-height: 30px; text-align:left">
                                                                    ${pl.tips }
                                                                    <#if pl.message>
                                                                        <span style="color:#00dc7d;cursor:poninter;margin-left:5px;" onclick="messageDialog('${pl.message}')">查看</span>
                                                                    </#if>
                                                                </p>
                                                            </#if> 
                                                        </div>
                                                    </#if>
                                                </#if>
                                                <#if pl.paycode == 'xydLoan'>
                                                    <#if pl.canUse == 'yes'>
                                                        <li apply-code="14" data-hide="true" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <img src="/static/images/xydcredit.png" alt="">
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                        </li>
                                                    <#else>
                                                        <div class="wli" style="position:relative">
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <div class="li">
                                                                <img src="/static/images/xydcredit.png" alt="">
                                                            </div>
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                            <#if pl.tips>
                                                                <p style="line-height: 30px; text-align:left">
                                                                    ${pl.tips }
                                                                    <#if pl.message>
                                                                        <span style="color:#00dc7d;cursor:poninter;margin-left:5px;" onclick="messageDialog('${pl.message}')">查看</span>
                                                                    </#if>
                                                                </p>
                                                            </#if>
                                                        </div>
                                                    </#if>
                                                </#if>
                                                <#if pl.paycode == 'kingDee'>
                                                    <#if pl.canUse == 'yes'>
                                                        <li apply-code="15" data-hide="true" style="position:relative" <#if pl.isSelected == 1>class="cur"</#if>>
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <img src="/static/images/jindie.png" alt="">
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                        </li>
                                                    <#else>
                                                        <div class="wli" style="position:relative">
                                                            <#if pl.mktTip != ''>
                                                                <p class="instantDiscount">${pl.mktTip}</p>
                                                            </#if>
                                                            <div class="li">
                                                                <img src="/static/images/jindie.png" alt="">
                                                            </div>
                                                            <p style="line-height: 30px; text-align:left">当前可用余额：¥${pl.availableAmount }</p>
                                                            <#if pl.tips>
                                                                <p style="line-height: 30px; text-align:left">
                                                                    ${pl.tips }
                                                                    <#if pl.message>
                                                                        <span style="color:#00dc7d;cursor:poninter;margin-left:5px;" onclick="messageDialog('${pl.message}')">查看</span>
                                                                    </#if>    
                                                                </p>
                                                            </#if>
                                                        </div>
                                                    </#if>
                                                </#if>
                                            </#list>
                                        <#else>
                                            <!--支付宝支付apply-code=1-->
                                            <li class="cur" apply-code="1"><img src="/static/images/zfb-new.png"  alt="支付宝"></li>
                                            <!--花呗支付apply-code=4-->
                                            <li apply-code="1"><img src="/static/images/huabei-new.png" alt=""></li>
                                            <!--银联支付apply-code=3-->
                                            <li apply-code="3"><img src="/static/images/yinlian-new.png" alt=""></li>
                                            <!--微信支付apply-code=2-->
                                            <li apply-code="2"><img src="/static/images/weixin-new.png" alt=""></li>
                                            <!--平安贷apply-code=2-->
                                            <li apply-code="4"><img src="/static/images/pingan.png" alt=""></li>
                                            <#--  京东采购融资  -->
                                            <li apply-code="12"><img src="/static/images/jdcredit.png" alt=""></li>
                                            <#--  小雨点白条  -->
                                            <li apply-code="14"><img src="/static/images/xydcredit.png" alt=""></li>
                                             <#--  金蝶  -->
                                            <li apply-code="15"><img src="/static/images/jindie.png" alt=""></li>
                                        </#if>
                                    </ul>
                                    <#if cashier?? && cashier.enableExtend == 1>
                                        <#--  <div class="zhifu-more">
                                            <span class="more-text">更多方式</span>
                                            <img src="/static/images/zhifu-more.png" alt="">
                                        </div>  -->
                                    </#if>
                                </div>
                            </div>
                            <div class="row5" hidden="hidden">
                                <div class="controls">
                                    <input type="hidden" id="orderId" placeholder="" data-rules="required" name="orderId" value="${order.id}" />
                                </div>
                            </div>
                            <#if tips?? >
                                <div class="row7">
                                    <!--温馨提示-->
                                    <div class="wxts" style="padding-left: 26px;margin-top: 15px;color:#ff0000;">
                                        <span class="spew">温馨提示：当前订单金额较大，建议您使用微信/支付宝进行支付</span>
                                    </div>
                                </div>
                            </#if>
                            <div class="row3 availVirtualGoldCon" style="padding: 5px 0;border: 1px dashed #e6e6e6;">
                                <#-- 购物金 order.availVirtualGold?string('0.00') -->
                                <div class="my-balance" style="padding-left: 20px">
                                    <div class="address-top-title" style="margin-bottom: 20px">我的购物金</div>
                                    <label class="checkboxLabel">
                                        <#if cashier.virtualGoldPayment?? && (cashier.virtualGoldPayment.availableVirtualGold > 0)>
                                                <#if cashier.virtualGoldPayment?? && cashier.virtualGoldPayment.goldTips?has_content>
                                                    <input class="virtualGoldInput hasGoldTips" id="virtualGoldInput" type="checkbox"
                                                            value="${cashier.virtualGoldPayment.availableVirtualGold}"
                                                        disabled style="opacity: 0;">
                                                <#else>
                                                    <input class="virtualGoldInput" id="virtualGoldInput" type="checkbox"
                                                        value="${cashier.virtualGoldPayment.availableVirtualGold}"
                                                        <#if (cashier.virtualGoldPayment.isSelected == 1)>checked="checked"</#if> 
                                                        style="opacity: 0;">                                               
                                                </#if>
                                            <#else>
                                                <input class="virtualGoldInput" id="virtualGoldInput" type="checkbox" value="0"
                                                  disabled="disabled"  style="opacity: 0;">
                                        </#if>
                                            <#if cashier.virtualGoldPayment?? && cashier.virtualGoldPayment.goldTips?has_content>
                                            <span class="virtualGoldCheckboxDisable" />
                                            <#else>
                                            <span class="virtualGoldCheckbox" />
                                            </#if>
                                        
                                    </label>
                                    <#if cashier.virtualGoldPayment?? && cashier.virtualGoldPayment.virtualGoldTips??>
                                        <span class="virtualGoldTips" style="padding-left: 10px">
                                            ${cashier.virtualGoldPayment.virtualGoldTips}
                                        </span>
                                    </#if>
                                    <#--  <span style="padding-left: 10px">剩余<span
                                            class="redStr" id="totalVirtualGold">
                                            ${((order.totalVirtualGold)!0)?string('0.00')}
                                        </span>元，可抵扣<span
                                            class="redStr"
                                            id="availVirtualGold">
                                            ${((order.availVirtualGold)!0)?string('0.00')}
                                        </span>元</span>  -->
                                        <#if cashier.virtualGoldPayment?? && cashier.virtualGoldPayment.goldTips?has_content>
                                            <div style="margin-top:10px;color:#a9a8a8"><span class="gwj-tips" onclick="openGwjWin('${cashier.virtualGoldPayment.goldTips}')">?</span> 含购物金不可购买店铺，暂不可用</div>
                                        </#if>                        
                                </div>
                            </div>
                            <div class="row6">
								<a href="javaScript:void(0); "  id="pay_btn">立即支付</a>
								<span id="timer" data-timer="${order.payEndTime}0000">
											<!--倒计时初始值设置data-timer="20161215000000"-->
                                            <span class="f18">剩余支付时间：</span>
											<span class="f24" id="timer_d">00</span>
											<span class="f18">天</span>
                                            <span class="f24" id="timer_h">23</span>
											<span class="f18">时</span>
											<span class="f24" id="timer_m">24</span>
											<span class="f18">分</span>
											<span class="f24" id="timer_s">00</span>
											<span class="f18">秒</span>
							     </span>
                            </div>
                        </div>
                    </div>
			</div>
			<!--主体部分结束-->
			<!--底部导航区域开始-->
			<div class="footer" id="footer">
                <!--底部导航区域开始-->
				<#include "/common/footer.ftl" />
			</div>
			<!--底部导航区域结束-->
		</div>
        <!--支付弹窗-->

        <!--支付弹窗-->
        <div id="payModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="colsebox">
                        <a href="#" class="close-btn"><img src="/static/images/cha.png" alt=""></a>
                    </div>

                    <div class="titleinfo">
                        <span>请在</span>
                        <span class="red">${payFinalHour}</span>
                        <span>前完成付款，否则订单会被系统取消！</span>
                    </div>
                    <div class="zf-lbox  fl">
                        <div class="title">支付成功请点击</div>
                        <a href="javaScript:void(0); "  id="payfin_btn">已完成付款</a>
                    </div>
                    <div class="zf-rbox  fl">
                        <div class="title">付款遇到问题请点击</div>
                        <a href="javaScript:void(0); "  id="payagain_btn">重新付款</a>
                    </div>
                </div>
                <div class="bottom1 ">
                    注：重新付款前，请关闭之前的付款页面
                </div>
                <div class="bottom2 ">
                    如有疑问或需要帮助，请联系
                    <a href="javaScript:callKf('','${merchant.id}');" name="call_custom">在线客服</a>
                </div>

            </div>
        </div>

        <!-- 认证手机号验证弹窗-->
        <div id="vertifyModal" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 id="myModalLabel" class="modal-title">认证手机号</h4>
                    </div>
                    <div class="modal-body">
                        <p class="phone-modal-title vertifyModalTitle"></p>
                        <div class="auth-form">
                            <ul>
                                <li>
                                    <div class="label"><b style="color: #f00;">*</b>验证码</div>
                                    <div class="input-box">
                                        <input type="text" class="auth-input-left" placeholder="请输入" id="codeNum" maxlength="4"><a href="javascript:;" class="code-btn1">重新发送</a><a href="javascript:;" class="yzmbox-repe1"><span class="datasub1">120</span>s重新发送</a>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large" id="modifyPhone">更换手机号</button>
                        <button type="button" class="sui-btn btn-primary btn-large" id="smsAuthentication">提交</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 更改手机号弹窗-->
        <div id="phoneModal" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 id="myModalLabel" class="modal-title">更换手机号</h4>
                    </div>
                    <div class="modal-body">
                        <p class="phone-modal-title phoneModalTitle">请输入当前账户${mobile}的登录密码</p>
                        <div class="auth-form">
                            <ul>
                                <li>
                                    <div class="label"><b style="color: #f00;">*</b>登录密码</div>
                                    <div class="input-box">
                                        <input oninput="checkPwdInput()" onpropertychange="checkPwdInput()" type="password" class="auth-input show-eyes" placeholder="请输入当前账号登录密码" id="password" >
                                        <i class="iconfont icon-biyanjing" id="togglePassword"></i>
                                    </div>
                                </li>
                                <li>
                                    <div class="label"><b style="color: #f00;">*</b>新手机号</div>
                                    <div class="input-box">
                                        <input oninput="checkPhoneInput()" onpropertychange="checkPhoneInput()" type="text" class="auth-input-left" placeholder="请输入" id="phoneNum" maxlength="11"><a href="javascript:;" class="code-btn">发送验证码</a><a href="javascript:;" class="yzmbox-repe"><span class="datasub">120</span>s重新发送</a>
                                    </div>
                                </li>
                                <li>
                                    <div class="label"><b style="color: #f00;">*</b>验证码</div>
                                    <div class="input-box">
                                        <input type="text" class="auth-input" placeholder="请输入" id="code" onpropertychange="checkCodeInput()" oninput="checkCodeInput()" maxlength="10">
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                        <button type="button" class="sui-btn btn-primary btn-large submit-btn" id="changeAuthenticationMobile">提交</button>
                    </div>
                </div>
            </div>
        </div>
        <#--  联合登陆form提交  -->
         <form id="scenceForm" action="https://test-api.jddglobal.com/shapi/v1/user/register" method="post" target="_blank">
            <input type="hidden" name="jrgw-user-id-type" id="jrgw-user-id-type" value="0" />
            <input type="hidden" name="gw-encrypt-type" id="gw-encrypt-type"  value="" />
            <input type="hidden" name="gw-sign-type" id="gw-sign-type" value="" />
            <input type="hidden" name="encrypt" id="encrypt" value="" />
            <input type="hidden" name="gw-sign" id="gw-sign" value="=" />
            <input type="hidden" name="jrgw-request-time" id="jrgw-request-time" value="" /> 
            <input type="hidden" readonly="readonly" name="jrgw-enterprise-user-id" id="jrgw-enterprise-user-id" value="" />
            <button type="submit" style="visibility: hidden" id="submitForm"></button>
         </form>
         <div id="xx-pay" class="xx-mask">
            <div class="xx-input-box">
                <div class="xx-title">
                    <span style="font-size:18px;">验证购物金支付密码</span>
                    <div id="xx-pay-close" style="cursor:pointer;">
                        <svg focusable="false" class="" data-icon="close" width="1em" height="1em" fill="currentColor" aria-hidden="true" fill-rule="evenodd" viewBox="64 64 896 896"><path d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"></path></svg>
                    </div>
                </div>
                <div id="inputBox" style="display:flex;gap:20px;margin-bottom:10px;">
                    <input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="0" aria-autocomplete="none">
                    <input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="1" aria-autocomplete="none">
                    <input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="2" aria-autocomplete="none">
                    <input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="3" aria-autocomplete="none">
                    <input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="4" aria-autocomplete="none">
                    <input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="5" aria-autocomplete="none">
                </div>
                <div class="xx-title">
                    <span  id="xx-pay-msg" style="color:#ec3d3d;font-size:14px;"></span>
                    <span id="xx-pay-forget" style="color:#00c200">忘记密码?</span>
                </div>
                <div id="xx-pay-submit">
                    <span>立即支付</span>
                </div>
            </div>
        </div>
	</body>
    <script>
        /*更多支付方式*/

        /*校验必填项是否未填*/
        var flags = [false,false,false];
        function enableSubmit(bool){
            // if(bool){
            //     $(".submit-btn").addClass("high-light")
            // }else {
            //     $(".submit-btn").removeClass("high-light")
            // }
            var btn = document.getElementById('changeAuthenticationMobile');
            if(bool){
                btn.className ="sui-btn btn-primary btn-large submit-btn high-light";
                // btn.setAttribute('class','submit-btn high-light')
                // $(".submit-btn").addClass("high-light")
            }else {
                // $(".submit-btn").removeClass("high-light")
                btn.className ="sui-btn btn-primary btn-large submit-btn";
                // btn.setAttribute('class','submit-btn')
            }
        }
        function submitbutton(){
            for(f in flags) {
                if(!flags[f]) {
                    enableSubmit(false);
                    return;
                }
            }
            enableSubmit(true);
        }
        function checkPwdInput(){
            var name = $("#password").val();
            if(name==null||name==""){
                flags[0]=false;
                enableSubmit(false);
            }else{
                flags[0] = true;
            }
            submitbutton();
        }
        function checkPhoneInput(){
            var phoneNum = $("#phoneNum").val();
            if(phoneNum==null||phoneNum==""){
                flags[1]=false;
                enableSubmit(false);
            }else{
                flags[1] = true;
            }
            submitbutton();
        }
        function checkCodeInput(){
            var code = $("#code").val();
            if(code==null||code==""){
                flags[2]=false;
                enableSubmit(false);
            }else{
                flags[2] = true;
            }
            submitbutton();
        }
        function messageDialog(message){
            $.alert({
                title: '不可用店铺',
                body: message
            })
        }
        function openGwjWin(str){
            layer.open({
                type: 1,
                title: '温馨提示',
                closeBtn: 1,
                shadeClose: false,
                skin: 'openAccountExplainDialogContent',
                area: ['400px', ''],
                content: '<div style="padding: 15px">' + str + '</div>',
                btn: '确定',
                success: function (lay) {
                    lay.find('.layui-layer-btn0').css({'background':'#00DC7D',"border":"none"})
                }
            });
        }
        window.addScmeV3 = function(type) {
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                let result = '';
                //14位
                if(type==1){
                    for (let i = 0; i < 14; i++) {
                        result += chars.charAt(Math.floor(Math.random() * chars.length));
                    }
                    return result;
                }
                //6位
                if(type==2){
                    for (let i = 0; i < 6; i++) {
                        result += chars.charAt(Math.floor(Math.random() * chars.length));
                    }
                    return result;
                }
                //8位
                if(type==3){
                    for (let i = 0; i < 8; i++) {
                        result += chars.charAt(Math.floor(Math.random() * chars.length));
                    }
                    return result;
                }
            }

        //qt埋点三期
        //页面曝光
        function pageExposure(){
            try{
            let spm_cnt = '1_4.' + "orderPendingPayment_"+($('#orderNos').val()||$('#orderno').val())+"-0_0." + '0.' + '0.' + window.getSpmE()
            let data = {
                spm_cnt: spm_cnt
            }
            aplus_queue.push({
                action: 'aplus.record',
                arguments: ['page_exposure', 'EXP', data]
            })
            }catch(e){
                console.log(e)
            }
        }
        $(document).ready(function(){
            pageExposure()
        })

        //qt埋点三期
        function payBtnClickQt(){
                try{
                let spm_cnt = "1_4." + "orderPendingPayment_"+($('#orderNos').val()||$('#orderno').val())+"-0_0." + "payOrder@Z." + "btn@1." + window.getSpmE()
                let scm_cnt = "pcFE." + "0." + "all_0." + "text-立即支付." + window.addScmeV3(1)
                let data = {
                    spm_cnt: spm_cnt,
                    scm_cnt: scm_cnt
                }
                window.scmAndSpm=data
                aplus_queue.push({
                    action: 'aplus.record',
                    arguments: ['action_sub_module_click', 'CLK', data]
                })
                }catch(e){
                    console.log(e)
                }
        }
        </script>
    <script src="/static/js/plugins/aes.js" type="text/javascript"></script>
</html>