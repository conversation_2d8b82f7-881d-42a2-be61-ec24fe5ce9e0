package com.xyy.ec.pc.controller.search;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.api.rpc.order.OrderRpcService;
import com.xyy.ec.base.framework.exception.search.XyyEcSearchBizCheckException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.client.api.MarketingQueryApi;
import com.xyy.ec.marketing.client.dto.ActInfoDTO;
import com.xyy.ec.marketing.client.dto.ActSkuInfoDTO;
import com.xyy.ec.marketing.client.dto.AppProductPackageDto;
import com.xyy.ec.marketing.client.dto.AppProductPackageSkuDTO;
import com.xyy.ec.marketing.client.dto.param.ActInfoQueryDTO;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingSkuDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.dto.ecp.orderforpromotion.PromotionMsg;
import com.xyy.ec.order.search.api.remote.dto.OrderSkuStatisticsQueryDto;
import com.xyy.ec.order.search.api.remote.result.OrderSkuBuyRecordResultDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.base.PageGross;
import com.xyy.ec.pc.config.*;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.ProductGiveEnum;
import com.xyy.ec.pc.enums.SnowGroundTypeEnum;
import com.xyy.ec.pc.enums.TerminalTypeEnum;
import com.xyy.ec.pc.enums.marketing.MarketingSeckillActivityStatusEnum;
import com.xyy.ec.pc.model.ProductPcDto;
import com.xyy.ec.pc.model.search.SearchSkuListParam;
import com.xyy.ec.pc.param.MaidianParams;
import com.xyy.ec.pc.remote.ProductExhibitionGroupBusinessAdminRemoteService;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.rpc.OrderSearchRpcService;
import com.xyy.ec.pc.rpc.ProductServiceRpc;
import com.xyy.ec.pc.rpc.ShopServiceRpc;
import com.xyy.ec.pc.search.service.SkuProductService;
import com.xyy.ec.pc.search.vo.ComparePricesProdcutInfoVo;
import com.xyy.ec.pc.search.vo.MaiDianVo;
import com.xyy.ec.pc.search.vo.PcSearchProductInfoVo;
import com.xyy.ec.pc.search.vo.SeckillActInfoVo;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.pc.vo.EcProductInfoVO;
import com.xyy.ec.pc.vo.EcPtActInfoVO;
import com.xyy.ec.product.business.api.HostSearchBusinessApi;
import com.xyy.ec.product.business.api.ProductBusinessApi;
import com.xyy.ec.product.business.api.ProductCompanyBusinessApi;
import com.xyy.ec.product.business.api.SkuInstructionImageBusinessApi;
import com.xyy.ec.product.business.api.ecp.skucategory.EcpCategoryBusinessApi;
import com.xyy.ec.product.business.api.ecp.skucategory.EcpCategoryRelationBusinessApi;
import com.xyy.ec.product.business.constants.TagSortEnum;
import com.xyy.ec.product.business.dto.*;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import com.xyy.ec.product.business.dto.pop.CompanyListVO;
import com.xyy.ec.product.business.dto.product.ProductActivityTag;
import com.xyy.ec.product.business.ecp.out.product.ProdcutApi;
import com.xyy.ec.product.business.ecp.productmerchant.api.EcpProductMerchantBusinessApi;
import com.xyy.ec.product.business.ecp.productmerchant.dto.ProductMerchantBusinessDTO;
import com.xyy.ec.product.business.enums.SourceFromEnum;
import com.xyy.ec.product.business.module.CategoryVo;
import com.xyy.ec.search.engine.api.EcHotWordsApi;
import com.xyy.ec.search.engine.api.EcSearchApi;
import com.xyy.ec.search.engine.constants.SearchConstants;
import com.xyy.ec.search.engine.dto.SearchCsuDTO;
import com.xyy.ec.search.engine.entity.*;
import com.xyy.ec.search.engine.enums.CsuOrder;
import com.xyy.ec.search.engine.enums.SortOrder;
import com.xyy.ec.shop.server.business.enums.ShopPropertyEnum;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.ec.system.business.api.BranchBusinessApi;
import com.xyy.ms.promotion.business.common.constants.VoucherEnum;
import com.xyy.ms.promotion.business.enums.MarketingActivitySourceTypeEnum;
import com.xyy.recommend.shop.api.RecommendedApi;
import com.xyy.recommend.shop.enums.PageEnum;
import com.xyy.recommend.shop.enums.SourceEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PC商品搜索页控制层
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/search")
public class SkuSearchController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SkuSearchController.class);
    @Reference(version = "1.0.0", timeout = 10000)
    EcpCategoryBusinessApi categoryBusinessApi;
    @Reference(version = "1.0.0", timeout = 10000)
    ProductBusinessApi productBusinessApi;
    @Reference(version = "1.0.0", timeout = 10000)
    EcpCategoryRelationBusinessApi categoryRelationBusinessApi;
    @Reference(version = "1.0.0", timeout = 10000)
    BranchBusinessApi branchBusinessApi;
    @Reference(version = "1.0.0")
    HostSearchBusinessApi hostSearchBusinessApi;
    @Reference(version = "1.0.0")
    SkuInstructionImageBusinessApi skuInstructionImageBusinessApi;
    @Reference(version = "1.0.0")
    EcSearchApi searchApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Reference(version = "1.0.0")
    private ProductCompanyBusinessApi companyService;
    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;
    @Reference(version = "1.0.0", timeout = 10000)
    private MerchantBussinessApi merchantBussinessApi;
    @Reference(version = "1.0.0")
    private EcpProductMerchantBusinessApi ecpProductMerchantBusinessApi;
    @Autowired
    private PcVersionUtils appVersionUtils;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private ShopServiceRpc shopServiceRpc;

    @Reference(version = "1.0.0")
    private ProdcutApi prodcutApi;

    @Reference(version = "1.0.0")
    private RecommendedApi recommendedApi;

    @Autowired
    private Config config;

    @Autowired
    private XyyConfig xyyConfig;

    private int grossCount = 0;

    private int otherCount = 0;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${xyy.ec.search.gray-scale}")
    private Integer searchGrayScale;

    @Value("${xyy.ec.search.gray-white-list}")
    private String whiteList;

    @Autowired
    private SearchUtils searchUtils;

    @Reference(version = "1.0.0")
    private EcHotWordsApi ecHotWordsApi;

    @Reference(version = "1.0.0")
    private MarketingQueryApi marketingQueryApi;

    @Value("${xyy.ec.promotion.package-url}")
    private String pcPackageUrl;

    @Value("${xyy.ec.promotion.package-gray-scale}")
    private String packageGrayScale;

    @Value("${pc.shop.index.url}")
    private String pcShopUrl;

    @Value("${pc.shop.pintuan.index.url}")
    private String pcPinTuanShopUrl;

    @Value("${xyy.app.sku.detail.group.buying.image}")
    private String skuGroupBuying;

    @Autowired
    OrderRpcService orderRpcService;
    @Autowired
    private AppProperties appProperties;
    @Autowired
    private OrderSearchRpcService orderSearchRpcService;

    @Autowired
    private ProductExhibitionGroupBusinessAdminRemoteService productExhibitionGroupBusinessAdminRemoteService;

    @Autowired
    private ProductServiceRpc productServiceRpc;

    @Autowired
    SkuProductService skuProductService;
    @Value("${pc.sku.detail.remove.special.coupon.switch:true}")
    private Boolean removeSpecialTagSwitch;


    /**
     * 关键字获取
     *
     * @param skuPOJO
     * @param request
     * @return
     */
    @RequestMapping("/getKeywords.json")
    @ResponseBody
    public Object getKeywords(SkuConditionDto skuPOJO, HttpServletRequest request) {
        try {
            //搜索关键词
            String keyword = request.getParameter("keyword");

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            //用户所属域的域编码
            String branchCode = null;
            Integer flag = null;
            if (merchant != null) {
                branchCode = merchant.getRegisterCode();
            } else {
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            }
            skuPOJO.setBranchCode(branchCode);
            /***********处理搜索关键词**************/
            //推荐搜索的关键词
            List<Map> listKeywords = null;
            if (StringUtil.isNotEmpty(keyword)) {
                skuPOJO.setShowName(keyword);
                listKeywords = productBusinessApi.findSkuName(skuPOJO);
                if (CollectionUtil.isNotEmpty(listKeywords)) {
                    if (listKeywords.size() > 5) {
                        listKeywords = listKeywords.subList(0, 5);
                    }
                }
            }
            return this.addDataResult("keywordList", listKeywords);
        } catch (Exception e) {
            LOGGER.error("/search/getKeywords.json-获取商品关键词异常", e);
            return this.addError("获取商品关键词失败！");
        }
    }

    /**
     * 获取sid
     *
     * @return
     */
    @RequestMapping("/getSid.json")
    @ResponseBody
    public Object getSid() {
        Long merchantId = 0L;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant != null) {
                merchantId = merchant.getId();
            }
        } catch (Exception ex) {
            LOGGER.warn("获取用户信息异常 : ", ex);
        }
        String sidData = SearchUtils.generateSidData(merchantId, TerminalTypeEnum.PC.getValue());
        return this.addDataResult("sid", sidData);


    }

    /**
     * 全部药品列表 新搜索接口
     *
     * @param page
     * @param skuPOJO
     * @return
     */
    public ModelAndView newSkuInfo(Page page, SearchCsuDTO skuPOJO, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            Long merchantId = 0L;
            //用户所属域的域编码
            String branchCode = null;
            Integer flag = null;
            if (merchant != null) {
                merchantId = merchant.getId();
                branchCode = merchant.getRegisterCode();
            } else {
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            }
            if (StringUtil.isNotEmpty(skuPOJO.getKeyword()) && ObjectUtils.isEmpty(skuPOJO.getType())) {
                skuPOJO.setType(1);
            }
            //判断是否KA用户
            if (merchant != null && merchant.getIsKa()) {
                skuPOJO.setHasGuidePrice(true);
            }
            //设置用户ID
            skuPOJO.setMerchantId(merchantId);
            if (StringUtil.isNotEmpty(skuPOJO.getKeyword()) && merchantId != 0L) {
                ProductBusinessDto productBusinessDto = new ProductBusinessDto();
                productBusinessDto.setShowName(skuPOJO.getKeyword());
                Long finalMerchantId = merchantId;
                RpcContext.getContext().setAttachment("async", "true").asyncCall(() -> {
                    try {
                        ecHotWordsApi.saveHistoryWord(finalMerchantId, skuPOJO.getKeyword());
                    } catch (Exception e) {
                        LOGGER.error("saveHistoryWord error", e);
                    }
                });
                RpcContext.getContext().removeAttachment("async");
            }

            skuPOJO.setBranchCode(branchCode);

            Map<String, Object> objModel = new HashMap<>();
            // 分页要用，将url传到前台
            String url = getRequestUrl(request);

            //搜索关键词
            String keyword = request.getParameter("keyword");
            /***********处理搜索关键词**************/
            if (StringUtil.isNotEmpty(keyword)) {
                skuPOJO.setKeyword(keyword);
            }
            //大图模式和列表模式的类型
            String modelTypeStr = request.getParameter("model_type");
            /*********默认大图模式展示搜索页********/
            int modelType = 1;
            if (StringUtil.isNotEmpty(modelTypeStr)) {
                modelType = Integer.parseInt(modelTypeStr);
            }

            //筛选类目展开状态
            String cjZhan = request.getParameter("cjZhan");
            String ejZhan = request.getParameter("ejZhan");
            String sjZhan = request.getParameter("sjZhan");
            //店铺展开状态
            String merchantZhan = request.getParameter("merchantZhan");
            //规格展开状态
            String specZhan = request.getParameter("specZhan");

            /****************处理商品分类**********************/
            //一级分类筛选条件
            String categoryFirstId = request.getParameter("categoryFirstId");
            //二级分类筛选条件
            String categorySecondId = request.getParameter("categorySecondId");
            //三级分类筛选条件
            String categoryThirdId = request.getParameter("categoryThirdId");
            if ("99999".equals(categoryFirstId)) {//当选择的为全部分类时，二级分类和三级分类置空，且展开状态为关闭
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                    categorySecondId = null;
                }
                if (StringUtil.isNotEmpty(categoryThirdId)) {
                    url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                    categoryThirdId = null;
                }
                ejZhan = "2";
                sjZhan = "2";

            }

            Map<Long, CategoryVo> categoryVoMap = categoryBusinessApi.getCategory(branchCode);

            //处理分类的筛选条件---当所选的分类的父类与所选的父类不对应时，清楚该分类的筛选
            //当所选的三级分类不为空时进行判断
            if (StringUtil.isNotEmpty(categoryThirdId)) {
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    //得到所选三级分类信息
                    CategoryBusinessDTO c3 = categoryVoMap.get(Long.parseLong(categoryThirdId));
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {//当所选一级分类不为空时，所选的二三级分类都要和一级分类比较
                        //得到所选二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(Long.parseLong(categorySecondId));
                        //如果所选二级分类的父类即一级分类也不是所选的一级分类，则将二级分类的筛选条件也去掉
                        if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                            categorySecondId = null;
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        } else {
                            //如果所选三级分类的父类即二级分类不是所选的二级分类，则将三级分类的筛选条件去掉
                            if (c3 != null && Long.parseLong(categorySecondId) != c3.getParentId()) {
                                url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                                categoryThirdId = null;
                            }
                        }
                    } else {
                        //如果所选三级分类的父类即二级分类不是所选的二级分类，则将三级分类的筛选条件去掉
                        if (c3 != null && Long.parseLong(categorySecondId) != c3.getParentId()) {
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        }
                    }
                } else {//当所选二级分类为空时，只和所选一记分类比较
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {
                        //得到所选的三级分类信息
                        CategoryBusinessDTO c3 = categoryVoMap.get(Long.parseLong(categoryThirdId));
                        if (c3 != null) {
                            //得到所选三级分类的二级分类信息
                            CategoryBusinessDTO c2 = categoryVoMap.get(c3.getParentId());
                            //如果所选三级分类的二级分类的父类即一级分类不是所选的一级分类，则将三级分类的筛选条件去掉
                            if (c2 != null && Long.parseLong(categoryFirstId) != c2.getParentId()) {
                                url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                                categoryThirdId = null;
                            }
                        }
                    }
                }
            } else {
                //当所选的二级分类不为空时进行判断
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {
                        //得到所选二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(Long.parseLong(categorySecondId));
                        //如果所选二级分类的父类即一级分类不是所选的一级分类，则将二级分类的筛选条件去掉
                        if (c2 != null && Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                            categorySecondId = null;
                        }
                    }
                }
            }

            //处理所选的一级分类
            if (StringUtil.isNotEmpty(categoryFirstId)) {
                if (!"99999".equals(categoryFirstId)) {
                    skuPOJO.setCategoryFirstId(Long.parseLong(categoryFirstId));
                } else {
                    //当一级分类的筛选条件为99999事搜索的是全部药品，分类的筛选条件置空
                    skuPOJO.setCategoryFirstId(null);
                    skuPOJO.setCategorySecondId(null);
                    skuPOJO.setCategoryThirdId(null);
                }
            }

            //分类树
            //获取缓存中的商品分类集合
            LOGGER.info("merchantId:" + merchantId + "-----branchCode:" + branchCode);


            // 推荐搜索关键字（展示在搜索框下面的）
            objModel.put("styleClass", "");
            String gjz = "";
            String gjz1 = "";

            //将搜索的分类展示在搜索框下面
            if (StringUtil.isNotEmpty(categorySecondId) && !"0".equals(categorySecondId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categorySecondId));
                String categoryName = "";
                if (category != null) {
                    categoryName = category.getName();
                }
                if (StringUtil.isNotEmpty(keyword)) {
                    gjz = keyword + " 、" + categoryName;
                } else if (StringUtil.isNotEmpty(gjz1)) {
                    gjz = gjz1 + " 、" + categoryName;
                } else {
                    gjz = categoryName;
                }

            } else if (StringUtil.isNotEmpty(categoryThirdId) && !"0".equals(categoryThirdId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categoryThirdId));
                String categoryName = "";
                if (category != null) {
                    categoryName = category.getName();
                }
                if (StringUtil.isNotEmpty(keyword)) {
                    gjz = keyword + " 、" + categoryName;
                } else if (StringUtil.isNotEmpty(gjz1)) {
                    gjz = gjz1 + " 、" + categoryName;
                } else {
                    gjz = categoryName;
                }
            } else if (StringUtil.isNotEmpty(categoryFirstId) && !"0".equals(categoryFirstId) && !"99999".equals(categoryFirstId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categoryFirstId));
                String categoryName = "";
                if (category != null) {
                    categoryName = category.getName();
                }
                if (StringUtil.isNotEmpty(keyword)) {
                    gjz = keyword + " 、" + categoryName;
                } else if (StringUtil.isNotEmpty(gjz1)) {
                    gjz = gjz1 + " 、" + categoryName;
                } else {
                    gjz = categoryName;
                }
            } else {
                if (StringUtil.isEmpty(keyword)) {
                    if (StringUtil.isNotEmpty(gjz1)) {
                        gjz = gjz1;
                    } else {
                        gjz = "全部商品";
                    }
                } else {
                    gjz = keyword;
                }

            }

            //获取筛选条件的厂商
            String manufacturer = request.getParameter("manufacturer");
            //获取销量的排序标识
            String orderSort = request.getParameter("order_sort");
            if (StringUtil.isNotEmpty(orderSort)) {
                skuPOJO.setCsuOrder(CsuOrder.SALE);
                if (orderSort.equals(SortOrder.DESC.getIntValue().toString())) {
                    skuPOJO.setSortOrder(SortOrder.DESC);
                } else {
                    skuPOJO.setSortOrder(SortOrder.ASC);
                }
            }

            //获取综合排行的标识
            String orderSaleRank = request.getParameter("order_sale_rank");
            if (StringUtil.isNotEmpty(orderSaleRank)) {
                skuPOJO.setCsuOrder(CsuOrder.DEFAULT);
                if (orderSaleRank.equals(SortOrder.DESC.getIntValue().toString())) {
                    skuPOJO.setSortOrder(SortOrder.DESC);
                } else {
                    skuPOJO.setSortOrder(SortOrder.ASC);
                }
            }

            //获取最新上架排序标识
            String orderTime = request.getParameter("order_time");
            if (StringUtil.isNotEmpty(orderTime)) {
                skuPOJO.setCsuOrder(CsuOrder.CREATE);
                if (orderTime.equals(SortOrder.DESC.getIntValue().toString())) {
                    skuPOJO.setSortOrder(SortOrder.DESC);
                } else {
                    skuPOJO.setSortOrder(SortOrder.ASC);
                }
            }

            //获取价格排序标识
            String orderFob = request.getParameter("order_fob");
            if (StringUtil.isNotEmpty(orderFob)) {
                skuPOJO.setCsuOrder(CsuOrder.FOB);
                if (orderFob.equals(SortOrder.DESC.getIntValue().toString())) {
                    skuPOJO.setSortOrder(SortOrder.DESC);
                } else {
                    skuPOJO.setSortOrder(SortOrder.ASC);
                }
            }

            //如果销量排序、最新上架排序、价格排序都为空，默认人气排序
            if (StringUtil.isEmpty(orderSort) && StringUtil.isEmpty(orderTime) && StringUtil.isEmpty(orderFob)) {
                if (orderSaleRank == null) {
                    orderSaleRank = "1";
                    skuPOJO.setCsuOrder(CsuOrder.DEFAULT);
                    skuPOJO.setSortOrder(SortOrder.DESC);
                }
            }
            //只看有货筛选条件
            String hasStock = request.getParameter("has_stock");
            if (hasStock != null) {
                skuPOJO.setHasStock(Integer.valueOf(hasStock));
            }
            //处方分类筛选条件
            String drugClassification = request.getParameter("drugClassification");
            if (StringUtil.isNotEmpty(drugClassification)) {
                if ("5".equals(drugClassification)) {
                    skuPOJO.setDrugClassificationList(null);
                } else {
                    skuPOJO.setDrugClassificationList(Lists.newArrayList(Integer.parseInt(drugClassification)));
                }
            }
            /****************价格区间的筛选条件********************/
            String minPrice = request.getParameter("minPrice");
            String maxPrice = request.getParameter("maxPrice");
            //设置筛选的价格
            if (StringUtil.isNotEmpty(minPrice)) {
                skuPOJO.setMinPrice(Double.parseDouble(minPrice));
            }
            if (StringUtil.isNotEmpty(maxPrice)) {
                skuPOJO.setMaxPrice(Double.parseDouble(maxPrice));
            }


            /*******************获取商品列表**********************/
            com.xyy.ec.search.engine.pagination.Page newPage = new com.xyy.ec.search.engine.pagination.Page();
            newPage.setPageSize(20);
            newPage.setPageNo(page.getOffset() <= 0 ? 1 : page.getOffset());

            //判断是否在自配区, false-表示自配区，true-非自配区，自配区用户才能查看易碎商品
            boolean isShowFragileGoods = merchantBussinessApi.checkFragileLimitedByMerchantId(merchantId);
            //易碎品是否可见
            if (!isShowFragileGoods) {
                skuPOJO.setIsShowFragileGoods(1);
            }

            String shopCodes = request.getParameter("shopCodes");
            if (StringUtil.isNotEmpty(shopCodes)) {
                skuPOJO.setShopCodes(Arrays.asList(shopCodes.split(",")));
            }
            String currentSpec = request.getParameter("currentSpec");
            if (StringUtil.isNotEmpty(currentSpec)) {
                skuPOJO.setSpecList(Arrays.asList(currentSpec.split(SearchConstants.COMMA_SEPARATOR_CHAR)));
            }

            LOGGER.info("params : {}", JsonUtil.toJson(skuPOJO));
            ApiRPCResult searchApiRPCResult = searchApi.searchForPC(newPage, skuPOJO);
            // 商品组探活埋点
            productExhibitionGroupBusinessAdminRemoteService.asyncSendExhibitionLiveEventMQForSearch(skuPOJO);
            if (searchApiRPCResult.isFail()) {
                LOGGER.error("调用搜索接口异常 code : {}, msg : {}, errMsg : {}", searchApiRPCResult.getCode(), searchApiRPCResult.getMsg(), searchApiRPCResult.getErrMsg());
                throw new XyyEcSearchBizCheckException("搜索接口调用异常");
            }
            com.xyy.ec.search.engine.pagination.Page pageInFo = (com.xyy.ec.search.engine.pagination.Page) searchApiRPCResult.getData();
            List<CsuInfo> csuInfoList = pageInFo.getRecordList();
            if (CollectionUtils.isNotEmpty(csuInfoList)) {
                //商户资质状态，1和5时修改fob为0
                handleCsuInfoFob(csuInfoList, merchant);
            }

            //截断返回当前推荐结果的搜索词
            String blockWord = "";
            List<String> wordList = new ArrayList<>();
            Map<String, Object> extendsMap = pageInFo.getExtendsMap();
            Integer type = (Integer) extendsMap.get("type");
            if (type != null) {
                switch (type) {
                    case 2:
                        wordList = (List<String>) extendsMap.get("wordList");
                        blockWord = wordList != null && wordList.size() > 0 ? wordList.get(0) : "";
                        wordList = wordList != null && wordList.size() > 1 ? wordList.subList(1, wordList.size()) : new ArrayList<>();
                        break;
                    case 3:
                        //兜底推荐，返回搜索热搜词
//                    List<HotSearchBusinessDto> hotSearchBusinessDtos = hostSearchBusinessApi.finhotSearchlist(merchantId, 1, branchCode);
//                    List<String> hotList = hotSearchBusinessDtos.stream().map(HotSearchBusinessDto::getKeyword).collect(Collectors.toList());
//                    wordList = hotList != null && hotList.size() > 0 ? hotList.subList(0, hotList.size() > Constants.MAX_HOT_WORD_SIZE ? Constants.MAX_HOT_WORD_SIZE : hotList.size()) : new ArrayList<>();
                        if (!url.contains("type=3")) {
                            url += "&type=" + 3;
                        }
                        break;
                }
            }

            /*******************获取聚合数据*************************/
            List<String> manufacturerList = new ArrayList<>();
            List<AggBucketVo> shopBucketList = new ArrayList<>();
            List<AggBucketVo> specBucketList = new ArrayList<>();
            skuPOJO.setMerchantId(merchantId);
            //聚合忽略商家、规格参数
            skuPOJO.setSpecList(null);
            skuPOJO.setShopCodes(null);
            ApiRPCResult<AggregatedVo> apiRPCResult = searchApi.aggregatedForPC(skuPOJO);
            if (apiRPCResult.isFail()) {
                LOGGER.info("调用厂家接口异常 code : {}, msg : {}, errMsg : {}", apiRPCResult.getCode(), apiRPCResult.getMsg(), apiRPCResult.getErrMsg());
            } else {
                Map<String, List<AggBucketVo>> aggregationsMap = apiRPCResult.getData().getAggregations();
                manufacturerList = aggregationsMap.get(SearchConstants.AGG_MF_STATS).stream().map(AggBucketVo::getKey).collect(Collectors.toList());
                shopBucketList = aggregationsMap.get(SearchConstants.AGG_SHOP_STATS);
                specBucketList = aggregationsMap.get(SearchConstants.AGG_SPEC_STATS);
            }

            //商品信息包装
            List<EcProductInfoVO> ecProductInfoVOS = getEcProductInfoVOS(merchantId, csuInfoList);

            //兼容老的Page
            Page<EcProductInfoVO> skuPOJOPage = new Page<>();
            skuPOJOPage.setLimit((int) pageInFo.getPageSize());
            skuPOJOPage.setTotal(pageInFo.getTotalCount());
            skuPOJOPage.setRows(ecProductInfoVOS);
            skuPOJOPage.setOffset((int) pageInFo.getPageNo());

            //生成埋点数据
            MaidianParams maidianParams = MaidianParams.builder().merchantId(merchantId).keyword(skuPOJO.getKeyword())
                    .categoryId(categoryFirstId)
                    .categorySecondId(categorySecondId)
                    .categoryThirdId(categoryThirdId)
                    .build();
            MaiDianVo maiDianVo = SearchUtils.buildMaiDianVoInfo(maidianParams, TerminalTypeEnum.PC.getValue(), SnowGroundTypeEnum.SEARCH);
            String spType = request.getParameter("sptype");
            String spId = request.getParameter("spid");
            String sId = request.getParameter("sid");
            if (!url.contains("&sptype=")) {
                url += "&sptype=" + maiDianVo.getSpType();
                spType = maiDianVo.getSpType();
            }
            if (!url.contains("&spid=")) {
                url += "&spid=" + maiDianVo.getSpId();
            } else {
                url = url.replace("&spid=" + spId, "&spid=" + maiDianVo.getSpId());
            }

            if (!url.contains("&sid=")) {
                url += "&sid=" + maiDianVo.getSid();
                sId = maiDianVo.getSid();
            } else {
                if (StringUtil.isNotEmpty(spType) && spType.equals("2") && !spId.equals(maiDianVo.getSpId())) {
                    url = url.replace("&sid=" + sId, "&sid=" + maiDianVo.getSid());
                }
            }

            //给spid赋值
            spId = maiDianVo.getSpId();

            skuPOJOPage.setRequestUrl(url);

            List<CategoryVo> listCategory = categoryBusinessApi.getCategoryTree(branchCode);
            objModel.put("sptype", spType);
            objModel.put("spid", spId);
            objModel.put("sid", sId);
            if (type != null && type == 3) {
                maidianParams.setSpFrom(SnowGroundTypeEnum.RecommendSpFrom.PC_SEARCH_NO_RECALL_REC.getValue());
                maiDianVo = SearchUtils.buildMaiDianVoInfo(maidianParams, TerminalTypeEnum.PC.getValue(), SnowGroundTypeEnum.RECOMMEND);
                // 无结果生成一组新值
                objModel.put("sptype_2", maiDianVo.getSpType());
                objModel.put("spid_2", maiDianVo.getSpId());
                objModel.put("sid_2", maiDianVo.getSid());
            }

            //获取商品idList
            List<Long> skuIdList = Optional.ofNullable((List<CsuInfo>) pageInFo.getRecordList()).orElseGet(Collections::emptyList).stream().filter(Objects::nonNull).map(csuInfo -> csuInfo.getId()).collect(Collectors.toList());
            //曝光埋点上报
            searchUtils.searchExposureEvent(request, sId, spType, spId, Optional.ofNullable(skuPOJOPage.getOffset()).orElse(1), skuPOJOPage.getLimit(), skuIdList);

            objModel.put("count", skuPOJOPage.getTotal());
            objModel.put("listCategory", listCategory);
            //商品搜索页分页数据
            objModel.put("pager", skuPOJOPage);
            //商品一级分类筛选数据
            objModel.put("categoryFirstId", categoryFirstId);
            //商品二级分类筛选数据
            objModel.put("categorySecondId", categorySecondId);
            //商品三级分类筛选数据
            objModel.put("categoryThirdId", categoryThirdId);
            //用户数据
            objModel.put("merchant", merchant);
            //用户id
            objModel.put("merchantId", merchantId);
            //厂家数据
            objModel.put("manufacturerList", manufacturerList);
            objModel.put("shopBucketList", shopBucketList);
            objModel.put("specBucketList", specBucketList);
            //筛选的厂家数据
            objModel.put("manufacturer", manufacturer);
            //筛选店铺
            objModel.put("shopCodes", shopCodes);
            objModel.put("shopCodeList", StringUtil.isNotEmpty(shopCodes) ? Arrays.asList(shopCodes.split(SearchConstants.COMMA_SEPARATOR_CHAR)) : Collections.emptyList());
            //店铺展开标识
            objModel.put("merchantZhan", StringUtil.isNotEmpty(merchantZhan) ? Integer.parseInt(merchantZhan) : 0);
            //规格
            objModel.put("currentSpec", StringUtil.isNotEmpty(currentSpec) ? currentSpec : "");
            objModel.put("currentSpecList", StringUtil.isNotEmpty(currentSpec) ? Arrays.asList(currentSpec.split(SearchConstants.COMMA_SEPARATOR_CHAR)) : Collections.emptyList());
            objModel.put("specZhan", StringUtil.isNotEmpty(specZhan) ? Integer.parseInt(specZhan) : 0);
            //销量筛选条件
            objModel.put("orderSort", orderSort);
            //人气筛选条件
            objModel.put("orderSaleRank", orderSaleRank);
            //最新上架筛选条件
            objModel.put("orderTime", orderTime);
            //价格筛选条件
            objModel.put("orderFob", orderFob);
            //我的控销筛选条件
//            objModel.put("isControl", isControl);
            //是否有货筛选条件
            objModel.put("hasStock", hasStock);
            //自营
            objModel.put("isThirdCompany", skuPOJO.getIsThirdCompany());
            //处方类型
            objModel.put("drugClassification", drugClassification);
            //最小价格筛选条件
            objModel.put("minPrice", minPrice);
            //最大价格的筛选条件
            objModel.put("maxPrice", maxPrice);
            //三级分类是否展开的标识
            objModel.put("cjZhan", StringUtil.isNotEmpty(cjZhan) ? Integer.parseInt(cjZhan) : 0);
            objModel.put("ejZhan", StringUtil.isNotEmpty(ejZhan) ? Integer.parseInt(ejZhan) : 0);
            objModel.put("sjZhan", StringUtil.isNotEmpty(sjZhan) ? Integer.parseInt(sjZhan) : 0);

            //大图模式和列表模式的类型
            objModel.put("modelType", modelType);
            //关键字（展示当前搜索的关键字用的）
            objModel.put("keyword", gjz);
            //搜索关键字（填充搜索框用的）
            objModel.put("keywordSearch", keyword);
            objModel.put("styleClassa", "cur");
            objModel.put("searchType", type == null ? 1 : type);
            objModel.put("blockWord", blockWord);
            objModel.put("wordList", wordList);
            //新增店铺名称
            if (CollectionUtil.isNotEmpty(skuPOJO.getShopCodes())) {
                //店铺信息
                List<ShopInfoDTO> shopInfoDTOList = shopServiceRpc.queryShopByShopCodes(skuPOJO.getShopCodes());
                if (CollectionUtil.isNotEmpty(shopInfoDTOList)) {
                    objModel.put("shopName", shopInfoDTOList.get(0).getShowName());
                }
            }
            return new ModelAndView("/sku_search_list.ftl", objModel);
        } catch (Exception e) {
            LOGGER.error("/search/skuInfo.htm--商品信息查询异常：", e);
            return new ModelAndView();
        }
    }

    /**
     * @param merchantId
     * @param csuInfoList
     * @return
     */
    private List<EcProductInfoVO> getEcProductInfoVOS(Long merchantId, List<CsuInfo> csuInfoList) {
        if (merchantId == null || CollectionUtil.isEmpty(csuInfoList)) {
            return Collections.emptyList();
        }
        /**
         * 拼团活动信息查询
         */
        List<Long> csuIds = csuInfoList.stream().filter(csuInfo -> csuInfo.getProductType() == ProductEnumDTO.ProductTypeEnum.PROMOTION_SKU_TYPE.getId()).map(csuInfo -> csuInfo.getId()).collect(Collectors.toList());
        Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap = marketingService.getGroupBuyingBySkuIdsForSearch(csuIds, merchantId);


        //查询秒杀商品活动信息
        List<Long> skIdList = csuInfoList.stream().filter(csuInfo -> csuInfo.getProductType() == ProductEnumDTO.ProductTypeEnum.SECKILL_SKU_TYPE.getId()).map(productDTO -> productDTO.getId()).collect(Collectors.toList());
        Map<Long, MarketingSeckillActivityInfoDTO> seckillActivityInfoDTOMap = marketingService.getShowingSeckillActivityInfoByCsuIdsForSearch(merchantId,skIdList);


        return csuInfoList.stream().map(csuInfo -> {
            EcProductInfoVO ecProductInfoVO = objectMapper.convertValue(csuInfo, EcProductInfoVO.class);
            //设置拼团信息
            setPtActInfo(groupBuyingInfoDtoMap, csuInfo, ecProductInfoVO);

            //设置秒杀信息
            setActSkInfo(csuInfo, ecProductInfoVO, seckillActivityInfoDTOMap);
            return ecProductInfoVO;
        }).collect(Collectors.toList());
    }


    /**
     * 填充秒杀活动信息
     * @param csuInfo
     * @param ecProductInfoVO
     * @param seckillActivityInfoDTOMap
     */
    private void setActSkInfo(CsuInfo csuInfo, EcProductInfoVO ecProductInfoVO, Map<Long, MarketingSeckillActivityInfoDTO> seckillActivityInfoDTOMap) {
        if (csuInfo == null || ecProductInfoVO == null) {
            return;
        }
        //秒杀类型商品
        Optional.ofNullable(seckillActivityInfoDTOMap.get(csuInfo.getId())).ifPresent(ackSkInfo -> {
            if (ecProductInfoVO.getProductType().equals(ProductEnumDTO.ProductTypeEnum.SECKILL_SKU_TYPE.getId())) {
                SeckillActInfoVo skInfo = SeckillActInfoVo.builder()
                        .currentTime(System.currentTimeMillis())
                        .startTime(ackSkInfo.getStartTime().getTime())
                        .endTime(ackSkInfo.getEndTime().getTime())
                        .skPrice(ackSkInfo.getSkPrice())
                        .status(ackSkInfo.getStatus())
                        .percentage(ackSkInfo.getPercentage())
                        .build();

                ecProductInfoVO.setActSk(skInfo);
            }
        });
    }

    /**
     * 填充拼团活动信息
     * @param groupBuyingInfoDtoMap
     * @param csuInfo
     * @param ecProductInfoVO
     */
    private void setPtActInfo(Map<Long, GroupBuyingInfoDto> groupBuyingInfoDtoMap, CsuInfo csuInfo, EcProductInfoVO ecProductInfoVO) {
        //设置拼团信息
        Optional.ofNullable(groupBuyingInfoDtoMap.get(csuInfo.getId())).ifPresent(buyingInfoDto -> {
            //活动信息
            GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtil.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList()) ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
            /** 拼团活动状态 1.未开始 ，2.拼团中，3.已结束 兼容老逻辑前端拼团活动状态 0-未开始 1-进行中 2-结束 **/
            Integer assembleStatus = buyingInfoDto.getStatus() == null ? 0 : buyingInfoDto.getStatus() - 1;

            EcPtActInfoVO ptActInfoVO = EcPtActInfoVO.builder()
                    .marketingId(buyingInfoDto.getMarketingId())
                    .percentage(String.valueOf(buyingInfoDto.getPercentage().multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_UP))))
                    .assembleStatus(assembleStatus)
                    .assembleStartTime(buyingInfoDto.getStartTime())
                    .assembleEndTime(buyingInfoDto.getEndTime())
                    .surplusTime((buyingInfoDto.getEndTime().getTime() - System.currentTimeMillis()) / 1000)
                    .orderNum(buyingInfoDto.getOrderNum())
                    .skuStartNum(groupBuyingSkuDto.getSkuStartNum())
                    .preheatShowPrice(buyingInfoDto.getPreheatShowPrice())
                    // 拼团多阶梯价信息
                    .stepPriceStatus(buyingInfoDto.getStepPriceStatus())
                    .minSkuPrice(buyingInfoDto.getMinSkuPrice())
                    .maxSkuPrice(buyingInfoDto.getMaxSkuPrice())
                    .startingPriceShowText(buyingInfoDto.getStartingPriceShowText())
                    .rangePriceShowText(buyingInfoDto.getRangePriceShowText())
                    .stepPriceShowTexts(buyingInfoDto.generateStepPriceShowTexts(ecProductInfoVO.getProductUnit()))
                    .build();
            Optional.ofNullable(groupBuyingSkuDto).ifPresent(groupBuyingSkuDto1 -> {
                ptActInfoVO.setSkuStartNum(groupBuyingSkuDto1.getSkuStartNum());
                ptActInfoVO.setAssemblePrice(groupBuyingSkuDto1.getSkuPrice());
            });
            //调整拼团商品的showName
            StringBuilder sbShowName = new StringBuilder();
            if (SearchUtils.isShouTuiYouXuan(csuInfo.getFirstChoose(), csuInfo.getHighGross())) {
                sbShowName.append(Constants.SHOU_TUI_YOU_XUAN_TEXT);
            } else if (StringUtils.isNotEmpty(buyingInfoDto.getTopicPrefix())){
                sbShowName.append(buyingInfoDto.getTopicPrefix());
            } else {
                sbShowName.append(Constants.PT_TEXT);
            }
            if (groupBuyingSkuDto.getSkuStartNum() != null) {
                String productUnit = Optional.ofNullable(ecProductInfoVO.getProductUnit()).orElse("");
                sbShowName.append(groupBuyingSkuDto.getSkuStartNum()).append(productUnit).append("包邮").append(" ")
                        .append(ecProductInfoVO.getShowName());
            } else {
                sbShowName.append(ecProductInfoVO.getShowName());
            }

            ecProductInfoVO.setShowName(sbShowName.toString());
            ecProductInfoVO.setActPt(ptActInfoVO);
        });
    }


    /**
     * 根据用户ID取模规则进行流量灰度控制
     *
     * @param merchantId
     * @return true, false
     */
    private boolean hasSearchGray(Long merchantId) {
        if (searchGrayScale == SearchConstants.FLOW_RATIO) {
            return true;
        }
        //根据用户ID取模,小于等于限流值
        if (merchantId != null && merchantId % SearchConstants.FLOW_RATIO <= searchGrayScale) {
            return true;
        }
        return false;
    }

    /**
     * 是否被允许的用户
     *
     * @param merchantId
     * @return true-允许, false-不允许,
     * 白名单的值为空，或者-1 返回 false
     * merchantId 为空 返回 false
     */
    public boolean isAllowed(Long merchantId) {
        if (StringUtils.isEmpty(whiteList) || whiteList.equals("-1") || merchantId == null) {
            return false;
        }
        String[] merchantIds = whiteList.split(",");
        Set<Long> idSets = Arrays.asList(merchantIds).stream().filter(x -> StringUtil.isNotEmpty(x)).map(x -> Long.parseLong(StringUtils.trim(x))).collect(Collectors.toSet());
        return idSets.contains(merchantId);
    }


    @RequestMapping("/flowrate/test")
    @ResponseBody
    @RateFlowLimit(methodString = "testFlowRate")
    public String testFlowRate(HttpServletRequest request) {
        return "test ok";
    }

    /**
     * 药品分类商品列表
     *
     * @param request
     * @return
     */
    @RequestMapping("/skuInfoByCategory.htm")
    @RateFlowLimit(methodString = "skuInfoByCategory")
    public ModelAndView skuInfoByCategory(HttpServletRequest request) {
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (Objects.isNull(merchant)) {
                LOGGER.warn("药品分类商品列表，用户未登录，跳转到登录页面.");
                // 未登录，跳转登录页
                return new ModelAndView(new RedirectView("/login/login.htm", true, false));
            }
            merchantId = merchant.getId();

            Boolean isApplySearchIndexV2 = appProperties.getIsApplySearchIndexV2();
            String searchIndexV2Html = null;
            if (BooleanUtils.isTrue(isApplySearchIndexV2) && StringUtils.isNotEmpty(appProperties.getSearchIndexV2PageUrl())) {
                try {
                    // 获取pc搜索首页 html 内容
                    String searchIndexUrl = StringUtils.join(appProperties.getBasePathUrl(), appProperties.getSearchIndexV2PageUrl());
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("药品分类商品列表，merchantId：{}，searchIndexUrl：{}", merchantId, searchIndexUrl);
                    }
                    searchIndexV2Html = IOUtils.toString(new URL(searchIndexUrl), "UTF-8");
                } catch (Exception e) {
                    LOGGER.warn("药品分类商品列表，获取 v2 html 异常，使用V1，merchantId：{}", merchantId, e);
                    isApplySearchIndexV2 = false;
                }
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("药品分类商品列表，merchantId：{}，isApplySearchIndexV2：{}，searchIndexV2Html：{}", merchantId, isApplySearchIndexV2, searchIndexV2Html);
            }

            Map<String, Object> objModel = new HashMap<>();
            objModel.put("merchantId", merchantId);
            objModel.put("keywordSearch", request.getParameter("keyword"));
            //商品一级分类筛选数据
            objModel.put("categoryFirstId", request.getParameter("categoryFirstId"));
            //商品二级分类筛选数据
            objModel.put("categorySecondId", request.getParameter("categorySecondId"));
            //商品三级分类筛选数据
            objModel.put("categoryThirdId", request.getParameter("categoryThirdId"));

            if (BooleanUtils.isTrue(isApplySearchIndexV2) && StringUtils.isNotEmpty(searchIndexV2Html)) {
                // 获取pc首页cms html 内容
                String searchIndexHtmlAssetsIndexJsRelativePath = SearchUtils.getSearchIndexHtmlAssetsIndexJsRelativePath(searchIndexV2Html);
                String searchIndexHtmlAssetsVendorJsRelativePath = SearchUtils.getSearchIndexHtmlAssetsVendorJsRelativePath(searchIndexV2Html);
                String searchIndexHtmlAssetsIndexCssRelativePath = SearchUtils.getSearchIndexHtmlAssetsIndexCssRelativePath(searchIndexV2Html);
                String searchIndexHtmlContent = SearchUtils.getSearchIndexHtmlBody(searchIndexV2Html);
                objModel.put("searchIndexHtmlAssetsIndexJsRelativePath", searchIndexHtmlAssetsIndexJsRelativePath);
                objModel.put("searchIndexHtmlAssetsVendorJsRelativePath", searchIndexHtmlAssetsVendorJsRelativePath);
                objModel.put("searchIndexHtmlAssetsIndexCssRelativePath", searchIndexHtmlAssetsIndexCssRelativePath);
                objModel.put("searchIndexHtmlContent", searchIndexHtmlContent);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("搜索首页，merchantId：{}，searchIndexHtmlAssetsIndexJsRelativePath：{}，searchIndexHtmlAssetsVendorJsRelativePath：{}，searchIndexHtmlAssetsIndexCssRelativePath：{}",
                            merchantId, searchIndexHtmlAssetsIndexJsRelativePath, searchIndexHtmlAssetsVendorJsRelativePath, searchIndexHtmlAssetsIndexCssRelativePath);
                }
                return new ModelAndView("/sku_search_list_v2.ftl", objModel);
            }
            return new ModelAndView("/sku_search_list.ftl", objModel);
        } catch (Exception e) {
            LOGGER.error("药品分类商品列表，/search/skuInfoByCategory.htm 出现异常，merchantId：{}", merchantId, e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 搜索首页
     *
     * @param request
     * @return
     */
    @RequestMapping("/skuInfo.htm")
    @RateFlowLimit(methodString = "skuInfo")
    public ModelAndView skuInfo(HttpServletRequest request) {
        Long merchantId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (Objects.isNull(merchant)) {
                LOGGER.warn("搜索首页，用户未登录，跳转到登录页面.");
                // 未登录，跳转登录页
                return new ModelAndView(new RedirectView("/login/login.htm", true, false));
            }
            merchantId = merchant.getId();

            String searchIndexHtml = null;
            String searchIndexUrl = StringUtils.join(appProperties.getBasePathUrl(), appProperties.getSearchIndexPageUrl());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("搜索首页，merchantId：{}，searchIndexUrl：{}", merchantId, searchIndexUrl);
            }
            // 获取pc搜索首页 html 内容
            try {
                searchIndexHtml = IOUtils.toString(new URL(searchIndexUrl), "UTF-8");
            } catch (Exception e) {
                LOGGER.warn("搜索首页，获取 html 异常，merchantId：{}", merchantId, e);
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("搜索首页，merchantId：{}，searchIndexV2Html：{}", merchantId, searchIndexHtml);
            }
            if (StringUtils.isEmpty(searchIndexHtml)) {
                LOGGER.error("搜索首页，/search/skuInfo.htm 失败，没有获取到搜索首页html，merchantId：{}，searchIndexUrl：{}", merchantId, searchIndexUrl);
                return new ModelAndView("/error/500.ftl");
            }
            Map<String, Object> objModel = new HashMap<>();
            objModel.put("toPageUrl", appProperties.getSearchIndexToPageUrl());
            objModel.put("merchantId", merchantId);
            objModel.put("keywordSearch", request.getParameter("keyword"));
            //商品一级分类筛选数据
            objModel.put("categoryFirstId", request.getParameter("categoryFirstId"));
            //商品二级分类筛选数据
            objModel.put("categorySecondId", request.getParameter("categorySecondId"));
            //商品三级分类筛选数据
            objModel.put("categoryThirdId", request.getParameter("categoryThirdId"));
            // 获取pc首页cms html 内容
            String searchIndexHtmlAssetsIndexJsRelativePath = SearchUtils.getSearchIndexHtmlAssetsIndexJsRelativePath(searchIndexHtml);
            String searchIndexHtmlAssetsVendorJsRelativePath = SearchUtils.getSearchIndexHtmlAssetsVendorJsRelativePath(searchIndexHtml);
            String searchIndexHtmlAssetsIndexCssRelativePath = SearchUtils.getSearchIndexHtmlAssetsIndexCssRelativePath(searchIndexHtml);
            String searchIndexHtmlContent = SearchUtils.getSearchIndexHtmlBody(searchIndexHtml);
            objModel.put("searchIndexHtmlAssetsIndexJsRelativePath", searchIndexHtmlAssetsIndexJsRelativePath);
            objModel.put("searchIndexHtmlAssetsVendorJsRelativePath", searchIndexHtmlAssetsVendorJsRelativePath);
            objModel.put("searchIndexHtmlAssetsIndexCssRelativePath", searchIndexHtmlAssetsIndexCssRelativePath);
            objModel.put("searchIndexHtmlContent", searchIndexHtmlContent);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("搜索首页，merchantId：{}，searchIndexHtmlAssetsIndexJsRelativePath：{}，searchIndexHtmlAssetsVendorJsRelativePath：{}，searchIndexHtmlAssetsIndexCssRelativePath：{}",
                        merchantId, searchIndexHtmlAssetsIndexJsRelativePath, searchIndexHtmlAssetsVendorJsRelativePath, searchIndexHtmlAssetsIndexCssRelativePath);
            }
            return new ModelAndView("/search.ftl", objModel);
        } catch (Exception e) {
            LOGGER.error("搜索首页，/search/skuInfo.htm 出现异常，merchantId：{}", merchantId, e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 全部药品列表
     *
     * @param page
     * @param searchSkuListParam
     * @return
     * @deprecated
     */
    @Deprecated
//    @RequestMapping("/skuInfo.htm")
//    @RateFlowLimit(methodString = "searchSkuInfo")
    public ModelAndView skuInfo(Page page, SearchSkuListParam searchSkuListParam, HttpServletRequest request) {
        try {
            SkuConditionDto skuPOJO = objectMapper.convertValue(searchSkuListParam, SkuConditionDto.class);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            //用户所属域的域编码
            String branchCode = null;
            Integer flag = null;
            if (merchant != null) {
                merchantId = merchant.getId();
                branchCode = merchant.getRegisterCode();
            } else {
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            }
            LOGGER.info("app sku findSkuInfo | merchantId:{},keyWord:{}", merchantId, skuPOJO.getKeyword());
            //根据用户ID进行新接口流量限制
            if (hasSearchGray(merchantId) || isAllowed(merchantId)) {
                LOGGER.info("用户 : {} ,使用新搜索接口", merchantId);
                SearchCsuDTO searchCsuDTO = objectMapper.convertValue(skuPOJO, SearchCsuDTO.class);
                if (StringUtil.isNotEmpty(searchSkuListParam.getShopCode())) {
                    searchCsuDTO.setShopCodes(Lists.newArrayList(searchSkuListParam.getShopCode()));
                }
                return newSkuInfo(page, searchCsuDTO, request);
            }
            LOGGER.info("用户 : {} ,使用老搜索接口", merchantId);

            if (StringUtil.isNotEmpty(skuPOJO.getKeyword()) && ObjectUtils.isEmpty(skuPOJO.getType())) {
                skuPOJO.setType(1);
            }
            //将搜索词加入到热搜
            if (StringUtil.isNotEmpty(skuPOJO.getShowName()) && merchantId != 0L) {
                ProductBusinessDto productBusinessDto = new ProductBusinessDto();
                productBusinessDto.setShowName(skuPOJO.getShowName());
                Long finalMerchantId = merchantId;
                RpcContext.getContext().setAttachment("async", "true").asyncCall(() -> {
                    try {
                        hostSearchBusinessApi.addHotSearch(productBusinessDto, finalMerchantId);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
                RpcContext.getContext().removeAttachment("async");
            }

            skuPOJO.setBranchCode(branchCode);

            Map<String, Object> objModel = new HashMap<>();
            //我的控销商品筛选条件
            String isControl = request.getParameter("is_control");
            if (StringUtil.isNotEmpty(isControl) && !"0".equals(isControl)) {
                if ("1".equals(isControl)) {
                    skuPOJO.setIsControl(null);
                } else if ("2".equals(isControl)) {
                    skuPOJO.setIsControl(1);
                }
            }
            // 分页要用，将url传到前台
            String url = getRequestUrl(request);

            //搜索关键词
            String keyword = request.getParameter("keyword");
            /***********处理搜索关键词**************/
            if (StringUtil.isNotEmpty(keyword)) {
                skuPOJO.setShowName(keyword);
            }
            //大图模式和列表模式的类型
            String modelTypeStr = request.getParameter("model_type");
            /*********默认大图模式展示搜索页********/
            int modelType = 1;
            if (StringUtil.isNotEmpty(modelTypeStr)) {
                modelType = Integer.parseInt(modelTypeStr);
            }

            //筛选类目展开状态
            String cjZhan = request.getParameter("cjZhan");
            String ejZhan = request.getParameter("ejZhan");
            String sjZhan = request.getParameter("sjZhan");


            /****************处理商品分类**********************/
            //一级分类筛选条件
            String categoryFirstId = request.getParameter("categoryFirstId");
            //二级分类筛选条件
            String categorySecondId = request.getParameter("categorySecondId");
            //三级分类筛选条件
            String categoryThirdId = request.getParameter("categoryThirdId");
            if ("99999".equals(categoryFirstId)) {//当选择的为全部分类时，二级分类和三级分类置空，且展开状态为关闭
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                    categorySecondId = null;
                }
                if (StringUtil.isNotEmpty(categoryThirdId)) {
                    url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                    categoryThirdId = null;
                }
                ejZhan = "2";
                sjZhan = "2";

            }

            Map<Long, CategoryVo> categoryVoMap = categoryBusinessApi.getCategory(branchCode);

            //处理分类的筛选条件---当所选的分类的父类与所选的父类不对应时，清楚该分类的筛选
            //当所选的三级分类不为空时进行判断
            if (StringUtil.isNotEmpty(categoryThirdId)) {
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    //得到所选三级分类信息
                    CategoryBusinessDTO c3 = categoryVoMap.get(Long.parseLong(categoryThirdId));
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {//当所选一级分类不为空时，所选的二三级分类都要和一级分类比较
                        //得到所选二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(Long.parseLong(categorySecondId));
                        //如果所选二级分类的父类即一级分类也不是所选的一级分类，则将二级分类的筛选条件也去掉
                        if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                            categorySecondId = null;
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        } else {
                            //如果所选三级分类的父类即二级分类不是所选的二级分类，则将三级分类的筛选条件去掉
                            if (Long.parseLong(categorySecondId) != c3.getParentId()) {
                                url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                                categoryThirdId = null;
                            }
                        }
                    } else {
                        //如果所选三级分类的父类即二级分类不是所选的二级分类，则将三级分类的筛选条件去掉
                        if (Long.parseLong(categorySecondId) != c3.getParentId()) {
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        }
                    }
                } else {//当所选二级分类为空时，只和所选一记分类比较
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {
                        //得到所选的三级分类信息
                        CategoryBusinessDTO c3 = categoryVoMap.get(Long.parseLong(categoryThirdId));
                        //得到所选三级分类的二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(c3.getParentId());
                        //如果所选三级分类的二级分类的父类即一级分类不是所选的一级分类，则将三级分类的筛选条件去掉
                        if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        }
                    }
                }
            } else {
                //当所选的二级分类不为空时进行判断
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {
                        //得到所选二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(Long.parseLong(categorySecondId));
                        //如果所选二级分类的父类即一级分类不是所选的一级分类，则将二级分类的筛选条件去掉
                        if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                            categorySecondId = null;
                        }
                    }
                }
            }

            //处理所选的一级分类
            if (StringUtil.isNotEmpty(categoryFirstId)) {
                if (!"99999".equals(categoryFirstId)) {
                    skuPOJO.setCategoryFirstId(Long.parseLong(categoryFirstId));
                } else {
                    //当一级分类的筛选条件为99999事搜索的是全部药品，分类的筛选条件置空
                    skuPOJO.setCategoryFirstId(null);
                    skuPOJO.setCategorySecondId(null);
                    skuPOJO.setCategoryThirdId(null);
                }
            }

            //分类树
            //获取缓存中的商品分类集合
            LOGGER.info("merchantId:" + merchantId + "-----branchCode:" + branchCode);


            // 推荐搜索关键字（展示在搜索框下面的）
            objModel.put("styleClass", "");
            String gjz = "";
            String gjz1 = "";

            //将搜索的分类展示在搜索框下面
            if (StringUtil.isNotEmpty(categorySecondId) && !"0".equals(categorySecondId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categorySecondId));
                if (StringUtil.isNotEmpty(keyword)) {
                    gjz = keyword + " 、" + category.getName();
                } else if (StringUtil.isNotEmpty(gjz1)) {
                    gjz = gjz1 + " 、" + category.getName();
                } else {
                    gjz = category.getName();
                }

            } else if (StringUtil.isNotEmpty(categoryThirdId) && !"0".equals(categoryThirdId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categoryThirdId));
                if (StringUtil.isNotEmpty(keyword)) {
                    gjz = keyword + " 、" + category.getName();
                } else if (StringUtil.isNotEmpty(gjz1)) {
                    gjz = gjz1 + " 、" + category.getName();
                } else {
                    gjz = category.getName();
                }
            } else if (StringUtil.isNotEmpty(categoryFirstId) && !"0".equals(categoryFirstId) && !"99999".equals(categoryFirstId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categoryFirstId));
                if (StringUtil.isNotEmpty(keyword)) {
                    gjz = keyword + " 、" + category.getName();
                } else if (StringUtil.isNotEmpty(gjz1)) {
                    gjz = gjz1 + " 、" + category.getName();
                } else {
                    gjz = category.getName();
                }
            } else {
                if (StringUtil.isEmpty(keyword)) {
                    if (StringUtil.isNotEmpty(gjz1)) {
                        gjz = gjz1;
                    } else {
                        gjz = "全部商品";
                    }
                } else {
                    gjz = keyword;
                }

            }

            //获取筛选条件的厂商
            String manufacturer = request.getParameter("manufacturer");
            //获取销量的排序标识
            String orderSort = request.getParameter("order_sort");
            if ("1".equals(orderSort)) {
                skuPOJO.setProperty("spa.sale_num");
                skuPOJO.setDirection("desc");
            } else if ("2".equals(orderSort)) {
                skuPOJO.setProperty("spa.sale_num");
                skuPOJO.setDirection("asc");
            }
            //获取人气排行的标识
            String orderSaleRank = request.getParameter("order_sale_rank");
            if ("1".equals(orderSaleRank)) {
                skuPOJO.setProperty("smsr.sale_num");
                skuPOJO.setDirection("desc");
            } else if ("2".equals(orderSaleRank)) {
                skuPOJO.setProperty("smsr.sale_num");
                skuPOJO.setDirection("asc");
            }
            //获取最新上架排序标识
            String orderTime = request.getParameter("order_time");
            if ("1".equals(orderTime)) {
                skuPOJO.setProperty("s.create_time");
                skuPOJO.setDirection("desc");
            } else if ("2".equals(orderTime)) {
                skuPOJO.setProperty("s.create_time");
                skuPOJO.setDirection("asc");
            }
            //获取价格排序标识
            String orderFob = request.getParameter("order_fob");
            if ("1".equals(orderFob)) {
                skuPOJO.setProperty("s.fob");
                skuPOJO.setDirection("desc");
            } else if ("2".equals(orderFob)) {
                skuPOJO.setProperty("s.fob");
                skuPOJO.setDirection("asc");
            }
            //如果销量排序、最新上架排序、价格排序都为空，默认人气排序
            if (StringUtil.isEmpty(orderSort) && StringUtil.isEmpty(orderTime) && StringUtil.isEmpty(orderFob)) {
                if (orderSaleRank == null) {
                    orderSaleRank = "1";
                    skuPOJO.setProperty("smsr.sale_num");
                    skuPOJO.setDirection("desc");
                }
            }
            //只看有货筛选条件
            String hasStock = request.getParameter("has_stock");
            if (hasStock != null) {
                skuPOJO.setHasStock(Integer.valueOf(hasStock));
            }
            //处方分类筛选条件
            String drugClassification = request.getParameter("drugClassification");
            if (StringUtil.isNotEmpty(drugClassification)) {
                if ("5".equals(drugClassification)) {
                    skuPOJO.setDrugClassification(null);
                } else {
                    skuPOJO.setDrugClassification(Integer.parseInt(drugClassification));
                }
            }
            /****************价格区间的筛选条件********************/
            String minPrice = request.getParameter("minPrice");
            String maxPrice = request.getParameter("maxPrice");
            //设置筛选的价格
            if (StringUtil.isNotEmpty(minPrice)) {
                skuPOJO.setMinPrice(Double.parseDouble(minPrice));
            }
            if (StringUtil.isNotEmpty(maxPrice)) {
                skuPOJO.setMaxPrice(Double.parseDouble(maxPrice));
            }

            /*******************获取厂家列表*************************/
            skuPOJO.setMerchantId(merchantId);
            List<String> manufacturerList = productBusinessApi.searchManufacturerList(skuPOJO);

            /*******************获取商品列表**********************/
            skuPOJO.setFlag(flag);
            //处理分页
            int oldOffset = page.getOffset();
            page.setLimit(20);//一页设置20条商品数据
            page.setOffset((oldOffset > 0 ? (page.getOffset() - 1) : 0) * page.getLimit());
            Page<ProductDto> skuPOJOPage = new Page<>();
            //根据筛选条件筛选商品
            PageDto pageDto = new PageDto();
            pageDto.setLimit(page.getLimit());
            pageDto.setOffset(page.getOffset());
            //判断是否在自配区
            boolean isShowFragileGoods = merchantBussinessApi.checkFragileLimitedByMerchantId(merchantId);
            if (!isShowFragileGoods) {
                skuPOJO.setIsShowFragileGoods(1);//易碎品是否可见
            }
            skuPOJO.setSourceFrom(SourceFromEnum.PC.getCode());
            LOGGER.info("params : {}", JsonUtil.toJson(skuPOJO));
            SkuSearchDataDto skuSearchData = productBusinessApi.findSkuInfo(pageDto, skuPOJO, merchantId);

            List<ProductDto> skuPOJOList = null;
            if (skuSearchData != null) {
                skuPOJOList = skuSearchData.getSkuDtoList();
                if (CollectionUtil.isNotEmpty(skuPOJOList)) {
                    //商户资质状态，1和5时修改fob为0
                    handleProductFob(skuPOJOList, merchant);
                }
            }
            //截断返回当前推荐结果的搜索词
            String blockWord = "";
            List<String> wordList = new ArrayList<>();
            if (skuSearchData != null && skuSearchData.getType() != null) {
                switch (skuSearchData.getType()) {
                    case 2:
                        wordList = skuSearchData.getWordList();
                        blockWord = wordList != null && wordList.size() > 0 ? wordList.get(0) : "";
                        wordList = wordList != null && wordList.size() > 1 ? wordList.subList(1, wordList.size()) : new ArrayList<>();
                        break;
                    case 3:
                        //兜底推荐，返回搜索热搜词
//                    List<HotSearchBusinessDto> hotSearchBusinessDtos = hostSearchBusinessApi.finhotSearchlist(merchantId, 1, branchCode);
//                    List<String> hotList = hotSearchBusinessDtos.stream().map(HotSearchBusinessDto::getKeyword).collect(Collectors.toList());
//                    wordList = hotList != null && hotList.size() > 0 ? hotList.subList(0, hotList.size() > Constants.MAX_HOT_WORD_SIZE ? Constants.MAX_HOT_WORD_SIZE : hotList.size()) : new ArrayList<>();
                        if (!url.contains("type=3")) {
                            url += "&type=" + 3;
                        }
                        break;
                }
            }

            skuPOJOPage.setLimit(page.getLimit());
            skuPOJOPage.setTotal(skuSearchData == null ? 0 : skuSearchData.getCount());
            skuPOJOPage.setRows(skuPOJOList);
            skuPOJOPage.setOffset((oldOffset > 0 ? oldOffset : 0));
            skuPOJOPage.setRequestUrl(url);

            List<CategoryVo> listCategory = categoryBusinessApi.getCategoryTree(branchCode);

            objModel.put("listCategory", listCategory);
            //商品搜索页分页数据
            objModel.put("pager", skuPOJOPage);
            //商品一级分类筛选数据
            objModel.put("categoryFirstId", categoryFirstId);
            //商品二级分类筛选数据
            objModel.put("categorySecondId", categorySecondId);
            //商品三级分类筛选数据
            objModel.put("categoryThirdId", categoryThirdId);
            //用户数据
            objModel.put("merchant", merchant);
            //用户id
            objModel.put("merchantId", merchantId);
            //厂家数据
            objModel.put("manufacturerList", manufacturerList);
            //筛选的厂家数据
            objModel.put("manufacturer", manufacturer);
            //销量筛选条件
            objModel.put("orderSort", orderSort);
            //人气筛选条件
            objModel.put("orderSaleRank", orderSaleRank);
            //最新上架筛选条件
            objModel.put("orderTime", orderTime);
            //价格筛选条件
            objModel.put("orderFob", orderFob);
            //我的控销筛选条件
            objModel.put("isControl", isControl);
            //是否有货筛选条件
            objModel.put("hasStock", hasStock);
            //处方类型
            objModel.put("drugClassification", drugClassification);
            //最小价格筛选条件
            objModel.put("minPrice", minPrice);
            //最大价格的筛选条件
            objModel.put("maxPrice", maxPrice);
            //三级分类是否展开的标识
            objModel.put("cjZhan", StringUtil.isNotEmpty(cjZhan) ? Integer.parseInt(cjZhan) : 0);
            objModel.put("ejZhan", StringUtil.isNotEmpty(ejZhan) ? Integer.parseInt(ejZhan) : 0);
            objModel.put("sjZhan", StringUtil.isNotEmpty(sjZhan) ? Integer.parseInt(sjZhan) : 0);
            //大图模式和列表模式的类型
            objModel.put("modelType", modelType);
            //关键字（展示当前搜索的关键字用的）
            objModel.put("keyword", gjz);
            //搜索关键字（填充搜索框用的）
            objModel.put("keywordSearch", keyword);
            objModel.put("styleClassa", "cur");
            objModel.put("searchType", skuSearchData.getType() == null ? 1 : skuSearchData.getType());
            objModel.put("blockWord", blockWord);
            objModel.put("wordList", wordList);
            return new ModelAndView("/sku_search_list.ftl", objModel);
        } catch (Exception e) {
            LOGGER.error("/search/skuInfo.htm--商品信息查询异常：", e);
            return new ModelAndView();
        }
    }


    //通过登录资质处理商品价格
    public void handleCsuInfoFob(List<CsuInfo> csuInfoList, MerchantBussinessDto merchant) {
        if (merchant != null) {
            merchant = appVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
        }
        if (merchant == null || !merchant.getPriceDisplayFlag()) {
            if (CollectionUtil.isNotEmpty(csuInfoList)) {
                for (CsuInfo csuInfo : csuInfoList) {
                    csuInfo.setFob(0.0);
                    //商品标签
                    CsuActivityTag productActivityTagVO = new CsuActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    csuInfo.setActivityTag(productActivityTagVO);
                    //毛利
                    csuInfo.setGrossMargin("");
                    //建议零售价
                    csuInfo.setSuggestPrice(BigDecimal.ZERO);
                    //控销零售价
                    csuInfo.setUniformPrice(BigDecimal.ZERO);
                    //对比价
                    csuInfo.setRetailPrice(0.0);
                }
            }
        }
    }

    //通过登录资质处理商品价格
    public void handleProductFob(List<ProductDto> productDtoList, MerchantBussinessDto merchant) {
        if (merchant != null) {
            merchant = appVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
        }
        if (merchant == null || !merchant.getPriceDisplayFlag()) {
            if (CollectionUtil.isNotEmpty(productDtoList)) {
                for (ProductDto productDto : productDtoList) {
                    productDto.setFob(0.0);
                    productDto.setUnitPrice(null);
                    productDto.setUnitPriceTag(null);
                    //商品标签
                    ProductActivityTag productActivityTagVO = new ProductActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    productDto.setActivityTag(productActivityTagVO);
                    //毛利
                    productDto.setGrossMargin("");
                    //建议零售价
                    productDto.setSuggestPrice(BigDecimal.ZERO);
                    //控销零售价
                    productDto.setUniformPrice(BigDecimal.ZERO);
                    //对比价
                    productDto.setRetailPrice(0.0);
                }
            }
        }
    }

    //通过登录资质处理商品价格
    public void handleListProductFob(List<ListProduct> productDtoList, MerchantBussinessDto merchant) {
        if (merchant != null) {
            merchant = appVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
        }
        if (merchant == null || !merchant.getPriceDisplayFlag()) {
            if (CollectionUtil.isNotEmpty(productDtoList)) {
                for (ListProduct productDto : productDtoList) {
                    productDto.setFob(0.0);
                    productDto.setUnitPrice(null);
                    productDto.setUnitPriceTag(null);
                    //商品标签
                    ProductActivityTag productActivityTagVO = new ProductActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    productDto.setActivityTag(productActivityTagVO);
                    //毛利
                    productDto.setGrossMargin("");
                    //建议零售价
                    productDto.setSuggestPrice(BigDecimal.ZERO);
                    //控销零售价
                    productDto.setUniformPrice(BigDecimal.ZERO);
                    //对比价
                    productDto.setRetailPrice(0.0);
                }
            }
        }

    }

    //通过登录资质处理商品价格
    public void handleRecommendProductFob(List<com.xyy.recommend.shop.dto.product.CsuInfo> productDtoList, MerchantBussinessDto merchant) {
        if (merchant != null) {
            merchant = appVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
        }
        if (merchant == null || !merchant.getPriceDisplayFlag()) {
            if (CollectionUtil.isNotEmpty(productDtoList)) {
                for (com.xyy.recommend.shop.dto.product.CsuInfo productDto : productDtoList) {
                    productDto.setFob(0.0);
                    //商品标签
                    com.xyy.recommend.shop.dto.product.CsuActivityTag productActivityTagVO = new com.xyy.recommend.shop.dto.product.CsuActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    productDto.setActivityTag(productActivityTagVO);
                    //毛利
                    productDto.setGrossMargin("");
                    //建议零售价
                    productDto.setSuggestPrice(BigDecimal.ZERO);
                    //控销零售价
                    productDto.setUniformPrice(BigDecimal.ZERO);
                    //对比价
                    productDto.setRetailPrice(0.0);
                }
            }
        }
    }

    //list集合排序
    @SuppressWarnings("unchecked")
    private void sortIntMethod(List list) {
        Collections.sort(list, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                CategoryBusinessDTO c1 = (CategoryBusinessDTO) o1;
                CategoryBusinessDTO c2 = (CategoryBusinessDTO) o2;
                if (c1.getPriority() > c2.getPriority()) {
                    return 1;
                } else if (c1.getPriority().equals(c2.getPriority())) {
                    return 0;
                } else {
                    return -1;
                }
            }
        });
    }

    /**
     * 根据输入的值获取相关的商品名
     *
     * @param showName
     * @return
     */
    @RequestMapping("/autoComplate.json")
    @ResponseBody
    public Map<String,Object> findSkuName(@RequestParam("showName") String showName, HttpServletRequest request) {
        try {
            if (StringUtil.isNotEmpty(showName)) {
                showName = showName.toUpperCase();
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = null;
            String branchCode = null;
            String provinceCode = null;
            if (merchant != null) {
                merchantId = merchant.getId();
                branchCode = merchant.getRegisterCode();
                provinceCode = merchant.getProvinceCode();
            } else {
                merchantId = 0L;
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
                if (StringUtils.isNotEmpty(branchCode)) {
                    if (Objects.equals(branchCode, BranchEnum.SHANXI_COUNTRY.getKey())) {
                        provinceCode = "140000";
                    } else {
                        provinceCode = branchCode.replaceFirst("XS", "");
                    }
                }
            }
            ApiRPCResult apiRPCResult = searchApi.suggest(merchantId, showName, StringUtils.isNotEmpty(provinceCode) ? Integer.parseInt(provinceCode) : null);
            if (apiRPCResult.isFail()) {
                LOGGER.error("获取商品名自动补全失败 code : {}, msg : {}, errMsg : {}", apiRPCResult.getCode(), apiRPCResult.getMsg(), apiRPCResult.getErrMsg());
                return this.addError("获取商品名自动补全失败！");
            }
            List<String> suggestionList = (List<String>) apiRPCResult.getData();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("app sku findSkuNameList | merchantId:{}, result:{}", merchantId, suggestionList.toString());
            }
            List<Map<String, String>> mapList = suggestionList.stream().map(x -> {
                Map<String, String> showMap = new HashMap<>();
                showMap.put("showName", x);
                return showMap;
            }).collect(Collectors.toList());

            //查询店铺sug
            ApiRPCResult<ShopInfoVo> shopInfoVoList = searchApi.shopSuggest(merchantId, showName);
            String[] names = {"showNameList","shopList"};
            Object[] values = {mapList, shopInfoVoList.isSuccess() ? shopInfoVoList.getData() : Collections.emptyList()};
            return BaseController.addResult(names, values);
        } catch (Exception e) {
            LOGGER.error("/search/autoComplate.json-获取商品名自动补全失败", e);
            return this.addError("获取商品名自动补全失败！");
        }
    }

    /**
     * 商品详情页获取商品分类
     *
     * @param id
     * @param request
     * @return
     */
    @RequestMapping("/skuDetailCategoryRelation.json")
    @ResponseBody
    public Object getCategoryRelation(Long id, HttpServletRequest request) {
        Long merchantId = 0L;
        //用户所属域的域编码
        String branchCode = null;
        ProductDto productDto = new ProductDto();
        productDto.setId(id);
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant != null) {
                merchantId = merchant.getId();
                branchCode = merchant.getRegisterCode();
            } else {
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            }
            //处理展示在商品详情页的分类
            List<SkuCategoryRelationBusinessDTO> skuCategoryRelationList = categoryRelationBusinessApi.findSkuCategoryRelationBySkuId(id);
            SkuCategoryRelationBusinessDTO skuCategoryRelation = null;
            if (CollectionUtil.isNotEmpty(skuCategoryRelationList)) {
                //取第一个关联的分类
                skuCategoryRelation = skuCategoryRelationList.get(0);
            }
            Map<Long, CategoryVo> categoryVoMap = categoryBusinessApi.getCategory(branchCode);
            //设置三级分类的名称是为了显示商品详情中的面包屑导航
            if (skuCategoryRelation != null) {
                if (skuCategoryRelation.getCategoryFirstId() != null) {
                    productDto.setCategoryFirstId(skuCategoryRelation.getCategoryFirstId());
                    //根据分类id获取分类信息
                    CategoryBusinessDTO category = categoryVoMap.get(skuCategoryRelation.getCategoryFirstId());
                    if (category != null) {
                        productDto.setCategoryFirstName(category.getName());
                    }
                }
                if (skuCategoryRelation.getCategorySecondId() != null) {
                    productDto.setCategorySecondId(skuCategoryRelation.getCategorySecondId());
                    //根据分类id获取分类信息
                    CategoryBusinessDTO category = categoryVoMap.get(skuCategoryRelation.getCategorySecondId());
                    if (category != null) {
                        productDto.setCategorySecondName(category.getName());
                    }
                }
                if (skuCategoryRelation.getCategoryThirdId() != null) {
                    productDto.setCategoryThirdId(skuCategoryRelation.getCategoryThirdId());
                    //根据分类id获取分类信息
                    CategoryBusinessDTO category = categoryVoMap.get(skuCategoryRelation.getCategoryThirdId());
                    if (category != null) {
                        productDto.setCategoryThirdName(category.getName());
                    }
                }
            }
            return this.addDataResult("productCategoryInfo", productDto);
        } catch (Exception e) {
            LOGGER.error("商品详情页分类查询失败", e);
            return this.addError("商品详情页分类查询失败");
        }
    }

    /**
     * 获取商品分类信息
     *
     * @param request
     * @return
     */
    @RequestMapping("/getCategoryTree.json")
    @ResponseBody
    public Object getCategoryTree(HttpServletRequest request) {
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            Long merchantId = 0L;
            //用户所属域的域编码
            String branchCode = null;
            if (merchant != null) {
                branchCode = merchant.getRegisterCode();
            } else {
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            }
            List<CategoryVo> listCategory = categoryBusinessApi.getCategoryTree(branchCode);
            return this.addDataResult("listCategory", listCategory);
        } catch (Exception e) {
            LOGGER.error("商品列表页分类树查询失败", e);
            return this.addError("商品列表分类树查询失败");
        }

    }

    @RequestMapping(value = "/skuDetail/{id}.htm", method = RequestMethod.GET)
    public ModelAndView skuDetail(@PathVariable Long id, HttpServletRequest request,
                                  @RequestParam(value = "isMainProductVirtualSupplier", required = false) Boolean isMainProductVirtualSupplier) {
        if (id == null) {
            LOGGER.error("查询商品详情异常：id参数的值为空");
            return new ModelAndView("/product_detail_no_result.ftl");
        }
        try {
            ProductDto product = new ProductDto();
            product.setId(id);
            Long merchantId = 0L;
            //用户所属域的域编码
            String branchCode = null;
            //接收查询来源
            String sourceId = request.getParameter("sourceId");

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant != null) {
                merchantId = merchant.getId();
                branchCode = merchant.getRegisterCode();
            } else {
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            }
            product.setBranchCode(branchCode);
            Map<String, Object> objModel = new HashMap<>();

            ProductDto productDto = prodcutApi.findSkuDetailByIdAndBranchCodeForPC(id, merchantId, branchCode);
            if (productDto == null) {
                if (merchant != null) {
                    return new ModelAndView("/product_detail_no_result.ftl");
                } else {
                    return new ModelAndView(new RedirectView("/"));
                }
            }
            // 处理限时加补标签
            if (CollectionUtils.isNotEmpty(productDto.getCxTagList())) {
                String jsonString = JSONObject.toJSONString(productDto.getCxTagList());
                if (StringUtils.isNotEmpty(jsonString)) {
                    List<TagDTO> tagList = JSONObject.parseArray(jsonString, TagDTO.class);
                    tagList.removeIf(this::handleXianShiJiaBuTag);
                    if (BooleanUtils.isTrue(removeSpecialTagSwitch)) {
                        tagList.removeIf(this::isSpecialTag);
                    }
                    productDto.setCxTagList(tagList);
                }
            }




            //填充库存信息
            productDto = productServiceRpc.fillAllActTotalSurplusQtyToAvailableQty(productDto);
            //处理商品价格
            handleProductFob(Arrays.asList(productDto), merchant);
            //处理商品价格区间
            List<SkuPriceRangeBusinessDTO> skuPriceRangeList = productDto.getSkuPriceRangeList();
            if (CollectionUtil.isNotEmpty(skuPriceRangeList)) {
                //存储处理后的价格区间
                productDto.setSkuPriceRangeList(skuPriceRangeList);
            }

            //通过商品id获取商品说明书信息
            SkuInstructionBusinessDTO skuInstruction = productBusinessApi.findSkuInstruction(id);
            if (skuInstruction != null) {
                //处理说明书中的格式
                //存储条件
                String storageCondition = skuInstruction.getStorageCondition();
                if (StringUtil.isNotEmpty(storageCondition)) {
                    storageCondition = storageCondition.replaceAll("\\r\\n", "<br/>");
                    productDto.setStorageCondition(storageCondition);
                }
                //适应症/功能主治
                String indication = skuInstruction.getIndication();
                if (StringUtil.isNotEmpty(indication)) {
                    indication = indication.replaceAll("\\r\\n", "<br/>");
                    productDto.setIndication(indication);
                }
                //不良反应
                String untowardEffect = skuInstruction.getUntowardEffect();
                if (StringUtil.isNotEmpty(untowardEffect)) {
                    untowardEffect = untowardEffect.replaceAll("\\r\\n", "<br/>");
                    productDto.setUntowardEffect(untowardEffect);
                }
                //用法与用量
                String usageAndDosage = skuInstruction.getUsageAndDosage();
                if (StringUtil.isNotEmpty(usageAndDosage)) {
                    usageAndDosage = usageAndDosage.replace("\\r\\n", "<br/>");
                    productDto.setUsageAndDosage(usageAndDosage);
                }
                //注意事项
                String considerations = skuInstruction.getConsiderations();
                if (StringUtil.isNotEmpty(considerations)) {
                    considerations = considerations.replace("\\r\\n", "<br/>");
                    productDto.setConsiderations(considerations);
                }
                //药品相互作用
                String interaction = skuInstruction.getInteraction();
                if (StringUtil.isNotEmpty(interaction)) {
                    interaction = interaction.replace("\\r\\n", "<br/>");
                    productDto.setInteraction(interaction);
                }
                //成分
                String component = skuInstruction.getComponent();
                if (StringUtil.isNotEmpty(component)) {
                    component = component.replace("\\r\\n", "<br/>");
                    productDto.setComponent(component);
                }
                //禁忌
                String abstain = skuInstruction.getAbstain();
                if (StringUtil.isNotEmpty(abstain)) {
                    abstain = abstain.replace("\\r\\n", "<br/>");
                    productDto.setAbstain(abstain);
                }
            }
            //通过商品id查询商品说明书信息
            List<SkuInstructionImageBusinessDTO> skuInstructionImageList = skuInstructionImageBusinessApi.selectSkuInstructionImageListBySkuId(productDto.getId());
            if (CollectionUtil.isNotEmpty(skuInstructionImageList)) {
                productDto.setSkuInstructionImageList(skuInstructionImageList);
            }


            if (StringUtil.isEmpty(productDto.getVideoUrl())) {
                productDto.setVideoUrl(null);
            }
            //pop需求查询商家信息
            if (productDto.getOrgId() != null && productDto.getIsThirdCompany() == 1) {
                try {
                    CompanyListVO companyListVO = companyService.getPopCompanyDetail(productDto.getOrgId());
                    if (companyListVO != null && companyListVO.getSaleSkuNum() != null && companyListVO.getUpSkuNum() != null) {
                        objModel.put("companyDetail", companyListVO);
                        String saleNumStr = companyListVO.getSaleSkuNum() > 10000L ? (companyListVO.getSaleSkuNum().doubleValue() / 10000d + "万") : companyListVO.getSaleSkuNum().toString();
                        String upSkuNumStr = companyListVO.getUpSkuNum() > 10000L ? (companyListVO.getUpSkuNum().doubleValue() / 10000d + "万") : companyListVO.getUpSkuNum().toString();
                        objModel.put("saleNum", saleNumStr);
                        objModel.put("upSkuNum", upSkuNumStr);
                    }
                } catch (Exception e) {
                }
            }
            ShopInfoDTO virtualShopInfoDTO = new ShopInfoDTO();
            //如果主品是虚拟供应商 则主品的赠品的店铺改为虚拟店铺 商品详情店铺信息更改为虚拟店铺
            if (BooleanUtils.isTrue(isMainProductVirtualSupplier) && ProductGiveEnum.GIFT.getCode().equals(productDto.getIsGive())) {
                //虚拟店铺信息
                virtualShopInfoDTO = shopServiceRpc.queryShopByShopCode(shopServiceRpc.getVirtualShopCode());
                CompanyListVO companyListVO = companyService.getPopCompanyDetail(virtualShopInfoDTO.getOrgId());
                objModel.put("companyDetail", companyListVO);
                String saleNumStr = companyListVO.getSaleSkuNum() > 10000L ? (companyListVO.getSaleSkuNum().doubleValue() / 10000d + "万") : companyListVO.getSaleSkuNum().toString();
                String upSkuNumStr = companyListVO.getUpSkuNum() > 10000L ? (companyListVO.getUpSkuNum().doubleValue() / 10000d + "万") : companyListVO.getUpSkuNum().toString();
                objModel.put("saleNum", saleNumStr);
                objModel.put("upSkuNum", upSkuNumStr);
            }
            List<SkuCategoryRelationBusinessDTO> skuCategoryRelationBusinessDTOS = categoryRelationBusinessApi.findSkuCategoryRelationBySkuId(id);
            if(CollectionUtils.isNotEmpty(skuCategoryRelationBusinessDTOS)){
                // 一般一个商品对应一个分类关系，但线上有大量商品对应多条一样的分类关系，所以这里取最新一条关系作为商品的分类
                SkuCategoryRelationBusinessDTO skuCategoryRelationBusinessDTO = skuCategoryRelationBusinessDTOS.get(skuCategoryRelationBusinessDTOS.size() - 1);
                productDto.setCategoryFirstId(skuCategoryRelationBusinessDTO.getCategoryFirstId());
                productDto.setCategoryFirstName(skuCategoryRelationBusinessDTO.getCategoryFirstName());
                productDto.setCategorySecondId(skuCategoryRelationBusinessDTO.getCategorySecondId());
                productDto.setCategorySecondName(skuCategoryRelationBusinessDTO.getCategorySecondName());
                productDto.setCategoryThirdId(skuCategoryRelationBusinessDTO.getCategoryThirdId());
                productDto.setCategoryThirdName(skuCategoryRelationBusinessDTO.getCategoryThirdName());
            }
            ProductPcDto productPcDto = new ProductPcDto();
            BeanUtils.copyProperties(productDto, productPcDto);

            //商品的一级分类为“养生中药”或“配方饮片”或“中药材”，且产地不为空或- 则展示
            if ((!Objects.equals(productPcDto.getCategoryFirstId(), 100010L) && !Objects.equals(productPcDto.getCategoryFirstId(), 100004L) && !Objects.equals(productPcDto.getCategoryFirstId(), 262683L)) || (StringUtils.isEmpty(productPcDto.getProducer()) || Objects.equals(productPcDto.getProducer(), "-"))) {
                productPcDto.setProducer(null);
            }

            if (productPcDto.getStatus().equals(Constants.STATUS_SOLD_OUT)) {
                productPcDto.setNearEffectiveFlag(null);
            }
            if (StringUtil.isNotEmpty(productPcDto.getNearEffect())) {
                if (StringUtil.equals(productDto.getNearEffect(), "-")) {
                    productPcDto.setNearEffectiveFlag(null);
                }
            }
            if (StringUtil.isBlank(productDto.getNearEffect()) || StringUtil.equals(productDto.getNearEffect(), "-")) {
                productPcDto.setEffectStr(null);
            } else {
                productPcDto.setEffectStr("近至" + productDto.getNearEffect());
            }
            objModel.put("merchant", merchant);
            objModel.put("merchantId", merchantId);
            objModel.put("sourceId", sourceId);
            objModel.put("sptype", StringUtil.isEmpty(request.getParameter("sptype")) ? 0 : request.getParameter("sptype"));
            objModel.put("spid", request.getParameter("spid"));
            objModel.put("sid", request.getParameter("sid"));
            List<AppProductPackageDto> productPackage = new ArrayList<>();
            if ("1".equals(packageGrayScale) && merchantId != null && merchantId != 0L) {
                List<AppProductPackageDto> productPackageDtoList = getProductPackage(id, branchCode, merchantId, productDto);
                if (CollectionUtil.isNotEmpty(productPackageDtoList)) {
                    rewritePackagePrice(productPackageDtoList);
                    productPackage = productPackageDtoList;
                }
            }
            objModel.put("productPrckagelList", productPackage);
            if (merchantId != null && merchantId != 0L) {
                ProductMerchantBusinessDTO productMerchantBusinessDTO = ecpProductMerchantBusinessApi.selectMerchantBySkuId(merchantId, productDto.getId());
                //未过期并且未发送: 出现已订阅的按钮
                if (productMerchantBusinessDTO != null && productMerchantBusinessDTO.getExpireDate().after((new Date()))) {
                    objModel.put("merchantBusiness", productMerchantBusinessDTO);
                }
            }
            //秒杀信息封装
            //处理秒杀结束时间字符串
            Map<String,Object> actSk = new HashMap<>();
            if (productDto.getSeckillEndTime() != null) {
                actSk.put("currentTime",new Date().getTime());
                actSk.put("startTime",productDto.getSeckillStartTime().getTime());
                actSk.put("endTime",productDto.getSeckillEndTime().getTime());
                if(productDto.getSeckillStatus() == 2){
                    actSk.put("status",MarketingSeckillActivityStatusEnum.NOT_STARTED.getStatus()); //秒杀状态:0-未开始 1-进行中 2-已结束
                    actSk.put("surplusTime",productDto.getSeckillStartTime().getTime());
                }else if(productDto.getSeckillStatus() == 3){
                    actSk.put("status",MarketingSeckillActivityStatusEnum.IN_PROGRESS.getStatus()); //秒杀状态:0-未开始 1-进行中 2-已结束
                    actSk.put("surplusTime",productDto.getSeckillEndTime().getTime());
                }else if(productDto.getSeckillStatus() == 4){
                    actSk.put("status",MarketingSeckillActivityStatusEnum.END.getStatus()); //秒杀状态:0-未开始 1-进行中 2-已结束
                }
                //秒杀活动信息
                objModel.put("actSk", actSk);
            }
            //查询拼团信息
            int assembleStatus = 0;
            if (merchantId != null && merchantId != 0L) {
                getBroupBuyingInfo(objModel, id, productPcDto.getMasterStandardProductId(), productPcDto.getProductUnit(), merchantId, productDto);
                //处理拼团活动商品展示名称
                StringBuilder showName = new StringBuilder();
                int isAssemble = (int) objModel.get("isAssemble");
                int isWholesale = (int) objModel.get("isWholesale");
                if (isAssemble == 1) {
                    Map actPt = (Map) objModel.get("actPt");
                    String productUnit = Optional.ofNullable(productPcDto.getProductUnit()).orElse("");
                    if(Constants.IS1 == productDto.getFirstChoose() && Constants.highGross.contains(productDto.getHighGross())){
                        showName.append("【首推优选】");
                    }else{
                        Object topicPrefix = actPt.get("topicPrefix");
                        if(null != topicPrefix && StringUtils.isNotEmpty(topicPrefix.toString())){
                            showName.append(topicPrefix.toString());
                        }
                    }
                    showName.append(actPt.get("skuStartNum")).append(productUnit).append("包邮");
                    assembleStatus = (int)actPt.get("assembleStatus");
                    if(assembleStatus == 1){//进行中替换起购数
                        Object skuStartNum = actPt.get("skuStartNum");
                        productPcDto.setLeastPurchaseNum((int)skuStartNum);
                        List<SkuInstructionImageBusinessDTO> siibd = new ArrayList<>();
                        SkuInstructionImageBusinessDTO instructionImageBusinessDTO = new SkuInstructionImageBusinessDTO();
                        instructionImageBusinessDTO.setInstrutionImageUrl(skuGroupBuying);
                        instructionImageBusinessDTO.setSkuId(id);
                        siibd.add(instructionImageBusinessDTO);
                        if(CollectionUtil.isNotEmpty(productPcDto.getSkuInstructionImageList())){
                            siibd.addAll(productPcDto.getSkuInstructionImageList());
                        }
                        productPcDto.setSkuInstructionImageList(siibd);
                    }
                    showName.append(productDto.getShowName());
                    productPcDto.setShowName(showName.toString());
                }else if(isWholesale == 1){
                    Map actPgby = (Map) objModel.get("actPgby");
                    assembleStatus = (int)actPgby.get("assembleStatus");
                    String productUnit = Optional.ofNullable(productPcDto.getProductUnit()).orElse("");
                    Object skuStartNum = actPgby.get("skuStartNum");
                    // 批购包邮进行中的展示起购数
                    if(assembleStatus == 1){
                        productPcDto.setLeastPurchaseNum((int)skuStartNum);
                    }
                    showName.append(String.valueOf((int)skuStartNum))
                            .append(productUnit)
                            .append("包邮 ")
                            .append(productDto.getShowName());

                    productPcDto.setShowName(showName.toString());
                }else {
                    showName.append(productDto.getShowName());
                    productPcDto.setShowName(showName.toString());
                }
            }
            //店铺信息
            List<ShopInfoDTO> shopInfoDTOList = shopServiceRpc.queryShopByShopCodes(Arrays.asList(productDto.getShopCode()));
            Map<String, Object> shop = new HashMap<>();
            if (CollectionUtil.isNotEmpty(shopInfoDTOList)) {
                ShopInfoDTO shopInfoDTO = shopInfoDTOList.get(0);

                shop.put("shopLogoUrl", shopInfoDTO.getPcLogoUrl());
                shop.put("shopName", shopInfoDTO.getShowName());
                shop.put("orgId", shopInfoDTO.getOrgId());
                shop.put("shopPatternCode", shopInfoDTO.getShopPatternCode());
                List<String> shopTag = new ArrayList<String>();
                if (ShopPropertyEnum.SELF.getCode().equals(shopInfoDTO.getShopPropertyCode())) {//自营店铺
                    shopTag.add("自营");
                }
                if (StringUtil.isNotEmpty(shopInfoDTO.getShopTags())) {
                    Collections.addAll(shopTag, shopInfoDTO.getShopTags().split(","));
                }
                if (shopTag.size() > 3) {
                    shopTag = shopTag.subList(0, 3);
                }
                shop.put("shopTag", shopTag);
                if(assembleStatus == 1 && productDto.getProductOwnerId().equals("1")){
                    shop.put("shopHomeUrl", pcPinTuanShopUrl+productDto.getShopCode()+"&orgId="+productDto.getOrgId());
                }else{
                    shop.put("shopHomeUrl", shopInfoDTO.getPcLink());
                }
                if (productDto.getIsThirdCompany() == 1) {
                    shop.put("shopName", productDto.getCompanyName());
                    shop.put("shopUrl", pcShopUrl + productDto.getOrgId());
                }
                objModel.put("isShowShop", 1);// 0不展示店铺入口，1展示店铺入口
            }
            //如果主品是虚拟供应商 本品是赠品，重新复制orgId 因为前端会用这个值去调用/company/center/companyInfo/getPopCompanyDetail.json接口
            if (BooleanUtils.isTrue(isMainProductVirtualSupplier) && ProductGiveEnum.GIFT.getCode().equals(productDto.getIsGive())) {
                productPcDto.setOrgId(virtualShopInfoDTO.getOrgId());
            }
            //受托生产厂家
            if(StringUtils.isNotBlank(productDto.getEntrustedManufacturer())){
                productPcDto.setManufacturer(ProductMangeUtils.getManufacturer(productDto.getMarketAuthor(),productDto.getManufacturer(),productDto.getEntrustedManufacturer()));
                productPcDto.setProductionAddress(productDto.getEntrustedManufacturerAddress());
            }

            objModel.put("shopInfo", shop);
            objModel.put("detail", productPcDto);
            objModel.put("id", id);
            objModel.put("merchantId", merchantId);
            return new ModelAndView("/sku_detail.ftl", objModel);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("search/skuDetail/{id}.htm-查询商品详情异常：", e);
            return new ModelAndView("/product_detail_no_result.ftl");
        }
    }

    /**
     * 计算指定时间与当前时间的差值
     * @param targetTime
     * @return
     */
    public String timeDifference(Date targetTime) {
        if (targetTime == null) {
            return "";
        }
        // 计算时间差（秒）
        long diffSecond = (new Date().getTime() - targetTime.getTime()) / 1000;

        if (diffSecond < 60) {
            return "刚刚";
        } else if (diffSecond < 3600) {
            return (diffSecond / 60) + "分钟前";
        } else if (diffSecond < 86400) {
            return (diffSecond / 3600) + "小时前";
        } else {
            return "1天前";
        }
    }

    private boolean handleXianShiJiaBuTag(TagDTO tagDTO){
        return Objects.equals(tagDTO.getText(),"限时加补");
    }
    private boolean isSpecialTag(TagDTO tagDTO){
        return Objects.equals(tagDTO.getPromoType(),MarketingEnum.YOU_HUI_QUAN.getCode())
                && Objects.equals(tagDTO.getSubType(), VoucherEnum.VoucherTypeEnum.SPECIALTY.getId());
    }

    /**
     * 查询同spu下其他sku信息
     * @param id
     * @param merchantId
     * @return
     */
    @RequestMapping("/findSpuAggregatSku")
    @ResponseBody
    public Object findSpuAggregatSku(Long id) {

        try {
            Long merchantId = null;
            Map<String, Object> resultMap = new HashMap<>();

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (ObjectUtil.isNotNull(merchant)) {
                merchantId = merchant.getId();
            }
            LOGGER.info("findSpuAggregatSku merchant:{}, id:{}, merchantId:{}", JsonUtil.toJson(merchant), id, merchantId);

            //参数校验
            if (ObjectUtil.isNull(id) || ObjectUtil.isNull(merchantId)) {
                resultMap.put("rows", Lists.newArrayList());
                return this.addResult("data", resultMap);
            }

            List<ComparePricesProdcutInfoVo> productList = skuProductService.findSpuAggregatSku(id, merchantId);

            List<Map<String, Object>> productMapList = Lists.newArrayList();
            for (PcSearchProductInfoVo item : productList) {
                Map<String, Object> productMap = Maps.newHashMap();
                productMap.put("productInfo",item);
                productMap.put("cardType",1); //cardType 1 表示组件是商品位，前端用于区分样式
                productMapList.add(productMap);
            }
            resultMap.put("rows", productMapList);
            resultMap.put("scmE", RandomUtil.nanoId(8));
            return this.addResult("data", resultMap);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }



    private void rewritePackagePrice(List<AppProductPackageDto> productPackageDtoList) {
        if (CollectionUtils.isEmpty(productPackageDtoList)){
            return;
        }
        if (StringUtils.isBlank(xyyConfig.getReWritePackagePriceIds())){
            return;
        }
        Set<Long> packageIdSet;
        try{
            packageIdSet = Sets.newHashSet(JSONArray.parseArray(xyyConfig.getReWritePackagePriceIds(), Long.class));
        }catch (Exception e){
            packageIdSet = Sets.newHashSet();
            LOGGER.error("rewritePackagePrice_reWritePackagePriceIds={}", JSON.toJSONString(xyyConfig.getReWritePackagePriceIds()));
        }
        if (CollectionUtils.isEmpty(packageIdSet)){
            return;
        }
        for (AppProductPackageDto item : productPackageDtoList){
            if (!packageIdSet.contains(item.getPackageId())){
                continue;
            }
            item.setPackagePrice(item.getSubtotalPrice());
            LOGGER.info("rewritePackagePrice_item={}", JSON.toJSONString(item));
        }
    }


    public void getBroupBuyingInfo(Map<String, Object> resuMap, Long skuId, String masterStandardProductId, String unit, Long merchantId, ProductDto productDto) {
        Set<Long> gaoMaoSkuIdSet = Sets.newHashSet();
        if(null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))){
            gaoMaoSkuIdSet.add(productDto.getId());
        }
        List<GroupBuyingInfoDto>  dtos = marketingService.getActCardInfoBySkuIdList(Arrays.asList(skuId), merchantId,Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
        GroupBuyingInfoDto dto = null;
        if (CollectionUtils.isNotEmpty(dtos)) {
            dto = dtos.get(0);
        }
        if(Objects.isNull(dto)){
            resuMap.put("isAssemble", 0);
            resuMap.put("isWholesale", 0);
            return;
        }
        if(dto.getActivityType().equals(MarketingEnum.PING_TUAN.getCode())){
            handlePtInfo(resuMap,dto,masterStandardProductId,unit,skuId, merchantId, productDto);
        } else if (dto.getActivityType().equals(MarketingEnum.PI_GOU_BAO_YOU.getCode())){
            handlePgbyInfo(resuMap,dto,masterStandardProductId,unit,skuId,merchantId);
        }else {
            resuMap.put("isAssemble", 0);
            resuMap.put("isWholesale", 0);
        }
    }

    private void handlePgbyInfo(Map<String, Object> resuMap,GroupBuyingInfoDto dto, String masterStandardProductId, String unit, Long skuId, Long merchantId){
        Map map = new HashMap();
        resuMap.put("isWholesale", 1);
        resuMap.put("isAssemble", 0);
        map.put("marketingId", dto.getMarketingId());
        map.put("activityType", dto.getActivityType());
        map.put("percentage", dto.getPercentage().multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_UP)));//进度
        if (dto.getStartTime().getTime() > System.currentTimeMillis()) {//未开始
            map.put("assembleStatus", 0);
            map.put("surplusTime", (dto.getStartTime().getTime() - System.currentTimeMillis()) / 1000);
        } else if (dto.getEndTime().getTime() < System.currentTimeMillis()) {//结束
            map.put("assembleStatus", 2);
        } else {
            map.put("assembleStatus", 1);
            map.put("surplusTime", (dto.getEndTime().getTime() - System.currentTimeMillis()) / 1000);
        }
        // 批购包邮暂不支持随心拼
        map.put("supportSuiXinPin", dto.isSupportSuiXinPin());
        map.put("suiXinPinButtonText", dto.getSuiXinPinButtonText());
        map.put("suiXinPinButtonBubbleText", dto.getSuiXinPinButtonBubbleText());
        map.put("preheatShowPrice", dto.getPreheatShowPrice());
        map.put("assembleStartTime", dto.getStartTime());
        map.put("assembleEndTime", dto.getEndTime());
        map.put("orderNum", dto.getOrderNum() != null ? dto.getOrderNum() : 0);
        map.put("supportSuiXinPin", dto.isSupportSuiXinPin());
        List recordlist = new ArrayList();
        ApiRPCResult<PromotionMsg> apiRPCResult = orderRpcService.queryPromotionMsg(skuId, dto.getMarketingId(), merchantId);
        if (apiRPCResult.isSuccess()) {
            PromotionMsg promotionMsg = apiRPCResult.getData();
            if (CollectionUtils.isNotEmpty(promotionMsg.getPromotionRecords())) {
                promotionMsg.getPromotionRecords().forEach(e -> {
                    Map recordMap = new HashMap();
                    String userId = String.valueOf(e.getMerchantName());
                    if (userId.length() > 2) {
                        String sub = userId.substring(1, userId.length() - 1);
                        String replace = StringUtils.repeat("*", sub.length());
                        userId = userId.replace(sub, replace);
                    }
                    recordMap.put("merchantName", userId);
                    recordMap.put("productAmount", "已购" + e.getProductAmount() + (unit == null ? "" : unit));
                    recordMap.put("createTime", timeDifference(e.getCreateTime()));
                    recordlist.add(recordMap);
                });
                map.put("assembleOrderList", recordlist);
            }
        }

        GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtils.isNotEmpty(dto.getGroupBuyingSkuDtoList())
                ? dto.getGroupBuyingSkuDtoList().get(0) : null;
        if (Objects.nonNull(groupBuyingSkuDto)) {
            // 起购数/活动价
            map.put("skuStartNum", groupBuyingSkuDto.getSkuStartNum());
            map.put("assemblePrice", groupBuyingSkuDto.getSkuPrice());
            map.put("totalLimitNum", groupBuyingSkuDto.getTotalLimitNum());
            map.put("personalLimitNum",groupBuyingSkuDto.getPersonalLimitNum());
            map.put("personalLimitType",groupBuyingSkuDto.getPersonalLimitType());
        }

        // 批购包邮没有阶梯价
        resuMap.put("actPgby", map);//拼团
    }

    public void handlePtInfo(Map<String, Object> resuMap,GroupBuyingInfoDto dto, String masterStandardProductId, String unit, Long skuId, Long merchantId, ProductDto productDto){
        Map map = new HashMap();
        resuMap.put("isAssemble", 1);
        resuMap.put("isWholesale", 0);
        map.put("marketingId", dto.getMarketingId());
        map.put("percentage", dto.getPercentage().multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_UP)).intValue());//进度
        if (dto.getStartTime().getTime() > System.currentTimeMillis()) {//未开始
            map.put("assembleStatus", 0);
            map.put("surplusTime", dto.getStartTime().getTime() - System.currentTimeMillis());
        } else if (dto.getEndTime().getTime() < System.currentTimeMillis()) {//结束
            map.put("assembleStatus", 2);
        } else {
            map.put("assembleStatus", 1);
            map.put("surplusTime", dto.getEndTime().getTime() - System.currentTimeMillis());
        }
        map.put("preheatShowPrice",dto.getPreheatShowPrice());
        GroupBuyingSkuDto groupBuyingSkuDto = dto.getGroupBuyingSkuDtoList().get(0);
        map.put("assemblePrice", groupBuyingSkuDto.getSkuPrice());
        map.put("assembleStartTime", dto.getStartTime());
        map.put("assembleEndTime", dto.getEndTime());
        map.put("skuStartNum", groupBuyingSkuDto.getSkuStartNum());
        map.put("subTitle", groupBuyingSkuDto.getMerchantCount() + "个客户已参与拼团");
        map.put("subTitleNum", String.valueOf(groupBuyingSkuDto.getMerchantCount()));
        map.put("orderNum", dto.getOrderNum() != null ? dto.getOrderNum() : 0 );
        map.put("supportSuiXinPin", dto.isSupportSuiXinPin());
        List recordlist = new ArrayList();
        if(BooleanUtils.isTrue(appProperties.getIsApplyGaoMaoGroupBuyingSaleLogic())
                && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                    || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))){
            OrderSkuStatisticsQueryDto queryDto = new OrderSkuStatisticsQueryDto();
            if(StringUtils.isNotEmpty(masterStandardProductId)){
                queryDto.setMasterStandardIds(Collections.singletonList(masterStandardProductId));
            }else{
                queryDto.setSkuIds(Collections.singletonList(skuId));
            }
            List<OrderSkuBuyRecordResultDto> resultDtos = orderSearchRpcService.orderSkuBuyRecordStatistics(queryDto);
            if(CollectionUtils.isNotEmpty(resultDtos)){
                resultDtos.forEach(resultDto -> {
                    Map<String, String> recordMap = new HashMap<>(3);
                    String userId = String.valueOf(resultDto.getMerchantName());
                    if (userId.length() > 2) {
                        String sub = userId.substring(1, userId.length() - 1);
                        String replace = StringUtils.repeat("*", sub.length());
                        userId = userId.replace(sub, replace);
                    }
                    recordMap.put("merchantName", userId);
                    recordMap.put("productAmount", "已购" + resultDto.getProductAmount() + (unit == null ? "" : unit));
                    recordMap.put("createTime", timeDifference(resultDto.getCreateTime()));
                    recordlist.add(recordMap);
                });
                map.put("assembleOrderList", recordlist);
            }
        }else {
            ApiRPCResult<PromotionMsg> apiRPCResult = orderRpcService.queryPromotionMsg(skuId, dto.getMarketingId(), merchantId);
            if (apiRPCResult.isSuccess()) {
                PromotionMsg promotionMsg = apiRPCResult.getData();
                if (CollectionUtils.isNotEmpty(promotionMsg.getPromotionRecords())) {
                    promotionMsg.getPromotionRecords().forEach(e -> {
                        Map recordMap = new HashMap();
                        String userId = String.valueOf(e.getMerchantName());
                        if (userId.length() > 2) {
                            String sub = userId.substring(1, userId.length() - 1);
                            String replace = StringUtils.repeat("*", sub.length());
                            userId = userId.replace(sub, replace);
                        }
                        recordMap.put("merchantName", userId);
                        recordMap.put("productAmount", "已购" + e.getProductAmount() + (unit == null ? "" : unit));
                        recordMap.put("createTime", timeDifference(e.getCreateTime()));
                        recordlist.add(recordMap);
                    });
                    map.put("assembleOrderList", recordlist);
                }
            }
        }
        // 拼团多阶梯价信息
        map.put("stepPriceStatus", dto.getStepPriceStatus());
        map.put("minSkuPrice", dto.getMinSkuPrice());
        map.put("maxSkuPrice", dto.getMaxSkuPrice());
        map.put("startingPriceShowText", dto.getStartingPriceShowText());
        map.put("rangePriceShowText", dto.getRangePriceShowText());
        map.put("stepPriceShowTexts", dto.generateStepPriceShowTexts(unit));
        map.put("topicPrefix", dto.getTopicPrefix());

        resuMap.put("actPt", map);//拼团
    }

    /**
     * 商品详情页根据商品id查询商品关联套餐
     *
     * @param skuId
     * @param branchCode
     * @param merchantId
     * @param thisProductDto
     * @return
     */
    public List<AppProductPackageDto> getProductPackage(Long skuId, String branchCode, Long merchantId, ProductDto thisProductDto) {
        List<AppProductPackageDto> result = new ArrayList<>();

        try {
            ActInfoQueryDTO actInfoQueryDTO = new ActInfoQueryDTO();
//            actInfoQueryDTO.setBranchCode(branchCode);
            actInfoQueryDTO.setCid(merchantId);
            List<Integer> status = new ArrayList<>();
            status.add(2);
            actInfoQueryDTO.setStatus(status);

            List<Long> csuIds = new ArrayList<>();
            csuIds.add(skuId);
            actInfoQueryDTO.setCsuIds(csuIds);

            Set<MarketingEnum> set = new HashSet<>();
            set.add(MarketingEnum.TAO_CAN);
            actInfoQueryDTO.setActTypeSet(set);
            actInfoQueryDTO.setIsNeedActSkuInfo(Boolean.TRUE);
            actInfoQueryDTO.setIsNeedActSkuLimit(Boolean.FALSE);
            actInfoQueryDTO.setIsNeedSelectAllSku(Boolean.TRUE);
            ApiRPCResult<Map<Long, List<ActInfoDTO>>> apiRPCResult = marketingQueryApi.queryActInfo(actInfoQueryDTO);

            if (apiRPCResult != null && apiRPCResult.isSuccess()) {
                Map<Long, List<ActInfoDTO>> data = apiRPCResult.getData();
                for (Long id : data.keySet()) {
                    List<ActInfoDTO> actInfoDTOS = data.get(id);
                    Integer count = 2;
                    if (actInfoDTOS.size() < 2) {
                        count = actInfoDTOS.size();
                    }
                    for (int i = 0; i < count; i++) {
                        ActInfoDTO actInfoDTO = actInfoDTOS.get(i);
                        String descriptionUrl = actInfoDTO.getExtend().get("descriptionUrl");
                        String descriptionPcUrl = actInfoDTO.getExtend().get("descriptionPcUrl");
                        String totalLimit = actInfoDTO.getExtend().get("totalLimit");
                        String personalLimit = actInfoDTO.getExtend().get("personalLimit");
                        String sourceType = actInfoDTO.getExtend().get("sourceType");


                        AppProductPackageDto appProductPackageDto = new AppProductPackageDto();
                        appProductPackageDto.setPackageId(actInfoDTO.getMarketingId());
                        appProductPackageDto.setPackagePrice(actInfoDTO.getDiscountContent().getPackageDiscount().getPackagePrice());
                        appProductPackageDto.setShopCode(actInfoDTO.getShopCode());
                        appProductPackageDto.setDescription(actInfoDTO.getDescription());
                        appProductPackageDto.setSourceType(StringUtil.isNotEmpty(sourceType) ? Integer.valueOf(sourceType) : MarketingActivitySourceTypeEnum.EC.getType());
                        //店铺套餐取配置的链接  自营取配置的链接
                        if (MarketingActivitySourceTypeEnum.EC.getType() == appProductPackageDto.getSourceType()) {
                            appProductPackageDto.setDescriptionPCUrl(pcPackageUrl);
                        } else {
                            appProductPackageDto.setDescriptionPCUrl(descriptionPcUrl);
                        }
                        appProductPackageDto.setDescriptionUrl(descriptionUrl);

                        BigDecimal oriPackageTotalPrice = BigDecimal.ZERO;

                        String canBuy = "1";
                        List<AppProductPackageSkuDTO> list = new ArrayList<>();
                        for (ActSkuInfoDTO actSkuInfoDTO : actInfoDTO.getActSkuInfoList()) {
                            String productNumber = actSkuInfoDTO.getExtend().get("productNumber");

                            AppProductPackageSkuDTO appProductPackageSkuDTO = new AppProductPackageSkuDTO();
                            appProductPackageSkuDTO.setSkuId(actSkuInfoDTO.getCsuId());
                            appProductPackageSkuDTO.setPackageId(actInfoDTO.getMarketingId());
                            appProductPackageSkuDTO.setProductNumber(StringUtil.isNotEmpty(productNumber) ? Integer.valueOf(productNumber) : 1);

                            ProductDto productDto = thisProductDto;
                            if (!skuId.equals(actSkuInfoDTO.getCsuId()) || productDto == null) {
                                productDto = prodcutApi.findSkuDetailByIdAndBranchCode(actSkuInfoDTO.getCsuId(), merchantId, branchCode);
                            }


                            appProductPackageSkuDTO.setBarcode(productDto.getBarcode());
                            appProductPackageSkuDTO.setCommonName(productDto.getCommonName());
                            Double fob = null != productDto.getDefaultFob() ? productDto.getDefaultFob() : productDto.getFob();
                            appProductPackageSkuDTO.setFob(new BigDecimal(String.format("%.2f", fob)));
                            appProductPackageSkuDTO.setSpec(productDto.getSpec());
                            appProductPackageSkuDTO.setIsControl(productDto.getIsControl());
                            appProductPackageSkuDTO.setIsPurchase(productDto.getPurchase());
                            appProductPackageSkuDTO.setIsOEM(productDto.getIsOEM());
                            appProductPackageSkuDTO.setSignStatus(productDto.getSignStatus());
                            appProductPackageSkuDTO.setShowAgree(productDto.getShowAgree());
                            appProductPackageSkuDTO.setAgreementEffective(productDto.getAgreementEffective());
                            if (totalLimit != null && Integer.valueOf(totalLimit) > 0) {
                                appProductPackageSkuDTO.setTotalLimit(Integer.valueOf(totalLimit));
                            } else {
                                appProductPackageSkuDTO.setTotalLimit(0);
                            }
                            if (personalLimit != null && Integer.valueOf(personalLimit) > 0) {
                                appProductPackageSkuDTO.setPersonalLimit(Integer.valueOf(personalLimit));
                            } else {
                                appProductPackageSkuDTO.setPersonalLimit(0);
                            }
                            BigDecimal mealMoney = appProductPackageSkuDTO.getFob().multiply(BigDecimal.valueOf(appProductPackageSkuDTO.getProductNumber()));
                            oriPackageTotalPrice = oriPackageTotalPrice.add(mealMoney);
                            appProductPackageSkuDTO.setSetMealMoney(mealMoney.setScale(2, BigDecimal.ROUND_HALF_UP));
                            appProductPackageSkuDTO.setImageUrl(productDto.getImageUrl());
                            list.add(appProductPackageSkuDTO);

                            //判断商品是否可买
                            if (!checkPackageProduct(appProductPackageSkuDTO)) {
                                //不可买修改套餐状态
                                canBuy = "0";
                            }
                        }
                        appProductPackageDto.setCanBuy(canBuy);
                        appProductPackageDto.setSkuList(list);
                        appProductPackageDto.setSubtotalPrice(oriPackageTotalPrice.setScale(2, BigDecimal.ROUND_HALF_UP));
                        appProductPackageDto.setDiscountPrice(oriPackageTotalPrice.subtract(appProductPackageDto.getSubtotalPrice()).setScale(2, BigDecimal.ROUND_HALF_UP));
                        appProductPackageDto.setSkuNum(list.size());
                        result.add(appProductPackageDto);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("getProductPackage error merchantId={},skuId={}", merchantId, skuId, e);
        }
        return result;
    }

    private boolean checkPackageProduct(AppProductPackageSkuDTO appProductPackageSkuDTO) {
        if (appProductPackageSkuDTO.getIsPurchase() != null && !appProductPackageSkuDTO.getIsPurchase()) {
            return false;
        }
        if ("true".equals(appProductPackageSkuDTO.getIsOEM()) && 0 == appProductPackageSkuDTO.getSignStatus()) {
            return false;
        }
        if ("0".equals(appProductPackageSkuDTO.getShowAgree())) {
            return false;
        }
        return true;
    }

    /**
     * 根据商品推荐id分类查询推荐的商品（PC换一批功能）
     *
     * @param categoryId
     * @param merchantId
     * @param rxNum      (统计当前页面是第几页)
     * @return
     */
    @RequestMapping("/findRecommendedSku")
    public ModelAndView findRecommendedSku(Long categoryId, Long merchantId, Integer rxNum, HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (rxNum == null) {
                rxNum = 1;
            }
            //根据用户id查询区域编码
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            ApiRPCResult<List<com.xyy.recommend.shop.dto.product.CsuInfo>> apiRPCResult = recommendedApi.recommendCusList(merchantId, branchCode, rxNum, 5, SourceEnum.H5, PageEnum.CUS_DETAIL_PAGE_RECOMMEND, null);
            List<com.xyy.recommend.shop.dto.product.CsuInfo> list = apiRPCResult.getData();
            //处理商品价格
            handleRecommendProductFob(list, merchant);

            //埋点数据生成
            MaidianParams maidianParams = MaidianParams.builder().merchantId(merchantId).spFrom(SnowGroundTypeEnum.RecommendSpFrom.PC_SKU_REC.getValue()).build();
            MaiDianVo maiDianVo = SearchUtils.buildMaiDianVoInfo(maidianParams, TerminalTypeEnum.PC.getValue(), SnowGroundTypeEnum.RECOMMEND);
           //处理受托生产厂家
            //受托生产厂家处理
            if(CollectionUtils.isNotEmpty(list)){
                for (com.xyy.recommend.shop.dto.product.CsuInfo info : list) {
                    if(StringUtils.isNotBlank(info.getEntrustedManufacturer())){
                        info.setManufacturer(ProductMangeUtils.getManufacturer(info.getMarketAuthor(),info.getManufacturer(),info.getEntrustedManufacturer()));
                    }
                }
            }

            //多嵌套一层map方便以后data中加其他属性
            resultMap.put("products", list);
            resultMap.put("merchant", merchant);
            resultMap.put("maiDianParams", maiDianVo);
            return new ModelAndView("/recommendedSku.ftl", resultMap);
        } catch (Exception e) {
            LOGGER.error("查询推荐商品列表异常：", e);
            return new ModelAndView("/recommendedSku.ftl", resultMap);
        }
    }

    /**
     * 查询第三方厂家商品
     *
     * @param orgId（第三方厂家商品组织机构id）
     * @return
     */
    @RequestMapping("/thirdSkuInfo.htm")
    public ModelAndView thirdSkuInfo(String orgId, String companyName, Page page, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            //用户所属域的域编码
            String branchCode = null;
            if (merchant != null) {
                merchantId = merchant.getId();
                branchCode = merchant.getRegisterCode();
            }
            if (StringUtil.isEmpty(branchCode)) {
                //当用户的域编码为空时，默认取湖北域
                branchCode = BranchEnum.HUBEI_COUNTRY.getKey();
            }

            SkuConditionDto skuPOJO = new SkuConditionDto();
            skuPOJO.setIsThirdCompany(Constants.IS1);
            skuPOJO.setOrgId(orgId);

//            branchCode = branchBusinessApi.convertCommon(branchCode);
            skuPOJO.setBranchCode(branchCode);

            Map<String, Object> objModel = new HashMap<>();
            // 分页要用，将url传到前台
            String url = getRequestUrl(request);
            /*******************获取商品列表**********************/
            //处理分页
            int oldOffset = page.getOffset();
            page.setLimit(20);//一页设置20条商品数据
            page.setOffset((oldOffset > 0 ? (page.getOffset() - 1) : 0) * page.getLimit());

            PageDto pageDto = new PageDto();
            BeanUtils.copyProperties(page, pageDto);
            Page<ProductDto> skuPOJOPage = new Page<>();
            //根据筛选条件筛选商品
            skuPOJO.setSourceFrom(SourceFromEnum.PC.getCode());
            SkuSearchDataDto skuSearchData = productBusinessApi.findSkuInfo(pageDto, skuPOJO, merchantId);
            List<ProductDto> skuPOJOList = skuSearchData.getSkuDtoList();
            skuPOJOPage.setLimit(page.getLimit());
            skuPOJOPage.setTotal(skuSearchData.getCount());
            skuPOJOPage.setRows(skuPOJOList);
            skuPOJOPage.setOffset((oldOffset > 0 ? oldOffset : 0));
            skuPOJOPage.setRequestUrl(url);
            //处理商品价格
            handleProductFob(skuPOJOList, merchant);
            //商品搜索页分页数据
            objModel.put("pager", skuPOJOPage);
            //用户数据
            objModel.put("merchant", merchant);
            //用户id
            objModel.put("merchantId", merchantId);

            //关键字（展示当前搜索的关键字用的）
            objModel.put("keyword", companyName);
            return new ModelAndView("/third_sku_search_list.ftl", objModel);
        } catch (Exception e) {
            LOGGER.error("/search/thirdSkuInfo.htm--第三方商品信息查询异常：", e);
            return new ModelAndView();
        }
    }

    /**
     * 高毛搜索商品
     *
     * @param page
     * @param skuConditionDto
     * @return
     */
    @RequestMapping("/findSkuInfoForGross.htm")
    @ResponseBody
    public Object findSkuInfoForGross(Page page, SkuConditionDto skuConditionDto, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            //根据用户id查询区域编码
            //用户所属域的域编码
            String branchCode = null;
            if (merchant != null) {
                merchantId = merchant.getId();
                branchCode = merchant.getRegisterCode();
            } else {
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            }
            skuConditionDto.setBranchCode(branchCode);
            Map<String, Object> objModel = new HashMap<>();
            //我的控销商品筛选条件
            String isControl = request.getParameter("is_control");
            if (StringUtil.isNotEmpty(isControl) && !"0".equals(isControl)) {
                if ("1".equals(isControl)) {
                    skuConditionDto.setIsControl(null);
                } else if ("2".equals(isControl)) {
                    skuConditionDto.setIsControl(1);
                }
            }
            // 分页要用，将url传到前台
            String url = getRequestUrl(request);
            //搜索关键词
            String keyword = request.getParameter("keyword");
            /***********处理搜索关键词**************/
            if (StringUtil.isNotEmpty(keyword)) {
                skuConditionDto.setShowName(keyword);
            }
            //大图模式和列表模式的类型
            String modelType = request.getParameter("model_type");
            /*********默认大图模式展示搜索页********/
            int type = 1;
            if (StringUtil.isNotEmpty(modelType)) {
                type = Integer.parseInt(modelType);
            }

            //筛选类目展开状态
            String cjZhan = request.getParameter("cjZhan");
            String ejZhan = request.getParameter("ejZhan");
            String sjZhan = request.getParameter("sjZhan");

            /****************处理商品分类**********************/
            //一级分类筛选条件
            String categoryFirstId = request.getParameter("categoryFirstId");
            //二级分类筛选条件
            String categorySecondId = request.getParameter("categorySecondId");
            //三级分类筛选条件
            String categoryThirdId = request.getParameter("categoryThirdId");
            if ("99999".equals(categoryFirstId)) {//当选择的为全部分类时，二级分类和三级分类置空，且展开状态为关闭
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                    categorySecondId = null;
                }
                if (StringUtil.isNotEmpty(categoryThirdId)) {
                    url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                    categoryThirdId = null;
                }
                ejZhan = "2";
                sjZhan = "2";

            }

            Map<Long, CategoryVo> categoryVoMap = categoryBusinessApi.getCategory(branchCode);

            //处理分类的筛选条件---当所选的分类的父类与所选的父类不对应时，清楚该分类的筛选
            //当所选的三级分类不为空时进行判断
            if (StringUtil.isNotEmpty(categoryThirdId)) {
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    //得到所选三级分类信息
                    CategoryBusinessDTO c3 = categoryVoMap.get(Long.parseLong(categoryThirdId));
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {//当所选一级分类不为空时，所选的二三级分类都要和一级分类比较
                        //得到所选二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(Long.parseLong(categorySecondId));
                        //如果所选二级分类的父类即一级分类也不是所选的一级分类，则将二级分类的筛选条件也去掉
                        if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                            categorySecondId = null;
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        } else {
                            //如果所选三级分类的父类即二级分类不是所选的二级分类，则将三级分类的筛选条件去掉
                            if (Long.parseLong(categorySecondId) != c3.getParentId()) {
                                url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                                categoryThirdId = null;
                            }
                        }
                    } else {
                        //如果所选三级分类的父类即二级分类不是所选的二级分类，则将三级分类的筛选条件去掉
                        if (Long.parseLong(categorySecondId) != c3.getParentId()) {
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        }
                    }
                } else {//当所选二级分类为空时，只和所选一记分类比较
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {
                        //得到所选的三级分类信息
                        CategoryBusinessDTO c3 = categoryVoMap.get(Long.parseLong(categoryThirdId));
                        //得到所选三级分类的二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(c3.getParentId());
                        //如果所选三级分类的二级分类的父类即一级分类不是所选的一级分类，则将三级分类的筛选条件去掉
                        if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categoryThirdId=" + Long.parseLong(categoryThirdId), "");
                            categoryThirdId = null;
                        }
                    }
                }
            } else {
                //当所选的二级分类不为空时进行判断
                if (StringUtil.isNotEmpty(categorySecondId)) {
                    if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {
                        //得到所选二级分类信息
                        CategoryBusinessDTO c2 = categoryVoMap.get(Long.parseLong(categorySecondId));
                        //如果所选二级分类的父类即一级分类不是所选的一级分类，则将二级分类的筛选条件去掉
                        if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                            url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                            categorySecondId = null;
                        }
                    }
                }
            }

            //处理所选的一级分类
            if (StringUtil.isNotEmpty(categoryFirstId)) {
                if (!"99999".equals(categoryFirstId)) {
                    skuConditionDto.setCategoryFirstId(Long.parseLong(categoryFirstId));
                } else {
                    //当一级分类的筛选条件为99999事搜索的是全部药品，分类的筛选条件置空
                    skuConditionDto.setCategoryFirstId(null);
                    skuConditionDto.setCategorySecondId(null);
                    skuConditionDto.setCategoryThirdId(null);
                }
            }
            // 推荐搜索关键字（展示在搜索框下面的）
            objModel.put("styleClass", "");
            String gjz = "";
            String gjz1 = "";

            //将搜索的分类展示在搜索框下面
            if (StringUtil.isNotEmpty(categorySecondId) && !"0".equals(categorySecondId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categorySecondId));
                if (null != category) {
                    if (StringUtil.isNotEmpty(keyword)) {
                        gjz = keyword + " 、" + category.getName();
                    } else if (StringUtil.isNotEmpty(gjz1)) {
                        gjz = gjz1 + " 、" + category.getName();
                    } else {
                        gjz = category.getName();
                    }
                }
            } else if (StringUtil.isNotEmpty(categoryThirdId) && !"0".equals(categoryThirdId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categoryThirdId));
                if (null != category) {
                    if (StringUtil.isNotEmpty(keyword)) {
                        gjz = keyword + " 、" + category.getName();
                    } else if (StringUtil.isNotEmpty(gjz1)) {
                        gjz = gjz1 + " 、" + category.getName();
                    } else {
                        gjz = category.getName();
                    }
                }
            } else if (StringUtil.isNotEmpty(categoryFirstId) && !"0".equals(categoryFirstId) && !"99999".equals(categoryFirstId)) {
                CategoryBusinessDTO category = categoryVoMap.get(Long.parseLong(categoryFirstId));
                if (category != null) {
                    if (StringUtil.isNotEmpty(keyword)) {
                        gjz = keyword + " 、" + category.getName();
                    } else if (StringUtil.isNotEmpty(gjz1)) {
                        gjz = gjz1 + " 、" + category.getName();
                    } else {
                        gjz = category.getName();
                    }
                }
            } else {
                if (StringUtil.isEmpty(keyword)) {
                    if (StringUtil.isNotEmpty(gjz1)) {
                        gjz = gjz1;
                    } else {
                        gjz = "髙毛专区";
                    }
                } else {
                    gjz = keyword;
                }

            }
            //获取筛选条件的厂商
            String manufacturer = request.getParameter("manufacturer");
            //获取销量的排序标识
            String orderSort = request.getParameter("order_sort");
            if ("1".equals(orderSort)) {
                skuConditionDto.setProperty("spa.sale_num");
                skuConditionDto.setDirection("desc");
            } else if ("2".equals(orderSort)) {
                skuConditionDto.setProperty("spa.sale_num");
                skuConditionDto.setDirection("asc");
            }
            //获取人气排行的标识
            String orderSaleRank = request.getParameter("order_sale_rank");
            if ("1".equals(orderSaleRank)) {
                skuConditionDto.setProperty("smsr.sale_num");
                skuConditionDto.setDirection("desc");
            } else if ("2".equals(orderSaleRank)) {
                skuConditionDto.setProperty("smsr.sale_num");
                skuConditionDto.setDirection("asc");
            }
            //获取最新上架排序标识
            String orderTime = request.getParameter("order_time");
            if ("1".equals(orderTime)) {
                skuConditionDto.setProperty("s.create_time");
                skuConditionDto.setDirection("desc");
            } else if ("2".equals(orderTime)) {
                skuConditionDto.setProperty("s.create_time");
                skuConditionDto.setDirection("asc");
            }
            //获取价格排序标识
            String orderFob = request.getParameter("order_fob");
            if ("1".equals(orderFob)) {
                skuConditionDto.setProperty("s.fob");
                skuConditionDto.setDirection("desc");
            } else if ("2".equals(orderFob)) {
                skuConditionDto.setProperty("s.fob");
                skuConditionDto.setDirection("asc");
            }
            //如果销量排序、最新上架排序、价格排序都为空，默认人气排序
            if (StringUtil.isEmpty(orderSort) && StringUtil.isEmpty(orderTime) && StringUtil.isEmpty(orderFob)) {
                if (orderSaleRank == null) {
                    orderSaleRank = "1";
                    skuConditionDto.setProperty("smsr.sale_num");
                    skuConditionDto.setDirection("desc");
                }
            }
            //只看有货筛选条件
            String hasStock = request.getParameter("has_stock");
            if (hasStock != null) {
                skuConditionDto.setHasStock(Integer.valueOf(hasStock));
            }
            //处方分类筛选条件
            String drugClassification = request.getParameter("drugClassification");
            if (StringUtil.isNotEmpty(drugClassification)) {
                if ("5".equals(drugClassification)) {
                    skuConditionDto.setDrugClassification(null);
                } else {
                    skuConditionDto.setDrugClassification(Integer.parseInt(drugClassification));
                }
            }
            /****************价格区间的筛选条件********************/
            String minPrice = request.getParameter("minPrice");
            String maxPrice = request.getParameter("maxPrice");
            //设置筛选的价格
            if (StringUtil.isNotEmpty(minPrice)) {
                skuConditionDto.setMinPrice(Double.parseDouble(minPrice));
            }
            if (StringUtil.isNotEmpty(maxPrice)) {
                skuConditionDto.setMaxPrice(Double.parseDouble(maxPrice));
            }

            /*******************获取厂家列表*************************/
            skuConditionDto.setMerchantId(merchantId);
            List<String> manufacturerList = productBusinessApi.searchManufacturerList(skuConditionDto);

            /*******************获取商品列表**********************/
            //处理分页
            int oldOffset = page.getOffset();
            page.setLimit(20);//一页设置20条商品数据
            page.setOffset((oldOffset > 0 ? (page.getOffset() - 1) : 0) * page.getLimit());
            PageGross<ListProduct> skuPOJOPage = new PageGross<>();
            //根据筛选条件筛选商品
            PageDto pageDto = new PageDto();
            pageDto.setLimit(page.getLimit());
            pageDto.setOffset(page.getOffset());
            //判断是否在自配区
            boolean isShowFragileGoods = merchantBussinessApi.checkFragileLimitedByMerchantId(merchantId);
            if (!isShowFragileGoods) {
                skuConditionDto.setIsShowFragileGoods(1);//易碎品是否可见
            }
            List<ListProduct> skuVOList = new ArrayList<>();
            List<ListProduct> grossProducts = new ArrayList<>();
            //查询髙毛商品
            skuConditionDto.setHighGross(Constants.IS2);
            skuConditionDto.setSourceFrom(SourceFromEnum.PC.getCode());
            ListSkuSearchData skuSearchData = productBusinessApi.findEsProductForGross(pageDto, skuConditionDto, merchantId);
            //髙毛商品的总个数
            if (oldOffset == 1 || oldOffset == 0) {
                grossCount = skuSearchData == null ? 0 : Integer.parseInt(skuSearchData.getCount().toString());
            }
            if (skuSearchData != null) {
                grossProducts.addAll(skuSearchData.getGrossSkus());
            }
            if (CollectionUtil.isNotEmpty(grossProducts)) {
                grossProducts.forEach(sku -> {
                    //如果是髙毛商品则打髙毛标签
                    if (sku.getHighGross().equals(Constants.IS2)) {
                        TagDTO tagDTO = new TagDTO();
                        tagDTO.setName("髙毛");
                        tagDTO.setUiType(2);
                        tagDTO.setSort(TagSortEnum.GaoMao.sortNum());
                        List<TagDTO> tagDTOList = sku.getTagList();
                        if (CollectionUtil.isNotEmpty(tagDTOList)) {
                            tagDTOList.add(tagDTO);
                            //按打标的优先级从小到大排序
                            tagDTOList = tagDTOList.stream().sorted(Comparator.comparing(TagDTO::getSort)).collect(Collectors.toList());
                            sku.setTagList(tagDTOList);
                        } else {
                            List<TagDTO> tagList = new ArrayList<>();
                            tagList.add(tagDTO);
                            sku.setTagList(tagList);
                        }
                    }
                });
            }

            ListSkuSearchData otherSkuSearchData = null;
            //非髙毛商品个数
            if (oldOffset == 1 || oldOffset == 0) {
                //筛选出非髙毛
                skuConditionDto.setHighGross(Constants.IS1);
                otherSkuSearchData = productBusinessApi.findEsProductForGross(pageDto, skuConditionDto, merchantId);
                otherCount = otherSkuSearchData == null ? 0 : Integer.parseInt(otherSkuSearchData.getCount().toString());
            }
            //髙毛商品占满一页(一页20个商品)的页数
            int grossPage = grossCount / 20;
            if (grossProducts.size() == 0 || grossProducts.size() < 20) {
                //判断在第几页才会出现非髙毛品种
                int num = 0;
                if (oldOffset == 0) {
                    num = 1 - grossPage;
                } else {
                    num = oldOffset - grossPage;
                }
                if (num > 0) {
                    //设置非髙毛查询初始化分页
                    pageDto.setOffset((num - 1) * 20);
                    //筛选出非髙毛
                    skuConditionDto.setHighGross(Constants.IS1);
                    otherSkuSearchData = productBusinessApi.findEsProductForGross(pageDto, skuConditionDto, merchantId);
                    if (otherSkuSearchData != null) {
                        skuVOList.addAll(otherSkuSearchData.getSkuDtoList());
                    }
                }
            }
            //处理商品价格
            handleListProductFob(skuVOList, merchant);
            handleListProductFob(grossProducts, merchant);
            //商品总个数(当髙毛和非髙毛商品同时存在时，只算非髙毛个数为一页)
            int total = grossPage * 20 + otherCount;
            skuPOJOPage.setLimit(page.getLimit());
            skuPOJOPage.setTotal(Long.parseLong(String.valueOf(total)));
            skuPOJOPage.setRows(skuVOList);
            skuPOJOPage.setGrossRows(grossProducts);
            skuPOJOPage.setOffset((oldOffset > 0 ? oldOffset : 0));
            skuPOJOPage.setRequestUrl(url);

            List<CategoryVo> listCategory = categoryBusinessApi.getCategoryTree(branchCode);
            objModel.put("listCategory", listCategory);
            //商品搜索页分页数据
            objModel.put("pager", skuPOJOPage);
            //商品一级分类筛选数据
            objModel.put("categoryFirstId", categoryFirstId);
            //商品二级分类筛选数据
            objModel.put("categorySecondId", categorySecondId);
            //商品三级分类筛选数据
            objModel.put("categoryThirdId", categoryThirdId);
            //用户数据
            objModel.put("merchant", merchant);
            //用户id
            objModel.put("merchantId", merchantId);
            //厂家数据
            objModel.put("manufacturerList", manufacturerList);
            //筛选的厂家数据
            objModel.put("manufacturer", manufacturer);
            //销量筛选条件
            objModel.put("orderSort", orderSort);
            //人气筛选条件
            objModel.put("orderSaleRank", orderSaleRank);
            //最新上架筛选条件
            objModel.put("orderTime", orderTime);
            //价格筛选条件
            objModel.put("orderFob", orderFob);
            //我的控销筛选条件
            objModel.put("isControl", isControl);
            //是否有货筛选条件
            objModel.put("hasStock", hasStock);
            //处方类型
            objModel.put("drugClassification", drugClassification);
            //最小价格筛选条件
            objModel.put("minPrice", minPrice);
            //最大价格的筛选条件
            objModel.put("maxPrice", maxPrice);
            //三级分类是否展开的标识
            objModel.put("cjZhan", StringUtil.isNotEmpty(cjZhan) ? Integer.parseInt(cjZhan) : 0);
            objModel.put("ejZhan", StringUtil.isNotEmpty(ejZhan) ? Integer.parseInt(ejZhan) : 0);
            objModel.put("sjZhan", StringUtil.isNotEmpty(sjZhan) ? Integer.parseInt(sjZhan) : 0);
            //大图模式和列表模式的类型
            objModel.put("modelType", type);
            //关键字（展示当前搜索的关键字用的）
            objModel.put("keyword", gjz);
            //搜索关键字（填充搜索框用的）
            objModel.put("keywordSearch", keyword);
            objModel.put("styleClass11", "cur");
            return new ModelAndView("/sku_search_gross_list.ftl", objModel);
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("/search//findSkuInfoForGross.htm-查询髙毛商品列表信息异常：", e);
            return this.addError("获取高毛商品列表失败！");
        }
    }
}
