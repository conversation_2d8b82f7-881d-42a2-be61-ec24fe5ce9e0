package com.xyy.ec.pc.cms.helpers;

import com.xyy.ec.pc.cms.param.CmsExpectProductQueryParam;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.Objects;

/**
 * {@link CmsExpectProductQueryParam} 帮助类
 *
 * <AUTHOR>
 */
public class CmsExpectProductQueryParamHelper {

    /**
     * 校验
     *
     * @param cmsExpectProductQueryParam
     * @return
     */
    public static Boolean validate(CmsExpectProductQueryParam cmsExpectProductQueryParam) {
        // 校验
        if (cmsExpectProductQueryParam == null) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "店铺商品查询");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        if (StringUtils.isEmpty(cmsExpectProductQueryParam.getExhibitionId())) {
            String message = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "商品展示组ID");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, message);
        }
        if (StringUtils.isEmpty(cmsExpectProductQueryParam.getBranchCode())) {
            String message = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "分公司编码");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, message);
        }
        if (Objects.isNull(cmsExpectProductQueryParam.getExpectNum()) || cmsExpectProductQueryParam.getExpectNum() <= 0) {
            String message = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "数量");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, message);
        }
        return true;
    }

}
