package com.xyy.ec.pc.newfront.service;

import com.xyy.ec.pc.newfront.dto.SearchAgesRespVO;
import com.xyy.ec.pc.newfront.dto.SearchCategoryRespVO;
import com.xyy.ec.pc.newfront.dto.SearchRespVO;
import com.xyy.ec.pc.newfront.vo.SearchParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.search.ecp.params.PcSearchQueryParam;

import javax.servlet.http.HttpServletRequest;

public interface SearchNewService {



    /**
     * 搜索商品列表
     */
    AjaxResult<SearchRespVO> listProducts(PcSearchQueryParam searchQueryParam, HttpServletRequest httpServletRequest) throws Exception;

    /**
     * 搜索结果分类
     */
    AjaxResult<SearchAgesRespVO> getSearchCategory(PcSearchQueryParam searchQueryParam) throws Exception;
}
