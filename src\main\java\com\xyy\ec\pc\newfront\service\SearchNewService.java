package com.xyy.ec.pc.newfront.service;

import com.iwhalecloud.xyy.cms.dto.CmsHotWordDto;
import com.iwhalecloud.xyy.cms.dto.param.HotWordShowListParam;
import com.xyy.ec.pc.newfront.dto.SearchAgesRespVO;
import com.xyy.ec.pc.newfront.dto.SearchRespVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.search.ecp.params.PcSearchQueryParam;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface SearchNewService {



    /**
     * 搜索商品列表
     */
    AjaxResult<SearchRespVO> listProducts(PcSearchQueryParam searchQueryParam, HttpServletRequest httpServletRequest) throws Exception;

    /**
     * 搜索结果分类
     */
    AjaxResult<SearchAgesRespVO> getSearchCategory(PcSearchQueryParam searchQueryParam) throws Exception;

    /**
     * 热词
     */
    AjaxResult<List<CmsHotWordDto>> getHotWords(HotWordShowListParam param);
}
