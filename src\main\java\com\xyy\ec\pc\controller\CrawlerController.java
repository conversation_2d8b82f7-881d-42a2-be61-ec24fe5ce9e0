package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.pc.base.BaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @Classname CrawlerController
 * @Description TODO
 * @Date 2020/8/21 15:26
 * @Created by gelong
 */
@RequestMapping("/crawler")
@Controller
public class CrawlerController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(CrawlerController.class);
    @Reference(version = "1.0.0")
    private MerchantLicenseApi licenseApi;

    /**
     * 爬虫获取用户信息
     *
     * @param merchantId
     * @return
     */
    @RequestMapping(value = "/getMerchantForReptiles")
    @ResponseBody
    public Object getMerchantForReptiles(long merchantId) {
        logger.info("爬虫获取用户信息,入参：merchantId:{}", merchantId);
        try {
            ApiRPCResult rpcResult = licenseApi.getMerchantForReptiles(merchantId);
            logger.info("爬虫获取用户信息接口返回值:{}", JSONObject.toJSONString(rpcResult));
            if (rpcResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                logger.info("获取爬虫获取用户信息失败");
                return this.addError(rpcResult.getErrMsg());
            }
            return this.addResult("data", rpcResult.getData());
        } catch (Exception e) {
            logger.error("爬虫获取用户信息异常:", e);
            return this.addError("系统错误，请稍后重试");
        }
    }

    /**
     * 接收爬虫系统推送的用户id
     * @param ids
     * @return
     */
//    @RequestMapping("/getCrawlerUser")
//    @ResponseBody
//    public Object getCrawlerUser(String ids) {
//        logger.info("接收爬虫系统推送的用户id:{}",JSON.toJSONString(ids));
//        try {
//            List<Long> list = JSONObject.parseArray(ids, Long.class);
//            licenseApi.setCrawlerUser(list);
//        }catch (Exception e){
//            logger.error("接收爬虫系统推送的用户id异常",e);
//            return this.addError("处理失败！");
//        }
//        return this.addResult();
//    }
}
