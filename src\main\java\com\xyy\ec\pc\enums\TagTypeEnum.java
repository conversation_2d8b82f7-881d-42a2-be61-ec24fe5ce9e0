package com.xyy.ec.pc.enums;

/**
 * 标签类型
 * @author: denghp
 * @date: 2021/12/3
 */
public enum TagTypeEnum {
    TITLE_TAG("titleTags", "标题标签"),
    PRODUCT_TAG("productTags", "商品标签"),
    ACTIVITY_TAG("activityTag", "活动标签"),
    ACTIVITY_TAGS("activityTags", "活动标签组"),
    DATA_TAG("dataTags", "数据标签"),
    MARKER_TAG("markerTag", "角标标签"),
    PURCHASE_TAG("purchaseTags", "购买次数"),
    COUPON_TAGS("couponTags", "优惠券标签"),
    UNIT_PRICE_TAG("unitPriceTag", "单价标签"),
    PURCHASE_SEARCH_TAG("userPurchasedSearchedTag", "常购常搜次数"),
    ;

    private String name;

    private String remark;

    TagTypeEnum(String name, String remark) {
        this.name = name;
        this.remark = remark;
    }

    public String getName() {
        return name;
    }

    public String getRemark() {
        return remark;
    }
}
