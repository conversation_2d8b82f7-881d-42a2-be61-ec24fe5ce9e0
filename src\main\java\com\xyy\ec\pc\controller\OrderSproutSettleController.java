package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.dianping.cat.message.Transaction;
import com.xyy.cat.util.CatUtil;
import com.xyy.ec.base.framework.exception.XyyEcOrderBizNoneCheckRTException;
import com.xyy.ec.merchant.bussiness.api.ShippingAddressBussinessApi;
import com.xyy.ec.merchant.bussiness.api.crm.InvoiceBussinessCrmApi;
import com.xyy.ec.merchant.bussiness.dto.InvoiceTypeBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.ShippingAddressBussinessDto;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderSproutBusinessApi;
import com.xyy.ec.order.business.api.ShoppingCartBusinessApi;
import com.xyy.ec.order.business.common.ResultDTO;
import com.xyy.ec.order.business.config.OrderEnum;
import com.xyy.ec.order.business.config.OrderSproutTypeEnum;
import com.xyy.ec.order.business.dto.OrderSproutBusinessDto;
import com.xyy.ec.order.business.dto.ecp.order.OrderSettleCommunicationDto;
import com.xyy.ec.order.business.model.ServiceResponse;
import com.xyy.ec.order.business.utils.BigDecimalUtils;
import com.xyy.ec.order.core.dto.Order;
import com.xyy.ec.order.core.dto.cart.ShoppingCartDto;
import com.xyy.ec.order.core.dto.cart.ShoppingCartGroupDto;
import com.xyy.ec.order.core.dto.cart.ShoppingCartInfo;
import com.xyy.ec.order.core.util.DateUtil;
import com.xyy.ec.order.dto.settle.SettleVO;
import com.xyy.ec.order.enums.BizSourceEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.constants.CodeMapConstants;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.model.order.OrderSettleVo;
import com.xyy.ec.pc.model.order.OrderSproutSettleVo;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.service.CodeItemService;
import com.xyy.ec.pc.service.OrderService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.impl.OrderServiceImpl;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ec.pc.vo.order.ConfirmOrderVo;
import com.xyy.ec.system.business.dto.CodeitemBusinessDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: jiangruyi
 * @Date: 2020年01月10日15:39:34
 * @Description: 代下单 下单 订单控制器
 */
@Controller
@RequestMapping("/merchant/center/order")
public class OrderSproutSettleController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(OrderSproutSettleController.class);

    @Autowired
    private HttpServletRequest request;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Autowired
    private CodeItemService codeItemService;

    @Reference(version = "1.0.0")
    private ShippingAddressBussinessApi shippingAddressBussinessApi;

    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;

    @Reference(version = "1.0.0")
    private OrderSproutBusinessApi orderSproutBusinessApi;
    @Reference(version = "1.0.0")
    private InvoiceBussinessCrmApi invoiceBussinessCrmApi;
    @Reference(version = "1.0.0")
    private ShoppingCartBusinessApi shoppingCartBusinessApi;
    @Autowired
    private OrderService orderService;
    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;
    /**
     * APP去结算，逻辑同settleNew
     *
     * @return
     */
    @RequestMapping(value = "/gotoSproutSettle.json")
    @ResponseBody
    public Object gotoSettle(ModelMap modelMap,String purchaseNo) {
        // 1. 拼接请求参数
        OrderSproutSettleVo order = new OrderSproutSettleVo();
        order.setOrderSource(4);
        order.setAppVersion(1);
        order.setUseBalance(false);
        order.setRealIP(IPUtils.getClientIP(request));
        order.setPurchaseNo(purchaseNo);
        logger.info("当前用户请求的IP地址为:"+order.getRealIP());
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            order.setBranchCode(merchant.getRegisterCode());
            // 2. 调用预结算方法
            ResultDTO<String> resultDTO = orderService.preSettleSprout(order);

            if(!resultDTO.getIsSuccess()){
                return addError(0, resultDTO.getErrorMsg());
            }
        } catch (IllegalArgumentException e) {
            logger.error("购物车生成订单异常", e);
            String message = e.getMessage();
            if (StringUtils.isNotEmpty(message)&&message.startsWith("您采购的部分商品因为库存不足")) {
                orderSproutBusinessApi.setErrorOrderSprout(purchaseNo,"您采购的部分商品因为库存不足、下架或超出经营范围");
                return this.addError(3, "您采购的部分商品因为库存不足、下架或超出经营范围");
            }

            if (StringUtil.isNotBlank(message) && message.contains("限购")){
                message = "商品超过最大限购数量";
            }
            //如果有错误 则将采购单改为失效状态
            orderSproutBusinessApi.setErrorOrderSprout(purchaseNo,message);
            return this.addError(1, message);
        } catch (Exception e) {
            //如果有错误 则将采购单改为失效状态
            orderSproutBusinessApi.setErrorOrderSprout(purchaseNo,"购物车生成订单异常");
            logger.error("购物车生成订单异常", e);
            return this.addError(1, "购物车生成订单异常");
        }
        return this.addResult();
    }

    /**
     * 购物车生成订单
     *
     * @param modelMap
     * @param voucherMonitor 优惠券提醒弹出框，用户点击查看的时候传入 1，其他情况下不用传
     *                       逻辑处理：传入1，表示该次刷新结算数据是从弹出框点击（则不清除优惠券弹出标记），而不是正常的购物车到结算（清除优惠券弹出标记），
     * @return String
     * @Title: pay
     */
    @RequestMapping("/sproutSettle.htm")
    public ModelAndView settle(ModelMap modelMap, RedirectAttributes attr, boolean storeStatus, @RequestParam(required = false) Integer claimVoucherNum, String voucherMonitor, String purchaseNo) {
        // 1. 拼接请求参数
        OrderSproutSettleVo order = new OrderSproutSettleVo();
        order.setOrderSource(4);
        order.setAppVersion(1);
        order.setUseBalance(false);
        order.setPurchaseNo(purchaseNo);
        order.setVoucherMonitor(voucherMonitor);

        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            order.setBranchCode(merchant.getRegisterCode());
            // 2. 调用预结算方法
//            ResultDTO<OrderSettleCommunicationDto> resultDTO = orderService.settleSprout(order);
            ResultDTO<SettleVO> resultDTO = orderService.settleSproutV2(order);

            if(!resultDTO.getIsSuccess()){
                attr.addFlashAttribute("errorMsg", resultDTO.getErrorMsg());
                orderSproutBusinessApi.setErrorOrderSprout(purchaseNo, resultDTO.getErrorMsg());
                return new ModelAndView(new RedirectView("/merchant/center/cart/index.htm",true,false));
            }

            // 3. 填充结算页数据
            modelMap.put("purchaseNo", purchaseNo);
            orderService.setSettleDataForRefactor(modelMap,resultDTO.getData(),order.getBranchCode(),merchant);
            if(claimVoucherNum!=null&&claimVoucherNum>0){
                modelMap.put("claimVoucherMsg",String.format(Constants.CLAIM_VOUCHER_DIALOG,claimVoucherNum));
            }
        } catch (IllegalArgumentException e) {
            logger.error("购物车生成订单异常", e);
            String message = e.getMessage();
            if (StringUtils.isNotEmpty(message)&&message.startsWith("您采购的部分商品因为库存不足")) {
                orderSproutBusinessApi.setErrorOrderSprout(purchaseNo,"您采购的部分商品因为库存不足、下架或超出经营范围");
                attr.addFlashAttribute("errorMsg", "您采购的部分商品因为库存不足、下架或超出经营范围");
                return new ModelAndView(new RedirectView("/merchant/center/sprout/order/index.htm",true,false));
            }

            if (StringUtil.isNotBlank(message) && message.contains("限购")){
                message = "商品超过最大限购数量";
            }
            attr.addFlashAttribute("errorMsg", message);
            //如果有错误 则将采购单改为失效状态
            orderSproutBusinessApi.setErrorOrderSprout(purchaseNo, message);
            return new ModelAndView(new RedirectView("/merchant/center/sprout/order/index.htm",true,false));
        }catch (Exception e) {
            logger.error("生成订单异常", e);
            attr.addFlashAttribute("errorMsg", e.getMessage());
            //如果有错误 则将采购单改为失效状态
            orderSproutBusinessApi.setErrorOrderSprout(purchaseNo, e.getMessage());
            return new ModelAndView(new RedirectView("/merchant/center/sprout/order/index.htm",true,false));
        }
        return new ModelAndView("/sprout/settle.ftl");
    }




    /**
     * 统计结算金额
     * @Title: calcSettleAmount
     * @return
     * Object
     */
    @RequestMapping("/calcSproutSettleAmount.json")
    @ResponseBody
    public Object calcSettleAmount(OrderSproutSettleVo paramOrder) {
        try{
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            String branchCode = merchant.getRegisterCode();
            paramOrder.setOrderSource(4);
            paramOrder.setAppVersion(1);
            paramOrder.setBranchCode(merchant.getRegisterCode());
            paramOrder.setMerchantId(merchant.getId());
            paramOrder.setUseBalance(false);
            ResultDTO<SettleVO> resultDTO = orderService.calcSproutSettleAmountV2(paramOrder);

            if(!resultDTO.getIsSuccess()){
                return addError(resultDTO.getErrorMsg());
            }

            Map<String, Object> resultMap = orderService.setCalcSettleAmount(resultDTO.getData(), branchCode);
            return resultMap;
        }catch(Exception se){
            logger.error("订单结算金额异常", se);
        }
        return null;
    }


    /**
     * 跳转到支付页面
     *
     * @param order
     * @return String
     * @Title: settle
     */
    @RequestMapping("/confirmSproutOrder.htm")
    public ModelAndView confirmOrder(ConfirmOrderVo order, ModelMap modelMap, String shoppingCartImgUUID, RedirectAttributes redirectAttributes) {
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
                ModelAndView modelAndView = orderService.setConfimOrder(order, merchant, modelMap, request, OrderSproutTypeEnum.SPROUT_ORDER.getKey(),null);
            return modelAndView;
        } catch (Exception e) {
            logger.error("生成订单异常", e);
            //解决url后面拼接参数问题
//            if(merchant !=null)
//                orderService.setErrorMsg(merchant.getId(), e.getMessage());
            String message = e.getMessage();
            if (StringUtil.isNotBlank(message) && message.contains("限购")){
                message = "商品超过最大限购数量";
            }
            orderSproutBusinessApi.setErrorOrderSprout(order.getPurchaseNo(), message);
            redirectAttributes.addFlashAttribute("errorMsg", message);
            RedirectView redirectView = new RedirectView("/merchant/center/sprout/order/index.htm", true, false, false);
            return new ModelAndView(redirectView);
        }
    }

    @RequestMapping("/voucherSproutMonitor.json")
    @ResponseBody
    public Object voucherMonitor(Order paramOrder){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            logger.info("优惠券检测，MerchantId:{}，voucherIds:{}",new Object[]{merchant.getId(),paramOrder.getVoucherIds()});
            paramOrder.setOrderSource(4);
            paramOrder.setAppVersion(1);
            paramOrder.setUseBalance(false);
            paramOrder.setMerchantId(merchant.getId());
            paramOrder.setBranchCode(merchant.getRegisterCode());
            ResultDTO<String> response = orderService.voucherMonitor(paramOrder, BizSourceEnum.SPROUT.getKey());

            if(!response.getIsSuccess()){
                return ServiceResponse.failure(response.getErrorMsg());
            }

            return ServiceResponse.success();
        } catch (Exception e) {
            logger.error("com.xyy.ec.pc.controller.OrderSproutSettleController.voucherMonitor 异常:",e);
            return ServiceResponse.success();
        }
    }
}
