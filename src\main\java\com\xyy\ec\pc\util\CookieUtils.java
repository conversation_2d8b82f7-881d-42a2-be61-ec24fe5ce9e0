/**
 * @(#)CookieUtils.java Copyright 2010 jointown, Inc. All rights reserved.
 */

package com.xyy.ec.pc.util;

import cn.hutool.extra.spring.SpringUtil;
import io.jsonwebtoken.Claims;
import com.xyy.ec.pc.authentication.consts.Constants;
import com.xyy.ec.pc.authentication.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * description
 * 
 * 
 */
@Slf4j
public class CookieUtils {

    public static final String VIEW_HISTORY_COOKIE_KEY = "client_view_history";
    public static final int    COOKIE_AGE              = 30 * 24 * 60 * 60;
    public static final String COOKIE_PATH             = "/";

    private CookieUtils(){
    }

    /**
     * 得到当前request请求的所有cookie
     * 
     * 
     * @return cookie数组
     */
    public static Cookie[] getCookies() {
        HttpServletRequest request = WebContext.currentRequest();
        return request == null ? null : request.getCookies();
    }

    /**
     * 根据cookie名字取得cookie
     * 
     * 
     * @param name cookie名字
     * @return cookie
     */
    public static Cookie getCookie(String name) {
        Cookie[] cookies = getCookies();
        if (CollectionUtil.isNotEmpty(cookies)) {
            for (int i = 0; i < cookies.length; i++) {
                Cookie cookie = cookies[i];
                String cookName = cookie.getName();
                if (cookName != null && cookName.equals(name)) {
                    return cookie;
                }
            }
        }
        return null;
    }

    /**
     * 将cookie写入客户端
     * 
     *
     * @param cookie
     */
    public static void writeCookie(Cookie cookie) {
        if (cookie == null) return;
        HttpServletResponse response = WebContext.currentResponse();
        HttpServletRequest request = WebContext.currentRequest();
        if (request != null) {
            String host = request.getHeader("Host");
            if (ConfigUtils.WEBSITE.equals(host)) cookie.setDomain("." + ConfigUtils.DOMAIN);
        }
        if (response != null) {
            response.addCookie(cookie);
        }
    }

    /**
     * 极光登录成功
     */
    public static void setJgLogin() {
        try {
            Cookie cookie = new Cookie("jg_login", "1");
            cookie.setPath("/");
            cookie.setMaxAge(86400);
            CookieUtils.writeCookie(cookie);
        } catch (Exception e) {
            log.error("写入极光标识到cookies出错",e);
        }
    }

    public static void removeCookie(String cookieName, String path) {
        HttpServletRequest request = WebContext.currentRequest();
        Cookie[] cookies = request.getCookies();
        if (CollectionUtil.isEmpty(cookies)) return;

        for (int i = 0; i < cookies.length; i++) {
            Cookie cookie = cookies[i];
            if (cookie.getName().equals(cookieName)) {
                cookie.setMaxAge(0);
                cookie.setPath(path);
                String host = request.getHeader("Host");
                if (ConfigUtils.WEBSITE.equals(host)) cookie.setDomain("." + ConfigUtils.DOMAIN);
                HttpServletResponse response = WebContext.currentResponse();
                if(response != null) {
                	response.addCookie(cookie);
                }
                break;
            }
        }
    }

    /**
     * 获取用户ID
     */
    public static String getMerchantId() {

        Cookie cookie = getCookie("xyy_principal");
        if (cookie == null) {
            return null;
        }
        String cookieValue = cookie.getValue();
        String[] values = cookieValue.split("&");
        if (values.length == 3) {
            return values[2];
        }
        return null;
    }

    /**
     * 获取账号ID
     */
    public static String getAccountId() {

        // 判断cookie中有无xyy_token, 兼容之前登录
        Cookie cookie = getCookie("xyy_token");
        if (cookie != null) {
            try {
                Claims claims = SpringUtil.getBean(TokenService.class).parseToken(cookie.getValue());
                return claims.get(Constants.JWT_ACCOUNT_ID) != null  ? claims.get(Constants.JWT_ACCOUNT_ID).toString() : null;
            }
            catch (Exception ignored) {

            }
        }
        else {
            cookie = getCookie("xyy_principal");
            if (cookie == null) {
                return null;
            }
            String cookieValue = cookie.getValue();
            String[] values = cookieValue.split("&");
            if (values.length >= 2) {
                return values[0];
            }
        }
        return null;
    }

    /**
     * 获取token
     */
    public static String getToken() {

        // 判断cookie中有无xyy_token, 兼容之前登录
        Cookie cookie = getCookie("xyy_token");
        if (cookie != null) {
            return cookie.getValue();
        }
        else {
            cookie = getCookie("xyy_principal");
            if (cookie == null) {
                return null;
            }
            String cookieValue = cookie.getValue();
            String[] values = cookieValue.split("&");
            if (values.length >= 2) {
                return values[1];
            }
        }
        return null;
    }
}
