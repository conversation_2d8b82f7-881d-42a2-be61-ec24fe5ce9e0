package com.xyy.ec.pc.cms.param;

import com.xyy.ec.layout.buinese.ecp.enums.CmsSelectProductStrategyTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * CMS：秒杀期望数量查询秒杀商品参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CmsSeckillProductExpectQueryParam implements Serializable {

    private static final long serialVersionUID = -1110233754593296959L;

    /**
     * 会员ID
     */
    private Long merchantId;

    /**
     * 商品区域编码
     */
    private String productBranchCode;

    /**
     * 会员是否不能看易碎品
     */
    private Boolean merchantIsNotWatchFragileGoods;

    /**
     * 选择商品的策略类型
     *
     * @see CmsSelectProductStrategyTypeEnum
     */
    private String selectProductStrategyType;

    /**
     * 指定商品ID列表
     */
    private List<Long> specifiedCsuIds;

    /**
     * 指定商品组ID
     */
    private String specifiedExhibitionIdStr;

    /**
     * 期望数量
     */
    private Integer expectedNum;
}
