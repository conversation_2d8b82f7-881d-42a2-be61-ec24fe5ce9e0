package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ms.promotion.business.api.layout.VoucherBagForLayoutBusinessApi;
import com.xyy.ms.promotion.business.common.ErrorCodeEum;
import com.xyy.ms.promotion.business.common.ResultDTO;
import com.xyy.ms.promotion.business.dto.voucher.ReceiveVoucherBagRequestDTO;
import com.xyy.ms.promotion.business.dto.voucher.VoucherBagLayOutDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: PC优惠券大礼包
 * @date 2018/11/15 2:56 PM
 */
@Controller
@RequestMapping("/couponPackage")
public class VoucherBagMerchantController extends BaseController {


    private static final Logger LOGGER = LoggerFactory.getLogger(VoucherBagMerchantController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Reference(version = "1.0.0")
    private VoucherBagForLayoutBusinessApi voucherBagApi;

    /**
     * 领取优惠券礼包记录
     *
     * @param voucherBagId
     * @return
     */
    @RequestMapping("/addCouponPackageecord.json")
    @ResponseBody
    public Object addCouponPackageecord(Long voucherBagId, String voucherTemplateId) {
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (null == merchant) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            ReceiveVoucherBagRequestDTO requestDTO = new ReceiveVoucherBagRequestDTO();
            requestDTO.setMerchantId(merchantId);
            requestDTO.setVoucherBagId(voucherBagId);
            requestDTO.setVoucherTemplateIds(voucherTemplateId);
            ResultDTO resultDTO = voucherBagApi.receiveVoucherBag(requestDTO);
            if (null != requestDTO && resultDTO.getErrorCode() == ErrorCodeEum.SUCCESS.getErrorCode()) {
                return this.addResult("领取成功");
            } else {
                return this.addResult("领取失败");
            }
        } catch (Exception e) {
            LOGGER.error("领取优惠券礼包记录/addCouponPackageecord.json异常：",e);
            return this.addError(e.toString());
        }
    }

    /**
     * 优惠券大礼包列表
     */
    @RequestMapping("/findVoucherBagTemplateList.json")
    @ResponseBody
    public Object findVoucherBagTemplateList() {
        Map<String, Object> map = null;
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (null == merchant) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            Long merchantId = 0l;
            if (merchant != null) {
                merchantId = merchant.getId();
            }
            ResultDTO<VoucherBagLayOutDTO> voucherBagLayOutDTOResultDTO = voucherBagApi.findVoucherBagTemplate(merchantId);
            if (null == voucherBagLayOutDTOResultDTO || voucherBagLayOutDTOResultDTO.getErrorCode() == ErrorCodeEum.SUCCESS.getErrorCode()) {
                return this.addResult("data", Maps.newHashMap());
            }
            VoucherBagLayOutDTO voucherBagLayOutDTO = voucherBagLayOutDTOResultDTO.getData();
            return this.addResult("data", JSONObject.parseObject(JSONObject.toJSONString(voucherBagLayOutDTO), HashMap.class));
        } catch (Exception e) {
            LOGGER.error("优惠券大礼包列表/findVoucherBagTemplateList.json异常：",e);
            return this.addResult("data", Maps.newHashMap());
        }
    }

    /**
     * 手动更新缓存
     */
    @RequestMapping("/voucherBagCatch.json")
    @ResponseBody
    public Object cat() {
//        try {
//            voucherBagService.voucherBagCatch();
//        } catch (Exception e) {
//            LOGGER.error(e.getMessage());
//            return this.addError("缓存更新失败");
//        }
        return this.addResult("缓存更新成功");
    }

}
