package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.PlanningScheduleBussinessApi;
import com.xyy.ec.merchant.bussiness.api.PlanningScheduleProductBusinessApi;
import com.xyy.ec.merchant.bussiness.api.ecp.ecpplanningschedule.EcpPlanningScheduleProductBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.PlanningScheduleBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.PlanningScheduleProductBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.PlanningScheduleProductInfoBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.TempProductBussinessDto;
import com.xyy.ec.merchant.bussiness.enums.ElectronicPlanOrderStatusEnum;
import com.xyy.ec.merchant.bussiness.excetion.PlanningScheduleProductBusinessDtoExtension;
import com.xyy.ec.merchant.bussiness.utils.ChineseCharToEnUtil;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderExtendBusinessApi;
import com.xyy.ec.pc.aspect.PlanningScheduleFlag;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.base.Sort;
import com.xyy.ec.pc.config.ExportTemplate;
import com.xyy.ec.pc.enums.ElectronicPlanOrderSkuColumnEnum;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.pc.util.excel.ExcelCommonUtil;
import com.xyy.ec.pc.util.excel.ExcelExport;
import com.xyy.ec.pc.util.excel.PageExportExcelDataUtil;
import com.xyy.ec.pc.util.excel.ReflectionUtils;
import com.xyy.ec.product.business.api.ProductBusinessApi;
import com.xyy.ec.product.business.api.ecp.productInfo.EcpProductBusinessApi;
import com.xyy.ec.product.business.dto.ProductDto;
import com.xyy.ec.system.business.api.BranchBusinessApi;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * 我的计划单
 * <AUTHOR>
 */
@RequestMapping("/merchant/center/planningSchedule")
@Controller
public class PlanningScheduleController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlanningScheduleController.class);

    private static final int HEAD_ROW_NUM = 1;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private PlanningScheduleProductBusinessApi planningScheduleProductBusinessApi;

    @Reference(version = "1.0.0")
    private EcpPlanningScheduleProductBusinessApi ecpPlanningScheduleProductBusinessApi;

    @Reference(version = "1.0.0")
    private PlanningScheduleBussinessApi planningScheduleBussinessApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;

    @Reference(version = "1.0.0")
    private OrderExtendBusinessApi orderExtendBusinessApi;

    @Reference(version = "1.0.0")
    private BranchBusinessApi branchBusinessApi;

    @Reference(version = "1.0.0")
    private ProductBusinessApi productBusinessApi;

    @Reference(version = "1.0.0")
    private EcpProductBusinessApi ecpProductBusinessApi;


    @RequestMapping(value = "/index.htm", method = RequestMethod.GET)
    public String planningScheduleIndex(PlanningScheduleBussinessDto planningSchedule, Page page, Sort sort, ModelMap modelMap,
                                        HttpServletRequest request) {
        MerchantPrincipal merchant = null;
        try {
            merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            planningSchedule.setMerchantId(merchant.getId());
            planningSchedule.setBranchCode(merchant.getRegisterCode());

            int oldOffset = page.getOffset();
            if (planningSchedule.getEndCreateTime() != null) {
                planningSchedule.setEndCreateTime(DateUtils.getLateInTheDay(planningSchedule.getEndCreateTime()));
            }
            //设置页码和页容量默认值
			Integer pageNum = 1;
			Integer pageSize = 10;
			if (page != null) {
				pageNum = page.getOffset();
				pageSize = page.getLimit();
			}
			page.setOffset((oldOffset > 0 ? (page.getOffset() - 1) : 0));
			PageInfo<PlanningScheduleBussinessDto> pageInfo = planningScheduleBussinessApi.selectPageList(planningSchedule, pageNum, pageSize);
            Page<PlanningScheduleBussinessDto> resultPage = new Page<>();

            resultPage.setRequestUrl(getRequestUrl(request));
            resultPage.setOffset(oldOffset);
            resultPage.setRows(pageInfo.getList());
            resultPage.setTotal(pageInfo.getTotal());

            modelMap.put("pager", resultPage);
            modelMap.put("paramOrder", planningSchedule);
            modelMap.put("center_menu", "myplan");
            modelMap.put("merchant", merchant);
            modelMap.put("total", pageInfo.getTotal());
            return "/planningSchedule/list.ftl";
        } catch (Exception e) {
            LOGGER.error("查下电子计划单列表失败！e="+ExceptionUtils.getStackTrace(e));
            return "/planningSchedule/list.ftl";
        }
    }


    @RequestMapping("/add.json")
    @ResponseBody
    public Object add(PlanningScheduleBussinessDto planningSchedule) throws Exception {
		//todo 电子计划单下线
		if(true){
			return this.addResult("电子计划单已下线!");
		}
        try {
            MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            if (merchant == null || merchant.getId() == null) {
                return this.addError("当前会话已失效，请重新登入");
            }

            PlanningScheduleBussinessDto entity = new PlanningScheduleBussinessDto();
            entity.setPlanningName(planningSchedule.getPlanningName());
            entity.setMerchantId(merchant.getId());
            int count = planningScheduleBussinessApi.selectCount(entity);
            if (count > 0) {
                return this.addError("已存在相同名称的计划单，请修改名称");
            }
            planningSchedule.setSourceType(1);
            planningSchedule.setMerchantId(merchant.getId());
            Long planningId = planningScheduleBussinessApi.insertSelective(planningSchedule);
            planningSchedule.setId(planningId);
            return super.addResult("planningSchedule", planningSchedule);
        } catch (Exception e) {
            LOGGER.error("生成电子计划单异常", e);
            return this.addError("生成电子计划单异常" + e);
        }
    }

    @RequestMapping("/delete.json")
    @ResponseBody
    public Object delete(PlanningScheduleBussinessDto planningSchedule) throws Exception {
		//todo 电子计划单下线
		if(true){
			return this.addResult("电子计划单已下线!");
		}
        try {
            planningScheduleBussinessApi.deleteByPrimaryKey(planningSchedule.getId());
            return super.addResult("");
        } catch (Exception e) {
            LOGGER.error("删除电子计划单异常", e);
            return this.addError("删除电子计划单异常" + e);
        }
    }


    @RequestMapping("/update.json")
    @ResponseBody
    public Object update(PlanningScheduleBussinessDto planningSchedule) throws Exception {
		//todo 电子计划单下线
		if(true){
			return this.addResult("电子计划单已下线!");
		}
        try {
            MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            if (merchant == null || merchant.getId() == null) {
                return this.addError("当前会话已失效，请重新登入");
            }
            PlanningScheduleBussinessDto entity = new PlanningScheduleBussinessDto();
            entity.setMerchantId(merchant.getId());
            List<PlanningScheduleBussinessDto> entityList = planningScheduleBussinessApi.selectList(entity);
            if (CollectionUtils.isNotEmpty(entityList)) {
                for (PlanningScheduleBussinessDto e : entityList) {
                    if (!e.getId().equals(planningSchedule.getId())
                            && e.getPlanningName().equals(planningSchedule.getPlanningName())) {
                        return this.addError("已存在相同名称的计划单，请修改名称");
                    }
                }
            }
            planningScheduleBussinessApi.updateByPrimaryKeySelective(planningSchedule);
            return super.addResult("");
        } catch (Exception e) {
            LOGGER.error("删除电子计划单异常", e);
            return this.addError("删除电子计划单异常" + e);
        }
    }

    /**
     * 商品信息导出
     *
     * @param response
     * @param req
     */
    @RequestMapping("/export")
    @PlanningScheduleFlag
    public void exportExcel(Long id, HttpServletResponse response, HttpServletRequest req) throws Exception {
        if (id == null) {
            LOGGER.error("id不能为空");
            return;
        }

        String headers = "69码,商品名称,规格,厂家,补货数量";
        String dataIndexs = "code,productName,spec,manufacturer,purchaseNumber";

        String excelFileName = "PlanningOrder";
        int[] styles = new int[]{1, 1, 1, 1, 1};
        int[] alignStyles = new int[]{1, 1, 1, 1, 1};
        int[] widths = new int[]{2000, 4000, 4000, 4000, 4000};

        // 创建一个工作表
        PageExportExcelDataUtil pageExportExcelDataUtil = new PageExportExcelDataUtil(null, excelFileName);
        pageExportExcelDataUtil.setSheetHeader(headers.split(","), widths);

        List<PlanningScheduleProductBussinessDto> dataList = planningScheduleProductBusinessApi.planningScheduleForProductStatus(id, null, null, null);

        pageExportExcelDataUtil.fillDataToSheet(dataIndexs.split(","), styles, alignStyles, dataList);

        pageExportExcelDataUtil.generateExcelFile(response);

    }


    @RequestMapping("/downloadTemplate")
	public void downloadTemplate(final HttpServletResponse response) {
		List<String> list = new ArrayList<String>();

		for (ElectronicPlanOrderSkuColumnEnum e : ElectronicPlanOrderSkuColumnEnum.values()) {
			list.add(e.getKey());
		}
		ExcelExport.exportTemplateFile(ExportTemplate.ELECTRONIC_PLAN_ORDER_SKU, list, new ArrayList<Integer>(),
				response, new HashMap<String, String>());
	}

	@RequestMapping("/importData")
    @ResponseBody
    public Object importData(HttpServletRequest request,
                             HttpServletResponse response, Long planningScheduleId)  throws Exception {
		//todo 电子计划单下线
		if(true){
			return this.addResult("电子计划单已下线!");
		}
	 	 MultipartHttpServletRequest me = ((MultipartHttpServletRequest)request);
		 Iterator iter=me.getFileNames();
		 MultipartFile file = null;
         while(iter.hasNext()){
             //一次遍历所有文件
              file=me.getFile(iter.next().toString());
         }

	    Workbook wb = null;
	    String fileName = file.getOriginalFilename();
	    StringBuilder errorMsg = new StringBuilder();
	    List<PlanningScheduleProductBussinessDto> matchSkuList = new ArrayList<PlanningScheduleProductBussinessDto>();
	    List<PlanningScheduleProductBussinessDto> thirdSkuList = new ArrayList<PlanningScheduleProductBussinessDto>();
		List<PlanningScheduleProductBussinessDto> noMatchSkuList = new ArrayList<PlanningScheduleProductBussinessDto>();
		List<String> hasGoodSkuCodeList = new ArrayList<String>();
		try {
			hasGoodSkuCodeList = planningScheduleProductBusinessApi.pcSelectHasGoodSkuCodeList(planningScheduleId);
			InputStream inputStream = file.getInputStream();
			if (ExcelCommonUtil.isExcel2003(fileName)) {
				wb = new HSSFWorkbook(inputStream);
			} else if (ExcelCommonUtil.isExcel2007(fileName)) {
				wb = new XSSFWorkbook(inputStream);
			}
			Sheet sheet = wb.getSheetAt(0);
			// 得到总行数
			int rowNum = sheet.getLastRowNum();
			Row rowCntRow = null;
			Row row = null;
			Cell cell = null;
			int validateNum = 0;


			row = sheet.getRow(HEAD_ROW_NUM - 1);
			int colNum = row.getPhysicalNumberOfCells();
			Map<Integer, String> columnEnMap = new HashMap<Integer, String>();
			Set<String> columnChNameset = new HashSet<String>();



			for (ElectronicPlanOrderSkuColumnEnum e : ElectronicPlanOrderSkuColumnEnum.values()) {
				columnChNameset.add(e.getKey());
			}

			String cellValue = "";
			// 这段校验可要可不要： 如果不要,只要导入的必须的字段，其他的字段不取值
			for (int j = 0; j < colNum; j++) {
				cell = row.getCell(j);
				cellValue = ExcelCommonUtil.getCellFormatValue(cell).trim();
				if (!columnChNameset.contains(cellValue)) {
					return super.addError(String.format("列名[%s]不合法", cellValue));
				}
				columnChNameset.remove(cellValue);
				columnEnMap.put(j, ElectronicPlanOrderSkuColumnEnum.get(cellValue));
			}

			if(!columnChNameset.isEmpty()){
				return super.addError("导入数据模板不对，请重新下载模板");
			}

			Object cellValueObj = null;

			for (int n = HEAD_ROW_NUM; n < rowNum + 1; n++) {
				row = sheet.getRow(n);
				if(row == null){
					continue;
				}
				for (int j = 0; j < colNum; j++) {
					cell = row.getCell(j);
					String propertyName = columnEnMap.get(j);
					//barcode 和branchCode 不能为空
					if(ElectronicPlanOrderSkuColumnEnum.CODE.getValue().equals(propertyName)){
						cellValueObj = ExcelCommonUtil.getCellValue(cell,Boolean.TRUE);
						if(null != cellValueObj){
							validateNum ++;
						}
						break;
					}
				}
			}

			if (CollectionUtil.isNotEmpty(hasGoodSkuCodeList)){
				if(validateNum > 1000){
					return this.addError("重复批量导入数据量过大,上限1000条");
				}
			}else {
				if(validateNum > 3000){
					return this.addError("批量导入数据量过大,上限3000条");
				}
			}

			PlanningScheduleProductBussinessDto pageData = null;
			Set<String> codeSet = new HashSet<String>();
			MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			String branchCode = merchant.getRegisterCode();
//			branchCode = branchBusinessApi.convertCommon(branchCode);

			for (int n = HEAD_ROW_NUM; n < rowNum + 1; n++) {
				row = sheet.getRow(n);
				if(row == null){
					continue;
				}

				pageData = new PlanningScheduleProductBussinessDto();
				boolean validateCode = false;

				for (int j = 0; j < colNum; j++) {
					cell = row.getCell(j);

					String propertyName = columnEnMap.get(j);

					//barcode 和branchCode 不能为空
					if(ElectronicPlanOrderSkuColumnEnum.CODE.getValue().equals(propertyName)){
						cellValueObj = ExcelCommonUtil.getCellValue(cell,Boolean.TRUE);
						if(null != cellValueObj){

							if(hasGoodSkuCodeList.contains(cellValueObj)){
								break;
							}

							if(codeSet.contains(cellValueObj.toString())) {
								errorMsg.append(String.format("第【%s】行的编码在列表中重复了;\n", n + 1));
								break;
							}
							codeSet.add(cellValueObj.toString());
							//先校验EC的库
							ProductDto product = ecpProductBusinessApi.getSkuInfoByCodeAndBranchCode(cellValueObj.toString(),branchCode,merchant.getId());
							if(product == null || null == product.getId()){
								//走第三方库
								TempProductBussinessDto entity = new TempProductBussinessDto();
								entity.setCode(cellValueObj.toString());
								List<TempProductBussinessDto> tempProductList = planningScheduleBussinessApi.selectTempProductList(entity);
								if(CollectionUtils.isNotEmpty(tempProductList)){
									validateCode = true;
									BeanUtils.copyProperties(tempProductList.get(0), pageData);
									pageData.setStatus(ElectronicPlanOrderStatusEnum.THIRD.getId());
									thirdSkuList.add(pageData);
								}
								else{
									String time = String.valueOf(System.nanoTime()) + RandomUtil.getRandomCode(6);
									String code = "ybm"+time;
									pageData.setCode(code);
									pageData.setStatus(ElectronicPlanOrderStatusEnum.NO_MATCH.getId());
									noMatchSkuList.add(pageData);
								}

							}
							else{
								validateCode = true;
								BeanUtils.copyProperties(product, pageData);
								pageData.setProductName(product.getShowName());
								pageData.setStatus(ElectronicPlanOrderStatusEnum.ON_SALE.getId());
								matchSkuList.add(pageData);
							}
						}

						continue;
					}

					if(ElectronicPlanOrderSkuColumnEnum.REGISTER_NUM.getValue().equals(propertyName)){
						cellValueObj = ExcelCommonUtil.getCellValue(cell,Boolean.FALSE);
						if(null != cellValueObj && (cellValueObj instanceof Number)){
							pageData.setPurchaseNumber(((BigDecimal)cellValueObj).intValue());
						}
						else{
							pageData.setPurchaseNumber(1);
						}
						continue;
					}


					if(validateCode == false){
						cellValueObj = ExcelCommonUtil.getCellValue(cell,Boolean.TRUE);
						ReflectionUtils.setFieldValue(pageData, propertyName, cellValueObj);
						if(ElectronicPlanOrderSkuColumnEnum.PRODUCT_NAME.getValue().equals(propertyName)){
							if(null != cellValueObj){
								pageData.setZjm(ChineseCharToEnUtil.getFirstUpperSpell(cellValueObj.toString()));
							}
						}
					}

				}
				if(StringUtil.isNotEmpty(pageData.getProductName()) && StringUtil.isEmpty(pageData.getZjm())){
			   		pageData.setZjm(ChineseCharToEnUtil.getFirstUpperSpell(pageData.getProductName()));
			   	}
			}

		} catch (Exception e) {
			LOGGER.error("批量导入数据异常", e);
			return this.addError("批量导入数据异常");
		}
		Integer allNum = 0;
		Integer hasNum = 0;
		if(CollectionUtil.isNotEmpty(matchSkuList)){
			allNum = matchSkuList.size();
			hasNum = matchSkuList.size();
		}
		if (CollectionUtil.isNotEmpty(thirdSkuList)){
			allNum += thirdSkuList.size();
		}
		if (CollectionUtil.isNotEmpty(noMatchSkuList)){
			allNum += noMatchSkuList.size();
		}
		return super.addResult(new String[]{"matchSkuList","noMatchSkuList","thirdSkuList", "msg","allNum","hasNum"},
				new Object[]{matchSkuList,noMatchSkuList,thirdSkuList,errorMsg.toString(),allNum,hasNum});
	}


	@RequestMapping("/generate.json")
    @ResponseBody
    public Object generate(@RequestBody PlanningScheduleProductBusinessDtoExtension planningScheduleProductDTO) {
		//todo 电子计划单下线
		if(true){
			return this.addResult("电子计划单已下线!");
		}
		if(planningScheduleProductDTO.getPlanningScheduleId() == null){
			return super.addError("计划表主键id不能为空");
		}
		if(CollectionUtils.isEmpty(planningScheduleProductDTO.getMatchList())
				&& CollectionUtils.isEmpty(planningScheduleProductDTO.getNoMatchList())){
			return super.addError("没有要生成的数据");
		}
		if (planningScheduleProductDTO.getIsChecked() == 1){
			planningScheduleProductDTO.setNoMatchList(null);
			planningScheduleProductDTO.setThirdSkuList(null);
		}

		try{
			planningScheduleBussinessApi.batchGenerate(planningScheduleProductDTO);
		}
		catch(Exception e){
			LOGGER.error("电子计划单批量生成失败",e);
			return super.addError("生成失败"+ e);
		}

		return super.addResult("操作成功");
	}

	/**
	 * 拼接页面参数
	 *
	 * @param request
	 * @return
	 */
	@Override
	protected String getRequestUrl(HttpServletRequest request){
		String url = "";
		String requestUri = request.getRequestURI();
		String queryString = request.getQueryString();
		String qs = StringUtil.removeParameter(queryString, "offset");
		if(requestUri.contains("/xyy-shop/")){
			requestUri = requestUri.replace("/xyy-shop/", "/");
		}
		if (StringUtil.isNotEmpty(qs)) {
			url = requestUri + "?" + EncodeUtil.urlDecode(qs,"UTF-8");
		} else {
			url = requestUri;
		}
		return url;
	}

	/**
	 * 根据69码获取商品信息
	 *
	 * @param code(69码)
	 *
	 * @return
	 */
	@RequestMapping("/getProductByCode.json")
	@ResponseBody
	public Object getProductByCode(String code) {
		try {
			MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			if (merchant == null || merchant.getId() == null) {
				return this.addError("当前会话已失效，请重新登入");
			}
			if (StringUtils.isEmpty(code)) {
				return this.addError("69码为空");
			}
			PlanningScheduleProductInfoBussinessDto planningScheduleProductInfo = ecpPlanningScheduleProductBusinessApi.addProduct(code,merchant.getRegisterCode(),merchant.getId());
			return this.addResult("planningSchedule", planningScheduleProductInfo);
		}
		catch (Exception e) {
			LOGGER.error("获取商品信息异常", e);
			return this.addError("获取商品信息异常，请稍后重试");
		}
	}

	/**
	 * 扫码后将药品添加到计划单
	 * @param planningScheduleProduct
	 * @return
	 */
	@RequestMapping("/addProductToPlanningSchedule.json")
	@ResponseBody
	public Object addProductToPlanningSchedule(PlanningScheduleProductBussinessDto planningScheduleProduct){
		//todo 电子计划单下线
		if(true){
			return this.addResult("电子计划单已下线!");
		}
		try {
			MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			if(merchant == null || merchant.getId() == null){
				return this.addError("当前会话已失效，请重新登入");
			}
			if (StringUtil.isEmpty(planningScheduleProduct.getCode())){
				return this.addError("添加到计划单的药品编码为空！");
			}
			if(planningScheduleProduct.getPlanningScheduleId() == null) {
				return this.addError("电子计划单添加失败");
			}
			planningScheduleProduct.setBranchCode(merchant.getRegisterCode());

			Long planProductId = ecpPlanningScheduleProductBusinessApi.addProductToPlanningSchedule(planningScheduleProduct,merchant.getId());
			return this.addResult();
			//已存在商品，更新商品数量
//			if (planProductId == 1L) {
//				return this.addResult("updateFlag", "true");
//			}
//			//返回商品信息给前端
//			PlanningScheduleProductBussinessDto hasSkuProduct = planningScheduleProductBusinessApi.getHasGoodSku(planProductId);
//			return this.addResult(new String[]{"updateFlag", "planningScheduleProduct"}, new Object[]{"false", hasSkuProduct});
		} catch (Exception e) {
			LOGGER.error("电子计划单商品处理异常", e);
			return this.addError("电子计划单商品处理异常");
		}
	}

	/**
	 * 添加药品到计划单 手动输入
	 * @param planningScheduleProductInfo
	 * @param planningScheduleProduct
	 * @return
	 */
	@RequestMapping("/addProductToPlanningScheduleByInput.json")
	@ResponseBody
	public Object addProductToPlanningScheduleByInput(PlanningScheduleProductInfoBussinessDto planningScheduleProductInfo,PlanningScheduleProductBussinessDto planningScheduleProduct){
		//todo 电子计划单下线
		if(true){
			return this.addResult("电子计划单已下线!");
		}
		try {
			MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
			if(merchant == null || merchant.getId() == null){
				return this.addError("当前会话已失效，请重新登入");
			}
			if (StringUtil.isEmpty(planningScheduleProduct.getCode())){
				return this.addError("添加到计划单的药品编码为空！");
			}
			if(planningScheduleProduct.getPlanningScheduleId() == null) {
				return this.addError("电子计划单添加失败");
			}
			planningScheduleProduct.setBranchCode(merchant.getRegisterCode());
			planningScheduleProduct.setProductName(planningScheduleProductInfo.getProductName());
			Long planProductId = ecpPlanningScheduleProductBusinessApi.addProductToPlanningScheduleByInput(planningScheduleProductInfo, planningScheduleProduct,merchant.getId());
			if (planProductId == null) {
				return this.addError("药品添加到电子计划单失败，请重新添加！");
			}
			//已存在商品，更新商品数量
			if (planProductId == 1L) {
				return this.addResult("updateFlag", "true");
			}
			return this.addResult();
			//返回商品信息给前端
//			PlanningScheduleProduct hasSkuProduct = planningScheduleProductService.getHasGoodSku(planProductId);
//			return this.addResult(new String[]{"updateFlag", "planningScheduleProduct"}, new Object[]{"false", hasSkuProduct});
		}
		catch (Exception e) {
			LOGGER.error("商品添加到计划单异常：", e.toString());
			return this.addError("药品添加到电子计划单失败，请重新添加！");
		}
	}
}
