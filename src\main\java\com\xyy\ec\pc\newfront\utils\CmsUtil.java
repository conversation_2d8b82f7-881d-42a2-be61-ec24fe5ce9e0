package com.xyy.ec.pc.newfront.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

public class CmsUtil {

    public static boolean isCmsAdmin() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String dessert = request.getHeader("dessert");
        Cookie[] cookies = request.getCookies();
        boolean hasToken = cookies != null && Arrays.stream(cookies).anyMatch(cookie -> cookie.getName().equals("xyy_token"));
        // 如果存在 xyy_token 就不认为是 cms admin
        return StringUtils.isNotBlank(dessert) && !hasToken;
    }
}
