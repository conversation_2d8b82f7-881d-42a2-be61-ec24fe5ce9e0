package com.xyy.ec.pc.recommend.vo;

import com.xyy.ec.product.business.dto.LimitFullDiscountActInfo;
import com.xyy.ec.product.business.ecp.csufillattr.dto.LevelPriceDTO;
import com.xyy.ec.product.business.ecp.csufillattr.dto.SkuPriceRangeBusinessDTO;
import com.xyy.ec.product.business.ecp.csutag.dto.ProductActivityTagDTO;
import com.xyy.ec.product.business.ecp.csutag.dto.TagDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 搜索商品基类。copy from {@link com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO} 。
 */

@Getter
@Setter
public class PcRecommendBaseProductVO implements Serializable {

    /**
     * 商品id
     */
    private Long id;

    /**
     * 原品ID
     */
    private Long pid;

    /**
     * 商品条码
     */
    private String code;

    /**
     * 商品编码
     */
    private String barcode;

    /**
     * 域编码
     */
    private String branchCode;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 商家名称
     */
    private String companyName;

    /**
     * 是否易碎品（0：否；1：是）
     */
    private Integer isFragileGoods;

    /**
     * 商品推荐分类
     */
    private Long categoryId;

    /**
     * 展示名称
     * 根据产品需求拼接的商品名称
     */
    private String showName;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 委托生产厂家
     */
    private String entrustedManufacturer;

    /**
     * 上市许可持有人
     */
    private String marketAuthor;

    /**
     * 规格
     */
    private String spec;

    /**
     * 价格前缀
     */
    private String pricePrefix;

    /**
     * 状态:1-销售中，2-已售罄，4-下架，6-待上架
     */
    private Integer status;

    /**
     * 库存状态:1-销售中，2-已售罄，3-特惠中，4-待上架（下架），5-秒杀
     */
    private Integer inventoryStatus;

    /**
     * 是否控销商品
     */
    private Integer isControl;

    /**
     * 1-统一价格，2-价格区间
     */
    private Integer priceType;

    /**
     * 商品主图
     */
    private String imageUrl;

    /**
     * 商品标签URL /精选或必买
     */
    private String markerUrl;

    /**
     * 建议零售价
     */
    private BigDecimal suggestPrice;

    /**
     * 毛利率
     */
    private String grossMargin;

    /**
     * 中包装
     **/
    private String mediumPackage;

    /**
     * 控销零售价
     */
    private BigDecimal uniformPrice;

    /**
     * 中包装数量（默认为1）
     */
    private Integer mediumPackageNum;

    /**
     * 中包装文案
     */
    private String mediumPackageTitle;

    /**
     * 代理（0：非独家，1：独家）
     */
    private Integer agent;

    /**
     * 是否可拆零（0:不可拆零；1:可拆零）
     */
    private Integer isSplit;

    /**
     * 库存
     */
    private Integer availableQty;

    /**
     * 活动总剩余数量
     */
    private Integer actTotalSurplusQty;

    /**
     * 收藏标记(1:收藏；2:取消收藏)
     */
    private Integer favoriteStatus;

    /**
     * 价格区间
     */
    private List<SkuPriceRangeBusinessDTO> skuPriceRangeList;

    /**
     * 是否可购买
     */
    private Boolean isPurchase;

    /**
     * 享礼标记(商品名称前的标记)
     */
    private Boolean gift;

    /**
     * 协议签署状态(0-未签署,1-已签署)
     */
    private Integer signStatus;

    /**
     * 是否OEM协议(此处设置为String类型是因为web页面boolean类型不支持判断是否存在,而为了兼容之前没有OEM需求的情况又需要判断)
     */
    private String isOEM;

    /**
     * 是否可用医保：0-否，1-是
     */
    private Integer isUsableMedicalStr;

    /**
     * 是否可用医保
     */
    private Boolean isUsableMedical;

    /**
     * 是否可拆零文案
     */
    private String isSplitTitle;

    /**
     * 近效期
     */
    private String nearEffect;

    /**
     * 近效期标识(1:临期，2:近效)
     */
    private Integer nearEffectiveFlag;

    /**
     * 是否显示806标签
     */
    private Boolean isShow806;

    /**
     * 购物车数量
     */
    private Integer cartProductNum;

    /**
     * 是否符合协议标准展示价格,1:符合0:不符合
     */
    private Integer showAgree;

    /**
     * 促销开始时间
     */
    private Date promotionStartTime;

    /**
     * 促销结束时间
     */
    private Date promotionEndTime;

    /**
     * 是否显示
     */
    private Integer promotionCountDown;

    /**
     * 限购商品总数量
     */
    private Integer promotionTotalQty;

    /**
     * 保质期（如 12个月）
     */
    private String shelfLife;

    /**
     * 购买次数 为null 或者 "" 不显示
     */
    private String buyedCountStr;

    /**
     * 是否第三方厂家（0：否；1：是）
     */
    private Integer isThirdCompany;

    /**
     * 远效期
     */
    private String farEffect;

    /**
     * 30天销量
     */
    private Long thirtyDaysAmount;

    /**
     * 1:不是髙毛，2:是髙毛
     */
    private Integer highGross;

    /**
     * 协议状态(0-未生效,1-生效中,2-已失效)
     */
    private Integer agreementEffective;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 商品单位
     */
    private String productUnit;

    /**
     * 是否控销协议商品（0不是控销协议商品，1是控销协议商品）
     */
    private Integer isControlAgreement;

    /**
     * 0:不是髙毛拼团，1:是髙毛拼团
     */
    private Integer highGrossGroupBuying;

    /**
     * 主标准库ID
     */
    private String masterStandardProductId;

    /**
     * 是否虚拟供应商
     */
    private Boolean isVirtualSupplier;

    /**
     * 阶梯价信息  没有阶梯价时为null
     */
    private LevelPriceDTO levelPriceDTO;

    /**
     * 是否支持加入购物车
     */
    private Boolean canAddToCart;

    /**
     * 是否支持整单包邮
     */
    private Boolean freeShippingFlag;

    /**
     * 整单包邮文案
     */
    private String freeShippingText;

    /**
     * 控销价格展示文案
     */
    private String controlTitle;

    /**
     * 控销注释
     */
    private String controlNotes;

    /**
     * 控销购买按钮
     */
    private String controlPurchaseButton;

    /**
     * 控销类型 com.xyy.ec.product.business.ecp.csufillattr.enums.ControlSalePurchaseEnum
     */
    private Integer controlType;

    /**
     * 活动id
     */
    private Long productActivityId;

    /**
     * 活动类型
     */
    private Integer productActivityType;

    /**
     * 对比价格
     */
    private BigDecimal retailPrice;

    /**
     * 药帮忙价
     */
    private BigDecimal fob;

    /**
     * 标签集合
     */
    private List<TagDTO> tagList;

    /**
     * 快递标签集合
     */
    private List<TagDTO> expressList;

    /**
     * 是否首推，0非，1是
     */
    private Integer firstChoose;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 319添加的标签字段
     */
    private ProductActivityTagDTO activityTag;

    /**
     * 限时满减DTO
     */
    private LimitFullDiscountActInfo limitFullDiscountActInfo;

    // Setters && Getters
    /**
     * 公斤价
     */
    private String unitPrice;
}
