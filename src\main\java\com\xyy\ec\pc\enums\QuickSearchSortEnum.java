package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;

public enum QuickSearchSortEnum {

    SORT_FOB(0, "价格排序"),
    SORT_DISCOUNT(1, "相似度排序"),
    ;

    private int code;
    private String name;

    QuickSearchSortEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private static Map<Integer, QuickSearchSortEnum> enumMaps = new HashMap<>();
    public static Map<Integer, String> maps = new HashMap<>();

    static {
        for (QuickSearchSortEnum e : QuickSearchSortEnum.values()) {
            enumMaps.put(e.getCode(), e);
            maps.put(e.getCode(), e.getName());
        }
    }

    public static String get(int code) {
        return maps.get(code);
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
