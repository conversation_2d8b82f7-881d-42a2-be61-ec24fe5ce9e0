package com.xyy.ec.pc.service;

import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Principal;
import com.xyy.ec.pc.util.Verifier;

/**
 * 身份验证接口
 * 
 * 
 * @version 1.0,2011-2-17
 */
public interface IdentityValidator {

    /**
     * 判断是否是访问者
     * 
     * 
     * @return true 表示是访问者 false 表示不是访问者
     */
    public boolean isVisited();

    /**
     * 得到当前访问者用户名
     * 
     * 
     * @return 当前访问者用户名 没有用户返回null
     */
    public String currentVisitor();

    /**
     * 判断用户是否登录
     * 
     * 
     * @return true 表示是已登录 false 表示没有登录
     */
    public boolean isLogined() throws Exception;

    /**
     * 得到当前用户（选择店铺之后才有值，没有选择店铺返回null,需要重新登录）
     * 
     * 
     * @return 当前用户 没有用户返回null
     */
    public Principal currentPrincipal() throws Exception;


    /**
     * 宽松异常检查
     */
    Principal currentPrincipalEaseEx();

    /**
     * 只要登录就有值，不关心有没有选择店铺
     * @return
     * @throws Exception
     */
    public Principal currentAccountPrincipal() throws Exception;

    /**
     * 验证登录是否成功
     * 
     *
     * @param verifier
     * @return 返回登录后的认证主体
     * @throws Exception 
     */
    public Principal login(Verifier verifier) throws  Exception;

    /**
     * 退出登录状态
     * 
     * 
     */
    public void logout();

    /**
     * 设置验证提供者，用于获得用户信息和登录验证
     * 
     * 
     */
    public void setAuthenticationProvider(AuthenticationProvider authenticationProvider);

    /**
     * 得到验证提供者，用于获得用户信息和登录验证
     * 
     * 
     * @return 验证提供者 *@see com.xhbs.validate.AuthenticationProvider
     */
    public AuthenticationProvider getAuthenticationProvider();

    /**
     * 设置merchantId
     * @param merchantId
     * @return
     */
    MerchantPrincipal setPrincipalMerchant(Long merchantId,Long accountId) throws Exception;

    /**
     * 清除响应cookie
     */
    void newLogout();
}
