package com.xyy.ec.pc.controller;

import com.xyy.ec.merchant.bussiness.dto.LoginAgreementDto;
import com.xyy.ec.merchant.bussiness.dto.LoginAgreementInfoDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.params.LoginAgreementLogParam;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Principal;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.model.AddLoginAgreementLogDto;
import com.xyy.ec.pc.remote.LoginAgreementBussinessRemoteService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.ipip.IPUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/3
 * @description 登录勾选协议
 */
@Slf4j
@Controller
@RequestMapping("/loginAgreement")
public class LoginAgreementController extends BaseController {

    @Autowired
    private LoginAgreementBussinessRemoteService loginAgreementBussinessRemoteService;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Autowired
    private AppProperties appProperties;
    @PostMapping("/addLog")
    @ResponseBody
    public Object addLog(@RequestBody AddLoginAgreementLogDto param, HttpServletRequest request) {
        try {
            if (CollectionUtils.isEmpty(param.getAgreements()) || ObjectUtils.isEmpty(param.getCheckTime())){
                return addError("params is empty");
            }
            if (param.getAgreements().size() > 5){
                return addError("params size is too large");
            }
            Date date = new Date();
            MerchantBussinessDto merchant = (MerchantBussinessDto)xyyIndentityValidator.currentPrincipal();
            String ip = IPUtils.getClientIP(request);
            Date checkTime = param.getCheckTime();
            String mobile = param.getMobile();
            Integer operateType = param.getOperateType();
            List<LoginAgreementLogParam> agreementLogParamList = param.getAgreements().stream().map(item -> {
                item.setClientType("pc");
                item.setAccountId(merchant != null?merchant.getAccountId():0);
                item.setLoginTime(date);
                item.setIp(ip);
                item.setOperateType(operateType);
                item.setCheckTime(checkTime);
                item.setMobile(mobile);
                return item;
            }).collect(Collectors.toList());
            loginAgreementBussinessRemoteService.insertLoginAgreementLog(agreementLogParamList);
            return this.addResult("保存勾选协议日志成功");
        }catch (Exception e){
            log.error("保存勾选协议日志失败",e);
            return this.addError("保存勾选协议日志失败");
        }
    }

    @GetMapping("/getLoginAgreement")
    @ResponseBody
    public Object getLoginAgreement(@RequestParam(value = "lang",required = false) String lang) {
        try{
            LoginAgreementInfoDto pc = loginAgreementBussinessRemoteService.getLoginAgreement("pc", lang);
            if (pc == null){
                return this.addError("获取登录协议失败");
            }else {
                LoginAgreementDto userServiceAgreement = pc.getUserServiceAgreement();
                LoginAgreementDto privacyPolicy = pc.getPrivacyPolicy();

                if (userServiceAgreement != null){
                    userServiceAgreement.setUrl(appProperties.getBasePathUrl() + userServiceAgreement.getUrl());
                }else{
                    userServiceAgreement = new LoginAgreementDto();
                    userServiceAgreement.setUrl(appProperties.getBasePathUrl() + "/login/agreement.htm");
                }
                if (privacyPolicy != null){
                    privacyPolicy.setUrl(appProperties.getBasePathUrl() + privacyPolicy.getUrl());
                }else {
                    privacyPolicy = new LoginAgreementDto();
                    privacyPolicy.setUrl(appProperties.getBasePathUrl() + "/helpCenter/privacy.htm");
                }

            }
            return this.addResult("data",pc);
        }catch (Exception e){
            log.error("获取登录协议失败",e);
            return this.addError("获取登录协议失败");
        }
    }
}
