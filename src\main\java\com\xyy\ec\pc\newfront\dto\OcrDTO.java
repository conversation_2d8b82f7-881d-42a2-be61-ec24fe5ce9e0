package com.xyy.ec.pc.newfront.dto;

import com.xyy.ec.pc.newfront.enums.TypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OcrDTO implements Serializable {


    /**
     * 类型
     */
    private TypeEnum type;
    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * UUID
     */
    private String uuid;
}