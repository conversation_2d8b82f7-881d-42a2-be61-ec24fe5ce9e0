package com.xyy.ec.pc.cms.utils;

import com.google.common.collect.Sets;

import java.util.Objects;
import java.util.Set;

public class LayoutProductUtils {

    /**
     * 商品控销的类型集合
     */
    private static final Set<Integer> PRODUCT_CONSOLE_TYPE_SET = Sets.newHashSet(2, 3, 4, 6, 7, 8, 9, 10);

    /**
     * 是否商品控销
     *
     * @param controlType
     * @return
     */
    public static boolean isProductControl(Integer controlType) {
        return PRODUCT_CONSOLE_TYPE_SET.contains(controlType);
    }

    /**
     * 是否不是商品控销
     *
     * @param controlType
     * @return
     */
    public static boolean isNotProductControl(Integer controlType) {
        return !isProductControl(controlType);
    }

    /**
     * 商品是否是销售中
     *
     * @param productStatus
     * @return
     */
    public static boolean isOnSale(Integer productStatus) {
        return Objects.equals(productStatus, 1);
    }

    /**
     * 商品是否不是销售中
     *
     * @param productStatus
     * @return
     */
    public static boolean isNotOnSale(Integer productStatus) {
        return !isOnSale(productStatus);
    }
}
