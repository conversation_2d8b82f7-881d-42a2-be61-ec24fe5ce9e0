package com.xyy.ec.pc.controller.vo;

import lombok.Data;

import java.util.List;

/**
 * 匹配基础参数
 * <AUTHOR>
 */
@Data
public class MatchBaseParamVO {

    /**
     * 店铺名称集合
     */
    private List<String> shopCodes;

    /**
     * 是否可买
     */
    private Boolean canBuyIs;

    /**
     * 可买/不可买/缺少必填字段
     */
    private String canBuyStr;

    //近效期，-1非近效期 ,2近效期
    private Integer nearEffectiveFlag;
    /**
     * 是否有优惠券
     */
    private Boolean hasCouponIs;

    /**
     * 是否买过的店
     */
    private Boolean buyShopIs;

}

