package com.xyy.ec.pc.constants;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponBaseDto;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;

/**
 * <AUTHOR>
 */
public class ReceiveCenterCouponSortConstants {
    private enum CouponMakeFromPriority{
        BIG_PLAFROM(2, 1),
        POP(3, 3),
        SHOP(4, 2),
        UNKNOWN(-1, 100000);
        private static ImmutableMap<Integer, CouponMakeFromPriority> _map = ImmutableMap.copyOf(Maps.uniqueIndex(Lists.newArrayList(CouponMakeFromPriority.values()), CouponMakeFromPriority::getType));


        private Integer type;
        private Integer priority;

        public Integer getType() {
            return type;
        }

        public Integer getPriority() {
            return priority;
        }

        CouponMakeFromPriority(Integer type, Integer priority) {
            this.type = type;
            this.priority = priority;
        }
        public static CouponMakeFromPriority of(Integer makeFrom){
            return null == makeFrom ? UNKNOWN : _map.getOrDefault(makeFrom, UNKNOWN);
        }
    }


    private enum CouponTypePriority{
        PRODUCT(2, 2),
        OVERLYING(6, 1),
        UNKNOWN(-1, 100000);


        private static ImmutableMap<Integer, CouponTypePriority> _map = ImmutableMap.copyOf(Maps.uniqueIndex(Lists.newArrayList(CouponTypePriority.values()), CouponTypePriority::getType));

        private Integer type;
        private Integer priority;

        CouponTypePriority(Integer type, Integer priority) {
            this.type = type;
            this.priority = priority;
        }

        public Integer getType() {
            return type;
        }

        public Integer getPriority() {
            return priority;
        }
        public static CouponTypePriority of(Integer type){
            return null == type ? UNKNOWN : _map.getOrDefault(type, UNKNOWN);
        }

    }



    public static final Comparator<CouponBaseDto> DISCOUNT_DESC_SORT = (o1, o2) -> {
        int o1Discount = getDiscount(o1);
        int o2Discount = getDiscount(o2);
        if (o2Discount != o1Discount){
            return o2Discount - o1Discount;
        }
        int minMoneyEnableCompare = o1.getMinMoneyToEnable().compareTo(o2.getMinMoneyToEnable());
        if (0 != minMoneyEnableCompare){
            return minMoneyEnableCompare;
        }
        if (null == o1.getExpireDate()){
            return 1;
        }
        if (null == o2.getExpireDate()){
            return -1;
        }
        int expireCompare = o1.getExpireDate().compareTo(o2.getExpireDate());
        if (0 != expireCompare){
            return expireCompare;
        }

        if (null == o1.getCreateTime()){
            return 1;
        }
        if (null == o2.getCreateTime()){
            return -1;
        }
        return o2.getCreateTime().compareTo(o1.getCreateTime());
    };


    public static final Comparator<CouponBaseDto> COUPON_MAKE_FROM_SORT = Comparator.comparingInt(o -> CouponMakeFromPriority.of(o.getMakeFrom()).getPriority());

    public static final Comparator<CouponBaseDto> COUPON_TYPE_SORT = Comparator.comparingInt(o -> CouponTypePriority.of(o.getVoucherType()).getPriority());


    public static int getDiscount(CouponBaseDto baseDto){
        if (null == baseDto){
            return 0;
        }
        if (null != baseDto.getDiscount() && baseDto.getDiscount().compareTo(BigDecimal.ZERO)>0){
            return (int)(baseDto.getDiscount().doubleValue()*1000);
        }
        if (null != baseDto.getMinMoneyToEnable() && null != baseDto.getMoneyInVoucher()){
            return (int)(baseDto.getMoneyInVoucher().divide(baseDto.getMinMoneyToEnable().compareTo(BigDecimal.ZERO)>0?baseDto.getMinMoneyToEnable():BigDecimal.ONE, 5, RoundingMode.DOWN).doubleValue()*10000);
        }
        return 0;
    }

}
