package com.xyy.ec.api.busi.place.register;

import com.alibaba.fastjson.JSONObject;
import com.ec.web.api.WebApiResp;
import com.xyy.ec.api.busi.base.WebBaseController;
import com.xyy.ec.api.rpc.user.UserRpcService;
import com.xyy.ec.merchant.bussiness.dto.PoiRequestBussinessDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Objects;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/15 09:57
 * @Description:
 */
@RestController
@RequestMapping("/pc/register")
@Slf4j
public class RegisterController extends WebBaseController {
    @Autowired
    UserRpcService userRpcService;

    @RequestMapping("/search")
    public WebApiResp search(@Valid PoiReq poiReq) {
        PoiRequestBussinessDto poiDto = new PoiRequestBussinessDto();
        BeanUtils.copyProperties(poiReq,poiDto);
        // 为了防止爬数据，限制每页显示的条数的上限。
        if (Objects.nonNull(poiReq) && (Objects.isNull(poiReq.getLimit()) || poiReq.getLimit() <= 0 || poiReq.getLimit() >= 50)) {
            poiReq.setLimit(30);
        }
        return new WebApiResp(userRpcService.findPageMerchantInfoList(poiDto,poiReq.getOffset(),poiReq.getLimit()));
    }
    @RequestMapping("/checkname")
    public WebApiResp checkname(@Valid CheckNameReq checkNameReq) {
        return new WebApiResp(userRpcService.checkPoiRelationAndExist(checkNameReq.getPoiId(),checkNameReq.getShopName()));
    }
}
