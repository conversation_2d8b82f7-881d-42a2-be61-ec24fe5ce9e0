<@header_data headerData>
<script type="text/javascript" src="/static/js/jquery.placeholder.min.js"></script>
<div class="headerT">
    <!--导航-->
    <div class="sui-navbar">
        <div class="navbar-inner">
            <ul class="sui-nav nologin" id="loginUserInfo">
                <li><a href="http://upload.ybm100.com/ybm/pc/activitys/html/药帮忙.url" class="cjzmkj"><img src="/static/images/zmkj.png"  class="zmkj">桌面快捷</a></li>
                <li><a href="javascript:void(0)" onclick="addCookie()" class="addCookie-btn" rel='sidebar'>收藏本站</a></li>
                <li><a href="javascript:void(0);" class="userno">欢迎来到药帮忙！</a></li>
                <li><a href="#" >请 <span class="speano"> 登录</span></a></li>
                <li><a href="#" >注册有礼</a></li>
            </ul>
            <ul class="sui-nav pull-right">
                <li><a href="/">首页</a></li>
                 <li class="xiexian">/</li>
                <li><a href="/merchant/center/order/index.htm">我的订单</a></li>
                <li class="xiexian">/</li>
                <li><a href="/merchant/center/index.htm">用户中心</a></li>
                <li class="xiexian">/</li>
                <li><a href="javascript:void(0);" class="userno">400-0505-111</a></li>
                <li class="xiexian">/</li>
                <li><a href="javascript:callKf('','${merchant.id}');">在线客服</a></li>
                <li class="xiexian">/</li>
                <li><a href="/helpCenter/about.htm">帮助中心</a></li>
                <li class="xiexian">/</li>
                <li class="shoujima"><a href="/helpCenter/about.htm"><img src="/static/images/shouji.png"  class="shoujiico">手机药帮忙 <img src="/static/images/top-erweima.png" class="posimg"  /> </a></li>
            </ul>
        </div>
    </div>

</div>
<!--搜索栏-->
<div class="searcherBox" id="searchBox">
    <div class="col1 fl">
        <a href="/"><img src="/static/images/logo_0516.png" alt="药帮忙" /></a>
    </div>

    <div class="col2 fl">
        <div class="search"  >
            <div class="inputbox fl">
                <input type="text" id="search" placeholder="药品名称/厂家名称/助记码" value="${keywordSearch}"/>
            </div>
            <div class="ss fl">
                <a href="#" onclick="searchProduct();">搜索</a>
            </div>
            <!--搜索下拉弹窗-->
            <ul class="searchUl" id="searchUl"></ul>
        </div>
        <div class="rmtag" id="hotWordDiv">
        <#--<span>热门搜索：</span>-->
            <#list model.listHostSearch as hot>
                <span>
                    <#if hot.url?? && hot.url!=''>
                        <a href="/${hot.url}" target="_blank">${hot.keyword}</a>
                    <#else>
                        <a href="/search/product_n.htm?keyword=${hot.keyword}" target="_blank">${hot.keyword}</a>
                    </#if>
                </span>
            </#list>
        </div>
    </div>
    <div class="col-new3 fr">
        <div class="youitem">
            <div class="imgwarp"><img class="youitem-img" src="/static/images/youitem1.png"></div>
            <div class="imgtext">带票销售</div>
        </div>
        <div class="youitem">
            <div class="imgwarp"><img class="youitem-img" src="/static/images/youitem2.png"></div>
            <div class="imgtext">正品行货</div>
        </div>
        <div class="youitem">
            <div class="imgwarp"><img class="youitem-img" src="/static/images/youitem3.png"></div>
            <div class="imgtext">专业服务</div>
        </div>
        <div class="youitem spejg">
            <div class="imgwarp"><img class="youitem-img" src="/static/images/youitem4.png"></div>
            <div class="imgtext">轻松采购</div>
        </div>
    </div>
</div>

<!--商品分类栏-->
<div class="newwarp">
    <div class="goodsmain">
        <div class="leftbox fl">
            <div class="fltitle index-noshow-fenlei">
                <i class="sui-icon icon-pc-list"></i>全部分类
            </div>
        </div>
        <div class="rightbox fl">
            <ul class="topnav">
                <#if (.now?date gt "2017-12-01 00:00:00"?date("yyyy-MM-dd HH:mm:ss"))>
                    <li class="index-noshow">
                        <a href="/">首页</a>
                    </li>
                    <li>
                        <a href="/search/product_n.htm?all=all" class="${styleClassa}" target="_blank">全部药品</a>
                    </li>
                    <li>
                        <a href="/activity/manyProfit.htm?activityId=146" class="${styleClass11}" target="_blank">独家高毛<img src="/static/images/hot.png" class="hot" ></a>
                    </li>
                    <li>
                        <a href="/activity/events.htm?moduleId=318&activityId=52&activityTitle=TOP100&limit=200" class="${styleClass6}" target="_blank">TOP100</a>
                    </li>
                    <li>
                    <#--<a href="/activity/daySale.htm" class="${styleClass3}" target="_blank">每日特价</a>-->
                        <a href="/activity/initActivity.htm?id=95&moduleCategoryId=149&version=1&activityType=events-20170901-xinpin" class="${styleClass3}" target="_blank">新品上架</a>
                    </li>
                    <li>
                        <a href="/activity/initActivity.htm?id=70&moduleCategoryId=149&version=1&activityType=events-20170714-zszq" class="${styleClass8}" target="_blank">诊所专区</a>
                    </li>
                <#elseif (.now?date gt "2017-11-11 00:00:00"?date("yyyy-MM-dd HH:mm:ss"))>
                    <li class="index-noshow">
                        <a href="/">首页</a>
                    </li>
                    <li>
                        <a href="/search/product_n.htm?all=all" class="${styleClassa}" target="_blank">全部药品</a>
                    </li>
                    <li>
                        <a href="/activity/initActivity.htm?id=145&activityType=events-20171101-index&version=1" class="${styleClass12}" target="_blank">双11专区<img src="/static/images/hot.png" class="hot" ></a>
                    </li>
                    <li>
                        <a href="/activity/events.htm?moduleId=318&activityId=52&activityTitle=TOP100&limit=200" class="${styleClass6}" target="_blank">TOP100</a>
                    </li>
                    <li>
                    <#--<a href="/activity/daySale.htm" class="${styleClass3}" target="_blank">每日特价</a>-->
                        <a href="/activity/initActivity.htm?id=95&moduleCategoryId=149&version=1&activityType=events-20170901-xinpin" class="${styleClass3}" target="_blank">新品上架</a>
                    </li>
                    <li>
                        <a href="/activity/initActivity.htm?id=70&moduleCategoryId=149&version=1&activityType=events-20170714-zszq" class="${styleClass8}" target="_blank">诊所专区</a>
                    </li>
                <#else>
                    <li class="index-noshow">
                        <a href="/">首页</a>
                    </li>
                    <li>
                        <a href="/search/product_n.htm?all=all" class="${styleClassa}" target="_blank">全部药品</a>
                    </li>
                    <li>
                        <a href="/activity/manyProfit.htm?activityId=146" class="${styleClass11}" target="_blank">独家高毛<img src="/static/images/hot.png" class="hot" ></a>
                    </li>
                    <li>
                        <a href="/activity/events.htm?moduleId=318&activityId=52&activityTitle=TOP100&limit=200" class="${styleClass6}" target="_blank">TOP100</a>
                    </li>
                    <li>
                    <#--<a href="/activity/daySale.htm" class="${styleClass3}" target="_blank">每日特价</a>-->
                        <a href="/activity/initActivity.htm?id=95&moduleCategoryId=149&version=1&activityType=events-20170901-xinpin" class="${styleClass3}" target="_blank">新品上架</a>
                    </li>
                    <li>
                        <a href="/activity/initActivity.htm?id=70&moduleCategoryId=149&version=1&activityType=events-20170714-zszq" class="${styleClass8}" target="_blank">诊所专区</a>
                    </li>
                </#if>

            </ul>

        </div>
        <style>

            .xfubox .flbox{
                height:auto!important;
            }
            .hang-col1{
                margin-right: 10px!important;

            }
        </style>
        <!--悬浮类目-->
        <div class="xfubox" id="categoryTree">
            <#if (categorys?? && categorys?size >0)>
                <div class="flbox index-spe">
                    <ul class="newul">
                        <#list categorys as categoryTreeItem>
                            <li>
                                <a href="/search/skuInfoByCategory.htm?categoryFirstId=${categoryTreeItem.id}" class="lia1" target="_blank">
                                    <#if categoryTreeItem.icon?? && categoryTreeItem.icon!="">
                                        <img src="${categoryTreeItem.icon}" style="width:21px;height:21px;;float: left;margin-top: 16px;margin-right: 10px;" />
                                    <#else>
                                        <i class="<#if categoryTreeItem.id == 1>
                                        icon iconfont icon-zhongxichengyao
                                        <#elseif  categoryTreeItem.id == 225>
                                        iconfont icon-zhongxichengyao
                                        <#elseif  categoryTreeItem.id == 3>
                                        icon iconfont1 icon-zhongyao
                                        <#elseif  categoryTreeItem.id == 65>
                                        icon iconfont icon-jishengyongpin
                                        <#elseif  categoryTreeItem.id == 66>
                                        icon iconfont icon-xiaoduyongpin
                                        <#elseif  categoryTreeItem.id == 67>
                                        icon iconfont icon-gerenhuli
                                        <#elseif  categoryTreeItem.id == 68>
                                        icon iconfont icon-shipinbaojian
                                        <#elseif  categoryTreeItem.id == 69>
                                        icon iconfont icon-zhusheyongpin
                                        <#else>
                                        </#if>"></i>
                                    </#if>
                                    <span>${categoryTreeItem.name}</span>
                                </a>
                            </li>
                        </#list>
                    </ul>
                </div>
                <!--二级类目弹窗-->
                <div class="leimu-TC">
                    <ul class="two-box-ul">
                        <#list categorys as categoryTreeItemCon>
                            <#if (categoryTreeItemCon.children?? && categoryTreeItemCon.children?size >0)>
                                <!--${categoryTreeItemCon.name}-->
                                <#assign list_index="1" />
                                <li class="warp-li">
                                    <div class="hangbox">
                                        <#list categoryTreeItemCon.children as categoryTreeItemConChild>
                                            <div class="hang-col1"  style="float:<#if (list_index?number) % 2 ==0><#elseif (list_index?number) % 3 ==0>right<#else>left</#if>">
                                                <div class="com-title"><a href="/search/skuInfoByCategory.htm?categorySecondId=${categoryTreeItemConChild.id}" target="_blank">${categoryTreeItemConChild.name}</a></div>
                                                <#if (categoryTreeItemConChild.children?? && categoryTreeItemConChild.children?size >0)>
                                                    <div class="com-info">
                                                        <#list categoryTreeItemConChild.children as categoryTreeItemConChildCon>
                                                            <a href="/search/skuInfoByCategory.htm?categoryThirdId=${categoryTreeItemConChildCon.id}" target="_blank">${categoryTreeItemConChildCon.name}</a>
                                                        </#list>
                                                    </div>
                                                </#if>
                                            </div>
                                            <#if (list_index?number) % 3 ==0><div style="clear:both;"></div><div style="float:left;margin:20px;"></div></#if>
                                            <#assign list_index="${(list_index?number +1)}"/>
                                        </#list>
                                    </div>
                                </li>
                            <#else>
                                <li class="warp-li">
                                    <div class="hangbox">
                                    </div>
                                </li>
                            </#if>
                        </#list>
                    </ul>
                </div>
            </#if>
        </div>
    </div>
</div>



<div class="kuanjie">
    <!--导航-->
    <div class="abs-warp">
        <!--采购单-->
        <a href="/merchant/center/cart/index.htm" class="tongyong cgdbox ">
            <div class="r-topbox">
                <i class="icon iconfont icon-caigoudan"></i>

            </div>
            <div class="r-footbox cycle2">采购单</div>
            <span id="rigthCartNum" class="topp"></span>
        </a>

        <!--收藏-->
        <a href="/merchant/center/collection/findAttention.htm" class="tongyong jdbox">
            <div class="r-topbox"><i class="icon iconfont icon-shoucang"></i></div>
            <!--<div class="r-footbox">收藏</div>-->
            <div class="zuotc">收藏</div>
        </a>

        <!--客服-->
        <a href="javaScript:callKf('','${merchant.id}');" class="tongyong jdbox">
            <div class="r-topbox"><i class="icon iconfont icon-kefu"></i></div>
            <!--<div class="r-footbox">客服</div>-->
            <div class="zuotc">客服</div>
        </a>

        <!--意见反馈-->
        <#--  <a href="/feedback/indexFeedback.html" class="tongyong jdbox">
            <div class="r-topbox"><i class="icon iconfont icon-yijianfankui"></i></div>
            <div class="zuotc">意见反馈</div>
        </a>  -->

        <!--心愿单-->
        <#--<a href="/merchant/center/wish/index.htm?tab=1" class="tongyong jdbox">-->
            <#--<div class="r-topbox"><i class="icon iconfont icon-xinyuandan"></i></div>-->
            <#--<!--<div class="r-footbox">心愿单</div>&ndash;&gt;-->
            <#--<!--<div class="you-er"><img src="img/xinyuandan-new.png" alt="" /></div>&ndash;&gt;-->
            <#--<div class="zuotc">心愿单</div>-->
        <#--</a>-->

        <div class="dingwei">
            <!--APP-->
            <a href="#" class="tongyong speapp jdbox app">
                <div class="r-topbox"><i class="icon iconfont icon-shouji"></i></div>
            </a>

            <!--top-->
            <a href="javascript:void(0);" id="toTop" class="tongyong jdbox">
                <div class="r-topbox"><i class="sui-icon sui-icon icon-touch-chevron-up"></i></div>
                <div class="zuotc">顶部</div>
            </a>
        </div>

    </div>
    <!--二维码动画-->
    <div class="erm-abs">
        <img src="/static/images/xzma.png" />
    </div>

</div>



</div>
</@header_data>



<script type="text/javascript">
    $(document).ready(function() {
        $.ajax({
            url: "/header_data.json",
            type: "GET",
            async: true,
            cache: false,
            dataType: "json",
            success: function(data) {
                if(data.header_data.merchant != null){
                    $("#loginUserInfo").removeClass("sui-nav nologin");
                    $("#loginUserInfo").addClass("sui-nav");
                    var html ='<li><a href="http://upload.ybm100.com/ybm/pc/activitys/html/药帮忙.url" class="cjzmkj"><img src="/static/images/zmkj.png"  class="zmkj">桌面快捷</a></li>'
                    //+'<li><a href="/">首页</a></li>'
                    +'<li><a href="javascript:void(0)" onclick="addCookie()" class="addCookie-btn" rel="sidebar">收藏本站</a></li>'
                    +'<li><a href="javascript:void(0);" class="user">'+data.header_data.merchant.realName+'！欢迎您！</a></li>'
                    +'<li><a href="/login/logout.htm" class="spea" rel="nofollow">退出</a></li>';
                    $("#loginUserInfo").html(html);
                    if(data.header_data.merchantCartCount>0){
                        $("#cartNumberLi").addClass("cycle");
                        $("#cartNumberLi").html(data.header_data.merchantCartCount);
                        $("#cartNumberDiv").addClass("topp");
                        $("#cartNumberDiv").html(data.header_data.merchantCartCount);
                        $("#rigthCartNum").addClass("topp");
                        $("#rigthCartNum").removeClass("noshow");
                        $("#rigthCartNum").addClass("cycle2");
                        $("#rigthCartNum").html(data.header_data.merchantCartCount);
                        $("#cartNumberDiv").removeClass("noshow");
                        $("#firstCartNumberDiv").removeClass("noshow");
                        $("#firstCartNumberDiv").addClass("cycle2");
                        $("#firstCartNumberDiv").addClass("topp");
                        $("#firstCartNumberDiv").html(data.header_data.merchantCartCount);
                    }else{
                        $("#cartNumberLi").html("");
                        $("#cartNumberDiv").html("");
                        $("#cartNumberDiv").text("");
                        $("#cartNumberDiv").addClass("noshow");
                        $("#rigthCartNum").removeClass("noshow");
                        $("#rigthCartNum").removeClass("topp");
                        $("#rigthCartNum").addClass("cycle2 noshow");
                        $("#rigthCartNum").html("");
                        $("#firstCartNumberDiv").html("");
                        $("#firstCartNumberDiv").removeClass("topp");
                    }

                }else{
                    var html = '<li><a href="http://upload.ybm100.com/ybm/pc/activitys/html/药帮忙.url" class="cjzmkj"><img src="/static/images/zmkj.png"  class="zmkj">桌面快捷</a></li>'
                    //+'<li><a href="/">首页</a></li>'
                    +'<li><a href="javascript:void(0)" onclick="addCookie()" class="addCookie-btn" rel="sidebar">收藏本站</a></li>'
                    +'<li><a href="javascript:void(0);" class="userno">欢迎来到药帮忙！</a></li>'
                    +'<li><a href="/login/login.htm" >请 <span class="speano"> 登录</span></a></li>'
                    +'<li><a href="/newstatic/#/register/index" >注册有礼</a></li>';
                    // +'<li><a href="/login/register.htm" >注册有礼</a></li>';
                    $("#loginUserInfo").html(html);
                    $("#cartNumberLi").removeClass("cycle");
                    $("#cartNumberLi").html("");
                    $("#cartNumberDiv").html("");
                    $("#cartNumberDiv").text("");
                    $("#cartNumberDiv").addClass("noshow");
                    $("#rigthCartNum").removeClass("topp");
                    $("#rigthCartNum").html("");
                    $("#firstCartNumberDiv").html("");
                }
            }
        });
        $.ajax({
            url: "/categoryTree?r=" + Math.random(),
            type: "POST",
            dataType: "html",
            traditional :true,
            data: {},
            success: function(result){
                $("#categoryTree").html(result);
                /*显示类目*/
                $(".goodsmain .fltitle,.xfubox .flbox").hover(function(){
                    $(".xfubox .flbox").css("display","block");
                },function(){
                    $(".xfubox .flbox").css("display","none");
                })
                $(".xfubox .flbox .newul,.leimu-TC").hover(function(){
                    $(".leimu-TC").css("display","block");
                    $(".xfubox .flbox").css("display","block");
                },function(){
                    $(".leimu-TC").css("display","none");
                    $(".xfubox .flbox").css("display","none");
                })
                /*切换显示*/
                $('.newul li').hover(function(){
                    $(".two-box-ul .warp-li").eq($(this).index()).addClass("show").siblings().removeClass('show');
                    $(this).addClass("cur").siblings().removeClass("cur");
                });
            }
        });
    })


    function searchProduct(){
        var name = $.trim($("#search").val());
        if(name == null || name ==""){
//		name="阿莫西林胶囊";
        }else{
            var pathname=window.location.pathname;
            if(pathname=="/"){
                window.open("/static/search/product_n.htm?keyword="+encodeURI(name),"_blank");
            }else{
                window.open("/static/search/product_n.htm?keyword="+encodeURI(name),"_top");
            }
        }


    }
    function loadProductNameByAutoComplete(name){
        url="/static/search/autoComplate.json?productName="+encodeURI(name);
        var html = "";
        if(name == null || name.trim() ==""){
        }else{
            name = name.trim();
            $.ajax({
                type : "GET",
                url: url,
                async:true,
                dataType: "json",
                success: function(data){
                    $.each(data.productNameList, function (index, entry) {
                        var  name = entry.commonName;
                        html += "<li><a href='/static/search/product_n.htm?keyword="+encodeURI(name)+"' target='_self'><span>"+entry.commonName+"</span></a></li>";
                    });
                    if(data.productNameList && data.productNameList.length>0){
                        $("#searchUl").html(html);
                        $('#searchUl').css("display","block");
                    }else{
                        $('#searchUl').css("display","none");
                    }
                }
            });
        }
    }

    var pressKeyInt = 0;
    /*搜索弹窗响应键盘事件*/
    $("#search").keyup(function (event) {
        var e = event || window.event;
        var k = e.keyCode || e.which;

        if(k == 38 || k == 37 || k == 39 || k == 40){
        }else{
            var name = $("#search").val();
            loadProductNameByAutoComplete(name);
        }
        $('#searchUl').css("display","block");
        switch (k) {
            case 38:
                pressKeyInt--;
                var tmp=$('#searchUl li').length-1;
                if (pressKeyInt <0) {
                    pressKeyInt =tmp;
                }
                $('#searchUl li').eq(pressKeyInt-1).addClass('active').siblings().removeClass('active');
                $("#search").val($('#searchUl li').eq(pressKeyInt-1).text());
                break;
            case 40:
                pressKeyInt++;
                var tmp=$('#searchUl li').length-1;
                if (pressKeyInt >tmp) {
                    pressKeyInt =0;
                }
                $('#searchUl li').eq(pressKeyInt-1).addClass('active').siblings().removeClass('active');
                $("#search").val($('#searchUl li').eq(pressKeyInt-1).text());
                break;
            case 13:
                searchProduct();
                console.log("回车");
                break;

        }
    });

    /*点击搜索下拉框事件*/
    $('#searchUl li').click(function(){
        $("#search").val($(this).text());
        $(".searchUl").hide();
    });


    /*隐藏搜索弹窗*/
    $("body").click(function (e) {
        if (!$(e.target).closest(".searchUl").length) {
            $(".searchUl").hide();
        }
    });

    /*显示类目*/
    $(".goodsmain .fltitle,.xfubox .flbox").hover(function(){
        $(".xfubox .flbox").css("display","block");
    },function(){
        $(".xfubox .flbox").css("display","none");
    })
    $(".xfubox .flbox .newul,.leimu-TC").hover(function(){
        $(".leimu-TC").css("display","block");
        $(".xfubox .flbox").css("display","block");
    },function(){
        $(".leimu-TC").css("display","none");
        $(".xfubox .flbox").css("display","none");
    })
    /*切换显示*/
    $('.newul li').hover(function(){
        $(".two-box-ul .warp-li").eq($(this).index()).addClass("show").siblings().removeClass('show');
        $(this).addClass("cur").siblings().removeClass("cur");
    });
    /*去掉分类高亮*/
    $(".xfubox").hover(function(){},function(){$(".newul li").removeClass("cur");});

    /*导航添加底部横线*/
    $('.topnav li').hover(function(){$(this).addClass("hovercur").siblings().removeClass("hovercur")},function(){$(this).removeClass("hovercur")});



    /*回到顶部*/
    $('#toTop').click(function() {
        $('html,body').animate({
            scrollTop: '0px'
        }, 'slow');
    });

    /*导航切换样式*/
    $('.topnav  li a').click(function(){
        $(this).addClass("cur").parent("li").siblings().find("a").removeClass("cur");
    });

    $('input, textarea').placeholder();

    $(".app,.erm-abs").hover(function(){
        $(".erm-abs").css("left","-108px");
    },function(){
        $(".erm-abs").css("left","0");
    });

    /*心愿单*/
    var cb=(function(){
        var xyd_timer,xyd_timeout=600;
        $(".xyd-pos-init,.xyd-pos-leftbox").hover(function(){
            if(!xyd_timer){
                $(".xyd-pos-leftbox").fadeIn();
            }else{
                xyd_timer = null;
            }
        },function(){
            xyd_timer=setTimeout(function(){
                $(".xyd-pos-leftbox").hide();
                xyd_timer = null;
            },xyd_timeout);
        });
    })();

    function addCookie(){
        var url = window.location;
        var title = document.title;
        if (document.all){
            window.external.addFavorite(url,title);
        } else if (window.sidebar){
            window.sidebar.addPanel(title, url, "");
        } else {
            alert("对不起，您的浏览器不支持此操作!\n请您使用菜单栏或Ctrl+D收藏本站。");
        }
    }

    function callKf(id,userId,orgId,isThirdCompany,merchantName){
        var url = '';
        if(url == ''){
            var path = isThirdCompany ? "/custom/getIMPackUrl?isThirdCompany="+isThirdCompany : "/custom/getIMPackUrl";
            $.ajax({
                url: path,
                type: "GET",
                async: false,
                cache: false,
                dataType: "json",
                success: function(data) {
                    if(data.status == "success"){
                        //url = data.data.IM_PACK_URL
                        if(isThirdCompany){ //pop客服入口
                            if(userId){
                                if(id){
                                    url = data.data.IM_PACK_URL+'&orderNo='+id+'&userid='+userId+'&merchantCode='+orgId+'&pop=1&ws=1&sc=1000&portalType=1&merchantName='+merchantName;
                                }else {
                                    url = data.data.IM_PACK_URL+'&userid='+userId+'&merchantCode='+orgId+'&pop=1&ws=1&sc=1000&portalType=1&merchantName='+merchantName;
                                }
                            }else {
                                window.location.href="/login/login.htm";
                            }
                        }else{//自营客服入口
                            if(userId){
                                if(id){
                                    url = data.data.IM_PACK_URL+'&orderNo='+id+'&userid='+userId+'&sc=1000&portalType=2';
                                }else {
                                    url = data.data.IM_PACK_URL+'&userid='+userId+'&sc=1000&portalType=1';
                                }
                            }else {
                                url = data.data.IM_PACK_URL+'&sc=1000&portalType=1';
                            }
                        }

                    }
                }
            });
        }
        if(url){
            if(isThirdCompany) { //pop客服入口
                window.open(url, '_blank', 'webcall', 'toolbar=no, status=no,scrollbars=0,resizable=0,menubar＝0,location=0,width=680,height=680');
            }else{
                window.open(url, 'webcall', 'toolbar=no, status=no,scrollbars=0,resizable=0,menubar＝0,location=0,width=680,height=680');
            }
        }else {
            alert("未连接到在线客服，请联系管理员");
        }
    }

</script>


      