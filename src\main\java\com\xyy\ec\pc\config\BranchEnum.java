package com.xyy.ec.pc.config;

import java.util.HashMap;
import java.util.Map;

public enum BranchEnum {

    ALL_COUNTRY("XS000000","全国"),
    HUBEI_COUNTRY("XS420000","湖北子公司"),
    HUNAN_COUNTRY("XS430000","湖南子公司"),
    ANHUI_COUNTRY("XS340000","安徽子公司"),
    ZHEJIANG_COUNTRY("XS330000","浙江子公司"),
    SHANDONG_COUNTRY("XS370000","山东子公司"),
    FUJIAN_COUNTRY("XS350000","福建子公司"),
    HENAN_COUNTRY("XS410000","河南子公司"),
    CHONGQING_COUNTRY("XS500000","重庆子公司"),
    JIANGXI_COUNTRY("XS360000","江西子公司"),
//    SHANXI_COUNTRY("XS140000","山西子公司"),
    SHANXI_COUNTRY("XS140001","山西子公司"),
    BEIJING_COUNTRY("XS110000","北京子公司"),
    SICHUAN_COUNTRY("XS510000","四川子公司"),
    YUNNAN_COUNTRY("XS530000","云南子公司"),
    HEBEI_COUNTRY("XS130000","河北子公司"),
    SHANGHAI_COUNTRY("XS310000","上海子公司"),
    JILIN_COUNTRY("XS220000","吉林子公司"),
    JIANGSU_COUNTRY("XS320000","江苏子公司"),
    XIANGGANG_COUNTRY("XS810000","香港子公司"),
    TIANJIN_COUNTRY("XS120000","天津子公司"),
    SX_COUNTRY("XS610000","陕西子公司"),
    SHUIXING_COUNTRY("XS666666","水星子公司"),
    HUOXING_COUNTRY("XS777777","火星子公司"),
    LIAONING_COUNTRY("XS210000","辽宁子公司"),
    GUANGDONG_COUNTRY("XS440000","广东子公司"),
    GUANGXI_COUNTRY("XS450000","广西子公司"),
    GUIZHOU_COUNTRY("XS520000","贵州子公司"),
    HEILONGJIANG_COUNTRY("XS230000","黑龙江子公司"),
    NEIMENGGU_COUNTRY("XS150000","内蒙古子公司"),
    XINJIANG_COUNTRY("XS650000","新疆子公司"),
    GANSU_COUNTRY("XS620000","甘肃子公司"),
    HAINAN_COUNTRY("XS460000","海南子公司"),
    XIZANG_COUNTRY("XS540000","西藏子公司"),
    QINGHAI_COUNTRY("XS630000","青海子公司"),
    NINGXIA_COUNTRY("XS640000","宁夏子公司");
    private String key ;
    private  String value;

    BranchEnum(String key, String value){
        this.key = key;
        this.value = value;
    }

    private static Map<String, BranchEnum> enumMaps = new HashMap<>();
    public static Map<String,String> maps = new HashMap<>();
    static {
        for(BranchEnum e : BranchEnum.values()) {
        	enumMaps.put(e.getKey(), e);
            maps.put(e.getKey(),e.getValue());
        }
    }

    public static String get(String key) {
        return enumMaps.get(key).getValue();
    }

    public String getValue() {
        return value;
    }

	public String getKey() {
		return key;
	}

   
}
