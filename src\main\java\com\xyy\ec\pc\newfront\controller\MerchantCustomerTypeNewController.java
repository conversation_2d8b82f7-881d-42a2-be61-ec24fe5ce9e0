package com.xyy.ec.pc.newfront.controller;


import com.xyy.ec.merchant.bussiness.dto.MerchantCustomerTypeBusinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.newfront.service.MerchantCustomerTypeNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequiredArgsConstructor
@RequestMapping("/new-front/merchantCustomerType")
public class MerchantCustomerTypeNewController {



    private final MerchantCustomerTypeNewService merchantCustomerTypeNewService;


    /**
     * 查询用户类型列表
     * @return
     */
    @GetMapping("/getCustomerType")
    public AjaxResult< List<MerchantCustomerTypeBusinessDto>> getCustomerType(){

        try {
            return merchantCustomerTypeNewService.getCustomerTypeBusiness();
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }

    }


}
