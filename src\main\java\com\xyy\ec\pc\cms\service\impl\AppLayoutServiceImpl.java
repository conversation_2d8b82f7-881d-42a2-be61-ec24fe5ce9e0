package com.xyy.ec.pc.cms.service.impl;

import com.xyy.ec.pc.cms.config.CmsAppProperties;
import com.xyy.ec.pc.cms.service.AppLayoutService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class AppLayoutServiceImpl implements AppLayoutService {

    @Autowired
    private CmsAppProperties cmsAppProperties;

    @Override
    public Long getNoLoginLogicMerchantId(Long merchantId) {
        // 非用户侧或者未登录，统一转为null
        Long innerMerchantId = merchantId;
        if (Objects.isNull(innerMerchantId) || innerMerchantId <= 0L) {
            innerMerchantId = null;
        }
        if (Objects.nonNull(innerMerchantId)) {
            // 登录场景
            return merchantId;
        }
        // 未登录用户，尝试获取用于人群场景的逻辑用户
        Long noLoginLogicMerchantId = cmsAppProperties.getNoLoginLogicMerchantId();
        if (log.isDebugEnabled()) {
            log.debug("在没有登录场景下获取逻辑用户，merchantId：{}，逻辑用户ID：{}", merchantId, noLoginLogicMerchantId);
        }
        return noLoginLogicMerchantId;
    }

}
