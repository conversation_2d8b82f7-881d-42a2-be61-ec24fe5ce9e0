package com.xyy.ec.pc.controller.promo;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.remoting.TimeoutException;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.constants.PromoCodeEnum;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.system.business.api.CodeitemBusinessApi;
import com.xyy.ec.system.business.dto.CodeitemBusinessDto;
import com.xyy.ms.promotion.business.api.admin.VoucherForAdminBusinessApi;
import com.xyy.ms.promotion.business.api.admin.VoucherTemplateForAdminBusinessApi;
import com.xyy.ms.promotion.business.api.pc.SyncVoucherApi;
import com.xyy.ms.promotion.business.api.pc.SyncVoucherTemplateApi;
import com.xyy.ms.promotion.business.api.pc.VoucherForPcBusinessApi;
import com.xyy.ms.promotion.business.api.pc.VoucherTemplateForPcBusinessApi;
import com.xyy.ms.promotion.business.common.ErrorCodeEum;
import com.xyy.ms.promotion.business.common.ResultDTO;
import com.xyy.ms.promotion.business.common.constants.VoucherEnum;
import com.xyy.ms.promotion.business.common.response.PromoResp;
import com.xyy.ms.promotion.business.dto.voucher.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

//import com.xyy.ec.pc.controller.VoucherController;

/**
 * created by xxz
 * date:2019-09-09
 */
@RequestMapping("/sass/voucher")
@Controller
public class VoucherForSASSController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(VoucherForSASSController.class);
    @Reference(version = "1.0.0")
    private VoucherForPcBusinessApi voucherBusinessApi;
    @Reference(version = "1.0.0", timeout = 60000)
    private VoucherTemplateForPcBusinessApi voucherTemplateApi;
    @Reference(version = "1.0.0", timeout = 60000)
    private VoucherTemplateForAdminBusinessApi promotionVoucherTemplateApi;
    @Reference(version = "1.0.0", timeout = 60000)
    private VoucherForAdminBusinessApi promotionVoucherApi;
    @Reference(version = "1.0.0", timeout = 60000)
    private CodeitemBusinessApi codeitemBusinessApi;
    @Reference(version = "1.0.0")
    private VoucherForPcBusinessApi promotionVoucherForPcApi;
    @Reference(version = "1.0.0")
    private SyncVoucherTemplateApi syncVoucherTemplateApi;
    @Reference(version = "1.0.0")
    private SyncVoucherApi syncVoucherApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;


    /**
     * 获取待领取的优惠券
     * @param request
     * @param merchantId
     * @return
     */
    @RequestMapping("/findPreReceiveVoucher")
    @ResponseBody
    public Object findPreReceiveVoucher(HttpServletRequest request,Long merchantId){
        LOGGER.info("sass获取待领取的优惠券，merchantId="+merchantId);
        try {
            Map<String, Object> resultMap = addResult();
            resultMap.put(CODE, CODE_SUCCESS);
            if(null==merchantId){
                resultMap.put("data", Lists.newArrayList());
                return resultMap;
            }

            ResultDTO<PageInfo<VoucherCenterDto>> voucherPageResult = voucherBusinessApi.getReceivedVoucherCenter(null, merchantId);
            if (null != voucherPageResult && voucherPageResult.getErrorCode() == ErrorCodeEum.SUCCESS.getErrorCode() && null != voucherPageResult.getData()) {
                List<VoucherCenterDto> list = voucherPageResult.getData().getList();
                resultMap.put("voucherList", list);
            } else {
                resultMap.put("voucherList", Lists.newArrayList());
            }
            return resultMap;
        }catch (Exception e){
            return this.addError(CODE_ERROR, "查询失败");
        }
    }

    /**
     * sass获取所有优惠券
     * @param voucher
     * @param page
     * @param request
     * @param merchantId
     * @return
     */
    @RequestMapping("/findAllVoucherInfo")
    @ResponseBody
    public Object findAllVoucherInfo(VoucherDTO voucher, Page page, HttpServletRequest request,Long merchantId ) {
        Map<String, Object> resultMap = new HashMap<>();
        LOGGER.info("sass获取所有优惠券，merchantId="+merchantId);
        try {
            String requestUrl = this.getRequestUrl(request);
            if (null != page && page.getOffset() == 0) {
                page.setOffset(1);
            }

            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(12);
            if (null == page || 0 == page.getLimit()) {
                pageInfo.setPageNum(1);
            } else {
                pageInfo.setPageSize(page.getLimit());
                pageInfo.setPageNum(page.getOffset());
            }
            int queryVoucherState = VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState();
            if (null != voucher && null != voucher.getState()) {
                queryVoucherState = voucher.getState();
            }
            ResultDTO<MyVoucherPageDTO> voucherPageResult = voucherBusinessApi.getMerchantAllVoucher(pageInfo, merchantId, queryVoucherState);

            MyVoucherPageDTO myVoucherPageDTO = null != voucherPageResult.getData() ? voucherPageResult.getData() : null;
            //已领取
            resultMap.put("notUsedNum", null != myVoucherPageDTO ? myVoucherPageDTO.getUnsueNum() : 0);
            //已用完
            resultMap.put("usedNum", null != myVoucherPageDTO ? myVoucherPageDTO.getUsedNum() : 0);
            //失效
            resultMap.put("expiredNum", null != myVoucherPageDTO ? myVoucherPageDTO.getExpiredNum() : 0);

            PageInfo<MyVoucherDTO> myVoucherDTOPageInfo = myVoucherPageDTO.getVoucherDTOPageInfo();
            resultMap.put("pager", myVoucherDTOPageInfo);
            resultMap.put("requestUrl", requestUrl);
            resultMap.put("center_menu", "voucher");
            resultMap.put("queryVoucherState", queryVoucherState);

            MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            resultMap.put("subAccount", Objects.equals(merchantBussinessDto.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);

            return resultMap;
        } catch (Exception e) {
            LOGGER.error("sass获取优惠券失败", e);
            return this.addError(CODE_ERROR, "查询失败");
        }
    }

    /**
     * 获取优惠券模版列表
     * @param page
     * @param branchCode
     * @return
     */
    @RequestMapping("/findVoucherTemplateList")
    @ResponseBody
    public Object findVoucherTemplateList(Page page,String branchCode,@RequestParam(value = "querySign", required = false) String querySign) {
        LOGGER.info("sass或者ad系统获取所有优惠券模版列表，branchCode="+branchCode+",querySign="+querySign);
        Map<String, Object> resultMap = addResult();
        resultMap.put(CODE, CODE_SUCCESS);
        if (null != page && page.getOffset() == 0) {
            page.setOffset(1);
        }

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(12);
        if (null == page || 0 == page.getLimit()) {
            pageInfo.setPageNum(1);
        } else {
            pageInfo.setPageSize(page.getLimit());
            pageInfo.setPageNum(page.getOffset());
        }
        VoucherTemplateQueryExtendDTO voucherTemplateQueryExtendDTO = new VoucherTemplateQueryExtendDTO();
        voucherTemplateQueryExtendDTO.setBranchCode(branchCode);
        if(StringUtil.isEmpty(branchCode)){
            return this.addError(CODE_ERROR, "查询优惠券模板列表失败,branchCode为空");
        }
        if (BranchEnum.ALL_COUNTRY.getKey().equals(branchCode)) {
            voucherTemplateQueryExtendDTO.setBranchCode(null);
        }
        try {
            voucherTemplateQueryExtendDTO.setBelongSrc(VoucherEnum.BelongSrcEnum.EC.getBelongSrc());
            if(StringUtil.isEmpty(querySign)){
                querySign = Constants.TEMPLATE_NAME_SASS ;
            }
            voucherTemplateQueryExtendDTO.setTemplateName(querySign);
            ResultDTO<PageInfo<VoucherTemplateQueryExtendDTO>> voucherTemplateQueryResult = voucherTemplateApi.getVoucherTemplateListForSass(pageInfo, voucherTemplateQueryExtendDTO);
            resultMap.put("data",voucherTemplateQueryResult.getData());
            return resultMap;
        } catch (Exception e) {
            LOGGER.error("sass或者ad系统获取优惠券模版列表失败", e);
            return this.addError(CODE_ERROR, "查询优惠券模板列表失败");
        }
    }

    /**
     * 获取领取的优惠券列表，供给广告系统后台使用
     * @param page
     * @param branchCode
     * @return
     */
    @RequestMapping("/queryVoucherList")
    @ResponseBody
    public Object queryVoucherList(Page page,@RequestParam(value = "branchCode", required = false) String branchCode,
                                   @RequestParam(value = "merchantId", required = false) Long merchantId,
                                   @RequestParam(value = "voucherState", required = false) Integer voucherState,
                                   @RequestParam(value = "createTime", required = false) String createTime) {
        LOGGER.info("ad平台获取所有的被领取的优惠券列表，branchCode="+branchCode+"，merchantId="+merchantId+"，voucherState="+voucherState+"，createTime="+createTime);
        Map<String, Object> resultMap = addResult();
        resultMap.put(CODE, CODE_SUCCESS);
        if (null != page && page.getOffset() == 0) {
            page.setOffset(1);
        }

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(12);
        if (null == page || 0 == page.getLimit()) {
            pageInfo.setPageNum(1);
        } else {
            pageInfo.setPageSize(page.getLimit());
            pageInfo.setPageNum(page.getOffset());
        }

        if (BranchEnum.ALL_COUNTRY.getKey().equals(branchCode)) {
            branchCode = null;
        }
        try {
            PromoResp<PageInfo<VoucherExtendDTO>> voucherTemplateQueryResult = voucherBusinessApi.queryVoucherListForAd(pageInfo,branchCode,merchantId,voucherState, createTime,Constants.TEMPLATE_NAME_AD );
            if(!voucherTemplateQueryResult.isSuccess()){
                LOGGER.info("通过条件查询已领取的ad系统的优惠券列表失败:", voucherTemplateQueryResult.getMsg());
                return new HashMap<>();
            }
            resultMap.put("data",voucherTemplateQueryResult.getData().getList());
            return resultMap;
        } catch (Exception e) {
            LOGGER.error("通过条件查询已领取的包含ad关键字的优惠券列表失败", e);
            return this.addError(CODE_ERROR, "查询已领取的优惠券列表失败");
        }
    }

    /**
     * 领取优惠券
     * @param merchantIds
     * @param voucherTemplateId
     * @return
     */
    @RequestMapping("/receiveVoucher")
    @ResponseBody
    public Object receiveVoucher(@RequestParam("merchantIds") String merchantIds,
                                 @RequestParam(value = "voucherTemplateId", required = false) Long voucherTemplateId,String token) {
        LOGGER.info("sass领取优惠券，merchantId="+merchantIds+",voucherTemplateId="+voucherTemplateId+",token="+token);
        ResultDTO<String> tokenResultDTO = voucherTemplateApi.getToken();
        String tokenResult = tokenResultDTO.getData();
        if(token==null||!token.equals(tokenResult)){
            return this.addError("token不匹配");
        }
        try {
            VoucherTemplateQueryExtendDTO voucherTemplateQueryExtendDTO = new VoucherTemplateQueryExtendDTO();
            voucherTemplateQueryExtendDTO.setTemplateId(voucherTemplateId);
            ResultDTO<PageInfo<VoucherTemplateQueryExtendDTO>> voucherTemplatePageListResult = promotionVoucherTemplateApi.getVoucherTemplatePageList(null, voucherTemplateQueryExtendDTO);
            if (null == voucherTemplatePageListResult || voucherTemplatePageListResult.getErrorCode() != ErrorCodeEum.SUCCESS.getErrorCode() || null == voucherTemplatePageListResult.getData()
                    || CollectionUtils.isEmpty(voucherTemplatePageListResult.getData().getList())) {
                return this.addError("模板不存在");
            }
            voucherTemplateQueryExtendDTO = voucherTemplatePageListResult.getData().getList().get(0);

            List<Long> oldMerchantIdsList = new ArrayList<Long>();
            if(StringUtils.isNotBlank(merchantIds)){
                oldMerchantIdsList = Arrays.asList(merchantIds.split(",")).stream().filter(merchantIdStr -> StringUtils.isNotBlank(merchantIdStr) && org.apache.commons.lang3.math.NumberUtils.isNumber(merchantIdStr.trim()))
                        .map(merchantIdStr -> Long.parseLong(merchantIdStr.trim())).collect(Collectors.toList());
            }
            ResultDTO resultDTO = voucherBusinessApi.deliveryVoucherForSaas(voucherTemplateId, oldMerchantIdsList, "sass");
            if(null == resultDTO){
                return this.addError("结果未知，请查看确认是否成功");
            }
            if(resultDTO.getErrorCode() != ErrorCodeEum.SUCCESS.getErrorCode()){
                return this.addError((StringUtils.isEmpty(resultDTO.getErrorMsg())? "发送失败" :  resultDTO.getErrorMsg()));
            }
            CodeitemBusinessDto codeitemBusinessDto = codeitemBusinessApi.selectCodeItem("VOUCHER_TEMPLATE", "once_batchNum", voucherTemplateQueryExtendDTO.getBranchCode());
            int onceMaxNum = 700;
            if(null != codeitemBusinessDto && NumberUtils.isNumber(codeitemBusinessDto.getName())){
                onceMaxNum = Integer.valueOf(codeitemBusinessDto.getName());
            }
            if(oldMerchantIdsList.size() > onceMaxNum){
                return this.addResult("异步派送中,请稍后查询明细确认");
            }
            return this.addResult("已成功派送【" + oldMerchantIdsList.size() + "张券】");
        } catch (Exception e) {
            LOGGER.error("券派送失败", e);
            if(e instanceof TimeoutException){
                return this.addError("发券超时，请查看明细确认是否发送成功!");
            }
            if(e.getMessage().indexOf("用户id")>=0){
                return this.addError(e.getMessage());
            }
            return this.addError("优惠券发券失败");
        }
    }

    /**
     * ad用积分兑换时，领取优惠券
     * @param merchantIds 用户id，如果多个用户id则用逗号进行分隔
     * @param voucherTemplateId
     * @param uniqueNo 唯一标识，用来 做幂等
     * @param adIdentification ad广告标识，用来当做领券的operator
     * @return
     */
    @RequestMapping("/receiveVoucherForAd")
    @ResponseBody
    public Object receiveVoucherForAd(@RequestParam("merchantIds") String merchantIds,
                                      @RequestParam(value = "voucherTemplateId", required = false) Long voucherTemplateId,
                                      @RequestParam("uniqueNo") String uniqueNo,
                                      @RequestParam(value = "adIdentification", required = false) String adIdentification,String token) {
        LOGGER.info("ad领取优惠券，merchantId="+merchantIds+",voucherTemplateId="+voucherTemplateId+",uniqueNo="+uniqueNo+",adIdentification="+adIdentification+",token="+token);
        ResultDTO<String> tokenResultDTO = voucherTemplateApi.getToken();
        String tokenResult = tokenResultDTO.getData();
        if(token==null||!token.equals(tokenResult)){
            return this.addError(PromoCodeEnum.TOKEN_EXCEPTION.getCode(),PromoCodeEnum.TOKEN_EXCEPTION.getMsg());
        }
        try {
            VoucherTemplateQueryExtendDTO voucherTemplateQueryExtendDTO = new VoucherTemplateQueryExtendDTO();
            voucherTemplateQueryExtendDTO.setTemplateId(voucherTemplateId);
            ResultDTO<PageInfo<VoucherTemplateQueryExtendDTO>> voucherTemplatePageListResult = promotionVoucherTemplateApi.getVoucherTemplatePageList(null, voucherTemplateQueryExtendDTO);
            if (null == voucherTemplatePageListResult || voucherTemplatePageListResult.getErrorCode() != ErrorCodeEum.SUCCESS.getErrorCode() || null == voucherTemplatePageListResult.getData()
                    || CollectionUtils.isEmpty(voucherTemplatePageListResult.getData().getList())) {
                return this.addError(PromoCodeEnum.VOUCHER_TEMPLATE_ERROR.getCode(),PromoCodeEnum.VOUCHER_TEMPLATE_ERROR.getMsg());
            }
            voucherTemplateQueryExtendDTO = voucherTemplatePageListResult.getData().getList().get(0);

            List<Long> oldMerchantIdsList = new ArrayList<Long>();
            if(StringUtils.isNotBlank(merchantIds)){
                oldMerchantIdsList = Arrays.asList(merchantIds.split(",")).stream().filter(merchantIdStr -> StringUtils.isNotBlank(merchantIdStr) && org.apache.commons.lang3.math.NumberUtils.isNumber(merchantIdStr.trim()))
                        .map(merchantIdStr -> Long.parseLong(merchantIdStr.trim())).collect(Collectors.toList());
            }
            ResultDTO resultDTO = voucherBusinessApi.deliveryVoucherForAd(voucherTemplateId, oldMerchantIdsList, adIdentification,uniqueNo);
            if(null == resultDTO){
                return this.addError(PromoCodeEnum.UNKNOWN.getCode(),"结果未知，请查看确认是否成功");
            }
            if(resultDTO.getErrorCode() != ErrorCodeEum.SUCCESS.getErrorCode()){
                return this.addError(resultDTO.getErrorCode(),(StringUtils.isEmpty(resultDTO.getErrorMsg())? "发送失败" :  resultDTO.getErrorMsg()));
            }
            CodeitemBusinessDto codeitemBusinessDto = codeitemBusinessApi.selectCodeItem("VOUCHER_TEMPLATE", "once_batchNum", voucherTemplateQueryExtendDTO.getBranchCode());
            int onceMaxNum = 700;
            if(null != codeitemBusinessDto && NumberUtils.isNumber(codeitemBusinessDto.getName())){
                onceMaxNum = Integer.valueOf(codeitemBusinessDto.getName());
            }
            if(oldMerchantIdsList.size() > onceMaxNum){
                return this.addResult("异步派送中,请稍后查询明细确认");
            }
            return this.addResult("result",resultDTO.getData());
        } catch (Exception e) {
            LOGGER.error("券派送失败", e);
            if(e instanceof TimeoutException){
                return this.addError(PromoCodeEnum.TIMEOUT_ERROR.getCode() ,"ad系统发券超时，请查看明细确认是否发送成功!");
            }
            if(e.getMessage().indexOf("用户id")>=0){
                return this.addError(PromoCodeEnum.FAIL.getCode(),e.getMessage());
            }
            return this.addError(PromoCodeEnum.FAIL.getCode(),"ad系统优惠券发券失败");
        }
    }

    /**
     * ad广告系统获取所有优惠券
     * @param voucher
     * @param page
     * @param request
     * @param merchantId
     * @param templateName 模板名称
     * @return
     */
    @RequestMapping("/findAllVoucherInfoForAd")
    @ResponseBody
    public Object findAllVoucherInfoForAd(VoucherDTO voucher, Page page, HttpServletRequest request,Long merchantId,@RequestParam(value = "templateName", required = false) String templateName) {
        Map<String, Object> resultMap = new HashMap<>();
        LOGGER.info("ad获取用户的所有优惠券，merchantId="+merchantId+",templateName="+templateName);
        try {
            String requestUrl = this.getRequestUrl(request);
            if (null != page && page.getOffset() == 0) {
                page.setOffset(1);
            }

            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(12);
            if (null == page || 0 == page.getLimit()) {
                pageInfo.setPageNum(1);
            } else {
                pageInfo.setPageSize(page.getLimit());
                pageInfo.setPageNum(page.getOffset());
            }
            int queryVoucherState = VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState();
            if (null != voucher && null != voucher.getState()) {
                queryVoucherState = voucher.getState();
            }

            if(StringUtil.isEmpty(templateName)){
                templateName = Constants.TEMPLATE_NAME_AD;//传空默认是查询包含ad的模板列表
            }
            PromoResp<MyVoucherPageDTO> voucherPageResult = voucherBusinessApi.getMerchantAllVoucherForAd(pageInfo, merchantId, queryVoucherState,templateName);
            if(!voucherPageResult.isSuccess()){
                LOGGER.info("ad获取某个用户的所有优惠券列表失败:", voucherPageResult.getMsg());
                return new HashMap<>();
            }

            MyVoucherPageDTO myVoucherPageDTO = null != voucherPageResult.getData() ? voucherPageResult.getData() : null;
//            //已领取
//            resultMap.put("notUsedNum", null != myVoucherPageDTO ? myVoucherPageDTO.getUnsueNum() : 0);
//            //已用完
//            resultMap.put("usedNum", null != myVoucherPageDTO ? myVoucherPageDTO.getUsedNum() : 0);
//            //失效
//            resultMap.put("expiredNum", null != myVoucherPageDTO ? myVoucherPageDTO.getExpiredNum() : 0);

            PageInfo<MyVoucherDTO> myVoucherDTOPageInfo = myVoucherPageDTO.getVoucherDTOPageInfo();
            resultMap.put("pager", myVoucherDTOPageInfo);
            resultMap.put("requestUrl", requestUrl);
            resultMap.put("center_menu", "voucher");
            MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            resultMap.put("subAccount", Objects.equals(merchantBussinessDto.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);

            resultMap.put("queryVoucherState", queryVoucherState);
            return resultMap;
        } catch (Exception e) {
            LOGGER.error("ad获取用户的所有优惠券列表失败", e);
            return this.addError(CODE_ERROR, "ad获取用户的所有优惠券列表失败");
        }
    }

    /**
     * SAAS客户端领取优惠券
     */
    @RequestMapping("/immediatelyReceiveVoucher")
    @ResponseBody
    public Object immediatelyReceiveVoucher(@RequestParam("merchantId") Long merchantId,
                                 @RequestParam(value = "voucherTemplateId") Long voucherTemplateId, String token) {
        LOGGER.info("#VoucherForSASSController.immediatelyReceiveVoucher#start,merchantId:{},voucherTemplateId:{}," +
                "token:{}",merchantId,voucherTemplateId,token);
        try {
            ResultDTO<String> tokenResultDTO = voucherTemplateApi.getToken();
            String tokenResult = tokenResultDTO.getData();
            if (token == null || !token.equals(tokenResult)) {
                LOGGER.error("#VoucherForSASSController.immediatelyReceiveVoucher#token不匹配,merchantId:{},voucherTemplateId:{}," +
                        "token:{}", merchantId, voucherTemplateId, token);
                return this.addError("token不匹配");
            }

            ReceiveVoucherRequestDTO voucherRequestDTO = new ReceiveVoucherRequestDTO();
            voucherRequestDTO.setMerchantId(merchantId);
            voucherRequestDTO.setVoucherTemplateId(voucherTemplateId);
            LOGGER.info("#VoucherForSASSController.immediatelyReceiveVoucher#receiveVoucher,start,param:{}",voucherRequestDTO);
            ResultDTO<VoucherExtendDTO> resultDTO = promotionVoucherForPcApi.receiveVoucher(voucherRequestDTO);
            LOGGER.info("#VoucherForSASSController.immediatelyReceiveVoucher#receiveVoucher,end,param:{},results:{}",voucherRequestDTO, resultDTO);
            VoucherExtendDTO voucherExtendDTO = resultDTO.getData();
            if (null == resultDTO) {
                LOGGER.error("#VoucherForSASSController.immediatelyReceiveVoucher#领取失败，返回结果为空,merchantId:{}," +
                        "voucherTemplateId:{},token:{}", merchantId, voucherTemplateId, token);
                return this.addError("领取失败");
            }
            if (ErrorCodeEum.SUCCESS.getErrorCode() != resultDTO.getErrorCode()) {
                LOGGER.warn("#VoucherForSASSController.immediatelyReceiveVoucher#领取失败,merchantId:{}," +
                                "voucherTemplateId:{},token:{},errorcode:{},errorMsg:{}", merchantId,
                        voucherTemplateId, token, resultDTO.getErrorCode(), resultDTO.getErrorMsg());
                return this.addError(resultDTO.getErrorCode(), resultDTO.getErrorMsg());
            }
            return this.addResult("优惠券领取成功");
        } catch (Exception e) {
            LOGGER.error("#VoucherForSASSController.immediatelyReceiveVoucher#优惠券领取失败,merchantId:{}," +
                            "voucherTemplateId:{},token:{},errorcode:{},errorMsg:{}", merchantId,
                    voucherTemplateId, token, e);
            LOGGER.error("优惠券领取失败", e);
            return this.addError("优惠券领取失败");
        }
    }

    /**
     * SAAS客户端同步优惠券模版
     */
    @RequestMapping("/syncVoucherTempInfo")
    @ResponseBody
    public Object syncVoucherTempInfo(@RequestParam("page") Integer page,
                                            @RequestParam(value = "pageSize") Integer pageSize, String token) {
        LOGGER.info("#VoucherForSASSController.syncVoucherTempInfo#start,page:{},pageSize:{}," +
                "token:{}",page,pageSize,token);
        try {
            ResultDTO<String> tokenResultDTO = voucherTemplateApi.getToken();
            String tokenResult = tokenResultDTO.getData();
            if (token == null || !token.equals(tokenResult)) {
                LOGGER.error("#VoucherForSASSController.syncVoucherTempInfo#token不匹配,page:{},pageSize:{}," +
                        "token:{}", page, pageSize, token);
                return this.addError("token不匹配");
            }

            if(page == null || pageSize == null || page<=0 || pageSize <= 0 || pageSize>200){
                LOGGER.warn("#VoucherForSASSController.syncVoucherTempInfo#token不匹配,page:{},pageSize:{}," +
                        "token:{}", page, pageSize, token);
                return this.addError("pageSize&page不能为空或者pageSize不能大于200");
            }
            ApiRPCResult<List<SyncVoucherTemplateDTO>> resultDTO = syncVoucherTemplateApi.syncVoucherTemp((long)(page-1)*pageSize, (long)pageSize);

            if(null == resultDTO){
                return this.addError("查询返回对象为空，请重试");
            }

            if(resultDTO.isFail()){
                return this.addError("查询失败，请重试，失败原因"+resultDTO.getErrMsg());
            }

            return this.addResult("查询成功",resultDTO.getData());
        } catch (Exception e) {
            LOGGER.error("#VoucherForSASSController.syncVoucherTempInfo#异常,page:{},pageSize:{}," +
                    "token:{}", page, pageSize, token, e);
            return this.addError("查询异常，请重试");
        }
    }

    /**
     * SAAS客户端同步优惠券模版
     */
    @RequestMapping("/syncVoucherDetail")
    @ResponseBody
    public Object syncVoucherDetail(@RequestParam("templateId") Long templateId,
                                      @RequestParam("page") Integer page,
                                      @RequestParam(value = "pageSize") Integer pageSize, String token) {
        LOGGER.info("#VoucherForSASSController.syncVoucherDetail#start,page:{},pageSize:{}," +
                "token:{}",page,pageSize,token);
        try {
            ResultDTO<String> tokenResultDTO = voucherTemplateApi.getToken();
            String tokenResult = tokenResultDTO.getData();
            if (token == null || !token.equals(tokenResult)) {
                LOGGER.error("#VoucherForSASSController.syncVoucherDetail#token不匹配,page:{},pageSize:{}," +
                        "token:{},templateId:{}", page, pageSize, token, templateId);
                return this.addError("token不匹配");
            }

            if(null == templateId || templateId <=0){
                return this.addError("模版id不合法");
            }

            if(page == null || pageSize == null || page<=0 || pageSize <= 0 || pageSize>200){
                LOGGER.warn("#VoucherForSASSController.syncVoucherDetail#token不匹配,page:{},pageSize:{}," +
                        "token:{},templateId:{}", page, pageSize, token, templateId);
                return this.addError("pageSize&page不能为空或者pageSize不能大于200");
            }
            ApiRPCResult<List<SyncVoucherDTO>> resultDTO = syncVoucherApi.syncVoucherInfo(templateId,(long)(page-1)*pageSize,(long)pageSize);

            if(null == resultDTO){
                return this.addError("查询返回对象为空，请重试");
            }

            if(resultDTO.isFail()){
                return this.addError("查询失败，请重试，失败原因"+resultDTO.getErrMsg());
            }

            return this.addResult("查询成功",resultDTO.getData());
        } catch (Exception e) {
            LOGGER.error("#VoucherForSASSController.syncVoucherDetail#异常,page:{},pageSize:{}," +
                    "token:{},templateId:{}", page, pageSize, token, templateId, e);
            return this.addError("查询异常，请重试");
        }
    }
}
