package com.xyy.ec.pc.newfront.annotation.handler;

import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.service.cms.preview.CustomizeCmsResponseHandleConfig;
import com.xyy.ec.pc.newfront.utils.CmsUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Aspect
@Component
public class CustomizeCmsResponseHandler {

    private final CustomizeCmsResponseHandleConfig customizeCmsResponseHandleConfig;

    @Around("@annotation(customizeCmsResponse) || @within(customizeCmsResponse)")
    public Object beforeOperation(ProceedingJoinPoint joinPoint, CustomizeCmsResponse customizeCmsResponse) {
        try {
            return this.createEmptyResponse(joinPoint, customizeCmsResponse);
        } catch (RuntimeException e) {
            throw e;
        } catch (Throwable e) {
            log.warn("CustomizeCmsResponse 处理失败!", e);
            throw new RuntimeException(e);
        }
    }

    private Object createEmptyResponse(ProceedingJoinPoint joinPoint, CustomizeCmsResponse ann) throws Throwable {
        if (CmsUtil.isCmsAdmin()) {
            return customizeCmsResponseHandleConfig.apply(joinPoint);
        } else {
            return joinPoint.proceed();
        }
    }
}
