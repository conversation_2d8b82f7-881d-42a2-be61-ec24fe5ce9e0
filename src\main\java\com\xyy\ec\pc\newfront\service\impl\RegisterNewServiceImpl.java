package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.api.busi.place.register.PoiReq;
import com.xyy.ec.api.rpc.user.UserRpcService;
import com.xyy.ec.merchant.bussiness.dto.MerchantPoiBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.PoiRequestBussinessDto;
import com.xyy.ec.pc.newfront.service.RegisterNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-07-11 10:28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class RegisterNewServiceImpl implements RegisterNewService {

    @Value("${max.register.search.num}")
    private Integer maxNum;

    private final UserRpcService userRpcService;

    @Override
    public AjaxResult<PageInfo<MerchantPoiBusinessDto>> getPageMerchantInfo(PoiReq poiReq) {
        PoiRequestBussinessDto poiDto = new PoiRequestBussinessDto();
        BeanUtils.copyProperties(poiReq, poiDto);
        // 为了防止爬数据，限制每页显示的条数的上限
        if (Objects.nonNull(poiReq) && (Objects.isNull(poiReq.getLimit()) || poiReq.getLimit() <= 0 || poiReq.getLimit() >= 50)) {
            poiReq.setLimit(30);
        }
        //限制最大查询数量
        if ((poiReq.getOffset()+1)*poiReq.getLimit()>maxNum){
            poiReq.setOffset(0);
        }
        PageInfo<MerchantPoiBusinessDto> pageMerchantInfoList = userRpcService.findPageMerchantInfoList(poiDto, poiReq.getOffset(), poiReq.getLimit());
        //限制最大查询数量
        if (pageMerchantInfoList.getTotal()>maxNum){
            pageMerchantInfoList.setTotal(maxNum);
        }
        return AjaxResult.successResult(pageMerchantInfoList);
    }
}
