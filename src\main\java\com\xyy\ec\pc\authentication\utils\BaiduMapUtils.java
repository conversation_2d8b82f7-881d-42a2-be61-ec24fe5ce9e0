package com.xyy.ec.pc.authentication.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xyy.ec.pc.util.ipip.IPUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class BaiduMapUtils {

    @Value("${baidu.map.aks:}")
    private String[] aks;

    @ApolloJsonValue("${baidu.map.test:{}}")
    private Map<String, String> ipTest;

    @Resource(name = "cacheRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    private final static String ipCacheKey = "ip_addr_baidu:";

    private final static String url = "https://api.map.baidu.com/location/ip?ip=%s&coor=bd09ll&ak=%s";

    private String getAk() {

        if (aks.length == 0) {
            return null;
        }
        return aks[new Random().nextInt(aks.length)];
    }

    private String getIpKey(String ip) {

        return ipCacheKey + ip;
    }

    public JSONObject getIpAddrInfo(String ip) {

        // 内网ip不查询
        if (IpUtils.internalIp(ip)) {
            return null;
        }
        String result = redisTemplate.opsForValue().get(getIpKey(ip));
        if (StringUtils.isNotBlank(result)) {
            return JSONObject.parseObject(result);
        }
        // 调用百度api查询ip地址信息
        String ak = getAk();
        if (StringUtils.isBlank(ak)) {
            return null;
        }
        String urls = String.format(url, ip, ak);
        try {
            String response = HttpUtil.get(urls);
            if (StringUtils.isNotBlank(response)) {
                // 记录到缓存
                redisTemplate.opsForValue().set(getIpKey(ip), response, 30, TimeUnit.DAYS);
                return JSONObject.parseObject(response);
            }
        }
        catch (Exception e) {
            log.error("查询ip地址信息异常, ip[{}], ak[{}]", ip, ak);
        }
        return null;
    }

    public String getIpProvince(String ip) {

        JSONObject ipAddrInfo = getIpAddrInfo(ip);
        if (ipAddrInfo == null) {
            return null;
        }
        JSONObject content = ipAddrInfo.getJSONObject("content");
        if (content == null) {
            return null;
        }
        JSONObject detail = content.getJSONObject("address_detail");
        if (detail == null) {
            return null;
        }
        return detail.getString("province");
    }

    public String getIpProvince() {

        String hostIp = IPUtils.getIp();
        log.info("当前ip[{}]", hostIp);
        if (ipTest.containsKey(hostIp)) {
            return ipTest.get(hostIp);
        }
        String province = getIpProvince(hostIp);
        if (StringUtils.isBlank(province)) {
            return null;
        }
        return province.replaceAll("自治区|维吾尔|回族|藏族|壮族|省|市", "");
    }
}
