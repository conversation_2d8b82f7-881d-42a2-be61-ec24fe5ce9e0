package com.xyy.ec.pc.model;

import lombok.Data;

import java.util.List;

@Data
public class WorkOrderDTO {
    //介入类型
    private Integer type;
    //票据类型
    private Integer invoiceType;
    //资质类型
    private List<Integer> qualificationTypes;
    //资质/票据状态
    private Integer interventionState;
    //诉求
    private Integer interventionAppeal;
    //售后类型
    private Integer afterSaleType;
    //订单号
    private  String orderNo;
    //售后编号
    private String afsNo;
    //退款单号
    private String refundNo;
    //售后问题描述
    private String remark;
    //客户联系方式
    private String mobile;
    //邮件
    private String email;
    //图片路径
    private String imgsUrl;
    //客户id
    private Long merchantId;
    //商业编码
    private String orgId;
    /** 上传图片凭证，最多10张 */
    private List<String> imgUrls;
    //留言id
    private Long dialogId;
}
