package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.pc.newfront.service.AreaNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025-07-11 9:44
 */


@RestController
@RequiredArgsConstructor
@RequestMapping("/new-front/login/area")
@Slf4j
public class AreaNewController {

    private final AreaNewService areaNewService;


    @GetMapping("/findAreaByParentId")
    public AjaxResult<Object> findAreaByParentId(@RequestParam("parentId") Integer parentId){

        try {
            return areaNewService.findAreaById(parentId);
        } catch (Exception e) {
            log.error("newArea findAreaByParentId error", e);
            return AjaxResult.errResult(e.getMessage());
        }

    }

}
