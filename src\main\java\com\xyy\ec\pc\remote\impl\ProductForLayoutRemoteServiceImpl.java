package com.xyy.ec.pc.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarkingHyperspaceApiResultCodeEnum;
import com.xyy.ec.marketing.hyperspace.api.common.exception.MarkingHyperspaceServiceException;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.remote.ProductForLayoutRemoteService;
import com.xyy.ec.product.business.dto.ProductBaseInfoAndControlParamDTO;
import com.xyy.ec.product.business.dto.ProductConditionDTO;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductBaseInfoAndControlDTO;
import com.xyy.ec.product.business.ecp.out.layout.api.ProductForLayoutApi;
import com.xyy.ec.product.business.ecp.out.layout.dto.FilterResultDTO;
import com.xyy.ec.product.business.ecp.out.promotion.api.ProductForPromotionApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * {@link ProductForLayoutApi} 远程调用Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductForLayoutRemoteServiceImpl implements ProductForLayoutRemoteService {

    @Reference(version = "1.0.0")
    private ProductForLayoutApi productForLayoutApi;
    @Reference(version = "1.0.0")
    private ProductForPromotionApi productForPromotionApi;

    @Override
    public List<Long> controlFilterCsuIdsForIsVisible(Long merchantId, String branchCode, List<Long> csuIds) {
        long start = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(csuIds)) {
            return Lists.newArrayList();
        }
        List<Long> result;
        int controlFilterCsuIdsMaxSize = 200;
        // 分组
        List<List<Long>> parts = Lists.partition(csuIds, controlFilterCsuIdsMaxSize);
        // 串行
        List<Long> allControlFilterCsuIds = Lists.newLinkedList();
        List<Long> partControlFilterCsuIds;
        for (List<Long> part : parts) {
            partControlFilterCsuIds = controlFilterCsuIdsForIsVisibleSingle(merchantId, branchCode, part);
            allControlFilterCsuIds.addAll(partControlFilterCsuIds);
        }
        result = doSortCsuIds(csuIds, allControlFilterCsuIds);
        if (log.isDebugEnabled()) {
            log.debug("【performance】查询商品服务——控销过滤，不含不可见的。耗时：{} ms。全部CsuId的数量：{}，单次控销的最大商品数：{}",
                    (System.currentTimeMillis() - start), csuIds.size(), controlFilterCsuIdsMaxSize);
            log.debug("查询商品服务——控销过滤，不含不可见的。merchantId：{}，branchCode：{}，csuIds：{}，result：{}",
                    merchantId, branchCode, JSONArray.toJSONString(csuIds), JSONArray.toJSONString(result));
        }
        return result;
    }

    /**
     * 单次：只留下可见的
     *
     * @param merchantId
     * @param csuIds
     * @return
     */
    private List<Long> controlFilterCsuIdsForIsVisibleSingle(Long merchantId, String branchCode, List<Long> csuIds) {
        long start = System.currentTimeMillis();
        ApiRPCResult<List<FilterResultDTO>> apiRPCResult = null;
        try {
            apiRPCResult = productForLayoutApi.filterSkuIdList(csuIds, branchCode, merchantId);
            if (apiRPCResult == null || !apiRPCResult.isSuccess()) {
//                String message = MessageFormat.format("单次；merchantId：{0}，branchCode：{1}，csuIds：{2}",
//                        merchantId, branchCode, JSONArray.toJSONString(csuIds));
//                throw new AppException(message, XyyJsonResultCodeEnum.CONSOLE_FILTER_CSU_ERROR);
                log.error("查询商品服务——控销过滤，不含不可见的（单次）失败。branchCode：{}，merchantId：{}，csuIds：{}，响应：{}",
                        branchCode, merchantId, JSONArray.toJSONString(csuIds), JSONObject.toJSONString(apiRPCResult));
                return Lists.newArrayList();
            }
        } catch (Exception e) {
            log.error("查询商品服务——控销过滤，不含不可见的（单次）出现异常。branchCode：{}，merchantId：{}，csuIds：{}，异常信息：",
                    branchCode, merchantId, JSONArray.toJSONString(csuIds), e);
            return Lists.newArrayList();
        }
        List<FilterResultDTO> filterResultDTOS = apiRPCResult.getData();
        if (log.isDebugEnabled()) {
            log.debug("【performance】查询商品服务——控销过滤，不含不可见的（单次）。耗时：{} ms。csuId数量：{}",
                    (System.currentTimeMillis() - start), csuIds.size());
            log.debug("查询商品服务——控销过滤，不含不可见的（单次）。branchCode：{}，merchantId：{}，csuIds：{}，结果：{}",
                    branchCode, merchantId, JSONArray.toJSONString(csuIds), filterResultDTOS);
        }
        if (CollectionUtils.isEmpty(filterResultDTOS)) {
            return Lists.newArrayList();
        }
        return controlFilterCsuIdsForIsVisible(filterResultDTOS);
    }

    /**
     * 只留下可见的
     *
     * @param filterResultForSearchDTOS
     * @return
     */
    private List<Long> controlFilterCsuIdsForIsVisible(List<FilterResultDTO> filterResultForSearchDTOS) {
        if (CollectionUtils.isEmpty(filterResultForSearchDTOS)) {
            return Lists.newArrayList();
        }
        return filterResultForSearchDTOS.stream().filter(item -> item != null && item.getControlStatus() != null && !item.getControlStatus().equals(2))
                .map(FilterResultDTO::getCsuId).collect(Collectors.toList());
    }

    /**
     * 将目标List按照第一个List进行排序，并且过滤掉null的。
     *
     * @param sortedCsuIds
     * @param sourceCsuIds
     * @return
     */
    private List<Long> doSortCsuIds(List<Long> sortedCsuIds, List<Long> sourceCsuIds) {
        if (CollectionUtils.isEmpty(sortedCsuIds) || CollectionUtils.isEmpty(sourceCsuIds)) {
            return Lists.newArrayList();
        }
        Set<Long> sourceCsuIdSet = new HashSet<>(sourceCsuIds);
        return sortedCsuIds.stream().filter(item -> item != null && sourceCsuIdSet.contains(item)).collect(Collectors.toList());
    }

    @Override
    public List<ListProduct> fillProductInfo(List<Long> csuIds, Long merchantId, boolean merchantIsNotShowFragileGoods, String branchCode) {
        return this.fillProductInfoV2(csuIds, merchantId, merchantIsNotShowFragileGoods, branchCode, false, false);
    }

    @Override
    public List<ListProduct> fillProductInfoV2(List<Long> csuIds, Long merchantId, boolean merchantIsNotShowFragileGoods, String branchCode,
                                               Boolean isNeedExpress, Boolean isNeedQualityShopTitleTag) {
        long start = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(csuIds)) {
            return Lists.newArrayList();
        }
        // 根据用户判断是否可以展示易碎品
        // 商品查询条件：是否展示易碎品。
        ProductEnumDTO.ShowFragileEnum productIsShowFragileEnum;
        if (BooleanUtils.isTrue(merchantIsNotShowFragileGoods)) {
            productIsShowFragileEnum = ProductEnumDTO.ShowFragileEnum.NOT_SHOW_FRAGILE;
        } else {
            productIsShowFragileEnum = ProductEnumDTO.ShowFragileEnum.IS_SHOW_FRAGILE;
        }
        int productIsShowFragile = productIsShowFragileEnum.getCode();
        ProductConditionDTO productConditionDTO = new ProductConditionDTO();
        productConditionDTO.setBranchCode(branchCode);
        productConditionDTO.setMerchantId(merchantId);
        productConditionDTO.setIsShowFragileGoods(productIsShowFragile);
        productConditionDTO.setSkuIdList(csuIds);
        productConditionDTO.setPageNum(1);
        productConditionDTO.setPageSize(csuIds.size());
//        productConditionDTO.setIsNeedExpress(isNeedExpress);
//        productConditionDTO.setIsNeedQualityShopTitleTag(isNeedQualityShopTitleTag);
        ListSkuSearchData listSkuSearchData = null;
        try {
            listSkuSearchData = productForLayoutApi.findCsuListOnlySale(productConditionDTO);
        } catch (Exception e) {
            String message = MessageFormat.format("获取在售商品信息（cms）失败，入参：{0}", JSONObject.toJSONString(productConditionDTO));
            throw new AppException(message, e, XyyJsonResultCodeEnum.FIND_CSU_LIST_ONLY_SALE_FOR_CMS_ERROR);
        }
        if (log.isDebugEnabled()) {
            log.debug("【performance】填充商品信息。耗时：{} ms。商品数量：{}", (System.currentTimeMillis() - start), csuIds.size());
            log.debug("填充商品信息，入参：{}，出参：{}", JSONObject.toJSONString(productConditionDTO), JSONObject.toJSONString(listSkuSearchData));
        }
        List<ListProduct> skuDtoList;
        if (listSkuSearchData == null || CollectionUtils.isEmpty(skuDtoList = listSkuSearchData.getSkuDtoList())) {
            return Lists.newArrayList();
        }
        List<ListProduct> productDTOS = doSortProducts(csuIds, skuDtoList);
        if (log.isDebugEnabled()) {
            log.debug("【performance】填充商品信息。耗时：{} ms。商品数量：{}", (System.currentTimeMillis() - start), csuIds.size());
            log.debug("填充商品信息结束，入参：{}，出参：{}", JSONObject.toJSONString(productConditionDTO), JSONObject.toJSONString(listSkuSearchData));
        }
        return productDTOS;
    }

    /**
     * 排序
     *
     * @param sortedCsuIds
     * @param sourceProducts
     * @return
     */
    private List<ListProduct> doSortProducts(List<Long> sortedCsuIds, List<ListProduct> sourceProducts) {
        if (CollectionUtils.isEmpty(sortedCsuIds) || CollectionUtils.isEmpty(sourceProducts)) {
            return Lists.newArrayList();
        }
        Map<Long, ListProduct> productMap = sourceProducts.stream().filter(item -> item != null && item.getId() != null)
                .collect(Collectors.toMap(ListProduct::getId, item -> item, (first, second) -> first));
        return sortedCsuIds.stream().filter(item -> item != null && productMap.containsKey(item))
                .map(item -> productMap.get(item)).collect(Collectors.toList());
    }
    /**
     * 查询商品信息，含有可见性控销、经营范围等逻辑。<br/>
     * 用于标签查询。
     *
     * @param merchantId
     * @param csuIds
     * @return
     */
    public List<ProductBaseInfoAndControlDTO> findProductForLabel(Long merchantId, List<Long> csuIds) {
        if (Objects.isNull(merchantId) || CollectionUtils.isEmpty(csuIds)) {
            return Lists.newArrayList();
        }
        List<ProductBaseInfoAndControlDTO> result = Lists.newArrayListWithExpectedSize(csuIds.size());
        ProductBaseInfoAndControlParamDTO paramDTO = new ProductBaseInfoAndControlParamDTO();
        paramDTO.setMerchantId(merchantId);
        paramDTO.setNeedPrice(true);
        List<List<Long>> csuIdsLists = Lists.partition(csuIds, 50);
        ApiRPCResult<List<ProductBaseInfoAndControlDTO>> apiRPCResult;
        for (List<Long> csuIdsList : csuIdsLists) {
            try {
                paramDTO.setSkuIdList(csuIdsList);
                apiRPCResult = productForPromotionApi.findProductBaseInfoAndControlWithoutActInfo(paramDTO);
                if (Objects.isNull(apiRPCResult) || apiRPCResult.isFail()) {
                    log.error("查询商品信息失败，productForPromotionApi.findProductForLabel，param：{}，apiRPCResult：{}",
                            JSONObject.toJSONString(paramDTO), JSONObject.toJSONString(apiRPCResult));
                    throw new MarkingHyperspaceServiceException(MarkingHyperspaceApiResultCodeEnum.FAIL);
                }
                List<ProductBaseInfoAndControlDTO> data = apiRPCResult.getData();
                if (log.isDebugEnabled()) {
                    log.debug("查询商品信息，productForPromotionApi.findProductForLabel，param：{}，商品信息：{}",
                            JSONObject.toJSONString(paramDTO), JSONArray.toJSONString(data));
                }
                if (CollectionUtils.isNotEmpty(data)) {
                    result.addAll(data);
                }
            } catch (Exception e) {
                log.error("查询商品信息失败，productForPromotionApi.findProductForLabel，param：{}，异常信息：",
                        JSONObject.toJSONString(paramDTO), e);
            }
        }
        return result;
    }
}
