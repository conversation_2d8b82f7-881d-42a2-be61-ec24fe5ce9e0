package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.xyy.ec.merchant.bussiness.api.MerchantCustomerTypeBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantCustomerTypeBusinessDto;
import com.xyy.ec.pc.newfront.service.MerchantCustomerTypeNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-11 10:45
 */
@Service
@Slf4j
public class MerchantCustomerTypeNewServiceImpl implements MerchantCustomerTypeNewService {

    @Reference(version = "1.0.0")
    private MerchantCustomerTypeBusinessApi merchantCustomerTypeBusinessApi;

    @Override
    public AjaxResult< List<MerchantCustomerTypeBusinessDto>> getCustomerTypeBusiness() {
        try {
            return AjaxResult.successResult(merchantCustomerTypeBusinessApi.getAll());
        } catch (Exception e) {
            log.error("用户类型列表查询异常",e);
            return AjaxResult.errResult("用户类型列表查询异常");
        }
    }
}
