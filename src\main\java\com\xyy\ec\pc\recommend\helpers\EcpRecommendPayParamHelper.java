package com.xyy.ec.pc.recommend.helpers;

import com.xyy.ec.pc.recommend.params.PcRecommendPayParam;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSceneEnum;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendTerminalTypeEnum;
import com.xyy.recommend.ecp.params.EcpRecommendPayQueryParam;


public class EcpRecommendPayParamHelper {

    public static EcpRecommendPayQueryParam create(PcRecommendPayParam recommendParam, Long merchantId, Long accountId) {
        return EcpRecommendPayQueryParam.builder()
                .merchantId(merchantId)
                .accountId(accountId)
                .terminalType(EcpRecommendTerminalTypeEnum.PC)
                .recommendScene(EcpRecommendSceneEnum.valueOfCustom(recommendParam.getRecommendScene()))
                .pageNum(recommendParam.getPageNum())
                .pageSize(recommendParam.getPageSize())
                .scmId(recommendParam.getScmId())
                .build();
    }
}
