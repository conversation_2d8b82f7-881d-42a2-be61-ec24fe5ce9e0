package com.xyy.ec.pc.recommend.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.pc.recommend.vo.PcRecommendCardVO;
import com.xyy.ec.pc.recommend.vo.PcRecommendProductVO;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendProductCardPositionTypeEnum;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSpuRecallStrategyEnum;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendYesNoEnum;
import com.xyy.recommend.ecp.result.EcpRecommendBaseCardDTO;
import com.xyy.recommend.ecp.result.EcpRecommendProductCardDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
public class PcRecommendCardVOHelper {

    /**
     * 创建
     *
     * @param recommendBaseCardDTO
     * @param csuIdToInfoMap
     * @param isShowSimilarGoodsJump
     * @return
     */
    public static PcRecommendCardVO create(EcpRecommendBaseCardDTO recommendBaseCardDTO, Map<Long, PcRecommendProductVO> csuIdToInfoMap, Boolean isShowSimilarGoodsJump) {
        if (Objects.isNull(recommendBaseCardDTO) || MapUtils.isEmpty(csuIdToInfoMap)) {
            return null;
        }
        if (recommendBaseCardDTO instanceof EcpRecommendProductCardDTO) {
            EcpRecommendProductCardDTO ecpRecommendProductCardDTO = (EcpRecommendProductCardDTO) recommendBaseCardDTO;
            if (Objects.isNull(ecpRecommendProductCardDTO.getProduct())) {
                return null;
            }
            PcRecommendProductVO productVO = csuIdToInfoMap.get(ecpRecommendProductCardDTO.getProduct().getId());
            if (Objects.isNull(productVO)) {
                return null;
            }
            PcRecommendCardVO recommendCardVO = new PcRecommendCardVO();
            recommendCardVO.setCardType(recommendBaseCardDTO.getCardType());
            productVO.setHasSimilarGoods(BooleanUtils.isTrue(isShowSimilarGoodsJump) && Objects.nonNull(ecpRecommendProductCardDTO.getProduct().getSimilarSkuNum())
                    ? EcpRecommendYesNoEnum.YES.getValue() : EcpRecommendYesNoEnum.NO.getValue());
            Integer positionType = ecpRecommendProductCardDTO.getPositionType();
            String positionTypeName;
            EcpRecommendProductCardPositionTypeEnum recommendProductCardPositionTypeEnum = EcpRecommendProductCardPositionTypeEnum.valueOfCustom(positionType);
            if (Objects.nonNull(recommendProductCardPositionTypeEnum)) {
                positionTypeName = recommendProductCardPositionTypeEnum.getName();
            } else {
                positionType = EcpRecommendProductCardPositionTypeEnum.NORMAL.getType();
                positionTypeName = EcpRecommendProductCardPositionTypeEnum.NORMAL.getName();
            }
            productVO.setPositionType(positionType);
            productVO.setPositionTypeName(positionTypeName);
            EcpRecommendSpuRecallStrategyEnum recommendSpuRecallStrategyEnum = EcpRecommendSpuRecallStrategyEnum.valueOfCustom(ecpRecommendProductCardDTO.getSpuRecallStrategyName());
            if (Objects.nonNull(recommendSpuRecallStrategyEnum)) {
                productVO.setSpuRecallStrategyName(recommendSpuRecallStrategyEnum.getName());
            }
            recommendCardVO.setProductInfo(productVO);
            return recommendCardVO;
        }
        return null;
    }

    /**
     * 创建
     *
     * @param recommendBaseCardDTOS
     * @param csuIdToInfoMap
     * @param isShowSimilarGoodsJump
     * @return
     */
    public static List<PcRecommendCardVO> creates(List<EcpRecommendBaseCardDTO> recommendBaseCardDTOS, Map<Long, PcRecommendProductVO> csuIdToInfoMap, Boolean isShowSimilarGoodsJump) {
        if (CollectionUtils.isEmpty(recommendBaseCardDTOS) || MapUtils.isEmpty(csuIdToInfoMap)) {
            return Lists.newArrayList();
        }
        return recommendBaseCardDTOS.stream().map(item -> PcRecommendCardVOHelper.create(item, csuIdToInfoMap, isShowSimilarGoodsJump))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

}
