package com.xyy.ec.pc.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.pc.constants.RedisConstants;
import com.xyy.ec.pc.model.dto.CrawlerDto;
import com.xyy.framework.redis.autoconfigure.core.RedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @author: baoxin
 * @date: 2021-09-03
 * @time: 21:57
 */
@Component
@Slf4j
public class CrawlerUtil {

    @Resource
    private RedisClient redisClient;

    @Value("${del_crawler_url}")
    private String delCrawlerUrl;

    @Value("${crawler_url}")
    private String crawlerUrl;

    @Value("${crawler_save_kick_out_log_url}")
    private String crawlerSaveKickOutLogUrl;

    private static final String SAVE_KICK_OUT_LOG_KEY = "SAVE_KICK_OUT_LOG_CONCURRENCE_PC:%s:%s";

    @Value("${kick_out_switch}")
    private String kickOutSwitch;

    @Value("${kick_out_sms_valid_switch:OPEN}")
    private String kickOutSmsValidSwitch;

    /**
     * 反爬开关：true：打开，false：关闭
     */
    @Value("${crawler_defense_switch:false}")
    private Boolean crawlerDefenseSwitch;

    /**
     * 办公室外网IP白名单
     */
    @Value("#{'${office.ip.white.list:}'.split(',')}")
    private String[] officeIpWhiteList;

    @Value("#{'${crawler.user.white.list:}'.split(',')}")
    private String[] crawlerUserWhiteList;

    @Value("#{'${crawler.user.ip.black.list:}'.split(',')}")
    private String[] crawlerUserIpBlackList;

    private static final Long ONE_YEAR = 3600 * 24 * 365L;

    private static final Long ONE_DAY = 3600 * 24L;

    /**
     * 判断是否白名单IP
     */
    public boolean isWhiteIP(Long merchantId, String ip) {
        for (String whiteIP : officeIpWhiteList) {
            if (whiteIP != null && whiteIP.equalsIgnoreCase(ip)) {
                log.info("跳过爬虫或KICK_OUT判断，白名单IP，merchantId:{}, IP:{}", merchantId, ip);
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否白名单用户
     */
    public boolean isWhiteUser(Long merchantId, String ip) {

        for (String whiteUser : crawlerUserWhiteList) {
            if (merchantId != null && whiteUser != null && whiteUser.equals(merchantId.toString())) {
                log.info("跳过爬虫或KICK_OUT判断，白名单USER，merchantId:{}, IP:{}", merchantId, ip);
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否用户黑名单IP
     */
    public boolean isBlackUserIp(Long merchantId, String ip) {
        
        return Arrays.asList(crawlerUserIpBlackList).contains(merchantId + "_" + ip);
    }
    
    /**
     * 清除爬虫标记（将爬虫用户设置为正常用户）
     * -- 清除 反爬系统的标记 和 Redis标记
     *
     * @param merchantId
     * @param ip
     */
    public void delCrawler(long merchantId, String ip) {
        try {
            String httpResStr = HttpClientUtil.doGet(delCrawlerUrl + "?userId=" + merchantId + "&ip=" + ip + "&siteType=pc");
            log.info("清除爬虫标记，入参: userId:{}, ip:{}, 返回值：{}", merchantId, ip, httpResStr);

        } catch (Exception e) {
            log.error("清除爬虫标记异常。入参: userId:{}, ip:{}. ", merchantId, ip, e);
        }

        //爬虫用户短信验证码校验通过将缓存中id移除
        redisClient.del(RedisConstants.CRAWLER + merchantId + "_" + ip);
    }


    /**
     * 判断请求是否需要被灰度踢出
     *
     * @param merchantId
     * @param ip
     * @return
     */
    public boolean isKickOut(Long merchantId, String ip) {

        if ("OFF".equalsIgnoreCase(kickOutSwitch)) {
            log.info("KICK_OUT已关闭。merchantId:{}, ip:{}", merchantId, ip);
            return false;
        }

        log.info("判断是否KICK_OUT灰度用户，merchantId:{}, ip:{}", merchantId, ip);

        // 如果校验短信验证开关打开，检查用户的IP是否做过短信验证
        if ("OPEN".equals(kickOutSmsValidSwitch)) {
            // 如果用户的IP已经做了短信验证，放行
//            if (redisClient.exists(RedisConstants.KICK_OUT_VERIFY_PC + merchantId + "_" + ip)) {
//                log.info("判断KICK_OUT灰度用户，用户在该IP已经做过短信验证。merchantId:{}, ip:{}", merchantId, ip);
//                return false;
//            }
            if (isKickOutValid(merchantId, ip)) {
                return false;
            }
        }

        // 如果不是PC端的灰度用户，放行
        if (!redisClient.exists(RedisConstants.KICK_OUT_PC + merchantId)) {
            log.info("判断KICK_OUT灰度用户，不是PC端的灰度用户。merchantId:{}", merchantId);
            return false;
        }

        return true;
    }

    public boolean isKickOutValid(Long merchantId, String ip) {

        String kickOutKey = RedisConstants.KICK_OUT_PC + merchantId;
        String value = redisClient.get(kickOutKey);
        if (StrUtil.isNotBlank(value)) {
            return Arrays.asList(value.split(",")).contains(ip);
        }
        return true;
    }

    public void setKickOutValid(Long merchantId, String ip) {

        String kickOutKey = RedisConstants.KICK_OUT_PC + merchantId;
        String value = redisClient.get(kickOutKey);
        if (StrUtil.isNotBlank(value)) {
            if (!Arrays.asList(value.split(",")).contains(ip)) {
                value = value + "," + ip;
                redisClient.set(kickOutKey, value, ONE_YEAR);
            }
        }
    }

    /**
     * 调用爬虫服务判断是否是爬虫用户
     *
     * @param userId
     * @param ip
     * @return
     */
    public boolean isCrawler(Long userId, String ip) {
        
        if (BooleanUtils.isFalse(crawlerDefenseSwitch)) {
            return false;
        }
        try {
            return redisClient.exists(RedisConstants.CRAWLER + userId + "_" + ip);
        } catch (Exception e) {
            log.error("登录判断是否爬虫, userId:{}, ip:{}, 发生异常", userId, ip, e);
            return false;
        }
    }

    /**
     * 保存 KICK_OUT 踢出日志
     */
    public boolean saveKickOutLog(Long userId, String ip) {
        try {
            String url = crawlerSaveKickOutLogUrl + "?userId=" + userId + "&ip=" + ip + "&siteType=pc";
            log.info("保存 KICK_OUT 踢出日志, url:{}", url);

            String key = String.format(SAVE_KICK_OUT_LOG_KEY, userId, ip);

            // 并发锁，避免并发调用多次接口
            if (redisClient.setNx(key, "1")) {

                redisClient.expire(key, 5); // 最多保存5秒，如果调用接口时程序异常终止，5秒后锁自动过期

                String httpResStr = HttpClientUtil.doGet(url); // 调用反爬接口，保存踢出日志

                log.info("保存 KICK_OUT 踢出日志, userId:{}, ip:{}, response:{}", userId, ip, httpResStr);

                redisClient.del(key);
            }
            return true;

        } catch (Exception e) {
            log.error("保存 KICK_OUT 踢出日志, userId:{}, ip:{}, 发生异常", userId, ip, e);
            return false;
        }
    }


}
