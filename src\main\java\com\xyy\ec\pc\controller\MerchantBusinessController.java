package com.xyy.ec.pc.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.FavoriteBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantCancelFreezeLogBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBusinessBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.enums.SubTypeEnum;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;


/**
 * 有货提醒/降价订阅
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/merchant/merchantBusiness")
public class MerchantBusinessController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantBusinessController.class);



    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
	private FavoriteBussinessApi favoriteBussinessApi;

    @Reference(version = "1.0.0")
	private MerchantCancelFreezeLogBusinessApi merchantCancelFreezeLogBusinessApi;

 

    /**
     * 到货订阅通知
     * @return
     */
    @RequestMapping("/subscribe/{id}.json")
    @ResponseBody
    public Object subscribe(@PathVariable Long id){
    	try {
			MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchant == null) {
				return new ModelAndView(new RedirectView("/login/login.htm",true,false));
			}
			MerchantBusinessBussinessDto merchantBusiness = new MerchantBusinessBussinessDto();
			merchantBusiness.setMerchantId(merchant.getId());
			merchantBusiness.setSkuId(id);
			merchantBusiness.setBusinessType(SubTypeEnum.HASGOODS.getId());
			favoriteBussinessApi.subscribe(merchantBusiness);
    		return super.addResult("操作成功");
    	}catch (Exception e) {
			LOGGER.error("操作异常",e);
			return this.addError(e.getMessage());
		}
    }

	/**
	 * id: 商品id
	 * 降价通知
	 * @return
	 */
	@RequestMapping("/pricenotify/{id}.json")
	@ResponseBody
	public Object pricenotify(@PathVariable Long id, MerchantBusinessBussinessDto merchantBusiness){
		try {
			Double expectPrice = merchantBusiness.getExpectPrice();
			if (id == null || expectPrice == null) {
				return this.addError("参数异常");
			}

			MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchant == null) {
				return new ModelAndView(new RedirectView("/login/login.htm",true,false));
			}
			merchantBusiness.setSkuId(id);
			merchantBusiness.setMerchantId(merchant.getId());
			merchantBusiness.setBusinessType(SubTypeEnum.DOWNPRICE.getId());
//			merchantBusinessService.subscribe(merchantBusiness);
			favoriteBussinessApi.subscribe(merchantBusiness);
			return super.addResult("操作成功");
		} catch (Exception e) {
			LOGGER.error("操作异常",e);
			return this.addError(e.getMessage());
		}
	}

	/**
	 * 撤销申请注销
	 */
	@RequestMapping("/cancelDropAccount")
	@ResponseBody
	public Object cancelDropAccount() {

		try {
			MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
			if (merchant == null) {
				return new ModelAndView(new RedirectView("/login/login.htm",true,false));
			}
			Long merchantId = merchant.getMerchantId();
			LOGGER.info("申请注销用户merchantId:{}", merchant.getMerchantId());
			if(merchantId == null || merchantId.compareTo(0L) <= 0){
				return this.addError("当前用户未登录！");
			}
			ApiRPCResult apiRPCResult = merchantCancelFreezeLogBusinessApi.cancelDropAccount(merchantId);
			if (apiRPCResult.isFail()) {
				return this.addError(apiRPCResult.getErrMsg());
			}
			return this.addResult(apiRPCResult.getMsg());
		} catch (Exception e) {
			LOGGER.error("操作异常",e);
			return this.addError(e.getMessage());
		}
	}
}
