package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.merchant.bussiness.api.*;
import com.xyy.ec.merchant.bussiness.api.ecp.ecpagreement.EcpAgreementBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.*;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.constants.AgreementStatusConstants;
import com.xyy.ec.pc.model.OemClassify;
import com.xyy.ec.pc.model.OemNavigation;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.DateUtils;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.product.business.api.ProductBusinessApi;
import com.xyy.ec.product.business.api.ecp.productInfo.EcpProductBusinessApi;
import com.xyy.ec.product.business.dto.ProductConditionDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import com.xyy.ec.product.business.dto.product.ProductActivityTag;
import com.xyy.ec.product.business.ecp.out.merchant.api.ProductForMerchantApi;
import com.xyy.ec.product.business.enums.BizFromEnum;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户协议相关接口
 */
@Controller
@RequestMapping("/merchantAgreement")
public class MerchantAgreementController extends BaseController {

    private Logger LOGGER = LoggerFactory.getLogger(MerchantAgreementController.class);

    @Reference(version = "1.0.0",timeout = 3000)
    private AgreementBussinessApi agreementBussinessApi;

    @Reference(version = "1.0.0",timeout = 3000)
    private AgreementPlatBussinessApi agreementPlatBussinessApi;

    @Reference(version = "1.0.0",timeout = 3000)
    private AgreeNavigationBusinessApi agreeNavigationBusinessApi;

    @Reference(version = "1.0.0",timeout = 3000)
    private AgreeClassifyBusinessApi agreeClassifyBusinessApi;

    @Reference(version = "1.0.0",timeout = 3000)
    AgreementMerchantSkuAccountBussinessApi agreementMerchantSkuAccountBussinessApi;

    @Reference(version = "1.0.0",timeout = 10000)
    private AgreementInfBussinessApi agreementInfBussinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0",timeout = 10000)
    private ProductBusinessApi productBusinessApi;

    @Reference(version = "1.0.0")
    private EcpProductBusinessApi ecpProductBusinessApi;

    @Reference(version = "1.0.0")
    private EcpAgreementBusinessApi ecpAgreementBusinessApi;

    @Autowired
    private PcVersionUtils pcVersionUtils;

    @Reference(version = "1.0.0")
    private ProductForMerchantApi productForMerchantApi;

    /**
     * 跳转协议专区
     * @return
     */
    @RequestMapping("/specialArea.htm")
    public ModelAndView toAgreementSpecialArea(Page page, Long platformId, Boolean spread, Model model, HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            if (null == page) {
                page = new Page(1, 20);
            }
            AgreementMerchantRequestBussinessDto agreementMerchantOfRequest = new AgreementMerchantRequestBussinessDto();
            agreementMerchantOfRequest.setPlatformCategory(AgreementStatusConstants.AgreementCategoryEnum.GENERAL.getCategory());
            agreementMerchantOfRequest.setMerchantId(merchant.getId());
            model.addAttribute("merchant", merchant);
            //获取用户已签署的所有协议
            agreementMerchantOfRequest.setExcludeStatusList(Lists.newArrayList(AgreementStatusConstants.AgreementStatusEnum.EFFECTIVE_EXPIRY.getStatus(), AgreementStatusConstants.AgreementStatusEnum.EFFECTIVE_NO.getStatus()));
            Page<AgreementMerchantResponseBussinessDto> agreementMerchantListByPage =new Page<>();
            com.github.pagehelper.Page requestPage=initPageParam(page);
            PageInfo<AgreementMerchantResponseBussinessDto> baseByPage = agreementBussinessApi.getAgreementMerchantForBaseByPage(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            setResultPage(agreementMerchantListByPage,baseByPage);
            if (null != platformId && platformId.longValue() != 0) {
                agreementMerchantOfRequest.setPlatformId(platformId);
            }
            model.addAttribute("spread", spread);
            model.addAttribute("agreementMerchantOfRequest", agreementMerchantOfRequest);
            /**
             * TODO 改调用协议新API
             */
//            PageInfo<AgreementSkuBussinessDto> pageInfo = agreementBussinessApi.getAgreementSkuList(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            PageInfo<AgreementSkuBussinessDto> pageInfo = ecpAgreementBusinessApi.getAgreementSkuList(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            Page<AgreementSkuBussinessDto> skuBussinessDtoPage=new Page<>();
            setResultPage(skuBussinessDtoPage,pageInfo);
            Page<AgreementSkuBussinessDto> agreementSkuPageList = null == merchant || null == agreementMerchantListByPage || CollectionUtils.isEmpty(agreementMerchantListByPage.getRows()) ? new Page() :skuBussinessDtoPage ;
            agreementSkuPageList.setRequestUrl(getRequestUrl(request));
            if(null == agreementSkuPageList || CollectionUtils.isEmpty(agreementSkuPageList.getRows())){
                return new ModelAndView("/merchantAgreementPage/agreement-no.ftl");
            }else{
                if (null != agreementMerchantListByPage && CollectionUtils.isNotEmpty(agreementMerchantListByPage.getRows())) {
                    model.addAttribute("agreementList", agreementMerchantListByPage.getRows());
                }
                model.addAttribute("agreementSkuListPage", agreementSkuPageList);
                return new ModelAndView("/merchantAgreementPage/agreement-commodity.ftl");
            }
        } catch (Exception e) {
            LOGGER.error("跳转协议专区获取数据异常,e="+e);
            return new ModelAndView("/merchantAgreementPage/agreement-no.ftl");
        }
    }

    @RequestMapping("/oem/special/area.htm")
    public Object toNewAgreementSpecialArea(HttpServletRequest request, @RequestParam(value = "platformId", required = false) Long platformId) {
        try {
            ModelMap modelMap = new ModelMap();
            List<OemNavigation> navigationList = Lists.newArrayList();
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            modelMap.put("merchant",merchant);
            Long merchantId = 0L;
            if(null != merchant) {
                merchantId = merchant.getId();
                modelMap.put("merchantId",merchantId);
            }
            String branchCode = getBranchCodeByMerchantId(merchantId);
            if(null == platformId){
                //根据域获取(兼容原来的情况)
                platformId = agreementPlatBussinessApi.getNewPlatFormByBranchCode(branchCode);
            }
            //查询导航列表
            AgreeNavigationBusinessDto navigationDto = new AgreeNavigationBusinessDto();
            navigationDto.setPlatformId(platformId);
            List<AgreeNavigationBusinessDto> list =  agreeNavigationBusinessApi.selectListNavigation(navigationDto);
            List<AgreeNavigationBusinessDto> sortList =  new ArrayList<>();
            if(CollectionUtil.isEmpty(list)) {
                modelMap.put("data",Lists.newArrayList());
                modelMap.put("navigation",Lists.newArrayList());
                return new ModelAndView("/merchantAgreementPage/agreement-newoem.ftl",modelMap);
            }
            //导航排序 升序
            sortList =  list.stream().sorted(Comparator.comparing(AgreeNavigationBusinessDto::getId)).collect(Collectors.toList());
            modelMap.put("navigation",sortList);
            for(AgreeNavigationBusinessDto na : sortList){
                //组装基础导航基础数据
                OemNavigation oemNavigation = new OemNavigation();
                oemNavigation.setNavigationId(na.getId());
                oemNavigation.setNavigationName(na.getNavigationName());
                //查询分类
                AgreeClassifyBusinessDto dto = new AgreeClassifyBusinessDto();
                dto.setNavigationId(na.getId());
                List<AgreeClassifyBusinessDto> clist = agreeClassifyBusinessApi.getClassifyList(dto);
                List<AgreeClassifyBusinessDto> classifyList = Lists.newArrayList();
                List<OemClassify> classifyArrayList = Lists.newArrayList();
                if(CollectionUtil.isNotEmpty(clist)) {
                    //导航下分类排序 升序
                    classifyList = clist.stream().sorted(Comparator.comparing(AgreeClassifyBusinessDto::getSort)).collect(Collectors.toList());
                    for (AgreeClassifyBusinessDto classify : classifyList) {
                        OemClassify oemClassify = new OemClassify();
                        oemClassify.setId(classify.getId());
                        oemClassify.setClassifyName(classify.getClassifyName());
                        oemClassify.setNavigationId(classify.getNavigationId());
                        oemClassify.setPlatformId(classify.getPlatformId());
                        oemClassify.setSort(classify.getSort());
                        //查询商品
                        /**
                         * TODO 改调用新的协议API
                         */
//                        List<AgreementSkuBussinessDto> skuList = agreementBussinessApi.getNewOEMSkuPage(merchantId, (classify.getId().intValue()),platformId.intValue());
                        List<AgreementSkuBussinessDto> skuList = ecpAgreementBusinessApi.getNewOEMSkuPage(merchantId, (classify.getId().intValue()),platformId.intValue());
                        if (CollectionUtil.isNotEmpty(skuList)) {
                            if(merchant.getPriceDisplayFlag() == false) {
                                ProductActivityTag productActivityTagVO = new ProductActivityTag();
                                productActivityTagVO.setTagUrl("");
                                productActivityTagVO.setTagNoteBackGroupUrl("");
                                productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                                for (AgreementSkuBussinessDto product : skuList) {
                                    product.setFob(0d);
                                    product.setUnitPrice(null);
                                    product.setUnitPriceTag(null);
                                    product.setActivityTag(productActivityTagVO);
                                    //毛利
                                    product.setGrossMargin("");
                                    //建议零售价
                                    product.setSuggestPrice(BigDecimal.ZERO);
                                    //控销零售价
                                    product.setUniformPrice(BigDecimal.ZERO);
                                    //对比价
                                    product.setRetailPrice(0.0);
                                }
                            }
                            oemClassify.setSkuList(skuList);
                        }
                        classifyArrayList.add(oemClassify);
                    }
                }
                oemNavigation.setClassifyList(classifyArrayList);
                navigationList.add(oemNavigation);
            }
            modelMap.put("data",navigationList);
            return new ModelAndView("/merchantAgreementPage/agreement-newoem.ftl",modelMap);
        } catch (Exception e) {
            LOGGER.error("获取跳转协议专区获取数据异常,e="+e);
            return new ModelAndView("/merchantAgreementPage/agreement-newoem.ftl");
        }
    }

    /**
     * 跳转OEM协议专区
     *
     * @return
     */
    @RequestMapping(value = "/OEMspecialArea.htm",method = RequestMethod.GET)
    public ModelAndView toOEMAgreementSpecialArea(Page<AgreementSkuBussinessDto> page, Model model, HttpServletRequest request) {
        try {
            LOGGER.info("----------跳转OEM协议专区 开始");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            if (null == page) {
                page = new Page<>(1, 20);
            }
            AgreementMerchantRequestBussinessDto agreementMerchantOfRequest = new AgreementMerchantRequestBussinessDto();
            agreementMerchantOfRequest.setMerchantId(null == merchant ? 0 : merchant.getId());
            agreementMerchantOfRequest.setPlatformCategory(AgreementStatusConstants.AgreementCategoryEnum.OEM.getCategory());
            model.addAttribute("merchant", merchant);
            Page<AgreementSkuBussinessDto> agreementSkuListPage =new Page<>();
            com.github.pagehelper.Page requestPage=initPageParam(page);
            /**
             * TODO 改调用新的协议API
             */
//            PageInfo<AgreementSkuBussinessDto> oemSkuPage = agreementBussinessApi.getOEMSkuPage(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            PageInfo<AgreementSkuBussinessDto> oemSkuPage = ecpAgreementBusinessApi.getOEMSkuPage(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            if(oemSkuPage != null && oemSkuPage.getList() != null) {
                if(merchant.getPriceDisplayFlag() == false) {
                    ProductActivityTag productActivityTagVO = new ProductActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    for (AgreementSkuBussinessDto product : oemSkuPage.getList()) {
                        product.setFob(0d);
                        product.setUnitPrice(null);
                        product.setUnitPriceTag(null);
                        product.setActivityTag(productActivityTagVO);
                        //毛利
                        product.setGrossMargin("");
                        //建议零售价
                        product.setSuggestPrice(BigDecimal.ZERO);
                        //控销零售价
                        product.setUniformPrice(BigDecimal.ZERO);
                        //对比价
                        product.setRetailPrice(0.0);
                    }
                }
            }
            setResultPage(agreementSkuListPage,oemSkuPage);
            agreementSkuListPage.setRequestUrl(getRequestUrl(request));
            model.addAttribute("agreementMerchantOfRequest", agreementMerchantOfRequest);
            model.addAttribute("agreementSkuListPage", agreementSkuListPage);
            LOGGER.info("------------跳转协议专区开始跳转,agreementSkuPageList is {}", JSONObject.toJSONString(agreementSkuListPage));
            return new ModelAndView("/merchantAgreementPage/agreement-oem.ftl");
        } catch (Exception e) {
            LOGGER.error("跳转协议专区获取数据异常,e="+ExceptionUtils.getStackTrace(e));
            return new ModelAndView("/merchantAgreementPage/agreement-no.ftl");
        }
    }

    /**
     * 跳转协议管理
     * @return
     */
    @RequestMapping("index.htm")
    public ModelAndView toMerchantAgreementPage(Page page, AgreementMerchantRequestBussinessDto agreementMerchantOfRequest, HttpServletRequest request, Model model){
        try {
            if (null == page) {
                page = new Page();
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            Long merchantId = merchant.getId();
            if(null == agreementMerchantOfRequest){
                agreementMerchantOfRequest = new AgreementMerchantRequestBussinessDto();
            }
            //处理结束时间到23：59：59,目前页面进能选择日期
            agreementMerchantOfRequest.setShowEndTime(null == agreementMerchantOfRequest.getShowEndTime() ? null : DateUtils.getDayEndTime(agreementMerchantOfRequest.getShowEndTime()));
            agreementMerchantOfRequest.setCreateEndTime(null == agreementMerchantOfRequest.getCreateEndTime() ? null : DateUtils.getDayEndTime(agreementMerchantOfRequest.getCreateEndTime()));
            agreementMerchantOfRequest.setValidEndTime(null == agreementMerchantOfRequest.getValidEndTime() ? null : DateUtils.getDayEndTime(agreementMerchantOfRequest.getValidEndTime()));
            agreementMerchantOfRequest.setStartEndTime(null == agreementMerchantOfRequest.getStartEndTime() ? null : DateUtils.getDayEndTime(agreementMerchantOfRequest.getStartEndTime()));
            agreementMerchantOfRequest.setEndEndTime(null == agreementMerchantOfRequest.getEndEndTime() ? null : DateUtils.getDayEndTime(agreementMerchantOfRequest.getEndEndTime()));

            agreementMerchantOfRequest.setMerchantId(merchantId);
            if(null ==agreementMerchantOfRequest.getSignStatus()){
                //默认查询已签署
                agreementMerchantOfRequest.setSignStatus(AgreementStatusConstants.AgreementSignStatusEnum.SIGN_DONE.getStatus());
            }
            //用户协议简单统计
            Map<String, Object> statisticDataResultMap =  agreementBussinessApi.getAgreementStatisticBaseInfo( merchantId);
            //用户协议分页查询
            com.github.pagehelper.Page requestPage=initPageParam(page);
            Page<AgreementMerchantResponseBussinessDto> agreementMerchantListByPage=new Page<>();
            PageInfo<AgreementMerchantResponseBussinessDto> baseByPage = agreementBussinessApi.getAgreementMerchantForBaseByPage(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            setResultPage(agreementMerchantListByPage,baseByPage);
//            String requestUrl = getRequestUrl(request);
//            if (!requestUrl.contains("?")) {
//                String params = paramToString(request);
//                params = StringUtil.removeParameter(params, "offset");
//                requestUrl = StringUtils.isBlank(params) ? requestUrl : new StringBuffer().append(requestUrl).append("?").append(params).toString();
//            }
            agreementMerchantListByPage.setRequestUrl(this.getRequestUrl(request));

            model.addAttribute("statisticDataResultMap", statisticDataResultMap);
            model.addAttribute("agreementMerchantListByPage", agreementMerchantListByPage);
            model.addAttribute("tab", agreementMerchantOfRequest.getSignStatus());
            model.addAttribute("merchant", merchant);
            model.addAttribute("requestDTO", agreementMerchantOfRequest);
            model.addAttribute("center_menu", "agreement");
            model.addAttribute("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
        } catch (Exception e) {
            LOGGER.error("跳转用户协议管理页面异常,e="+e);
            return new ModelAndView("/merchantAgreementPage/agreement-management.ftl");
        }
        return new ModelAndView("/merchantAgreementPage/agreement-management.ftl");
    }

    /**
     * 获取协议专区用户协议商品分页信息
     * @param page
     * @param merchantId
     * @return
     */
    @RequestMapping("/getAgreementSkuByPage")
    @ResponseBody
    public Object getAgreementSkuByPage(Page page, Long merchantId){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            AgreementMerchantRequestBussinessDto agreementMerchantOfRequest = new AgreementMerchantRequestBussinessDto();
            agreementMerchantOfRequest.setMerchantId(merchantId);
            //获取协议专区背景图 待补全配置项
            String agreementBackGroundUrl = "";
            //获取协议专区用户协议产品分页信息
            com.github.pagehelper.Page requestPage=initPageParam(page);
            /**
             * TODO 改调用新的协议API
             */
//            PageInfo<AgreementSkuBussinessDto> agreementSkuList = agreementBussinessApi.getAgreementSkuList(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            PageInfo<AgreementSkuBussinessDto> agreementSkuList = ecpAgreementBusinessApi.getAgreementSkuList(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            if(agreementSkuList != null && agreementSkuList.getList() != null) {
                if(merchant.getPriceDisplayFlag() == false) {
                    ProductActivityTag productActivityTagVO = new ProductActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    for (AgreementSkuBussinessDto product : agreementSkuList.getList()) {
                        product.setFob(0d);
                        product.setUnitPrice(null);
                        product.setUnitPriceTag(null);
                        product.setActivityTag(productActivityTagVO);
                        //毛利
                        product.setGrossMargin("");
                        //建议零售价
                        product.setSuggestPrice(BigDecimal.ZERO);
                        //控销零售价
                        product.setUniformPrice(BigDecimal.ZERO);
                        //对比价
                        product.setRetailPrice(0.0);
                    }
                }
            }
            Page<AgreementSkuBussinessDto> agreementSkuPageList =new Page<>();
            setResultPage(agreementSkuPageList,agreementSkuList);
            Map<String, Object> resultMap = this.addDataResult("agreementSkuPageList", agreementSkuPageList);
            resultMap.put("agreementBackGroundUrl", agreementBackGroundUrl);
            return resultMap;
        } catch (Exception e) {
            LOGGER.error(String.format("获取用户merchantId: %d协议专区的协议商品分页信息异常", merchantId), e);
            return this.addError(String.format("获取用户merchantId: %d协议专区的协议商品分页信息异常", merchantId));
        }
    }

    /**
     * 获取用户协议管理中基本统计信息
     * @param request
     * @return
     */
    @RequestMapping("/getAgreementStatisticBaseInfo")
    @ResponseBody
    public Object getAgreementStatisticBaseInfo(HttpServletRequest request){

        Long merchantId = 0L;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            if(null == merchant){
                return this.addError("请先登录");
            }
            merchantId = merchant.getId();
            //用户协议简单统计
            Map<String, Object> dataResultMap =  agreementBussinessApi.getAgreementStatisticBaseInfo( merchantId);
            return this.addDataResult("resultMap", dataResultMap);
        } catch (Exception e) {
            LOGGER.error(String.format("获取用户merchantId: %d协议统计信息异常", merchantId), e);
            return this.addError(String.format("获取用户merchantId: %d协议统计信息异常", merchantId));
        }
    }

    /**
     * 获取用户协议分页信息
     * @param page
     * @param agreementMerchantOfRequest
     * @return
     */
    @RequestMapping("/getAgreementMerchantByPage")
    @ResponseBody
    public Object getAgreementMerchantByPage(Page page, AgreementMerchantRequestBussinessDto agreementMerchantOfRequest){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            if (null == page) {
                page = new Page();
            }
            //用户协议分页查询
            com.github.pagehelper.Page requestPage=initPageParam(page);
            Page<AgreementMerchantResponseBussinessDto> agreementMerchantListByPage =new Page<>();
            PageInfo<AgreementMerchantResponseBussinessDto> baseByPage = agreementBussinessApi.getAgreementMerchantForBaseByPage(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            setResultPage(agreementMerchantListByPage,baseByPage);
            return this.addDataResult("agreementMerchantListByPage", agreementMerchantListByPage);
        } catch (Exception e) {
            LOGGER.error("获取用户协议分页信息异常,e="+e);
            return this.addError("获取用户协议分页信息异常");
        }
    }

    @RequestMapping("/detail.htm")
    public ModelAndView toAgreementMerchantDetailPage(Page page, AgreementMerchantRequestBussinessDto agreementMerchantOfRequest, HttpServletRequest request, Model model){
        //用户协议分页查询
        AgreementMerchantResponseBussinessDto agreementMerchantDetail=new AgreementMerchantResponseBussinessDto();
        Page<AgreementSkuBussinessDto> agreementMerchantSkuPage=new Page<>();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            model.addAttribute("merchant",merchant);
            Long merchantId = merchant.getId();
            if(null == agreementMerchantOfRequest){
                agreementMerchantOfRequest = new AgreementMerchantRequestBussinessDto();
            }
            agreementMerchantOfRequest.setMerchantId(merchantId);
            if(null == agreementMerchantOfRequest.getPlatformId()){
                return new ModelAndView(new RedirectView("/merchantAgreementPage/index.htm",true,false));
            }
            agreementMerchantDetail= agreementBussinessApi.getAgreementMerchantDetail(agreementMerchantOfRequest);
            //获取协议专区用户协议产品分页信息
            com.github.pagehelper.Page requestPage=initPageParam(page);
            /**
             * TODO 改调用新的协议API
             */
//            PageInfo<AgreementSkuBussinessDto> pageForDetail = agreementBussinessApi.getMerchantAgreementSkuListByPageForDetail(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            PageInfo<AgreementSkuBussinessDto> pageForDetail = ecpAgreementBusinessApi.getMerchantAgreementSkuListByPageForDetail(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
//            if(pageForDetail != null && pageForDetail.getList() != null){
//                if(merchant.getPriceDisplayFlag() == false) {
//                    ProductActivityTag productActivityTagVO = new ProductActivityTag();
//                    productActivityTagVO.setTagUrl("");
//                    productActivityTagVO.setTagNoteBackGroupUrl("");
//                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
//                    for (AgreementSkuBussinessDto product : pageForDetail.getList()) {
//                        product.setFob(0d);
//                        product.setActivityTag(productActivityTagVO);
//                        //毛利
//                        product.setGrossMargin("");
//                        //建议零售价
//                        product.setSuggestPrice(BigDecimal.ZERO);
//                        //控销零售价
//                        product.setUniformPrice(BigDecimal.ZERO);
//                        //对比价
//                        product.setRetailPrice(0.0);
//                    }
//                }
//            }

            setResultPage(agreementMerchantSkuPage,pageForDetail);

            agreementMerchantSkuPage.setRequestUrl(getRequestUrl(request));
            model.addAttribute("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
        } catch (Exception e) {
            LOGGER.error("跳转协议详情页面异常,e="+e);
        }
        model.addAttribute("agreementMerchantSkuPage", agreementMerchantSkuPage);
        model.addAttribute("agreementMerchant", agreementMerchantDetail);
        model.addAttribute("center_menu", "agreement");
        return new ModelAndView("/merchantAgreementPage/agreement-alone.ftl");
    }

    /**
     * 获取用户协议详情
     * @param agreementMerchantOfRequest
     * @return
     */
    @RequestMapping("/getAgreementMerchantDetail")
    @ResponseBody
    public Object getAgreementMerchantDetail(AgreementMerchantRequestBussinessDto agreementMerchantOfRequest){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            if(null == agreementMerchantOfRequest || null == agreementMerchantOfRequest.getMerchantId() || null == agreementMerchantOfRequest.getPlatformId()){
                return this.addError("必要的参数不能为空");
            }

            //用户协议分页查询
            AgreementMerchantResponseBussinessDto agreementMerchantDetail = agreementBussinessApi.getAgreementMerchantDetail(agreementMerchantOfRequest);
            if(null == agreementMerchantDetail){
                return this.addError(String.format("无法匹配到数据，请确认协议id: %s", agreementMerchantOfRequest.getPlatformId()));
            }
            return this.addDataResult("agreementMerchant", agreementMerchantDetail);
        } catch (Exception e) {
            LOGGER.error("获取用户协议详情信息异常,e="+e);
            return this.addError("获取用户协议详情异常");
        }
    }

    /**
     * 获取用户协议详情中商品分页信息
     * @param page
     * @param agreementMerchantOfRequest
     * @return
     */
    @RequestMapping("/getAgreementSkuByPageForDetail")
    @ResponseBody
    public Object getAgreementSkuByPageForDetail(Page page, AgreementMerchantRequestBussinessDto agreementMerchantOfRequest){
        if(null == agreementMerchantOfRequest || null == agreementMerchantOfRequest.getMerchantId() || null == agreementMerchantOfRequest.getPlatformId()){
            return this.addError("必要的参数不能为空");
        }
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            //获取协议专区用户协议产品分页信息
            Page<AgreementSkuBussinessDto> agreementMerchantSkuPage = new Page<>();
            com.github.pagehelper.Page requestPage = initPageParam(page);
            /**
             * TODO 改调用新的协议API
             */
//            PageInfo<AgreementSkuBussinessDto> pageForDetail = agreementBussinessApi.getMerchantAgreementSkuListByPageForDetail(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            PageInfo<AgreementSkuBussinessDto> pageForDetail = ecpAgreementBusinessApi.getMerchantAgreementSkuListByPageForDetail(page.getOffset(),page.getLimit(), agreementMerchantOfRequest);
            if(pageForDetail != null && pageForDetail.getList() != null) {
                if(merchant.getPriceDisplayFlag() == false) {
                    ProductActivityTag productActivityTagVO = new ProductActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    for (AgreementSkuBussinessDto product : pageForDetail.getList()) {
                        product.setFob(0d);
                        product.setUnitPrice(null);
                        product.setUnitPriceTag(null);
                        product.setActivityTag(productActivityTagVO);
                        //毛利
                        product.setGrossMargin("");
                        //建议零售价
                        product.setSuggestPrice(BigDecimal.ZERO);
                        //控销零售价
                        product.setUniformPrice(BigDecimal.ZERO);
                        //对比价
                        product.setRetailPrice(0.0);
                    }
                }
            }
            setResultPage(agreementMerchantSkuPage,pageForDetail);
            this.addResult("merchant",merchant);
            return this.addDataResult("agreementMerchantSkuPage", agreementMerchantSkuPage);
        } catch (Exception e) {
            LOGGER.error(String.format("获取用户merchantId: %d协议id: %d专区的协议商品分页信息异常", agreementMerchantOfRequest.getMerchantId(), agreementMerchantOfRequest.getPlatformId()), e);
            return this.addError(String.format("获取用户merchantId: %d协议id: %d专区的协议商品分页信息异常", agreementMerchantOfRequest.getMerchantId(), agreementMerchantOfRequest.getPlatformId()));
        }
    }

	@RequestMapping("/signAgreement")
	@ResponseBody
	public Object signAgreement(AgreementMerchantRequestBussinessDto agreementMerchant) {
		try {
		    if(null == agreementMerchant || null == agreementMerchant.getPlatformId()){
		        return this.addError("必要的参数不能为空");
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            agreementMerchant.setMerchantId(merchant.getId());
			//根据商户id和分页参数查询用户协议专区商品数据
			boolean isSign = agreementBussinessApi.signAgreement(agreementMerchant);

			return this.addResult("data", isSign);
		} catch (Exception e) {
			LOGGER.error("签约协议异常 agreementMerchant={}", JSON.toJSONString(agreementMerchant), e);
			return this.addError("签约协议异常");
		}
	}
    /**
     * 品牌协议获取用户协议详情中商品分页信息
     * @param limit 每页数量
     * @param offset  当前页
     * @param platFormId  协议ID
     * @return
     *  /merchantAgreement/brand/special/area.htm
     */
    @RequestMapping("/brand/special/area.htm")
    public Object toNewAgreementSpecialArea(Long platFormId, @RequestParam(name ="offset",defaultValue ="1")Integer offset, @RequestParam(name ="limit",defaultValue ="10")Integer limit, HttpServletRequest request ) {
        try {
            if(offset == 0){
                offset= 1;
            }
            //获取协议专区用户协议产品分页信息
            Page<AgreementSkuBussinessDto> agreementSkuBussinessDtoPage = new Page<>();
            ModelMap modelMap = new ModelMap();
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            modelMap.put("merchant",merchant);
            //获取的厂商协议
            modelMap.put("platFormId",platFormId);
            Long merchantId = null;
            if(null != merchant) {
                merchantId = merchant.getId();
            }
            //获取区域
            String branchCode = getBranchCodeByMerchantId(merchantId);
            //根据域获取
            Map<String, Object> paramMap = new HashMap<>();
            //普通协议
            paramMap.put("platformCategory",0);
//            paramMap.put("status",1); //生效中
            //已发布
            paramMap.put("showStartTime",new Date());
            //未过期
            paramMap.put("expiryStartDate",new Date());
            //区域
            paramMap.put("branchCode",branchCode);
            List<AgreementPlatFormBussinessExtensionDto> listByMap = agreementPlatBussinessApi.selectListByMap(paramMap);
           if(listByMap == null){
               return new ModelAndView("/merchantAgreementPage/agreement-pinpai.ftl",modelMap);
           }
            //获取的厂商协议
            modelMap.put("manufacturer",listByMap);
            /**
             * TODO 改调用新的协议API
             */
//            PageInfo<AgreementSkuBussinessDto> pageInfo = agreementPlatBussinessApi.getNewAgreementSkuPage(merchantId,platFormId,offset, limit);
            PageInfo<AgreementSkuBussinessDto> pageInfo = ecpAgreementBusinessApi.getNewAgreementSkuPage(merchantId,platFormId,offset, limit);
            if(pageInfo != null && pageInfo.getList() != null) {
                if(merchant.getPriceDisplayFlag() == false) {
                    ProductActivityTag productActivityTagVO = new ProductActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    for (AgreementSkuBussinessDto product : pageInfo.getList()) {
                        product.setFob(0d);
                        product.setUnitPrice(null);
                        product.setUnitPriceTag(null);
                        product.setActivityTag(productActivityTagVO);
                        //毛利
                        product.setGrossMargin("");
                        //建议零售价
                        product.setSuggestPrice(BigDecimal.ZERO);
                        //控销零售价
                        product.setUniformPrice(BigDecimal.ZERO);
                        //对比价
                        product.setRetailPrice(0.0);
                    }
                }
            }
            if(pageInfo !=null){
                setResultPage(agreementSkuBussinessDtoPage,pageInfo);
            }
            //获取商品数据
            modelMap.put("data",agreementSkuBussinessDtoPage);
            modelMap.put("requestUrl",getRequestUrl(request));
            return new ModelAndView("/merchantAgreementPage/agreement-pinpai.ftl",modelMap);
        } catch (Exception e) {
            LOGGER.error("获取跳转协议专区获取数据异常,e="+e);
            return new ModelAndView("/merchantAgreementPage/agreement-pinpai.ftl");
        }
    }

    /**
     * 三合一协议获取用户协议详情中商品分页信息
     * @param limit 每页数量
     * @param offset  当前页
     * @param agreementType  协议类型
     * @return
     *  /merchantAgreement/trinity/special/area.htm
     */
    @RequestMapping("/trinity/special/area.htm")
    public Object trinitySpecialArea( @RequestParam(name ="agreementType",defaultValue ="1") Byte agreementType, @RequestParam(name ="offset",defaultValue ="1")Integer offset, @RequestParam(name ="limit",defaultValue ="5")Integer limit, HttpServletRequest request ) {
        ModelMap modelMap = new ModelMap();
        try {
            if(offset == 0){
                offset= 1;
            }
            modelMap.put("requestUrl",getRequestUrl(request));
            modelMap.put("agreementType",agreementType);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            modelMap.put("merchant",merchant);
            if(merchant == null ){
                return new ModelAndView("/trinityAgreement/agreement.ftl");
            }
            Long merchantId = merchant.getId();
            //获取区域
            String branchCode = getBranchCodeByMerchantId(merchantId);
            PageInfo<AgreementForPcAppThemBussinessDto> pageInfo = agreementInfBussinessApi.getAgreementInfForThem(branchCode,merchantId,agreementType,offset,limit);
            if(pageInfo == null ){
                return new ModelAndView("/trinityAgreement/agreement.ftl",modelMap);
            }
            if(pageInfo.getList()!=null){
                Integer pageNum=1;
                Integer pageSize=5;
                //如果就有一页，并且只有一条数据，查询10条商品
                if(pageInfo.getPages() ==1 && pageInfo.getList().size()==1){
                    pageSize=10;
                }
                ProductConditionDTO productConditionDTO = new ProductConditionDTO();
                for (AgreementForPcAppThemBussinessDto a : pageInfo.getList()) {
                    if(a.getSkuIds() != null) {
                        productConditionDTO.setMerchantId(merchantId);
                        productConditionDTO.setSkuIdList(a.getSkuIds());
                        productConditionDTO.setBranchCode(branchCode);
                        productConditionDTO.setPageNum(pageNum);
                        productConditionDTO.setPageSize(pageSize);
                        //接口引用来源：会员
                        productConditionDTO.setBizFrom(BizFromEnum.MEMBER.getValue());
                        LOGGER.info("三合一协议查询商品传参:" + JSON.toJSONString(productConditionDTO));
                        /**
                         * TODO 改调用商品新API
                         */
//            ListSkuSearchData listSkuSearchData = productBusinessApi.findEsProductPageBySkuIdListSort(productConditionDTO);
                        //todo 店铺项目替换接口
                        ListSkuSearchData listSkuSearchData = productForMerchantApi.findProductPageBySkuIdListSort(productConditionDTO);
                        LOGGER.info("三合一协议查询商品返回值:" + JSON.toJSONString(listSkuSearchData));
                        if (listSkuSearchData != null && listSkuSearchData.getSkuDtoList() != null) {
                            if(merchant.getPriceDisplayFlag() == false) {
                                ProductActivityTag productActivityTagVO = new ProductActivityTag();
                                productActivityTagVO.setTagUrl("");
                                productActivityTagVO.setTagNoteBackGroupUrl("");
                                productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                                for (ListProduct product : listSkuSearchData.getSkuDtoList()) {
                                    product.setFob(0d);
                                    product.setUnitPrice(null);
                                    product.setUnitPriceTag(null);
                                    product.setActivityTag(productActivityTagVO);
                                    //毛利
                                    product.setGrossMargin("");
                                    //建议零售价
                                    product.setSuggestPrice(BigDecimal.ZERO);
                                    //控销零售价
                                    product.setUniformPrice(BigDecimal.ZERO);
                                    //对比价
                                    product.setRetailPrice(0.0);
                                }
                            }
                            a.setListProductList(listSkuSearchData.getSkuDtoList());
                        }
                    }
                }
            }
            //获取协议专区用户协议产品分页信息
            Page<AgreementSkuBussinessDto> agreementSkuBussinessDtoPage = new Page<>();
            setResultPageNew(agreementSkuBussinessDtoPage,pageInfo);

            modelMap.put("data",agreementSkuBussinessDtoPage); //获取商品数据
            return new ModelAndView("/trinityAgreement/agreement.ftl",modelMap);
        } catch (Exception e) {
            LOGGER.error("跳转三合一协议专区列表，获取数据异常",e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 三合一协议获取用户协议详情中商品分页信息
     * @param limit 每页数量
     * @param offset  当前页
     * @param agreementType  协议类型
     * @return
     *  /merchantAgreement/trinity/special/areaDetail.htm
     */
    @RequestMapping("/trinity/special/areaDetail.htm")
    @ResponseBody
    public Object trinitySpecialAreaDetail( @RequestParam(name ="agreementType",defaultValue ="1") Byte agreementType, @RequestParam(name ="offset",defaultValue ="1")Integer offset, @RequestParam(name ="limit",defaultValue ="5")Integer limit, HttpServletRequest request ) {
        ModelMap modelMap = new ModelMap();
        try {
            offset += 1;
            modelMap.put("requestUrl",getRequestUrl(request));
            modelMap.put("agreementType",agreementType);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            modelMap.put("merchant",merchant);
            if(merchant == null ){
                modelMap.put("status","success");
                return new ModelAndView("/trinityAgreement/agreement_detail.ftl");
            }
            Long merchantId = merchant.getId();
            //获取区域
            String branchCode = getBranchCodeByMerchantId(merchantId);
            PageInfo<AgreementForPcAppThemBussinessDto> pageInfo = agreementInfBussinessApi.getAgreementInfForThem(branchCode,merchantId,agreementType,offset,limit);
            if(pageInfo == null ){
                modelMap.put("status","success");
                return new ModelAndView("/trinityAgreement/agreement_detail.ftl",modelMap);
            }
            if(pageInfo.getList()!=null){
                Integer pageNum=1;
                Integer pageSize=5;
                //如果就有一页，并且只有一条数据，查询10条商品
                if(pageInfo.getPages() ==1 && pageInfo.getList().size()==1){
                    pageSize=10;
                }
                ProductConditionDTO productConditionDTO = new ProductConditionDTO();
                for (AgreementForPcAppThemBussinessDto a : pageInfo.getList()) {
                    if(a.getSkuIds() != null){
                        productConditionDTO.setMerchantId(merchantId);
                        productConditionDTO.setSkuIdList(a.getSkuIds());
                        productConditionDTO.setBranchCode(branchCode);
                        productConditionDTO.setPageNum(pageNum);
                        productConditionDTO.setPageSize(pageSize);
                        //接口引用来源：会员
                        productConditionDTO.setBizFrom(BizFromEnum.MEMBER.getValue());
                        LOGGER.info("三合一协议查询商品传参:" + JSON.toJSONString(productConditionDTO));
                        /**
                         * TODO 改调用商品新API
                         */
//            ListSkuSearchData listSkuSearchData = productBusinessApi.findEsProductPageBySkuIdListSort(productConditionDTO);
                        //todo 店铺项目替换接口
                        ListSkuSearchData listSkuSearchData = productForMerchantApi.findProductPageBySkuIdListSort(productConditionDTO);
                        if(listSkuSearchData != null && listSkuSearchData.getSkuDtoList() != null){
                            LOGGER.info("三合一协议查询商品返回值:" + JSON.toJSONString(listSkuSearchData));
                            if(merchant.getPriceDisplayFlag() == false) {
                                ProductActivityTag productActivityTagVO = new ProductActivityTag();
                                productActivityTagVO.setTagUrl("");
                                productActivityTagVO.setTagNoteBackGroupUrl("");
                                productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                                for (ListProduct product : listSkuSearchData.getSkuDtoList()) {
                                    product.setFob(0d);
                                    product.setUnitPrice(null);
                                    product.setUnitPriceTag(null);
                                    product.setActivityTag(productActivityTagVO);
                                    //毛利
                                    product.setGrossMargin("");
                                    //建议零售价
                                    product.setSuggestPrice(BigDecimal.ZERO);
                                    //控销零售价
                                    product.setUniformPrice(BigDecimal.ZERO);
                                    //对比价
                                    product.setRetailPrice(0.0);
                                }
                            }
                            a.setListProductList(listSkuSearchData.getSkuDtoList());
                        }
                    }
                }
            }
            //获取协议专区用户协议产品分页信息
            Page<AgreementSkuBussinessDto> agreementSkuBussinessDtoPage = new Page<>();
            setResultPageNew(agreementSkuBussinessDtoPage,pageInfo);
            modelMap.put("data",agreementSkuBussinessDtoPage); //获取商品数据
            modelMap.put("status","success");
            return new ModelAndView("/trinityAgreement/agreement_detail.ftl",modelMap);
        } catch (Exception e) {
            LOGGER.error("跳转三合一协议专区列表，获取数据异常",e);
            modelMap.put("status","failure");
            return new ModelAndView("/trinityAgreement/agreement_detail.ftl",modelMap);
        }
    }

    /**
     * 三合一协议获取用户协议基本信息与商品分页信息
     * @param limit 每页数量
     * @param offset  当前页
     * @param platformId  协议ID
     * @return
     *  /merchantAgreement/trinity/special/sku.htm
     */
    @RequestMapping("/trinity/special/sku.htm")
    public Object trinitySpecialSku(@RequestParam(name ="platformId")Long platformId, @RequestParam(name ="offset",defaultValue ="1")Integer offset, @RequestParam(name ="limit",defaultValue ="10")Integer limit, HttpServletRequest request ) {
        ModelMap modelMap = new ModelMap();
        try {
            if(offset == 0){
                offset= 1;
            }
            modelMap.put("requestUrl",getRequestUrl(request));
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            modelMap.put("merchant",merchant);
            if(merchant == null ){
                return new ModelAndView("/trinityAgreement/agreement_sku.ftl",modelMap);
            }
            Long merchantId = merchant.getId();
            //获取区域
            String branchCode = getBranchCodeByMerchantId(merchantId);
            AgreementForPcAppThemBussinessDto agreement = agreementInfBussinessApi.getAgreementInfForThemDetail(branchCode,merchantId,platformId);
            if(agreement == null){
                return new ModelAndView("/trinityAgreement/agreement_sku.ftl",modelMap);
            }
            modelMap.put("agreement",agreement);
            //获取协议专区用户协议产品分页信息
            if(agreement.getSkuIds()!=null){
                Page<ListProduct> agreementSkuBussinessDtoPage = new Page<>();
                ProductConditionDTO productConditionDTO = new ProductConditionDTO();
                productConditionDTO.setMerchantId(merchantId);
                productConditionDTO.setSkuIdList(agreement.getSkuIds());
                productConditionDTO.setBranchCode(branchCode);
                productConditionDTO.setPageNum(offset);
                productConditionDTO.setPageSize(limit);
                //接口引用来源：会员
                productConditionDTO.setBizFrom(BizFromEnum.MEMBER.getValue());
                LOGGER.info("三合一协议查询商品传参:" + JSON.toJSONString(productConditionDTO));
                /**
                 * TODO 改调用商品新API
                 */
//            ListSkuSearchData listSkuSearchData = productBusinessApi.findEsProductPageBySkuIdListSort(productConditionDTO);
                //todo 店铺项目替换接口
                ListSkuSearchData listSkuSearchData = productForMerchantApi.findProductPageBySkuIdListSort(productConditionDTO);
                if(listSkuSearchData != null && listSkuSearchData.getSkuDtoList() != null){
                    LOGGER.info("三合一协议查询商品返回值:" + JSON.toJSONString(listSkuSearchData));
                    if(merchant.getPriceDisplayFlag() == false) {
                        ProductActivityTag productActivityTagVO = new ProductActivityTag();
                        productActivityTagVO.setTagUrl("");
                        productActivityTagVO.setTagNoteBackGroupUrl("");
                        productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                        for (ListProduct product : listSkuSearchData.getSkuDtoList()) {
                            product.setFob(0d);
                            product.setUnitPrice(null);
                            product.setUnitPriceTag(null);
                            product.setActivityTag(productActivityTagVO);
                            //毛利
                            product.setGrossMargin("");
                            //建议零售价
                            product.setSuggestPrice(BigDecimal.ZERO);
                            //控销零售价
                            product.setUniformPrice(BigDecimal.ZERO);
                            //对比价
                            product.setRetailPrice(0.0);
                        }
                    }
                    agreementSkuBussinessDtoPage.setLimit(limit);
                    agreementSkuBussinessDtoPage.setOffset(offset);
                    agreementSkuBussinessDtoPage.setRows(listSkuSearchData.getSkuDtoList());
                    long page = listSkuSearchData.getCount()%limit;
                    Long pages=listSkuSearchData.getCount()/limit;
                    if(page > 0){
                        pages= pages+1;
                    }
                    agreementSkuBussinessDtoPage.setPageCount(pages.intValue());
                    agreementSkuBussinessDtoPage.setTotal(listSkuSearchData.getCount());
                    agreementSkuBussinessDtoPage.setCurrentPage(listSkuSearchData.getPageNum());
                }
                modelMap.put("data",agreementSkuBussinessDtoPage); //获取商品数据
            }
            return new ModelAndView("/trinityAgreement/agreement_sku.ftl",modelMap);
        } catch (Exception e) {
            LOGGER.error("跳转三合一协议专区商品列表，获取数据异常",e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 三合一协议获取用户协议详情中商品分页信息
     * @param limit 每页数量
     * @param offset  当前页
     * @param platformId  协议ID
     * @return
     *  /merchantAgreement/trinity/special/skuDetail.htm
     */
    @RequestMapping("/trinity/special/skusDetail.htm")
    @ResponseBody
    public Object trinitySpecialskuDetail(@RequestParam(name ="platformId")Long platformId, @RequestParam(name ="offset",defaultValue ="0")Integer offset, @RequestParam(name ="limit",defaultValue ="10")Integer limit, HttpServletRequest request ) {
        ModelMap modelMap = new ModelMap();
        try {
            offset += 1;
            modelMap.put("requestUrl",getRequestUrl(request));
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            modelMap.put("merchant",merchant); //获取商品数据
            if(merchant == null ){
                modelMap.put("status","success");
                return new ModelAndView("/trinityAgreement/skus_detail.ftl");
            }
            Long merchantId = merchant.getId();
            //获取区域
            String branchCode = getBranchCodeByMerchantId(merchantId);
            List<Long> skuIds = agreementInfBussinessApi.getAgreementSkuIds(branchCode,platformId);
            if(skuIds == null){
                modelMap.put("status","success");
                return new ModelAndView("/trinityAgreement/skus_detail.ftl");
            }
            modelMap.put("agreement",skuIds);
            //获取协议专区用户协议产品分页信息
            Page<ListProduct> agreementSkuBussinessDtoPage = new Page<>();
            ProductConditionDTO productConditionDTO = new ProductConditionDTO();
            productConditionDTO.setMerchantId(merchantId);
            productConditionDTO.setSkuIdList(skuIds);
            productConditionDTO.setBranchCode(branchCode);
            productConditionDTO.setPageNum(offset);
            productConditionDTO.setPageSize(limit);
            //接口引用来源：会员
            productConditionDTO.setBizFrom(BizFromEnum.MEMBER.getValue());
            LOGGER.info("三合一协议查询商品传参:" + JSON.toJSONString(productConditionDTO));
            /**
             * TODO 改调用商品新API
             */
//            ListSkuSearchData listSkuSearchData = productBusinessApi.findEsProductPageBySkuIdListSort(productConditionDTO);
            ListSkuSearchData listSkuSearchData = productForMerchantApi.findProductPageBySkuIdListSort(productConditionDTO);
            if(listSkuSearchData != null && listSkuSearchData.getSkuDtoList() != null){
                LOGGER.info("三合一协议查询商品传参:" + JSON.toJSONString(listSkuSearchData));
                if(merchant.getPriceDisplayFlag() == false) {
                    ProductActivityTag productActivityTagVO = new ProductActivityTag();
                    productActivityTagVO.setTagUrl("");
                    productActivityTagVO.setTagNoteBackGroupUrl("");
                    productActivityTagVO.setSkuTagNotes(new ArrayList<>());
                    for (ListProduct product : listSkuSearchData.getSkuDtoList()) {
                        product.setFob(0d);
                        product.setUnitPrice(null);
                        product.setUnitPriceTag(null);
                        product.setActivityTag(productActivityTagVO);
                        //毛利
                        product.setGrossMargin("");
                        //建议零售价
                        product.setSuggestPrice(BigDecimal.ZERO);
                        //控销零售价
                        product.setUniformPrice(BigDecimal.ZERO);
                        //对比价
                        product.setRetailPrice(0.0);
                    }
                }
                agreementSkuBussinessDtoPage.setLimit(limit);
                agreementSkuBussinessDtoPage.setOffset(offset);
                agreementSkuBussinessDtoPage.setRows(listSkuSearchData.getSkuDtoList());
                agreementSkuBussinessDtoPage.setPageCount(listSkuSearchData.getPages());
                agreementSkuBussinessDtoPage.setTotal(listSkuSearchData.getCount());
                agreementSkuBussinessDtoPage.setCurrentPage(listSkuSearchData.getPageNum());
            }
            modelMap.put("merchant",merchant);
            modelMap.put("data",agreementSkuBussinessDtoPage); //获取商品数据
            modelMap.put("status","success");
            return new ModelAndView("/trinityAgreement/skus_detail.ftl",modelMap);
        } catch (Exception e) {
            LOGGER.error("跳转三合一协议专区商品列表，获取数据异常",e);
            modelMap.put("status","failure");
            return new ModelAndView("/trinityAgreement/skus_detail.ftl",modelMap);
        }
    }

    protected void setResultPageNew(Page retPage, PageInfo merchantForBaseByPage) {
        retPage.setTotal(merchantForBaseByPage.getTotal());
        retPage.setCurrentPage(merchantForBaseByPage.getPageNum());
        retPage.setRows(merchantForBaseByPage.getList());
        retPage.setLimit(merchantForBaseByPage.getPageSize());
        retPage.setPageCount(merchantForBaseByPage.getPages());
    }
}
