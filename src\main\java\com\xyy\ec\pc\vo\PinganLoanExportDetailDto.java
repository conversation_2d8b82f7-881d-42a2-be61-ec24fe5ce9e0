package com.xyy.ec.pc.vo;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Builder
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PinganLoanExportDetailDto implements Serializable {
    /**
     * 订单支付时间
     */
    private String payDay;
    /**
     * 平台贴息截止日
     */
    private String subsidyInterestDate;
    /**
     * 是否有原路退款 例：1
     * 0：无
     * 1：有
     */
    private String refund;
    /**
     * 结清状态 例：0
     * 0：未结清
     * 1：已结清
     */
    private String payoffflag;

    /**
     * 商家名称 例：湖北小药药自营旗舰店
     */
    private String receiverName;
    /**
     * YBM订单号 例：YBM20230723102505000001
     */
    private String businessPayNo;
    /**
     * 金额 例：25776.00
     */
    private BigDecimal tradeAmount;
    /**
     * 应还本金 例：980.91
     */
    private String curPrincipalAmount;
    /**
     * 已还本金 例：0.00
     */
    private String actPrincipalAmount;
    /**
     * 剩余应还本金 例：0.00
     * 计算得出：应还本金 - 已还本金
     */
    private String curPrincipalAmountBalance;
    /**
     * 已还利息 例：25.78
     */
    private String actInterestAmount;
    /**
     * 平台贴息(实还贴息) 例：25.56
     */
    private String actSubsidyInterestAmount;
    /**
     * 最后还款日 例：2023-10-23
     */
    private String curRepayDate;
    /**
     * 实际还款日 例：2023-10-23
     */
    private String actRepayDate;

}
