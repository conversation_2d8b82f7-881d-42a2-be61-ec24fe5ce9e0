package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;

public enum AuditStatusEnum {

    UNDER_REVIEW(1, "审核中"),
    PASSED(2, "审核通过"),
    NOT_PASSED(3, "审核不通过"),
    ;

    private int id;
    private String value;

    AuditStatusEnum(int id, String value) {
        this.id = id;
        this.value = value;
    }

    private static Map<Integer, AuditStatusEnum> enumMaps = new HashMap<>();
    public static Map<Integer, String> maps = new HashMap<>();

    static {
        for (AuditStatusEnum e : AuditStatusEnum.values()) {
            enumMaps.put(e.getId(), e);
            maps.put(e.getId(), e.getValue());
        }
    }

    public static String get(int id) {
        return enumMaps.get(id).getValue();
    }

    public String getValue() {
        return value;
    }

    public int getId() {
        return id;
    }

}
