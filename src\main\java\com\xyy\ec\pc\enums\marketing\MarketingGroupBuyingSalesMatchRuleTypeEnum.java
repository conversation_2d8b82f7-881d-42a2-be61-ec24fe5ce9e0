package com.xyy.ec.pc.enums.marketing;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Auther chenshaobo
 * @Date 2024/9/14
 * @Description 拼团销量展示规则结果类型枚举
 * @Version V1.0
 **/
@Getter
@AllArgsConstructor
public enum MarketingGroupBuyingSalesMatchRuleTypeEnum {

    SKU(1, "sku维度"),
    PID(2, "pid维度"),
    MASTER_STANDARD(3, "主标准库维度"),
    DEFAULT_TYPE(4, "默认");

    private Integer type;

    private String name;

    public static Integer validRuleTypeEnum(Integer type) {
        for (MarketingGroupBuyingSalesMatchRuleTypeEnum value : values()) {
            if (value.type.equals(type)) {
                return type;
            }
        }
        return null;
    }

}
