package com.xyy.ec.pc.newfront.controller.exception;

import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.exception.AppException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;


@Slf4j
@RestControllerAdvice("com.xyy.ec.pc.newfront.controller")
public class ExceptionHandel {

    @ExceptionHandler(AppException.class)
    public AjaxResult<Void> handelBizException(AppException appException) {
        log.info("接口异常堆栈信息:", appException);
        return AjaxResult.errResult(appException.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public AjaxResult<Void> handelBizException(Exception exception) {
        log.warn("接口异常堆栈信息:", exception);
        return AjaxResult.errResult("系统开小差了, 请稍后再试吧！");
    }
}
