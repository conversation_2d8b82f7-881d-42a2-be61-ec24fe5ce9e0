package com.xyy.ec.pc.search.ecp.helpers;

import com.xyy.ec.pc.search.ecp.params.PcSearchRecPurchaseQueryParam;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpTerminalTypeEnum;
import com.xyy.ec.search.engine.ecp.params.EcpSearchRecPurchaseQueryParam;

/**
 * {@link EcpSearchRecPurchaseQueryParam} 帮助类
 *
 * <AUTHOR>
 */
public class EcpSearchRecPurchaseQueryParamHelper {

    public static EcpSearchRecPurchaseQueryParam create(PcSearchRecPurchaseQueryParam searchRecPurchaseQueryParam
            , Long accountId, Long merchantId) {
        return EcpSearchRecPurchaseQueryParam.builder().accountId(accountId).merchantId(merchantId)
                .terminalType(EcpTerminalTypeEnum.PC)
                .mainCsuId(searchRecPurchaseQueryParam.getMainCsuId())
                .showRecPurchaseType(searchRecPurchaseQueryParam.getShowRecPurchaseType()).build();
    }

}
