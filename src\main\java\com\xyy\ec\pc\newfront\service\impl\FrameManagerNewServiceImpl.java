package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.api.AppDialogBuinseseApi;
import com.xyy.ec.layout.buinese.dto.CmsDialogDto;
import com.xyy.ec.layout.buinese.dto.CmsDialogTypeDto;
import com.xyy.ec.layout.buinese.ecp.enums.CmsClientTypeEnum;
import com.xyy.ec.layout.buinese.ecp.enums.CmsDialogSceneTypeEnum;
import com.xyy.ec.layout.buinese.ecp.enums.CmsDialogTypeEnum;
import com.xyy.ec.marketing.hyperspace.api.VoucherForCmsApi;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponBaseDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.newfront.service.FrameManagerNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class FrameManagerNewServiceImpl implements FrameManagerNewService {

    @Resource
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0",timeout = 60000)
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private AppDialogBuinseseApi appDialogBuinseseApi;

    @Reference(version = "1.0.0")
    private VoucherForCmsApi voucherForCmsApi;
    @Override
    public AjaxResult<CmsDialogDto> getNewCmsDialog(Integer sceneType) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null || merchant.getId() == null) {
                return AjaxResult.successResultNotData();
            }
            Long merchantId = merchant.getId();
            CmsDialogSceneTypeEnum cmsDialogSceneTypeEnum = CmsDialogSceneTypeEnum.valueOfCustom(sceneType);
            if (cmsDialogSceneTypeEnum == null) {
                return AjaxResult.successResultNotData();
            }
            String branchCode = getBranchCodeByMerchantId(merchantId);
            // TODO
            ApiRPCResult<CmsDialogTypeDto> apiRPCResult = appDialogBuinseseApi.hasNewCmsDialog(merchantId,
                    branchCode, sceneType, CmsClientTypeEnum.PC.getType());
            if (!apiRPCResult.isSuccess()) {
                log.error("【cms】新版首页弹窗，接口hasNewCmsDialog异常, merchantId：{}，sceneType：{},msg:{} ",
                        merchantId, sceneType, apiRPCResult.getMsg());
                return AjaxResult.successResultNotData();
            }
            CmsDialogTypeDto cmsDialogTypeDto = apiRPCResult.getData();
            if (cmsDialogTypeDto == null) {
                return AjaxResult.successResultNotData();
            }
            String pageId = cmsDialogTypeDto.getPageId();
            ApiRPCResult<CmsDialogDto> detailApiRPCResult = appDialogBuinseseApi.getNewCmsDialog(branchCode, pageId);
            if (!detailApiRPCResult.isSuccess()) {
                log.error("【cms】新版首页弹窗，接口getNewCmsDialog异常, merchantId：{}，sceneType：{},pageId:{},msg:{} ",
                        merchantId, sceneType, pageId, apiRPCResult.getMsg());
                return AjaxResult.successResultNotData();
            }
            CmsDialogDto cmsDialogDto = detailApiRPCResult.getData();
            if (cmsDialogDto == null) {
                return AjaxResult.successResultNotData();
            }
            List<Long> couponIds = cmsDialogDto.getCouponIds();
            if (CollectionUtils.isEmpty(couponIds) && Objects.equals(cmsDialogDto.getDialogType(), CmsDialogTypeEnum.COUPON.getType())) {
                return AjaxResult.successResultNotData();
            }
            if (CollectionUtils.isNotEmpty(couponIds) && Objects.equals(cmsDialogDto.getDialogType(), CmsDialogTypeEnum.COUPON.getType())) {
                ApiRPCResult<List<CouponBaseDto>> couponApiRPCResult = voucherForCmsApi.listCouponByIdsWithFilter(merchantId, couponIds);
                if (!couponApiRPCResult.isSuccess()) {
                    log.warn("【cms】弹窗listCouponByIds异常,merchantId:{},pageId:{},couponIds:{},msg:{}", merchantId, pageId, couponIds, apiRPCResult.getMsg());
                    return AjaxResult.successResultNotData();
                }
                List<CouponBaseDto> data = couponApiRPCResult.getData();
                if (CollectionUtils.isEmpty(data)) {
                    return AjaxResult.successResultNotData();
                }
                cmsDialogDto.setCouponDtos(data);
            }
            return AjaxResult.successResult(cmsDialogDto);

        } catch (Exception e) {
            log.error("cms新版首页弹窗异常， sceneType：{} ", sceneType, e);
        }
        return AjaxResult.successResultNotData();
    }

    /**
     * 通过用户id获取用户所在的域编码
     */
    protected String getBranchCodeByMerchantId(Long merchantId){
        String branchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchantId);
        if(StringUtil.isEmpty(branchCode)){
            branchCode = BranchEnum.HUBEI_COUNTRY.getKey();
        }
        return branchCode;
    }
}
