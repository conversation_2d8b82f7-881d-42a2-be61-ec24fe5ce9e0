package com.xyy.ec.pc.cms.utils;

import com.xyy.ec.pc.cms.dto.CmsRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class FeedParamUtils {

    /**
     * 处理分页请求
     *
     * @param cmsRequestDTO
     * @return
     */
    public CmsRequestDTO handleCmsRequestParam(CmsRequestDTO cmsRequestDTO) {
        if (cmsRequestDTO == null) {
            cmsRequestDTO = new CmsRequestDTO();
        }
        // 处理分页信息
        Integer pageNum = Optional.ofNullable(cmsRequestDTO.getPageNum()).orElse(1);
        Integer pageSize = Optional.ofNullable(cmsRequestDTO.getPageSize()).orElse(10);
        cmsRequestDTO.setPageNum(++pageNum);
        cmsRequestDTO.setPageSize(pageSize);
        return cmsRequestDTO;
    }

}
