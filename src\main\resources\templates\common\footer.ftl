<@footer_data footerData>
<div class="foot_top">
    <div class="foot-best-box">
		<div class="foot_best clear">
			<div class="foot_best_item">
				<div class="imgbox"><img class="ficon" src="/static/images/ft1.png"></div>
				<div class="infobox">
					<p class="ftext">带票销售，直配包邮</p>
					<p class="ftext-t">发票保障，满就包邮</p>
				</div>
			</div>
			<div class="foot_best_item">
				<div class="imgbox"><img class="ficon" src="/static/images/ft2.png"></div>
				<div class="infobox">
					<p class="ftext">正品行货，品类齐全</p>
					<p class="ftext-t">百万好药，等你来选</p>
				</div>
			</div>
			<div class="foot_best_item">
				<div class="imgbox"><img class="ficon" src="/static/images/ft3.png"></div>
				<div class="infobox">
					<p class="ftext">专业服务，闪电配送</p>
					<p class="ftext-t">专业物流，准时送达</p>
				</div>
			</div>
			<div class="foot_best_item last">
				<div class="imgbox"><img class="ficon" src="/static/images/ft4.png"></div>
				<div class="infobox">
					<p class="ftext">轻松采购，便捷支付</p>
					<p class="ftext-t">一键采购，便捷无忧</p>
				</div>
			</div>
		</div>
    </div>
	<div class="footbox">
		<ul class="about">
			<#--<li class="title_f"><a href="/helpCenter/about.htm">帮助</a></li>-->
			<#--<li><a href="/helpCenter/web.htm" target="_blank">网站操作流程</a></li>-->
			<#--<li><a href="/helpCenter/app.htm" target="_blank">APP操作流程</a></li>-->
			<#--<li><a href="/helpCenter/returnprocess.htm" target="_blank">售后服务</a></li>-->
            <#--  <li class="title_f"><a href="/helpCenter/about.htm">关于我们</a></li>  -->
            <li class="title_f"><a href="//app.mokahr.com/apply/xyy/28491#/?anchorName=default_banner&sourceToken=" target="_blank">集团官网</a></li>
           <#--   <li><a href="/helpCenter/about.htm" target="_blank">公司介绍</a></li>  -->
            <#--   <li id="xyy_license_footer" style="display: none;" ><a href="/helpCenter/licenseList.htm" >小药药资质</a></li>  -->
            <li><a href="//app.mokahr.com/apply/xyy/28491#/?anchorName=default_joblist&sourceToken=" target="_blank">加入我们</a></li>
		</ul>
		<ul class="about">
			<#--<li class="title_f"><a href="/helpCenter/state.htm">法律服务</a></li>-->
			<#--<li><a href="/helpCenter/privacy.htm" target="_blank">隐私协议</a></li>-->
			<#--<li><a href="/helpCenter/serve.htm" target="_blank">服务协议</a></li>-->
			<#--<li><a href="/helpCenter/state.htm" target="_blank">法律声明</a></li>-->
            <li class="title_f"><a href="/helpCenter/about.htm">新手指南</a></li>
            <li><a href="http://www.ybm100.com/helpCenter/qualification.htm" target="_blank">资质指引</a></li>
            <li><a href="/helpCenter/web.htm" target="_blank">网站操作流程</a></li>
            <li><a href="/helpCenter/app.htm" target="_blank">APP操作流程</a></li>
		</ul>
        <ul class="about">
            <li class="title_f"><a href="/helpCenter/state.htm">法律服务</a></li>
            <li><a href="/helpCenter/privacy.htm" target="_blank">隐私协议</a></li>
            <li><a href="/helpCenter/serve.htm" target="_blank">服务协议</a></li>
            <li><a href="/helpCenter/state.htm" target="_blank">法律声明</a></li>
        </ul>
		<ul class="about">
			<#--<li class="title_f"><a href="/helpCenter/cosu.htm">售后投诉</a></li>-->
			<#--<li>固定电话：181 6338 2701</li>-->
			<#--<li>投诉邮箱：<EMAIL></li>-->
			<#--<li>药监投诉电话：12331</li>-->
            <li class="title_f"><a href="/helpCenter/cosu.htm">售后帮助</a></li>
<#--            <li><a href="javascript:callKf('','${merchant.id}');">在线客服</a></li>-->
            <li><a href="/helpCenter/cosu.htm" target="_blank">售后服务</a></li>
            <#--  <li><a href="/feedback/indexFeedback.html" target="_blank">意见反馈</a></li>  -->
		</ul>
        <ul class="about" style="margin-right:30px;">
            <#--<li class="title_f"><a href="javascript:callKf();">售后客服</a></li>-->
            <li class="title_f" style="font-size: 16px;color:#333;font-weight: bold;">客服热线: 400-0505-111</li>
            <li style="font-size: 14px;color:#333;">咨询时间: 8:00-20:00 周一至周日</li>
            <li style="font-size: 14px;color:#333;">反馈邮箱: <EMAIL></li>
        </ul>
        <div class="gzma fr">
            <img src="/static/images/ybmdyh.png" alt="" />
            <span>关注药帮忙订阅号</span>
        </div>
        <div class="gzma fr">
            <img src="/static/images/gzma.png" alt="" />
            <span>关注药帮忙服务号</span>
        </div>
        <div class="gzma fr">
            <img src="/static/images/xzma.png" alt="" />
            <span>下载药帮忙手机APP</span>
        </div>
<#--        <div class="wangjian">-->
<#--        </div>-->
	</div>
	<div class="footboot">
		<div class="row1 common">
			<#--<a href="/helpCenter/about.htm" target="_blank">关于我们</a>-->
			<#--<span>|</span>-->
			<#--<a href="/helpCenter/contact.htm" target="_blank">联系我们</a>-->
			<#--<span>|</span>-->
			<#--<a href="/helpCenter/about.htm" target="_blank">帮助中心</a>-->
			<#--<span>|</span>-->
			<#--<a href="http://xyy-www.ybm100.com/about.html" target="_blank">公司官网</a>-->

		   <#--<#if (merchant ? exists) && (merchant.registerCode == "XS350000" ||  merchant.registerCode == "XS360000")>-->
		    <#--<span>|</span>-->
		    <#--<a href="/activity/xm_yiliaoqixiexukezheng/ybmActivity.htm"  target="_blank" class="foot-yl">医疗器械经营备案凭证</a>-->
		   <#--</#if>-->
            <#--未登录默认湖北-->
            <#--<#if (merchantId < 1) || (merchant ? exists) && merchant.registerCode == "XS420000">-->
		    <#--<span>|</span>-->
		    <#--<a href="/activity/wh_yiliaoqixiexukezheng/ybmActivity.htm"  target="_blank" class="foot-yl">医疗器械经营备案凭证</a>-->
            <#--</#if>-->
            <#--     湖南       -->
<#--            <#if (merchant ? exists) && merchant.registerCode == "XS430000">-->
<#--                <span>|</span>-->
<#--                <a href="/activity/cs_yiliaoqixie/ybmActivity.htm"  target="_blank" class="foot-yl">医疗器械经营备案凭证</a>-->
<#--            </#if>-->
            <div id="chongqingzgz">
                <div class="row2 common">监管机构：<a href="http://www.nmpa.gov.cn/WS04/CL2042/" target="_blank">国家药品监督管理局&nbsp;&nbsp;</a></div>
                <div class="row2 common common-size"><a href="/branchCertificate/showByName/1101.htm" target="_blank">药品医疗器械网络信息服务备案编号：(京)网药械信息备字（2022）第00246号</a><span>|</span><a href="/branchCertificate/showByName/1100.htm" target="_blank">营业执照号:91110105MA01A6H66K</a><span>|</span><a href="/branchCertificate/showByName/1104.htm" target="_blank">药品网络交易第三方平台备案凭证：（京）网药平台备字[2023]第000008号</a></div>
                <div class="row2 common common-size">
                <a href="/branchCertificate/showByName/1102.htm" target="_blank">医疗器械网络交易服务第三方平台备案凭证:(京)网械平台备字 (2022)第00019号</a>
                <span>|</span>
                <a href="/branchCertificate/showByName/1103.htm" target="_qwblank">网络食品交易第三方平台备案编号：京市监网食备20220010041</a><span>|</span><a href="https://12377.cn/" target="_blank">违法和不良信息举报</a></div>
            </div>
		</div>
		<div class="row2 common-line">
            <#--<a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=42018502002012" target="_blank">鄂公安网备42018502002012号</a><span id="beiantext"><a href="https://beian.miit.gov.cn" target="_blank">鄂ICP备16004053号-1</a> 互联网药品交易服务资格证编号：（鄂）-非经营性-2016-0009 互联网药品交易服务资格证： ${model.jyzgzNo} 经营许可证编号：鄂AA0270434</span>-->
            <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502049946" target="_blank">
                <img src="/static/images/ba.png" style="width: 20px;height: 20px;" />
                京公网安备11010502049946号
            </a>&nbsp;<span id="beiantext" style="color:#666;"><a href="https://beian.miit.gov.cn" target="_blank">京ICP备2022016495号-1</a></span>&nbsp;<span style="color:#666;"><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">电信业务经营许可证编号：合字B2-20220424</a></span>
        </div>
		<div class="row3 common-line" style="color:#999999;">
            Copyright ©2016-2019 ybm100.com All rights reserved. 版权所有:药帮忙
		</div>
	</div>
</div>
<!--取消收藏提示-->
<div id="nopCollect" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade">
    <span>取消收藏</span>
</div>
<!--中包装提示-->
<div id="boxInfo" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade">
	<span>只能以<i id="box-num">200</i> 的倍数购买</span>
</div>
<!--超时登录弹窗-->
<div id="timeout" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade timeout-spe-tc">
    <div class="fltcbox">
        <a href="#" class="close-btn"><img src="/static/images/cha.png" alt=""></a>
        <div class="loginbox">
            <!-- 标题  有错误提示时，标题隐藏-->
            <ul class="l-title ">
                <li class="phonelogin">账户登录</li>
            </ul>

            <!--错误提示-->
            <div class="err-box">
                <#if errorMsg != null>
                    <input type="hidden" value="${errorMsg}" id="error">
                <div class="errinfo">
                <#else>
                <div class="errinfo noshow">
                </#if>
                    <img src="/static/images/cuowutishi.png" class="errorimg"> <span class="wenantishi">账号不存在！</span>
                </div>
            </div>

            <!--账号登录-->
            <div class="l-con">
                <form class="sui-form form-horizontal sui-validate" action="" id="loginForm" method="post">
                    <input type="hidden" name = "redirectUrl" id="redirectUrl" value=""/>
					<div class="control-group">
                        <!-- <label for="inputEmail" class="control-label">手机号：</label>-->
                        <div class="controls">
                            <div class="spediv">
                                <img src="/static/images/ren.png" alt="">
                                <input type="text" id="inputPhone" class="inputPhone" name="name" placeholder="请输入手机号" >
                            </div>
                        </div>
                    </div>
                    <div class="control-group jiange">
                        <!--  <label for="inputPassword" class="control-label">密码：</label>-->
                        <div class="controls">
                            <div class="spediv">
                                <img src="/static/images/suo.png" alt="">
                                <input type="password" id="inputPassword" class="inputPassword" name="password" placeholder="密码"  title="密码">
                            </div>
                        </div>
                    </div>

                    <div class="control-group">
                        <div class="controls">
                            <button type="button" class="sui-btn btn-primary" onclick="check_timeout(this)" >登录</button>
                        </div>
                    </div>

                    <div class="wrapbox">
                        <label class="checkbox-pretty inline checked" id="remember">
                            <input type="checkbox" checked="checked"><span>记住密码</span>
                        </label>
                        <a href="/login/forgetPassword.htm" class="fr">忘记密码？</a>
                    </div>


                </form>
                <div class="bot-info">
                    <div class="wx-login-content">
                        <div>
                            <span class="vx-click" onclick="openWeixin()" >
                                <img src="../static/images/wx-login.png" alt="">
                                微信登录
                            </span> 
                        </div>
                    </div>
                    <div style="float: left;">
                        还没有账号，立即去
                        <a href="/new/register">注册</a>
                    </div>
<#--                    <a href="/login/register.htm">注册</a>-->
                </div>
            </div>
        </div>
        <div class="vx-login">
            <div class="login-switch" onclick="changeLogin()">手机密码登录</div>
            <div class="wx-box">
                <iframe id="login_container"  height="400" style="height:400px" frameborder="0">
            
                </iframe>
            </div>
        </div>
    </div>
</div>

<!--活动节左侧导航-->
<!--左边导航-->
<div class="common-md-box">
    <!--主会场链接-->
    <a href="/activity/anniversary.htm?id=106&moduleCategoryId=149&version=3" target="_blank" class="md-index tyong "></a>
    <!--领券更省心-->
    <a href="/activity/yhq.html" target="_blank" class="md-a1 tyong "></a>
    <!--爆款折上折-->
    <a href="/activity/bkzc.html" target="_blank" class="md-a2 tyong"></a>
    <!--独家高毛-->
    <a href="/activity/events.htm?moduleId=398&activityId=108&activityTitle=独家高毛返利&type=20170913-gaomao&limit=10&offset=1" target="_blank" class="md-a3 tyong"></a>
    <#if (.now?date gt "2017-10-09 00:00:00"?date("yyyy-MM-dd HH:mm:ss"))>
        <!--整点秒杀-->
        <a href="/index.htm#miaoshatop" target="_blank" class="md-a4 tyong"></a>
        <!--超值套餐-->
        <a href="/activityPackage/index.html?effectiveStatus=1&offset=0&type=1" target="_blank" class="md-a5 tyong"></a>
    <#else>
        <!--整点秒杀-->
        <a href="javascript:void(0);" onclick="showMsTC()" class="md-a4 tyong"></a>
        <!--超值套餐-->
        <a href="/activity/10/activity.htm"  class="md-a5 tyong"></a>
    </#if>
    <!--抗感冒专场-->
    <a href="/activity/events.htm?moduleId=707&activityId=109&activityTitle=抗感冒专场&type=common" target="_blank" class="md-a6 tyong"></a>
    <!--消化系统专场-->
    <a href="/activity/events.htm?moduleId=708&activityId=110&activityTitle=消化系统专场&type=common" target="_blank" class="md-a7 tyong"></a>
    <!--呼吸系统专场-->
    <a href="/activity/events.htm?moduleId=709&activityId=111&activityTitle=呼吸系统&type=common" target="_blank" class="md-a8 tyong"></a>
    <!--心脑血管专场-->
    <a href="/activity/events.htm?moduleId=710&activityId=112&activityTitle=心脑血管专场&type=common" target="_blank" class="md-a9 tyong"></a>
    <!--滋补保健专场-->
    <a href="/activity/events.htm?moduleId=712&activityId=114&activityTitle=滋补保健&type=common" target="_blank" class="md-a10 tyong"></a>
    <!--皮肤外用专场-->
    <a href="/activity/events.htm?moduleId=711&activityId=113&activityTitle=皮肤外用专场&type=common" target="_blank" class="md-a11 tyong"></a>
    <!--江中满减-->
    <a href="/activity/109jiangzhong.html" target="_blank" class="md-a12 tyong"></a>
    <!--华润三九满减-->
    <a href="/activity/events.htm?moduleId=727&activityId=123&activityTitle=华润三九品牌专场&type=common" target="_blank" class="md-a13 tyong"></a>
    <!--九芝堂满减-->
    <a href="/activity/events.htm?moduleId=730&activityId=126&activityTitle=九芝堂品牌专场&type=common" target="_blank" class="md-a14 tyong"></a>
    <!--先声满减-->
    <a href="/activity/events.htm?moduleId=735&activityId=131&activityTitle=先声品牌专场&type=common" target="_blank" class="md-a15 tyong"></a>
    <!--广药集团满减-->
    <a href="/activity/events.htm?moduleId=725&activityId=121&activityTitle=广药品牌专场&type=common" target="_blank" class="md-a16 tyong"></a>
    <!--天士力满减-->
    <a href="/activity/events.htm?moduleId=734&activityId=130&activityTitle=天士力品牌专场&type=common" target="_blank" class="md-a17 tyong"></a>
    <!--更多品牌-->
    <a href="/activity/initActivity.htm?id=115&moduleCategoryId=719&version=3&activityType=events-20170517-neiye" target="_blank" class="md-a18 tyong"></a>
</div>

<div id="msfc" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static" class="sui-modal hide fade">
    <span>活动尚未开始，敬请期待</span>
</div>

<script>
    $(function () {
        $.ajax({
            type: 'get',
            cache: false,
            url: '/helpCenter/checkLicenseStatus.json',
            dataType: 'json',
            success: function(data) {
                if (data == 1) {
                    $("#xyy_license_footer").show();
                }
            },
            error: function(XMLHttpRequest, textStatus, errorThrown){
               console.log("网络异常");
            }
        });
    })
</script>
<script>
    function changeLogin(){
        $('.loginbox').show()
        $('.vx-login').hide()
    }
    function openWeixin(){
        $('.loginbox').hide()
        $('.vx-login').show()
        $.ajax({
            url: "/login/wechat/getLoginUrl",
            type : "GET",
            dataType: "json",
            success: function (res) {
                if (res.code == 1000) {
                    var  imageUrl =  res.data.url
                    $('#login_container').attr('src', imageUrl);
                } else {
                    $.alert({
                        title: '提示',
                        body: res.msg
                    });
                }
            },
            error: function () {
                $.alert({
                    title: '提示',
                    body: '网络异常，请稍后再试！'
                });
            }
        })
    }
    $(function(){
        //获取底部证书数据
        // $.ajax({
        //     url:"/branchCertificate/listByBranchCode",
        //     // "data":{"mobileNumber" : form.inputPhone.value,"password":$('#inputPassword').val()},
        //     type:"POST",
        //     dataType:"JSON",
        //     success:function(data){
        //         var result = data.data || [];
        //         if(result.length > 0){
        //             var txt =  '<div class="row2 common">';
        //             txt += '监管机构：<a href="http://www.nmpa.gov.cn/WS04/CL2042/" target="_blank">国家药品监督管理局&nbsp;&nbsp;</a>';
        //             // if(result[result.length - 1].sort === 999){
        //             //     var lastValue = result.pop();
        //             //     // txt += '监管机构：<a href="http://www.nmpa.gov.cn/WS04/CL2042/" target="_blank">国家药品监督管理局&nbsp;&nbsp;</a>';
        //             //     txt += '<a href="'+lastValue.jumpUrl+'" target="_blank">'+lastValue.code+'</a>';
        //             //     txt += '</div>';
        //             // }
        //             if(result.length > 3){
        //                 var arr = [];
        //                 for(var i = 0;i < result.length;i += 3){
        //                     arr.push(result.slice(i,i+3));
        //                 }
        //                 for(var j = 0;j < arr.length;j++){
        //                     txt +=  '<div class="row2 common common-size">';
        //                     for(var k = 0;k < arr[j].length;k++){
        //                         if (arr[j][k].jumpUrl != '') {
        //                             if(k > arr[j].length - 3) {
        //                                 txt += '<span>|</span>';
        //                             }
        //                             txt += '<a href="'+arr[j][k].jumpUrl+'" target="_blank">'+arr[j][k].type+':'+arr[j][k].code+'</a>';
        //
        //                         }
        //                     }
        //                     txt += '</div>';
        //                 }
        //             }else{
        //                 txt +=  '<div class="row2 common common-size">';
        //                 for(var s = 0;s < result.length;s++){
        //                     if (result[s].jumpUrl != '') {
        //                         if(s > result.length - 3){
        //                             txt+='<span>|</span>';
        //                         }
        //                         txt += '<a href="'+result[s].jumpUrl+'" target="_blank">'+result[s].type+':'+result[s].code+'</a>';
        //                     }
        //                 }
        //                 txt += '</div>';
        //             }
        //             $('#chongqingzgz').append(txt);
        //         }
        //     }
        // });
        var branchCode = $('#branchCode').val();
        // if(branchCode){
        //     if(branchCode == 'XS500000'){
        //         var txt =  '<div class="row2 common">';
        //         txt+='<a href="/activity/businesslicense/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91500107MA5YQ1DN2N</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/drugbusiness/ybmActivity.htm" target="_blank">药品经营许可证证号：渝*********</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/qualitycertificate/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：CQ09-Aa-20180724</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/yiliaoqixie/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：渝08食药监械经营备20180081号</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/foodbusiness/ybmActivity.htm" target="_blank">食品经营许可证编号：JY15001080108843</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/internetdrug/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：（渝）-非经营性-2018-0020</a>';
        //         txt+='</div>';
        //         $('#chongqingzgz').empty();
        //         $('#chongqingzgz').append(txt);
        //         $('.foot-yl').hide();
        //         $('.foot-yl').prev('span').hide();
        //         $('#beiantext').html('鄂ICP备16004053号-1');
        //     }else if(branchCode == 'XS420000'){
        //         var txt =  '<div class="row2 common">';
        //         txt+='<a href="/activity/wh_yingyezhizhao/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91420112MA4KLGHW01</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/wh_yaopinjingyingxukezheng/ybmActivity.htm" target="_blank">药品经营许可证证号：鄂AA0270434</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/wh_yaopinjingyingzhiliangguanli/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：HB01-Aa-20160043</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/wh_yiliaoqixie/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：鄂汉食药监械经营备2017HP004号</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/wh_shipinjingyingxukezheng/ybmActivity.htm" target="_blank">食品经营许可证编号：JY14201180022698</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/wh_hulianwangyaopinxinxizige/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(鄂)-非营利性-2016—0009</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/wh_yiliaoqixiexukezheng/ybmActivity.htm" target="_blank">医疗器械经营许可证编号：鄂汉食药监械经营许20161115号</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/wh_hlwyaopinjiaoyifuwuzigezheng/ybmActivity.htm" target="_blank">互联网药品交易服务资格证书编号：鄂B20160004</a>';
        //         txt+='</div>';
        //         $('#chongqingzgz').empty();
        //         $('#chongqingzgz').html(txt);
        //         $('#beiantext').html('鄂ICP备16004053号-1');
        //     }else if(branchCode == 'XS430000'){
        //         var txt =  '<div class="row2 common">';
        //         txt+='<a href="/activity/cs_yingyezhizhaofuben/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91430105MA4PD27G9N</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/cs_yaopinjingyingxukezheng/ybmActivity.htm" target="_blank">药品经营许可证证号：湘AA7310512</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/cs_yaopinjingyingzhiliangguanli/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：HN01-Aa-20180059</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/cs_yiliaoqixie/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：湘长食药监械经营备2018G2048号</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/cs_shipinjingyingxukezheng/ybmActivity.htm" target="_blank">食品经营许可证编号：JY14301020345268</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/cs_hulianwangyaopinxinxizige/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：（湘）—经营性—2018—0017</a>';
        //         txt+='</div>';
        //         $('#chongqingzgz').empty();
        //         $('#chongqingzgz').html(txt);
        //         $('.foot-yl').hide();
        //         $('.foot-yl').prev('span').hide();
        //         $('#beiantext').html('鄂ICP备16004053号-1');
        //     }else if(branchCode == 'XS330000'){
        //         var txt =  '<div class="row2 common">';
        //         txt+='<a href="/activity/hz_yingyezhizhao/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91330110MA2CCJE32Y</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/hz_yaopinjingyingxukezheng/ybmActivity.htm" target="_blank">药品经营许可证证号：浙AA5710173</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/hz_yaopinjingyingzhiliangguanli/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：A-ZJ18-022</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/hz_yiliaoqixie/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：浙杭食药监械经营备20183346号</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/hz_shipinjingyingxukezheng/ybmActivity.htm" target="_blank">食品经营许可证编号：JY13301810071232</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/hz_hulianwangyaopinxinxizige/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(浙)-经营性-2018-0043</a>';
        //         txt+='</div>';
        //         $('#chongqingzgz').empty();
        //         $('#chongqingzgz').html(txt);
        //         $('.foot-yl').hide();
        //         $('.foot-yl').prev('span').hide();
        //         $('#beiantext').html('鄂ICP备16004053号-1');
        //     }else if(branchCode == 'XS350000'){
        //         var txt =  '<div class="row2 common">';
        //         txt+='<a href="/activity/xm_yingyezhizhao/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91350200MA3287U59C</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/xm_yaopinjingyingxukezheng/ybmActivity.htm" target="_blank">药品经营许可证证号：闽AA5920342</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/xm_yaopinjingyingzhiliangguanli/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：FJ02-Aa-20190002</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/xm_yiliaoqixie/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：闽夏食药监械经营备20193008号</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/xm_shipinjingyingxukezheng/ybmActivity.htm" target="_blank">食品经营许可证编号：JY13502110200418</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/xm_hulianwangyaopinxinxizige/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(闽)-非经营性-2019—0011</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/xm_yiliaoqixiexukezheng/ybmActivity.htm" target="_blank">医疗器械经营许可证编号：闽夏食药监械经营许20193005号</a>';
        //         txt+='</div>';
        //         $('#chongqingzgz').empty();
        //         $('#chongqingzgz').html(txt);
        //         // $('.foot-yl').hide();
        //         // $('.foot-yl').prev('span').hide();
        //         $('.foot-yl').attr('href','/activity/xm_yiliaoqixiexukezheng/ybmActivity.htm');
        //         $('#beiantext').html('鄂ICP备16004053号-1');
        //     }else if(branchCode == 'XS370000'){
        //         var txt =  '<div class="row2 common">';
        //         txt+='<a href="/activity/businesslicense_shandong/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91370102780630133F</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/drugbusiness_shandong/ybmActivity.htm" target="_blank">药品经营许可证证号：鲁*********</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/qualitycertificate_shandong/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：SD01-Aa-20190015</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/internetdrug_shandong/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(鲁)-经营性-2019-0006</a>';
        //         txt+='</div>';
        //         $('#chongqingzgz').empty();
        //         $('#chongqingzgz').html(txt);
        //         $('.foot-yl').hide();
        //         $('.foot-yl').prev('span').hide();
        //         $('#beiantext').html('鄂ICP备16004053号-1');
        //     }else if(branchCode == 'XS140001'){
        //         var txt =  '<div class="row2 common">';
        //         txt+='<a href="/activity/businesslicense_shanxi/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91149900MA0KLWLK4K</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/drugbusiness_shanxi/ybmActivity.htm" target="_blank">药品经营许可证证号：晋*********</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/qualitycertificate_shanxi/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：AA-SX-0183</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/ty_hulianwangyaopinxinxizige/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(晋)-非经营性-2019-0021</a>';
        //         txt+='</div>';
        //         $('#chongqingzgz').empty();
        //         $('#chongqingzgz').html(txt);
        //         $('.foot-yl').hide();
        //         $('.foot-yl').prev('span').hide();
        //         $('#beiantext').html('鄂ICP备16004053号-1');
        //     }else if(branchCode == 'XS360000'){
        //         var txt =  '<div class="row2 common">';
        //         txt+='<a href="/activity/businesslicense_jiangxi/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91360106MA38ANUN6M</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/drugbusiness_jiangxi/ybmActivity.htm" target="_blank">药品经营许可证证号：赣*********</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/qualitycertificate_jiangxi/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：A-JX19-40N</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/medicalrecord_jiangxi/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：赣南械经营备20190007号</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/foodbusiness_jiangxi/ybmActivity.htm" target="_blank">食品经营许可证编号：JY13601210072719</a>';
        //         txt+='<span>|</span>';
        //         txt+='<a href="/activity/internetdrug_jiangxi/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(赣)-非经营性-2019-0017</a>';
        //         txt+='</div>';
        //         txt+='<div class="row2 common">';
        //         txt+='<a href="/activity/medicalmanagement_jiangxi/ybmActivity.htm" target="_blank">医疗器械经营许可证编号：赣南械经营许20190029号</a>';
        //         txt+='</div>';
        //         $('#chongqingzgz').empty();
        //         $('#chongqingzgz').html(txt);
        //         // $('.foot-yl').hide();
        //         // $('.foot-yl').prev('span').hide();
        //         $('.foot-yl').attr('href','/activity/medicalmanagement_jiangxi/ybmActivity.htm');
        //         $('#beiantext').html('鄂ICP备16004053号-1');
        //     }else {
        //         $('#chongqingzgz').empty();
        //         $('#beiantext').html('鄂ICP备16004053号-1');
        //     }
        // }

         // $('#footer .wangjian').html('<div><a href="http://whgswj.whhd.gov.cn:8089/whwjww/indexquery/indexqueryAction!dizview.dhtml?chr_id=8367f0ac6afc7ae374ae15205211dccc&amp;bus_ent_id=442010000000579583&amp;bus_ent_chr_id=43e1bcfe52714ec1b8397dd57d9e4f8c" target="_blank"><img src="http://whgswj.whhd.gov.cn:8089/whwjww/images/govIcon.gif" width="100" height="130" title="武汉网监电子标识" alt="武汉网监电子标识" border="0"></a></div>')
        // $('#footer .wangjian').html('<div><a href="http://wljg.scjgj.wuhan.gov.cn:80/whwjww/indexquery/indexqueryAction!dizview.dhtml?chr_id=8367f0ac6afc7ae374ae15205211dccc&bus_ent_id=442010000000579583&bus_ent_chr_id=43e1bcfe52714ec1b8397dd57d9e4f8c" target="_blank"><img src="http://wljg.scjgj.wuhan.gov.cn:80/whwjww/images/govIcon.gif" width="100" height="130" title="武汉网监电子标识" alt="武汉网监电子标识" border="0"></a></div>')

    })
    function login(){
        if($('#remember').hasClass("checked")){
            if($('#inputPhone').val() != '' && $('#inputPassword').val() != ''){
                setCookie("inputPhone",$('#inputPhone').val(),24*7,"/");
                var loginPwd = $('#inputPassword').val();
                var cookieLoginPwd =  getCookieValue('inputPassword');
                if(!loginPwd.match(/^([a-fA-F0-9]{32})$/)){
                    setCookie("inputPassword",$.md5(loginPwd),24*7,"/");
                    $('#inputPassword').val($.md5(loginPwd));
                }
            }
        }

        $("#loginForm").submit();
    }

    /*表单验证*/
    function check_timeout(){
    	var form = $("#loginForm")[0];
        if(form.inputPhone.value=='') {
            form.inputPhone.focus();
            $(".l-title").css("display","none");
            $(".err-box").css("display","block");
            $(".wenantishi").text("手机号不能为空！");
            return false;
        }
        if(!(/^1\d{10}$/.test(form.inputPhone.value))){
            form.inputPhone.focus();
            $(".l-title").css("display","none");
            $(".err-box").css("display","block");
            $(".wenantishi").text("请填写正确的手机号码！");
            return false;
        }
        if(form.inputPassword.value==''){
            $(".wenantishi").text("密码不能为空！");
            $(".l-title").css("display","none");
            $(".err-box").css("display","block");
            form.inputPassword.focus();
            return false;
        }

        var loginPwd1 = $('#inputPassword').val();
        // $('#inputPassword').val($.md5(loginPwd1));
        //记住密码按钮
        if($('#remember').hasClass("checked")){
            if($('#inputPhone').val() != '' && $('#inputPassword').val() != ''){
                setCookie("inputPhone",$('#inputPhone').val(),24*7,"/");
                var loginPwd = $('#inputPassword').val();
                var cookieLoginPwd =  getCookieValue('inputPassword');
                if(!loginPwd.match(/^([a-fA-F0-9]{32})$/)){
                    setCookie("inputPassword",loginPwd,24*7,"/");
                    $('#inputPassword').val($.md5(loginPwd));
                }
            }
        }
        //调用ajax提交登录
        $.ajax({
            url:"/login/login.json",
            "data":{"mobileNumber" : form.inputPhone.value,"password":$('#inputPassword').val()},
            type:"POST",
            dataType:"JSON",
            success:function(data){
                //console.info('----debug ', data);
                if (data.status === "success") {
                    var $data = data['data'];
                    var $shopCount = $data['shopCount'];
                    var $merchantId = $data['merchantId'];
                    var $isAudit = $data['isAudit'];
                    var $redirectUrl = $('#redirectUrl').val();
                    webSdk.track('pc_action_login', {
                        user_id: $merchantId,
                    });
                    $("#timeout").modal('hide');
                    if ($shopCount == 1) {
                     if ($isAudit == true) {
                        location.reload(true);
                     } else {
                        if ($redirectUrl) {
                            window.location='/newstatic/#/register/selectLoginShop?redirectUrl='+encodeURIComponent($redirectUrl);
                        } else {
                            window.location='/newstatic/#/register/selectLoginShop';
                        }
                     }
                    } else if ($shopCount < 1) {
                        window.location='/newstatic/#/register/connectPharmacy';
                    } else if ($shopCount > 1) {
                        if ($redirectUrl) {
                            window.location='/newstatic/#/register/selectLoginShop?redirectUrl='+encodeURIComponent($redirectUrl);
                        } else {
                            window.location='/newstatic/#/register/selectLoginShop';
                        }
                    } else {
                        location.reload(true);
                    }
                }else{
                    $(".l-title").css("display","none");
                    $(".err-box").css("display","block");
                    $(".wenantishi").text(data.errorMsg);
                }
            }
        });
        return false;
    }

    function showMsTC(){
//        $("#msfc").modal('show');
//        setTimeout(function(){$("#msfc").modal('hide');},1000)
        $.alert({
            title: '提示',
            body: '活动尚未开始,敬请期待'
        });
    }

    function getRemember(){
        var loginName = getCookieValue('inputPhone');
        var loginPwd = getCookieValue('inputPassword');
        if(loginName != '' && loginPwd != ''){
            $('#inputPhone').val(loginName);
            $('#inputPassword').val(loginPwd);
        }
    }

    $(document).ready(function() {
        getRemember();
    });

</script>

<#if model.statistics==2>
    <div style="display: none;">
        <script>
            /* 添加百度统计 */
            var _hmt = _hmt || [];
            (function() {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?9bc5b736851e4fc1eee311b5580c1fd4";
                var s = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(hm, s);
            })();
        </script>
    </div>
<#else>
    <div style="display: none;">
        <script>
            /* 添加百度统计 */
            var _hmt = _hmt || [];
            (function() {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?9f4bfe0c69e174e92f282183ee72bed2";
                var s = document.getElementsByTagName("script")[0];
                s.parentNode.insertBefore(hm, s);
            })();
        </script>
    </div>
</#if>
</@footer_data>
<!--<div class="foot_bottom">
	<h2><a href="https://beian.miit.gov.cn" target="_blank">鄂ICP备16004053号-1</a> 互联网药品交易服务资格证编号：（鄂）-非经营性-2016-0009 互联网药品交易服务资格证： 鄂B20160004 经营许可证编号：鄂AA0270434 </h2>
	<h2>Copyright ©2016 ybm100.com All rights reserved. 版权所有:药帮忙</h2>

	Copyright©2012-2016&nbsp;武汉小药药医药科技有限公司&nbsp;027-81318588 &nbsp;版权所有&nbsp;粤ICP备XXXXXXXX-3
</div>-->
