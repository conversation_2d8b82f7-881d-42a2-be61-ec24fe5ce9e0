<!DOCTYPE HTML>
<html>

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<title>采购单</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
		<#include "/common/common.ftl" />
		<link rel="stylesheet" href="/static/css/order.css?t=${t_v}" />
		<style>
			/*小屏幕样式*/
			.smallDev{
				max-width:100%;
				/*height: 152px;*/
			}
			.sui-tooltip{
				border:1px solid #eee;
                border-radius:8px;
			}
            .sui-tooltip.bottom .tooltip-arrow, .tooltip-only-arrow.bottom .tooltip-arrow{
                border-bottom-color: #eee;
			}
			.tooltip-inner{
                line-height: 25px;
			}
            .sui-tooltip.default .tooltip-inner, .sui-tooltip.normal .tooltip-inner, .sui-tooltip.confirm .tooltip-inner{
                color:#333;
			}

		</style>
        <script type="text/javascript">
            var ctx="/static";
            var cdnProduct="${cdnProduct}";
        </script>
		<#if errorMsg>
        <script type="text/javascript">
            $(function() {
                <#if errorType??>
                    $.alert({"title":"提示：","body":"${errorMsg}", okHidden:function () {
                        location.href = "/static/merchant/center/license/findLicenseCategoryInfo.htm";
                    }});
				<#else>
                <!-- $.alert({"title":"提示：","body":"${errorMsg}"}); -->
                <!-- 检查 errorMsg 是否包含 "您已申请账号注销" -->
                if ("${errorMsg}".includes("您已申请账号注销")) {
                    $.confirm({
                        body: "您已申请注销，暂无法下单。撤销申请后，可继续下单，是否撤销申请？",
                        cancelBtn: "否",
                        okBtn: "是",
                        okHidden: function () {
                            $.ajax({
                                url: "/merchant/merchantBusiness/cancelDropAccount",
                                type: "POST",
                                dataType: "json",
                                success: function(result) {
                                    if (result.status == "success") {
                                        alert("已撤销");
                                    } else {
                                        $.alert(result.errorMsg);
                                    }
                                }
                            });
                        }
                    });
                } else {
                    // 默认情况，直接显示 errorMsg
                    $.alert({
                        "title": "提示：",
                        "body": "${errorMsg}"
                    });
                }
				</#if>
            });
        </script>
		</#if>
	</head>

	<body>
		<div class="container">
            <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>
            <input type="hidden" id="imgSrc" name="imgSrc" value="${productImageUrl}"/>
			<!--头部导航区域开始-->
			<div class="headerBox" id="headerBox">
                <#include "/common/header.ftl" />
			</div>
			<!--头部导航区域结束-->
            <!--头部步骤条-->
            <div class="topbzbox">
                <div class="warp">
                    <div class="bzcol1"><a href="/"><img src="/static/images/logo_login.png" ></a></div>
                    <div class="bzcol2"></div>
                    <div class="bzcol3">采购单</div>
                    <div class="bzcol4"><img src="/static/images/caigoudan.png" ></div>
                </div>
            </div>
            <!--资质状态提示信息-->
            <div class="sui-msg msg-large msg-tips" id="licenseMsg" style="display:none;background:#fff3ce;padding:11px 20px;">
                <div style="width:1200px;margin:0 auto;color: #ff8e29;font-weight:400;">
                    <span class="main-msg" style="margin-right: 20px;font-size: 14px;"></span>
                    <a id="updateLicense" href="/merchant/center/licenseAudit/findLicenseCategoryInfo.htm" style="color:#ff8e29;font-size: 14px;">点击去更新资质&nbsp;></a>
                </div>
            </div>
			<!--主体部分开始-->
			<div class="main" style="padding-top:0;">
				<div style="min-height: 420px"></div>
			</div>
			<!--主体部分结束-->

			<!--底部导航区域开始-->
			<div class="footer" id="footer">
				<#include "/common/footer.ftl" />
			</div>
			<!--底部导航区域结束-->
			

		</div>
        <script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
		<script type="text/javascript" src="/static/js/cart/index.js?t=${t_v}"></script>
        <script type="text/javascript" src="/static/js/qtShopDetail.js?t=${t_v}"></script>
	</body>

</html>
<script>
    //更新资质埋点
    $("#updateLicense").click(function(){
        var link = $(this);
        webSdk.track('QualificationUpdate_click', {
            'pageName': window.document.title
        },function() {
            location.href = $(link).attr('href'); //继续跳转到目标页面
        });
        // return false;
    })
    $(function(){
        //qt 购物车页面曝光
        aplus_queue.push({
				'action': 'aplus.record',
				'arguments': ['page_exposure', 'EXP',{
                    spm_cnt:"1_4.shoppingCart_0-0_0.0.0."+window.getSpmE()
            }]
		});
    })
</script>