package com.xyy.ec.pc.authentication.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.merchant.server.dto.MerchantAndAccountDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRelStatusEnum;
import com.xyy.ec.pc.authentication.consts.Constants;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.authentication.utils.ServletUtils;
import com.xyy.ec.pc.enums.AbnormalAccountTypeEnum;
import com.xyy.ec.pc.enums.MerchantStatusEnum;
import com.xyy.ec.pc.enums.RequestPhaseEnum;
import com.xyy.ec.pc.remote.AccountProviderService;
import com.xyy.ec.pc.rpc.SaasServiceRpc;
import com.xyy.ec.pc.service.IdentityValidator;
import com.xyy.ec.pc.util.CookieUtils;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.saas.user.center.api.pojo.request.QueryOneYbmAccountDTO;
import com.xyy.saas.user.center.api.pojo.response.BaseYbmAccountDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description saas用户鉴权拦截器
 * @Date 2025/7/30 15:08
 */
@Slf4j
@Component
public class SaasAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SaasServiceRpc saasServiceRpc;

    @Autowired
    private IdentityValidator identityValidator;

    @Autowired
    private AccountProviderService accountProviderService;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {
        return this.valid(request, response, o);
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

    }


    private boolean valid(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {

        String phase = CookieUtils.getCookieValue(Constants.SAAS_REQUEST_PHASE);
        String abnormalAccountType = CookieUtils.getCookieValue(Constants.ABNORMAL_ACCOUNT_TYPE);
        String organSign = CookieUtils.getCookieValue(Constants.SAAS_ORGAN_SIGN);
        String provinceCode = CookieUtils.getCookieValue(Constants.SAAS_PROVINCE_CODE);

        request.setAttribute("saasOrganSign", organSign);
        request.setAttribute("merchantProvinceCode", ObjectUtil.isNotNull(provinceCode) ? Integer.parseInt(provinceCode) : null);

        //如果是初始化请求，则强制进行鉴权，防止出现未绑定用户，药帮忙已经登录，如果不鉴权，则直接使用药帮忙的登录信息查询的问题
        if (StringUtils.equals(RequestPhaseEnum.INIT.getCode(), phase)) {
            returnErrorJson();
            return false;
        }
        //如果当前是未绑定ec账号的saas用户，则不进行鉴权，但是要重新判断是否绑定了ec账号
        if (StringUtils.equals(AbnormalAccountTypeEnum.NOT_BIND_EC.getCode(), abnormalAccountType)) {
            QueryOneYbmAccountDTO param = new QueryOneYbmAccountDTO();
            param.setOrganSign(organSign);
            param.setBindStatus((byte) 1);
            BaseYbmAccountDTO baseYbmAccount = saasServiceRpc.getEcSyncNoBySaasOrganSign(param);
            //如果存在，则说明已经绑定，就需要重新登录
            if (ObjectUtil.isNotNull(baseYbmAccount) && StringUtil.isNotEmpty(baseYbmAccount.getBindCode())) {
                returnErrorJson();
                return false;
            }
            request.setAttribute("bindStatus", 0);
            return true;
        }
        //账号禁止登录
        if (StringUtils.equals(AbnormalAccountTypeEnum.FORBIDDEN.getCode(), abnormalAccountType)) {
            String syncNo = CookieUtils.getCookieValue(Constants.SAAS_SYNC_NO);
            MerchantAndAccountDto merchantAndAccountDto = accountProviderService.queryManagerAccountBySyncNo(syncNo);
            Boolean checked = checkAccountStatus(merchantAndAccountDto);
            if (checked) {
                returnErrorJson();
                return false;
            }
            request.setAttribute("bindStatus", 0);
            return true;
        }
        JwtPrincipal jwtPrincipal = (JwtPrincipal) identityValidator.currentPrincipal();
        // 用户未登录
        if (jwtPrincipal == null) {
            returnErrorJson();
            return false;
        }
        // 验证刷新token
        tokenService.verifyToken(jwtPrincipal);
        request.setAttribute("bindStatus", 1);
        // 判断qt session是否需要重新生成
        refreshSaasQtSession();
        return true;
    }


    private void returnErrorJson() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "failure");
        result.put("code", 90000);
        result.put("msg", "登录失效，请重新登陆");
        result.put("errorMsg", "登录失效，请重新登陆");
        try {
            ServletUtil.write(ServletUtils.getResponse(), JSONObject.toJSONString(result), ContentType.APPLICATION_JSON.toString());
        } catch (Exception e) {
            log.error("ajax请求登录失效，响应结果失败：{}", JSONObject.toJSONString(result), e);
        }
    }

    private void refreshSaasQtSession() {
        try {
            String cookieValue = ServletUtils.getCookieValue(Constants.SAAS_QT_SESSION_KET);
            if (StringUtils.isNotBlank(cookieValue)) {
                String[] cookie = cookieValue.split("_");
                if (cookie.length == 2) {
                    String session = cookie[0];
                    long time = Long.parseLong(cookie[1]);
                    if (System.currentTimeMillis() - time > 1000 * 60 * 30L) {
                        session = RandomStringUtils.randomAlphanumeric(8);
                    }
                    // 记录QT用来上报埋点的session放入cookie
                    Cookie qtSessionCookie = ServletUtils.createCookie(Constants.SAAS_QT_SESSION_KET, StrUtil.format("{}_{}", session, System.currentTimeMillis()), Constants.COOKIE_MAX_AGE);
                    ServletUtils.writeCookie(qtSessionCookie);
                }
            }
        } catch (Exception e) {
            log.error("刷新qt session异常", e);
        }
    }

    /**
     * 校验账号是否有效
     *
     * @param merchantAndAccountDto
     * @returkkkkkk
     */
    private Boolean checkAccountStatus(MerchantAndAccountDto merchantAndAccountDto) {

        if (ObjectUtil.isNull(merchantAndAccountDto)) {
            return false;
        }
        //账号状态是激活状态
        if (ObjectUtil.notEqual(merchantAndAccountDto.getMerchantStatus(), MerchantStatusEnum.STATUS_NORMAL.getId())) {
            return false;
        }
        //资质状态是已通过
        if (ObjectUtil.notEqual(merchantAndAccountDto.getLicenseStatus(), 4)) {
            return false;
        }
        //店铺审核状态是审核通过
        if (ObjectUtil.notEqual(merchantAndAccountDto.getAuditStatus(), 2)) {
            return false;
        }
        //账号关系状态是审核通过
        if (ObjectUtil.isNotNull(merchantAndAccountDto.getAccountRelStatus()) && ObjectUtil.notEqual(merchantAndAccountDto.getAccountRelStatus(), AccountMerchantRelStatusEnum.PASS.getValue())) {
            return false;
        }
        //账号状态是激活状态
        if (ObjectUtil.isNotNull(merchantAndAccountDto.getAccountStatus()) && ObjectUtil.notEqual(merchantAndAccountDto.getAccountStatus(), 1)) {
            return false;
        }
        //账号密码不为空
        if (StringUtil.isEmpty(merchantAndAccountDto.getMobile()) || StringUtil.isEmpty(merchantAndAccountDto.getPassword())) {
            return false;
        }
        return true;
    }


}
