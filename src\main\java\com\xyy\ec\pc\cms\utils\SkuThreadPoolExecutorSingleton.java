package com.xyy.ec.pc.cms.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

/**
 * {@link ThreadPoolExecutor} CMS工具类，应用内缓存一个线程池
 *
 * <AUTHOR>
 */
@Slf4j
public class SkuThreadPoolExecutorSingleton {

    public static final int WORK_QUEUE_CAPACITY_GET_BY_SKU_ID_LIST = 1;
    private static final BlockingQueue<Runnable> WORK_QUEUE_GET_SKU_BY_CSU_IDS = new LinkedBlockingQueue<>(WORK_QUEUE_CAPACITY_GET_BY_SKU_ID_LIST);
    private static final ThreadPoolExecutor THREAD_POOL_GET_SKU_BY_CSU_IDS = new ThreadPoolExecutor(128, 128, 3,
            TimeUnit.SECONDS, WORK_QUEUE_GET_SKU_BY_CSU_IDS, Executors.defaultThreadFactory(), (r, e) -> {
        String msg = String.format("首页查询商品信息线程池 Thread pool is EXHAUSTED!" +
                        " Thread Name: %s, Pool Size: %d (active: %d, core: %d, max: %d, largest: %d), Task: %d (completed: %d)," +
                        " Executor status:(isShutdown:%s, isTerminated:%s, isTerminating:%s)",
                r.toString(), e.getPoolSize(), e.getActiveCount(), e.getCorePoolSize(), e.getMaximumPoolSize(), e.getLargestPoolSize(),
                e.getTaskCount(), e.getCompletedTaskCount(), e.isShutdown(), e.isTerminated(), e.isTerminating());
        log.warn(msg);
        throw new RejectedExecutionException(msg);
    });
    public static ThreadPoolExecutor getSkuByCsuIds() {
        return THREAD_POOL_GET_SKU_BY_CSU_IDS;
    }
}
