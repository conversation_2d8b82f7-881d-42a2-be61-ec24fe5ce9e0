package com.xyy.ec.pc.newfront.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pc.newfront.dto.NoticeDetailRespVO;
import com.xyy.ec.pc.newfront.dto.NoticeRowRespVO;
import com.xyy.ec.pc.newfront.vo.NoticeDetailParamVO;
import com.xyy.ec.pc.newfront.vo.NoticeParamVO;

import javax.servlet.http.HttpServletRequest;

public interface NoticeNewService {
    /**
     * 获取公告列表
     */
    PageInfo<NoticeRowRespVO> getNoticeList(NoticeParamVO paramVO, HttpServletRequest request);

    /**
     * 获取公告详情
     */
    NoticeDetailRespVO getNoticeDetail(NoticeDetailParamVO paramVO, HttpServletRequest request);
}
