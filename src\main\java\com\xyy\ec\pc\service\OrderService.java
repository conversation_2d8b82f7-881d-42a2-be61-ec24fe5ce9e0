package com.xyy.ec.pc.service;

import com.alibaba.fastjson.JSONArray;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.common.ResultDTO;
import com.xyy.ec.order.business.dto.*;
import com.xyy.ec.order.business.dto.ecp.order.OrderSettleCommunicationDto;
import com.xyy.ec.order.business.exception.ServiceException;
import com.xyy.ec.order.core.dto.Order;
import com.xyy.ec.order.core.dto.cart.ShoppingCartInfo;
import com.xyy.ec.order.dto.pay.CashierDto;
import com.xyy.ec.order.dto.pay.CashierQueryParamDto;
import com.xyy.ec.order.dto.settle.KaMatchPriceVO;
import com.xyy.ec.order.dto.settle.SettleDto;
import com.xyy.ec.order.dto.settle.SettleSkuDto;
import com.xyy.ec.order.dto.settle.SettleVO;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.model.order.OrderSettleVo;
import com.xyy.ec.pc.model.order.OrderSproutSettleVo;
import com.xyy.ec.pc.vo.SaasAdVo;
import com.xyy.ec.pc.vo.order.ConfirmOrderVo;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @Author: zhaoyun
 * @Date: 2018/8/27 21:29
 * @Description:订单服务类
 */
public interface OrderService {
    public List<String> queryOutOrderPdfUrl(String  orderNo,String  branchCode,String  docType);

    /**
     * 查询订单详情
     * @param merchantId
     * @param id
     * @param scene
     * @return
     */
    OrderBusinessDto getOrderDatails(Long merchantId,Long id, int scene) throws Exception;

    /**
     * 获取订单信息
     * @param orderId
     * @return
     */
    OrderBusinessDto selectByPrimaryKey(Long orderId);

    OrderExtendBusinessDto quereyExtendInfo(String orderNo);

    /**
     * 获取退款明细
     * @param orderId
     * @param customOrderDetailBusniessDtoList
     * @param refundType
     * @return
     */
    List<CustomOrderDetailBusniessDto> canRefundCustomOrderDeatils(Long orderId, List<CustomOrderDetailBusniessDto> customOrderDetailBusniessDtoList, int refundType);

    /**
     * 根據退款明細返回所查詢的退款商品統計金額
     * @param customOrder
     * @return
     */
    Object getOrderRefundCensus(CustomOrderBusniessDto customOrder);


    Object getOrderFreightRefundCensus(String orderNo);


    /**
     * 申请退款
     * @param customOrder
     * @param customOrderDetails
     */
    void submitApplyRefund(CustomOrderBusniessDto customOrder,List<CustomOrderDetailBusniessDto> customOrderDetails);

    void applyFreightRefund(CustomOrderBusniessDto customOrder);
    /**
     * 查看退款明细详情
     * @param orderRefundDetailBusinessDto
     * @param page
     * @param isShow
     * @return
     */
    Map<String,Object> selectDetailByRefundId(OrderRefundDetailBusinessDto orderRefundDetailBusinessDto, Page page, boolean isShow);

//    /**
//     * 退款列表
//     * @param orderRefundBusinessDto
//     * @param page
//     * @return
//     */
//    List<OrderRefundBusinessDto> findRefundOrders(OrderRefundBusinessDto orderRefundBusinessDto,Page page);

    /**
     * 去结算
     * @param order
     * @param isCheck
     * @param branchCode
     */
    ShoppingCartInfo settleNew(Order order, boolean isCheck, String branchCode) throws ServiceException;

    /**
     * 动态查询订单信息(包含商户,支付类型,扩展表余额)
     * @param order
     * @return
     */
    Order selectWebServiceOrder(Order order);
    List<OrderBusinessDto> selectWebServiceWithSubOrders(Long id);

    /**
     * 确认下单
     * @param order
     * @param branchCode
     * @return
     */
    Order confirmOrderNew(Order order,String branchCode);

    /**
     * 获取在线支付的超时时间
     * @param branchCode
     * @return
     */
    Integer getOnlinePayOrderFinalPaymentHour(String branchCode);

    /**
     * 设置业务异常消息到缓存
     * @param merchantId
     * @param errorMsg
     */
    void setErrorMsg(Long merchantId, String errorMsg);

    /**
     * 通过orderNo查询订单
     * @param orderNo
     * @return
     */
    Order getOrderByOrderNo(String orderNo);

    /**
     * 根据会员id，获取最近一条提交收汇款银行记录
     * @return
     */
    OrderRefundBankBusinessDto selectLastOrderRefundBankByMerchantId(Long merchantId);

    void setSttleMes(ModelMap modelMap, ShoppingCartInfo shoppingCartInfo, String branchCode, Long merchantId)throws Exception;

    Map<String, Object> setCalcSettleAmount(ShoppingCartInfo shoppingCartInfo,String branchCode);

    ModelAndView setConfimOrder(ConfirmOrderVo order, MerchantBussinessDto merchant, ModelMap modelMap, HttpServletRequest request, String source, String token) throws Exception;

    ModelAndView groupConfimOrder(ConfirmOrderVo order, MerchantBussinessDto merchant, ModelMap modelMap, HttpServletRequest request,String source) throws Exception;

    /**
     * @description: 1.1 订单预结算接口
     * @author: wcwanga
     * @create: 2020-02-19 14:12
     * @param: [order]
     * @return: void
     **/
    ApiRPCResult<SettleVO> preSettle(OrderSettleVo order);
    /**
     * @description: 1.2 代下单预结算
     * @author: wcwanga
     * @create: 2020-02-27 00:07
     * @param: [order]
     * @return: com.xyy.ec.order.business.common.ResultDTO<java.lang.String>
     **/
    ResultDTO<String> preSettleSprout(OrderSproutSettleVo order);
    /**
     * @description: 2. 订单去结算接口
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     **/
    @Deprecated
    ResultDTO<OrderSettleCommunicationDto> settle(OrderSettleVo order);
    /**
     * @description: 2. 订单去结算接口
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     **/
    ResultDTO<SettleVO> settleForRefactor(OrderSettleVo order,List<SettleSkuDto> bizProducts);
    ResultDTO matchPricePreSettle(OrderSettleVo order);

    SettleDto queryMatchPriceSettleParam(Long merchantId);
    ResultDTO<SettleVO> matchPriceSettle(OrderSettleVo order);

    /**
     * @description: 2.2 订单去结算接口
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     **/
    @Deprecated
    ResultDTO<OrderSettleCommunicationDto> settleSprout(OrderSproutSettleVo order);
    /**
     * @description: 2.2 订单去结算接口
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     **/
    ResultDTO<SettleVO> settleSproutV2(OrderSproutSettleVo order);
    /**
     * @description: 3. 填充结算页数据
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     **/
    void setSettleData(ModelMap modelMap, OrderSettleCommunicationDto orderSettleCommunicationDto, String branchCode, Long merchantId)throws Exception;
    /**
     * @description: 3. 填充结算页数据
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     **/
    void setSettleDataForRefactor(ModelMap modelMap, SettleVO settleVO, String branchCode, MerchantBussinessDto merchant)throws Exception;

    /**
     * @description: 4. 金额重新计算
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     **/
    @Deprecated
    ResultDTO<OrderSettleCommunicationDto> calcSettleAmount(OrderSettleVo order);
    /**
     * @description: 4. 金额重新计算
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     **/
    ResultDTO<SettleVO> calcSettleAmountForRefactor(OrderSettleVo order);

    /**
     * @description: 4. 代下单金额重新计算
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     **/
    ResultDTO<OrderSettleCommunicationDto> calcProutSettleAmount(OrderSproutSettleVo paramOrder);    /**
     * @description: 4. 代下单金额重新计算
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     **/
    ResultDTO<SettleVO> calcSproutSettleAmountV2(OrderSproutSettleVo paramOrder);
    /**
     * @description: 4. 金额重新计算数据填充
     * @author: wcwanga
     * @create: 2020-02-29 13:01
     * @param: [orderSettleCommunicationDto, branchCode]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     **/
    @Deprecated
    Map<String, Object> setCalcSettleAmount(OrderSettleCommunicationDto orderSettleCommunicationDto,String branchCode);
    /**
     * @description: 4. 金额重新计算数据填充
     * @author: wcwanga
     * @create: 2020-02-29 13:01
     * @param: [orderSettleCommunicationDto, branchCode]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     **/
    Map<String, Object> setCalcSettleAmount(SettleVO settleVO,String branchCode);

    /**
     * @description: 5. 优惠券监测
     * @author: wcwanga
     * @create: 2020-02-29 23:35
     * @param: [paramOrder]
     * @return: com.xyy.ec.order.business.common.ResultDTO<java.lang.String>
     **/
    ResultDTO<String> voucherMonitor(Order paramOrder,Integer bizSource);

    /**
     * * 1、共仓逻辑：
     *      * 共仓是域，订单和退款单下推时，将订单信息中的branchCode转换成共仓的域编码
     *      * 2、切仓逻辑：曾经共仓，后来自建仓库
     *      * 切仓后，退款问题：
     *      * 1) 切仓前的订单发起退款，退款单下推时，必须还转换成共仓时的branchCode
     *      * 2）切仓后的订单、退款单，则采用自己域的新域编码
     * @param order
     */
    void shareWarehouseLogic(OrderBusinessDto order);

    JSONArray getAvailableChannel(Order order);

    JSONArray getAvailableChannel(OrderBusinessDto order);

    CashierDto getCashierForPc(OrderBusinessDto order, List<OrderBusinessDto> orderList, Boolean useVirtualGold);

    CashierDto getCashierForRecharge(CashierQueryParamDto order);

    SaasAdVo queryAdRotation(MerchantBussinessDto merchantBussinessDto);

}
