package com.xyy.ec.pc.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/7
 * @description
 */
@Component
public class ImUrlBuilder {

    @Value("${im-url-builder}")
    private String imUrlBuilder;

    @Value("${im-pop-url-builder}")
    private String imPopUrlBuilder;

    public String buildImUrl(Long merchantId) {
        return String.format(imUrlBuilder, merchantId,merchantId);
    }

    public String buildImPopUrl(Long workOrderId, Long merchantId,String orgId,String shopName) {
        return String.format(imPopUrlBuilder, workOrderId,merchantId,orgId,shopName);
    }
}
