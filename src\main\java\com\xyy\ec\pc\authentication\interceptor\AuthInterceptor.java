package com.xyy.ec.pc.authentication.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.pc.authentication.consts.Constants;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.authentication.utils.ServletUtils;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.interceptor.SpiderInterceptor;
import com.xyy.ec.pc.interceptor.helper.DifferentPlacesLoginHelper;
import com.xyy.ec.pc.service.IdentityValidator;
import com.xyy.ec.pc.util.AjaxUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/4/11 16:53
 * @File AuthInterceptor.class
 * @Software IntelliJ IDEA
 * @Description
 */
@Slf4j
@Component
public class AuthInterceptor implements HandlerInterceptor {

    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private IdentityValidator identityValidator;
    
    @Autowired
    private SpiderInterceptor spiderInterceptor;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private DifferentPlacesLoginHelper differentPlacesLoginHelper;

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {

        return this.valid(httpServletRequest, httpServletResponse, o);
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }

    private boolean valid(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {

        JwtPrincipal jwtPrincipal = (JwtPrincipal) identityValidator.currentPrincipal();
        // 用户未登录
        if (jwtPrincipal == null) {
            redirectToLoginPage(ServletUtils.getRequest(), ServletUtils.getResponse(), "sessionTimeOut");
            return false;
        }
        // 异地登录未验证拦截
        if (differentPlacesLoginHelper.isDifferentPlacesLogin(jwtPrincipal)) {
            redirectToLoginPage(ServletUtils.getRequest(), ServletUtils.getResponse(), "sessionTimeOut");
            return false;
        }
        // 爬虫拦截
        if (!spiderInterceptor.preHandle(request, response, o)) {
            return false;
        }
        // 验证刷新token
        tokenService.verifyToken(jwtPrincipal);
        // 判断qt session是否需要重新生成
        refreshQtSession();
        return true;
    }

    private void redirectToLoginPage(HttpServletRequest request, HttpServletResponse response, String sessionStatus) {

        if (AjaxUtil.isAjaxRequest(request)) {
            // 如果是ajax请求，返回sessionStatus为 sessionStatus，在common.js中会有处理，跳转到登录页
            response.setHeader("SessionStatus", sessionStatus);
            // 为了兼容前端（前端框架限制，没有响应体数据则取不到响应头）
            this.writeAjaxErrorJson();
        }
        else {
            String redirectUrl = request.getParameter("redirectUrl");
            if (StringUtils.isEmpty(redirectUrl)) {
                redirectUrl = request.getServletPath();
                String queryString = request.getQueryString();
                redirectUrl = queryString == null ? redirectUrl : redirectUrl + "?" + queryString;
            }
            log.info("重定向至登录页, redirectUrl: {}", redirectUrl);
            // 如果是页面跳转请求，直接跳转到登录页，加上redirectUrl
            String loginUrl = appProperties.getBasePathUrl() + "/login/login.htm";
            if (StringUtils.isNotEmpty(redirectUrl)) {
                try {
                    loginUrl += "?redirectUrl=" + URLEncoder.encode(redirectUrl, "utf-8");
                } catch (UnsupportedEncodingException e) {
                    log.error(e.getMessage());
                }
            }
            log.info("重定向至登录页, loginUrl: {}", loginUrl);
            try {
                response.sendRedirect(loginUrl);
            } catch (IOException ioException) {
                log.error(ioException.getMessage());
            }
        }
    }

    private void writeAjaxErrorJson() {

        Map<String, Object> result = new HashMap<>();
        result.put("status", "failure");
        result.put("code", 90000);
        result.put("msg", "登录失效，请重新登陆");
        result.put("errorMsg", "登录失效，请重新登陆");
        try {
            ServletUtil.write(ServletUtils.getResponse(), JSONObject.toJSONString(result), ContentType.APPLICATION_JSON.toString());
        } catch (Exception e) {
            log.error("ajax请求登录失效，响应结果失败：{}", JSONObject.toJSONString(result), e);
        }
    }

    public void refreshQtSession() {

        try {
            String cookieValue = ServletUtils.getCookieValue(Constants.QT_SESSION_KET);
            if (StringUtils.isNotBlank(cookieValue)) {
                String[] cookie = cookieValue.split("_");
                if (cookie.length == 2) {
                    String session = cookie[0];
                    long time = Long.parseLong(cookie[1]);
                    if (System.currentTimeMillis() - time > 1000 * 60 * 30L) {
                        session = RandomStringUtils.randomAlphanumeric(8);
                    }
                    // 记录QT用来上报埋点的session放入cookie
                    Cookie qtSessionCookie = ServletUtils.createCookie(Constants.QT_SESSION_KET, StrUtil.format("{}_{}", session, System.currentTimeMillis()), Constants.COOKIE_MAX_AGE);
                    ServletUtils.writeCookie(qtSessionCookie);
                }
            }
        }
        catch (Exception e) {
            log.error("刷新qt session异常", e);
        }
    }
}