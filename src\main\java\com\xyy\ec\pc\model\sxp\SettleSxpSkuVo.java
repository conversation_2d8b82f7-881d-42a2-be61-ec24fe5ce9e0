package com.xyy.ec.pc.model.sxp;


import com.xyy.ec.pc.search.vo.SxpQtDataVo;
import lombok.Data;

import java.io.Serializable;

/**
 * 结算随心拼商品dto
 * <AUTHOR>
 * @version V1.0
 * @date 2022年09月14日16:15:05
 */
@Data
public class SettleSxpSkuVo implements Serializable {

    private static final long serialVersionUID = 8535016650285511610L;
    /**
     * 商品ID
     */
    private Long skuId;
    /**
     * 商品数量
     */
    private Integer quantity;
    /**
     * 已有XXX人 下单
     */
    private String promoTag;
    /**
     * 埋点数据
     */
    private String mddata;

    private Integer type;


    private Integer skuStartNum;

    private String tag;
    /**
     * 显示名字
     */
    private String showName;
    /**
     *  埋点数据
     */
    private SxpQtDataVo qtData;
}
