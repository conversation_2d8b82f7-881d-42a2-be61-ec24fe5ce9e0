
/*主体部分*/
.main{width:100%;border-top:1px solid #00dc82;}



/*面包屑导航*/
.mb-box{  width: 1180px;margin: 0 auto;padding-top: 10px;padding-bottom: 10px;color: #999999;}
.mb-box span{padding: 0 5px;}
.mb-box a{color: #999999;font-size: 12px;}
.mb-box a.cur{color: #999999;}

/*商品图片*/
.d-mainbox{width: 1160px;margin: 0 auto;overflow: hidden;background-color: #ffffff;padding: 30px 20px;}
.d-mainbox .d-lbox{width: 420px;/*height: 418px;border: 1px solid #e0e0e0;*/margin-right:18px;text-align: center;/*position: relative;*/}
/*.d-mainbox .d-lbox img{width: 290px;height: 290px;margin-top: 60px;}*/
.d-mainbox .d-lbox .yaokuanghuan-pos{position: absolute;top:0px;left: 0px;}
.d-mainbox .d-lbox .yaokuanghuan-pos img{width: 370px;height: 370px;margin-top: 0;}
/*.d-mainbox .d-lbox .noshow{display: none;}*/

.spec-preview{width:370px;height:370px;border:1px solid #DFDFDF;}
.spec-scroll{clear:both;margin-top:15px;width:372px;}
.noshow{display: none;}
.spec-scroll .prev{float:left;margin-right:4px;}
.spec-scroll .next{float:right;}
.spec-scroll .prev,.spec-scroll .next{display:block;font-family:"宋体";text-align:center;width:20px;height:64px; line-height:64px;font-size:30px;color:#d2d2d2;cursor:pointer;text-decoration:none;}
.spec-scroll .noclick{color:#d2d2d2;}
.spec-scroll .items{float:left;position:relative;width:322px;height:66px;overflow:hidden;}
.spec-scroll .items ul{position:absolute;width:999999px;height:66px;}
.spec-scroll .items ul li{float:left;width:80px;text-align:center;}
.spec-scroll .items ul li img{border:1px solid #CCC;padding:2px;width:60px;height:60px;}
.spec-scroll .items ul li img:hover{border:2px solid #31cb96;padding:1px;}
/*图片放大镜样式*/
.jqzoom{float:left;border:none;position:relative;padding:0px;cursor:pointer;margin:0px;display:block;width: 370px;text-align: center;}
.jqzoom img{width: 290px;height: 290px;margin-top: 40px;}
.zoomdiv{z-index:100;position:absolute;top:0px;left:0px;width:370px!important;height:370px!important;background:#ffffff;border:1px solid #CCCCCC;display:none;text-align:center;overflow:hidden;}
.jqZoomPup{z-index:10;visibility:hidden;position:absolute;top:0px;left:0px;width:20px;height:20px;border:1px solid #aaa;background:#ffffff /*url(../images/zoom.png) 50% center no-repeat*/;opacity: 0.5;-moz-opacity: 0.5;-khtml-opacity: 0.5;filter: alpha(Opacity=50);}

img{max-width: none;}




.d-mainbox .d-rbox{width: 710px;overflow: hidden;}
.d-mainbox .d-rbox .title{line-height: 24px;margin-bottom: 9px;}
.d-mainbox .d-rbox .title .dujia{background: #27ADFF;border-radius: 4px;font-size: 18px;color: #FFFFFF;padding: 1px 7px;}
.d-mainbox .d-rbox .title .yibao{
    font-weight: 500;
    border-radius: 3px;
    border: solid #17B9BB 1px;
    line-height: 14px;
    padding: 3px;
    color: #17B9BB;
    vertical-align: middle;
    transform: translateY(-1px);
}
.d-mainbox .d-rbox .title .otc{background: #DA0000;border-radius: 4px;font-size: 18px;color: #FFFFFF;padding: 1px 7px;}
.d-mainbox .d-rbox .title .tit{font-size: 24px;color: #333333;font-weight: bold;}
.d-mainbox .d-rbox .info{font-size: 14px;color: #ff2400;margin-top: 5px;margin-bottom:5px;word-break: break-all;}
.d-mainbox .d-rbox .info .gomore{font-size: 14px;color: #28a3ef;}
.d-mainbox .d-rbox .info .gomore:hover{text-decoration: underline;}


.d-mainbox .d-rbox .xscxbox{background-color:#FABF19;background-image: linear-gradient(-90deg, #FABF19 0%, #FF7900 100%);height: 37px;line-height: 37px;}
.d-mainbox .d-rbox .xscxbox span{color: #ffffff;font-size: 14px;}
.d-mainbox .d-rbox .xscxbox span.xscxspan{font-size: 16px;margin-left: 15px;margin-right: 30px;}
.d-mainbox .d-rbox .xscxbox span.only{margin-right: 10px;}

.d-mainbox .d-rbox .section{background: #F5F5F5;padding: 20px 15px;}
.d-mainbox .d-rbox span{display: inline-block;font-size: 14px;}
.d-mainbox .d-rbox span.zhehou-price{font-size: 12px;color: #ff2400;margin-left:5px;position: relative;top:0px;}

/*有价格区间*/
.d-mainbox .d-rbox .hasqujian{}
/*.d-mainbox .d-rbox .hasqujian .linfo{margin-right: 20px;}*/
.d-mainbox .d-rbox .hasqujian .qujian{position: relative;width: 100px;}
.hasqujian .qujian .shuxian{position: absolute;right: -10px;top:-10px;}
.d-mainbox .d-rbox .hprice{margin-top: 0px;}
.d-mainbox .d-rbox .hprice .linfo{color: #666666;font-size: 14px;width: 70px;}
.d-mainbox .d-rbox .hprice .price{color: #ff2400;font-size: 20px;margin-left: 20px;font-weight: bold;}
.d-mainbox .d-rbox .hprice .xiangou{padding: 0 3px; display: inline-block;position: relative;background: #BCBCBC; border-radius: 2px;top:-5px;margin-left: 5px;font-size: 12px;color: #ffffff;}
.d-mainbox .d-rbox .hprice .sanjiao{position: absolute;left: -4px;bottom:0;}
.d-mainbox .d-rbox .hprice .login_show{color: #f39800;font-size: 20px;margin-left: 20px;}
.d-mainbox .d-rbox .hprice .noPermission{color: #ff2400;font-size: 20px;margin-left: 20px;}

.d-mainbox .d-rbox .cgounum{margin-top: 10px;}
.d-mainbox .d-rbox .cgounum .linfo{color: #666666;font-size: 14px;width: 70px;display: inline-block;}
.d-mainbox .d-rbox .cgounum .price{color: #333333;font-size: 14px;width: 100px;margin-left: 20px;text-indent: 5px;}


.d-mainbox .d-rbox .yprice{margin-top: 10px;}
.d-mainbox .d-rbox .yprice .linfo{color: #666666;font-size: 14px;width: 70px;}
.d-mainbox .d-rbox .yprice .price{color: #333333;font-size: 14px;margin-left: 20px;}
.d-mainbox .d-rbox .yprice .xiangou{color:#00dc82; }
/*控销价*/
.d-mainbox .d-rbox .yprice .ptit{color: #666666;font-size: 14px;margin-right: 30px;margin-left: 30px;}
.d-mainbox .d-rbox .yprice .pinfo{color: #ff0000;font-size: 14px;}

/*优惠券*/
.d-mainbox .d-rbox .youhuiquan{margin-top: 10px;}
.d-mainbox .d-rbox .youhuiquan .linfo{color: #666666;font-size: 14px;width: 70px;vertical-align: top;}
.d-mainbox .d-rbox .youhuiquan .quan-a-box{display: inline-block;width: 530px;margin-left: 20px;}
.d-mainbox .d-rbox .youhuiquan a.quan-a{display: inline-block;margin-right:5px;font-size: 12px;color: #5EA2DB;background: rgba(216,235,255,0.30);border: 1px solid #E4EEF9;padding: 3px 9px;margin-bottom: 10px;}
.d-mainbox .d-rbox .youhuiquan a.quan-a:hover{background: #D8EBFF;}
/*促销*/
.d-mainbox .d-rbox .cuxiaobox{margin-top: 5px;position: relative;}
.d-mainbox .d-rbox .cuxiaobox .linfo{color: #666666;font-size: 14px;width: 70px;vertical-align: top;}
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul{display: inline-block;width: 580px;margin-left: 20px;max-height: 54px;overflow: hidden;}
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul li{margin-bottom: 5px;line-height: 22px;word-wrap:break-word; word-break:break-all;width: 580px;}
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul li span{word-wrap:break-word; word-break:break-all;}
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul li .biao{font-size: 14px; color: #FF0202;border: 1px solid #E70006; border-radius: 2px;padding: 0px 5px;}
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul li.sbzhan .biao{margin-right: 5px;}
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul li .zhuanpin-biao{font-size: 14px; color: #FF155D;border: 1px solid #FF155D; background: #FFF7F9; border-radius: 4px;padding: 0px 5px;}
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul li.sbzhan .zhuanpin-biao{margin-right: 5px;}

.synthesis {display: inline-block;}
.synthesis-biao {display: flex;border: 1px solid #FF2121;border-radius: 2px;overflow: hidden;}
.synthesis-biao .synthesis-biao-left {padding: 0px 5px;box-sizing: border-box; PingFangSC-Regular;font-weight: 400;font-size: 10px;color: #FFFFFF;background: #FF2121;}
.synthesis-biao .synthesis-biao-shuoming {padding: 0px 5px;box-sizing: border-box;font-family: PingFangSC-Regular;font-weight: 400;font-size: 10px;color: #FF2121;}
/* <div class="synthesis-biao">
    <span class="synthesis-biao-left">${item.name}</span>
    <#if (item.uiType == 3 && item.cxtagDescription ?? && item.cxtagDescription != '')>
        <span class="synthesis-biao-shuoming">${item.cxtagDescription}</span>
    </#if>
    <#if item.description ?? && item.description != ''>
        <span class="synthesis-biao-shuoming">${item.description}</span>
    </#if>
</div> */
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul li .shuoming{font-size: 14px; color: #666666;display: inline;}
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul li .tiaozhuan{}
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul li .tiaozhuan:hover{text-decoration: underline;}
.d-mainbox .d-rbox .cuxiaobox .cuxiao-ul li .default{background-color: #E4E4E4;color: #333333;border: 1px solid #E4E4E4;}
.d-mainbox .d-rbox .cuxiaobox .zhankai{ position: absolute; bottom: -13px; right:0;font-size: 12px; color: #999999;display: block;}


.d-mainbox .d-rbox .cuxiao{margin-top: 10px;}
.d-mainbox .d-rbox .cuxiao .xscx{color: #fff;background: #ff2400;padding: 1px 4px;}
.d-mainbox .d-rbox .cuxiao .time{color:#ff2400;}

body{-webkit-font-smoothing: antialiased;}

.d-mainbox .d-rbox .tongyong-info{margin-left: 15px;vertical-align: middle;margin-top: 10px;}
.d-mainbox .d-rbox .tongyong-info span{font-size: 14px;vertical-align: middle;word-wrap:break-word; word-break:break-all;}
.d-mainbox .d-rbox .tongyong-info .linfo-col1{color: #666666;width: 70px;}
.d-mainbox .d-rbox .tongyong-info .rtext-co11{color: #000000;width: 250px;margin-left: 20px;overflow: hidden;  text-overflow: ellipsis;  white-space: nowrap;}
.d-mainbox .d-rbox .tongyong-info .linfo-col2{color: #666666;width: 70px;}
.d-mainbox .d-rbox .tongyong-info .rtext-col2{color: #000000;width: 150px;margin-left: 10px;overflow: hidden;  text-overflow: ellipsis;  white-space: nowrap;}
.d-mainbox .d-rbox .tongyong-info .nolimit{width: 550px;}
.d-mainbox .d-rbox .tongyong-info .bkcl{color: #F1BA3B;font-size: 12px;padding: 0 8px;margin-left: 5px;border: 1px solid #F1BA3B; border-radius: 2px;}
.d-mainbox .d-rbox .tongyong-info .dsffahuo i{color: #d9d9d8;vertical-align: middle;font-size: 20px;margin-left: 4px;}
.d-mainbox .d-rbox .tongyong-info.d-tishi{margin-top: 3px;}
.d-mainbox .d-rbox .tongyong-info.d-tishi .rtext-co11{
    margin-left: -5px;
    color: #ff2400;
    font-size: 12px;
    font-weight: 700;
}


/*.d-mainbox .d-rbox .changjia{line-height: 18px;overflow: hidden;}*/
/*.d-mainbox .d-rbox .changjia .linfo{color: #999999;font-size: 12px;width: 70px;}*/
/*.d-mainbox .d-rbox .changjia .rtext{color: #666666;font-size: 12px;margin-left: 20px;}*/

.d-mainbox .pzspe{color: #666666;font-size: 12px;margin-left: 20px;width:200px;}
.d-mainbox .pzspe_s{color: #666666;font-size: 12px;margin-left: 20px;}

.goumaibox{overflow: hidden;margin-top: 15px;margin-left: 15px;}
.goumaibox .goumaititle{width: 70px;margin-top: 23px;margin-right: 23px;font-size: 14px; color: #666666;}
.suliang{border: 1px solid #e0e0e0;width:120px;height: 32px;overflow: hidden;margin-top: 15px;background: #EFEFEF;}
 .suliang a.sub{display:block;width: 30px;height: 32px;line-height: 32px;border-right: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
 .suliang input{display:block;width: 54px;height: 32px;line-height: 32px;margin: 0;padding: 0;border: none;text-align: center;}
 .suliang a.add{display:block;width: 30px;height: 32px;line-height: 32px;border-left: 1px solid #e0e0e0;text-align: center;color: #999;font-size: 18px;}
.goumaibox .kucun{padding-left: 25px;margin-top: 23px;}
.goumaibox .kucun span{font-size: 14px;}

.d-mainbox .d-rbox .soucang a span{font-size:18px;}


.d-mainbox .d-rbox .soucang{margin-top: 15px;padding-left: 108px;overflow: hidden}
.d-mainbox .d-rbox .soucang a{text-align: center;display: inline-block;font-size: 18px;float: left;}
.d-mainbox .d-rbox .soucang .addbuy{width: 168px;height: 42px;line-height: 42px;background: #00C675;color: #fff;margin-right: 10px;}
.d-mainbox .d-rbox .soucang .ptpgbyAddbuy{width: 168px;height: 42px;line-height: 42px;background-color:#FF6204 ;color: #fff;margin-right: 10px;}

.d-mainbox .d-rbox .soucang .addbuy:hover{background: #00B068;}
.d-mainbox .d-rbox .soucang .disableBuy{width: 168px;height: 42px;line-height: 42px;background: #e0e0e0;color: #999;margin-right: 10px;}
.d-mainbox .d-rbox .soucang .ptpgbyAddbuy:hover{background-color: #EB5800;}

.d-mainbox .d-rbox .soucang .gray{color: #999999;}
.text-ma{margin-top: 8px;padding-left: 108px;color: #f96a25;font-size: 12px;}
/*content*/
.c-main{overflow: hidden;width: 1200px;margin: 0 auto;padding-bottom: 30px;margin-top: 20px;}
 /*热销商品*/
.c-main .rx-box{
    width: 238px;
    /*border: 1px solid #e0e0e0;*/
    margin-right: 15px;
    /*background-color: #ffffff;*/
    overflow: hidden;
}
.c-main .rx-box .rxsp-title{text-align: center;height: 38px;line-height: 38px;border-bottom: 1px solid #e0e0e0;background: #E8E8E8;}
.c-main .rx-box .rxsp-title .rxsp-title-l{float: left;font-size: 14px;color: #666666;margin-left: 14px;}
.c-main .rx-box .rxsp-title .rxsp-title-r{float: right;font-size: 12px;color: #666666;margin-right: 17px;cursor: pointer;}
.c-main .rx-box .rxsp-title .rxsp-title-r:hover{color: #00C675;}
.c-main .rxgoods{width: 238px;box-shadow:none;}
.c-main .rxgoods li{height:300px;float: none;border: none;border-bottom: 1px solid #ededed;}
.c-main .rxgoods li:hover{border: none;border-bottom: 1px solid #ededed;}
 /*商品详情*/
.c-main .xq-box{width: 820px;width:945px;background-color: #ffffff;}
.c-main .xq-box .sptab{overflow: hidden;/*border: 1px solid #e0e0e0;*//*border-bottom: 2px solid #00dc82;*/background: #E8E8E8;}
.c-main .xq-box .sptab li{float: left;text-align: center;height: 38px;line-height: 38px;width: 110px;font-size: 14px;color: #333333;}
.c-main .xq-box .sptab li.cur{background: #ffffff;border-top: 2px solid #00dc82;}
.c-main .xq-box .sptab li:hover{cursor: pointer;}

/*商品详情*/
.c-spxq{}
/*视频*/
.c-spxq .video{text-align: center;margin-top: 20px;}
/*商品说明书*/
.c-spxq .smsbox{margin-top: 20px;}
.c-spxq .smsbox table{width: 885px;margin: 0 auto;}
.c-spxq .smsbox table tr{border-bottom: 1px dotted #ededed;}
.c-spxq .smsbox table tr.first{ border-top:none;}
.c-spxq .smsbox table tr.end{border-bottom:none;}
.c-spxq .smsbox table td{height: 40px;}
.c-spxq .smsbox table td.td1{width: 110px;text-align: left;padding-right: 10px;}
.c-spxq .smsbox table td.td2{width: 260px;padding-left: 30px;}
.c-spxq .smsbox table td.td3{width: 90px;text-align: left;padding-right: 30px;}
.c-spxq .smsbox table td.td4{width: 280px;padding-left: 10px;}
.c-spxq .smsbox table td.tdhead{padding-left: 20px;color: #31cb96;font-size: 16px;font-weight: bold;}
.c-spxq .smsbox table td.tdspe3{padding-left: 30px;}
.c-spxq .smsbox table td.graybd{background: #ffffff;}
.bkcl-td{margin-left: 5px;}
/*图文说明*/
.c-spxq .twsm-imgbox{text-align: center;margin-top: 50px;padding-bottom: 30px;}
.c-spxq .twsm-imgbox img{max-width:100%;}

         /*关于药帮忙*/
.c-ybm{display: none;text-align: center;min-height: 700px;}
.c-ybm img{margin-top: 35px;}
/*售后无忧*/
.c-shwy{display: none;}
.c-shwy p{font-size: 16px;color: #497c92;line-height: 30px;font-weight: bold;}
.c-shwy p.topimg{text-align: center;margin-top: 40px;margin-bottom: 20px;}
.c-shwy p.jg20{padding-left: 20px;}
.c-shwy p.jg60{padding-left: 60px;}
.c-shwy p.top30{margin-top: 30px;}
.c-shwy p.suojing{text-indent:25px}
.c-shwy p.bottom{text-align: right;margin-top: 60px;}
span.spespan{background: #31cb96;color: #ffffff;border-radius: 5px;margin-left: 0;padding: 0 3px;}
.c-shwy p.common{margin-top: 30px;margin-bottom: 20px;}
.c-shwy p.common span{background: #f39800;color: #ffffff;font-size: 16px;padding: 5px;border-radius: 3px;position: relative;}
.c-shwy p.common img{position: absolute;right: -15px;top: 7px;}
.c-shwy p.listbox{margin-left: 20px;}
.c-shwy span.list{display: inline;}
.c-shwy span.list span{background: #f39800;color: #ffffff;font-size: 16px;border-radius: 3px;position: relative;margin-right: 20px;padding-left: 3px;padding-right: 3px;}
.c-shwy span.list img{position: absolute;right: -8px;top: 7px;width: 8px;height: 8px;}

       /*药品颜色分类*/
.oc-icon{
 display: inline-block;
 width: 40px;
 height: 24px;
 position: relative;
 top: 5px;
 margin-right: 5px;
}
.otcg-icon{
 background: url(/static/images/otcg.png) no-repeat;
}
.otcr-icon{
 background: url(/static/images/otcr.png) no-repeat;
}
.rx-icon{
 background: url(/static/images/rx.png) no-repeat;
}
/*优惠券弹窗*/
#fpxzTc{width: 680px;border-radius: 5px;margin-left: -340px;}
#fpxzTc .modal-body{line-height: 17px;padding: 0;padding-bottom: 20px;margin-left: 25px;}
#fpxzTc .modal-header{border-radius: 5px;}
.sui-modal .modal-body{text-align: left;}
.klqbox{}
.klqbox .tishibox{margin: 15px auto;text-align: center;font-size: 14px;color: #666666; letter-spacing: 0.78px;font-weight: bold;}
.noyhq{text-align: center;font-size: 12px;color: #BCBCBC;margin: 20px 0;}


/*收藏*/
.soucang .w-collectZone_list {height: 40px;line-height:40px;width: 40px;border: 1px solid #CCC;overflow: hidden;vertical-align: middle;text-align: center;cursor: pointer;border-radius: 5px;}
.soucang .w-collectZone_list .zone-1 {height: 40px;overflow: hidden;position: relative;float: left;width: 35px;}
.soucang .w-collectZone_list .zone-2 {height: 40px;overflow: hidden;position: relative;float: left;width: 45px;}
.soucang .w-collectZone_list .top {position: relative;height: 40px;width: 40px;}
.soucang .w-collectZone_list .w-icon-normal {margin-top: 12px}
.soucang .w-collectZone_list .bottom {height: 40px;font-size: 13px;line-height: 40px;color: #999;text-align: left;}
.soucang .w-collectZone_list .icon-normal-collectEpt{width: 16px;height: 16px;background: url("/static/images/shoucang.png") no-repeat;display: inline-block;}
.soucang .w-collectZone_list .icon-normal-collectFull{width: 16px;height: 16px;background: url("/static/images/shoucang2.png") no-repeat;display: inline-block;}
.soucang .hasCollect .top-2 {-webkit-animation: sliderUp .8s;animation: sliderUp .8s;-webkit-animation-fill-mode: forwards;animation-fill-mode: forwards  }
.soucang .nopCollect .top-1 {-webkit-animation: sliderDown .8s;animation: sliderDown .8s;-webkit-animation-fill-mode: forwards;animation-fill-mode: forwards  }
.soucang .initial .top-1,.initial .top-2 { -webkit-animation-duration: 0s!important;  animation-duration: 0s!important }
.soucang .hasCollect .bottom-1,  .hasCollect .top-1, .nopCollect .bottom-2,  .nopCollect .top-2{display: none;}
.soucang .w-collectZone_list .bottom p{font-size: 12px;font-family: SimSun;color: #adadad;padding-left: 3px;}

.main .section{
  position: relative;
}
.main .section .price-notice{
  position: absolute;
  top: 18px;
    right: 10px;
}
.main .section .price-notice span{
 font-size: 12px;
 margin-left: 5px;
color: #00C675;
}
.main .section .price-notice img{
	width:12px;
}
.main .d-mainbox .d-rbox .soucang .reminder{background: #F7C100;}
.main .d-mainbox .d-rbox .soucang .reminder:hover{background: #E0AF00;}
.main .d-mainbox .d-rbox .soucang .subscribed-stock-reminder{background: #DDDDDD;margin-left: 0px;
color: #666666;}

.soucang .w-collectZone_list{
	line-height: normal;
}
.soucang .w-collectZone_list .w-icon-normal{
	margin-top: 5px;
	margin-left: 12px;
	display: block;
}
.main .soucang .zone-1 .sc-wenzhi{
	position: absolute;
    top: 22px;
	left: 8px;
	color: #333333;
    font-size: 12px;
}

/* 详情页双十标签 */
.d-mainbox .d-lbox .yaokuanghuan-pos{
    position: absolute;
    left: 0px;
}
.d-mainbox .d-lbox .yaokuanghuan-pos img{
    width: 370px;
    height: auto;
}
.d-mainbox .yaokuanghuan-pos .tejia806{
    width: 356px;
    text-align: center;
    bottom: 59px;
    font-size: 18px;
    left: 9px;
    margin-left: 0px;
    font-family: '微软雅黑';
    font-weight: 700;
    overflow: hidden;
    height: 26px;
    line-height: 26px;
}
.d-mainbox .yaokuanghuan-pos .tejia806 .price806{
    font-size: 24px;
    display: inline-block;
    margin-left: -5px;
    font-family: '微软雅黑';
    font-weight: 700;

}
.d-mainbox .yaokuanghuan-pos .tejia806 .bq-yellow{
    color:#fee901;
}
.title .bq806{
    height: 26px;
    vertical-align: text-bottom;
}
.title .bq806 img{
    height: 26px;
    margin-top: 2px;
}
/* 详情页企业介绍 */
.xq-qy{
    width: 240px;
    background: #FFFFFF;
    margin-bottom: 15px;
}
.qy-title{
    padding: 7px 0 7px 14px;
    background: #F9F9F9;
    font-size: 16px;
    color: #666666;
    font-weight: 700;
}
.qy-shuliang{
    overflow: hidden;
    margin: 15px 0;
}
.qy-zhong,.qy-jian{
    width: 49%;
    text-align: center;
}
.qy-center{
    width: 1px;
    height: 26px;
    background: #EEEEEE;
}
.qy-row1{
    font-size: 14px;
    color: #666666;
}
.qy-row2{
    font-size: 12px;
    color: #999999;
}
.qy-dianpu{
    text-align: center;
}
.qy-dianpu a{
    display: inline-block;
    background: #00C675;
    border: 1px solid rgba(0,198,117,0.50);
    border-radius: 2px;
    font-size: 14px;
    color: #FFFFFF;
    padding: 2px 12px;
    margin: 10px 0;
}

/* 水印 */
.xq-shuiyin{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
    height: 100%;
}
.xq-shuiyin img {
    width: 290px;
    height: 290px;
    margin-top: 40px;
}
.youxianbox{
    height: 44px;
    line-height: 40px;
    background: url("../../static/images/youxian.png") no-repeat;
    background-size: 100% 100%;
    background: none\9;
    /*下一行为关键设置*/
    filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='../../static/images/youxian.png', sizingMethod='scale');
}
.youxianbox .xscxspan{
    padding-left: 138px;
    font-size: 16px !important;
    color: #ffffff;
    font-weight: 500;
}
.youxianbox .timeP{
    text-align: right;
    padding-right: 20px;
    color: #ffffff;
}
.youxianbox .timeP span.ySpan{
    display: inline-block;
    width:24px;
    height:24px;
    line-height: 24px;
    text-align: center;
    background:#ffffff;
    box-shadow:0px 2px 4px 0px #004284;
    border-radius:1px;
    color: #005CB9;
    vertical-align: middle;
}
.kucun .bkcl{
    color: #F1BA3B;
    font-size: 12px;
    padding: 0 8px;
    margin-left: 5px;
    border: 1px solid #F1BA3B;
    border-radius: 2px;
}

/*商品套餐*/
.taocan-box{border-top:1px solid #eeeeee;width: 1172px;overflow: hidden;margin:0px auto 20px;padding: 15px 14px;background-color: #ffffff;}
.taocan-head{margin-bottom:10px;}
.taocan-head .taocan-title{font-size: 14px;font-weight: 400;color: #292933;}
.taocan-head a{float: right;}
.taocan-more{color: #9494a6;font-size: 14px;}
.more-icon{width:6px;height:11px;position:relative;top:-1px;}
/*.xstcn{width: 1172px;overflow: hidden;margin:0px auto 20px;padding: 15px 14px;}*/

/*商品推荐语*/
.rec-box{box-sizing: border-box;padding:10px 20px 15px;margin: 20px auto 10px;width: 890px;background:rgba(255,33,33,0.05);border-radius: 2px;}
.rec-box h2{font-size:14px;color: #292933;line-height: 20px;}
.rec-box ul li{margin-top:15px;line-height: 17px;font-size: 12px;color: #292933;padding-left:26px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.rec-box ul li.rec-icon3{background:url('../../static/images/tuijian-3.png') 0 0 no-repeat;background-size:16px;}
.rec-box ul li.rec-icon2{background:url('../../static/images/tuijian-2.png') 0 0 no-repeat;background-size:16px;}
.rec-box ul li.rec-icon1{background:url('../../static/images/tuijian-1.png') 0 0 no-repeat;background-size:16px;}

.xstcn .modelbox{display: inline-block;margin-right:7px;}
.xstcn .modelbox:nth-child(2n){margin-right:0;}
.xstcn .vipa{padding-bottom: 0px;}
.xstcn .vipa .tcnbox{position:relative;width:580px;margin: 0 auto;background: #fff7ef;border-radius: 2px;}
.xstcn .vipa .tcnbox .titlebox{width:325px;height:36px;margin:0 auto;margin-top: -1px;background:url("/static/images/taocan-header-3.png") no-repeat center center;background-size: 100%;}
.xstcn .vipa .tcnbox .pricebox{position:relative;top:-27px;z-index:100;text-align: center;}
.xstcn .vipa .tcnbox .pricebox .tcprice-t{font-size: 12px;color: #ffffff;font-family: "PingFang SC";font-weight: 400;}
.xstcn .vipa .tcnbox .pricebox .tcprice-f{font-size: 16px;color: #ffffff;font-family: "PingFang SC";font-weight: 500;position:relative;top:1px;}
.xstcn .vipa .tcnbox .pricebox .tcprice{font-size: 12px;opacity: 0.7;color: #ffffff;font-family: "PingFang SC";font-weight: 400;margin-right: 8px;}
.xstcn .vipa .tcnbox .pricebox .yprice-t{font-size: 12px;opacity: 0.7;color: #ffffff;font-family: "PingFang SC";text-decoration: line-through;font-weight: 400;}
.xstcn .vipa .tcnbox .pricebox .login_tit{color: #ffffff;font-size: 12px;}
.xstcn .vipa .tcnbox .pricebox .login_show{color: #ff0;font-size: 12px;}
/*.xstcn .vipa .tcnbox .resultbox{margin-top: -10px;}*/
.xstcn .vipa .tcnbox .tcwarp{overflow: hidden;overflow-x: auto;box-sizing: border-box;height: 315px;padding-left:10px;text-align: center;margin-top:16px;white-space: nowrap;}
.xstcn .vipa .tcnbox .itembox{margin-right:8px;margin-bottom:20px;width: 220px;padding-bottom:10px; display: inline-block; position: relative; z-index: 1;background: #ffffff;}
/*.xstcn .vipa .tcnbox .itembox:hover{border: 1px solid #2f3844; z-index: 1; }*/
.xstcn .vipa .tcnbox .row1{text-align: center;width: 200px;height: 200px;margin: 0 auto;padding:27px;box-sizing: border-box;}
.xstcn .vipa .tcnbox .row1 a{display: block;width: 100%;height: 100%;overflow: hidden;}
.xstcn .vipa .tcnbox .row1 a img{max-width: 70%;margin: 20px 0;transition: all 0.2s;}
.xstcn .vipa .tcnbox .row1 a:hover img{transfrom:scale(1.1); -ms-transform:scale(1.1);  -webkit-transform:scale(1.1);  -o-transform:scale(1.1);  -moz-transform:scale(1.1);}
.xstcn .vipa .tcnbox .row1{position: relative;}
.xstcn .vipa .tcnbox .row1 .bq-box{position: absolute;top:30px;left: 60px;text-align: center;}
.xstcn .vipa .tcnbox .row1 .bq-box img{width: 100px;height: 100px;}
.xstcn .vipa .tcnbox .row2{font-size: 14px;margin-top: 4px;line-height: 28px;text-align: left;padding: 0 10px;}
.xstcn .vipa .tcnbox .row2 a{display: block;color: #333333;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;}
.xstcn .vipa .tcnbox .row2 a:hover{color: #f39800;}
.xstcn .vipa .tcnbox .row3{margin-top: 8px;text-align: left;padding: 0 10px;}
.xstcn .vipa .tcnbox .row3 .meiyuan{color: #ff2121;font-size: 16px;}
.xstcn .vipa .tcnbox .row3 .price{color: #ff2121;font-size: 16px;font-weight: 400;}
.xstcn .vipa .tcnbox .row3 .tcsj{color: #333333;font-size: 16px;margin-right: 5px;float: right;}
.xstcn .vipa .tcnbox .row3 .login_show{color: #f39800;}
.xstcn .vipa .tcnbox .row3 .noPermission{color: #ff2400;}
.xstcn .vipa .tcnbox .row4{color: #999999;font-size: 12px;line-height: 20px;text-align: left;padding: 0 10px;}
.xstcn .vipa .tcnbox .row5{color: #999999;margin-top: 4px;font-size: 12px;line-height: 20px;text-align: left;padding: 0 10px;}
.xstcn .vipa .tcnbox .row6-box{width:100%;height: 120px;position: absolute;left: 0;bottom: 0;background: #2f3844;display: none;}
.xstcn .vipa .tcnbox .row6{width: 255px;height: 32px;overflow: hidden;margin: 0 auto;margin-top: -3px;}
.xstcn .vipa .tcnbox .row6 a.subt{display:block;width: 30px;height: 30px;line-height: 30px;background:#e0e0e0;border: 1px solid #dfdfdf;text-align: center;color: #999;font-size: 22px;}
.xstcn .vipa .tcnbox .row6 input{box-sizing:border-box;display:block;width: 73px;height: 32px;line-height: 32px;margin: 0;padding: 0;border: none;border-bottom: 1px solid #dfdfdf;border-top: 1px solid #dfdfdf;text-align: center;}
.xstcn .vipa .tcnbox .row6 a.addt{display:block;width: 30px;height: 30px;line-height: 30px;background:#e0e0e0;border: 1px solid #dfdfdf;text-align: center;color: #999;font-size: 22px;}
.xstcn .vipa .tcnbox .row6 a.buy{display:block;width: 110px;height: 32px;line-height: 32px;color:#fff;background: #00dc82;text-align: center;margin-left: 8px;}
.xstcn .vipa .tcnbox .row6 a.gary{display:block;width: 110px;height: 32px;line-height: 32px;color:#999999;background: #e0e0e0;text-align: center;margin-left: 8px;}

.xstcn .vipa .tcnbox .itembox .jia{position: absolute;top: 150px;left: 285px;z-index: 100;width: 29px;height: 29px;}
.xstcn .vipa .tcnbox .last .jia{display: none;}
.xstcn .vipa .tcnbox .three-n .jia{display: none;}

.xstcn .vipb{background-color: #ffdf92;padding: 0px 0;padding-top: 143px;}
.xstcn .vipc{background-color: #fff0d3;padding: 43px 0 70px 0;}
/*抢光*/
.xstcn .vipa .tcnbox .qgbox{position: absolute;top: 28px;right: 0;}
.xstcn .vipa .tcnbox .qgbox img{width:100px;}
.ss-title a {
    width: 105px;
}
.stock{
    margin-left: 10px;
}
.vipa .tcnbox .row3 .quan {
    background-color: #FFA500;
    color: #ffffff;
    border-radius: 4px;
    border: 1px solid #FFA500;
    /*float: right;*/
}
.jp-quality{margin-top: -14px;}
.jp-quality .timeBox {
    /*background: #FEDCD6;*/
    border-radius: 4px;
    height: 34px;
    line-height: 34px;
    width: 290px;
    margin: 0 auto;
    color: #ED6464;
    font-size: 14px;
    text-align: center;
}

.jp-quality span {
    color: #ED6464;
    font-size: 14px;
    letter-spacing: 0.78px;
}
.xstcn .vipa .tcnbox .row6 a.zizhi{display:block;height: 32px;line-height: 32px;color:#ffffff;background: #2f3844;text-align: center;}

body{background: #fafafa;}
.typeItem {
    text-align: center;
    padding: 10px 0px;
    margin: 15px 0px 0px 0px;
    border-radius: 10px;
    border: 2px solid #fff;
    position: relative;
    overflow: hidden;
    border: 2px solid #ECF7F1;
}

.typeItem div span {
    font-size: 16px;
    font-weight: 500;
    color: rgba(0, 179, 119, 1);
}

.typeItem.active, .typeItem:hover {
    border: 2px solid #00B377;
}

.noborder.active, .noborder:hover {
    border: 0px solid #00B377;
}

.typeItem.active::after {
    content: '';
    border-right: 20px solid #00B377;
    border-top: 20px solid transparent;
    position: absolute;
    bottom: 0px;
    right: 0px;
}

.typeItem.active::before {
    content: '\e645';
    position: absolute;
    right: 0px;
    color: #fff;
    bottom: 1px;
    z-index: 1;
    font-size: 12px!important;
}

.typeItem.active div span, .typeItem:hover div span {
    color: #00B377;
}

.typeItem > div img {
    width: 25px;
    display: block;
    margin: 10px auto;
}

.sui-dropup .caret:before, .sui-dropdown .caret:before{
    color:#ddd;
}

.sui-form.form-horizontal .control-label {
    text-align: right;
    width: 75px;
    height: 36px;
    line-height: 36px;
}

.sui-form.form-horizontal .control-group {
    width: 100%;
    padding: 10px 0px 0px 0px;
    margin-top: 15px;
    border-top: 1px dashed rgba(228, 228, 235, 1);
}

.sui-form.form-horizontal .control-group.noline {
    border-width: 0px;
    padding: 0px;
    padding-top: 0px;
    margin-top: 0px;
}

.sui-form.form-horizontal .control-group input, .sui-form.form-horizontal .control-group select, .sui-form.form-horizontal .control-group .select {
    width: 300px;
    line-height: 36px;
    height: 36px;
    padding-top: 0px;
    padding-bottom: 0px;
}

.sui-form.form-horizontal .control-group .select .dropdown-inner {
    width: 100%;
    height: 100%;
}

.sui-form.form-horizontal .control-group .select .dropdown-inner .caret {
    line-height: 36px;
}


.checkbox-pretty span:before, .radio-pretty span:before{
    top:0px;
}
.sui-form.form-horizontal .controls{
    padding-left: 10px;
}

.sui-form.form-horizontal .controls button{
    width: 83px;
    height: 32px;
    border-radius: 4px;
    background: rgba(0, 220, 130, 1);
    font-size: 14px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
}

.sui-form.form-horizontal .control-group .select{
    width: 310px;
}
.sui-dropdown.dropdown-bordered #drop4{
    height: 36px;
    line-height: 36px;
    margin: 0px 5px;
    font-size: 14px;
    font-weight: 600;
}
.sui-dropdown.dropdown-bordered .dropdown-inner>.sui-dropdown-menu{
    height: 112px;
    width: 312px;
}
/*.sui-dropup .caret:before, .sui-dropdown .caret:before{*/
/*	color:#ddd;*/
/*}*/
.sui-dropdown.dropdown-bordered .dropdown-inner>.sui-dropdown-menu li a{
    height:36px;
    line-height: 36px;
    color:#666;
    background: #fff;
    font-size: 14px;
    font-weight: 600;
}
.sui-dropdown.dropdown-bordered .dropdown-inner>.sui-dropdown-menu li.active{
    background: #f7f7f8;
}
.sui-dropdown.dropdown-bordered .dropdown-inner>.sui-dropdown-menu li.active a{
    background:#f7f7f8;
    color:black;
}
.sui-dropdown-menu>li.active:hover,.sui-dropdown-menu>li.active a:hover{
    background: #f7f7f8;
    color:black!important;
}
.sui-dropdown-menu>li:hover,.sui-dropdown-menu>li a:hover{
    background: #f7f7f8!important;
    color:#666!important;
}

.checkbox-pretty>span:before, .radio-pretty>span:before{
    content: "\e6d5";
    font-size: 18px!important;
    top: -3px;
}
.checkbox-pretty.checked>span:before, .radio-pretty.checked>span:before{
    content: '\e6d4';
    color: #00B377;
    font-size: 18px!important;
    top: -3px;
}

#jc_form{
    border-width: 0px!important;
}


.zsxCon img{
    width: 100%;
    max-width: 100%;
    display: none;
}

.firstFormItem{
    margin-top:10px!important;
    border-width: 0px!important;
}


.d-mainbox .d-rbox .tongyong-info.d-tishi .rtext-co11.noCtrl{
    font-size:14px;
    font-family:PingFangSC;
    font-weight:400;
    color:rgba(255,91,91,1);
    line-height:30px;
}


.lastJian{
    font-size:12px;
    font-family:PingFangSC;
    font-weight:400;
    color:#F04134;
    line-height:17px;
}
/*店铺样式*/
.shop-box{
    width:236px;
    height:254px;
    background:rgba(255,255,255,1);
    border:1px solid rgba(224,224,224,1);
    margin-bottom: 15px;
    display: inline-block;
}
.shop-box .shop-row1{
    width:100px;
    height:100px;
    margin:19px auto 10px;
}
.shop-box .shop-row1 img{
    width:100%;
    height:100%;
    object-fit: cover;
}
.shop-row2{
    text-align: center;
}
.shop-row2 span{
    font-size:14px;
    color:#333;
    font-weight:500;
    display: inline-block;
    margin-right: 7px;
    max-width: 166px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    position:relative;
    top:5px;
}
.shop-row2 img{
    cursor:pointer;
    width:16px;
}
.grn{
    background-color: #00B377;
}
.blu{
    background-color: #5670F0;
}
.shop-row3{
    margin:8px auto 0;
    text-align: center;
}
.shop-row3 span{
    font-size:12px;
    color:#ffffff;
    padding:1px 8px;
    margin-right: 2px;
    border-radius: 2px;
    display: inline-block;
}
.shop-row4{
    margin-top: 16px;
}
.shop-row4 a{
    display: block;
    width:104px;
    height:34px;
    line-height:34px;
    border-radius:4px;
    border:1px solid rgba(0,198,117,1);
    margin:0 auto;
    font-weight:500;
    color:rgba(0,198,117,1);
    text-align: center;
}
.shop-row4 a img{
    width:16px;
}
.top-info{
    font-size: 12px;
    line-height: 17px;
    margin-bottom: 20px;
}
.content-info{
    margin-bottom: 17px;
}
.content-info .content-header{
    overflow:hidden;
}
.content-item{
    overflow:hidden;
    margin-top: 5px;
}
.content-info .content-header .content-left{
    float:left;
    font-size: 14px;
    color: #30303c;
    font-weight: 400;
}
.content-info .content-left-gray{
    float:left;
    font-size: 12px;
    color: #9494a6;
    font-weight: 400;
}
.content-info .content-header .content-right{
    float:right;
    font-size: 14px;
    color: #30303c;
    font-weight: 400;
}
.content-info .content-header .content-right-red{
    float:right;
    color: #ff2121;
}
.content-info .content-right-gray{
    color: #9494a6;
    font-size: 12px;
    float:right;
}
.total-price{
    text-align: right;
    font-size: 14px;
    color: #30303c;
    font-weight: 500;
}
.price-red{
    color: #ff2121;
    font-weight: 500;
    font-size: 14px;
    margin-left: 5px;
}
.tip-content{
    margin-top: 10px;
    font-size: 12px;
    color: #fb9b1f;
    text-align: right;
}
.modal-footer .btn-default{
    background: #ffffff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    width:83px;
    height:32px;
    text-align:center;
    line-height:30px;
}
.modal-footer .btn-primary{
    background: #00c675;
    border: 1px solid #00c675;
    border-radius: 4px;
    width:83px;
    height:32px;
    text-align:center;
    line-height:30px;
}
.yiling{
    display:inline-block;
    font-size: 12px;
    padding:1px 7px;
    border-radius:10px ;
    background: #e7eaec;
    transform: scale(0.8);
    color: #9494a6;
}
.weiling{
    display:inline-block;
    font-size: 12px;
    padding:1px 7px;
    border-radius:10px ;
    background: #ffdbdb;
    transform: scale(0.8);
    color: #ff2121;
}
.pinGo{
    line-height: 34px;
    height: 34px;
    opacity: 1;
    background: #FF6204;
}
.pinGo .timeP{
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    color: #ffffff;
    padding-left: 15px;
}
.pinGo .titles{
    width: 73px;
    height: 20px;
    margin-left: 17px;
    vertical-align: sub;
}
.pinNone{
    height: 50px;
    opacity: 1;
    background: linear-gradient(210deg,#ffd086 2%, #ff8d2f 94%);
}
.pinNone span{
    float: none;
    margin: 0;
}
.pinNone span.spanJ{
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-weight: 400;
    text-align: center;
    color: #ffffff;
    line-height: 20px;
    padding-left: 15px;
    padding-top: 20px;
}
.pinNone span.spanP{
    font-size: 28px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    text-align: center;
    color: #ffffff;
    line-height: 40px;
    padding-left: 30px;
    padding-top: 6px;
}
.pinNone span.spanX{
    display: inline-block;
    height: 21px;
    opacity: 1;
    background: rgba(255,255,255,0.30);
    border-radius: 4px;
    font-size: 14px;
    font-family: PingFangSC, PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    color: #873f00;
    line-height: 21px;
    padding: 0 5px;
    margin-left: 15px;
}
.infoDiv{
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
}
.infoDiv span{
    vertical-align: middle;
    display: inline-block;
    line-height: normal;
    float: none;
    color: #FE5427;
}
.infoDiv .infoTip{
    padding: 3px 6px;
    border-radius: 4px;
    background: #ff0e02;
    color: #ffffff;
    text-align: center;
    font-size: 12px;
}
.infoDiv .infoText{
    font-size: 14px;
    color: #333333;
    padding-left: 10px;
    padding-right: 215px;
}
.infoDiv .pJin{
    display: inline-block;
    vertical-align: middle;
    line-height: normal;
    padding-right: 5px;
}
.infoDiv span{
    margin: 0;
    padding: 0;
}
.infoDiv .spanD{
    width: 180px;
    height: 6px;
    background: rgba(254,84,39,0.3);
    border-radius: 6px;
    position: relative;
}
.infoDiv .spanN{
    width: 100%;
    height: 6px;
    opacity: 1;
    background: #fe5427;
    border-radius: 6px;
    position: absolute;
    top: 0;
    left: 0;
}
.infoDiv .spanT{font-size: 12px}
#slide{position:absolute;height:400px;width:95%;overflow:hidden;padding: 20px 20px;}
#slide li{width:100%;height:34px;line-height:34px;overflow:hidden;display: flex;justify-content: space-between;}
#slide span{color: #666666;font-size: 12px;font-weight: normal}
#slide span.paddingSpan{
    padding-left: 20px;
    /*padding-right: 215px;*/
}

.bg_color{
    background: #F8F8F8;
    border-radius: 4px;
    background-color: #F8F8F8;
    padding: 10px 28px;
    margin: 10px;
}
.bg_color_weight{
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 24px;
    letter-spacing: 0;
    text-align: justify;
}
.bg_color_two{
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 24px;
    letter-spacing: 0;
    text-align: justify;
}
.addSubsidy {
    background-color: #FF2121;
    height: 34px;
    line-height: 34px;
    display: flex;
    justify-content: space-between;
}
.addSubsidy .titlesDiv {
    display: flex;
    align-items: center;
}
.addSubsidy .titlesDiv .titles {
    width: 73px;
    margin-left: 17px;
    vertical-align: sub;
    display: block;
}
.addSubsidy .timer {
    display: inline-block;
    padding-right: 20px;
}
.addSubsidy .timer .timeP {
    color: #fff;
}
.addSubsidy .timer .timeP .font {
    background-color: #fff;
    color: red;
    height: 20px;
    line-height: 20px;
    border-radius: 2px;
    padding: 0 1px;
}
.next-day-service-tag {
    margin-left: 20px;
    padding: 0px 3px 0px 4px;
    border-radius: 4px;
}
.next-day-service-tag span {
    font-size: 12px !important;
}
.next-day-service-span {
    margin-left: 5px;
    color: #333;
    float: none;
}