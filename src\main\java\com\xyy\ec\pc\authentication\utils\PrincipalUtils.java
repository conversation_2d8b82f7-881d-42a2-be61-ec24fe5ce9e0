package com.xyy.ec.pc.authentication.utils;

import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.util.ipip.IPUtils;
import eu.bitwalker.useragentutils.UserAgent;
import org.springframework.beans.BeanUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/4/12 16:28
 * @File PrincipalUtils.class
 * @Software IntelliJ IDEA
 * @Description
 */
public class PrincipalUtils {

    public static MerchantPrincipal toMerchantPrincipal(Object object) {

        MerchantPrincipal merchantPrincipal = new MerchantPrincipal();
        BeanUtils.copyProperties(object, merchantPrincipal);
        return merchantPrincipal;
    }

    public static void initJwtPrincipal(JwtPrincipal jwtPrincipal) {

        Date loginDate = new Date();
        jwtPrincipal.setLastLoginDate(loginDate);
        jwtPrincipal.setLoginTime(loginDate.getTime());
//        jwtPrincipal.setLoginIpAddr(IpUtils.getRealAddress().toJSONString());
        jwtPrincipal.setLoginIpAddr(IPUtils.getIp());
        UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        jwtPrincipal.setLoginOs(userAgent.getOperatingSystem().getName());
        jwtPrincipal.setLoginBrowser(userAgent.getBrowser().getName());
    }
}