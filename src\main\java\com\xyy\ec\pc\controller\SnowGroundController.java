package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.address.api.BaseRegionBusinessApi;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.address.dto.XyyRegionParams;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.enums.MaiDianActionEnum;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.system.business.api.BranchBusinessApi;
import com.xyy.ec.system.business.api.DicAreaBusinessApi;
import com.xyy.ec.system.business.dto.BranchBusinessDto;
import com.xyy.ec.system.business.dto.DicAreaBusinessDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.xyy.ec.pc.config.BranchEnum.*;

/**
 * 埋点参数获取
 */
@RequestMapping("/snowGround")
@Controller
public class SnowGroundController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(SnowGroundController.class);
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;


    @RequestMapping("/getParam")
    @ResponseBody
    public Object findAreaByParentId(@RequestParam("name") String name) throws Exception{
    	try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return this.addError("请登陆后再试！");
            }
            MaiDianActionEnum maiDianActionEnum = MaiDianActionEnum.getEnumByName(name);
            if(maiDianActionEnum == null){
                return this.addError("参数错误");
            }
            Map<String,String> map = SearchUtils.generateMaiDianParams(merchant.getId(),null,null,null,null,maiDianActionEnum);
			return this.addResult("result", map);
		} catch (Exception e) {
			LOGGER.error("埋点数据获取异常",e);
			return this.addError("埋点数据获取异常");
		}
    }

}
