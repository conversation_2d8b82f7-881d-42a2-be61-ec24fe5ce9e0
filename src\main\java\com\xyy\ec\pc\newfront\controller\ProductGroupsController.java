package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.pc.cms.param.CmsRequestParam;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.service.ProductGroupsService;
import com.xyy.ec.pc.newfront.vo.AddPurchaseCalculationParam;
import com.xyy.ec.pc.newfront.vo.ProductGroupsVO;
import com.xyy.ec.pc.newfront.vo.ProductParam;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * pc 改造
 * 商品组相关
 */
@CustomizeCmsResponse
@RequestMapping("/new-front/product-groups")
@RestController
@Slf4j
public class ProductGroupsController {

    @Resource
    private ProductGroupsService productGroupsService;

    /**
     * 获取期望数量的商品流
     *
     * @param terminalType    终端类型
     * @param cmsRequestParam cmsRequestParam
     * @param request         request
     * @return 响应
     */
    @Deprecated
    @PostMapping(value = "/expect-products-list")
    public AjaxResult<ProductGroupsVO> listExpectProducts(@RequestHeader(name = "terminalType", required = false) Integer terminalType,
                                                          @RequestBody CmsRequestParam cmsRequestParam,
                                                          HttpServletRequest request) {
        return productGroupsService.listExpectProducts(terminalType, cmsRequestParam, request);
    }


    /**
     * 计算购物车中商品数量
     *
     * @param param param
     * @return 响应
     */
    @PostMapping(value = "/cal-quantity")
    public AjaxResult<Integer> calculateTheNumOfShoppingCarts(@RequestBody AddPurchaseCalculationParam param) {
        return productGroupsService.calculateTheNumOfShoppingCarts(param);
    }


    @PostMapping("/list-products")
    public AjaxResult<ProductGroupsVO> listProducts(@RequestHeader(name = "isAdmin", required = false) Boolean isAdmin,
                                                    @RequestHeader(name = "terminalType", required = false) Integer terminalType,
                                                    @RequestBody ProductParam param,
                                                    HttpServletRequest request) {
        param.setIsAdmin(isAdmin);
        param.setTerminalType(terminalType);
        return productGroupsService.listProducts(param, request);
    }


}
