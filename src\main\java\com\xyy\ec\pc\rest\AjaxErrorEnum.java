package com.xyy.ec.pc.rest;

public enum AjaxErrorEnum {

    UN_LOGIN(10001, "请登录后重试!"),
    REMOTE_LOGIN(10002, "检测到异地登录!"),
    SPIDER_ACCOUNT(10004, "账号需要验证!"),
    // 老首页用户请求新首页, 前端会根据本code自动跳转, 不需要提示
    OLD_INDEX_USER(10005, null),
    NO_STORE_SELECTED(10006, "请先选择店铺!"),
    ;

    public final int code;
    public final String msg;


    AjaxErrorEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


}
