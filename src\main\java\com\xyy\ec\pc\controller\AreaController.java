package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.address.api.BaseRegionBusinessApi;
import com.xyy.address.dto.XyyRegionBusinessDto;
import com.xyy.address.dto.XyyRegionParams;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.system.business.api.BranchBusinessApi;
import com.xyy.ec.system.business.api.DicAreaBusinessApi;
import com.xyy.ec.system.business.dto.BranchBusinessDto;
import com.xyy.ec.system.business.dto.DicAreaBusinessDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static com.xyy.ec.pc.config.BranchEnum.*;

/**
 * App省市区控制器
 *<AUTHOR>
 *@Date:2018年1月22日上午9:32:55
 */
@RequestMapping("/login/area")
@Controller
public class AreaController extends BaseController {

	private static final Logger LOGGER = LoggerFactory.getLogger(AreaController.class);

//    @Autowired
//    private DicAreaService dicAreaService;

	@Reference(version ="1.0.0")
	private DicAreaBusinessApi dicAreaBusinessApi;

    @Reference(version ="1.0.0")
	private BranchBusinessApi branchBusinessApi;

	@Reference(version = "1.0.0")
	private BaseRegionBusinessApi baseRegionBusinessApi;


    @RequestMapping("/findAreaByParentId.json")
    @ResponseBody
    public Object findAreaByParentId(@RequestParam("parentId") Integer parentId) throws Exception{
    	try {
            List<XyyRegionBusinessDto> dtos = null;
            List<XyyRegionBusinessDto> list = new ArrayList<>();
            if(parentId != null){
                List<String> branchs = getEcAllBranchs();
                XyyRegionParams params = new XyyRegionParams();
                params.setParentCode(parentId);
                dtos = baseRegionBusinessApi.queryRegionByParentCode(params);
				for(XyyRegionBusinessDto dto : dtos){
					dto.setId(dto.getAreaCode());
					if(branchs.contains(dto.getAreaCode().toString().substring(0,2))){
					    list.add(dto);
                    }
				}
            }
			return this.addResult("areaList", list);
		} catch (Exception e) {
			LOGGER.error("区域获取异常",e);
			return this.addError("区域获取异常");
		}
    }

    private void removeHk(List<DicAreaBusinessDto> areaList){
        if (CollectionUtil.isEmpty(areaList)){
        	return;
		}
		Integer areaCode = 810000;
		Iterator<DicAreaBusinessDto> iterator = areaList.iterator();
        while (iterator.hasNext()){
			DicAreaBusinessDto code = iterator.next();
			if (code.getId()!=null && code.getId().equals(areaCode)){
				iterator.remove();
			}
		}
	}

    private List<String> getEcAllBranchs(){
        List<String> branchs = new ArrayList<>();
        List<BranchBusinessDto> allBranchs = branchBusinessApi.getAllBranchs();
        for(BranchBusinessDto dto : allBranchs){
            //过滤全国
            if(ALL_COUNTRY.getKey().equals(dto.getBranchCode())){
                continue;
            }
            if(SHUIXING_COUNTRY.getKey().equals(dto.getBranchCode())){
                continue;
            }
            if(HUOXING_COUNTRY.getKey().equals(dto.getBranchCode())){
                continue;
            }
            String provinceCode = dto.getBranchCode().replace("XS", "").substring(0,2);
            branchs.add(provinceCode);
        }
        return branchs;
    }

}
