/*被委托人认证*/
.auth-box{
  width:980px;
  box-sizing: border-box;
  background:rgba(255,255,255,1);
  border-radius:2px 2px 0px 0px;
  padding:30px 34px 78px;
}
.auth-box .mr-auth .auth-title{
  font-size:14px;
  font-weight:500;
  color:rgba(51,51,51,1);
  line-height:20px;
  margin-bottom: 22px;
}
.auth-box .mr-auth .auth-form ul li{
  line-height: 30px;
  margin: 0 0 20px 0;
  font-size: 14px;
  color: #999999;
}
.auth-box .mr-auth .auth-form ul li .label{
  display: inline-block;
  text-align:right;
  width: 130px;
  font-size: 12px;
  color: #999999;
}
.auth-box .mr-auth .auth-form ul li .input-box{
  display: inline-block;
  margin-left: 5px;
}
.auth-box .mr-auth .auth-form ul li .auth-input{
  padding-left: 8px;
  box-sizing: border-box;
  width: 320px;
  height: 32px;
  border: 1px solid #D9D9D9;
  border-radius: 2px;
}
.auth-box .mr-auth .auth-form ul li .auth-input-left{
  padding-left: 8px;
  box-sizing: border-box;
  width: 230px;
  height: 32px;
  border: 1px solid #D9D9D9;
  border-radius: 2px 0 0 2px;
}
.auth-box .mr-auth .auth-form ul li .code-btn{
  width:89px;
  display: inline-block;
  text-align:center;
  height: 32px;
  line-height:32px;
  box-sizing: border-box;
  border: 1px solid #D9D9D9;
  border-radius: 0 2px 2px 0;
  color: #00DC82;
  border-left:0;
  position:relative;
  top:2.5px;
  left:-4px;
}
.auth-box .mr-auth .auth-form .yzmbox-repe{
  width:89px;
  display: inline-block;
  text-align:center;
  height: 32px;
  line-height:32px;
  box-sizing: border-box;
  border: 1px solid #D9D9D9;
  border-radius: 0 2px 2px 0;
  color: #00DC82;
  border-left:0;
  position:relative;
  top:1.5px;
  color: #999999;
  font-size: 12px;
  display: none;
}
.auth-box .mr-auth .auth-form ul li .auth-input:focus{
  border:1px solid rgba(0,220,130,1);
}
.submit-btn{
  width:78px;
  height:34px;
  background:rgba(153,153,153,1);
  border-radius:2px;
  display: inline-block;
  text-align: center;
  color:#fff;
  line-height:34px;
  outline:none;
  border:none;
}
.submit-btn:hover,.submit-btn:focus{
  color:#fff;
}
.high-light{
  background:#00C675;
}
.auth-box .mr-info{
  margin-top: 38px;
}
.auth-box .mr-info .info-title{
  font-size:12px;
  font-weight:400;
  color:rgba(102,102,102,1);
  line-height:17px;
  margin-bottom: 10px;
}
.auth-box .mr-info .info-content{
  font-size:12px;
  font-weight:400;
  color:rgba(102,102,102,0.66);
  line-height:17px;
}
.sui-modal .modal-header .modal-title {
  font-size: 20px;
  color:rgba(0,0,0,0.75);
  font-weight: normal;
}
.sui-modal .modal-body {
  font-size:14px;
  font-weight:400;
  color:rgba(51,51,51,1);
  line-height:21px;
}