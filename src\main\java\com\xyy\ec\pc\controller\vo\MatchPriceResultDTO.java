package com.xyy.ec.pc.controller.vo;

import com.xyy.ec.pc.shop.vo.ShopInfoVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 匹配返回结果
 */
@Data
public class MatchPriceResultDTO {
    /**匹价商品*/
    private List<MatchLineVO> matchSkuList;
    /** 品种数 */
    private Integer productVarietyNum;
    /** 选中品种数 */
    private Integer productSelectedNum;
    /** 商品总数 */
    private Integer productTotalNum;
    /** 总金额 */
    private BigDecimal totalAmount;
    /** 总优惠金额 */
    private BigDecimal discountAmount;
    /** 运费总金额 */
    private BigDecimal freightTotalAmt;
    /** 实付金额 */
    private BigDecimal payAmount;
    /** 商家数量 */
    private Integer shopCount;

    /**
     * 商家名称集合
     */
    private Set<String> shopNames;


    /**
     * 店铺信息
     */
    private List<ShopInfoVO> shopList;

    /**
     * 为满足起送价公司运费信息列表
     */
    List<FreightInfoVO> unsatisfiedStartPriceList;
    /**
     * 未包邮公司运费信息列
     */
    List<FreightInfoVO> unsatisfiedFreeShippingList;

}
