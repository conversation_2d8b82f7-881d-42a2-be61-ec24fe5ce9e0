package com.xyy.ec.pc.cms.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.layout.buinese.ecp.results.RuleExhibitionGroupSimpleAggregateCsuCategoryDTO;
import com.xyy.ec.pc.cms.vo.CmsProductFlowAggCategoryVO;
import com.xyy.ec.search.engine.dto.layout.EcSearchSimpleAggregateCsuCategoryDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class CmsProductFlowAggCategoryVOHelper {

    /**
     * 创建
     *
     * @param ecSearchSimpleAggregateCsuCategoryDTO
     * @return
     */
    public static CmsProductFlowAggCategoryVO createForSearch(EcSearchSimpleAggregateCsuCategoryDTO ecSearchSimpleAggregateCsuCategoryDTO) {
        if (Objects.isNull(ecSearchSimpleAggregateCsuCategoryDTO)) {
            return null;
        }
        return CmsProductFlowAggCategoryVO.builder()
                .id(ecSearchSimpleAggregateCsuCategoryDTO.getId())
                .name(ecSearchSimpleAggregateCsuCategoryDTO.getName())
                .build();
    }

    public static List<CmsProductFlowAggCategoryVO> createsForSearch(List<EcSearchSimpleAggregateCsuCategoryDTO> ecSearchSimpleAggregateCsuCategoryDTOS) {
        if (CollectionUtils.isEmpty(ecSearchSimpleAggregateCsuCategoryDTOS)) {
            return Lists.newArrayList();
        }
        return ecSearchSimpleAggregateCsuCategoryDTOS.stream().map(item -> createForSearch(item)).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 创建
     *
     * @param ecSearchSimpleAggregateCsuCategoryDTO
     * @return
     */
    public static CmsProductFlowAggCategoryVO createForLayout(RuleExhibitionGroupSimpleAggregateCsuCategoryDTO ecSearchSimpleAggregateCsuCategoryDTO) {
        if (Objects.isNull(ecSearchSimpleAggregateCsuCategoryDTO)) {
            return null;
        }
        return CmsProductFlowAggCategoryVO.builder()
                .id(ecSearchSimpleAggregateCsuCategoryDTO.getId())
                .name(ecSearchSimpleAggregateCsuCategoryDTO.getName())
                .build();
    }

    public static List<CmsProductFlowAggCategoryVO> createsForLayout(List<RuleExhibitionGroupSimpleAggregateCsuCategoryDTO> ecSearchSimpleAggregateCsuCategoryDTOS) {
        if (CollectionUtils.isEmpty(ecSearchSimpleAggregateCsuCategoryDTOS)) {
            return Lists.newArrayList();
        }
        return ecSearchSimpleAggregateCsuCategoryDTOS.stream().map(item -> createForLayout(item)).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }



}
