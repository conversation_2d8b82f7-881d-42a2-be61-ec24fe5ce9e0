package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.api.ExhibitionBuineseApi;
import com.xyy.ec.layout.buinese.dto.ExhibitionBuineseDto;
import com.xyy.ec.marketing.client.api.query.CouponForReceiveCenterQueryService;
import com.xyy.ec.marketing.client.api.query.CouponForTradeQueryService;
import com.xyy.ec.marketing.client.api.query.MyCouponSearchService;
import com.xyy.ec.marketing.client.api.query.query.SkuForCouponQuery;
import com.xyy.ec.marketing.client.api.query.resp.CouponsInSettleView;
import com.xyy.ec.marketing.client.dto.VoucherDto;
import com.xyy.ec.marketing.client.dto.coupon.MyCouponDto;
import com.xyy.ec.marketing.client.dto.coupon.MyCouponPageDto;
import com.xyy.ec.marketing.client.dto.coupon.VoucherSkuImageDto;
import com.xyy.ec.marketing.exception.MarketingBusinessException;
import com.xyy.ec.marketing.hyperspace.api.CouponApi;
import com.xyy.ec.marketing.hyperspace.api.ShopCouponForShopcartQueryService;
import com.xyy.ec.marketing.hyperspace.api.dto.OneClickRestockCouponDTO;
import com.xyy.ec.marketing.hyperspace.api.dto.OneClickRestockCouponTemplateDTO;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponBaseDto;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponCenterDto;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponCsuImageDto;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.api.OrderVoucherBusinessApi;
import com.xyy.ec.order.business.api.ShoppingCartBusinessApi;
import com.xyy.ec.order.business.api.ecp.order.EcpOrderBusinessApi;
import com.xyy.ec.order.business.dto.ShoppingVoucherDto;
import com.xyy.ec.order.business.exception.ServiceException;
import com.xyy.ec.order.business.model.ServiceResponse;
import com.xyy.ec.order.dto.voucher.SkuForVoucherQuery;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.constants.ReceiveCenterCouponSortConstants;
import com.xyy.ec.pc.controller.vo.CouponConditionVO;
import com.xyy.ec.pc.helper.VoucherDtoHelper;
import com.xyy.ec.pc.model.VoucherDetailDTO;
import com.xyy.ec.pc.model.dto.SkuInfoForVoucher;
import com.xyy.ec.pc.param.CouponSkuConditionParam;
import com.xyy.ec.pc.rpc.HyperSpaceRpc;
import com.xyy.ec.pc.service.CodeItemService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.product.vouchercenter.VoucherCenterService;
import com.xyy.ec.pc.shop.service.ShopService;
import com.xyy.ec.pc.shop.vo.ShopStaticsVo;
import com.xyy.ec.pc.util.BeanUtils;
import com.xyy.ec.product.business.dto.ProductConditionDTO;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import com.xyy.ec.product.business.ecp.out.promotion.api.ProductForPromotionApi;
import com.xyy.ec.product.business.ecp.vouchercenter.api.ProductVoucherCenterBusinessApi;
import com.xyy.ec.product.business.ecp.vouchercenter.dto.CsuImageVoucherDto;
import com.xyy.ec.product.business.ecp.vouchercenter.dto.VoucherCsuSearchDto;
import com.xyy.ec.shop.server.business.results.ShopStatisticsReportDTO;
import com.xyy.ms.marketing.nine.chapters.api.coupon.ShopCouponForAccountComputeApi;
import com.xyy.ms.marketing.nine.chapters.api.order.CouponForOrderApi;
import com.xyy.ms.marketing.nine.chapters.common.constants.PromoEnum;
import com.xyy.ms.marketing.nine.chapters.dto.ShopCouponForAccountDto;
import com.xyy.ms.marketing.nine.chapters.dto.coupon.CouponDetailForOrderDto;
import com.xyy.ms.marketing.nine.chapters.vo.AccountCsuVo;
import com.xyy.ms.promotion.business.api.pc.VoucherForPcBusinessApi;
import com.xyy.ms.promotion.business.api.pc.VoucherTemplateForPcBusinessApi;
import com.xyy.ms.promotion.business.common.ErrorCodeEum;
import com.xyy.ms.promotion.business.common.ResultDTO;
import com.xyy.ms.promotion.business.common.constants.VoucherEnum;
import com.xyy.ms.promotion.business.common.response.PromoResp;
import com.xyy.ms.promotion.business.dto.MerchantRequestDTO;
import com.xyy.ms.promotion.business.dto.SkuExtendDTO;
import com.xyy.ms.promotion.business.dto.voucher.*;
import com.xyy.scm.constant.Constant;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券控制器
 */
@RequestMapping("/merchant/center/voucher")
@Controller
public class VoucherController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(VoucherController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private VoucherForPcBusinessApi promotionVoucherApi;
    @Reference(version = "1.0.0")
    private VoucherTemplateForPcBusinessApi voucherTemplateApi;
    @Reference(version = "1.0.0")
    ShoppingCartBusinessApi shoppingCartBusinessApi;
    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;
    @Reference(version = "1.0.0")
    private ShopCouponForShopcartQueryService shopCouponForShopcartQueryService;
    @Reference(version = "1.0.0")
    private MyCouponSearchService myCouponSearchService;
    @Reference(version = "1.0.0")
    ProductForPromotionApi productForPromotionApi;
    @Reference(version = "1.0.0")
    private EcpOrderBusinessApi ecpOrderBusinessApi;
    @Reference(version = "1.0.0")
    private OrderVoucherBusinessApi orderVoucherBusinessApi;
    @Reference(version = "1.0.0")
    private CouponForOrderApi couponForOrderApi;
    @Reference(version = "1.0.0")
    private CodeItemService codeItemService;

    @Reference(version = "1.0.0")
    private ShopCouponForAccountComputeApi shopCouponForAccountComputeApi;

    @Reference(version = "1.0.0")
    private CouponForTradeQueryService couponForTradeQueryService;

    @Reference(version = "1.0.0")
    ProductVoucherCenterBusinessApi productVoucherCenterBusinessApi;

    // 跨店券店铺列表凑单页，搜索时shopCodeList最大长度
    @Value("${voucher.sku.recall.max.size}")
    private int voucherSkuRecallMaxSize;

    @Reference(version = "1.0.0")
    private CouponForReceiveCenterQueryService couponForReceiveCenterQueryService;

    @Autowired
    private ShopService shopService;

    @Value("${voucher.info.with.shop.flag:true}")
    private boolean voucherInfoWithShop;

    @Autowired
    private HyperSpaceRpc hyperSpaceRpc;

    @Reference(version = "1.0.0")
    private CouponApi couponApi;

    @Reference(version = "1.0.0")
    private ExhibitionBuineseApi exhibitionBuineseApi;

    @Autowired
    private VoucherCenterService voucherCenterService;

    @Reference(version = "1.0.0")
    private VoucherForPcBusinessApi voucherForPcBusinessApi;

    /**
     * 获取商户优惠券对应的商品列表
     *
     * @param voucher
     * @return
     */
    @SuppressWarnings("rawtypes")
    @RequestMapping("/findSkuInfo.htm")
    public ModelAndView findSkuInfoByVoucherTemplateId(VoucherDTO voucher, Page page, HttpServletRequest request) {
        Map<String, Object> objModel = new HashMap<>();
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            //用户数据
            objModel.put("merchant", merchant);
            //用户id
            objModel.put("merchantId", merchant.getId());
            Long voucherTemplateId = voucher.getTemplateId();
            if (null == voucherTemplateId) {
                voucherTemplateId = voucher.getVoucherTemplateId();
            }

            ResultDTO<VoucherDTO> voucherDTOResultDTO = voucherTemplateApi.getVoucherTemplateWithSku(merchant.getRegisterCode(), voucherTemplateId);
            if (null == voucherDTOResultDTO || ErrorCodeEum.FAIL.getErrorCode() == voucherDTOResultDTO.getErrorCode() || null == voucherDTOResultDTO.getData()) {
                return new ModelAndView(new RedirectView("/search/skuInfoByCategory.htm?all=all",true,false));
            }
            VoucherDTO voucherDTO = voucherDTOResultDTO.getData();

            String pcUrl = voucherDTO.getPcUrl();
            //如果有优惠卷模板配置了对应的跳转url，则跳转
            if (StringUtils.isNotEmpty(pcUrl)) {
                return new ModelAndView(new RedirectView(pcUrl,true,false));
            }
            //如果优惠卷没有配置跳转url，除了商品卷，其余的卷都跳转到所有商品页面
            if (voucherDTO.getVoucherType() == null ||
                    VoucherEnum.VoucherTypeEnum.PRODUCT.getId() != voucherDTO.getVoucherType()) {
                //return new ModelAndView(new RedirectView("/search/skuInfoByCategory.htm?all=all"));
                return new ModelAndView(new RedirectView("/selfShop/center/shopSkuInfo.htm?orgId=&shopCode=" + voucherDTO.getShopCode(),true,false));
            }

            List<Long> skuIdList = voucherDTO.getAssignProductIds();
            if (org.apache.commons.collections.CollectionUtils.isEmpty(skuIdList)) {
                //return new ModelAndView(new RedirectView("/search/skuInfoByCategory.htm?all=all"));
                return new ModelAndView(new RedirectView("/selfShop/center/shopSkuInfo.htm?orgId=&shopCode=" + voucherDTO.getShopCode(),true,false));
            }

            boolean isShowFragileGoods = merchantBussinessApi.checkFragileLimitedByMerchantId(merchant.getId());
            ProductConditionDTO productConditionDTO = new ProductConditionDTO();
            productConditionDTO.setSkuIdList(skuIdList);
            productConditionDTO.setMerchantId(merchant.getId());
            productConditionDTO.setBranchCode(merchant.getRegisterCode());
            productConditionDTO.setIsShowFragileGoods(isShowFragileGoods ? ProductEnumDTO.ShowFragileEnum.NOT_SHOW_FRAGILE.getCode() : ProductEnumDTO.ShowFragileEnum.IS_SHOW_FRAGILE.getCode());
            productConditionDTO.setPageNum(page.getOffset());
            productConditionDTO.setPageSize(20);

            ListSkuSearchData skuSearchDataDto = productForPromotionApi.findProductInfoBySkuIdList(productConditionDTO);
            //ListSkuSearchData skuSearchDataDto = productBusinessApi.findEsProductBySkuIdList(productConditionDTO);
            if (null == skuSearchDataDto || org.apache.commons.collections.CollectionUtils.isEmpty(skuSearchDataDto.getSkuDtoList())) {
                //return new ModelAndView(new RedirectView("/search/skuInfoByCategory.htm?all=all"));
                return new ModelAndView(new RedirectView("/selfShop/center/shopSkuInfo.htm?orgId=&shopCode=" + voucherDTO.getShopCode(),true,false));

            }

            List<ListProduct> skuPOJOList = skuSearchDataDto.getSkuDtoList();
            PageInfo<ListProduct> productDtoPageInfo = new PageInfo<>();
            productDtoPageInfo.setList(skuPOJOList);
            productDtoPageInfo.setTotal(skuSearchDataDto.getCount());
            productDtoPageInfo.setPages(skuSearchDataDto.getCount() % 20 == 0 ? Integer.parseInt(String.valueOf(skuSearchDataDto.getCount() / 20)) : Integer.parseInt(String.valueOf((skuSearchDataDto.getCount() / 20 + 1))));
            productDtoPageInfo.setPageNum(page.getOffset()<1?1:page.getOffset());
            productDtoPageInfo.setPageSize(20);

            objModel.put("requestUrl", this.getRequestUrl(request));
            //商品搜索页分页数据
            objModel.put("pager", productDtoPageInfo);
            return new ModelAndView("/sku_search_by_templateId_list.ftl", objModel);
        } catch (Exception e) {
            LOGGER.error("获取商户优惠券对应的商品列表异常：", e);
            return new ModelAndView("/sku_search_by_templateId_list.ftl", objModel);
        }
    }

    /**
     * 获取商户优惠券信息
     *
     * @param voucher
     * @return
     */
    @RequestMapping("/findAllVoucherInfo.htm")
    public ModelAndView findAllVoucherInfo(VoucherDTO voucher, Page page, HttpServletRequest request) {
        Map<String, Object> model = new HashMap<>();

        try {
            String requestUrl = this.getRequestUrl(request);
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            if (null != page && page.getOffset() == 0) {
                page.setOffset(1);
            }

            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(12);
            if (null == page || 0 == page.getLimit()) {
                pageInfo.setPageNum(1);

            } else {
                pageInfo.setPageNum(page.getOffset());
            }
            Long merchantId = merchant.getId();
            int queryVoucherState = VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState();
            if (null != voucher && null != voucher.getState()) {
                queryVoucherState = voucher.getState();
            }
            ResultDTO<MyVoucherPageDTO> voucherPageResult = promotionVoucherApi.getMerchantAllVoucher(pageInfo, merchantId, queryVoucherState);

            MyVoucherPageDTO myVoucherPageDTO = null != voucherPageResult.getData() ? voucherPageResult.getData() : null;
            //已领取
            model.put("notUsedNum", null != myVoucherPageDTO ? myVoucherPageDTO.getUnsueNum() : 0);
            //已用完
            model.put("usedNum", null != myVoucherPageDTO ? myVoucherPageDTO.getUsedNum() : 0);
            //失效
            model.put("expiredNum", null != myVoucherPageDTO ? myVoucherPageDTO.getExpiredNum() : 0);

            PageInfo<MyVoucherDTO> myVoucherDTOPageInfo = new PageInfo<MyVoucherDTO>();
            if(myVoucherPageDTO!=null){
                myVoucherDTOPageInfo = myVoucherPageDTO.getVoucherDTOPageInfo();
                List<MyVoucherDTO> list = myVoucherDTOPageInfo.getList();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
                    list.stream().forEach(obj -> {
                        if (Objects.equals(obj.getVoucherType(), 8) && Objects.equals(obj.getVoucherState(), 1)) {
                            obj.setMoneyInVoucher(obj.getDiscount());
                        }
                    });
                }
            }else{
                myVoucherDTOPageInfo.setList(null);
                myVoucherDTOPageInfo.setTotal(0);
                myVoucherDTOPageInfo.setPages(0);
                myVoucherDTOPageInfo.setPageNum(page.getOffset());
                myVoucherDTOPageInfo.setPageSize(page.getLimit());
            }
            model.put("pager", myVoucherDTOPageInfo);
//            List<MyVoucherDTO> voucherDTOList = Lists.newArrayList();
//            if (null != myVoucherPageDTO && null != myVoucherPageDTO.getVoucherDTOPageInfo() && CollectionUtils.isNotEmpty(myVoucherPageDTO.getVoucherDTOPageInfo().getList())) {
//                voucherDTOList = myVoucherPageDTO.getVoucherDTOPageInfo().getList();
//            }
//            model.put("voucherList", voucherDTOList);
            model.put("requestUrl", requestUrl);
            model.put("center_menu", "voucher");
            model.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            model.put("merchantId", merchantId);
            model.put("queryVoucherState", queryVoucherState);
            return new ModelAndView("/voucher/voucher.ftl", model);
        } catch (Exception e) {
            LOGGER.error("获取商户优惠券失败：", e);
            return new ModelAndView("/voucher/voucher.ftl", model);
        }
    }

    /**
     * 获取商户优惠券信息
     *
     * @param voucher
     * @return
     */
    @RequestMapping("/findAllVoucherInfoWithShop.htm")
    public ModelAndView findAllVoucherInfoWithShop(VoucherDTO voucher, Page page, HttpServletRequest request) {
        Map<String, Object> model = new HashMap<>();

        try {
            String requestUrl = this.getRequestUrl(request);
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            if (null != page && page.getOffset() == 0) {
                page.setOffset(1);
            }

            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(12);
            if (null == page || 0 == page.getLimit()) {
                pageInfo.setPageNum(1);

            } else {
                pageInfo.setPageNum(page.getOffset());
            }
            Long merchantId = merchant.getId();
            int queryVoucherState = VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState();
            if (null != voucher && null != voucher.getState()) {
                queryVoucherState = voucher.getState();
            }
            MerchantRequestDTO merchantRequestDTO = new MerchantRequestDTO();
            merchantRequestDTO.setMerchantId(merchantId);

/*
            PromoResp<MyVoucherPageDTO> voucherResp = shopCouponForShopcartQueryService.getAllMerchantVoucher(merchantRequestDTO, queryVoucherState, false);
*/
            MyCouponPageDto allMerchantVoucher;
            try{
                allMerchantVoucher = myCouponSearchService.getAllMerchantVoucher(merchantRequestDTO.getMerchantId(), queryVoucherState, true);
            }catch (MarketingBusinessException e){
                LOGGER.error("获取商户优惠券失败,merchantId={},queryVoucherState={}",merchantRequestDTO.getMerchantId(),queryVoucherState, e);
                return new ModelAndView("/voucher/voucher.ftl", model);
            }


            //已领取
            model.put("notUsedNum", null != allMerchantVoucher ? allMerchantVoucher.getUnUseNum() : 0);
            //已用完
            model.put("usedNum", 0);
            //失效
            model.put("expiredNum", 0);

            PageInfo<MyCouponDto> myVoucherDTOPageInfo = new PageInfo<>();
            if(allMerchantVoucher!=null){
                List<MyCouponDto> myVoucherList = allMerchantVoucher.getCouponDtoList();
                long total = myVoucherList.size();
                long totalPage = 0 == pageInfo.getPageSize() ? 0 : total % pageInfo.getPageSize() == 0 ? total / pageInfo.getPageSize() : (total / pageInfo.getPageSize()) + 1;
                //分页
                List<List<MyCouponDto>> pageList = Lists.partition(myVoucherList, pageInfo.getPageSize());
                List<MyCouponDto> subList = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(pageList)){
                    if (voucherInfoWithShop) {
                        if (pageInfo.getPageNum() > 0 && pageList.size() >= pageInfo.getPageNum()) {
                            subList = pageList.get(pageInfo.getPageNum()-1);
                        } else {
                            // 不做任何处理
                        }
                    } else {
                        subList = pageList.get(pageInfo.getPageNum()-1);
                    }
                }
                if (CollectionUtils.isNotEmpty(subList)) {
                    setMyCouponCsuImage(subList, voucher.getMerchantId());
                    subList.stream().forEach(obj -> {
                        if (Objects.equals(obj.getVoucherType(), 8) && Objects.equals(obj.getVoucherState(), 1)) {
                            obj.setMoneyInVoucher(obj.getDiscount());
                        }
                    });
                }
                myVoucherDTOPageInfo.setList(subList);
                myVoucherDTOPageInfo.setTotal(total);
                myVoucherDTOPageInfo.setPages((int)totalPage);
                myVoucherDTOPageInfo.setPageNum(page.getOffset());
                myVoucherDTOPageInfo.setPageSize(page.getLimit());

            }else{
                myVoucherDTOPageInfo.setList(null);
                myVoucherDTOPageInfo.setTotal(0);
                myVoucherDTOPageInfo.setPages(0);
                myVoucherDTOPageInfo.setPageNum(page.getOffset());
                myVoucherDTOPageInfo.setPageSize(page.getLimit());
            }
            model.put("pager", myVoucherDTOPageInfo);
            model.put("requestUrl", requestUrl);
            model.put("center_menu", "voucher");
            model.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            model.put("merchantId", merchantId);
            model.put("queryVoucherState", queryVoucherState);
            return new ModelAndView("/voucher/voucher.ftl", model);
        } catch (Exception e) {
            LOGGER.error("获取商户优惠券失败：", e);
            PageInfo<MyCouponDto> myVoucherDTOPage = new PageInfo<>();

            model.put("pager", myVoucherDTOPage);
            String requestUrl = this.getRequestUrl(request);
            model.put("requestUrl", requestUrl);
            model.put("center_menu", "voucher");
//            MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
//            Long merchantId = merchant.getId();
//            model.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
            model.put("merchantId", 0);
            model.put("queryVoucherState", 0);
            return new ModelAndView("/voucher/voucher.ftl", model);
        }
    }

    /**
     * 领取优惠券
     *
     * @param merchantId
     * @param voucherId
     * @return
     */
    @RequestMapping("/receiveVoucher")
    @ResponseBody
    public Object receiveVoucher(@RequestParam(value = "merchantId", required = false) Long merchantId,
                                 @RequestParam(value = "voucherId", required = false) Long voucherId,
                                 @RequestParam(value = "voucherTemplateId", required = false) Long voucherTemplateId) {
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (merchant == null || (null != merchantId && merchantId != merchant.getId().longValue())) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }

            ReceiveVoucherRequestDTO voucherRequestDTO = new ReceiveVoucherRequestDTO();
            voucherRequestDTO.setMerchantId(merchant.getId());
            voucherRequestDTO.setVoucherTemplateId(voucherTemplateId);
            ResultDTO<VoucherExtendDTO> resultDTO = promotionVoucherApi.receiveVoucher(voucherRequestDTO);
            VoucherExtendDTO voucherExtendDTO = resultDTO.getData();
            if(null == resultDTO){
                return this.addError("领取失败");
            }
            if(ErrorCodeEum.SUCCESS.getErrorCode() != resultDTO.getErrorCode()){
                return this.addError(resultDTO.getErrorCode(), resultDTO.getErrorMsg());
            }

            Map<String, Object> resultMap = this.addResult("优惠券领取成功");
            resultMap.put("validDate", voucherExtendDTO.getValidDate());
            resultMap.put("expireDate", voucherExtendDTO.getExpireDate());
            resultMap.put("voucherType", voucherExtendDTO.getVoucherType());
            resultMap.put("voucherTemplateId", voucherTemplateId);
            resultMap.put("jumpUrl", voucherExtendDTO.getPcUrl());
            resultMap.put("noReceiveCount", 0);
            return resultMap;
        }  catch (Exception e) {
            LOGGER.error("优惠券领取失败：", e);
            return this.addError("优惠券领取失败！");
        }
    }

    /**
     * 获取待领取优惠券
     *
     * @param merchantId
     * @return
     */
    @RequestMapping("/findPreReceiveVoucher")
    @ResponseBody
    public Object findPreReceiveVoucher(@RequestParam("merchantId") Long merchantId, Page page) {
        try {
            VoucherDTO voucher = new VoucherDTO();
            voucher.setMerchantId(merchantId);
            voucher.setState(1);

            ResultDTO<MyVoucherPageDTO> voucherPageResult = promotionVoucherApi.getMerchantAllVoucher(buildOnePage(), merchantId, VoucherEnum.MerchantVoucherStatusEnum.NO_RECEIVE.getState());
            if(ErrorCodeEum.SUCCESS.getErrorCode() != voucherPageResult.getErrorCode()){
                return this.addError(voucherPageResult.getErrorCode(), voucherPageResult.getErrorMsg());
            }

            MyVoucherPageDTO myVoucherPageDTO = null != voucherPageResult ? voucherPageResult.getData() : null;
            List<MyVoucherDTO> voucherDTOList = Lists.newArrayList();
            if (null != myVoucherPageDTO && null != myVoucherPageDTO.getVoucherDTOPageInfo() && null != myVoucherPageDTO.getVoucherDTOPageInfo().getList()) {
                voucherDTOList = myVoucherPageDTO.getVoucherDTOPageInfo().getList();
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(voucherDTOList)) {
                    voucherDTOList.stream().forEach(obj -> {
                        if (Objects.equals(obj.getVoucherType(), 8) && Objects.equals(obj.getVoucherState(), 1)) {
                            obj.setMoneyInVoucher(obj.getDiscount());
                        }
                    });
                }
            }
            return this.addResult("preReceiveVoucherList", voucherDTOList);
        } catch (Exception e) {
            LOGGER.error("获取待领取优惠券失败：", e);
            return this.addError("获取待领取优惠券失败！");
        }
    }

    /**
     * 删除优惠券
     * 逻辑删除
     *
     * @param merchantId
     * @param voucherId
     * @return
     */
    @RequestMapping("/removeVoucher")
    @ResponseBody
    public Object removeVoucher(@RequestParam("merchantId") Long merchantId, @RequestParam("voucherId") Long voucherId) {
        try {
            MerchantRequestDTO merchantRequestDTO = new MerchantRequestDTO();
            merchantRequestDTO.setMerchantId(merchantId);
            ResultDTO resultDTO = promotionVoucherApi.updateVoucherState(merchantRequestDTO, null, Lists.newArrayList(voucherId), String.valueOf(VoucherEnum.MerchantVoucherStatusEnum.DELETE.getState()));
            if(ErrorCodeEum.SUCCESS.getErrorCode() != resultDTO.getErrorCode()){
                return this.addError(resultDTO.getErrorCode(), resultDTO.getErrorMsg());
            }
            return this.addResult("优惠券刪除成功");
        } catch (IllegalArgumentException e) {
            LOGGER.error("删除优惠券异常：", e);
            return this.addError(e.getMessage());
        } catch (Exception e) {
            LOGGER.error("优惠券刪除失敗：", e);
            return this.addError("优惠券刪除失敗");
        }
    }

    /**
     * 获取商户优惠券对应的商品列表
     *
     * @param voucher
     * @return
     */
    @SuppressWarnings("rawtypes")
    @RequestMapping("/findShangpinquanSkuInfo.htm")
    public ModelAndView findShangpinquanSkuInfo(VoucherDetailDTO voucher, Page page, HttpServletRequest request) {
        VoucherDetailDTO voucherDTO = new VoucherDetailDTO();
        Map<String, Object> objModel = new HashMap<>();
        Integer isSelectSkuNum = 0;
        BigDecimal selectSkuAmount = new BigDecimal(0);
        ProductConditionDTO productConditionDTO = new ProductConditionDTO();
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            //调用卷模板信息
            ResultDTO<VoucherDTO> voucherResultDTO = voucherTemplateApi.getVoucherTemplateWithSku(merchant.getRegisterCode(), voucher.getVoucherTemplateId());
            if (voucherResultDTO != null && voucherResultDTO.getData() != null) {
                VoucherDTO tempVoucherDTO = voucherResultDTO.getData();
                voucherDTO.setMoneyInVoucher(tempVoucherDTO.getMoneyInVoucher());
                voucherDTO.setMinMoneyToEnable(tempVoucherDTO.getMinMoneyToEnable());
                //调用购物车接口  返回   isSelectSkuNum   selectSkuAmount  参数  入参会员id   重新赋值
                //shoppingCartCacheHelper.getCartSelectList(merchantId);
                ServiceResponse<ShoppingVoucherDto> shoppingVoucherDto = shoppingCartBusinessApi.findCartNumAndPrice(merchant.getId(), tempVoucherDTO.getMoneyInVoucher(), tempVoucherDTO.getMinMoneyToEnable(), tempVoucherDTO.getAssignProductIds());
                if (shoppingVoucherDto != null && shoppingVoucherDto.getResult() != null){
                    ShoppingVoucherDto shoppingVoucherDtoObj = shoppingVoucherDto.getResult();
                    isSelectSkuNum = shoppingVoucherDtoObj.getIsSelectSkuNum();
                    selectSkuAmount = shoppingVoucherDtoObj.getSelectSkuAmount();
                }
                if (tempVoucherDTO.getMinMoneyToEnable().compareTo(selectSkuAmount) > 0) {
                    voucherDTO.setNoEnoughMoney(tempVoucherDTO.getMinMoneyToEnable().subtract(selectSkuAmount).setScale(2, RoundingMode.HALF_UP));
                }else {
                    voucherDTO.setNoEnoughMoney(new BigDecimal(0));
                }
                productConditionDTO.setSkuIdList(tempVoucherDTO.getAssignProductIds());
            }else {
                voucherDTO.setNoEnoughMoney(new BigDecimal(0));
            }
            voucherDTO.setIsSelectSkuNum(isSelectSkuNum);
            voucherDTO.setSelectSkuAmount(selectSkuAmount);
            voucherDTO.setVoucherTemplateId(voucher.getVoucherTemplateId());
            int oldOffset = 0;
            page.setLimit(20);//一页设置20条商品数据
            if (null != page.getOffset()) {
                oldOffset = page.getOffset();
                page.setOffset((oldOffset > 0 ? (page.getOffset() - 1) : 0)
                        * page.getLimit());
            }
            Integer pageNum = (oldOffset + 20) /20;
            //处理分页  assignProductIds
            productConditionDTO.setBranchCode(merchantPrincipal.getRegisterCode());
            productConditionDTO.setPageNum(pageNum);
            productConditionDTO.setPageSize(page.getLimit());
            productConditionDTO.setMerchantId(merchant.getId());
            ListSkuSearchData skuSearchDataDto = productForPromotionApi.findProductInfoBySkuIdList(productConditionDTO);
            //ListSkuSearchData skuSearchDataDto = productBusinessApi.findEsProductBySkuIdList(productConditionDTO);
            Page<ListProduct> skuPOJOPage = new Page<>();
            if(skuSearchDataDto != null){
                skuPOJOPage.setTotal(skuSearchDataDto.getCount());
                skuPOJOPage.setRows(skuSearchDataDto.getSkuDtoList());
            }
            skuPOJOPage.setLimit(page.getLimit());
            skuPOJOPage.setOffset((oldOffset > 0 ? oldOffset : 0));
            String requestUrl = this.getRequestUrl(request);
            skuPOJOPage.setRequestUrl(requestUrl);
            //用户数据
            objModel.put("merchant", merchant);
            //用户id
            objModel.put("merchantId", merchant.getId());
            objModel.put("voucherDTO", voucherDTO);
            //商品搜索页分页数据
            objModel.put("pager", skuPOJOPage);
            return new ModelAndView("/shangpinquan_sku_list.ftl", objModel);
        } catch (Exception e) {
            LOGGER.error("获取商户优惠券对应的商品列表：", e);
            return new ModelAndView("/shangpinquan_sku_list.ftl", objModel);
        }
    }

    @RequestMapping("/findShangpinquanInfo.json")
    @ResponseBody
    public Object findShangpinquanInfo(Long voucherTemplateId) {
        VoucherDetailDTO voucherDTO = new VoucherDetailDTO();
        Integer isSelectSkuNum = 0;
        BigDecimal selectSkuAmount = new BigDecimal(0);
        MerchantBussinessDto merchant = null;
        try {
            MerchantPrincipal  merchantObj =  (MerchantPrincipal)xyyIndentityValidator.currentPrincipal();
            merchant = merchantObj;
        } catch (Exception e) {
            LOGGER.error("查询会员用户信息异常：",e);
        }
        if (merchant == null) {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }
        try {
            //调用卷模板信息
            ResultDTO<VoucherDTO> voucherResultDTO = voucherTemplateApi.getVoucherTemplateWithSku(merchant.getRegisterCode(), voucherTemplateId);
            if (voucherResultDTO != null && voucherResultDTO.getData() != null) {
                VoucherDTO tempVoucherDTO = voucherResultDTO.getData();
                voucherDTO.setMoneyInVoucher(tempVoucherDTO.getMoneyInVoucher());
                voucherDTO.setMinMoneyToEnable(tempVoucherDTO.getMinMoneyToEnable());
                //调用购物车接口  返回   isSelectSkuNum   selectSkuAmount  参数  入参会员id   重新赋值
                //shoppingCartCacheHelper.getCartSelectList(merchantId);
                ServiceResponse<ShoppingVoucherDto> shoppingVoucherDto = shoppingCartBusinessApi.findCartNumAndPrice(merchant.getId(), tempVoucherDTO.getMoneyInVoucher(), tempVoucherDTO.getMinMoneyToEnable(), tempVoucherDTO.getAssignProductIds());
                if (shoppingVoucherDto != null && shoppingVoucherDto.getResult() != null){
                    ShoppingVoucherDto shoppingVoucherDtoObj = shoppingVoucherDto.getResult();
                    isSelectSkuNum = shoppingVoucherDtoObj.getIsSelectSkuNum();
                    selectSkuAmount = shoppingVoucherDtoObj.getSelectSkuAmount();
                }
                if (tempVoucherDTO.getMinMoneyToEnable().compareTo(selectSkuAmount) > 0) {
                    voucherDTO.setNoEnoughMoney(tempVoucherDTO.getMinMoneyToEnable().subtract(selectSkuAmount).setScale(2, RoundingMode.HALF_UP));
                }else {
                    voucherDTO.setNoEnoughMoney(new BigDecimal(0));
                }
            }else {
                voucherDTO.setNoEnoughMoney(new BigDecimal(0));
            }
            voucherDTO.setIsSelectSkuNum(isSelectSkuNum);
            voucherDTO.setSelectSkuAmount(selectSkuAmount);
            voucherDTO.setVoucherTemplateId(voucherTemplateId);
//            voucherDTO = voucherTemplateService.findShangpinquanCheapData(merchant.getId(), voucherTemplateId);
        } catch (Exception e) {
            LOGGER.error("获取商户优惠券列表失败：", e);
            return this.addError("网络异常");
        }
        return this.addResult("voucherDTO", voucherDTO);
    }

    /**
     * @param
     * @return java.lang.Object
     * @Description: 用户签到领卷
     * @throws
     * <AUTHOR>
     * @date 2018/9/20 20:10
     */
    @RequestMapping("/signVoucher")
    @ResponseBody
    public Object signVoucher(){
        try {
            Map<String,Object> map = null;
            MerchantPrincipal  merchantObj =  (MerchantPrincipal)xyyIndentityValidator.currentPrincipal();
            if (merchantObj == null) {
                map = this.addError("");
                map.put("error_code", "1110001");
                return map;
            }
            ResultDTO resultDTO = promotionVoucherApi.receiveSignVoucherForPC(merchantObj.getId());
            if (resultDTO.getErrorCode() == ErrorCodeEum.FAIL.getErrorCode()){
                throw new ServiceException(resultDTO.getErrorMsg());
            }
            map.put(RESULT_STATUS, RESULT_SUCCESS);
            map.put(CODE, CODE_SUCCESS);
            return  map;
        } catch (ParseException e) {
            LOGGER.error("领取异常：",e);
            return this.addError("领取异常！");
        } catch (ServiceException e) {
            LOGGER.error("领取异常：",e);
            return  this.addError(e.getMessage());
        } catch (Exception e){
            LOGGER.error("领取异常：",e);
            return this.addError(e.getMessage());
        }
    }

    @RequestMapping("/getVoucherTemplateList")
    @ResponseBody
    public Object getVoucherListByTemplateIdList(HttpServletRequest request, Long merchantId, String templateIds){
        try {
            List<Long> templateIdList = null;
            if(StringUtils.isNotBlank(templateIds)){
                templateIdList = Arrays.asList(templateIds.split(",")).stream().filter(templateIdStr -> StringUtils.isNotBlank(templateIdStr) && NumberUtils.isNumber(templateIdStr.trim()))
                        .map(templateIdStr -> Long.parseLong(templateIdStr.trim())).collect(Collectors.toList());
            }

            Map<String, Object> resultMap = addResult();
            resultMap.put(CODE, CODE_SUCCESS);
            if(org.apache.commons.collections.CollectionUtils.isEmpty(templateIdList)){
                resultMap.put("data", Lists.newArrayList());
                return resultMap;
            }

            ResultDTO<List<VoucherCenterDto>> result = promotionVoucherApi.getVoucherListByTemplateIdList(merchantId, templateIdList);
            if(null == result || (result.getErrorCode() != ErrorCodeEum.SUCCESS.getErrorCode() && StringUtils.isEmpty(result.getErrorMsg()))){
                return this.addError(CODE_ERROR,"查询失败");
            }
            if(result.getErrorCode() != ErrorCodeEum.SUCCESS.getErrorCode()){
                return this.addError(CODE_ERROR, result.getErrorMsg());
            }
            resultMap.put("data", result.getData());
            return resultMap;
        }catch (Exception e){
            LOGGER.error("获取优惠券模板列表异常：",e);
            return this.addError(CODE_ERROR, "查询失败");
        }
    }

    @RequestMapping("/getVoucherTemplateListWithShop")
    @ResponseBody
    public Object getVoucherListByTemplateIdListWithShop(HttpServletRequest request, Long merchantId, String templateIds){
        try {
            List<Long> templateIdList = null;
            if(StringUtils.isNotBlank(templateIds)){
                templateIdList = Arrays.asList(templateIds.split(",")).stream().filter(templateIdStr -> StringUtils.isNotBlank(templateIdStr) && NumberUtils.isNumber(templateIdStr.trim()))
                        .map(templateIdStr -> Long.parseLong(templateIdStr.trim())).collect(Collectors.toList());
            }

            Map<String, Object> resultMap = addResult();
            resultMap.put(CODE, CODE_SUCCESS);
            if(org.apache.commons.collections.CollectionUtils.isEmpty(templateIdList)){
                resultMap.put("data", Lists.newArrayList());
                return resultMap;
            }

            PromoResp<List<VoucherCenterDto>> voucherResp = shopCouponForShopcartQueryService.getVoucherListByTemplateIdList(merchantId, templateIdList);
            if(!voucherResp.isSuccess()){
                return this.addError(CODE_ERROR, voucherResp.getMsg());
            }
            resultMap.put("data", voucherResp.getData());
            return resultMap;
        }catch (Exception e){
            LOGGER.error("获取优惠券模板列表异常：",e);
            return this.addError(CODE_ERROR, "查询失败");
        }
    }

    /**
     * 获取订单可使用优惠券
     * （面额、到期时间排序）
     * 2020-09-17
     * @return
     */
    @RequestMapping(value = "/findVoucherInfo")
    @ResponseBody
    public Object findVoucherInfo(
                                  @RequestParam("skuInfoForPc") String skuInfoForPc,
                                  //是否可用0查询可用优惠券列表1查询不可用优惠券列表
                                  @RequestParam(value="isUse",required=false) Integer isUse,
                                  @RequestParam(value="needOptimal") boolean needOptimal,
                                  @RequestParam(value="selectVoucherIds") String selectVoucherIds,
                                  @RequestParam(value="shopCode",required = false) String shopCode,
                                  @RequestParam(value="shopPatternCode",required = false) String shopPatternCode){
        Map<String, Object> dataMap = new HashMap<>();
        try {
            LOGGER.info("查询优惠券返回,入参 productIdAndSingleTotalAmount:{},isUse:{},needOptimal:{},selectVoucherIds:{}",
                    skuInfoForPc, isUse, needOptimal,selectVoucherIds);

            MerchantPrincipal  merchantObj =  (MerchantPrincipal)xyyIndentityValidator.currentPrincipal();
            if (merchantObj == null) {
                dataMap = this.addError("");
                dataMap.put("error_code", "1110001");
                return dataMap;
            }

            //解析优惠券ID
            List<Long> selectVoucherIdsNew = Splitter.on(",")
                    .omitEmptyStrings().trimResults()
                    .splitToList(selectVoucherIds).stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());


            List<SkuForVoucherQuery> skuInfoForVoucherList = JSON.parseArray(skuInfoForPc, SkuForVoucherQuery.class);
            fillVoucherInfo(dataMap, merchantObj.getId(), skuInfoForVoucherList, selectVoucherIdsNew, needOptimal,isUse);

//            if (ShopUtils.isXyyShop(shopPatternCode)) {
//                fillVoucherInfo(dataMap, shopCode, merchantObj.getId(), isUse, needOptimal, selectVoucherIdsNew, skuInfoForVoucherList);
//            } else {
//                fillVoucherInfoForShop(dataMap, shopCode, merchantObj.getId(), isUse, needOptimal, selectVoucherIdsNew, skuInfoForVoucherList);
//            }

            return this.addResult(new String[]{"data"}, new Object[]{dataMap});
        }catch (Exception e){
            LOGGER.error("获取订单可用优惠券失败：", e);
            return this.addError("获取订单可用优惠券失败");
        }
    }
    private void fillVoucherInfo(Map<String, Object> dataMap,Long merchantId, List<SkuForVoucherQuery> skuForVoucherQueries, List<Long> voucherIds, Boolean isUserSelected,Integer isUse){
        if(org.apache.commons.collections.CollectionUtils.isEmpty(skuForVoucherQueries)){
            dataMap.put("voucherTip","暂无可用");
            return ;
        }
        List<SkuForCouponQuery> collect = skuForVoucherQueries.stream().map(sku -> {
            SkuForCouponQuery res = new SkuForCouponQuery();
            res.setThresholdAmount(sku.getSubTotal());
            res.setCsuId(sku.getSkuId());
            return res;
        }).collect(Collectors.toList());
        BigDecimal orderAmount = collect.stream().map(SkuForCouponQuery::getThresholdAmount).reduce(BigDecimal.ZERO,BigDecimal::add);

        ApiRPCResult<CouponsInSettleView> couponResult = couponForTradeQueryService.getCouponForSettleScene(merchantId,collect,orderAmount,voucherIds,isUserSelected);
        LOGGER.info("查询优惠券返回，computeVoucherAmountDetail,result:{}", JSON.toJSONString(couponResult));
        Preconditions.checkArgument(couponResult.isSuccess(), "获取优惠券失败");
        if (couponResult.getData() == null) {
            dataMap.put("voucherList", Lists.newArrayList());
            dataMap.put("voucherTip","暂无可用");
            return;
        }
        CouponsInSettleView data = couponResult.getData();
        List<VoucherDto> couponDetails = data.getCouponDetails();
        if(CollectionUtils.isNotEmpty(couponDetails)){
            couponDetails = couponDetails.stream().filter(k-> (isUse == 0) == k.isCanUse()).collect(Collectors.toList());
        }
        List<VoucherDto> selectVouchers = couponDetails.stream().filter(VoucherDto::getIsSelected).collect(Collectors.toList());
        if(isUse==0){
            String title = "";
            if(CollectionUtils.isNotEmpty(selectVouchers)){
                BigDecimal voucherDiscount = selectVouchers.stream().map(VoucherDto::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                title =  selectVouchers.size() + "张，共优惠金额" + voucherDiscount.setScale(2, RoundingMode.HALF_UP) + "元";
            } else {
                title =  couponDetails.size() + "张可用";
            }
            dataMap.put("voucherTip",title);
        }

        dataMap.put("voucherList", couponDetails);
        dataMap.put("isOptimal", data.getIsOptimal());
        dataMap.put("totalDiscount", data.getTotalAmount());
    }
    @Deprecated
    private void fillVoucherInfo(Map<String, Object> dataMap, String shopCode, Long merchantId, Integer isUse, boolean needOptimal, List<Long> selectVoucherIdsNew, List<SkuInfoForVoucher> skuInfoForVoucherList) {
        if (CollectionUtils.isEmpty(skuInfoForVoucherList)) {
            return;
        }

        List<SkuExtendDTO> skuList = skuInfoForVoucherList.stream()
                .map(x->{
                    SkuExtendDTO skuExtendDTO = new SkuExtendDTO();
                    skuExtendDTO.setSkuId(x.getSkuId());
                    //营销需要把单价=售价*数量
                    skuExtendDTO.setPurchasePrice(x.getPrice().multiply(BigDecimal.valueOf(x.getAmount())));
                    return skuExtendDTO;
                })
                .collect(Collectors.toList());

        BigDecimal orderAmount = skuInfoForVoucherList.stream()
                .map(x -> {
                    return x.getPrice().multiply(BigDecimal.valueOf(x.getAmount()));
                }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        if (isUse == 0) {
            ApiRPCResult<CouponDetailForOrderDto> couponResult = couponForOrderApi.computeVoucherAmountDetail(merchantId, skuList, orderAmount, selectVoucherIdsNew, needOptimal);
            LOGGER.info("查询优惠券返回，computeVoucherAmountDetail,result:{}", JSON.toJSONString(couponResult));
            Preconditions.checkArgument(couponResult.isSuccess(), "获取优惠券失败");
            if (couponResult.getData() == null) {
                dataMap.put("voucherList", Lists.newArrayList());
                return;
            }
            CouponDetailForOrderDto data = couponResult.getData();
            List<com.xyy.ms.marketing.nine.chapters.dto.coupon.VoucherDto> couponDetails = data.getCouponDetails();
            dataMap.put("voucherList", couponDetails);
            dataMap.put("isOptimal", data.getIsOptimal());
            dataMap.put("totalDiscount", data.getTotalAmount());
        } else {
            ApiRPCResult<List<com.xyy.ms.marketing.nine.chapters.dto.coupon.VoucherDto>> couponResult = couponForOrderApi.findNoAvailableVoucher(merchantId, skuList, orderAmount);
            LOGGER.info("查询优惠券返回,findNoAvailableVoucher,result:{}", JSON.toJSONString(couponResult));
            Preconditions.checkArgument(couponResult.isSuccess(), "获取优惠券失败");
            if (couponResult.getData() == null) {
                dataMap.put("voucherList", Lists.newArrayList());
                return;
            }
            List<com.xyy.ms.marketing.nine.chapters.dto.coupon.VoucherDto> couponDetails = couponResult.getData();
            dataMap.put("voucherList", couponDetails);
        }

    }

    private List<MyCouponDto> setMyCouponCsuImage(List<MyCouponDto> couponCenterDtoList, Long merchantId) {
        if (CollectionUtils.isEmpty(couponCenterDtoList)){
            return couponCenterDtoList;
        }
        for (List<MyCouponDto> part : Lists.partition(couponCenterDtoList,1)){
            doSetMyCouponCsuImage(part, merchantId);
        }
        return couponCenterDtoList;
    }
    private void doSetMyCouponCsuImage(List<MyCouponDto> couponCenterDtoList, Long merchantId){
        if (org.apache.commons.collections.CollectionUtils.isEmpty(couponCenterDtoList)){
            return;
        }
        Map<Long,List<Long>> couponSkuMap = Maps.newHashMapWithExpectedSize(couponCenterDtoList.size());
        Set<Long> allSkuId = Sets.newHashSetWithExpectedSize(couponCenterDtoList.size()*5);
        for (MyCouponDto item : couponCenterDtoList){
            List<Long> assignProductIds = item.getAssignProductIds();
            if (org.apache.commons.collections.CollectionUtils.isEmpty(assignProductIds)){
                continue;
            }
            List<Long> searchCusIds;
            if (assignProductIds.size() <= voucherSkuRecallMaxSize) {
                searchCusIds = Lists.newArrayList(assignProductIds);
            } else {
                searchCusIds = assignProductIds.subList(0, voucherSkuRecallMaxSize-1);
            }
            couponSkuMap.put(item.getVoucherTemplateId(), searchCusIds);
            allSkuId.addAll(searchCusIds);
        }
        if (CollectionUtils.isEmpty(allSkuId)){
            return;
        }
        VoucherCsuSearchDto voucherCsuSearchDto = new VoucherCsuSearchDto();
        voucherCsuSearchDto.setCsuIdList(Lists.newArrayList(allSkuId));
        voucherCsuSearchDto.setMerchantId(merchantId);
        List<CsuImageVoucherDto> list = productVoucherCenterBusinessApi.searchCsuImageListByskuId(voucherCsuSearchDto);
        Map<Long,CsuImageVoucherDto> csuImageVoucherDtoMap = Maps.newHashMapWithExpectedSize(list.size());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)){
            for(CsuImageVoucherDto item : list){
                csuImageVoucherDtoMap.put(item.getId(), item);
            }
        }
        for (MyCouponDto item : couponCenterDtoList){
            List<Long> searchSkuId = couponSkuMap.get(item.getVoucherTemplateId());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(searchSkuId)){
                return;
            }
            List<VoucherSkuImageDto> couponCsuImageDtos = Lists.newArrayList();
            int count=0;
            for (Long skuId : searchSkuId){
                CsuImageVoucherDto csuImageVoucherDto = csuImageVoucherDtoMap.get(skuId);
                if (null == csuImageVoucherDto || count>3){
                    continue;
                }
                ++count;
                VoucherSkuImageDto couponCsuImageDto = new VoucherSkuImageDto();
                couponCsuImageDto.setSkuId(csuImageVoucherDto.getId());
                couponCsuImageDto.setImageUrl(csuImageVoucherDto.getImageUrl());
                couponCsuImageDto.setSort(count);
                couponCsuImageDtos.add(couponCsuImageDto);
            }
            item.setVoucherSkuImages(couponCsuImageDtos);
        }
    }

    @RequestMapping("/receiveCenter/index")
    @ResponseBody
    public Object receiveCenterIndex(){
        Map<String, Object> responseData = new HashMap<>();
        responseData.put(RESULT_STATUS, RESULT_SUCCESS);

        MerchantPrincipal merchant = null;
        try {
            merchant = (MerchantPrincipal)xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            LOGGER.info("receiveCenterIndex解析用户异常", e);
        }
        if (null == merchant){
            LOGGER.info("receiveCenterIndex_用户未登录");
            return responseData;
        }
        List<CouponBaseDto> couponBaseDtoList = couponForReceiveCenterQueryService.getCouponShowInReceiveCenter(merchant.getId(), Lists.newArrayList(5,8,9));
        Map<String, ShopStaticsVo> shopInfoSupperVOMap = shopService.mgetShopStaticsByCode(couponBaseDtoList.stream().map(CouponBaseDto::getShopCode).filter(s -> null != s && !"XS000000".equals(s)).distinct().collect(Collectors.toList()));

        List<CouponBaseDto> received = Lists.newArrayList();
        List<CouponBaseDto> newCustomerCoupon = Lists.newArrayList();
        List<CouponBaseDto> platformCoupon = Lists.newArrayList();
        for (CouponBaseDto item : couponBaseDtoList){
            if (null == item){
                continue;
            }
            if (null != item.getId() && item.getId()>0){
                received.add(item);
            }else if(Objects.equals(com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.NEWMAN.getId(),item.getVoucherType())){
                newCustomerCoupon.add(item);
            } else if (Objects.equals(com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.CROSS_PLATFORM.getId(), item.getVoucherType())
                    // 添加专品券
                    || Objects.equals(com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.SPECIALTY.getId(), item.getVoucherType())) {
                platformCoupon.add(item);
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(received)){
            received.sort(ReceiveCenterCouponSortConstants.DISCOUNT_DESC_SORT);
            responseData.put("recommendReceived", toCouponCenterDto(received.get(0), shopInfoSupperVOMap.get(received.get(0).getShopCode())));
        }else{
            responseData.put("recommendReceived", Lists.newArrayList());
        }
        newCustomerCoupon.sort(ReceiveCenterCouponSortConstants.DISCOUNT_DESC_SORT);
        platformCoupon.sort(ReceiveCenterCouponSortConstants.DISCOUNT_DESC_SORT);
        platformCoupon.sort(ReceiveCenterCouponSortConstants.COUPON_MAKE_FROM_SORT);

        responseData.put("newCustomerCoupon", toCouponCenterDto(newCustomerCoupon, shopInfoSupperVOMap));
        responseData.put("platformCoupon", toCouponCenterDto(platformCoupon, shopInfoSupperVOMap));
        return responseData;
    }


    @RequestMapping("/receiveCenter/shopSelect")
    @ResponseBody
    public Object receiveCenterShopSelect(@RequestParam(value = "pageNo", required = false) Integer pageNo, @RequestParam(value = "pageSize", required = false) Integer pageSize, @RequestHeader(value = "terminalType", required = false) Integer terminalType, @RequestHeader(value = "version", required = false) Integer version){
        if (null == pageNo){
            pageNo = 1;
        }
        if (null == pageSize || pageSize>20){
            pageSize = 20;
        }
        Map<String, Object> responseData = new HashMap<>();
        MerchantPrincipal merchant = null;
        try {
            merchant = (MerchantPrincipal)xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            LOGGER.info("receiveCenterIndex解析用户异常", e);
        }
        responseData.put(RESULT_STATUS, RESULT_SUCCESS);
        if (null == merchant){
            return responseData;
        }
        com.xyy.ec.marketing.common.dto.PageInfo<CouponBaseDto> couponBaseDtoPageInfo = couponForReceiveCenterQueryService.getCouponShowInReceiveCenterShopSelectCoupon(merchant.getId(), pageNo, pageSize);
        Map<String, ShopStaticsVo> shopInfoSupperVOMap = shopService.mgetShopStaticsByCode(couponBaseDtoPageInfo.getList().stream().map(CouponBaseDto::getShopCode).filter(s -> null != s && !"XS000000".equals(s)).distinct().collect(Collectors.toList()));
        List<CouponBaseDto> shopCouponSelected = sortDistinctShopSelectCoupon(merchant, couponBaseDtoPageInfo.getList(), shopInfoSupperVOMap);
        responseData.put("couponList", toCouponCenterDto(shopCouponSelected, shopInfoSupperVOMap));
        responseData.put("nextPage", couponBaseDtoPageInfo.getNextPage());
        responseData.put("pageSize", couponBaseDtoPageInfo.getPageSize());
        responseData.put("realDataSize", couponBaseDtoPageInfo.getRealDataSize());
        responseData.put("isEnd", couponBaseDtoPageInfo.getNextPage()>couponBaseDtoPageInfo.getPages());
        return responseData;
    }


    private List<CouponBaseDto> sortDistinctShopSelectCoupon(MerchantPrincipal merchant, List<CouponBaseDto> shopCouponSelected, Map<String, ShopStaticsVo> shopInfoSupperVOMap) {
        if (CollectionUtils.isEmpty(shopCouponSelected)){
            return Lists.newArrayList();
        }
        String branchCode = merchant.getRegisterBranch();
        Map<String,CouponBaseDto> map = new HashMap<>();
        for (CouponBaseDto item : shopCouponSelected){
            CouponBaseDto before = map.get(item.getShopCode());
            if (null == before){
                map.put(item.getShopCode(), item);
            }else if (ReceiveCenterCouponSortConstants.getDiscount(item)> ReceiveCenterCouponSortConstants.getDiscount(before)){
                map.put(item.getShopCode(), item);
            }
        }
        List<CouponBaseDto> couponBaseDtos = Lists.newArrayList(map.values());
        if (CollectionUtils.isEmpty(couponBaseDtos)){
            return Lists.newArrayList();
        }

        CouponBaseDto benShenZiYing = null;
        CouponBaseDto benShenPop = null;

        for (CouponBaseDto item : couponBaseDtos){
            if (!Objects.equals(com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.SHOP_COUPON.getId(),item.getVoucherType()) && Objects.equals(branchCode,item.getBranchCode())){
                benShenZiYing = item;
            }else if(Objects.equals(com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.SHOP_COUPON.getId(),item.getVoucherType()) && null != shopInfoSupperVOMap.get(item.getShopCode()) && Objects.equals("XS"+shopInfoSupperVOMap.get(item.getShopCode()).getProvinceCode(),merchant.getRegisterBranch())){
                if (null ==  benShenPop|| ReceiveCenterCouponSortConstants.getDiscount(item)> ReceiveCenterCouponSortConstants.getDiscount(benShenPop)){
                    benShenPop = item;
                }
            }
        }

        List<CouponBaseDto> toSort = Lists.newArrayListWithExpectedSize(couponBaseDtos.size());
        for (CouponBaseDto item : couponBaseDtos){
            if (item != benShenZiYing && item != benShenPop){
                toSort.add(item);
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(toSort)){
            PageInfo<ShopStatisticsReportDTO> shopsByCodes = shopService.getShopsByCodes(toSort.stream().map(CouponBaseDto::getShopCode).distinct().collect(Collectors.toList()),Arrays.asList("ybm","pop"), 1, Math.min(map.values().size(), 200));
            final Map<String,BigDecimal> mapShopStaticsMap = Maps.newHashMapWithExpectedSize(shopsByCodes.getSize());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopsByCodes.getList())){
                for (ShopStatisticsReportDTO item : shopsByCodes.getList()){
                    if (null != item && null != item.getSalesVolume()){
                        mapShopStaticsMap.put(item.getShopCode(), item.getSalesVolume());
                    }
                }
            }
            toSort.sort((o1, o2) -> {
                if (Objects.equals(o1.getShopCode(), o2.getShopCode())){
                    return 0;
                }
                BigDecimal o1SalesVolume = mapShopStaticsMap.getOrDefault(o1.getShopCode(), BigDecimal.ZERO);
                BigDecimal o2SalesVolume = mapShopStaticsMap.getOrDefault(o2.getShopCode(), BigDecimal.ZERO);
                return o2SalesVolume.compareTo(o1SalesVolume);
            });
        }
        List<CouponBaseDto> result = Lists.newArrayListWithExpectedSize(couponBaseDtos.size());
        if (null != benShenZiYing){
            result.add(benShenZiYing);
        }
        if (null != benShenPop){
            result.add(benShenPop);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(toSort)){
            result.addAll(toSort);
        }
        return result;
    }


    @RequestMapping("/receiveCenter/shopCoupon")
    @ResponseBody
    public Object receiveCenterShopCoupon( @RequestParam(value = "bizType", required = false) Integer bizType, @RequestParam(value = "pageNo", required = false) Integer pageNo, @RequestParam(value = "pageSize", required = false) Integer pageSize, @RequestHeader(value = "terminalType", required = false) Integer terminalType, @RequestHeader(value = "version", required = false) Integer version){
        if (null == pageNo){
            pageNo = 1;
        }
        if (null == pageSize || pageSize>20){
            pageSize = 20;
        }
        //为空或者负数表示获取全部
        if (null == bizType){
            bizType = -1;
        }
        MerchantPrincipal merchant = null;
        try {
            merchant = (MerchantPrincipal)xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            LOGGER.info("receiveCenterIndex解析用户异常", e);
        }
        Map<String, Object> responseData = new HashMap<>();
        responseData.put(RESULT_STATUS, RESULT_SUCCESS);
        if (null == merchant){
            LOGGER.info("receiveCenterIndex_用户未登录");
            return responseData;
        }
        com.xyy.ec.marketing.common.dto.PageInfo<CouponBaseDto> pageInfo = couponForReceiveCenterQueryService.getCouponShowInReceiveCenterShopCoupon(merchant.getId(), bizType, pageNo, pageSize);
        Map<String, ShopStaticsVo> shopInfoSupperVOMap = shopService.mgetShopStaticsByCode(pageInfo.getList().stream().map(CouponBaseDto::getShopCode).filter(s -> null != s && !"XS000000".equals(s)).distinct().collect(Collectors.toList()));
        pageInfo.getList().sort(ReceiveCenterCouponSortConstants.DISCOUNT_DESC_SORT);
        responseData.put("couponList", toCouponCenterDto(pageInfo.getList(), shopInfoSupperVOMap));
        responseData.put("nextPage", pageInfo.getNextPage());
        responseData.put("pageSize", pageInfo.getPageSize());
        responseData.put("realDataSize", pageInfo.getRealDataSize());
        responseData.put("isEnd", pageInfo.getNextPage()>pageInfo.getPages());
        return responseData;
    }


    @RequestMapping("/receiveCenter/productCoupon")
    @ResponseBody
    public Object receiveCenterProductCoupon(@RequestParam(value = "pageNo", required = false) Integer pageNo, @RequestParam(value = "pageSize", required = false) Integer pageSize, @RequestHeader(value = "terminalType", required = false) Integer terminalType, @RequestHeader(value = "version", required = false) Integer version){
        if (null == pageNo){
            pageNo = 1;
        }
        if (null == pageSize || pageSize>20){
            pageSize = 20;
        }
        MerchantPrincipal merchant = null;
        try {
            merchant = (MerchantPrincipal)xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            LOGGER.info("receiveCenterIndex解析用户异常", e);
        }
        Map<String, Object> responseData = new HashMap<>();
        responseData.put(RESULT_STATUS, RESULT_SUCCESS);
        if (null == merchant){
            LOGGER.info("receiveCenterIndex_用户未登录");
            return responseData;
        }

        com.xyy.ec.marketing.common.dto.PageInfo<CouponBaseDto> pageInfo = couponForReceiveCenterQueryService.getCouponShowInReceiveCenterProductCoupon(merchant.getId(), pageNo, pageSize);
        Map<String, ShopStaticsVo> shopInfoSupperVOMap = shopService.mgetShopStaticsByCode(pageInfo.getList().stream().map(CouponBaseDto::getShopCode).filter(s -> null != s && !"XS000000".equals(s)).distinct().collect(Collectors.toList()));
        pageInfo.getList().sort(ReceiveCenterCouponSortConstants.DISCOUNT_DESC_SORT);
        pageInfo.getList().sort(ReceiveCenterCouponSortConstants.COUPON_TYPE_SORT);
        List<CouponCenterDto> list = setCsuImage(toCouponCenterDto(pageInfo.getList(), shopInfoSupperVOMap), merchant.getId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)){
            for (CouponCenterDto item : list){
                if (Objects.equals(7, item.getVoucherType())){
                    item.setVoucherType(2);
                    item.setVoucherTypeDesc("商品券");
                    item.setVoucherTypeText("商品券");
                }
            }
        }
        responseData.put("couponList", list);
        responseData.put("nextPage", pageInfo.getNextPage());
        responseData.put("pageSize", pageInfo.getPageSize());
        responseData.put("realDataSize", pageInfo.getRealDataSize());
        responseData.put("isEnd", pageInfo.getNextPage()>pageInfo.getPages());
        return responseData;
    }

    private List<CouponCenterDto> setCsuImage(List<CouponCenterDto> couponCenterDtoList, Long merchantId) {
        if (CollectionUtils.isEmpty(couponCenterDtoList)){
            return couponCenterDtoList;
        }
        for (List<CouponCenterDto> part : Lists.partition(couponCenterDtoList,1)){
            doSetCsuImage(part, merchantId);
        }
        return couponCenterDtoList;
    }

    private void doSetCsuImage(List<CouponCenterDto> couponCenterDtoList, Long merchantId){
        if (org.apache.commons.collections.CollectionUtils.isEmpty(couponCenterDtoList)){
            return;
        }
        Map<Long,List<Long>> couponSkuMap = Maps.newHashMapWithExpectedSize(couponCenterDtoList.size());
        Set<Long> allSkuId = Sets.newHashSetWithExpectedSize(couponCenterDtoList.size()*5);
        for (CouponCenterDto item : couponCenterDtoList){
            List<Long> assignProductIds = item.getAssignProductIds();
            if (org.apache.commons.collections.CollectionUtils.isEmpty(assignProductIds)){
                continue;
            }
            List<Long> searchCusIds;
            if (assignProductIds.size() <= voucherSkuRecallMaxSize) {
                searchCusIds = Lists.newArrayList(assignProductIds);
            } else {
                searchCusIds = assignProductIds.subList(0, voucherSkuRecallMaxSize-1);
            }
            couponSkuMap.put(item.getTemplateId(), searchCusIds);
            allSkuId.addAll(searchCusIds);
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(allSkuId)){
            return;
        }
        VoucherCsuSearchDto voucherCsuSearchDto = new VoucherCsuSearchDto();
        voucherCsuSearchDto.setCsuIdList(Lists.newArrayList(allSkuId));
        voucherCsuSearchDto.setMerchantId(merchantId);
        List<CsuImageVoucherDto> list = productVoucherCenterBusinessApi.searchCsuImageListByskuId(voucherCsuSearchDto);
        Map<Long,CsuImageVoucherDto> csuImageVoucherDtoMap = Maps.newHashMapWithExpectedSize(list.size());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)){
            for(CsuImageVoucherDto item : list){
                csuImageVoucherDtoMap.put(item.getId(), item);
            }
        }
        for (CouponCenterDto item : couponCenterDtoList){
            List<Long> searchSkuId = couponSkuMap.get(item.getTemplateId());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(searchSkuId)){
                return;
            }
            List<CouponCsuImageDto> couponCsuImageDtos = Lists.newArrayList();
            int count=0;
            for (Long skuId : searchSkuId){
                CsuImageVoucherDto csuImageVoucherDto = csuImageVoucherDtoMap.get(skuId);
                if (null == csuImageVoucherDto || count>3){
                    continue;
                }
                ++count;
                CouponCsuImageDto couponCsuImageDto = CouponCsuImageDto
                        .builder()
                        .skuId(csuImageVoucherDto.getId())
                        .imageUrl(csuImageVoucherDto.getImageUrl())
                        .sort(count)
                        .build();
                couponCsuImageDtos.add(couponCsuImageDto);
            }
            item.setVoucherSkuImages(couponCsuImageDtos);
        }
    }


    private List<CouponCenterDto> toCouponCenterDto(List<CouponBaseDto> src, Map<String, ShopStaticsVo> shopInfoSupperVOMap){
        if (org.apache.commons.collections.CollectionUtils.isEmpty(src)){
            return Lists.newArrayList();
        }
        List<CouponCenterDto> result = Lists.newArrayListWithExpectedSize(src.size());
        for (CouponBaseDto item : src){
            if (null == item){
                continue;
            }
            result.add(toCouponCenterDto(item,shopInfoSupperVOMap.get(item.getShopCode())));
        }
        return result;
    }


    private CouponCenterDto toCouponCenterDto(CouponBaseDto src, ShopStaticsVo shopInfoSupperVO){
        String voucherInstructions = null;
        String shopName = "";
        Integer voucherType = src.getVoucherType();
        Integer voucherUsageWay = src.getVoucherUsageWay();
        BigDecimal minMoneyToEnable = src.getMinMoneyToEnable();
        BigDecimal maxMoneyInVoucher = src.getMaxMoneyInVoucher();
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.NEWMAN.getId())) {
            if (StringUtils.isNotBlank(shopName)){
                voucherInstructions = shopName + "全部商品可用";
            }else{
                voucherInstructions = VoucherEnum.VoucherInstructionsEnum.XINREN_QUAN.getInstructionsText();
            }
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.COMMON.getId())) {
            if(StringUtils.isNotBlank(shopName)){
                voucherInstructions = shopName + VoucherEnum.VoucherInstructionsEnum.TONGYOPNG_QUAN.getInstructionsText();
            }
            else {
                voucherInstructions = VoucherEnum.VoucherInstructionsEnum.SHOP_QUAN.getInstructionsText();
            }
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.OVERLYING.getId())
                && org.apache.commons.collections.CollectionUtils.isEmpty(src.getAssignProductIds()) && BooleanUtils.isNotTrue(src.getExhibitionGroupVoucher())) {
            voucherInstructions = VoucherEnum.VoucherInstructionsEnum.DIEJIA_QUAN.getInstructionsText();
        }
        if (Objects.equals(voucherType, com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.CROSS_PLATFORM.getId())
                && org.apache.commons.collections.CollectionUtils.isEmpty(src.getAssignProductIds()) && BooleanUtils.isNotTrue(src.getExhibitionGroupVoucher())) {
            voucherInstructions = VoucherEnum.VoucherInstructionsEnum.DIEJIA_QUAN.getInstructionsText();
        }

        String minMoneyToEnableDesc = null;
        if (minMoneyToEnable != null
                && (Objects.equals(voucherType, 8) || Objects.equals(voucherType, 7) || Objects.equals(voucherType, 6) || Objects.equals(voucherType, 2) || Objects.equals(voucherType, 1) || Objects.equals(voucherType, 9))
                && Objects.equals(voucherUsageWay, 1)) {
            minMoneyToEnableDesc = StringUtils.join("每满", minMoneyToEnable.intValue(), "可用");
        } else if (minMoneyToEnable != null) {
            minMoneyToEnableDesc = StringUtils.join("满", minMoneyToEnable.intValue(), "可用");
        }

        String maxMoneyInVoucherDesc = null;
        if (maxMoneyInVoucher != null && maxMoneyInVoucher.compareTo(BigDecimal.ZERO) > 0) {
            maxMoneyInVoucherDesc = StringUtils.join("最高减", maxMoneyInVoucher.intValue());
        }

        boolean isDiscount = src.getDiscount() != null && src.getDiscount().compareTo(BigDecimal.ZERO) > 0;
        String validDayStr = (null != src.getValidDays() && src.getValidDays() > 0) ? "领取后"+src.getValidDays()+"天有效" : null;
        CouponCenterDto build = CouponCenterDto.builder().templateId(src.getTemplateId())
                .templateName(src.getTemplateName())
                .voucherType(voucherType)
                .voucherTypeDesc(com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.of(src.getVoucherType()).getValue())
                .voucherTypeText(com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum.of(src.getVoucherType()).getValue())
                .voucherState(isDiscount ? 1 : 0)
                .isLq(null == src.getId() ? 0 : 1)
                .bizType(Objects.equals(src.getVoucherType(), 7) ? 3 : 1)
                .shopCode(src.getShopCode())
                .shopName(null == shopInfoSupperVO ? "" : shopInfoSupperVO.getShowName())
                .shopLogoUrl(null == shopInfoSupperVO ? "" : shopInfoSupperVO.getAppLogo())
                .voucherInstructions(voucherInstructions)
                .voucherTitle(src.getVoucherTitle())
                .moneyInVoucher(isDiscount ? src.getDiscount() : src.getMoneyInVoucher())
                .minMoneyToEnable(src.getMinMoneyToEnable())
                .maxMoneyInVoucher(src.getMaxMoneyInVoucher())
                .discount(src.getDiscount())
                .expireDate(src.getExpireDate())
                .expireDateToString(src.getExpireDate() == null ? "" : DateFormatUtils.format(src.getExpireDate(), "yyyy/MM/dd"))
                .validDate(src.getValidDate())
                .validDateToString(src.getValidDate() == null ? "" : DateFormatUtils.format(src.getValidDate(), "yyyy/MM/dd"))
                .voucherUsageWay(src.getVoucherUsageWay() == null ? 0 : src.getVoucherUsageWay())
                .assignProductIds(src.getAssignProductIds())
                .skuRelationType(src.getSkuRelationType())
                .appUrl(src.getAppUrl())
                .pcUrl(src.getPcUrl())
                .describeUrl(src.getDescribeUrl())
                .minMoneyToEnableDesc(minMoneyToEnableDesc)
                .maxMoneyInVoucherDesc(maxMoneyInVoucherDesc)
                .sortNo(src.getSortNo())
                .validDays(src.getValidDays())
                .validDayStr(validDayStr)
                .build();
        fillUpPcUrl(build, src);
        return build;
    }


    private void fillUpPcUrl(CouponCenterDto couponCenterDto, CouponBaseDto src) {
        LOGGER.info("路径跳转测试 templateId:{}, assignProductIds:{} shopCode:{}, voucherType:{}, pcUrl:{}, --------- assignShopCodes:{}"
        ,couponCenterDto.getTemplateId(), JSON.toJSONString(couponCenterDto.getAssignProductIds()) , couponCenterDto.getShopCode(), couponCenterDto.getVoucherType(), couponCenterDto.getPcUrl(), src.getAssignShopCodes());
        String pcUrl;
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(couponCenterDto.getAssignProductIds())) {
            pcUrl = "/voucher/centre/findVoucherSku.htm?voucherTemplateId=" + couponCenterDto.getTemplateId();
        } else if (!Objects.equals(couponCenterDto.getVoucherType(), VoucherEnum.VoucherTypeEnum.SHOP_COUPON.getId()) && !Objects.equals(couponCenterDto.getVoucherType(), VoucherEnum.VoucherTypeEnum.CROSS_PLATFORM.getId())) {
            pcUrl = "/selfShop/center/shopSkuInfo.htm?shopCode=" + couponCenterDto.getShopCode();
        } else if (StringUtils.isEmpty(couponCenterDto.getPcUrl()) && Objects.equals(couponCenterDto.getVoucherType(), VoucherEnum.VoucherTypeEnum.CROSS_PLATFORM.getId())) {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(src.getAssignShopCodes())){
                pcUrl = "/shop/shopList.htm?sort=0&shopPropertyCode=self";
            }else{
                //pcUrl = "/voucher/centre/findVoucherShop.htm?voucherTemplateId=" + couponCenterDto.getTemplateId();
                pcUrl = "/voucher/centre/findVoucherShopV2.htm?voucherTemplateId=" + couponCenterDto.getTemplateId();
            }
        }else {
            pcUrl = couponCenterDto.getPcUrl();
        }

        if (crossOrSpecVoucherList.contains(couponCenterDto.getVoucherType())) {
            pcUrl = "/voucher/centre/findVoucherSku.htm?voucherTemplateId=" + couponCenterDto.getTemplateId();
        }
        couponCenterDto.setPcUrl(pcUrl);
    }

    private static List<Integer> crossOrSpecVoucherList = Lists.newArrayList(VoucherEnum.VoucherTypeEnum.CROSS_PLATFORM.getId(), VoucherEnum.VoucherTypeEnum.SPECIALTY.getId());

    @GetMapping("/oneClickRestockCoupon")
    @ResponseBody
    public Object oneClickRestockCoupon() {
        Map<String, Object> resultMap = addResult();
        resultMap.put(CODE, CODE_SUCCESS);

        MerchantPrincipal merchant = null;
        try {
            merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            LOGGER.info("oneClickRestockCoupon解析用户异常", e);
        }
        if (null == merchant) {
            LOGGER.info("oneClickRestockCoupon_用户未登录");
            return resultMap;
        }
        try {
            OneClickRestockCouponDTO couponDTO = hyperSpaceRpc.getOneClickRestockCoupon(merchant.getMerchantId());
            LOGGER.debug("VoucherController#oneClickRestockCoupon merchantId:{} couponDTO:{}", merchant.getMerchantId(), JSONObject.toJSONString(couponDTO));
            if (Objects.isNull(couponDTO) || CollectionUtils.isEmpty(couponDTO.getCouponTemplateList())) {
                resultMap.put("data", couponDTO);
                return resultMap;
            }

            String allReceivedCouponDesc = StringUtils.EMPTY;
            Set<String> receivedCouponDescSet = new HashSet<>();
            for (OneClickRestockCouponTemplateDTO couponTemplateDTO : couponDTO.getCouponTemplateList()) {
                if (Objects.equals(VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState(), couponTemplateDTO.getState())) {
                    receivedCouponDescSet.add(couponTemplateDTO.getVoucherDesc());
                    continue;
                }

                // 未领取才调用领取
                if (Objects.equals(VoucherEnum.MerchantVoucherStatusEnum.NO_RECEIVE.getState(), couponTemplateDTO.getState())) {
                    ReceiveVoucherRequestDTO receiveVoucherRequestDTO = new ReceiveVoucherRequestDTO(merchant.getMerchantId(), couponTemplateDTO.getTemplateId());
                    ResultDTO<VoucherExtendDTO> rpcResult = voucherForPcBusinessApi.receiveVoucher(receiveVoucherRequestDTO);

                    LOGGER.debug("VoucherController#oneClickRestockCoupon receive. param:{} result:{}", JSONObject.toJSONString(receiveVoucherRequestDTO), JSONObject.toJSONString(rpcResult));
                    if (Objects.nonNull(rpcResult)) {
                        if (Objects.equals(ErrorCodeEum.SUCCESS.getErrorCode(), rpcResult.getErrorCode())) {
                            receivedCouponDescSet.add(couponTemplateDTO.getVoucherDesc());
                            couponTemplateDTO.setState(VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState());
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(receivedCouponDescSet)) {
                allReceivedCouponDesc = receivedCouponDescSet.stream().collect(Collectors.joining(Constant.COMMA));
            }
            couponDTO.setReceivedCouponDesc(allReceivedCouponDesc);
            List<Long> receivedCouponTemplateIdList = couponDTO.getCouponTemplateList().stream()
                    .filter(e -> Objects.equals(VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState(), e.getState()))
                    .map(e -> e.getTemplateId()).collect(Collectors.toList());
            couponDTO.setReceivedCouponTemplateIdList(receivedCouponTemplateIdList);

            resultMap.put("data", couponDTO);
            return resultMap;
        } catch (Exception e) {
            LOGGER.error("获取一键采购补货优惠劵模板列表异常：", e);
            return this.addError(CODE_ERROR, "查询失败");
        }
    }

    @PostMapping("/couponSku/condition")
    @ResponseBody
    public Object oneClickRestockCouponSkuCondition(@RequestBody CouponSkuConditionParam param){
        try {
            Map<String, Object> resultMap = addResult();
            resultMap.put(CODE, CODE_SUCCESS);

            MerchantPrincipal merchant = null;
            try {
                merchant = (MerchantPrincipal)xyyIndentityValidator.currentPrincipal();
            } catch (Exception e) {
                LOGGER.info("oneClickRestockCouponSkuCondition解析用户异常", e);
            }
            if (null == merchant){
                LOGGER.info("oneClickRestockCouponSkuCondition_用户未登录");
                return resultMap;
            }

            if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getReceivedCouponTemplateIdList())) {
                LOGGER.info("oneClickRestockCouponSkuCondition param illegal. param {}", JSONObject.toJSONString(param));
                return resultMap;
            }
            List<Long> receivedCouponTemplateIdList = param.getReceivedCouponTemplateIdList();

            List<CouponConditionVO> voList = new ArrayList<>();
            for (Long voucherTemplateId : receivedCouponTemplateIdList) {
                ApiRPCResult<CouponBaseDto> couponTemplateRpcRes = couponApi.getCouponTemplateBaseDto(voucherTemplateId);
                if (couponTemplateRpcRes.isFail() || Objects.isNull(couponTemplateRpcRes.getData())) {
                    continue;
                }

                CouponConditionVO vo = getCouponConditionByCouponBaseDto(couponTemplateRpcRes.getData());
                voList.add(vo);
            }
            List<CouponConditionVO> conditionVOList = voList.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (conditionVOList.stream().anyMatch(e -> BooleanUtils.isTrue(e.getAllProductTag()))) {
                resultMap.put("data", null);
                return resultMap;
            }

            CouponConditionVO resultVo = null;
            if (CollectionUtils.isNotEmpty(conditionVOList)) {
                List<String> tagList = conditionVOList.stream()
                        .filter(vo -> CollectionUtils.isNotEmpty(vo.getCouponTagList()))
                        .flatMap(vo -> vo.getCouponTagList().stream())
                        .distinct()
                        .collect(Collectors.toList());
                List<String> shopCodeList = conditionVOList.stream()
                        .filter(vo -> CollectionUtils.isNotEmpty(vo.getShopCodeList()))
                        .flatMap(vo -> vo.getShopCodeList().stream())
                        .distinct()
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tagList)) {
                    resultVo = new CouponConditionVO();
                    resultVo.setCouponTagList(tagList);
                }
                if (CollectionUtils.isNotEmpty(shopCodeList)) {
                    if (Objects.isNull(resultVo)) {
                        resultVo = new CouponConditionVO();
                    }
                    resultVo.setShopCodeList(shopCodeList);
                }
            }

            resultMap.put("data", resultVo);
            return resultMap;
        }catch (Exception e){
            LOGGER.error("获取一键采购补货优惠劵SkuCondition异常：",e);
            return this.addError(CODE_ERROR, "查询失败");
        }
    }

    private CouponConditionVO getCouponConditionByCouponBaseDto(CouponBaseDto couponBaseDto) {
        CouponConditionVO vo = new CouponConditionVO();
        vo.setAllProductTag(false);
        List<String> assignShopCodeList = couponBaseDto.getAssignShopCodes();
        if (CollectionUtils.isNotEmpty(assignShopCodeList)) {
            // 指定店铺
            vo.setShopCodeList(assignShopCodeList);
        } else if (Objects.equals(couponBaseDto.getSkuRelationType(), PromoEnum.PromoSkuRelateType.ALL_IN.getType())) {
            // 全部商品
            vo.setAllProductTag(true);
        } else {
            // 指定商品组
            if (BooleanUtils.isTrue(couponBaseDto.getExhibitionGroupVoucher())) {
                List<ExhibitionBuineseDto> exhibitionBusDtoList = exhibitionBuineseApi.selectExhibitionGroupByIdList(couponBaseDto.getExhibitionGroupIds());
                if (CollectionUtils.isNotEmpty(exhibitionBusDtoList)) {
                    List<String> tagList = exhibitionBusDtoList.stream().map(e -> e.getExhibitionId()).collect(Collectors.toList());
                    vo.setCouponTagList(tagList);
                }
            } else {
                // 指定商品
                vo.setCouponTagList(Collections.singletonList(Constants.SEARCH_PRODUCT_COUPON_PREFIX + couponBaseDto.getTemplateId()));
            }
        }
        return vo;
    }
}
