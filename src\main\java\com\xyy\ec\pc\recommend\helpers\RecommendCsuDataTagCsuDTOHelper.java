package com.xyy.ec.pc.recommend.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.pc.recommend.dto.RecommendCsuDataTagCsuDTO;
import com.xyy.ec.pc.recommend.vo.PcRecommendBaseProductVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class RecommendCsuDataTagCsuDTOHelper {

    /**
     * 创建
     *
     * @param product
     * @return
     */
    public static <T extends PcRecommendBaseProductVO> RecommendCsuDataTagCsuDTO create(T product) {
        return RecommendCsuDataTagCsuDTO.builder()
                .id(product.getId())
                .pid(product.getPid())
                .shopCode(product.getShopCode())
                .isVirtualSupplier(product.getIsVirtualSupplier())
                .build();
    }

    /**
     * 创建
     *
     * @param products
     * @return
     */
    public static <T extends PcRecommendBaseProductVO> List<RecommendCsuDataTagCsuDTO> creates(List<T> products) {
        if (CollectionUtils.isEmpty(products)) {
            return Lists.newArrayList();
        }
        return products.stream().map(RecommendCsuDataTagCsuDTOHelper::create).filter(Objects::nonNull).collect(Collectors.toList());
    }


}
