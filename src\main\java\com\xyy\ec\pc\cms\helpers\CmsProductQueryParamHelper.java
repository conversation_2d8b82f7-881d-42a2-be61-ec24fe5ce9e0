package com.xyy.ec.pc.cms.helpers;

import com.xyy.ec.pc.cms.param.CmsProductQueryParam;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;

/**
 * {@link CmsProductQueryParam} 帮助类
 *
 * <AUTHOR>
 */
public class CmsProductQueryParamHelper {

    /**
     * 校验
     *
     * @param cmsProductQueryParam
     * @return
     */
    public static Boolean validate(CmsProductQueryParam cmsProductQueryParam) {
        // 校验
        if (cmsProductQueryParam == null) {
            String msg = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "商品查询");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, msg);
        }
        /*if (!BooleanUtils.isTrue(cmsShopProductsQueryParam.getIsAdmin()) && cmsShopProductsQueryParam.getMerchantId() == null) {
            // 非admin侧 且会员ID为null
            throw new AppException(XyyJsonResultCodeEnum.AUTHORITY_ERROR);
        }*/
        if (StringUtils.isEmpty(cmsProductQueryParam.getExhibitionId())) {
            String message = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "商品展示组ID");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, message);
        }
        if (StringUtils.isEmpty(cmsProductQueryParam.getBranchCode())) {
            String message = MessageFormat.format(XyyJsonResultCodeEnum.PARAMETER_ERROR.getMsg(), "分公司编码");
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, message);
        }
        return true;
    }

}
