package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.layout.buinese.api.ExhibitionBuineseApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.layout.LayoutBaseService;
import com.xyy.ec.pc.enums.LayoutMerchantStatusEnum;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.product.business.dto.listOfSku.ListSkuSearchData;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Title:
 * @Description: 跳转到活动页面(二级页面)
 * @date 2018/9/4 17:29
 */
@Controller
@RequestMapping("/activityData")
public class ActivityDataController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ActivityDataController.class);


    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private LayoutBaseService layoutBaseService;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Autowired
    private Config config;

    @Reference(version = "1.0.0")
    private ExhibitionBuineseApi exhibitionBuineseApi;


    /**
     * @param request
     * @return org.springframework.web.servlet.ModelAndView
     * @throws
     * @Description: 静态页数据加载--活动二级页面
     * <AUTHOR>
     * @date 2018/9/4 17:32
     */
    @RequestMapping("/exhibitionData.htm")
    public ModelAndView activityPage(HttpServletRequest request) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            Long merchantId;
            if (merchant != null) {
                merchantId = merchant.getId();
            } else {
                merchantId = 0L;
            }

            int offset = NumberUtils.toInt(request.getParameter("offset"));
            if (offset < 1) {
                offset = 1;
            }
            String isLast = request.getParameter("isLast");

            /** 从url上获取分页数据 */
            int limit = NumberUtils.toInt(request.getParameter("limit"));
            if (limit < 1) {
                limit = 20;
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            List<ListProduct> skuDtoList;
            ListSkuSearchData listSkuSearchData = exhibitionBuineseApi.getExhibitionProductSkuInfoWrapper(request.getParameter("exhibitionId"), merchantId, branchCode, offset, limit);
            if (listSkuSearchData != null) {
                skuDtoList = listSkuSearchData.getSkuDtoList();
            } else {
                skuDtoList = new ArrayList<>(0);
            }
            LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
            if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                skuDtoList = layoutBaseService.setProductProperties(skuDtoList, statusEnum, request);
            }
            skuDtoList = layoutBaseService.setProductShopName(skuDtoList);
            modelMap.put("skuVOList", skuDtoList);
            modelMap.put("merchant", merchant);
            modelMap.put("productImageUrl", config.getProductImagePathUrl());
            modelMap.put("merchantId", merchantId);
            modelMap.put("isLast", isLast);
            return new ModelAndView("/common/skuVOList.ftl", modelMap);
        } catch (Exception e) {
            logger.error("静态页数据加载失败:", e);
            return new ModelAndView("/error/500.ftl");
        }

    }

    /**
     * @Description:h5页面使用直接通过展示id查询商品
     * @TestCase:
     */
    @RequestMapping("/initExhibitionModulePage")
    @ResponseBody
    public Object initExhibitionModulePage(HttpServletRequest request, String exhibitionId, Integer limit, Integer offset) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId;
            Integer licenseStatus = null;
            if (merchant == null) {
                merchantId = 0L;
            } else {
                merchantId = merchant.getId();
                licenseStatus = merchant.getLicenseStatus();
            }
            String branchCode = this.getBranchCodeByMerchantId(request, merchantId);
            Page<ListProduct> page = new Page<>();
            page.setLimit(limit);
            page.setOffset(offset);
            ListSkuSearchData listSkuSearchData = exhibitionBuineseApi.getExhibitionProductSkuInfoWrapper(exhibitionId, merchantId, branchCode, offset, limit);
            if (listSkuSearchData == null) {
                page.setTotal(0L);
                page.setRows(new ArrayList<>());
            } else {
                page.setTotal(listSkuSearchData.getCount());
                List<ListProduct> skuDtoList = listSkuSearchData.getSkuDtoList();
                LayoutMerchantStatusEnum statusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
                if (!Objects.equals(statusEnum, LayoutMerchantStatusEnum.MARCHANT_NORMAL)) {
                    skuDtoList = layoutBaseService.setProductProperties(skuDtoList, statusEnum, request);
                }
                skuDtoList = layoutBaseService.setProductShopName(skuDtoList);
                page.setRows(skuDtoList);
            }
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(page));
            jsonObject.put("licenseStatus", licenseStatus);
            return this.addResult(new String[]{"data"}, new Object[]{jsonObject});
        } catch (Exception e) {
            logger.error("initExhibitionModulePage异常", e);
            return this.addError("系统异常");
        }
    }
}
