#应用唯一标识，在Apollo Portal（管理界面）创建项目时，同应用id
app.id=xyy-ec-pc
#是否在项目启动的阶段注入默认application namespace的配置
apollo.bootstrap.enabled=true
# 将apollo初始化放在日志系统初始化之前,保证正确的加载日志配置
apollo.bootstrap.eagerLoad.enabled=true
#启动项
spring.profiles.active=test


# REDIS
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A0\uFF09
redis.deploy.type=single
spring.redis.database=0
# Redis\u670D\u52A1\u5668\u5730\u5740
spring.redis.host=nginx01-ec-prod.redis.ybm100.top
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u7AEF\u53E3
spring.redis.port=6379
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u5BC6\u7801\uFF08\u9ED8\u8BA4\u4E3A\u7A7A\uFF09
spring.redis.password=
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09 \u9ED8\u8BA4 8
spring.redis.lettuce.pool.max-active=8
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09 \u9ED8\u8BA4 -1
spring.redis.lettuce.pool.max-wait=-1
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5 \u9ED8\u8BA4 8
spring.redis.lettuce.pool.max-idle=8
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5 \u9ED8\u8BA4 0
spring.redis.lettuce.pool.min-idle=0
spring.jackson.default-property-inclusion=non_null