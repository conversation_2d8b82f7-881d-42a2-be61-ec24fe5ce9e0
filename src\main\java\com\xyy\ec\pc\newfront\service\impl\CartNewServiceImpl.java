package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.message.Transaction;
import com.xyy.cat.util.CatUtil;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.dto.ShoppingCartBusinessDto;
import com.xyy.ec.order.dto.cart.*;
import com.xyy.ec.order.enums.BizSourceEnum;
import com.xyy.ec.order.enums.PlatformEnum;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.newfront.controller.CartNewController;
import com.xyy.ec.pc.newfront.dto.CartRespVO;
import com.xyy.ec.pc.newfront.dto.ChangeCartRespVO;
import com.xyy.ec.pc.newfront.service.CartNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.rpc.OrderServerRpcService;
import com.xyy.ec.pc.service.CartService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Service
@Slf4j
public class CartNewServiceImpl implements CartNewService {


    private final XyyIndentityValidator xyyIndentityValidator;


    private final OrderServerRpcService orderServerRpcService;
    private static final Logger logger = LoggerFactory.getLogger(CartNewController.class);

    private final CartService cartService;

    @Resource
    private HttpServletRequest request;


    @Override
    public AjaxResult<CartRespVO> list() {

        CartRespVO cartRespVO = new CartRespVO();
        try {

            Transaction t1 = CatUtil.initTransaction("cartlist", "cartlist");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return AjaxResult.errResult("请登录");
            }
            GetCartDto getCartDto = new GetCartDto();
            //设置商家下单平台端
            getCartDto.setBizSource(BizSourceEnum.B2B.getKey());
            //设置终端端类型
            getCartDto.setTerminalType(PlatformEnum.PC.getValue());
            //设置商家ID
            getCartDto.setMerchantId(merchant.getId());
            //设置商家角色
            getCartDto.setAccountRole(merchant.getAccountRole());
            //设置商家ID
            getCartDto.setAccountId(merchant.getAccountId());
            //设置是否使用红包
            getCartDto.setUseRedPacket(true);
            if (merchant.getAccountRole().equals(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId())) {
                getCartDto.setUseRedPacket(false);
                //TODO 购物车优惠券咋用的
                getCartDto.setUseOptimal(false);
            }
            //Rpc获取商家购物车信息
            CartBusinessDto cartBusinessDto = orderServerRpcService.getCart(getCartDto);
            BeanUtils.copyProperties(cartBusinessDto, cartRespVO);

            List<FreightInfo> unsatisfiedStartPriceList = cartBusinessDto.getUnsatisfiedStartPriceList();
            if (CollectionUtils.isNotEmpty(unsatisfiedStartPriceList)) {
                String notSubmitOrderOrgIds = unsatisfiedStartPriceList.stream().map(FreightInfo::getOrgId)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(","));
                //未达到起送价
                cartRespVO.setNotSubmitOrderOrgIds(notSubmitOrderOrgIds);
            }

            CatUtil.successCat(t1);
            return AjaxResult.successResult(cartRespVO);
        }
        catch (Exception e){
            throw new AppException(e.getMessage(), XyyJsonResultCodeEnum.FAIL);
        }

        }

    @Override
    public AjaxResult<Integer> getCartNum() {
        try {
            Transaction t2 = CatUtil.initTransaction("batchRemoveProductFromCart", "batchRemoveProductFromCart");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null){
               return AjaxResult.errResult("请登录");
            }
            Integer cartNum = 0;
            logger.info("getCartNum merchant:{}", JSON.toJSONString(merchant));
            if (AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())){
                cartNum = orderServerRpcService.getSkuVarietyNumSubAccount(merchant.getAccountId(),merchant.getId());
            }else {
                if (orderServerRpcService.commonIfaceGray("getCartNum")){
                    cartNum = orderServerRpcService.getSkuVarietyNum(merchant.getId());
                } else {
                    cartNum = cartService.getCartNum(merchant.getId());
                }
            }
            CatUtil.successCat(t2);
            return AjaxResult.successResult(cartNum);

        } catch (Exception e) {
            logger.error("getCartNum error",e);
            throw new AppException(e.getMessage(), XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public AjaxResult<ChangeCartRespVO> changeCart(ShoppingCartBusinessDto cart) {
        try{
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setMerchantId(merchant.getId());
            //终端机类型
            cart.setTerminalType(4);
            cart.setAppVersion(1);
            try {
                Transaction t2 = CatUtil.initTransaction("cartchangeCart", "cartchangeCart");
                String realIP = IPUtils.getClientIP(request);
                cart.setRealIP(realIP);
                logger.info("当前用户请求的IP地址为:"+realIP);

                Map<String, Object> dataMap = new HashMap<>();

                ChangeCartDto changeCartDto = new ChangeCartDto();
                BeanUtils.copyProperties(cart, changeCartDto);
                if (Objects.isNull(changeCartDto.getBizSource())) {
                    changeCartDto.setBizSource(BizSourceEnum.B2B.getKey());
                }

                changeCartDto.setTerminalType(PlatformEnum.PC.getValue());
                changeCartDto.setQuantity(cart.getAmount());
                changeCartDto.setAccountId(merchant.getAccountId());
                changeCartDto.setAccountRole(merchant.getAccountRole());
                dataMap = orderServerRpcService.changeCart(changeCartDto);
                ChangeCartRespVO changeCartRespVO = new ChangeCartRespVO();
                changeCartRespVO.setPrice(dataMap.get("price").toString());
                changeCartRespVO.setQty((Integer) dataMap.get("qty"));
                changeCartRespVO.setSkuId((Long) dataMap.get("skuId"));
                changeCartRespVO.setTotalAmount((BigDecimal) dataMap.get("totalAmount"));
                changeCartRespVO.setMessage(dataMap.get("message") != null ? dataMap.get("message").toString().replace("商品", "").replaceFirst(" ","") : null);
                changeCartRespVO.setDialogStyle(dataMap.get("dialogStyle") != null ? dataMap.get("dialogStyle").toString() : null);
                CatUtil.successCat(t2);

                return AjaxResult.successResult(changeCartRespVO);
            } catch (IllegalArgumentException e) {
                log.error(e.getMessage());
                return AjaxResult.errResult(e.getMessage());
            } catch (RuntimeException e) {
                logger.error("购物车改变数量异常",e);
                return AjaxResult.errResult("购物车改变数量异常");
            }
        }catch (Exception e){
            logger.error("changeCart error",e);
            throw new AppException("系统打盹，请稍后再试", XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public ChangeCartRespVO changeGroupCart(ChangeCartDto cart,HttpServletRequest req) {
        logger.info("changeCartForGroup args ChangeCartDto:{}", cart);

        if (cart.getSkuId() == null || cart.getMerchantId() == null) {
            throw new AppException("商品ID、客户ID不能为空",XyyJsonResultCodeEnum.FAIL);
        }
        if (cart.getQuantity() == null || cart.getQuantity() <= 0) {
            throw new AppException("数量必须大于0",XyyJsonResultCodeEnum.FAIL);
        }
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            cart.setAccountId(merchant.getAccountId());
            cart.setAccountRole(merchant.getAccountRole());
            cart.setTerminalType(4);
            String realIp = IPUtils.getClientIP(req);
            cart.setRealIp(realIp);
            Map<String, Object> dataMap = cartService.changeCartForPromotion(cart);
            if (CollectionUtil.isEmpty(dataMap)) {
                throw new AppException("修改拼团数量失败", XyyJsonResultCodeEnum.FAIL);
            }
            ChangeCartRespVO changeCartRespVO = new ChangeCartRespVO();
            changeCartRespVO.setPrice(dataMap.get("price").toString());
            changeCartRespVO.setQty((Integer) dataMap.get("qty"));
            changeCartRespVO.setSkuId((Long) dataMap.get("skuId"));
            changeCartRespVO.setTotalAmount((BigDecimal) dataMap.get("totalAmount"));
            return changeCartRespVO;
        } catch (IllegalArgumentException e) {
            throw new AppException(e.getMessage(), XyyJsonResultCodeEnum.FAIL);
        } catch (Exception e) {
            logger.error("修改拼团数量异常", e);
            throw new AppException("修改拼团数量异常", XyyJsonResultCodeEnum.FAIL);
        }
    }

    @Override
    public AjaxResult<CartRespVO> selectItem(SelectCartDto cart) {
        if (cart.getSkuId() == null && cart.getPackageId() == null) {
            return AjaxResult.errResult("购物车ID不能为空");
        }
        Transaction t2 = CatUtil.initTransaction("selectItem", "selectItem");
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipalEaseEx();
        if (merchant.getId() == null){
            return AjaxResult.errResult("商户ID不能为空");
        }
        cart.setMerchantId(merchant.getId());
        try {
            orderServerRpcService.selectItem(cart);
        } catch (AppException e) {
            CatUtil.errorCat(t2, e);
            logger.error("修改购物车数量异常",e);
            return AjaxResult.errResult("修改购物车数量异常");
        }

        CatUtil.successCat(t2);
        return AjaxResult.successResultNotResult("修改成功");
    }

    @Override
    public AjaxResult cancelItem(SelectCartDto cart) {
        if (cart.getSkuId() == null && cart.getPackageId() == null) {
            return AjaxResult.errResult("购物车ID不能为空");
        }
        Transaction t2 = CatUtil.initTransaction("selectItem", "selectItem");
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipalEaseEx();
        if (merchant.getId() == null){
            return AjaxResult.errResult("商户ID不能为空");
        }
        cart.setMerchantId(merchant.getId());
        try {
            orderServerRpcService.unSelectItem(cart);
        }catch (AppException e){
            CatUtil.errorCat(t2, e);
            logger.error("取消购物车商品异常",e);
            return AjaxResult.errResult("取消购物车商品异常");
        }

        CatUtil.successCat(t2);
        return AjaxResult.successResultNotResult("取消成功");

    }

    @Override
    public AjaxResult selectAllItem(BatchSelectCartDto cart) {
        Transaction t2 = CatUtil.initTransaction("selectAllItem", "selectAllItem");
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipalEaseEx();
        if (merchant.getId() == null){
            return AjaxResult.errResult("商户ID不能为空");
        }
        cart.setMerchantId(merchant.getId());
        try {
            orderServerRpcService.batchSelectByGoupId(cart);
        }catch (AppException e){
            CatUtil.errorCat(t2, e);
            logger.error("全选购物车商品异常",e);
            return AjaxResult.errResult("全选购物车商品异常");
        }
        CatUtil.successCat(t2);
        return AjaxResult.successResultNotResult("全选成功");
    }

    @Override
    public AjaxResult cancelAllItem(BatchSelectCartDto cart) {
        Transaction t2 = CatUtil.initTransaction("cancelAllItem", "cancelAllItem");
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipalEaseEx();
        if (merchant.getId() == null){
           return AjaxResult.errResult("商户ID不能为空");
        }
        cart.setMerchantId(merchant.getId());
        try {
            orderServerRpcService.batchUnSelectByGroupId(cart);
        }catch (AppException e){
            CatUtil.errorCat(t2, e);
            logger.error("取消全选购物车商品异常",e);
            return AjaxResult.errResult("取消全选购物车商品异常");
        }

        CatUtil.successCat(t2);
        return AjaxResult.successResultNotResult("取消全选成功");
    }

    @Override
    public AjaxResult cleanCart() {
        try {
            Transaction t2 = CatUtil.initTransaction("cleanCart", "cleanCart");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipalEaseEx();
            if (AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
                orderServerRpcService.clearCartSubAccount(merchant.getAccountId(), merchant.getMerchantId());
            } else {
                orderServerRpcService.clearCart(merchant.getId());

            }
            CatUtil.successCat(t2);
            return AjaxResult.successResultNotResult("清空购物车成功");
        } catch (AppException e) {
            logger.error("清空购物车异常", e);
            return AjaxResult.errResult("清空购物车异常");
        }
    }


}
