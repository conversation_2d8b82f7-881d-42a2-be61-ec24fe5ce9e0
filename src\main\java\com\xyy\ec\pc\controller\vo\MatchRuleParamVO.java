package com.xyy.ec.pc.controller.vo;

import lombok.Data;

import java.util.List;

/**
 *
 * 匹配规则实体
 */
@Data
public class MatchRuleParamVO {
    //自营店铺编码
    private List<MatchRuleShopCodeParam> shopCodeList;
    //pop店铺编码
    private List<MatchRuleShopCodeParam> popShopCodeList;
    //是否满足采购库存
    private Boolean isCheckStock;

    //是否满足限购采购数量
    private Boolean isCheckLimit;

    //是否满足起订采购数量
    private Boolean isCheckMinOrder;
    //是否匹配自然人店铺商品
    //private Boolean isMatchPersonalShop;
    /** 近效期大于多少天 */
    private Integer nearEffectDay;
    /** 客户id */
    private Long merchantId;
    /**
     * 比价类型: 1:折后价，2:连锁指导价
     */
    private Integer matchPriceType;
}
