package com.xyy.ec.api.busi.shop;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.api.busi.base.WebBaseController;
import com.xyy.ec.api.rpc.hyperspace.HyperspaceRpc;
import com.xyy.ec.api.service.ActService;
import com.xyy.ec.marketing.hyperspace.api.common.dto.ApiActivityDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.enums.LayoutMerchantStatusEnum;
import com.xyy.ec.pc.exception.ShopException;
import com.xyy.ec.pc.model.dto.XyyIpAddressInfoDTO;
import com.xyy.ec.pc.rpc.MarketActivityPackageRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.XyyIpAddressService;
import com.xyy.ec.pc.service.layout.LayoutBaseService;
import com.xyy.ec.pc.shop.service.ShopImageService;
import com.xyy.ec.pc.shop.service.ShopProductService;
import com.xyy.ec.pc.shop.service.ShopService;
import com.xyy.ec.pc.shop.vo.ShopImageVO;
import com.xyy.ec.pc.shop.vo.ShopInfoVO;
import com.xyy.ec.pc.util.EncodeUtil;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.dto.product.ProductActivityTag;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.shop.server.business.enums.ShopImagePlatformTypeEnum;
import com.xyy.ec.shop.server.business.enums.ShopImageTypeEnum;
import com.xyy.ec.shop.server.business.enums.ShopProductQueryOrderColumnEnum;
import com.xyy.ec.shop.server.business.params.ShopImageQueryParam;
import com.xyy.ec.shop.server.business.params.ShopProductQueryOrderItemParam;
import com.xyy.ec.shop.server.business.params.ShopProductQueryPageParam;
import com.xyy.ec.shop.server.business.params.ShopProductQueryParam;
import com.xyy.ec.shop.server.business.results.ShopCategoryDTO;
import com.xyy.ms.marketing.nine.chapters.api.pc.ActivitySkuBuyLimitForPcApi;
import com.xyy.ms.marketing.nine.chapters.common.config.ActivityEnum;
import com.xyy.ms.promotion.business.dto.activitypackage.ActivityPackageSkuDTO;
import com.xyy.ms.promotion.business.dto.activitypackage.ActivityPackageVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/18 18:02
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/actshop")
public class ActivityShop extends WebBaseController {

    @Autowired
    private ShopService shopService;

    @Autowired
    private ShopImageService shopImageService;

    @Autowired
    private ShopProductService shopProductService;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private XyyIpAddressService xyyIpAddressService;

    @Autowired
    private LayoutBaseService layoutBaseService;
    @Autowired
    private HyperspaceRpc hyperspaceRpc;
    @Autowired
    private ActService actService;
    @Autowired
    private MarketActivityPackageRpc marketActivityPackageRpc;
    @Autowired
    private PcVersionUtils pcVersionUtils;

    /**
     * 店铺活动
     * @return
     */
    @RequestMapping("/shop_activity.htm")
    public ModelAndView activity(ACTReq actReq,HttpServletRequest request) throws Exception {
        ModelAndView modelAndView = new ModelAndView("/shop/shop_activity.ftl");
        actReq.setBranchCode(this.getBranchCode());
        actReq.setUserId(this.getUserId());
        AtomicInteger isStop = new AtomicInteger(1);//活动结束0否 1是
        List list = new ArrayList();
        List<ApiActivityDto>  acts = hyperspaceRpc.findMarketingActivityForApi(actReq.getBranchCode(),actReq.getUserId(),actReq.getShopCode(),actReq.getActId());
        if(CollectionUtils.isNotEmpty(acts)){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
            acts.forEach(e->{
                if (actReq.getActId()==null){
                    actReq.setActId(e.getActId());
                }
                Map map = new HashMap();
                map.put("actId",e.getActId());
                map.put("actTitle",e.getActTitle());
                map.put("startTime",e.getStartTime());
                map.put("endTime",e.getEndTime());
                if (e.getStartTime().after(new Date())){
                    map.put("status",0);
                    map.put("statusStr","未开始");
                }else if (e.getEndTime().before(new Date())){
                    map.put("status",2);
                    map.put("statusStr","已结束");
                }else{
                    map.put("status",1);
                    map.put("statusStr","进行中");
                }
                if(actReq.getActId().longValue()==e.getActId()){
                    if (e.getEndTime().before(new Date())){
                        isStop.set(1);
                    }else{
                        isStop.set(0);
                    }
                }
                long time = (e.getStartTime().getTime()-System.currentTimeMillis())/1000;
                map.put("limitSpecialTimer",time);
                map.put("timeStr", simpleDateFormat.format(e.getStartTime())+"至"+simpleDateFormat.format(e.getEndTime()));
                map.put("ruleStr",e.getRuleStr());
                list.add(map);
            });
        }
        if (actReq.getActId()==null){//没有获得不弹窗
            isStop.set(0);
        }
        modelAndView.addObject("tags",list);
        modelAndView.addObject("actId",actReq.getActId());
        modelAndView.addObject("isStop",isStop);
        excuteAct(modelAndView,actReq,request);
        return modelAndView;
    }

    private void excuteAct(ModelAndView modelAndView,ACTReq actReq,HttpServletRequest request) throws Exception {
        String shopCode = actReq.getShopCode();
        Integer modelType = actReq.getModelType();
        Integer hasStock = actReq.getHasStock();
        Integer totalSalesVolumeType = actReq.getTotalSalesVolumeType();
        Integer tagType = actReq.getTagType();
         Integer offset = actReq.getOffset();
         Integer limit = actReq.getLimit();
        Long merchantId = null;

        // 回显查询条件
        modelAndView.addObject("shopCode", shopCode);
        modelAndView.addObject("modelType", modelType);
        modelAndView.addObject("hasStock", hasStock);
        modelAndView.addObject("totalSalesVolumeType", totalSalesVolumeType);
        modelAndView.addObject("tagType", tagType);
        modelAndView.addObject("offset", offset);
        modelAndView.addObject("limit", limit);
        // 分页要用，将url传到前台
        String url = getRequestUrl(request);
        // 初始化 & 转换
        boolean totalSalesVolume = false;
        boolean totalSalesVolumeIsAsc = false;
        if (totalSalesVolumeType != null) {
            if (totalSalesVolumeType.equals(1)) {
                totalSalesVolume = true;
                totalSalesVolumeIsAsc = false;
            } else if (totalSalesVolumeType.equals(2)) {
                totalSalesVolume = true;
                totalSalesVolumeIsAsc = true;
            }
        }
        Integer realHasStock = hasStock;
        if (realHasStock != null && !realHasStock.equals(1)) {
            realHasStock = null;
        }
        int realPageNum = offset == null ? 1 : offset;
        int realPageSize = limit == null ? 20 : limit;

        String branchCode;
        int provinceCode = 0;
        Integer licenseStatus = null;
        // 取出当前登录会员ID
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        if (merchant != null) {
            merchantId = merchant.getId();
            branchCode = merchant.getRegisterCode();
            licenseStatus = merchant.getLicenseStatus();
        } else {
            // 未登录从ip中定位获取
            XyyIpAddressInfoDTO xyyIpAddressInfo = xyyIpAddressService.getXyyIpAddressInfo(request);
            branchCode = xyyIpAddressInfo.getBranchCode();
            provinceCode = xyyIpAddressInfo.getProvinceCode();
        }
        if (log.isDebugEnabled()) {
            log.debug("取出当前的区域相关信息，merchantId：{}，branchCode：{}，provinceCode：{}",
                    merchantId, branchCode, provinceCode);
        }
        ShopInfoVO shopInfoVO = null;
        List<ShopImageVO> shopSlideshowImageVOS = Lists.newArrayList();
        List<ShopImageVO> shopFloorImageVOS = Lists.newArrayList();
        PageInfo<ProductDTO> pageInfo = new PageInfo<>(Lists.newArrayList());
        pageInfo.setPageNum(realPageNum);
        pageInfo.setPageSize(realPageSize);
        // 校验店铺可见性
        boolean isVisible = shopService.getShopVisible(shopCode, merchantId, provinceCode);
        // 店铺信息
        if (isVisible) {
            shopInfoVO = shopService.getByCode(shopCode);
            isVisible = shopInfoVO != null;
            if (shopInfoVO != null) {
                // 店铺图片列表
                // 首页轮播图列表
                ShopImageQueryParam shopImageQueryParam = ShopImageQueryParam.builder().shopCode(shopCode)
                        .platformType(ShopImagePlatformTypeEnum.PC.getType())
                        .imageType(ShopImageTypeEnum.INDEX_SLIDESHOW.getType())
                        .build();
                shopSlideshowImageVOS = shopImageService.listShopImages(shopImageQueryParam);
                // 首页通栏图列表
                shopImageQueryParam = ShopImageQueryParam.builder().shopCode(shopCode)
                        .platformType(ShopImagePlatformTypeEnum.PC.getType())
                        .imageType(ShopImageTypeEnum.INDEX_FLOOR.getType())
                        .build();
                shopFloorImageVOS = shopImageService.listShopImages(shopImageQueryParam);
                // 店铺商品列表
                List<ShopProductQueryOrderItemParam> orderItems = Lists.newArrayListWithCapacity(1);
                // 默认按照销量从高到底排序
                String salesVolumeColumn = ShopProductQueryOrderColumnEnum.THIRTY_DAYS_SALES_VOLUME.getColumn();
                boolean salesVolumeIsAsc = false;
                if (totalSalesVolume) {
                    salesVolumeColumn = ShopProductQueryOrderColumnEnum.TOTAL_SALES_VOLUME.getColumn();
                    salesVolumeIsAsc = totalSalesVolumeIsAsc;
                }
                ShopProductQueryOrderItemParam orderItemParam = ShopProductQueryOrderItemParam.builder()
                        .column(salesVolumeColumn)
                        .asc(salesVolumeIsAsc)
                        .build();
                orderItems.add(orderItemParam);
                ShopProductQueryParam shopProductQueryParam = ShopProductQueryParam.builder()
                        .shopCode(shopCode).merchantId(merchantId).branchCode(branchCode).hasStock(realHasStock).orderItems(orderItems)
                        .build();
                pageInfo = actService.pagingShopProducts(shopProductQueryParam, realPageNum, realPageSize,actReq.getActId());
//                pageInfo = shopProductService.pagingShopProducts(shopProductQueryParam, realPageNum, realPageSize);

                // 商品信息根据资质状态过滤
                List<ProductDTO> list = pageInfo.getList();
                LayoutMerchantStatusEnum layoutMerchantStatusEnum = layoutBaseService.checkMerchantStatusByMerchantId(merchant);
                if (LayoutMerchantStatusEnum.MARCHANT_LICENSE_NO_PASS.equals(layoutMerchantStatusEnum)) {
                    list = layoutBaseService.filterProductAttrsForShop(list);
                    pageInfo.setList(list);
                }
            }
        }
        modelAndView.addObject("merchant", merchant);
        modelAndView.addObject("merchantId", merchantId);
        modelAndView.addObject("licenseStatus", licenseStatus);
        modelAndView.addObject("isVisible", isVisible);
        modelAndView.addObject("shopInfo", shopInfoVO);
        modelAndView.addObject("shopSlideshowImages", shopSlideshowImageVOS);
        modelAndView.addObject("shopFloorImages", shopFloorImageVOS);
        if (hyperspaceRpc.hasAct(branchCode,merchantId,shopCode)){
            modelAndView.addObject("hasAct",1);
        }
        if (hyperspaceRpc.hasShopPackageActivity(shopCode,merchantId)){
            modelAndView.addObject("hasShopPackageActivity",1);
        }
        Page<ProductDTO> skuPOJOPage = new Page<>();
        skuPOJOPage.setOffset(pageInfo.getPageNum());
        skuPOJOPage.setLimit(pageInfo.getPageSize());
        if (pageInfo.getTotal()>0){//bug...
            skuPOJOPage.setTotal(pageInfo.getTotal());
        }
        skuPOJOPage.setRows(pageInfo.getList());
        skuPOJOPage.setRequestUrl(url);
        modelAndView.addObject("pager", skuPOJOPage);
    }


    /**
     * 拼接页面参数
     *
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    protected String getRequestUrl(HttpServletRequest request){
        String url = "";
        String requestUri = request.getRequestURI();
        String queryString = request.getQueryString();
        String qs = StringUtil.removeParameter(queryString, "offset");
        if (requestUri.contains("/xyy-ec-pc/")) {
            requestUri = requestUri.replace("/xyy-ex-pc/", "/");
        }
        if (StringUtil.isNotEmpty(qs)) {
            url = requestUri + "?" + EncodeUtil.urlDecode(qs,"UTF-8");
        } else {
            url = requestUri;
        }
        return url;
    }

    /**
     * 店铺套餐活动页
     * @param shopCode  商品编码
     * @param actReq
     * @param request
     * @return
     */
    @RequestMapping("/shop/activityPackage.htm")
    public ModelAndView shopActivityPackageIndex(String shopCode, ACTReq actReq, HttpServletRequest request){
        ModelAndView modelAndView = new ModelAndView("/shop/shop_taocan.ftl");
        actReq.setBranchCode(this.getBranchCode());
        Long merchantId = null;
        try {
        // 取出当前登录会员ID
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
        modelAndView.addObject("merchant", merchant);
        if(null != merchant){
            merchantId = merchant.getId();
            actReq.setUserId(merchantId);
        }
            modelAndView.addObject("merchantId", merchantId);
        //店铺套餐列表
        List<ActivityPackageVo> packageList = marketActivityPackageRpc.getShopActivityPackageList(shopCode, merchantId);
        //商品价格可见性逻辑
        packageList = addSkuPriceVisible(packageList, merchant);
        modelAndView.addObject("packageList", packageList);
        modelAndView.addObject("currentDate", new Date());
        modelAndView.addObject("actId",actReq.getActId());
        excuteAct(modelAndView,actReq,request);
        return modelAndView;
        }catch (ShopException e) {
            if (e.isWarn()) {
                log.error("进入店铺套餐页失败，shopCode：{}，merchantId：{}", shopCode, merchantId, e);
            }
        } catch (Exception e) {
            log.error("进入店铺套餐页失败，shopCode：{}，merchantId：{}", shopCode, merchantId, e);
        }
        return new ModelAndView("/error/500.ftl");
    }

    /**
     * 添加商品零售价是否可见逻辑
     * @param packageList
     * @param merchant
     * @return
     */
    private List<ActivityPackageVo> addSkuPriceVisible(List<ActivityPackageVo> packageList, MerchantBussinessDto merchant){
        if(CollectionUtils.isNotEmpty(packageList) && (null == merchant || merchant.getPriceDisplayFlag() == false)){
            ProductActivityTag productActivityTagVO = new ProductActivityTag();
            productActivityTagVO.setTagUrl("");
            productActivityTagVO.setTagNoteBackGroupUrl("");
            productActivityTagVO.setSkuTagNotes(new ArrayList<>());
            for(ActivityPackageVo packageVo : packageList){
                if(CollectionUtils.isNotEmpty(packageVo.getSkuList())){
                    for(ActivityPackageSkuDTO product : packageVo.getSkuList()){
                        product.setFob(BigDecimal.ZERO);
                        if(product.getSku() != null){
                            product.getSku().setFob(0d);
                            product.getSku().setUnitPrice(null);
                            product.getSku().setUnitPriceTag(null);
                            product.getSku().setActivityTag(productActivityTagVO);
                            //毛利
                            product.getSku().setGrossMargin("");
                            //建议零售价
                            product.getSku().setSuggestPrice(BigDecimal.ZERO);
                            //控销零售价
                            product.getSku().setUniformPrice(BigDecimal.ZERO);
                            //对比价
                            product.getSku().setRetailPrice(0.0);
                        }
                    }
                }
            }
        }
        return packageList;
    }
}
