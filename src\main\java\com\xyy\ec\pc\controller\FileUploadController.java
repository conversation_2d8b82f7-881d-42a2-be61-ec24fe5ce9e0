/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.xyy.ec.pc.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.util.DateUtil;
import com.xyy.ec.pc.util.FileUploadUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 上传图片控制器
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/merchant/center/uploadFile")
public class FileUploadController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(FileUploadController.class);

    @Autowired
    private XyyConfig.CdnConfig cdnConfig;

    @Autowired
    private Config config;

    @Value("${ossDomain:https://oss-ec-test.ybm100.com/}")
    public String ossDomain;

    /**
     * 上传文件到FTP服务器
     *
     * @param request
     * @param response
     * @param uploadPath
     * @param targetFileName
     * @return
     */
    @ResponseBody
    @RequestMapping("/invoke")
    public Object uploadFile(HttpServletRequest request, HttpServletResponse response,
            @RequestParam("uploadPath") String uploadPath,
            @RequestParam(value = "targetFileName", required = false) String targetFileName) {
        //上传文件所属目录
        try {
            String localTempPath = System.getProperty("xyy-webservice");
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("merchant/center/uploadFile/invoke ，uploadPath：{}，targetFileName：{}", uploadPath, targetFileName);
            }
            Map<String,Object> map = FileUploadUtil.fileUpload(request, uploadPath, cdnConfig, targetFileName, localTempPath);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("merchant/center/uploadFile/invoke ，map：{}", JSONObject.toJSONString(map));
            }
            map.put("productImageUrl", config.getProductImagePathUrl());
            JSONArray filePaths = new JSONArray();
            String relativeFilePath;
            if (Objects.nonNull(map)) {
                String status = (String) map.get("status");
                if (Objects.equals(status, "success")) {
                    List<String> fileNameList = (List<String>) map.get("fileName");
                    if (CollectionUtils.isNotEmpty(fileNameList)) {
                        for (String fileName : fileNameList) {
                            JSONObject filePathObj = new JSONObject();
                            relativeFilePath = uploadPath + "/" + fileName;
                            filePathObj.put("relativeFilePath", relativeFilePath);
                            filePathObj.put("absoluteFilePath", config.getProductImagePathUrl() + relativeFilePath);
                            filePaths.add(filePathObj);
                        }
                    }
                }
            }
            map.put("filePaths", filePaths);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("merchant/center/uploadFile/invoke ，map：{}", JSONObject.toJSONString(map));
            }
            return map;
        } catch (Exception e) {
            LOGGER.error("文件上传异常",e);
            return this.addError("文件上传异常");
        }
    }

    /**
     * 上传
     *
     * @param uploadPath 规范：前后带"/", 可以不同业务定义一个目录：如/ybm/evidences/
     * @param request    请求request
     * @return
     */
    @ResponseBody
    @RequestMapping("/upload")
    public Object uploadImage(@RequestParam("uploadPath") String uploadPath, HttpServletRequest request) {


        try {
            LOGGER.error("文件上传uploadImage：{}", uploadPath);
            String path = uploadPath+ DateUtil.date2String(new Date(), "yyyyMMdd")+"/";
            Map<String, Object> resultMap = FileUploadUtil.fileUpload(request, path, cdnConfig, null, null);
            LOGGER.error("文件上传resultMap：{}", resultMap);
            List<String> fileNameList = (List<String>) resultMap.get("fileName");
            Map result = new HashMap<>();
            List<String> newFileNameList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(fileNameList)) {
                fileNameList.forEach(fileName -> newFileNameList.add(path + fileName));
            }
            result.put("host",ossDomain);
            result.put("downloadPath",newFileNameList);
            return this.addResult("data",result);
        } catch (Exception e) {
            LOGGER.error("文件上传异常：", e);
            return this.addError("文件上传异常");
        }
    }
}
