package com.xyy.ec.pc.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ec.system.business.api.SendSMSBusinessApi;
import com.xyy.ec.system.business.common.MessageBodyBusinessDTO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 第三方SmS短息中心控制器
 */
@Controller
@RequestMapping("/crm")
public class ToSmSApiController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ToSmSApiController.class);

    @Reference(version = "1.0.0")
    private SendSMSBusinessApi sendSMSBusinessApi;

    /**
     * ip校验开关 0：开1：关
     */
    @Value("${ip.switch}")
    private String ipSwitch;

    /**
     * ip白名单
     */
    @Value("${ip.whitelist}")
    private String ipWhitelist;

    @Autowired
    private HttpServletRequest request;

    /**
     * 发送“小药药监控系统告警通知”短信
     *
     * @param mobile
     * @param appName
     * @param content
     * @return
     */
    @RequestMapping("/getYyyYbmMonitoringwarning")
    @ResponseBody
    public Object getYyyYbmMonitoringWarning(@RequestParam("mobile") String mobile,
                                             @RequestParam("appName") String appName,
                                             @RequestParam("content") String content) {
        try {
            sendSMSBusinessApi.getYyyYbmMonitoringWarning(mobile, appName, content);
            return this.addResult("发送成功");
        } catch (Exception e) {
            LOGGER.error("发送“小药药监控系统告警通知”短信失败", e);
            return this.addError("发送“小药药监控系统告警通知”短信失败");
        }
    }

    /**
     * 发送“单体联营验证码”短信
     *
     * @param mobile
     * @param userPhoneNumber
     * @param drugstoreName
     * @param verifyCode
     * @return
     */
    @RequestMapping("/sms/send/dtly/verificationCode")
    @ResponseBody
    public Object sendSmsForDtlyVerificationCode(@RequestParam("mobile") String mobile,
                                                 @RequestParam(value = "userPhoneNumber", required = false) String userPhoneNumber,
                                                 @RequestParam(value = "drugstoreName", required = false) String drugstoreName,
                                                 @RequestParam(value = "verifyCode", required = false) String verifyCode) {
        try {
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.sendSmsForDtlyVerificationCode(mobile,
                    userPhoneNumber, drugstoreName, verifyCode);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“单体联营验证码”短信失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 使用阿里云短信服务批量发送短信。
     *
     * @param mobiles      短信接收号码
     * @param signName     短信签名
     * @param templateCode 短信模板
     * @param params       模板参数，Map的json字符串
     * @return
     */
    @RequestMapping(value = "/sms/aliyun/sendBatch", method = RequestMethod.POST)
    @ResponseBody
    public Object sendBatchSmsUsingAliyun(@RequestParam("mobiles") String mobiles, @RequestParam("signName") String signName,
                                          @RequestParam("templateCode") String templateCode, @RequestParam(value = "params", required = false) String params) {
        try {
            List<String> phoneNumbers = splitPhoneNumbers(mobiles);
            Map<String, Object> paramsObjectMap = null;
            if (StringUtils.isNotEmpty(params)) {
                paramsObjectMap = JSONObject.parseObject(params, Map.class);
            }
            Map<String, String> paramsMap = null;
            if (MapUtils.isNotEmpty(paramsObjectMap)) {
                paramsMap = Maps.newHashMapWithExpectedSize(paramsObjectMap.size());
                String hashValue = null;
                for (Map.Entry<String, Object> entry : paramsObjectMap.entrySet()) {
                    if (entry != null && entry.getKey() != null) {
                        if (entry.getValue() != null) {
                            hashValue = String.valueOf(entry.getValue());
                        } else {
                            hashValue = null;
                        }
                        paramsMap.put(entry.getKey(), hashValue);
                    }
                }
            }
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.sendBatchSmsUsingAliYun(phoneNumbers, signName,
                    templateCode, paramsMap);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            // 应要求，失败时，数据格式与成功时一致。
            LOGGER.error("请求阿里云短信服务发送短信接口失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 使用海岩短信服务批量发送短信。
     *
     * @param mobiles 短信接收号码
     * @param type    短信类型，字符串类型。可接受值：1：营销类；2：通知类。若不填写，默认为1。
     * @param content 短信内容，含签名，营销类短信请末尾追加“退订回T”。String 类型。必填。
     * @return
     */
    @RequestMapping(value = "/sms/haiyan/sendBatch", method = RequestMethod.POST)
    @ResponseBody
    public Object sendBatchSmsUsingHaiYan(@RequestParam("mobiles") String mobiles, @RequestParam(value = "type", required = false) String type,
                                          @RequestParam("content") String content) {
        try {
            //获取开关
            if("0".equals(ipSwitch)){
                //获取调用方ip
                String realIP = IPUtils.getClientIP(request);
                LOGGER.info("当前用户请求的IP地址为:"+realIP);
                //判断是否在白名单
                if (!ipWhitelist.contains(realIP)) {
                    return this.addResult(false, "此ip不能发送短信");
                }
            }
            List<String> phoneNumbers = splitPhoneNumbers(mobiles);
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.sendBatchSmsUsingHaiYan(phoneNumbers, type, content);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            // 应要求，失败时，数据格式与成功时一致。
            LOGGER.error("请求海岩短信服务发送短信接口失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 发送短信 <br/>
     * 会员：远程问诊APP账号开通
     *
     * @param mobile   短信接收号码，单个
     * @param account
     * @param password
     * @return
     */
    @RequestMapping("/sms/send/memberRemoteInquiryAppOpen")
    @ResponseBody
    public Object sendSmsForMemberRemoteInquiryAppOpen(@RequestParam("mobile") String mobile,
                                                       @RequestParam(value = "account", required = false) String account,
                                                       @RequestParam(value = "password", required = false) String password) {
        try {
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.getMemberRemoteInquiryAppOpen(mobile, account, password);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“会员：远程问诊APP账号开通”失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 发送短信 <br/>
     * 会员：药店充值+回赠+余额
     *
     * @param mobiles
     * @param organName
     * @param amount
     * @param bonus
     * @param currentAmount
     * @param currentBonus
     * @return
     */
    @RequestMapping("/sms/sendBatch/memberDrugstorePayFeedback")
    @ResponseBody
    public Object sendBatchSmsForMemberDrugstorePayFeedback(@RequestParam("mobiles") String mobiles,
                                                            @RequestParam(value = "organName", required = false) String organName,
                                                            @RequestParam(value = "amount", required = false) String amount,
                                                            @RequestParam(value = "bonus", required = false) String bonus,
                                                            @RequestParam(value = "currentAmount", required = false) String currentAmount,
                                                            @RequestParam(value = "currentBonus", required = false) String currentBonus) {
        try {
            List<String> phoneNumbers = splitPhoneNumbers(mobiles);
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.getMemberDrugstorePayFeedback(phoneNumbers,
                    organName, amount, bonus, currentAmount, currentBonus);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“会员：药店充值+回赠+余额”失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 发送短信 <br/>
     * 会员：药店充值+余额
     *
     * @param mobiles
     * @param organName
     * @param amount
     * @param currentAmount
     * @param currentBonus
     * @return
     */
    @RequestMapping("/sms/sendBatch/memberDrugstorePayBalance")
    @ResponseBody
    public Object sendBatchSmsForMemberDrugstorePayBalance(@RequestParam("mobiles") String mobiles,
                                                           @RequestParam(value = "organName", required = false) String organName,
                                                           @RequestParam(value = "amount", required = false) String amount,
                                                           @RequestParam(value = "currentAmount", required = false) String currentAmount,
                                                           @RequestParam(value = "currentBonus", required = false) String currentBonus) {
        try {
            List<String> phoneNumbers = splitPhoneNumbers(mobiles);
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.getMemberDrugstorePayBalance(phoneNumbers,
                    organName, amount, currentAmount, currentBonus);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“会员：药店充值+余额”失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 发送短信 <br/>
     * 会员：药店赠送+余额
     *
     * @param mobiles
     * @param organName
     * @param bonus
     * @param currentAmount
     * @param currentBonus
     * @return
     */
    @RequestMapping("/sms/sendBatch/memberFeedbackBalance")
    @ResponseBody
    public Object sendBatchSmsForMemberFeedbackBalance(@RequestParam("mobiles") String mobiles,
                                                       @RequestParam(value = "organName", required = false) String organName,
                                                       @RequestParam(value = "bonus", required = false) String bonus,
                                                       @RequestParam(value = "currentAmount", required = false) String currentAmount,
                                                       @RequestParam(value = "currentBonus", required = false) String currentBonus) {
        try {
            List<String> phoneNumbers = splitPhoneNumbers(mobiles);
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.getMemberFeedbackBalance(phoneNumbers,
                    organName, bonus, currentAmount, currentBonus);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“会员：药店赠送+余额”失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 发送短信 <br/>
     * 会员：药店消费+余额+赠送余额
     *
     * @param mobiles
     * @param organName
     * @param payAmount
     * @param currentAmount
     * @param currentBonus
     * @return
     */
    @RequestMapping("/sms/sendBatch/memberExpenseBalanceFeedback")
    @ResponseBody
    public Object sendBatchSmsForMemberExpenseBalanceFeedback(@RequestParam("mobiles") String mobiles,
                                                              @RequestParam(value = "organName", required = false) String organName,
                                                              @RequestParam(value = "payAmount", required = false) String payAmount,
                                                              @RequestParam(value = "currentAmount", required = false) String currentAmount,
                                                              @RequestParam(value = "currentBonus", required = false) String currentBonus) {
        try {
            List<String> phoneNumbers = splitPhoneNumbers(mobiles);
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.getMemberExpenseBalanceFeedback(phoneNumbers,
                    organName, payAmount, currentAmount, currentBonus);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“会员：药店消费+余额+赠送余额”失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 发送短信 <br/>
     * 会员：药店退款+余额+赠送余额
     *
     * @param mobiles
     * @param organName
     * @param refundAmount
     * @param currentAmount
     * @param currentBonus
     * @return
     */
    @RequestMapping("/sms/sendBatch/memberRefundBalanceFeedback")
    @ResponseBody
    public Object sendBatchSmsForMemberRefundBalanceFeedback(@RequestParam("mobiles") String mobiles,
                                                             @RequestParam(value = "organName", required = false) String organName,
                                                             @RequestParam(value = "refundAmount", required = false) String refundAmount,
                                                             @RequestParam(value = "currentAmount", required = false) String currentAmount,
                                                             @RequestParam(value = "currentBonus", required = false) String currentBonus) {
        try {
            List<String> phoneNumbers = splitPhoneNumbers(mobiles);
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.getMemberRefundBalanceFeedback(phoneNumbers,
                    organName, refundAmount, currentAmount, currentBonus);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“会员：药店退款+余额+赠送余额”失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 发送短信 <br/>
     * 会员：药店扣减+扣减赠送+余额
     *
     * @param mobiles
     * @param organName
     * @param amount
     * @param bonus
     * @param currentAmount
     * @param currentBonus
     * @return
     */
    @RequestMapping("/sms/sendBatch/memberDeductionFeedbackBalance")
    @ResponseBody
    public Object sendBatchSmsForMemberDeductionFeedbackBalance(@RequestParam("mobiles") String mobiles,
                                                                @RequestParam(value = "organName", required = false) String organName,
                                                                @RequestParam(value = "amount", required = false) String amount,
                                                                @RequestParam(value = "bonus", required = false) String bonus,
                                                                @RequestParam(value = "currentAmount", required = false) String currentAmount,
                                                                @RequestParam(value = "currentBonus", required = false) String currentBonus) {
        try {
            List<String> phoneNumbers = splitPhoneNumbers(mobiles);
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.getMemberDeductionFeedbackBalance(phoneNumbers,
                    organName, amount, bonus, currentAmount, currentBonus);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“会员：药店扣减+扣减赠送+余额”失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 发送短信 <br/>
     * 会员：药店扣减储值+余额+赠送余额
     *
     * @param mobiles
     * @param organName
     * @param amount
     * @param currentAmount
     * @param currentBonus
     * @return
     */
    @RequestMapping("/sms/sendBatch/memberDeductionRechargeBalanceFeedback")
    @ResponseBody
    public Object sendBatchSmsForMemberDeductionRechargeBalanceFeedback(@RequestParam("mobiles") String mobiles,
                                                                        @RequestParam(value = "organName", required = false) String organName,
                                                                        @RequestParam(value = "amount", required = false) String amount,
                                                                        @RequestParam(value = "currentAmount", required = false) String currentAmount,
                                                                        @RequestParam(value = "currentBonus", required = false) String currentBonus) {
        try {
            List<String> phoneNumbers = splitPhoneNumbers(mobiles);
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.getMemberDeductionRechargeBalanceFeedback(phoneNumbers,
                    organName, amount, currentAmount, currentBonus);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“会员：药店扣减储值+余额+赠送余额”失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 发送短信 <br/>
     * 会员：药店扣减赠送+余额+赠送余额
     *
     * @param mobiles
     * @param organName
     * @param bonus
     * @param currentAmount
     * @param currentBonus
     * @return
     */
    @RequestMapping("/sms/sendBatch/memberDeductionFeedbackBalanceFeedback")
    @ResponseBody
    public Object sendBatchSmsForMemberDeductionFeedbackBalanceFeedback(@RequestParam("mobiles") String mobiles,
                                                                        @RequestParam(value = "organName", required = false) String organName,
                                                                        @RequestParam(value = "bonus", required = false) String bonus,
                                                                        @RequestParam(value = "currentAmount", required = false) String currentAmount,
                                                                        @RequestParam(value = "currentBonus", required = false) String currentBonus) {
        try {
            List<String> phoneNumbers = splitPhoneNumbers(mobiles);
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.getMemberDeductionFeedbackBalanceFeedback(phoneNumbers,
                    organName, bonus, currentAmount, currentBonus);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“会员：药店扣减赠送+余额+赠送余额”失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 分割手机号
     *
     * @param mobiles
     * @return
     */
    private List<String> splitPhoneNumbers(String mobiles) {
        if(StringUtils.isEmpty(mobiles)) {
            return new ArrayList<>(0);
        }
        String[] phoneNumberArray = mobiles.split(",");
        return Arrays.asList(phoneNumberArray);
    }

    /**
     * 发送短信 <br/>
     * 远程问诊-发送验证码
     *
     * @param mobile 短信接收号码。单个数量手机号。
     * @param code   短信验证码
     * @return
     */
    @RequestMapping("/sms/send/remoteInquiryVerificationCode")
    @ResponseBody
    public Object sendSmsForRemoteInquiryVerificationCode(@RequestParam("mobile") String mobile,
                                                          @RequestParam(value = "code", required = false) String code) {
        try {
            MessageBodyBusinessDTO messageBodyBusinessDTO = sendSMSBusinessApi.getRemoteInquiryVerificationCode(mobile, code);
            boolean isSuccess = messageBodyBusinessDTO.isSuccess();
            String msg = isSuccess ? "发送成功" : messageBodyBusinessDTO.getSubMsg();
            return this.addResult(isSuccess, msg);
        } catch (Exception e) {
            LOGGER.error("发送“远程问诊验证码”短信失败", e);
            return this.addResult(false, "发送失败");
        }
    }

    /**
     * 供多多验证码
     *
     * @param mobile
     * @param verificationCode
     * @return
     */
    @RequestMapping("/getGddValidateCode")
    @ResponseBody
    public Object getGddValitionCode(@RequestParam("mobile") String mobile, @RequestParam("verificationCode") String verificationCode) {

        try {
            sendSMSBusinessApi.getGddValitionCode(verificationCode,mobile);
            return this.addResult(verificationCode);
        } catch (Exception e) {
            LOGGER.error("短信验证码发送失败", e);
            return this.addError("短信验证码发送失败");
        }
    }

    /**
     * 智慧脸验证码
     *
     * @param mobile
     * @param verificationCode
     * @return
     */
    @RequestMapping("/getZhlValitionCode")
    @ResponseBody
    public Object getZhlValitionCode(@RequestParam("mobile") String mobile, @RequestParam("verificationCode") String verificationCode) {

        try {
            sendSMSBusinessApi.getZhlValitionCode(verificationCode,mobile);
            return this.addResult(verificationCode);
        } catch (Exception e) {
            LOGGER.error("短信验证码发送失败", e);
            return this.addError("短信验证码发送失败");
        }
    }


    /**
     * 宜块钱注册验证码
     *
     * @param verificationCode
     * @param mobile
     * @return
     */
    @RequestMapping("/getRegisterValitionYkqCode")
    @ResponseBody
    public Object getRegisterValitionYkqCode(@RequestParam("verificationCode") String verificationCode, @RequestParam("mobile") String mobile
    )  {
        try {
            sendSMSBusinessApi.getRegisterValitionYkqCode(verificationCode,mobile);
            return this.addResult("发送成功");
        } catch (Exception e) {
            LOGGER.error("短信验证码发送失败", e);
            return this.addError("短信验证码发送失败");
        }
    }

    /**
     * 宜块钱平团订单提醒
     *
     * @param verificationCode
     * @param data
     * @param merchant
     * @param address
     * @param phone
     * @param money
     * @return
     */
    @RequestMapping("/getYkqCollageOrderRemind")
    @ResponseBody
    public Object getYkqCollageOrderRemind(@RequestParam("verificationCode") String verificationCode,
                                           @RequestParam("data") String data,
                                           @RequestParam("merchant") String merchant,
                                           @RequestParam("address") String address,
                                           @RequestParam("phone") String phone,
                                           @RequestParam("mobile") String mobile,
                                           @RequestParam("money") String money
    ) {
        try {
            sendSMSBusinessApi.getYkqCollageOrderRemind(verificationCode,data,merchant,address,phone,mobile,money);
            return this.addResult("发送成功");
        } catch (Exception e) {
            LOGGER.error("短信验证码发送失败", e);
            return this.addError("短信验证码发送失败");
        }
    }

    /**
     * 宜块钱关注提醒
     * @param skuName
     * @param mobile
     * @return
     */
	@RequestMapping("/getYkqFocusRemind")
	@ResponseBody
	public Object getYkqFocusRemind(@RequestParam("skuName")String skuName, @RequestParam("mobile")String mobile
	)  {
		try {
            sendSMSBusinessApi.getYkqFocusRemind(skuName,mobile);
            return this.addResult("发送成功");
		} catch (Exception e) {
			LOGGER.error("短信验证码发送失败", e);
			return this.addError("短信验证码发送失败");
		}
	}

    /**
     * 宜块钱审核通过提醒
     *
     * @param skuName
     * @param verificationCode
     * @param data
     * @param merchant
     * @param address
     * @param phone
     * @return
     */
    @RequestMapping("/getYkqAuditingPassRemind")
    @ResponseBody
    public Object getYkqAuditingPassRemind(@RequestParam("skuName") String skuName,
                                           @RequestParam("verificationCode") String verificationCode,
                                           @RequestParam("data") String data,
                                           @RequestParam("merchant") String merchant,
                                           @RequestParam("address") String address,
                                           @RequestParam("phone") String phone,
                                           @RequestParam("mobile") String mobile
    ) {
        try {
            sendSMSBusinessApi.getYkqAuditingPassRemind(skuName,verificationCode,data,merchant,address,phone,mobile);
            return this.addResult("发送成功");
        } catch (Exception e) {
            LOGGER.error("短信验证码发送失败", e);
            return this.addError("短信验证码发送失败");
        }
    }

    /**
     * 宜块钱审核不通过提醒
     *
     * @param skuName
     * @param mobile
     * @return
     */
    @RequestMapping("/getYkqAuditingNotPassRemind")
    @ResponseBody
    public Object getYkqAuditingNotPassRemind(@RequestParam("skuName") String skuName, @RequestParam("mobile") String mobile
    )  {
        try {
            sendSMSBusinessApi.getYkqAuditingNotPassRemind(skuName,mobile);
            return this.addResult("发送成功");
        } catch (Exception e) {
            LOGGER.error("短信验证码发送失败", e);
            return this.addError("短信验证码发送失败");
        }
    }

}
