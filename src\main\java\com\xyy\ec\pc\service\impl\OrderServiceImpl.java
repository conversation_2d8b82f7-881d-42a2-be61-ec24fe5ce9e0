package com.xyy.ec.pc.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.cat.util.CatUtil;
import com.xyy.ec.api.rpc.order.OrderRpcService;
import com.xyy.ec.base.framework.exception.XyyEcOrderBizNoneCheckRTException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.ShippingAddressBussinessApi;
import com.xyy.ec.merchant.bussiness.api.crm.InvoiceBussinessCrmApi;
import com.xyy.ec.merchant.bussiness.dto.InvoiceTypeBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.ShippingAddressBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.api.pay.PayApi;
import com.xyy.ec.order.business.api.*;
import com.xyy.ec.order.business.api.ecp.order.OrderSettleApi;
import com.xyy.ec.order.business.api.ecp.pay.PayChannelApi;
import com.xyy.ec.order.business.common.ResultDTO;
import com.xyy.ec.order.business.config.OrderEnum;
import com.xyy.ec.order.business.config.OrderSproutTypeEnum;
import com.xyy.ec.order.business.dto.*;
import com.xyy.ec.order.business.dto.ecp.order.OrderSettleCommunicationDto;
import com.xyy.ec.order.business.dto.ecp.pay.PayChannelRouteDto;
import com.xyy.ec.order.business.dto.ecp.pay.PayCommunicationDto;
import com.xyy.ec.order.business.dto.ecp.pay.PayResponseDto;
import com.xyy.ec.order.business.enums.promo.OrderPromoTypeEnum;
import com.xyy.ec.order.business.exception.ServiceException;
import com.xyy.ec.order.business.model.ActivityPackageModel;
import com.xyy.ec.order.business.model.ServiceResponse;
import com.xyy.ec.order.business.utils.BigDecimalUtils;
import com.xyy.ec.order.core.api.promo.OrderPromoDetailApi;
import com.xyy.ec.order.core.dto.Order;
import com.xyy.ec.order.core.dto.cart.ShoppingCartDto;
import com.xyy.ec.order.core.dto.cart.ShoppingCartGroupDto;
import com.xyy.ec.order.core.dto.cart.ShoppingCartInfo;
import com.xyy.ec.order.core.dto.cart.ShoppingCartItemDto;
import com.xyy.ec.order.core.dto.cart.VoucherDto;
import com.xyy.ec.order.core.dto.promo.OrderPromoDetail;
import com.xyy.ec.order.core.dto.shop.CompanySettleBusinessDto;
import com.xyy.ec.order.core.dto.shop.ShopSettleBusinessDto;
import com.xyy.ec.order.core.dto.shop.ShopSettleBusinessForPcDto;
import com.xyy.ec.order.core.enums.CouponComputeAccountEnum;
import com.xyy.ec.order.core.util.BeanHelper;
import com.xyy.ec.order.core.util.EntityConvert;
import com.xyy.ec.order.dto.order.ConfirmOrderDetailDto;
import com.xyy.ec.order.dto.order.ConfirmOrderDto;
import com.xyy.ec.order.dto.pay.CashierDto;
import com.xyy.ec.order.dto.pay.CashierQueryParamDto;
import com.xyy.ec.order.dto.pay.ShopInfoDto;
import com.xyy.ec.order.dto.settle.SettleDto;
import com.xyy.ec.order.dto.settle.SettleSkuDto;
import com.xyy.ec.order.dto.settle.SettleVO;
import com.xyy.ec.order.dto.settle.*;
import com.xyy.ec.order.dto.settle.SettleDto;
import com.xyy.ec.order.dto.settle.SettleSkuDto;
import com.xyy.ec.order.dto.settle.SettleVO;
import com.xyy.ec.order.enums.*;
import com.xyy.ec.order.vo.OrderVO;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.constants.CodeMapConstants;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.SxpTypeEnum;
import com.xyy.ec.pc.model.ShoppingCartVo;
import com.xyy.ec.pc.model.dto.SkuInfoForVoucher;
import com.xyy.ec.pc.model.order.OrderSettleVo;
import com.xyy.ec.pc.model.order.OrderSproutSettleVo;
import com.xyy.ec.pc.model.order.SettleSkuVo;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.rpc.MarketingForOrderApiRpc;
import com.xyy.ec.pc.rpc.OrderServerRpcService;
import com.xyy.ec.pc.service.CodeItemService;
import com.xyy.ec.pc.service.MerchantService;
import com.xyy.ec.pc.service.OrderService;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ec.pc.vo.order.ConfirmOrderVo;
import com.xyy.ec.pc.vo.order.ProductCredentialVo;
import com.xyy.ec.product.business.dto.ProductConditionDTO;
import com.xyy.ec.shop.server.business.enums.ShopPatternEnum;
import com.xyy.ec.system.business.dto.CodeitemBusinessDto;
import com.xyy.framework.redis.autoconfigure.core.RedisClient;
import com.xyy.xtools.vkit.reflect.BeanMapper;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: zhaoyun
 * @Date: 2018/8/27 21:29
 * @Description: 订单服务实现类
 */
@Component
public class OrderServiceImpl implements OrderService {

    private static final Logger logger = LoggerFactory.getLogger(OrderServiceImpl.class);
    @Resource
    private RedisClient redisClient;
    @Reference(version = "1.0.0", timeout = 60000)
    private OrderRefundBusinessApi orderRefundBusinessApi;

    @Reference(version = "1.0.0", timeout = 60000)
    private ShoppingCartBusinessApi shoppingCartBusinessApi;

    @Reference(version = "1.0.0", timeout = 60000)
    private OrderBusinessApi orderBusinessApi;

    @Reference(version = "1.0.0", timeout = 60000)
    private OrderExtendBusinessApi orderExtendBusinessApi;

    @Reference(version = "1.0.0", timeout = 60000)
    private OrderDetailBusinessApi orderDetailBusinessApi;

    @Reference(version = "1.0.0", timeout = 60000)
    private OrderRefundBankBusinessApi OrderRefundBankBusinessApi;

    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;

    @Reference(version = "1.0.0")
    private ShippingAddressBussinessApi shippingAddressBussinessApi;

    @Reference(version = "1.0.0")
    private InvoiceBussinessCrmApi invoiceBussinessCrmApi;

    @Autowired
    private CodeItemService codeItemService;

    @Reference(version = "1.0.0")
    private OrderSproutBusinessApi orderSproutBusinessApi;

    @Reference(version = "1.0.0")
    private OrderSettleApi orderSettleApi;
    @Autowired
    OrderRpcService orderRpcService;

    @Autowired
    private OrderServerRpcService orderServerRpcService;


    @Reference(version = "1.0.0")
    private PayChannelApi payChannelApi;

    @Reference(version = "1.0.0")
    private TvPaymentBusinessApi tvPaymentBusinessApi;
    @Reference(version = "1.0.0")
    private PayApi payApi;
    @Autowired
    private MerchantService merchantService;

    @Reference(version = "1.0.0")
    private OrderPromoDetailApi orderPromoDetailApi;

    @Resource
    private MarketingForOrderApiRpc marketingForOrderApiRpc;

    @Override
    public List<String> queryOutOrderPdfUrl(String  orderNo,String  branchCode,String  docType) {
        try {
            ApiRPCResult<List<String>> result = orderBusinessApi.queryWmsOutOrderPdfUrl(orderNo, branchCode, docType);
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            logger.error("queryOutOrderPdfUrl error {} branchCode:{} docType:{}",orderNo,branchCode,docType);
        }

        return Lists.newArrayList();
    }
    /**
     * 查询订单详情
     *
     * @param merchantId
     * @param id
     * @param scene
     * @return
     */
    @Override
    public OrderBusinessDto getOrderDatails(Long merchantId, Long id, int scene) throws Exception {
        OrderBusinessDto orderBusinessDto = orderBusinessApi.selectById(id);
        if (orderBusinessDto.getIsKa() == 1 && (orderBusinessDto.getIsThirdCompany() != 1 || orderBusinessDto.getIsFbp() == 1)) {
            return orderBusinessDto;
        }
        List<OrderDetailBusinessDto> nearEffectiveList = new ArrayList<>();
        if (orderBusinessDto != null && StringUtils.isNotEmpty(orderBusinessDto.getOrderNo())) {
            List<OrderDetailBusinessDto> orderDetailDtoList = orderDetailBusinessApi.getOrderDetailsAndRefundDetail(orderBusinessDto.getOrderNo());
            List<OrderDetailBusinessDto> resultList = Lists.newArrayList();
            Map<Long, ActivityPackageModel> packageModelMap = Maps.newHashMap();

            /*
                校验如果这个商品都没得退了,你也就别显示了
             */
            for (OrderDetailBusinessDto orderDetailBusinessDto : orderDetailDtoList) {
                if (orderDetailBusinessDto.getCanRefundAmount() > 0) {

                    if (handleCanRefundAmount(nearEffectiveList, orderDetailBusinessDto, orderBusinessDto)) {
                        continue;
                    }
                    /*
                        判断是否有需要退的套餐
                     */
                    if (orderDetailBusinessDto.getPackageId() == null) {
                        resultList.add(orderDetailBusinessDto);
                    } else {
                        int packageSpec = orderDetailBusinessDto.getProductAmount() / orderDetailBusinessDto.getPackageCount();
                        orderDetailBusinessDto.setPackageCount(orderDetailBusinessDto.getCanRefundAmount() / packageSpec);
                        Long key = orderDetailBusinessDto.getPackageId();
                        if (packageModelMap.get(key) == null) {
                            ActivityPackageModel activityPackageModel = new ActivityPackageModel();
                            List<OrderDetailBusinessDto> orderDetailBusinessDtoList = new ArrayList<>();
                            orderDetailBusinessDtoList.add(orderDetailBusinessDto);
                            // 存入集合
                            activityPackageModel.setOrderDetailList(orderDetailBusinessDtoList);
                            packageModelMap.put(key, activityPackageModel);
                        } else {
                            /*
                                将套餐取出,取出套餐内集合,存入元素,再放回map里
                             */
                            ActivityPackageModel activityPackageModel = packageModelMap.get(key);
                            List<OrderDetailBusinessDto> orderDetailBusinessDtoList = activityPackageModel.getOrderDetailList();
                            // 存入元素
                            orderDetailBusinessDtoList.add(orderDetailBusinessDto);
                            // 存入集合
                            activityPackageModel.setOrderDetailList(orderDetailBusinessDtoList);
                            // put入map
                            packageModelMap.put(key, activityPackageModel);
                        }
                    }
                    // 将可退款数量赋值给商品数量
                    orderDetailBusinessDto.setProductAmount(orderDetailBusinessDto.getCanRefundAmount());

                }


            }
            // 如果存在套餐数据,则将套餐数据进行存放
            if (packageModelMap.size() > 0) {
                List<ActivityPackageModel> activityPackageModelList = Lists.newArrayList();
                for (Long key : packageModelMap.keySet()) {
                    activityPackageModelList.add(packageModelMap.get(key));
                }
                orderBusinessDto.setActivityPackageList(activityPackageModelList);
            }

            // 判断列表商品数据还有没有
            if (CollectionUtils.isNotEmpty(resultList)) {
                List<Long> skuIds = resultList.stream().
                        map(OrderDetailBusinessDto::getSkuId)
                        .collect(Collectors.toList());
                /*
                    获获取真实商品数据
                 */
                ProductConditionDTO productConditionDTO = new ProductConditionDTO();
                productConditionDTO.setSkuIdList(skuIds);
                productConditionDTO.setMerchantId(merchantId);
                productConditionDTO.setBranchCode(orderBusinessDto.getBranchCode());
                productConditionDTO.setPageSize(2000);
                productConditionDTO.setPageNum(1);
            }
            orderBusinessDto.setDetailList(giftSort(orderBusinessDto.getOrderNo(), resultList));
            orderBusinessDto.setNearEffectiveList(nearEffectiveList);
            fillOrderAfterSaleTags(orderBusinessDto);

        }
        return orderBusinessDto;
    }

    public List<OrderDetailBusinessDto> giftSort(String orderNo,List<OrderDetailBusinessDto> orderDetails) {
        List<OrderPromoDetail> orderPromoDetails = orderPromoDetailApi.queryOrderPromoDetailListByOrderNo(orderNo);
        if (CollectionUtils.isEmpty(orderPromoDetails)) {
            return orderDetails;
        }
        //赠品重新排序
        Map<Long, OrderDetailBusinessDto> orderDetailMap = orderDetails.stream()
                .collect(Collectors.toMap(OrderDetailBusinessDto::getId, Function.identity(),(o1, o2)  -> o1));

        List<OrderDetailBusinessDto> resultOrderDetails = Lists.newArrayList();
        Map<Long,OrderPromoDetail> masterOrderDetailIdMap = orderPromoDetails.stream()
                .filter(o -> Objects.equals(OrderPromoTypeEnum.FULL_GIFT.getValue(), o.getPromoType()) && Objects.equals(1,o.getType()))
                .collect(Collectors.toMap(OrderPromoDetail::getOrderDetailId,Function.identity(),(o1,o2) ->o1));

        Map<Long,List<OrderPromoDetail>>  giftOrderPromoDetailMap = orderPromoDetails.stream()
                .filter(o -> Objects.equals(OrderPromoTypeEnum.GIFT.getValue(), o.getPromoType()) && Objects.equals(1,o.getType()))
                .collect(Collectors.groupingBy(OrderPromoDetail::getPromoId));

        Set<Long> promoIds = giftOrderPromoDetailMap.keySet();
        //获取活动对应的赠品类型
        Map<Long,Integer> promoIdToGiveSkuType = marketingForOrderApiRpc.getOrderActGiveSkuType(new ArrayList<>(promoIds));

        for (OrderDetailBusinessDto orderDetailBusinessDto : orderDetails){
            OrderPromoDetail orderPromoDetail = masterOrderDetailIdMap.get(orderDetailBusinessDto.getId());
            if (Objects.nonNull(orderPromoDetail)) {
                orderDetailBusinessDto.setExtraGiftId(orderPromoDetail.getPromoId());
                orderDetailBusinessDto.setExtraGiftVariety(1);//赠品活动主品
                orderDetailBusinessDto.setGiveSkuType(promoIdToGiveSkuType.get(orderPromoDetail.getPromoId()));//赠品类型
            }
        }

        List<OrderDetailBusinessDto> unGiftOrderDetails = orderDetails.stream()
                .filter(o -> !Objects.equals(1, o.getExtraGift()))
                .sorted(Comparator.comparing(OrderDetailBusinessDto::getExtraGiftId,Comparator.nullsFirst(Long::compareTo)))
                .collect(Collectors.toList());
        Map<Long, Integer> lastPositionByGiftId = new TreeMap<>();
        // 记录每个 getExtraGiftId() 的位置
        for (int i = 0; i < unGiftOrderDetails.size(); i++) {
            OrderDetailBusinessDto orderDetail = unGiftOrderDetails.get(i);
            resultOrderDetails.add(orderDetail);

            if (Objects.isNull(orderDetail.getExtraGiftId())) {
                continue;
            }

            lastPositionByGiftId.put(orderDetail.getExtraGiftId(), i);
        }

        Map<Long, OrderDetailBusinessDto> orderDetailBusinessDtoMap = resultOrderDetails.stream()
                .filter(c -> Objects.nonNull(c.getExtraGiftId()))
                .collect(Collectors.toMap(OrderDetailBusinessDto::getExtraGiftId, Function.identity(), (o1, o2) -> o1));

        int step=0;
        for (Map.Entry<Long, Integer> entry : lastPositionByGiftId.entrySet()) {
            Long extraGiftId = entry.getKey();
            Integer lastPosition = entry.getValue();
            List<OrderPromoDetail> giftOrderPromoDetail = giftOrderPromoDetailMap.get(extraGiftId);
            OrderDetailBusinessDto orderDetail = orderDetailBusinessDtoMap.get(extraGiftId);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(giftOrderPromoDetail)|| Objects.isNull(orderDetail)){
                continue;
            }
            List<OrderDetailBusinessDto> temps=new ArrayList<>();
            for (OrderPromoDetail orderPromoDetail : giftOrderPromoDetail){
                OrderDetailBusinessDto giftOrderDetail = orderDetailMap.get(orderPromoDetail.getOrderDetailId());
                if(giftOrderDetail == null){
                    continue;
                }
                giftOrderDetail.setExtraGiftId(extraGiftId);
                giftOrderDetail.setGiveSkuType(orderDetail.getGiveSkuType());//赠品类型
                if(Objects.equals(2,orderDetail.getGiveSkuType())) {
                    giftOrderDetail.setIsGiftSelected(false);
                }
                temps.add(giftOrderDetail);
            }
            resultOrderDetails.addAll(lastPosition+step+1,temps);
            step+=temps.size();
        }
        return resultOrderDetails;
    }

    private void fillOrderAfterSaleTags(OrderBusinessDto order) {
        List<OrderDetailBusinessDto> detailList = order.getDetailList();
        if (CollectionUtil.isEmpty(detailList)) {
            return;
        }

        Integer orderChannel = order.getOrderChannel();

        boolean isGroupPurchase = orderChannel != null && orderChannel == OrderChannelEnum.GROUP_PURCHASE.getId() ||
                CollectionUtil.isNotEmpty(detailList) && detailList.stream()
                        .map(OrderDetailBusinessDto::getType)
                        .filter(Objects::nonNull)
                        .anyMatch(type -> OrderEnum.OrderDetailConstant.TYPE_GROUP_PURCHASE.getId() == type);
        // 自营或FBP拼团订单
        boolean selfGroupOrder = ((order.getIsThirdCompany() != null && order.getIsThirdCompany() == 0)
                || (order.getIsFbp() != null && order.getIsFbp() == 1))
                && isGroupPurchase;

        if (selfGroupOrder) {
            for (OrderDetailBusinessDto orderDetailBusinessDto : detailList) {

                if (Objects.equals(orderDetailBusinessDto.getType(), OrderEnum.OrderDetailConstant.TYPE_GROUP_PURCHASE.getId())) {
                    List<AfterSaleTag> afterSaleTags = Collections.singletonList(new AfterSaleTag().setText("不支持7天无理由退货"));
                    orderDetailBusinessDto.setAfterSaleTags(afterSaleTags);
                }

            }
        }
    }

    /**
     *
     * @param nearEffectList
     * @param orderDetailBusinessDto
     * @param order
     * @return 表示该订单明细是否加入到近效期列表
     */
    boolean handleCanRefundAmount(List<OrderDetailBusinessDto> nearEffectList, OrderDetailBusinessDto orderDetailBusinessDto,OrderBusinessDto order) {
        if (order != null
                &&  (order.getStatus() == com.xyy.ec.order.core.enums.OrderEnum.OrderStatus.PENDING.getId()
                || order.getStatus() == com.xyy.ec.order.core.enums.OrderEnum.OrderStatus.OUTBOUND.getId())) {
                return false;
        }
        Integer nearEffectiveFlag = orderDetailBusinessDto.getNearEffectiveFlag();
        if (nearEffectiveFlag != null && (nearEffectiveFlag == 1 || nearEffectiveFlag == 2)) {
            nearEffectList.add(orderDetailBusinessDto);
            orderDetailBusinessDto.setCanRefundAmount(0);
            return true;
        }
        return false;
    }

    /**
     * 获取订单信息
     *
     * @param orderId
     * @return
     */
    @Override
    public OrderBusinessDto selectByPrimaryKey(Long orderId) {
        OrderBusinessDto orderBusinessDto = new OrderBusinessDto();
        try {
            orderBusinessDto = orderBusinessApi.selectById(orderId);
        } catch (Exception e) {
            logger.error("selectByPrimaryKey error", e);
        }
        return orderBusinessDto;
    }

    @Override
    public OrderExtendBusinessDto quereyExtendInfo(String orderNo) {
        return orderExtendBusinessApi.selectByOrderNo(orderNo);
    }

    /**
     * 获取退款明细
     *
     * @param orderId
     * @param customOrderDetailBusniessDtoList
     * @param refundType
     * @return
     */
    @Override
    public List<CustomOrderDetailBusniessDto> canRefundCustomOrderDeatils(Long orderId, List<CustomOrderDetailBusniessDto> customOrderDetailBusniessDtoList, int refundType) {
        List<CustomOrderDetailBusniessDto> orderDetailBusniessDtoList = Lists.newArrayList();
        try {
            orderDetailBusniessDtoList = orderRefundBusinessApi.canRefundCustomOrderDeatils(orderId, customOrderDetailBusniessDtoList, refundType);
        } catch (Exception e) {
            logger.error("canRefundCustomOrderDeatils error", e);
        }
        return orderDetailBusniessDtoList;
    }

    /**
     * 根據退款明細返回所查詢的退款商品統計金額
     *
     * @param customOrder
     * @return
     */
    @Override
    public Object getOrderRefundCensus(CustomOrderBusniessDto customOrder) {
        Object o;
        try {
            o = orderRefundBusinessApi.getOrderRefundCensus(customOrder);
        } catch (Exception e) {
            logger.error("getOrderRefundCensus", e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", "1001");
            jsonObject.put("status", "failure");
            jsonObject.put("errorMsg", "系统出现异常，请稍后重试");
            o = jsonObject;
        }
        return o;
    }


    @Override
    public Object getOrderFreightRefundCensus(String orderNo) {
        Object o;
        try {
            o = orderRefundBusinessApi.getOrderFreightRefundCensus(orderNo);
        } catch (Exception e) {
            logger.error("getOrderFreightRefundCensus", e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", "1001");
            jsonObject.put("status", "failure");
            jsonObject.put("errorMsg", "系统出现异常，请稍后重试");
            o = jsonObject;
        }
        return o;
    }

    /**
     * 申请退款
     *
     * @param customOrder
     * @param customOrderDetails
     */
    @Override
    public void submitApplyRefund(CustomOrderBusniessDto customOrder, List<CustomOrderDetailBusniessDto> customOrderDetails) {
        try {
            orderRefundBusinessApi.submitApplyRefund(customOrder, customOrderDetails);
        } catch (Exception e) {
            logger.error("submitApplyRefund error", e);
        }
    }


    @Override
    public void applyFreightRefund(CustomOrderBusniessDto customOrder) {
        try {
            orderRefundBusinessApi.applyFreightRefund(customOrder);
        } catch (Exception e) {
            logger.error("applyFreightRefund error", e);
        }
    }

    /**
     * 查看退款明细详情
     *
     * @param orderRefundDetailBusinessDto
     * @param page
     * @param isShow
     * @return
     */
    @Override
    public Map<String, Object> selectDetailByRefundId(OrderRefundDetailBusinessDto orderRefundDetailBusinessDto, Page page, boolean isShow) {
        Map<String, Object> map = Maps.newHashMap();
        try {
            map = orderRefundBusinessApi.selectDetailByRefundId(orderRefundDetailBusinessDto, null, isShow);
        } catch (Exception e) {
            logger.error("selectDetailByRefundId error", e);
        }
        return map;
    }

//    /**
//     * 退款列表
//     * @param orderRefundBusinessDto
//     * @param page
//     * @return
//     */
//    @Override
//    public List<OrderRefundBusinessDto> findRefundOrders(OrderRefundBusinessDto orderRefundBusinessDto, Page page) {
//        List<OrderRefundBusinessDto> orderRefundBusinessDtoList = Lists.newArrayList();
//        try{
//            PageInfo pageInfo = new PageInfo();
//            pageInfo.setPageNum(page.getCurrentPage());
//            pageInfo.setPageSize(page.getOffset());
//            com.github.pagehelper.Page<OrderRefundBusinessDto> businessDtoPage = orderRefundBusinessApi.findRefundOrders(orderRefundBusinessDto,pageInfo);
//            orderRefundBusinessDtoList = businessDtoPage.getResult();
//        }catch (Exception e){
//            logger.error("findRefundOrders error",e);
//        }
//        return orderRefundBusinessDtoList;
//    }

    /**
     * 去结算
     *
     * @param order
     * @param isCheck
     * @param branchCode
     */
    @Override
    public ShoppingCartInfo settleNew(Order order, boolean isCheck, String branchCode) throws ServiceException {
        return shoppingCartBusinessApi.settleNew(order, isCheck, branchCode);
    }

    /**
     * 动态查询订单信息(包含商户,支付类型,扩展表余额)
     *
     * @param order
     * @return
     */
    @Override
    public Order selectWebServiceOrder(Order order) {
        Order result = new Order();
        try {
            result = orderBusinessApi.selectWebServiceOrder(order);
        } catch (Exception e) {
            logger.error("selectWebServiceOrder error", e);
            return null;
        }
        return result;
    }

    @Override
    public List<OrderBusinessDto> selectWebServiceWithSubOrders(Long id) {
        try {
            return orderBusinessApi.selectByOrderIdWithSubOrderAndExtendInfoFromMaster(id);
        } catch (Exception e) {
            logger.error("selectWebServiceWithSubOrders error", e);
            return Collections.emptyList();
        }
    }


    /**
     * 确认下单
     *
     * @param order
     * @param branchCode
     * @return
     */
    @Override
    public Order confirmOrderNew(Order order, String branchCode) {
        return orderBusinessApi.confirmOrder(order, branchCode);
    }

    /**
     * 获取在线支付的超时时间
     *
     * @param branchCode
     * @return
     */
    @Override
    public Integer getOnlinePayOrderFinalPaymentHour(String branchCode) {
        Integer payFinal = null;
        try {
            payFinal = orderBusinessApi.getOnlinePayOrderFinalPaymentHour(branchCode);
        } catch (Exception e) {
            logger.error("getOnlinePayOrderFinalPaymentHour error", e);
        }
        return payFinal;
    }

    /**
     * 设置业务异常消息到缓存
     *
     * @param merchantId
     * @param errorMsg
     */
    @Override
    public void setErrorMsg(Long merchantId, String errorMsg) {
        try {
            shoppingCartBusinessApi.setErrorMsg(merchantId, errorMsg);
        } catch (Exception e) {
            logger.error("setErrorMsg error");
        }
    }


    /**
     * 通过orderNo查询订单
     *
     * @param orderNo
     * @return
     */
    @Override
    public Order getOrderByOrderNo(String orderNo) {
        Order order = new Order();
        try {
            OrderBusinessDto orderBusinessDto = orderBusinessApi.selectByOrderNo(orderNo);
            orderBusinessDto.setPayEndTime(DateUtil.date2String(orderBusinessDto.getCreateTime(), "yyyyMMddHHmmss"));
            if (orderBusinessDto != null) order = BeanHelper.copyTo(orderBusinessDto, Order.class);
        } catch (Exception e) {
            logger.error("getOrderByOrderNo error", e);
        }
        return order;
    }

    /**
     * 根据会员id，获取最近一条提交收汇款银行记录
     *
     * @return
     */
    @Override
    public OrderRefundBankBusinessDto selectLastOrderRefundBankByMerchantId(Long merchantId) {
        return OrderRefundBankBusinessApi.selectLastOrderRefundBankByMerchantId(merchantId);
    }

    @Override
    public void setSttleMes(ModelMap modelMap, ShoppingCartInfo shoppingCartInfo, String branchCode, Long merchantId) throws Exception {
        // 设置起送价
        setStartingPriceString(modelMap, branchCode);
        // 设置收货地址
        setShippingAddressList(modelMap, merchantId);

        shoppingCartInfo.setIsShowOrderSprout(1);
        modelMap.put("hasThirdCompany", false);
        modelMap.put("shoppingCartInfo", shoppingCartInfo);
        modelMap.put("payTips", "");
        modelMap.put("voucherList", shoppingCartInfo.getVoucherList());

        int orderQty = 0;
        if (shoppingCartInfo.isNewUser()) {
            orderQty = 1;
        }
        modelMap.put("orderQty", orderQty);
        if (shoppingCartInfo.getTotalNum() < 1) {
            // 得到还差多少钱
            throw new IllegalArgumentException("采购的商品数量不能为0，请选择要采购的商品！");
        }
        // 设置返利文案
        setReturnBalanceTips(modelMap, branchCode, shoppingCartInfo.getRebateBalanceAmt());
        // 设置优惠券信息
        setVoucherInfo(modelMap, branchCode);
        // 设置发票随行
        setPtdzInvoincePeer(modelMap, merchantId);
        // 设置发票类型
        setBillType(modelMap, merchantId);
        // 设置购物车镜像ID
        setShoppingCartImgUUID(modelMap, merchantId);
    }

    private void setVoucherInfo(ModelMap modelMap, String branchCode) {
        Map<String, CodeitemBusinessDto> codemap = codeItemServiceRpc.selectByCodemapRTMap("VOUCHER", branchCode);
        modelMap.put("voucherTitle", codemap.get("VOUCHER_TITLE").getName());
        modelMap.put("voucherDemo", codemap.get("VOUCHER_DEMO").getName());
        modelMap.put("voucherText", codemap.get("VOUCHER_DIALOG_TEXT").getName());
    }

    private void setShoppingCartImgUUID(ModelMap modelMap, Long merchantId) {
        ServiceResponse<String> shoppingCartImg = shoppingCartBusinessApi.createShoppingCartImg(merchantId);
        modelMap.put("shoppingCartImgUUID", shoppingCartImg.getResult());
    }

    private void setBillType(ModelMap modelMap, Long merchantId) {
        InvoiceTypeBussinessDto invoiceTypeBussinessDto = invoiceBussinessCrmApi.queryInvoiceTypeById(merchantId);
        Integer invoiceType = 1;
        String invoiceText = "电子普通发票";
        if (invoiceTypeBussinessDto != null) {
            invoiceType = invoiceTypeBussinessDto.getId();
            invoiceText = invoiceTypeBussinessDto.getName();
        }
        modelMap.put("invoiceType", invoiceType);
        modelMap.put("invoiceText", invoiceText);
    }

    private void setPtdzInvoincePeer(ModelMap modelMap, Long merchantId) {
        modelMap.put("ptdzInvoincePeer", orderRpcService.ptdzInvoincePeer(merchantId));
    }

    private void setReturnBalanceTips(ModelMap modelMap, String branchCode, BigDecimal rebateBalanceAmt) {
        String returnBalanceTips = ""; //返余额文案
        if (isActivity(branchCode)) {
            Map<String, CodeitemBusinessDto> activitConfigMap = codeItemServiceRpc.selectByCodemapRTMap(CodeMapConstants.GLOBAL_ACTIVITY_CONF, branchCode);
            returnBalanceTips = activitConfigMap.get("returnBalanceTips").getName();
        }
        if (rebateBalanceAmt != null && rebateBalanceAmt.compareTo(BigDecimal.ZERO) == 1) {
            modelMap.put("returnBalanceTips", String.format(returnBalanceTips, "", rebateBalanceAmt));
        }
    }

    private void setShippingAddressList(ModelMap modelMap, Long merchantId) throws Exception {
        ShippingAddressBussinessDto shippingAddressBussinessDtoExample = new ShippingAddressBussinessDto();
        List<ShippingAddressBussinessDto> shippingAddressList = null;
        shippingAddressBussinessDtoExample.setMerchantId(merchantId);
        shippingAddressList = shippingAddressBussinessApi.selectList(shippingAddressBussinessDtoExample);
        for (ShippingAddressBussinessDto shippingAddress : shippingAddressList) {
            if (shippingAddress.getIsdefault()) {
                if (shippingAddress.getAuditState() == 0) {
                    ShippingAddressBussinessDto updateAddr = new ShippingAddressBussinessDto();
                    updateAddr.setId(shippingAddress.getId());
                    // 弹框完成后修改地址表弹窗状态
                    updateAddr.setAuditState(1);
                    updateAddr.setMerchantId(merchantId);
                    shippingAddressBussinessApi.updateShippingAddress(updateAddr);
                }
                modelMap.put("shippingAddress", shippingAddress);
            }
        }

        modelMap.put("shippingAddressList", shippingAddressList);
    }

    private void setStartingPriceString(ModelMap modelMap, String branchCode) {
        //起送价
        String startingPriceString = "200";
        CodeitemBusinessDto codeItem = codeItemServiceRpc.getCodeitemBusinessDto(CodeMapConstants.SETTLE_CONF, "START_PRICE", branchCode);
        if (codeItem != null) {
            startingPriceString = codeItem.getName();
        }
        modelMap.put("startingPriceText", startingPriceString);
    }

    public boolean isActivity(String branchCode) {
        Date startTime = null;
        Date endTime = null;
        try {
            Map<String, CodeitemBusinessDto> activityMap = codeItemServiceRpc.selectByCodemapRTMap(CodeMapConstants.GLOBAL_ACTIVITY_CONF, branchCode);
            CodeitemBusinessDto startTimeItem = activityMap.get("START_TIME");
            CodeitemBusinessDto endTimeItem = activityMap.get("END_TIME");
            if (startTimeItem == null || endTimeItem == null) {
                return false;
            }
            startTime = com.xyy.ec.order.core.util.DateUtil.string2Date(startTimeItem.getName(), Constants.DateFormats.PATTERN_STANDARD);
            endTime = com.xyy.ec.order.core.util.DateUtil.string2Date(endTimeItem.getName(), Constants.DateFormats.PATTERN_STANDARD);
        } catch (Exception e) {
            throw new XyyEcOrderBizNoneCheckRTException("数据异常");
        }
        if (startTime == null || endTime == null) {
            return false;
        }
        Date nowDate = new Date();
        if (nowDate.compareTo(startTime) < 0 || nowDate.compareTo(endTime) > 0) {
            return false;
        }
        return true;
    }

    @Override
    public Map<String, Object> setCalcSettleAmount(ShoppingCartInfo shoppingCartInfo, String branchCode) {
        //可能可以删除
        Map<String, String> orgGroup = new HashMap<>(2);
        String voucherMoney = "0";
        if (shoppingCartInfo != null) {
            if (shoppingCartInfo.getVoucher() != null) {
                voucherMoney = shoppingCartInfo.getVoucher().getMoneyInVoucher() + "";
            }
            for (ShoppingCartGroupDto cartGroup : shoppingCartInfo.getGroup()) {
                if (cartGroup.getIsThirdCompanyLastFlag() == 1 && cartGroup.getIsThirdCompany() == 0) {
                    orgGroup.put("productVarietyNum", cartGroup.getProductVarietyNum().toString());
                    orgGroup.put("productTotalNum", cartGroup.getProductTotalNum().toString());
                    orgGroup.put("directTotalAmount", cartGroup.getTotalAmount().toString());
                    break;
                }
            }
        }
        //可能可以删除
        String returnBalanceTips = ""; //返余额文案
        if (isActivity(branchCode)) {
            Map<String, CodeitemBusinessDto> activitConfigMap = codeItemService.selectByCodemapRTMap(CodeMapConstants.GLOBAL_ACTIVITY_CONF, branchCode);
            returnBalanceTips = activitConfigMap.get("returnBalanceTips").getName();
            BigDecimal rebateBalanceAmt = shoppingCartInfo.getRebateBalanceAmt();
            if (rebateBalanceAmt != null && rebateBalanceAmt.compareTo(BigDecimal.ZERO) == 1) {
                returnBalanceTips = String.format(returnBalanceTips, "", rebateBalanceAmt);
            } else {
                returnBalanceTips = "";
            }
        }

        Map<String, Object> resultMap = BaseController.addResult(new String[]{"discount", "rebate", "payablePrice", "balanceAmount", "hasBalanceAmount"
                        , "returnBalanceTips", "noRebateDiscountAmount", "noRebateVoucherAmount", "rebateDiscountAmount"
                        , "rebateVoucherAmount", "wholeDiscountAmount", "wholeVoucherAmount", "voucherDiscountAmount", "promoDiscountAmount", "price", "fixedPriceAmount"}
                , new String[]{voucherMoney, shoppingCartInfo.getRebate() + "", shoppingCartInfo.getPayablePrice() + ""
                        , shoppingCartInfo.getBalanceAmount() + "", shoppingCartInfo.getAvailBalanceAmt() + ""
                        , returnBalanceTips, shoppingCartInfo.getNoRebateDiscountAmount() + ""
                        , shoppingCartInfo.getNoRebateVoucherAmount() + "", shoppingCartInfo.getRebateDiscountAmount() + ""
                        , shoppingCartInfo.getRebateVoucherAmount() + "", shoppingCartInfo.getWholeDiscountAmount() + ""
                        , shoppingCartInfo.getWholeVoucherAmount() + ""
                        , shoppingCartInfo.getVoucherDiscountAmount() + "", shoppingCartInfo.getPromoDiscountAmount() + "",
                        shoppingCartInfo.getPrice().toString(), shoppingCartInfo.getFixedPriceAmount() + ""});

        List<ShoppingCartDto> allCartItemList = shoppingCartInfo.getAllCartItemList();
        List<Map<String, Object>> detailList = new ArrayList<>();
        for (ShoppingCartDto cart : allCartItemList) {
            Map<String, Object> detail = new HashMap<>();
            setCartAmoutInfo(detail, cart);
            detailList.add(detail);
        }
        resultMap.put("directProduct", orgGroup);
        resultMap.put("detailList", detailList);

        return resultMap;
    }

    @Override
    public Map<String, Object> setCalcSettleAmount(OrderSettleCommunicationDto orderSettleCommunicationDto, String branchCode) {
        Map<String, Object> resultMap = BaseController.addResult();
        // 遍历商品数据，放入集合中
        List<Map<String, Object>> detailList = new ArrayList<>();
        List<Map<String, Object>> shopSubtotalList = new ArrayList<>();
        List<Map<String, Object>> companySubtotalList = new ArrayList<>();
        for (CompanySettleBusinessDto company : orderSettleCommunicationDto.getCompanys()) {
            for (ShopSettleBusinessDto shop : company.getShops()) {
                for (ShoppingCartGroupDto group : shop.getGroups()) {
                    for (ShoppingCartItemDto cartItem : group.getSorted()) {

                        if (cartItem.getItemType() == 3) {
                            // 套餐
                            List<ShoppingCartDto> subItemList = cartItem.getSubItemList();
                            for (ShoppingCartDto cart : subItemList) {
                                Map<String, Object> detail = new HashMap<>();
                                setCartAmoutInfo(detail, cart);
                                detailList.add(detail);
                            }
                        } else {
                            // 单品
                            ShoppingCartDto cart = cartItem.getItem();
                            Map<String, Object> detail = new HashMap<>();
                            setCartAmoutInfo(detail, cart);
                            detailList.add(detail);
                        }
                    }
                }
                setShopSubtotalInfo(shop, shopSubtotalList);
            }
            setCompanySubtotalInfo(company, companySubtotalList);
        }

        // 设置商品金额信息
        resultMap.put("detailList", detailList);
        // 设置店铺合计信息
        resultMap.put("shopSubtotalList", shopSubtotalList);
        // 设置公司合计信息
        resultMap.put("companySubtotalList", companySubtotalList);
        // 设置总合计
        resultMap.put("myBalanceAmount1", orderSettleCommunicationDto.getBalanceAmount());
        resultMap.put("myBalanceAmount2", orderSettleCommunicationDto.getBalanceAmount());
        resultMap.put("myTotalBalanceAmt", orderSettleCommunicationDto.getTotalBalanceAmt());
        resultMap.put("myAvailBalanceAmt", orderSettleCommunicationDto.getAvailBalanceAmt());
        resultMap.put("finalTotalAmount", orderSettleCommunicationDto.getTotalAmount());
        resultMap.put("finalPromoTotalAmt", orderSettleCommunicationDto.getPromoTotalAmt());
        resultMap.put("finalVoucherTotalAmt", orderSettleCommunicationDto.getVoucherTotalAmt());
        resultMap.put("finalFreightTotalAmt", orderSettleCommunicationDto.getFreightTotalAmt());
        resultMap.put("finalBalanceAmount", orderSettleCommunicationDto.getBalanceAmount());
        resultMap.put("finalFixedPriceAmount", orderSettleCommunicationDto.getFixedPriceAmount());
        resultMap.put("finalPayAmount", orderSettleCommunicationDto.getPayAmount());
        return resultMap;
    }


    /**
     * @param settleVO
     * @param branchCode
     * @description: 4. 金额重新计算数据填充
     * @author: wcwanga
     * @create: 2020-02-29 13:01
     * @param: [orderSettleCommunicationDto, branchCode]
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     */
    @Override
    public Map<String, Object> setCalcSettleAmount(SettleVO settleVO, String branchCode) {
        Map<String, Object> resultMap = BaseController.addResult();
        // 遍历商品数据，放入集合中
        List<Map<String, Object>> detailList = new ArrayList<>();
        List<Map<String, Object>> shopSubtotalList = new ArrayList<>();
        List<Map<String, Object>> companySubtotalList = new ArrayList<>();
        for (com.xyy.ec.order.dto.settle.CompanySettleBusinessDto company : settleVO.getCompanys()) {
            for (com.xyy.ec.order.dto.settle.ShopSettleBusinessDto shop : company.getShops()) {
                for (com.xyy.ec.order.dto.settle.ShoppingCartGroupDto group : shop.getGroups()) {
                    for (com.xyy.ec.order.dto.settle.ShoppingCartItemDto cartItem : group.getSorted()) {

                        if (cartItem.getItemType() == 3) {
                            // 套餐
                            List<com.xyy.ec.order.dto.settle.ShoppingCartDto> subItemList = cartItem.getSubItemList();
                            for (com.xyy.ec.order.dto.settle.ShoppingCartDto cart : subItemList) {
                                Map<String, Object> detail = new HashMap<>();
                                setCartAmoutInfo(detail, cart);
                                detailList.add(detail);
                            }
                        } else {
                            // 单品
                            com.xyy.ec.order.dto.settle.ShoppingCartDto cart = cartItem.getItem();
                            Map<String, Object> detail = new HashMap<>();
                            setCartAmoutInfo(detail, cart);
                            detailList.add(detail);
                        }
                    }
                }
                setShopSubtotalInfo(shop, shopSubtotalList);
            }
            setCompanySubtotalInfo(company, companySubtotalList);
        }

        // 设置商品金额信息
        resultMap.put("detailList", detailList);
        // 设置店铺合计信息
        resultMap.put("shopSubtotalList", shopSubtotalList);
        // 设置公司合计信息
        resultMap.put("companySubtotalList", companySubtotalList);
        // 设置总合计
        resultMap.put("myBalanceAmount1", settleVO.getBalanceAmount());
        resultMap.put("myBalanceAmount2", settleVO.getBalanceAmount());
        resultMap.put("myTotalBalanceAmt", settleVO.getTotalBalanceAmt());
        resultMap.put("myAvailBalanceAmt", settleVO.getAvailBalanceAmt());
        resultMap.put("finalTotalAmount", settleVO.getTotalAmount());
        resultMap.put("finalPromoTotalAmt", settleVO.getPromoTotalAmt());
        resultMap.put("finalVoucherTotalAmt", settleVO.getVoucherTotalAmt());
        resultMap.put("finalFreightTotalAmt", settleVO.getFreightTotalAmt());
        resultMap.put("finalBalanceAmount", settleVO.getBalanceAmount());
        resultMap.put("finalFixedPriceAmount", settleVO.getFixedPriceAmount());
        resultMap.put("finalPayAmount", settleVO.getPayAmount());
        resultMap.put("money", settleVO.getMoney());
        resultMap.put("virtualGoldBalance", settleVO.getVirtualGold());
        resultMap.put("availVirtualGold", settleVO.getAvailVirtualGold());
        resultMap.put("useVirtualGold", settleVO.getUseVirtualGold());
        resultMap.put("useRedPacket", settleVO.getUseRedPacket());
        resultMap.put("redPacketAmount", settleVO.getRedPacketAmount());
        logger.info("9528-sss-res:{}",JSON.toJSONString(resultMap));
        return resultMap;
    }

    private void setCompanyFreightInfo(CompanySettleBusinessDto company, Map<String, Object> companySubtotalInfo) {
        if (company.getIsThirdCompany() == 0) {
            companySubtotalInfo.put("freightTipsShowStatus", company.getFreightTipsShowStatus());
            companySubtotalInfo.put("freightIconShowStatus", company.getFreightIconShowStatus());
            companySubtotalInfo.put("freeDeliveryAmount", company.getFreeDeliveryAmount());
            companySubtotalInfo.put("freeFreightDiffAmount", company.getFreeFreightDiffAmount());
            companySubtotalInfo.put("freightUrlText", company.getFreightUrlText());
        }
    }

    private void setCompanyFreightInfo(com.xyy.ec.order.dto.settle.CompanySettleBusinessDto company, Map<String, Object> companySubtotalInfo) {
        if (company.getIsThirdCompany() == 0) {
            companySubtotalInfo.put("freightTipsShowStatus", company.getFreightTipsShowStatus());
            companySubtotalInfo.put("freightIconShowStatus", company.getFreightIconShowStatus());
            companySubtotalInfo.put("freeDeliveryAmount", company.getFreeDeliveryAmount());
            companySubtotalInfo.put("freeFreightDiffAmount", company.getFreeFreightDiffAmount());
            companySubtotalInfo.put("freightUrlText", company.getFreightUrlText());
        }
    }

    private void setCompanySubtotalInfo(com.xyy.ec.order.dto.settle.CompanySettleBusinessDto company, List<Map<String, Object>> companySubtotal) {
        Map<String, Object> companySubtotalInfo = new HashMap<>();
        companySubtotalInfo.put("uniqueKey", company.getCompanyCode());
        companySubtotalInfo.put("productVarietyNum", company.getProductVarietyNum());
        companySubtotalInfo.put("productTotalNum", company.getProductTotalNum());
        companySubtotalInfo.put("totalAmount", company.getTotalAmount());
        companySubtotalInfo.put("freightTotalAmt", company.getFreightTotalAmt());
        companySubtotalInfo.put("promoTotalAmt", company.getPromoTotalAmt());
        companySubtotalInfo.put("voucherTotalAmt", company.getVoucherTotalAmt());
        companySubtotalInfo.put("payAmount", company.getPayAmount());
        companySubtotalInfo.put("shops", company.getShops());

        setCompanyFreightInfo(company, companySubtotalInfo);
        companySubtotal.add(companySubtotalInfo);
    }

    private void setCompanySubtotalInfo(CompanySettleBusinessDto company, List<Map<String, Object>> companySubtotal) {
        Map<String, Object> companySubtotalInfo = new HashMap<>();
        companySubtotalInfo.put("uniqueKey", company.getCompanyCode());
        companySubtotalInfo.put("productVarietyNum", company.getProductVarietyNum());
        companySubtotalInfo.put("productTotalNum", company.getProductTotalNum());
        companySubtotalInfo.put("totalAmount", company.getTotalAmount());
        companySubtotalInfo.put("freightTotalAmt", company.getFreightTotalAmt());
        companySubtotalInfo.put("promoTotalAmt", company.getPromoTotalAmt());
        companySubtotalInfo.put("voucherTotalAmt", company.getVoucherTotalAmt());
        companySubtotalInfo.put("payAmount", company.getPayAmount());
        setCompanyFreightInfo(company, companySubtotalInfo);
        companySubtotal.add(companySubtotalInfo);
    }

    private void setShopSubtotalInfo(ShopSettleBusinessDto shop, List<Map<String, Object>> shopSubtotal) {
        Map<String, Object> shopSubtotalInfo = new HashMap<>();
        shopSubtotalInfo.put("uniqueKey", shop.getShopCode());
        shopSubtotalInfo.put("promoTotalAmt", shop.getPromoTotalAmt());
        shopSubtotalInfo.put("voucherTotalAmt", shop.getVoucherTotalAmt());
        shopSubtotalInfo.put("payAmount", shop.getPayAmount());
        shopSubtotalInfo.put("voucherTip", shop.getVoucherTip());
        shopSubtotal.add(shopSubtotalInfo);
    }

    private void setShopSubtotalInfo(com.xyy.ec.order.dto.settle.ShopSettleBusinessDto shop, List<Map<String, Object>> shopSubtotal) {
        Map<String, Object> shopSubtotalInfo = new HashMap<>();
        shopSubtotalInfo.put("uniqueKey", shop.getShopCode());
        shopSubtotalInfo.put("promoTotalAmt", shop.getPromoTotalAmt());
        shopSubtotalInfo.put("voucherTotalAmt", shop.getVoucherTotalAmt());
        shopSubtotalInfo.put("payAmount", shop.getPayAmount());
        shopSubtotalInfo.put("voucherTip", shop.getVoucherTip());
        shopSubtotal.add(shopSubtotalInfo);
    }

    private void setCartAmoutInfo(Map<String, Object> detail, com.xyy.ec.order.dto.settle.ShoppingCartDto cart) {
        detail.put("uniqueKey", cart.getUniqueKey());
        detail.put("useBalanceAmount", cart.getUseBalanceAmount());
        detail.put("realPayAmount", cart.getRealPayAmount());
        detail.put("discountAmount", cart.getDiscountAmount());
        detail.put("balanceAmount", cart.getBalanceAmount());

        //优惠
        BigDecimal discount = BigDecimal.ZERO;
        if (null != cart.getDiscountAmount()) {
            discount = cart.getDiscountAmount().divide(BigDecimal.valueOf(cart.getAmount()), 2, BigDecimal.ROUND_HALF_UP);
        }
        //余额抵扣
        BigDecimal useBalance = BigDecimal.ZERO;
        if (null != cart.getUseBalanceAmount()) {
            useBalance = cart.getUseBalanceAmount().divide(BigDecimal.valueOf(cart.getAmount()), 2, BigDecimal.ROUND_HALF_UP);
            //useBalance = BigDecimal.divide(cart.getUseBalanceAmount().doubleValue(), cart.getAmount());
        }
        //实付价  实付价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）-余额抵扣；
        BigDecimal purchasePrice = BigDecimalUtils.subtract(cart.getPrice(), discount).subtract(useBalance).setScale(2, BigDecimal.ROUND_HALF_UP);
        //返利
        BigDecimal balance = BigDecimal.ZERO;
        if (null != cart.getBalanceAmount()) {
            balance = cart.getBalanceAmount().divide(BigDecimal.valueOf(cart.getAmount()), 2, BigDecimal.ROUND_HALF_UP);
        }
        //成本价  成本价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）-返利；
        BigDecimal costPrice = BigDecimalUtils.subtract(cart.getPrice(), discount).subtract(balance).setScale(2, BigDecimal.ROUND_HALF_UP);
        detail.put("purchasePrice", purchasePrice);
        detail.put("costPrice", costPrice);
        detail.put("tcList", cart.getTagList());
    }

    private void setCartAmoutInfo(Map<String, Object> detail, ShoppingCartDto cart) {
        detail.put("uniqueKey", cart.getUniqueKey());
        detail.put("useBalanceAmount", cart.getUseBalanceAmount());
        detail.put("realPayAmount", cart.getRealPayAmount());
        detail.put("discountAmount", cart.getDiscountAmount());
        detail.put("balanceAmount", cart.getBalanceAmount());

        //优惠
        BigDecimal discount = BigDecimal.ZERO;
        if (null != cart.getDiscountAmount()) {
            discount = cart.getDiscountAmount().divide(BigDecimal.valueOf(cart.getAmount()), 2, BigDecimal.ROUND_HALF_UP);
        }
        //余额抵扣
        BigDecimal useBalance = BigDecimal.ZERO;
        if (null != cart.getUseBalanceAmount()) {
            useBalance = cart.getUseBalanceAmount().divide(BigDecimal.valueOf(cart.getAmount()), 2, BigDecimal.ROUND_HALF_UP);
            //useBalance = BigDecimal.divide(cart.getUseBalanceAmount().doubleValue(), cart.getAmount());
        }
        //实付价  实付价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）-余额抵扣；
        BigDecimal purchasePrice = BigDecimalUtils.subtract(BigDecimal.valueOf(cart.getPrice()), discount).subtract(useBalance).setScale(2, BigDecimal.ROUND_HALF_UP);
        //返利
        BigDecimal balance = BigDecimal.ZERO;
        if (null != cart.getBalanceAmount()) {
            balance = cart.getBalanceAmount().divide(BigDecimal.valueOf(cart.getAmount()), 2, BigDecimal.ROUND_HALF_UP);
        }
        //成本价  成本价=药帮忙价（原单价）-优惠（满减 + 套餐 + 优惠券）-返利；
        BigDecimal costPrice = BigDecimalUtils.subtract(BigDecimal.valueOf(cart.getPrice()), discount).subtract(balance).setScale(2, BigDecimal.ROUND_HALF_UP);
        detail.put("purchasePrice", purchasePrice);
        detail.put("costPrice", costPrice);
        detail.put("tcList", cart.getTagList());
    }

    @Override
    public ModelAndView groupConfimOrder(ConfirmOrderVo order, MerchantBussinessDto merchant, ModelMap modelMap, HttpServletRequest request, String source) throws Exception {
        Transaction t2 = CatUtil.initTransaction("queryConfirmOrder", "queryConfirmOrder");

        String branchCode = merchant.getRegisterCode();
        order.setUseBalance(false);
        OrderBusinessDto confirmOrder = new OrderBusinessDto();
        List<OrderBusinessDto> orderList = null;
        if (order.getId() != null) {
            orderList = this.selectWebServiceWithSubOrders(order.getId());
            confirmOrder = orderList.stream().filter(o -> o.getId().equals(order.getId())).findFirst().get();
            if(confirmOrder==null){
                throw new RuntimeException("查询不到订单信息");
            }
        } else {
            order.setOrderSource(Constants.IS4);    //订单来源PC
            order.setMerchantId(merchant.getId());
            order.setAccountId(merchant.getAccountId());
            String realIP = IPUtils.getClientIP(request);
            order.setRealIP(realIP);
            logger.info("当前用户请求的IP地址为:" + realIP);
            if (BizSourceEnum.GROUP_PURCHASE.getKey().equals(source)) {
                order.setOrderChannel(OrderEnum.OrderChannel.GROUP_PURCHASE.getId());//订单渠道

                confirmOrder = confirmOrderSprout(order, branchCode,null);

                ConfirmOrderDto confirmOrderDto = new ConfirmOrderDto();
                BeanUtils.copyProperties(order, confirmOrderDto);
                confirmOrderDto.setPurchaseNo(order.getPurchaseNo());
                confirmOrderDto.setBizSource(BizSourceEnum.GROUP_PURCHASE.getKey());
                confirmOrderDto.setTerminalType(PlatformEnum.PC.getValue());
                BeanUtils.copyProperties(order, confirmOrderDto);
//                confirmOrderDto.setRemark(null); TODO????
                confirmOrderDto.setUseBalance(order.isUseBalance());
                confirmOrderDto.setUseBalanceAmount(order.getBalanceAmount());
                Map<String, List<Long>> shopVoucherIds = getVoucherIdMap(order.getVoucherIds());
                confirmOrderDto.setShopVoucherIds(shopVoucherIds);
                setRemark(confirmOrderDto, order.getRemark());

                List<ConfirmOrderDetailDto> bizProducts = Lists.newArrayList();
                ConfirmOrderDetailDto skuDto = new ConfirmOrderDetailDto();
                skuDto.setQuantity(order.getProductNum());
                skuDto.setSkuId(order.getSkuId());
                bizProducts.add(skuDto);
                confirmOrderDto.setOrderDetailDtoList(bizProducts);

                OrderVO result = orderServerRpcService.confirmOrder(confirmOrderDto);
                Order returnOrder = new Order();
                BeanUtils.copyProperties(result, returnOrder);
            }
            this.addDataToModelMap(branchCode, modelMap);
            Object retryIntervalObject = modelMap.get("retryInterval");
            //如果retryIntervalObject的值为空或者为0 代表不走缓存页，直接走支付页面
            //为了确保安全： 只有在生成订单的时候才有可能走等待页
            if (retryIntervalObject != null && StringUtil.noEquals(retryIntervalObject.toString(), "0")) {
                String orderNo = confirmOrder.getOrderNo();
                orderBusinessApi.setGenerateOrder(orderNo);
                //带orderNo参数是为了解决刷新问题
                return new ModelAndView(new RedirectView("/merchant/center/order/cacheGenerateOrder.htm?orderNo="
                        + orderNo + "&jumpType=" + confirmOrder.getCallType(),true,false));
            }
        }
        CatUtil.successCat(t2);
        order.setOrderSource(Constants.IS4);
        order.setAppVersion(1);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(confirmOrder.getCreateTime());
        Date payExpireTime = confirmOrder.getPayExpireTime();
        int onlinePayFinalHour = Long.valueOf(DateUtil.dateDiff(confirmOrder.getCreateTime(), payExpireTime, 2)).intValue();
        calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) + onlinePayFinalHour);

        payExpireTime = payExpireTime == null ? calendar.getTime() : payExpireTime;
        //这里已指定是在线业务，不用考虑订单过期时间按类型分别存储
        confirmOrder.setPayEndTime(new SimpleDateFormat("yyyyMMddHHmmss").format(payExpireTime));
        //发票相关
//               TODO 发票
        InvoiceTypeBussinessDto invoiceTypeBussinessDto = invoiceBussinessCrmApi
                .queryInvoiceTypeById(merchant.getId());
        String invoinceTxt = invoiceTypeBussinessDto == null ? "电子普通发票" : invoiceTypeBussinessDto.getName();
        confirmOrder.setInvoinceText(invoinceTxt);
        confirmOrder.setBillInfo(invoinceTxt);
        confirmOrder.setAccountId(merchant.getAccountId());
        modelMap.put("order", confirmOrder);
        modelMap.put("payFinalHour", DateFormatUtils.format(payExpireTime, "yyyy-MM-dd HH:mm:ss"));
        modelMap.put("branchCode", branchCode);
        modelMap.put("isList", "1");

        //订单金额超过5万提示
        if (confirmOrder.getMoney().compareTo(new BigDecimal(50000)) >= 0) {
            String tips = "当前订单金额较大，建议您使用微信/支付宝进行支付";
            modelMap.put("tips", tips);
        }

        /**
         * wcwanga add
         * 2019-05-28
         * 重复刷新，或者回调没有回来了，重新发起支付，做订单状态判定
         */
        if (confirmOrder != null && confirmOrder.getStatus() == OrderEnum.OrderStatus.PENDING.getId()) {
            return new ModelAndView("/order/resultok.ftl");
        }

        int payType = confirmOrder.getPayType();
        if (payType == OrderEnum.OrderPayType.ONLINEPAYMENT.getId()) {
            modelMap.put("cashier", getCashierForPc(confirmOrder, orderList));
            return new ModelAndView("/order/pay_order.ftl");
        } else if (payType == OrderEnum.OrderPayType.OFFLINETRANSFER.getId()) {
            Map<String, String> transferInfo = codeItemServiceRpc.findCodeMap("TRANSFER_INFO", branchCode);
            return new ModelAndView("/order/offline_pay_ok.ftl", transferInfo);
        } else {
            return new ModelAndView("/order/resultok.ftl");
        }
    }

    @Override
    public ModelAndView setConfimOrder(ConfirmOrderVo order, MerchantBussinessDto merchant, ModelMap modelMap, HttpServletRequest request, String source, String token) throws Exception {
        Transaction t2 = CatUtil.initTransaction("queryConfirmOrder", "queryConfirmOrder");

        if (StringUtils.isEmpty(source)) {
            source = OrderEnum.OrderChannel.B2B.getId()+"";
        }

        String branchCode = merchant.getRegisterCode();
        order.setUseBalance(false);
        OrderBusinessDto confirmOrder = new OrderBusinessDto();
        List<OrderBusinessDto> orderList = null;
        if (order.getId() != null) {
            orderList = this.selectWebServiceWithSubOrders(order.getId());
            confirmOrder = orderList.stream().filter(o -> o.getId().equals(order.getId())).findFirst().get();
            if(confirmOrder==null){
                throw new RuntimeException("查询不到订单信息");
            }
        } else {
            order.setOrderSource(Constants.IS4);    //订单来源PC
            order.setMerchantId(merchant.getId());
            order.setAccountId(merchant.getAccountId());
            String realIP = IPUtils.getClientIP(request);
            order.setRealIP(realIP);
            order.setAccountRole(merchant.getAccountRole());
            if (merchant.getAccountRole().equals(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId())) {
                order.setUseBalance(false);
                order.setUseRedPacket(false);
                order.setUseVirtualGold(false);
                order.setVoucherIds("");
            }
            if (OrderSproutTypeEnum.SPROUT_ORDER.getKey().equals(source)) {
                order.setOrderChannel(OrderEnum.OrderChannel.SPROUT.getId());//订单渠道
                confirmOrder = confirmOrderSprout(order, branchCode, token);
            } else if (OrderSproutTypeEnum.ORDER.getKey().equals(source) ||"5".equals(source)) {
                MerchantBussinessDto merchantBussinessDto = merchantService.getMerchant(merchant.getId());
                order.setOrderChannel(merchantBussinessDto.getIsKa() ?  BizSourceEnum.KA.getKey() : BizSourceEnum.B2B.getKey());//订单渠道
                confirmOrder = confirmOrder(order, branchCode,token);
            } else if (BizSourceEnum.GROUP_PURCHASE.getKey().toString().equals(source) || BizSourceEnum.PIGOU.getKey().toString().equals(source)) {
                order.setOrderChannel(Integer.valueOf(source));//订单渠道

                ConfirmOrderDto confirmOrderDto = new ConfirmOrderDto();
                BeanUtils.copyProperties(order, confirmOrderDto);
                confirmOrderDto.setPurchaseNo(order.getPurchaseNo());
                confirmOrderDto.setIsPayMerge(order.getIsPayMerge()) ;
                confirmOrderDto.setBizSource(Integer.valueOf(source));
                confirmOrderDto.setToken(token);
                confirmOrderDto.setTerminalType(PlatformEnum.PC.getValue());
                BeanUtils.copyProperties(order, confirmOrderDto);
                confirmOrderDto.setRemark(null);
                confirmOrderDto.setUseBalance(order.isUseBalance());
                confirmOrderDto.setUseBalanceAmount(order.getBalanceAmount());
                Map<String, List<Long>> shopVoucherIds = getVoucherIdMap(order.getVoucherIds());
                confirmOrderDto.setShopVoucherIds(shopVoucherIds);
                setRemark(confirmOrderDto, order.getRemark());

                logger.info("9528-ssm-order:{}",JSON.toJSONString(order));

                this.setSxpSkus(confirmOrderDto, order.getSuiXinPinSkus());
                logger.info("9528-ssm-confirmOrderDto:{}",JSON.toJSONString(confirmOrderDto));

                List<ConfirmOrderDetailDto> bizProducts = Lists.newArrayList();
                ConfirmOrderDetailDto skuDto = new ConfirmOrderDetailDto();
                skuDto.setQuantity(order.getProductNum());
                skuDto.setSkuId(order.getSkuId());
                bizProducts.add(skuDto);

                if(StringUtils.isNotEmpty(order.getBizProducts())) {
                    List<SettleSkuDto> list = JSON.parseArray(order.getBizProducts(), SettleSkuDto.class);
                    bizProducts.addAll( list.stream().map(x->{
                        ConfirmOrderDetailDto dto = new ConfirmOrderDetailDto();
                        dto.setSkuId(x.getSkuId());
                        dto.setQuantity(x.getQuantity());
                        dto.setSelectStatus(x.getSelectStatus());
                        return dto;
                    }).collect(Collectors.toList()))  ;
                }
                confirmOrderDto.setOrderDetailDtoList(bizProducts);

                String productCredential = order.getProductCredential();
                if (StringUtils.isNotEmpty(productCredential)) {
                    List<ProductCredentialVo> productCredentialVos = JSONObject.parseArray(productCredential, ProductCredentialVo.class);
                    Map<String, String> productCredentialMap = productCredentialVos.stream().collect(Collectors.toMap(ProductCredentialVo::getSkuId, ProductCredentialVo::getProductCredential, (x1, x2) -> x1));
                    confirmOrderDto.setProductCredentialMap(productCredentialMap);
                }
                String corpCredential = order.getCorpCredential();
                if (StringUtils.isNotEmpty(corpCredential)) {
                    List<ProductCredentialVo> credentialVos = JSONObject.parseArray(corpCredential, ProductCredentialVo.class);
                    Map<String, String> credentialMap = credentialVos.stream().collect(Collectors.toMap(ProductCredentialVo::getOrgId, ProductCredentialVo::getEnterpriseCredential, (x1, x2) -> x1));
                    confirmOrderDto.setCorpCredentialMap(credentialMap);
                }
                logger.info("9528-ssm-2-confirmOrderDto:{}",JSON.toJSONString(confirmOrderDto));
                this.setSxpSkus(confirmOrderDto, order.getSuiXinPinSkus());

                OrderVO result = orderServerRpcService.confirmOrder(confirmOrderDto);
                OrderBusinessDto returnOrder = new OrderBusinessDto();
                BeanUtils.copyProperties(result, returnOrder);
                confirmOrder = returnOrder;
            }
            this.addDataToModelMap(branchCode, modelMap);
            Object retryIntervalObject = modelMap.get("retryInterval");
            //如果retryIntervalObject的值为空或者为0 代表不走缓存页，直接走支付页面
            //为了确保安全： 只有在生成订单的时候才有可能走等待页
            if (retryIntervalObject != null && StringUtil.noEquals(retryIntervalObject.toString(), "0")) {
                String orderNo = confirmOrder.getOrderNo();
                orderBusinessApi.setGenerateOrder(orderNo);
                //带orderNo参数是为了解决刷新问题
                return new ModelAndView(new RedirectView("/merchant/center/order/cacheGenerateOrder.htm?orderNo="
                        + orderNo + "&jumpType=" + confirmOrder.getCallType() + "&token=" + token + "&tranNo=" + order.getTranNo() + "&useVirtualGold=" + order.getUseVirtualGold(), true, false));
            }
        }
        CatUtil.successCat(t2);
        order.setOrderSource(Constants.IS4);
        order.setAppVersion(1);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(confirmOrder.getCreateTime());
        Date payExpireTime = confirmOrder.getPayExpireTime();
        int onlinePayFinalHour = Long.valueOf(DateUtil.dateDiff(confirmOrder.getCreateTime(), payExpireTime, 2)).intValue();
        calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) + onlinePayFinalHour);

        payExpireTime = payExpireTime == null ? calendar.getTime() : payExpireTime;
        //这里已指定是在线业务，不用考虑订单过期时间按类型分别存储
        confirmOrder.setPayEndTime(new SimpleDateFormat("yyyyMMddHHmmss").format(payExpireTime));
        //发票相关
//               TODO 发票
        InvoiceTypeBussinessDto invoiceTypeBussinessDto = invoiceBussinessCrmApi
                .queryInvoiceTypeById(merchant.getId());
        String invoinceTxt = invoiceTypeBussinessDto == null ? "电子普通发票" : invoiceTypeBussinessDto.getName();
        confirmOrder.setInvoinceText(invoinceTxt);
        confirmOrder.setBillInfo(invoinceTxt);
        confirmOrder.setAccountId(merchant.getAccountId());
        modelMap.put("order", confirmOrder);
        modelMap.put("payFinalHour", DateFormatUtils.format(payExpireTime, "yyyy-MM-dd HH:mm:ss"));
        modelMap.put("branchCode", branchCode);
        modelMap.put("tranNo", order.getTranNo());
        modelMap.put("token", token);
        modelMap.put("isList", 1);
        modelMap.put("useVirtualGold", order.getUseVirtualGold());

        //订单金额超过5万提示
        if (confirmOrder.getMoney().compareTo(new BigDecimal(50000)) >= 0) {
            String tips = "当前订单金额较大，建议您使用微信/支付宝进行支付";
            modelMap.put("tips", tips);
        }
        /**
         * wcwanga add
         * 2019-05-28
         * 重复刷新，或者回调没有回来了，重新发起支付，做订单状态判定
         */
        if (confirmOrder != null && confirmOrder.getStatus() == OrderEnum.OrderStatus.PENDING.getId()) {
            return new ModelAndView("/order/resultok.ftl");
        }

        int payType = confirmOrder.getPayType();
        if (payType == OrderEnum.OrderPayType.ONLINEPAYMENT.getId()) {
            modelMap.put("cashier", getCashierForPc(confirmOrder, orderList));
            return new ModelAndView("/order/pay_order.ftl");
        } else if (payType == OrderEnum.OrderPayType.OFFLINETRANSFER.getId()) {
            Map<String, String> transferInfo = codeItemServiceRpc.findCodeMap("TRANSFER_INFO", branchCode);
            return new ModelAndView("/order/offline_pay_ok.ftl", transferInfo);
        } else {
            return new ModelAndView("/order/resultok.ftl");
        }
    }
    /**
     * 设置随心拼属性
     * @param confirmOrderDto
     * @param sxpSkus
     */
    private void setSxpSkus(ConfirmOrderDto confirmOrderDto, String sxpSkus) {
        if (StringUtil.isNotEmpty(sxpSkus)) {
            try {
                List<SettleSxpSkuDto> sxpSkuDtos = JsonUtil.wapperStringToList(sxpSkus, SettleSxpSkuDto.class);
                if(org.apache.commons.collections.CollectionUtils.isNotEmpty(sxpSkuDtos)) {
                    sxpSkuDtos.forEach(x->{
                        if(x.getType()== null) {
                            x.setType(SxpTypeEnum.NORMAL_SXP.getType());
                        }
                    });
                }
                confirmOrderDto.setSettleSxpSkuDtos(sxpSkuDtos);
            } catch (IOException e) {
                logger.error("setSxpSkus error:", e);
            }
        }
    }
    private OrderBusinessDto confirmOrderSprout(ConfirmOrderVo order, String branchCode,String token) throws Exception {
        ConfirmOrderDto confirmOrderDto = new ConfirmOrderDto();
        BeanUtils.copyProperties(order, confirmOrderDto);
        confirmOrderDto.setPurchaseNo(order.getPurchaseNo());
        confirmOrderDto.setBizSource(BizSourceEnum.SPROUT.getKey());
        confirmOrderDto.setTerminalType(PlatformEnum.PC.getValue());
        BeanUtils.copyProperties(order, confirmOrderDto);
        confirmOrderDto.setRemark(null);
        confirmOrderDto.setUseBalance(order.isUseBalance());
        confirmOrderDto.setToken(token);
        confirmOrderDto.setUseBalanceAmount(order.getBalanceAmount());
        Map<String, List<Long>> shopVoucherIds = getVoucherIdMap(order.getVoucherIds());
        confirmOrderDto.setShopVoucherIds(shopVoucherIds);
        setRemark(confirmOrderDto, order.getRemark());
        this.setSxpSkus(confirmOrderDto, order.getSuiXinPinSkus());
        OrderVO result = orderServerRpcService.confirmOrder(confirmOrderDto);
        OrderBusinessDto returnOrder = new OrderBusinessDto();
        BeanUtils.copyProperties(result, returnOrder);
        return returnOrder;
//        ApiRPCResult<Order> response = orderSproutBusinessApi.confirmOrder(OrderSettleUtil.getRequestDto(order, branchCode, CouponComputeAccountEnum.ORDER_SETTLEMENT_ALLOCATION));
//        if (response.isFail()) {
//            throw new Exception(response.getErrMsg());
//        }
//        return response.getData();
    }

    private OrderBusinessDto confirmOrder(ConfirmOrderVo order, String branchCode,String token) throws Exception {
        ConfirmOrderDto confirmOrderDto = new ConfirmOrderDto();
        BeanUtils.copyProperties(order, confirmOrderDto);
        confirmOrderDto.setPurchaseNo(order.getPurchaseNo());
        confirmOrderDto.setBizSource(BizSourceEnum.B2B.getKey());
        confirmOrderDto.setTerminalType(PlatformEnum.PC.getValue());
        BeanUtils.copyProperties(order, confirmOrderDto);
        confirmOrderDto.setRemark(null);
        confirmOrderDto.setToken(token);
        confirmOrderDto.setUseBalance(order.isUseBalance());
        confirmOrderDto.setUseBalanceAmount(order.getBalanceAmount());
        Map<String, List<Long>> shopVoucherIds = getVoucherIdMap(order.getVoucherIds());
        confirmOrderDto.setShopVoucherIds(shopVoucherIds);
        setRemark(confirmOrderDto, order.getRemark());
        confirmOrderDto.setBizScene(order.getBizScene());

        String productCredential = order.getProductCredential();
        if (StringUtils.isNotEmpty(productCredential)) {
            List<ProductCredentialVo> productCredentialVos = JSONObject.parseArray(productCredential, ProductCredentialVo.class);
            Map<String, String> productCredentialMap = productCredentialVos.stream().collect(Collectors.toMap(ProductCredentialVo::getSkuId, ProductCredentialVo::getProductCredential, (x1, x2) -> x1));
            confirmOrderDto.setProductCredentialMap(productCredentialMap);
        }
        String corpCredential = order.getCorpCredential();
        if (StringUtils.isNotEmpty(corpCredential)) {
            List<ProductCredentialVo> credentialVos = JSONObject.parseArray(corpCredential, ProductCredentialVo.class);
            Map<String, String> credentialMap = credentialVos.stream().collect(Collectors.toMap(ProductCredentialVo::getOrgId, ProductCredentialVo::getEnterpriseCredential, (x1, x2) -> x1));
            confirmOrderDto.setCorpCredentialMap(credentialMap);
        }

        List<ConfirmOrderDetailDto> bizProducts = Lists.newArrayList();
//        ConfirmOrderDetailDto skuDto = new ConfirmOrderDetailDto();
//        skuDto.setQuantity(order.getProductNum());
//        skuDto.setSkuId(order.getSkuId());
//        bizProducts.add(skuDto);
        if(StringUtils.isNotEmpty(order.getBizProducts())) {
            List<SettleSkuDto> list = JSON.parseArray(order.getBizProducts(), SettleSkuDto.class);
            bizProducts.addAll( list.stream().map(x->{
                ConfirmOrderDetailDto dto = new ConfirmOrderDetailDto();
                dto.setSkuId(x.getSkuId());
                dto.setQuantity(x.getQuantity());
                dto.setSelectStatus(x.getSelectStatus());
                return dto;
            }).collect(Collectors.toList()))  ;
        }
        logger.info("9528-jsj-order2:{}",JSON.toJSONString(order));
        logger.info("9528-jsj-bizProducts:{}",JSON.toJSONString(bizProducts));

        confirmOrderDto.setOrderDetailDtoList(bizProducts);

        if(StringUtils.isNotEmpty(order.getBizProducts())) {
            List<SettleSkuDto> list = JSON.parseArray(order.getBizProducts(), SettleSkuDto.class);
            bizProducts.addAll( list.stream().map(x->{
                ConfirmOrderDetailDto dto = new ConfirmOrderDetailDto();
                dto.setSkuId(x.getSkuId());
                dto.setQuantity(x.getQuantity());
                dto.setSelectStatus(x.getSelectStatus());
                return dto;
            }).collect(Collectors.toList()))  ;
        }

        confirmOrderDto.setOrderDetailDtoList(bizProducts);
        this.setSxpSkus(confirmOrderDto, order.getSuiXinPinSkus());
        OrderVO result = orderServerRpcService.confirmOrder(confirmOrderDto);
        OrderBusinessDto returnOrder = new OrderBusinessDto();
        BeanUtils.copyProperties(result, returnOrder);
        return returnOrder;
    }

    private static void setRemark(ConfirmOrderDto confirmOrderDto, String orderRemarks) {
        if (com.xyy.ec.order.core.util.StringUtil.isNotEmpty(orderRemarks)) {
            // 格式：{"companyCode1":"remark1","companyCode2":"remark2"}
            Map<String, String> companyRemarks = new HashMap<>();
            JSONObject remarkJson = JSONObject.parseObject(orderRemarks);
            for (String companyCode : remarkJson.keySet()) {
                String remark = remarkJson.get(companyCode) != null ? remarkJson.get(companyCode).toString() : "";
                if (companyCode.equals("0")) {
                    // 小药药商城备注
                    confirmOrderDto.setRemark(remark);
                } else {
                    // pop公司备注
                    companyRemarks.put(companyCode, remark);
                }
            }
            confirmOrderDto.setCompanyRemarks(companyRemarks);
        }
    }

    private Map<String, List<Long>> getVoucherIdMap(String orderVoucherIds) {
        if (StringUtil.isEmpty(orderVoucherIds)) {
            return Maps.newHashMap();
        }

        // 格式：{"voucherId1":"shopCode1::shopPatternCode1","voucherId2":"shopCode2::shopPatternCode2"}
        StringBuilder xyyMallVoucherIds = new StringBuilder();
        Map<String, List<Long>> shopVoucherIds = new HashMap<>();
        JSONObject voucherJson = JSONObject.parseObject(orderVoucherIds);
        if (voucherJson != null) {
            for (String key : voucherJson.keySet()) {

                if (key.equals("notVoucherIds")) {
                    continue;
                }

                String[] data = voucherJson.get(key).toString().split(":");
                // 店铺券
                if (!shopVoucherIds.containsKey(data[0])) {
                    shopVoucherIds.put(data[0], new ArrayList<>());
                }
                shopVoucherIds.get(data[0]).add(Long.valueOf(key));
//                if (data[1].equals(ShopPatternEnum.YBM.getCode())) {
//                    // 小药药商城券
//                    xyyMallVoucherIds.append(key).append(",");
//                } else {
//                    // 店铺券
//                    if (!shopVoucherIds.containsKey(data[0])) {
//                        shopVoucherIds.put(data[0], new ArrayList<>());
//                    }
//                    shopVoucherIds.get(data[0]).add(Long.valueOf(key));
//                }
            }
        }

//        List<Long> voucherIds = Splitter.on(",")
//                .omitEmptyStrings().trimResults()
//                .splitToList(xyyMallVoucherIds.toString()).stream()
//                .map(Long::valueOf)
//                .collect(Collectors.toList());
        ;
//        shopVoucherIds.put(ShopPatternEnum.YBM.getCode(), voucherIds);
        return shopVoucherIds;

    }

    private void addDataToModelMap(String branchCode, ModelMap modelMap) {
        try {
            Map<String, CodeitemBusinessDto> codeitemMap = codeItemServiceRpc.selectByCodemapRTMap("SETTLE_CONF", branchCode);
            CodeitemBusinessDto retryInterval = codeitemMap.get("RETRY_INTERVAL");
            modelMap.put("retryInterval", retryInterval.getName());

            CodeitemBusinessDto retryTimes = codeitemMap.get("RETRY_TIMES");
            modelMap.put("retryTimes", retryTimes.getName());
        } catch (Exception e) {
            logger.error("addDataToModelMap error", e);
        }
    }

    @Override
    public ApiRPCResult<SettleVO> preSettle(OrderSettleVo order) {
        SettleDto settleDto = buildSettleDto(order);
        settleDto.setIsOptimal(true);
        settleDto.setIsCheck(true);
        if(StringUtils.isNotEmpty(order.getBizProducts())) {
            List<SettleSkuDto> list = JSON.parseArray(order.getBizProducts(), SettleSkuDto.class);
            settleDto.setBizProducts(list);
        }
        logger.info("9582-settleDto:{}",JSON.toJSONString(settleDto));

        return orderServerRpcService.settle(settleDto);
    }

    @Override
    public ResultDTO<String> preSettleSprout(OrderSproutSettleVo order) {
        // 释放库存
        orderSproutBusinessApi.releaseSproutStock(order.getPurchaseNo());
        // 调用预结算接口
        SettleDto settleDto = buildSettleDto(order);
        settleDto.setIsOptimal(true);
        settleDto.setIsCheck(true);
        settleDto.setBizSource(BizSourceEnum.SPROUT.getKey());
        settleDto.setPurchaseNo(order.getPurchaseNo());
        ApiRPCResult<SettleVO> result = orderServerRpcService.settle(settleDto);
        if (result.isFail()) {
            // 去结算失败
            return ResultDTO.fail(result.getMsg());
        }
        return ResultDTO.success();
    }

    @Override
    @Deprecated
    public ResultDTO<OrderSettleCommunicationDto> settle(OrderSettleVo order) {
//        if (orderServerRpcService.refactorGray(order.getBranchCode(), OrderAbilityEnum.SETTLE.getKey(), order.getMerchantId())) {
        SettleDto settleDto = buildSettleDto(order);
        settleDto.setIsOptimal(true);
        ApiRPCResult<SettleVO> result = orderServerRpcService.settle(settleDto);
        if (result.isFail()) {
            // 去结算失败
            return ResultDTO.fail(result.getErrMsg());
        }

        OrderSettleCommunicationDto dto = convert(result.getData());
        return ResultDTO.success(dto);
//        } else {
//            // 1. 调用去结算方法
//            ApiRPCResult<OrderSettleCommunicationDto> response = orderSettleApi.settle(OrderSettleUtil.getRequestDto(order, CouponComputeAccountEnum.INITIALIZE_SETTLEMENT_ALLOCATION));
//
//            try {
//                SettleDto settleDto = buildSettleDto(order);
//                settleDto.setIsOptimal(true);
//                orderServerRpcService.checkSettle(settleDto, JSON.toJSONString(response));
//            }catch (Exception e){
//                logger.error("checkSettle error!msg:{},param:{}",e.getMessage(),order,e);
//            }
//
//            return settle(response);
//        }
    }


    /**
     * @param order
     * @description: 2. 订单去结算接口
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     */
    @Override
    public ResultDTO<SettleVO> settleForRefactor(OrderSettleVo order,List<SettleSkuDto> bizProducts) {
        SettleDto settleDto = buildSettleDto(order);
        if(CollectionUtils.isNotEmpty(bizProducts)) {
            if(CollectionUtils.isNotEmpty(settleDto.getBizProducts())) {
                settleDto.getBizProducts().addAll(bizProducts);
            }else {
                settleDto.setBizProducts(bizProducts);
            }
        }

        settleDto.setIsOptimal(true);
        ApiRPCResult<SettleVO> result = orderServerRpcService.settle(settleDto);
        if (result.isFail()) {
            // 去结算失败
            return ResultDTO.fail(result.getErrMsg());
        }
        return ResultDTO.success(result.getData());
    }
    @Override
    public ResultDTO<SettleVO> matchPriceSettle(OrderSettleVo order) {
        SettleDto settleDto = buildSettleDto(order);
        settleDto.setIsOptimal(true);
        if (StringUtils.isEmpty(settleDto.getTranNo())) {
            settleDto.setIsCheck(true);
        }
        ApiRPCResult<SettleVO> result = orderServerRpcService.matchPriceSettle(settleDto);
        if (result.isFail()) {
            // 去结算失败
            return ResultDTO.fail(result.getErrMsg());
        }
        return ResultDTO.success(result.getData());
    }
    @Override
    public ResultDTO matchPricePreSettle(OrderSettleVo order) {
        filterNoneSubmitOrderOrgIds(order);

        SettleDto settleDto = buildSettleDto(order);
        settleDto.setIsOptimal(true);
        settleDto.setIsCheck(true);
        ApiRPCResult result = orderServerRpcService.matchPricePreSettle(settleDto);
        if (result.isFail()) {
            // 去结算失败
            return ResultDTO.fail(result.getErrMsg());
        }
        return ResultDTO.success(result.getData());
    }
    public void filterNoneSubmitOrderOrgIds(OrderSettleVo order) {
        String notSubmitOrderOrgIds = order.getNotSubmitOrderOrgIds();
        List<SettleSkuVo> skuList = order.getSkuList();
        if (StringUtils.isNotBlank(notSubmitOrderOrgIds)) {
            String[] orgIdArray = notSubmitOrderOrgIds.split(",");
            if(Objects.nonNull(orgIdArray) && orgIdArray.length>0){
                Set<String> orgIdSet = Sets.newHashSet(orgIdArray);
                List<SettleSkuVo> newBizProducts = new ArrayList<>();
                for (SettleSkuVo bizProduct : skuList) {
                    if (orgIdSet.contains(bizProduct.getOrgId())) {

                    } else {
                        newBizProducts.add(bizProduct);
                    }
                }
                order.setSkuList(newBizProducts);

            }

        }
    }
    @Override
    public SettleDto queryMatchPriceSettleParam(Long merchantId) {

        logger.info("queryMatchPriceSettleParam入参:{}", merchantId);
        String val = redisClient.get("matchPriceCalcPreSettle" + merchantId);
        if (StringUtils.isNotEmpty(val)) {
            return JSONObject.parseObject(val, SettleDto.class);
        }
        return null;
    }
    private OrderSettleCommunicationDto convert(SettleVO settleVO) {
        OrderSettleCommunicationDto res = new OrderSettleCommunicationDto();
        BeanUtils.copyProperties(settleVO, res);
        List<com.xyy.ec.order.dto.settle.CompanySettleBusinessDto> companys = settleVO.getCompanys();
        if (CollectionUtils.isNotEmpty(companys)) {
            res.setCompanys(companys.stream().map(k -> {
                CompanySettleBusinessDto companySettleBusinessDto = new CompanySettleBusinessDto();
                BeanUtils.copyProperties(k, companySettleBusinessDto, "shops");
                if (CollectionUtils.isNotEmpty(k.getShops())) {
                    List<ShopSettleBusinessDto> shops = k.getShops().stream().map(s -> {
                        // 因为之后计算要强转所以需要在这一次beanmapper 转化
                        ShopSettleBusinessForPcDto shopSettleBusinessDto = BeanMapper.map(s, ShopSettleBusinessForPcDto.class);

                        List<ShoppingCartGroupDto> groupDtos = s.getGroups().stream().map(g -> {
                            ShoppingCartGroupDto groupDto = new ShoppingCartGroupDto();
                            BeanUtils.copyProperties(g, groupDto, "sorted");
                            List<ShoppingCartItemDto> sorted = g.getSorted().stream().map(item -> {
                                ShoppingCartItemDto shoppingCartItemDto = new ShoppingCartItemDto();
                                BeanUtils.copyProperties(item, shoppingCartItemDto);
                                if (item.getItem() != null) {
                                    // 因为原来逻辑中的商品
                                    shoppingCartItemDto.setItem(BeanMapper.map(item.getItem(), ShoppingCartVo.class));
                                }
                                // 因为realpayamount 老接口get方法有逻辑新接口没有逻辑 所以用子对象覆盖get方法
                                shoppingCartItemDto.setSubItemList(new ArrayList<>());
                                if (CollectionUtil.isNotEmpty(item.getSubItemList())) {
                                    item.getSubItemList().forEach(sku -> {
                                        shoppingCartItemDto.getSubItemList().add(BeanMapper.map(sku, ShoppingCartVo.class));
                                    });
                                }
                                return shoppingCartItemDto;
                            }).collect(Collectors.toList());
                            groupDto.setSorted(sorted);
                            return groupDto;
                        }).collect(Collectors.toList());


                        shopSettleBusinessDto.setGroups(groupDtos);
                        return shopSettleBusinessDto;
                    }).collect(Collectors.toList());
                    companySettleBusinessDto.setShops(shops);
                }
                return companySettleBusinessDto;
            }).collect(Collectors.toList()));
        }
        return res;
    }

    private <T> List<T> copyList(List<? extends Object> list, Class<T> cls) {
        if (CollectionUtil.isNotEmpty(list)) {
            return list.stream().map(k -> copy(k, cls)).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }

    private <T> T copy(Object obj, Class<T> cls) {
        try {
            T t = cls.newInstance();
            BeanUtils.copyProperties(obj, t);
            return t;
        } catch (InstantiationException e) {
            logger.error("copyList error InstantiationException,param:{}", obj, e);
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            logger.error("copyList error IllegalAccessException,param:{}", obj, e);
            throw new RuntimeException(e);
        }
    }

    private SettleDto buildSettleDto(OrderSettleVo order) {
        SettleDto settleDto = new SettleDto();
        BeanUtils.copyProperties(order, settleDto);
        if (Objects.isNull(settleDto.getBizSource())) {
            settleDto.setBizSource(BizSourceEnum.B2B.getKey());
        }
        settleDto.setAccountId(order.getAccountId());
        settleDto.setTerminalType(PlatformEnum.PC.getValue());
        settleDto.setUseBalance(order.isUseBalance());
        settleDto.setBalanceAmount(order.getBalanceAmount());
        settleDto.setUseVirtualGold(order.getUseVirtualGold());
        settleDto.setVirtualGoldBalance(order.getVirtualGoldBalance());
        settleDto.setUseRedPacket(order.getUseRedPacket());
        settleDto.setShopCode(order.getShopCode());
        setVoucherIds(settleDto, order.getVoucherIds());
        setRemark(settleDto, order.getRemark());
        this.setSxpSkus(settleDto, order.getSuiXinPinSkus());
        if (settleDto.getPayType() == null) {
            settleDto.setPayType(1);
        }
        List<SettleSkuDto> bizProducts = Lists.newArrayList();
        if (BizSourceEnum.GROUP_PURCHASE.getKey().equals(settleDto.getBizSource()) || BizSourceEnum.PIGOU.getKey().equals(settleDto.getBizSource())) {
            SettleSkuDto skuDto = new SettleSkuDto();
            skuDto.setQuantity(order.getProductNum());
            skuDto.setSkuId(order.getSkuId());
            bizProducts.add(skuDto);
        }
        if (CollectionUtils.isNotEmpty(order.getSkuList())) {
            List<SettleSkuVo> skuList = order.getSkuList();
            for (SettleSkuVo settleSkuVo : skuList) {
                SettleSkuDto skuDto = new SettleSkuDto();
                skuDto.setQuantity(settleSkuVo.getQty());
                skuDto.setSkuId(settleSkuVo.getSkuId());
                skuDto.setSelectStatus(1);
                skuDto.setOrgId(settleSkuVo.getOrgId());
                bizProducts.add(skuDto);
            }

        }
        settleDto.setBizProducts(bizProducts);
        logger.info("9528-ssm-setSxpSkus-2-settleDto:{}",JSON.toJSONString(settleDto));

        return settleDto;
    }
    /**
     * 设置随心拼属性
     * @param settleDto
     * @param sxpSkus
     */
    private void setSxpSkus(SettleDto settleDto, String sxpSkus) {
        if (StringUtil.isNotEmpty(sxpSkus)) {
            try {
                logger.info("9528-ssm-setSxpSkus-sxpSkus:{}",JSON.toJSONString(sxpSkus));

                List<SettleSxpSkuDto> sxpSkuDtos = JsonUtil.wapperStringToList(sxpSkus, SettleSxpSkuDto.class);
                logger.info("9528-ssm-setSxpSkus-sxpSkuDtos:{}",JSON.toJSONString(sxpSkuDtos));

                if(org.apache.commons.collections.CollectionUtils.isNotEmpty(sxpSkuDtos))
                {
                    sxpSkuDtos.forEach(x->{
                        if(x.getType()== null) {
                            x.setType(SxpTypeEnum.NORMAL_SXP.getType());
                        }
                    });
                }
                settleDto.setSettleSxpSkuDtos(sxpSkuDtos);
            } catch (IOException e) {
                logger.error("setSxpSkus error:", e);
            }
        }
        logger.info("9528-ssm-setSxpSkus-settleDto:{}",JSON.toJSONString(settleDto));

    }
    private static void setRemark(SettleDto communication, String orderRemarks) {
        if (StringUtil.isNotEmpty(orderRemarks)) {
            // 格式：{"companyCode1":"remark1","companyCode2":"remark2"}
            Map<String, String> companyRemarks = new HashMap<>();
            JSONObject remarkJson = JSONObject.parseObject(orderRemarks);
            for (String companyCode : remarkJson.keySet()) {
                String remark = remarkJson.get(companyCode) != null ? remarkJson.get(companyCode).toString() : "";
                if (companyCode.equals("0")) {
                    // 小药药商城备注
                    communication.setXyyMallRemark(remark);
                } else {
                    // pop公司备注
                    companyRemarks.put(companyCode, remark);
                }
            }
            communication.setCompanyRemarks(companyRemarks);
        }
    }

    private static void setVoucherIds(SettleDto communication, String orderVoucherIds) {
        if (StringUtil.isNotEmpty(orderVoucherIds)) {
            // 格式：{"voucherId1":"shopCode1::shopPatternCode1","voucherId2":"shopCode2::shopPatternCode2"}
            StringBuffer xyyMallVoucherIds = new StringBuffer();
            Map<String, List<Long>> shopVoucherIds = new HashMap<>();
            List<Long> platformVouchers = new ArrayList<>();
            JSONObject voucherJson = JSONObject.parseObject(orderVoucherIds);
            if (voucherJson != null) {
                for (String key : voucherJson.keySet()) {
                    if (key.equals("notVoucherIds")) {
                        communication.setIsOptimal(true);
                        continue;
                    }
                    String[] data = voucherJson.get(key).toString().split(":");
                    if (data[1].equals(ShopPatternEnum.YBM.getCode())) {
                        // 小药药商城券
                        xyyMallVoucherIds.append(key).append(",");
                        // 店铺券
                        if (!shopVoucherIds.containsKey(data[0])) {
                            shopVoucherIds.put(data[0], new ArrayList<>());
                        }
                        shopVoucherIds.get(data[0]).add(Long.valueOf(key));
                    } else if (data[1].equals("ALL")) {
                        platformVouchers.add(Long.valueOf(key));
                    } else {
                        // 店铺券
                        if (!shopVoucherIds.containsKey(data[0])) {
                            shopVoucherIds.put(data[0], new ArrayList<>());
                        }
                        shopVoucherIds.get(data[0]).add(Long.valueOf(key));
                    }
                }
            }
            communication.setXyyMallVoucherIds(xyyMallVoucherIds.toString());
            communication.setShopVoucherIds(shopVoucherIds);
            communication.setPlatformVouchers(platformVouchers);
        }
    }

    @Override
    public ResultDTO<OrderSettleCommunicationDto> calcSettleAmount(OrderSettleVo order) {
//        if (orderServerRpcService.refactorGray(order.getBranchCode(), OrderAbilityEnum.SETTLE.getKey(), order.getMerchantId())) {
        SettleDto settleDto = buildSettleDto(order);
        settleDto.setIsOptimal(false);
        ApiRPCResult<SettleVO> result = orderServerRpcService.settle(settleDto);
        if (result.isFail()) {
            // 去结算失败
            return ResultDTO.fail(result.getErrMsg());
        }
        OrderSettleCommunicationDto dto = convert(result.getData());
        return ResultDTO.success(dto);
//        } else {
//            ApiRPCResult<OrderSettleCommunicationDto> response = orderSettleApi.settle(OrderSettleUtil.getRequestDto(order, CouponComputeAccountEnum.AGAIN_SETTLEMENT_ALLOCATION));
//            try {
//                SettleDto settleDto = buildSettleDto(order);
//                settleDto.setIsOptimal(false);
//                orderServerRpcService.checkSettle(settleDto, JSON.toJSONString(response));
//            }catch (Exception e){
//                logger.error("checkSettle error!msg:{},param:{}",e.getMessage(),order,e);
//            }
//            return settle(response);
//        }
    }

    /**
     * @param order
     * @description: 4. 金额重新计算
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     */
    @Override
    public ResultDTO<SettleVO> calcSettleAmountForRefactor(OrderSettleVo order) {
        SettleDto settleDto = buildSettleDto(order);
        settleDto.setIsOptimal(false);
        //标识是结算页重洗计算
        settleDto.setIsRetryCalc(1);
        if(StringUtils.isNotEmpty(order.getBizProducts())) {
            List<SettleSkuDto> list = JSON.parseArray(order.getBizProducts(), SettleSkuDto.class);
            settleDto.setBizProducts(list);
        }
        this.setSxpSkus(settleDto, order.getSuiXinPinSkus());
        ApiRPCResult<SettleVO> result = orderServerRpcService.settle(settleDto);
        if (result.isFail()) {
            // 去结算失败
            return ResultDTO.fail(result.getErrMsg());
        }
        return ResultDTO.success(result.getData());
    }

    @Override
    public ResultDTO<OrderSettleCommunicationDto> settleSprout(OrderSproutSettleVo order) {
        // 1. 调用去结算方法
        ApiRPCResult<OrderSettleCommunicationDto> response = orderSproutBusinessApi.settle(OrderSettleUtil.getRequestDto(order, CouponComputeAccountEnum.INITIALIZE_SETTLEMENT_ALLOCATION));
        return settle(response);
    }

    /**
     * @param order
     * @description: 2.2 订单去结算接口
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     */
    @Override
    public ResultDTO<SettleVO> settleSproutV2(OrderSproutSettleVo order) {
        SettleDto settleDto = buildSettleDto(order);
        settleDto.setIsOptimal(true);
        settleDto.setBizSource(BizSourceEnum.SPROUT.getKey());
        settleDto.setPurchaseNo(order.getPurchaseNo());
        ApiRPCResult<SettleVO> result = orderServerRpcService.settle(settleDto);
        if (result.isFail()) {
            // 去结算失败
            return ResultDTO.fail(result.getErrMsg());
        }
        return ResultDTO.success(result.getData());
    }

    /**
     * @param paramOrder
     * @description: 4. 代下单金额重新计算
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     */
    @Override
    public ResultDTO<SettleVO> calcSproutSettleAmountV2(OrderSproutSettleVo paramOrder) {
        SettleDto settleDto = buildSettleDto(paramOrder);
        settleDto.setIsOptimal(false);
        settleDto.setPurchaseNo(paramOrder.getPurchaseNo());
        settleDto.setBizSource(BizSourceEnum.SPROUT.getKey());
        ApiRPCResult<SettleVO> result = orderServerRpcService.settle(settleDto);
        if (result.isFail()) {
            // 去结算失败
            return ResultDTO.fail(result.getErrMsg());
        }
        return ResultDTO.success(result.getData());
    }

    @Override
    public ResultDTO<OrderSettleCommunicationDto> calcProutSettleAmount(OrderSproutSettleVo order) {
        ApiRPCResult<OrderSettleCommunicationDto> response = orderSproutBusinessApi.settle(OrderSettleUtil.getRequestDto(order, CouponComputeAccountEnum.AGAIN_SETTLEMENT_ALLOCATION));
        return settle(response);
    }

    private ResultDTO<OrderSettleCommunicationDto> settle(ApiRPCResult<OrderSettleCommunicationDto> response) {
        if (response.isFail()) {
            // 去结算失败
            return ResultDTO.fail(response.getErrMsg());
        }
        return ResultDTO.success(response.getData());
    }

    @Override
    public void setSettleData(ModelMap modelMap, OrderSettleCommunicationDto orderSettleCommunicationDto, String branchCode, Long merchantId) throws Exception {
        if (orderSettleCommunicationDto.getProductTotalNum() < 1) {
            // 得到还差多少钱
            throw new IllegalArgumentException("采购的商品数量不能为0，请选择要采购的商品！");
        }
        // 设置起送价
        setStartingPriceString(modelMap, branchCode);
        // 设置收货地址
        setShippingAddressList(modelMap, merchantId);
        modelMap.put("hasThirdCompany", false);
        modelMap.put("orderSettle", orderSettleCommunicationDto);

        fillExtraInfo(orderSettleCommunicationDto);

        // 设置小药药自营叠加券
        if (orderSettleCommunicationDto.getHasXyyMallShop() != null && orderSettleCommunicationDto.getHasXyyMallShop()) {
            ShopSettleBusinessForPcDto xyyMallShop = (ShopSettleBusinessForPcDto) orderSettleCommunicationDto.getCompanys().get(0).getShops().get(0);
            modelMap.put("availDjVoucherList", xyyMallShop.getAvailDjVoucherList());
            modelMap.put("unavailDjVoucherList", xyyMallShop.getUnavailDjVoucherList());
            modelMap.put("xyyMallShop", xyyMallShop);
        }


        modelMap.put("payTips", "");
        modelMap.put("orderQty", orderSettleCommunicationDto.isNewUser() ? 1 : 0);
        // 设置返利文案
        setReturnBalanceTips(modelMap, branchCode, orderSettleCommunicationDto.getRebateBalanceAmt());
        // 设置优惠券信息
        setVoucherInfo(modelMap, branchCode);
        // 设置发票随行
        setPtdzInvoincePeer(modelMap, merchantId);
        // 设置发票类型
        setBillType(modelMap, merchantId);
        // 设置购物车镜像ID
        setShoppingCartImgUUID(modelMap, merchantId);
    }

    /**
     * @param modelMap
     * @param settleVO
     * @param branchCode
     * @description: 3. 填充结算页数据
     * @author: wcwanga
     * @create: 2020-02-19 14:13
     * @param: [order]
     * @return: void
     */
    @Override
    public void setSettleDataForRefactor(ModelMap modelMap, SettleVO settleVO, String branchCode, MerchantBussinessDto merchant) throws Exception {
        if (settleVO.getProductTotalNum() < 1) {
            // 得到还差多少钱
            throw new IllegalArgumentException("采购的商品数量不能为0，请选择要采购的商品！");
        }
        Long merchantId = merchant.getId();
        if (AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
            settleVO.setIsHideOffLinePay(false);
            settleVO.setIsShowAccountPeriodPay(false);
        }
        // 设置起送价
        setStartingPriceString(modelMap, branchCode);
        //TODO 删除
        fillExtraInfo(settleVO);
        // 设置收货地址
        setShippingAddressList(modelMap, merchantId);
        modelMap.put("hasThirdCompany", false);
        modelMap.put("orderSettle", settleVO);
        modelMap.put("payTips", "");
        if(Objects.nonNull(settleVO) && StringUtils.isNotEmpty(settleVO.getTranNo())){
            modelMap.put("tranNo", settleVO.getTranNo());
        }
        modelMap.put("orderQty", settleVO.isNewUser() ? 1 : 0);
        // 设置返利文案
        setReturnBalanceTips(modelMap, branchCode, settleVO.getRebateBalanceAmt());
        // 设置优惠券信息
        setVoucherInfo(modelMap, branchCode);
        // 设置发票随行
        setPtdzInvoincePeer(modelMap, merchantId);
        // 设置发票类型
        setBillType(modelMap, merchantId);
        // 设置购物车镜像ID
        setShoppingCartImgUUID(modelMap, merchantId);
    }

    @Deprecated
    private void fillExtraInfo(SettleVO settleVO) {

        List<com.xyy.ec.order.dto.settle.CompanySettleBusinessDto> companys = settleVO.getCompanys();
        if (CollectionUtils.isEmpty(companys)) {
            return;
        }
        for (com.xyy.ec.order.dto.settle.CompanySettleBusinessDto company : companys) {
            List<com.xyy.ec.order.dto.settle.ShopSettleBusinessDto> shops = company.getShops();
            if (CollectionUtils.isEmpty(shops)) {
                return;
            }
            for (com.xyy.ec.order.dto.settle.ShopSettleBusinessDto shop : shops) {

                List<SkuInfoForVoucher> skuInfoForVoucherList = Lists.newArrayList();

                shop.getGroups().stream()
                        .flatMap(x -> x.getSorted().stream())
                        .forEach(x -> {
                            if (CollectionUtils.isEmpty(x.getSubItemList())) {
                                com.xyy.ec.order.dto.settle.ShoppingCartDto item = x.getItem();
                                skuInfoForVoucherList.add(SkuInfoForVoucher.builder()
                                        .skuId(item.getSkuId())
                                        .price(item.getPrice())
                                        .shopCode(item.getShopCode())
                                        .amount(item.getAmount()).build());
                            } else {
                                x.getSubItemList().forEach(y -> {
                                    skuInfoForVoucherList.add(SkuInfoForVoucher.builder()
                                            .skuId(y.getSkuId())
                                            .price(y.getPrice())
                                            .shopCode(y.getShopCode())
                                            .amount(y.getAmount()).build());
                                });
                            }
                        });

                String skuInfo = JSON.toJSONString(skuInfoForVoucherList);
                shop.setSkus(skuInfo);

                //填充优惠券信息
                List<com.xyy.ec.order.dto.voucher.VoucherDto> availVoucherList = shop.getUseVoucherList();
                if (CollectionUtils.isEmpty(availVoucherList)) {
                    shop.setSelectVoucherIds("");
                    continue;
                }
                String selectVoucherIds = availVoucherList.stream()
                        .map(x -> String.valueOf(x.getVoucherId()))
                        .collect(Collectors.joining(","));
                shop.setSelectVoucherIds(selectVoucherIds);

            }
        }
    }

    private void fillExtraInfo(OrderSettleCommunicationDto orderSettleCommunicationDto) {

        List<CompanySettleBusinessDto> companys = orderSettleCommunicationDto.getCompanys();
        if (CollectionUtils.isEmpty(companys)) {
            return;
        }
        for (CompanySettleBusinessDto company : companys) {
            List<ShopSettleBusinessDto> shops = company.getShops();
            if (CollectionUtils.isEmpty(shops)) {
                return;
            }
            for (ShopSettleBusinessDto shop : shops) {

                List<SkuInfoForVoucher> skuInfoForVoucherList = Lists.newArrayList();

                shop.getGroups().stream()
                        .flatMap(x -> x.getSorted().stream())
                        .forEach(x -> {
                            if (CollectionUtils.isEmpty(x.getSubItemList())) {
                                ShoppingCartDto item = x.getItem();
                                skuInfoForVoucherList.add(SkuInfoForVoucher.builder()
                                        .skuId(item.getSkuId())
                                        .price(BigDecimal.valueOf(item.getPrice()))
                                        .shopCode(item.getShopCode())
                                        .amount(item.getAmount()).build());
                            } else {
                                x.getSubItemList().forEach(y -> {
                                    skuInfoForVoucherList.add(SkuInfoForVoucher.builder()
                                            .skuId(y.getSkuId())
                                            .price(BigDecimal.valueOf(y.getPrice()))
                                            .shopCode(y.getShopCode())
                                            .amount(y.getAmount()).build());
                                });
                            }
                        });

                String skuInfo = JSON.toJSONString(skuInfoForVoucherList);
                shop.setSkus(skuInfo);

                //填充优惠券信息
                List<VoucherDto> availVoucherList = shop.getUseVoucherList();
                if (CollectionUtils.isEmpty(availVoucherList)) {
                    shop.setSelectVoucherIds("");
                    continue;
                }
                String selectVoucherIds = availVoucherList.stream()
                        .map(x -> String.valueOf(x.getVoucherId()))
                        .collect(Collectors.joining(","));
                shop.setSelectVoucherIds(selectVoucherIds);

            }
        }
    }

    @Override
    public ResultDTO<String> voucherMonitor(Order paramOrder, Integer bizSource) {
        ConfirmOrderDto confirmOrderDto = new ConfirmOrderDto();
        BeanUtils.copyProperties(paramOrder, confirmOrderDto);
        confirmOrderDto.setBizSource(bizSource);
        confirmOrderDto.setPurchaseNo(paramOrder.getPurchaseNo());
        confirmOrderDto.setUseBalance(paramOrder.isUseBalance());
        Map<String, List<Long>> shopVoucherIds = getVoucherIdMap(paramOrder.getVoucherIds());
        confirmOrderDto.setShopVoucherIds(shopVoucherIds);
        setRemark(confirmOrderDto, paramOrder.getRemark());
        return orderServerRpcService.voucherMonitor(confirmOrderDto);
    }

    /**
     * 1、共仓逻辑：
     * 共仓是域，订单和退款单下推时，将订单信息中的branchCode转换成共仓的域编码
     * 2、切仓逻辑：曾经共仓，后来自建仓库
     * 切仓后，退款问题：
     * 1) 切仓前的订单发起退款，退款单下推时，必须还转换成共仓时的branchCode
     * 2）切仓后的订单、退款单，则采用自己域的新域编码
     */
    @Override
    public void shareWarehouseLogic(OrderBusinessDto order) {
        if (order == null) {
            return;
        }
        //获取共仓数据字典配置信息
        Map<String, String> shareWarehouseInfo = getShareWarehouseInfo(order);
        //根据域编码获取切仓时间，只有切仓的域，才会有这个参数值
        String switchWarehouseTime = shareWarehouseInfo.get("SWITCH_WAREHOUSE_TIME");
        if (StringUtils.isNotEmpty(switchWarehouseTime)) {
            //不为空 说明切仓了:切仓之前的订单还取共仓时的branchCode,切仓之后的订单取自己新仓branchCode
            //false 订单创建时间在切仓之前;true 订单创建时间在切仓之后
            if (order.getCreateTime().after(DateUtil.string2Date(switchWarehouseTime, DateUtil.PATTERN_STANDARD))) {
                logger.info("订单<{}>创建时间在切仓时间之后：切仓时间<{}>", order.getOrderNo(), switchWarehouseTime);
                return;
            }
            ;
        }
        //获取共仓的域编码
        String targetBranchCode = shareWarehouseInfo.get(order.getBranchCode());
        if (StringUtil.isBlank(targetBranchCode)) {
            return;
        }
        order.setBranchCode(targetBranchCode);
    }

    /**
     * 获取共仓数据字典配置信息
     * 2020-04-05
     *
     * @param order
     * @return
     */
    private Map<String, String> getShareWarehouseInfo(OrderBusinessDto order) {

        String branchCode = order.getBranchCode();
        Map<String, String> map = new HashMap();
        try {
            logger.info("获取订单号<{}>共仓数据字典，请求参数：branchCode={}", order.getOrderNo(), branchCode);
            List<CodeitemBusinessDto> list = codeItemServiceRpc.selectByCodemapRTList("ORDER_SHARE_WAREHOUSE", branchCode);
            logger.info("获取订单号<{}>共仓数据字典，返回数据：{}", order.getOrderNo(), JSONObject.toJSONString(list));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
                for (CodeitemBusinessDto codeitemBusinessDto : list) {
                    map.put(codeitemBusinessDto.getCode(), codeitemBusinessDto.getName());
                }
            }
            return map;
        } catch (Exception e) {
            logger.error("获取订单号<{}>，branchCode<{}>获取共仓数据字典出现异常:{}", order.getOrderNo(), branchCode, e);
        }
        return map;
    }

    @Override
    public JSONArray getAvailableChannel(Order confirmOrder) {
        return getAvailableChannel(confirmOrder.getIsThirdCompany());
    }

    private JSONArray getAvailableChannel(Integer isThirdCompany) {
        try {
            PayCommunicationDto payCommunicationDto = new PayCommunicationDto();
            PayChannelRouteDto payChannelRouteDto = new PayChannelRouteDto();
            payChannelRouteDto.setIsThirdCompanyOrder(isThirdCompany == 1 ? true : false);
            payCommunicationDto.setPayChannelRoute(payChannelRouteDto);
            PayResponseDto<JSONArray> result = payChannelApi.getAvailableChannel(payCommunicationDto);
            if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
                return result.getData();
            }
            return null;
        } catch (Exception e) {
            logger.error("getAvailableChannel error", e);
            return null;
        }
    }

    @Override
    public JSONArray getAvailableChannel(OrderBusinessDto order) {
        return getAvailableChannel(order.getIsThirdCompany());
    }

    @Override
    public CashierDto getCashierForPc(OrderBusinessDto order, List<OrderBusinessDto> orderList) {
        CashierQueryParamDto param = new CashierQueryParamDto();
        param.setMerchantId(order.getMerchantId());
        param.setAccountId(order.getAccountId());
        param.setOrderId(order.getId());
        param.setOrderNo(order.getOrderNo());
        param.setReqScene("cashier");
        param.setTerminalType(PlatformEnum.getByValue(4).getKey());
        param.setVersion(0);
        param.setOrderPayAmount(order.getCashPayAmount().toPlainString());

        if (order.getIsParent() == 1) {
            boolean hasPop = false;
            for (OrderBusinessDto dto : orderList) {
                // 只获取子单
                if (dto.getIsParent() == 0) {
                    param.getOrgIds().add(dto.getOrgId());
                    if (dto.getIsThirdCompany() == 1 && dto.getIsFbp() == 0) {
                        hasPop = true;
                    }
                }
            }
            param.setHasPop(hasPop);
        } else {
            if (order.getIsThirdCompany() == 0 || order.getIsFbp() == 1) {
                param.setHasPop(false);
            } else if (order.getIsThirdCompany() == 1 && order.getIsFbp() == 0) {
                param.setHasPop(true);
            }
            param.setOrgIds(Collections.singletonList(order.getOrgId()));
            ShopInfoDto shopInfoDto = new ShopInfoDto();
            shopInfoDto.setOrgId(order.getOrgId());
            shopInfoDto.setIsVirtualSupplier(order.getIsVirtualSupplier() == 1?true:false);
            if (order.getIsVirtualSupplier() == 1) {
                shopInfoDto.setShopName("药店品种预约中心");
            } else {
                shopInfoDto.setShopName(order.getCompanyName());
            }
            param.getShops().add(shopInfoDto);
        }
        if (StringUtils.isNotEmpty(param.getPaycode())) {
            param.setPayChannelSelected(PaymentTypeCodeEnum.getCodeByValue(param.getPaycode()));
        }
        logger.info("查看收银台3：{}",order.getOrderNo(),JSONObject.toJSONString(param));
        ApiRPCResult<CashierDto> apiRPCResult = payApi.queryCashierList(param);
        logger.info("查看收银台4：{},{}",order.getOrderNo(),JSONObject.toJSONString(apiRPCResult));

        if (apiRPCResult.isSuccess()) {
            return apiRPCResult.getData();
        }
        return null;
    }

    @Override
    public CashierDto getCashierForRecharge(CashierQueryParamDto param) {
        if (StringUtils.isNotEmpty(param.getPaycode())) {
            param.setPayChannelSelected(PaymentTypeCodeEnum.getCodeByValue(param.getPaycode()));
        }
        logger.info("购物金充值查看收银台 req：{}",JSONObject.toJSONString(param));
        ApiRPCResult<CashierDto> result = payApi.queryCashierList(param);
        logger.info("购物金充值查看收银台 result：{}",JSONObject.toJSONString(result));

        if (result.isSuccess()) {
            if(result.getData()!=null && CollectionUtil.isNotEmpty(result.getData().getExtendPaymentList())){
                logger.info("##getCashierForRecharge--合并{}",result.getData().getPaymentlist().addAll(result.getData().getExtendPaymentList()));
            }
            logger.info("##getCashierForRecharge--result.getData{}",result.getData());
            return result.getData();
        }
        return null;
    }
}
