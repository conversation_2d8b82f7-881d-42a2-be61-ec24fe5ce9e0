<!DOCTYPE HTML>
<html>
	<head>
		<#include "/common/common.ftl" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<title>退款列表</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">
		<link rel="stylesheet" href="/static/css/user.css?t=${t_v}" />
		<link rel="stylesheet" href="/static/css/dingdan.css?t=${t_v}" />
		<link rel="stylesheet" href="/static/css/order/reset.css?t=${t_v}" />
        <link rel="stylesheet" href="/static/css/order/myreorderdetail.css?t=${t_v}"/>
        <link rel="stylesheet" href="/static/css/element.index.css?t=${t_v}" />
		<script type="text/javascript" src="/static/js/order/refundReason.js?t=${t_v}"></script>
		<script type="text/javascript" src="/static/js/uploadPreview.js?t=${t_v}"></script>
        <script src="${ctx}/static/js/ajaxfileupload.js"></script>
        <style>
            .orderBtnStyle {
                border-radius: 4px;
                border: 1px solid red;
                height: 30px;
                line-height: 30px;
                color: red;
                text-align: center;
                padding: 0 10px;
                font-size: 14px;
                margin-right: 10px;
                background: #fff;
            }
            .green {
                border: 1px solid #00dc82;
                color: #00dc82;
            }
            .tou_litle {
                height: 80px;
            }

            .center_tui {
                height: 30px;
                /*line-height: 30px;*/
                width: 280px;
            }

            .center_qu {
                margin-top: 20px;
            }

            .sui-steps-round > div .round {
                width: 18px;
                height: 18px;
                line-height: 18px;
                font-size: 16px;
            }

            .sui-steps-round > div .bar {
                margin-top: 10px;
            }

            .sui-steps-round > .finished .round {
                border: 4px solid #00dc82;
                background-color: #00dc82;
            }

            .sui-steps-round > .finished > label, .sui-steps-round > .current > label {
                color: #999999;
                font-weight: 600;
            }

            .sui-steps-round > .todo > label, .sui-steps-round > .last > label {
                color: #ddd;
                font-weight: 600;
            }

            .sui-steps-round > .last.finished > label, .sui-steps-round > .last.current > label {
                color: #999999;
                font-weight: 600;
            }


            .sui-steps-round > .current .round {
                width: 10px;
                height: 10px;
                border: 8px solid #00dc82;
                background-color: #fff;
            }

            .sui-steps-round > .finished .bar {
                background-color: #00dc82;
            }

            .sui-steps-round > .current-finished .round {
                border: 4px solid #00dc82;
                background-color: #00dc82;
            }

            .sui-steps-round > .current-finished .bar {
                background-color: #ddd;
            }

            .sui-steps-round > .current-finished > label {
                color: #999999;
                font-weight: 600;

            }

            .sui-steps-round > .current .bar {
                background-color: #ddd;
            }

            .sui-steps-round > .todo .round {
                width: 10px;
                height: 10px;
                border: 8px solid #eee;
                background-color: #c6c6c6;
                color: #fff;
            }

            .sui-steps-round > .todo.last .round {
                width: 10px;
                height: 10px;
                border: 8px solid #eee;
                background-color: #c6c6c6;
                color: #fff;
            }

            .sui-steps-round > .finished.last .round, .sui-steps-round > .current.last .round {
                border: 4px solid #00dc82;
                background-color: #00dc82;
                color: #fff;
            }
            .el-radio {
                display: block;
                font-weight: normal;
                line-height: 20px;
            }

            .smallSty{
                line-height: 10px;
                padding: 20px 0 0 48px;
            }
            .smallSty .p1{
                font-size: 15px;
                font-weight: bold;
                color: black;
            }
            .smallSty .p2{
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                padding: 10px 0;
            }
            .someAmountSty{
                height: 80px;
                display: flex;
                align-items: center;
            }
            .someAmountSty .textSty{
                color: #333 !important;
                font-weight: bold !important;
                margin: 10px !important;
            }
            .someAmountSty .imgSty{
                display: flex !important;
                align-items: center !important;
            }
            .numSty{
                padding-left: 90px !important;
            }
        </style>
	</head>

	<body>
        <div class="container">

        <!--头部导航区域开始-->
        <div class="headerBox" id="headerBox">
				<#include "/common/header.ftl" />
        </div>
        <!--头部导航区域结束-->

        <!--主体部分开始-->
        <div class="main">
            <input type="hidden" id="merchantId" value="${merchant.id}"/>
            <div class="myorder row">
                <!--面包屑-->
                <ul class="sui-breadcrumb">
                    <li><a href="${ctx }/">首页</a>></li>
                    <li><a href="${ctx }/merchant/center/index.htm">用户中心</a>></li>
                    <li><a href="${ctx }/merchant/center/order/index.htm">我的订单</a>></li>
                    <li class="active">查看退款</li>
                </ul>
                <div class="myorderdetail-content clear">
                    <div class="side-left fl">
                            <#include "/common/merchantCenter/left.ftl" />
                    </div>

                    <div class="main-right fr">
                        <!--提示信息-->
                        <div class="sui-msg msg-large msg-tips">
                            <div class="msg-con">温馨提示：1.如退款商品包含赠品，请将赠品同退款商品一并寄回；2.客服会在第一时间与您联系确认退货事宜，给您带来的不便，敬请谅解，药帮忙竭诚为您服务！
                            </div>
                        </div>
                        <!--收货人信息 POP订单隐藏-->
                        <#if isShowRefundInfo == 1>
                            <div class="receiver_info">
                                <div class="receiver_info_tit">收货人信息：</div>
                                <div class="receiver_info_nane"><span class="l_tit">姓　　名：</span>
                                    <span class="r_info">${refundOrderName}</span>
                                </div>
                                <div class="receiver_info_tel"><span class="l_tit">联系电话：</span>
                                    <span class="r_info">${refundOrderPhone}</span></div>
                                <div class="receiver_info_add"><span class="l_tit">收货地址：</span>
                                    <span>${refundOrderAdress}</span>
                                </div>
                                <div class="receiver_info_add ems_info"><span class="l_tit">快递说明：</span>
                                    <span>${refundOrderExpressDelivery}</span>
                                </div>
                            </div>
                        </#if>
                        <div class="tuikuanbox">
                            <#if (refundOrdersList??)>
                                <!--列表模式-->
                            <#list refundOrdersList as refundOrder>
                                <!--套餐列表模式-->
                                <div class="listmode-tk">
                                    <#if refundOrder.expressCountDownTime?? && refundOrder.expressCountDownTime != 0 >
                                        <div class="receiver_time">
                                        <div class="time_left"><span>待填写退货物流</span><span
                                                    style="color: #F62827;background: #FFF7F7;border: 1px solid #F62626;border-radius: 2px 0 0 2px;line-height: 18px" id="expressCountDownTime" data_time="${refundOrder.expressCountDownTime}"></span>
                                        </div>
                                        <a href="/merchant/center/order/editOrderRefundExpress/${refundOrder.id}.htm" style="color: #FF2021">编辑退货物流>></a>
                                    </div>
                                    </#if>

                                    <!--展开 收起-->
                                    <div class="headbox" style="display: flex;">
                                        <div class="message-list" style="display: flex; width: auto; height: auto">
                                            <div style="width: 280px">
                                                <p style="float: none;width: auto">
                                                    <span>退款单号：</span><span>${refundOrder.refundOrderNo}</span>
                                                </p>
                                                <p style="float: none;width: auto">
                                                    <span>申请时间：</span><span><#if refundOrder.refundCreateTime??>${refundOrder.refundCreateTime?string('yyyy年MM月dd日  HH:mm:ss') }</#if></span>
                                                    <#if refundOrder.invoiceDeliveryInfo?? && refundOrder.invoiceDeliveryInfo != "">
                                                        发票寄出快递： ${refundOrder.invoiceDeliveryInfo}
                                                    </#if>
                                                </p>
                                            </div>
                                            <div style="height: auto; width: 410px">
                                                <div style="display: flex; width: 410px; height: auto">
                                                    <p style="flex: 1; display: flex">
                                                        <span>退款状态：</span>
                                                        <#if refundOrder.auditProcessState??>
                                                            <#if refundOrder.isToBeConfirmedProcess?? && refundOrder.isToBeConfirmedProcess==1>
                                                                <#if refundOrder.refundChannel==4>
                                                                    <#if refundOrder.auditState==0 && refundOrder.auditProcessState==0>
                                                                        <span class="tui_hao1">待客户确认退款</span>
                                                                    <#elseif refundOrder.auditState==0 && refundOrder.auditProcessState==3>
                                                                        <span class="tui_hao1">待审核</span>
                                                                    <#elseif refundOrder.auditState==0 && refundOrder.auditProcessState==-1>
                                                                        <span class="tui_hao1">待入库&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==0 && (refundOrder.auditProcessState==1 || refundOrder.auditProcessState==4 || refundOrder.auditProcessState==5 || refundOrder.auditProcessState==6)>
                                                                        <span class="tui_hao1">待退款&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==0>
                                                                        <span class="tui_hao3">客户已拒绝退款</span>
                                                                    <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==3>
                                                                        <span class="tui_hao3">客服审核不通过&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==-1>
                                                                        <span class="tui_hao3">入库失败&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==-1 && (refundOrder.auditProcessState==1 || refundOrder.auditProcessState==4 || refundOrder.auditProcessState==5 || refundOrder.auditProcessState==6)>
                                                                        <span class="tui_hao3">退款失败&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==1>
                                                                        <span class="tui_hao2">退款成功&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    </#if>
                                                                <#elseif refundOrder.refundChannel==5>
                                                                    <#if refundOrder.auditState==0 && refundOrder.auditProcessState==0>
                                                                        <span class="tui_hao1">待审核</span>
                                                                    <#elseif refundOrder.auditState==0 && refundOrder.auditProcessState==-1>
                                                                        <span class="tui_hao1">待客户确认退款</span>
                                                                    <#elseif refundOrder.auditState==0 && refundOrder.auditProcessState==3>
                                                                        <span class="tui_hao1">待入库&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==0 && (refundOrder.auditProcessState==1 || refundOrder.auditProcessState==4 || refundOrder.auditProcessState==5 || refundOrder.auditProcessState==6)>
                                                                        <span class="tui_hao1">待退款&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==0>
                                                                        <span class="tui_hao3">客服审核不通过&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==-1>
                                                                        <span class="tui_hao3">客户拒绝退款</span>
                                                                    <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==3>
                                                                        <span class="tui_hao3">入库失败&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==-1 && (refundOrder.auditProcessState==1 || refundOrder.auditProcessState==4 || refundOrder.auditProcessState==5 || refundOrder.auditProcessState==6)>
                                                                        <span class="tui_hao3">退款失败&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==1>
                                                                        <span class="tui_hao2">退款成功&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    </#if>
                                                                <#elseif refundOrder.refundChannel==1>
                                                                    <#if refundOrder.auditState==0 && refundOrder.auditProcessState==2 && refundOrder.refundMode==4>
                                                                        <span class="tui_hao1">待客户确认退款</span>
                                                                    <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==0&& refundOrder.toBeConfirmedPassType==4>
                                                                        <span class="tui_hao3">退款失败&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    <#elseif refundOrder.auditState==1>
                                                                        <span class="tui_hao2">退款成功&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                    </#if>
                                                                </#if>
                                                            <#else>
                                                                <#if refundOrder.auditState==0 && refundOrder.auditProcessState==0>
                                                                    <span class="tui_hao1">待审核</span>
                                                                <#elseif refundOrder.auditState==0 && refundOrder.auditProcessState==-1>
                                                                    <span class="tui_hao1">待入库&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                <#elseif refundOrder.auditState==0 && (refundOrder.auditProcessState==1 || refundOrder.auditProcessState==4 || refundOrder.auditProcessState==5 || refundOrder.auditProcessState==6)>
                                                                    <span class="tui_hao1">待退款&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==0 && refundOrder.cancelChannel == 2>
                                                                    <span class="tui_hao3">退款取消&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==0 && refundOrder.cancelChannel == 1>
                                                                    <span class="tui_hao3">客服审核不通过&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==0 && refundOrder.cancelChannel == 0>
                                                                    <span class="tui_hao3">客服审核不通过&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                <#elseif refundOrder.auditState==-1 && refundOrder.auditProcessState==-1>
                                                                    <span class="tui_hao3">入库失败&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                <#elseif refundOrder.auditState==-1 && (refundOrder.auditProcessState==1 || refundOrder.auditProcessState==4 || refundOrder.auditProcessState==5 || refundOrder.auditProcessState==6)>
                                                                    <span class="tui_hao3">退款失败&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                <#elseif refundOrder.auditState==1>
                                                                    <span class="tui_hao2">退款成功&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                                </#if>
                                                            </#if>
                                                        <#else>
                                                            <#if refundOrder.auditState==0 >
                                                                <span class="tui_hao1">退款审核中</span>
                                                            <#elseif refundOrder.auditState==1>
                                                                <span class="tui_hao2">退款成功&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                            <#elseif refundOrder.auditState==-1>
                                                                <span class="tui_hao3">退款关闭&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                                            </#if>
                                                        </#if>
                                                        <#if refundOrder.refundMode!=4 >
                                                            <a class="refund-detail" href="javascript:void(0);"
                                                            data-id="${refundOrder.id}" style="margin-left:5px;"> 详情 》</a>
                                                        </#if>
                                                        </p>
                                                        <p style="width: auto">
                                                        <#if refundOrder.isToBeConfirmedProcess?? && refundOrder.isToBeConfirmedProcess==1 && refundOrder.toBeConfirmedCountDownTime?? && refundOrder.toBeConfirmedCountDownTime gt 0>
                                                        <button type="button" id="refuseRefound" data-id="${refundOrder.id}" data-dismiss="modal" class="orderBtnStyle">拒绝退款</button>
                                                        <button type="button" id="agreeRefound" data-id="${refundOrder.id}"  data-dismiss="modal" class="orderBtnStyle green agreeRefound">同意退款</button>
                                                        </#if>
                                                        <#if refundOrder.refundPayEvidence?? && refundOrder.refundPayEvidence != "">
                                                            <a class="refund-pay-evidence" target="_blank" href="${productImageUrl}/${refundOrder.refundPayEvidence}" style="margin-left:5px;">查看打款凭证 》</a>
                                                        </#if>
                                                        </p>
                                                </div>
                                                <#if refundOrder.canPlatformIn == true>
                                                <div onclick="jumpPlatformHandle('${refundOrder.refundOrderNo}','${refundOrder.orderNo}')" style="width: 100px;height: 36px;background-color: #00B955;color: #fff;border-radius: 4px;display: flex;align-items: center;justify-content: center;font-size: 14px;padding: 0 10px;">平台介入</div>
                                                </#if>
                                                <#if refundOrder.toBeConfirmedCountDownTime?? && refundOrder.toBeConfirmedCountDownTime gt 0>
                                                    <div style="height: auto">
                                                        <p style="float: none; width: auto">剩余时间：<span style="color: red" data-id="${refundOrder.toBeConfirmedCountDownTime}" id="countDown"></span></p>
                                                        <p style="float: none; width: auto; line-height: 20px;">${refundOrder.toBeConfirmedPrompt}</p>
                                                    </div>
                                                <#elseif refundOrder.refundMode==4>
                                                    <#if refundOrder.auditState==-1 && refundOrder.auditProcessState==0 && refundOrder.toBeConfirmedPassType==4>
                                                        <div style="height: auto">
                                                            <p style="float: none; width: auto; line-height: 20px;">您已拒绝小额赔偿</p>
                                                        </div>
                                                    <#elseif refundOrder.auditState==1>
                                                        <div style="height: auto">
                                                            <p style="float: none; width: auto; line-height: 20px;">已小额赔偿，请检查购物金余额</p>
                                                        </div>
                                                    </#if>
                                                </#if>
                                            </div>
                                            <#if refundOrder.isThirdCompany ==0>
                                                <#--<p>-->
                                                    <#--<span>渠&nbsp;道：</span><span><#if refundOrder.channelCode?? && refundOrder.channelCode == "2">宜块钱 <#else>药帮忙</#if></span>-->
                                                <#--</p>-->
                                            </#if>
                                        </div>
                                        <div style="flex: 1">
                                            <div class="btn-box" style="width: auto">
                                                <#if refundOrder.auditState==0 && refundOrder.auditProcessState == 0 && refundOrder.refundChannel==1 && orderBusinessDto.status != 7 && refundOrder.refundOrderType != 2>
                                                    <a href="javascript:void(0);" class="sui-btn btn-bordered"
                                                       onclick="cancelRefundOrder(${refundOrder.id},${refundOrder.auditProcessState})">取消退款</a>
                                                </#if>
                                                <#if  1 == refundOrder.refundMode && (1 == refundOrder.refundChannel || 2 == refundOrder.refundChannel || 4 == refundOrder.refundChannel) && refundOrder.auditState==0 && refundOrder.auditProcessState==-1 && refundOrder.refundOrderType != 2>
                                                    <!--客户对待入库状态且为“退货退款”类型的退款单详情，可填写退货物流信息-->
                                                    <a href="javascript:void(0);"
                                                       class="sui-btn  btn-primary _edit_ems" onclick="editExpressStatusCheck(${refundOrder.id})">
                                                        <#if refundOrder.orderRefundExpressBusinessDto.id??>编辑退货物流<#else>填写退货物流</#if>
                                                    </a>
                                                <#elseif 3 == refundOrder.payType && 1== refundOrder.refundChannel && refundOrder.auditState==0 && refundOrder.auditProcessState==0 && refundOrder.refundOrderType != 2>
                                                    <a href="javascript:void(0);"
                                                       class="sui-btn  btn-primary _confirm" onclick="editBankStatusCheck(${refundOrder.id})">编辑收款账户</a>
                                                </#if>
                                                <span class="is_hidden">
                                                <span>收起<img src="/static/img/zhan.png"></span>
                                                <span style="display: none">展开<img src="/static/img/shou.png"></span>
                                        </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!--商品体-->
                                    <div class="goods-box">
                                        <!--顶部表头-->
                                        <div class="tophead taocan-box">
                                            <ul>
                                                <li class="li1">商品信息</li>
                                                <li class="li5">数量</li>
                                                <li class="li3">退款金额</li>
                                                <li class="li4">合计</li>

                                            </ul>
                                        </div>
                                        <#assign no = 0 />
                                        <#assign totalPrice = 0 />
                                        <#if refundOrder.activityPackageList??>
                                            <#list refundOrder.activityPackageList as activityPackage>
                                                <!--套餐商品-->
                                                <div class="taocan-box">
                                                    <div class="tc-tit">套餐商品：</div>
                                                    <!--表体-->
                                                    <#list activityPackage.orderRefundVOList as refundOrderDetail>
                                                        <#assign no = no+1 />
                                                        <#assign totalPrice = totalPrice + (refundOrderDetail.productQuantity*refundOrderDetail.productPrice-refundOrderDetail.discountAmount) />
                                                        <div class="bodybox">
                                                            <!--列表-->
                                                            <ul>
                                                                <li class="lib1">
                                                                    <div class="l-box fl">
                                                                        <a href="${ctx }/search/skuDetail/${refundOrderDetail.productId}.htm">
                                                                            <img src="${productImageUrl}/ybm/product/min/${refundOrderDetail.imageUrl }"
                                                                                 alt="${refundOrderDetail.productName }"></a>
                                                                        <!--标签-->
                                                                        <#-- <div class="bq-box">
                                                                             <img src="img/bq-qiangguang.png" alt="">
                                                                         </div>-->
                                                                        <!--不参与返点提示-->
                                                                        <div class="nofd">
                                                                            ${refundOrderDetail.blackProductText}
                                                                        </div>
                                                                    </div>
                                                                    <div class="r-box fr">
                                                                        <div class="lib1-row1 text-overflow"
                                                                             style="width: 200px">
                                                                            <a href="${ctx }/search/skuDetail/${refundOrderDetail.productId}.htm"
                                                                               target="_blank">${refundOrderDetail.productName}</a>
                                                                        </div>
                                                                        <div class="lib1-row2 text-overflow">
                                                                            <span class="title">规　　格：</span>
                                                                            <span class="info">${refundOrderDetail.spec }</span>
                                                                        </div>
                                                                        <div class="lib1-row3 text-overflow">
                                                                            <#if refundOrderDetail.categoryFirstId ?? && refundOrderDetail.categoryFirstId == 100005>
                                                                                <span class="title">医疗器械注册证<br>或备案凭证编号：</span>
                                                                            <#else >
                                                                                <span class="title">批准文号：</span>
                                                                            </#if>
                                                                            <span class="info">${refundOrderDetail.approvalNumber }</span>
                                                                        </div>
                                                                        <div class="lib1-row4 text-overflow">
                                                                            <span class="title">生产厂家：</span>
                                                                            <span class="info">${refundOrderDetail.manufacturer }</span>
                                                                        </div>
                                                                    </div>
                                                                </li>
                                                                <li class="lib5">
                                                                    <spam>x</spam>
                                                                    <span>${refundOrderDetail.productQuantity}</span>
                                                                </li>
                                                                <li class="lib3">
                                                                    <#if refundOrder.refundMode!=4>
                                                                        <#assign totalAmount = (refundOrderDetail.productPrice * refundOrderDetail.productQuantity-refundOrderDetail.discountAmount)>
                                                                        <span>￥${totalAmount?string("#.##")}</span>
                                                                    </#if>
                                                                </li>
                                                                <li class="lib4">
                                                                    <#if !refundOrderDetail_has_next && refundOrder.refundMode!=4>
                                                                        <span>￥${totalPrice?string("#.##")}</span>
                                                                    </#if>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                        <!--表体-->
                                                    </#list>
                                                </div>
                                            </#list>
                                        </#if>
                                        <!--普通商品-->
                                        <#if refundOrder.orderRefundVOList??>
                                            <div class="bodybox">
                                                <!--列表-->

                                                <#list refundOrder.orderRefundVOList as refundOrderDetail>
                                                    <#assign no = no+1 />
                                                    <#assign totalPrice = totalPrice + (refundOrderDetail.productQuantity*refundOrderDetail.productPrice-refundOrderDetail.discountAmount)/>

                                                    <!--列表-->
                                                    <ul>
                                                        <li class="lib1">
                                                            <div class="l-box fl">
                                                                <a href="/search/skuDetail/${refundOrderDetail.skuId}.htm">
                                                                    <img src="${productImageUrl}/ybm/product/min/${refundOrderDetail.imageUrl}"
                                                                         alt="">
                                                                </a>
                                                                <!--不参与返点提示-->
                                                                <#if refundOrderDetail.balanceFlag ==0>
                                                                    <div class="bq-box">
                                                                        ${refundOrderDetail.blackProductText}
                                                                    </div>
                                                                </#if>
                                                            </div>
                                                            <div class="r-box fr">
                                                                <div class="lib1-row1 text-overflow"
                                                                     style="width: 200px">
                                                                    <a href="/search/skuDetail/${refundOrderDetail.skuId}.htm">${refundOrderDetail.productName}</a>
                                                                </div>
                                                                <div class="lib1-row2 text-overflow">
                                                                    <span class="title">规　　格：</span>
                                                                    <span class="info">${refundOrderDetail.spec}</span>
                                                                </div>
                                                                <div class="lib1-row3 text-overflow">
                                                                    <#if refundOrderDetail.categoryFirstId ?? && refundOrderDetail.categoryFirstId == 100005>
                                                                        <span class="title">医疗器械注册证<br>或备案凭证编号：</span>
                                                                    <#else >
                                                                        <span class="title">批准文号：</span>
                                                                    </#if>
                                                                    <span class="info">${refundOrderDetail.approvalNumber}</span>
                                                                </div>
                                                                <div class="lib1-row4 text-overflow">
                                                                    <span class="title">生产厂家：</span>
                                                                    <span class="info">${refundOrderDetail.manufacturer}</span>
                                                                </div>
                                                            </div>
                                                        </li>
                                                        <li class="lib5">
                                                            <spam>x</spam>
                                                            <span>${refundOrderDetail.productQuantity}</span>
                                                        </li>
                                                        <li class="lib3">
                                                            <#if refundOrder.refundMode!=4>
                                                                <#assign totalAmount = (refundOrderDetail.productPrice * refundOrderDetail.productQuantity-refundOrderDetail.discountAmount)>
                                                                <span>￥${totalAmount?string("#.##")}</span>
                                                            </#if>
                                                        </li>
                                                    </ul>
                                                </#list>
                                                <#if refundOrder.freightAmount??>
                                                    <ul>
                                                        <li class="lib1 someAmountSty" style="width:430px;">
                                                            <div class="l-box fl imgSty">
                                                                <a href="/search/skuDetail/62564565.htm">
                                                                    <img style="width: auto;height: auto;" src="/static/images/order/freight.png" alt="">
                                                                </a>
                                                            </div>
                                                            <div class="r-box fr textSty">运费</div>
                                                        </li>
                                                        <li class="lib5 someAmountSty">
                                                            <span></span>
                                                        </li>
                                                        <li class="lib3 someAmountSty numSty">
                                                            <span>￥${refundOrder.freightAmount}</span>
                                                        </li>
                                                    </ul>
                                                </#if>
                                                <!-- 额外赔偿 -->
                                                <#if refundOrder.indemnityMoney!=null && refundOrder.indemnityMoney!=0 && refundOrder.refundMode!=4>
                                                    <ul>
                                                        <li class="lib1 someAmountSty" style="width:430px;">
                                                            <div class="l-box fl imgSty">
                                                                <a href="/search/skuDetail/62564565.htm">
                                                                    <img style="width: auto;height: auto;" src="/static/images/order/compensate.png" alt="">
                                                                </a>
                                                            </div>
                                                            <div class="r-box fr textSty">额外赔偿</div>
                                                        </li>
                                                        <li class="lib5 someAmountSty">
                                                            <span></span>
                                                        </li>
                                                        <li class="lib3 someAmountSty numSty">
                                                            <span>￥${refundOrder.indemnityMoney}</span>
                                                        </li>
                                                    </ul>
                                                </#if>
                                                <div class="money-count">
                                                    ￥${refundOrder.refundFee}
                                                </div>
                                                <!-- 删除小额打款等功能 -->
                                            </div>
                                            <!-- 小额赔偿 -->
                                            <#if refundOrder.refundMode==4>
                                                <div class="bodybox">
                                                    <ul>
                                                        <li class="lib1 someAmountSty" style="width:430px;">
                                                            <div class="l-box fl imgSty">
                                                                <a href="/search/skuDetail/62564565.htm">
                                                                    <img style="width: auto;height: auto;" src="/static/images/order/smallPayment.png" alt="">
                                                                </a>
                                                            </div>
                                                            <div class="r-box fr textSty">小额赔偿</div>
                                                        </li>
                                                        <li class="lib5 someAmountSty">
                                                            <span></span>
                                                        </li>
                                                        <li class="lib3 someAmountSty numSty">
                                                            <span>￥${refundOrder.indemnityMoney}</span>
                                                        </li>
                                                    </ul>
                                                    <div class="money-count">
                                                        ￥${refundOrder.indemnityMoney}
                                                    </div>
                                                </div>
                                            </#if>
                                        </#if>
                                        <!-- 退款原因说明凭证 只显示用户发起 的退款原因和退款说明 -->
                                        <#if refundOrder.refundChannel == 1>
                                            <div class="tk-why">
                                                <!--售后类型-->
                                                <div class="leftbox" style="padding-left: 0;">
                                                    <div class="sub-l-box">售后类型：</div>
                                                    <#if refundOrder.afterSalesType?? && refundOrder.afterSalesType == 1>
                                                        <div class="sub-r-box">我要退款(无需退货)</div>
                                                        <#elseif refundOrder.afterSalesType?? && refundOrder.afterSalesType == 2>
                                                        <div class="sub-r-box">我要退货退款</div>
                                                        <#else>
                                                    </#if>
                                                </div>
                                                <!--退款原因-->
                                                <div class="leftbox">
                                                    <div class="sub-l-box">退款原因：</div>
                                                    <div class="sub-r-box">${refundOrder.refundReason}</div>
                                                </div>
                                                <!--退款说明-->
                                                <div class="middlebox">
                                                    <div class="sub-l-box">退款说明：</div>
                                                    <div class="sub-r-box">${refundOrder.refundExplain}</div>
                                                </div>
                                                <!--退款凭证-->
                                                <div class="rightbox">
                                                    <div class="sub-l-box">退款凭证：</div>
                                                    <div class="sub-r-box">
                                                        <#assign hasSpecifyImages = false>
                                                        <#if refundOrder??>
                                                            <#assign hasSpecifyImages = 
                                                                (refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.expressWaybillNumber?has_content) ||
                                                                (refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.damagedGoods?has_content) ||
                                                                (refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.expressOuterBoxList?? && refundOrder.orderRefundExtDto.expressOuterBoxList?size > 0) ||
                                                                (refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.problemDisplay?has_content) ||
                                                                (refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.physicalBatchNumber?has_content) ||
                                                                (refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.physicalPicture?has_content) ||
                                                                (refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.productImage?has_content)>
                                                        </#if>
                                                        <#if hasSpecifyImages>
                                                            <div class="specify-voucher-section" style="margin-bottom: 15px;">
                                                                <div style="font-weight: bold; margin-bottom: 10px; color: #333;">指定凭证：</div>
                                                                
                                                                <#-- 快递面单号 -->
                                                                <#if refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.expressWaybillNumber??>
                                                                    <div style="margin-bottom: 8px;">
                                                                        <span style="display: inline-block; width: 120px; color: #666;">快递面单号：</span>
                                                                        <a href="javascript:void(0)" class="photo">
                                                                            <#if refundOrder.orderRefundExtDto.expressWaybillNumber?starts_with("http")>
                                                                                <img src="${refundOrder.orderRefundExtDto.expressWaybillNumber}" alt="快递面单号" class="hao_img">
                                                                            <#else>
                                                                                <img src="${productImageUrl}${refundOrder.orderRefundExtDto.expressWaybillNumber}" alt="快递面单号" class="hao_img">
                                                                            </#if>
                                                                        </a>
                                                                    </div>
                                                                </#if>

                                                                <#-- 破损商品 -->
                                                                <#if refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.damagedGoods??>
                                                                    <div style="margin-bottom: 8px;">
                                                                        <span style="display: inline-block; width: 120px; color: #666;">破损商品：</span>
                                                                        <a href="javascript:void(0)" class="photo">
                                                                            <#if refundOrder.orderRefundExtDto.damagedGoods?starts_with("http")>
                                                                                <img src="${refundOrder.orderRefundExtDto.damagedGoods}" alt="破损商品" class="hao_img">
                                                                            <#else>
                                                                                <img src="${productImageUrl}${refundOrder.orderRefundExtDto.damagedGoods}" alt="破损商品" class="hao_img">
                                                                            </#if>
                                                                        </a>
                                                                    </div>
                                                                </#if>

                                                                <#-- 快递外箱（数组） -->
                                                                <#if refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.expressOuterBoxList??>
                                                                    <div style="margin-bottom: 8px;">
                                                                        <span style="display: inline-block; width: 120px; color: #666; vertical-align: top;">快递外箱：</span>
                                                                        <div style="display: inline-block;">
                                                                            <#list refundOrder.orderRefundExtDto.expressOuterBoxList as item>
                                                                                <#if item?? && item != ''>
                                                                                    <a href="javascript:void(0)" class="photo" style="margin-right: 5px;">
                                                                                        <#if item?starts_with("http")>
                                                                                            <img src="${item}" alt="快递外箱" class="hao_img">
                                                                                        <#else>
                                                                                            <img src="${productImageUrl}${item}" alt="快递外箱" class="hao_img">
                                                                                        </#if>
                                                                                    </a>
                                                                                </#if>
                                                                            </#list>
                                                                        </div>
                                                                    </div>
                                                                </#if>

                                                                <#-- 问题展示 -->
                                                                <#if refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.problemDisplay??>
                                                                    <div style="margin-bottom: 8px;">
                                                                        <span style="display: inline-block; width: 120px; color: #666;">问题展示：</span>
                                                                        <a href="javascript:void(0)" class="photo">
                                                                            <#if refundOrder.orderRefundExtDto.problemDisplay?starts_with("http")>
                                                                                <img src="${refundOrder.orderRefundExtDto.problemDisplay}" alt="问题展示" class="hao_img">
                                                                            <#else>
                                                                                <img src="${productImageUrl}${refundOrder.orderRefundExtDto.problemDisplay}" alt="问题展示" class="hao_img">
                                                                            </#if>
                                                                        </a>
                                                                    </div>
                                                                </#if>
                                                                
                                                                <#-- 实物批号 -->
                                                                <#if refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.physicalBatchNumber??>
                                                                    <div style="margin-bottom: 8px;">
                                                                        <span style="display: inline-block; width: 120px; color: #666;">实物批号：</span>
                                                                        <a href="javascript:void(0)" class="photo">
                                                                            <#if refundOrder.orderRefundExtDto.physicalBatchNumber?starts_with("http")>
                                                                                <img src="${refundOrder.orderRefundExtDto.physicalBatchNumber}" alt="实物批号" class="hao_img">
                                                                            <#else>
                                                                                <img src="${productImageUrl}${refundOrder.orderRefundExtDto.physicalBatchNumber}" alt="实物批号" class="hao_img">
                                                                            </#if>
                                                                        </a>
                                                                    </div>
                                                                </#if>
                                                                
                                                                <#-- 实物图片 -->
                                                                <#if refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.physicalPicture ??>
                                                                    <div style="margin-bottom: 8px;">
                                                                        <span style="display: inline-block; width: 120px; color: #666;">实物图片：</span>
                                                                        <a href="javascript:void(0)" class="photo">
                                                                            <#if refundOrder.orderRefundExtDto.physicalPicture?starts_with("http")>
                                                                                <img src="${refundOrder.orderRefundExtDto.physicalPicture}" alt="实物图片" class="hao_img">
                                                                            <#else>
                                                                                <img src="${productImageUrl}${refundOrder.orderRefundExtDto.physicalPicture}" alt="实物图片" class="hao_img">
                                                                            </#if>
                                                                        </a>
                                                                    </div>
                                                                </#if>
                                                                
                                                                <#-- 商品图片 -->
                                                                <#if refundOrder.orderRefundExtDto?? && refundOrder.orderRefundExtDto.productImage??>
                                                                    <div style="margin-bottom: 8px;">
                                                                        <span style="display: inline-block; width: 120px; color: #666;">商品图片：</span>
                                                                        <a href="javascript:void(0)" class="photo">
                                                                            <#if refundOrder.orderRefundExtDto.productImage?starts_with("http")>
                                                                                <img src="${refundOrder.orderRefundExtDto.productImage}" alt="商品图片" class="hao_img">
                                                                            <#else>
                                                                                <img src="${productImageUrl}${refundOrder.orderRefundExtDto.productImage}" alt="商品图片" class="hao_img">
                                                                            </#if>
                                                                        </a>
                                                                    </div>
                                                                </#if>
                                                            </div>
                                                        </#if>
                                                        
                                                        <#-- 其他凭证区域（保持原有逻辑） -->
                                                        <#if refundOrder.imageList?? && (refundOrder.imageList?size > 0)>
                                                            <div class="other-voucher-section">
                                                                <#if hasSpecifyImages>
                                                                    <div style="font-weight: bold; margin-bottom: 10px; color: #333;">其他凭证：</div>
                                                                </#if>
                                                                <#list refundOrder.imageList as item>
                                                                    <#if (item?? && item !='')>
                                                                        <a href="javascript:void(0)" class="photo">
                                                                            <#if item?starts_with("http")>
                                                                                <img src="${item}" alt="" class="hao_img">
                                                                            <#else>
                                                                                <img src="${productImageUrl}${item}" alt="" class="hao_img">
                                                                            </#if>
                                                                        </a>
                                                                    </#if>
                                                                </#list>
                                                            </div>
                                                        </#if>
                                                        
                                                        <#-- 如果没有任何凭证 -->
                                                        <#if !hasSpecifyImages && (!refundOrder.imageList?? || refundOrder.imageList?size == 0)>
                                                            <span style="color: #999;">暂无凭证</span>
                                                        </#if>
                                                    </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </#if>
                                        <!--退款账户信息-->
                                        <#if refundOrder.orderRefundBankBusinessDto.id??>
                                            <div class="refund-mes">
                                                <ul>
                                                    <li>
                                                        <span>开户行及支行：</span>
                                                        <span>${refundOrder.orderRefundBankBusinessDto.bankName}</span>
                                                    </li>
                                                    <li>
                                                        <span>银行卡号：</span>
                                                        <span>${refundOrder.orderRefundBankBusinessDto.bankCard}</span>
                                                    </li>
                                                    <li>
                                                        <span>开户人：</span>
                                                        <span>${refundOrder.orderRefundBankBusinessDto.owner}</span>
                                                    </li>
                                                    <li>
                                                        <span>联系电话：</span>
                                                        <span>${refundOrder.orderRefundBankBusinessDto.cellphone}</span>
                                                    </li>
                                                </ul>
                                            </div>
                                        </#if>

                                        <#if refundOrder.orderRefundExpressBusinessDto.id??>
                                            <div class="refund-mes">
                                                <ul>
                                                    <li>
                                                        <span>快递名称：</span>
                                                        <span>${refundOrder.orderRefundExpressBusinessDto.expressName}</span>
                                                    </li>
                                                    <li>
                                                        <span>快递单号：</span>
                                                        <span>${refundOrder.orderRefundExpressBusinessDto.expressNo}</span>
                                                    </li>
                                                    <li>
                                                        <span style="vertical-align: top;">运单截图：</span>
                                                        <#if refundOrder.orderRefundExpressBusinessDto.expressNo?? && refundOrder.orderRefundExpressBusinessDto.expressEvidence??>
                                                            <a href="javascript:void(0);" class="photo"><img
                                                                        src="${productImageUrl}${refundOrder.orderRefundExpressBusinessDto.expressEvidence}"
                                                                        alt="" style="width: 100px;"></a>
                                                        </#if>
                                                    </li>
                                                </ul>
                                            </div>
                                        </#if>

                                        <#if refundOrder.auditState==-1 || refundOrder.cancelChannel==2>
                                            <div class="refund-mes">
                                                <ul>
                                                    <li>
                                                        <span>关闭时间：</span>
                                                        <span>
                                                        ${refundOrder.closeTime}
                                                    </span>
                                                    </li>
                                                    <li>
                                                        <span>关闭原因：</span>
                                                        <span>
                                                     ${refundOrder.closeReason}
                                                    </span>
                                                    </li>
                                                </ul>
                                            </div>
                                        </#if>
                                    </div>
                                </div>
                            </#list>
                            </#if>
                        </div>
                    </div>
                </div>
                <!--温馨提示-->
                <div class="b-infobox">
                    <i class="sui-icon icon-pc-info-circle"></i> <span>退款温馨提示：</span>
                    <p>1、退款金额不含活动优惠金额。</p>
                    <p>2、所有退款原路返回，可能会因为银行不同的银行卡可能要1-3个工作日，若有退款延迟还请您耐心等待。</p>
                    <p>3、我们的客服将会第一时间与您联系确认退货产品，退货金额和退货数量。一切关于提货给您带来的不便，敬请谅解。药帮忙竭诚为您服务！</p>

                </div>
            </div>
        </div>
        <!--主体部分结束-->

        <!--底部导航区域开始-->
        <div class="footer" id="footer">
				<#include "/common/footer.ftl" />
        </div>
        <!--底部导航区域结束-->
        <!--客服入口开始-->
        <div class="kefu-box">
            <a href="javaScript:callKf('','${merchant.id}');">
                <img src="/static/images/kefu-online.png" alt="">
            </a>
        </div>
        <!--客服入口结束-->

        <!--退款进度详情弹出框-->
        <div id="refundStatusDetil" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 id="myModalLabel" class="modal-title">退款状态详情</h4>
                    </div>
                    <div class="modal-body" style="min-height: 150px;">
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large">确定</button>
                    </div>

                </div>
            </div>
        </div>
    </div>
    </body>
    <script type="text/javascript">
        $(function () {
            $(".zhan-tk").click(function () {
                $(this).css("display", "none");
                $(this).siblings(".shou-tk").css("display", "block");
                $(this).parent().addClass("up").removeClass("down");
            });
            $(".shou-tk").click(function () {
                $(this).css("display", "none");
                $(this).siblings(".zhan-tk").css("display", "block");
                $(this).parent().addClass("down").removeClass("up");
            })

        })
        /*点击放大*/
        $(".photo").click(function () {
            var imgUrl = $(this).find("img").attr("src");
            var html = '<img src="' + imgUrl + '" />';
            if (imgUrl) {
                $.alert({
                    backdrop: true,
                    keyboard: true,  //是否可由esc按键关闭
                    title: '',
                    body: html, //必填
                    hasfoot: false,
                    width: 'large'
                })
            }
        })

        function jumpPlatformHandle(refundOrderNo, orderNo){
             $.ajax({
                type: "GET",
                url: "/intervention/getRedirectUrl",
                data: {
                    afsNo:refundOrderNo,
                    orderNo:orderNo
                },
                contentType: "application/json",
                dataType: "json",
                success: function (data) {
                        if(data.status=="success"){
                         window.open(data.data.data)  
                       }else{
                         $.alert(data.errorMsg);
                       }
                   
                }
            });

        }

        function cancelRefundOrder(refundOrderId,auditProcessState) {
            console.log(auditProcessState);
            if(auditProcessState === 0){
                console.log(auditProcessState);
                $.confirm({
                    title: '提示',
                    body:'确认取消当前退款吗？',
                    okBtn : '确定',
                    cancelBtn : '我在想想',
                    okHidden : function(e){
                        $.ajax({
                            type: "GET",
                            url: "/merchant/center/order/cancelRefundOrder",
                            data: {"refundOrderId": refundOrderId,cancelChannel: 2},
                            dataType: "json",
                            success: function (data) {
                                if (data.status === "success") {
                                     location.reload();
                                } else {
                                    $.alert({
                                        title: '提示',
                                        body:'取消退款失败'
                                    });
                                }
                            }
                        });
                    }
                });
            }else{
                $.alert({
                    title: '提示',
                    body: '当前退款单客服已受理，如需取消请联系客服人员'
                });
            }
        }

        function editExpressStatusCheck(refundId){
            $.ajax({
                url: '/merchant/center/order/findRefundOrderStatus',
                type: "get",
                dataType: "json",
                traditional: true,
                async: false,
                data: {"refundId":refundId},
                success: function (data) {
                    if (data.status === "success") {
                        if (data.data.checkExpressState==0) {
                            $.alert({
                                body: '当前退款单仓库已处理，如需提供退货物流信息或有其他问题请联系客服人员!',
                                title: '  <i class="sui-icon icon-pc-info-circle"></i>提示',
                                okBtn: '确认',
                                hide:function(){
                                    location.reload();
                                }
                            })
                        }else {
                            window.location.href="/merchant/center/order/editOrderRefundExpress/"+refundId+".htm"
                        }
                    } else {
                        $.alert({
                            body: '当前退款单仓库已处理，如需提供退货物流信息或有其他问题请联系客服人员!',
                            title: '  <i class="sui-icon icon-pc-info-circle">result.data.errorMsg</i>提示',
                            okBtn: '确认',
                        })
                    }
                }
            });
        }

        function editBankStatusCheck(refundId){
            $.ajax({
                url: '/merchant/center/order/findRefundOrderStatus',
                type: "get",
                dataType: "json",
                traditional: true,
                async: false,
                data: {"refundId":refundId},
                success: function (data) {
                    if (data.status === "success") {
                        if (data.data.checkBankState==0) {
                            $.alert({
                                body: '当前退款单客服已受理，如需修改请联系客服人员!',
                                title: '  <i class="sui-icon icon-pc-info-circle"></i>提示',
                                okBtn: '确认',
                                hide:function(){
                                    location.reload();
                                }
                            })
                        }else {
                            window.location.href="/merchant/center/order/editOrderRefundBank/"+refundId+".htm";
                        }
                    } else {
                        $.alert({
                            body: '当前服务器繁忙，请稍后再试!',
                            title: '  <i class="sui-icon icon-pc-info-circle">result.data.errorMsg</i>提示',
                            okBtn: '确认',
                        })
                    }
                }
            });
        }

        $(".center_xuan").click(function () {
            var xuan = $(this).parent().siblings(".center_tao").css("display", "none")
            var xuan1 = $(this).css("display", "none")
            var xuan2 = $(this).siblings(".center_xuan1").css("display", "block")
        })
        $(".center_xuan1").click(function () {
            var xuan = $(this).parent().siblings(".center_tao").css("display", "block")
            var xuan1 = $(this).css("display", "none")
            var xuan2 = $(this).siblings(".center_xuan").css("display", "block")
        })


        $('.refund-detail').on('click',function() {
            var refundId =  $(this).data('id');
            console.log(refundId)
            $.ajax({
                url: '/merchant/center/order/refundStatusDetail/' + refundId + '.htm' ,
                type: "get",
                dataType: "html",
                traditional: true,
                async: false,
                data: {},
                success: function (result) {
                    console.log(result,'---result---');
                    $("#refundStatusDetil .modal-body").html(result);
                    $("#refundStatusDetil").modal({
                        show:true
                    })
                }
            });
        })
        // 展开和收起商品列表
        $(function () {
            $(".is_hidden").click(function () {
                console.log($(this).children("span")[0].style.display)
                if ($(this).children("span")[0].style.display === "none") {
                    //展示
                    $(this).children("span")[0].style.display = "block";
                    $(this).children("span")[1].style.display = "none";
                    $(this).parent().parent().parent().siblings(".goods-box").show()
                } else {
                    //隐藏
                    $(this).children("span")[0].style.display = "none"
                    $(this).children("span")[1].style.display = "block"
                    $(this).parent().parent().parent().siblings(".goods-box").hide()
                }
            })
        });
    </script>
    <script src="/static/js/vue.js?t=${t_v}"></script>
    <script src="/static/js/element.index.js?t=${t_v}""></script>
    <script>
        // countDown
        window.onload = function () {
          var nowSecond = $('#countDown').data('id');
          if (nowSecond) {
            nowSecond = +nowSecond;
            function countDown(second) {   
                if (second < 1) {
                    $('#countDown').html('0');
                    return false;
                }                    //传入秒
                var minute=0;                    //分钟
                var hour=0;                    //小时
                var day=0;                    //天
                minute = parseInt(second/60); 		//算出一共有多少分钟
                second%=60;							//算出有多少秒
                if(minute>60) { 					//如果分钟大于60，计算出小时和分钟
                    hour = parseInt(minute/60);
                    minute%=60;						//算出有多分钟
                }
                if(hour>24){						//如果小时大于24，计算出天和小时
                    day = parseInt(hour/24);
                    hour%=24;						//算出有多分钟
                }
                second = second>9?second:"0"+second;
                minute = minute>9?minute:"0"+minute;
                hour = hour>9?hour:"0"+hour;
                $('#countDown').html(day+"天"+hour+"小时"+minute+"分钟"+second+"秒"); //返回剩余天小时分钟秒
                nowSecond = nowSecond - 1;
                setTimeout(function() {
                    countDown(nowSecond);
                }, 1000)
            }
            countDown(nowSecond);
          }
        }
    </script>
</html>