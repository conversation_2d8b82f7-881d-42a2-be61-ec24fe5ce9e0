<!DOCTYPE HTML>
<html>

<head>
    <#include "/common/common.ftl" /> 
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>结算页</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="/static/css/order/settle.css?v=${t_v}" />
    <style>
    .gwj-tips{
        cursor: pointer;
        font-size: 10px;
        text-align: center;
        display: inline-block;
        border-radius: 50%;
        width: 15px;
        height: 15px;
        line-height: 15px;
        border: 1px solid #a9a8a8;
    }
    #fullGiveModal .i-dialog-mask {
        position: fixed;
        width: 100%;
        height: 100%;
        top:0;
        background-color: #0000007a;
    }
    #fullGiveModal div {
        box-sizing: border-box;
        font-size: 14px;
    }
    #fullGiveModal .i-dialog-box {
        position: fixed;
        width: 600px;
        min-height: 450px;
        background-color: white;
        left: 50%;
        top: 50%;
        transform:translate(-50%, -50%);
        border-radius: 5px;
        overflow: hidden;
        display: flex;
        flex-direction: column
    }
    .i-dialog-box p {
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 最多显示两行 */
        -webkit-box-orient: vertical; /* 垂直布局 */
        overflow: hidden; /* 隐藏多余内容 */
        text-overflow: ellipsis; /* 用省略号表示多余内容 */
    }
    .i-dialog-box .i-title,
    .i-dialog-box .i-footer {
        flex-shrink: 0;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px;
    }
    .i-dialog-box .i-footer .i-submit,
    .i-dialog-box .i-footer .i-close {
        padding: 5px 25px;
        border-radius: 3px;
        cursor: pointer;
    }
    .i-dialog-box .i-footer .i-close {
        border: solid 1px #dfdfdf;
    }
    .i-dialog-box .i-footer .i-submit {
        background-color: #31cf60;
        border: solid 1px #26bb53;
        color: white;
    }
    .i-dialog-box .i-content {
        flex-grow: 1;
        flex-shrink: 0;
        width: 100%;
        border-top: solid 1px #dfdfdf;
        padding: 0 5px;
    }
    @supports not (gap: 10px) {
        .i-info {
            margin: 0 10px;
        }
        .i-close {
            margin-right: 10px;
        }
    }
    @supports (appearance: none) {
        input[type="checkbox"] {
            position: relative;
            width: 14px;
            height: 14px;
            border-radius: 3px;
            margin: 0;
            border: solid 1px #d2d2d2;
            cursor: pointer;
            outline: none;
            appearance: none;
        }
        input[type="checkbox"]:checked {
            background-color: #31cf60;
            border: solid 1px #26bb53;
        }
        input[type="checkbox"]:checked::after {
            content: '\2714';
            font-size: 12px;
            color: white;
            position: absolute;
            transform: translate(1px,-2px);
        }
    }
    .i-actList {
        width: 100%;
        overflow-x: auto;
    }
    .i-actList > div {
        position: relative;
        flex-shrink: 0;
        width: 100px;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin: 0 10px;
        text-align: center;
    }
    .i-actStatus {
        position: absolute;
        font-size: 10px !important;
        color: white;
        padding: 0 3px;
        border-radius: 3px;
        display: inline-block;
        top: -10px;
        right: -10px;
        width: max-content;
        background: #a56464;
    }
    .i-actList-active {
        color:#26bb53;
    }
    #productList {
        overflow-y: auto;
        max-height: 500px;
        padding: 10px;
        transition: all 0.3s;
        -webkit-transition: all 0.3s;
        /* scrollbar-width: thin;
        scrollbar-color: #ccc #f1f1f1; */
    }
    #productList .i-item {
        width: 100%;
        display: flex;
        align-items: center;
        padding: 15px 5px;
        cursor: pointer;
        transition: all 0.2s;
        border-bottom: solid 1px #e4e4e4;
    }
    #productList .i-item:hover {
        border-bottom-color: #0da600;
    }
    #productList .i-item > div {
        flex-grow: 0;
        flex-shrink: 0;
    }
    #productList .i-item > div:nth-child(2) {
        flex-grow: 1;
    }
    #productList .i-item .i-info {
        display: flex;
        gap: 10px;
        margin: 0 10px;
    }
    #productList .i-item .i-info .i-img {
        position: relative;
        width: 70px;
        height: 70px;
        object-fit: cover;
        overflow: hidden;
        border-radius: 5px;
        flex-shrink: 0;
    }
    #productList .i-item .i-numBtn {
        position: relative;
        height: 30px;
        display: flex;
        border: solid 1px #d2d2d2;
        border-radius: 3px 3px 0 0;
        align-items: center;
        color: #8b8b8b;
    }
    #productList .i-item .i-numBtn + div {
        background-color: #f0f0f0;
        padding: 3px;
        color: #686868;
        font-size: 12px;
        border-radius: 0 0 3px 3px;
    }
    #productList .i-item .i-numBtn input {
        width: 70px;
        text-align: center;
        border: none;
        font-size: 16px;
        color: #8b8b8b;
        background-color: transparent;
        outline: none;
    }
    #productList .i-item .i-numBtn > div {
        width: 30px;
        height: 100%;
        text-align: center;
    }
    #productList .i-item .i-numBtn > div:first-child {
        border-right: solid 1px #d2d2d2;
    }
    #productList .i-item .i-numBtn > div:last-child{
        border-left: solid 1px #d2d2d2;
    }
    .i-dialog-box ::-webkit-scrollbar-button {
        display: none !important;
    }
    .i-dialog-box ::-webkit-scrollbar {
        width: 5px;
        height: 5px;
        cursor: pointer;
    }
    .i-dialog-box ::-webkit-scrollbar-thumb {
        background-color: #d7d7d7;
        border-radius: 8px;
        cursor: pointer;
    }
    .i-disabled {
        background: #ededed;
        cursor: not-allowed;
    }
	#xx-pay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: #27292b8a;
		z-index: -10;
		opacity: 0;
		transition: all 0.3s;
	}
	#xx-pay .xx-input-box {
		position: relative;
		width: max-content;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50px);
		background: white;
		border-radius: 5px;
		padding: 20px;
	}
	#xx-pay .xx-title {
		display: flex;
		justify-content: space-between;
		margin: 10px 0;
		align-items: center;
	}
	#xx-pay .xx-input-box .xx-input {
		outline: none;
		text-align: center;
		border-radius: 5px;
		border: 1px #cdcdcd solid;
		font-size: 30px;
		padding: 0;
		width: 50px;
		height: 50px;
		transition: all 0.1s;
	}
	#xx-pay-submit {
		width: max-content;
		padding: 10px 40px;
		margin: 0 auto;
		background: #2bd975;
		color: white;
		border-radius: 5px;
		cursor: pointer;
	}
    .main1 {
        background: #F2F2F2;
    }

    .fd-warp .list-item span.spec {
        width: 240px;
    }

    .default_address {
        margin-top: 15px;
        color: #999999;
    }

    .isdefaultSec.checkbox-pretty>span {
        position: relative;
    }

    .isdefaultSec.checkbox-pretty>span:before {
        content: "";
        width: 15px;
        height: 15px;
        display: block;
        border: 1px solid #999999;
        border-radius: 2px;
        top: 0px;
        left: -20px;
        font-size: 0px;
        margin: 0px;
        margin-right: 5px;
        position: absolute;
    }

    .isdefaultSec.checkbox-pretty.checked>span:before {
        content: "✓";
        width: 17px;
        height: 17px;
        display: block;
        border: 0px solid black;
        border-radius: 2px;
        text-align: center;
        line-height: 17px;
        top: 0px;
        left: -20px;
        background: #00dc82;
        font-size: 14px !important;
        font-weight: 600;
        color: #fff !important;
        margin: 0px;
        margin-right: 5px;
        position: absolute;
    }

    .sui-tooltip {
        border: 1px solid #eee;
        border-radius: 8px;
    }

    .sui-tooltip.bottom .tooltip-arrow,
    .tooltip-only-arrow.bottom .tooltip-arrow {
        border-bottom-color: #eee;
    }

    .tooltip-inner {
        line-height: 25px;
    }

    .sui-tooltip.default .tooltip-inner,
    .sui-tooltip.normal .tooltip-inner,
    .sui-tooltip.confirm .tooltip-inner {
        color: #333;
    }

    .cg-yhq {
        position: absolute;
        display: none;
        top: 60px;
        width: 900px;
        background: #FFFFFF;
        border-radius: 5px;
        z-index: 1008;
        box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
        border: 1px solid rgba(238, 238, 238, 1);
    }

    .cg-yhq .address-top-title {
        margin-top: 21px;
        margin-left: 19px;
    }

    .cg-yhq ul {
        padding-left: 10px;
    }

    .yhq-common {
        overflow: hidden;
    }

    .yhq-common li {
        margin: 10px 0 10px 10px;
        height: 120px;
    }

    .yhq-san {
        position: absolute;
        top: -10px;
        left: 50%;
        line-height: 0px;
        font-size: 40px;
        z-index: -1;
        width: 15px;
        height: 15px;
        margin-left: -15px;
        background: #fff;
        border: 1px solid rgba(238, 238, 238, 1);
        border-bottom: 1px solid #fff;
        border-right: 1px solid #fff;
        transform: rotate(45deg);
    }

    .tanbox {
        max-height: 300px;
        min-height: 140px;
        overflow-y: scroll;
    }

    .tanbox::-webkit-scrollbar {
        width: 2px;
    }

    .tanbox::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(0, 0, 0, 0.1);
        /*-webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);*/
    }

    .defaultH {
        max-height: 705px;
        overflow: hidden;
    }

    .shop-item {
        background: #ffffff;
    }

    .xyy-confirm {
        display: none;
        background: #fff;
        position: absolute;
        top: 50%;
        left: 50%;
        width: 300px;
        height: 150px;
        margin-left: -150px;
        margin-top: -75px;
        z-index: 9999;
    }

    .xyy-confirm .xyy-header {
        height: 30px;
        line-height: 30px;
        padding: 0 15px;
        background: #00dc82;
        color: #fff;
    }

    .xyy-confirm .xyy-footer {
        position: absolute;
        width: 100%;
        bottom: 0;
        height: 40px;
        line-height: 40px;
        border-top: 1px solid #f5f5f5;
        text-align: right;
    }

    .xyy-confirm .xyy-footer button {
        padding: 0 10px;
        height: 30px;
        line-height: 30px;
        border: none;
        margin: 0 5px;
        outline: none;
    }

    .xyy-confirm .xyy-footer button.ok-btn {
        color: #fff;
        background: #00dc82;
    }

    .xyy-confirm .xyy-footer button.cancel-btn {
        color: #666;
        background: #fff;
        border: 1px solid #f5f5f5;
    }

    .xyy-confirm .xyy-body {
        height: 59px;
        padding: 10px;
    }

    .xyy-cover {
        display: none;
        position: fixed;
        background: #222;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        opacity: 0.5;
        filter: alpha(opacity=50);
        z-index: 9998;
    }

    .kuaidi {
        margin-left: 60px;
        margin-top: 10px;
        color: #e73734;
        font-size: 12px;
    }

    .tuijian {
        /*margin-left: 750px;*/
        color: #fff;
        padding: 5px 10px;
        border-radius: 5px;
        background: #00dc7d;
        display: inline-block;
        cursor: pointer;
        position: absolute;
        right: 40px;
    }

    /*优惠券弹窗*/
    .youhuiquan-sum .cg-yhq {
        position: absolute;
        display: none;
        top: 50px;
        left: -619px;
        width: 900px;
        background: #FFFFFF;
        border-radius: 5px;
        z-index: 1008;
        box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
        border: 1px solid rgba(238, 238, 238, 1);
    }

    .youhuiquan-sum .yhq-san {
        position: absolute;
        top: -10px;
        left: 677px;
        line-height: 0px;
        font-size: 40px;
        z-index: -1;
        width: 15px;
        height: 15px;
        margin-left: -15px;
        background: #fff;
        border: 1px solid rgba(238, 238, 238, 1);
        border-bottom: 1px solid #fff;
        border-right: 1px solid #fff;
        transform: rotate(45deg);
    }

    .js-tip {
        font-size: 12px;
        color: #ff8e29;
        padding: 10px 12px;
        border-top: 1px solid #f1f1f1;
        clear: both;
    }

    .redStr {
        color: red;
        padding: 0 5px;
    }

    .checkboxLabel {
        position: relative;
        cursor: pointer;
    }

    .virtualGoldInput {
        cursor: pointer;
    }

    .virtualGoldInput:checked+.virtualGoldCheckbox {
        background-color: #00dc82;
    }
    .virtualGoldInput:checked+.virtualGoldCheckboxDisable {
        background-color: #00dc82;
    }

    .virtualGoldCheckbox {
        position: absolute;
        top: 2px;
        left: 0;
        width: 16px;
        height: 16px;
        border-radius: 16px;
        border: 1px solid #d8d8d8;
        background: white;
    }

    .virtualGoldCheckbox:before {
        content: '';
        position: absolute;
        top: 2px;
        left: 6px;
        width: 3px;
        height: 8px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }
    .virtualGoldCheckboxDisable {
        position: absolute;
        top: 2px;
        left: 0;
        width: 16px;
        height: 16px;
        border-radius: 16px;
        border: 1px solid #d8d8d8;
        background: #e8e8e8;
    }

    .virtualGoldCheckboxDisable:before {
        content: '';
        position: absolute;
        top: 2px;
        left: 6px;
        width: 3px;
        height: 8px;
        border: solid #e8e8e8;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    .redPacketInput {
        cursor: pointer;
    }

    .redPacketInput:checked+.redPacketCheckbox {
        background-color: #00dc82;
    }

    .redPacketCheckbox {
        position: absolute;
        top: 2px;
        left: 0;
        width: 16px;
        height: 16px;
        border-radius: 16px;
        border: 1px solid #d8d8d8;
        background: white;
    }

    .redPacketCheckbox:before {
        content: '';
        position: absolute;
        top: 2px;
        left: 6px;
        width: 3px;
        height: 8px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    .alert-title-row {
        padding: 0px 10px;
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .alert-title-row-top {
        padding: 0px 10px;
        width: calc(100% - 20px);
        height: 40px;
        display: flex;
        flex-direction: row;
        align-items: center;
        background: #F5F5F5;
    }

    .alert-title-icon {
        margin-right: 10px;
        width: 3px;
        height: 16px;
        background: #00B955;
        border-radius: 1.5px;
    }

    .alert-title-text {
        height: 20px;
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 14px;
        color: #222222;
        text-align: left;
    }

    .alert-tag-row {
        width: calc(100% - 20px);
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }

    .alert-tag-item {
        width: 112px;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid #DDDDDD;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin: 3px 5px;
        ;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        letter-spacing: 0.59px;
    }

    .alert-tag-item-checked {
        border: 1px solid green;
        color: green;
    }

    .alert-list-row-top {
        width: calc(100% - 20px);
        height: 40px;
        background: #FFFFFF;
        border: 1px solid #EEEEEE;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding-left: 18px;
    }

    .alert-list-row-top-search {
        width: 200px;
        height: 24px;
        background: #FFFFFF;
        border: 1px solid #D6D6D6;
        border-radius: 2px;
    }

    .li-searchUl {
        border: 1px solid #cccccc;
        position: absolute;
        top: 230px;
        left: 36px;
        width: 649px;
        background: #fff;
        z-index: 9999999;
        display: none;
    }

    .alert-customer-list-content {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-left: 1px solid #EEEEEE;
        border-right: 1px solid #EEEEEE;
        border-bottom: 1px solid #EEEEEE;
        border-top: 1px solid #EEEEEE;
        width: calc(100% - 45px);
        align-items: center;
        padding: 10px 20px;
    }

    .alert-customer-list-content-tc {
        display: flex;
        flex-direction: column;
    }

    .alert-customer-list-content-tc-content {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: calc(100% - 45px);
        align-items: center;
        padding: 10px 20px;
    }

    .alert-row {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .checkbox-selected {
        background-color: green;
    }

    .alert-customer-list {
        overflow-y: scroll;
        overflow-x: hidden;
        height: 300px;
    }

    .alert-list-row-title {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 14px;
        color: #292933;
        letter-spacing: 0;
    }

    .alert-list-row-mintitle {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 12px;
        color: #676773;
        letter-spacing: 0;
    }

    .showBox {
        display: none;
        z-index: 100;
        padding: 10px;
        position: fixed;
        top: 40%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: #FFFFFF !important;
        border-radius: 5px;
    }

    .dialog_box {
        display: none;
        width: 100%;
        height: 100%;
        z-index: 99;
        position: fixed;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, .5) !important;
    }

    .open-box-class {
        display: flex;
        flex-direction: column;
    }

    .open-box-row-class {
        display: flex;
        flex-direction: row;
    }
    .xxTip{
        display: none;
        color: #FF4603;
        font-size: 12px;
    }
    .next-day-service-span {
        margin-left: 5px;
        color: #333;
        float: none;
    }
    .transaction{
        font-size: 20px;
        text-align: center;
        margin-right: 0px !important;
        padding-top: 20px  !important;
        font-family: PingFangSC-Medium;
        font-weight: 500 !important;
        color: #333333 !important;
        background: #FFFFFF !important;
    }
    .transactionOk{
        width: 300px !important;
        margin: 0 auto;
        border:none !important;
        margin-right: 0px !important;
        background:#00B955 !important;
        height: 40px !important;
    }
    .transactionNo{
        background:#7FDCAA !important;
        height: 40px !important;
    }
    .fxzhifu{
        font-size: 14px !important;
        font-family: PingFangSC-Regular;
        color: #333333 !important;
    }
        .tooltip-zf {
        position: absolute;
        padding: 6px 12px;
        background: #303133;
        color: #fff;
        border-radius: 4px;
        font-size: 14px;
        white-space: nowrap; /* 确保文字不换行 */
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.3s, visibility 0.3s;
        z-index: 1000;
        }

        /* 顶部靠左浮窗样式 */
        .tooltip-zf.top-left {
        bottom: 100%;
        left: 0;
        margin-bottom: 10px;
        }

        /* 靠左的三角箭头 */
        .tooltip-zf.top-left::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 15px; /* 调整这个值可以改变箭头位置 */
        border-width: 5px;
        border-style: solid;
        border-color: #303133 transparent transparent transparent;
        }
    </style>
    <script type="text/javascript" src="/static/js/jquery.cityselect.js?t=${t_v}"></script>
    <script type="text/javascript" src="/static/js/order/settle20200326.js?v=${t_v}"></script>
    <script type="text/javascript" src="/static/js/qtShopDetail.js?t=${t_v}"></script>
    <script type="text/javascript">
    var ctx = "${ctx}";
    var shoppingCartImgUUID = "${shoppingCartImgUUID}";
    $('.xiala-all').click(function() {
        $(this).parent(".shop-item").find(".cg-yhq").slideDown();
    })
    $('.body').click(function() {
        $(".cg-yhq").slideUp();
    })
    // 从地址栏获取参数的函数
    function getUrlParam(key, defaultValue = '') {
        // 优先级1：使用 URLSearchParams（现代浏览器）
        if (window.URLSearchParams) {
            const params = new URLSearchParams(window.location.search);
            const value = params.get(key);
            return value !== null ? value : defaultValue;
        }
        // 优先级2：旧浏览器解析方案（兼容IE）
        const query = window.location.search.substring(1);
        const pairs = query.split('&');
        for (const pair of pairs) {
            const [k, v] = pair.split('=');
            if (decodeURIComponent(k) === key) {
                return decodeURIComponent(v) || defaultValue;
            }
        }
        return defaultValue;
    }
    //领券提示
    var msg = '${claimVoucherMsg}';
        if(msg && !getUrlParam('loadCount')){
            $(function() {
            if (msg) {
                $.alert({
                    "title": "提示",
                    "body": msg,
                    hide: function() {}
                });
            }
        })
    }
    </script>
</head>

<body>
    <div class="container">
        <input type="hidden" id="divmore" value="0">
        <input type="hidden" id="tranNo" value="${orderSettle.tranNo}">
        <input type="hidden" id="orderMoney" value="${orderSettle.money}">
        <input type="hidden" id="unpcaTips" value="${orderSettle.unpcaTips}">
        <input type="hidden" id="isHasPop" value="${orderSettle.hasPopPay2Seller}">
        <input type="hidden" id="isSupportMultiple" value="${orderSettle.isSupportMultiple}">
        <!--头部导航区域开始-->
        <div class="headerBox" id="headerBox">
            <#include "/common/header.ftl" />
        </div>
        <!--头部导航区域结束-->
        <!--结算页面头部导航-->
        <div class="topbzbox">
            <div class="warp">
                <div class="bzcol1"><a href="/"><img src="/static/images/logo_login.png"></a></div>
                <div class="bzcol2"></div>
                <div class="bzcol3">结算</div>
                <div class="bzcol4"><img src="/static/images/buzhoujiesuan.png"></div>
            </div>
        </div>
        <!--主体部分开始-->
        <div class="main1">
            <!--资质状态提示信息-->
            <div class="sui-msg msg-large msg-tips" id="licenseMsg" style="display:none;font-size: 14px;background:#fff3ce;padding:11px 20px;">
                <div style="width:1200px;margin:0 auto;color: #ff8e29;font-weight:400;">
                    <span class="main-msg" style="margin-right: 20px;"></span>
                    <a id="updateLicense" href="/merchant/center/licenseAudit/findLicenseCategoryInfo.htm" style="color:#ff8e29;">点击去更新资质&nbsp;></a>
                </div>
            </div>
            <!--提示文案-->
            <div class="titleTips" style=" width: 1200px; margin: 10px auto;">
                <div class="sui-msg msg-default msg-notice" style="position: relative;">
                    <i class="sui-icon icon-touch-noti-circle"
                        style="position: absolute;top:10px;left:10px;font-size:22px;"></i>
                    <div class="msg-con" style="padding:10px 40px;background: #FFFDF4;color: #FBA475">
                        声明：为严格执行《药品管理法》及《药品经营质量管理规范》的相关规定，送货地址默认为《药品经营许可证》中的经营地址或仓库地址，并不能随意修改。若地址有误，请联系客服：400-0505-111
                    </div>
                </div>
            </div>
            <!--支付主体内容-->
            <div class="pay-content" style="margin: 0px auto;padding: 10px 25px 0 25px;">
                <!--填写并核对订单信息-->
                <#--<div class="p-title">填写并核对订单信息：</div>-->
            <!--收货人信息-->
            <div class="recebox">
                <div class="address-top-title">收货人信息</div>
                <!--地址列表-->
                <div class="default-show">
                    <#if (shippingAddressList?size>0)>
                        <#list shippingAddressList as shippingAddress>
                            <div class="gspadres clear">
                                <input type="hidden" hi="id" value="${shippingAddress.id }"/>
                                <input type="hidden" hi="remark" value="${shippingAddress.remark }"/>
                                <input type="hidden" hi="auditState" value="${shippingAddress.auditState }"/>
                                <input type="hidden" hi="isdefault" value="${shippingAddress.isdefault!"" }"
                                       class="isdefault"/>
                                <label class="adres-row adres-row-new radio-pretty inline <#if shippingAddress.isdefault>checked </#if>">
                                    <input type="radio" <#if shippingAddress.isdefault>checked="checked" </#if>
                                           name="radio"><span></span>
                                </label>
                                <span class="adres-row adres-row1 text-overflow" data-id="contactor">
                                    ${shippingAddress.contactor }
                                </span>
                                <span class="adres-row adres-row2">
									<p class="main-ades text-overflow" data-id="fullAddress" style="white-space: unset;">
                                        ${shippingAddress.fullAddress }
                                    </p>
								</span>
                                <span class="adres-row adres-row3 text-overflow" data-id="mobile">
                                    <span class="mobile">${shippingAddress.mobile }</span>
                                    <#if shippingAddress.isdefault>
                                        <span class="def-biaoqian">
                                            <span class="default">默认</span>
                                        </span>
                                    </#if>
                                </span>
                                <span class="adres-row adres-row4">
                                    <#if accountRole?? && accountRole!=3>
									<a href="javascript:void(0)" class="res-btn" data-toggle="modal"
                                       onclick="editAddress(this)" data-keyboard="false" data-target="#editModal">
                                       <i class="sui-icon icon-touch-edit-rect"></i>编辑
                                    </a>
                                    </#if>
								</span>
                            </div>
                        </#list>
                    </#if>
                </div>
            </div>
            <!--支付方式-->
            <div class="zffs">
                <div class="address-top-title">选择支付方式</div>
                <ul>
                    <#if accountRole !=3>
                    <li class="cur" t="1">
                        在线支付<br/>
                        <span class="payTypeTip">（快捷方便）</span>
                    </li>
                    </#if>
                    <#if !orderSettle.isHideOffLinePay>
                        <li t="3" <#if accountRole ==3>class="cur" </#if>>
                            线下转账<br/>
                            <span class="payTypeTip">(公对公)</span>
                        </li>
                    </#if>
                    <#if shoppingCartInfo.isShow==1>
                        <li t="2">货到付款</li>
                    </#if>
                    <#if orderSettle.isShowAccountPeriodPay>
                        <#if orderSettle.isGrayAccountPeriodPay>
                            <li t="5" class="zq-li grayBtn">
                                <input type="hidden" id="isGrayBtn" value="${orderSettle.isGrayAccountPeriodPay}">
                                自有账期 <i class="sui-icon icon-notification zq-icon"></i><br />
                                <span class="payTypeTip">额度：${orderSettle.availableCredit}
                                </span>
                                <div class="zqTip">
                                    1、小药药自有账期仅支持购买控销及自营仓商品；<br>
                                    2、还款后需财务核款，会导致额度未及时返还或逾期未清的场景，该类问题可联系专属销售处理。
                                </div>
                                <#if orderSettle.isOverduePay>
                                    <div class="tyqiIcon"></div>
                                </#if>
                                </li>
                                <#else>
                                    <li t="5" class="zq-li">
                                        <input type="hidden" id="isGrayBtn" value="${orderSettle.isGrayAccountPeriodPay}">
                                        自有账期 <i class="sui-icon icon-notification zq-icon"></i><br />
                                        <span class="payTypeTip">额度：${orderSettle.availableCredit}
                                        </span>
                                        <div class="zqTip">
                                            1、小药药自有账期仅支持购买控销及自营仓商品；<br>
                                            2、还款后需财务核款，会导致额度未及时返还或逾期未清的场景，该类问题可联系专属销售处理。
                                        </div>
                                        <#if orderSettle.isOverduePay>
                                            <div class="tyqiIcon"></div>
                                        </#if>
                                    </li>
                    </#if>
                    </#if>
                    </ul>     
                    <#if shoppingCartInfo.isShow==1>
                        <div class="hdfk-box">
                            <span>
                                ${shoppingCartInfo.offlineMessage}
                            </span>
                        </div>
                    </#if>
                    <input type="hidden" id="payTips" value="${payTips}" />
                    <input type="hidden" id="offlinePayTips" value="${orderSettle.offlinePayTips}" />
                    <br>
                     <div class="xxTip">${orderSettle.selectedTips}</div>
                </div>
                
                
            </div>
            <input type="hidden" id="merchantId" value="${orderSettle.merchantId}"></input>
            <!--送货清单-->
            <div class="shqdtitle">送货清单</div>
            <div class="shqd">
                <!--默认显示-->
                <div>
                    <!--列表模式-->
                    <div class="listmode">
                        <!--表头-->
                        <div class="headbox">
                            <ul>
                                <li class="li1">
                                    <span>商品信息</span>
                                </li>
                                <li class="li_mingxi"><span class="head-tit">明细</span></li>
                                <li class="li3">单价</li>
                                <li class="li5">数量</li>
                                <li class="li4">金额</li>
                            </ul>
                        </div>
                        <div class="list-content">
                            <#if orderSettle.companys ?? && (orderSettle.companys?size>0)>
                                <#list orderSettle.companys as company>
                                    <#if company.isThirdCompany==0>
                                        <div class="list-ziying">
                                            <!--自营公司名称 -->
                                            <div class="cgd-qy" style="background: #fff;">
                                                <span class="ziying">自营</span>
                                                <span class="qy-title">
                                                    ${company.companyName}
                                                </span>
                                            </div>
                                            <!--次日达相关配置-->
                                            <#if company.nextDayDeliveryDto ??>
                                                <div class="cuxiaobox" style="height: 40px;line-height: 40px;background: #fff;">
                                                    <span class="next-day-service-tag" 
                                                        style="border: 1px solid #04BE88;margin-left: 20px;font-size: 12px;border-radius: 4px;background: #04BE88;padding: 1px 0px 1px 2px;">
                                                        <#if company.nextDayDeliveryDto.tagDto.appIcon??>
                                                            <img src="${productImageUrl}${company.nextDayDeliveryDto.tagDto.appIcon}" 
                                                                alt="${company.nextDayDeliveryDto.tagDto.name!''}" 
                                                                style="height: 12px; width: auto; object-fit: cover;display: inline-block;">
                                                        </#if>
                                                        <span style="color:#fff;border: none">
                                                            ${company.nextDayDeliveryDto.tagDto.name!''}
                                                        </span>
                                                    </span>
                                                    <span class="next-day-service-span" style="float: none !important">${company.nextDayDeliveryDto.tips}</span>
                                                </div>
                                            </#if>
                                            <!-- 遍历店铺数据判断 -->
                                            <#if company.shops ?? && (company.shops?size>0) >
                                                <!--遍历店铺数据开始-->
                                                <#list company.shops as shop>
                                                    <#if shop.shopPatternCode=='ybm'>
                                                        <input type="hidden" id="ybmTotalAmount" value="${shop.totalAmount}" />
                                                        <input type="hidden" id="ybmPromoTotalAmt" value="${shop.promoTotalAmt}" />
                                                    </#if>
                                                    <div class="shop-item">
                                                        <div class="defaultbox">
                                                            <!--店铺结构开始-->
                                                            <#assign showCartItemNum=0 />
                                                            <div class="pro-box">
                                                                <!--店铺名称-->
                                                                <div class="cgd-qy cgd-qy1">
                                                                    <span class="qy-title">
                                                                        ${shop.shopName}
                                                                    </span>
                                                                </div>
                                                                <!--遍历分组数据判段-->
                                                                <#if shop.groups ?? && (shop.groups?size>0) >
                                                                    <!--遍历分组数据开始-->
                                                                    <#list shop.groups as shoppingCartGroup>
                                                                        <!--常规分组名称-->
                                                                        <#if shoppingCartGroup.type==9>
                                                                            <div class="taocanbox-onther">
                                                                                <#--<span class="new">其它商品：</span>-->
                                                                            </div>
                                                                        </#if>
                                                                        <!--活动分组名称-->
                                                                        <#if (shoppingCartGroup.type!=10 && shoppingCartGroup.type!=9)>
                                                                            <div class="manjianbox" name="${shoppingCartGroup.id}">
                                                                                <#if shoppingCartGroup.title??>
                                                                                    <span class="title">
                                                                                        <#if shoppingCartGroup.type==1>满减<#elseif shoppingCartGroup.type==2>满折<#elseif shoppingCartGroup.type==3>满赠<#elseif shoppingCartGroup.type==4>满减赠<#elseif shoppingCartGroup.type==6>一口价<#elseif shoppingCartGroup.type==11>满返<#elseif shoppingCartGroup.type==12>满减返券</#if>
                                                                                    </span>
                                                                                    <!-- 拼团或批购包邮直接进结算页 -->
                                                                                    <#if (shoppingCartGroup.canGoToGiftPool == true) || (shoppingCartGroup.gifts?? && shoppingCartGroup.gifts?size > 0 )>
                                                                                        <input type="hidden" value="${shoppingCartGroup.canGoToGiftPool == true}" />
                                                                                        <input type="hidden" value="${shoppingCartGroup.gifts?size}" />
                                                                                        <span class="info">
                                                                                            ${shoppingCartGroup.title}
                                                                                        </span>
                                                                                        <#if (shoppingCartGroup.canGoToGiftPool == true)>
                                                                                            <a id='fullGiveModalBtn' style="cursor:pointer;color:#00B377;" onclick="fullNumSelectGive(${orderSettle.bizSource},${shoppingCartGroup.giftPoolActHasSelectedNum},${shoppingCartGroup.giftPoolActTotalSelectedNum},${shoppingCartGroup.promoId})" style="color: #15BA81;margin: 0 5px;">
                                                                                                <#if (shoppingCartGroup.isGiveUpGift == false)>
                                                                                                    选择赠品(${shoppingCartGroup.giftPoolActHasSelectedNum}/${shoppingCartGroup.giftPoolActTotalSelectedNum})<img src="/static/images/right-arrow.png" alt="" style="margin-right: 3px;top: -1px;position: relative;width: 12px;">
                                                                                                    <#if (shoppingCartGroup.giftPoolActCanSelectedNum>0) >
                                                                                                        <input type="hidden" class="giftPoolActCanSelectedNum"  onclick="fullNumSelectGive(${orderSettle.bizSource},${shoppingCartGroup.giftPoolActHasSelectedNum},${shoppingCartGroup.giftPoolActTotalSelectedNum},${shoppingCartGroup.promoId})"/>
                                                                                                    </#if>
                                                                                                <#else>
                                                                                                    已放弃赠品
                                                                                                </#if>
                                                                                                <input type="hidden" class="giftPoolActTitle"  onclick="fullNumSelectGive(${orderSettle.bizSource},${shoppingCartGroup.giftPoolActHasSelectedNum},${shoppingCartGroup.giftPoolActTotalSelectedNum},${shoppingCartGroup.promoId}, true)"/>
                                                                                            </a>
                                                                                        </#if>
                                                                                    </#if>
                                                                                </#if>
                                                                                <#if shoppingCartGroup.type==5>
                                                                                    <input type="radio" name="giftId"
                                                                                        style="margin-left: 17px;margin-right: 5px;"
                                                                                        value="${shoppingCartGroup.id}"
                                                                                        <#if shoppingCartGroup.selectStatus==1>checked="true"
                                                                                    data-mutex-check="true"
                                                                                </#if>
                                                                                <#if shoppingCartGroup.selectStatus==0> data-mutex-check="false"</#if> >
                                                                                    <span class="title_hui">物料心愿单礼包</span>
                                                                                    <span class="info_hui" value="${shoppingCartGroup.id}">不需要</span>
                                                                                    <input type="hidden" name="giftIds" value="${shoppingCartGroup.id}" />
                                                                                    <input type="hidden" name="giftTotalAmount" value="${shoppingCartGroup.totalAmount}" />
                                                                                </#if>
                                                                            </div>
                                                                        </#if>
                                                                        <!--遍历分组商品-->
                                                                        <div class="list-default-box defaultH">
                                                                            <#if shoppingCartGroup.sorted ?? && (shoppingCartGroup.sorted?size>0) >
                                                                                <!--遍历分组商品开始-->
                                                                                <#list shoppingCartGroup.sorted as shoppingCartItem>
                                                                                    <#assign showCartItemNum=showCartItemNum + 1 />
                                                                                    <!--套餐商品开始-->
                                                                                    <#if shoppingCartItem.itemType=3>
                                                                                        <!--套餐商品标题-->
                                                                                        <div class="taocanbox">
                                                                                            <span class="new">套餐商品</span>
                                                                                        </div>
                                                                                        <div class="bodybox taocanspe">
                                                                                            <!---套餐商品列表开始-->
                                                                                            <#if shoppingCartItem.subItemList ?? && (shoppingCartItem.subItemList?size>0) >
                                                                                                <#list shoppingCartItem.subItemList as tcList>
                                                                                                    <ul>
                                                                                                        <li class="lib1">
                                                                                                            <div class="l-box fl">
                                                                                                                <a href="/search/skuDetail/${tcList.id}.htm?<#if company.isVirtualSupplier == true>isMainProductVirtualSupplier=true</#if>"
                                                                                                                    target="_blank" title="${tcList.commonName }"><img src="${productImageUrl}/ybm/product/min/${tcList.imageUrl }"
                                                                                                                    alt="${tcList.commonName}" onerror="this.src='/static/images/default-big.png'">
                                                                                                                </a>
                                                                                                                <!--标签-->
                                                                                                                <#if shoppingCartItem.item.valid=0>
                                                                                                                    <div class="bq-box">
                                                                                                                        <img src="img/bq-qiangguang.png" alt="">
                                                                                                                    </div>
                                                                                                                </#if>
                                                                                                                <#if tcList.blackSku=1>
                                                                                                                    <!--不参与返点提示-->
                                                                                                                    <#if (tcList.blackSkuText??) && (tcList.blackSkuText!="" )>
                                                                                                                        <div class="nofd">
                                                                                                                            ${tcList.blackSkuText}
                                                                                                                        </div>
                                                                                                                    </#if>
                                                                                                                </#if>
                                                                                                            </div>
                                                                                                            <div class="r-box fr">
                                                                                                                <div class="lib1-row1 text-overflow">
                                                                                                                    <#if tcList.tagTitle ?? && (tcList.tagTitle != "" && tcList.tagTitle != null)>
                                                                                                                        <span class="tag-title">${tcList.tagTitle}</span>
                                                                                                                    </#if>
                                                                                                                    <a href="/search/skuDetail/${tcList.id}.htm?<#if company.isVirtualSupplier == true>isMainProductVirtualSupplier=true</#if>"
                                                                                                                        target="_blank" title="${tcList.commonName }">
                                                                                                                            ${tcList.sku.showName}
                                                                                                                    </a>
                                                                                                                </div>
                                                                                                                <div class=" lib1-row3">
                                                                                                                    <div class="row-biaoqian" id="${tcList.uniqueKey}row-biaoqian">
                                                                                                                        <#if tcList.tagList ?? && (tcList.tagList?size>0) >
                                                                                                                            <#list tcList.tagList as item>
                                                                                                                                <#if (item_index < 3)>
                                                                                                                                    <span class="<#if item.uiType == 1>linqi</#if>
                                                                                                                                        <#if item.uiType == 2>quan</#if>
                                                                                                                                        <#if item.uiType == 3>manjian</#if>
                                                                                                                                        <#if item.uiType == 4>default</#if>
                                                                                                                                        <#if item.uiType == 5>yibao</#if>
                                                                                                                                        ">
                                                                                                                                        ${item.name}
                                                                                                                                    </span>
                                                                                                                                </#if>
                                                                                                                            </#list>
                                                                                                                        </#if>
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                                <div class="lib1-row2 text-overflow">
                                                                                                                    <span class="title">规　　格：</span>
                                                                                                                    <span class="info">
                                                                                                                        ${tcList.sku.spec}
                                                                                                                    </span>
                                                                                                                </div>
                                                                                                                <div class="lib1-row4 text-overflow">
                                                                                                                    <span class="title">生产厂家：</span>
                                                                                                                    <span class="info">
                                                                                                                        ${tcList.sku.manufacturer}
                                                                                                                    </span>
                                                                                                                </div>
                                                                                                                <div class="lib1-row3">
                                                                                                                    <div class="row-biaoqian">
                                                                                                                        <#if tcList.tagWholeOrderList ?? && (tcList.tagWholeOrderList?size>0) >
                                                                                                                            <#list tcList.tagWholeOrderList as item >
                                                                                                                                <div class="synthesis">
                                                                                                                                    <div class="synthesis-biao">
                                                                                                                                        <#if item.description ?? && item.description != ''>
                                                                                                                                            <span class="synthesis-biao-left">${item.name}</span>
                                                                                                                                            <span class="synthesis-biao-shuoming">${item.description}</span>
                                                                                                                                        <#else>
                                                                                                                                            <span class="synthesis-biao-left-single">${item.name}</span>
                                                                                                                                        </#if>
                                                                                                                                    </div>
                                                                                                                                </div>
                                                                                                                            </#list>
                                                                                                                        </#if>
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                            </div>
                                                                                                        </li>
                                                                                                        <li class="li_mingxi"
                                                                                                            id="${tcList.uniqueKey}">
                                                                                                            <div class="mxrow1">
                                                                                                                <span class="mxrow_tit">实付金额：</span><span
                                                                                                                    class="mxrow_info"
                                                                                                                    id="${tcList.uniqueKey}realPayAmount">￥${tcList.realPayAmount}
                                                                                                                </span>
                                                                                                                <span class="mxrow_tit">优惠金额：</span><span
                                                                                                                    class="mxrow_info"
                                                                                                                    id="${tcList.uniqueKey}discountAmount">￥${tcList.discountAmount}
                                                                                                                </span>
                                                                                                            </div>
                                                                                                            <div class="mxrow2">
                                                                                                                <span class="mxrow_tit">余额抵扣：</span><span
                                                                                                                    class="mxrow_info"
                                                                                                                    id="${tcList.uniqueKey}useBalanceAmount">￥${tcList.useBalanceAmount}
                                                                                                                </span>
                                                                                                                <span class="mxrow_tit">返点金额：</span><span
                                                                                                                    class="mxrow_info"
                                                                                                                    id="${tcList.uniqueKey}balanceAmount">￥${tcList.balanceAmount}
                                                                                                                </span>
                                                                                                            </div>
                                                                                                            <div class="mxrow2">
                                                                                                                <span class="mxrow_tit">实付价：</span>
                                                                                                                <span class="mxrow_info ${tcList.uniqueKey}purchasePriceK">￥<#if 1==tcList.sku.isGive>
                                                                                                                        ${tcList.sku.fob?string('0.00')}
                                                                                                                        <#elseif tcList.purchasePrice ??>
                                                                                                                            ${tcList.purchasePrice?string('0.00')}
                                                                                                                            <#else>0.00
                                                                                                                    </#if></span>
                                                                                                                <span class="mxrow_tit">成本价：</span><span
                                                                                                                    class="mxrow_info  ${tcList.uniqueKey}costPriceK">￥<#if 1==tcList.sku.isGive>
                                                                                                                        ${tcList.sku.fob?string('0.00')}
                                                                                                                        <#elseif tcList.costPrice ??>
                                                                                                                            ${tcList.costPrice?string('0.00')}
                                                                                                                            <#else>0.00
                                                                                                                    </#if></span>
                                                                                                            </div>
                                                                                                        </li>
                                                                                                        <li class="lib3">
                                                                                                            <div class="zkj">
                                                                                                                ￥${tcList.sku.fob}
                                                                                                            </div>
                                                                                                        </li>
                                                                                                        <li class="lib5">
                                                                                                            <span>x${tcList.amount}
                                                                                                            </span>
                                                                                                        </li>
                                                                                                        <li class="lib4"></li>
                                                                                                    </ul>
                                                                                                </#list>
                                                                                                <!--套餐小计-->
                                                                                                <div class="taocanxj">
                                                                                                    <div class="zkj">
                                                                                                        ￥${shoppingCartItem.item.subtotal}
                                                                                                    </div>
                                                                                                </div>
                                                                                            </#if>
                                                                                        </div>
                                                                                        <!---套餐商品列表结束-->
                                                                                    </#if>
                                                                                    <!--套餐商品结束-->
                                                                                    <!--非套餐商品开始-->
                                                                                    <#if shoppingCartItem.itemType!=3>                                                                                        <div class="bodybox <#if shoppingCartGroup.type==5>bigGift${shoppingCartGroup.id}</#if>">
                                                                                            <ul>
                                                                                                <li class="lib1">
                                                                                                    <div class="l-box fl">
                                                                                                        <a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm?<#if company.isVirtualSupplier == true>isMainProductVirtualSupplier=true</#if>"
                                                                                                            target="_blank" title="${shoppingCartItem.item.sku.showName }">
                                                                                                            <img src="${productImageUrl}/ybm/product/min/${shoppingCartItem.item.sku.imageUrl }"
                                                                                                                alt="${shoppingCartItem.item.sku.showName}" onerror="this.src='/static/images/default-big.png'">
                                                                                                            <div class="bq-box">
                                                                                                                <#if shoppingCartItem.item.sku.status==2>
                                                                                                                    <img src="/static/images/product/bq-shouqing.png" alt="">
                                                                                                                </#if>
                                                                                                                <#if shoppingCartItem.item.sku.status==4 && shoppingCartItem.item.sku.isGive==0>
                                                                                                                    <img src="/static/images/product/bq-xiajia.png" alt="">
                                                                                                                </#if>
                                                                                                            </div>
                                                                                                            <#if shoppingCartItem.item.blackSku=1>
                                                                                                                <!--不参与返点提示-->
                                                                                                                <#if (shoppingCartItem.item.blackSkuText??) && (shoppingCartItem.item.blackSkuText!="" )>
                                                                                                                    <div class="nofd">
                                                                                                                        ${shoppingCartItem.item.blackSkuText}
                                                                                                                    </div>
                                                                                                                </#if>
                                                                                                            </#if>
                                                                                                        </a>
                                                                                                    </div>
                                                                                                    <div class="r-box fr">
                                                                                                        <div class="lib1-row1 text-overflow">
                                                                                                            <#if shoppingCartItem.item.tagTitle ?? && (shoppingCartItem.item.tagTitle != "" && shoppingCartItem.item.tagTitle != null)>
                                                                                                                <span class="tag-title">${shoppingCartItem.item.tagTitle}</span>
                                                                                                            </#if>
                                                                                                            <span>
                                                                                                                <a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm?<#if company.isVirtualSupplier == true>isMainProductVirtualSupplier=true</#if>"
                                                                                                                    target="_blank" title="${shoppingCartItem.item.sku.showName }">
                                                                                                                    <#if shoppingCartItem.item.isShow806 || shoppingCartItem.item.gift>
                                                                                                                        <div class="bq806">
                                                                                                                            <img src="/static/images/bq806.png"
                                                                                                                                    alt="">
                                                                                                                        </div>
                                                                                                                    </#if>
                                                                                                                    <#if shoppingCartItem.item.agent == 1>
                                                                                                                        <span class="dujia">独家</span>
                                                                                                                    </#if>
                                                                                                                    ${shoppingCartItem.item.sku.showName }
                                                                                                                </a>
                                                                                                            </span>
                                                                                                        </div>
                                                                                                        <div class="lib1-row3">
                                                                                                            <div class="row-biaoqian" id="${shoppingCartItem.item.uniqueKey}row-biaoqian">
                                                                                                                <#if shoppingCartItem.item.tagList ?? && (shoppingCartItem.item.tagList?size>0) >
                                                                                                                    <#list shoppingCartItem.item.tagList as item>
                                                                                                                        <#if (item_index < 3)>
                                                                                                                            <span class="<#if item.uiType == 1>linqi</#if>
                                                                                                                                <#if item.uiType == 2>quan</#if>
                                                                                                                                <#if item.uiType == 3>manjian</#if>
                                                                                                                                <#if item.uiType == 4>default</#if>
                                                                                                                                <#if item.uiType == 5>yibao</#if>
                                                                                                                                ">
                                                                                                                                    ${item.name}
                                                                                                                            </span>
                                                                                                                        </#if>
                                                                                                                    </#list>
                                                                                                                </#if>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                        <div class="lib1-row5">
                                                                                                            <div class="row-last">
                                                                                                                <#if (shoppingCartItem.item.sku.suggestPrice ?? ) && (shoppingCartItem.item.sku.suggestPrice !='' )>
                                                                                                                    <div class="kongxiao-box">
                                                                                                                        <span class="s-kx">零售价</span><span
                                                                                                                            class="jg">￥${shoppingCartItem.item.sku.suggestPrice}
                                                                                                                        </span>
                                                                                                                    </div>
                                                                                                                </#if>
                                                                                                                <#if shoppingCartItem.item.sku.grossMargin ?? && shoppingCartItem.item.sku.grossMargin !=''>
                                                                                                                    <div class="maoli-box">
                                                                                                                        <span class="s-ml">毛利</span><span
                                                                                                                            class="jg">
                                                                                                                            ${shoppingCartItem.item.sku.grossMargin}
                                                                                                                        </span>
                                                                                                                    </div>
                                                                                                                </#if>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                        <div class="lib1-row2 text-overflow">
                                                                                                            <span class="title">规　　格：</span>
                                                                                                            <span class="info">
                                                                                                                ${shoppingCartItem.item.sku.spec }
                                                                                                            </span>
                                                                                                        </div>
                                                                                                        <div class="lib1-row4 text-overflow">
                                                                                                            <span class="title">生产厂家：</span>
                                                                                                            <span class="info">
                                                                                                                ${shoppingCartItem.item.sku.manufacturer }
                                                                                                            </span>
                                                                                                        </div>
                                                                                                        <div class="lib1-row3">
                                                                                                            <div class="row-biaoqian">
                                                                                                                <#if shoppingCartItem.item.tagWholeOrderList ?? && (shoppingCartItem.item.tagWholeOrderList?size>0) >
                                                                                                                    <#list shoppingCartItem.item.tagWholeOrderList as item>
                                                                                                                        <div class="synthesis">
                                                                                                                            <div class="synthesis-biao">
                                                                                                                                <#if item.description ?? && item.description != ''>
                                                                                                                                    <span class="synthesis-biao-left">${item.name}</span>
                                                                                                                                    <span class="synthesis-biao-shuoming">${item.description}</span>
                                                                                                                                <#else>
                                                                                                                                    <span class="synthesis-biao-left-single">${item.name}</span>
                                                                                                                                </#if>
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                    </#list>
                                                                                                                </#if>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </li>
                                                                                                <li class="li_mingxi">
                                                                                                    <div class="mxrow1">
                                                                                                        <span class="mxrow_tit">实付金额：</span>
                                                                                                        <span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}realPayAmount">
                                                                                                            ￥${shoppingCartItem.item.realPayAmount}
                                                                                                        </span>
                                                                                                        <span class="mxrow_tit">优惠金额：</span>
                                                                                                        <span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}discountAmount">
                                                                                                            ￥${shoppingCartItem.item.discountAmount}
                                                                                                        </span>
                                                                                                    </div>
                                                                                                    <div class="mxrow2">
                                                                                                        <span class="mxrow_tit">余额抵扣：</span><span
                                                                                                            class="mxrow_info"
                                                                                                            id="${shoppingCartItem.item.uniqueKey}useBalanceAmount">￥${shoppingCartItem.item.useBalanceAmount}
                                                                                                        </span>
                                                                                                        <span class="mxrow_tit">返点金额：</span><span
                                                                                                            class="mxrow_info"
                                                                                                            id="${shoppingCartItem.item.uniqueKey}balanceAmount">￥${shoppingCartItem.item.balanceAmount}
                                                                                                        </span>
                                                                                                    </div>
                                                                                                    <div class="mxrow2">
                                                                                                        <span class="mxrow_tit">实付价：</span>
                                                                                                        <span class="mxrow_info ${shoppingCartItem.item.uniqueKey}purchasePrice">
                                                                                                            ￥
                                                                                                            <#if 1==shoppingCartItem.item.sku.isGive>
                                                                                                                ${shoppingCartItem.item.price?string('0.00')}
                                                                                                            <#elseif shoppingCartItem.item.purchasePrice ??>
                                                                                                                ${shoppingCartItem.item.purchasePrice?string('0.00')}
                                                                                                            <#else>
                                                                                                                0.00
                                                                                                            </#if>
                                                                                                        </span>
                                                                                                        <span class="mxrow_tit">成本价：</span>
                                                                                                        <span class="mxrow_info  ${shoppingCartItem.item.uniqueKey}costPrice">
                                                                                                            ￥
                                                                                                            <#if 1==shoppingCartItem.item.sku.isGive>
                                                                                                                ${shoppingCartItem.item.price?string('0.00')}
                                                                                                            <#elseif shoppingCartItem.item.costPrice ??>
                                                                                                                ${shoppingCartItem.item.costPrice?string('0.00')}
                                                                                                            <#else>
                                                                                                            0.00
                                                                                                            </#if>
                                                                                                        </span>
                                                                                                    </div>
                                                                                                </li>
                                                                                                <li class="lib3">
                                                                                                    <div class="zkj">
                                                                                                        ￥${shoppingCartItem.item.price }
                                                                                                    </div>
                                                                                                </li>
                                                                                                <li class="lib5">
                                                                                                    <span>
                                                                                                        x${shoppingCartItem.item.amount }
                                                                                                    </span>
                                                                                                </li>
                                                                                                <li class="lib4">
                                                                                                    <div class="zkj">
                                                                                                        ￥${shoppingCartItem.item.subtotal }
                                                                                                    </div>
                                                                                                </li>
                                                                                            </ul>
                                                                                        </div>
                                                                                    </#if>
                                                                                    <!--非套餐商品结束-->
                                                                                </#list>
                                                                                <!--遍历分组商品结束-->
                                                                            </#if>
                                                                        </div>
                                                                        <!--遍历分组商品判定结束-->
                                                                    </#list>
                                                                    <!--遍历分组数据结束-->
                                                                </#if>
                                                                <!--遍历分组数据判定结束-->
                                                            </div>
                                                            <!--店铺结构结束-->
                                                            <!--查看更多-->
                                                            <!--默认页面展示5行，更多数据通过点击查看更多-->
                                                            <#if (showCartItemNum > 5)>
                                                                <a href="javaScript:void(0); " class="more">
                                                                    点击展开 
                                                                    <i class="sui-icon icon-tb-unfold "></i> 
                                                                </a>
                                                            </#if>
                                                            <!--收起-->
                                                            <a href="javaScript:void(0); " class="no-more">
                                                                点击收起 
                                                                <i class="sui-icon icon-tb-fold"></i> 
                                                            </a>
                                                            <!--店铺优惠券-->
                                                            <!-- 
                                                                                                                                                <#--<div class="youhuiquan" style="overflow: visible;position:relative;border-bottom: 1px dashed #e6e6e6;padding-left: 40px;padding-bottom:15px;">-->
                                                                                                                                                <#--<span style="display: inline-block;margin-right: 30px;">优惠券</span>-->
                                                                                                                                                <#--<span style="width:200px;display: inline-block;margin-right: 80px;color:#FF0000;font-size:12px;" id="${shop.shopCode}voucherTip">
                                                                                                                                                                    ${shop.voucherTip}
                                                                                                                                                                    </span>-->
                                                                                                                                                                    <#--<a href="javascript:;" class="xiala-all" shopCode="${shop.shopCode}">-->
                                                                                                                                                                        <#--<input type="hidden" id="shopPatternCode" value="${shop.shopPatternCode}">-->
                                                                                                                                                                            <#--<span style="font-size: 12px;">查看全部优惠券</span><img style="margin-left:5px;" src="/static/images/xiala.png" alt="">-->
                                                                                                                                                                                <#--< /a>-->
                                                                                                                                                                                    <#--<div class="cg-yhq">-->
                                                                                                                                                                                        <#--<div class="xyy-cover">
                                                                                                                                                                </div>-->
                                                                                                                                                                <#--<div class="xyy-confirm">-->
                                                                                                                                                                    <#--<div class="xyy-header">提示
                                                                                                                                                    </div>-->
                                                                                                                                                    <#--<div class="xyy-body">选中的叠加券优惠金额已超过应付总额，是否确认使用？
                                                                                                                                    </div>-->
                                                                                                                                    <#--<div class="xyy-footer">-->
                                                                                                                                        <#--<button class="ok-btn" id="xyyOk">确定使用</button>-->
                                                                                                                                            <#--<button class="cancel-btn" id="xyyCancel">放弃使用</button>-->
                                                                                                                                                <#--< /div>-->
                                                                                                                                                    <#--<input type="hidden" id="curCoupon" value="">-->
                                                                                                                                                        <#--< /div>-->
                                                                                                                                                            <#--<div class="yhq-san">
                                                                                                                                </div>-->
                                                                                                                                <#--<div class="address-top-title">-->
                                                                                                                                    <#--<div class="yhq-l-box">-->
                                                                                                                                        <#--<a href="javascript:;" class="ky-yhq cur">可用优惠券</a>-->
                                                                                                                                            <#--<a href="javascript:;" class="bky-yhq ">不可用优惠券</a>-->
                                                                                                                                                <#--< /div>-->
                                                                                                                                                    <#--< /div>-->
                                                                                                                                                        <#--<#if shop.shopPatternCode=='ybm'>-->
                                                                                                                                                            <#--<div class="ybm">-->
                                                                                                                                                                <#--<!--可用优惠券开始&ndash;&gt;-->
                                                                                                                                                                    <#--<#assign availItemNum=0 />-->
                                                                                                                                                                    <#--<ul class="yhq-common weishiyong ky tanbox">-->
                                                                                                                                                                        <#--<!--叠加券&ndash;&gt;-->
                                                                                                                                                                            <#--&lt;#&ndash;<#if (shop.availDjVoucherList ?? && shop.availDjVoucherList?size>0)>&ndash;&gt;-->
                                                                                                                                                                                <#--&lt;#&ndash;<#list availDjVoucherList as voucher>&ndash;&gt;-->
                                                                                                                                                                                    <#--&lt;#&ndash;<#assign availItemNum=availItemNum + 1 />&ndash;&gt;-->
                                                                                                                                                                                    <#--&lt;#&ndash;<li class="<#if (voucher.isUse==1)>cur </#if><#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher_index}">&ndash;&gt;-->
                                                                                                                                                                                        <#--&lt;#&ndash;<input type="hidden" shopcode="${xyyMallShop.shopCode}" shoppatterncode="${xyyMallShop.shopPatternCode}" name="voucherId" value="${voucher.id }"/>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-top">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if voucher.voucherState==1>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="price">
                                                                                                                                                    ${voucher.discountRatio }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="fuhao">折</span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#else>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="fuhao">￥</span><span class="price">
                                                                                                                                                    ${voucher.moneyInVoucher }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-foot">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;${voucher.minMoneyToEnableDesc }&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucher?? && voucher.maxMoneyInVoucher!=null && voucher.maxMoneyInVoucher!="" && voucher.maxMoneyInVoucher>0 ) >&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-foot">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;${voucher.maxMoneyInVoucherDesc }&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb-top">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="quan quan-die">
                                                                                                                                                    ${voucher.voucherTypeDesc}
                                                                                                                                                    </span><span class=" info">
                                                                                                                                                                                                                                                        ${voucher.voucherDesc }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div style="height:30px;overflow:hidden;"></div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb-foot" style="position:absolute;bottom:10px;">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span>
                                                                                                                                                    ${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-checkb">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<input type="checkbox" <#if voucher.isUse==0>disabled=""</#if>><span></span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</label>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</li>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#list>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--<!--遍历可用-非叠加券&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if (shop.availVoucherList ?? && shop.availVoucherList?size>0)>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#list shop.availVoucherList as voucher>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#assign availItemNum = availItemNum + 1 />&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<li class="<#if (voucher.isUse==1)>cur </#if><#if voucher.isUse==0 || voucher.isUse==Null>ygq </#if><#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">&ndash;&gt;-->
                                                                                                                                                                                            <#--&lt;#&ndash;${voucher.isUse}&ndash;&gt;-->
                                                                                                                                                                                                <#--&lt;#&ndash;<input type="hidden" shopCode="${shop.shopCode}" shopPatternCode="${shop.shopPatternCode}" name="voucherId" value="${voucher.id }"/>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-top">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if voucher.voucherState==1>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="price">
                                                                                                                                                    ${voucher.discountRatio }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="fuhao">折</span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#elseif voucher.voucherUsageWay==1>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="fuhao">￥</span><span class="price">
                                                                                                                                                    ${voucher.sourceMoneyInVoucher }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#else>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="fuhao">￥</span><span class="price">
                                                                                                                                                    ${voucher.moneyInVoucher }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-foot">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;${voucher.minMoneyToEnableDesc }&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-foot">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;${voucher.maxMoneyInVoucherDesc }&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb-top">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if voucher.voucherType == 2>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="quan">
                                                                                                                                                    ${voucher.voucherTypeDesc}
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if voucher.voucherType == 1>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class=" quan quan-tong">
                                                                                                                                                                                                    ${voucher.voucherTypeDesc}
                                                                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                                                                    <#--&lt;#&ndash;< /#if>&ndash;&gt;-->
                                                                                                                                                                                                        <#--&lt;#&ndash;<#if voucher.voucherType==6>&ndash;&gt;-->
                                                                                                                                                                                                            <#--&lt;#&ndash;<span class="quan quan-die">
                                                                                                                                                                                                                ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                </span>&ndash;&gt;-->
                                                                                                                                                                                                                <#--&lt;#&ndash;< /#if>&ndash;&gt;-->
                                                                                                                                                                                                                    <#--&lt;#&ndash;<#if voucher.voucherType==5>&ndash;&gt;-->
                                                                                                                                                                                                                        <#--&lt;#&ndash;<span class="quan quan-xin">
                                                                                                                                                                                                                            ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                            </span>&ndash;&gt;-->
                                                                                                                                                                                                                            <#--&lt;#&ndash;< /#if>&ndash;&gt;-->
                                                                                                                                                                                                                                <#--&lt;#&ndash;<#if voucher.voucherType==7>&ndash;&gt;-->
                                                                                                                                                                                                                                    <#--&lt;#&ndash;<span class="quan quan-shop">
                                                                                                                                                                                                                                        ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                                        </span>&ndash;&gt;-->
                                                                                                                                                                                                                                        <#--&lt;#&ndash;< /#if>&ndash;&gt;-->
                                                                                                                                                                                                                                            <#--&lt;#&ndash;<span class="info" title="${voucher.shopName}">
                                                                                                                                                                                                                                                ${voucher.shopName}
                                                                                                                                                                                                                                                </span>&ndash;&gt;-->
                                                                                                                                                                                                                                                <#--&lt;#&ndash;< /div>&ndash;&gt;-->
                                                                                                                                                                                                                                                    <#--&lt;#&ndash;<div style="height:30px;overflow:hidden;line-height:30px;">
                                                                                                                                                                                                                                                        ${voucher.voucherDesc }
                                                                                                                                                                            </div>&ndash;&gt;-->
                                                                                                                                                                                                                                    <#--&lt;#&ndash;<div class="yhq-rb-foot">&ndash;&gt;-->
                                                                                                                                                                                                                                    <#--&lt;#&ndash;<span>
                                                                                                                                                                            ${voucher.voucherScope}
                                                                                                                                                                                                                                                        </span>&ndash;&gt;-->
                                                                                                                                                                                                                                                        <#--&lt;#&ndash;< /div>&ndash;&gt;-->
                                                                                                                                                                                                                                                            <#--&lt;#&ndash;<div style="font-size:12px;color:#999999;">
                                                                                                                                                                                                                                                                ${voucher.validDate?string("yyyy/MM/dd")}-${voucher.expireDate?string("yyyy/MM/dd")}
                                                                                                                </div>&ndash;&gt;-->
                                                                                                                <#--&lt;#&ndash;< /div>&ndash;&gt;-->
                                                                                                                    <#--&lt;#&ndash;<div class="yhq-checkb">&ndash;&gt;-->
                                                                                                                        <#--&lt;#&ndash;<label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>">&ndash;&gt;-->
                                                                                                                            <#--&lt;#&ndash;<input type="checkbox" <#if voucher.isUse==0>disabled=""</#if> ><span></span>&ndash;&gt;-->
                                                                                                                                <#--&lt;#&ndash;< /label>&ndash;&gt;-->
                                                                                                                                    <#--&lt;#&ndash;< /div>&ndash;&gt;-->
                                                                                                                                        <#--&lt;#&ndash;< /li>&ndash;&gt;-->
                                                                                                                                            <#--&lt;#&ndash;< /#list>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;< /#if>&ndash;&gt;-->
                                                                                                                                                    <#--< /ul>-->
                                                                                                                                                        <#--<!--可用优惠券结束&ndash;&gt;-->
                                                                                                                                                            <#--<!--不可用优惠券开始&ndash;&gt;-->
                                                                                                                                                                <#--<ul class="yhq-common weishiyong bky tanbox">-->
                                                                                                                                                                    <#--<#assign unavailItemNum=0 />-->
                                                                                                                                                                    <#--<!--叠加券&ndash;&gt;-->
                                                                                                                                                                        <#--&lt;#&ndash;<#if (shop.unavailDjVoucherList ?? &&shop.unavailDjVoucherList?size>0)>&ndash;&gt;-->
                                                                                                                                                                            <#--&lt;#&ndash;<#list unavailDjVoucherList as voucher>&ndash;&gt;-->
                                                                                                                                                                                <#--&lt;#&ndash;<#assign unavailItemNum=unavailItemNum + 1 />&ndash;&gt;-->
                                                                                                                                                                                <#--&lt;#&ndash;<li class="<#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher_index}">&ndash;&gt;-->
                                                                                                                                                                                    <#--&lt;#&ndash;<div class="yhq-lb">&ndash;&gt;-->
                                                                                                                                                                                        <#--&lt;#&ndash;<div class="yhq-lb-top">&ndash;&gt;-->
                                                                                                                                                                                            <#--&lt;#&ndash;<#if voucher.voucherState==1>&ndash;&gt;-->
                                                                                                                                                                                                <#--&lt;#&ndash;<span class="price">
                                                                                                                                                                                                    ${voucher.discountRatio }
                                                                                                                                                                        </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="fuhao">折</span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#else>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="fuhao">￥</span><span class="price">
                                                                                                                                                    ${voucher.moneyInVoucher }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-foot">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;${voucher.minMoneyToEnableDesc }&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucher?? && voucher.maxMoneyInVoucher!=null && voucher.maxMoneyInVoucher!="" && voucher.maxMoneyInVoucher>0 ) >&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-foot">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;${voucher.maxMoneyInVoucherDesc }&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb-top">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="quan quan-die">
                                                                                                                                                        ${voucher.voucherTypeDesc}
                                                                                                                                                                                                    </span><span class="info">
                                                                                                                                                                                                        ${voucher.voucherDesc }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div style="height:30px;overflow:hidden;"></div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb-foot" style="position:absolute;bottom:10px;">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span>
                                                                                                                                                        ${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }
                                                                                                                                                        </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</li>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#list>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--<!--遍历不可用-非叠加券&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if (shop.unavailVoucherList ?? && shop.unavailVoucherList?size>0)>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#list shop.unavailVoucherList as voucher>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#assign unavailItemNum = unavailItemNum + 1 />&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<li class="<#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">&ndash;&gt;-->
                                                                                                                                                                                                        <#--&lt;#&ndash;<input type="hidden" name="voucherId" value="${voucher.id }"/>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-top">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if voucher.voucherState==1>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="price">
                                                                                                                                                    ${voucher.discountRatio }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="fuhao">折</span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#elseif voucher.voucherUsageWay==1>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="fuhao">￥</span><span class="price">
                                                                                                                                                    ${voucher.sourceMoneyInVoucher }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#else>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="fuhao">￥</span><span class="price">
                                                                                                                                                    ${voucher.moneyInVoucher }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-foot">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;${voucher.minMoneyToEnableDesc }&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-lb-foot">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;${voucher.maxMoneyInVoucherDesc }&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb-top">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if voucher.voucherType == 2>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class="quan">
                                                                                                                                                    ${voucher.voucherTypeDesc}
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<#if voucher.voucherType == 1>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span class=" quan quan-tong">
                                                                                                                                                                                                            ${voucher.voucherTypeDesc}
                                                                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                                                                    <#--&lt;#&ndash;< /#if>&ndash;&gt;-->
                                                                                                                                                                                                        <#--&lt;#&ndash;<#if voucher.voucherType==6>&ndash;&gt;-->
                                                                                                                                                                                                            <#--&lt;#&ndash;<span class="quan quan-die">
                                                                                                                                                                                                                ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                </span>&ndash;&gt;-->
                                                                                                                                                                                                                <#--&lt;#&ndash;< /#if>&ndash;&gt;-->
                                                                                                                                                                                                                    <#--&lt;#&ndash;<#if voucher.voucherType==5>&ndash;&gt;-->
                                                                                                                                                                                                                        <#--&lt;#&ndash;<span class="quan quan-xin">
                                                                                                                                                                                                                            ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                            </span>&ndash;&gt;-->
                                                                                                                                                                                                                            <#--&lt;#&ndash;< /#if>&ndash;&gt;-->
                                                                                                                                                                                                                                <#--&lt;#&ndash;<#if voucher.voucherType==7>&ndash;&gt;-->
                                                                                                                                                                                                                                    <#--&lt;#&ndash;<span class="quan quan-shop">
                                                                                                                                                                                                                                        ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                                        </span>&ndash;&gt;-->
                                                                                                                                                                                                                                        <#--&lt;#&ndash;< /#if>&ndash;&gt;-->
                                                                                                                                                                                                                                            <#--&lt;#&ndash;<span class="info" title="${voucher.shopName }">
                                                                                                                                                    ${voucher.shopName }
                                                                                                                                                    </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div style="height:30px;overflow:hidden;line-height:30px;">
                                                                                                                                                            ${voucher.voucherDesc }
                                                                                                                                                            </div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div class="yhq-rb-foot">&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<span>
                                                                                                                                                        ${voucher.voucherScope }
                                                                                                                                                        </span>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;<div style="font-size:12px;color:#999999;">
                                                                                                                                                    ${voucher.validDate?string("yyyy/MM/dd")}-${voucher.expireDate?string("yyyy/MM/dd")}
                                                                                                                                                    </div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</li>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#list>&ndash;&gt;-->
                                                                                                                                                <#--&lt;#&ndash;</#if>&ndash;&gt;-->
                                                                                                                                                <#--</ul>-->
                                                                                                                                                <#--<!--不可用优惠券结束&ndash;&gt;-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--</#if>-->
                                                                                                                                                <#--<#if shop.shopPatternCode != 'ybm'>-->
                                                                                                                                                <#--<div class=" not-ybm">-->
                                                                                                                                                                                                                                                <#--<!--可用优惠券开始&ndash;&gt;-->
                                                                                                                                                                                                                                                    <#--<#assign availItemNum=0 />-->
                                                                                                                                                                                                                                                    <#--<ul class="yhq-common weishiyong ky tanbox">-->
                                                                                                                                                                                                                                                        <#--<!--遍历可用-非叠加券&ndash;&gt;-->
                                                                                                                                                                                                                                                            <#--<#if (shop.shopSelectVouchers ?? && shop.shopSelectVouchers?size>0)>-->
                                                                                                                                                                                                                                                                <#--<#list shop.shopSelectVouchers as voucher>-->
                                                                                                                                                                                                                                                                    <#--<#assign availItemNum=availItemNum + 1 />-->
                                                                                                                                                                                                                                                                    <#--<li class="<#if (voucher.isUse==1)>cur </#if><#if voucher.isUse==0 || voucher.isUse==Null>ygq </#if><#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">-->
                                                                                                                                                                                                                                                                        <#--<input type="hidden" shopCode="${shop.shopCode}" shopPatternCode="${shop.shopPatternCode}" name="voucherId" value="${voucher.id }"/>-->
                                                                                                                                                <#--<input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>-->
                                                                                                                                                <#--<div class="yhq-lb">-->
                                                                                                                                                <#--<div class="yhq-lb-top">-->
                                                                                                                                                <#--<#if voucher.voucherState==1>-->
                                                                                                                                                <#--<span class="price">
                                                                                                                                                    ${voucher.discountRatio }
                                                                                                                                                    </span>-->
                                                                                                                                                <#--<span class="fuhao">折</span>-->
                                                                                                                                                <#--<#elseif voucher.voucherUsageWay==1>-->
                                                                                                                                                <#--<span class="fuhao">￥</span><span class="price">
                                                                                        ${voucher.sourceMoneyInVoucher }
                                                                                        </span>-->
                                                                                                                                                <#--<#else>-->
                                                                                                                                                <#--<span class="fuhao">￥</span><span class="price">
                                                                                        ${voucher.moneyInVoucher }
                                                                                        </span>-->
                                                                                                                                                <#--</#if>-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--<div class="yhq-lb-foot">-->
                                                                                                                                                <#--${voucher.minMoneyToEnableDesc }-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
                                                                                                                                                <#--<div class="yhq-lb-foot">-->
                                                                                                                                                <#--${voucher.maxMoneyInVoucherDesc }-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--</#if>-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--<div class="yhq-rb">-->
                                                                                                                                                <#--<div class="yhq-rb-top">-->
                                                                                                                                                <#--<#if voucher.voucherType == 2>-->
                                                                                                                                                <#--<span class="quan">
                                                                                        ${voucher.voucherTypeDesc}
                                                                                        </span>-->
                                                                                                                                                <#--</#if>-->
                                                                                                                                                <#--<#if voucher.voucherType == 1>-->
                                                                                                                                                <#--<span class=" quan quan-tong">
                                                                                                                                                                                                                                                                            ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                                                                            </span>-->
                                                                                                                                                                                                                                                                            <#--< /#if>-->
                                                                                                                                                                                                                                                                                <#--<#if voucher.voucherType==6>-->
                                                                                                                                                                                                                                                                                    <#--<span class="quan quan-die">
                                                                                                                                                                                                                                                                                        ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                                                                                        </span>-->
                                                                                                                                                                                                                                                                                        <#--< /#if>-->
                                                                                                                                                                                                                                                                                            <#--<#if voucher.voucherType==5>-->
                                                                                                                                                                                                                                                                                                <#--<span class="quan quan-xin">
                                                                                                                                                                                                                                                                                                    ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                                                                                                    </span>-->
                                                                                                                                                                                                                                                                                                    <#--< /#if>-->
                                                                                                                                                                                                                                                                                                        <#--<#if voucher.voucherType==7>-->
                                                                                                                                                                                                                                                                                                            <#--<span class="quan quan-shop">
                                                                                                                                                                                                                                                                                                                ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                                                                                                                </span>-->
                                                                                                                                                                                                                                                                                                                <#--< /#if>-->
                                                                                                                                                                                                                                                                                                                    <#--<span class="info" title="${voucher.shopName}">
                                                                                                                                                                                                                                                                                                                        ${voucher.shopName}
                                                                                                                                                                                                                                                                                                                        </span>-->
                                                                                                                                                                                                                                                                                                                        <#--< /div>-->
                                                                                                                                                                                                                                                                                                                            <#--<div style="height:30px;overflow:hidden;line-height:30px;">
                                                                                                                                                                                                                                                                                                                                ${voucher.voucherDesc }
                                                                                        </div>-->
                                                                                                                                                <#--<div class="yhq-rb-foot">-->
                                                                                                                                                <#--<span>
                                                                                        ${voucher.voucherScope }
                                                                                        </span>-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--<div style="font-size:12px;color:#999999;">
                                                                                        ${voucher.validDate?string("yyyy/MM/dd")}-${voucher.expireDate?string("yyyy/MM/dd")}
                                                                                                            </div>-->
                                                                                                            <#--< /div>-->
                                                                                                                <#--<div class="yhq-checkb">-->
                                                                                                                    <#--<label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>">-->
                                                                                                                        <#--<input type="checkbox" <#if voucher.isUse==0>disabled=""</#if> ><span></span>-->
                                                                                                                            <#--< /label>-->
                                                                                                                                <#--< /div>-->
                                                                                                                                    <#--< /li>-->
                                                                                                                                        <#--< /#list>-->
                                                                                                                                            <#--< /#if>-->
                                                                                                                                                <#--< /ul>-->
                                                                                                                                                    <#--<!--可用优惠券结束&ndash;&gt;-->
                                                                                                                                                        <#--<!--不可用优惠券开始&ndash;&gt;-->
                                                                                                                                                            <#--<ul class="yhq-common weishiyong bky tanbox">-->
                                                                                                                                                                <#--<#assign unavailItemNum=0 />-->
                                                                                                                                                                <#--<!--遍历不可用-非叠加券&ndash;&gt;-->
                                                                                                                                                                    <#--<#if (shop.shopUnSelectVouchers ?? && shop.shopUnSelectVouchers?size>0)>-->
                                                                                                                                                                        <#--<#list shop.shopUnSelectVouchers as voucher>-->
                                                                                                                                                                            <#--<#assign unavailItemNum=unavailItemNum + 1 />-->
                                                                                                                                                                            <#--<li class="<#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">-->
                                                                                                                                                                                <#--<input type="hidden" name="voucherId" value="${voucher.id }"/>-->
                                                                                                                                                <#--<input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>-->
                                                                                                                                                <#--<div class="yhq-lb">-->
                                                                                                                                                <#--<div class="yhq-lb-top">-->
                                                                                                                                                <#--<#if voucher.voucherState==1>-->
                                                                                                                                                <#--<span class="price">
                                                                                        ${voucher.discountRatio }
                                                                                        </span>-->
                                                                                                                                                <#--<span class="fuhao">折</span>-->
                                                                                                                                                <#--<#elseif voucher.voucherUsageWay==1>-->
                                                                                                                                                <#--<span class="fuhao">￥</span><span class="price">
                                                                                        ${voucher.sourceMoneyInVoucher }
                                                                                        </span>-->
                                                                                                                                                <#--<#else>-->
                                                                                                                                                <#--<span class="fuhao">￥</span><span class="price">
                                                                                        ${voucher.moneyInVoucher }
                                                                                        </span>-->
                                                                                                                                                <#--</#if>-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--<div class="yhq-lb-foot">-->
                                                                                                                                                <#--${voucher.minMoneyToEnableDesc }-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
                                                                                                                                                <#--<div class="yhq-lb-foot">-->
                                                                                                                                                <#--${voucher.maxMoneyInVoucherDesc }-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--</#if>-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--<div class="yhq-rb">-->
                                                                                                                                                <#--<div class="yhq-rb-top">-->
                                                                                                                                                <#--<#if voucher.voucherType == 2>-->
                                                                                                                                                <#--<span class="quan">
                                                                                        ${voucher.voucherTypeDesc}
                                                                                        </span>-->
                                                                                                                                                <#--</#if>-->
                                                                                                                                                <#--<#if voucher.voucherType == 1>-->
                                                                                                                                                <#--<span class=" quan quan-tong">
                                                                                                                                                                                    ${voucher.voucherTypeDesc}
                                                                                                                                                                                    </span>-->
                                                                                                                                                                                    <#--< /#if>-->
                                                                                                                                                                                        <#--<#if voucher.voucherType==6>-->
                                                                                                                                                                                            <#--<span class="quan quan-die">
                                                                                                                                                                                                ${voucher.voucherTypeDesc}
                                                                                                                                                                                                </span>-->
                                                                                                                                                                                                <#--< /#if>-->
                                                                                                                                                                                                    <#--<#if voucher.voucherType==5>-->
                                                                                                                                                                                                        <#--<span class="quan quan-xin">
                                                                                                                                                                                                            ${voucher.voucherTypeDesc}
                                                                                                                                                                                                            </span>-->
                                                                                                                                                                                                            <#--< /#if>-->
                                                                                                                                                                                                                <#--<#if voucher.voucherType==7>-->
                                                                                                                                                                                                                    <#--<span class="quan quan-shop">
                                                                                                                                                                                                                        ${voucher.voucherTypeDesc}
                                                                                                                                                                                                                        </span>-->
                                                                                                                                                                                                                        <#--< /#if>-->
                                                                                                                                                                                                                            <#--<span class="info" title="${voucher.shopName}">
                                                                                                                                                                                                                                ${voucher.shopName}
                                                                                                                                                                                                                                </span>-->
                                                                                                                                                                                                                                <#--< /div>-->
                                                                                                                                                                                                                                    <#--<div style="height:30px;overflow:hidden;line-height:30px;">
                                                                                                                                                                                                                                        ${voucher.voucherDesc }
                                                                                        </div>-->
                                                                                                                                                <#--<div class="yhq-rb-foot">-->
                                                                                                                                                <#--<span>
                                                                                        ${voucher.voucherScope }
                                                                                        </span>-->
                                                                                                                                                <#--</div>-->
                                                                                                                                                <#--<div style="font-size:12px;color:#999999;">
                                                                                        ${voucher.validDate?string("yyyy/MM/dd")}-${voucher.expireDate?string("yyyy/MM/dd")}
                                                                                                        </div>-->
                                                                                                        <#--< /div>-->
                                                                                                            <#--< /li>-->
                                                                                                                <#--< /#list>-->
                                                                                                                    <#--< /#if>-->
                                                                                                                        <#--< /ul>-->
                                                                                                                            <#--<!--不可用优惠券结束&ndash;&gt;-->
                                                                                                                                <#--< /div>-->
                                                                                                                                    <#--< /#if>-->
                                                                                                                                        <#--< /div>-->
                                                                                                                                            <#--< /div>-->
                                                                -->
                                                            <#if (company.shops?size> 1)>
                                                                <!--店铺小计-->
                                                                <div style="background:#ffffff;overflow: hidden;margin-bottom: 8px;">
                                                                    <div class="totalbox fr"
                                                                        style="display: inline-block;width:400px;clear:none;">
                                                                        <div class="rbox-total fr">
                                                                            <div class="fd-warp">
                                                                                <div class="right-fd-box">
                                                                                    <div class="list-item">
                                                                                        <span class="spec">活动优惠:</span><span
                                                                                            class="red">-￥</span><span
                                                                                            class="red"
                                                                                            id="${shop.shopCode}promoTotalAmt">
                                                                                            ${shop.promoTotalAmt?string('0.00')}
                                                                                        </span>
                                                                                    </div>
                                                                                    <div class="list-item">
                                                                                        <span class="spec">优惠券:</span><span
                                                                                            class="red">-￥</span><span
                                                                                            class="red"
                                                                                            id="${shop.shopCode}voucherTotalAmt">
                                                                                            ${shop.voucherTotalAmt?string('0.00')}
                                                                                        </span>
                                                                                    </div>
                                                                                    <div class="list-item">
                                                                                        <span class="spec"
                                                                                            style="font-size:16px;font-weight:500;">小计:</span><span
                                                                                            style="font-size:16px;font-weight:500;">￥</span><span
                                                                                            style="font-size:16px;font-weight:500;"
                                                                                            name="balancespan"
                                                                                            id="${shop.shopCode}payAmount">
                                                                                            ${shop.payAmount?string('0.00')}
                                                                                        </span>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </#if>
                                                        </div>
                                                    </div>
                                                </#list>
                                                <!--遍历店铺数据结束-->
                                            </#if>
                                            <!-- 遍历店铺数据判断 -->
                                        </div>
                                        <!--店铺备注留言-->
                                        <#assign itemIndex=0>
                                        <#list company.shops as shop>
                                            <#if itemIndex==0>
                                                <div style="margin-top:8px;margin-bottom:8px;overflow: hidden;background:#ffffff;padding-left: 40px;">
                                                    <div class="bzly fl" style="border:none;padding-bottom: 0;">
                                                        <div class="address-top-title" style="display: inline-block;">备注留言</div>
                                                        <input type="text" class="companyRemark" company='${company.companyCode}' placeholder="选填，请先和商家协商一致">
                                                        <!-- <div class="kuaidi">提示：抱歉，平台目前不支持指定快递。</div> -->
                                                    </div>
                                                    <!--总计-->
                                                    <div class="totalbox fr" style="display: inline-block;width:460px;clear:none;">
                                                        <div class="rbox-total fr">
                                                            <div class="spzjbox">
                                                                共
                                                                <span class="zhongshu" id="${company.companyCode}productVarietyNum">
                                                                    ${shop.productVarietyNum}
                                                                </span>
                                                                    种商品，总件数
                                                                <span class="zongjianshu" id="${company.companyCode}productTotalNum">
                                                                    ${shop.productTotalNum}
                                                                </span>
                                                                    商品总计：￥
                                                                <span id="${company.companyCode}totalAmount">
                                                                    ${shop.totalAmount?string('0.00')}
                                                                </span>
                                                            </div>
                                                            <div class="fd-warp">
                                                                <!--中间竖线-->
                                                                <#--<div class="mid-line"></div>-->
                                                                <div class="right-fd-box">
                                                                    <div class="list-item">
                                                                        <span class="spec" style="width:370px;">
                                                                            <#if shop.freightTipsShowStatus ?? && shop.freightTipsShowStatus==1>
                                                                                <p id="freightTipsP" style="font-size:12px;color:#666666;display: inline-block;background: rgba(255,243,206,0.3);border-radius: 1px;padding:2px 5px;">
                                                                                    实付金额满
                                                                                    <span class="red">
                                                                                        ${shop.freeDeliveryAmount}
                                                                                    </span>
                                                                                        元包邮，还需凑
                                                                                    <span class="red">
                                                                                        ${shop.freeFreightDiffAmount}元
                                                                                        <#if shop.freightUrlText?? && shop.freightUrlText !=''>
                                                                                            <a href="/merchant/freight/list.htm?route=settle" style="color:#e73734;padding-left:10px;">
                                                                                                ${shop.freightUrlText}>
                                                                                            </a>
                                                                                        </#if>
                                                                                    </span>
                                                                                </p>
                                                                            </#if>
                                                                            <#if company.freightIconShowStatus?? && company.freightIconShowStatus==1>
                                                                                <a id="freightTipsA" data-code="${company.mainShopCode}" href="javascript:void(0)" class="fplx-a get-fei" data-keyboard="false">
                                                                                    <img src="/static/images/yunfei-tip.png" alt="" style="top: -2px;position: relative;width: 14px;">
                                                                                </a>
                                                                            </#if>
                                                                            运费:
                                                                        </span>
                                                                        <span class="red">￥</span>
                                                                        <span class="red" id="${company.companyCode}freightTotalAmt">
                                                                            ${company.freightTotalAmt?string('0.00')}
                                                                        </span>
                                                                    </div>
                                                                    <div class="list-item">
                                                                        <span class="spec" style="width:370px;">活动优惠:</span>
                                                                        <span class="red">-￥</span>
                                                                        <span class="red"
                                                                            id="${company.companyCode}promoTotalAmt">
                                                                            ${shop.promoTotalAmt?string('0.00')}
                                                                        </span>
                                                                    </div>
                                                                    <div class="list-item">
                                                                        <#--<div style="width:337px;text-align:right;display: inline-block;"></div>-->
                                                                        <span class="spec" style="width:370px;">
                                                                            优惠券:
                                                                        </span>
                                                                        <span class="red">-￥</span>
                                                                        <span class="red" id="${company.companyCode}voucherTotalAmt">
                                                                            ${shop.voucherTotalAmt?string('0.00')}
                                                                        </span>
                                                                    </div>
                                                                    <#--  <div class="list-item">
                                                                        <span class="spec"
                                                                            style="font-size:16px;font-weight:500;width:335px;">总计:</span><span
                                                                            style="font-size:16px;font-weight:500;">￥</span><span
                                                                            style="font-size:16px;font-weight:500;"
                                                                            name="balancespan"
                                                                            id="${company.companyCode}payAmount">
                                                                            ${shop.payAmount?string('0.00')}
                                                                        </span>
                                                                    </div>  -->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </#if>
                                            <#assign itemIndex=itemIndex+1>
                                        </#list>
                                        <!--小药药自营公司结束-->
                                    </#if>
                                    <#if company.isThirdCompany==1>
                                        <!--POP公司开始-->
                                        <div class="list-ziying defaultbox">
                                            <!--自营公司名称 -->
                                            <div class="cgd-qy" style="background: #fff;">
                                                <img src="/static/images/pop-shop-icon.png" alt=""
                                                    style="margin-right: 3px;top: -1px;position: relative;width: 18px;">
                                                <span class="qy-title">
                                                    ${company.companyName}
                                                </span>
                                            </div>
                                            <!--次日达相关配置-->
                                            <#if company.nextDayDeliveryDto ??>
                                                <div class="cuxiaobox" style="height: 40px;line-height: 40px;background: #fff;">
                                                    <span class="next-day-service-tag" 
                                                        style="border: 1px solid #04BE88;margin-left: 20px;font-size: 12px;border-radius: 4px;background: #04BE88;padding: 1px 0px 1px 2px;">
                                                        <#if company.nextDayDeliveryDto.tagDto.appIcon??>
                                                            <img src="${productImageUrl}${company.nextDayDeliveryDto.tagDto.appIcon}" 
                                                                alt="${company.nextDayDeliveryDto.tagDto.name!''}" 
                                                                style="height: 12px; width: auto; object-fit: cover;display: inline-block;">
                                                        </#if>
                                                        <span style="color:#fff;border: none">
                                                            ${company.nextDayDeliveryDto.tagDto.name!''}
                                                        </span>
                                                    </span>
                                                    <span class="next-day-service-span" style="float: none !important">${company.nextDayDeliveryDto.tips}</span>
                                                </div>
                                            </#if>
                                            <!-- 遍历店铺数据判断 -->
                                            <#if company.shops ?? && (company.shops?size>0) >
                                                <!--遍历店铺数据开始-->
                                                <#list company.shops as shop>
                                                    <!--店铺结构开始-->
                                                    <#assign showCartItemNum=0 />
                                                    <div class="pro-box">
                                                        <!--POP没有店铺名称-->
                                                                                <#--<div class="cgd-qy cgd-qy1">&ndash;&gt;-->
                                                                                <#--<span class="qy-title"> ${shop.shopName} </span>-->
                                                                                <#--< /div>-->
                                                        <!--遍历分组数据判段-->
                                                        <#if shop.groups ?? && (shop.groups?size>0) >
                                                            <!--遍历分组数据开始-->
                                                            <#list shop.groups as shoppingCartGroup>
                                                                <!--常规分组名称-->
                                                                <#if shoppingCartGroup.type==9>
                                                                    <div class="taocanbox-onther">
                                                                        <#--<span class="new">其它商品：</span>-->
                                                                    </div>
                                                                </#if>
                                                                <!--活动分组名称-->
                                                                <#if (shoppingCartGroup.type!=10 && shoppingCartGroup.type!=9)>
                                                                    <div class="manjianbox" name="${shoppingCartGroup.id}">
                                                                        <#if shoppingCartGroup.title??>
                                                                            <span class="title">
                                                                                <#if shoppingCartGroup.type==1>
                                                                                    满减
                                                                                <#elseif shoppingCartGroup.type==2>
                                                                                    满折
                                                                                <#elseif shoppingCartGroup.type==3>
                                                                                    满赠
                                                                                <#elseif shoppingCartGroup.type==4>
                                                                                    满减赠
                                                                                <#elseif shoppingCartGroup.type==6>
                                                                                    一口价
                                                                                <#elseif group.type==11>
                                                                                    满返
                                                                                <#elseif group.type==12>
                                                                                    满减返券
                                                                                </#if>
                                                                            </span>
                                                                            <span class="info">
                                                                                ${shoppingCartGroup.title}
                                                                            </span>
                                                                            <!-- 拼团或批购包邮直接进结算页 -->
                                                                            <#if groupSkuId??>
                                                                                <#if (shoppingCartGroup.canGoToGiftPool == true)>
                                                                                    <a id='fullGiveModalBtn' style="cursor:pointer;color:#00B377;" onclick="fullNumSelectGive(${orderSettle.bizSource},${shoppingCartGroup.giftPoolActHasSelectedNum},${shoppingCartGroup.giftPoolActTotalSelectedNum},${shoppingCartGroup.promoId})" style="color: #15BA81;margin: 0 5px;">
                                                                                        <#if (shoppingCartGroup.isGiveUpGift == false)>
                                                                                            选择赠品(${shoppingCartGroup.giftPoolActHasSelectedNum}/${shoppingCartGroup.giftPoolActTotalSelectedNum})<img src="/static/images/right-arrow.png" alt="" style="margin-right: 3px;top: -1px;position: relative;width: 12px;">
                                                                                            <#if (shoppingCartGroup.giftPoolActCanSelectedNum>0) >
                                                                                                <input type="hidden" class="giftPoolActCanSelectedNum"  onclick="fullNumSelectGive(${orderSettle.bizSource},${shoppingCartGroup.giftPoolActHasSelectedNum},${shoppingCartGroup.giftPoolActTotalSelectedNum},${shoppingCartGroup.promoId})"/>
                                                                                            </#if>
                                                                                        <#else>
                                                                                            已放弃赠品
                                                                                        </#if>
                                                                                        <input type="hidden" class="giftPoolActTitle"  onclick="fullNumSelectGive(${orderSettle.bizSource},${shoppingCartGroup.giftPoolActHasSelectedNum},${shoppingCartGroup.giftPoolActTotalSelectedNum},${shoppingCartGroup.promoId}, true)"/>
                                                                                    </a>
                                                                                </#if>
                                                                            </#if>
                                                                        </#if>
                                                                        <#if shoppingCartGroup.type==5>
                                                                            <input type="radio" name="giftId"
                                                                                style="margin-left: 17px;margin-right: 5px;"
                                                                                value="${shoppingCartGroup.id}"
                                                                                <#if shoppingCartGroup.selectStatus==1>checked="true"
                                                                            data-mutex-check="true"
                                                                        </#if>
                                                                        <#if shoppingCartGroup.selectStatus==0> data-mutex-check="false"</#if> >
                                                                            <span class="title_hui">物料心愿单礼包</span>
                                                                            <span class="info_hui" value="${shoppingCartGroup.id}">不需要</span>
                                                                            <input type="hidden" name="giftIds" value="${shoppingCartGroup.id}" />
                                                                            <input type="hidden" name="giftTotalAmount" value="${shoppingCartGroup.totalAmount}" />
                                                                        </#if>
                                                                    </div>
                                                                </#if>
                                                                <!--遍历分组商品-->
                                                                <div class="list-default-box defaultH">
                                                                    <#if shoppingCartGroup.sorted ?? && (shoppingCartGroup.sorted?size>0) >
                                                                        <!--遍历分组商品开始-->
                                                                        <#list shoppingCartGroup.sorted as shoppingCartItem>
                                                                            <#assign showCartItemNum=showCartItemNum + 1 />
                                                                            <!--套餐商品开始-->
                                                                            <#if shoppingCartItem.itemType=3>
                                                                                <!--套餐商品标题-->
                                                                                <div class="taocanbox">
                                                                                    <span class="new">套餐商品</span>
                                                                                </div>
                                                                                <div class="bodybox taocanspe">
                                                                                    <!---套餐商品列表开始-->
                                                                                    <#if shoppingCartItem.subItemList ?? && (shoppingCartItem.subItemList?size>0) >
                                                                                        <#list shoppingCartItem.subItemList as tcList>
                                                                                            <ul>
                                                                                                <li class="lib1">
                                                                                                    <div class="l-box fl">
                                                                                                        <a href="/search/skuDetail/${tcList.id}.htm?<#if company.isVirtualSupplier == true>isMainProductVirtualSupplier=true</#if>" target="_blank" title="${tcList.commonName }">
                                                                                                            <img src="${productImageUrl}/ybm/product/min/${tcList.imageUrl }" alt="${tcList.commonName}" onerror="this.src='/static/images/default-big.png'">
                                                                                                        </a>
                                                                                                        <!--标签-->
                                                                                                        <#if shoppingCartItem.item.valid=0>
                                                                                                            <div class="bq-box">
                                                                                                                <img src="img/bq-qiangguang.png" alt="">
                                                                                                            </div>
                                                                                                        </#if>
                                                                                                        <#if tcList.blackSku=1>
                                                                                                            <!--不参与返点提示-->
                                                                                                            <#if (tcList.blackSkuText??) && (tcList.blackSkuText!="" )>
                                                                                                                <div class="nofd">
                                                                                                                    ${tcList.blackSkuText}
                                                                                                                </div>
                                                                                                            </#if>
                                                                                                        </#if>
                                                                                                    </div>
                                                                                                    <div class="r-box fr">
                                                                                                        <div class="lib1-row1 text-overflow">
                                                                                                            <#if tcList.tagTitle ?? && (tcList.tagTitle != "" && tcList.tagTitle != null)>
                                                                                                                <span class="tag-title">${tcList.tagTitle}</span>
                                                                                                            </#if>
                                                                                                            <a href="/search/skuDetail/${tcList.id}.htm?<#if company.isVirtualSupplier == true>isMainProductVirtualSupplier=true</#if>" target="_blank" title="${tcList.commonName }">
                                                                                                                ${tcList.sku.showName}
                                                                                                            </a>
                                                                                                        </div>
                                                                                                        <div class=" lib1-row3">
                                                                                                            <div class="row-biaoqian" id="${tcList.uniqueKey}row-biaoqian">
                                                                                                                <#if tcList.tagList ?? && (tcList.tagList?size>0) >
                                                                                                                    <#list tcList.tagList as item>
                                                                                                                        <#if (item_index < 3)>
                                                                                                                            <span class="<#if item.uiType == 1>linqi</#if>
                                                                                                                                        <#if item.uiType == 2>quan</#if>
                                                                                                                                        <#if item.uiType == 3>manjian</#if>
                                                                                                                                        <#if item.uiType == 4>default</#if>
                                                                                                                                        <#if item.uiType == 5>yibao</#if> ">
                                                                                                                                ${item.name}
                                                                                                                            </span>
                                                                                                                        </#if>
                                                                                                                    </#list>
                                                                                                                </#if>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                        <div class="lib1-row2 text-overflow">
                                                                                                            <span class="title">规　　格：</span>
                                                                                                            <span class="info">
                                                                                                                ${tcList.sku.spec}
                                                                                                            </span>
                                                                                                        </div>
                                                                                                        <div class="lib1-row4 text-overflow">
                                                                                                            <span class="title">生产厂家：</span>
                                                                                                            <span class="info">
                                                                                                                ${tcList.sku.manufacturer}
                                                                                                            </span>
                                                                                                        </div>
                                                                                                        <div class=" lib1-row3">
                                                                                                            <div class="row-biaoqian">
                                                                                                                <#if tcList.tagWholeOrderList ?? && (tcList.tagWholeOrderList?size>0) >
                                                                                                                    <#list tcList.tagWholeOrderList as item >
                                                                                                                        <div class="synthesis">
                                                                                                                            <div class="synthesis-biao">
                                                                                                                                <#if item.description ?? && item.description != ''>
                                                                                                                                    <span class="synthesis-biao-left">${item.name}</span>
                                                                                                                                    <span class="synthesis-biao-shuoming">${item.description}</span>
                                                                                                                                <#else>
                                                                                                                                    <span class="synthesis-biao-left-single">${item.name}</span>
                                                                                                                                </#if>
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                    </#list>
                                                                                                                </#if>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </li>
                                                                                                <li class="li_mingxi" id="${tcList.uniqueKey}">
                                                                                                    <div class="mxrow1">
                                                                                                        <span class="mxrow_tit">实付金额：</span>
                                                                                                        <span class="mxrow_info" id="${tcList.uniqueKey}realPayAmount">
                                                                                                            ￥${tcList.realPayAmount}
                                                                                                        </span>
                                                                                                        <span class="mxrow_tit">优惠金额：</span>
                                                                                                        <span class="mxrow_info" id="${tcList.uniqueKey}discountAmount">
                                                                                                            ￥${tcList.discountAmount}
                                                                                                        </span>
                                                                                                    </div>
                                                                                                    <div class="mxrow2">
                                                                                                        <span class="mxrow_tit">余额抵扣：</span>
                                                                                                        <span class="mxrow_info" id="${tcList.uniqueKey}useBalanceAmount">
                                                                                                            ￥${tcList.useBalanceAmount}
                                                                                                        </span>
                                                                                                        <span class="mxrow_tit">返点金额：</span>
                                                                                                        <span class="mxrow_info" id="${tcList.uniqueKey}balanceAmount">
                                                                                                            ￥${tcList.balanceAmount}
                                                                                                        </span>
                                                                                                    </div>
                                                                                                    <div class="mxrow2">
                                                                                                        <span class="mxrow_tit">实付价：</span>
                                                                                                        <span class="mxrow_info ${tcList.uniqueKey}purchasePriceK">
                                                                                                            ￥
                                                                                                            <#if 1==tcList.sku.isGive>
                                                                                                                ${tcList.sku.fob?string('0.00')}
                                                                                                            <#elseif tcList.purchasePrice ??>
                                                                                                                ${tcList.purchasePrice?string('0.00')}
                                                                                                            <#else>
                                                                                                                0.00
                                                                                                            </#if>
                                                                                                        </span>
                                                                                                        <span class="mxrow_tit">成本价：</span>
                                                                                                        <span class="mxrow_info  ${tcList.uniqueKey}costPriceK">
                                                                                                            ￥
                                                                                                            <#if 1==tcList.sku.isGive>
                                                                                                                ${tcList.sku.fob?string('0.00')}
                                                                                                            <#elseif tcList.costPrice ??>
                                                                                                                ${tcList.costPrice?string('0.00')}
                                                                                                            <#else>
                                                                                                                0.00
                                                                                                            </#if>
                                                                                                        </span>
                                                                                                    </div>
                                                                                                </li>
                                                                                                <li class="lib3">
                                                                                                    <div class="zkj">
                                                                                                        ￥${tcList.sku.fob}
                                                                                                    </div>
                                                                                                </li>
                                                                                                <li class="lib5">
                                                                                                    <span>x${tcList.amount}
                                                                                                    </span>
                                                                                                </li>
                                                                                                <li class="lib4"></li>
                                                                                            </ul>
                                                                                        </#list>
                                                                                        <!--套餐小计-->
                                                                                        <div class="taocanxj">
                                                                                            <div class="zkj">
                                                                                                ￥${shoppingCartItem.item.subtotal}
                                                                                            </div>
                                                                                        </div>
                                                                                    </#if>
                                                                                </div>
                                                                                <!---套餐商品列表结束-->
                                                                            </#if>
                                                                            <!--套餐商品结束-->
                                                                            <!--非套餐商品开始-->
                                                                            <#if shoppingCartItem.itemType!=3>
                                                                                <div class="bodybox <#if shoppingCartGroup.type==5>bigGift${shoppingCartGroup.id}</#if>">
                                                                                    <ul>
                                                                                        <li class="lib1">
                                                                                            <div class="l-box fl">
                                                                                                <a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm?<#if company.isVirtualSupplier == true>isMainProductVirtualSupplier=true</#if>" target="_blank" title="${shoppingCartItem.item.sku.showName }">
                                                                                                    <img src="${productImageUrl}/ybm/product/min/${shoppingCartItem.item.sku.imageUrl }" alt="${shoppingCartItem.item.sku.showName}"
                                                                                                            onerror="this.src='/static/images/default-big.png'">
                                                                                                    <div class="bq-box">
                                                                                                        <#if shoppingCartItem.item.sku.status==2>
                                                                                                            <img src="/static/images/product/bq-shouqing.png" alt="">
                                                                                                        </#if>
                                                                                                        <#if shoppingCartItem.item.sku.status==4 && shoppingCartItem.item.sku.isGive==0>
                                                                                                            <img src="/static/images/product/bq-xiajia.png" alt="">
                                                                                                        </#if>
                                                                                                    </div>
                                                                                                    <#if shoppingCartItem.item.blackSku=1>
                                                                                                        <!--不参与返点提示-->
                                                                                                        <#if (shoppingCartItem.item.blackSkuText??) && (shoppingCartItem.item.blackSkuText!="" )>
                                                                                                            <div class="nofd">
                                                                                                                ${shoppingCartItem.item.blackSkuText}
                                                                                                            </div>
                                                                                                        </#if>
                                                                                                    </#if>
                                                                                                </a>
                                                                                            </div>
                                                                                            <div class="r-box fr">
                                                                                                <div class="lib1-row1 text-overflow">
                                                                                                    <#if shoppingCartItem.item.tagTitle ?? && (shoppingCartItem.item.tagTitle != "" && shoppingCartItem.item.tagTitle != null)>
                                                                                                        <span class="tag-title">${shoppingCartItem.item.tagTitle}</span>
                                                                                                    </#if>
                                                                                                    <span>
                                                                                                        <a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm?<#if company.isVirtualSupplier == true>isMainProductVirtualSupplier=true</#if>" target="_blank"
                                                                                                            title="${shoppingCartItem.item.sku.showName }">
                                                                                                            <#if shoppingCartItem.item.isShow806 || shoppingCartItem.item.gift>
                                                                                                                <div class="bq806">
                                                                                                                    <img src="/static/images/bq806.png" alt="">
                                                                                                                </div>
                                                                                                            </#if>
                                                                                                            <#if shoppingCartItem.item.agent == 1>
                                                                                                                <span class="dujia">独家</span>
                                                                                                            </#if>
                                                                                                            ${shoppingCartItem.item.sku.showName }
                                                                                                        </a>
                                                                                                    </span>
                                                                                                </div>
                                                                                                <div class="lib1-row3">
                                                                                                    <div class="row-biaoqian" id="${shoppingCartItem.item.uniqueKey}row-biaoqian">
                                                                                                        <#if shoppingCartItem.item.tagList ?? && (shoppingCartItem.item.tagList?size>0) >
                                                                                                            <#list shoppingCartItem.item.tagList as item>
                                                                                                                <#if (item_index < 3)>
                                                                                                                    <span class="<#if item.uiType == 1>linqi</#if>
                                                                                                                                    <#if item.uiType == 2>quan</#if>
                                                                                                                                    <#if item.uiType == 3>manjian</#if>
                                                                                                                                    <#if item.uiType == 4>default</#if>
                                                                                                                                    <#if item.uiType == 5>yibao</#if> ">
                                                                                                                        ${item.name}
                                                                                                                    </span>
                                                                                                                </#if>
                                                                                                            </#list>
                                                                                                        </#if>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="lib1-row5">
                                                                                                    <div class="row-last">
                                                                                                        <#if (shoppingCartItem.item.sku.suggestPrice ?? ) && (shoppingCartItem.item.sku.suggestPrice !='' )>
                                                                                                            <div class="kongxiao-box">
                                                                                                                <span class="s-kx">零售价</span>
                                                                                                                <span class="jg">
                                                                                                                    ￥${shoppingCartItem.item.sku.suggestPrice}
                                                                                                                </span>
                                                                                                            </div>
                                                                                                        </#if>
                                                                                                        <#if shoppingCartItem.item.sku.grossMargin ?? && shoppingCartItem.item.sku.grossMargin !=''>
                                                                                                            <div class="maoli-box">
                                                                                                                <span class="s-ml">毛利</span>
                                                                                                                <span class="jg">
                                                                                                                    ${shoppingCartItem.item.sku.grossMargin}
                                                                                                                </span>
                                                                                                            </div>
                                                                                                        </#if>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="lib1-row2 text-overflow">
                                                                                                    <span class="title">规　　格：</span>
                                                                                                    <span class="info">
                                                                                                        ${shoppingCartItem.item.sku.spec }
                                                                                                    </span>
                                                                                                </div>
                                                                                                <div class="lib1-row4 text-overflow">
                                                                                                    <span class="title">生产厂家：</span>
                                                                                                    <span class="info">
                                                                                                        ${shoppingCartItem.item.sku.manufacturer }
                                                                                                    </span>
                                                                                                </div>
                                                                                                <div class="lib1-row3">
                                                                                                    <div class="row-biaoqian" id="${shoppingCartItem.item.uniqueKey}row-biaoqian">
                                                                                                        <#if shoppingCartItem.item.tagWholeOrderList ?? && (shoppingCartItem.item.tagWholeOrderList?size>0) >
                                                                                                            <#list shoppingCartItem.item.tagWholeOrderList as item>
                                                                                                                <div class="synthesis">
                                                                                                                    <div class="synthesis-biao">
                                                                                                                        <#if item.description ?? && item.description != ''>
                                                                                                                            <span class="synthesis-biao-left">${item.name}</span>
                                                                                                                            <span class="synthesis-biao-shuoming">${item.description}</span>
                                                                                                                        <#else>
                                                                                                                            <span class="synthesis-biao-left-single">${item.name}</span>
                                                                                                                        </#if>
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                            </#list>
                                                                                                        </#if>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </li>
                                                                                        <li class="li_mingxi">
                                                                                            <div class="mxrow1">
                                                                                                <span class="mxrow_tit">实付金额：</span>
                                                                                                <span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}realPayAmount">
                                                                                                    ￥${shoppingCartItem.item.realPayAmount}
                                                                                                </span>
                                                                                                <span class="mxrow_tit">优惠金额：</span>
                                                                                                <span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}discountAmount">
                                                                                                    ￥${shoppingCartItem.item.discountAmount}
                                                                                                </span>
                                                                                            </div>
                                                                                            <div class="mxrow2">
                                                                                                <span class="mxrow_tit">余额抵扣：</span>
                                                                                                <span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}useBalanceAmount">
                                                                                                    ￥${shoppingCartItem.item.useBalanceAmount}
                                                                                                </span>
                                                                                                <span class="mxrow_tit">返点金额：</span>
                                                                                                <span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}balanceAmount">
                                                                                                    ￥${shoppingCartItem.item.balanceAmount}
                                                                                                </span>
                                                                                            </div>
                                                                                            <div class="mxrow2">
                                                                                                <span class="mxrow_tit">实付价：</span>
                                                                                                <span class="mxrow_info ${shoppingCartItem.item.uniqueKey}purchasePrice">
                                                                                                    ￥
                                                                                                    <#if 1==shoppingCartItem.item.sku.isGive>
                                                                                                        ${shoppingCartItem.item.price?string('0.00')}
                                                                                                    <#elseif shoppingCartItem.item.purchasePrice ??>
                                                                                                        ${shoppingCartItem.item.purchasePrice?string('0.00')}
                                                                                                    <#else>
                                                                                                        0.00
                                                                                                    </#if>
                                                                                                </span>
                                                                                                <span class="mxrow_tit">成本价：</span>
                                                                                                <span class="mxrow_info  ${shoppingCartItem.item.uniqueKey}costPrice">
                                                                                                    ￥
                                                                                                    <#if 1==shoppingCartItem.item.sku.isGive>
                                                                                                        ${shoppingCartItem.item.price?string('0.00')}
                                                                                                    <#elseif shoppingCartItem.item.costPrice ??>
                                                                                                        ${shoppingCartItem.item.costPrice?string('0.00')}
                                                                                                    <#else>
                                                                                                        0.00
                                                                                                    </#if>
                                                                                                </span>
                                                                                            </div>
                                                                                        </li>
                                                                                        <li class="lib3">
                                                                                            <div class="zkj">
                                                                                                ￥${shoppingCartItem.item.price }
                                                                                            </div>
                                                                                        </li>
                                                                                        <li class="lib5">
                                                                                            <span>
                                                                                                x${shoppingCartItem.item.amount }
                                                                                            </span>
                                                                                        </li>
                                                                                        <li class="lib4">
                                                                                            <div class="zkj">
                                                                                                ￥${shoppingCartItem.item.subtotal }
                                                                                            </div>
                                                                                        </li>
                                                                                    </ul>
                                                                                </div>
                                                                            </#if>
                                                                            <!--非套餐商品结束-->
                                                                        </#list>
                                                                        <!--遍历分组商品结束-->
                                                                    </#if>
                                                                </div>
                                                                <!--遍历分组商品判定结束-->
                                                            </#list>
                                                            <!--遍历分组数据结束-->
                                                        </#if>
                                                        <!--遍历分组数据判定结束-->
                                                    </div>
                                                    <!--店铺结构结束-->
                                                    <!--查看更多-->
                                                    <!--默认页面展示5行，更多数据通过点击查看更多-->
                                                    <#if (showCartItemNum > 5)>
                                                        <a href="javaScript:void(0); " class="more">
                                                            点击展开 
                                                            <i class="sui-icon icon-tb-unfold "></i> 
                                                        </a>
                                                    </#if>
                                                    <!--收起-->
                                                    <a href="javaScript:void(0); " class="no-more">
                                                        点击收起 
                                                        <i class="sui-icon icon-tb-fold"></i> 
                                                    </a>
                                                    <!--店铺优惠券-->
                                                    <#if (company.shops?size> 1)>
                                                        <!--店铺小计-->
                                                        <div style="background:#ffffff;overflow: hidden;margin-bottom: 8px;">
                                                            <div class="totalbox fr" style="display: inline-block;width:400px;clear:none;">
                                                            <div class="rbox-total fr">
                                                                    <div class="fd-warp">
                                                                        <div class="right-fd-box">
                                                                            <div class="list-item">
                                                                                <span class="spec">活动优惠:</span>
                                                                                <span class="red">
                                                                                    -￥
                                                                                </span>
                                                                                <span class="red" id="${shop.shopCode}promoTotalAmt">
                                                                                    ${shop.promoTotalAmt?string('0.00')}
                                                                                </span>
                                                                            </div>
                                                                            <div class="list-item">
                                                                                <span class="spec">优惠券:</span>
                                                                                <span class="red">-￥</span>
                                                                                <span class="red" id="${shop.shopCode}voucherTotalAmt">
                                                                                    ${shop.voucherTotalAmt?string('0.00')}
                                                                                </span>
                                                                            </div>
                                                                            <div class="list-item">
                                                                                <span class="spec" style="font-size:16px;font-weight:500;">小计:</span>
                                                                                <span style="font-size:16px;font-weight:500;">￥</span>
                                                                                <span style="font-size:16px;font-weight:500;" name="balancespan" id="${shop.shopCode}payAmount">
                                                                                    ${shop.payAmount?string('0.00')}
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </#if>
                                                </#list>
                                                <!--遍历店铺数据结束-->
                                                <!--店铺备注留言-->
                                                <#assign itemIndex=0>
                                                <#list company.shops as shop>
                                                    <#if itemIndex==0>
                                                        <div style="border-top: 1px dashed #e6e6e6;overflow: hidden;background:#ffffff;padding-left: 40px;margin:8px 0;">
                                                            <div class="bzly fl" style="border:none;padding-bottom: 0;">
                                                                <div class="address-top-title" style="display: inline-block;">备注留言
                                                                </div>
                                                                <input type="text" class="companyRemark"
                                                                    company='${company.companyCode}' placeholder="填写您的备注留言">
                                                                <#if (company.isFbp?? && company.isFbp == 0)>
                                                                <div style="margin-top: 10px;" class="open-box-row-class">
                                                                    <div>随货资质需求:</div>
                                                                    <div class="open-box-class">
                                                                        <span style="color: #6bd88a; cursor: pointer;"
                                                                            id="clickBox_${company.companyCode}"
                                                                            onclick="clickBox(`${company.companyCode}`)">点击选择企业、商品资质</span>
                                                                        <span style="width:500px;white-space: nowrap; overflow: hidden; text-overflow: ellipsis;font-size:13px;"
                                                                            id="clickBox_bottomtip_${company.companyCode}"></span>
                                                                    </div>
                                                                </div>
                                                                </#if>
                                                            </div>
                                                            <!--总计-->
                                                            <div class="totalbox fr"
                                                                style="display: inline-block;width:400px;clear:none;">
                                                                <div class="rbox-total fr">
                                                                    <div class="spzjbox">
                                                                        共<span class="zhongshu"
                                                                            id="${company.companyCode}productVarietyNum">
                                                                            ${shop.productVarietyNum}
                                                                        </span>种商品，总件数<span
                                                                            class="zongjianshu"
                                                                            id="${company.companyCode}productTotalNum">
                                                                            ${shop.productTotalNum}
                                                                        </span>商品总计：￥<span
                                                                            id="${company.companyCode}totalAmount">
                                                                            ${shop.totalAmount?string('0.00')}
                                                                        </span>
                                                                    </div>
                                                                    <div class="fd-warp">
                                                                        <!--中间竖线-->
                                                                        <#--<div class="mid-line"></div>-->
                                                                        <div class="right-fd-box">
                                                                            <div class="list-item">
                                                                                <span class="spec">运费:</span><span>￥</span><span
                                                                                    id="${company.companyCode}freightTotalAmt">
                                                                                    ${company.freightTotalAmt?string('0.00')}
                                                                                </span>
                                                                            </div>
                                                                            <div class="list-item">
                                                                                <span class="spec">活动优惠:</span><span
                                                                                    class="red">-￥</span><span class="red"
                                                                                    id="${company.companyCode}promoTotalAmt">
                                                                                    ${shop.promoTotalAmt?string('0.00')}
                                                                                </span>
                                                                            </div>
                                                                            <div class="list-item">
                                                                                <span class="spec">优惠券:</span><span
                                                                                    class="red">-￥</span><span class="red"
                                                                                    id="${company.companyCode}voucherTotalAmt">
                                                                                    ${shop.voucherTotalAmt?string('0.00')}
                                                                                </span>
                                                                            </div>
                                                                            <#--  <div class="list-item">
                                                                                <span class="spec"
                                                                                    style="font-size:16px;font-weight:600;">总计:</span><span
                                                                                    style="font-size:16px;font-weight:600;">￥</span><span
                                                                                    style="font-size:16px;font-weight:600;"
                                                                                    name="balancespan"
                                                                                    id="${company.companyCode}payAmount">
                                                                                    ${shop.payAmount?string('0.00')}
                                                                                </span>
                                                                            </div>  -->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </#if>
                                                    <#assign itemIndex=itemIndex+1>
                                                </#list>
                                                <div id="dialog_box_${company.companyCode}" class="dialog_box"></div>
                                                <!-- 勾选资质开始 -->
                                                <div class="container_log showBox" id="showBox_${company.companyCode}" style="width: 800px;margin-top:40px" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade" data-show="true">
                                                    <div class="modal-dialog">
                                                        <input type="hidden" id="companyOrgId" value="${company.companyCode}" />
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close" id="cloneBox" onclick="closeBox('${company.companyCode}')">×</button>
                                                                <h4 id="myModalLabel" class="modal-title">随货资质需求</h4>
                                                            </div>
                                                            <div class="modal-body" style="text-align: left;font-size: 14px;line-height:20px;">
                                                                <div class="alert-title-row-top">
                                                                    <img src="/static/img/shop_icon.png" style="width:20px; height:20px;" alt="">
                                                                    <div class="alert-title-text" style="margin-left: 10px;">
                                                                        ${company.companyName}
                                                                    </div>
                                                                </div>
                                                                <!--选择企业相关资质 -->
                                                                <div class="alert-title-row" style="margin-top: 20px;">
                                                                    <div class="alert-title-icon">
                                                                    </div>
                                                                    <div class="alert-title-text">选择企业相关资质</div>
                                                                </div>
                                                                <div id="itemId" class="alert-tag-row" name="skuCorpCredential" alert-credential="" alert-orgId="${company.companyCode}" style="margin-top: 10px;">
                                                                    <#list credentialTypeList as item>
                                                                        <div class="alert-tag-item" id="item_${item.itemType}_${company.companyCode}" credentialType="${item.itemType}" onclick="clickTagItem('${item.itemType}', '${company.companyCode}', '${item.itemName}')">
                                                                            ${item.itemName}
                                                                        </div>
                                                                    </#list>
                                                                </div>
                                                                <!-- 选择商品相关资质 -->
                                                                <div class="alert-title-row" style="margin-top: 20px;margin-bottom: 10px;">
                                                                    <div class="alert-title-icon">
                                                                    </div>
                                                                    <div class="alert-title-text">选择商品相关资质</div>
                                                                </div>
                                                                <!-- 搜索商品 -->
                                                                <#-- <div class="alert-list-row-top"> <div id="searchbox"> <input class="alert-list-row-top-search" type="text" id="alert-search" placeholder="髙毛药品" /> -->
                                                                    <!--搜索下拉弹窗-->
                                                                    <#-- <ul class="li-searchUl" id="li-searchUl"></ul></div></div> -->
                                                                <div class="alert-customer-list" id="alert-customer-list">
                                                                    <#list company.shops as shop>
                                                                        <!--遍历分组数据判段-->
                                                                        <#if shop.groups ?? && (shop.groups?size>0) >
                                                                            <!--遍历分组数据开始-->
                                                                            <#list shop.groups as shoppingCartGroup>
                                                                                <!--常规分组名称-->
                                                                                    <#if shoppingCartGroup.sorted ?? && (shoppingCartGroup.sorted?size>0) >
                                                                                        
                                                                                            <!--遍历分组商品开始-->
                                                                                            <#list shoppingCartGroup.sorted as shoppingCartItem>
                                                                                                
                                                                                                    <!--套餐商品开始-->
                                                                                                    <#if shoppingCartItem.itemType=3>
                                                                                                    <div class="alert-customer-list-content-tc">
                                                                                                        <#if shoppingCartItem.subItemList ?? && (shoppingCartItem.subItemList?size>0) >
                                                                                                            <#list shoppingCartItem.subItemList as tcList>
                                                                                                                <div class="alert-customer-list-content">
                                                                                                                    <div id="${tcList.id}"
                                                                                                                        class="alert-row"
                                                                                                                        name="skuCredential"
                                                                                                                        alert-credential=""
                                                                                                                        alert-skuId="${tcList.id}"
                                                                                                                        alert-orgId="${tcList.sku.orgId}"
                                                                                                                        alert-spec="${tcList.sku.spec}">
                                                                                                                        <img src="${productImageUrl}/ybm/product/min/${tcList.imageUrl}"
                                                                                                                            alt=""
                                                                                                                            style="width:44px;height:44px;">
                                                                                                                        <div style="margin-left:10px;">
                                                                                                                            <div class="alert-list-row-title">
                                                                                                                                ${tcList.sku.showName}
                                                                                                                            </div>
                                                                                                                            <div class="alert-list-row-mintitle">
                                                                                                                                ${tcList.sku.spec}
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                    </div>
                                                                                                                    <div class="alert-row">
                                                                                                                        <span style="margin-right: 10px;">
                                                                                                                            <input style="transform: translate(0, -10%);"
                                                                                                                                id="checkbox_one_${tcList.sku.id}" type="checkbox"
                                                                                                                                name="alert_check_1" value="1"
                                                                                                                                onchange="checkBoxChange(this,'${tcList.sku.id}', '${tcList.sku.showName}')" />
                                                                                                                            <label for="checkbox1" class="checkbox-pretty inline">药检报告</label>
                                                                                                                        </span>
                                                                                                                        <span>
                                                                                                                            <input style="transform: translate(0, -10%);"
                                                                                                                                id="checkbox_two_${tcList.sku.id}" type="checkbox"
                                                                                                                                name="alert_check_2" value="2"
                                                                                                                                onchange="checkBoxChange(this,'${tcList.sku.id}', '${tcList.sku.showName}')" />
                                                                                                                            <label for="checkbox2" class="checkbox-pretty inline">首营资质</label>
                                                                                                                        </span>
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                            </#list>
                                                                                                        </#if>
                                                                                                    </div>
                                                                                                    </#if>
                                                                                                    <!--非套餐商品开始-->
                                                                                                    <#if shoppingCartItem.itemType!=3>
                                                                                                    <div class="alert-customer-list-content">
                                                                                                        <div class="alert-row"
                                                                                                            alert-orgId=""
                                                                                                            alert-spec="${shoppingCartItem.item.sku.spec }">
                                                                                                                                                    <img src="${productImageUrl}/ybm/product/min/${shoppingCartItem.item.sku.imageUrl }"
                                                                                                                                                        alt=""
                                                                                                                                                        style="width:44px;height:44px;">
                                                                                                                                                    <div style="margin-left:10px;">
                                                                                                                                                        <div class="alert-list-row-title">
                                                                                                                                                            ${shoppingCartItem.item.sku.showName }
                                                                                                                                                        </div>
                                                                                                                                                        <div class="alert-list-row-mintitle">
                                                                                                                                                            ${shoppingCartItem.item.sku.spec }
                                                                                                                                                        </div>
                                                                                                                                                    </div>
                                                                                                        </div>
                                                                                                        <div class="alert-row">
                                                                                                        <span style="margin-right: 10px;">
                                                                                                        <input style="transform: translate(0, -10%);" id="checkbox_one_${shoppingCartItem.item.sku.id }" type="checkbox" name="alert_check_1" value="1" onchange="checkBoxChange(this,'${shoppingCartItem.item.sku.id }', '${shoppingCartItem.item.sku.showName }')"/>
                                                                                                        <label for="checkbox1" class="checkbox-pretty inline">药检报告</label>
                                                                                                        </span>
                                                                                                        <span>
                                                                                                        <input style="transform: translate(0, -10%);" id="checkbox_two_${shoppingCartItem.item.sku.id }" type="checkbox" name="alert_check_2" value="2" onchange="checkBoxChange(this,'${shoppingCartItem.item.sku.id }', '${shoppingCartItem.item.sku.showName }')"/>
                                                                                                        <label for="checkbox2" class="checkbox-pretty inline">首营资质</label>
                                                                                                        </span>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </#if>
                                                                                            </#list>
                                                                                        
                                                                                    </#if>
                                                                            </#list>
                                                                        </#if>
                                                                    </#list>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button style="margin-top: 20px;margin-top: 10px; float:right; background: #00B377; color: white; border-radius: 1px;"  type="button" class="sui-btn btn-default btn-large" onclick="closeBox()">确定</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- 勾选资质end -->
                                            </#if>
                                            <!-- 遍历店铺数据判断 -->
                                        </div>
                                        <!--POP公司结束-->
                                    </#if>

                                </#list>
                            </#if>
                        </div>
                    </div>
                    <#include "/order/suixinpin.ftl">
                <!--发票类型-->
                <div class="fplx">
                    <div class="address-top-title">选择发票类型 <a href="javascript:void(0)" class="fplx-a"
                                                             data-toggle="modal" data-keyboard="false"
                                                             data-target="#fpxzTc"><i
                                    class="sui-icon icon-notification"></i></a></div>
                    <ul id="invoiceUL">
                        <li class="cur" t="${invoiceType}">
                                                        ${invoiceText}
                                                        </li>
                                                        </ul>
                                                        <#if ((invoiceType==1 || invoiceType==4) && ptdzInvoincePeer==1)>
                                                            <div class="fptx">
                                                                <label class="checkbox-pretty inline checked" id="peerTypeLab">
                                                                    <input type="checkbox" value="1" id="peerType" checked="true" /><span>电子普通发票随货同行<i>勾选后，我们会将您的电子普通发票打印，随货为您寄出。</i></span>
                                                                </label>
                                                            </div>
                                                        </#if>
                                                    </div>
                                                    <#--<#if (orderSettle.totalBalanceAmt>0)>
                                                        <!--我的余额&ndash;&gt;
                    <div class="my-balance">
                        <div class="address-top-title">我的余额</div>
                        <div class="infobox">
                            <div class="infobox-top">
                                本次使用<input class="inputprice" type="text" id="myBalanceAmount1" data-placement="top" name="balanceAmount" onkeyup="clearNoNum(this)" onblur="getTotalGoodsMoney(3)" value="${orderSettle.balanceAmount }"/>元
                                <span class="dikou">抵扣</span>
                                <span class="redcolor">￥</span><span class="redcolor resultprice" id="myBalanceAmount2">
${orderSettle.balanceAmount }
</span> 元
                            </div>
                            <div class="infobox-bot">
                                账户可用余额共计为<span class="redcolor allprice" id="myTotalBalanceAmt">
${orderSettle.totalBalanceAmt}
</span>元
                                <!--<span class="tishi">(余额抵扣不得使订单实付金额低于任一店铺的起送金额)</span>&ndash;&gt;
                                <a href="/merchant/center/balanceJournal/helpBalance.html" class="rightbox fr" target="_blank"><i class="sui-icon icon-tb-question"></i>了解什么是余额</a>
                            </div>
                        </div>
                    </div>
                </#if>
-->
                                                        <#-- 红包 orderSettle.availVirtualGold?string('0.00') -->
                                                            <div class="my-balance availRedPacketCon">
                                                                <div class="address-top-title" style="margin-bottom: 20px">我的红包</div>
                                                                <label class="checkboxLabel">
                                                                    <#if (orderSettle.redPacketBalance>0)>
                                                                        <input class="redPacketInput" id="redPacketInput" type="checkbox"
                                                                            value="${orderSettle.redPacketCanUse}"
                                                                            checked="checked" style="opacity: 0;">
                                                                        <#else>
                                                                            <input class="redPacketInput" id="redPacketInput" type="checkbox" value="0"
                                                                                disabled="disabled" style="opacity: 0;">
                                                                    </#if>
                                                                    <span class="redPacketCheckbox" />
                                                                </label>
                                                                <span style="padding-left: 10px">剩余<span
                                                                        class="redStr">
                                                                        ${orderSettle.redPacketBalance?string('0.00')}
                                                                    </span>元，可抵扣<span
                                                                        class="redStr" id="availRedPacket">
                                                                        ${orderSettle.redPacketCanUse?string('0.00')}
                                                                    </span>元</span>
                                                            </div>
                                                            <#-- 购物金 orderSettle.availVirtualGold?string('0.00') -->
                                                                <div class="my-balance availVirtualGoldCon">
                                                                    <div class="address-top-title" style="margin-bottom: 20px">我的购物金</div>
                                                                    <label class="checkboxLabel">
                                                                        <#if (orderSettle.totalVirtualGold>0)>
                                                                                <#if orderSettle?? && orderSettle.goldTips?has_content>
                                                                                   <input class="virtualGoldInput hasGoldTips" id="virtualGoldInput" type="checkbox"
                                                                                            value="${orderSettle.availVirtualGold}"
                                                                                        disabled style="opacity: 0;">
                                                                                <#else>
                                                                                 <input class="virtualGoldInput" id="virtualGoldInput" type="checkbox"
                                                                                            value="${orderSettle.availVirtualGold}"
                                                                                            checked="checked" style="opacity: 0;">                                               
                                                                                </#if>
                                                                            
                                                                            <#else>
                                                                                <input class="virtualGoldInput" id="virtualGoldInput" type="checkbox" value="0"
                                                                                    disabled="disabled" style="opacity: 0;">
                                                                        </#if>
                                                                         <#if orderSettle?? && orderSettle.goldTips?has_content>
                                                                            <span class="virtualGoldCheckboxDisable" />
                                                                           <#else>
                                                                            <span class="virtualGoldCheckbox" />
                                                                         </#if>
                                                                       
                                                                    </label>
                                                                    <span style="padding-left: 10px">剩余<span
                                                                            class="redStr">
                                                                            ${orderSettle.totalVirtualGold?string('0.00')}
                                                                        </span>元，可抵扣<span
                                                                            class="redStr"
                                                                            id="availVirtualGold">
                                                                            ${orderSettle.availVirtualGold?string('0.00')}
                                                                        </span>元</span>
                                                                        <#if orderSettle?? && orderSettle.goldTips?has_content>
                                                                            <div style="margin-top:10px;color:#a9a8a8"><span class="gwj-tips" onclick="openGwjWin('${orderSettle.goldTips}')">?</span> 含购物金不可购买店铺，暂不可用</div>
                                                                        </#if>                        
                                                                </div>
                                                                
                                                                <!--提交订单-->
                                                                <div class="applybox">
                                                                    <div class="js-total ">
                                                                        <div class="fd-warp right-fd-box fr">
                                                                            <div class="list-item">
                                                                                <span class="spec">商品总金额:</span><span>￥</span><span name="price"
                                                                                    id="finalTotalAmount">
                                                                                    ${orderSettle.totalAmount?string('0.00')}
                                                                                </span>
                                                                            </div>
                                                                            <div class="list-item">
                                                                                <span class="spec">满减总额:</span><span class="red">-￥</span><span class="red"
                                                                                    name="promoDiscountAmount"
                                                                                    id="finalPromoTotalAmt">
                                                                                    ${orderSettle.promoTotalAmt?string('0.00')}
                                                                                </span>
                                                                            </div>
                                                                            <div class="list-item" style="position:relative;">
                                                                                <!--店铺优惠券-->
                                                                                <div class="youhuiquan youhuiquan-sum"
                                                                                    style="padding:0;overflow: visible;position:absolute;left: 65px;">
                                                                                    <#--<span style="display: inline-block;margin-right: 30px;">优惠券</span>-->
                                                                                        <#--<span style="width:200px;display: inline-block;margin-right: 80px;color:#FF0000;font-size:12px;" id="${shop.shopCode}voucherTip">
                                                                                            ${shop.voucherTip}
                                                                                            </span>-->
                                                                                            <a href="javascript:;" class="xiala-all">
                                                                                                <input type="hidden" id="skuInfoForPc" value='${orderSettle.skus}'>
                                                                                                <input type="hidden" id="selectVoucherIds"
                                                                                                    value='${orderSettle.selectVoucherIds}'>
                                                                                                <#if orderSettle.isHideCoupon ?? && orderSettle.isHideCoupon==0>
                                                                                                    <span style="color: #28a3ef;font-size: 12px;">查看全部优惠券</span><img
                                                                                                        style="margin-left:5px;" src="/static/images/xiala.png" alt="">
                                                                                                </#if>
                                                                                            </a>
                                                                                            <div class="cg-yhq">
                                                                                                <div class="xyy-cover"></div>
                                                                                                <div class="xyy-confirm">
                                                                                                    <div class="xyy-header">提示</div>
                                                                                                    <div class="xyy-body">选中的自营叠加券优惠金额已超过应付总额，是否确认使用？</div>
                                                                                                    <div class="xyy-footer">
                                                                                                        <button class="ok-btn" id="xyyOk">确定使用</button>
                                                                                                        <button class="cancel-btn" id="xyyCancel">放弃使用</button>
                                                                                                    </div>
                                                                                                    <input type="hidden" id="curCoupon" value="">
                                                                                                </div>
                                                                                                <div class="yhq-san"></div>
                                                                                                <div class="address-top-title">
                                                                                                    <div class="yhq-l-box">
                                                                                                        <a href="javascript:;" class="ky-yhq cur">可用优惠券</a>
                                                                                                        <a href="javascript:;" class="bky-yhq ">不可用优惠券</a>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <div class="ybm">
                                                                                                    <!--可用优惠券开始-->
                                                                                                    <#assign availItemNum=0 />
                                                                                                    <ul class="yhq-common weishiyong ky tanbox">
                                                                                                        <input type="hidden" value="${orderSettle}">
                                                                                                        <!--叠加券-->
                                                                                                        <#if (orderSettle.availDjVoucherList ?? && orderSettle.availDjVoucherList?size>0)>
                                                                                                            <#list orderSettle.availDjVoucherList as voucher>
                                                                                                                <input type="hidden" value="${voucher}">
                                                                                                                <#assign availItemNum=availItemNum + 1 />
                                                                                                                <li class="<#if (voucher.isUse==1)>cur </#if><#if availItemNum%2==0>three-3n</#if>"
                                                                                                                    type="${voucher.voucherType}-${voucher_index}">
                                                                                                                    <input type="hidden" shopcode="${voucher.shopCode}"
                                                                                                                        shoppatterncode="${voucher.shopPatternCode}"
                                                                                                                        name="voucherId" value="${voucher.id }"/>
                                                            <input type="hidden" name="voucherMoney"
                                                                   value="${voucher.moneyInVoucher }"/>
                                                            <div class="yhq-lb">
                                                                <div class="yhq-lb-top">
                                                                    <#if voucher.voucherState==1>
                                                                        <span class="price">
${voucher.discountRatio }
</span>
                                                                        <span class="fuhao">折</span>
                                                                    <#else>
                                                                        <span class="fuhao">￥</span><span
                                                                            class="price">
${voucher.moneyInVoucher }
</span>
                                                                    </#if>
                                                                </div>
                                                                <div class="yhq-lb-foot">
                                                                    ${voucher.minMoneyToEnableDesc }
                                                                </div>
                                                                <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                                                    <div class="yhq-lb-foot">
                                                                        ${voucher.maxMoneyInVoucherDesc }
                                                                    </div>
                                                                </#if>
                                                            </div>
                                                            <div class="yhq-rb">
                                                                <div class="yhq-rb-top">
                                                                    <span class="quan quan-die">
                                                                        ${voucher.voucherTypeDesc}
                                                                    </span>
                                                                    <span class=" info">
                                                                        ${voucher.voucherDesc }
                                                                    </span>
                                                                </div>
                                                                <div style="height:30px;overflow:hidden;"></div>
                                                                <div class="yhq-rb-foot"
                                                                     style="position:absolute;bottom:10px;">
                                                                    <span>
${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }
</span>
                                                                </div>
                                                            </div>
                                                            <div class="yhq-checkb">
                                                                <label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>">
                                                                    <input type="checkbox"
                                                                           <#if voucher.isUse==0>disabled=""</#if>><span></span>
                                                                </label>
                                                            </div>
                                                        </li>
                                                    </#list>
                                                </#if>
                                                <!--遍历可用-非叠加券-->
                                                <#if (orderSettle.availVoucherList ?? && orderSettle.availVoucherList?size>0)>
                                                    <#list orderSettle.availVoucherList as voucher>
                                                        <#assign availItemNum = availItemNum + 1 />
                                                        <li class="<#if (voucher.isUse==1)>cur </#if><#if voucher.isUse==0 || voucher.isUse==Null>ygq </#if><#if availItemNum%2==0>three-3n</#if>"
                                                            type="${voucher.voucherType}-${voucher.manufacturerId}">
                                                                                                                    <input type="hidden" shopcode="${voucher.shopCode}"
                                                                                                                        shoppatterncode="${voucher.shopPatternCode}"
                                                                                                                        name="voucherId" value="${voucher.id }"/>
                                                            <input type="hidden" name="voucherMoney"
                                                                   value="${voucher.moneyInVoucher }"/>
                                                            <div class="yhq-lb">
                                                                <div class="yhq-lb-top">
                                                                    <#if voucher.voucherState==1>
                                                                        <span class="price">
${voucher.discountRatio }
</span>
                                                                        <span class="fuhao">折</span>
                                                                    <#elseif voucher.voucherUsageWay==1>
                                                                        <span class="fuhao">￥</span><span
                                                                            class="price">
${voucher.sourceMoneyInVoucher }
</span>
                                                                    <#else>
                                                                        <span class="fuhao">￥</span><span
                                                                            class="price">
${voucher.moneyInVoucher }
</span>
                                                                    </#if>
                                                                </div>
                                                                <div class="yhq-lb-foot">
                                                                    ${voucher.minMoneyToEnableDesc }
                                                                </div>
                                                                <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >
                                                                    <div class="yhq-lb-foot">
                                                                        ${voucher.maxMoneyInVoucherDesc }
                                                                    </div>
                                                                </#if>
                                                            </div>
                                                            <div class="yhq-rb">
                                                                <div class="yhq-rb-top">
                                                                    <#if voucher.voucherType == 2>
                                                                        <span class="quan">
${voucher.voucherTypeDesc}
</span>
                                                                    </#if>
                                                                    <#if voucher.voucherType == 1>
                                                                        <span class=" quan quan-tong">
                                                                                                                    ${voucher.voucherTypeDesc}
                                                                                                                    </span>
                                                                                                        </#if>
                                                                                                        <#if voucher.voucherType==6>
                                                                                                            <span class="quan quan-die">
                                                                                                                ${voucher.voucherTypeDesc}
                                                                                                            </span>
                                                                                                        </#if>
                                                                                                        <#if voucher.voucherType==5>
                                                                                                            <span class="quan quan-xin">
                                                                                                                ${voucher.voucherTypeDesc}
                                                                                                            </span>
                                                                                                        </#if>
                                                                                                        <#if voucher.voucherType==7>
                                                                                                            <span class="quan quan-shop">
                                                                                                                ${voucher.voucherTypeDesc}
                                                                                                            </span>
                                                                                                        </#if>
                                                                                                        <#if voucher.voucherType==8>
                                                                                                            <span class="quan quan-platform">
                                                                                                                ${voucher.voucherTypeDesc}
                                                                                                            </span>
                                                                                                        </#if>
                                                                                                        <#if voucher.voucherType==9>
                                                                                                            <span class="quan quan-zhuanpin">
                                                                                                                ${voucher.voucherTypeDesc}
                                                                                                            </span>
                                                                                                        </#if>
                                                                                                        <span class="info"
                                                                                                            title="${voucher.shopName}">
                                                                                                            ${voucher.shopName}
                                                                                                        </span>
                                                                                                </div>
                                                                                                <div style="height:30px;overflow:hidden;line-height:30px;">
                                                                                                    ${voucher.voucherDesc }
</div>
                                                                <div class="yhq-rb-foot">
                                                                    <span>
${voucher.voucherScope}
                                                                                                    </span>
                                                                                                </div>
                                                                                                <div style="font-size:12px;color:#999999;">
                                                                                                    ${voucher.validDate?string("yyyy/MM/dd")}
                                                                                                    -${voucher.expireDate?string("yyyy/MM/dd")}
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="yhq-checkb">
                                                                                                <label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>">
                                                                                                    <input type="checkbox"
                                                                                                        <#if voucher.isUse==0>disabled=""
                                            </#if> ><span></span>
                                            </label>
                    </div>
                    </li>
                    </#list>
                    </#if>
                    </ul>
                    <!--可用优惠券结束-->
                    <!--不可用优惠券开始-->
                    <ul class="yhq-common weishiyong bky tanbox"></ul>
                    <!--不可用优惠券结束-->
                </div>
            </div>
        </div>
        <span class="spec">优惠券总额:</span><span class="red">-￥</span><span class="red"
            name="voucherDiscountAmount"
            id="finalVoucherTotalAmt">
            ${orderSettle.voucherTotalAmt?string('0.00')}
        </span>
    </div>
    <div class="list-item">
        <span class="spec">运费总额:</span><span class="red">+￥</span><span class="red"
            name="totalFreightAmount"
            id="finalFreightTotalAmt">
            ${orderSettle.freightTotalAmt?string('0.00')}
        </span>
    </div>
    <#if (orderSettle.redPacketBalance>0)>
        <div class="list-item">
            <span class="spec">红包:</span><span class="red">-￥</span><span class="red "
                name="balancespan"
                id="finalRedPacketAmount">
                <#if orderSettle.redPacketAmount ??>
                    ${orderSettle.redPacketAmount?string('0.00')}
                    <#else>0.00
                </#if>
            </span>
        </div>
    </#if>
    <div class="list-item">
        <span class="spec">购物金抵扣:</span><span class="red">-￥</span><span class="red "
            name="virtualGoldBalance"
            id="virtualGoldBalance">
            <#if orderSettle.availVirtualGold ??>
                ${orderSettle.availVirtualGold?string('0.00')}
                <#else>0.00
            </#if>
        </span>
    </div>
    <#if orderSettle.fixedPriceAmount?? && orderSettle.fixedPriceAmount gt 0>
        <div class="list-item">
            <span class="spec">一口价优惠:</span><span class="red">-￥</span><span class="red "
                name="fixedpricespan"
                id="finalFixedPriceAmount">
                ${orderSettle.fixedPriceAmount?string('0.00')}
            </span>
        </div>
    </#if>
    </div>
    </div>
    <div class="js-tip">请确认您所加购的商品品种、数量、规格等是否无误，再进行结算；如加购商品中含近效期商品，不退不换商品，非质量问题（包装破损）概不退换。</div>
    <div class="reward-banner" id="rewardbanner">
        <div class="banner-head">
            <img src="/static/images/rebate-title.png" alt="">
            <div class="countdown"><div id="countdown"></div><span>活动结束></span></div>
        </div>
        <div class="banner-content" id="tip-content">
            
        </div>
    </div>

    <div class="js-order">
        <div class="acol6 ">
            <div class="wrapbox ">
                <p style="padding-top: 20px;">应付总额: <span class="money"
                        id="finalPayAmount">￥${orderSettle.payAmount?string('0.00')}
                    </span>
                    <#if orderSettle.freightTotalAmt?? && orderSettle.freightTotalAmt gt 0>
                        <b id="orderSettleFreightTotalAmt"
                            style="font-weight: normal;padding-left:4px;">(含运费¥${orderSettle.freightTotalAmt?string('0.00')}
                            )</b>
                    </#if>
                </p>
                <p style="display: none;"><label
                        class="isdefaultSec checkbox-pretty inline all checked">
                        <input type="checkbox" checked="checked"><span>我已阅读并同意签订</span>
                    </label><a href="purchases_contract.html" target="_blank"
                        class="fr">《药帮忙网上购销合同》 </a>
                </p>
            </div>
            <#if orderSettle.bizSource?? && orderSettle.bizSource==7>
                <a href="javascript:void(0);" class="tjbtn"  id="group_settle_btn">提交订单</a>
			<#elseif orderSettle.bizSource?? && orderSettle.bizSource==10>
				<a href="javascript:void(0);" class="tjbtn"  id="group_settle_btn">提交订单</a>
            <#else>
                <a href="javascript:void(0);" class="tjbtn"  id="settle_btn">提交订单</a>
            </#if>
            <input type="hidden" id="bizSource" value="${orderSettle.bizSource}" />
            <input type="hidden" id="groupProductNum" value="${groupProductNum}" />
            <input type="hidden" id="groupSkuId" value="${groupSkuId}" />
            <input type="hidden" id="notSubmitOrderOrgIds" value="${notSubmitOrderOrgIds}" />
            <input type="hidden" id="bizScene" value="${bizScene}" />
        </div>
    </div>
    </div>
    </div>
    </div>
    <!--主体部分结束-->
    </div>
    <!--底部导航区域开始-->
    <div class="footer" id="footer">
        <#include "/common/footer.ftl" />
    </div>
    <!--底部导航区域结束-->
    </div>
    <!--删除弹窗-->
    <div id="delModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                    <h4 id="myModalLabel" class="modal-title">删除</h4>
                </div>
                <div class="modal-body">
                    <div class="scbox">
                        收货人地址将被删除，你确定吗？
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large">确定</button>
                    <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                </div>
            </div>
        </div>
    </div>
    <!--编辑收货地址-->
    <div id="editModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                    <h4 id="myModalLabel" class="modal-title">修改配送信息</h4>
                </div>
                <div class="modal-body">
                    <div class="add-address-modal">
                        <form class="sui-form form-horizontal sui-validate" onsubmit="return doEditAddress()">
                            <input type="hidden" name="id" />
                            <div class="control-group">
                                <label for="contactor" class="control-label"><span
                                        class="sui-text-danger">*</span>姓名：</label>
                                <div class="controls">
                                    <input type="text" id="contactor" placeholder=""
                                        data-rules="required|minlength_xm=2|maxlength_xm=8" maxlength="8"
                                        name="contactor">
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="tel" class="control-label"><span class="sui-text-danger">*</span>电话号码：</label>
                                <div class="controls">
                                    <input type="text" id="mobile" placeholder="" data-rules="required|tel" name="mobile" />
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="mobile" class="control-label">收货地址：</label>
                                <div class="controls">
                                    <div class="adres-text" name="fullAddress"></div>
                                </div>
                            </div>
                            <#--<div class="control-group">-->
                                <#--<label for="mobile" class="control-label">地址备注：</label>-->
                                    <#--<div class="controls">-->
                                        <#--<div class="detailaddress">-->
                                            <#--<#if (shippingAddress.auditState==2 || shippingAddress.auditState==3)>-->
                                                <#--${shippingAddress.remark}-->
                                                    <#--< /#if>-->
                                                        <#--<#if (shippingAddress.auditState !=2 && shippingAddress.auditState !=3)>-->
                                                            <#--<input type="text" maxlength="50" class="detailinp" name="remark" placeholder="若以上gsp地址不能被识别，请在此输入框中填写gsp地址的街道和门牌号">-->
                                                                <#--< /#if>-->
                                                                    <#--< /div>-->
                                                                        <#--< /div>-->
                                                                            <#--< /div>-->
                                                                                <!--地址备注提示-->
                                                                                <div class="control-group" style="margin-top: -22px;">
                                                                                    <label for="mobile" class="control-label"></label>
                                                                                    <div class="controls">
                                                                                        <div class="addressTips"></div>
                                                                                        <div class="is_default" style="margin-left: 23px;margin-top:15px;">
                                                                                            <label id="isdefault" class="isdefaultSec checkbox-pretty inline">
                                                                                                <input type="checkbox" name="isdefault"><span>设置为默认收货地址</span>
                                                                                            </label>
                                                                                        </div>
                                                                                        <div class="default_address">
                                                                                            <span>默认收货地址</span>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <#--<div class="control-group">
                                                                                    <label for="mobile" class="control-label"><span class="sui-text-danger">*</span>收货地址：</label>
                                                                                    <div class="controls">
                                                                                        <div id="city_5">
                                                                                            <select class="prov" name="province"></select>
                                                                                            <select class="city" disabled="disabled" name="city"></select>
                                                                                            <select class="dist" disabled="disabled" name="district"></select>
                                                                                        </div>
                                                                                        <div class="detailaddress">
                                                                                            <input type="text" class="detailinp" name="address" placeholder="详细地址" data-rules="required|minlength=2|maxlength=100">
                                                                                        </div>
                                                                                    </div>
                    </div>
                    <div class="control-group">
                        <label for="mobile" class="control-label"></label>
                        <div class="controls">
                            <label class="checkbox-pretty inline ">
                                <input type="checkbox" name="isdefault"><span>设为默认地址</span>
                            </label>
                        </div>
                    </div>-->
                    <div class="modal-bot">
                        <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                        <button type="submit" class="sui-btn btn-primary btn-large">保存</button>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    </div>
    <!--设为默认地址弹窗-->
    <div id="editDefaultModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                    <h4 id="myModalLabel" class="modal-title">设为默认地址</h4>
                </div>
                <div class="modal-body">
                    <div class="scbox">
                        您确认将该地址信息设为默认地址吗?
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large">确定</button>
                    <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                </div>
            </div>
        </div>
    </div>
     <!--交易快照弹窗-->
    <div id="transactionSnapshotModal" style="width:600px;border-radius: 10px;" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="border-radius: 10px;">
                    <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                    <h4 id="myModalLabel" class="modal-title transaction" style="border-radius: 10px;">关于电汇商业渠道支付声明</h4>
                </div>
                <div class="modal-body">
                    <div class="scbox fxzhifu" style="width:500px;line-height:26px;margin-top:10px;padding: 0 27px;">
                        线下支付货款有风险，推荐您使用在线支付及购物金支付。<br>
                        电汇作为一种常见的支付方式，虽然高效便捷，但在商业
                        交易中存在一定的风险。<br>
                        为降低电汇风险，建议您：<br>
                        1、核实信息：确认收款方账户信息及身份。<br>
                        2、选择可靠渠道：通过银行或正规金融机构进行电汇。<br>
                        3、保留凭证：保存电汇记录、合同及相关凭证。<br>
                        <br>
                         <span style="position:relative">
                          <input type="checkbox" class="tooltip-zf-trigger" id="declarationBox" name="declarationBox">
                             <div class="tooltip-zf top-left">请勾选平台免责声明</div>
                         </span>
                        &nbsp;&nbsp;平台免责声明：交易具体风险以实际情况为准。建议您在电汇前充分了解相关风险，并采取必要的防范措施。
                         
                    </div>
                </div>
                <div class="modal-footer" style="text-align:center;">
                    <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large transactionOk">我已了解风险，仍使用线下转账支付</button>
                </div>
            </div>
        </div>
    </div>
    <!--查看优惠券弹窗-->
    <div id="yhqModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                    <h4 id="myModalLabel" class="modal-title">叠加优惠券</h4>
                </div>
                <div class="modal-body">
                    <ul class="yhq-common weishiyong">
                        <#list availDjVoucherList as voucher>
                            <li>
                                <input type="hidden" shopcode="${voucher.shopCode}"
                                    shoppatterncode="${voucher.shopPatternCode}" name="voucherId"
                                    value="${voucher.id }"/>
                            <input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>
                            <div class="yhq-lb">
                                <div class="yhq-lb-top">
                                    <#if voucher.voucherState==1>
                                        <span class="price">
${voucher.discountRatio }
</span>
                                        <span class="fuhao">折</span>
                                    <#else>
                                        <span class="fuhao">￥</span><span
                                            class="price">
${voucher.moneyInVoucher }
</span>
                                    </#if>
                                </div>
                                <div class="yhq-lb-foot">
                                    ${voucher.minMoneyToEnableDesc }
                                </div>
                                <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                    <div class="yhq-lb-foot">
                                        ${voucher.maxMoneyInVoucherDesc }
                                    </div>
                                </#if>
                            </div>
                            <div class="yhq-rb">
                                <div class="yhq-rb-top">
                                    <span class="quan quan-die">
${voucher.voucherTypeDesc}
</span><span
                                            class=" info">
                                ${voucher.voucherDesc }
</span>
                                </div>
                                <div style="height:30px;overflow:hidden;"></div>
                                <div class="yhq-rb-foot" style="position:absolute;bottom:10px;">
                                    <span>
${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }
</span>
                                </div>
                            </div>
                        </li>
                    </#list>
                </ul>
            </div>
        </div>
    </div>
</div>
<div id="unyhqModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 id="myModalLabel" class="modal-title">叠加优惠券</h4>
            </div>
            <div class="modal-body">
                <ul class="yhq-common weishiyong">
                    <#list unavailDjVoucherList as voucher>
                        <li>
                            <div class="yhq-lb">
                                <div class="yhq-lb-top">
                                    <#if voucher.voucherState==1>
                                        <span class="price">
${voucher.discountRatio }
</span>
                                        <span class="fuhao">折</span>
                                    <#else>
                                        <span class="fuhao">￥</span><span
                                            class="price">
${voucher.moneyInVoucher }
</span>
                                    </#if>
                                </div>
                                <div class="yhq-lb-foot">
                                    ${voucher.minMoneyToEnableDesc }
                                </div>
                                <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                    <div class="yhq-lb-foot">
                                        ${voucher.maxMoneyInVoucherDesc }
                                    </div>
                                </#if>
                            </div>
                            <div class="yhq-rb">
                                <div class="yhq-rb-top">
                                    <span class="quan quan-die">
${voucher.voucherTypeDesc}
                                </span><span
                                    class="info">
                                    ${voucher.voucherDesc }
</span>
                                </div>
                                <div style="height:30px;overflow:hidden;"></div>
                                <div class="yhq-rb-foot" style="position:absolute;bottom:10px;">
                                    <span>
${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }
</span>
                                </div>
                            </div>
                        </li>
                    </#list>
                </ul>
            </div>
        </div>
    </div>
</div>
<!--发票须知弹窗-->
<div id="fpxzTc" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 class="modal-title">发票须知</h4>
            </div>
            <div class="modal-body">
                <div class="xzmain">
                    <p class="xz-title">1.电子普通发票</p>
                    <p class="xz-info">
                        电子发票是税局认可的有效收付款凭证，其法律效力、基本用途及使用规范等同于纸质发票，可作为用户报销、维权的有效凭据。详见《国家税务总局公告2015年第84号》；</p>
                    <p class="xz-info">如需纸质发票，可自行下载打印。</p>
                    <p class="xz-title">2.增值税专用发票</p>
                    <p class="xz-info">
                        纸质增值税专用发票，包括纸质版和电子版，不仅具有商事凭证的作用，还可以进行进项抵扣。开具增值税专用发票需提供药店完整的开票信息，详询客服400-0505-111；</p>
                    <p class="xz-info">我司依法开具发票，请您依法使用。</p>
                    <p class="xz-title">3.发票金额</p>
                    <p class="xz-info">发票金额为实付金额，不包括优惠金额。</p>
                    <p class="xz-title">4. 电子普通发票开票时间</p>
                    <p class="xz-info">电子普通发票：确认收货后24小时内生成，您可以在个人中心--我的订单--订单详情中，查看下载（APP端只支持查看，PC端支持查看、下载、打印）。</p>
                    <p class="xz-title">5.已开具电子普通发票，申请退货，发票怎么办</p>
                    <p class="xz-info">部分退货：商品退回后，我司会把原电子发票冲红，开具一张新的发票，请您留意订单详情中电子发票的变更；</p>
                    <p class="xz-info">整单退货：商品退回后，我司会把原电子发票冲红，订单中的电子发票无法再次查看下载。</p>
                    <p class="xz-title">6.已开具增值税专用发票，申请退货，发票怎么办</p>
                    <p class="xz-info">部分退货或者整单退货，纸质发票都必须随商品一起退回。</p>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="xx-pay" class="xx-mask">
	<div class="xx-input-box">
		<div class="xx-title">
			<span style="font-size:18px;">验证支付密码</span>
			<div id="xx-pay-close" style="cursor:pointer;">
				<svg focusable="false" class="" data-icon="close" width="1em" height="1em" fill="currentColor" aria-hidden="true" fill-rule="evenodd" viewBox="64 64 896 896"><path d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"></path></svg>
			</div>
		</div>
		<div id="inputBox" style="display:flex;gap:20px;margin-bottom:10px;">
			<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="0" aria-autocomplete="none">
			<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="1" aria-autocomplete="none">
			<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="2" aria-autocomplete="none">
			<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="3" aria-autocomplete="none">
			<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="4" aria-autocomplete="none">
			<input class="xx-input" type="password" autocomplete="new-password" maxlength="0" sub="5" aria-autocomplete="none">
		</div>
		<div class="xx-title">
			<span  id="xx-pay-msg" style="color:#ec3d3d;font-size:14px;"></span>
			<span id="xx-pay-forget" style="color:#00c200">忘记密码?</span>
		</div>
		<div id="xx-pay-submit">
			<span>立即支付</span>
		</div>
	</div>
</div>
<!--配送服务费说明弹窗-->
<div id="fuwu" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                <h4 class="modal-title" style="text-align: center;">配送服务收费标准</h4>
            </div>
            <div class="modal-body">
                <div class="xzmain freight">
                </div>
            </div>
        </div>
    </div>
</div>
<div style="display: none" id="voucherUnavailableDialog">
    ${voucherText}
                </div>
                <#if shippingAddress.auditState==0>
                    <script>
                    $.alert({
                        title: '温馨提示：',
                        body: '为严格执行《药品管理法》及《药品经营质量管理规范》的相关规定，收货地址将默认为许可证注册地址或仓库地址，并不能随意修改。如该地址无法被快递员识别请在收货地址右方点击修改并在输入框内加以描述，如有其它疑问您还可以联系官方客服进行咨询！', //必填
                        okBtn: '我知道啦'
                    })
                    </script>
                </#if>
                <div class="ti_box">
                    <input type="hidden" value="" id="bigPackage">
                    <input type="hidden" value="" id="bigPackageTotalAmount">
                    <div class="ti_shi">
                        <div class="shi_tou">
                            <i class="sui-icon icon-tb-infofill"></i>
                            <span>提示</span>
                            <i class="sui-icon icon-tb-close"></i>
                        </div>
                        <div class="shi_xia">
                            <p>确定不需要后，您将无法获取该礼包</p>
                        </div>
                        <div class="shi_que">
                            确 认
                        </div>
                        <div class="shi_qu">
                            取 消
                        </div>
                    </div>
                </div>
                <div class="ti_box_v">
                    <div class="ti_shi_v">
                        <div class="shi_tou_v">
                            <i class="sui-icon_v icon-tb-infofill_v"></i>
                            <span>提示</span>
                        </div>
                        <div class="shi_xia_v">
                            <p>您有优惠力度更高的优惠券，当前订单是否使用？</p>
                        </div>
                        <div class="shi_que_v">
                            确 认
                        </div>
                        <div class="shi_qu_v">
                            取 消
                        </div>
                    </div>
                </div>
<!--满赠凑单弹窗-->
<div id="fullGiveModal" style="display: none;">
    <input id="promoId" type="hidden" value=""></input>
    <input id="bizSource" type="hidden" value="${cartInfo.bizSource}">
    <div class="i-dialog-mask"></div>
    <div class="i-dialog-box">
        <div class="i-title">
            <div style="overflow-x: hidden;margin-right: 15px;">
                <#if orderSettle.needToBePerfectedActList?? && (orderSettle.needToBePerfectedActList?size>1) >
                    <div id="mz-actList" class="i-actList" style="padding: 20px 0;display: flex;display:none;">
                        <#list orderSettle.needToBePerfectedActList as actItem>
                            <div>
                                <div id="act-${actItem.promoId}"  style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;" title="${actItem.labTitle}" onclick="fullNumSelectGive(${orderSettle.bizSource},'0',${actItem.giftPoolActTotalSelectedNum},${actItem.promoId})">${actItem.labTitle}</div>
                                <div id="actStatus-${actItem.promoId}" class="i-actStatus"></div>
                            </div>
                        </#list>
                    </div>
                </#if>
                <div id="mz-title" style="padding: 10px 0;">
                    <span style="font-size: 14px;">选择赠品</span>
                </div>
            </div>
            <div onclick="fullGiveClose()">
                <svg style="cursor: pointer;" fill="#8f8f8f" focusable="false" class="" data-icon="close" width="1em" height="1em" fill="currentColor" aria-hidden="true" fill-rule="evenodd" viewBox="64 64 896 896"><path d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.***********.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.***********.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"></path></svg>
            </div>
        </div>
        <div class="i-content">
            <p style="margin: 10px 0;display: flex;justify-content: space-between;align-items: center;padding: 0 15px;">
                <span style="font-weight: 600;">
                    <span>可选择</span>
                    <span id="canSelectNum" style="color: #22b14c;">
                    </span>

                    <span>盒赠品，当前已选择</span>
                    <span id="selectedNum" style="color: #22b14c;">
                    </span>

                    <span>盒</span>
                </span>
                <span onclick="abandonGift()">
                    <input id="abandon" type="checkbox" name="abandon">
                    <label for="abandon" style="cursor: pointer;user-select: none;">放弃当前赠品</label>
                </span>
            </p>
            <div id="productList">
            </div>
        </div>
        <div id="mz-normal" class="i-footer" style="justify-content:flex-end;padding-bottom:10px;">
            <div class="i-close" onclick="fullGiveClose()">取消</div>
            <div class="i-submit" style="margin-left: 5px;" onclick="fullGiveSubmit()">确定</div>
        </div>
        <div id="mz-submit" class="i-footer" style="justify-content:flex-end;padding-bottom:10px;display:none;">
            <div class="i-close" onclick="fullGiveClose()">取消</div>
            <#if orderSettle.bizSource?? && orderSettle.bizSource==7>
                <div class="i-submit" style="margin-left: 5px;" onclick="submitOrder()">确定</div>
			<#elseif orderSettle.bizSource?? && orderSettle.bizSource==10>
				<div class="i-submit" style="margin-left: 5px;" onclick="submitOrder()">确定</div>
            <#else>
                <div class="i-submit" style="margin-left: 5px;" onclick="checkVoucherMonitor()">确定</div>
            </#if>
        </div>
    </div>
</div>

<div id="consumerRebate" class="consumer-rebate">
          <div class="rebate-content">
            <div class="tip-content">
                <div class="order-state">
                    <img style="height: 30px;width: 87px;" src="/static/images/rebate-title.png" alt="">
                <div class="order-table-box">
                    <img  src="/static/images/red-packet-h.png" alt="">
                <div class="head-container">
                    <div class="red-packet-content" id="levelScene2" style="display: none;">
                            <p class="rebate-explain-p1">返利比例</p>
                            <div class="text-style">
                                <span class="text-span-1" id="referralPercentage"></span><span class="text-span-2">%起</span>
                            </div>
                            <p class="rebate-explain-p2">仅差<span id="differenceValue" style="color: #FF0000;"></span>可参与活动</p>
                        
                    </div>
                    <div id="levelScene3" style="display: none;">
                        <p class="rebate-explain-p1">订单完成获得红包</p>
                        <div class="text-style">
                                <span class="text-span-1" id="moneyVal">17</span><span class="text-span-2">元</span>
                        </div>
                        <p class="rebate-explain-p2">仅差<span id="differValue" style="color: #FF0000;">元</span>可得<span style="color: #FF0000;" id="redPacketVal"></span></p>
                    </div>
                    <div id="levelScene5" style="display: none;">
                        <p class="rebate-explain-p1">订单完成获得红包</p>
                        <div class="text-style">
                                <span class="text-span-1" id="moneyZeroVal">17</span><span class="text-span-2">元</span>
                        </div>
                        
                    </div>
                    <div id="levelScene4" style="display: none;">
                        <p class="rebate-explain-p1">订单完成获得红包</p>
                            <div class="text-style">
                            <span id="amountVal" class="text-span-1"></span><span class="text-span-2">元</span>
                            </div>
                            <p  class="rebate-explain-p2">多买多返，最高得<span style="color: #FF0000;" id="maxAmount">10000元</span></p>
                    </div>
                      <div class="guide">
                                    <div style="width: 100%;height: 230px;overflow-y: auto;">
                                               <table id="data-table">
                                                    <thead>
                                                        <tr>
                                                            <th>月采购金额</th>
                                                            <th>返利率</th>
                                                            <th>档位内最高返现</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        
                                                    </tbody>
                                                </table>
                                                <!-- <div class="table-row" v-for="(item,index) in resultData.result?.actResultList">
                                                    <div v-if="item.nextLevelAmount" class="table-cell">{{ item.amount }} - {{ item.nextLevelAmount }}元</div>
                                                    <div v-else class="table-cell">{{ item.amount }}元</div>
                                                    <div class="table-cell"><span style="margin-left: 80px;">{{item.extraSubsidyAmountRate}}%</span><div class="add-ratio" v-if="true">+{{item.extraSubsidyAmountRate}}%</div>
                                                        <div class="limited-time-offer" v-if="true">限时加补{{item.extraSubsidyAmountRate}}，仅剩29:52</div>
                                                    </div>
                                                    <div class="table-cell">{{ item.maxAmount }}元</div>
                                                </div> -->
                                    </div>
                                    </div>
                                
                            <button class="closeRebateBtn" id="closeRebateBtn">我知道了</button>  
                </div>    
              </div>
            </div>
          </div>
    </div>
</body>
<script>
var orgID = '';
var clickKey = '';
var checkObj = {};
var checkBoxStr = {};
var checkTagStr = {};
/**运费提示**/
$(".get-fei").click(function() {
    var code = $(this).attr('data-code');
    $.ajax({
        url: "/merchant/freight/query",
        type: "POST",
        data: {shopCode: code},
        success: function(result) {
            if (result) {
                $("#fuwu").modal();
                ele = '';
                if (result.freightTemplateTipsList.length) {
                    result.freightTemplateTipsList.forEach(function(item, index) {
                        ele += '<p class="xz-title">' + item.title + '</p>'
                        item.freightTips.forEach(function(item2, index2) {
                            ele += '<p class="xz-info">' + item2 + '</p>'
                        })
                    })
                }
                ele += '<br>';
                $(".freight").html(ele);
            } else {
                $.alert(result.errorMsg);
            }
        }
    })
})
//更新资质埋点
$("#updateLicense").click(function() {
    var link = $(this);
    webSdk.track('QualificationUpdate_click', {
        'pageName': window.document.title
    }, function() {
        location.href = $(link).attr('href'); //继续跳转到目标页面
    });
    // return false;
})
$(document).click(function(e) {
    var con = $(".cg-yhq")
    if (!con.is(e.target) && con.has(e.target).length === 0) {
        $(".cg-yhq").slideUp();
    }
})
// 展示弹窗
function clickBox(val) {
    const showKey = "#showBox_" + val;
    const dialogKey = "#dialog_box_" + val;
    clickKey = val;
    $(showKey).show();
    $(dialogKey).show();
    if (!checkBoxStr[clickKey]) {
        checkBoxStr[clickKey] = [];
    }
    if (!checkTagStr[clickKey]) {
        checkTagStr[clickKey] = [];
    }
}

function closeBox() {
    let arrStr = checkTagStr[clickKey].concat(checkBoxStr[clickKey]);
    if (arrStr.length > 0) {
        $("#clickBox_bottomtip_" + clickKey).text('已选:' + arrStr.join(','));
    } else {
        $("#clickBox_bottomtip_" + clickKey).text('');
    }
    const showKey = "#showBox_" + clickKey;
    const dialogKey = "#dialog_box_" + clickKey;
    $(showKey).hide();
    $(dialogKey).hide();
}

function submitBox() {
    const showKey = "#showBox_" + clickKey;
    const dialogKey = "#dialog_box_" + clickKey;
    $(showKey).hide();
    $(dialogKey).hide();
}
// 关闭弹窗
$('#cloneBox').click(function() {
    const showKey = "#showBox_" + clickKey;
    const dialogKey = "#dialog_box_" + clickKey;
    $(showKey).hide();
    $(dialogKey).hide();
})
// 弹窗提交按钮
$('#submitBtn').click(function() {
    const showKey = "#showBox_" + clickKey;
    const dialogKey = "#dialog_box_" + clickKey;
    $(showKey).hide();
    $(dialogKey).hide();
})

$("#closeRebateBtn").click(function(){
    $("#consumerRebate").hide()
})
var tagCheckeds = {};

function clickTagItem(itemType, orgId, itemName) {
    var child = $('#tagId');
    let itemOBJ = tagCheckeds[orgId];
    if (itemOBJ) {
        if (itemOBJ.includes(itemType)) {
            itemOBJ = itemOBJ.filter(item => {
                return item !== itemType;
            })
            $('#item_' + itemType + '_' + orgId).removeClass('alert-tag-item-checked');
            $('#item_' + itemType + '_' + orgId + '-text').removeClass('alert-tag-item-checked');
            if (checkTagStr[clickKey].includes(itemName)) {
                checkTagStr[clickKey] = checkTagStr[clickKey].filter(item => {
                    return item !== itemName;
                })
            }
        } else {
            itemOBJ.push(itemType);
            $('#item_' + itemType + '_' + orgId).addClass('alert-tag-item-checked');
            $('#item_' + itemType + '_' + orgId + '-text').addClass('alert-tag-item-checked');
            if (checkTagStr[clickKey].includes(itemName)) {
                checkTagStr[clickKey] = checkTagStr[clickKey].filter(item => {
                    return item !== itemName;
                })
            } else {
                checkTagStr[clickKey].push(itemName);
            }
        }
    } else {
        itemOBJ = [itemType];
        $('#item_' + itemType + '_' + orgId).addClass('alert-tag-item-checked');
        $('#item_' + itemType + '_' + orgId + '-text').addClass('alert-tag-item-checked');
        if (checkTagStr[clickKey].includes(itemName)) {
            checkTagStr[clickKey] = checkTagStr[clickKey].filter(item => {
                return item !== itemName;
            })
        } else {
            checkTagStr[clickKey].push(itemName);
        }
    }
    tagCheckeds[orgId] = itemOBJ;
    child.attr('alert-credential', itemOBJ.join(','))
}
//从这里可以获取到id，从而获取值
function checkBoxChange(e, id, str) {
    var child = $(`#${id}`);
    var val1 = $('#checkbox_one_' + id).is(':checked');
    var val2 = $('#checkbox_two_' + id).is(':checked');
    let vals = []
    if (val1) {
        vals.push('1')
    }
    if (val2) {
        vals.push('2')
    }
    if (val1 || val2) {
        if (checkBoxStr[clickKey].includes(str)) {
            //无需操作
        } else {
            checkBoxStr[clickKey].push(str);
        }
    } else {
        checkBoxStr[clickKey] = checkBoxStr[clickKey].filter(item => {
            return item !== str;
        })
    }
    checkObj[id] = vals.join(',');
    return
    var li = child.attr('alert-credential').split(',');
    li = li.filter(k => k != '')
    if (e.checked) {
        // 药检报告
        if (e.value == '1') {
            li.push('1')
        } else {
            li.push('2')
        }
    } else {
        if (e.value == '1') {
            li = li.filter(k => k != '1')
        } else {
            li = li.filter(k => k != '2')
        }
    }
    child.attr('alert-credential', li.join(','))
}
//选择某一行
//设置id与name一致，这样可以在选择时通过name更方便获取到id
function chooseSel(val) {
    $("#alert-search").val(val)
    //给数据添加监听
}
var pressKeyInt = 0;
/*搜索弹窗响应键盘事件*/
$("#alert-search").keyup(function(event) {
    var e = event || window.event;
    var k = e.keyCode || e.which;
    var name = $("#alert-search").val();
    loadProductNameByAutoComplete(name);
    $('#li-searchUl').css("display", "block");
    switch (k) {
        case 38:
            pressKeyInt--;
            var tmp = $('#li-searchUl li').length - 1;
            if (pressKeyInt < 0) {
                pressKeyInt = tmp;
            }
            $('#li-searchUl li').eq(pressKeyInt - 1).addClass('active').siblings().removeClass('active');
            $("#alert-search").val($('#li-searchUl li').eq(pressKeyInt - 1).text());
            break;
        case 40:
            pressKeyInt++;
            var tmp = $('#li-searchUl li').length - 1;
            if (pressKeyInt > tmp) {
                pressKeyInt = 0;
            }
            $('#li-searchUl li').eq(pressKeyInt - 1).addClass('active').siblings().removeClass('active');
            $("#alert-search").val($('#li-searchUl li').eq(pressKeyInt - 1).text());
            break;
        case 13:
            console.log("回车");
            break;
    }
});
//搜索框
function loadProductNameByAutoComplete(name) {
    var html = "";
    if (name == null || name.trim() == "") {} else {
        name = name.trim();
        const showNameList = [{showName: '1111', name: '22221'}, {
            showName: '1111',
            name: '22222'
        }, {showName: '1111', name: '22223'}]
        $.each(showNameList, function(index, entry) {
            var name = entry.showName;
            html += "<li><span onclick='chooseSel(" + entry.name + ")'>" + entry.showName + "</span></li>";
        });
        if (showNameList && showNameList.length > 0) {
            $("#li-searchUl").html(html);
            $('#li-searchUl').css("display", "block");
        } else {
            $('#li-searchUl').css("display", "none");
        }
    }
}
/*点击搜索下拉框事件*/
$('#li-searchUl li').click(function() {
    $("#alert-search").val($(this).text());
    $(".li-searchUl").hide();
});
/*隐藏搜索弹窗*/
$("body").click(function(e) {
    if (!$(e.target).closest(".li-searchUl").length) {
        $(".li-searchUl").hide();
    }
});

// 未登录隐藏在线客服入口
if ($('#merchantId').val() == '0') {
    $('#kefuFloat').hide();
}
$("#authModal").modal('show')
function openGwjWin(str){
    layer.open({
        type: 1,
        title: '温馨提示',
        closeBtn: 1,
        shadeClose: false,
        skin: 'openAccountExplainDialogContent',
        area: ['400px', ''],
        content: '<div style="padding: 15px">' + str + '</div>',
        btn: '确定',
        success: function (lay) {
            lay.find('.layui-layer-btn0').css({'background':'#00DC7D',"border":"none"})
        }
    });
}
</script>
<script src="/static/js/plugins/aes.js" type="text/javascript"></script>
</html>