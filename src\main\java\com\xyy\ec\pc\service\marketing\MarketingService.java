package com.xyy.ec.pc.service.marketing;

import com.xyy.ec.marketing.client.dto.SuiXinPinSkuDTO;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.pc.controller.vo.ConsumeRebateDetailVo;
import com.xyy.ec.pc.service.marketing.dto.MarketingCsuResultDto;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.search.engine.enums.CsuOrder;
import com.xyy.ec.search.engine.pagination.Page;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 营销服务
 */
public interface MarketingService {

    /**
     * <pre>
     * 通用。根据商品ID获取展示中的秒杀活动信息。
     * 数据结构：Map&lt;key:csuId, value:秒杀活动信息&gt;。
     * 获取进行中 + 未开始的活动。优先级：进行中 > 未开始，同状态中按活动开始时间升序、活动ID降序排序。
     * </pre>
     *
     * @param merchantId
     * @param csuIds
     * @return
     */
    Map<Long, MarketingSeckillActivityInfoDTO> getShowingSeckillActivityInfoByCsuIds(Long merchantId, List<Long> csuIds);

    /**
     * <pre>
     * 通用。根据商品ID获取展示中的秒杀活动信息。
     * 数据结构：<xmp>Map<key:csuId, value:秒杀活动信息></xmp>。
     * 获取进行中 + 未开始的活动。优先级：进行中 > 未开始，同状态中按活动开始时间升序、活动ID降序排序。
     * </pre>
     *
     * @param merchantId 会员ID
     * @param csuId      商品ID列表
     * @return
     * @see #getShowingSeckillActivityInfoByCsuIds(Long, List)
     */
    MarketingSeckillActivityInfoDTO getShowingSeckillActivityInfoByCsuId(Long merchantId, Long csuId);

    /**
     * <pre>
     * 批量根据商品id查询秒杀活动信息。
     * 按活动开始时间升序、活动ID降序排序。
     * 分页查询。
     * 优先级：进行中 > 未开始；
     * 若查询进行中的，则返回参加进行中活动的信息；若查询未开始的，则返回不参加进行中且参加未开始活动的信息。
     * </pre>
     *
     * @param csuIds
     * @param statusList 只接受{@linkplain MarketingQueryStatusEnum#UN_START 未开始} 和 {@linkplain MarketingQueryStatusEnum#STARTING 进行中}。
     * @param merchantId
     * @return
     */
    Map<Long, MarketingSeckillActivityInfoDTO> getShowingSeckillActivityInfoByCsuIds(Long merchantId, List<Long> csuIds, List<Integer> statusList);

    /**
     * <pre>
     * 通用。根据商品ID获取展示中的某个状态的秒杀活动信息。
     * 数据结构：<xmp>Map<key:csuId, value:秒杀活动信息></xmp>。
     * </pre>
     *
     * @param merchantId 会员ID
     * @param csuIds     商品ID列表
     * @param status     状态，{@link MarketingQueryStatusEnum}。
     * @return
     */
    Map<Long, MarketingSeckillActivityInfoDTO> getShowingSeckillActivityInfoByCsuIds(Long merchantId, List<Long> csuIds, Integer status);

    /**
     * <pre>
     * 搜索专用。根据商品ID获取展示中的秒杀活动信息。
     * 数据结构：<xmp>Map<key:csuId, value:秒杀活动信息></xmp>。
     * <s>获取进行中活动。</s>
     * 更改需求为：获取进行中 + 未开始的活动。优先级：进行中 > 未开始，同状态中按活动开始时间升序、活动ID降序排序，
     *  与{@link #getShowingSeckillActivityInfoByCsuIds}等价。
     * </pre>
     *
     * @param merchantId 会员ID
     * @param csuIds     商品ID列表
     * @return
     *
     * @see #getShowingSeckillActivityInfoByCsuIds(Long, List, Integer)
     */
    Map<Long, MarketingSeckillActivityInfoDTO> getShowingSeckillActivityInfoByCsuIdsForSearch(Long merchantId, List<Long> csuIds);

    Map<Long, SuiXinPinSkuDTO> getSuiXinPinSkuDiscountBySkuIds(Long merchantId, List<Long> skuIdList);

    /**
     * 根据商品id查询拼团活动信息
     * @param skuIdList  商品id列表 最长20个
     * @param merchantId
     * @return
     */
    List<GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdList(List<Long> skuIdList, Long merchantId);

    /**
     * <pre>
     * 根据商品id查询拼团活动信息。
     * 按活动开始时间升序、活动ID降序排序。
     * </pre>
     *
     * @param skuIdList
     * @param statusList
     * @param merchantId
     * @return
     */
    Map<Long, GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdList(List<Long> skuIdList, List<Integer> statusList, Long merchantId);

    /**
     * <pre>
     * CMS后台专用。
     * 根据商品id查询拼团活动信息。
     * 按活动开始时间升序、活动ID降序排序。
     * </pre>
     *
     * @param skuIdList
     * @param statusList
     * @return
     */
    Map<Long, GroupBuyingInfoDto> getGroupBuyingInfoBySkuIdListForCms(List<Long> skuIdList, List<Integer> statusList);

    /**
     * <pre>
     * 根据商品id查询拼团活动信息。
     * 按活动开始时间升序、活动ID降序排序。
     * </pre>
     *
     * @param skuIdList
     * @param merchantId
     * @return
     * @see #getGroupBuyingInfoBySkuIdList(List, List, Long)
     */
    Map<Long, GroupBuyingInfoDto> getGroupBuyingBySkuIdsForSearch(List<Long> skuIdList, Long merchantId);

    /**
     * 根据拼团活动id查询活动信息（落地页使用 不走选人）
     * @param marketingId
     * @return
     */
    GroupBuyingInfoDto getGroupBuyingInfoById(Long marketingId);

    /**
     * 根据商品id批量查询未开始、进行中的活动卡片信息。目前仅支持拼团品和批购包邮品。
     *
     * @param skuIdList       商品id列表，内部100个一组，若没有值则直接返回。
     * @param merchantId      用户id，必填。
     * @param activityTypeSet 活动状态类型列表，枚举见{@link MarketingEnum}，必填。
     * @return
     */
    List<GroupBuyingInfoDto> getActCardInfoBySkuIdList(List<Long> skuIdList, Long merchantId, Set<Integer> activityTypeSet, Set<Long> gaoMaoSkuIdSet);

    /**
     * <pre>
     * 根据商品id批量查询某些状态的活动卡片信息。目前仅支持拼团品和批购包邮品。
     * 按活动开始时间升序、活动ID降序排序。
     * </pre>
     *
     * @param skuIdList       商品id列表，内部100个一组，若没有值则直接返回。
     * @param statusList      活动状态列表，枚举见{@link MarketingQueryStatusEnum}，必填。
     * @param merchantId      用户id，必填。
     * @param activityTypeSet 活动状态类型列表，枚举见{@link MarketingEnum}，必填。
     * @return
     */
    Map<Long, GroupBuyingInfoDto> getActCardInfoBySkuIdList(List<Long> skuIdList, List<Integer> statusList, Long merchantId, Set<Integer> activityTypeSet, Set<Long> gaoMaoSkuIdSet);

    /**
     * <pre>
     * CMS后台专用。
     * 根据商品id批量查询某些状态的活动卡片信息。目前仅支持拼团品和批购包邮品。
     * 按活动开始时间升序、活动ID降序排序。
     * </pre>
     *
     * @param skuIdList       商品id列表，内部100个一组，若没有值则直接返回。
     * @param statusList      活动状态列表，枚举见{@link MarketingQueryStatusEnum}，必填。
     * @param activityTypeSet 活动状态类型列表，枚举见{@link MarketingEnum}，必填。
     * @return
     */
    Map<Long, GroupBuyingInfoDto> getActCardInfoBySkuIdListForCms(List<Long> skuIdList, List<Integer> statusList, Set<Integer> activityTypeSet, Set<Long> gaoMaoSkuIdSet);

    /**
     * 根据商品id批量查询未开始、进行中的活动卡片信息。目前仅支持拼团品和批购包邮品。
     *
     * @param skuIdList       商品id列表，内部100个一组，若没有值则直接返回。
     * @param merchantId      用户id，必填。
     * @param activityTypeSet 活动状态类型列表，枚举见{@link MarketingEnum}，必填。
     * @return
     * @see #getActCardInfoBySkuIdList(List, List, Long, Set)
     */
    Map<Long, GroupBuyingInfoDto> getActCardInfoBySkuIdListForSearch(List<Long> skuIdList, Long merchantId, Set<Integer> activityTypeSet, Set<Long> gaoMaoSkuIdSet);

    /**
     * 店铺首页列表(目前支持拼团和特价)
     * @param pageNum       页数
     * @param pageSize      每页条数
     * @param merchantId    用户id
     * @param shopCode      店铺code
     * @param csuOrder      排序
     * @param activityTypeEnum 活动类型枚举(拼团：22 特价：8)
     * @return
     */
    Page<MarketingCsuResultDto> getMarketingShopIndex(Integer pageNum, Integer pageSize, Long merchantId, String shopCode, CsuOrder csuOrder, MarketingEnum activityTypeEnum);

    List<Long> getSkuIdListForShopIndex(Integer pageNum, Integer pageSize, Long merchantId, String shopCode, CsuOrder csuOrder, MarketingEnum activityTypeEnum);

    /**
     * 逻辑没变 在原来接口基础上改造的
     * @param csuIdList
     * @param merchantId
     * @return
     */
    Map<Long, GroupBuyingInfoDto> getGroupBuyingInfo(List<Long> csuIdList, Long merchantId);

    /**
     * 逻辑没变 在原来接口基础上改造的
     * @param csuIdList
     * @param merchantId
     * @return
     */
    Map<Long, GroupBuyingInfoDto> getPiGouInfo(List<Long> csuIdList, Long merchantId);

    /**
     * 逻辑没变 在原来接口基础上改造的(上游没有用到)
     * @param merchantId
     * @param csuIds
     * @return
     */
    public Map<Long, MarketingSeckillActivityInfoDTO> getCsuMarketingSeckillActivityInfoForShopFloor(Long merchantId, List<Long> csuIds);

    /**
     * 结算页获取消费返文案
     * @param merchantId 用户id
     * @param money 结算页的金额
     * @return
     */
    ConsumeRebateDetailVo getMarketingRebateConsumptionReturnInfo(Long merchantId, BigDecimal money);

    /**
     * 一键采购补库需求:获取SaaS推品折后价
     * key = skuId  value = 折后价
     * @param merchantId
     * @param skuIdList
     * @return
     */
    Map<Long, BigDecimal> getSaasPromotionProductDiscountPrice(Long merchantId, List<Long> skuIdList);
}
