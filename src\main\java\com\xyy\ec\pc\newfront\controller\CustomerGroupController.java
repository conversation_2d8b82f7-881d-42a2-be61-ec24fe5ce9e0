package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.service.IndexNewService;
import com.xyy.ec.pc.newfront.service.InsightChosenCustomerRpcService;
import com.xyy.ec.pc.rest.AjaxErrorEnum;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URL;
import java.util.List;

@CustomizeCmsResponse
@RestController
@RequiredArgsConstructor
@RequestMapping("/new-front/customer-group")
public class CustomerGroupController {

    private final XyyIndentityValidator xyyIndentityValidator;
    private final InsightChosenCustomerRpcService insightChosenCustomerRpcService;

    /**
     * 根据传入的人群, 过滤当前用户所在人群
     */
    @PostMapping("/filter")
    public AjaxResult<List<Long>> filterHitCustomerGroupIds(@RequestBody List<Long> customerGroupIds) {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipalEaseEx();
        if (merchant == null) {
            throw new AppException("用户登陆异常", XyyJsonResultCodeEnum.FAIL);
        }
        List<Long> groupList = insightChosenCustomerRpcService.getChoseGroupsByMerchant(merchant.getId(), customerGroupIds);
        return AjaxResult.successResult(groupList);
    }
}
