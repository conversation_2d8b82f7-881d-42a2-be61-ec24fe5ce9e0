package com.xyy.ec.pc.enums;

import lombok.Getter;

/**
 * 〈一句话功能简述〉<br>
 * 〈后台下单导入模板类型〉
 */
@Getter
public enum ImportProductTypeEnum {
    /**
     * 品名、规格、厂家匹配
     */
    NAME(1, "品名+规格+厂家", new String[]{"品名", "规格", "厂家", "数量"}),
    /**
     * 国药准字
     */
    APPROVAL_NUMBER(2, "国药准字", new String[]{"国药准字", "规格", "数量"}),
    /**
     * 商品条码（69码）
     */
    CODE(3, "商品条码（以69开头）", new String[]{"商品条码（以69开头）", "数量",}),
    /**
     * 商品条码（69码）
     */
    BARCODE(4, "商品编码（小药药商品编码）", new String[]{"商品编码（小药药商品编码）", "数量",}),
    /**
     * 批量比价
     */
    MATCH_PRICE(5, "批量比价", new String[]{"商品条码（69码）", "通用名称", "规格", "生产厂家", "批准文号", "采购数量", "采购价格"});

    private final int type;
    private final String text;
    private String[] importFields;

    ImportProductTypeEnum(int type, String text, String[] importFields) {
        this.type = type;
        this.text = text;
        this.importFields = importFields;
    }


    public static ImportProductTypeEnum getByType(int type) {
        ImportProductTypeEnum typeEnum = null;
        switch (type) {
            case 1:
                typeEnum = ImportProductTypeEnum.NAME;
                break;
            case 2:
                typeEnum = ImportProductTypeEnum.APPROVAL_NUMBER;
                break;
            case 3:
                typeEnum = ImportProductTypeEnum.CODE;
                break;
            case 4:
                typeEnum = ImportProductTypeEnum.BARCODE;
                break;
            case 5:
                typeEnum = ImportProductTypeEnum.MATCH_PRICE;
                break;
            default:
        }
        return typeEnum;
    }
}
