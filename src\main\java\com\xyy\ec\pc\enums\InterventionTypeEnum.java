package com.xyy.ec.pc.enums;


/**
 * 介入类型
 */
public enum InterventionTypeEnum {
    ProductIssues(1,"商品问题"),
    //发票问题
    InvoiceIssues(2,"票据问题"),
    //资质问题
    QualificationsIssues(3,"资质问题");


    private int id;
    private String value;


    public  int getId() {
        return id;
    }
    public  String getValue() {
        return value;
    }

    public static String get(int id) {
        for (InterventionTypeEnum c : InterventionTypeEnum.values()) {
            if (c.getId() == id) {
                return c.value;
            }
        }
        return null;
    }
    InterventionTypeEnum(int id, String value) {
        this.id = id;
        this.value = value;
    }
}
