package com.xyy.ec.pc.newfront.dto;

import com.xyy.ec.layout.buinese.dto.AppModuleBuineseDto;
import com.xyy.ec.layout.buinese.dto.AppModuleCategoryBuineseDto;
import com.xyy.ec.layout.buinese.dto.NoticeDetailBuineseDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.dto.Order618ProgressDataBusinessDto;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;
import com.xyy.ec.product.business.module.CategoryVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndexRespVO {
    private String errorMsg;

    private String pcVersion;
    private List<AppModuleBuineseDto> moduleBanner;
    private List<CmsListProductVO> seckillProductInfos;
    private AppModuleCategoryBuineseDto index_bg;
    private AppModuleCategoryBuineseDto index_footer_div;
    private Order618ProgressDataBusinessDto index_618_div_data;

    private List<CategoryVo> categorys;
    private String skuImageUrl;
    private List<AppModuleCategoryBuineseDto> appModuleManageBuineseDtoList;
    private List<NoticeDetailBuineseDto> noticeDetails;
    private Long merchantId;
    private MerchantBussinessDto merchant;
    private String branchCode;

    private String alertFlag;
    private String alertFlag1;
    private String count;

}
