package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderDetailBusinessApi;
import com.xyy.ec.order.business.api.OrderEscortBusinessApi;
import com.xyy.ec.order.business.api.OrderEscortInvoiceBusinessApi;
import com.xyy.ec.order.business.dto.*;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.base.Sort;
import com.xyy.ec.pc.enums.OrderDetailEnum;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.system.business.api.BranchBusinessApi;
import com.xyy.ec.system.business.dto.BranchBusinessDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

/**
 * @Auther: willow
 * @Date: 2019/4/16 10:22
 * @Description:
 */
@Controller
@RequestMapping("/merchant/center/orderPriceProtection")
public class OrderPriceProtectionController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(OrderPriceProtectionController.class);


    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;
    @Reference(version = "1.0.0")
    private OrderDetailBusinessApi orderDetailBusinessApi;

    @Reference(version = "1.0.0")
    private BranchBusinessApi branchBusinessApi;
    @Reference(version = "1.0.0")
    private OrderEscortBusinessApi orderEscortBusinessApi;
    @Reference(version = "1.0.0")
    private OrderEscortInvoiceBusinessApi orderEscortInvoiceBusinessApi;

    /**
     * 我的订单
     *
     * @param order
     * @param page
     * @param sort
     * @param modelMap
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/index.htm")
    public String list(OrderBusinessDto order, Page page, Sort sort, ModelMap modelMap, HttpServletRequest request) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        order.setMerchantId(merchant.getId());
        order.setBranchCode(merchant.getRegisterCode());
        order.setStatus(null);
        order.setStatuses(new Integer[]{2, 3});
        order.setVisibled(OrderBusinessDto.STATUS_VISIBLED);
        order.setIsThirdCompany(0);
        Calendar calendar = Calendar.getInstance();
	    calendar.set(Calendar.HOUR_OF_DAY, 0);
	    calendar.set(Calendar.MINUTE, 0);
	    calendar.set(Calendar.SECOND, 0);
	    calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.DATE, -30);
        if(order.getStartCreateTime()==null||order.getStartCreateTime().compareTo(calendar.getTime())<0)
        order.setStartCreateTime(calendar.getTime());
        if(order.getStartCreateTime()!=null)
            order.setStartCreateTime(getStartOfDay(order.getStartCreateTime()));
        if(order.getEndCreateTime()!=null)
            order.setEndCreateTime(getEndOfDay(order.getEndCreateTime()));
        //获取字典表
        PageInfo pageInfoParam = new PageInfo<>();
        pageInfoParam.setPageNum(page.getOffset());
        pageInfoParam.setPageSize(page.getLimit());
        if (order.getOrderSource() != null && order.getOrderSource() == -1) {
            order.setOrderSourceArray(new Integer[]{-1, 1, 2, 3});
        }
        PageInfo<MyOrderBusinessDto> modelPageInfo = orderBusinessApi.findMerchantOrders(pageInfoParam, order);
        Page<MyOrderBusinessDto> pageInfo = new Page<>();
        pageInfo.setRows(modelPageInfo.getList());
        pageInfo.setTotal(modelPageInfo.getTotal());
        pageInfo.setPageCount(modelPageInfo.getPages());
        String requestUrl = this.getRequestUrl(request);
        pageInfo.setRequestUrl(requestUrl);
        pageInfo.setOffset(page.getOffset());
        modelMap.put("pager", pageInfo);
        modelMap.put("paramOrder", order);
        modelMap.put("center_menu", "orderEscort");
        modelMap.put("merchant", merchant);
        return "/orderPriceProtection/list.ftl";
    }

    // 获得某天最大时间 2018-03-20 23:59:59
    public static Date getEndOfDay(Date date) {
        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.setTime(date);
        calendarEnd.set(Calendar.HOUR_OF_DAY, 23);
        calendarEnd.set(Calendar.MINUTE, 59);
        calendarEnd.set(Calendar.SECOND, 59);
        //防止mysql自动加一秒,毫秒设为0
        calendarEnd.set(Calendar.MILLISECOND, 0);
        return calendarEnd.getTime();
    }
    // 获得某天最小时间 2018-03-20 00:00:00
    public static Date getStartOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());

    }


    /**
     * 查看订单详情
     *
     * @param id
     * @param modelMap
     * @return
     */
    @RequestMapping("/detail/{id}.htm")
    public String detail(@PathVariable Long id, ModelMap modelMap) {
        MyOrderInfoBusinessDto order = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order = orderBusinessApi.selectOrderDetail(merchant.getId(), id, OrderDetailEnum.DELIVERING.getCode());
            modelMap.put("merchant", merchant);
        } catch (Exception e) {
            logger.error("保价护航查看订单异常", e);
        }
        modelMap.put("order", order);
        modelMap.put("center_menu", "orderEscort");
        return "/orderPriceProtection/detail.ftl";
    }

    /**
     * 添加保价护航
     *
     * @param id
     * @param modelMap
     * @return
     */
    @RequestMapping("/add/{id}.htm")
    public String add(@PathVariable Long id, ModelMap modelMap) {
        OrderDetailBusinessDto orderDetailBusinessDto = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            orderDetailBusinessDto = orderDetailBusinessApi.selectById(id);
            OrderBusinessDto order=orderBusinessApi.selectByOrderNo(orderDetailBusinessDto.getOrderNo());
            modelMap.put("merchant", merchant);
            modelMap.put("contactor", order.getContactor());
            modelMap.put("mobile", order.getMobile());
        } catch (Exception e) {
            logger.error("保价护航查看订单异常", e);
        }
        modelMap.put("orderDetail", orderDetailBusinessDto);
        modelMap.put("center_menu", "orderEscort");
        return "/orderPriceProtection/add.ftl";
    }

    /**
     * 申请报价护航保存
     * @param orderEscort
     * @return
     */
    @RequestMapping(value = "/saveOrderEscort.htm")
    public String saveOrderEscort(OrderEscortBusinessDto orderEscort,String[] imageUrl){
        OrderDetailBusinessDto  orderDetailBusinessDto = orderDetailBusinessApi.selectById(orderEscort.getOrderDetailId());
        orderEscort.setSkuId(orderDetailBusinessDto.getSkuId());
        orderEscort.setImageUrl(orderDetailBusinessDto.getImageUrl());
        orderEscort.setProductPrice(orderDetailBusinessDto.getProductPrice());
        orderEscort.setSubTotal(orderDetailBusinessDto.getSubTotal());
        orderEscort.setSpec(orderDetailBusinessDto.getSpec());
        orderEscort.setProductAmount(orderDetailBusinessDto.getProductAmount());
        orderEscort.setProductName(orderDetailBusinessDto.getProductName());
        orderEscort.setOrderNo(orderDetailBusinessDto.getOrderNo());
        orderEscort.setOrderTime(orderDetailBusinessDto.getCreateTime());

        try{
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            orderEscort.setMerchantId(merchant.getId());
            orderEscort.setMerchantName(merchant.getRealName());
            orderEscort.setBranchCode(merchant.getRegisterCode());
            orderEscort.setCreateBy(merchant.getId().toString());
            logger.info("申请报价护航保存入参:{}", JSON.toJSONString(orderEscort));
            //获取区域名称
            BranchBusinessDto branchBusinessDto = branchBusinessApi.getBranchByCode(orderEscort.getBranchCode());
            orderEscort.setCreateTime(new Date());
            orderEscort.setBranchName(branchBusinessDto.getBranchName());
            Long id = orderEscortBusinessApi.saveOrderEscort(orderEscort);
            OrderEscortInvoiceBusinessDto orderEscortInvoice=null;
                for(String url:imageUrl){
                    orderEscortInvoice=new OrderEscortInvoiceBusinessDto();
                    orderEscortInvoice.setCreateTime(new Date());
                    orderEscortInvoice.setOrderEscortId(id);
                    orderEscortInvoice.setImageUrl(url);
                    orderEscortInvoice.setCreateBy(merchant.getId().toString());
                    orderEscortInvoiceBusinessApi.saveOrderEscort(orderEscortInvoice);
                }
            return "redirect:/merchant/center/orderEscort/index.htm";
        }catch(Exception e){
            logger.error("添加保驾护航异常",e);
            return "redirect:/merchant/center/orderEscort/index.htm";
        }
    }


}
