package com.xyy.ec.pc.controller.order;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.server.api.AccountCardApi;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.order.dto.giftPool.ChangeGiftPoolDto;
import com.xyy.ec.order.dto.giftPool.CommonGiftPoolDto;
import com.xyy.ec.order.dto.giftPool.DeleteGiftPoolDto;
import com.xyy.ec.order.dto.giftPool.SelectGiftPoolDto;
import com.xyy.ec.order.dto.giftPool.vo.ChangeGiftPoolVo;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.rpc.OrderServerRpcService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;

/**
 * <AUTHOR>
 * 京东绑卡
 */
@Controller
@RequestMapping("/app")
public class GiftPoolController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(GiftPoolController.class);
    @Autowired
    private OrderServerRpcService orderServerRpcService;

    /**
     * 放弃赠品
     * @param
     * @return
     */
    @RequestMapping("/addAutoGiveUpActFlag")
    @ResponseBody
    public Object addAutoGiveUpActFlag(@RequestParam(value="merchantId",required = true,defaultValue = "0")Long merchantId,
                                       @RequestParam(value = "bizSource", required = false) Integer bizSource,
                                       @RequestParam(value = "promoId", required = false) Long promoId,
                                       @RequestHeader(value = "version", required = false) Integer version,
                                       @RequestHeader(value = "terminalType", required = false) Integer terminalType) {
        try {
            CommonGiftPoolDto commonGiftPoolDto =new CommonGiftPoolDto();
            commonGiftPoolDto.setPromoId(promoId);
            commonGiftPoolDto.setBizSource(bizSource);
            commonGiftPoolDto.setMerchantId(merchantId);
            orderServerRpcService.addAutoGiveUpActFlag(commonGiftPoolDto);
            return this.addResult();
        } catch (Exception e) {
            LOGGER.error("addAutoGiveUpActFlag merchantId:{} e", merchantId, e);
            return this.addError(" 放弃赠品异常");
        }
    }

    /**
     * 取消放弃赠品
     * @param
     * @return
     */
    @RequestMapping("/deleteAutoGiveUpActFlag")
    @ResponseBody
    public Object deleteAutoGiveUpActFlag(@RequestParam(value="merchantId",required = true,defaultValue = "0")Long merchantId,
                                       @RequestParam(value = "bizSource", required = false) Integer bizSource,
                                          @RequestParam(value = "promoId", required = false) Long promoId,
                                       @RequestParam(value = "version", required = false) Integer version,
                                       @RequestParam(value = "terminalType", required = false) Integer terminalType) {
        try {
            CommonGiftPoolDto commonGiftPoolDto =new CommonGiftPoolDto();
            commonGiftPoolDto.setPromoId(promoId);
            commonGiftPoolDto.setBizSource(bizSource);
            commonGiftPoolDto.setMerchantId(merchantId);
            orderServerRpcService.deleteAutoGiveUpActFlag(commonGiftPoolDto);
            return this.addResult();
        } catch (Exception e) {
            LOGGER.error("deleteAutoGiveUpActFlag merchantId:{} e", merchantId, e);
            return this.addError("取消放弃赠品异常");
        }
    }


    /**
     * 赠品池修改
     * @param
     * @return
     */
    @RequestMapping("/changeGiftPool")
    @ResponseBody
    public Object changeGiftPool(@RequestParam(value="merchantId",required = true,defaultValue = "0")Long merchantId,
                                          @RequestParam(value = "bizSource", required = true) Integer bizSource,
                                          @RequestParam(value = "promoId", required = false) Long promoId,
                                          @RequestParam(value = "skuId", required = true) Long skuId,
                                           @RequestParam(value = "amount", required = false) Integer amount,
                                          @RequestParam(value = "totalSelectedNum", required = false) Integer totalSelectedNum,
                                          @RequestParam(value = "version", required = false) Integer version,
                                          @RequestParam(value = "terminalType", required = false) Integer terminalType) {
        try {
            ChangeGiftPoolDto changeGiftPoolDto =new ChangeGiftPoolDto();
            changeGiftPoolDto.setBizSource(bizSource);
            changeGiftPoolDto.setMerchantId(merchantId);
            changeGiftPoolDto.setPromoId(promoId);
            changeGiftPoolDto.setSkuId(skuId);
            changeGiftPoolDto.setAmount(amount);
            ChangeGiftPoolVo changeGiftPoolVo=orderServerRpcService.changeGiftPool(changeGiftPoolDto);
            return this.addResult("data", changeGiftPoolVo);
        } catch (Exception e) {
            LOGGER.error("changeGiftPool merchantId:{} e", merchantId, e);
            return this.addError("赠品池修改异常");
        }
    }


    /**
     * 赠品池取消选中
     * @param
     * @return
     */
    @RequestMapping("/cancelItemGiftPool")
    @ResponseBody
    public Object cancelItemGiftPool (@RequestParam(value="merchantId",required = true,defaultValue = "0")Long merchantId,
                                          @RequestParam(value = "bizSource", required = false) Integer bizSource,
                                          @RequestParam(value = "promoId", required = false) Long promoId,
                                          @RequestParam(value = "skuId", required = true) Long skuId,
                                          @RequestParam(value = "version", required = false) Integer version,
                                          @RequestParam(value = "terminalType", required = false) Integer terminalType) {
        try {
            SelectGiftPoolDto selectGiftPoolDto =new SelectGiftPoolDto();
            selectGiftPoolDto.setBizSource(bizSource);
            selectGiftPoolDto.setMerchantId(merchantId);
            selectGiftPoolDto.setPromoId(promoId);
            selectGiftPoolDto.setSkuId(skuId);
            orderServerRpcService.unSelectItem(selectGiftPoolDto);
            return this.addResult();
        } catch (Exception e) {
            LOGGER.error("cancelItemGiftPool merchantId:{} e", merchantId, e);
            return this.addError("赠品池取消选中");
        }
    }

    /**
     * 赠品池选中
     * @param
     * @return
     */
    @RequestMapping("/selectItemGiftPool")
    @ResponseBody
    public Object selectItemGiftPool (@RequestParam(value="merchantId",required = true,defaultValue = "0")Long merchantId,
                                      @RequestParam(value = "bizSource", required = false) Integer bizSource,
                                      @RequestParam(value = "promoId", required = false) Long promoId,
                                      @RequestParam(value = "skuId", required = true) Long skuId,
                                      @RequestParam(value = "amount", required = true) Integer amount,
                                      @RequestParam(value = "totalSelectedNum", required = false) Integer totalSelectedNum,
                                      @RequestParam(value = "version", required = false) Integer version,
                                      @RequestParam(value = "terminalType", required = false) Integer terminalType) {
        try {
            SelectGiftPoolDto selectGiftPoolDto =new SelectGiftPoolDto();
            selectGiftPoolDto.setBizSource(bizSource);
            selectGiftPoolDto.setMerchantId(merchantId);
            selectGiftPoolDto.setPromoId(promoId);
            selectGiftPoolDto.setSkuId(skuId);
            selectGiftPoolDto.setAmount(amount);
            orderServerRpcService.selectItem(selectGiftPoolDto);
            return this.addResult();
        } catch (Exception e) {
            LOGGER.error("selectItemGiftPool merchantId:{} e", merchantId, e);
            return this.addError("赠品池选中");
        }
    }

    /**
     * 赠品池删除
     * @param
     * @return
     */
    @RequestMapping("/removeProductFromGiftPool")
    @ResponseBody
    public Object removeProductFromGiftPool(@RequestParam(value="merchantId",required = true,defaultValue = "0")Long merchantId,
                                          @RequestParam(value = "bizSource", required = false) Integer bizSource,
                                            @RequestParam(value = "promoId", required = false) Long promoId,
                                            @RequestParam(value = "skuId", required = true) Long skuId,
                                          @RequestParam(value = "version", required = false) Integer version,
                                          @RequestParam(value = "terminalType", required = false) Integer terminalType) {
        try {
            DeleteGiftPoolDto deleteGiftPoolDto =new DeleteGiftPoolDto();
            deleteGiftPoolDto.setPromoId(promoId);
            deleteGiftPoolDto.setBizSource(bizSource);
            deleteGiftPoolDto.setMerchantId(merchantId);
            deleteGiftPoolDto.setSkuIdList(Arrays.asList(skuId));
            orderServerRpcService.batchRemoveProduct(deleteGiftPoolDto);
            return this.addResult();
        } catch (Exception e) {
            LOGGER.error("removeProductFromGiftPool merchantId:{} e", merchantId, e);
            return this.addError("赠品池删除");
        }
    }
}
