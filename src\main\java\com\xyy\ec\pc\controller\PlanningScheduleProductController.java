package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.PlanningScheduleBussinessApi;
import com.xyy.ec.merchant.bussiness.api.PlanningScheduleProductBusinessApi;
import com.xyy.ec.merchant.bussiness.api.ecp.ecpplanningschedule.EcpPlanningScheduleProductBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.*;
import com.xyy.ec.order.business.api.ShoppingCartBusinessApi;
import com.xyy.ec.order.business.exception.ServiceException;
import com.xyy.ec.pc.aspect.PlanningScheduleFlag;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.base.Sort;
import com.xyy.ec.pc.service.PlanningScheduleService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.api.ProductBusinessApi;
import com.xyy.ec.product.business.api.ecp.productInfo.EcpProductBusinessApi;
import com.xyy.ec.product.business.api.ecp.skucategory.EcpCategoryBusinessApi;
import com.xyy.ec.product.business.dto.CategoryBusinessDTO;
import com.xyy.ec.product.business.dto.ProductDto;
import com.xyy.ec.product.business.dto.SkuConditionDto;
import com.xyy.ec.product.business.dto.product.ProductSimpleVO;
import com.xyy.ec.product.business.module.CategoryVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequestMapping("/merchant/center/planningScheduleProduct")
@Controller
public class PlanningScheduleProductController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlanningScheduleProductController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private PlanningScheduleBussinessApi planningScheduleBussinessApi;

    @Reference(version = "1.0.0")
    private ShoppingCartBusinessApi shoppingCartBusinessApi;

    @Reference(version = "1.0.0")
    private EcpCategoryBusinessApi categoryBusinessApi;

    @Reference(version = "1.0.0")
    private PlanningScheduleProductBusinessApi planningScheduleProductBusinessApi;

    @Reference(version = "1.0.0")
    private EcpPlanningScheduleProductBusinessApi ecpPlanningScheduleProductBusinessApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private ProductBusinessApi productBusinessApi;

    @Reference(version = "1.0.0")
    private EcpProductBusinessApi ecpProductBusinessApi;

    @Autowired
    private PlanningScheduleService planningScheduleService;

    /**
     * 仅仅查询有货的
     *
     * @param merchantId
     * @param status
     * @param sort
     * @param modelMap
     * @param request
     * @return
     */
    @RequestMapping(value = "/index/{id}.htm", method = RequestMethod.GET)
    @PlanningScheduleFlag
    public Object index(@PathVariable Long id, Long merchantId, Integer status,
                        SortBusinessDto sort, ModelMap modelMap, HttpServletRequest request) throws Exception {

        // 分页要用，将url传到前台
        String url = this.getRequestUrl(request);
        //筛选类目展开状态
        String cjZhan = request.getParameter("cjZhan");
        String ejZhan = request.getParameter("ejZhan");

        //获取筛选条件的厂商
        String manufacturer = request.getParameter("manufacturer");

        //一级分类筛选条件
        String categoryFirstId = request.getParameter("categoryFirstId");
        //二级分类筛选条件
        String categorySecondId = request.getParameter("categorySecondId");

        //处方分类筛选条件
        String drugClassification = request.getParameter("drugClassification");

        if ("99999".equals(categoryFirstId)) {//当选择的为全部分类时，二级分类和三级分类置空，且展开状态为关闭
            if (StringUtils.isNotEmpty(categorySecondId)) {
                url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                categorySecondId = null;
            }
            ejZhan = "2";
        }

        //处理分类的筛选条件---当所选的分类的父类与所选的父类不对应时，清楚该分类的筛选
        if (StringUtils.isNotEmpty(categorySecondId)) {
            if (StringUtils.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {//当所选一级分类不为空时，所选的二级分类都要和一级分类比较
                //得到所选二级分类信息
                CategoryBusinessDTO c2 = null;
                c2 = categoryBusinessApi.selectByPrimaryKey(Long.parseLong(categorySecondId));
                //如果所选二级分类的父类即一级分类也不是所选的一级分类，则将二级分类的筛选条件也去掉
                if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                    url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                    categorySecondId = null;
                }
            }
        }
        PlanningScheduleProductBussinessDto param = new PlanningScheduleProductBussinessDto();
        SkuConditionDto skuConditionDto = new SkuConditionDto();
        param.setPlanningScheduleId(id);
        //获取综合(销量)的排序标识
        String orderSort = request.getParameter("order_sort");
        if ("1".equals(orderSort)) {
//            sort.setProperty("smsr.sale_num");
//            sort.setDirection("desc");
            skuConditionDto.setDirection("desc");
            skuConditionDto.setProperty("smsr.sale_num");
        } else if ("2".equals(orderSort)) {
//            sort.setProperty("smsr.sale_num");
//            sort.setDirection("asc");
            skuConditionDto.setDirection("smsr.sale_num");
            skuConditionDto.setProperty("asc");
        }

        //获取价格排序标识
        String orderFob = request.getParameter("order_fob");
        if ("1".equals(orderFob)) {
//            sort.setProperty("t2.fob");
//            sort.setDirection("desc");
            skuConditionDto.setDirection("desc");
            skuConditionDto.setProperty("t2.fob");
        } else if ("2".equals(orderFob)) {
//            sort.setProperty("t2.fob");
//            sort.setDirection("asc");
            skuConditionDto.setDirection("asc");
            skuConditionDto.setProperty("t2.fob");
        }

        String fuzzyPlanningName = request.getParameter("fuzzyPlanningName");
        if (StringUtils.isNotEmpty(fuzzyPlanningName)) {
//            param.setFuzzyPlanningName(fuzzyPlanningName);
            skuConditionDto.setShowName(fuzzyPlanningName);
        }


        if (StringUtils.isNotEmpty(manufacturer)) {
//            param.setManufacturer(manufacturer);
//            String[] s = {manufacturer};
//            param.setManufacturers(s);
            List<String> manufacturers = new ArrayList<>();
            manufacturers.add(manufacturer);
            skuConditionDto.setManufacturer(manufacturer);
        }

        if (StringUtils.isNotEmpty(drugClassification)) {
            if ("5".equals(drugClassification)) {
//                param.setDrugClassification(null);
                skuConditionDto.setDrugClassification(null);
            } else {
//                param.setDrugClassificationStr(drugClassification);
//                Integer[] a = new Integer[1];
//                a[0] = Integer.parseInt(drugClassification);
//                param.setDrugClassifications(a);
                List<Integer> list = new ArrayList<>(1);
                list.add(Integer.parseInt(drugClassification));
                skuConditionDto.setDrugClassificationStr(drugClassification);
                skuConditionDto.setDrugClassificationList(list);
            }
        }

        String minPrice = request.getParameter("minPrice");
        String maxPrice = request.getParameter("maxPrice");
        //设置筛选的价格
        if (StringUtils.isNotEmpty(minPrice)) {
//            param.setMinPrice(Double.parseDouble(minPrice));
            skuConditionDto.setMinPrice(Double.parseDouble(minPrice));
        }
        if (StringUtils.isNotEmpty(maxPrice)) {
//            param.setMaxPrice(Double.parseDouble(maxPrice));
            skuConditionDto.setMaxPrice(Double.parseDouble(maxPrice));
        }

        if (StringUtils.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("99999")) {
//            param.setCategoryFirstId(Long.valueOf(categoryFirstId));
//            param.setCategoryId(Long.valueOf(categoryFirstId));
            skuConditionDto.setCategoryFirstId(Long.valueOf(categoryFirstId));
            skuConditionDto.setCategoryId(Long.valueOf(categoryFirstId));
        }
        if (StringUtils.isNotEmpty(categorySecondId)) {
//            param.setCategorySecondId(Long.valueOf(categorySecondId));
//            param.setCategoryId(Long.valueOf(categorySecondId));
            skuConditionDto.setCategorySecondId(Long.valueOf(categorySecondId));
            skuConditionDto.setCategoryId(Long.valueOf(categorySecondId));
        }
        MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
        List<PlanningScheduleProductBussinessDto> dataList = null;
        List<com.xyy.ec.product.business.module.CategoryVo> listCategory = null;
//		String branchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchant.getId());
        param.setBranchCode(merchant.getRegisterCode());
        skuConditionDto.setBranchCode(merchant.getRegisterCode());
//        dataList = planningScheduleProductBusinessApi.pcSelectHasGoodSku(param, sort);
        dataList = planningScheduleService.queryPlanningDetailsInfo(param,merchant,skuConditionDto);
        PlanningScheduleBussinessDto planningSchedule = planningScheduleBussinessApi.selectByPrimaryKey(id);
        int count = planningScheduleProductBusinessApi.selectCount(param);
        if (CollectionUtil.isNotEmpty(dataList)){
            modelMap.put("hasGoodSkuNum", dataList.size());
            modelMap.put("noHasGoodSkuNum", planningSchedule.getProductAmount()-dataList.size());
		}else {
			modelMap.put("hasGoodSkuNum",0);
			modelMap.put("noHasGoodSkuNum",planningSchedule.getProductAmount());
		}
        try {
            listCategory = planningScheduleService.category(param, merchant.getId());
        } catch (Exception e) {
            LOGGER.error("获取分类异常,e="+ExceptionUtils.getStackTrace(e));
        }
        //提供商品价格为活动价格
//		List<Long> productIds = getProductIds(dataList);
//		if (CollectionUtils.isNotEmpty(productIds)) {
//			try {
//				Map<Long,ProductSimpleVO> skuVOS = productBusinessApi.getSimpleProduct(merchant.getRegisterCode(),productIds);
//				dataList = replaceFobBySku(dataList,skuVOS,minPrice,maxPrice,orderFob);
//			}
//			catch (Exception e) {
//				LOGGER.error("获取计划单商品活动信息异常", e);
//			}
//		}

        modelMap.put("cjZhan", StringUtils.isNotEmpty(cjZhan) ? Integer.parseInt(cjZhan) : 0);
        modelMap.put("ejZhan", StringUtils.isNotEmpty(ejZhan) ? Integer.parseInt(ejZhan) : 0);
        modelMap.put("url", url);
        modelMap.put("categoryFirstId", categoryFirstId);
        modelMap.put("categorySecondId", categorySecondId);
        modelMap.put("drugClassification", drugClassification);
        modelMap.put("paramOrder", dataList);
        modelMap.put("listCategory", listCategory);
        modelMap.put("center_menu", "myplan");
        modelMap.put("merchant", merchant);
        modelMap.put("planningScheduleId", id);
        modelMap.put("planStatus", planningSchedule == null ? -1 : planningSchedule.getPlanStatus());
        modelMap.put("orderSaleRank", orderSort);
        modelMap.put("orderFob", orderFob);
        modelMap.put("manufacturer", manufacturer);
        modelMap.put("fuzzyPlanningName", fuzzyPlanningName);
        //最小价格筛选条件
        modelMap.put("minPrice", minPrice);
        //最大价格的筛选条件
        modelMap.put("maxPrice", maxPrice);
        //添加计划单名显示
        modelMap.put("planningScheduleName", planningSchedule == null ? "" : planningSchedule.getPlanningName());
        modelMap.put("totalSkuNum", planningSchedule.getProductAmount());
        if (CollectionUtil.isNotEmpty(dataList)) {
            modelMap.put("totalNum", dataList.size());
            BigDecimal totalMoney = BigDecimal.ZERO;
            List<ManufacturerBusinessDTO> manufacturerList = new ArrayList<>();
            List<String> mList = new ArrayList<>();
            for (PlanningScheduleProductBussinessDto pp : dataList) {
                totalMoney = totalMoney.add(pp.getSubTatol());
                if (!mList.contains(pp.getManufacturer())) {
                    mList.add(pp.getManufacturer());
                    ManufacturerBusinessDTO man = new ManufacturerBusinessDTO();
                    man.setManufacturer(pp.getManufacturer());
                    manufacturerList.add(man);
                }
            }
            modelMap.put("totalMoney", totalMoney);
            modelMap.put("manufacturerList", manufacturerList);
        }
        return "/planningScheduleProduct/list.ftl";
    }

    /**
     * 得到商品id集合
     *
     * @param planningScheduleProducts
     * @return 入参为空返回空
     */
    private List<Long> getProductIds(List<PlanningScheduleProductBussinessDto> planningScheduleProducts) {
        if (CollectionUtils.isEmpty(planningScheduleProducts)) {
            return null;
        }
        List<Long> productIds = new ArrayList<>(planningScheduleProducts.size());
        planningScheduleProducts.stream().forEach(planningScheduleProduct -> productIds.add(planningScheduleProduct.getProductId()));
        return productIds;
    }

    /**
     * 根据活动商品价格替换库中价格<br/>
     * 按照价格入参过滤活动价格不符合的商品<br/>
     * 若入参有价格排序就重新排序处理活动价格带来的影响
     *
     * @param planningScheduleProducts 原商品信息
     * @param fobMap                   活动商品价格
     * @return fobMap为空或者planningScheduleProducts为空返回planningScheduleProducts本身，否则返回处理后的结果
     */
    private List<PlanningScheduleProductBussinessDto> replaceFobBySku(List<PlanningScheduleProductBussinessDto> planningScheduleProducts, Map<Long, ProductSimpleVO> fobMap, String minPrice, String maxPrice, String orderFob) {
        if (MapUtils.isEmpty(fobMap)) {
            return planningScheduleProducts;
        }
        if (CollectionUtils.isEmpty(planningScheduleProducts)) {
            return planningScheduleProducts;
        }
        planningScheduleProducts.stream().forEach(planningScheduleProduct -> {
            Double fob = null;
            if (fobMap.get(planningScheduleProduct.getProductId()) != null && fobMap.get(planningScheduleProduct.getProductId()).getFob() != null) {
                fob = fobMap.get(planningScheduleProduct.getProductId()).getFob().doubleValue();
            }
            if (fob != null && fob.doubleValue() > 0d) {
                planningScheduleProduct.setFob(fob);
            }
        });
        //根据价格条件处理活动价格带来的影响
        Double minPriceNum = NumberUtils.toDouble(minPrice, 0.0D);
        Double maxPriceNum = NumberUtils.toDouble(maxPrice, 9999.0D);
        planningScheduleProducts = planningScheduleProducts.stream().filter(planningScheduleProduct -> planningScheduleProduct.getFob() >= minPriceNum && planningScheduleProduct.getFob() <= maxPriceNum).collect(Collectors.toList());
        if (StringUtil.isNotBlank(orderFob)) {
            planningScheduleProducts = planningScheduleProducts.stream().sorted((x, y) -> {
                if (StringUtil.equals("1", orderFob)) {
                    return x.getFob() > y.getFob() ? 1 : -1;
                } else {
                    return x.getFob() > y.getFob() ? -1 : 1;
                }
            }).collect(Collectors.toList());
        }
        return planningScheduleProducts;
    }

    /**
     * 有货无货一起查 ： status == 1 查询有货的 ； status == 2 查询无货的
     *
     * @param param
     * @param page
     * @param sort
     * @param modelMap
     * @param request
     * @return
     * @throws ServiceException
     */
    @RequestMapping(value = "/queryAll.htm", method = RequestMethod.GET)
    public Object queryAll(PlanningScheduleProductBussinessDto param, Page page,
                           SortBusinessDto sort, ModelMap modelMap, HttpServletRequest request)
            throws ServiceException {
        MerchantPrincipal merchant = null;
        int oldOffset = page.getOffset();
        Long total = 0L;
        List<PlanningScheduleProductBussinessDto> dataList = Lists.newArrayList();
        try {
            if (param.getPlanningScheduleId() == null || param.getPlanningScheduleId().intValue() <= 0) {
                page.setRequestUrl(this.getRequestUrl(request));
                page.setOffset(oldOffset);
                page.setTotal(total);
                page.setRows(dataList);

                modelMap.put("begin", 0);
                modelMap.put("end", total);
                modelMap.put("pager", page);
                modelMap.put("param", param);
                modelMap.put("center_menu", "myplan");
                modelMap.put("merchant", merchant);
                return "/planningScheduleProduct/all.ftl";
            }

            merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            page.setOffset((oldOffset > 0 ? (page.getOffset() - 1) : 0) * page.getLimit());
            PageInfo pageInfo = new PageInfo();
            dataList = planningScheduleProductBusinessApi.planningScheduleForProductStatus(
                    param.getPlanningScheduleId(), param.getStatus(), null, param.getFuzzyPlanningName());

            if (CollectionUtil.isEmpty(dataList)) {
                page.setRequestUrl(this.getRequestUrl(request));
                page.setOffset(oldOffset);
                page.setTotal(total);
                page.setRows(dataList);

                modelMap.put("begin", 0);
                modelMap.put("end", total);
                modelMap.put("pager", page);
                modelMap.put("param", param);
                modelMap.put("center_menu", "myplan");
                modelMap.put("merchant", merchant);
                return "/planningScheduleProduct/all.ftl";
            }

            //组装code
            List<String> productIds = Lists.newArrayList();
            dataList.forEach(planningScheduleProductBussinessDto -> {
                productIds.add(planningScheduleProductBussinessDto.getCode());
            });
            //status==null是全部，status==1是有货，status==2是无货
            //查询全部
            if (param.getStatus() == null) {
                /**
                 * TODO 改调用商品新API
                 */
                List<ProductDto> productDtoList = ecpProductBusinessApi.getSkuInfoByCodeAndBranchCode(productIds, merchant.getRegisterCode(),merchant.getId());
                Map<String,ProductDto> productDtoMap = new HashMap<>();
                productDtoList.forEach(productDto -> {
                    productDtoMap.put(productDto.getCode(),productDto);
                });
                dataList.forEach(dto -> {
                    ProductDto productDto = productDtoMap.get(dto.getCode());
                    if (productDto!=null){
                        dto.setProductName(productDto.getShowName());
                        dto.setSpec(productDto.getSpec());
                        dto.setManufacturer(productDto.getManufacturer());
                    }
                });
                total = Long.valueOf(dataList.size());
                page.setRequestUrl(this.getRequestUrl(request));
                page.setOffset(oldOffset);
                page.setTotal(total);
                page.setRows(dataList);
                modelMap.put("begin", total>0?1:0);
                modelMap.put("end", total);
                modelMap.put("pager", page);
                modelMap.put("param", param);
                modelMap.put("center_menu", "myplan");
                modelMap.put("merchant", merchant);
                return "/planningScheduleProduct/all.ftl";
            } else if (param.getStatus().equals(1)) {//查询有货
                /**
                 * TODO 改调用商品新API
                 */
                List<ProductDto> productDtoList = ecpProductBusinessApi.getSkuInfoByCodeAndBranchCode(productIds, merchant.getRegisterCode(),merchant.getId());

                if (CollectionUtil.isEmpty(productDtoList)){
                    total = 0L;
                    page.setRequestUrl(this.getRequestUrl(request));
                    page.setOffset(oldOffset);
                    page.setTotal(total);
                    page.setRows(Lists.newArrayList());

                    modelMap.put("begin", 0);
                    modelMap.put("end", total);
                    modelMap.put("pager", page);
                    modelMap.put("param", param);
                    modelMap.put("center_menu", "myplan");
                    modelMap.put("merchant", merchant);
                    return "/planningScheduleProduct/all.ftl";
                }

                List<PlanningScheduleProductBussinessDto> list = Lists.newArrayList();
                Map<String, PlanningScheduleProductBussinessDto> map = Maps.newHashMap();
                dataList.forEach(planningScheduleProductBussinessDto -> {
                    map.put(planningScheduleProductBussinessDto.getCode(), planningScheduleProductBussinessDto);
                });

                Map<String,ProductDto> productDtoMap = new HashMap<>();
                productDtoList.forEach(productDto -> {
                    productDtoMap.put(productDto.getCode(),productDto);
                });

//                productDtoMap.forEach(productDto -> {
//                    PlanningScheduleProductBussinessDto dto = map.get(productDto.getCode());
//                    if (dto != null) {
//                        dto.setProductName(productDto.getShowName());
//                        dto.setSpec(productDto.getSpec());
//                        dto.setManufacturer(productDto.getManufacturer());
//                        list.add(dto);
//                    }
//                });
                Set<String> keys = productDtoMap.keySet();
                for (String key : keys){
                    ProductDto productDto = productDtoMap.get(key);
                    PlanningScheduleProductBussinessDto dto = map.get(productDto.getCode());
                    if (productDto!=null && dto!=null){
                        dto.setProductName(productDto.getShowName());
                        dto.setSpec(productDto.getSpec());
                        dto.setManufacturer(productDto.getManufacturer());
                        list.add(dto);
                    }
                }
                total = Long.valueOf(list.size());
                page.setRequestUrl(this.getRequestUrl(request));
                page.setOffset(oldOffset);
                page.setTotal(total);
                page.setRows(list);
                modelMap.put("begin", total>0?1:0);
                modelMap.put("end", total);
                modelMap.put("pager", page);
                modelMap.put("param", param);
                modelMap.put("center_menu", "myplan");
                modelMap.put("merchant", merchant);
                return "/planningScheduleProduct/all.ftl";
            } else if (param.getStatus().equals(2)) {//无货
                /**
                 * TODO 改调用商品新API
                 */
                List<ProductDto> productDtoList = ecpProductBusinessApi.getSkuInfoByCodeAndBranchCode(productIds, merchant.getRegisterCode(),merchant.getId());
                //全是无货商品
                if (CollectionUtil.isEmpty(productDtoList)){
                    total = Long.valueOf(dataList.size());
                    page.setRequestUrl(this.getRequestUrl(request));
                    page.setOffset(oldOffset);
                    page.setTotal(total);
                    page.setRows(dataList);

                    modelMap.put("begin", 0);
                    modelMap.put("end", total);
                    modelMap.put("pager", page);
                    modelMap.put("param", param);
                    modelMap.put("center_menu", "myplan");
                    modelMap.put("merchant", merchant);
                    return "/planningScheduleProduct/all.ftl";
                }

                //部分无货
                List<PlanningScheduleProductBussinessDto> list = Lists.newArrayList();
                Map<String, ProductDto> map = Maps.newHashMap();
                productDtoList.forEach(productDto -> {
                    map.put(productDto.getCode(), productDto);
                });

                dataList.forEach(planningScheduleProductBussinessDto -> {
                    ProductDto dto = map.get(planningScheduleProductBussinessDto.getCode());
                    if (dto == null) {
                        list.add(planningScheduleProductBussinessDto);
                    }
                });

                total = Long.valueOf(list.size());
                page.setRequestUrl(this.getRequestUrl(request));
                page.setOffset(oldOffset);
                page.setTotal(total);
                page.setRows(list);

                modelMap.put("begin", total>0?1:0);
                modelMap.put("end", total);
                modelMap.put("pager", page);
                modelMap.put("param", param);
                modelMap.put("center_menu", "myplan");
                modelMap.put("merchant", merchant);
                return "/planningScheduleProduct/all.ftl";
            } else {//其他情况直接返回空
                page.setRequestUrl(this.getRequestUrl(request));
                page.setOffset(oldOffset);
                page.setTotal(0L);
                page.setRows(Lists.newArrayList());

                modelMap.put("begin", 0);
                modelMap.put("end", 0);
                modelMap.put("pager", page);
                modelMap.put("param", param);
                modelMap.put("center_menu", "myplan");
                modelMap.put("merchant", merchant);
                return "/planningScheduleProduct/all.ftl";
            }
        } catch (Exception e) {
            LOGGER.error("查询电子计划单全部商品异常,e" + ExceptionUtils.getStackTrace(e));
            page.setRequestUrl(this.getRequestUrl(request));
            page.setOffset(oldOffset);
            page.setTotal(0L);
            page.setRows(Lists.newArrayList());

            modelMap.put("begin", 0);
            modelMap.put("end", 0);
            modelMap.put("pager", page);
            modelMap.put("param", param);
            modelMap.put("center_menu", "myplan");
            modelMap.put("merchant", merchant);
            return "/planningScheduleProduct/all.ftl";
        }
    }


    @RequestMapping("/delete.json")
    @ResponseBody
    public Object delete(PlanningScheduleProductBussinessDto planningScheduleProduct) throws Exception {
        List<Long> ids = planningScheduleProduct.getIds();

        if (CollectionUtils.isEmpty(ids)) {
            return super.addError("没有可操作的id");
        }
        try {
            planningScheduleProductBusinessApi.batchDeleteByIdsAndUpdateProductAmount(ids, planningScheduleProduct.getPlanningScheduleId());
            return super.addResult("");
        } catch (Exception e) {
            LOGGER.error("删除电子计划单异常", e);
            return this.addError("删除电子计划单异常" + e);
        }
    }


    @RequestMapping("/update.json")
    @ResponseBody
    public Object update(PlanningScheduleProductBussinessDto planningScheduleProduct) throws Exception {
        if (planningScheduleProduct.getId() == null) {
            return super.addError("没有可操作的id");
        }
        try {
            planningScheduleProductBusinessApi.updateByPrimaryKeySelective(planningScheduleProduct);
            return super.addResult("");
        } catch (Exception e) {
            LOGGER.error("更新计划单详情异常", e);
            return this.addError("更新计划单详情异常" + e);
        }
    }


    /**
     * 拼接页面参数
     *
     * @param request
     * @return
     */
//	@Override
//	protected String getRequestUrl(HttpServletRequest request){
//		String url = "";
//		String requestUri = request.getRequestURI();
//		String queryString = request.getQueryString();
//		String qs = StringUtil.removeParameter(queryString, "offset");
//		if(requestUri.contains("/xyy-shop/")){
//			requestUri = requestUri.replace("/xyy-shop/", "/");
//		}
//		if (StringUtil.isNotEmpty(qs)) {
//			url = requestUri + "?" + EncodeUtil.urlDecode(qs,"UTF-8");
//		} else {
//			url = requestUri;
//		}
//		return url;
//	}
//

    /**
     * 批量添加商品到购物车
     *
     * @param planningScheduleProduct 商品id数组
     * @return
     */
    @RequestMapping("/batchAddProductToCart")
    @ResponseBody
    public Object batchAddProductToCart(PlanningScheduleProductBussinessDto planningScheduleProduct) {

        Map<String, Object> map = Maps.newHashMap();
//        try {
//            MerchantPrincipal merchant = null;
//            try {
//                merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            if (CollectionUtil.isEmpty(planningScheduleProduct.getIds())) {
//                return this.addError("添加到购物车的商品不能为空");
//            }
//            List<PlanningScheduleProductBussinessDto> checkedSkuList = planningScheduleProductBusinessApi.pcSelectCheckedSku(planningScheduleProduct);
//            if (CollectionUtil.isEmpty(checkedSkuList)) {
//                return this.addError("添加到购物车的商品不能为空!!");
//            }
//            for (PlanningScheduleProductBussinessDto ps : checkedSkuList) {
//                map.put(ps.getProductId() + "", ps.getPurchaseNumber());
//            }
//            if (CollectionUtil.isEmpty(map)) {
//                return this.addError("添加到购物车的商品不能为空");
//            }
//            Boolean result = planningScheduleProductBusinessApi.batchAddProductToCart(map, merchant.getId());
//            if (!result) {
//                return this.addError("添加到购物车失败");
//            }
//        } catch (Exception e) {
//            LOGGER.error("批量购买异常:", e);
//            return this.addError(e.getMessage());
//        }
        return this.addResult();
    }

    /**
     * 得到所有的种类
     *
     * @param planningScheduleProduct
     * @return
     */
    @RequestMapping("/category")
    @ResponseBody
    public Object category(PlanningScheduleProductBussinessDto planningScheduleProduct) {
        List<CategoryVo> categoryList = null;

        try {
            MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            categoryList = planningScheduleService.category(planningScheduleProduct, merchant.getId());
        } catch (Exception e) {
            LOGGER.error("获取分类异常", e);
        }
        return super.addResult("categoryList", categoryList);
    }


    /**
     * 点击选择或者取消  等待完善
     *
     * @param planningScheduleProduct
     * @return
     */
    @RequestMapping("/checked")
    @ResponseBody
    public Object checked(PlanningScheduleProductBussinessDto planningScheduleProduct, Integer isChecked) {
        PlanningScheduleProductBussinessDto entity = new PlanningScheduleProductBussinessDto();
        entity.setIsChecked(isChecked);
        List<Long> paramIds = planningScheduleProduct.getIds();
        if (CollectionUtils.isEmpty(paramIds)) {
            LOGGER.error("没有传递过来的ids数据");
            return this.addError("没有传递过来的记录id数据");
        }
        Long planningScheduleId = planningScheduleProduct.getPlanningScheduleId();
        if (planningScheduleId == null) {
            LOGGER.error("没有传递过来的planningScheduleId数据");
            return this.addError("没有传递过来的计划单主键数据");
        }

        if (isChecked == null) {
            LOGGER.error("没有传递过来的isChecked数据");
            return this.addError("没有传递过来的选择状态数据");
        }
        if (ArrayUtils.indexOf(new int[]{0, 1}, isChecked.intValue()) == -1) {
            LOGGER.error("选中isChecked的状态不合法");
            return this.addError("选中isChecked的状态不合法");
        }

        try {
            planningScheduleBussinessApi.checked(paramIds, isChecked);
        } catch (Exception e) {
            LOGGER.error("调用数据异常", e);
        }
        return this.addResult();
    }

    /**
     * 查询商品对应的厂家
     *
     * @param planningScheduleProduct
     * @return
     */
    @RequestMapping("/manufacturer")
    @ResponseBody
    public Object queryManufacturer(PlanningScheduleProductBussinessDto planningScheduleProduct) {

        Long planningScheduleId = planningScheduleProduct.getPlanningScheduleId();
        if (planningScheduleId == null) {
            LOGGER.error("没有传递过来的planningScheduleId数据");
            return this.addError("没有传递过来的计划单主键数据");
        }
        List<ManufacturerBusinessDTO> dataList = null;
        try {
            dataList = planningScheduleProductBusinessApi.queryManufacturer(planningScheduleProduct);
        } catch (Exception e) {
            LOGGER.error("调用数据异常", e);
        }
        return super.addResult("data", dataList);
    }

    /**
     * 编辑电子计划单内商品登记数量
     *
     * @param planningScheduleProduct
     * @return
     */
    @RequestMapping("/editPlanningScheduleProductNum")
    @ResponseBody
    public Object editPlanningScheduleProductNum(PlanningScheduleProductBussinessDto planningScheduleProduct, Long merchantId) {
        try {
            Long planningScheduleId = planningScheduleProduct.getPlanningScheduleId();
            String code = planningScheduleProduct.getCode();
            if (planningScheduleId != null && StringUtil.isNotEmpty(code)) {
                int r = planningScheduleProductBusinessApi.updateByCodeAndPlanningScheduleId(planningScheduleProduct);
                if (r > 0) {
                    return this.addResult();
                } else {
                    return this.addError("修改了了0条电子计划单，请重新修改！");
                }
            } else {
                return this.addError("计划单id或商品69码不能为空！");
            }
        } catch (Exception e) {
            LOGGER.error("计划单修改失败：", e.toString());
            return this.addError("修改电子计划单异常：" + e.getMessage());
        }
    }

    /**
     * 电子计划单创建
     *
     * @param id
     * @param status
     * @param modelMap
     * @param request
     * @return
     * @throws ServiceException
     */
    @RequestMapping(value = "/add.htm", method = RequestMethod.GET)
    public String addEdit(Long id, Sort sort, Integer status, ModelMap modelMap, HttpServletRequest request) {

        // 分页要用，将url传到前台
//		String url = getRequestUrl(request);
        String url = this.getRequestUrl(request);
        //筛选类目展开状态
        String cjZhan = request.getParameter("cjZhan");
        String ejZhan = request.getParameter("ejZhan");

        //获取筛选条件的厂商
        String manufacturer = request.getParameter("manufacturer");

        //一级分类筛选条件
        String categoryFirstId = request.getParameter("categoryFirstId");
        //二级分类筛选条件
        String categorySecondId = request.getParameter("categorySecondId");

        //处方分类筛选条件
        String drugClassification = request.getParameter("drugClassification");

        if ("99999".equals(categoryFirstId)) {//当选择的为全部分类时，二级分类和三级分类置空，且展开状态为关闭
            if (StringUtil.isNotEmpty(categorySecondId)) {
                url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                categorySecondId = null;
            }
            ejZhan = "2";
        }

        PlanningScheduleProductBussinessDto param = new PlanningScheduleProductBussinessDto();

        String fuzzyPlanningName = request.getParameter("fuzzyPlanningName");
        if (StringUtil.isNotEmpty(fuzzyPlanningName)) {
            param.setFuzzyPlanningName(fuzzyPlanningName);
        }

        if (StringUtil.isNotEmpty(manufacturer)) {
            param.setManufacturer(manufacturer);
            String[] s = {manufacturer};
            param.setManufacturers(s);
        }

        if (StringUtil.isNotEmpty(drugClassification)) {
            if ("5".equals(drugClassification)) {
                param.setDrugClassification(null);
            } else {
                param.setDrugClassificationStr(drugClassification);
                Integer[] a = new Integer[1];
                a[0] = Integer.parseInt(drugClassification);
                param.setDrugClassifications(a);
            }
        }
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            e.printStackTrace();
        }
        PlanningScheduleBussinessDto planningSchedule = null;
        if (id != null) {
            param.setPlanningScheduleId(id);
            planningSchedule = planningScheduleBussinessApi.selectByPrimaryKey(id);
//			Long hasGoodSkuNum = planningScheduleProductBusinessApi.countHasGoodSku(id);
            param.setMerchantId(merchant.getId());
            List<PlanningScheduleProductBussinessDto> hasGoodSkuList = ecpPlanningScheduleProductBusinessApi.pcSelectHasGoodSku(param, null);
            //添加计划单名显示
            modelMap.put("planningScheduleName", planningSchedule == null ? "" : planningSchedule.getPlanningName());
            if (CollectionUtil.isNotEmpty(hasGoodSkuList)) {
                modelMap.put("hasGoodSkuNum", hasGoodSkuList.size());
                modelMap.put("noHasGoodSkuNum", planningSchedule.getProductAmount() - hasGoodSkuList.size());
            } else {
                modelMap.put("hasGoodSkuNum", 0);
                modelMap.put("noHasGoodSkuNum", planningSchedule.getProductAmount());
            }
        } else {
            modelMap.put("hasGoodSkuNum", 0);
            modelMap.put("noHasGoodSkuNum", 0);
        }
        //获取销量的排序标识
        String orderSort = request.getParameter("order_sort");
        //综合排序-正序或者倒序
        if ("1".equals(orderSort)) {
            sort.setProperty("smsr.sale_num");
            sort.setDirection("desc");
        } else if ("2".equals(orderSort)) {//
            sort.setProperty("smsr.sale_num");
            sort.setDirection("asc");
        }

        //获取价格排序标识-正序或者倒序
        String orderFob = request.getParameter("order_fob");
        if ("1".equals(orderFob)) {
            sort.setProperty("t2.fob");
            sort.setDirection("desc");
        } else if ("2".equals(orderFob)) {
            sort.setProperty("t2.fob");
            sort.setDirection("asc");
        }

        //处理分类的筛选条件---当所选的分类的父类与所选的父类不对应时，清楚该分类的筛选
        if (StringUtil.isNotEmpty(categorySecondId)) {
            if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("0")) {//当所选一级分类不为空时，所选的二级分类都要和一级分类比较
                //得到所选二级分类信息
                CategoryBusinessDTO c2 = null;
                try {
                    c2 = categoryBusinessApi.selectByPrimaryKey(Long.parseLong(categorySecondId));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //如果所选二级分类的父类即一级分类也不是所选的一级分类，则将二级分类的筛选条件也去掉
                if (Long.parseLong(categoryFirstId) != c2.getParentId()) {
                    url = url.replace("&categorySecondId=" + Long.parseLong(categorySecondId), "");
                    categorySecondId = null;
                }
            }
        }
        String minPrice = request.getParameter("minPrice");
        String maxPrice = request.getParameter("maxPrice");
        //设置筛选的价格
        if (StringUtil.isNotEmpty(minPrice)) {
            param.setMinPrice(Double.parseDouble(minPrice));
        }
        if (StringUtil.isNotEmpty(maxPrice)) {
            param.setMaxPrice(Double.parseDouble(maxPrice));
        }

        if (StringUtil.isNotEmpty(categoryFirstId) && !categoryFirstId.equals("99999")) {
            param.setCategoryFirstId(Long.valueOf(categoryFirstId));
            param.setCategoryId(Long.valueOf(categoryFirstId));
        }
        if (StringUtil.isNotEmpty(categorySecondId)) {
            param.setCategorySecondId(Long.valueOf(categorySecondId));
            param.setCategoryId(Long.valueOf(categorySecondId));
        }
        List<PlanningScheduleProductBussinessDto> dataList = null;
        try {
            if (id != null) {
                dataList = ecpPlanningScheduleProductBusinessApi.pcSelectHasGoodSku(param, new SortBusinessDto());
            }
        } catch (Exception e) {
            LOGGER.error("获取计划单商品异常", e);
        }

        //替换商品价格为活动价格，只替换活动有的商品  TODO 等待商品提供
        List<Long> productIds = this.getProductIds(dataList);
        if (CollectionUtils.isNotEmpty(productIds)) {
            try {
                Map<Long, ProductSimpleVO> skuVOS = ecpProductBusinessApi.getSimpleProduct(merchant.getRegisterCode(), productIds,merchant.getId());
                dataList = replaceFobBySku(dataList, skuVOS, minPrice, maxPrice, orderFob);
            } catch (Exception e) {
                LOGGER.error("获取计划单商品活动信息异常", e);
            }
        }


        List<CategoryVo> listCategory = null;
        try {
            listCategory = planningScheduleService.category(param, merchant.getMerchantId());
        } catch (Exception e) {
            LOGGER.error("获取分类异常", e);
        }

        modelMap.put("cjZhan", StringUtil.isNotEmpty(cjZhan) ? Integer.parseInt(cjZhan) : 0);
        modelMap.put("ejZhan", StringUtil.isNotEmpty(ejZhan) ? Integer.parseInt(ejZhan) : 0);
        modelMap.put("url", url);
        modelMap.put("categoryFirstId", categoryFirstId);
        modelMap.put("categorySecondId", categorySecondId);
        modelMap.put("drugClassification", drugClassification);
        modelMap.put("paramOrder", dataList);
        modelMap.put("listCategory", listCategory);
        modelMap.put("center_menu", "myplan");
        modelMap.put("merchant", merchant);
        modelMap.put("orderSaleRank", orderSort);
        modelMap.put("orderFob", orderFob);
        modelMap.put("planningScheduleId", id);
        modelMap.put("planStatus", planningSchedule == null ? -1 : planningSchedule.getPlanStatus());
        modelMap.put("manufacturer", manufacturer);
        modelMap.put("fuzzyPlanningName", fuzzyPlanningName);
        //最小价格筛选条件
        modelMap.put("minPrice", minPrice);
        //最大价格的筛选条件
        modelMap.put("maxPrice", maxPrice);
        modelMap.put("totalSkuNum", planningSchedule == null ? 0 : planningSchedule.getProductAmount());
        if (CollectionUtil.isNotEmpty(dataList)) {
            modelMap.put("totalNum", dataList.size());
            BigDecimal totalMoney = BigDecimal.ZERO;
            List<ManufacturerBusinessDTO> manufacturerList = new ArrayList<>();
            List<String> mList = new ArrayList<>();
            for (PlanningScheduleProductBussinessDto pp : dataList) {
                totalMoney = totalMoney.add(pp.getSubTatol());
                if (!mList.contains(pp.getManufacturer())) {
                    mList.add(pp.getManufacturer());
                    ManufacturerBusinessDTO man = new ManufacturerBusinessDTO();
                    man.setManufacturer(pp.getManufacturer());
                    manufacturerList.add(man);
                }
            }
            modelMap.put("totalMoney", totalMoney);
            modelMap.put("manufacturerList", manufacturerList);
        }
        return "/planningSchedule/add.ftl";
    }
}
