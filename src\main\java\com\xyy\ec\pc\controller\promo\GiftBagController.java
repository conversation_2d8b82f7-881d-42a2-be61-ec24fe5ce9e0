package com.xyy.ec.pc.controller.promo;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ms.promotion.business.api.pc.GiftPackageForPcBusinessApi;
import com.xyy.ms.promotion.business.common.ErrorCodeEum;
import com.xyy.ms.promotion.business.common.ResultDTO;
import com.xyy.ms.promotion.business.common.response.PromoResp;
import com.xyy.ms.promotion.business.dto.gitfpackage.UserPackageBusiResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
@RequestMapping("/giftBag")
public class GiftBagController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(GiftBagController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Reference(version = "1.0.0")
    private GiftPackageForPcBusinessApi giftPackageForPcBusinessApi;

    @Autowired
    private PcVersionUtils pcVersionUtils;

    /**
     * 首页获取礼品包弹框
     * @param servletRequest
     * @param servletResponse
     * @param model
     * @return
     */
    @RequestMapping("/findLatestGiftBag")
    @ResponseBody
    public Object findLatestGiftBag(HttpServletRequest servletRequest, HttpServletResponse servletResponse, Model model){
        Long merchantId = null;
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (merchant == null) {
                return this.addError("请先登录");
            }
            merchantId = merchant.getId();
            ResultDTO<UserPackageBusiResp> resultDTO = giftPackageForPcBusinessApi.getLatestGiftPackage(merchantId);
            if (null != resultDTO && resultDTO.getErrorCode() == ErrorCodeEum.SUCCESS.getErrorCode()) {
                return this.addResult("userGiftBag", resultDTO.getData());
            }
        } catch (Exception e) {
            logger.error("获取用户{}的礼品包异常：", merchantId, e);
        }
        return this.addError("查询失败");
    }

    /**
     * 记录首页礼品包弹框
     * @param servletRequest
     * @param servletResponse
     * @param model
     * @return
     */
    @RequestMapping("/recordLatestGiftBag")
    @ResponseBody
    public Object recordLatestGiftBag(HttpServletRequest servletRequest, HttpServletResponse servletResponse, Model model, Long giftId){
        Long merchantId = null;
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (merchant == null) {
                return this.addError("请先登录");
            }
            merchantId = merchant.getId();
            giftPackageForPcBusinessApi.recordLatestGiftPackage(merchantId, giftId);
            return this.addResult();
        } catch (Exception e) {
            logger.error("记录用户{}的礼品包异常：", merchantId, e);
            return this.addError("记录失败");
        }
    }

    @RequestMapping("/findGiftBag.htm")
    public ModelAndView test(HttpServletRequest servletRequest, HttpServletResponse servletResponse, Model model, Page page){
        Long merchantId = null;
        try {
            String requestUrl = this.getRequestUrl(servletRequest);
            model.addAttribute("requestUrl", requestUrl);
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            if (null != page && page.getOffset() == 0) {
                page.setOffset(1);
            }

            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(6);
            if (null == page || 0 == page.getLimit()) {
                pageInfo.setPageNum(1);

            } else {
                pageInfo.setPageNum(page.getOffset());
            }

            model.addAttribute("center_menu", "giftBag");
            merchantId = merchant.getId();
            model.addAttribute("merchant",merchant);
            ResultDTO<PageInfo<UserPackageBusiResp>> userGiftBagPageResult = giftPackageForPcBusinessApi.selectUserGiftPage(merchantId, pageInfo);
            logger.info(String.format("查询用户: %s礼品包返回结果: %s", String.valueOf(merchantId),JSONObject.toJSONString(userGiftBagPageResult)));
            if(null != userGiftBagPageResult && userGiftBagPageResult.getErrorCode() == ErrorCodeEum.SUCCESS.getErrorCode() && null != userGiftBagPageResult.getData()){
                model.addAttribute("giftBagPage", userGiftBagPageResult.getData());
            }
        } catch (Exception e) {
            logger.error("获取用户{}的礼品包异常：", merchantId, e);
        }
        return new ModelAndView("giftbag/giftBag.ftl");
    }

    /**
     * 我的礼品包标记
     * @param servletRequest
     * @param servletResponse
     * @param model
     * @return
     */
    @RequestMapping("/haveLatestGiftPackage")
    @ResponseBody
    public Object haveLatestGiftPackage(HttpServletRequest servletRequest, HttpServletResponse servletResponse, Model model){
        Long merchantId = null;
        try {
            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            MerchantBussinessDto merchant = merchantPrincipal;
            if (merchant == null) {
                return this.addError("请先登录");
            }
            merchantId = merchant.getId();
            ResultDTO<Integer> haveLatestNumResult = giftPackageForPcBusinessApi.haveLatestGiftPackage(merchantId);
            if(null != haveLatestNumResult && null != haveLatestNumResult.getData()){
                return this.addResult("haveLatestNum", haveLatestNumResult.getData());
            }
        } catch (Exception e) {
            logger.error("获取用户{}的礼品包标记：", merchantId, e);
        }
        return this.addError("获取礼品包标记失败");
    }
}