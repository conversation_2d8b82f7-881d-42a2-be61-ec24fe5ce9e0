package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.message.Transaction;
import com.github.pagehelper.PageInfo;
import com.xyy.cat.util.CatUtil;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.order.business.api.Order618PromBusinessApi;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.config.OrderEnum;
import com.xyy.ec.order.business.dto.MyOrderActivityBusinessDto;
import com.xyy.ec.order.business.dto.Order618ActivityBusinessDto;
import com.xyy.ec.order.business.dto.Order618PromBusinessDto;
import com.xyy.ec.order.business.dto.OrderBusinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.service.CodeItemService;
import com.xyy.ec.pc.service.ShippingAddressService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.DateUtil;
import com.xyy.ec.pc.util.DateUtils;
import com.xyy.ec.system.business.api.RedisComponentApi;
import com.xyy.ms.promotion.business.api.common.prosale.vo.ProSaleResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName SuperRebateController
 * @Description
 * <AUTHOR>
 * @Date 2019/7/3 17:18
 **/
@Controller
@RequestMapping("/merchant/center/superRebate")
public class SuperRebateController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SuperRebateController.class);

    private  final  static String ORDER_618_REBATE_TIME = "ORDER_618_REBATE_TIME";
    private  final  static String REBATE_TIME = "REBATE_TIME";

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;

    @Reference(version = "1.0.0")
    private Order618PromBusinessApi order618PromBusinessApi;

    @Autowired
    private CodeItemService codeItemService;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private com.xyy.ms.promotion.business.api.common.prosale.ProSaleBusinessApi ProSaleBusinessApi;

    @Reference(version = "1.0.0")
    private RedisComponentApi redisComponentApi;

    @Autowired
    private ShippingAddressService shippingAddressService;

        /**
     * @Description 我的超级返一级页面
     * <AUTHOR>
     * @Date 2019/7/3
     * @Param [modelAndView]
     * @Return org.springframework.web.servlet.ModelAndView
     **/
    @RequestMapping("/index.htm")
    @ResponseBody
    public ModelAndView index(ModelMap modelMap, Page page, HttpServletRequest request) {
        PageInfo pageInfoParam = new PageInfo<>();
        pageInfoParam.setPageNum(page.getOffset());
        pageInfoParam.setPageSize(page.getLimit());
        try {
            /*获取用户信息*/
            Transaction t1 = CatUtil.initTransaction("getSuperRebateActivity", "getSuperRebateActivity");
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            CatUtil.successCat(t1);

            if(null != merchant && null!= merchant.getRegisterCode()){
                String branchCode = merchant.getRegisterCode();
                Date rebateTime = getRebateTime();
                /*根据用户所在区域调用促销接口获取活动信息*/
                Long merchantId = null != merchant.getId() ? merchant.getId() : null;
                //分页获取活动信息
                PageInfo<ProSaleResp> pageInfoPromo = order618PromBusinessApi.queryProSaleByPage(branchCode, pageInfoParam);
                logger.info("active page info -> " + JSONObject.toJSONString(pageInfoPromo));

                if(pageInfoPromo!=null){
                    List<ProSaleResp> proSaleRespList = pageInfoPromo.getList();
                    List<Order618PromBusinessDto> list = order618PromBusinessApi.getActivityData(merchantId,proSaleRespList,rebateTime);
                    logger.info("active data  -> " + JSONObject.toJSONString(list));

                    Page<Order618PromBusinessDto> pageInfo = new Page<>();
                    pageInfo.setRows(list);
                    pageInfo.setTotal(pageInfoPromo.getTotal());
                    pageInfo.setPageCount(pageInfoPromo.getPages());
                    pageInfo.setLimit(page.getLimit());
                    pageInfo.setOffset(page.getOffset());
                    String requestUrl = this.getRequestUrl(request);
                    pageInfo.setRequestUrl(requestUrl);
                    modelMap.put("pager", pageInfo);

                    //不分页获取区域下所有活动信息
                    List<ProSaleResp> allProSaleRespList = order618PromBusinessApi.queryAllProSale(branchCode);
                    logger.info("all active info -> " + JSONObject.toJSONString(allProSaleRespList));

                    List<Order618PromBusinessDto> allList = order618PromBusinessApi.getActivityData(merchantId,allProSaleRespList,rebateTime);
                    logger.info("active statistics  data  -> " + JSONObject.toJSONString(allList));

                    BigDecimal returnedTotal = allList.stream().filter(e->e!=null && e.getReturnedTotal()!=null).map(Order618PromBusinessDto::getReturnedTotal).reduce(BigDecimal.ZERO,BigDecimal::add);
                    BigDecimal estimateTotal = allList.stream().filter(e->e!=null && e.getEstimateTotal()!=null).map(Order618PromBusinessDto::getEstimateTotal).reduce(BigDecimal.ZERO,BigDecimal::add);
                    BigDecimal balanceTotal = allList.stream().filter(e->e!=null && e.getBalanceTotal()!=null).map(Order618PromBusinessDto::getBalanceTotal).reduce(BigDecimal.ZERO,BigDecimal::add);
                    BigDecimal realPayTotal = allList.stream().filter(e->e!=null && e.getRealPayTotal()!=null).map(Order618PromBusinessDto::getRealPayTotal).reduce(BigDecimal.ZERO,BigDecimal::add);

                    modelMap.put("returnedTotal", returnedTotal.setScale(2,BigDecimal.ROUND_HALF_UP));
                    modelMap.put("estimateTotal", estimateTotal.setScale(2,BigDecimal.ROUND_HALF_UP));
                    modelMap.put("balanceTotal", balanceTotal.setScale(2,BigDecimal.ROUND_HALF_UP));
                    modelMap.put("realPayTotal", realPayTotal.setScale(2,BigDecimal.ROUND_HALF_UP));
                } else {
                    logger.info("我的超级返查询促销活动数据为空。。。");
                }
            }
        } catch (Exception e) {
            logger.error("用户信息查询异常", e);
        }
        return new ModelAndView("/superRebate/superRebateActivity.ftl");
    }

    /**
     * 我的超级返订单
     *
     * @return
     */
    @RequestMapping(value = "/order.htm")
    public ModelAndView order(Date  startCreateTime,Date endCreateTime,Long promoId,Page page,
                              ModelMap modelMap, HttpServletRequest request) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

        Date rebateTime = getRebateTime();

        OrderBusinessDto order = new OrderBusinessDto();
        order.setMerchantId(merchant.getId());
        order.setStartCreateTime(startCreateTime);
        order.setEndCreateTime(endCreateTime);
        order.setStatuses(new Integer[]{1,2,3,7,90,91});

        PageInfo pageInfoParam = new PageInfo<>();
        pageInfoParam.setPageNum(page.getOffset());
        pageInfoParam.setPageSize(page.getLimit());

        logger.info("startCreateTime " + startCreateTime + " endCreateTime" + endCreateTime + " promoId " + promoId + " page " + JSONObject.toJSONString(page));

        final PageInfo<MyOrderActivityBusinessDto> modelPageInfo = orderBusinessApi.findActivityOrders(pageInfoParam, order);

        logger.info("findActivityOrders result -> " + JSONObject.toJSONString(modelPageInfo));

        List<MyOrderActivityBusinessDto> myOrderActivityBusinessDtoList = modelPageInfo.getList();

        myOrderActivityBusinessDtoList.forEach(orderActivityBusiness ->{
            Order618PromBusinessDto order618PromDto = new Order618PromBusinessDto();
            order618PromDto.setOrderNo(orderActivityBusiness.getOrderNo());
            order618PromDto.setPromoId(promoId);
            order618PromDto.setMerchantId(merchant.getId());
            order618PromDto.setStartCreateTime(startCreateTime);
            order618PromDto.setEndCreateTime(endCreateTime);
            Order618ActivityBusinessDto order618ActivityBusinessDto = order618PromBusinessApi.query618StatisticsData(order618PromDto);

            logger.info("query618StatisticsData request -> " + JSONObject.toJSONString(order618PromDto) + " \n and result ->" + JSONObject.toJSONString(order618ActivityBusinessDto));
            if(null != order618ActivityBusinessDto && null != order618ActivityBusinessDto.getRealPayTotal()){
                orderActivityBusiness.setRealPayTotal(order618ActivityBusinessDto.getRealPayTotal());
                orderActivityBusiness.setRefundTotal(order618ActivityBusinessDto.getRefundTotal());
                orderActivityBusiness.setBalanceTotal(order618ActivityBusinessDto.getBalanceTotal());
                orderActivityBusiness.setEstimateTotal(order618ActivityBusinessDto.getEstimateTotal());
                orderActivityBusiness.setReturnedTotal(order618ActivityBusinessDto.getReturnedTotal());
                boolean flag = rebateTime.compareTo(new Date()) > 0;
                String rebateStatus = flag ? "待返" : "已返";
                orderActivityBusiness.setRebateStatus(rebateStatus);
            }
        });

        Page<MyOrderActivityBusinessDto> pageInfo = new Page<>();
        pageInfo.setRows(myOrderActivityBusinessDtoList);
        pageInfo.setTotal(modelPageInfo.getTotal());
        pageInfo.setPageCount(modelPageInfo.getPages());
        String requestUrl = this.getRequestUrl(request);
        pageInfo.setRequestUrl(requestUrl);
        pageInfo.setOffset(page.getOffset());
        modelMap.put("pager", pageInfo);
        modelMap.put("rebateTime",DateUtil.date2String(getRebateTime(), DateUtil.PATTERN_DATE));
        modelMap.put("activityStartTime",DateUtil.date2String(startCreateTime, DateUtil.PATTERN_STANDARD));
        modelMap.put("showActivityEndTime",DateUtil.date2String(endCreateTime, DateUtil.PATTERN_STANDARD));
        if(endCreateTime!=null){
            Date endTime = endCreateTime;
            if(endTime.getHours()==0 && endTime.getMinutes()==0 && endTime.getSeconds()==0){
                endTime = DateUtils.getFrontDay(endTime, 1);
                modelMap.put("showActivityEndTime",DateUtil.date2String(endTime, DateUtil.PATTERN_STANDARD));
            }
        }
        modelMap.put("activityEndTime",DateUtil.date2String(endCreateTime, DateUtil.PATTERN_STANDARD));
        modelMap.put("promoId",promoId);
        return new ModelAndView("/superRebate/superRebateActivityOrder.ftl");
    }

    /**
     * 获取发放返利时间
     */
    public Date getRebateTime() {

        Map<String, String> codeMap = codeItemService.findCodeMap(ORDER_618_REBATE_TIME, null);
        try {
            if (CollectionUtil.isNotEmpty(codeMap) && codeMap.containsKey(REBATE_TIME)) {
                return DateUtil.string2Date(codeMap.get(REBATE_TIME), DateUtil.PATTERN_STANDARD);
            }
        } catch (Exception e) {

        }

        return new Date();
    }


    /**
     * 分页查询订单商品数据
     * 订单编号
     * 返利时间
     * offset 当前页
     * limit 查询多少页
     * /merchant/center/superRebate/orderDetail
     */
    @RequestMapping(value = "/orderDetail.htm")
    public String orderDetailData(HttpServletRequest request,Long promoId,
                                  Date startCreateTime, Date endCreateTime,
                                  String orderNo, Page page,
                                  Model model) {


        logger.info("orderDetailData startCreateTime " + startCreateTime + " endCreateTime" +
                "" + endCreateTime + " promoId " + promoId + " page " + JSONObject.toJSONString(page)
        + "orderNo " + orderNo);
        Date now = new Date();
        Date rebateTime = getRebateTime();

        int pa = page.getOffset() == null || page.getOffset() < 1 ? 1 : page.getOffset();
        int limit = page.getLimit() == null ? 0 : page.getLimit();
        int offset = (pa - 1) * limit;
        // YBM20190622130115100003
        Order618PromBusinessDto dto = new Order618PromBusinessDto();

        PageInfo<Order618PromBusinessDto> pageInfo = new PageInfo<Order618PromBusinessDto>();
        pageInfo.setPageNum(offset);
        pageInfo.setPageSize(limit);


        logger.info("query618OrderDetailForPc request " + JSONObject.toJSONString(dto) + " page is " + JSONObject.toJSONString(pageInfo));

        try {


            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            dto.setOrderNo(orderNo);
            dto.setMerchantId(merchant.getId());
            dto.setEndCreateTime(endCreateTime);
            dto.setStartCreateTime(startCreateTime);
            dto.setPromoId(promoId);

            PageInfo<Order618PromBusinessDto> result = order618PromBusinessApi.query618OrderDetailForPc(dto, pageInfo);
            Order618ActivityBusinessDto orderInfo = order618PromBusinessApi.query618StatisticsData(dto);
            logger.info("query618OrderDetailForPc result  " + JSONObject.toJSONString(result) );
            logger.info("query618StatisticsData result " + JSONObject.toJSONString(orderInfo));
            if(orderInfo == null){
                orderInfo = new Order618ActivityBusinessDto();
            }
            orderInfo.setOrderNo(orderNo);
            Page<Order618PromBusinessDto> pageInf = new Page<Order618PromBusinessDto>();
            pageInf.setRows(result.getList());
            pageInf.setTotal(result.getTotal());
            pageInf.setPageCount(result.getPages());
            String requestUrl = this.getRequestUrl(request);
            pageInf.setRequestUrl(requestUrl);
            pageInf.setOffset(page.getOffset());
            model.addAttribute("pager", pageInf);
            // 待返时间 大于当前时间则为 待返 否则为已返
            boolean flag = rebateTime.compareTo(now) > 0;
            String rebateStatus = flag ? "待返" : "已返";

            if (pageInf != null && CollectionUtil.isNotEmpty(pageInf.getRows())) {

                pageInf.getRows().forEach(e -> {
                            e.setStatusName(rebateStatus);
                            if(e != null && e.getProductAmount() != null && e.getRealPayAmount() != null && e.getProductAmount() > 0){
                                Double tmp = e.getRealPayAmount().doubleValue() / e.getProductAmount();
                                e.setRealPrice(BigDecimal.valueOf(tmp).setScale(2,BigDecimal.ROUND_HALF_UP));
                            }
                        }
                );

            }

            OrderBusinessDto businessDto = orderBusinessApi.selectByOrderNo(orderNo);
            model.addAttribute("orderStatus", OrderEnum.OrderStatus.maps.get(businessDto !=null ? businessDto.getStatus() : 0 )); // 订单状态
            model.addAttribute("rebateTime", DateUtil.date2String(rebateTime,DateUtil.PATTERN_DATE)); // 返利时间
            model.addAttribute("rebateStatus",rebateStatus); // 返利状态
            model.addAttribute("orderInfo", orderInfo);
            model.addAttribute("startCreateTime", DateUtil.date2String(startCreateTime, DateUtil.PATTERN_STANDARD));
            model.addAttribute("endCreateTime", DateUtil.date2String(endCreateTime, DateUtil.PATTERN_STANDARD));
            model.addAttribute("promoId", promoId);

        }catch (Exception e ){


        }




        return "/superRebate/orderDetail.ftl";
    }
}
