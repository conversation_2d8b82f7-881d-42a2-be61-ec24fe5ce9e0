package com.xyy.ec.pc.interceptor.helper;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.account.LoginAccountApi;
import com.xyy.ec.merchant.bussiness.dto.account.LoginAccountDto;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.utils.BaiduMapUtils;
import com.xyy.ec.pc.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.ArrayList;

@Slf4j
@Component
public class DifferentPlacesLoginHelper {

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    @Autowired
    private BaiduMapUtils baiduMapUtils;

    @Autowired
    private SpiderHelper spiderHelper;

    @Value("${account.different.places.login.check.open:false}")
    private Boolean isCheck;

    public boolean isDifferentPlacesLogin(JwtPrincipal jwtPrincipal) {

        // 判断是否开启异地登录检测
        if (!isCheck) {
            return false;
        }
        if (jwtPrincipal != null && jwtPrincipal.getIsDifferentPlacesLogin() != null && jwtPrincipal.getIsDifferentPlacesLogin()) {
            return true;
        }
        return false;
    }

    public boolean isDifferentPlacesLogin(LoginAccountDto loginAccountDto) {

        // 判断是否开启异地登录检测
        if (!isCheck) {
            return false;
        }
        // 判断是否为白名单ip
        if (spiderHelper.isSpiderWhiteIp()) {
            return false;
        }
        try {
            if (loginAccountDto != null && Constants.IS1.equals(loginAccountDto.getCheckSwitch())) {
                // 获取IP地址省份信息
                String ipProvince = baiduMapUtils.getIpProvince();
                if (StringUtils.isBlank(ipProvince)) {
                    return false;
                }
                if (loginAccountDto.getOftenLoginProvinces() != null && !loginAccountDto.getOftenLoginProvinces().isEmpty()) {
                    // 如果常登录省份不包含当前ip所在省份
                    if (!loginAccountDto.getOftenLoginProvinces().contains(ipProvince)) {
                        return true;
                    }
                }
                else {
                    // 记录登录地址
                    loginAccountDto.setOftenLoginProvinces(new ArrayList<String>(){{add(ipProvince);}});
                    loginAccountApi.updateCheckSwitchOrProvinceById(loginAccountDto);
                }
            }
        }
        catch (Exception e) {
            log.error("异地登录检查异常", e);
        }
        return false;
    }
}
