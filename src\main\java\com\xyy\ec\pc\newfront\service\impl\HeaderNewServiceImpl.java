package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.model.dto.XyyIpAddressInfoDTO;
import com.xyy.ec.pc.newfront.controller.HeaderNewController;
import com.xyy.ec.pc.newfront.dto.HostWordsRespVO;
import com.xyy.ec.pc.newfront.service.HeaderNewService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.XyyIpAddressService;
import com.xyy.ec.search.engine.api.EcHotWordsApi;
import com.xyy.ec.search.engine.dto.HotWordsDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Collections;
import java.util.List;


@Service
@Slf4j
@RequiredArgsConstructor
public class HeaderNewServiceImpl implements HeaderNewService {


    private final XyyIndentityValidator xyyIndentityValidator;

    private final XyyIpAddressService xyyIpAddressService;

    private final Config config;

    /**
     * 爬虫开关 1：开启 0 ：关闭
     */
    @Value("${crawler_switch}")
    private String crawlerSwitch;

    @Reference(version = "1.0.0")
    private EcHotWordsApi ecHotWordsApi;

    @Override
    public HostWordsRespVO getHotWords() {
        MerchantBussinessDto merchant = null;
        try {
            // 获取当前商户登录信息
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            // 忽略异常或记录日志
            log.error("头部标签获取用户失败");
        }

        String branchCode = BranchEnum.HUBEI_COUNTRY.getKey();
        if (merchant != null) {
            branchCode = merchant.getRegisterCode();
        } else {
            String currentBranchCode = getBranchCodeByIP();
            branchCode = StringUtils.isNotEmpty(currentBranchCode) ? currentBranchCode : branchCode;
        }

        List<HotWordsDTO> listHostSearch = Collections.emptyList();
        try {
            // type: 1热词 2搜索推荐
            listHostSearch = ecHotWordsApi.queryEffectiveHotSearchWords(branchCode, 1);
        } catch (Exception e) {
            // 记录日志
            log.error("pc,queryEffectiveHotSearchWords error,branchCode={}",branchCode,e);
        }
        HostWordsRespVO respVO = new HostWordsRespVO();
        respVO.setHotWords(listHostSearch);
        respVO.setCrawlerSwitch(crawlerSwitch);
//        result.put("merchant", merchant);
        return respVO;
    }

    private String getBranchCodeByIP() {
        try {
            ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            XyyIpAddressInfoDTO xyyIpAddressInfo = xyyIpAddressService.getXyyIpAddressInfo(attr.getRequest());
            return xyyIpAddressInfo.getBranchCode();
        } catch (Exception ex) {
            // 记录日志
            log.error("pc getBranchCodeByIP error : ", ex);
        }
        return null;
    }
}
