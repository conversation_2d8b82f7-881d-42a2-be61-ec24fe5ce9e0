package com.xyy.ec.pc.controller;

import com.xyy.ec.pc.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @Date: 2020/1/1
 */

@Slf4j
@RequestMapping("comparisonPrice")
@Controller
public class ComparisonPriceController extends BaseController {

//    @Autowired
//    XyyIndentityValidator xyyIndentityValidator;

//    @Reference(version = "1.0.0")
//    ProductCompareBusinessApi productCompareBusinessApi;

//    @RequestMapping("/skuInfo.htm")
//    public ModelAndView skuInfo(String showName, @RequestParam(required = false, defaultValue = "0") Integer offset, @RequestParam(required = false, defaultValue = "1") Integer pageNum, HttpServletRequest request) {
//
//        Map<String, Object> objModel = new HashMap<>();
//
//        try {
//            if (StringUtil.isNotEmpty(showName)) {
//                showName = showName.replaceAll(",","");
//                showName = showName.replaceAll("，","");
//            }
//
//            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
//            if (ObjectUtils.isEmpty(merchant) || ObjectUtils.isEmpty(merchant.getId())) {
//                return new ModelAndView(new RedirectView("/login/purchase/login.htm"));
//            }
//
//            Long merchantId = 0L;
//            //用户所属域的域编码
//            String branchCode = null;
//
//            if (merchant != null){
//                merchantId = merchant.getId();
//                branchCode = merchant.getRegisterCode();
//            }else {
//                branchCode = this.getBranchCodeByMerchantId(request,merchantId);
//            }
//
//            Cookie [] cookies = request.getCookies();
//            String deviceId = "";
//            for (Cookie cookie : cookies) {
//                if ("device_id".equals(cookie.getName())) deviceId = cookie.getValue();
//            }
//            log.info("Merchant ID : {}, Branch_code : {}, DeviceId : {}", merchantId, branchCode, deviceId);
//
//            // 分页要用，将url传到前台
//            String url = getRequestUrl(request);
//
//            //筛选类目展开状态
//            String cjZhan = request.getParameter("cjZhan");
//            String ejZhan = request.getParameter("ejZhan");
//            String sjZhan = request.getParameter("sjZhan");
//
//            //一级分类筛选条件
//            String categoryFirstId = request.getParameter("categoryFirstId");
//            //二级分类筛选条件
//            String categorySecondId = request.getParameter("categorySecondId");
//            //三级分类筛选条件
//            String categoryThirdId = request.getParameter("categoryThirdId");
//
//            //获取筛选条件的厂商
//            String manufacturer = request.getParameter("manufacturer");
//
//            //处方分类筛选条件
//            String drugClassification = request.getParameter("drugClassification");
//
//            SearchProductParams searchProductParams = new SearchProductParams()
//                    .setBranchCode(branchCode).setShowName(showName).setUrl(url).setMerchantId(merchantId)
//                    .setCjZhan(cjZhan).setEjZhan(ejZhan).setSjZhan(sjZhan)
//                    .setCategoryFirstId(categoryFirstId).setCategorySecondId(categorySecondId).setCategoryThirdId(categoryThirdId)
//                    .setManufacturer(manufacturer)
//                    .setDrugClassification(drugClassification)
//                    .setPageNum(pageNum).setOffset(offset);
//
//
//            SearchProductResultDTO productResult = productCompareBusinessApi.searchProductInfo(searchProductParams);
//
//            objModel.put("listCategory",productResult.getListCategory());
//
//            objModel.put("skuInfos",productResult.getProductInfos());
//
//            //下次开始  offset
//            objModel.put("offSet",productResult.getNextOffSet());
//
//            //当前页页码
//            objModel.put("pageNum",productResult.getPageNum());
//            objModel.put("nextPageNum",productResult.getNextPageNum());
//            //商品一级分类筛选数据
//            objModel.put("categoryFirstId", productResult.getCategoryFirstId());
//            //商品二级分类筛选数据
//            objModel.put("categorySecondId", productResult.getCategorySecondId());
//            //商品三级分类筛选数据
//            objModel.put("categoryThirdId", productResult.getCategoryThirdId());
//            //用户数据
//            objModel.put("merchant", merchant);
//            //用户id
//            objModel.put("merchantId", productResult.getMerchantId());
//            //厂家数据
//            objModel.put("manufacturerList", productResult.getManufacturerList());
//            //筛选的厂家数据
//            objModel.put("manufacturer", productResult.getManufacturer());
//            //经营类型
//            objModel.put("drugClassification",productResult.getDrugClassification());
//
//            objModel.put("requestUrl",productResult.getUrl());
//
//            //三级分类是否展开的标识
//            objModel.put("cjZhan",StringUtil.isNotEmpty(productResult.getCjZhan())?Integer.parseInt(productResult.getCjZhan()):0);
//            objModel.put("ejZhan",StringUtil.isNotEmpty(productResult.getEjZhan())?Integer.parseInt(productResult.getEjZhan()):0);
//            objModel.put("sjZhan",StringUtil.isNotEmpty(productResult.getSjZhan())?Integer.parseInt(productResult.getSjZhan()):0);
//
//            //搜索关键字（填充搜索框用的）
//            objModel.put("showName", productResult.getShowName());
//        } catch (Exception e){
//            log.info("查询商品信息失败！");
//        }
//        return new ModelAndView("/comparativePrice/sku_search_list.ftl", objModel);
//    }




}
