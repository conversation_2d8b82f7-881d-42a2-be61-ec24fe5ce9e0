package com.xyy.ec.pc.util;

import com.xyy.ec.cs.api.dto.HolidayDTO;

import java.time.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30
 * @description
 */
public class HolidayUtil {

    public static Duration calculateHolidayDuration(Instant instant1, Instant instant2, List<HolidayDTO> holidays) {
        if (instant1.isAfter(instant2)) {
            return Duration.ZERO;
        }

        ZonedDateTime start = instant1.atZone(ZoneId.systemDefault());
        ZonedDateTime end = instant2.atZone(ZoneId.systemDefault());

        Duration totalHoliday = Duration.ZERO;

        // 1. 处理起始时间在节假日中的情况
        if (isInHoliday(start.toLocalDate(), holidays)) {
            ZonedDateTime endOfDay = start.toLocalDate().plusDays(1).atStartOfDay(ZoneId.systemDefault());
            ZonedDateTime effectiveEnd = end.isBefore(endOfDay) ? end : endOfDay;
            totalHoliday = totalHoliday.plus(Duration.between(start, effectiveEnd));
            start = effectiveEnd;
        }

        // 2. 处理结束时间在节假日中的情况
        if (isInHoliday(end.toLocalDate(), holidays)) {
            ZonedDateTime startOfDay = end.toLocalDate().atStartOfDay(ZoneId.systemDefault());
            ZonedDateTime effectiveStart = start.isAfter(startOfDay) ? start : startOfDay;
            totalHoliday = totalHoliday.plus(Duration.between(effectiveStart, end));
            end = effectiveStart;
        }

        // 3. 计算中间完整的天数
        LocalDate currentDate = start.toLocalDate().plusDays(1);
        LocalDate endDate = end.toLocalDate().minusDays(1);

        while (!currentDate.isAfter(endDate)) {
            if (isInHoliday(currentDate, holidays)) {
                totalHoliday = totalHoliday.plus(Duration.ofDays(1));
            }
            currentDate = currentDate.plusDays(1);
        }

        return totalHoliday;
    }

    private static boolean isInHoliday(LocalDate date, List<HolidayDTO> holidays) {
        return holidays.stream()
                .anyMatch(h -> {
                    LocalDate holidayDate = h.getDt().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    return holidayDate.equals(date) && (h.getHoliday() == 1 || h.getWeekend() == 1);
                });
    }
}
