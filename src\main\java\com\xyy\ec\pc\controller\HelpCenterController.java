package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.merchant.bussiness.api.LicenseAuditBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.electron.data.bussiness.data.api.SubjectCompanyLetterBussinessApi;
import com.xyy.electron.data.bussiness.data.dto.LicensesDto;
import com.xyy.electron.data.bussiness.data.enums.SceneCodeEnum;
import com.xyy.electron.data.bussiness.data.tool.ErrorCode;
import com.xyy.electron.data.bussiness.data.tool.ResultUtil;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 帮助中心控制类
 * @ClassName: HelpCenterController 
 * <AUTHOR>
 * @date 2016-12-26 下午9:08:59
 */
@Controller
@RequestMapping("/helpCenter")
public class HelpCenterController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(HelpCenterController.class);

    @Reference(version = "1.0.0")
    private LicenseAuditBussinessApi licenseAuditBussinessApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private SubjectCompanyLetterBussinessApi subjectCompanyLetterBussinessApi;
	/**
	 * 跳转到帮助关于页面
	 * @Title: about
	 * @return
	 * String
	 * <AUTHOR> 
	 * @date 2016-12-26 下午9:30:38
	 */
    @RequestMapping("/about.htm")
    public String about(){
        return "/helpCenter/about.ftl";
    }
    
    /**
     * 跳转到联系我们页面
     * @Title: contact
     * @return
     * String
     * <AUTHOR> 
     * @date 2016-12-26 下午9:31:04
     */
    @RequestMapping("/contact.htm")
    public String contact(){
        return "/helpCenter/contact.ftl";
    }
    
    /**
     * 跳转到换货流程页面
     * @Title: returnprocess
     * @return
     * String
     * <AUTHOR> 
     * @date 2016-12-26 下午9:31:38
     */
    @RequestMapping("/returnprocess.htm")
    public String returnprocess(){
        return "/helpCenter/returnprocess.ftl";
    }
    
    /**
     * 跳转到退货规则页面
     * @Title: returnrule
     * @return
     * String
     * <AUTHOR> 
     * @date 2016-12-26 下午9:32:00
     */
    @RequestMapping("/returnrule.htm")
    public String returnrule(){
        return "/helpCenter/returnrule.ftl";
    }
    
    /**
     * 跳转到帮助投诉界面
     * @Title: cosu
     * @return
     * String
     * <AUTHOR> 
     * @date 2016-12-26 下午9:32:20
     */
    @RequestMapping("/cosu.htm")
    public String cosu(){
        return "/helpCenter/cosu.ftl";
    }
    
    /**
     * 跳转到APP操作页面
     * @Title: app
     * @return
     * String
     * <AUTHOR> 
     * @date 2016-12-26 下午9:33:26
     */
    @RequestMapping("/app.htm")
    public String app(){
        return "/helpCenter/app.ftl";
    }
    
    /**
     * 跳转到服务协议页面
     * @Title: serve
     * @return
     * String
     * <AUTHOR> 
     * @date 2016-12-26 下午9:34:39
     */
    @RequestMapping("/serve.htm")
    public String serve(){
        return "/helpCenter/serve.ftl";
    }
    
    /**
     * 跳转到法律声明页面
     * @Title: state
     * @return
     * String
     * <AUTHOR> 
     * @date 2016-12-26 下午9:35:21
     */
    @RequestMapping("/state.htm")
    public String state(){
        return "/helpCenter/state.ftl";
    }
    
    /**
     * 跳转到隐私协议新页面
     * @Title: privacy
     * @return
     * String
     * <AUTHOR> 
     * @date 2016-12-26 下午9:37:07
     */
    @RequestMapping("/privacy.htm")
    public String privacy(){
        return "/helpCenter/privacy.ftl";
    }
    
    /**
     * 跳转到网站操作页面
     * @Title: web
     * @return
     * String
     * <AUTHOR> 
     * @date 2016-12-26 下午9:33:26
     */
    @RequestMapping("/web.htm")
    public String web(){
        return "/helpCenter/web.ftl";
    }

    /**
     * 跳转到APP操作页面
     * @Title: app
     * @return
     * String
     * <AUTHOR>
     * @date 2016-12-26 下午9:33:26
     */
    @RequestMapping("/planning.htm")
    public String palnning(){
        return "/helpCenter/planning.ftl";
    }



    /**
     * @param
     * @return java.lang.String
     * @throws
     * @Description: 账户指引
     * <AUTHOR>
     * @date 2019-02-22 14:46
     */
    @RequestMapping("/account.htm")
    public String account(){
        return "/helpCenter/commonproblem-account.ftl";
    }

    /**
     * @param
     * @return java.lang.String
     * @throws
     * @Description:售后问题
     * <AUTHOR>
     * @date 2019-02-22 14:46
     */
    @RequestMapping("/aftermarket.htm")
    public String aftermarket(){
        return "/helpCenter/commonproblem-aftermarket.ftl";
    }


    /**
     * @param
     * @return java.lang.String
     * @throws
     * @Description:订单问题
     * <AUTHOR>
     * @date 2019-02-22 14:46
     */
    @RequestMapping("/order.htm")
    public String order(){
        return "/helpCenter/commonproblem-order.ftl";
    }


    /**
     * @param
     * @return java.lang.String
     * @throws
     * @Description:其他问题
     * <AUTHOR>
     * @date 2019-02-22 14:46
     */
    @RequestMapping("/other.htm")
    public String other(){
        return "/helpCenter/commonproblem-other.ftl";
    }


    /**
     * @param
     * @return java.lang.String
     * @throws
     * @Description:资质指引
     * <AUTHOR>
     * @date 2019-02-22 14:46
     */
    @RequestMapping("/qualification.htm")
    public String qualification(){
        return "/helpCenter/commonproblem-qualification.ftl";
    }

    /**
     * 用户是否能够看到小药药资质,0 隐藏， 1展示
     * @return
     * String
     * <AUTHOR>
     * @date 2020-1-14 下午9:30:38
     */
    @RequestMapping("/checkLicenseStatus.json")
    @ResponseBody
    public Integer checkLicenseStatus(){
        MerchantBussinessDto merchantBussinessDto=null;
        Integer boo = 0;
        try {
            merchantBussinessDto = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            if(merchantBussinessDto == null){
                return boo;
            }
            //判断用户是否在开放的区域
            String newbranchCodes = licenseAuditBussinessApi.getNewLicenseBranchCode();
            if(!newbranchCodes.contains(merchantBussinessDto.getRegisterCode()) && !newbranchCodes.contains(BranchEnum.ALL_COUNTRY.getKey())){
                return boo;
            }
            //判断用户资质是不是一审通过
            Boolean b = licenseAuditBussinessApi.getAuditStatusByMerchantId(merchantBussinessDto.getId());
            if(b != null && b){
                boo = 1;
                return boo;
            }
            //判断用户资质状态是不是未提交，或者首营待审核
            MerchantBussinessDto merchantBussinessDto1 = merchantBussinessApi.selectByPrimaryKey(merchantBussinessDto.getId());
            if(merchantBussinessDto1!=null && merchantBussinessDto1.getLicenseStatus()!= 1 && merchantBussinessDto1.getLicenseStatus()!= 5){
                boo = 1;
            }
            return boo;
        }catch (Exception e){
            LOGGER.error("获取用户资质状态异常：{}", ExceptionUtils.getStackTrace(e));
            return boo;
        }
    }

    /**
     * 公司资质列表
     * @return
     */
    @RequestMapping("/licenseList.htm")
    public ModelAndView licenseList(){
        try{
            Map<String,Object> model = new HashMap<String,Object>();
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            //判断用户是否在开放的区域
            String newbranchCodes = licenseAuditBussinessApi.getNewLicenseBranchCode();
            if(!newbranchCodes.contains(merchant.getRegisterCode()) && !newbranchCodes.contains(BranchEnum.ALL_COUNTRY.getKey())){
                model.put("msg","此功能暂未开放");
                return new ModelAndView("/helpCenter/xyyLicense.ftl",model);
            }
            String branchCode = merchant.getRegisterCode();
            String sceneCode = SceneCodeEnum.ec_ybm_scene_code.getCode();
            ResultUtil<List<LicensesDto>> resultUtil = subjectCompanyLetterBussinessApi.getContracts(branchCode,sceneCode,merchant.getRealName(), merchant.getId());
            if(resultUtil.getCode() == ErrorCode.FAIL){
                LOGGER.info("药帮忙资质获取失败,resultUtil:{}", JSONObject.toJSONString(resultUtil));
                model.put("msg",resultUtil.getMsg());
                return new ModelAndView("/helpCenter/xyyLicense.ftl",model);
            }
            model.put("data",resultUtil.getDataInfo());
            return new ModelAndView("/helpCenter/xyyLicense.ftl",model);
        }catch(Exception e){
            LOGGER.error("药帮忙资质获取异常,e="+e);
            return new ModelAndView("/helpCenter/xyyLicense.ftl");
        }
    }

}
