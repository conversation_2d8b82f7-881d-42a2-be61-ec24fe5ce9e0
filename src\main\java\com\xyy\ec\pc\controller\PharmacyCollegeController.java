package com.xyy.ec.pc.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.system.business.api.PharmacyCgAggregationBusinesApi;
import com.xyy.ec.system.business.dto.pharmacycollege.PharmacyAggregationRsp;
import com.xyy.ec.system.business.dto.pharmacycollege.PharmacyCollegeReqModel;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/***
 *
 * 药学院接口入口服务
 *
 */
@Controller
@RequestMapping("/pharmacyCollege")
public class PharmacyCollegeController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(PharmacyCollegeController.class);

    /***
     * 药学院服务入口
     */
    @Reference(version = "1.0.0")
    private PharmacyCgAggregationBusinesApi pharmacyCgAggregationBusinesApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;


    /***
     * TODO 不是很优雅，在服务端控制样式
     * @param model
     */
    public void setPharmacySchoolStyle(Model model) {
        model.addAttribute("styleClass2", "cur");
    }




    /**
     *
     */
    @RequestMapping(value = {"/pharmacyOrder.htm"}, method = RequestMethod.GET)
    public ModelAndView getAggregationView(HttpServletRequest request,
                                           @RequestParam(name = "orderTime", required = false) String orderTime) {
        Map<String, Object> model = new HashMap<>();
        model.put("orderTime", orderTime);
        return new ModelAndView("pharmacySchool/pharmacy-order.ftl", model);
    }


    /**
     * 药学院PC商城聚合服务
     * 获取首页药学院展示的视频内容简介
     */
    @RequestMapping(value = {"/getAggregationView.htm"}, method = RequestMethod.GET)
    public ModelAndView getAggregationView(Model model) {
        try {

            List<PharmacyAggregationRsp> rsp = pharmacyCgAggregationBusinesApi.getAggregationView();
            if (CollectionUtil.isNotEmpty(rsp)) {
                rsp.stream().forEach(r->{
                    if (r != null && r.getPharmacyCollegeRsplList() != null &&  CollectionUtil.isNotEmpty(r.getPharmacyCollegeRsplList().getList())) {
                        r.getPharmacyCollegeRsplList().getList().stream().forEach(s->{
//                            if (s.getIsFree() == 1){
                                s.setUrl(null);
//                            }
                        });
                    }
                });
            }
            model.addAttribute("pharmacyAggregationRspList", rsp);
            model.addAttribute("merchant", xyyIndentityValidator.currentPrincipal());
//            model.addAttribute("cdnPath", cdnPath);
            setPharmacySchoolStyle(model);

            return new ModelAndView("/pharmacySchool/pharmacy-school.ftl");

        } catch (Exception e) {
            logger.error("获取首页药学院展示的视频异常", e);
        }
        return null;
    }


    /**
     * 要学院列表页数据统一输出入口（药必考，问病百科，药指南，药咨询）
     */
    @RequestMapping(value = {"/getPharmacyCollegeList.htm"})
    public ModelAndView getPharmacyCollegeList(Model model, Integer offset, Integer limit, PageInfo<PharmacyCollegeReqModel> page, HttpServletRequest request, PharmacyCollegeReqModel req) {
        try {
            String requestUrl = getRequestUrl(request);
            if (null != page) {
                if (0 == page.getPageNum() || 0 == page.getPageSize()) {
                    if (offset == null && limit == null) {
                        page.setPageNum(1);
                    } else {
                        page.setPageNum(offset);
                    }
                    page.setPageSize(10);
                }
            }
            PharmacyAggregationRsp rsp = pharmacyCgAggregationBusinesApi.getPharmacyCollegeList(page, req);
            if (rsp != null && rsp.getPharmacyCollegeRsplList() != null &&  CollectionUtil.isNotEmpty(rsp.getPharmacyCollegeRsplList().getList())) {
                rsp.getPharmacyCollegeRsplList().getList().stream().forEach(s-> {
//                    if (s.getIsFree() == 1){
                        s.setUrl(null);
//                    }
                });
            }
            model.addAttribute("requestUrl", requestUrl);
            model.addAttribute("pharmacyAggregationRsp", rsp);
            setPharmacySchoolStyle(model);

            boolean isPharmacyInfo = pharmacyCgAggregationBusinesApi.isPharmacyInformation(rsp);

            //跳转药资讯,跳转一级别分类
            if (isPharmacyInfo) {
                return new ModelAndView("/pharmacySchool/pharmacist-information.ftl");
                //跳转视频列表
            } else {
                return new ModelAndView("/pharmacySchool/pharmacist-list.ftl");
            }


        } catch (Exception e) {
            logger.error("药帮忙一级和二级分类数据", e);
        }
        return null;
    }


    /***
     *
     *
     * 获取药学院详情数据
     * @param req
     * @return
     */
    @RequestMapping(value = {"/getPharmacyCollegeDetail.htm"})
    public ModelAndView getPharmacyCollegeDetail(Model model, PharmacyCollegeReqModel req) {
        try {
            PharmacyAggregationRsp rsp = pharmacyCgAggregationBusinesApi.getPharmacyCollegeDetail(req);
            if (rsp != null && rsp.getPharmacyCollegeRsplList() != null &&  CollectionUtil.isNotEmpty(rsp.getPharmacyCollegeRsplList().getList())) {
                rsp.getPharmacyCollegeRsplList().getList().stream().forEach(s-> {
                    if (s.getIsFree() == 1){
                        s.setUrl(null);
                    }
                });
            }
            model.addAttribute("pharmacyAggregationRsp", rsp);
            setPharmacySchoolStyle(model);

            boolean isPharmacyInfo = pharmacyCgAggregationBusinesApi.isPharmacyInformation(rsp);

            //跳转药资讯详情
            if (isPharmacyInfo) {
                return new ModelAndView("/pharmacySchool/pharmacist-information-details.ftl");
                //跳转视频列表
            } else {
                return new ModelAndView("/pharmacySchool/pharmacist-play.ftl");
            }
        } catch (Exception e) {
            logger.error("获取药学院详情数据异常", e);
        }
        return null;
    }


}
