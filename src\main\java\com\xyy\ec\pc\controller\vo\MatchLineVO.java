package com.xyy.ec.pc.controller.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 每行匹配数据
 */
@Data
public class MatchLineVO {
    // 导入保存的Id,对应tb_xyy_ka_order_import_product主键
    private Long excelId;
    //导入的商品条码
    private String excelCode;
    //导入的商品通用名称
    private String excelCommonName;
    //导入的商品规格
    private String excelSpec;
    //导入的生产厂家
    private String excelManufacturer;
    //导入的批准文号
    private String excelApprovalNumber;
    //导入的购买数
    private Integer excelBuyNum;
    //导入的价格
    private BigDecimal excelPrice;
    //行号
    private int lineNum;
    //**最优店铺编码**/
    private String optimalShopCode;
    //**最优商品ID**/
    private Long optimalSkuId;
    //**最优价格**/
    private BigDecimal optimalPrice;

    private String merchantCode;

    //未匹配到商品类型(8没有匹配到商品，6匹配到商品不能够买,13重复匹配)
    private Integer noMatchFlag;
    //未匹配到商品类型(8没有匹配到商品，6匹配到商品不能够买13重复匹配)
    private String noMatchMsg;
    /**
     * 是否自动修改采购数量
     */
    private int autoAdjustNum;
    //匹配到的商品
    List<MatchSkuVO> skus;
}
