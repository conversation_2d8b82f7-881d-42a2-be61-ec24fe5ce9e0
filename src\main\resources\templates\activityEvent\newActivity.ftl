<!DOCTYPE HTML>
<html>
<head>
    <#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>
        <#if (title?? && title!='')>
            ${title}
        <#else>
            药帮忙
        </#if>
    </title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">


    <#list jsList as js>
        <script type="text/javascript" src="${js}"></script>
    </#list>

    <link rel="stylesheet" href="/static/css/search.css?t=${t_v}" />
    <link rel="stylesheet" href="/static/css/lib.css" />

    <#list cssList as css>
        <link rel="stylesheet" href="${css}" />
    </#list>

    <style type="text/css">
        ${csstext}
    </style>

</head>

<body>
    <div class="container">
        <input type="hidden" id="merchantId" name="merchantId" value="${merchantId}"/>

        <!--头部导航区域开始-->
        <div class="headerBox" id="headerBox">
            <#include "/common/header.ftl" />
        </div>
        <!--头部导航区域结束-->

        <!--主体部分开始-->
            ${areaInfo}
        <!--主体部分结束-->

        <!--底部导航区域开始-->
        <div class="footer" id="footer">
            <#include "/common/footer.ftl" />
        </div>
        <!--底部导航区域结束-->
    </div>
</body>
<script type="text/javascript" src="/static/js/collection/addOrDelCollection.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/jquery.fly.min.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/search.js?t=${t_v}"></script>
<script type="text/javascript" src="/static/js/cart/list.js?t=${t_v}"></script>
<#--<script type="text/javascript" src="/static/js/requestAnimationFrame.js?t=${t_v}"></script>-->
<#--<script type="text/javascript" src="/static/js/lib.js?t=${t_v}"></script>-->
<script type="text/javascript">
    ${jstext}
    $("div[name='typecode']").each(function(j,item){
        var typeCode = item.id;
        var limit = item.getAttribute("limit");
        console.log(item);  //输出input 中的 value 值到控制台
        console.log(limit);  //输出input 中的 value 值到控制台
        var isLast = 0;
        if((j+1) == $("div[name='typecode']").length){
            isLast = 1;
        }
        // 你要实现的业务逻辑
        $.ajax({
            url: "/activityData/exhibitionData.htm?r=" + Math.random(),
            type: "POST",
            dataType: "html",
            traditional :true,
            data: {
                exhibitionId: typeCode,
                limit : limit,
                isLast: isLast
            },
            success: function(result){
                $("#" + typeCode).html(result);
                $(".mrth-new li").hover(
                    function() {
                        $(this).find(".showorno").css("display", "block");
                        setTimeout(function(){ $(".w-collectZone").removeClass("initial");},1000);
                        $(this).find(".w-collectZone").css("display", "block");
                    },
                    function() {
                        $(this).find(".showorno").css("display", "none");
                        $(this).find(".w-collectZone").css("display", "none");
                    }
                );

                /*减操作*/
                $("#" + typeCode).on("click",".sub",function() {
                    var step = 1;

                    var me = $(this),
                        txt = me.next(":text");
                    var isSplit = txt.attr("isSplit");
                    var middpacking = parseInt(txt.attr('middpacking'));
                    if(isSplit == 0){
                        step = parseFloat(middpacking);
                    }
                    var val = parseFloat(txt.val());
                    if(val <= step) {
                        txt.val(0);
                    } else {
                        txt.val(val - step);
                    }
                });
                /*加操作*/
                $("#" + typeCode).on("click",".add",function() {
                    var step = 1;
                    var me = $(this),
                        txt = me.prev(":text");
                    var middpacking = parseInt(txt.attr('middpacking'));
                    var val = parseFloat(txt.val());
                    txt.val(val + middpacking);
                });
            }
        });
    });

    $("div[name='activitycode']").each(function(j,item){
        var activitycode = item.id;
        var limit = item.value;
        console.log(item.id);  //输出input 中的 value 值到控制台
        // 你要实现的业务逻辑
        $.ajax({
            url: "/activityData/exhibitionData.htm?r=" + Math.random(),
            type: "POST",
            dataType: "html",
            traditional :true,
            data: {
                exhibitionId: typeCode
            },
            success: function(result){
                $("#" + typeCode).html(result);
                $(".mrth-new li").hover(
                    function() {
                        $(this).find(".showorno").css("display", "block");
                        setTimeout(function(){ $(".w-collectZone").removeClass("initial");},1000);
                        $(this).find(".w-collectZone").css("display", "block");
                    },
                    function() {
                        $(this).find(".showorno").css("display", "none");
                        $(this).find(".w-collectZone").css("display", "none");
                    }
                );

                /*减操作*/
                $("#" + typeCode).on("click",".sub",function() {
                    var step = 1;

                    var me = $(this),
                        txt = me.next(":text");
                    var isSplit = txt.attr("isSplit");
                    var middpacking = parseInt(txt.attr('middpacking'));
                    if(isSplit == 0){
                        step = parseFloat(middpacking);
                    }
                    var val = parseFloat(txt.val());
                    if(val <= step) {
                        txt.val(0);
                    } else {
                        txt.val(val - step);
                    }
                    return false;
                });
                /*加操作*/
                $("#" + typeCode).on("click",".add",function() {
                    var step = 1;
                    var me = $(this),
                        txt = me.prev(":text");
                    var middpacking = parseInt(txt.attr('middpacking'));
                    var val = parseFloat(txt.val());
                    txt.val(val + middpacking);
                    return false;
                });
            }
        });
    });
</script>
</html>