package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.model.dto.XyyIpAddressInfoDTO;
import com.xyy.ec.pc.newfront.dto.ShopListRespVO;
import com.xyy.ec.pc.newfront.dto.SkuRespVO;
import com.xyy.ec.pc.newfront.service.SkuSearchNewService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.service.XyyIpAddressService;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.dto.SearchRecordBusinessDTO;
import com.xyy.ec.search.engine.api.EcSearchApi;
import com.xyy.ec.search.engine.entity.ShopInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SkuSearchNewServiceImpl implements SkuSearchNewService {

    @Resource
    private XyyIndentityValidator xyyIndentityValidator;

    @Resource
    private XyyIpAddressService xyyIpAddressService;

    @Reference(version = "1.0.0")
    EcSearchApi searchApi;


    @Reference(version = "1.0.0",timeout = 60000)
    private MerchantBussinessApi merchantBussinessApi;

    @Override
    public SkuRespVO getShopInfoList(String showName, HttpServletRequest request) {
        try {
            if (StringUtil.isNotEmpty(showName)) {
                showName = showName.toUpperCase();
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = null;
            String branchCode = null;
            String provinceCode = null;
            if (merchant != null) {
                merchantId = merchant.getId();
                branchCode = merchant.getRegisterCode();
                provinceCode = merchant.getProvinceCode();
            } else {
                merchantId = 0L;
                branchCode = this.getBranchCodeByMerchantId(request, merchantId);
                if (StringUtils.isNotEmpty(branchCode)) {
                    if (Objects.equals(branchCode, BranchEnum.SHANXI_COUNTRY.getKey())) {
                        provinceCode = "140000";
                    } else {
                        provinceCode = branchCode.replaceFirst("XS", "");
                    }
                }
            }
            ApiRPCResult apiRPCResult = searchApi.suggest(merchantId, showName, StringUtils.isNotEmpty(provinceCode) ? Integer.parseInt(provinceCode) : null);
            if (apiRPCResult.isFail()) {
                log.error("获取商品名自动补全失败 code : {}, msg : {}, errMsg : {}", apiRPCResult.getCode(), apiRPCResult.getMsg(), apiRPCResult.getErrMsg());
                throw new AppException("获取商品名自动补全失败！", XyyJsonResultCodeEnum.FAIL);
            }
            List<String> suggestionList = (List<String>) apiRPCResult.getData();
            if (log.isDebugEnabled()) {
                log.debug("app sku findSkuNameList | merchantId:{}, result:{}", merchantId, suggestionList.toString());
            }
            List<SearchRecordBusinessDTO> searchDTOList = suggestionList.stream().map(x -> {
                SearchRecordBusinessDTO searchRecordBusinessDTO = new SearchRecordBusinessDTO();
                searchRecordBusinessDTO.setKeyword(x);
                return searchRecordBusinessDTO;
            }).collect(Collectors.toList());

            SkuRespVO skuRespVO = new SkuRespVO();
            skuRespVO.setKeywords(searchDTOList);
            // 查询店铺sug
            ApiRPCResult<List<ShopInfoVo>> shopInfoVoList = searchApi.shopSuggest(merchantId, showName);
            if (shopInfoVoList.isSuccess()){
                List<ShopInfoVo> data = shopInfoVoList.getData();
                List<ShopListRespVO> shopListRespVO;
                shopListRespVO = data.stream().map(shopInfoVo -> {
                    ShopListRespVO shopListRespVO1 = new ShopListRespVO();
                    BeanUtils.copyProperties(shopInfoVo, shopListRespVO1);
                    return shopListRespVO1;
                }).collect(Collectors.toList());

                skuRespVO.setShopList(shopListRespVO);
            }else {
                skuRespVO.setShopList(Collections.emptyList());
            }
            return skuRespVO;
        } catch (Exception e) {
            log.error("/new-front/list获取商品名自动补全失败", e);
            throw new AppException("获取商品名自动补全失败！", XyyJsonResultCodeEnum.FAIL);
        }
    }

    public String getBranchCodeByMerchantId(HttpServletRequest request, Long merchantId){
        if(merchantId == null || merchantId == 0L){
            XyyIpAddressInfoDTO xyyIpAddressInfo = xyyIpAddressService.getXyyIpAddressInfo(request);
            return xyyIpAddressInfo.getBranchCode();
        }else{
            return getBranchCodeByMerchantId(merchantId);
        }
    }


    /**
     * 通过用户id获取用户所在的域编码
     */
    protected String getBranchCodeByMerchantId(Long merchantId){
        String branchCode = merchantBussinessApi.getBranchCodeByMerchantId(merchantId);
        if(StringUtil.isEmpty(branchCode)){
            branchCode = BranchEnum.HUBEI_COUNTRY.getKey();
        }

        return branchCode;
    }

}
