/*公共样式*/
.row{ width: 1200px; margin: 0 auto; }
/* 清理浮动 */
.clear:after { visibility:hidden; display:block; font-size:0; content:" "; clear:both; height:0;}
.clear { zoom:1; /* for IE6 IE7 */ }
.checkbox-pretty span:before, .radio-pretty span:before{font-size: 150%;position: relative;top:2px;font-size: 130% \9;vertical-align: -3px \9;}
.checkbox-pretty.checked>span:before, .radio-pretty.checked>span:before{color: #00DC82;}
.checkbox-pretty:hover span:before, .radio-pretty:hover span:before{color: #00DC82;}
@media all and (-ms-high-contrast:none) {*::-ms-backdrop, .checkbox-pretty span:before, .radio-pretty span:before{font-size: 130%;}}
/*弹框样式修改*/
.sui-modal{ border: none }
.sui-modal .modal-header {padding: 0;margin: 0;border:none;height: 40px;background: #f5f5f5;}
.sui-close{margin: 10px;}
.sui-modal .modal-header .modal-title {font-size: 20px;color: #333;font-weight: normal;height: 40px;line-height: 40px;padding-left: 15px;}
.sui-modal .modal-footer{background: none;padding-bottom: 20px;}
.sui-modal .modal-body{font-size: 16px;padding-top: 20px;text-align: center;}
.sui-modal .modal-body .icon-tb-warnfill{font-size: 50px;color: #f3c335;}
.sui-modal .modal-body .icon-tb-roundcheckfill{font-size: 50px;color: #31cb96;}
.sui-modal .modal-body .icon-tb-roundclosefill{font-size: 50px;color: #ea4a36;}

.sui-modal .modal-footer .sui-btn{margin-right: 20px;width: 100px;height: 30px;}
.btn-primary:hover, .btn-primary:focus {color: #fff;background-color: #31cb96;border: 1px solid #31cb96;}
.btn-primary {color: #fff;background-color: #31cb96;filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);border: 1px solid #31cb96;}

/*分页器*/
.page{margin: 0 auto;text-align: center;margin-top: 30px;margin-bottom: 30px;}
.pagination-small ul>li>a, .pagination-small ul>li>span{font-size: 12px;padding: 5px 10px;color: #666;}
.sui-pagination ul>.active>a, .sui-pagination ul>.active>span{background: #31cb96;border-color:#31cb96;}
.sui-pagination ul li a:hover,.sui-pagination ul li a:focus{background: #31cb96;border-color:#31cb96;}
.sui-pagination ul li a:active{background: #31cb96;border-color:#31cb96;}
.sui-pagination ul>.active a:hover{background: #31cb96;border-color:#31cb96;}
.sui-pagination ul>.active a:active,.sui-pagination ul>.active a:visited{background: #00dc82;border-color:#00dc82;}
.sui-pagination div .page-num:focus{border: 1px solid #00dc82;}

.pagination-small div .page-num+.page-confirm, .pagination-small div .page-num+.page-confirm{background: #31cb96;border-color:#31cb96;}
.pagination-small div .page-num+.page-confirm:hover, .pagination-small div .page-num+.page-confirm:focus{background: #31cb96;border-color:#31cb96;}
.sui-navbar .sui-nav>li>a.cjzmkj:hover{  color: #00dc82; }
input[type="number"]{-moz-appearance:textfield;}
input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance: none !important;margin: 0;}
.sui-navbar .sui-nav>li>a:hover{color: #555;text-decoration: none;background-color: #e2e2e2;}


/* 下拉框样式修改 */
.ybm-drop-down {position: relative;height: 35px;}
.ybm-drop-down .ybm-title {color: #404C59;font-size: 16px;cursor: pointer;position: relative;}
.ybm-drop-down .ybm-title i {padding-left: 3px;}
.ybm-drop-down .ybm-drop-down{float: left;}
.ybm-drop-down .ybm-menu {width: 158px;display: none;position: absolute;top: 25px;left: 0px;background: #fff;z-index: 99;border: 1px solid #D9D9D9;box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.2);border-radius: 4px;}
.ybm-drop-down .ybm-menu li {height: 32px;line-height: 32px;cursor: pointer;font-size: 12px;color: #595959;white-space: nowrap;padding-left: 8px;}
.ybm-drop-down .ybm-menu li:hover {background: #e5fff5;}

/* 客服入口样式 */
.kefu-box{position: fixed;bottom:25px;right:40px;z-index:1000;}
.kefu-box img{width:80px;}

/* 店铺活动标签样式 */
.shop-activity-tag{
    position: absolute;
}
.shop-activity-tag img{
    width:100%;
    max-width:100% !important;
    height:100%;
    margin:0 !important;
}
.shop-activity-tag>span{
    position:absolute;
    left:0;
    width:100%;
    font-family: MicrosoftYaHei;
    color: #ffffff;
    box-sizing: border-box;
}
.shop-activity-tag .top-box{
    top:0;
}
.shop-activity-tag .bottom-box{
    bottom:0;
}
.shop-activity-tag .price-box{
    position:absolute;
    width:100%;
    font-weight: 700;
    font-family: MicrosoftYaHei;
    color:#EB3A3A;
}
.shop-activity-tag.w290{
    width:290px;
    height:290px;
    top:40px;
    left:50%;
    margin-left:-145px;
}
.shop-activity-tag.w290 .top-box{
    line-height:45px;
    font-size: 20px;
}
.shop-activity-tag.w290 .bottom-box{
    line-height: 29px;
    font-size: 18px;
}
.shop-activity-tag.w290 .price-box{
    bottom:29px;
    line-height: 43px;
    font-size: 24px;
}
.shop-activity-tag.w290 .time-box{
    bottom:72px;
    line-height: 30px;
    font-size: 20px;
    text-align: left;
    padding-left:10px;
}
.shop-activity-tag.w185{
    width:185px;
    height:180px;
    top:0;
    left:50%;
    margin-left: -92.5px;
}
.shop-activity-tag.w185 .top-box{
    line-height:28px;
    font-size: 14px;
}
.shop-activity-tag.w185 .bottom-box{
    line-height: 18px;
    font-size: 12px;
}
.shop-activity-tag.w185 .price-box{
    bottom:18px;
    line-height: 27px;
    font-size: 16px;
}
.shop-activity-tag.w185 .time-box{
    bottom:45px;
    line-height: 18px;
    font-size: 12px;
    text-align: left;
    padding-left:8px;
}

.consumer-rebate{
    position: fixed;bottom: 128px;right: 40px;z-index: 1000; display:none;
}
.rebate-head{
   height: 120px;width: 200px;background: #FF4A27;border-radius: 10px;position: relative;
}
.rebate-flex{
    display: flex;justify-content: space-between;padding: 5px 10px 0 10px;position: relative;
}
.red-packet-h{height: 20px;position: absolute;right: 11px;bottom: -5px;}
.rebate-content-box{width: 184px;height: 80px; background: #FFFFFF; border-radius: 6px; padding: 17px 8px;  box-sizing: border-box; margin-left: 8px; margin-top: 5px; color: #000000;font-size: 16px;}
.state-content{color: #000;text-align: center;line-height: 1.3;}
.rebate-look-btn{width: 100px;height: 32px;background-image: linear-gradient(-80deg, #FF2222 40%, #FE510F 100%);
            border: 2px solid #FFDFA2;border-radius: 18.28px;line-height: 32px;text-align: center;color: #fff;position: absolute;
            left: 45.5px;bottom: -12px;cursor: pointer;padding-right: 5px;}
.rebate-content-box span{color:#FF0000;}
.rebate-btn-icon{width: 14px; height: 14px;border-radius: 50%; position: absolute; right: 6px;top: 10px;}