package com.xyy.ec.pc.common.params;

import com.xyy.ec.pc.common.enums.AppEventTrackingSpTypeEnum;
import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchSceneEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * APP埋点参数spId生成参数。供于app项目埋点。
 *
 * <AUTHOR>
 * @see AppEventTrackingSpTypeEnum
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppEventTrackingSpIdGenerateParam implements Serializable {

    /**
     * spType 枚举，必填。
     *
     * @see AppEventTrackingSpTypeEnum
     */
    private AppEventTrackingSpTypeEnum spTypeEnum;

    /* 其他特例参数，PS：根据场景自行补充 */
    /**
     * 用户ID
     */
    private Long merchantId;
    /**
     * 标题
     */
    private String title;
    /**
     * 位置
     */
    private Integer location;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 搜索场景
     */
    private EcpSearchSceneEnum searchScene;

    /**
     * 搜索词
     */
    private String queryWord;
}
