package com.xyy.ec.pc.newfront.dto;

import com.xyy.ec.pc.search.ecp.vo.PcSearchGroupPurchaseVO;
import com.xyy.ec.pc.search.ecp.vo.PcSearchOperationVO;
import lombok.Data;

@Data
public class SearchCardRespVO  {
    /**
     * 商品id。
     * 为了兼容前端新老搜索公用同一个到手价组件，取的数据层级一致。
     */
    private Long id;

    /**
     * 卡片类型。1商品，2运营位。
     */
    private Integer cardType;

    /**
     * 新商品信息
     */
    private SearchProductRespVO productInfo;

    /**
     * 运营信息
     */
    private PcSearchOperationVO operationInfo;

    /**
     * 组合购信息
     */
    private PcSearchGroupPurchaseVO groupPurchaseInfo;
}
