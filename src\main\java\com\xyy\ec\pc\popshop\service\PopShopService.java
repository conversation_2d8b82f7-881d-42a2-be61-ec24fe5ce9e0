package com.xyy.ec.pc.popshop.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.pc.controller.vo.ShopGoodsVo;
import com.xyy.ec.pc.popshop.vo.*;
import com.xyy.ec.system.business.dto.CompanyBranchBusinessDto;

import java.util.List;

/**
 * <AUTHOR> lizhi<PERSON>
 * @description: POP店铺服务
 * create at:  2021/3/16  16:42
 **/
public interface PopShopService {
    /**
     * pop商家店铺首页展示
     * @param orgId
     * @param merchantId
     * @param branchCode
     * @return
     */
    ShopIndexVo shopIndexView(String orgId, Long merchantId, String branchCode);

    /**
     * pop商家店铺首页展示
     * @param orgId
     * @param merchantId
     * @param branchCode
     * @return
     */
    List<ShopListProductVo> shopGood(String orgId, Long merchantId, Integer floorId, Integer floorType, String branchCode);

    /**
     * 店铺企业信息
     * @param orgId
     * @return
     */
    CompanyDetailVo getPopCompanyDetail(String orgId, String branchCode);

    /**
     * 分页查询供应商
     * @param merchantId 买家id
     * @param orgName 商户名称
     * @param pageNum 页码
     * @param pageSize 每页条数
     * @return
     */
    PageInfo<CompanyBranchBusinessDto> pageQueryCompany(Long merchantId,String orgName, int pageNum, int pageSize);

    /**
     *资质列表
     * @param orgId 机构id
     * @return
     */
    List<ShopQualificationVO> getCorporationQualification(String orgId);

    /**
     * 开户流程
     * @param orgId
     * @return
     */
    ShopOpenAccountProcessVo getClientAccountInfoByOrgId(String orgId);

    /**
     * 配送售后
     * @param orgId
     * @return
     */
    String getReturnNoticeByOrgId(String orgId) throws Exception;

    /**
     * 资质下载
     * @param id
     * @return
     */
    String compressionCorporationQualification(String orgId, Long id);

    ShopGoodsVo shopGoodV2(String orgId, Long merchantId, Integer floorId, Integer floorType, String branchCode);
}
