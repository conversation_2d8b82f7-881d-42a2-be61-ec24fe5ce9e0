package com.xyy.ec.pc.base;

import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.dto.AccountInfoDto;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2018/8/26 下午3:55
 */
public class MerchantPrincipal extends MerchantBussinessDto implements Principal  {

    @Override
    public Long getLastLoginTime() {
        Date lastLoginDate = super.getLastLoginDate();
        return lastLoginDate==null? System.currentTimeMillis():lastLoginDate.getTime();
    }

    @Override
    public Long getMechantId() {
        return super.getId();
    }

    @Override
    public String getIdentity(){
        return getAccountId().toString();
    }

}
