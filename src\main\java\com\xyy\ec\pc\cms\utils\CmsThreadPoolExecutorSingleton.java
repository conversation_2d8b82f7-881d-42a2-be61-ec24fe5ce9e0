package com.xyy.ec.pc.cms.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

/**
 * {@link ThreadPoolExecutor} CMS工具类，应用内缓存一个线程池
 *
 * <AUTHOR>
 */
@Slf4j
public class CmsThreadPoolExecutorSingleton {

    /* 商品组活跃事件 */
    /**
     * 商品组活跃事件-队列数
     */
    public static final int WORK_QUEUE_CAPACITY_EXHIBITION_LIVE_EVENT = 2048;
    /**
     * 队列
     */
    private static final BlockingQueue<Runnable> WORK_QUEUE_EXHIBITION_LIVE_EVENT = new LinkedBlockingQueue<>(WORK_QUEUE_CAPACITY_EXHIBITION_LIVE_EVENT);
    /**
     * 以默认配置参数构建线程池实例
     */
    private static final ThreadPoolExecutor THREAD_POOL_EXECUTOR_EXHIBITION_LIVE_EVENT = new ThreadPoolExecutor(
            Math.max(Runtime.getRuntime().availableProcessors() - 1, 3),
            Math.max(Runtime.getRuntime().availableProcessors() - 1, 3), 0,
            TimeUnit.SECONDS, WORK_QUEUE_EXHIBITION_LIVE_EVENT, Executors.defaultThreadFactory(), (r, e) -> {
        String msg = String.format("商品组活跃事件-线程池 Thread pool is EXHAUSTED!" +
                        " Thread Name: %s, Pool Size: %d (active: %d, core: %d, max: %d, largest: %d), Task: %d (completed: %d)," +
                        " Executor status:(isShutdown:%s, isTerminated:%s, isTerminating:%s)",
                r.toString(), e.getPoolSize(), e.getActiveCount(), e.getCorePoolSize(), e.getMaximumPoolSize(), e.getLargestPoolSize(),
                e.getTaskCount(), e.getCompletedTaskCount(), e.isShutdown(), e.isTerminated(), e.isTerminating());
        log.info(msg);
        throw new RejectedExecutionException(msg);
    });

    /**
     * “商品组活跃事件”：获取单例的{@link ThreadPoolExecutor} 单例
     *
     * @return
     */
    public static ThreadPoolExecutor getInstanceForExhibitionLiveEvent() {
        return THREAD_POOL_EXECUTOR_EXHIBITION_LIVE_EVENT;
    }
}
