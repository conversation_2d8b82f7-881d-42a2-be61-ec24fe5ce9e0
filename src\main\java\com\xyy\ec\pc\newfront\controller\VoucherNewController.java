package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.dto.CouponRespVO;
import com.xyy.ec.pc.newfront.service.VoucherNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@CustomizeCmsResponse
@RestController
@RequestMapping("/new-front/merchant/center/voucher")
public class VoucherNewController {

    @Resource
    private VoucherNewService voucherNewService;

    @PostMapping("/receive-voucher")
    public AjaxResult<Integer> receiveVoucher(@RequestBody List<Long> templateIds) {
        try {
            return voucherNewService.receive(templateIds);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @PostMapping("/coupon-info")
    public AjaxResult<List<CouponRespVO>> getCouponBase(@RequestBody List<Long> templateIds){
        try {
            return voucherNewService.getCoupon(templateIds);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }
}
