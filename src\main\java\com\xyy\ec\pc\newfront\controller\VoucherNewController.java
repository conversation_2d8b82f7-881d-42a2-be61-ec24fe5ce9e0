package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.dto.CouponRespVO;
import com.xyy.ec.pc.newfront.dto.ReceiveCouponRespVO;
import com.xyy.ec.pc.newfront.dto.ReceiveVoucherRespVO;
import com.xyy.ec.pc.newfront.dto.VoucherNewRespVO;
import com.xyy.ec.pc.newfront.service.VoucherNewService;
import com.xyy.ec.pc.newfront.vo.CheckCouponParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@CustomizeCmsResponse
@RestController
@RequestMapping("/new-front/merchant/center/voucher")
public class VoucherNewController {

    @Resource
    private VoucherNewService voucherNewService;

    @PostMapping("/receive-voucher")
    public AjaxResult<Integer> receiveVoucher(@RequestBody List<Long> templateIds) {
        try {
            return voucherNewService.receive(templateIds);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @PostMapping("/coupon-info")
    public AjaxResult<List<CouponRespVO>> getCouponBase(@RequestBody List<Long> templateIds){
        try {
            return voucherNewService.getCoupon(templateIds);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @PostMapping("/received")
    public AjaxResult<ReceiveCouponRespVO> receiveCouponsInfo(){
        try {
            return voucherNewService.getReceiveCouponsInfo();
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @PostMapping("/platform-coupons")
    public AjaxResult<VoucherNewRespVO> platformCouponsInfo(@RequestBody CheckCouponParamVO couponParamVO){
        try {
            return voucherNewService.getPlatformCouponsInfo(couponParamVO);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @PostMapping("/new-customer-coupons")
    public AjaxResult<VoucherNewRespVO> newCustomerCouponsInfo(@RequestBody CheckCouponParamVO couponParamVO){
        try {
            return voucherNewService.getNewCustomerCouponsInfo(couponParamVO);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @PostMapping("/product-coupons")
    public AjaxResult<VoucherNewRespVO> productCouponsInfo(@RequestBody CheckCouponParamVO couponParamVO){
        try {
            return voucherNewService.getProductCouponsInfo(couponParamVO);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    @PostMapping("/shop-coupons")
    public AjaxResult<VoucherNewRespVO> shopCouponsInfo(@RequestBody CheckCouponParamVO couponParamVO){
        try {
            return voucherNewService.getShopCouponsInfo(couponParamVO);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }


    @GetMapping("/receive-only-voucher")
    public AjaxResult<ReceiveVoucherRespVO> receiveOnlyVoucher(Long templateId) {
        try {
            return voucherNewService.receiveOnlyVoucher(templateId);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }





}
