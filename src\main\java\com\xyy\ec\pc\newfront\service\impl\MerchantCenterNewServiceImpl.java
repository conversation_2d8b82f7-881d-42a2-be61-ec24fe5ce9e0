package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.client.api.query.MyCouponSearchService;
import com.xyy.ec.marketing.client.dto.coupon.MyCouponPageDto;
import com.xyy.ec.marketing.hyperspace.api.ShopCouponForMerchantQueryApi;
import com.xyy.ec.merchant.bussiness.api.BalanceBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantAuthenticationBusinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.SignInRecordBussinessApi;
import com.xyy.ec.merchant.bussiness.api.account.JgBuryingPointApi;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantAuthenticationBusinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.licence.LicenseValidDto;
import com.xyy.ec.merchant.bussiness.dto.merchant.AddMerchantResultDto;
import com.xyy.ec.merchant.bussiness.dto.merchant.TycBasicDataDto;
import com.xyy.ec.merchant.bussiness.enums.PlatformEnum;
import com.xyy.ec.merchant.bussiness.params.MerchantAddClerkLicenseAuditParam;
import com.xyy.ec.merchant.bussiness.params.MerchantClerkLicenseAuditImageParam;
import com.xyy.ec.merchant.bussiness.params.MerchantListAccountRelatedMerchantsQueryParam;
import com.xyy.ec.merchant.bussiness.params.MerchantRelateShopParam;
import com.xyy.ec.merchant.bussiness.result.MerchantRelateShopResult;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.merchant.server.api.MerchantApi;
import com.xyy.ec.merchant.server.dto.AccountMerchantRelDto;
import com.xyy.ec.merchant.server.dto.LoginAccountDto;
import com.xyy.ec.merchant.server.dto.SimpleMerchantDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRelStatusEnum;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.api.pay.PingAnAccountApi;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderDetailBusinessApi;
import com.xyy.ec.order.business.api.OrderExtendBusinessApi;
import com.xyy.ec.order.business.config.OrderEnum;
import com.xyy.ec.order.business.dto.MyOrderBusinessDto;
import com.xyy.ec.order.business.dto.OrderBusinessDto;
import com.xyy.ec.order.dto.pay.PingAnCreditMerchantDto;
import com.xyy.ec.pc.common.helpers.MerchantClerkLicenseAuditImageParamHelper;
import com.xyy.ec.pc.common.params.AppMerchantClerkLicenseAuditImageParam;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.controller.vo.merchant.MerchantAccountInfoVO;
import com.xyy.ec.pc.controller.vo.merchant.MerchantAccountRelatedMerchantInfoVO;
import com.xyy.ec.pc.enums.MerchantStatusEnum;
import com.xyy.ec.pc.enums.TerminalTypeEnum;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.interceptor.helper.SpiderHelper;
import com.xyy.ec.pc.model.Merchant;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.newfront.dto.MerchantRespVO;
import com.xyy.ec.pc.newfront.service.MerchantCenterNewService;
import com.xyy.ec.pc.newfront.utils.RegexUtil;
import com.xyy.ec.pc.newfront.vo.AddMerchantParamVO;
import com.xyy.ec.pc.newfront.vo.MerchantParamVO;
import com.xyy.ec.pc.newfront.vo.MerchantStatusSearchParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.service.MerchantService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CookieUtils;
import com.xyy.ec.pc.util.JsonUtil;
import com.xyy.ec.pc.util.ipip.IPUtils;
import com.xyy.ms.promotion.business.common.constants.VoucherEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class MerchantCenterNewServiceImpl implements MerchantCenterNewService {
    private final XyyIndentityValidator xyyIndentityValidator;
    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    @Reference(version = "1.0.0")
    private ShopCouponForMerchantQueryApi shopCouponForMerchantQueryApi;

    @Reference(version = "1.0.0")
    private SignInRecordBussinessApi signInRecordBussinessApi;

    @Reference(version = "1.0.0")
    private PingAnAccountApi pingAnAccountApi;

    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;

    @Reference(version = "1.0.0")
    private OrderDetailBusinessApi orderDetailBusinessApi;

    @Reference(version = "1.0.0")
    private OrderExtendBusinessApi orderExtendBusinessApi;

    @Reference(version = "1.0.0")
    private MerchantLicenseApi merchantLicenseApi;

    @Reference(version = "1.0.0")
    private MerchantAuthenticationBusinessApi merchantAuthenticationBusinessApi;

    @Reference(version = "1.0.0")
    private BalanceBussinessApi balanceBussinessApi;

    private final SpiderHelper spiderHelper;

    @Reference(version = "1.0.0")
    private MyCouponSearchService myCouponSearchService;

    private final MerchantService merchantService;
    private final CodeItemServiceRpc codeItemServiceRpc;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private JgBuryingPointApi jgBuryingPointApi;

    private static final Logger logger = LoggerFactory.getLogger(com.xyy.ec.pc.controller.MerchantCenterController.class);

    @Value("${ka.business.type}")
    private String kaBusinessType;

    @Value("${ka.branch}")
    private String kaBranch;

    @Value("${ka.balance.switch}")
    private Boolean kaBalanceSwitch;

    @Reference(version = "1.0.0")
    private MerchantApi merchantApi;


    private final Config config;


    /**
     * 获取资质提醒  type有值说明是主页弹窗，一天弹一次
     */
    @Override
    public AjaxResult<LicenseValidDto> validityRemind(TerminalTypeEnum terminalType) throws Exception {

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return AjaxResult.errResult("请登录");
            }
            ApiRPCResult<LicenseValidDto> mapApiRPCResult = merchantLicenseApi.licenseValidityRemindV3(merchant.getId(),terminalType.getValue());
            if (mapApiRPCResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                return AjaxResult.errResult(mapApiRPCResult.getMsg());
            }
            return AjaxResult.successResult(mapApiRPCResult.getData());

    }


    @Override
    public AjaxResult<MerchantRespVO> selectUser() throws Exception {
        MerchantRespVO merchantRespVO = new MerchantRespVO();

        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipalEaseEx();
        if (merchant == null) {
           return AjaxResult.errResult("请登录");
        }

        //商户信息
        Merchant m = new Merchant();
        BeanUtils.copyProperties(merchant, m);
        merchantRespVO.setMerchant(m);

        //订单信息
        OrderBusinessDto orderBusinessDto = new OrderBusinessDto();
        orderBusinessDto.setMerchantId(merchant.getId());
        orderBusinessDto.setVisibled(OrderBusinessDto.STATUS_VISIBLED);
        if (Objects.nonNull(merchant.getAccountRole())){
            orderBusinessDto.setAccountRole(merchant.getAccountRole());
        }
        //如果账号角色是子账号
        if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
            merchantRespVO.setSubAccount(merchant.getAccountRole());
            orderBusinessDto.setAccountId(merchant.getAccountId());
        } else {
            orderBusinessDto.setAccountId(null);
        }
        // TODO 类型返回的类型不一致，需要处理
        Map<String, ?> resultMap = orderBusinessApi.findNumGroupByStatus(orderBusinessDto);
        logger.info("实际返回类型: {}", resultMap.get("waitPayNum").getClass());
        merchantRespVO.setWaitPayNum( resultMap.get("waitPayNum"));
        merchantRespVO.setWaitShippingNum( resultMap.get("waitShippingNum"));
        merchantRespVO.setWaitReceiveNum( resultMap.get("waitReceiveNum"));
        merchantRespVO.setWaitAppraiseNum(resultMap.get("waitAppraiseNum"));

        //请求订单列表
        OrderBusinessDto order = new OrderBusinessDto();
        order.setMerchantId(merchant.getId());
        order.setVisibled(OrderBusinessDto.STATUS_VISIBLED);
        order.setNoQueryStatus(OrderEnum.OrderStatus.DELETE.getId());
        order.setStatuses(new Integer[]{OrderEnum.OrderStatus.SHIPPING.getId(), OrderEnum.OrderStatus.WAITBUYERPAY.getId()});
        //如果账号角色是子账号
        if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
            order.setAccountId(merchant.getAccountId());
        } else {
            order.setAccountId(null);
        }
        PageInfo<OrderBusinessDto> pageDto = new PageInfo<>();
        pageDto.setPageNum(1);
        pageDto.setPageSize(10);
        PageInfo<MyOrderBusinessDto> modelPageInfo = orderBusinessApi.findMerchantOrders(pageDto, order);
        List<MyOrderBusinessDto> pendingOrderList = modelPageInfo.getList();
        //进行订单过滤,待支付订单优先取3条
        List<MyOrderBusinessDto> orderList =  getFilteredOrders(pendingOrderList);
        //方法内部已做空列表处理，所以这里不需要处理空列表
        merchantRespVO.setOrderList(orderList);


        //优惠卷已领取优惠券数(未使用)
        int queryVoucherState = VoucherEnum.MerchantVoucherStatusEnum.RECEIVED.getState();

        MyCouponPageDto   allMerchantVoucher = myCouponSearchService.getAllMerchantVoucher(merchant.getId(), queryVoucherState, true);

        merchantRespVO.setVoucherNum(allMerchantVoucher.getUnUseNum());
        //账户状态
        final BigDecimal virtualGold = merchantService.queryVirtualGold(merchant.getMerchantId());
        ApiRPCResult<PingAnCreditMerchantDto> pingan = pingAnAccountApi.queryCreditByMerchantId(merchant.getMerchantId());
        merchantRespVO.setVirtualGold(virtualGold);
        int accountState = 0;
        if (pingan.isSuccess() && pingan.getData() != null && pingan.getData().getAccountState().equals(32)) {
            accountState = 1;
        }
        merchantRespVO.setAccountState(accountState);
        MerchantAuthenticationBusinessDto merchantAuthenticationBusinessDto = merchantAuthenticationBusinessApi.getInfoByMerchantId(merchant.getId());
        if(merchantAuthenticationBusinessDto != null) {
            merchantRespVO.setStatus(merchantAuthenticationBusinessDto.getStatus());
        }

        //todo 暂时写死头像url
        merchantRespVO.setAvatarUrl("https://vue.ybm100.com/xyy-micro-core/tmp/default_head.png");
        return AjaxResult.successResult(merchantRespVO);

    }

    @Override
    public AjaxResult<Integer> validity() {
        // final String validity = "validity";
        Integer status = 0;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                throw new AppException("请先登录", XyyJsonResultCodeEnum.FAIL);
            }
            logger.info("资质过期或者临期判断标识, 用户:{}",merchant.getId());
            ApiRPCResult<Integer> rs = merchantLicenseApi.licenseValidity(merchant.getId());
            if (rs==null){
                return AjaxResult.successResult(status);
            }
            status = rs.getData();
        }catch (Exception e){
            logger.error("资质过期或者临期判断标识接口异常",e);
            throw new AppException("资质过期或者临期判断标识接口异常",XyyJsonResultCodeEnum.FAIL);
        }
        return AjaxResult.successResult(status);
    }


    @Override
    public AjaxResult<MerchantRespVO> selectMerchant(HttpServletRequest request, MerchantParamVO merchantParamVO) throws Exception {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            MerchantRespVO merchantRespVO = new MerchantRespVO();
            if(merchant == null){
                return AjaxResult.errResult("未登录，请重新登录！");
            }
            AccountMerchantRelDto accountMerchantRelDto = loginAccountApi.selectByAccountIdAndMerchantId(merchant.getAccountId(),merchantParamVO.getMerchantId());
            if(accountMerchantRelDto == null){
              return AjaxResult.errResult("非法入参");
            }
            if(!Objects.equals(accountMerchantRelDto.getStatus(), AccountMerchantRelStatusEnum.PASS.getValue())){
                return AjaxResult.errResult("店铺审核中或委托书未上传");
            }
            MerchantBussinessDto bussinessDto = merchantService.getMerchant(merchantParamVO.getMerchantId());
            if(bussinessDto == null){
                return AjaxResult.errResult("店铺非法");
            }
            if(bussinessDto.getStatus() != MerchantStatusEnum.STATUS_NORMAL.getId()){
                return AjaxResult.errResult("该店铺已冻结，请选择其他店铺");
            }

            String realIP = IPUtils.getClientIP(request);
            logger.info("登录用户id:{}",merchantParamVO.getMerchantId() + "_" + realIP);
            //设置店铺信息到cookie
            xyyIndentityValidator.setPrincipalMerchant(merchantParamVO.getMerchantId(), merchant.getAccountId());
            // 判断是否需要登出做短信验证
            merchantRespVO.setCrawler(false);
            if (spiderHelper.getNewSpiderInterceptionOpen()) {
                if (spiderHelper.needSmsVerify(merchant.getAccountId().toString())) {
                    LoginAccountDto loginAccountDto = loginAccountApi.selectLoginAccountById(merchant.getAccountId());
                    if (loginAccountDto != null) {
                        merchantRespVO.setMobile(loginAccountDto.getMobile());
                    }
                    merchantRespVO.setCrawler(true);
                }
            }
            merchantRespVO.setLicenseStatus(bussinessDto.getLicenseStatus());
            CookieUtils.setJgLogin();
            return AjaxResult.successResult(merchantRespVO);

    }

    @Override
    public AjaxResult<MerchantRespVO> listAccountRelatedMerchants(MerchantParamVO merchantParamVO) throws Exception {
        Long merchantId = null;
        Long accountId = null;
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return AjaxResult.errResult("未登录，请重新登录！");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getMerchantId();
            if (logger.isDebugEnabled()) {
                logger.debug("分页查询账号关联的店铺，开始，accountId：{}，merchantId：{}，isOnlyCanDirectLogin：{}，pageNoStr：{}，pageSizeStr：{}",
                        accountId, merchantId, merchantParamVO.getIsOnlyCanDirectLogin(), merchantParamVO.getPageNum(), merchantParamVO.getPageSize());
            }
            // 入参合法性校验
            boolean isOnlyCanDirectLogin;
            if (merchantParamVO.getIsOnlyCanDirectLogin() ==null){
                merchantParamVO.setIsOnlyCanDirectLogin(false);
                isOnlyCanDirectLogin = false;
            }else {
                isOnlyCanDirectLogin = merchantParamVO.getIsOnlyCanDirectLogin();
            }
            Integer pageNum = merchantParamVO.getPageNum();
            Integer pageSize = merchantParamVO.getPageSize();
            boolean pageNoStrIsLegal = Objects.nonNull(pageNum) && pageNum > 0;
            boolean pageSizeStrIsLegal = Objects.nonNull(pageSize) && pageSize > 0;
            if (logger.isDebugEnabled()) {
                logger.debug("分页查询账号关联的店铺，参数校验结果，pageNoStrIsLegal：{}，pageSizeStrIsLegal：{}，accountId：{}，merchantId：{}，isOnlyCanDirectLogin：{}，pageNoStr：{}，pageSizeStr：{}",
                        pageNoStrIsLegal, pageSizeStrIsLegal, accountId, merchantId, merchantParamVO.getIsOnlyCanDirectLogin(), merchantParamVO.getPageNum(), merchantParamVO.getPageSize());
            }
            if (!pageNoStrIsLegal) {
                 return AjaxResult.errResult("翻页错误，请重新加载页面");
            }
            if (!pageSizeStrIsLegal) {
                return AjaxResult.errResult("翻页错误，请重新加载页面");
            }
            MerchantListAccountRelatedMerchantsQueryParam queryParam = MerchantListAccountRelatedMerchantsQueryParam.builder()
                    .accountId(accountId).isOnlyCanDirectLogin(isOnlyCanDirectLogin).pageNum(pageNum).pageSize(pageSize).build();
            PageInfo<MerchantAccountRelatedMerchantInfoVO> pageInfo = merchantService.listAccountRelatedMerchants(queryParam);
            if (logger.isDebugEnabled()) {
                logger.debug("分页查询账号关联的店铺，结果，pageInfo：{}，accountId：{}，merchantId：{}，isOnlyCanDirectLogin：{}，pageNoStr：{}，pageSizeStr：{}",
                        JSONObject.toJSONString(pageInfo),
                        accountId, merchantId, merchantParamVO.getIsOnlyCanDirectLogin(), merchantParamVO.getPageNum(), merchantParamVO.getPageSize());
            }
            MerchantRespVO merchantRespVO = new MerchantRespVO();
            merchantRespVO.setMerchantInfoPage(pageInfo);
            return AjaxResult.successResult(merchantRespVO);

    }


    @Override
    public AjaxResult<MerchantAccountInfoVO> getAccountInfo() {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return AjaxResult.errResult("未登录，请重新登录！");
            }
            merchantId = merchant.getMerchantId();
            accountId = merchant.getAccountId();
            if (logger.isDebugEnabled()) {
                logger.debug("获取当前登录的账号信息，开始，accountId：{}，merchantId：{}", accountId, merchantId);
            }
            LoginAccountDto loginAccountDto = loginAccountApi.selectLoginAccountById(accountId);
            String accountMobile = Objects.nonNull(loginAccountDto) ? loginAccountDto.getMobile() : null;
            MerchantAccountInfoVO merchantAccountInfoVO = MerchantAccountInfoVO.builder()
                    .accountId(accountId)
                    .merchantId(merchantId)
                    .role(merchant.getAccountRole())
                    .mobile(accountMobile).build();
            if (logger.isDebugEnabled()) {
                logger.debug("获取当前登录的账号信息成功，accountId：{}，merchantId：{}，merchantAccountInfoVO：{}",
                        accountId, merchantId, JSONObject.toJSONString(merchantAccountInfoVO));
            }
            return AjaxResult.successResult(merchantAccountInfoVO);
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("获取当前登录的账号信息失败，accountId：{}，merchantId：{}，msg：{}，异常信息：", accountId, merchantId, e.getMsg(), e);
            }
            return AjaxResult.errResult(e.getMsg());
        } catch (Exception e) {
            logger.error("获取当前登录的账号信息失败，accountId：{}，merchantId：{}，异常信息：", accountId, merchantId, e);
            return AjaxResult.errResult(Constants.MSG_ERROR);
        }
    }

    @Override
    public AjaxResult<MerchantRelateShopResult> relateShop(HttpServletRequest request, String poiIdStr) {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return AjaxResult.errResult("未登录，请重新登录！");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getMerchantId();

            logger.info("关联店铺，开始，accountId：{}，merchantId：{}，poiIdStr：{}", accountId, merchantId, poiIdStr);
            // 入参合法性校验
            Long poiId = null;
            if (StringUtils.isNotEmpty(poiIdStr)) {
                try {
                    poiId = Long.parseLong(poiIdStr);
                } catch (Exception e) {
                }
            }
            boolean poiIdStrIsLegal = Objects.nonNull(poiId) && poiId > 0;
            logger.info("关联店铺，参数校验结果，poiIdStrIsLegal：{}，accountId：{}，merchantId：{}，poiIdStr：{}",
                    poiIdStrIsLegal, accountId, merchantId, poiIdStr);
            if (!poiIdStrIsLegal) {
                return AjaxResult.errResult("请选择店铺");
            }
            MerchantRelateShopParam param = MerchantRelateShopParam.builder().poiId(poiId).accountId(accountId)
                    .terminalType(TerminalTypeEnum.PC.getValue()).build();
            MerchantRelateShopResult result = merchantService.relateShop(param);
            logger.info("关联店铺，结果，result：{}，accountId：{}，merchantId：{}，poiIdStr：{}",
                    JSONObject.toJSONString(result), accountId, merchantId, poiIdStr);

            {
                // 由于在关联店铺后，若角色是店长角色则FE逻辑会跳到资质管理页面（/merchant/center/licenseAudit/findLicenseCategoryInfo.htm），故而这里在关联成功后需要保存cookie。
                if (Objects.equals(result.getAccountRole(), 1)) {
                    xyyIndentityValidator.setPrincipalMerchant(result.getMerchantId(), accountId);
                    String realIP = IPUtils.getClientIP(request);
                    logger.info("关联店铺，当前账号为店长角色，保存登录凭证，accountId:{}, merchantId:{}, realIP:{}", accountId, result.getMerchantId(), realIP);
                    // 判断是否需要登出做短信验证
                }
            }

            return AjaxResult.successResult(result);
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("关联店铺失败，accountId：{}，merchantId：{}，poiIdStr：{}，msg：{}，异常信息：",
                        accountId, merchantId, poiIdStr, e.getMsg(), e);
            }
            return AjaxResult.errResult(e.getMsg());
        } catch (Exception e) {
            logger.error("关联店铺失败，merchantId：{}，poiIdStr：{}，accountIdStr：{}，异常信息：",
                    accountId, merchantId, poiIdStr, e);
            return AjaxResult.errResult(Constants.MSG_ERROR);
        }
    }

    @Override
    public AjaxResult<Object> addClerkLicenseAudit(String targetMerchantIdStr, String licenseAuditImagesJsonStr) {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return AjaxResult.errResult("未登录，请重新登录！");
            }
            merchantId = merchant.getMerchantId();
            accountId = merchant.getAccountId();

            log.info("添加店员资质审核信息，开始，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，licenseAuditImagesJsonStr：{}",
                    accountId, merchantId, targetMerchantIdStr, licenseAuditImagesJsonStr);
            // 入参合法性校验
            Long targetMerchantId = null;
            List<AppMerchantClerkLicenseAuditImageParam> appImageParams = null;
            if (!org.springframework.util.StringUtils.isEmpty(targetMerchantIdStr)) {
                try {
                    targetMerchantId = Long.parseLong(targetMerchantIdStr);
                } catch (Exception e) {
                }
            }
            if (!org.springframework.util.StringUtils.isEmpty(licenseAuditImagesJsonStr)) {
                try {
                    appImageParams = JSONArray.parseArray(licenseAuditImagesJsonStr, AppMerchantClerkLicenseAuditImageParam.class);
                } catch (Exception e) {
                }
            }
            List<MerchantClerkLicenseAuditImageParam> imageParams = MerchantClerkLicenseAuditImageParamHelper.creates(appImageParams);
            boolean targetMerchantIdStrIsLegal = Objects.nonNull(targetMerchantId) && targetMerchantId > 0L;
            boolean licenseAuditImagesJsonStrIsLegal = com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(imageParams);
            log.info("添加店员资质审核信息，参数校验结果，targetMerchantIdStrIsLegal：{}，licenseAuditImagesJsonStrIsLegal：{}，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，licenseAuditImagesJsonStr：{}",
                    targetMerchantIdStrIsLegal, licenseAuditImagesJsonStrIsLegal, accountId, merchantId, targetMerchantIdStr, licenseAuditImagesJsonStr);
            if (!targetMerchantIdStrIsLegal) {
                return AjaxResult.errResult("请选择店铺");
            }
            if (!licenseAuditImagesJsonStrIsLegal) {
                return AjaxResult.errResult("请上传图片");
            }
            MerchantAddClerkLicenseAuditParam param = MerchantAddClerkLicenseAuditParam.builder()
                    .merchantId(targetMerchantId).accountId(accountId).imageParams(imageParams).build();
            merchantService.addClerkLicenseAudit(param);
            log.info("添加店员资质审核信息成功，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，licenseAuditImagesJsonStr：{}",
                    accountId, merchantId, targetMerchantIdStr, licenseAuditImagesJsonStr);
            return AjaxResult.successResult("添加店员资质审核信息成功");
        } catch (AppException e) {
            if (e.isWarn()) {
                log.error("添加店员资质审核信息失败，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，licenseAuditImagesJsonStr：{}，msg：{}，异常信息：",
                        accountId, merchantId, targetMerchantIdStr, licenseAuditImagesJsonStr, e.getMsg(), e);
            }
            return AjaxResult.errResult(e.getMsg());
        } catch (Exception e) {
            log.error("添加店员资质审核信息失败，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，licenseAuditImagesJsonStr：{}，异常信息：",
                    accountId, merchantId, targetMerchantIdStr, licenseAuditImagesJsonStr, e);
            return AjaxResult.errResult(Constants.MSG_ERROR);
        }
    }

    @Override
    public AjaxResult<Integer> getLicenseStatus(Long merchantId) {
        try {
            return AjaxResult.successResult(merchantService.getMerchant(merchantId).getLicenseStatus());
        } catch (Exception e) {
            log.error("MerchantCenterNew getLicenseStatus error",e);
            return AjaxResult.errResult(e.getMessage());
        }

    }

    @Override
    public AjaxResult<Object> addMerchant(AddMerchantParamVO vo) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return AjaxResult.errResult("未登录，请重新登录！");
            }
            Long accountId = merchant.getAccountId();
            MerchantBussinessDto merchantBussinessDto = new MerchantBussinessDto();
            BeanUtils.copyProperties(vo, merchantBussinessDto);
            merchantBussinessDto.setAccountId(accountId);
            merchantBussinessDto.setRealName(vo.getName());
            merchantBussinessDto.setNickname(vo.getName());
            merchantBussinessDto.setCode(vo.getLicenseNo());
            try {
                TycBasicDataDto tycBasicDataDto = JSON.parseObject(vo.getTycBasicDataDto(), TycBasicDataDto.class);
                if (tycBasicDataDto != null && tycBasicDataDto.getSuccessFlag()!=null && tycBasicDataDto.getSuccessFlag() == 1 && tycBasicDataDto.getBaseRegStatus() != null && tycBasicDataDto.getBaseRegStatus() == 2) {
                    return AjaxResult.errResult("工商系统查询门店经营状态已注销，请核实药店名称是否正确，如有问题可联系专属销售");
                }
            } catch (Exception e) {
                logger.error("解析天眼查数据失败", e);
            }
            merchantBussinessDto.setTycBasicDataDto(vo.getTycBasicDataDto());
            //需要自动审核判断
            merchantBussinessDto.setAutoProcessSwitch(true);

            ApiRPCResult<AddMerchantResultDto> res = merchantBussinessApi.addMerchant(merchantBussinessDto);
            logger.info("addMerchant req:{}, resp:{}", JsonUtil.toJson(merchantBussinessDto), JsonUtil.toJson(res));
            if (!res.isSuccess()) {
                return AjaxResult.errResult(res.getErrMsg());
            }
            AddMerchantResultDto addMerchantResultDto = res.getData();
            // 上报店铺注册事件到极光
            if (addMerchantResultDto.getMerchantId() != null) {
                try {
                    jgBuryingPointApi.profileSetLoginOrRegister(addMerchantResultDto.getMerchantId(), PlatformEnum.PC.getKey());
                } catch (Exception e) {
                    logger.error("注册埋点数据上报异常, merchantId -> {}", addMerchantResultDto.getMerchantId(), e);
                }
            }
            return AjaxResult.successResult(addMerchantResultDto);
        } catch (Exception e) {
            logger.error("添加店铺失败,accountId:{}", vo.getAccountId(), e);
            return AjaxResult.errResult("添加店铺失败");
        }
    }

    @Override
    public AjaxResult<Object> getBusinessInfo(HttpServletRequest request, AddMerchantParamVO vo) {
        try {
            if (Objects.isNull(vo)) {
                return AjaxResult.errResult("获取工商信息入参不能为空！");
            }
            if (!org.springframework.util.StringUtils.isEmpty(vo.getLicenseNo()) && RegexUtil.chineseChar(vo.getLicenseNo())) {
                //营业执照号,医疗机构执业许可证编号
                return AjaxResult.errResult("营业执照号或医疗执业许可证编码格式不正确！");
            }
            String accountIdStr = request.getHeader("accountId");
            if (org.springframework.util.StringUtils.isEmpty(accountIdStr)) {
                return AjaxResult.errResult("accountId不能为空");
            }
            Long accountId = Long.valueOf(accountIdStr);
            MerchantBussinessDto merchantBussinessDto = new MerchantBussinessDto();
            BeanUtils.copyProperties(vo, merchantBussinessDto);
            merchantBussinessDto.setAccountId(accountId);
            merchantBussinessDto.setRealName(vo.getName());
            merchantBussinessDto.setNickname(vo.getName());
            ApiRPCResult<TycBasicDataDto> res = merchantBussinessApi.getTycBasicBusinessInfo(merchantBussinessDto);
            if (!res.isSuccess()) {
                return AjaxResult.errResult(res.getErrMsg());
            }
            TycBasicDataDto data = res.getData();
            return AjaxResult.successResult(data);
        } catch (Exception e) {
            logger.error("查询天眼查工商信息,AddMerchantParamVO:{}", JSON.toJSONString(vo), e);
            return AjaxResult.errResult("查询工商信息失败");
        }
    }

    @Override
    public AjaxResult<Object> getMerchantStatus(HttpServletRequest request, MerchantStatusSearchParamVO vo) {
        if (Objects.isNull(vo)) {
            AjaxResult.errResult("获取店铺状态信息入参不能为空！");
        }
        ApiRPCResult<Integer> res = merchantBussinessApi.getMerchantStatus(vo.getMerchantId());
        if (!res.isSuccess()) {
            return AjaxResult.errResult(res.getErrMsg());
        }
        Integer auditStatus = res.getData();
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("auditStatus", auditStatus);
        return AjaxResult.successResult(dataMap);
    }

    public List<MyOrderBusinessDto> getFilteredOrders(List<MyOrderBusinessDto> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }

        // 1. 过滤出待支付且未过期的订单
        List<MyOrderBusinessDto> orderToPayList = orderList.stream()
                .filter(x -> x.getStatus() == OrderEnum.OrderStatus.WAITBUYERPAY.getId())
                .filter(x -> x.getPayExpireTime() != null && x.getPayExpireTime().toInstant().isAfter(Instant.now()))
                .collect(Collectors.toList());

        List<MyOrderBusinessDto> finalOrderList;

        //所需返回订单数
        int orderSize = 3;
        //进行过滤和筛选
        if (orderToPayList.size() >= orderSize) {
            // 2. 如果满足条件的订单足够，直接取前 orderSize 条
            finalOrderList = orderToPayList.stream()
                    .limit(orderSize)
                    .collect(Collectors.toList());
        } else {
            // 3. 如果不足，先加入所有符合条件的待支付订单
            finalOrderList = new ArrayList<>(orderToPayList);

            // 4. 补充待配送的订单
            int additionalOrderCount =  orderSize - orderToPayList.size();
            List<MyOrderBusinessDto> additionalOrders = orderList.stream()
                    .filter(x -> x.getStatus() == OrderEnum.OrderStatus.SHIPPING.getId())
                    .limit(additionalOrderCount)
                    .collect(Collectors.toList());


            finalOrderList.addAll(additionalOrders);
        }
        //判断有没有订单列表
        if (!CollectionUtils.isEmpty(finalOrderList)){
            //请求别的接口获取地址参数
            List<String> orderNoList = finalOrderList.stream().map(MyOrderBusinessDto::getOrderNo).collect(Collectors.toList());
            List<OrderBusinessDto> orderDtoList = orderBusinessApi. selectByOrderNoListWithExtendInfoFromMaster(orderNoList);
            Map<String, String> addressMap = new HashMap<>(finalOrderList.size());
            if (!CollectionUtils.isEmpty(orderDtoList)) {
                for (OrderBusinessDto dto : orderDtoList) {
                    addressMap.put(dto.getOrderNo(), dto.getAddress());
                }
            }

            //VO加入商品图片与收货地址
            String productImageUrl = config.getProductImagePathUrl() + "/ybm/product/min/";
            if (!CollectionUtils.isEmpty(finalOrderList)){
                finalOrderList.forEach(orderVo -> {
                    String imagesUrl = orderVo.getImageUrl();
                    if (StringUtils.isNotBlank(imagesUrl)) {
                        orderVo.setImageUrl(productImageUrl + imagesUrl);
                        orderVo.setAddress(addressMap.getOrDefault(orderVo.getOrderNo(), null));
                    }
                });
            }
            return finalOrderList;
        }
      //没有返回空列表
        return Collections.emptyList();

    }


    @Override
    public AjaxResult<Object> getSimpleMerchantInfo(Long merchantId) {
        if(merchantId == null){
            return AjaxResult.errResult("merchantId不能为空");
        }
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if(merchant == null){
                return AjaxResult.errResult("未登录，请重新登录！");
            }
            Long accountId = merchant.getAccountId();
            ApiRPCResult<SimpleMerchantDto> rpcResult = merchantApi.findMerchantInfo(accountId, merchantId);
            if(!rpcResult.isSuccess()){
                return AjaxResult.errResult(rpcResult.getErrMsg());
            }
            return AjaxResult.successResult(rpcResult.getData());
        } catch (Exception e) {
            logger.error("店铺信息查询失败",  e);
            return AjaxResult.errResult("店铺信息查询失败");
        }
    }

}
