package com.xyy.ec.pc.cms.service.complement.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.layout.buinese.ecp.api.ProductExhibitionGroupBusinessApi;
import com.xyy.ec.layout.buinese.ecp.enums.CmsSelectProductStrategyTypeEnum;
import com.xyy.ec.layout.buinese.ecp.enums.marketing.MarketingSeckillActivityStatusEnum;
import com.xyy.ec.layout.buinese.ecp.params.ExhibitionProductQueryParam;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingQueryStatusEnum;
import com.xyy.ec.pc.cms.constants.CmsConstants;
import com.xyy.ec.pc.cms.helpers.CmsListProductVOHelper;
import com.xyy.ec.pc.cms.helpers.CmsSeckillProductExpectQueryParamHelper;
import com.xyy.ec.pc.cms.param.CmsSeckillProductExpectQueryParam;
import com.xyy.ec.pc.cms.service.CmsSkuService;
import com.xyy.ec.pc.cms.service.complement.CmsSeckillProductListComplementQueryCsuIdInterf;
import com.xyy.ec.pc.cms.service.complement.ComplementQueryService;
import com.xyy.ec.pc.cms.service.complement.dto.CmsSeckillProductListComplementContext;
import com.xyy.ec.pc.cms.service.complement.params.CmsSeckillProductIdPagingQueryParam;
import com.xyy.ec.pc.cms.service.complement.params.CmsSeckillProductListComplementQueryParam;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.remote.ProductExhibitionGroupBusinessAdminRemoteService;
import com.xyy.ec.pc.remote.ProductForLayoutRemoteService;
import com.xyy.ec.pc.remote.ShopQueryRemoteService;
import com.xyy.ec.pc.rpc.ProductServiceRpc;
import com.xyy.ec.pc.search.service.DataService;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import com.xyy.ec.search.engine.api.EcSearchApi;
import com.xyy.ec.search.engine.dto.SearchCsuDTO;
import com.xyy.ec.search.engine.enums.CsuOrder;
import com.xyy.ec.search.engine.enums.EcRankType;
import com.xyy.ec.search.engine.enums.SortOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ComplementQueryServiceImpl implements ComplementQueryService {

    @Reference(version = "1.0.0")
    private EcSearchApi searchApi;

    @Reference(version = "1.0.0")
    private ProductExhibitionGroupBusinessApi productExhibitionGroupBusinessApi;

    @Autowired
    private ProductForLayoutRemoteService productForLayoutRemoteService;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private ShopQueryRemoteService shopQueryRemoteService;

    @Autowired
    private ProductExhibitionGroupBusinessAdminRemoteService productExhibitionGroupBusinessAdminRemoteService;

    @Autowired
    private DataService dataService;

    @Autowired
    private CmsSkuService cmsSkuService;

    @Autowired
    private ProductServiceRpc productServiceRpc;

    @Override
    public List<CmsListProductVO> listExpectCmsSeckillProducts(CmsSeckillProductExpectQueryParam expectQueryParam) {
        // 会员未登录情况统一转为null。
        if (expectQueryParam != null && expectQueryParam.getMerchantId() != null && expectQueryParam.getMerchantId() <= 0L) {
            expectQueryParam.setMerchantId(null);
        }
        // 参数验证
        Boolean validate = CmsSeckillProductExpectQueryParamHelper.validate(expectQueryParam);
        if (!BooleanUtils.isTrue(validate)) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "参数非法");
        }
        // 入参
        Long merchantId = expectQueryParam.getMerchantId();
        String productBranchCode = expectQueryParam.getProductBranchCode();
        Boolean merchantIsNotWatchFragileGoods = expectQueryParam.getMerchantIsNotWatchFragileGoods();
        String selectProductStrategyType = expectQueryParam.getSelectProductStrategyType();
        List<Long> specifiedCsuIdList = expectQueryParam.getSpecifiedCsuIds();
        String specifiedExhibitionIdStr = expectQueryParam.getSpecifiedExhibitionIdStr();
        int expectedNum = expectQueryParam.getExpectedNum();
        CmsSelectProductStrategyTypeEnum selectProductStrategyTypeEnum = CmsSelectProductStrategyTypeEnum.valueOfCustom(selectProductStrategyType);
        List<CmsListProductVO> result = Lists.newArrayListWithExpectedSize(16);

        /* 上下文 */
        CmsSeckillProductListComplementContext complementContext = CmsSeckillProductListComplementContext.builder()
                .csuIdToSeckillActivityInfoMap(Maps.newHashMapWithExpectedSize(16))
                .csuIdToProductInfoMap(Maps.newHashMapWithExpectedSize(16))
                .build();

        // 指定商品/商品组的商品ID列表
        List<Long> csuIds = Lists.newArrayListWithExpectedSize(16);
        if (Objects.equals(CmsSelectProductStrategyTypeEnum.APPOINT_PRODUCT, selectProductStrategyTypeEnum)) {
            // 指定商品
            if (CollectionUtils.isNotEmpty(specifiedCsuIdList)) {
                csuIds.addAll(specifiedCsuIdList);
            }
        } else if (Objects.equals(CmsSelectProductStrategyTypeEnum.APPOINT_PRODUCT_GROUP, selectProductStrategyTypeEnum)) {
            // 指定商品组
            ExhibitionProductQueryParam exhibitionProductQueryParam = ExhibitionProductQueryParam.builder()
                    .merchantId(merchantId).branchCode(productBranchCode).exhibitionIdStr(specifiedExhibitionIdStr).build();
            ApiRPCResult<List<Long>> pageInfoApiRPCResult = productExhibitionGroupBusinessApi.pagingUsingExhibitionProductIdsWithoutControlAndPageInfo(
                    exhibitionProductQueryParam, 1, expectedNum * 3);
            if (Objects.nonNull(pageInfoApiRPCResult) && pageInfoApiRPCResult.isSuccess()) {
                List<Long> tempCsuIds = pageInfoApiRPCResult.getData();
                if (CollectionUtils.isNotEmpty(tempCsuIds)) {
                    csuIds.addAll(tempCsuIds);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(csuIds)) {
            // 指定商品
            // 指定商品ID补足查询
            CmsSeckillProductListComplementQueryParam complementQueryParam = CmsSeckillProductListComplementQueryParam.builder()
                    .merchantId(merchantId).productBranchCode(productBranchCode)
                    .merchantIsNotWatchFragileGoods(merchantIsNotWatchFragileGoods)
                    .activityStatusList(Lists.newArrayList(MarketingQueryStatusEnum.UN_START.getType(), MarketingQueryStatusEnum.STARTING.getType()))
                    .exhibitionIdStrList(null)
                    .specifiedCsuIds(csuIds)
                    .expectedNum(expectedNum)
                    .maxComplementNum(3)
                    .excludeCsuIdsSet(null)
                    // 指定商品策略时，源数据都是没有经过控销过滤的，需要进行控销过滤。
                    .isDoCsuControlFilter(true)
                    .build();
            List<CmsListProductVO> cmsSeckillProductInfos = this.complementQueryCmsSeckillProducts(complementContext,
                    complementQueryParam, (s) -> {
                        Integer pageNum = s.getPageNum();
                        Integer pageSize = s.getPageSize();
                        Long csuIdsTotal = s.getCsuIdsTotal();
                        List<List<Long>> csuIdsLists = s.getCsuIdsLists();
                        PageInfo<Long> pageInfo = PageInfo.of(Lists.newArrayList());
                        pageInfo.setPageNum(pageNum);
                        pageInfo.setPageSize(pageSize);
                        pageInfo.setPages(0);
                        pageInfo.setTotal(0L);
                        if (CollectionUtils.isEmpty(csuIdsLists)) {
                            return pageInfo;
                        }
                        int pages = csuIdsLists.size();
                        pageInfo.setPages(pages);
                        pageInfo.setTotal(csuIdsTotal);
                        if (pageNum > pages) {
                            return pageInfo;
                        }
                        pageInfo.setList(csuIdsLists.get(pageNum - 1));
                        return pageInfo;
                    });
            if (log.isDebugEnabled()) {
                log.debug("补页查询指定商品的参加秒杀活动的商品列表（指定商品策略），入参：{}，出参：{}", JSONObject.toJSONString(complementQueryParam),
                        JSONArray.toJSONString(cmsSeckillProductInfos));
            }
            List<CmsListProductVO> joiningStartingSeckillActivityProducts = Lists.newArrayListWithExpectedSize(16);
            List<CmsListProductVO> joiningPreheatingSeckillActivityProducts = Lists.newArrayListWithExpectedSize(16);
            for (CmsListProductVO cmsSeckillProductInfo : cmsSeckillProductInfos) {
                MarketingSeckillActivityInfoDTO actSk = cmsSeckillProductInfo.getActSk();
                Integer status = actSk.getStatus();
                if (Objects.equals(status, MarketingSeckillActivityStatusEnum.IN_PROGRESS.getStatus())) {
                    joiningStartingSeckillActivityProducts.add(cmsSeckillProductInfo);
                } else if (Objects.equals(status, MarketingSeckillActivityStatusEnum.NOT_STARTED.getStatus())) {
                    joiningPreheatingSeckillActivityProducts.add(cmsSeckillProductInfo);
                }
            }
            // 先放入进行中后放入预热中
            for (CmsListProductVO joiningStartingSeckillActivityProduct : joiningStartingSeckillActivityProducts) {
                if (result.size() >= expectedNum) {
                    break;
                }
                result.add(joiningStartingSeckillActivityProduct);
            }
            for (CmsListProductVO joiningPreheatingSeckillActivityProduct : joiningPreheatingSeckillActivityProducts) {
                if (result.size() >= expectedNum) {
                    break;
                }
                result.add(joiningPreheatingSeckillActivityProduct);
            }
        }
        // 兜底，系统自动补足
        int currentRowSize = result.size();
        if (currentRowSize < expectedNum) {
            // 补足数量
            int complementNum = expectedNum - currentRowSize;
            // 排除当前的商品ID
            Set<Long> excludeCsuIdsSet = result.stream().map(CmsListProductVO::getId).collect(Collectors.toSet());
            // 系统自动
            // 查询进行中
            CmsSeckillProductListComplementQueryParam complementQueryParam = CmsSeckillProductListComplementQueryParam.builder()
                    .merchantId(merchantId).productBranchCode(productBranchCode)
                    .merchantIsNotWatchFragileGoods(merchantIsNotWatchFragileGoods)
                    .activityStatusList(Lists.newArrayList(MarketingQueryStatusEnum.STARTING.getType()))
                    .exhibitionIdStrList(Lists.newArrayList(CmsConstants.EXHIBITION_ID_STR_SECKILL_IN_PROGRESS))
                    .specifiedCsuIds(null)
                    .expectedNum(complementNum)
                    .maxComplementNum(3)
                    .excludeCsuIdsSet(excludeCsuIdsSet)
                    // 依赖搜索接口的召回，召回逻辑内已含有控销逻辑。
                    .isDoCsuControlFilter(false)
                    .build();
            List<CmsListProductVO> cmsSeckillProductInfos = this.complementQueryCmsSeckillProducts(complementContext,
                    complementQueryParam, (s) -> this.pagingSomeTagsSeckillProductIds(s.getMerchantId(),
                            s.getMerchantIsNotWatchFragileGoods(), s.getExhibitionIdStrList(),
                            s.getPageNum(), s.getPageSize()));
            if (log.isDebugEnabled()) {
                log.debug("补页查询参加进行中秒杀活动的商品列表（系统自动策略），入参：{}，出参：{}", JSONObject.toJSONString(complementQueryParam),
                        JSONArray.toJSONString(cmsSeckillProductInfos));
            }
            for (CmsListProductVO cmsSeckillProductInfo : cmsSeckillProductInfos) {
                if (result.size() >= expectedNum) {
                    break;
                }
                result.add(cmsSeckillProductInfo);
            }
            // 进行中不足，则取预热中
            currentRowSize = result.size();
            if (currentRowSize < expectedNum) {
                // 补足数量
                complementNum = expectedNum - currentRowSize;
                // 排除当前的商品ID
                excludeCsuIdsSet = result.stream().map(CmsListProductVO::getId).collect(Collectors.toSet());
                // 查询预热中
                complementQueryParam = CmsSeckillProductListComplementQueryParam.builder()
                        .merchantId(merchantId).productBranchCode(productBranchCode)
                        .merchantIsNotWatchFragileGoods(merchantIsNotWatchFragileGoods)
                        .activityStatusList(Lists.newArrayList(MarketingQueryStatusEnum.UN_START.getType()))
                        .exhibitionIdStrList(Lists.newArrayList(CmsConstants.EXHIBITION_ID_STR_SECKILL_NOT_START))
                        .specifiedCsuIds(null)
                        .expectedNum(complementNum)
                        .maxComplementNum(3)
                        .excludeCsuIdsSet(excludeCsuIdsSet)
                        // 依赖搜索接口的召回，召回逻辑内已含有控销逻辑。
                        .isDoCsuControlFilter(false)
                        .build();
                cmsSeckillProductInfos = this.complementQueryCmsSeckillProducts(complementContext,
                        complementQueryParam, (s) -> this.pagingSomeTagsSeckillProductIds(s.getMerchantId(),
                                s.getMerchantIsNotWatchFragileGoods(), s.getExhibitionIdStrList(),
                                s.getPageNum(), s.getPageSize()));
                if (log.isDebugEnabled()) {
                    log.debug("补页查询参加预热中秒杀活动的商品列表（系统自动策略），入参：{}，出参：{}", JSONObject.toJSONString(complementQueryParam),
                            JSONArray.toJSONString(cmsSeckillProductInfos));
                }
                for (CmsListProductVO cmsSeckillProductInfo : cmsSeckillProductInfos) {
                    if (result.size() >= expectedNum) {
                        break;
                    }
                    result.add(cmsSeckillProductInfo);
                }
            }
        }
        // 填充店铺信息
        result = cmsSkuService.fillListProductsShopInfo(result);
        // 填充标签信息
        result = cmsSkuService.fillListProductsTagInfo(merchantId, result, true);
        //填充库存信息
        result = productServiceRpc.fillCmsActTotalSurplusQtyToAvailableQty(result);
        return result;
    }

    /**
     * <pre>
     * 补页查询秒杀商品流。
     *
     * 根据查询的活动状态来决定补页策略：
     * 1.仅进行中：
     *  isContainQueryPreheating：false；
     *  isContainQueryStarting：true；
     *  isOnlyQueryPreheating：false；
     *  策略：先补页向products中放入进行中的，直到达到期望数量。
     *
     * 2.仅预热中：
     *  isContainQueryPreheating：true；
     *  isContainQueryStarting：false；
     *  isOnlyQueryPreheating：true；
     *  策略：补页向products中放入预热中的，直到达到期望数量。
     *
     * 3.进行中+预热中：
     *  isContainQueryPreheating：true；
     *  isContainQueryStarting：true；
     *  isOnlyQueryPreheating：false；
     *  策略：先补页向products中放入进行中的，后从joiningPreheatingSeckillActivityProducts中补足放入预热中的，直到达到期望数量。
     *
     * </pre>
     *
     * @param complementContext
     * @param queryParam
     * @param queryCsuIdInterf
     * @return </pre>
     */
    private List<CmsListProductVO> complementQueryCmsSeckillProducts(CmsSeckillProductListComplementContext complementContext,
                                                                     CmsSeckillProductListComplementQueryParam queryParam,
                                                                     CmsSeckillProductListComplementQueryCsuIdInterf queryCsuIdInterf) {
        /* 查询参数 */
        Long merchantId = queryParam.getMerchantId();
        String productBranchCode = queryParam.getProductBranchCode();
        Boolean merchantIsNotWatchFragileGoods = queryParam.getMerchantIsNotWatchFragileGoods();
        List<String> exhibitionIdStrList = queryParam.getExhibitionIdStrList();
        List<Integer> activityStatusList = queryParam.getActivityStatusList();
        List<Long> specifiedCsuIds = queryParam.getSpecifiedCsuIds();
        boolean isSpecifiedCsuIds = CollectionUtils.isNotEmpty(specifiedCsuIds);
        int expectedNum = queryParam.getExpectedNum();
        int maxComplementNum = queryParam.getMaxComplementNum();
        Set<Long> excludeCsuIdsSet = queryParam.getExcludeCsuIdsSet();
        Boolean isDoCsuControlFilter = queryParam.getIsDoCsuControlFilter();
        List<List<Long>> csuIdsLists = isSpecifiedCsuIds ? Lists.partition(specifiedCsuIds, expectedNum * 3) : null;
        Long csuIdsTotal = isSpecifiedCsuIds ? (long) specifiedCsuIds.size() : 0L;
        Set<Integer> activityStatusSet = Sets.newHashSet(activityStatusList);
        boolean isContainQueryPreheating = activityStatusSet.contains(MarketingQueryStatusEnum.UN_START.getType());
        boolean isContainQueryStarting = activityStatusSet.contains(MarketingQueryStatusEnum.STARTING.getType());
        boolean isOnlyQueryPreheating = isContainQueryPreheating && !isContainQueryStarting;

        /* 上下文 */
        // 商品ID - 秒杀活动信息 Map
        Map<Long, MarketingSeckillActivityInfoDTO> csuIdToSeckillActivityInfoMap = complementContext.getCsuIdToSeckillActivityInfoMap();
        // 商品ID - 商品信息 Map
        Map<Long, ListProduct> csuIdToProductInfoMap = complementContext.getCsuIdToProductInfoMap();

        // 结果
        List<CmsListProductVO> result = Lists.newArrayListWithExpectedSize(16);
        // 参加预热中秒杀活动的商品列表
        List<CmsListProductVO> joiningPreheatingSeckillActivityProducts = Lists.newArrayListWithExpectedSize(16);
        {
            // 补页逻辑
            int pageSize = expectedNum * 3;
            int currentPageNum = 1;
            if (pageSize <= 10) {
                pageSize = 10;
            }
            int num = 0;
            c1:
            while (true) {
                // 当前页，补页数非0时递增当前页码
                if (num != 0) {
                    currentPageNum++;
                }
                {
                    if (num > maxComplementNum) {
                        break c1;
                    }
                    num++;
                }
                {
                    if (result.size() >= expectedNum) {
                        break c1;
                    }
                }
                /* 查询当前页的商品ID列表 */
                // 充查询商品ID参数
                CmsSeckillProductIdPagingQueryParam pagingQueryParam = CmsSeckillProductIdPagingQueryParam.builder()
                        .merchantId(merchantId).productBranchCode(productBranchCode)
                        .merchantIsNotWatchFragileGoods(merchantIsNotWatchFragileGoods)
                        .exhibitionIdStrList(exhibitionIdStrList).csuIdsLists(csuIdsLists).csuIdsTotal(csuIdsTotal)
                        .pageNum(currentPageNum).pageSize(pageSize)
                        .build();
                PageInfo<Long> csuIdsListPageInfo = queryCsuIdInterf.pagingSeckillProductIds(pagingQueryParam);
                if (log.isDebugEnabled()) {
                    log.debug("补页查询秒杀商品流-查询当前页商品ID，入参：{}，出参：{}",
                            JSONObject.toJSONString(pagingQueryParam), JSONObject.toJSONString(csuIdsListPageInfo));
                }
                if (Objects.isNull(csuIdsListPageInfo)) {
                    break c1;
                }
                List<Long> csuIdsList = csuIdsListPageInfo.getList();
                int pages = csuIdsListPageInfo.getPages();
                if (CollectionUtils.isEmpty(csuIdsList)) {
                    if (currentPageNum >= pages) {
                        // 最后一页跳出翻页
                        break c1;
                    } else {
                        // 继续翻页
                        continue c1;
                    }
                }
                /* 过滤掉需要排除的商品ID */
                if (CollectionUtils.isNotEmpty(excludeCsuIdsSet)) {
                    csuIdsList = csuIdsList.stream().filter(item -> !excludeCsuIdsSet.contains(item)).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(csuIdsList)) {
                        if (currentPageNum >= pages) {
                            // 最后一页跳出翻页
                            break c1;
                        } else {
                            // 继续翻页
                            continue c1;
                        }
                    }
                }
                if (BooleanUtils.isTrue(isDoCsuControlFilter)) {
                    /* 过滤商品控销 */
                    csuIdsList = productForLayoutRemoteService.controlFilterCsuIdsForIsVisible(merchantId, productBranchCode, csuIdsList);
                    if (CollectionUtils.isEmpty(csuIdsList)) {
                        if (currentPageNum >= pages) {
                            // 最后一页跳出翻页
                            break c1;
                        } else {
                            // 继续翻页
                            continue c1;
                        }
                    }
                }

                /* 查询营销活动，商品ID - 秒杀活动信息 */
                Map<Long, MarketingSeckillActivityInfoDTO> tempCsuIdToSeckillActivityInfoMap = this.batchGetSeckillActivityInfosByCsuIds(
                        merchantId, csuIdsList, activityStatusList);
                if (log.isDebugEnabled()) {
                    log.debug("补页查询秒杀商品流-查询参加的秒杀活动信息，入参：merchantId：{}，商品ID列表：{}，活动状态：{}，出参：{}",
                            merchantId, JSONArray.toJSONString(csuIdsList), JSONArray.toJSONString(activityStatusList),
                            JSONObject.toJSONString(tempCsuIdToSeckillActivityInfoMap));
                }
                csuIdToSeckillActivityInfoMap.putAll(tempCsuIdToSeckillActivityInfoMap);
                /* 筛出参加秒杀活动的商品ID */
                csuIdsList = csuIdsList.stream().filter(item -> Objects.nonNull(tempCsuIdToSeckillActivityInfoMap.get(item)))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(csuIdsList)) {
                    if (currentPageNum >= pages) {
                        // 最后一页跳出翻页
                        break c1;
                    } else {
                        // 继续翻页
                        continue c1;
                    }
                }
                /* 分组填充商品信息 */
                List<List<Long>> joiningSeckillActivityCsuIdsLists = Lists.partition(csuIdsList, expectedNum * 2);
                c2:
                for (List<Long> joiningSeckillActivityCsuIdsList : joiningSeckillActivityCsuIdsLists) {
                    {
                        if (result.size() >= expectedNum) {
                            break c1;
                        }
                    }
                    Map<Long, ListProduct> tempCsuIdToProductInfoMap = this.batchGetCsuInfosByCsuIds(merchantId, productBranchCode,
                            merchantIsNotWatchFragileGoods, joiningSeckillActivityCsuIdsList);
                    if (log.isDebugEnabled()) {
                        log.debug("补页查询秒杀商品流-查询商品信息，入参：merchantId：{}，isNotWatchFragileGoods：{}，productBranchCode：{}，商品ID列表：{}，出参：{}",
                                merchantId, merchantIsNotWatchFragileGoods, productBranchCode, JSONArray.toJSONString(joiningSeckillActivityCsuIdsList),
                                JSONObject.toJSONString(tempCsuIdToProductInfoMap));
                    }
                    csuIdToProductInfoMap.putAll(tempCsuIdToProductInfoMap);
                    List<ListProduct> products = Lists.newArrayListWithExpectedSize(16);
                    c4:
                    for (Long joiningSeckillActivityCsuId : joiningSeckillActivityCsuIdsList) {
                        ListProduct product = tempCsuIdToProductInfoMap.get(joiningSeckillActivityCsuId);
                        if (Objects.isNull(product)) {
                            continue c4;
                        }
                        // 只留下销售中的商品
                        if (!Objects.equals(product.getStatus(), 1)) {
                            continue c4;
                        }
                        MarketingSeckillActivityInfoDTO seckillActivityInfoDTO = csuIdToSeckillActivityInfoMap.get(joiningSeckillActivityCsuId);
                        if (Objects.isNull(seckillActivityInfoDTO)) {
                            continue c4;
                        }
                        if ((isContainQueryStarting && Objects.equals(seckillActivityInfoDTO.getStatus(), MarketingSeckillActivityStatusEnum.IN_PROGRESS.getStatus()))
                                || (isOnlyQueryPreheating && Objects.equals(seckillActivityInfoDTO.getStatus(), MarketingSeckillActivityStatusEnum.NOT_STARTED.getStatus()))) {
                            // （含查询进行中的且当前为进行中活动） 或 （仅查预热的且当前为预热中活动）
                            products.add(product);
                        } else if (isContainQueryPreheating && !isOnlyQueryPreheating
                                && Objects.equals(seckillActivityInfoDTO.getStatus(), MarketingSeckillActivityStatusEnum.NOT_STARTED.getStatus())) {
                            // 含查询预热中的且不仅仅只查询预热中的（即含查询预热和开始）且当前为预热中活动，暂存，后放
                            CmsListProductVO cmsListProductVO = CmsListProductVOHelper.create(product, seckillActivityInfoDTO);
                            if (Objects.nonNull(cmsListProductVO)) {
                                joiningPreheatingSeckillActivityProducts.add(cmsListProductVO);
                            }
                        }
                    }
                    c3:
                    for (ListProduct product : products) {
                        {
                            if (result.size() >= expectedNum) {
                                break c1;
                            }
                        }
                        Long productId = product.getId();
                        MarketingSeckillActivityInfoDTO seckillActivityInfoDTO = csuIdToSeckillActivityInfoMap.get(productId);
                        CmsListProductVO cmsListProductVO = CmsListProductVOHelper.create(product, seckillActivityInfoDTO);
                        if (Objects.isNull(cmsListProductVO)) {
                            continue c3;
                        }
                        result.add(cmsListProductVO);
                    }
                }
                if (currentPageNum >= pages) {
                    // 最后一页跳出翻页
                    break c1;
                } else {
                    // 继续翻页
                    continue c1;
                }
            }
            int currentRowSize = result.size();
            if (isContainQueryPreheating && !isOnlyQueryPreheating && currentRowSize < expectedNum
                    && CollectionUtils.isNotEmpty(joiningPreheatingSeckillActivityProducts)) {
                // 含查询预热中的且不仅仅只查询预热中的（即含查询预热和开始），后放入参加预热中的秒杀活动的商品
                c1:
                for (CmsListProductVO joiningPreheatingSeckillActivityProduct : joiningPreheatingSeckillActivityProducts) {
                    {
                        if (result.size() >= expectedNum) {
                            break c1;
                        }
                    }
                    result.add(joiningPreheatingSeckillActivityProduct);
                }
            }
        }
        return result;
    }

    /**
     * 分页获取某个标签的参加秒杀活动的商品ID列表
     *
     * @param merchantId
     * @param merchantIsNotWatchFragileGoods
     * @param exhibitionIdStrList
     * @param pageNum
     * @param pageSize
     * @return
     */
    private PageInfo<Long> pagingSomeTagsSeckillProductIds(Long merchantId, Boolean merchantIsNotWatchFragileGoods,
                                                           List<String> exhibitionIdStrList, int pageNum, int pageSize) {
        PageInfo<Long> pageInfo = PageInfo.of(Lists.newArrayList());
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages(0);
        pageInfo.setTotal(0L);
        if (Objects.isNull(merchantId) || merchantId <= 0L || CollectionUtils.isEmpty(exhibitionIdStrList)) {
            return pageInfo;
        }
        String tagList = String.join(",", exhibitionIdStrList);
        Integer isShowFragileGoods;
        if (BooleanUtils.isTrue(merchantIsNotWatchFragileGoods)) {
            isShowFragileGoods = 0;
        } else {
            isShowFragileGoods = 1;
        }
        SearchCsuDTO searchCsuDTO = new SearchCsuDTO();
        searchCsuDTO.setMerchantId(merchantId);
        searchCsuDTO.setIsShowFragileGoods(isShowFragileGoods);
        searchCsuDTO.setTagList(tagList);
        CsuOrder csuOrder = CsuOrder.SK_HOT;
        searchCsuDTO.setRankType(EcRankType.EC_SK_RANK.getValue());
        searchCsuDTO.setCsuOrder(csuOrder);
        searchCsuDTO.setSortOrder(SortOrder.DESC);
        com.xyy.ec.search.engine.pagination.Page pageParam = new com.xyy.ec.search.engine.pagination.Page(pageNum, pageSize);
        try {
            ApiRPCResult<com.xyy.ec.search.engine.pagination.Page<Long>> apiRPCResult = searchApi.searchCsuIdListForApp(pageParam, searchCsuDTO);
            if (log.isDebugEnabled()) {
                log.debug("分页获取某个标签的参加秒杀活动的商品ID列表，入参：merchantId：{}，查询参数：{}，分页参数：{}，出参：{}",
                        merchantId, JSONObject.toJSONString(searchCsuDTO), JSONObject.toJSONString(pageParam),
                        JSONObject.toJSONString(apiRPCResult));
            }
            // 商品组探活埋点
            productExhibitionGroupBusinessAdminRemoteService.asyncSendExhibitionLiveEventMQForSearch(searchCsuDTO);
            if (Objects.isNull(apiRPCResult) || !apiRPCResult.isSuccess()) {
                log.error("分页获取某个标签的参加秒杀活动的商品ID列表，入参：merchantId：{}，查询参数：{}，分页参数：{}，出参：{}",
                        merchantId, JSONObject.toJSONString(searchCsuDTO), JSONObject.toJSONString(pageParam),
                        JSONObject.toJSONString(apiRPCResult));
                return pageInfo;
            }
            com.xyy.ec.search.engine.pagination.Page<Long> skuIdPage = apiRPCResult.getData();
            if (Objects.isNull(skuIdPage)) {
                return pageInfo;
            }
            pageInfo.setPages((int) skuIdPage.getPages());
            pageInfo.setTotal(skuIdPage.getTotalCount());
            List<Long> skuIds = skuIdPage.getRecordList();
            if (CollectionUtils.isEmpty(skuIds)) {
                return pageInfo;
            }
            pageInfo.setList(skuIds);
            return pageInfo;
        } catch (Exception e) {
            log.error("分页获取某个标签的参加秒杀活动的商品ID列表异常，入参：merchantId：{}，查询参数：{}，分页参数：{}",
                    merchantId, JSONObject.toJSONString(searchCsuDTO), JSONObject.toJSONString(pageParam), e);
            return pageInfo;
        }
    }

    /**
     * <pre>
     * 批量根据商品id查询秒杀活动信息。
     * 按活动开始时间升序、活动ID降序排序。
     * 分页查询。
     * 优先级：进行中 > 未开始；
     * 若查询进行中的，则返回参加进行中活动的信息；若查询未开始的，则返回不参加进行中且参加未开始活动的信息。
     * </pre>
     *
     * @param csuIds
     * @param statusList 只接受{@linkplain MarketingQueryStatusEnum#UN_START 未开始} 和 {@linkplain MarketingQueryStatusEnum#STARTING 进行中}。
     * @param merchantId
     * @return
     */
    private Map<Long, MarketingSeckillActivityInfoDTO> batchGetSeckillActivityInfosByCsuIds(Long merchantId, List<Long> csuIds,
                                                                                            List<Integer> statusList) {
        if (Objects.isNull(merchantId) || merchantId <= 0L || CollectionUtils.isEmpty(csuIds) || CollectionUtils.isEmpty(statusList)) {
            return Maps.newHashMap();
        }
        Set<Integer> statusSet = Sets.newHashSet(statusList);
        statusSet.remove(null);
        if (CollectionUtils.isEmpty(statusSet)) {
            return Maps.newHashMap();
        }
        for (Integer status : statusSet) {
            if (!Objects.equals(status, MarketingQueryStatusEnum.UN_START.getType())
                    && !Objects.equals(status, MarketingQueryStatusEnum.STARTING.getType())) {
                return Maps.newHashMap();
            }
        }
        boolean isContainPreheating = statusSet.contains(MarketingQueryStatusEnum.UN_START.getType());
        // 不是进行中的商品ID列表
        List<Long> notStartingCsuIds = Lists.newArrayListWithExpectedSize(16);
        List<Long> tempNotStartingCsuIds;
        // 分组
        List<List<Long>> skuIdsLists = Lists.partition(csuIds, 20);
        // 进行中
        Map<Long, MarketingSeckillActivityInfoDTO> startingCsuIdToGroupBuyingInfoMap = Maps.newHashMapWithExpectedSize(csuIds.size());
        for (List<Long> skuIdsList : skuIdsLists) {
            Map<Long, MarketingSeckillActivityInfoDTO> tempStartingCsuIdToGroupBuyingInfoMap = marketingService.getShowingSeckillActivityInfoByCsuIds(merchantId,
                    skuIdsList, MarketingQueryStatusEnum.STARTING.getType());
            if (MapUtils.isNotEmpty(tempStartingCsuIdToGroupBuyingInfoMap)) {
                startingCsuIdToGroupBuyingInfoMap.putAll(tempStartingCsuIdToGroupBuyingInfoMap);
                if (isContainPreheating) {
                    // 包含预热，则筛出未参加进行中活动的商品ID。
                    tempNotStartingCsuIds = skuIdsList.stream()
                            .filter(item -> Objects.nonNull(item) && Objects.isNull(tempStartingCsuIdToGroupBuyingInfoMap.get(item)))
                            .collect(Collectors.toList());
                    notStartingCsuIds.addAll(tempNotStartingCsuIds);
                }
            } else {
                if (isContainPreheating) {
                    // 包含预热，则筛出未参加进行中活动的商品ID。
                    notStartingCsuIds.addAll(skuIdsList);
                }
            }
        }
        // 预热中且不是未开始
        Map<Long, MarketingSeckillActivityInfoDTO> preheatingCsuIdToGroupBuyingInfoMap = Maps.newHashMapWithExpectedSize(notStartingCsuIds.size());
        if (isContainPreheating && CollectionUtils.isNotEmpty(notStartingCsuIds)) {
            List<List<Long>> notStartingCsuIdsLists = Lists.partition(notStartingCsuIds, 20);
            Map<Long, MarketingSeckillActivityInfoDTO> tempPreheatingCsuIdToGroupBuyingInfoMap;
            for (List<Long> skuIdsList : notStartingCsuIdsLists) {
                tempPreheatingCsuIdToGroupBuyingInfoMap = marketingService.getShowingSeckillActivityInfoByCsuIds(merchantId,
                        skuIdsList, MarketingQueryStatusEnum.UN_START.getType());
                if (MapUtils.isNotEmpty(tempPreheatingCsuIdToGroupBuyingInfoMap)) {
                    preheatingCsuIdToGroupBuyingInfoMap.putAll(tempPreheatingCsuIdToGroupBuyingInfoMap);
                }
            }
        }
        // 返回结果
        Map<Long, MarketingSeckillActivityInfoDTO> result = Maps.newHashMapWithExpectedSize(csuIds.size());
        if (statusSet.contains(MarketingQueryStatusEnum.STARTING.getType())) {
            // 进行中
            result.putAll(startingCsuIdToGroupBuyingInfoMap);
        }
        if (statusSet.contains(MarketingQueryStatusEnum.UN_START.getType())) {
            // 预热中
            result.putAll(preheatingCsuIdToGroupBuyingInfoMap);
        }
        return result;
    }

    /**
     * 批量获取商品信息
     *
     * @param merchantId
     * @param productBranchCode
     * @param merchantIsNotWatchFragileGoods
     * @param csuIds
     * @return
     */
    private Map<Long, ListProduct> batchGetCsuInfosByCsuIds(Long merchantId, String productBranchCode,
                                                            Boolean merchantIsNotWatchFragileGoods, List<Long> csuIds) {
        if (CollectionUtils.isEmpty(csuIds) || (Objects.isNull(merchantId) && StringUtils.isEmpty(productBranchCode))) {
            return Maps.newHashMap();
        }
        // TODO 临时方案解决：属于POP店铺的商品，不执行用户易碎品限制逻辑
        List<ListProduct> products = productForLayoutRemoteService.fillProductInfoV2(csuIds, merchantId, false, productBranchCode, true, true);
        if (CollectionUtils.isEmpty(products)) {
            return Maps.newHashMap();
        }
        Map<Long, ListProduct> csuIdToInfoMap = products.stream().filter(item -> {
            if (Objects.isNull(item) || Objects.isNull(item.getId())) {
                return false;
            }
            if (!Objects.equals(item.getIsFragileGoods(), 1)) {
                // 非易碎品
                return true;
            }
            // 易碎品
            if (Objects.equals(item.getIsThirdCompany(), 1)) {
                // POP品
                return true;
            }
            // 平台品
            return BooleanUtils.isNotTrue(merchantIsNotWatchFragileGoods);
        }).collect(Collectors.toMap(ListProduct::getId, item -> item, (first, second) -> first));
        return csuIdToInfoMap;
    }

}
