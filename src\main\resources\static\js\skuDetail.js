$(function() {
	const params = new URLSearchParams(window.location.search);
	const combination = params.get('combination'); // 获取 param1 的值
	if (combination) {
		let src = $('#combination').attr('src');
		$('#combination').attr('src',src + '&flag=' + combination);
		var iframe = document.getElementById('combination');
		iframe.contentDocument.location.reload();
		$('#combination').show();
	}else{
		$('#combination').hide();
	}
    // hoverClass();
	// 监控移除iframe
	removeIframe();
	/* 控制促销的展开按钮 */
    showZhan();

    /*倒计时*/
    timer('timer');
    timer('timer_you');
	timerRemoveDay('add_subsidy','you')
	// 监控中药扩展属性
	skuExtAttrDTOSList();
    var merchantId = $("#merchantId").val();
    var categoryId = $("#categoryId").val();
    var rxNum = $("#rxNum").val();
    showRecommendedSku(merchantId,categoryId,rxNum);
    //点击换一批
    $("#rxNew").click(function(){
        var merchantId = $("#merchantId").val();
        var categoryId = $("#categoryId").val();
        var rxNum = $("#rxNum").val();
        showRecommendedSku(merchantId,categoryId,rxNum);
		//qt 子模块点击
		// aplus_queue.push({
		// 	'action': 'aplus.record',
		// 	'arguments': ['action_sub_module_click', 'CLK', {
		// 	"spm_cnt":"1_4.productDetail_"+$('#productId').val()+"-<EMAIL>@1."+window.getSpmE(),
		// 	"scm_cnt":"search.main_precise_sort_A.all_0.text_换一批."+window.scmEShopDetail(14),
		// 	}]
		// });
	   
    });
    if($("#cxUl li")&&$("#cxUl li").length>0){
    	var _textStr="";
    	for(var i=0;i<$("#cxUl li").length;i++){
    		var _that=$("#cxUl li").eq(i);
    		$(_that).attr("title",$(_that).find(".shuoming").text());
            if($(_that).find(".shuoming").text().length>40){
                _textStr=$(_that).find(".shuoming").text();
                _textStr=_textStr.slice(0,34)+"......";
                $(_that).find(".shuoming").text(_textStr);
			}
		}
	}
    // $("#cxUl li").hover(function () {
    //
    // },function () {
    //     console.log(3);
    // })
    //加载分类面包屑
    getCategory();
    //控制可领取显示
    klqShow();
    //控制已领取显示
    ylqShow();


    /*点击收藏*/
    setTimeout(function(){ $(".w-collectZone_list").removeClass("initial");},1000);
    /*$(".w-collectZone_list").click(function(){
        if($(this).hasClass("hasCollect")){
            取消收藏
            $(this).removeClass("hasCollect").addClass("nopCollect");
            弹窗提示 2秒后消失
            showCollectTC();
        }else{
            添加收藏
            $(this).removeClass("nopCollect").addClass("hasCollect");
        }
    })*/


    /* 显示收藏*/
	$(".mrth li").hover(
		function() {
			$(this).find(".showorno").css("display", "block");
		},
		function() {
			$(this).find(".showorno").css("display", "none");
		}
	);



	/*减操作*/
	$(".sub_detail").click(function() {
		var step = 1;
		var me = $(this),
			txt = me.next(":text");
		var val = parseFloat(txt.val());
		var isSplit = txt.attr("isSplit");
		var middpacking = txt.attr("middpacking");
		if(isSplit == 0){
			step = parseFloat(middpacking);
		}
		var num = 0;
		if(!isNaN(val)){
			num = val;
		}
		if(num <= step) {
			txt.val(0);
		} else {
			txt.val(num - step);
		}
	});
	/*加操作*/
	$(".add_detail").click(function() {
		var step = 10;
		var me = $(this),
			txt = me.prev(":text");
		var val = parseFloat(txt.val());
		var isSplit = txt.attr("isSplit");
		var middpacking = txt.attr("middpacking");
		// if(isSplit == 0){
		//无论是否可拆零都加中包装数量
			step = parseFloat(middpacking);
		// }
		var num = 0;
		if(!isNaN(val)){
			num = val;
		}
		txt.val(num + step);
	});

	/*激活商品详情*/
	$(".spxqfn").click(function () {
		$(this).addClass("cur").siblings().removeClass("cur");
		$(".c-spxq").css("display","block");
		$(".c-ybm").css("display","none");
		$(".c-shwy").css("display","none");
	});

	/*激活关于药帮忙*/
	$(".ybmfn").click(function () {
		$(this).addClass("cur").siblings().removeClass("cur");
		$(".c-spxq").css("display","none");
		$(".c-ybm").css("display","block");
		$(".c-shwy").css("display","none");
	});
	/*激活售后无忧*/
	$(".shwyfn").click(function () {
		$(this).addClass("cur").siblings().removeClass("cur");
		$(".c-spxq").css("display","none");
		$(".c-ybm").css("display","none");
		$(".c-shwy").css("display","block");
		if($("#slide1 li").length>8){
			$("div.scrollDiv").myScroll({
				speed:80, //数值越大，速度越慢
				rowHeight:34 //li的高度
			})
		}

	});

});
function skuExtAttrDTOSList(){
    var skuExtAttrDTOS = $("#skuExtAttrDTOS").val();
    if(skuExtAttrDTOS){
        skuExtAttrDTOS = JSON.parse(skuExtAttrDTOS);
        if(skuExtAttrDTOS.length > 0){
            var fragment = document.createDocumentFragment();
            for(var i = 0; i < skuExtAttrDTOS.length; i += 2){
                var tr = document.createElement('tr');
                tr.innerHTML = `
                    <td class="td1 graybd">${skuExtAttrDTOS[i].attrTitle}</td>
                    <td class="td2">${skuExtAttrDTOS[i].attrValue}</td>
                `;
                if(i + 1 < skuExtAttrDTOS.length){
                    tr.innerHTML += `
                        <td class="td3 graybd">${skuExtAttrDTOS[i+1].attrTitle}</td>
                        <td class="td4">${skuExtAttrDTOS[i+1].attrValue}</td>
                    `;
                }
                fragment.appendChild(tr);
            }
            var targetElement = document.querySelector('#skuExtAttrDTOS');
            targetElement.parentNode.insertBefore(fragment, targetElement.nextSibling);
        }
	}
}
function removeIframe(){
	// 在父页面的脚本中
	window.addEventListener('message', function(event) {
		if (event.data.action === 'removeIframe') {
			// 检查消息来源是否安全（可选，但推荐）
			if (event.origin !== window.location.origin) return;
			let iframe = document.querySelector('#spuFather');
			if (iframe) {
				iframe.parentNode.removeChild(iframe);
				console.log("已移除iframe");
			}
		}
	});

}

//==================图片详细页函数=====================
//鼠标经过预览图片函数
function preview(img){
	$("#preview .jqzoom img.sptp").attr("src",$(img).attr("src"));
	$("#preview .jqzoom img.sptp").attr("jqimg",$(img).attr("bimg"));
}

//图片放大镜效果
// $(function(){
// 	$(".jqzoom").jqueryzoom({xzoom:380,yzoom:450});
// });
//图片放大镜效果（根据图片像素比例放大）
$(function(){
	$(".jqzoom").each(function(){
		var $zoomContainer = $(this);
		var img = $zoomContainer.find('img');
		
		// 如果图片已经加载完成
		if (img[0].complete && img[0].naturalWidth > 0) {
			initZoom($zoomContainer, img[0]);
		} else {
			// 等待图片加载完成
			img.on('load', function() {
				initZoom($zoomContainer, this);
			});
		}
	});
	
	function initZoom($container, imgElement) {
		var originalWidth = imgElement.naturalWidth;
		var originalHeight = imgElement.naturalHeight;
		
		// 确保获取到了有效的尺寸
		if (originalWidth > 0 && originalHeight > 0) {
			$container.jqueryzoom({
				xzoom: originalWidth * 0.253,
				yzoom: originalHeight * 0.3,
				position: "right"  // 强制固定在右侧显示
			});
		}
	}
});



//图片预览小图移动效果,页面加载时触发
$(function(){
	var tempLength = 0; //临时变量,当前移动的长度
	var viewNum = 4; //设置每次显示图片的个数量
	var moveNum = 1; //每次移动的数量
	var moveTime = 300; //移动速度,毫秒
	var scrollDiv = $(".spec-scroll .items ul"); //进行移动动画的容器
	var scrollItems = $(".spec-scroll .items ul li"); //移动容器里的集合
	var moveLength = scrollItems.eq(0).width() * moveNum; //计算每次移动的长度
	var countLength = (scrollItems.length - viewNum) * scrollItems.eq(0).width(); //计算总长度,总个数*单个长度

	$(".spec-scroll .prev").addClass("noclick");
	//下一张
	$(".spec-scroll .next").bind("click",function(){
		$(".spec-scroll .prev").removeClass("noclick");
		if(tempLength < countLength){
			if((countLength - tempLength) > moveLength){
				scrollDiv.animate({left:"-=" + moveLength + "px"}, moveTime);
				tempLength += moveLength;
			}else{
				scrollDiv.animate({left:"-=" + (countLength - tempLength) + "px"}, moveTime);
				tempLength += (countLength - tempLength);
				$(this).addClass("noclick");
			}
		}
	});
	//上一张
	$(".spec-scroll .prev").bind("click",function(){
		$(".spec-scroll .next").removeClass("noclick");
		if(tempLength > 0){
			if(tempLength > moveLength){
				scrollDiv.animate({left: "+=" + moveLength + "px"}, moveTime);
				tempLength -= moveLength;
			}else{
				scrollDiv.animate({left: "+=" + tempLength + "px"}, moveTime);
				tempLength = 0;
				$(this).addClass("noclick");
			}
		}
	});
});

//缩略图为空时隐藏
$(function(){
	var li_length=$(".spec-scroll ul").find("li").length;
	if(li_length<=0){
		$(".spec-scroll").addClass("noshow");
	}
	if(li_length>0 && li_length<=4){
		$(".spec-scroll .next,.spec-scroll .prev").unbind( "click" );
		$(".spec-scroll .next,.spec-scroll .prev").addClass("noclick");
	}
})
//==================图片详细页函数=====================

//点击链接跳转事件
function jumpUrl() {
	var isNewTab = $("#isNewTab").val();
	var descriptionPCUrl = $("#descriptionPCUrl").val();
	if (isNewTab == 1){
		window.open(descriptionPCUrl);
	}else{
		// 离开详情页曝光-极光埋点
		// window.AnalysysAgent.track('product_view_close', {
		// 	'product_id': $('#productId').val() * 1,
		// 	'product_name': $('#productShowName').val(),
		// 	'product_first' : $("#categorySecondName").text(),
		// 	'sptype': $('#sptype').val(),
		// 	'spid': $('#spid').val(),
		// 	'sid': $('#sid').val(),
		// });
		window.location.href=descriptionPCUrl;
	}
}

//==================图片详细页函数=====================

//时分秒倒计时方法
function timer(eleId) {
	function ptTimeShow(show,that) {
		try{
			if(show == 'show'){
				requestAnimationFrame(function() {
					$(that).closest('#ptTimeShow').show();
				 });
				
			}else{
				requestAnimationFrame(function() {
					$(that).closest('#ptTimeShow').hide();
				 });
			}
		}catch(e){}
	}
    var element = document.getElementById(eleId);
    if(element) {
        endTime = element.getAttribute('data-timer');
		var ts = '';
        if(element.getAttribute('from') == 'you'){
        	ts = endTime - 1000;
			element.setAttribute('data-timer',ts)
		}else{
        	ts = endTime - new Date().getTime();
		}
        if(ts > 0) {
            if(element.getAttribute('from') == 'you'){
				var dd = parseInt(ts / 1000 / 60 / 60 / 24, 10);
				var hh = parseInt(ts / 1000 / 60 / 60 % 24, 10);
				var mm = parseInt(ts / 1000 / 60 % 60, 10);
				var ss = parseInt(ts / 1000 % 60, 10);
				dd = dd<10?("0" + dd):dd;   //天
				hh = hh < 10 ? ("0" + hh) : hh; //时
				mm = mm < 10 ? ("0" + mm) : mm; //分
				ss = ss < 10 ? ("0" + ss) : ss; //秒
				if(dd > 0){
					// hh = Number(hh) + Number(24);
					document.getElementById("timer_d").innerHTML=dd;
					ptTimeShow('',element)
				}else {
					document.getElementById("timer_d").style.display = 'none';
					document.getElementById("daySpan").style.display = 'none';
					ptTimeShow('show',element)
				}
				document.getElementById("timer_h_you").innerHTML = hh;
				document.getElementById("timer_m_you").innerHTML = mm;
				document.getElementById("timer_s_you").innerHTML = ss;
			}else{
				var dd = parseInt(ts / 1000 / 60 / 60 / 24, 10);
				var hh = parseInt(ts / 1000 / 60 / 60 % 24, 10);
				var mm = parseInt(ts / 1000 / 60 % 60, 10);
				var ss = parseInt(ts / 1000 % 60, 10);
				dd = dd<10?("0" + dd):dd;   //天
				hh = hh < 10 ? ("0" + hh) : hh; //时
				mm = mm < 10 ? ("0" + mm) : mm; //分
				ss = ss < 10 ? ("0" + ss) : ss; //秒
				document.getElementById("timer_d").innerHTML=dd;
				document.getElementById("timer_h").innerHTML = hh;
				document.getElementById("timer_m").innerHTML = mm;
				document.getElementById("timer_s").innerHTML = ss;
				if(dd>0){
					ptTimeShow('',element)
				}else {				
					ptTimeShow('show',element)
				}
			}
            setTimeout(function() {
                timer(eleId);
            }, 1000);
        } else {
            document.getElementById("timer_d").innerHTML=0;
            document.getElementById("timer_h").innerHTML = 0;
            document.getElementById("timer_m").innerHTML = 0;
            document.getElementById("timer_s").innerHTML = 0;
			ptTimeShow('show',element)
        }
    }
}

//优惠券弹窗
function test(){
    $("#fpxzTc").modal('show')
}

// 領取優惠券
function receiveTemplate(merchantId,receiveTemplateId){
    if(merchantId && merchantId > 0){
        $.ajax({
            url: "/merchant/center/voucher/receiveVoucher",
            type: "POST",
            dataType: "json",
            data: {
                merchantId: merchantId,
                voucherTemplateId: receiveTemplateId
            },
            success: function(result){
                if(result.status == "success"){
                    // $.alert({
                    //     title: '提示',
                    //     body: result.msg
                    // });
                    $('#v_'+receiveTemplateId).replaceWith('<img src="/static/images/events/20180605-lt-lqzx/quan_icon_2.png" class="pos-ysy">');
                }else {
                    $.alert({
                        title: '提示',
                        body: result.errorMsg
                    });
                }
            },
            error: function(){
                $.alert({
                    title: '提示',
                    body: '因为某些原因导致优惠券领取异常哟!'
                });
            }
        });
    }else{
        $.alert({
            title: '提示',
            body: '您还没有登录，请先登录!',
            okHidden : function(e){
                window.location.href="/login/login.htm?redirectUrl=/";
            }
        });
    }
}

function showZhan(){
	var liLength = $("#cxUl li").length;
	if(parseInt(liLength) == 2){
		var desch1 = $("#desc0").height();
        var desch2 = $("#desc1").height();
        if(desch1 > 24 || desch2 > 24){
            $('.cuxiao-ul li').addClass("text-overflow");
            hoverClass();
		}else{
            $(".zhankai").css("display","none");
		}
	}

	if(parseInt(liLength) > 2){
        hoverClass();
	}
}

function hoverClass(){
	let isFold = true;
    /*展开收起*/
    $(".zhankai").click(
        function() {
			if (isFold) {
				isFold = false;
				$(".zhankai").text("收起");
				$('.cuxiao-ul').css("max-height","100%");
				$('.cuxiao-ul li').removeClass("text-overflow");
				$('.cuxiao-ul li.sbzhan').css("display","none");
				var _textStr="";
				for(var i=0;i<$("#cxUl li").length;i++){
					var _that=$("#cxUl li").eq(i);
					_textStr=$("#cxUl li").eq(i).attr("title");
					$(_that).find(".shuoming").text(_textStr);
				}
			} else {
				isFold = true;
				$(".zhankai").text("展开");
				$('.cuxiao-ul').css("max-height","54px");
				$('.cuxiao-ul li').addClass("text-overflow");
				$('.cuxiao-ul li.sbzhan').css("display","block");
			}
        }
    )
    /* $(".cuxiaobox").hover(
        function() {
		},
        function() {
            $(".zhankai").css("display","block");
            $('.cuxiao-ul').css("max-height","54px");
            $('.cuxiao-ul li').addClass("text-overflow");
            $('.cuxiao-ul li.sbzhan').css("display","block");
        }
    ) */
}


function showRecommendedSku(merchantId,categoryId,rxNum){
    $.ajax({
        url: "/search/findRecommendedSku",
        type: "POST",
        dataType: "html",
        traditional :true,
        data: {
            merchantId: merchantId,
            categoryId: categoryId,
            rxNum:rxNum
        },
        success: function(result){
            $("#rexiao").html(result);
            var newRxNum = parseInt(rxNum)+1;
            $("#rxNum").val(newRxNum);
            //推荐列表曝光
            webSdk.track('pc_page_Commoditylist', {
                'sptype': $('#sptype1').val(),
                'spid': $('#spid1').val(),
                'sid': $('#sid1').val()
            });
            var productList = $(".mrth-new li");
            var idList = '';
            for(var i = 0;i < productList.length;i++){
                if(i < productList.length-1){
                    idList += $(productList[i]).find("#skuId1").val() + '_';
                }else{
                    idList += $(productList[i]).find("#skuId1").val() + '';
                }
            }
            if(idList){
                //推荐列表商品曝光
                webSdk.track('pc_page_ListPage_Exposure', {
                    'sptype': $('#sptype1').val(),
                    'spid': $('#spid1').val(),
                    'sid': $('#sid1').val(),
                    "commodityId": idList
                });
            }
			//qt 埋点
			//  // 判断元素是否在视口内
			//  function isElementInViewport($element) {
			// 	const elementTop = $element.offset().top; // 元素距离顶部的偏移量
			// 	const elementBottom = elementTop + $element.outerHeight(); // 元素底部距离顶部的偏移量
			// 	const viewportTop = $(window).scrollTop(); // 视口顶部位置
			// 	const viewportBottom = viewportTop + $(window).height(); // 视口底部位置	  
			// 	return elementBottom > viewportTop && elementTop < viewportBottom;
			//   }
			// let arrIndex=[]
			// window.page_component_exposure_shopDetail=function(){
			// 	if( $("#rexiao li").length){
			// 		$("#rexiao li").each(function(index,$target){
			// 			if(arrIndex.indexOf(index)==-1){
			// 				if (isElementInViewport($($target))) {
			// 					let data={
			// 						"spm_cnt":"1_4.productDetail_"+$('#productId').val()+"-<EMAIL>@"+(index+1)+"."+window.getSpmE(),
			// 						"scm_cnt":"search.main_precise_sort_A.338828_ZS202502101303025367.prod-"+$($target).find('#skuId1').val()+".scmE",//yz 商品组和人群scme14位
			// 						"result_cnt":$("#rexiao li").length,
			// 						"product_id": $($target).find('#skuId1').val(),
			// 						"product_name":$($target).find('.row2 a').text().trim()
			// 					}
			// 					arrIndex.push(index)
			// 					console.log('商品'+index+'曝光');
			// 					console.log(data)
			// 					aplus_queue.push({
			// 						'action': 'aplus.record',
			// 						'arguments': ['page_list_product_exposure', 'EXP', 
			// 							data
			// 						]
			// 					});
			// 				}else{								
			// 					// console.log('Target Element is not in the viewport!');
			// 				}   
			// 			}						
			//  	 })
			// 	}                     
            // }
			// $(window).on('scroll',window.page_component_exposure_shopDetail);
			// setTimeout(function(){
			// 	window.page_component_exposure_shopDetail();
			// 	if( $("#rexiao li").length){
			// 		$("#rexiao li").each(function(index,dom){
			// 			dom.addEventListener('click',function(){
			// 				//qt
			// 				let data={
			// 					"spm_cnt":"1_4.productDetail_"+$('#productId').val()+"-<EMAIL>@"+(index+1)+"."+window.getSpmE(),
			// 					"scm_cnt":"search.main_precise_sort_A.338828_ZS202502101303025367.prod-"+$(dom).find('#skuId1').val()+".scmE",//yz 商品组和人群scme14位
			// 					"result_cnt":$("#rexiao li").length,
			// 					"product_id": $(dom).find('#skuId1').val(),
			// 					"product_name":$(dom).find('.row2 a').text().trim()
			// 				}
			// 				aplus_queue.push({
			// 					'action': 'aplus.record',
			// 					'arguments': ['action_list_product_click', 'CLK', 
			// 						data
			// 					]
			// 				});
			// 			})
			// 		})
			// 	}
			// },1000)
        }
    });
}

/**
 * 加载顶部面包屑
 */
function getCategory() {
	var skuId = $('#productId').val();
    $.ajax({
        url: "/search/skuDetailCategoryRelation.json",
        type: "POST",
        dataType: "json",
        data: {
            id: skuId
        },
        success: function(result){
        	if(result.status != "success"){
        		window.history.back();
        		return;
			}
			var html = '<a href="/">首页</a> <span >/ </span>';
			var detail = result.data.productCategoryInfo;
        	if(detail.categoryFirstId != null && detail.categoryFirstName != null && detail.categoryFirstName != ''){
        		html = '<a href="/search/skuInfoByCategory.htm?categoryFirstId='+detail.categoryFirstId+'">'+detail.categoryFirstName+'</a>';
        		html += '<span >/ </span>';
			}
			if(detail.categorySecondId != null && detail.categorySecondName != null && detail.categorySecondName != ''){
        		html += '<a href="/search/skuInfoByCategory.htm?categorySecondId='+detail.categorySecondId+'">'+detail.categorySecondName+'</a>'
                html += '<span >/ </span>';
        		$("#categorySecondName").text(detail.categorySecondName);
			}
			if(detail.categoryThirdId != null && detail.categoryThirdName != null && detail.categoryThirdName != ''){
				html += '<a href="/search/skuInfoByCategory.htm?categoryThirdId='+detail.categoryThirdId+'">'+detail.categoryThirdName+'</a>';
				html += '<span >/ </span>';
			}
			html += '<a href="javascript:void(0);" class="cur">'+$('#productShowName').val()+'</a>';
			$('#productRelation').html(html);
            // zhuge.track('pc_action_CommodityDetails', {
            //     'commodityId' : $("#productId").val(),
            //     'commodityName' : $("#productShowName").val(),
            //     'commodityCode' : $("#barcode").val(),
            //     'commodityCategory' : $("#categorySecondName").text()
            // });
            webSdk.track('pc_page_CommodityDetails', {
                'commodityId' : $("#productId").val(),
                'commodityName' : $("#productShowName").val(),
                'commodityCode' : $("#barcode").val(),
                'commodityCategory' : $("#categorySecondName").text(),
                'sptype': $('#sptype').val(),
                'spid': $('#spid').val(),
                'sid': $('#sid').val(),
                'real': 2
            });
            webSdk.track('pc_action_CommodityDetails', {
                'commodityId' : $("#productId").val(),
                'commodityName' : $("#productShowName").val(),
                'commodityCode' : $("#barcode").val(),
                'commodityCategory' : $("#categorySecondName").text()
            });
        }
    });
}

function klqShow(){
    var liLength = $("#klq li").length;
    if(parseInt(liLength) == 0){
        $("#klq").addClass("noshow");
        $("#klqb").append('<div class="noyhq">暂无可领取的优惠券</div>');
    }
}

function ylqShow(){
    var liLength = $("#ylq li").length;
    if(parseInt(liLength) == 0){
        $("#ylqb").addClass("noshow");
    }
}

function priceNotify(skuId, fob) {
	var merchantId = $('#merchantId').val();
	if(!merchantId || merchantId <=0 ){
		$.alert({
			title: '提示',
			body: '您还没有登录，请先登录!',
			okHidden : function(e){
				window.location.href="/login/login.htm?redirectUrl=/search/skuDetail/"+skuId+".htm";
			}
		});
	}
	else{
	    $.alert({
	        title: '降价通知',
	        body:'<div style="background: #FFFBDB;height: 33px;width: 100%;padding: 0px 30px 0 0;text-indent: 16px;margin:-20px 0 20px -15px;line-height: 33px;font-size: 12px;color: rgba(255,0,0,0.75);">当该商品在<span>45</span>天内降价，您将收到推送消息</div>\
						<div style="font-size: 14px;color: rgba(0,0,0,0.75);margin-bottom: 15px;">当前价格：￥<span>'+fob+'</span></div>\
						<div style="font-size: 14px;color: rgba(0,0,0,0.75);position: relative;">您的渠道价格：<span style="position: absolute;top: 8px;left: 100px;">￥</span><input id="expectPrice" onfocus="javascrpit:hideMsg(this);"  type="text" placeholder="低于该价格后会通知您" style="height: 30px;width: 243px;border: 1px solid #D9D9D9;border-radius: 4px;padding-left: 16px;"></div>\
						<div id="priceAlert" hidden style="color:red">渠道价格应该小于当前价格</div>\
						<div id="gtZero" hidden style="color:red">渠道价格必须大于0</div>\
						<div style="font-size: 12px;color: rgba(153,153,153,0.75);margin-top: 15px;">*确认后，同时将收藏该商品，您可以在我的收藏夹查看您订阅过的所有商品</div>',
	        okBtn : '提交',
	        okHide:   function(e){ 
	        	var expectPrice = $("#expectPrice").val();
		        if(expectPrice >= fob){
		        	$('#priceAlert').show();
		        	return false;
		        }
		        
		        if(expectPrice<= 0){
		        	$('#gtZero').show();
		        	return false;
		        }
	         },
	        okHidden : function(e){
	        	var index=layer.load(2);
	            var expectPrice = $("#expectPrice").val();
	            $.ajax({
	                type: "POST",
	                url: "/merchant/merchantBusiness/pricenotify/"+skuId+".json",
	                data: {
	                    expectPrice: expectPrice,
	                    currentPrice: fob
	                },
	                dataType: "json",
	                success: function(result){
	                	layer.close(index);
	                    if(result.status == "success"){
	                        $.alert("订阅成功");
	                        setTimeout(function(){
	                        	 window.location.reload(true);
	           			 },2000);
	
	                    }else {
	                        $.alert(result.errorMsg);
	                    }
	                }
	            });
	        }
	    });
	}
    $('#expectPrice').keyup(function(event) {
	    var $amountInput = $(this);
	    //响应鼠标事件，允许左右方向键移动
	    event = window.event || event;
	    if (event.keyCode == 37 | event.keyCode == 39) {
	        return;
	    }
	    //先把非数字的都替换掉，除了数字和.
	    $amountInput.val($amountInput.val().replace(/[^\d.]/g, "").
	        //只允许一个小数点
	            replace(/^\./g, "").replace(/\.{2,}/g, ".").
	        //只能输入小数点后两位
	            replace(".", "$#$").replace(/\./g, "").replace("$#$", ".").replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'));
	});
	
}


function hideMsg(obj){
	$('#priceAlert').hide();
	$('#gtZero').hide();
	
	var value = $(obj).val();
	if(!(value+"")){
		return;
	}
	  //最后一位是小数点的话，移除
	value = value.replace(/\.$/g, "");
	$(obj).val(value);
	var reg = new RegExp("^[0-9]+(.([0-9]{1,2}))?$");;
	if(!reg.test(value + "")){
		return;
	}
	
}

function timerRemoveDay(eleId,from) {
	var element = document.getElementById(eleId);
	if (!element) return;
	var endTime;
	if (from === 'you') {
		endTime = new Date().getTime() + parseInt(element.getAttribute('data-timer'), 10) - 1000;
	}else {
		endTime = parseInt(element.getAttribute('data-timer'), 10);
	}
	var intervalId = setInterval(function() {
		var now = new Date().getTime();
		var ts = endTime - now;
		if (ts > 0) {
			var hh = String(parseInt(ts / 3600000 % 24, 10)).padStart(2, '0');
			var mm = String(parseInt(ts / 60000 % 60, 10)).padStart(2, '0');
			var ss = String(parseInt(ts / 1000 % 60, 10)).padStart(2, '0');
			if (from === 'you') {
				document.getElementById("timer_h_you").innerHTML = hh;
				document.getElementById("timer_m_you").innerHTML = mm;
				document.getElementById("timer_s_you").innerHTML = ss;
			} else {
				document.getElementById("timer_h").innerHTML = hh;
				document.getElementById("timer_m").innerHTML = mm;
				document.getElementById("timer_s").innerHTML = ss;
			}
		}else {
			clearInterval(intervalId); // 停止计时
			if (from === 'you') {
				document.getElementById("timer_h_you").innerHTML = "00";
				document.getElementById("timer_m_you").innerHTML = "00";
				document.getElementById("timer_s_you").innerHTML = "00";
			} else {
				document.getElementById("timer_h").innerHTML = "00";
				document.getElementById("timer_m").innerHTML = "00";
				document.getElementById("timer_s").innerHTML = "00";
			}    
		}
	},1000)
}
