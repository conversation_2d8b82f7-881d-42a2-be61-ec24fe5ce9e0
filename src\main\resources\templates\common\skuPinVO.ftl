<#macro skuVO skuVO skuIndex='-1' shopIndex='-1'>
<li class="sku-item-qt sku-item-qt-click" qtData="${skuIndex},${shopIndex},${skuVO.id},${skuVO.showName},${skuVO.scmId}">
    <input type="hidden" id="skuId" name="skuId" value="${skuVO.id}">
    <input type="hidden" id="skuName" name="skuName" value="${skuVO.showName}"/>
    <input type="hidden" id="barcode" name="barcode" value="${skuVO.barcode}"/>
    <input type="hidden" id="categoryId" name="categoryId" value="${skuVO.categoryId}"/>
    <input type="hidden" id="favoriteStatus" name="favoriteStatus" value="${skuVO.favoriteStatus}"/>
    <input type="hidden" id="preheatShowPrice" name="preheatShowPrice" value="${skuVO.actPt.preheatShowPrice}"/>
    <input type="hidden" name="skuIndex" value="${skuIndex}"/>
    <input type="hidden" name="shopIndex" value="${shopIndex}"/>
<div class="row1">
    <#if sptype_2??>
    <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype_2}&spid=${spid_2}&sid=${sid_2}" target="_blank" title="${skuVO.commonName}">
    <#else>
    <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype}&spid=${spid}&sid=${sid}" target="_blank" title="${skuVO.commonName}">
    </#if>
        <!--特价标签优先promotionSkuImageUrl-->
	    <#if (merchant ? exists)>
	        <#if skuVO.promotionSkuImageUrl?? && skuVO.promotionSkuImageUrl != null>
	           <#if skuVO.isControl ==1 && !skuVO.isPurchase>
	           <#else>
	        	<div class="yaokuanghuan-pos ">
									<img src="${productImageUrl}/${skuVO.promotionSkuImageUrl}" class="jjks" alt="">
									<div class="tejia806">￥<span class="price806">${skuVO.promotionSkuPrice}</span></div>
			    </div>
			   </#if>
			</#if>
	    </#if>

        <#if skuVO.promotionSkuImageUrl == null && skuVO.markerUrl?? && skuVO.markerUrl!=''>
            <div class="yaokuanghuan-pos">
                <img src="${productImageUrl}/${skuVO.markerUrl}" alt="">
            </div>
        </#if>

        <img id="dt_${skuVO.id}" src="${productImageUrl}/ybm/product/min/${skuVO.imageUrl}" alt="" onerror="this.src='/static/images/default-middle.png'"/>
        <#if skuVO.activityTag != null && skuVO.activityTag.tagNoteBackGroupUrl != ''>
            <#if skuVO.activityTag.sourceType == 2 >
                <div class="shop-activity-tag w185">
                    <img src="${skuVO.activityTag.tagNoteBackGroupUrl}" alt="">
                    <span class="top-box">${skuVO.activityTag.customTopNote}</span>
                    <span class="time-box">${skuVO.activityTag.timeStr}</span>
                    <div class="price-box">
                        <#list skuVO.activityTag.skuTagNotes as skuTagNote>
                            <span>${skuTagNote.text}</span>
                        </#list>
                    </div>
                    <span class="bottom-box">${skuVO.activityTag.customBottomNote}</span>
                </div>
            <#else>
                <div class="yaokuanghuan-posnew">
                    <img src="${productImageUrl}/${skuVO.activityTag.tagNoteBackGroupUrl}" alt="">
                    <div class="acTime">
                        <span class="ast">${skuVO.activityTag.timeStr}</span>
                    </div>
                    <#if skuVO.activityTag.skuTagNotes?? && skuVO.activityTag.skuTagNotes?size != 0>
                        <div class="tejia806">
                            <#list skuVO.activityTag.skuTagNotes as skuTagNote>
                                <span class="price806" style="color: #${skuTagNote.textColor}">${skuTagNote.text}</span>
                            </#list>
                        </div>
                    </#if>
                </div>
            </#if>

        </#if>

    </a>
    <!--标签-->
    <div class="bq-box">
		<#if skuVO.status == 4>
            <img src="/static/images/product/bq-xiajia.png" alt="">
        <#elseif (skuVO.status == 1 && skuVO.availableQty == 0) || skuVO.status == 2 || ((skuVO.status == 3 || skuVO.status == 5)  && skuVO.promotionTotalQty == 0)  || (skuVO.isSplit == 0 && skuVO.availableQty - skuVO.mediumPackageNum lt 0)>
            <img src="/static/images/product/bq-shouqing.png" alt="">
		</#if>
    </div>
</div>
<div class="row2">
    <#if sptype_2??>
    <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype_2}&spid=${spid_2}&sid=${sid_2}" style="word-break: break-all;
    padding-top:1px
    text-overflow: ellipsis;
    display: -webkit-box;
    white-space: normal;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;" target="_blank" title="${skuVO.showName}">
    <#else>
    <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype}&spid=${spid}&sid=${sid}" style="word-break: break-all;
    padding-top:1px
    text-overflow: ellipsis;
    white-space: normal;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;" target="_blank" title="${skuVO.showName}">
    </#if>
        <#if skuVO.isShow806 || skuVO.gift>
			<div class="bq806">
                <img src="/static/images/bq806.png" alt="">
            </div>
        </#if>
        <#if skuVO.activityTag != null && skuVO.activityTag.tagUrl != ''>
            <div class="bq806">
                <img src="${productImageUrl}/${skuVO.activityTag.tagUrl}" alt="">
            </div>
        </#if>
        <#if skuVO.agent == 1>
            <span class="dujia">独家</span>
        </#if>
        <#if skuVO.isUsableMedicalStr ?? && skuVO.isUsableMedicalStr == 1>
            <#--  <span class="yibao">国家医保</span>  -->
            <#if skuVO.medicalPayType ?? && skuVO.medicalPayType == 1>
                <span class="yibao">医保甲类</span>
            <#elseif skuVO.medicalPayType ?? && skuVO.medicalPayType == 2>
                <span class="yibao">医保乙类</span>
            <#elseif skuVO.medicalPayType ?? && skuVO.medicalPayType == 3>
                <span class="yibao">医保丙类</span>
            <#--  <#else>
                <span class="yibao">医保</span>  -->
            </#if>
        </#if>
        <#--  ${skuVO.showName}/${skuVO.spec}  -->
        ${skuVO.showName}
    </a>
</div>
<#--    <div class="row4 text-overflow">-->
<#--        ${skuVO.spec}-->
<#--    </div>-->
<div class="row4 text-overflow" style="font-size: 12px">
    <#if skuVO.nearEffect?? && skuVO.farEffect>
    有效期${skuVO.nearEffect}/${skuVO.farEffect}
    </#if>
</div>
<div class="row3">
	<#if (merchant ? exists)>
        <#--        <#if skuVO.isOEM?? && skuVO.isOEM == 'true' && skuVO.signStatus == '0' >-->
        <#--            <span class="noPermission">价格签署协议可见</span>-->
        <#--        <#elseif skuVO.showAgree =='0'>-->
        <#--            <span class="noPermission">价格签署协议可见</span>-->
        <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
            <span class="noPermission">含税价认证资质后可见</span>
        <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
            <span class="noPermission">含税价认证资质后可见</span>
        <#elseif skuVO.controlType == '2'>
            <span class="noPermission">不在经营范围</span>
        <#elseif skuVO.agreementEffective?? && skuVO.agreementEffective =='3'>
            <span class="noPermission">协议已冻结，价格解冻后可见</span>
        <#elseif skuVO.isControl ==1 >
			<#if skuVO.isPurchase>
				<#if skuVO.priceType==1>
                    <span class="meiyuan">￥</span><span class="price" style="font-size: 18px">

                    <#if (skuVO.actPt.assembleStatus?? && skuVO.actPt.assembleStatus ==1) || skuVO.actPt.preheatShowPrice == 1>
                        <#if skuVO.actPt.stepPriceStatus == 1>
                            ${skuVO.actPt.minSkuPrice}-${skuVO.actPt.maxSkuPrice}
                            <input type="hidden" id="price-${skuVO.id}" value="${skuVO.actPt.minSkuPrice}"/>
                        <#else>
                            ${skuVO.actPt.assemblePrice}
                            <input type="hidden" id="price-${skuVO.id}" value="${skuVO.actPt.assemblePrice}"/>
                        </#if>
                    <#else>
                        ?
                    </#if>

                    <#--                    <#if skuVO.actPt.assembleStatus?? && skuVO.actPt.assembleStatus ==1>-->
                    <#--                        ${skuVO.actPt.assemblePrice}-->
                    <#--                    <#else>-->
                    <#--                        <#if skuVO.actPt.preheatShowPrice == 1>-->
                    <#--                            ${skuVO.actPt.assemblePrice}-->
                    <#--                        <#else >-->
                    <#--                            ?-->
                    <#--                        </#if>-->
                    <#--                    </#if>-->
                    </span>
                    <span class="zhehou-price zhehou-price-${skuVO.id}"></span>
				<#else >
					<#if skuVO.skuPriceRangeList ??>
                        <span class="meiyuan">￥</span><span class="price">
                            <#list skuVO.skuPriceRangeList  as priceReange>
                                <#if priceReange_index==0>
                                ${priceReange.price}
                                </#if>
                                <#if !priceReange_has_next>
                                    ～${priceReange.price}
                                </#if>
                            </#list>
                        </span>
					</#if>
				</#if>
			<#else>
                <span class="noPermission">暂无购买权限</span>
			</#if>
		<#else>
			<#if skuVO.priceType==1 >
                <span class="price">￥
                    <#if (skuVO.actPt.assembleStatus?? && skuVO.actPt.assembleStatus ==1) || skuVO.actPt.preheatShowPrice == 1>
                        <#if skuVO.actPt.stepPriceStatus == 1>
                            ${skuVO.actPt.minSkuPrice}-${skuVO.actPt.maxSkuPrice}
                            <input type="hidden" id="price-${skuVO.id}" value="${skuVO.actPt.minSkuPrice}"/>
                        <#else>
                            ${skuVO.actPt.assemblePrice}
                            <input type="hidden" id="price-${skuVO.id}" value="${skuVO.actPt.assemblePrice}"/>
                        </#if>

                    <#else>
                        ?
                    </#if>
                    <#--                    <#if skuVO.actPt.assembleStatus?? && skuVO.actPt.assembleStatus ==1>-->
                    <#--                        ${skuVO.actPt.assemblePrice}-->
                    <#--                    <#else>-->
                    <#--                        <#if skuVO.actPt.preheatShowPrice == 1>-->
                    <#--                            ${skuVO.actPt.assemblePrice}-->
                    <#--                        <#else >-->
                    <#--                            ?-->
                    <#--                        </#if>-->
                    <#--                    </#if>-->
                </span>
                <span class="zhehou-price zhehou-price-${skuVO.id}"></span>
			<#else >
				<#if skuVO.skuPriceRangeList ??>
                    <span class="price">￥
						<#list skuVO.skuPriceRangeList  as priceReange>
							<#if priceReange_index==0>
							${priceReange.price}
							</#if>
							<#if !priceReange_has_next>
                                ～${priceReange.price}
							</#if>
						</#list>
                                                   </span>
				</#if>
			</#if>
		</#if>
	<#else>
        <span class="login_show">价格登录可见</span>
	</#if>
    <!--正常显示价格样式
    <span class="meiyuan">￥</span><span class="price">22.50</span>-->
    <!--价格登录可见样式-->
    <!--<span class="login_show">价格登录可见</span>-->
    <!--暂无购买权限样式-->
    <!--<span class="noPermission">暂无购买权限</span>-->

        <br>
    <#if skuVO.unitPrice?? && skuVO.unitPrice != '' && !(skuVO.isControl == 1 && !skuVO.isPurchase) && skuVO.actPt.assembleStatus == 1>
      <span style="color: #FF3535;font-size: 10px;display: inline-block;background-color: transparent;border: 1px solid #FF3535;padding: 0px 1px;border-radius: 3px">${skuVO.unitPrice}</span>  
    </#if>

</div>
    <div class="row5 text-overflow">
        <span style="height: 18px;font-size: 12px;color: #ffffff;background: url('/static/img/events/pinjie.png') no-repeat;background-size: 100% 100%;padding-left: 10px;padding-right: 15px;padding-top: 2px;padding-bottom: 2px">已拼${skuVO.actPt.orderNum}${skuVO.productUnit}</span>
        <span style="height: 18px;font-size: 12px;color: #FF2121;background: url('/static/img/events/pinjie1.png') no-repeat;background-size: 100% 100%;padding-left: 15px;padding-right: 10px;margin-left: -15px;padding-top: 2px;padding-bottom: 2px">${skuVO.actPt.skuStartNum}${skuVO.productUnit}起拼</span>
    </div>
    <#if skuVO.actPt.assembleStatus == 1>
        <div class="timer_you" style="font-size: 14px;
font-family: PingFangSC, PingFangSC-Regular;display: inline-block;margin-left: 20px;
font-weight: 400;
text-align: left;
color: #fe5427;" from="you" data-timer="${skuVO.actPt.surplusTime}" id="ptTimeShow" style="display: none">
            <span class="only">距离结束仅剩</span>
            <span class="f24 timer_d">0</span>
            <span class="f18 daySpan">天</span>
            <span class="f24 ySpan timer_h_you">00</span>
            <span class="f18">:</span>
            <span class="f24 ySpan timer_m_you">00</span>
            <span class="f18">:</span>
            <span class="f24 ySpan timer_s_you">00</span>
            <#--									<span class="f18">:</span>-->
        </div>
    <#else >
        <div style="font-size: 14px;font-family: PingFangSC, PingFangSC-Regular;margin-left: 20px;font-weight: 400;text-align: left;color: #fe5427;">${skuVO.actPt.assembleStartTime?string('MM月dd日 HH:mm:ss')}开抢</div>
    </#if>
    <div class="row7">
        <#if skuVO.isThirdCompany == 0 && (skuVO.titleTagList?? && skuVO.titleTagList?size > 0)><span class="ziying">${skuVO.titleTagList[0].text}</span></#if>
        <#if skuVO.shopName ??><a href="javascript:;" style="text-decoration: none;width: 132px;display: inline-block;overflow: hidden;vertical-align: middle;text-overflow: ellipsis;white-space: nowrap;">${skuVO.shopName}</a></#if>
    </div>
<div class="row6-box showorno" style="height:auto;padding: 10px 0;">
    <!--中包装-->
    <#if skuVO.actPt.assembleStatus == 1 && skuVO.actPt.stepPriceStatus == 2>
        <#--  <div style="text-align: center;padding-bottom: 8px">
            <p style="display: inline-block;vertical-align: middle">
                <span class="spanD" style=" width: 120px;height: 8px;background: rgba(254,84,39,0.6);border-radius: 6px;position: relative;float: initial;display: inline-block;margin: 0">
			        <#if (skuVO.actPt.percentage >= 100)>
                        <span class="spanN" style="width: 100%; height: 8px;
                        opacity: 1;
                        background: #fe5427;
                        border-radius: 6px;
                        position: absolute;
                        margin: 0;
                        top: 0;
                        left: 0;"></span>
                    </#if>
			    </span>
            </p>
            <span class="spanT" style="vertical-align: middle;float: initial;margin: 0">
                <#if (skuVO.actPt.percentage?is_string)>
                    ${skuVO.actPt.percentage?number?string("#")}%
                <#else>
                    ${skuVO.actPt.percentage?string("#")}%
                </#if>
            </span>
        </div>  -->
    </#if>
<#--    <#if skuVO.isOEM?? && skuVO.isOEM == 'true' && (skuVO.signStatus == '0' || skuVO.agreementEffective =='0')>-->
    <#if merchant.licenseStatus ==1 && skuVO.fob == '0'>
        <div class="verifyBox">
            <a href="/merchant/center/license/findLicenseCategoryInfo.htm">资质认证</a>
        </div>
    <#elseif merchant.licenseStatus ==5 && skuVO.fob == '0'>
        <div class="verifyBox">
            <a href="/merchant/center/license/findLicenseCategoryInfo.htm">资质审核中</a>
        </div>
    <#elseif skuVO.agreementEffective?? && skuVO.agreementEffective =='3'>
<#--        <span class="noPermission">协议已冻结，价格解冻后可见</span>-->
    <#elseif skuVO.isOEM?? && skuVO.isOEM == 'true' && (skuVO.signStatus == '0' || skuVO.agreementEffective =='0')>
    <#else>
        <#-- 进行中 -->
        <#if skuVO.actPt.assembleStatus == 1>
            <#-- 单阶梯 -->
            <#if skuVO.actPt.stepPriceStatus == 2>
                <div class="verifyBox" style="width: 220px;height: 42px;opacity: 1;background-color:#FF6204;margin: 0 auto;border-radius: 2px;">
                    <#if sptype_2??>
                        <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype_2}&spid=${spid_2}&sid=${sid_2}" target="_blank" title="${skuVO.showName}" style="overflow: auto;text-overflow: initial;white-space: initial" onclick="action_list_product_btn_click_skuPinVo(this,event,1,'立即参团')">立即参团</a>
                    <#else>
                        <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype}&spid=${spid}&sid=${sid}" target="_blank" title="${skuVO.showName}" style="overflow: auto;text-overflow: initial;white-space: initial" onclick="action_list_product_btn_click_skuPinVo(this,event,1,'立即参团')">立即参团</a>
                    </#if>
                </div>
            <#-- 多阶梯 -->
            <#elseif skuVO.actPt.stepPriceStatus == 1>
                <div class="stepPriceBox">
                    <#if skuVO.controlTitle>
                        <div style="color: #FF5B5B;font-size: 16px;">${skuVO.controlTitle}</div>
                        <#else>
                        <div style="color: #FF5B5B;font-size: 16px;">
                            <span id="ptPrice_${skuVO.id}">￥${skuVO.actPt.maxSkuPrice}</span>
                            <span style="font-size:12px; margin-left: 5px;" class="zhehou-price-${skuVO.id}"></span>
                        </div>
                    </#if>
                    <div class="showName" style="color: #fff; font-size: 14px;margin-top: 10px;">${skuVO.showName}/${skuVO.spec}</div>
                    <#if skuVO.nearEffect?? && skuVO.farEffect>
                        <span style="color: #B4B8BC; font-size: 12px">有效期${skuVO.nearEffect}/${skuVO.farEffect}</span>
                    </#if>
                    <#if skuVO.actPt.stepPriceShowTexts>
                        <#list skuVO.actPt.stepPriceShowTexts as item>
                            <div style="font-size: 12px; color: #FFCD86;margin-top: 4px;">${item}</div>
                        </#list>
                    </#if>

                    <div style="margin-top: 10px;">
                        <span style="height: 18px;font-size: 12px;color: #FF6F48;">已拼${skuVO.actPt.orderNum}${skuVO.productUnit}</span>
                        <span style="height: 18px;font-size: 12px;color: #B4B8BC;">/${skuVO.actPt.skuStartNum}${skuVO.productUnit}起拼</span>
                    </div>
                    <#--  <div style="padding-bottom: 8px">
                        <p style="display: inline-block;vertical-align: middle">
                            <span class="spanD" style=" width: 154px;height: 5px;background: rgba(254,84,39,0.6);border-radius: 4px;position: relative;float: initial;display: inline-block;margin: 0">
                                <#if (skuVO.actPt.percentage >= 100)>
                                    <span class="spanN" style="width: 100%; height: 5px;
                                            opacity: 1;
                                            background: #fe5427;
                                            border-radius: 4px;
                                            position: absolute;
                                            margin: 0;
                                            top: 0;
                                            left: 0;"></span>
                                </#if>
			                </span>
                        </p>
                        <span class="spanT" style="vertical-align: middle;float: initial;margin: 0;color: #FF6F48;">
                            <#if (skuVO.actPt.percentage?is_string)>
                                ${skuVO.actPt.percentage?number?string("#")}%
                            <#else>
                                ${skuVO.actPt.percentage?string("#")}%
                            </#if>
                        </span>
                    </div>  -->
                    <div style="display: flex;align-items: center;justify-content: space-around">
                        <div>
                            <a href="javascript:void(0);" class="sub fl" is-pt-id="${skuVO.id}"  onclick="action_list_product_btn_click_skuPinVo(this,event,1,'减')">-</a>
                            <input class="fl" type="text" value="${skuVO.actPt.skuStartNum}" id="buyNumDl_${skuVO.id}" name="buyNum"
                                   onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                   onblur="handleCheckValue(${skuVO.id},${skuVO.actPt.skuStartNum})"
                                   onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"
                                   isSplit="${skuVO.isSplit}" middpacking="${skuVO.mediumPackageNum}" skuStartNum="${skuVO.actPt.skuStartNum}" onclick="action_list_product_btn_click_skuPinVo(this,event,2,this.value)" />
                            <a href="javascript:void(0);" class="add fl" is-pt-id="${skuVO.id}" onclick="action_list_product_btn_click_skuPinVo(this,event,3,'加')">+</a>
                        </div>
                        <a href="javascript:void(0);" class="buy fl" id="href_DETAIL_${skuVO.id}" onclick="action_list_product_btn_click_skuPinVo(this,event,4,'立即参团');addCartDetail(${skuVO.id},${skuVO.mediumPackageNum},${skuVO.isSplit},event,'',7)">立即参团</a>
                    </div>
                </div>
            </#if>

        <#-- 未开始 -->
        <#else>
            <#if skuVO.actPt.preheatShowPrice == 1>
                <div class="verifyBox" style="width: 220px;height: 42px;opacity: 1;background: #00b377;margin: 0 auto;border-radius: 2px;">
            <#else >
                <div class="verifyBox" style="width: 220px;height: 42px;opacity: 1;background: #bfc5c3;margin: 0 auto;border-radius: 2px;">
            </#if>
                <#if skuVO.actPt.preheatShowPrice == 1>
                    <#if sptype_2??>
                    <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype_2}&spid=${spid_2}&sid=${sid_2}" target="_blank" title="${skuVO.showName}" style="overflow: auto;text-overflow: initial;white-space: initial">
                        <#else>
                        <a href="/search/skuDetail/${skuVO.id}.htm?sptype=${sptype}&spid=${spid}&sid=${sid}" target="_blank" title="${skuVO.showName}" style="overflow: auto;text-overflow: initial;white-space: initial">立即查看</a>
                        </#if>
                <#else >
                    <#if sptype_2??>
                <a href="javascript:;" title="${skuVO.showName}" style="overflow: auto;text-overflow: initial;white-space: initial;font-size: 14px">
                    <#else>
                    <a href="javascript:;" title="${skuVO.showName}" style="overflow: auto;text-overflow: initial;white-space: initial;background: ">
                        </#if>${skuVO.actPt.assembleStartTime?string('MM月dd日 HH:mm:ss')} 开团</a>
                </#if>
            </div>
        </#if>
    </#if>
</div>
<!--收藏 已收藏类名hasCollect  未收藏类名nopCollect-->
    <#--<#if skuVO.isOEM?? && skuVO.isOEM == 'true' && (skuVO.signStatus == '0' || skuVO.agreementEffective =='0')>-->
    <#--<#else >-->
        <#if skuVO.favoriteStatus == 1>
            <div class="w-collectZone hasCollect initial j-collectBtn" id="${skuVO.id}"
                 onclick="removeSC(${skuVO.id},event,this)">
                <div class="zone-1">
                    <div class="top top-1">
                        <span class="w-icon-normal icon-normal-collectEpt"></span>
                    </div>
                    <div class="top top-2">
                        <span class="w-icon-normal icon-normal-collectFull"></span>
                    </div>
                    <#--  <span class="sc-wenzhi">已收藏</span>  -->
                </div>
                <div class="zone-2">
                    <div class="bottom bottom-1">
                        <p class="textOne">收藏</p>
                    </div>
                    <div class="bottom bottom-2">
                        <p class="textTwo">已收藏</p>
                    </div>
                </div>
            </div>
        <#else >
            <div class="w-collectZone nopCollect initial j-collectBtn" id="${skuVO.id}"
                 onclick="addDTSX(${skuVO.id},event,this)">
                <div class="zone-1">
                    <div class="top top-1">
                        <span class="w-icon-normal icon-normal-collectEpt"></span>
                    </div>
                    <div class="top top-2">
                        <span class="w-icon-normal icon-normal-collectFull"></span>
                    </div>
                    <#--  <span class="sc-wenzhi">收藏</span>  -->
                </div>
                <div class="zone-2">
                    <div class="bottom bottom-1">
                        <p class="textOne">收藏</p>
                    </div>
                    <div class="bottom bottom-2">
                        <p class="textTwo">已收藏</p>
                    </div>
                </div>
            </div>
        </#if>
    <#--</#if>-->

<!--角标begin-->
<!--药狂欢角标 默认隐藏 去掉noshow显示-->
	<#--<#if skuVO.markerUrl?? && skuVO.markerUrl!=''>-->
    <#--<div class="yaokuanghuan-pos">-->
        <#--<img src="${productImageUrl}/${skuVO.markerUrl}" alt="">-->
    <#--</div>-->
    <#--</#if>-->
<#--<div class="jiaobiaobox">-->
	<#--<#if skuVO.promotionTag?? && skuVO.promotionTag!=''>-->
        <#--<div class="xiangou-pos">-->
            <#--<span>${skuVO.promotionTag }</span>-->
        <#--</div>-->
	<#--</#if>-->

    <#--<!--近期角标 默认隐藏 去掉noshow显示&ndash;&gt;-->
	<#--<#if skuVO.validity?? && skuVO.validity != ''>-->
        <#--<div class="jingqi-pos ">-->
            <#--<span>临期特价</span>-->
        <#--</div>-->
	<#--</#if>-->

    <#--<!--满减角标 默认隐藏 去掉noshow显示&ndash;&gt;-->
	<#--<#if skuVO.isBuyReward==1>-->
        <#--<div class="manjian-pos">-->
            <#--<span>满减</span>-->
        <#--</div>-->
	<#--</#if>-->

	<#--<#if skuVO.status==5>-->
        <#--<div class="manjian-pos">-->
            <#--<span>秒杀</span>-->
        <#--</div>-->
	<#--</#if>-->
<#--</div>-->
    <!--建议零售价或者建议统一价-->
    <#--<#if (merchant ? exists)>-->
        <#--<#if skuVO.isControl ==1 >-->
            <#--<#if skuVO.isPurchase>-->
                <#--<#if (skuVO.suggestPrice ?? ) && (skuVO.suggestPrice != '')>-->
                    <#--<div class="jylsj">-->
                        <#--<span>建议零售价：</span><span>￥${skuVO.suggestPrice}</span>-->
                    <#--</div>-->
                <#--</#if>-->
            <#--</#if>-->
        <#--<#else>-->
            <#--<#if (skuVO.suggestPrice ?? ) && (skuVO.suggestPrice != '')>-->
                <#--<div class="jylsj">-->
                    <#--<span>建议零售价：</span><span>￥${skuVO.suggestPrice}</span>-->
                <#--</div>-->
            <#--</#if>-->
        <#--</#if>-->
    <#--</#if>-->
<!--不参与返点提示-->
	<#if (skuVO.blackProductText)!>
    <div class="nofd">
	${skuVO.blackProductText}
    </div>
	</#if>
<!--end-->
</li>
<script>
    /**获取折后价**/
    var merchantId = $("#merchantId").val();
    // var branchCode = $("#branchCode").val();
    function getPrice(){
        var productList = $(".mrth-new li");
        var idList = '';
        for(var i = 0;i < productList.length;i++){
            if(i < productList.length-1){
                idList += $(productList[i]).find("#skuId").val() + ',';
            }else{
                idList += $(productList[i]).find("#skuId").val() + '';
            }
        }
        if(idList){
            var idListArr = idList.split(",");
            if(idListArr && idListArr.length > 0){
                $.ajax({
                    url: "/marketing/discount/satisfactoryInHandPrice",
                    type: "POST",
                    dataType: "json",
                    data: {
                        merchantId: merchantId,
                        skuIds: idListArr[idListArr.length - 1]
                        // branchCode: branchCode
                    },
                    success: function(result){
                        if(result){
                            if(result.data){
                                var priceList = result.data;
                                if(priceList && priceList.length>0){
                                    priceList.forEach(function(item,index){
                                        var isH = $(item).find('#preheatShowPrice').val();
                                        var price = $('#price-' + item.skuId).val();
                                        idListArr.forEach(function(item1,index1){
                                            if(item.skuId == item1 && item.price && !isNaN(price) && Number(price) > Number(item.price.split("￥")[1])){
                                                var str = item.price
                                                $(".zhehou-price-"+item1).html(str);
                                            }
                                        })
                                    })
                                }
                            }
                        }else{
                            $.alert(result.errorMsg);
                        }
                    }
                })
            }

        }

    }
    getPrice();
    function timer() {
        var list  = $('.timer_you');
        function ptTimeShow(show,that) {
		try{
			if(show == 'show'){
				requestAnimationFrame(function() {
					$(that).closest('#ptTimeShow').show();
				 });
				
			}else{
				requestAnimationFrame(function() {
					$(that).closest('#ptTimeShow').hide();
				 });
			}
		}catch(e){}
	}
        list.each(function (item) {
            (function (that) {
                var endTime = parseInt(that.attr('data-timer'));
                var ts = '';
                var obj_time = {};
                var clearT = '';
                var dd, hh, mm, ss;
                function s() {
                    dd = parseInt(endTime / 60 / 60 / 24, 10);
                    hh = parseInt(endTime / 60 / 60 % 24, 10);
                    mm = parseInt(endTime / 60 % 60, 10);
                    ss = parseInt(endTime % 60, 10);
                    obj_time.dd = dd < 10 ? ("0" + dd) : dd;   //天
                    obj_time.hh = hh < 10 ? ("0" + hh) : hh; //时
                    obj_time.mm = mm < 10 ? ("0" + mm) : mm; //分
                    obj_time.ss = ss < 10 ? ("0" + ss) : ss; //秒
                    if (dd > 0) {
                        // obj_time.hh = Number(hh) + Number(24);
                        that.find(".timer_d").html(obj_time.dd) ;
                        ptTimeShow("",that)
                    } else {
                        ptTimeShow("show",that)
                        that.find(".timer_d").css('display', 'none');
                        that.find(".daySpan").css('display', 'none');
                    }
                    that.find(".timer_h_you").html(obj_time.hh) ;
                    that.find(".timer_m_you").html(obj_time.mm);
                    that.find(".timer_s_you").html(obj_time.ss);
                    endTime = endTime - 1;
                    if(endTime < 0) {
                        clearTimeout(clearT);
                    } else {
                        clearT = setTimeout(s,1000);
                    }
                    
                }
                s();
                that.removeClass('timer_you');
            })($(this))
        })

            // setTimeout(function() {
            //     timer();
            // }, 1000);
    }
    timer();
</script>
<script>
//qt埋点
function action_list_product_btn_click_skuPinVo(el,event,index,name){
   try{
     if(window.companyShopBtnClick){
        window.companyShopBtnClick(el,event,index,name)
     }
   }  catch (e) {
     console.log(e)
   }
}
</script>
</#macro>
