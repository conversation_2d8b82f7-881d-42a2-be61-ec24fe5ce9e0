package com.xyy.ec.pc.cms.constants;

/**
 * CMS常量类
 *
 * <AUTHOR>
 */
public class CmsConstants {

    /**
     * 未开始拼团活动的展示ID字符串
     */
    public static final String EXHIBITION_ID_STR_GROUP_BUYING_NOT_START = "ZS_System_GroupBuyingNotStart";

    /**
     * 进行中的拼团活动的展示ID字符串
     */
    public static final String EXHIBITION_ID_STR_GROUP_BUYING_IN_PROGRESS = "ZS_System_PINTUAN";

    /**
     * 拼团商品分类的全部ID
     */
    public static final Long GROUP_BUYING_PRODUCT_CATEGORY_ID_ALL = -1L;

    /**
     * 品类商品流组件分类的全部ID
     */
    public static final Long CATEGORY_FLOW_CATEGORY_ID_ALL = -1L;

    /**
     * 菜单
     */
    public static final String MENU_URL_GROUP_BUYING_ACTIVITY = "/cms/activity/groupBuyingActivity";

    /**
     * 未开始秒杀活动的展示ID字符串
     */
    public static final String EXHIBITION_ID_STR_SECKILL_NOT_START = "ZS_System_SeckillNotStart";

    /**
     * 进行中的秒杀活动的展示ID字符串
     */
    public static final String EXHIBITION_ID_STR_SECKILL_IN_PROGRESS = "ZS_System_MIAOSHA";

    /**
     * 全国区域编码
     */
    public static final String GLOBAL_BRANCH_CODE = "XS000000";

    /**
     * 页面提示
     */
    public static final String MSG_ERROR = "网络异常";

    /**
     * 不存在时的PC首页版本
     */
    public static final Long PC_INDEX_ID_NOT_EXISTS = -1L;
}
