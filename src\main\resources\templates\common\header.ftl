<@header_data headerData>
<script type="text/javascript" src="/static/js/jquery.placeholder.min.js"></script>
<script type="text/javascript" src="/static/js/zhuge/zhugeio.js?t=${t_v}"></script>
 <#if model.crawlerSwitch==1>
<script type="text/javascript" src="/static/js/honeyJar.js?t=${t_v}"></script>
 </#if>
<div class="headerT">
    <!-- 判断是否是IE -->
    <div id="IETip"></div>
    <!--导航-->
    <div class="sui-navbar">
        <div class="shopListWrapper">
            <ul id="shopListBox"></ul>
            <div class="pagination"></div>
        </div>
        <div class="navbar-inner" id="header_nav">
<#--            <input type="hidden" id="merchantId" name="merchantId" value="${merchant.id}"/>-->
            <ul class="sui-nav nologin" id="loginUserInfo">
                <li class="header-li" scmType="other" scmdata="桌面快捷"><a href="http://upload.ybm100.com/ybm/pc/activitys/html/药帮忙.url" class="cjzmkj"><img src="/static/images/zmkj.png"  class="zmkj">桌面快捷</a></li>
                <li class="header-li" scmType="other" scmdata="收藏本站"><a href="javascript:void(0)" onclick="addCookie()" class="addCookie-btn" rel='sidebar'>收藏本站</a></li>
                <li class="header-li" scmType="other" scmdata="欢迎来到药帮忙！"><a href="javascript:void(0);" class="userno">欢迎来到药帮忙！</a></li>
                <li class="header-li" scmType="other" scmdata="登录"><a href="#" >请 <span class="speano"> 登录</span></a></li>
                <li class="header-li" scmType="other" scmdata="注册有礼"><a href="#" >注册有礼</a></li>
            </ul>
            <ul class="sui-nav pull-right">
                <li class="header-li"  scmType="home" scmdata="首页"><a href="/">首页</a></li>
                <li class="xiexian" >/</li>
                <li class="header-li" scmType="orderList" scmdata="我的订单"><a href="/merchant/center/order/index.htm">我的订单</a></li>
                <li class="xiexian">/</li>
                <li class="header-li" scmType="center" scmdata="用户中心" style="position: relative;"><a href="/merchant/center/index.htm">用户中心</a>
                    <span id="zizhi-liqi" style="display:none;position:absolute;left: 30px;top: 25px;z-index: 1;">
                        <a href="/merchant/center/licenseAudit/findLicenseCategoryInfo.htm"><img src="/static/images/zizhilinqi.png" alt="" width="161px" height="46px"></a></span>
<#--                    <span id="dianjiqugengxin" style="display:none; position: absolute;left: 130px;top: 32px;width: 100px;color: red;">-->
<#--                        <a style="color: red" href="/merchant/center/licenseAudit/findLicenseCategoryInfo.htm">点击去更新</a></span>-->
                    <span id="zizhi-guoqi" style="display:none;position:absolute;left: 30px;top: 25px;z-index: 1;">
                        <a href="/merchant/center/licenseAudit/findLicenseCategoryInfo.htm"><img src="/static/images/zizhiguoqi.png" alt="" width="161px" height="46px"></a></span>
<#--                    <span id="dianjiqugengxin" style="display:none; position: absolute;left: 130px;top: 32px;width: 100px;color: red;">-->
<#--                        <a style="color: red" href="/merchant/center/licenseAudit/findLicenseCategoryInfo.htm">点击去更新</a></span></li>-->
                <li class="xiexian">/</li>
                <li><a href="javascript:void(0);" class="userno header-li" scmdata="400-0505-111">400-0505-111</a></li>
                <li class="xiexian">/</li>
                <li class="header-li " scmType="custom" scmdata="在线客服" id="kefuEntry"><a href="javascript:callKf('','${merchant.id}' || '${merchantId}');">在线客服<img src="/static/images/paoHeader.png" alt="" style="width:44px;height:17px;position: relative;top: -1px;"/></a></li>
                <li id="kefuXiexian" class="xiexian">/</li>
                <li class="header-li" scmType="help" scmdata="帮助中心"><a href="/helpCenter/about.htm">帮助中心</a></li>
                <li class="xiexian">/</li>
                <li class="shoujima header-li" scmType="other" scmdata="手机药帮忙"><a href="/helpCenter/about.htm"><img src="/static/images/shouji.png"  class="shoujiico">手机药帮忙 <img src="/static/images/top-erweima.png" class="posimg"  /> </a></li>
            </ul>
        </div>
    </div>

</div>
<!--搜索栏-->
<div class="searcherBox 1" id="searchBox">
    <div class="col1 fl col1-augest col2-augest">
        <a href="/"><img src="/static/images/logo_0516.png" alt="药帮忙" /></a>
    </div>

    <div class="col2 fl">
        <div class="search"  >
            <div class="inputbox fl">
                <span id="clearInputbox">x</span>
                <input type="text" id="search" placeholder="药品名称/厂家名称/助记码" value="${keywordSearch}" autocomplete="off"/>
            </div>
            <div class="ss fl">
                <a href="javascript:void(0)" id="search-btn" class="searchBtnLqw">搜索</a>
            </div>
            <!--搜索下拉弹窗-->
            <ul class="searchUl" id="searchUl"></ul>
        </div>
        <div class="rmtag" id="hotWordDiv">
            <#list model.listHostSearch as hot>
                <span class="hotKeywordTrack" onclick="hotKeywordTrack(this,'${hot_index}','${hot.url}','${hot.keyword}')">
                    <#if hot.url?? && hot.url!=''>
                        <a href="/${hot.url}" target="_blank">${hot.keyword}</a>
                    <#else>
                        <a href="/search/skuInfo.htm?keyword=${hot.keyword}" target="_blank">${hot.keyword}</a>
                    </#if>
                </span>
            </#list>
        </div>
    </div>
    <div class="col-new3 fr">
        <div class="youitem">
            <div class="imgwarp"><img class="youitem-img" src="/static/images/youitem1.png"></div>
            <div class="imgtext">带票销售</div>
        </div>
        <div class="youitem">
            <div class="imgwarp"><img class="youitem-img" src="/static/images/youitem2.png"></div>
            <div class="imgtext">正品行货</div>
        </div>
        <div class="youitem">
            <div class="imgwarp"><img class="youitem-img" src="/static/images/youitem3.png"></div>
            <div class="imgtext">专业服务</div>
        </div>
        <div class="youitem spejg">
            <div class="imgwarp"><img class="youitem-img" src="/static/images/youitem4.png"></div>
            <div class="imgtext">轻松采购</div>
        </div>
    </div>
</div>

<!--商品分类栏-->
<div class="newwarp">
    <div class="goodsmain">
        <div class="leftbox fl">
            <div class="fltitle index-noshow-fenlei">
                <i class="sui-icon icon-pc-list"></i>全部分类
            </div>
        </div>
        <div class="rightbox fl">
            <ul class="topnav" id="topnav">
                <li class="index-noshow">
                    <a href="/">首页</a>
                </li>
                <li>
                    <a href="/search/skuInfoByCategory.htm?all=all" class="${styleClassa}" index-data="" customer-data="all" title-data="全部药品" target="_blank">全部药品</a>
                </li>
                <div id="diyGuide" name="diyGuide">
                    <li>
                        <a href="/activity/202/activePage.htm" class="${styleClass11}" target="_blank">高毛专区<img src="/static/images/hot.png" class="hot" ></a>
                    </li>
                    <li>
                        <a href="/activity/201/activePage.htm" class="${styleClass13}" target="_blank">黄金单品</a>
                    </li>
                    <li>
                        <a href="/activity/initActivity.htm?id=95&moduleCategoryId=149&version=1&activityType=events-20170901-xinpin" class="${styleClass3}" target="_blank">新品上架</a>
                    </li>
                    <li>
                        <a href="/activity/preferredBrand.htm" class="${styleClass15}" target="_blank">品牌推荐</a>
                    </li>
                </div>
                <!--<li>
                    <a href="https://pharmacycollege.ybm100.com/getAggregationView.htm" class="${styleClass2}" target="_blank">药学院</a>
                </li>-->
            </ul>

        </div>
        <style>
            .xUserName {
                width: 280px;
                color: #00dc82;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .xfubox .flbox{
                height:auto!important;
            }
            .hang-col1{
                margin-right: 10px!important;

            }
            .shopListWrapper {
                position: absolute;
                top: 36px;
                width: 100%;
                padding-bottom: 20px;
                background: #fff;
                z-index: 1100;
                display: none;
                border: 1px solid #EFEFEF;
            }
            #shopListBox {
                width: 1200px;
                margin: 0 auto;
                display: flex;
                flex-wrap: wrap;
                min-height: 150px;
            }
            .shopItem {
                width: 260px;
                height: 50px;
                line-height: 50px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space:nowrap;
                color: #333333;
                border-right: 1px solid #EFEFEF;
                margin-left: 20px;
                cursor: pointer;
            }
            .shopItem:hover {
                color: #00C675;
            }
            .shopItem:nth-of-type(4n) {
                border: none;
            }
            .noData {
                width: 100%;
                height: 342px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
            }
            .pagination {
                color: #999999;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 1200px;
                margin: 0 auto;
                margin-top: 65px;
            }
            .pageChange {
                width: 32px;
                height: 32px;
                background: #FFFFFF;
                border: 1px solid #E2E2E2;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 5px;
            }
            .moreIcon {
                width: 8px;
                height: 6px;
                transform: rotate(180deg);
            }
            .Slogan:hover {
                transform: translateY(-2px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .Slogan:active {
                transform: translateY(1px);
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
        </style>
        <!--悬浮类目-->
        <div class="xfubox" id="categoryTree">
            <#if (categorys?? && categorys?size >0)>
                <div class="flbox index-spe">
                    <ul class="newul">
                        <#list categorys as categoryTreeItem>
                            <li>
                                <a href="/search/skuInfoByCategory.htm?categoryFirstId=${categoryTreeItem.id}" class="lia1" target="_blank">
                                    <#if categoryTreeItem.icon?? && categoryTreeItem.icon!="">
                                        <img src="${categoryTreeItem.icon}" style="width:21px;height:21px;;float: left;margin-top: 16px;margin-right: 10px;" />
                                    <#else>
                                        <i class="<#if categoryTreeItem.id == 1>
                                        icon iconfont icon-zhongxichengyao
                                        <#elseif  categoryTreeItem.id == 225>
                                        iconfont icon-zhongxichengyao
                                        <#elseif  categoryTreeItem.id == 3>
                                        icon iconfont1 icon-zhongyao
                                        <#elseif  categoryTreeItem.id == 65>
                                        icon iconfont icon-jishengyongpin
                                        <#elseif  categoryTreeItem.id == 66>
                                        icon iconfont icon-xiaoduyongpin
                                        <#elseif  categoryTreeItem.id == 67>
                                        icon iconfont icon-gerenhuli
                                        <#elseif  categoryTreeItem.id == 68>
                                        icon iconfont icon-shipinbaojian
                                        <#elseif  categoryTreeItem.id == 69>
                                        icon iconfont icon-zhusheyongpin
                                        <#else>
                                        </#if>"></i>
                                    </#if>
                                    <span>${categoryTreeItem.name}</span>
                                </a>
                            </li>
                        </#list>
                    </ul>
                </div>
                <!--二级类目弹窗-->
                <div class="leimu-TC">
                    <ul class="two-box-ul">
                        <#list categorys as categoryTreeItemCon>
                            <#if (categoryTreeItemCon.children?? && categoryTreeItemCon.children?size >0)>
                                <!--${categoryTreeItemCon.name}-->
                                <#assign list_index="1" />
                                <li class="warp-li">
                                    <div class="hangbox">
                                        <#list categoryTreeItemCon.children as categoryTreeItemConChild>
                                            <div class="hang-col1"  style="float:<#if (list_index?number) % 2 ==0><#elseif (list_index?number) % 3 ==0>right<#else>left</#if>">
                                                <div class="com-title"><a href="/search/skuInfoByCategory.htm?categorySecondId=${categoryTreeItemConChild.id}" target="_blank">${categoryTreeItemConChild.name}</a></div>
                                                <#if (categoryTreeItemConChild.children?? && categoryTreeItemConChild.children?size >0)>
                                                    <div class="com-info">
                                                        <#list categoryTreeItemConChild.children as categoryTreeItemConChildCon>
                                                                <a href="/search/skuInfoByCategory.htm?categoryThirdId=${categoryTreeItemConChildCon.id}" target="_blank">${categoryTreeItemConChildCon.name}</a>
                                                        </#list>
                                                    </div>
                                                </#if>
                                            </div>
                                            <#if (list_index?number) % 3 ==0><div style="clear:both;"></div><div style="float:left;margin:20px;"></div></#if>
                                            <#assign list_index="${(list_index?number +1)}"/>
                                        </#list>
                                    </div>
                                </li>
                            <#else>
                                <li class="warp-li">
                                    <div class="hangbox">即将上架，敬请期待！
                                    </div>
                                </li>
                            </#if>
                        </#list>
                    </ul>
                </div>
            </#if>
        </div>
    </div>
</div>

<div class="kuanjie" id="kuanjie">
    <!--导航-->
    <div class="abs-warp">
        <!--采购单-->
        <a href="/merchant/center/cart/index.htm" class="tongyong cgdbox abs-warp-tab" scmdata="link-shoppingCart_text-采购单">
            <div class="r-topbox">
                <i class="icon iconfont icon-caigoudan"></i>

            </div>
            <div class="r-footbox cycle2 zuotc">采购单</div>
             <!--<span id="rigthCartNum" class="topp"></span>-->
             <span id="rigthCartNum"></span>
        </a>

        <!--收藏-->
        <a href="/merchant/center/collection/findAttention.htm" class="tongyong jdbox abs-warp-tab" scmdata="link-findAttention_text-收藏">
            <div class="r-topbox"><i class="icon iconfont icon-shoucang"></i></div>
            <!--<div class="r-footbox">收藏</div>-->
            <div class="zuotc">收藏</div>
        </a>

        <!--客服-->
        <a id="kefuFixed" href="javaScript:callKf('','${merchant.id}' || '${merchantId}');" class="tongyong jdbox abs-warp-tab" scmdata="link-custom_text-客服">
            <div class="r-topbox"><i class="icon iconfont icon-kefu"></i></div>
            <!--<div class="r-footbox">客服</div>-->
            <div class="zuotc">客服</div>
        </a>

        <!--意见反馈-->
        <a href="/feedback/indexFeedback.html" class="tongyong jdbox abs-warp-tab" scmdata="link-feedback_text-意见反馈">
            <div class="r-topbox"><i class="icon iconfont icon-yijianfankui"></i></div>
            <div class="zuotc">意见反馈</div>
        </a>

        <!--心愿单-->
        <#--<a href="/merchant/center/wish/index.htm?tab=1" class="tongyong jdbox">-->
            <#--<div class="r-topbox"><i class="icon iconfont icon-xinyuandan"></i></div>-->
            <#--<!--<div class="r-footbox">心愿单</div>&ndash;&gt;-->
            <#--<!--<div class="you-er"><img src="img/xinyuandan-new.png" alt="" /></div>&ndash;&gt;-->
            <#--<div class="zuotc">心愿单</div>-->
        <#--</a>-->
        <!--专属销售-->
        <a href="#" class="tongyong jdbox abs-warp-tab" onmousedown="zhuanshuxiaoshou()" scmdata="text-专属销售电话">
            <div class="r-topbox"><i class="icon iconfont1 icon-zhuanshuxiaoshou"></i></div>
            <div class="zuotc topbox-zsxs">
                <div class="img-boxshow">
                    <div class="img-box">
                        <img id="myImage" alt="Old Image">
                    </div>
                    <div style="color: #222;">
                        <img style="width: 14px;height: 14px;" src="/static/images/wx-login.png" alt="">手机微信扫码联系
                    </div>
                </div>
                <div class="counselor-info">
                    <div class="info-name"><img src="/static/images/counselor-name.png" alt="">专属业务员：<span id="zhuanshuxiaoshou"></span></div>
                    <div class="info-phone"><img src="/static/images/wx-phone.png" alt="">联系电话：<span id="zhuanshuPhoneNum"></span></div>
                </div>
            </div>
        </a>
        <div class="dingwei">
            <!--APP-->
            <a href="#" class="tongyong speapp jdbox app abs-warp-tab" scmdata="text-二维码">
                <div class="r-topbox"><i class="icon iconfont icon-shouji"></i></div>
            </a>

            <!--top-->
            <a href="javascript:void(0);" id="toTop" class="tongyong jdbox abs-warp-tab" scmdata="text-顶部">
                <div class="r-topbox"><i class="sui-icon sui-icon icon-touch-chevron-up"></i></div>
                <div class="zuotc">顶部</div>
            </a>
        </div>

    </div>
    <!--二维码动画-->
    <div class="erm-abs">
        <img src="/static/images/xzma.png" />
    </div>

</div>

</div>
</@header_data>

<!--监控错误脚本-->
<script type="text/javascript">(function (w) {w.frontjsConfig = {token: "11980861fa141558a1570073817dd8fe", behaviour: 15, optimisedForSPA: true, useHistory: true};w.frontjsTmpData = {r:[],e:[],l:[]};w.frontjsTmpCollector = function (ev) {(ev.message ? window.frontjsTmpData.e : window.frontjsTmpData.r).push([new Date().getTime(), ev])};w.FrontJS = {addEventListener: function (t, f) {w.frontjsTmpData.l.push([t, f]);return f;},removeEventListener: function (t, f) {for (var i = 0; i < w.frontjsTmpData.l.length; i++) {t === w.frontjsTmpData.l[i][0] && f === w.frontjsTmpData.l[i][1] && w.frontjsTmpData.l.splice(i, 1);}return f;}};w.document.addEventListener("error", w.frontjsTmpCollector, true);w.addEventListener("error", w.frontjsTmpCollector, true);w.addEventListener("load", function () {var n = w.document.createElement("script");n.src = "https://static.frontjs.com/dist/current/tracker.min.js"; w.document.body.appendChild(n);}, true);})(window);</script>

<script type="text/javascript">
    if ( !Array.prototype.forEach ) {
        Array.prototype.forEach = function forEach( callback, thisArg ) {
            var T, k;
            if ( this == null ) {
                throw new TypeError( "this is null or not defined" );
            }
            var O = Object(this);
            var len = O.length >>> 0;
            if ( typeof callback !== "function" ) {
                throw new TypeError( callback + " is not a function" );
            }
            if ( arguments.length > 1 ) {
                T = thisArg;
            }
            k = 0;
            while( k < len ) {
                var kValue;
                if ( k in O ) {
                    kValue = O[ k ];
                    callback.call( T, kValue, k, O );
                }
                k++;
            }
        };
    }
    var merchantio = '';
    var isSugFlag = false;
    //获取资质状态
    function getLicenseStatus() {
        $.ajax({
            url: "/merchant/center/license/validity",
            type: "POST",
            dataType: "json",
            async: false,
            success: function (result) {
                if (result.status == "success") {
                    if (result.data.validity == 1) { //过期
                        $("#zizhi-guoqi").css("display", "block");
                        // $("#dianjiqugengxin").css("display", "block");
                        // $("#licenseMsg").css("display", "block");
                        // console.log($("#licenseMsg"),$("#licenseMsg").find(".main-msg"))
                        // $("#licenseMsg").find(".main-msg").html('您存在资质/证件过期，为不影响您发货，请您在15天内更新')
                    } else if (result.data.validity == 2) { //临期
                        $("#zizhi-liqi").css("display", "block");
                        // $("#dianjiqugengxin").css("display", "block");
                        // $("#licenseMsg").css("display", "block");
                        // $("#licenseMsg").find(".main-msg").html('您存在资质/证件即将过期，为不影响您采购，请及时更新资质')
                    }
                }
            }
        });
    }


    function getLicenseStatusRemind(){
        $.ajax({
            url: "/merchant/center/license/validity/remind",
            type: "POST",
            dataType: "json",
            data: {
                type: location.pathname === '/' ? 'pcIndex' : null,
            },
            async: false,
            success: function (result) {
                if(result.status == "success") {
                    if(result.data && result.data.msg) {
                        if(location.pathname === '/'){
                            layer.confirm(result.data.msg, {title: result.data.title || '资质过期提醒', btn: ['去更新']}, function (index) {
                                window.open('/merchant/center/licenseAudit/findLicenseCategoryInfo.htm');
                                layer.close(index);
                            }, function (index) {
                                layer.close(index);
                            });
                        }else {
                            $("#licenseMsg").css("display","block");
                            // console.log($("#licenseMsg"),$("#licenseMsg").find(".main-msg"))
                            $("#licenseMsg").find(".main-msg").html(result.data.msg)
                        }

                    }
                }
            }
        });
    }
    // 判断是否IE
    function IEVersion() {
        var isIEBrowser = false;
        var ua = window.navigator.userAgent;
        var msie = ua.indexOf("MSIE ");
        var trident = ua.indexOf("Trident/");

        if (msie > 0 || trident > 0) {
            isIEBrowser = true;
        } else {
            isIEBrowser = false;
        }
        // var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
        // var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
        // var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
        // var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
        // if(isIE) {
        //     isIEBrowser = true;
        // } else if(isEdge) {
        //     isIEBrowser = true;
        // } else if(isIE11) {
        //     isIEBrowser = true;
        // }else{
        //     isIEBrowser = false;//不是ie浏览器
        // }
        if (isIEBrowser) {
            var IEHtml = '<div style="width: 1200px;height: 35px;margin: 0 auto;padding: 0 20px;line-height: 35px;background: #FFD793;">'
               + '<span style="font-size: 16px;color: #505050;">您当前使用的IE浏览器存在兼容性问题，无法体验完整服务，请点击此处查看解决方法</span>'
               + '<a href="/static/word/浏览器兼容方法说明.docx" style="font-size: 16px;color: #505050;margin-left: 20px;text-decoration:underline;cursor:pointer">下载</a>'
               + '</div>';

            $("#IETip").html(IEHtml);
        }
    }
    $(function() {
        IEVersion();
        $.ajax({
            url: "/header_data.json",
            type: "GET",
            async:true,
            cache: false,
            dataType: "json",
            success: function(data) {
                var province = data.header_data.province;
                var branchCode = data.header_data.branchCode;
                if(window.getBranchCode){
                    getBranchCode(branchCode);
                }
                if(data.header_data.merchant == null) {
					$('#kefuEntry').hide();
                    $('#kefuXiexian').hide();
                    $('#kefuFixed').hide();
				}
                // if(branchCode){
                //     if(branchCode == 'XS500000'){
                //         var txt =  '<div class="row2 common">';
                //         txt+='<a href="/activity/businesslicense/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91500107MA5YQ1DN2N</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/drugbusiness/ybmActivity.htm" target="_blank">药品经营许可证证号：渝*********</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/qualitycertificate/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：CQ09-Aa-20180724</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/yiliaoqixie/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：渝08食药监械经营备20180081号</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/foodbusiness/ybmActivity.htm" target="_blank">食品经营许可证编号：*********0108843</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/internetdrug/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：（渝）-非经营性-2018-0020</a>';
                //         txt+='</div>';
                //         $('#chongqingzgz').empty();
                //         $('#chongqingzgz').append(txt);
                //         $('.foot-yl').hide();
                //         $('.foot-yl').prev('span').hide();
                //         $('#beiantext').html('鄂ICP备16004053号-1');
                //     }else if(branchCode == 'XS420000'){
                //         var txt =  '<div class="row2 common">';
                //         txt+='<a href="/activity/wh_yingyezhizhao/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91420112MA4KLGHW01</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/wh_yaopinjingyingxukezheng/ybmActivity.htm" target="_blank">药品经营许可证证号：鄂AA0270434</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/wh_yaopinjingyingzhiliangguanli/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：HB01-Aa-20160043</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/wh_yiliaoqixie/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：鄂汉食药监械经营备2017HP004号</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/wh_shipinjingyingxukezheng/ybmActivity.htm" target="_blank">食品经营许可证编号：JY14201180022698</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/wh_hulianwangyaopinxinxizige/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(鄂)-非营利性-2016—0009</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/wh_yiliaoqixiexukezheng/ybmActivity.htm" target="_blank">医疗器械经营许可证编号：鄂汉食药监械经营许20161115号</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/wh_hlwyaopinjiaoyifuwuzigezheng/ybmActivity.htm" target="_blank">互联网药品交易服务资格证书编号：鄂B20160004</a>';
                //         txt+='</div>';
                //         $('#chongqingzgz').empty();
                //         $('#chongqingzgz').html(txt);
                //         $('#beiantext').html('鄂ICP备16004053号-1');
                //     }else if(branchCode == 'XS430000'){
                //         var txt =  '<div class="row2 common">';
                //         txt+='<a href="/activity/cs_yingyezhizhaofuben/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91430105MA4PD27G9N</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/cs_yaopinjingyingxukezheng/ybmActivity.htm" target="_blank">药品经营许可证证号：湘AA7310512</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/cs_yaopinjingyingzhiliangguanli/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：HN01-Aa-20180059</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/cs_yiliaoqixie/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：湘长食药监械经营备2018G2048号</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/cs_shipinjingyingxukezheng/ybmActivity.htm" target="_blank">食品经营许可证编号：JY14301020345268</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/cs_hulianwangyaopinxinxizige/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：（湘）—经营性—2018—0017</a>';
                //         txt+='</div>';
                //         $('#chongqingzgz').empty();
                //         $('#chongqingzgz').html(txt);
                //         $('.foot-yl').hide();
                //         $('.foot-yl').prev('span').hide();
                //         $('#beiantext').html('鄂ICP备16004053号-1');
                //     }else if(branchCode == 'XS330000'){
                //         var txt =  '<div class="row2 common">';
                //         txt+='<a href="/activity/hz_yingyezhizhao/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91330110MA2CCJE32Y</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/hz_yaopinjingyingxukezheng/ybmActivity.htm" target="_blank">药品经营许可证证号：浙AA5710173</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/hz_yaopinjingyingzhiliangguanli/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：A-ZJ18-022</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/hz_yiliaoqixie/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：浙杭食药监械经营备20183346号</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/hz_shipinjingyingxukezheng/ybmActivity.htm" target="_blank">食品经营许可证编号：JY13301810071232</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/hz_hulianwangyaopinxinxizige/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(浙)-经营性-2018-0043</a>';
                //         txt+='</div>';
                //         $('#chongqingzgz').empty();
                //         $('#chongqingzgz').html(txt);
                //         $('.foot-yl').hide();
                //         $('.foot-yl').prev('span').hide();
                //         $('#beiantext').html('鄂ICP备16004053号-1');
                //     }else if(branchCode == 'XS350000'){
                //         var txt =  '<div class="row2 common">';
                //         txt+='<a href="/activity/xm_yingyezhizhao/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91350200MA3287U59C</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/xm_yaopinjingyingxukezheng/ybmActivity.htm" target="_blank">药品经营许可证证号：闽AA5920342</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/xm_yaopinjingyingzhiliangguanli/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：FJ02-Aa-20190002</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/xm_yiliaoqixie/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：闽夏食药监械经营备20193008号</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/xm_shipinjingyingxukezheng/ybmActivity.htm" target="_blank">食品经营许可证编号：JY13502110200418</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/xm_hulianwangyaopinxinxizige/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(闽)-非经营性-2019—0011</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/xm_yiliaoqixiexukezheng/ybmActivity.htm" target="_blank">医疗器械经营许可证编号：闽夏食药监械经营许20193005号</a>';
                //         txt+='</div>';
                //         $('#chongqingzgz').empty();
                //         $('#chongqingzgz').html(txt);
                //         // $('.foot-yl').hide();
                //         // $('.foot-yl').prev('span').hide();
                //         $('.foot-yl').attr('href','/activity/xm_yiliaoqixiexukezheng/ybmActivity.htm');
                //         $('#beiantext').html('鄂ICP备16004053号-1');
                //     }else if(branchCode == 'XS370000'){
                //         var txt =  '<div class="row2 common">';
                //         txt+='<a href="/activity/businesslicense_shandong/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91370102780630133F</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/drugbusiness_shandong/ybmActivity.htm" target="_blank">药品经营许可证证号：鲁*********</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/qualitycertificate_shandong/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：SD01-Aa-20190015</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/internetdrug_shandong/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(鲁)-经营性-2019-0006</a>';
                //         txt+='</div>';
                //         $('#chongqingzgz').empty();
                //         $('#chongqingzgz').html(txt);
                //         $('.foot-yl').hide();
                //         $('.foot-yl').prev('span').hide();
                //         $('#beiantext').html('鄂ICP备16004053号-1');
                //     }else if(branchCode == 'XS140001'){
                //         var txt =  '<div class="row2 common">';
                //         txt+='<a href="/activity/businesslicense_shanxi/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91149900MA0KLWLK4K</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/drugbusiness_shanxi/ybmActivity.htm" target="_blank">药品经营许可证证号：晋*********</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/qualitycertificate_shanxi/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：AA-SX-0183</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/ty_hulianwangyaopinxinxizige/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(晋)-非经营性-2019-0021</a>';
                //         txt+='</div>';
                //         $('#chongqingzgz').empty();
                //         $('#chongqingzgz').html(txt);
                //         $('.foot-yl').hide();
                //         $('.foot-yl').prev('span').hide();
                //         $('#beiantext').html('鄂ICP备16004053号-1');
                //     }else if(branchCode == 'XS360000'){
                //         var txt =  '<div class="row2 common">';
                //         txt+='<a href="/activity/businesslicense_jiangxi/ybmActivity.htm" target="_blank">营业执照统一社会信用代码：91360106MA38ANUN6M</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/drugbusiness_jiangxi/ybmActivity.htm" target="_blank">药品经营许可证证号：赣*********</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/qualitycertificate_jiangxi/ybmActivity.htm" target="_blank">药品经营质量管理规范认证证书编号：A-JX19-40N</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/medicalrecord_jiangxi/ybmActivity.htm" target="_blank">第二类医疗器械经营备案号：赣南械经营备20190007号</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/foodbusiness_jiangxi/ybmActivity.htm" target="_blank">食品经营许可证编号：JY13601210072719</a>';
                //         txt+='<span>|</span>';
                //         txt+='<a href="/activity/internetdrug_jiangxi/ybmActivity.htm" target="_blank">互联网药品信息服务资格证书编号：(赣)-非经营性-2019-0017</a>';
                //         txt+='</div>';
                //         txt+='<div class="row2 common">';
                //         txt+='<a href="/activity/medicalmanagement_jiangxi/ybmActivity.htm" target="_blank">医疗器械经营许可证编号：赣南械经营许20190029号</a>';
                //         txt+='</div>';
                //         $('#chongqingzgz').empty();
                //         $('#chongqingzgz').html(txt);
                //         // $('.foot-yl').hide();
                //         // $('.foot-yl').prev('span').hide();
                //         $('.foot-yl').attr('href','/activity/medicalmanagement_jiangxi/ybmActivity.htm');
                //         $('#beiantext').html('鄂ICP备16004053号-1');
                //     }else {
                //         $('#chongqingzgz').empty();
                //         $('#beiantext').html('鄂ICP备16004053号-1');
                //     }
                // }
                //
                // $('#footer .wangjian').html('<div><a href="http://whgswj.whhd.gov.cn:8089/whwjww/indexquery/indexqueryAction!dizview.dhtml?chr_id=8367f0ac6afc7ae374ae15205211dccc&amp;bus_ent_id=442010000000579583&amp;bus_ent_chr_id=43e1bcfe52714ec1b8397dd57d9e4f8c" target="_blank"><img src="http://whgswj.whhd.gov.cn:8089/whwjww/images/govIcon.gif" width="100" height="130" title="武汉网监电子标识" alt="武汉网监电子标识" border="0"></a></div>');

                if(data.header_data) {
                    if (data.header_data.listHostSearch && data.header_data.listHostSearch.length > 0) {
                        var list = data.header_data.listHostSearch;
                        for (var i in list) {
                            var keyword = list[i].keyword;
                            var url = list[i].url;
                            if (url) {
                                $('#hotWordDiv').append('<span><a href="/'+url+'" target="_blank">'+keyword+'</a></span> ');
                            } else {
                                var a = '<span><a href="/search/skuInfo.htm?keyword='+keyword+'" target="_blank">'+keyword+'</a></span>';
                                $("#hotWordDiv").append(a);

                            }
                        }
                    }
                }
                var keyword = $("#search").val().trim()
                //if ($("#searchBox").css("display") != "none") {
                //    $("#hotWordDiv").find('span a').each(function(index) {
                //        $(this).attr("a-index", index);
                //        window.AnalysysAgent.track('pc_page_top_hot_word_exposure', {
                //            jgspid: '151',
                //            key_word: keyword,
                //            rank: index * 1 + 1,
                //            click_name: $(this).text(),
                //            click_link: location.origin + this.getAttribute("href")
                //        });
                //    })
                //}
                merchantio = data.header_data.merchant;
                window.merchantIdFromHeader = merchantio ? merchantio.id : 0;
                if(data.header_data.merchant != null && data.header_data.merchant.id != null){
                    $("#loginUserInfo").removeClass("sui-nav nologin");
                    $("#loginUserInfo").addClass("sui-nav");
                    var html ='<li class="nav-province header-li" scmType="other" scmdata='+province+'> <a href="javascript:void(0)" class="field"><span class="sui-icon icon-touch-location-sign"></span><span class="province">'+province+'</span></a> </li><li class="header-li" scmType="other" scmdata="桌面快捷"><a href="http://upload.ybm100.com/ybm/pc/activitys/html/药帮忙.url" class="cjzmkj"><img src="/static/images/zmkj.png"  class="zmkj">桌面快捷</a></li>'
                    +'<li class="header-li" scmType="other" scmdata="收藏本站"><a href="javascript:void(0)" onclick="addCookie()" class="addCookie-btn" rel="sidebar">收藏本站</a></li>'
                    +'<li class="header-li" scmType="other" scmdata='+data.header_data.merchant.realName+"！欢迎您！"+'><a href="javascript:void(0);" class="user xUserName" onclick="showShopList()">'+data.header_data.merchant.realName+'！欢迎您！<img src="/static/images/more.png" class="moreIcon" /></a></li>'
                    +'<li class="header-li" scmType="other" scmdata="退出"><a href="/login/logout.htm" class="spea">退出</a></li>';

                    $("#loginUserInfo").html(html);
                    if(data.header_data.merchantCartCount>0){
                        $("#cartNumberLi").addClass("cycle");
                        $("#cartNumberLi").html(data.header_data.merchantCartCount);
                        $("#cartNumberDiv").addClass("topp");
                        $("#cartNumberDiv").html(data.header_data.merchantCartCount);
                        $("#rigthCartNum").addClass("topp");
                        $("#rigthCartNum").removeClass("noshow");
                        $("#rigthCartNum").addClass("cycle2");
                        $("#rigthCartNum").html(data.header_data.merchantCartCount);
                        $("#cartNumberDiv").removeClass("noshow");
                        $("#firstCartNumberDiv").removeClass("noshow");
                        $("#firstCartNumberDiv").addClass("cycle2");
                        $("#firstCartNumberDiv").addClass("topp");
                        $("#firstCartNumberDiv").html(data.header_data.merchantCartCount);
                    }else{
                        $("#cartNumberLi").html("");
                        $("#cartNumberDiv").html("");
                        $("#cartNumberDiv").text("");
                        $("#cartNumberDiv").addClass("noshow");
                        $("#rigthCartNum").removeClass("noshow");
                        $("#rigthCartNum").removeClass("topp");
                        $("#rigthCartNum").addClass("cycle2 noshow");
                        $("#rigthCartNum").html("");
                        $("#firstCartNumberDiv").html("");
                        $("#firstCartNumberDiv").removeClass("topp");
                    }
                    //zhugeio();
                    //获取资质状态
                    getLicenseStatus();
                    getLicenseStatusRemind();
                }else{
                	/* var html =  "<li class='nav-province'>"
                    +"<a href='javascript:void(0)' class='field'>"
                    +"<span class='sui-icon icon-touch-location-sign'></span>"
                    +"<span class='province'></span><span class='sui-icon icon-tb-unfold'></span></a>"
                    +"<div class='field-show'><ul>";
                    var provinceList = data.header_data.provinceList;
                    for ( var element in provinceList) {
                    	html+="<li code='XS"+provinceList[element].areaCode+"'>"+provinceList[element].areaName+"</li>";
                    }
                    html+="</ul></div></li>" */
                    var html = '<li class="nav-province" scmType="other" scmdata='+province+'> <a href="javascript:void(0)" class="field"><span class="sui-icon icon-touch-location-sign"></span><span class="province">'+province+'</span></a> </li><li><a href="http://upload.ybm100.com/ybm/pc/activitys/html/药帮忙.url" class="cjzmkj"><img src="/static/images/zmkj.png"  class="zmkj">桌面快捷</a></li>'
                    +'<li scmType="other" scmdata="收藏本站"><a href="javascript:void(0)" onclick="addCookie()" class="addCookie-btn" rel="sidebar">收藏本站</a></li>'
                    +'<li scmType="other" scmdata="欢迎来到药帮忙"><a href="javascript:void(0);" class="userno">欢迎来到药帮忙！</a></li>'
                    +'<li scmType="other" scmdata="登录"><a href="/login/login.htm" >请 <span class="speano"> 登录</span></a></li>'
                    +'<li scmType="other" scmdata="注册有礼"><a href="/newstatic/#/register/index" >注册有礼</a></li>';
                    // +'<li><a href="/login/register.htm" >注册有礼</a></li>';
                    $("#loginUserInfo").html(html);
                    $("#cartNumberLi").removeClass("cycle");
                    $("#cartNumberLi").html("");
                    $("#cartNumberDiv").html("");
                    $("#cartNumberDiv").text("");
                    $("#cartNumberDiv").addClass("noshow");
                    $("#rigthCartNum").removeClass("topp");
                    $("#rigthCartNum").html("");
                    $("#firstCartNumberDiv").html("");

                    $(".nologin .nav-province").hover(function(){
                		$(".field-show").show();
                		$(this).find(".icon-tb-unfold").attr("class","sui-icon icon-tb-fold");
                	},function(){
                		$(".field-show").hide();
                		$(this).find(".icon-tb-fold").attr("class","sui-icon icon-tb-unfold");
                	});
                	$(".field-show li").click(function(){
                		var pro = $(this).text();
                		$(".nav-province .province").text(pro);
                		setCookie("XSESSION_CODE",$(this).attr("code"),24*7,"/");
                		location.reload();
                	});
                    //获取当前用户所在省份
                	var sessionCode = getCookieValue("XSESSION_CODE");
                	if(!sessionCode){
                		$.ajax({
                            url: "http://api.map.baidu.com/location/ip?ak=SbCKuq9uoYxlmUwvidfcGi97csggOaaY",
                            type: "GET",
                            async: false,
                            dataType: "jsonp",
                            success: function(data) {
                            	var sessionProvince = data.content.address_detail.province;
                            	switch (sessionProvince) {
            					/* case "重庆市":
            						sessionCode = "XS500000";
            						break;
            					case "安徽省":
            						sessionCode = "XS340000";
            						break;
            					case "浙江省":
            						sessionCode = "XS330000";
            						break; */
            					default:
            						sessionCode = "XS420000";
            						break;
            					}
                            	//$(".nav-province .province").text($(".field-show ul li[code='"+sessionCode+"']").text());
                            	setCookie("XSESSION_CODE",sessionCode,24*7,"/");
                            }
                		});
                	}else{
                		setCookie("XSESSION_CODE","XS420000",24*7,"/");
                		//$(".nav-province .province").text($(".field-show ul li[code='"+sessionCode+"']").text());
                	}
                }
                if(window.page_component_exposure&&window.addScme){
                    setTimeout(function(){
                         $(".header-li").each(function(index,item){
                            window.addScme(item,1)
                        })
                       setTimeout(function(){
                         window.page_component_exposure()
                       },500)
                    },1000)
                }               
            }
        });
        /* 解决前端兼容性问题 */
        $("#topnav > li a").each(function(index, item) {
            if (index == 1) {
                $(item).attr("index-data", window.isIndexV2 ? 1 : 2);
            }

        })
        if(window.btoa && window.location){
            $.ajax({
                url: "/header_guide.json?thisPath=" + window.btoa(window.location.pathname + window.location.search),
                type: "GET",
                async: true,
                cache: false,
                dataType: "json",
                success: function(data) {
                    if(data.status == "success" && data.header_guide.headerGuideList && data.header_guide.headerGuideList.length>0){
                        $("#diyGuide").html("");
                        var headerGuideList = data.header_guide.headerGuideList;
                        var html ='';
                        for(i in headerGuideList){
                            var headerGuide = headerGuideList[i];
                            html += "<li>";
                            html += "<a href="+headerGuide.url;
                            html += " index-data=" + (i * 1 + (window.isIndexV2 ? 2 : 3));
                            html += " title-data=" + headerGuide.title;
                            if (window.isIndexV2) {
                                html += " activity-data=" + window.getId(headerGuide.url)
                            }
                            html += " customer-data=" + (headerGuide.customerGroupId == -1 ? 'all' : headerGuide.customerGroupId);
                            if(headerGuide.className){
                                html += " class='"+headerGuide.className+"'"
                            }
                            html +=" target='_blank'>"+headerGuide.title;
                            if(headerGuide.icon && headerGuide.icon!=''){
                                html+= "<img src='/static"+headerGuide.icon+"' class='hot'>"
                            }
                            html += "</a>";
                            html += "</li>";
                        }
                        html += '<li>';
                        html += '<a href="/pc/recommend/v3/oneClickRestock.htm"';
                        html += ' index-data="' + (headerGuideList.length + (window.isIndexV2 ? 2 : 3)) + '"';
                        html += ' title-data="一键补货"';
                        html += ' customer-data="all"';
                        html += ' target="_blank">';
                        html += '一键补货';
                        html += '</a>';
                        html += '</li>';
                        $("#diyGuide").append(html);
                        /*导航添加底部横线*/
                        $('.topnav li').hover(function(){$(this).addClass("hovercur").siblings().removeClass("hovercur")},function(){$(this).removeClass("hovercur")});
                        $('.topnav li a').click(function(){
                            //lwq的埋点，666
                            var index = $(this).attr("index-data");
                            var title = $(this).attr("title-data");
                            var groupId = $(this).attr("customer-data");
                            if(window.isIndexV2){
                                let spm_cnt = "1_4"
                                    spm_cnt += ".home_"+$("#pageId").val()+"-"+$("#version").val()+"_0"
                                    spm_cnt += ".newwarp@" + window.getPosition(3)
                                    spm_cnt += ".btn@" + index
                                    spm_cnt += "."+window.getSpmE();
                                let scm_cnt = "admin.0."+ groupId +"_0.";
                                    if ($(this).attr("activity-data") && $(this).attr("activity-data") != '0') {
                                        scm_cnt += "h5-" + $(this).attr("activity-data") + "_";
                                    }
                                    if ($(this).attr('href').includes('shopList')) {
                                        scm_cnt += "link-shopList_";
                                    }
                                    scm_cnt += "text-" + title +"."+ window.addScmeV2(1);
                                aplus_queue.push({
                                    'action': 'aplus.record',
                                    'arguments': ['action_sub_module_click', 'CLK', {
                                        'spm_cnt': spm_cnt,
                                        'scm_cnt': scm_cnt,
                       
                                    }]
                                });
                            }
                        });
                    }
                }
            });
        }
        let navEl = document.getElementById("topnav");
        const observer = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
            if (entry.isIntersecting&&window.isIndexV2) {
                observer.unobserve(navEl);
                let spm_cnt = '1_4';
                spm_cnt += ".home_"+$("#pageId").val()+"-"+$("#version").val()+"_0";
                spm_cnt += ".newwarp@" + window.getPosition(3);
                spm_cnt += ".0";
                spm_cnt += "." + window.getSpmE();
                let data = {
                    spm_cnt: spm_cnt,

                }
                aplus_queue.push({
                    'action': 'aplus.record',
                    'arguments': ['page_component_exposure', 'EXP', data]
                });
                //binding.value(el,true); // 元素可见时，调用传入的回调函数
            }
            });
        });
        observer.observe(navEl);
        zhugeio();
    });
    
    //诸葛IO埋点
    function zhugeio() {
        //console.log('common-header', merchantio)
        if(merchantio){
            var channelStr = '';
            var merchatChannelList = merchantio.channelList;
            if (merchatChannelList && Array.isArray(merchatChannelList)) {
                var channelCodeNameMap = {'1':'B2B','2':'宜块钱'};
                var channelCodeName;
                for (var i = 0; i < merchatChannelList.length; i ++) {
                    channelCodeName = channelCodeNameMap[merchatChannelList[i]] || '';
                    if (channelCodeName) {
                        channelStr += channelCodeName + ',';
                    }
                }
                if (channelStr && channelStr.length > 0) {
                    channelStr = channelStr.substring(0, channelStr.length-1);
                }
            }
            // zhuge.setSuperProperty({
            //     'channelCode': channelStr
            // });
            // zhuge.identify(merchantio.id,{
            //     name:merchantio.nickname,
            //     loginName :merchantio.loginName,
            //     phoneNum : merchantio.mobile,
            //     nickname :merchantio.nickname,
            //     realName :merchantio.realName,
            //     registeredDate:merchantio.createTime,
            //     lastLoginTime:merchantio.lastLoginTime,
            //     businessType:merchantio.businessType,
            //     businessTypeName:merchantio.businessTypeName,
            //     provinceId:merchantio.provinceCode,
            //     provinceName:merchantio.province,
            //     cityId:merchantio.cityCode,
            //     cityName:merchantio.city,
            //     districtId:merchantio.areaCode,
            //     districtName:merchantio.district,
            //     address:merchantio.address,
            //     channelCode:channelStr
            // });
            webSdk.setSuperProperty({
                'channelCode': channelStr
            });
            webSdk.identify(merchantio.id,{
                name:merchantio.nickname,
                loginName :merchantio.loginName,
                phoneNum : merchantio.mobile,
                nickname :merchantio.nickname,
                realName :merchantio.realName,
                registeredDate:merchantio.createTime,
                lastLoginTime:merchantio.lastLoginTime,
                businessType:merchantio.businessType,
                businessTypeName:merchantio.businessTypeName,
                provinceId:merchantio.provinceCode,
                provinceName:merchantio.province,
                cityId:merchantio.cityCode,
                cityName:merchantio.city,
                districtId:merchantio.areaCode,
                districtName:merchantio.district,
                address:merchantio.address,
                channelCode:channelStr
            });
            window.postMessage({
                message: 'webSdkIdentifyDown',
            }, '*');
        }else {
            //游客
            // zhuge.identify('0',{
            //     name:"游客"
            // });
            webSdk.identify('0',{
                name:"游客"
            });
        }
        $(".sui-navbar").find("li").find("a").click(function () {
            var text = this.innerText;
            if (text){
                let hrefUrl = "";
                if (this.getAttribute("href")) {
                    hrefUrl = location.host + this.getAttribute("href");
                }
                kuanjie(text,'pc_top_click');
                //jgTrackMethods(text,'pc_action_top_navigation_click', hrefUrl)
            }
        });
        $("#topnav").find("li").find("a").click(function() {
            var link = $(this);
            // zhuge.track('pc_Navigation_Menu', {
            //             'navigationName': $(link).text()
            //         },
            //         function() {
            //             location.href = $(link).attr('href'); //继续跳转到目标页面
            //         });
            webSdk.track('pc_Navigation_Menu', {
                        'navigationName': $(link).text()
                    },
                    function() {
                        location.href = $(link).attr('href'); //继续跳转到目标页面
                    });
            return false;
        });
        $(".xfubox").find("ul").find("li").find("a").click(function() {
            var link = $(this);
            // zhuge.track('pc_Navigation_Drugclassification', {
            //             'className': $(link).text()
            //         },
            //         function() {
            //             location.href = $(link).attr('href'); //继续跳转到目标页面
            //         });
            webSdk.track('pc_Navigation_Drugclassification', {
                        'className': $(link).text()
                    },
                    function() {
                        location.href = $(link).attr('href'); //继续跳转到目标页面
                    });
            return false;
        });
        var keyword = $("#search").val().trim()

        $("#hotWordDiv").find("a").click(function (event) {
            event.preventDefault();
            console.log(this.getAttribute("href"), "href111");
            var name = this.text;
            var ind = $(this).attr('a-index')
            webSdk.track('pc_action_Search', {
                'searchTerms' : name,
                'source' : 3 //(1:输入2:历史3:推荐4:联想)
            });
            sessionStorage.setItem("jgspid", "151");
            
            window.open(this.getAttribute("href"));
        });
        $(".kuanjie").find(".cycle2").click(function () {
            var text = this.innerText;
            kuanjie(text,'pc_right_kuanjie');
        });
        $(".kuanjie").find("a").click(function () {
            var text = $(this).find(".zuotc").text();
            if (text){
                let hrefUrl = location.host + this.getAttribute("href") || "";
                kuanjie(text,'pc_right_kuanjie');
            }
        });
    }

    function kuanjie(text,title) {
        if(merchantio){
            webSdk.track(title,{
                name:merchantio.nickname,
                loginName :merchantio.loginName,
                phoneNum : merchantio.mobile,
                nickname :merchantio.nickname,
                realName :merchantio.realName,
                businessTypeName:merchantio.businessTypeName,
                action : text
            });
        }else {
            //游客1
            webSdk.track(title,{
                name:"游客",
                action : text
            });
        }
    }

    function jgTrackMethods(text, title, hrefUrl) {
    }
    
    
    // 热词埋点
    function hotKeywordTrack(dom,index,url,keyword) {
        // var merchantId = $('#merchantId').val();
        webSdk.track('action_Search', {
            sdk: 'sg-js',
            sptype: 1,
            spid: 1, // 1-大搜，2-店铺搜索，3-专区搜索
            sug: 'hotword_' + index,
            keyword: $("#search").val().trim(),
        });
        if(window.actionSearchClick){
            window.actionSearchClick(dom,index,url,keyword);
        }
    }
    String.prototype.ltrim = function(){
        return this.replace(/^\s+/, '');
    };
    String.prototype.rtrim = function (){
        return this.replace(/\s+$/, '');
    };
    String.prototype.trim = function(){
        return this.ltrim().rtrim();
    };
    // 搜索匹配和搜索历史记录展示的逻辑
    var _search_type = null;
    $(function () {
        var int = 0;
        //下拉控制字段
        var is_hide = true;
        //搜索处理
        var search_data = [];
        function replaceParamVal(paramName,replaceWith) {
            var oUrl = this.location.href.toString();
            var re=eval('/('+ paramName+')([^&]*)/gi');
            var nUrl = oUrl.replace(re,paramName+'='+replaceWith);
            this.location = nUrl;
            window.location.href=nUrl
        }
        function searchProduct() {
            var name = $.trim($("#search").val());
            var shopUrl = $("#search").attr('shopUrl')
            var merchantId = $('#merchantId').val();
            var sug = [];
            $('#searchUl').find('li a span').each(function() {
                sug.push($(this).text());
            });
            webSdk.track('action_Search', {
                user_id: merchantId,
                sdk: 'sg-js',
                sptype: 1,
                spid: 1, // 1-大搜，2-店铺搜索，3-专区搜索
                wq: name,
                keyword: name,
                sug: '', // 如果用户未点击sug则此参数为空
                sg1: sug[0] || '',
                sg2: sug[1] || '',
                sg3: sug[2] || '',
                sg4: sug[3] || '',
                sg5: sug[4] || '',
                pkw: getUrlParam('keyword'),
            });
            

            $("#search").attr('shopUrl', '')
            if (shopUrl) {
                let isJgspid = '150';
                console.log(isSugFlag, "这是isSugFlag");
                if (isSugFlag) {
                    isJgspid = sessionStorage.getItem("jgspid");
                } else {
                    sessionStorage.setItem("jgspid", "150");
                }
                
                isSugFlag = false;
                window.open(shopUrl);
                return false
            }
            if (name == null || name == "") {
//		name="阿莫西林胶囊";
                replaceParamVal('keyword','')
            } else {
                var jumpHref = "";
                webSdk.track('pc_action_Search', {
                    'searchTerms' : name,
                    'source' : _search_type //(1:输入2:历史3:推荐4:联想)
                });
                var pathname = window.location.pathname;
                var isJumpNewOpen = true;
                var isJumpType = "_blank";
                if (pathname == "/") {
                    if(window.location.href.indexOf('shopCode') > -1 && window.location.href.indexOf('keyword') < 0){
                        //window.location.href = window.location.href + '&keyword=' + encodeURI(name)
                        //window.open("/search/skuInfo.htm?keyword=" + encodeURI(name), "_blank");
                        jumpHref = window.location.href + '&keyword=' + encodeURI(name);
                        isJumpNewOpen = false;
                    }else if(window.location.href.indexOf('keyword') > -1){
                        replaceParamVal('keyword',encodeURI(name))
                    }else {
                        //window.open("/search/skuInfo.htm?keyword=" + encodeURI(name), "_blank");
                        jumpHref = location.origin + "/search/skuInfo.htm?keyword=" + encodeURI(name);
                    }
                } else {
                    // if(window.location.href.indexOf('shopCode') > -1 && window.location.href.indexOf('keyword') < 0){
                    //     window.location.href = window.location.href + '&keyword=' + encodeURI(name)
                    //     //window.open("/search/skuInfo.htm?keyword=" + encodeURI(name), "_blank");
                    // }else if(window.location.href.indexOf('keyword') > -1){
                    //     replaceParamVal('keyword',encodeURI(name))
                    // }else{
                    //     window.open("/search/skuInfo.htm?keyword=" + encodeURI(name), "_top");
                    // }
                    //window.open("/search/skuInfo.htm?keyword=" + encodeURI(name), "_top");
                    isJumpType = "_top";
                    jumpHref = location.origin + "/search/skuInfo.htm?keyword=" + encodeURI(name);
                }
                let isJgspid = '150';
                console.log(isSugFlag, "这是isSugFlag111");
                if (isSugFlag) {
                    isJgspid = sessionStorage.getItem("jgspid");
                } else {
                    sessionStorage.setItem("jgspid", "150");
                }
                
                isSugFlag = false;
                if (isJumpNewOpen) {
                    window.open(jumpHref, isJumpType);
                } else {
                    window.location.href = jumpHref;
                }
            }

        }
        function checkSearchText(text, keyword) {
            let index = 0;
            let newStr = "";
            index = text.indexOf(keyword);
            if (index > -1) {
                text = text.split(keyword);
                newStr = text[0] +"<span style='color: #00b955;'>" + keyword + "</span>" + text[1];
            } else {
                newStr = text;
            }
            return newStr;
        }
        function loadProductNameByAutoComplete(name) {
            var url = "/search/autoComplate.json?showName=" + encodeURI(name);
            var html = "";
            if (name == null || name.trim() == "") {
            } else {
                name = name.trim();
                $.ajax({
                    type: "GET",
                    url: url,
                    async: true,
                    dataType: "json",
                    success: function (data) {
                        var allList = (data.shopList || []).concat(data.showNameList || [])
                        $.each(allList, function (index, item) {
                            if (item.pcUrl) {
                                html += "<li><a li-index=" + index + (item.shopCode ? " shop-id=" + item.shopCode : '') +" ><span shopUrl=" + item.pcUrl + ">" + checkSearchText(item.shopName, name) + "</span></a></li>";
                            } else {
                                html += "<li><a li-index=" + index + (item.shopCode ? " shop-id=" + item.shopCode : '') +" href='/search/skuInfo.htm?keyword=" + encodeURI(item.showName) + "' target='_self'><span class='t8'>" + checkSearchText(item.showName, name) + "</span></a></li>";
                            }
                        })
                        var marketingListAll = data.marketingList || []
                        marketingListAll.forEach(function (item,index) {
                            let restock = '<li class="Slogan" style="width: 97%;height: 38px;margin: 6px auto;background: #FBE7E8;border-radius: 5px;' +
                                            'display: flex;align-items: center;padding-left: 0px;cursor: pointer;" data-route="' + item.route + '">' +
                                            '<img src="/static/img/searchRedBao.png" alt="红包" style="width: 25px;height: 25px;margin:0 5px;">' +
                                            '<span style="font-weight: 500;font-size: 15px;color: #333333;letter-spacing: 0;font-family: PingFangSC-Medium;">' +
                                                item.marketingText +
                                            '</span>' +
                                            '<span style="font-weight: 400;font-size: 15px;color: #FF0025;letter-spacing: 0;font-family: PingFangSC-Medium;margin-left: 6px;">' +
                                                '去看看<span> > </span>' +
                                            '</span>' +
                                            '</li>';
                            html = restock + html
                        })
                        // var shopList = data.shopList || []
                        // $.each(shopList, function (index, item) {
                        //     html += "<li><a><span shopUrl=" + item.pcUrl + ">" + item.shopName + "</span></a></li>";
                        // })
                        // $.each(data.showNameList, function (index, entry) {
                        //     var name = entry.showName;
                        //     html += "<li><a href='/search/skuInfo.htm?keyword=" + encodeURI(name) + "' target='_self'><span>" + entry.showName + "</span></a></li>";
                        // });


                        if ((data.showNameList && data.showNameList.length > 0) || (data.shopList && data.shopList.length > 0)) {
                            $("#searchUl").html(html);
                            $('#searchUl').css("display", "block");
                        } else {
                            $('#searchUl').css("display", "none");
                        }
                    }
                });
            }
        }

        /*搜索弹窗响应键盘事件*/
        $("#search").unbind("keyup");
        $("#search").keyup(function (event) {
            var e = event || window.event;
            var k = e.keyCode || e.which;
            if (k == 38 || k == 37 || k == 39 || k == 40) {
            } else {
                var name = $("#search").val();
                loadProductNameByAutoComplete(name);
            }
        });
        function inputFocus() {
            $("#clearInputbox").css("display", "block");
        }
        function inputBlur() {
            $("#clearInputbox").css("display", "none");
        }
        /*搜索弹窗响应键盘事件*/
        $("#search").keydown(function (event) {
            var e = event || window.event;
            var k = e.keyCode || e.which;
            switch (k) {
                case 38:
                    int--;
                    var tmp = $('#searchUl li').length - 1;
                    if (int < 0) {
                        int = tmp;
                    }
                    $('#searchUl li').eq(int - 1).addClass('active').siblings().removeClass('active');
                    $("#search").val($('#searchUl li').eq(int - 1).find("span").text());
                    //判断一下搜索来源
                    check_search_type($('#searchUl li').eq(int - 1))
                    break;
                case 40:
                    int++;
                    var tmp = $('#searchUl li').length - 1;
                    if (int > tmp) {
                        int = 0;
                    }
                    $('#searchUl li').eq(int - 1).addClass('active').siblings().removeClass('active');
                    $("#search").val($('#searchUl li').eq(int - 1).find("span").text());

                    //判断一下搜索来源
                    check_search_type($('#searchUl li').eq(int - 1))
                    break;
                case 13:
                    check_search_type();
                    handle_submit();
                    $("#search").blur()
                    break;
            }
        });


        /*点击搜索下拉框事件*/
        function handle_click() {
            $('#searchUl').click(function (e) {
                $('#searchUl li a:first-of-type').unbind("click");
                $('#searchUl li .history').unbind("click");
                //搜索
                $('#searchUl li a:first-of-type').click(function (e) {
                    e.stopPropagation();
                    $("#search").val($(this).find("span:first").text());
                    var shopUrl = $(this).find("span").attr('shopUrl')
                    if (shopUrl) {
                        $("#search").attr('shopUrl', shopUrl);
                    }
                    check_search_type($(this))
                    $(".searchUl").hide();
                    var merchantId = $('#merchantId').val();
                    var keyword = $("#search").val().trim()
                    var ind = $(this).attr('li-index')
                    var isHistory = $('#searchUl').find('li').attr('data-history') === 'history';
                    var sug = isHistory ? ('history_' + ind) : ('suggest_' + ind);
                    var shopId = $(this).attr('shop-id');
                    var sugList = [];
                    $('#searchUl').find('li a span').each(function() {
                        sugList.push($(this).text());
                    });
                    sessionStorage.setItem("jgspid", isHistory ? "152" : "153");
                    console.log("定义issug");
                    isSugFlag = true;
                    

                    //lwq的埋点，666
                    if(window.isIndexV2){
                        let spm_cnt = "1_4"
                            spm_cnt += ".home_"+$("#pageId").val()+"-"+$("#version").val()+"_0"
                            spm_cnt += ".searcherBox1@" + window.getPosition(2)
                            spm_cnt += ".sug@" + (ind * 1 + 1)
                            spm_cnt += "."+window.getSpmE();
                        let scm_cnt="admin.0.all_0." +"text-" + window.keepChineseEnglishAndNumbers(keyword) + (shopId ? "_shop-" + shopId : "") +"."+ window.addScmeV2(1);
                        aplus_queue.push({
                            'action': 'aplus.record',
                            'arguments': ['action_sub_module_click', 'CLK', {
                                'spm_cnt': spm_cnt,
                                'scm_cnt': scm_cnt,

                            }]
                        });
                    }
                    
                    webSdk.track('action_Search', {
                        user_id: merchantId,
                        sdk: 'sg-js',
                        sptype: 1,
                        spid: 1, // 1-大搜，2-店铺搜索，3-专区搜索
                        wq: '',
                        keyword: keyword,
                        sug: sug,
                        sg1: sugList[0] || '',
                        sg2: sugList[1] || '',
                        sg3: sugList[2] || '',
                        sg4: sugList[3] || '',
                        sg5: sugList[4] || '',
                        pkw: getUrlParam('keyword'),
                    });
                    handle_submit(true);
                })
                //删除
                $('#searchUl li .history').click(function (e) {
                    e.stopPropagation();
                    handle_search_data("delete", $(this).parent().find("span").text());
                    $(this).parent().remove()
                    is_hide = false;
                });
                // 跳转补货专区
                $('#searchUl .Slogan').click(function (e) {
                    e.stopPropagation();
                    e.preventDefault();
                    var route = $(this).data('route');
                    console.log(route,"route");
                    if (route) {
                        window.location.href = route;
                    }
                });
                $(e.target).click()
            });
        }


        /*隐藏搜索弹窗*/
        $("#search").blur(function () {
            setTimeout(function () {
                if (is_hide) {
                    $(".searchUl").hide();
                }
                is_hide = true
                inputBlur();
            }, 200)
        });

        //判断搜索类型
        function check_search_type(obj) {
            if (obj) {
                if (obj.data("history") === "history") {
                    _search_type = 2;
                    return
                }
            }
            if (_search_type === null) {
                _search_type = 1;
                return
            }
            _search_type = 4;
        }
        function clearInput() {
            console.log($("#search").val(), "11");
            if ($("#search").val() !== "") {
                $("#search").val("");
                searchProduct();
            }
        }
        $("#clearInputbox").click(function () {
            if ($("#search").val() !== "") {
                $("#search").val('');
                var keyword = $("#search").val('');
                check_search_type()
                handle_search_data("add", keyword)
                searchProduct()
            }
            
        });
        
        //处理搜索逻辑
        function handle_submit(type) {
            var submit = $("#search-btn")
            submit.unbind("click");
            submit.click(function () {
                var keyword = $("#search").val().trim()
                handle_search_data("add", keyword)
                check_search_type();
                searchProduct()
            });
            submit.click()
        }

        //显示搜索弹窗
        $("#search").focus(function () {
            handle_search_data("get")
            $('#searchUl').show();
            inputFocus();
        });


        //渲染dom函数
        function render(data) {
            var box = $("#searchUl")
            box.html("")
            data.forEach(function (item, index) {
                var el = '<li data-history="history"><a href="javascript:;" li-index=' + (index);
                el += '><span>' + item.keyword + '</span></a></li>';
                var li = $(el)
                li.append($('<a href="javascript:;" class="history">删除</a>'))
                box.append(li)
            });
        }


        // 接口处理函数
        function handle_search_data(type, keyword) {
            if (keyword) {
                keyword = keyword.replace(/\s+/g, " ")
            }
            switch (type) {
                case "get":
                    $.ajax({
                        url: "/searchRecord/list.json",
                        type: "get",
                        success: function (data) {
                            if (data.status === "success") {
                                search_data = data.data;
                                render(search_data)
                            } else {

                            }
                        },
                        error: function () {
                        }
                    });
                    break;
                case "add":
                    $.ajax({
                        url: "/searchRecord/add.json",
                        type: "post",
                        data: {
                            keyword: keyword
                        },
                        success: function (data) {
                            // if (data.status === "success") {
                            //         search_data.unshift({
                            //                 keyword: keyword
                            //         });
                            //         render(search_data)
                            // }
                        },
                        error: function () {
                        }
                    });
                    break;
                case "delete":
                    $.ajax({
                        url: "/searchRecord/del.json",
                        type: "post",
                        data: {
                            keyword: keyword
                        },
                        success: function () {
                            for (var i = 0; i < search_data.length; i++) {
                                if (search_data[i].keyword == keyword) {
                                    search_data.splice(i, i + 1);
                                    break
                                }
                            }
                        },
                        error: function () {
                        }
                    });
                    break
            }
        }

        // 处理红包点击事件
        window.handleRestockClick = function (url) {
            if(url) {
                window.location.href = url;
            }
        }

        handle_search_data("get")
        handle_click();
        $("#search-btn").click(function () {
            var keyword = $("#search").val().trim()
            //lwq的埋点，666
            if(window.isIndexV2){
                let spm_cnt="1_4.home_"+$("#pageId").val()+"-"+$("#version").val()+"_0.searcherBox1@" + window.getPosition(2) +".btn@1."+window.getSpmE();
                let scm_cnt="admin.0."+"all_0.text-搜索"+"."+window.addScmeV2(1);
                aplus_queue.push({
                    'action': 'aplus.record',
                    'arguments': ['action_sub_module_click', 'CLK', {
                        'spm_cnt': spm_cnt,
                        'scm_cnt': scm_cnt,

                    }]
                });
            }
            check_search_type()
            handle_search_data("add", keyword)
            searchProduct()
        });

        function getUrlParam(name) {
            var urlArr = window.location.href.split('?');
            if (urlArr.length < 2) {
                return '';
            }
            var tempArr = urlArr[1].split('&');
            for (var i = 0; i < tempArr.length; i++) {
                var item = tempArr[i].split('=');
                if (item[0].trim() === name) {
                    return decodeURI(item[1]);
                }
            }
            return '';
        }


        $.ajax({
            url: "/categoryTree?r=" + Math.random(),
            type: "POST",
            dataType: "html",
            traditional :true,
            data: {},
            success: function(result){
                $("#categoryTree").html(result);
                if (window.isIndexV2) {
                    window.setTimeout(function() {
                        $(".newul > li > a").click(function() {
                            let spm_cnt = "1_4";
                                spm_cnt += ".home_"+$("#pageId").val()+"-"+$("#version").val()+"_0";
                                spm_cnt += ".newwarp@" + window.getPosition(3);
                                spm_cnt += "." + $(this).attr('index-data');
                                spm_cnt += "."+window.getSpmE();
                            let scm_cnt = "admin.0."+"all_0." + "category-" + $(this).attr('category-data') +"_text-" + $(this).attr("title-data") +"."+ window.addScmeV2(1);
                            aplus_queue.push({
                                'action': 'aplus.record',
                                'arguments': ['action_sub_module_click', 'CLK', {
                                    'spm_cnt': spm_cnt,
                                    'scm_cnt': scm_cnt,

                                }]
                            });
                        })
                        $(".leimu-TC .two-box-ul .com-title a").click(function() {
                            let spm_cnt = "1_4";
                                spm_cnt += ".home_"+$("#pageId").val()+"-"+$("#version").val()+"_0";
                                spm_cnt += ".newwarp@" + window.getPosition(3);
                                spm_cnt += "." + $(this).attr('index-data');
                                spm_cnt += "."+window.getSpmE();
                            let scm_cnt = "admin.0."+"all_0." + "category-" + $(this).attr('category-data') +"_text-" + $(this).text() +"."+ window.addScmeV2(1);
                            aplus_queue.push({
                                'action': 'aplus.record',
                                'arguments': ['action_sub_module_click', 'CLK', {
                                    'spm_cnt': spm_cnt,
                                    'scm_cnt': scm_cnt,

                                }]
                            });
                        })
                        $(".leimu-TC .two-box-ul .com-info a").click(function() {
                            let spm_cnt = "1_4";
                                spm_cnt += ".home_"+$("#pageId").val()+"-"+$("#version").val()+"_0";
                                spm_cnt += ".newwarp@" + window.getPosition(3);
                                spm_cnt += "." + $(this).attr('index-data');
                                spm_cnt += "."+window.getSpmE();
                            let scm_cnt = "admin.0."+"all_0." + "category-" + $(this).attr('category-data') +"_text-" + $(this).text() +"."+ window.addScmeV2(1);
                            aplus_queue.push({
                                'action': 'aplus.record',
                                'arguments': ['action_sub_module_click', 'CLK', {
                                    'spm_cnt': spm_cnt,
                                    'scm_cnt': scm_cnt,

                                }]
                            });
                        })
                    }, 0)
                }
                /*显示类目*/
                $(".goodsmain .fltitle,.xfubox .flbox").hover(function(){
                    $(".xfubox .flbox").css("display","block");
                },function(){
                    $(".xfubox .flbox").css("display","none");
                })
                $(".xfubox .flbox .newul,.leimu-TC").hover(function(){
                    $(".leimu-TC").css("display","block");
                    $(".xfubox .flbox").css("display","block");
                },function(){
                    $(".leimu-TC").css("display","none");
                    $(".xfubox .flbox").css("display","none");
                })
                /*切换显示*/
                $('.newul li').hover(function(){
                    $(".two-box-ul .warp-li").eq($(this).index()).addClass("show").siblings().removeClass('show');
                    $(this).addClass("cur").siblings().removeClass("cur");
                });
            }
        });
    });

    /*显示类目*/
    $(".goodsmain .fltitle,.xfubox .flbox").hover(function(){
        $(".xfubox .flbox").css("display","block");
    },function(){
        $(".xfubox .flbox").css("display","none");
    })
    $(".xfubox .flbox .newul,.leimu-TC").hover(function(){
        $(".leimu-TC").css("display","block");
        $(".xfubox .flbox").css("display","block");
    },function(){
        $(".leimu-TC").css("display","none");
        $(".xfubox .flbox").css("display","none");
    })
    /*切换显示*/
    $('.newul li').hover(function(){
        $(".two-box-ul .warp-li").eq($(this).index()).addClass("show").siblings().removeClass('show');
        $(this).addClass("cur").siblings().removeClass("cur");
    });
    /*去掉分类高亮*/
    $(".xfubox").hover(function(){},function(){$(".newul li").removeClass("cur");});

    /*导航添加底部横线*/
    $('.topnav li').hover(function(){$(this).addClass("hovercur").siblings().removeClass("hovercur")},function(){$(this).removeClass("hovercur")});



    /*回到顶部*/
    $('#toTop').click(function() {
        $('html,body').animate({
            scrollTop: '0px'
        }, 'slow');
    });

    /*导航切换样式*/
    $('.topnav  li a').click(function(){
        $(this).addClass("cur").parent("li").siblings().find("a").removeClass("cur");
    });

    $('input, textarea').placeholder();

    $(".app,.erm-abs").hover(function(){
        $(".erm-abs").css("left","-108px");
    },function(){
        $(".erm-abs").css("left","0");
    });

    /*心愿单*/
    var cb=(function(){
        var xyd_timer,xyd_timeout=600;
        $(".xyd-pos-init,.xyd-pos-leftbox").hover(function(){
            if(!xyd_timer){
                $(".xyd-pos-leftbox").fadeIn();
            }else{
                xyd_timer = null;
            }
        },function(){
            xyd_timer=setTimeout(function(){
                $(".xyd-pos-leftbox").hide();
                xyd_timer = null;
            },xyd_timeout);
        });
    })();

    function addCookie(){
        var url = window.location;
        var title = document.title;
        if (document.all){
            window.external.addFavorite(url,title);
        } else if (window.sidebar){
            window.sidebar.addPanel(title, url, "");
        } else {
            alert("对不起，您的浏览器不支持此操作!\n请您使用菜单栏或Ctrl+D收藏本站。");
        }
    }


    function callKf(id,userId,orgId,isThirdCompany,merchantName){
        var url = '';
        var msg = "未连接到在线客服，请联系管理员";
        if(url == ''){
            var path = isThirdCompany ? "/custom/getIMPackUrl?isThirdCompany="+isThirdCompany : "/custom/getIMPackUrl";
            $.ajax({
                url: path,
                type: "GET",
                async: false,
                cache: false,
                dataType: "json",
                success: function(data) {
                    if(data.status == "success"){
                        //url = data.data.IM_PACK_URL
                        if(isThirdCompany){ //pop客服入口
                            if(userId){
                                if(id){
                                    url = data.data.IM_PACK_URL+'&orderNo='+id+'&userid='+userId+'&merchantCode='+orgId+'&pop=1&ws=1&sc=1000&portalType=1&merchantName='+merchantName;
                                }else {
                                    url = data.data.IM_PACK_URL+'&userid='+userId+'&merchantCode='+orgId+'&pop=1&ws=1&sc=1000&portalType=1&merchantName='+merchantName;
                                }
                            }else {
                                url = data.data.IM_PACK_URL+'&merchantCode='+orgId+'&pop=1&ws=1&sc=1000&portalType=1';
                            }
                        }else{//自营客服入口
                            if(userId){
                                if(id){
                                    url = data.data.IM_PACK_URL+'&orderNo='+id+'&userid='+userId+'&sc=1000&portalType=2';
                                }else {
                                    url = data.data.IM_PACK_URL+'&userid='+userId+'&sc=1000&portalType=1';
                                }
                            }else {
                                url = data.data.IM_PACK_URL+'&sc=1000&portalType=1';
                            }
                        }

                    }else {
                        msg = data.errorMsg;
                    }
                }
            });
        }
        if(url){
            if(isThirdCompany) { //pop客服入口
                window.open(url, '_blank', 'webcall', 'toolbar=no, status=no,scrollbars=0,resizable=0,menubar＝0,location=0,width=680,height=680');
            }else{
                window.open(url, 'webcall', 'toolbar=no, status=no,scrollbars=0,resizable=0,menubar＝0,location=0,width=680,height=680');
            }
        }else {
            alert(msg);
        }
    }

    function zhuanshuxiaoshou() {
        // $.ajax({
        //     url: "/merchant/center/salesInfo.json",
        //     type: "POST",
        //     async: true,
        //     cache: false,
        //     dataType: "json",
        //     success: function(data) {
        //         if(data != null && data.status == "success"){
        //             if(data.phone){
        //                 $("#zhuanshuxiaoshou").html(data.phone);
        //             }else{
        //                 $("#zhuanshuxiaoshou").html("无");
        //             }
                    
        //         }
        //     }
        // });
        $.ajax({
            url: "/merchant/center/salesInfo/qrCode",
            type: "get",
            async: true,
            cache: false,
            dataType: "json",
            success: function(data) {
                if(data != null && data.status == "success"){
                     $("#myImage").attr("src", data.data.qrCode);
                     $("#zhuanshuxiaoshou").html(data.data.name);
                     $("#zhuanshuPhoneNum").html(data.data.phone);
                     $(".img-boxshow").show()
                }
            }
        });
    }

    function checkoutShop(id) {
        $.ajax({
            url: "/merchant/center/selectMerchant",
            type: "POST",
            async: true,
            cache: false,
            dataType: "json",
            data: {
                merchantId: id,
            },
            success: function(data) {
                if(data != null && data.status == "success"){
                    window.location.href = '/';
                } else {
                    $.alert(data.msg);
                }
            }
        });
    }

    function getStyle(element, attr) {
        // 判断浏览器是否支持这个方法
        if(window.getComputedStyle) {
            console.log("浏览器支持window.getComputedStyle函数");
            return window.getComputedStyle(element, null)[attr];
        }else {
            console.log("浏览器支持element.currentStyle函数");
            return element.currentStyle[attr];
        }
    }

    var pageNo = 1;
    var pageTotal = 0; // 总页数

    function lastPage() {
        if (pageNo === 1) {
            return;
        }
        pageNo--;
        getShopList();
    }

    function nextPage() {
        if (pageNo === pageTotal) {
            return;
        }
        pageNo++;
        getShopList();
    }

    function getShopList() {
        var loginShopList = [];
        $.ajax({
            url: "/merchant/center/account/merchantList",
            type: "POST",
            async: true,
            cache: false,
            dataType: "json",
            data: {
                pageNo: pageNo,
                pageSize: 12,
                isOnlyCanDirectLogin: true,
            },
            success: function(data) {
                console.log('关联的所有店铺', data);
                if(data != null && data.status == "success"){
                    loginShopList = (data.data || {}).list || [];
                    pageTotal = data.data.pages || 0;
                    var html = '';
                    $.each(loginShopList, function (index, item) {
                        html += "<li class='shopItem' onclick='checkoutShop(" + item.merchantId + ")'><span title=" + item.name + ">" + item.name + "</span></li>";
                    })
                    if (!html) {
                        html = "<div class='noData'><img src='/static/images/wuguo.png' />暂无可切换的店铺</div>"
                    }
                    $("#shopListBox").html(html);

                    if (data.data.total > 0) {
                        var pageHtml = "<span>共" + data.data.total + "条</span>"
                            + "<div class='pageChange' onclick='lastPage()'><img src='/static/images/license/btnLeft.png' width='10px' height='8px' /></div>"
                            + "<div class='pageChange'>" + pageNo + "</div>"
                            + "<div class='pageChange' onclick='nextPage()'><img src='/static/images/license/btnRight.png' width='10px' height='8px' /></div>"
                            + "<span>共" + pageTotal + "页</span>"
                        $('.pagination').html(pageHtml);
                    }
                }
            }
        });
    }

    function showShopList() {
        var elem = document.querySelector('.shopListWrapper');
        var showStatus = getStyle(elem, 'display')
        if (showStatus == 'none') {
            $(".moreIcon").css("transform","rotate(0deg)");
            $(".shopListWrapper").css("display","block");
            getShopList();
        } else {
            $(".moreIcon").css("transform","rotate(180deg)");
            $(".shopListWrapper").css("display","none");
        }
    }
    document.addEventListener('click', function (e) {
        // 获取弹窗对象
        var dialogBox = document.querySelector('.shopListWrapper');
        var selectBtn = document.querySelector('.navbar-inner');
        // 点击对象不在弹窗范围内
        if(dialogBox &&　!dialogBox.contains(e.target) && selectBtn && !selectBtn.contains(e.target)) {
            $(".moreIcon").css("transform","rotate(180deg)");
            $(".shopListWrapper").css("display","none");
        }
    });
</script>
