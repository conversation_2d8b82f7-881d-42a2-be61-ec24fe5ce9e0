package com.xyy.ec.pc.search.config;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 搜索配置信息
 * @date 2022/11/15
 */
@Slf4j
@Data
@Component
@NoArgsConstructor
public class SearchProperties {

    /**
     * 活动信息
     */
    @Value("${xyy.ec.app.search.activity.info}")
    private String activityInfo;

    /**
     * 坑位推荐abtest表达式
     */
    @Value("${xyy.ec.search.position.recommend.abtest.expression}")
    private String positionRecommendABTestExpression;

    /**
     * 是否展示同省标签
     */
    @Value("${xyy.ec.pc.search.isShowSameProvince:false}")
    private Boolean isShowSameProvince;

    /**
     * 是否开启发送搜索结果商品信息Mq
     */
    @Value("${xyy.ec.pc.search.isOpenSendSearchResultCsuInfoMq:false}")
    private Boolean isOpenSendSearchResultCsuInfoMq;

    /**
     * 是否支持搜索推荐购买
     */
    @Value("${xyy.ec.pc.search.isSupportSearchRecPurchase:true}")
    private Boolean isSupportSearchRecPurchase;

    /**
     * 组合购title
     */
    @Value("${xyy.ec.pc.search.groupPurchaseTitle}")
    private String groupPurchaseTitle;

    /**
     * 组合购是否放开批购包邮
     */
    @Value("${xyy.ec.pc.search.searchRecPurchase.isOpenWholesale:false}")
    private Boolean isOpenSearchRecPurchaseWholesale;

    /**
     * 拼团与批购包邮品商品详情是否放开组合购
     */
    @Value("${xyy.ec.pc.search.isGroupBuyingOrWholesaleAllowSearchRecPurchase:false}")
    private Boolean isGroupBuyingOrWholesaleAllowSearchRecPurchase;

    /**
     * 组合购轮播，时间下发
     */
    @Value("${xyy.ec.pc.search.searchRecPurchaseCarouselTime}")
    private Integer searchRecPurchaseCarouselTime;

    /**
     * 组合购是否开启营销标语
     */
    @Value("${xyy.ec.pc.search.searchRecPurchase.isOpenMarketingSlogan:true}")
    private Boolean isOpenMarketingSlogan;

    /**
     * 组合购数据查询批次量
     */
    @Value("${xyy.ec.pc.search.searchRecPurchase.query.batchSize:100}")
    private Integer searchRecPurchaseQueryBatchSize;
}
