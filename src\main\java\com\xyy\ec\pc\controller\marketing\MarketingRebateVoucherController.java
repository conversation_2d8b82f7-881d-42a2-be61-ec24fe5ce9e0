package com.xyy.ec.pc.controller.marketing;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.marketing.client.common.enums.MarketingRebateSceneEnum;
import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingCouponCenterStatusEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateConsumptionBaseResult;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateConsumptionReturnResult;
import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponCenterDto;
import com.xyy.ec.marketing.interest.dto.redPacket.ConsumtionReturnRedPacketRecordDTO;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.rpc.HyperSpaceRpc;
import com.xyy.ec.pc.rpc.MarketingInterestRpcService;
import com.xyy.ec.pc.rpc.NineChaptersRpc;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 *
 */
@Slf4j
@Controller
@RequestMapping("/marketing/rebateVoucher")
public class MarketingRebateVoucherController {
    @Autowired
    private HyperSpaceRpc hyperSpaceRpc;
    @Autowired
    private NineChaptersRpc nineChaptersRpc;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Autowired
    private MarketingInterestRpcService marketingInterestRpcService;


    @Value("${marketing.rebateVoucher.autoReceiveFlag:1}")
    Integer autoReceiveFlag;
    @Value("${marketing.rebateVoucher.autoReceive.dialog.imgUrl:}")
    String autoReceiveDialogImgUrl;

    @ResponseBody
    @RequestMapping(value = "/getMarketingRebateVoucherTemplateList", method = RequestMethod.POST)

    public XyyJsonResult autoReceiveMarketingRebateVoucherTemplateList(@RequestParam(value = "orderNo", required = false) String orderNo) {
        log.info("MarketingRebateVoucherController.autoReceiveMarketingRebateVoucherTemplateList start, orderNo:{}", orderNo);
        MerchantBussinessDto merchantBussinessDto = null;
        try {
            merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            log.info("satisfactoryInHandPrice_orderNo={}", orderNo, e);
        }
        if (null == merchantBussinessDto){
            return XyyJsonResult.createFailure().msg("未登录");
        }

        log.info("autoReceiveMarketingRebateVoucherTemplateList_merchantBussinessDto={}", JSON.toJSONString(merchantBussinessDto));
        try {
            List<CouponCenterDto> listCanReceive = hyperSpaceRpc.getMarketingRebateVoucherTemplateList(orderNo, merchantBussinessDto.getId(), MarketingRebateSceneEnum.ORDER_SUCCESS.getType());
            log.info("MarketingRebateVoucherController.getMarketingRebateVoucherTemplateList success, orderNo:{}, cid:{}, list:{}", orderNo, merchantBussinessDto.getId(), JSONObject.toJSONString(listCanReceive));
            List<CouponCenterDto> listReceive = nineChaptersRpc.autoReceiveMarketingRebateCouponList(orderNo, merchantBussinessDto.getId(), MarketingRebateSceneEnum.ORDER_SUCCESS_AUTO_SEND.getType());
            log.info("MarketingRebateVoucherController.autoReceiveMarketingRebateVoucherTemplateList success, orderNo:{}, cid:{}, list:{}", orderNo, merchantBussinessDto.getId(), JSONObject.toJSONString(listReceive));
            Map<Long, CouponCenterDto> mapCanReceive = Maps.newHashMapWithExpectedSize(listCanReceive.size() + listReceive.size());
            if (CollectionUtils.isNotEmpty(listCanReceive)){
                for (CouponCenterDto item : listCanReceive){
                    mapCanReceive.putIfAbsent(item.getTemplateId(),item);
                }
            }
            if (CollectionUtils.isNotEmpty(listReceive)){
                for (CouponCenterDto item : listReceive){
                    CouponCenterDto couponCenterDto = mapCanReceive.putIfAbsent(item.getTemplateId(), item);
                    if (null != couponCenterDto){
                        couponCenterDto.setIsLq(MarketingCouponCenterStatusEnum.RECEIVED.getType());
                    }
                }
            }

            List<CouponCenterDto> result = Lists.newArrayList(mapCanReceive.values());
            if (CollectionUtils.isNotEmpty(result)){
                result.sort((o1, o2) -> {
                    if (null == o1.getIsLq()){
                        return -1;
                    }
                    if (null == o2.getIsLq()){
                        return 1;
                    }
                    return o1.getIsLq() - o2.getIsLq();
                });

                for (CouponCenterDto item : result){
                    if (StringUtils.isNotBlank(item.getAppUrl())){
                        item.setAppUrl(item.getAppUrl());
                    }else{
                        if (Objects.equals(com.xyy.ms.promotion.business.common.constants.VoucherEnum.RelationTypeEnum.ALL_PRODUCT.getId(), item.getSkuRelationType())){
                            item.setAppUrl("ybmpage://searchproduct?voice=ALL");
                        }else{
                            item.setAppUrl("ybmpage://couponavailableactivity?coupon_id=" + item.getTemplateId());
                        }
                    }
                    if (StringUtils.isNotBlank(item.getPcUrl())){
                        item.setPcUrl(item.getPcUrl());
                    }else{
                        if (Objects.equals(com.xyy.ms.promotion.business.common.constants.VoucherEnum.RelationTypeEnum.ALL_PRODUCT.getId(), item.getSkuRelationType())){
                            item.setPcUrl("/search/skuInfoByCategory.htm?all=all");
                        }else{
                            item.setPcUrl("/voucher/centre/findVoucherSku.htm?voucherTemplateId=" + item.getTemplateId());
                        }
                    }
                }
            }
            log.info("MarketingRebateVoucherController.autoReceiveMarketingRebateVoucherTemplateList success, orderNo:{}, cid:{}, result:{}", orderNo, merchantBussinessDto.getId(), JSONObject.toJSONString(result));
            if(CollectionUtils.isNotEmpty(result)){
                return XyyJsonResult.createSuccess().addResult("list", result);
            }else{
                return XyyJsonResult.createSuccess();
            }
        } catch (Exception e) {
            log.error("MarketingRebateVoucherController.autoReceiveMarketingRebateVoucherTemplateList error, orderNo:{}, cid:{}, msg:{}", orderNo, merchantBussinessDto.getId(),  e);
            return XyyJsonResult.createFailure().msg(e.getMessage());
        }
    }

    private JSONObject getColorJson(){
        JSONObject colorJso = new JSONObject();
        colorJso.put("couponColor", "#D73624");
        colorJso.put("isHyaline", false);
        return colorJso;
    }

    private JSONArray getImageDtosJson(){
        JSONArray jsa = new JSONArray();
        JSONObject jso = new JSONObject();
        jso.put("action", "");
        jso.put("imgUrl", autoReceiveDialogImgUrl);
        jso.put("isHyaline", true);
        jsa.add(jso);
        return jsa;
    }

    /**
     * 购物金消费返活动页面跳转
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/consumerRebate.htm", method = RequestMethod.GET)
    public ModelAndView index() throws Exception {
        MerchantBussinessDto merchantBussinessDto = null;
        try {
            merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            log.error("MarketingRebateVoucherController#index login error ", e);
        }
        if (Objects.isNull(merchantBussinessDto)) {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }
        return new ModelAndView("/consumerRebate/index.ftl");
    }

    /**
     * 消费返 查询活动信息
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/consumption/startActTags", method = RequestMethod.GET)
    public XyyJsonResult startActTags() {
        MerchantBussinessDto merchantBussinessDto = null;
        try {
            merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            log.info("startActTags get user error, msg:{}", e);
        }
        if (null == merchantBussinessDto){
            return XyyJsonResult.createFailure().msg("未登录");
        }
        Long merchantId = merchantBussinessDto.getId();
        if(log.isDebugEnabled()){
            log.debug("MarketingRebateVoucherController.startActBaseInfo start, cid:{}", merchantId);
        }
        try {
            // 对于不满足要求的用户ID数值，转为null。
            if (Objects.nonNull(merchantId) && merchantId <= 0L) {
                merchantId = null;
            }
            MarketingRebateConsumptionBaseResult result = hyperSpaceRpc.getMarketingRebateConsumptionBaseInfo(merchantId);
            if(log.isDebugEnabled()){
                log.debug("MarketingRebateVoucherController.startActBaseInfo success, cid:{}, result:{}", merchantId, JSONObject.toJSONString(result));
            }
            return XyyJsonResult.createSuccess().addResult("result", result);
        } catch (Exception e) {
            log.error("MarketingRebateVoucherController.startActBaseInfo error, cid:{}, msg:{}", merchantId, e);
            return XyyJsonResult.createFailure().msg(e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/consumption/startActInfo", method = RequestMethod.GET)
    public XyyJsonResult startActInfo() {
        MerchantBussinessDto merchantBussinessDto = null;
        try {
            merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            log.info("startActTags get user error, msg:{}", e);
        }
        if (null == merchantBussinessDto){
            return XyyJsonResult.createFailure().msg("未登录");
        }
        Long merchantId = merchantBussinessDto.getId();
        if(log.isDebugEnabled()){
            log.debug("MarketingRebateVoucherController.startActInfo start, cid:{}", merchantId);
        }
        try {
            // 对于不满足要求的用户ID数值，转为null。
            if (Objects.nonNull(merchantId) && merchantId <= 0L) {
                merchantId = null;
            }
            MarketingRebateConsumptionReturnResult result = hyperSpaceRpc.getMarketingRebateConsumptionReturnInfo(merchantId);
            if(log.isDebugEnabled()){
                log.debug("MarketingRebateVoucherController.startActInfo success, cid:{}, result:{}", merchantId, JSONObject.toJSONString(result));
            }
            List<ConsumtionReturnRedPacketRecordDTO> redPacketRecord = Lists.newArrayList();
            if(null != result && null != result.getTemplateId()){
                redPacketRecord = marketingInterestRpcService.getConsumptionReturnRedPacketRecord(result.getTemplateId());
                if(log.isDebugEnabled()){
                    log.debug("MarketingRebateVoucherController.startActInfo getConsumptionReturnRedPacketRecord success, templateId:{}, redPacketRecord:{}", result.getTemplateId(), JSONObject.toJSONString(redPacketRecord));
                }
            }
            return XyyJsonResult.createSuccess().addResult("result", result).addResult("redPacketRecord", redPacketRecord);
        } catch (Exception e) {
            log.error("MarketingRebateVoucherController.startTiming error, cid:{}, msg:{}", merchantId, e);
            return XyyJsonResult.createFailure().msg(e.getMessage());
        }
    }

}
