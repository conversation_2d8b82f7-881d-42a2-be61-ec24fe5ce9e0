package com.xyy.ec.api.rpc.product;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.exception.BusiCommonException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.base.log.ApiLog;
import com.xyy.ec.product.business.ecp.csu.dto.CsuDTO;
import com.xyy.ec.product.business.ecp.csu.dto.CsuQueryDTO;
import com.xyy.ec.product.business.ecp.out.product.ProdcutApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/5/13 10:56
 * @Description:商品接口
 */
@Component
@Slf4j
public class ProductRpcService {
    @Reference(version = "1.0.0")
    ProdcutApi prodcutApi;
    private static final int MAX_COUNT = 150;
    @ApiLog
    public ApiRPCResult<List<CsuDTO>> findCsuListByParams(CsuQueryDTO csuQueryDTO){
//        if(CollectionUtils.isNotEmpty(csuQueryDTO.getBarCodes())){
//            if (csuQueryDTO.getBarCodes().size()>MAX_COUNT){
//                ArrayList<String> list = new ArrayList(csuQueryDTO.getBarCodes());
//                int index = 0;
//                ApiRPCResult<List<CsuDTO>> apiRPCResult = null;
//                while (index+MAX_COUNT<=list.size()){
//                    List<String> subList = list.subList(index,index+MAX_COUNT-1);
//                    csuQueryDTO.setBarCodes(subList);
//                    if (apiRPCResult==null){
//                        apiRPCResult = queryProduct(csuQueryDTO);
//                    }else{
//                        ApiRPCResult<List<CsuDTO>> local = queryProduct(csuQueryDTO);
//                        if (local.isSuccess() && CollectionUtils.isNotEmpty(local.getData())){
//                            apiRPCResult.getData().addAll(local.getData());
//                        }
//                    }
//                    index+=MAX_COUNT;
//                }
//                csuQueryDTO.setBarCodes(list);
//                return apiRPCResult;
//            }
//        }
        return prodcutApi.findCsuListByParams(csuQueryDTO);
    }


    @ApiLog
    public List<Long> findCsuIdListByParams(CsuQueryDTO csuQueryDTO){
//        List<Long> cusIds = new ArrayList<>();
//        if(CollectionUtils.isNotEmpty(csuQueryDTO.getBarCodes())){
//            if (csuQueryDTO.getBarCodes().size()>MAX_COUNT){
//                ArrayList<String> list = new ArrayList(csuQueryDTO.getBarCodes());
//                int index = 0;
//                while (index+MAX_COUNT<=list.size()){
//                    List<String> subList = list.subList(index,index+MAX_COUNT-1);
//                    csuQueryDTO.setBarCodes(subList);
//                    cusIds.addAll(queryProductId(csuQueryDTO));
//                    index+=MAX_COUNT;
//                }
//                csuQueryDTO.setBarCodes(list);
//                return cusIds;
//            }
//        }
        return queryProductId(csuQueryDTO);
    }
    private List<Long> queryProductId(CsuQueryDTO csuQueryDTO){
        ApiRPCResult<List<CsuDTO>> local = prodcutApi.findCsuListByParams(csuQueryDTO);
        if (local.isSuccess()){
            return local.getData()==null?new ArrayList<>():local.getData().stream().map(CsuDTO::getId).collect(Collectors.toList());
        }else{
            throw new BusiCommonException(local.getCode(),local.getMsg());
        }
    }

    private ApiRPCResult<List<CsuDTO>> queryProduct(CsuQueryDTO csuQueryDTO){
        return prodcutApi.findCsuListByParams(csuQueryDTO);
    }
}
