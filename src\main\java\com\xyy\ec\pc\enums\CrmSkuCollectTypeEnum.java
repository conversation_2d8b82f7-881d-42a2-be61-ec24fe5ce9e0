package com.xyy.ec.pc.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CrmSkuCollectTypeEnum {

    GENERAL(1, "普药销售"),
    CONTROL(2, "控销销售");
    private int type;

    private String desc;

    public static  String getSalesName(Integer type, String oname){
        for (CrmSkuCollectTypeEnum value : CrmSkuCollectTypeEnum.values()) {
            if(value.getType() == type){
                StringBuilder sb = new StringBuilder(oname);
                sb.append("(").append(value.getDesc()).append(")");
                return sb.toString();
            }
        }
        return "";
    }
}
