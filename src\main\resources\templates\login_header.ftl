<!--登录页面头部导航-->
<div class="login-nav">

	<div class="p-lbox fl">
		<a href="/"><img src="/static/images/logo_login.png" alt="药帮忙" /></a>
	</div>
	<div class="p-rbox fr">
		<span>客服电话：</span><span>400-0505-111</span>
		<#--  <a href="javascript:callKf('','${merchant.id}');" class="kefu" style="position:relative;left:17px;">在线客服</a>
        <img src="/static/images/pao.png" alt="" style="width:44px;height:17px;position: relative;top: -17px;"/>  -->
	</div>
</div>

<!--
<script src="https://g.alicdn.com/aliww/ww/json/json.js" charset="utf-8"></script>

<script src="https://g.alicdn.com/aliww/??h5.openim.sdk/1.0.6/scripts/wsdk.js,h5.openim.kit/0.3.3/scripts/kit.js" charset="utf-8"></script>
-->


<script type="text/javascript">
    function callKf(id,userId){
        var url = '';
        if(url == ''){
            $.ajax({
                url: "/custom/getIMPackUrl",
                type: "GET",
                async: false,
                cache: false,
                dataType: "json",
                success: function(data) {
                    if(data.status == "success"){
						//url = data.data.IM_PACK_URL
						if(userId){
							if(id){
								url = data.data.IM_PACK_URL+'&orderNo='+id+'&userid='+userId+'&sc=1000&portalType=2';
							}else {
								url = data.data.IM_PACK_URL+'&userid='+userId+'&sc=1000&portalType=1';
							}
						}else {
							url = data.data.IM_PACK_URL+'&sc=1000&portalType=1';
						}
                    }
                }
            });
        }
        if(url){
            window.open(url, 'webcall', 'toolbar=no, status=no,scrollbars=0,resizable=0,menubar＝0,location=0,width=680,height=680');
        }else {
            alert("未连接到在线客服，请联系管理员");
        }
    }

	function findImUrlForOrderDetail(orderNo){
		var url = '';
		if(url == ''){
			$.ajax({
				url: "/merchant/center/order/findImUrlForOrderDetail?orderNo=" + orderNo,
				type: "GET",
				async: false,
				cache: false,
				dataType: "json",
				success: function(data) {
					if(data.status == "success"){
						url = data.data;
					}
				}
			});
		}
		if(url){
			window.open(url, 'webcall', 'toolbar=no, status=no,scrollbars=0,resizable=0,menubar＝0,location=0,width=680,height=560');
		}else {
			alert("未连接到在线客服，请联系管理员");
		}
	}

</script>





      