package com.xyy.ec.pc.common.helpers;

import com.google.common.collect.Lists;
import com.xyy.ec.merchant.bussiness.result.MerchantClerkInfoDTO;
import com.xyy.ec.pc.common.utils.MobileDesensitizationUtils;
import com.xyy.ec.pc.controller.vo.merchant.MerchantClerkInfoVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class MerchantClerkInfoVOHelper {

    public static MerchantClerkInfoVO create(MerchantClerkInfoDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        return MerchantClerkInfoVO.builder()
                .id(dto.getId())
                .mobile(MobileDesensitizationUtils.desensitizeMobilePhoneNumber(dto.getMobile()))
                .build();
    }

    public static List<MerchantClerkInfoVO> creates(List<MerchantClerkInfoDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Lists.newArrayList();
        }
        return dtos.stream().map(MerchantClerkInfoVOHelper::create)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}
