package com.xyy.ec.pc.recommend.helpers;

import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.recommend.params.PcRecommendPayParam;
import com.xyy.recommend.ecp.commons.constants.enums.EcpRecommendSceneEnum;

import java.util.Objects;

public class PcRecommendPayParamHelper {

    /**
     * 查询校验
     *
     * @param recommendParam
     * @return
     */
    public static boolean validate(PcRecommendPayParam recommendParam) {
        if (!commonValidate(recommendParam)) {
            return false;
        }
        Integer pageNum = recommendParam.getPageNum();
        if (Objects.isNull(pageNum) || pageNum <= 0L) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "pageNum参数必填且从1开始");
        }
        Integer pageSize = recommendParam.getPageSize();
        if (Objects.isNull(pageSize) || pageSize <= 0L) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "pageSize参数必填且从1开始");
        }
        return true;
    }

    private static boolean commonValidate(PcRecommendPayParam recommendParam) {
        if (recommendParam == null) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "推荐参数必填");
        }
        EcpRecommendSceneEnum recommendSceneEnum = EcpRecommendSceneEnum.valueOfCustom(recommendParam.getRecommendScene());
        if (Objects.isNull(recommendSceneEnum)) {
            throw new AppException(XyyJsonResultCodeEnum.PARAMETER_ERROR, "recommendScene参数非法");
        }
        return true;
    }

}
