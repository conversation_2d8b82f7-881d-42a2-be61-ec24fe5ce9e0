package com.xyy.ec.pc.base;

/**
 * @Description: 此接口表示需要认证主体的抽象概念，它可以用来表示任何实体
 * @Author: WanKp
 * @Date: 2018/8/25 19:30
 **/
public interface Principal {

    /**
     * 得到用户标识
     *
     *
     * @return 用户标识
     */
    public String getIdentity();

    /**
     * 得到用户最后登录时间
     *
     *
     * @return 用户最后登录时间 （返回自 1970 年 1 月 1 日 00:00:00 GMT 以来此日期表示的毫秒数）
     */
    public Long getLastLoginTime();

    /**
     * 得到登录用户名
     *
     *
     * @return 用户名
     */
    public String getLoginName();

    /**
     * 得到业务用途的,用户最后登录时间
     */
    public Long getBizLastLoginTime();

    public Long getMechantId();

}
