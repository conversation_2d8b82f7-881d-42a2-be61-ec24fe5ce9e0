package com.xyy.ec.pc.authentication.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.enums.SiteEnum;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.merchant.server.dto.AccountMerchantRelDto;
import com.xyy.ec.pc.authentication.consts.CacheConstants;
import com.xyy.ec.pc.authentication.consts.Constants;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.utils.ServletUtils;
import com.xyy.ec.pc.util.BeanUtils;
import com.xyy.framework.redis.autoconfigure.core.RedisClient;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/4/10 14:09
 * @File TokenService.class
 * @Software IntelliJ IDEA
 * @Description
 */
@Slf4j
@Component
public class TokenService {

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    @Reference(version = "1.0.0")
    private com.xyy.ec.merchant.bussiness.api.account.LoginAccountApi businessLoginAccountApi;

    // 令牌自定义标识
    @Value("${token.header:xyy_token}")
    private String header;

    // 令牌秘钥
    @Value("${token.secret:xyyecabcdefghijklmnopqrstuvwxyz}")
    private String secret;

    // 令牌有效期（默认30天）
    @Value("${token.expireTime:2592000}")
    private Integer expireTime;

    @Resource
    private RedisClient redisClient;

    protected static final long MILLIS_SECOND = 1000;

    protected static final long MILLIS_MINUTE = 60 * 1000;

    private static final long MILLIS_MINUTE_TEN = 10 * 60 * 1000;

    /**
     * 获取token
     */
    public String getToken() {

        return ServletUtils.getCookieValue(header);
    }

    /**
     * 获取cookie中的xyy_principal
     */
    public String getXyyPrincipal() {

        return ServletUtils.getCookieValue(Constants.PRINCIPAL_COOKIE_NAME);
    }

    public Long getXyyPrincipalAccountId() {

        String cookieValue = ServletUtils.getCookieValue(Constants.PRINCIPAL_COOKIE_NAME);
        if (cookieValue == null) {
            return null;
        }
        String[] split = cookieValue.split("&");
        if (split.length > 0) {
            try {
                return Long.parseLong(split[0]);
            }
            catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    /**
     * 获取用户身份信息
     */
    public JwtPrincipal getPrincipal() {

        return this.getPrincipalByToken(this.getToken());
    }

    public JwtPrincipal getPrincipalByToken(String token) {

        if (StrUtil.isNotEmpty(token)) {
            try {
                Claims claims = parseToken(token);
                // 解析对应的权限以及用户信息
                String uuid = (String) claims.get(Constants.JWT_LOGIN_USER_KEY);
                String userKey = getTokenKey(uuid);
                Object cacheObject = this.getCacheObject(userKey);
                if (cacheObject != null) {
                    return JSONObject.parseObject(cacheObject.toString(), JwtPrincipal.class);
                }
            } catch (Exception e) {
                log.error("获取用户信息异常'{}'", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(JwtPrincipal principal) {

        if (ObjectUtil.isNotNull(principal) && StrUtil.isNotEmpty(principal.getToken())) {
            refreshToken(principal);
        }
    }

    /**
     * 删除用户身份信息
     */
    public void delLoginUser(String token) {

        if (StrUtil.isNotEmpty(token)) {
            String userKey = getTokenKey(token);
            this.deleteObject(userKey);
            // ******** 删除 accountId 的当前会话记录
            businessLoginAccountApi.deleteBySessionId(token, SiteEnum.PC.getValue());
        }
    }

    /**
     * 延迟删除用户身份信息，避免平滑切换时导致反复登录
     */
    public void delayDelLoginUser(String token) {

        if (StrUtil.isNotEmpty(token)) {
            String userKey = getTokenKey(token);
            redisClient.setEx(userKey, "1", 120);
        }
    }


    public void verifyToken(JwtPrincipal principal) {

        long expireTime = principal.getExpireTime();
        long currentTime = System.currentTimeMillis();
        //过期时间小于10分钟，刷新token
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
            refreshToken(principal);
        }
    }

    public void refreshToken(JwtPrincipal jwtPrincipal) {

//        jwtPrincipal.setLoginTime(System.currentTimeMillis());
//        // expireTime * 1000 单位转换成ms
//        jwtPrincipal.setExpireTime(jwtPrincipal.getLoginTime() + expireTime * MILLIS_SECOND);
//        // 根据uuid将principal缓存
//        String userKey = getTokenKey(jwtPrincipal.getToken());
//        this.setCacheObject(userKey, jwtPrincipal, expireTime);
        this.refreshToken(jwtPrincipal, expireTime);
    }

    public void refreshTokenOneHour(JwtPrincipal jwtPrincipal) {

        this.refreshToken(jwtPrincipal, 60 * 60);
    }

    public void refreshToken(JwtPrincipal jwtPrincipal, Integer expireTime) {

        jwtPrincipal.setLoginTime(System.currentTimeMillis());
        // expireTime * 1000 单位转换成ms
        jwtPrincipal.setExpireTime(jwtPrincipal.getLoginTime() + expireTime * MILLIS_SECOND);
        // 根据uuid将principal缓存
        String userKey = getTokenKey(jwtPrincipal.getToken());
        this.setCacheObject(userKey, jwtPrincipal, expireTime);
    }

    public String createToken(JwtPrincipal jwtPrincipal) {

        String token = UUID.fastUUID().toString();
        jwtPrincipal.setToken(token);
        refreshToken(jwtPrincipal);
        // ******** 记录 accountId 对应的会话信息
        businessLoginAccountApi.addAccountSessionId(jwtPrincipal.getAccountId(), jwtPrincipal.getToken(), SiteEnum.PC.getValue());
        return createToken(createClaims(jwtPrincipal));
    }

    private String createToken(Map<String, Object> claims) {

        return Jwts.builder()
                .setClaims(claims)
                .signWith(SignatureAlgorithm.HS512, secret).compact();
    }

    public Claims parseToken(String token) {

        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }

    public Map<String, Object> createClaims(JwtPrincipal jwtPrincipal) {

        Map<String, Object> claims = new HashMap<>();
        // Redis中获取登录用户信息的key
        claims.put(Constants.JWT_LOGIN_USER_KEY, jwtPrincipal.getToken());
        // 登录账号ID
        claims.put(Constants.JWT_ACCOUNT_ID, jwtPrincipal.getAccountId());
        // 登录店铺用户ID
        claims.put(Constants.JWT_MERCHANT_ID, jwtPrincipal.getMerchantId());
        // 登录时间
        claims.put(Constants.JWT_LOGIN_TIME, jwtPrincipal.getLoginTime());
        // 登录IP、地址等信息
        claims.put(Constants.JWT_IP_ADDR, jwtPrincipal.getLoginIpAddr());
        // 登录设备号
        claims.put(Constants.JWT_LOGIN_DEVICE_ID, jwtPrincipal.getLoginDeviceId());
        // 登录版本号
        claims.put(Constants.JWT_LOGIN_VERSION, jwtPrincipal.getLoginVersion());
        // 登录操作系统
        claims.put(Constants.JWT_LOGIN_OS, jwtPrincipal.getLoginOs());
        // 登录浏览器
        claims.put(Constants.JWT_LOGIN_BROWSER, jwtPrincipal.getLoginBrowser());
        return claims;
    }

    private String getTokenKey(String uuid) {

        return CacheConstants.LOGIN_TOKEN_KEY + uuid;
    }

    public Object getCacheObject(String key) {

        return redisClient.get(key);
    }

    public void setCacheObject(final String key, final Object value, final Integer timeout) {

        redisClient.set(key, value, timeout);
    }

    public void deleteObject(final String key) {

        redisClient.del(key);
    }

    /**
     * 根据merchantId生成token, 给新cms预览页面使用
     */
    public String genTokenForCmsPreview(String keyword) {
        // 查询店铺店长的accountId
        AccountMerchantRelDto dto = loginAccountApi.sampleManagerByKeyword(keyword);
        if (dto == null) {
            throw new RuntimeException("查询账号失败");
        }
        Long merchantId = dto.getMerchantId();
        MerchantBussinessDto merchantPrincipal;
        try {
            merchantPrincipal = merchantBussinessApi.findMerchantById(Convert.toLong(merchantId));
        } catch (Exception e) {
            log.info("新CMS生成预览token失败!merchantId:{}", merchantId);
            throw new RuntimeException("查询店铺信息失败");
        }
        JwtPrincipal jwtPrincipal = new JwtPrincipal();
        BeanUtils.copyProperties(merchantPrincipal, jwtPrincipal);
        jwtPrincipal.setAccountId(dto.getAccountId());
        jwtPrincipal.setMerchantId(merchantId);
        // 兼容旧版
        jwtPrincipal.setMechantId(merchantId);
        jwtPrincipal.setLoginIpAddr("0.0.0.0");
        jwtPrincipal.setLoginDeviceId("cms");
        jwtPrincipal.setLoginVersion("cms");
        jwtPrincipal.setLoginOs("cms");
        jwtPrincipal.setLoginBrowser("cms");
        return this.createTokenOneDay(jwtPrincipal);
    }

    public String createTokenOneDay(JwtPrincipal jwtPrincipal) {
        String token = UUID.fastUUID().toString();
        jwtPrincipal.setToken(token);
        // 有效期一天
        this.refreshToken(jwtPrincipal, 86400);
        // ******** 记录 accountId 对应的会话信息
        businessLoginAccountApi.addAccountSessionId(jwtPrincipal.getAccountId(), jwtPrincipal.getToken(), SiteEnum.PC.getValue());
        return createToken(createClaims(jwtPrincipal));
    }


    /**
     * 获取用户身份信息
     */
    public JwtPrincipal getLoginPrincipal(HttpServletResponse response) {
        return this.getPrincipalByToken(ServletUtils.getCookieValueFromResponse(header,response));
    }
}