package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.service.SearchRecordNewService;
import com.xyy.ec.pc.newfront.service.SkuSearchNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@CustomizeCmsResponse
@RestController
@RequestMapping("/new-front/search-record")
@Slf4j
public class SearchRecordNewController extends BaseController {

    @Resource
    private SearchRecordNewService searchRecordNewService;

    @Resource
    private SkuSearchNewService skuSearchNewService;

    /**
     * 获取用户的搜索记录
     */
    @GetMapping("/list")
    public AjaxResult<Object> list(String showName,HttpServletRequest request) {
        try {
            return AjaxResult.successResult(StringUtil.isNotBlank(showName) ? skuSearchNewService.getShopInfoList(showName, request) : searchRecordNewService.getList(request));
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    /**
     * 新增搜索记录
     */
    @GetMapping("/add")
    public AjaxResult<Object> add(HttpServletRequest request,String keyword) {
        try {
            searchRecordNewService.addRecord(request,keyword);
            return AjaxResult.successResultNotData();
        } catch (Exception e) {
            return AjaxResult.errResult("新增搜索记录失败！");
        }
    }

    /**
     * 删除搜索记录
     */
    @GetMapping("/del")
    public AjaxResult<Object> del(String keyword,HttpServletRequest request) {
        try {
            if (keyword == null){
                searchRecordNewService.delBatchRecord(request);
            }else {
                searchRecordNewService.delRecord(request,keyword);
            }
            return AjaxResult.successResultNotData();
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

}
