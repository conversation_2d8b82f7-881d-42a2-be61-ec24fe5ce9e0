
<!DOCTYPE HTML>
<html>
<head>
    <#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>代下单列表</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="/static/css/user.css?t=${t_v}" />
    <link rel="stylesheet" href="/static/css/myplan.css?t=${t_v}" />
    <script type="text/javascript" src="/static/js/sprout/index.js?t=${t_v}"></script>
    <script src="/static/js/ajaxfileupload.js"></script>
    <script type="text/javascript">
        var ctx="/static";
    </script>
    <#if errorMsg>
        <script type="text/javascript">
            $(function() {
                $.alert({"title":"提示：","body":"${errorMsg}"});
            });
        </script>
    </#if>
</head>

<body>

<div class="container">
    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
        <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->

    <!--主体部分开始-->
    <div class="main">
        <div class="myplan row">
            <!--面包屑-->
            <ul class="sui-breadcrumb">
                <li><a href="/">首页</a>></li>
                <li><a href="/merchant/center/index.htm">用户中心</a>></li>
                <li><a href="/merchant/center/sprout/order/index.htm">代下单专区</a>></li>
                <li class="active">订单专区</li>
            </ul>

            <div class="myqual-content clear">
                <div class="side-left fl">
                    <#include "/common/merchantCenter/left.ftl" />
                </div>

                <div class="main-right fr">
                    <div class="agreement-state">
                        <div class="already"><a class="active" href="javascript:;">订单专区(${orderNum })</a><i></i></div>
                        <div class="unsigned"><a href="/substitute/order/authorize/index.htm">授权专区(${authorizeNum })</a>
                        </div>
                    </div>
                    <div class="myneworder-search myneworder-search-none clear">
                        <div class="fl">
                            <label>采购单编号</label>
                            <input class="inp-num" type="text" id="purchaseNo" placeholder="输入采购单单编号" value="${paramOrder.purchaseNo }">
                            <label class="padding-left">状态：</label>
                            <select class="inp-sel " id="status" name="status">
                                <option value="-1"  selected="selected">全部</option>
                                <option value="1" <#if paramOrder.status==1>selected="selected"</#if>>待确认</option>
                                <option value="2" <#if paramOrder.status==2>selected="selected"</#if>>已取消</option>
                                <option value="3" <#if paramOrder.status==3>selected="selected"</#if>>已驳回</option>
                            </select>

                            <div class="sui-form form-horizontal fr" style="margin-bottom: 0">
                                <div data-toggle="datepicker" class="control-group input-daterange">
                                    <label class="padding-left">下单时间：</label>
                                    <div class="controls">
                                        <input type="text" id="createTimeStart" class="input-medium input-date" value="${paramOrder.createTimeStart }"><span> 到 </span>
                                        <input type="text" id="createTimeEnd" class="input-medium input-date" value="${paramOrder.createTimeEnd}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="fl" style="width: 100%">
                            <div class="form-horizontal fl">
                                <label>提交人</label>
                                <input class="inp-num" type="text" id="saleName" placeholder="输入提交人" value="${paramOrder.saleName}">
                            </div>
                            <div class="sui-form form-horizontal fl" style="padding-left: 10px">
                                <label>商品名称</label>
                                <input class="inp-num" type="text" id="productName" placeholder="输入商品名称" value="${paramOrder.productName}">
                            </div>
                            <div class="myplan-buttons myplan-buttons-new clearfix">
                                <div class="plan-anniu fr">
                                    <button class="plan-empty clear-btn fl">清空</button>
                                    <button class="search-for query-btn fl">搜索</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="user-myplan user-myplan-sub tab-content myorder-tab-content">
                        <table class="table-body">
                            <thead>
                            <th class="myorder-row1" style="width: 340px">订单详情</th>
                            <th class="" style="width: 60px;">订单金额</th>
                            <th class="myorder-row2">提交人姓名</th>
                            <th class="myorder-row3">提交人电话</th>
                            <th class="myorder-row2" style="width: 70px">状态</th>
                            <th class="myorder-row5">操作</th>
                            </thead>
                            <tbody>
                            <#if pager.rows??&&(pager.rows?size>0)>
                            <#list pager.rows as order>
                                <tr class="table-item-body">
                                    <td name="id" hidden><input type="checkbox" name="id" hidden value="${order.purchaseNo }" /></td>
                                    <td>
                                        <div class="table-body-row table-body-row1">
                                            <img src="${productImageUrl}/ybm/product/min/${order.imageUrl}">
                                        </div>
                                        <span class="table-body-row table-body-row2" style="padding-top: 10px; text-align: left">
                                            <div>采购单编号：${order.purchaseNo}</div>
                                            <div>下单时间：${order.createTime?string('yyyy-MM-dd HH:mm:ss')}</div>
                                            <div>订单内共有${order.varietyNum}件商品</div>
                                            <#if order.status==1>
                                                 <div class="countdown-timer" style="color: red;" date-timer="${order.payEndTime}"></div>
                                             </#if>
                                        </span>
                                    </td>
                                    <td><span>${order.money}</span></td>
                                    <td><span>${order.saleName}</span></td>
                                    <td><span>${order.saleMobile}</span></td>
                                    <td>
                                                    <span style="color: #00B377">
                                                        <#if (order.status==1)>
                                                            待确认
                                                        <#elseif (order.status==2)>
                                                            已取消
                                                        <#elseif (order.status==3)>
                                                            已驳回
                                                        <#elseif (order.status==4)>
                                                            审核通过
                                                        </#if>
                                                    </span>
                                    </td>
                                    <td>

                                            <#if order.status==1>
                                                <a href="javascript:void(0)" class="details details-review" style="color: #6470B0" id="detailsReview" date-id="${order.purchaseNo}">审核</a>
                                            <#elseif (order.status!=1)>
                                                  <a href="/merchant/center/sprout/order/detail/${order.id}.htm" class="details" style="color: #6470B0">查看订单</a>
                                            </#if>

                                    </td>
                                </tr>
                            </#list>
                            </tbody>
                        </table>
                        <#else>
                            </table>

                            <!--没有订单:   加样式和图片以及事件-->
                            <div class="noplan">
                                <img src="/static/images/user/noplan.png" alt="">
                                <#if total == 0>
                                    <p>还没有订单列表</p>
                                <#else>
                                    <p>暂无记录</p>
                                </#if>
                            </div>
                        </#if>
                    </div>
                    <!--分页器-->
                    <div class="page">
                        <#import "/common/pager.ftl" as p>
                        <@p.pager currentPage=pager.currentPage limit=pager.limit total=pager.total pageCount=pager.pageCount toURL=pager.requestUrl method="get"/>
                    </div>


                </div>

            </div>
        </div>
    </div>
</div>
<script>
</script>
<!--主体部分结束-->
<!--底部导航区域开始-->
<div class="footer" id="footer">
    <#include "/common/footer.ftl" />
</div>
<!--底部导航区域结束-->
<!--客服入口开始-->
<div class="kefu-box">
    <a href="javaScript:callKf('','${merchant.id}');">
        <img src="/static/images/kefu-online.png" alt="">
    </a>
</div>
<!--客服入口结束-->
</div>

</body>
</html>