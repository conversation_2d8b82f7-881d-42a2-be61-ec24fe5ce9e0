package com.xyy.ec.pc.recommend.helpers;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.data.vo.ProductTagVo;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingSkuDto;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.recommend.vo.PcRecommendProductVO;
import com.xyy.ec.pc.service.marketing.dto.MarketingGroupBuyingActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingWholesaleActivityInfoDTO;
import com.xyy.ec.pc.util.ProductMangeUtils;
import com.xyy.ec.pc.util.SearchUtils;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class PcRecommendProductVOHelper {

    /**
     * 设置商品的拼团活动信息
     *
     * @param productVO
     * @param groupBuyingInfoDto
     * @param isApplyListShowType
     * @return
     */
    public static PcRecommendProductVO setActPt(PcRecommendProductVO productVO, GroupBuyingInfoDto groupBuyingInfoDto, Boolean isApplyListShowType) {
        if (Objects.isNull(productVO) || Objects.isNull(groupBuyingInfoDto)) {
            return productVO;
        }
        /* 活动信息 */
        // 拼团活动状态 1.未开始 ，2.拼团中，3.已结束 兼容老逻辑前端拼团活动状态 0-未开始 1-进行中 2-结束
        Integer assembleStatus = groupBuyingInfoDto.getStatus() == null ? 0 : groupBuyingInfoDto.getStatus() - 1;
        BigDecimal percentage = groupBuyingInfoDto.getPercentage();
        percentage = (Objects.isNull(percentage) || percentage.compareTo(BigDecimal.ZERO) < 0) ? BigDecimal.ZERO : percentage;
        Long assembleStartTime = Objects.nonNull(groupBuyingInfoDto.getStartTime()) ? groupBuyingInfoDto.getStartTime().getTime() : 0L;
        Long assembleEndTime = Objects.nonNull(groupBuyingInfoDto.getEndTime()) ? groupBuyingInfoDto.getEndTime().getTime() : 0L;
        Long surplusTime = (assembleEndTime - System.currentTimeMillis()) / 1000;
        surplusTime = surplusTime <= 0L ? 0L : surplusTime;
        MarketingGroupBuyingActivityInfoDTO groupBuyingActivityInfoDTO = MarketingGroupBuyingActivityInfoDTO.builder()
                .marketingId(groupBuyingInfoDto.getMarketingId())
                .activityType(groupBuyingInfoDto.getActivityType())
                .percentage(String.valueOf(percentage.multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_UP))))
                .assembleStatus(assembleStatus)
                .assembleStartTime(assembleStartTime)
                .assembleEndTime(assembleEndTime)
                .surplusTime(surplusTime)
                .orderNum(groupBuyingInfoDto.getOrderNum())
                .preheatShowPrice(groupBuyingInfoDto.getPreheatShowPrice())
                // 拼团多阶梯价信息
                .stepPriceStatus(groupBuyingInfoDto.getStepPriceStatus())
                .minSkuPrice(groupBuyingInfoDto.getMinSkuPrice())
                .maxSkuPrice(groupBuyingInfoDto.getMaxSkuPrice())
                .startingPriceShowText(groupBuyingInfoDto.getStartingPriceShowText())
                .rangePriceShowText(groupBuyingInfoDto.getRangePriceShowText())
                .stepPriceShowTexts(groupBuyingInfoDto.generateStepPriceShowTexts(productVO.getProductUnit()))
                .isApplyListShowType(isApplyListShowType)
                .supportSuiXinPin(groupBuyingInfoDto.isSupportSuiXinPin())
                .suiXinPinButtonText(groupBuyingInfoDto.getSuiXinPinButtonText())
                .suiXinPinButtonBubbleText(groupBuyingInfoDto.getSuiXinPinButtonBubbleText())
                .build();
        /* 活动的商品信息 */
        GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtils.isNotEmpty(groupBuyingInfoDto.getGroupBuyingSkuDtoList())
                ? groupBuyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
        Long merchantCount = 0L;
        if (Objects.nonNull(groupBuyingSkuDto)) {
            groupBuyingActivityInfoDTO.setSkuStartNum(groupBuyingSkuDto.getSkuStartNum());
            groupBuyingActivityInfoDTO.setAssemblePrice(groupBuyingSkuDto.getSkuPrice());
            merchantCount = groupBuyingSkuDto.getMerchantCount();
        }
        if (Objects.isNull(merchantCount) || merchantCount < 0) {
            merchantCount = 0L;
        }
        String merchantCountDesc = MessageFormat.format("{0}人已拼团", String.valueOf(merchantCount));
        groupBuyingActivityInfoDTO.setMerchantCount(merchantCount);
        groupBuyingActivityInfoDTO.setMerchantCountDesc(merchantCountDesc);
        /* 调整商品的showName */
        String productUnit = Optional.ofNullable(productVO.getProductUnit()).orElse("");
        String originalShowName = Optional.ofNullable(productVO.getOriginalShowName()).orElse("");
        StringBuilder sbShowName = new StringBuilder();
        String tagName = "";
        if (Objects.equals(productVO.getFirstChoose(), 1) && Constants.highGross.contains(productVO.getHighGross())) {
            tagName = "【首推优选】";
        } else if (StringUtils.isNotEmpty(groupBuyingInfoDto.getTopicPrefix())) {
            tagName = groupBuyingInfoDto.getTopicPrefix();
        }
        sbShowName.append(tagName);
        String effectiveText = SearchUtils.getEffectiveText(productVO.getNearEffectiveFlag());
        sbShowName.append(effectiveText);
        if (groupBuyingActivityInfoDTO.getSkuStartNum() != null) {
            sbShowName.append(groupBuyingActivityInfoDTO.getSkuStartNum())
                    .append(productUnit).append("包邮")
                    .append(" ").append(originalShowName);
        } else {
            sbShowName.append(originalShowName);
        }
        // 追加规格
        if (StringUtils.isNotEmpty(productVO.getSpec()) && !Objects.equals(productVO.getSpec(), "-")) {
            sbShowName.append("/").append(productVO.getSpec());
        }
        productVO.setShowName(sbShowName.toString());
        // 价格前缀
        productVO.setPricePrefix("拼团价");
        /* 设置拼团活动信息 */
        productVO.setActPt(groupBuyingActivityInfoDTO);
        return productVO;
    }

    /**
     * 设置商品的批购包邮活动信息
     *
     * @param buyingInfoDto
     * @return
     */
    public static PcRecommendProductVO setActPgby(PcRecommendProductVO productVO, GroupBuyingInfoDto buyingInfoDto, Boolean isApplyListShowType) {
        if (Objects.isNull(productVO) || Objects.isNull(buyingInfoDto)) {
            return productVO;
        }
        /* 活动信息 */
        MarketingWholesaleActivityInfoDTO wholesaleActivityInfoDTO = MarketingWholesaleActivityInfoDTO.builder()
                .marketingId(buyingInfoDto.getMarketingId())
                .activityType(buyingInfoDto.getActivityType())
                .isApplyListShowType(isApplyListShowType)
                .supportSuiXinPin(buyingInfoDto.isSupportSuiXinPin())
                .suiXinPinButtonText(buyingInfoDto.getSuiXinPinButtonText())
                .suiXinPinButtonBubbleText(buyingInfoDto.getSuiXinPinButtonBubbleText())
                .build();
        /* 活动的商品信息 */
        GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtils.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList())
                ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
        if (Objects.nonNull(groupBuyingSkuDto)) {
            wholesaleActivityInfoDTO.setSkuStartNum(groupBuyingSkuDto.getSkuStartNum());
            wholesaleActivityInfoDTO.setAssemblePrice(groupBuyingSkuDto.getSkuPrice());
        }
        /* 调整商品的showName */
        String productUnit = Optional.ofNullable(productVO.getProductUnit()).orElse("");
        String originalShowName = Optional.ofNullable(productVO.getOriginalShowName()).orElse("");
        StringBuilder sbShowName = new StringBuilder();
        if (wholesaleActivityInfoDTO.getSkuStartNum() != null) {
            sbShowName.append(wholesaleActivityInfoDTO.getSkuStartNum())
                    .append(productUnit).append("包邮")
                    .append(" ").append(originalShowName);
        } else {
            sbShowName.append(originalShowName);
        }
        // 追加规格
        if (StringUtils.isNotEmpty(productVO.getSpec()) && !Objects.equals(productVO.getSpec(), "-")) {
            sbShowName.append("/").append(productVO.getSpec());
        }
        productVO.setShowName(sbShowName.toString());
        /* 设置批购包邮活动信息 */
        productVO.setActPgby(wholesaleActivityInfoDTO);
        return productVO;
    }

    /**
     * 创建
     *
     * @param productDTO
     * @param actSk
     * @return
     */
    public static PcRecommendProductVO create(ProductDTO productDTO, MarketingSeckillActivityInfoDTO actSk) {
        if (productDTO == null) {
            return null;
        }
        PcRecommendProductVO productVO = new PcRecommendProductVO();
        BeanUtils.copyProperties(productDTO, productVO);
        // 类型转换
        if (Objects.nonNull(productDTO.getRetailPrice())) {
            productVO.setRetailPrice(BigDecimal.valueOf(productDTO.getRetailPrice()));
        }
        if (Objects.nonNull(productDTO.getFob())) {
            productVO.setFob(BigDecimal.valueOf(productDTO.getFob()));
        }
        // 商品名称初步处理
        productVO.setOriginalShowName(productDTO.getShowName());
        //显示名称加上临期/近效期
        productVO.setShowName(SearchUtils.getEffectiveText(productDTO.getNearEffectiveFlag()) + productDTO.getShowName());
        // 有效期：不展示远效期，仅展示近效期
        if (StringUtils.isNotEmpty(productVO.getNearEffect()) && !Objects.equals(productVO.getNearEffect(), "-")) {
            productVO.setEffectStr(productVO.getNearEffect());
        }
        Map<String, Object> tags = Maps.newHashMap();
        tags.put("productTags", productDTO.getTagList());
        tags.put("titleTags", productDTO.getTitleTagList());
        tags.put("markerTag", productDTO.getMarkerUrl());
        tags.put("activityTag", productDTO.getActivityTag());
        tags.put("productTagsV2", productDTO.getProductTagList());
        tags.put("productMainTagList", productDTO.getFirstTagList());
        tags.put("productOtherTagList", productDTO.getSecondTagList());
        productVO.setTags(tags);
        productVO.setActPt(null);
        productVO.setActSk(actSk);
        //受托厂家
        if (StringUtils.isNotBlank(productVO.getEntrustedManufacturer())) {
            productVO.setManufacturer(ProductMangeUtils.getManufacturer(productVO.getMarketAuthor(), productVO.getManufacturer(), productVO.getEntrustedManufacturer()));
        }
        return productVO;
    }
    /**
     * 创建
     *
     * @param csuDTO
     * @return
     */
    public static PcRecommendProductVO createsNoSimple(ProductDTO productDTO) {
        if (productDTO == null) {
            return null;
        }
        PcRecommendProductVO productVO = new PcRecommendProductVO();
        BeanUtils.copyProperties(productDTO, productVO);
        // 商品名称初步处理
        productVO.setOriginalShowName(productDTO.getShowName());
        //显示名称加上临期/近效期
        productVO.setShowName(SearchUtils.getEffectiveText(productDTO.getNearEffectiveFlag()) + productDTO.getShowName());
        // 有效期：不展示远效期，仅展示近效期
        if (StringUtils.isNotEmpty(productVO.getNearEffect()) && !Objects.equals(productVO.getNearEffect(), "-")) {
            productVO.setEffectStr(productVO.getNearEffect());
        }
        Map<String, Object> tags = Maps.newHashMap();
        productVO.setTags(tags);
        productVO.setActPt(null);
        productVO.setActSk(null);
        //受托厂家
        if (StringUtils.isNotBlank(productVO.getEntrustedManufacturer())) {
            productVO.setManufacturer(ProductMangeUtils.getManufacturer(productVO.getMarketAuthor(), productVO.getManufacturer(), productVO.getEntrustedManufacturer()));
        }
        return productVO;
    }

    /**
     * 创建
     *
     * @param productDTO
     * @return
     */
    public static PcRecommendProductVO create(ProductDTO productDTO) {
        return create(productDTO, null);
    }

    /**
     * 创建
     *
     * @param productDTOS
     * @return
     */
    public static List<PcRecommendProductVO> creates(List<ProductDTO> productDTOS) {
        if (CollectionUtils.isEmpty(productDTOS)) {
            return Lists.newArrayList();
        }
        return productDTOS.stream().map(PcRecommendProductVOHelper::create).filter(Objects::nonNull).collect(Collectors.toList());
    }
    /**
     * 创建
     *
     * @param csuDTOS
     * @return
     */
    public static List<PcRecommendProductVO> createsNoSimple(List<ProductDTO> csuDTOS) {
        if (CollectionUtils.isEmpty(csuDTOS)) {
            return Lists.newArrayList();
        }
        return csuDTOS.stream().map(PcRecommendProductVOHelper::createsNoSimple).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 创建
     *
     * @return
     */
    public static void setTagInfo(PcRecommendProductVO productVO, Map<String, Object> dataTagInfo) {
        if (Objects.isNull(productVO)) {
            return;
        }
        Map<String, Object> allTags = productVO.getTags();
        if (Objects.isNull(allTags)) {
            return;
        }
        if (Objects.isNull(dataTagInfo)) {
            dataTagInfo = Maps.newHashMapWithExpectedSize(16);
        }
        // 将快递标签添加到数据标签中
        if (CollectionUtils.isNotEmpty(productVO.getExpressList())) {
            List<ProductTagVo> expressProductTagVos = productVO.getExpressList().stream()
                    .map(tagDTO -> {
                        ProductTagVo productTagVo = new ProductTagVo();
                        BeanUtils.copyProperties(tagDTO, productTagVo);
                        productTagVo.setUiStyle(String.valueOf(tagDTO.getUiStyle()));
                        return productTagVo;
                    }).collect(Collectors.toList());
            List<ProductTagVo> expressDataTags = (List<ProductTagVo>) dataTagInfo.get("dataTags");
            if (Objects.isNull(expressDataTags)) {
                expressDataTags = Lists.newArrayListWithExpectedSize(16);
            }
            expressDataTags.addAll(expressProductTagVos);
            dataTagInfo.put("dataTags", expressProductTagVos);
        }
        // 若数据标签中有markerTag，则以商品的为准
        if (allTags.containsKey("markerTag") && dataTagInfo.containsKey("markerTag")) {
            dataTagInfo.remove("markerTag");
        }
        // 添加数据标签
        if (MapUtils.isNotEmpty(dataTagInfo)) {
            allTags.putAll(dataTagInfo);
        }
        //重新赋值所有标签数据
        productVO.setTags(allTags);
    }
}
