package com.xyy.ec.pc.constants;

import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.xyy.ms.promotion.business.common.constants.VoucherEnum.VoucherTypeEnum.CROSS_PLATFORM;
import static com.xyy.ms.promotion.business.common.constants.VoucherEnum.VoucherTypeEnum.SPECIALTY;

/**
 * @Description:
 * @Author: WanKp
 * @Date: 2018/8/25 19:06
 **/
public class Constants {

    public static final String PRODUCT_CATEGORY_INFO = "PRODUCT_CATEGORY_INFO";
    public static final String PRODUCT_INFO = "PRODUCT_INFO";
    public static final String PRODUCT_CATEGORY_KEY = "PRODUCT_CATEGORY_KEY";
    public static final String PRODUCT_KEY = "PRODUCT_KEY";
    public static final String DEFAULT_PASSWORD = "ybm100";
    //正常商品个人限购数量
    public static final Integer PRODUCT_PERSON_PURCHASE_QTY = 50;
    //pop店铺编码
    public static final String POP_SHOP_CODE="DP000000";
    //品质店铺编码
    public static final String QUALITY_SHOP_CODE="DP111111";
    //导入计划单用户名
    public static final String IMPORT_PLAN_USERNAME = "PC";

    public static final List<Integer> highGross = Arrays.asList(2,3,4);

    public final static Integer IS0 =0;
    public final static Integer IS1 =1;
    public final static Integer IS2 =2;
    public final static Integer IS3 =3;
    public final static Integer IS4 =4;
    public final static Integer IS5 =5;
    public final static Integer IS6 =6;
    public final static Integer IS7 =7;
    public final static Integer IS8 =8;
    public final static Integer IS9 =9;
    public final static Integer IS10 =10;
    public final static Integer IS11 =11;

    public static Integer IS99 = 99;
    public final static Integer IS100 =100;

    // 商品状态标识
    public static final int PRODUCT_IS_NOT_SPLIT = 0; // 商品不可拆零
    public static final int STATUS_IN_THE_SALES = 1; // 销售中
    public static final int STATUS_SOLD_OUT = 2; // 已售罄
    public static final int STATUS_IN_THE_PREFERENTIAL = 3; // 特惠中
    public static final int STATUS_THE_SHELVES = 4; // 下架
    public static final int STATUS_THE_SECKILL = 5; // 秒杀
    public static final int STATUS_THE_TO_SALES = 6; // 待上架


    public static final Boolean IS_DEFAULTED = Boolean.TRUE;      //设置默认

    public static final String IS_STRING_DEFAULTED = "true";      //设置默认

    public static final Boolean IS_NOT_DEFAULTED = Boolean.FALSE;  //非默认

    public static final Integer GSP_REMARK_IS_PASS = 3;  //GSP备注地址审核通过


    public static final int IS_SELECT = 1;
    public static final int IS_UNSELECT = 0;

    public static final int IS_CONTROL_YES = 1;

    public static final int IS_CONTROL_NO = 0;

    /** 病单状态标识 */
    /** 待处理 */
    public static final int INTERVENTION_HANDLE_WAIT = 1;

    /** 已处理 */
    public static final int INTERVENTION_HANDLE_COMPLETE  = 2;

    /** 已关闭 */
    public static final int INTERVENTION_HANDLE_CLOSE  = 3;

    /**用于页面传值判断**/
    public static final String DRUGREPORT ="drugReport";

    /**首营质料  用于创建文件夹**/
    public static final String FIRST_CAMP_DATA = "首营质料";

    /**药检报告 用于创建文件夹**/
    public static final String DRUG_CHECK_REPORT = "药检报告";

    /** 是否上传图片 0：未上传 1：上传 **/
    public static final int IS_UPLOAD = 0;

    /**
     * 默认编码
     */
    public static final String DEFAULT_ENCODING = "UTF-8";

    public static final String XYD_LOAN_CHECK_HAS_PAY_SUCCESS_MSG = "小雨点放款中，预计等待时长2分钟。请勿取消订单或使用其他渠道支付";


    public static final Map<Integer, String> promoTypeMap = new HashMap<>();
    public static final Map<String , String> nextMonth = new HashMap<>();

    public static final String LINE = "-";

    static{
        promoTypeMap.put(1, "满减促销");
        promoTypeMap.put(2, "满折促销");
        promoTypeMap.put(3, "满赠促销");
        promoTypeMap.put(4, "满减赠促销");
        promoTypeMap.put(5, "返点返券");


        nextMonth.put("01","02");
        nextMonth.put("02","03");
        nextMonth.put("03","04");
        nextMonth.put("04","05");
        nextMonth.put("05","06");
        nextMonth.put("06","07");
        nextMonth.put("07","08");
        nextMonth.put("08","09");
        nextMonth.put("09","10");
        nextMonth.put("10","11");
        nextMonth.put("11","12");
        nextMonth.put("12","01");
    }

    /**
     * 布尔状态(通用)
     * <AUTHOR> 2015-3-11
     */
    public static class BoolStatus {
        public static final int  YES= 1;
        public static final int  NO= 0;

    }

    /**
     * API或Web应用返回码
     * <AUTHOR> 2014-12-8
     */
    public static class ReturnCodes {
        public static final int CODE_1 = 1;
        public static final int CODE_0 = 0;
        public static final int CODE_400 = 400;
        public static final int CODE_404 = 404;
        public static final int CODE_500 = 500;
        public static final int CODE_200 = 200;
        public static final String RESULT_STATUS = "status";
        public static final String RESULT_ERRORMSG = "errorMsg";
        public static final String RESULT_ERRORCODE = "errorCode";
        public static final String RESULT_MSG = "msg";
        public static final String RESULT_SUCCESS = "success";
        public static final String RESULT_FAILURE = "failure";
    }

    /**
     * 上传规格限制
     * <AUTHOR> 2015-3-11
     */
    public static class UploadSpecs {
        /**
         * 上传单张图片允许的大小上限Byte
         */
        public static final int IMG_SIZE_LIMIT = 2 * 1024 * 1024;

        public static final int ATTACHMENT_SIZE_LIMIT = 10*1024*1024;
        /**
         * 上传图片允许的后缀类型
         */
        public static final String IMG_SUFFIX_LIST = ".gif.jpg.jpeg.bmp.png";

        public static final String ATTACHMENT_SUFFIX_LIST = ".gif.jpg.jpeg.bmp.png.pdf.ppt.pptx.doc.docx.xls.xlsx.txt";
    }

    /**
     * 日期格式
     * <AUTHOR> 2014-12-3
     */
    public static class DateFormats {
        public static final String PATTERN_STANDARD = "yyyy-MM-dd HH:mm:ss";
        public static final String PATTERN_DATE = "yyyy-MM-dd";
    }

    /**
     * 请求内容类型
     * <AUTHOR> 2014-12-4
     */
    public static class ContentType {
        public static final String TEXT_TYPE = "text/plain";
        public static final String JSON_TYPE = "application/json";
    }

    /**
     * API数据类型
     * <AUTHOR> 2014-12-4
     */
    public static class DataType {
        public static final String JSON = "json";
        public static final String XML = "xml";
        public static final String BINARY = "binary";
    }

    /**
     * 模板类型
     * <AUTHOR> 2014-12-23
     */
    public static class TemplateType {
        public static final String SMS = "sms";
        public static final String EMAIL = "email";
        public static final String NOTICE = "notice";
    }

    /**
     * 移动操作系统类型
     * <AUTHOR> 2014-12-27
     */
    public static class OsType {
        public static final String ALL="all";
        public static final String ANDROID = "android";
        public static final String IOS = "ios";
        public static final String WINPHONE = "winphone";
        public static final String PC="pc";
    }
    /**
     * 语言编码
     * <AUTHOR> 2014-12-23
     */
    public static class LanguageCodes {
        public static final String ZH = "zh";//中文
        public static final String EN = "en";//英文
        public static final String FR = "fr";//法文
    }

    /**
     * Memcached对象过期列表(单位：秒)
     * <AUTHOR> 2015-6-15
     */
    public static class MemcachedExpireds{
        public static final int EXPIRED_TIME = 1*60*60;   //过期时间，1小时
        public static final int DEMO_EXPIRED = 1*60*60;   //Demo详情1小时过期
        public static final int DEMO_LIST_EXPIRED = 1*60*60;   //Demo详情1小时过期
        public static final int SUB_CATEGORY_LIST_EXPIRED = 1*24*60*60; //商品子类目列表缓存1天
        public static final int GOODES_EXPIRED = 10*60; //商品详情10分钟过期
        public static final int GOODES_LIST_EXPIRED = 10*60; //商品分页列表缓存10分钟过期
    }

    /**
     * 后台菜单类型
     * @ClassName: AdminMenuType
     * <AUTHOR>
     * @date 2017-5-24 下午2:02:50
     */
    public static class AdminMenuType{
        public static final String DRIVER_WEBSERVICE = "司机端后台菜单";
        public static final String WEBSERVICE = "运营后台菜单";
        public static final String MAGIC_WEBSERVICE = "爬虫后台菜单";
        public static final String REPORT = "报表后台菜单";
    }

    public static class BranchProperty{
        public static final String BRANCH_CODE = "branchCode";
        public static final String BRANCH_CODE_STARTWITH= "XS";
    }

    /** KA用户标识 */
    public static final String TYPE_IS_KA = "TYPE_IS_KA";

    /** 同步标识前缀 */
    public static final String SYNC_STARTWITH = "SYNC";

    /** 订单号前缀 */
    public static final String ORDERNO_PREFIX = "YBM";

    public static final String CODE = "code";

    /*电子普通发票*/
    public static final String DZPT_INVOICE="电子普通发票";
    /*纸质普通发票*/
    public static final String ZZPT_INVOICE="纸质普通发票";

    //药帮忙资质图片文件夹路径
    public static String xyyFtpFilePath = "/ybm/xyy_template_img/";

    //模板pdf文件名
    public static String xyyPdfName = "药帮忙资质.pdf";

    //模板图片文件名
    public static String xyyFileName = "小药药委托书模板.png";

    //存放会员委托书模板img文件夹路径
    public static String merFtpFilePath = "/ybm/merchant_template_img/";

    //当前运行环境临时下载路劲
    public static String localDirectoryPath="/tmp/license_template/";

    /*saas提供查询优惠券模板名称*/
    public static final String TEMPLATE_NAME_SASS ="saas";
    /*ad系统提供查询优惠券模板名称包含*/
    public static final String TEMPLATE_NAME_AD ="ad";

    /*B2B 药帮忙*/
    public static final String CHANNEL_CODE_B2B ="1";
    /*B2C 宜块钱*/
    public static final String CHANNEL_CODE_B2C ="2";

    /**
     * 搜索返回热搜词最大数量
     */
    public static final int MAX_HOT_WORD_SIZE = 3;

    /**
     * 搜索推荐曝光埋点上报事件名称
     */
    public static final String SEARCH_RECOMMEND_ACTION_TYPE = "sid_tracing_generation";

    public static final String REGION = "ap-beijing";

    public static final String CAPTCHA_TYPE = "9";

    public static final Integer CAPTCHA_APP_ID = **********;

    public static final String APP_SECRET_KEY = "0aqzom2LDN1wAUv31HEZzlQ**";

    public static final String SECRET_ID = "AKIDykHqCHNPFp8EF8l4Tp5oHXWHm2TfQKmR";

    public static final String SECRET_KEY = "WnvmN7ax2qNcZIo4LM7pM5XksjIXVBhH";

    public static final String ENDPOINT = "captcha.tencentcloudapi.com";
    public static final String CLAIM_VOUCHER_DIALOG = "已帮您自动领取%s张优惠券，下单更省钱";

    /**
     * 逗号字符
     */
    public static final String COMMA_SEPARATOR_CHAR = ",";

    /**
     * 搜索推荐曝光埋点上报事件名称
     */
    public static final String SKU_LIST_ACTION_TYPE = "order_list";

    /**
     * 搜索聚合曝光
     */
    public static final String ACT_PAGE_CATEGORY_COMMODITY_EXPOSURE = "ActPage_CategoryCommodity_Exposure";

    /**
     * 分类统计KEY
     */
    public static final String AGG_CAT_STATS = "catStats";

    /**
     * 埋点事件类型：登录成功
     */
    public static final String EVENT_ACTION_TYPE_LOGIN_SUCCESS = "pc_action_login";

    /**
     * 是否上传过的常量
     */
    public interface Upload {
        Integer UPLOADED = 1;
        Integer UNUPLOAD = 0;
    }

    /**
     * 首推优选
     */
    public static final String SHOU_TUI_YOU_XUAN_TEXT = "【首推优选】";

    /**
     * 拼团
     */
    public static final String PT_TEXT = "";


    /**
     * 虚拟店铺编码
     */
    public static final String VIR_SHOP_CODE = "DP999";

    /**
     * 专区标签前缀
     */
    public static final String ACT_TAG_PRE = "ZS_";

    /**
     * 页面提示：网络异常
     */
    public static final String MSG_ERROR = "网络异常";

    /**
     * 搜索es中商品优惠券标签前缀
     */
    public static final String SEARCH_PRODUCT_COUPON_PREFIX = "ZS_System_Coupon_";

    /**
     * 专品券 + 跨店券
     */
    public static Set<Integer> MULTI_SHOP_COUPON_TYPE_SET = new HashSet<Integer>(){{
        add(CROSS_PLATFORM.getId());
        add(SPECIALTY.getId());
    }};

    /**
     * 随心拼标签
     */
    public static final String YBM_ACT_SUI_XIN_PIN = "YBM_ACT_SUI_XIN_PIN";

    /**
     * 负一
     */
    public static final Long NEGATIVE_ONE = -1L;
    /**
     * 最大返回数量
     */
    public static final Integer SEARCH_MAX_PAGE_SIZE = 20;

    /**
     * top 8
     */
    public static final Integer TOP_EIGHT = 8;
}
