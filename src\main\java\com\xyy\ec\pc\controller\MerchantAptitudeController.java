package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.api.MerchantAptitudeBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBankinfoBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantAptitudeBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantBankinfoBussinessDto;
import com.xyy.ec.order.core.util.CollectionUtil;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.FileUploadUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PC余额提现资质及账户信息认证
 *<AUTHOR>
 */
@Controller
@RequestMapping("/aptitude")
public class MerchantAptitudeController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantAptitudeController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private MerchantAptitudeBussinessApi    merchantAptitudeBussinessApi;

    @Reference(version = "1.0.0")
    private MerchantBankinfoBussinessApi merchantBankinfoBussinessApi;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Autowired
    private XyyConfig.CdnConfig cdnConfig;

    /**
     * 跳转余额提现资质认证页面
     * @return
     */
    @RequestMapping("/indexAptitude.htm")
    public ModelAndView indexAptitude()  {
        Map<String,Object> model = new HashMap<String,Object>();
        try {
            String cdnPath = cdnConfig.getCdnHostname();
            MerchantPrincipal merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            model.put("cdnPath",cdnPath);
            model.put("merchantId",merchant.getId());
            return new ModelAndView("/merchantAptitude/aptitude.ftl",model);
        }catch (Exception e){
            LOGGER.error("跳转余额提现资质认证页面异常,e="+e);
            return new ModelAndView("/merchantAptitude/aptitude.ftl",model);
        }
    }

    /**
     * 提交相关资质以及账户信息
     * @param merchantBankinfoDTO
     * @return
     */
    @ResponseBody
    @RequestMapping("/submitAptitude")
    public Object submitAptitude(@RequestBody MerchantBankinfoBussinessDto merchantBankinfoDTO, HttpServletRequest request) {
        if (merchantBankinfoDTO.getMerchantId() == null){
            return this.addError("用户ID不能为空！");
        }
        if (CollectionUtil.isEmpty(merchantBankinfoDTO.getMerchantAptitudeList())){
            return this.addError("用户相关资质信息不能为空！");
        }
        try {
            String branchCode =merchantBussinessApi.getBranchCodeByMerchantId(merchantBankinfoDTO.getMerchantId());
            merchantBankinfoDTO.setBranchCode(branchCode);
            if (merchantBankinfoDTO.getId() == null){
                merchantBankinfoBussinessApi.submitMerchantAptitud(merchantBankinfoDTO);
            }else{
                merchantBankinfoBussinessApi.updateMerchantAptitude(merchantBankinfoDTO);
            }
            return this.addResult("提交成功！");
        } catch (Exception e) {
            LOGGER.error("提交相关资质以及账户信息异常：",e);
            return this.addError("提交相关资质以及账户信息异常！");
        }
    }

    /**
     * 上传提现资质相关图片
     * @param request
     * @param response
     * @return
     */
    @ResponseBody
    @RequestMapping("/uploadAptitudeImg")
    public Object uploadFile(HttpServletRequest request, HttpServletResponse response) {
        try {
            //上传文件所属目录
            String uploadPath = "/ybm/aptitude/";
            String targetFileName = "";
            String localTempPath = System.getProperty("xyy-shop");
            Map<String, Object> stringObjectMap = FileUploadUtil.fileUpload(request, uploadPath, cdnConfig, targetFileName, localTempPath);
            return stringObjectMap;

        } catch (Exception e) {
            LOGGER.error("上传资质相关图片异常,e"+e);
            return this.addError("上传资质相关图片异常！");
        }
    }

    /**
     * 查看资质和账号信息审核状态
     * @param merchantId
     * @return
     */
    @ResponseBody
    @RequestMapping("/viewAptitude.htm")
    public Object viewAptitude(Long merchantId) {
        Map<String,Object> map = new HashMap<>();
        if (merchantId == null){
            return this.addError("用户ID不能为空！");
        }

        //资质状态判断
        int state= 0;
        //资质详情判断
        int s = 0;
        try {
        //通过用户id查询用户账户信息
        MerchantBankinfoBussinessDto merchantBankinfo = merchantBankinfoBussinessApi.selectBankinfoByMerchantId(merchantId);
        if (merchantBankinfo.getStatus() == Constants.IS3 || merchantBankinfo.getBankCardStatus() != Constants.IS1){
            state = 1;
            if (merchantBankinfo.getStatus() == Constants.IS3 && merchantBankinfo.getBankCardStatus() == Constants.IS1){
                s= 1;
            }
        }
        List<MerchantAptitudeBussinessDto> merchantAptitudeList = new ArrayList<>();
        if (merchantBankinfo.getId() != null){
            //通过银行信息id查询资质详情
            merchantAptitudeList = merchantAptitudeBussinessApi.selectAptitueByMerchantBankinfoId(merchantBankinfo.getId());
        }
            map.put("merchantBankinfo",merchantBankinfo);
            map.put("merchantAptitudeList",merchantAptitudeList);
            map.put("merchantId",merchantId);
            map.put("id",merchantBankinfo.getId());
            map.put("state",state);
            map.put("s",s);
        } catch (Exception e) {
            LOGGER.error("出现异常,e="+e);
        }
        return new ModelAndView("/merchantAptitude/viewAptitude.ftl",map);
    }

    /**
     * 委托书下载
     * @param response
     * @throws IOException
     */
    @RequestMapping("/batchDownload")
    public void batchDownload(HttpServletResponse response) throws IOException {
        try {
            String localTempPath = System.getProperty("xyy-shop");
//            CDNConfig config = SysConfig.getCDNConfig();
//            FileDownloadUtil.fileDownload(response, "ybm/aptitudeDownload/", config, localTempPath);
//            FileDownloadUtil.fileDownload(response, "ybm/aptitudeDownload/", config, localTempPath);
            //todo 待补充下载组件
        } catch (Exception e) {
            LOGGER.error(e.toString());
            PrintWriter pw = response.getWriter();
            pw.println("下载失败!");
            pw.flush();
        }
    }

}
