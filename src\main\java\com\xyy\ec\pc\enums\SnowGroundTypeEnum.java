package com.xyy.ec.pc.enums;

import lombok.Getter;

/**
 * 雪地埋点sptype-来源类型
 *
 * @author: denghp
 * @date: 2022/1/12
 */
public enum SnowGroundTypeEnum {

    /**
     * 搜索页面
     */
    SEARCH("search", 1),

    /**
     * 分类页面
     */
    CAT_PAGE("cat_page", 2),

    /**
     * 推荐页
     */
    RECOMMEND("recommend", 3),

    /**
     * 活动列表页
     */
    ACT_PAGE("act_page", 4),
    /**
     * 再次购买
     */
    BUY_AGAIN("buy_again", 5),
    /**
     * 常购清单
     */
    ALWAYS_BUY_LIST("always_buy_list", 6),
    /**
     * 店铺
     */
    SHOP("shop", 14),

    /**
     * 分类聚合
     */
    AGG_CAT("agg_cat", 15),
    ;

    /**
     * 来源类型名称
     */
    private String name;

    /**
     * 来源类型值
     */
    private Integer value;

    SnowGroundTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public static SnowGroundTypeEnum getByValue(Integer value) {
        if (value != null) {
            SnowGroundTypeEnum[] typeEnums = values();
            int length = typeEnums.length;
            for (int i = 0; i < length; i++) {
                SnowGroundTypeEnum typeEnum = typeEnums[i];
                if (value.equals(typeEnum.value)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 店铺搜索页面来源
     */
    @Getter
    public enum ShopSpFrom {
        SHOP_CAT_LIST("shop_cat_list", 1),
        SHOP_SEARCH("shop_search",2);
        /**
         * 来源页面名称
         */
        private String name;

        /**
         * 来源页面值
         */
        private Integer value;

        ShopSpFrom(String name, Integer value) {
            this.name = name;
            this.value = value;
        }
        public static ShopSpFrom getByValue(Integer value) {
            if (value != null) {
                ShopSpFrom[] spFroms = values();
                int length = spFroms.length;
                for (int i = 0; i < length; i++) {
                    ShopSpFrom spFrom = spFroms[i];
                    if (value.equals(spFrom.value)) {
                        return spFrom;
                    }
                }
            }
            return null;
        }
    }
    /**
     * 分类聚合来源页
     *
     * @author: denghp
     * @date: 2022/1/12
     */
    public enum AggCatSpFrom {
        /**
         * 搜索页面
         */
        SEARCH("search", 1),
        /**
         * 店铺页面
         */
        SHOP("shop", 2),
        /**
         * 活动页面品类商品流
         */
        ACT_SKU_PAGE("act_sku_page", 3),
        /**
         * 活动页拼团商品流
         */
        ACT_PT_PAGE("act_pt_page", 4),
        /**
         * 活动页秒杀商品流
         */
        ACT_SK_PAGE("act_sk_page", 5),
        ;
        /**
         * 来源页面名称
         */
        private String name;

        /**
         * 来源页面值
         */
        private Integer value;

        AggCatSpFrom(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public static AggCatSpFrom getByValue(Integer value) {
            if (value != null) {
                AggCatSpFrom[] spFroms = values();
                int length = spFroms.length;

                for (int i = 0; i < length; i++) {
                    AggCatSpFrom spFrom = spFroms[i];
                    if (value.equals(spFrom.value)) {
                        return spFrom;
                    }
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 常购清单来源页
     *
     * @author: denghp
     * @date: 2022/1/12
     */
    public enum AlwaysBuyListSpFrom {
        /**
         * APP首页常购清单
         */
        APP_INDEX_ABL("app_index_abl", 1),
        /**
         * APP发现页面常购清单
         */
        APP_DISCOVER_ABL("app_discover_abl", 2),

        ;
        /**
         * 来源页面名称
         */
        private String name;

        /**
         * 来源页面值
         */
        private Integer value;

        AlwaysBuyListSpFrom(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public static AlwaysBuyListSpFrom getByValue(Integer value) {
            if (value != null) {
                AlwaysBuyListSpFrom[] spFroms = values();
                int length = spFroms.length;

                for (int i = 0; i < length; i++) {
                    AlwaysBuyListSpFrom spFrom = spFroms[i];
                    if (value.equals(spFrom.value)) {
                        return spFrom;
                    }
                }
            }
            return null;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 推荐来源页面
     *
     * @author: denghp
     * @date: 2022/1/12
     */
    public enum RecommendSpFrom {
        /**
         * APP首页推荐
         */
        APP_INDEX_REC("app_recommend", 1),
        /**
         * APP详情页推荐
         */
        APP_SKU_REC("app_sku_recommend", 2),
        /**
         * PC详情页推荐
         */
        PC_SKU_REC("pc_sku_recommend", 3),
        /**
         * PC搜索无结果推荐
         */
        PC_SEARCH_NO_RECALL_REC("pc_search_no_recall_recommend", 4),
        /**
         * APP搜索无结果推荐
         */
        APP_SEARCH_NO_RECALL_REC("app_search_no_recall_recommend", 5),
        /**
         * APP发现页推荐
         */
        APP_DISCOVER_REC("app_discover_recommend", 6),
        /**
         * APP商详页底部推荐
         */
        APP_SKU_BOTTOM_REC("app_sku_bottom_recommend", 7),

        /**
         * APP购物车底部推荐
         */
        APP_SHOP_CART_BOTTOM_REC("app_shop_cart_bottom_recommend", 8),

        /**
         * APP我的页面底部推荐
         */
        APP_MY_CENTER_BOTTOM_REC("app_my_center_bottom_recommend", 9),

        /**
         * APP订单支付完成底部推荐
         */
        APP_PAY_FINISH_BOTTOM_REC("app_pay_finish_bottom_recommend", 10),

        /**
         * 搜索启动页推荐
         */
        APP_SEARCH_START_REC("app_search_start_REC", 11),

        /**
         * 首页拼团推荐
         */
        INDEX_PT_REC("index_pt_recommend", 12),
        /**
         * 商详页拼团推荐
         */
        SKU_PT_REC("sku_pt_recommend", 13),
        /**
         * 店铺首页楼层
         */
        SHOP_FLOOR_REC("shop_floor_recommend", 14),
        /**
         * 搜索启动页拼团推荐
         */
        SEARCH_START_PT_REC("search_start_pt_recommend", 15),
        /**
         * 找相似页
         */
        FIND_SIMILAR("find_similar_recommend", 16),


        /**
         * 搜索启动页榜单
         */
        SEARCH_START_PAGE_HOT("search_start_page_hot", 17),
        ;
        /**
         * 来源页面名称
         */
        private String name;

        /**
         * 来源页面值
         */
        private Integer value;

        RecommendSpFrom(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public static RecommendSpFrom getByValue(Integer value) {
            if (value != null) {
                RecommendSpFrom[] spFroms = values();
                int length = spFroms.length;

                for (int i = 0; i < length; i++) {
                    RecommendSpFrom spFrom = spFroms[i];
                    if (value.equals(spFrom.value)) {
                        return spFrom;
                    }
                }
            }

            return null;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * 雪地搜索具体来源页面
     *
     * @author: denghp
     * @date: 2022/1/12
     */
    public enum SearchSpFrom {
        /**
         * 店铺搜索
         */
        SEARCH("search", 1),
        /**
         * 店铺搜索
         */
        SHOP_SEARCH("shop_search", 2),
        /**
         * 专区搜索
         */
        ACT_SEARCH("act_search", 3),

        /**
         * 随心拼搜索
         */
        ACT_SUI_XIN_PIN_SEARCH("act_sui_xin_pin_search", 4),

        /**
         * 随心拼-待确认订单页
         */
        SUI_XIN_PIN_CONFIRM_ORDER_PAGE("SUI_XIN_PIN_CONFIRM_ORDER_PAGE", 8),

        ;
        /**
         * 来源页面名称
         */
        private String name;

        /**
         * 来源页面值
         */
        private Integer value;

        SearchSpFrom(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public static SearchSpFrom getByValue(Integer value) {
            if (value != null) {
                SearchSpFrom[] spFroms = values();
                int length = spFroms.length;

                for (int i = 0; i < length; i++) {
                    SearchSpFrom spFrom = spFroms[i];
                    if (value.equals(spFrom.value)) {
                        return spFrom;
                    }
                }
            }

            return null;
        }

        public String getName() {
            return name;
        }

        public Integer getValue() {
            return value;
        }
    }
}
