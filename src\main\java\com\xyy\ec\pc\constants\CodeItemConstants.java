package com.xyy.ec.pc.constants;



/**
 * 
 * <AUTHOR>
 *
 */
public class CodeItemConstants {
	
	public static final String EMAIL_PERSON = "email_person";			//电子发票邮件发送人
	
	public static final String PROMO_COLOR = "promo_color";			//电子发票邮件发送人

   public static final String USE_PROVINCE_CODE = "USE_PROVINCE_CODE"; // 投放地区


   public static final String ALL = "all"; // 查询商品跳转页面
	
	   
	   
   //客户资质限定开始时间
   public static final String CLIENT_BEGIN_DATE= "client_begin_time";
   
   //客户资质限定结束时间
   public static final String ClIENT_END_DATE= "client_end_time";
   
 
   
   
   
   //是否显示提现按钮
   public static final String CASH_BRANCH_CODE = "CASH_BRANCH_CODE";
   
   //开始认证的key
   public static final String BEGIN_APTITUDE = "BEGIN_APTITUDE";
   
   public static final String BEGIN_ACTION = "BEGIN_ACTION";
   
   public static final String BEGIN_TEXT = "BEGIN_TEXT";
   
   
   //再次认证的key
   public static final String AGAIN_APTITUDE = "AGAIN_APTITUDE";
   
   public static final String AGAIN_ACTION = "AGAIN_ACTION";
   
   
   public static final String AGAIN_TEXT = "AGAIN_TEXT";
   
   
   //等待认证的key
   public static final String WAITING_APTITUDE = "WAITING_APTITUDE";
   
   //等待的动作
   public static final String WAITING_ACTION = "WAITING_ACTION";
   
   //等待的提示语
   public static final String WAITING_TEXT = "WAITING_TEXT";
   
   //成功跳转的key
   public static final String SUCCESS_ACTION = "SUCCESS_ACTION";
   
   public static final String SMS_CODE = "SMS_CODE";  //短信平台开关

   //跳转到消息中心
   public static final String MSG_CENTER = "MSG_CENTER";
   
   
   //跳转到有货列表
   public static final String HAS_GOODS = "HAS_GOODS";
   
   //降价跳转
   public static final String MSG_CENTER_DOWN_PRICE = "MSG_CENTER_DOWN_PRICE";

   //降价跳转url
   public static final String DOWN_PRICE = "DOWN_PRICE";

   //跳转到订单详情
   public static final String ORDER_DETAIL="ORDER_DETAIL";
   
}
