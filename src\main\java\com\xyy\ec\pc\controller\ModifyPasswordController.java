package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantPwdModifiLogBussinessApi;
import com.xyy.ec.merchant.bussiness.api.account.LoginAccountApi;
import com.xyy.ec.merchant.bussiness.api.admin.MerchantAdminBusinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantPwdModifiLogBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.account.LoginAccountDto;
import com.xyy.ec.merchant.bussiness.dto.account.UpdateAccountPasswordVo;
import com.xyy.ec.merchant.bussiness.utils.Pwdverifiers;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.rpc.MerchantServiceRpc;
import com.xyy.ec.pc.util.MD5Util;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 修改密码
 * @Author: WanKp
 * @Date: 2018/8/25 21:48
 **/
@RequestMapping("/merchant/center/password")
@Controller
public class ModifyPasswordController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Reference(version = "1.0.0")
    private MerchantPwdModifiLogBussinessApi logBussinessApi;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Autowired
    private MerchantServiceRpc merchantServiceRpc;

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    @Reference(version = "1.0.0")
    private MerchantAdminBusinessApi merchantAdminBusinessApi;

    /**
     * 修改密码页面入口
     *
     * @return
     */
    @RequestMapping(value = "/modifyPassword.htm", method = RequestMethod.GET)
    public ModelAndView modifyPassword() {
        Map<String, Object> model = new HashMap<String, Object>();
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            logger.error("修改密码异常,", e);
        }
        if (merchant == null) {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }
        model.put("center_menu", "password");
        return new ModelAndView("/password/modifyPassword.ftl", model);
    }

    /**
     * 修改密码-修改密码
     *
     * @param oldPassword
     * @param newPassword
     * @return
     * @throws Exception
     */
    @RequestMapping("/updatePassword.htm")
    public ModelAndView updatePassword(@RequestParam(value = "oldPassword", required = false) String oldPassword, @RequestParam("newPassword") String newPassword) {
        Map<String, Object> model = new HashMap<String, Object>();
        try {
            String checkResult = Pwdverifiers.checkPwd(newPassword);
            if (!checkResult.equals("OK")) {
                model.put("msg", checkResult);
                return new ModelAndView("/password/modifyPassword.ftl", model);
            }
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            LoginAccountDto loginAccountDto = merchantServiceRpc.selectLoginAccountById(merchant.getAccountId());
            if(loginAccountDto == null){
                logger.error("根据账号id{}查询当前账号信息为空，请重新登陆。", merchant.getAccountId());
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            if (StringUtil.isNotEmpty(oldPassword)) {//修改密码
//                MerchantBussinessDto tempMerchant = merchantBussinessApi.selectByPrimaryKey(merchant.getId());
                if (!loginAccountDto.getPassword().equals(MD5Util.getMD5Str(oldPassword))) {
                    model.put("msg", "当前密码错误,请确认后重新输入");
                    return new ModelAndView("/password/modifyPassword.ftl", model);
                }
            }
//            merchant.setPassword(MD5Util.getMD5Str(newPassword));
//            merchant.setUpdateTime(new Date());
//            merchantBussinessApi.updateByPrimaryKeySelective(merchant);
            loginAccountDto.setPassword(MD5Util.getMD5Str(newPassword));
            merchantServiceRpc.updateLoginAccount(loginAccountDto);
            //todo insert 主动修改密码记录
            MerchantPwdModifiLogBussinessDto logBussinessDto = new MerchantPwdModifiLogBussinessDto();
            logBussinessDto.setMerchantId(merchant.getId());
            logBussinessDto.setType(2);
            logBussinessApi.insert(logBussinessDto);
            // 关闭账号强制密码修改
            UpdateAccountPasswordVo updateAccountPasswordVo = UpdateAccountPasswordVo.builder()
                    .accountId(loginAccountDto.getId())
                    .userId(loginAccountDto.getId())
                    .username(loginAccountDto.getContactName())
                    .reason("完成修改密码, 解除账号强制修改密码")
                    .build();
            merchantAdminBusinessApi.liftForceUpdatePasswordById(updateAccountPasswordVo);
            // ******** 删除 accountId 所有会话
            loginAccountApi.clearAccountSessionId(merchant.getAccountId());
            return new ModelAndView(new RedirectView("/merchant/center/index.htm",true,false));
        } catch (Exception e) {
            logger.error("修改密码异常-e : {}", e.getMessage());
            model.put("msg", "当前密码修改失败,请稍后重试!");
            return new ModelAndView("/password/modifyPassword.ftl", model);
        }
    }

}
