package com.xyy.ec.pc.cms.helpers;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingSkuDto;
import com.xyy.ec.pc.cms.vo.CmsListProductVO;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.TagTypeEnum;
import com.xyy.ec.pc.service.marketing.dto.MarketingGroupBuyingActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.pc.service.marketing.dto.MarketingWholesaleActivityInfoDTO;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.product.business.dto.TagDTO;
import com.xyy.ec.product.business.dto.listOfSku.ListProduct;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * {@link CmsListProductVO} 帮助类
 *
 * <AUTHOR>
 */
@Component
public class CmsListProductVOHelper {


    private static String uploadPathUrl;
    private static String markerUrl;
    private final static String imgPath = "/ybm/product/min/";


    private static String traceCodeTagName;

    @Value("${traceCodeTagName:有追溯码}")
    public void setTraceCodeTagName(String str) {
        traceCodeTagName = str;
    }


    @Value("${config.product_image_path_url}")
    public void setStaticUploadPathUrl(String str) {
        uploadPathUrl = str;
    }


    @Value("${oss.url}")
    public void setMarkerUrl(String str) {
        markerUrl = str;
    }

    /**
     * 隐藏商品的拼团活动价格信息
     *
     * @param cmsListProductVO
     * @return
     */
    public static void hideActPtPriceInfo(CmsListProductVO cmsListProductVO) {
        if (Objects.isNull(cmsListProductVO) || Objects.isNull(cmsListProductVO.getActPt())) {
            return;
        }
        MarketingGroupBuyingActivityInfoDTO actPt = cmsListProductVO.getActPt();
        actPt.setAssemblePrice(BigDecimal.ZERO);
        actPt.setMinSkuPrice(BigDecimal.ZERO);
        actPt.setMaxSkuPrice(BigDecimal.ZERO);
        actPt.setStartingPriceShowText("");
        actPt.setRangePriceShowText("");
        actPt.setStepPriceShowTexts(Lists.newArrayList());
    }

    /**
     * 设置商品的拼团活动信息
     *
     * @param cmsListProductVO
     * @param groupBuyingInfoDto
     * @return
     */
    public static CmsListProductVO setActPt(CmsListProductVO cmsListProductVO, GroupBuyingInfoDto groupBuyingInfoDto) {
        if (Objects.isNull(cmsListProductVO) || Objects.isNull(groupBuyingInfoDto)) {
            return cmsListProductVO;
        }
        /* 活动信息 */
        // 拼团活动状态 1.未开始 ，2.拼团中，3.已结束 兼容老逻辑前端拼团活动状态 0-未开始 1-进行中 2-结束
        Integer assembleStatus = groupBuyingInfoDto.getStatus() == null ? 0 : groupBuyingInfoDto.getStatus() - 1;
        BigDecimal percentage = groupBuyingInfoDto.getPercentage();
        percentage = (Objects.isNull(percentage) || percentage.compareTo(BigDecimal.ZERO) < 0) ? BigDecimal.ZERO : percentage;
        Long assembleStartTime = Objects.nonNull(groupBuyingInfoDto.getStartTime()) ? groupBuyingInfoDto.getStartTime().getTime() : 0L;
        Long assembleEndTime = Objects.nonNull(groupBuyingInfoDto.getEndTime()) ? groupBuyingInfoDto.getEndTime().getTime() : 0L;
        Long surplusTime = (assembleEndTime - System.currentTimeMillis()) / 1000;
        surplusTime = surplusTime <= 0L ? 0L : surplusTime;
        MarketingGroupBuyingActivityInfoDTO groupBuyingActivityInfoDTO = MarketingGroupBuyingActivityInfoDTO.builder()
                .marketingId(groupBuyingInfoDto.getMarketingId())
                .activityType(groupBuyingInfoDto.getActivityType())
                .percentage(String.valueOf(percentage.multiply(new BigDecimal(100).setScale(0, BigDecimal.ROUND_UP))))
                .assembleStatus(assembleStatus)
                .assembleStartTime(assembleStartTime)
                .assembleEndTime(assembleEndTime)
                .surplusTime(surplusTime)
                .orderNum(groupBuyingInfoDto.getOrderNum())
                .preheatShowPrice(groupBuyingInfoDto.getPreheatShowPrice())
                // 拼团多阶梯价信息
                .stepPriceStatus(groupBuyingInfoDto.getStepPriceStatus())
                .minSkuPrice(groupBuyingInfoDto.getMinSkuPrice())
                .maxSkuPrice(groupBuyingInfoDto.getMaxSkuPrice())
                .startingPriceShowText(groupBuyingInfoDto.getStartingPriceShowText())
                .rangePriceShowText(groupBuyingInfoDto.getRangePriceShowText())
                .stepPriceShowTexts(groupBuyingInfoDto.generateStepPriceShowTexts(cmsListProductVO.getProductUnit()))
                .supportSuiXinPin(groupBuyingInfoDto.isSupportSuiXinPin())
                .suiXinPinButtonText(groupBuyingInfoDto.getSuiXinPinButtonText())
                .suiXinPinButtonBubbleText(groupBuyingInfoDto.getSuiXinPinButtonBubbleText())
                .build();
        /* 活动的商品信息 */
        GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtils.isNotEmpty(groupBuyingInfoDto.getGroupBuyingSkuDtoList())
                ? groupBuyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
        Long merchantCount = 0L;
        if (Objects.nonNull(groupBuyingSkuDto)) {
            groupBuyingActivityInfoDTO.setSkuStartNum(groupBuyingSkuDto.getSkuStartNum());
            groupBuyingActivityInfoDTO.setAssemblePrice(groupBuyingSkuDto.getSkuPrice());
            merchantCount = groupBuyingSkuDto.getMerchantCount();
        }
        if (Objects.isNull(merchantCount) || merchantCount < 0) {
            merchantCount = 0L;
        }
        String merchantCountDesc = MessageFormat.format("{0}人已拼团", String.valueOf(merchantCount));
        groupBuyingActivityInfoDTO.setMerchantCount(merchantCount);
        groupBuyingActivityInfoDTO.setMerchantCountDesc(merchantCountDesc);
        /* 调整商品的showName */
        String productUnit = Optional.ofNullable(cmsListProductVO.getProductUnit()).orElse("");
        String showName = Optional.ofNullable(cmsListProductVO.getShowName()).orElse("");
        StringBuilder sbShowName = new StringBuilder();
        if (groupBuyingActivityInfoDTO.getSkuStartNum() != null) {
            //校验商品属性 修改文案 拼团-》首推
            String tagName = "";
            if (Constants.IS1 == cmsListProductVO.getFirstChoose() && Constants.highGross.contains(cmsListProductVO.getHighGross())) {
                tagName = "【首推优选】";
            } else if (StringUtils.isNotEmpty(groupBuyingInfoDto.getTopicPrefix())) {
                tagName = groupBuyingInfoDto.getTopicPrefix();
            }
            sbShowName.append(tagName).append(groupBuyingActivityInfoDTO.getSkuStartNum())
                    .append(productUnit).append("包邮")
                    .append(" ").append(showName);
        } else {
            //校验商品属性 修改文案 拼团-》首推
            String tagName = "";
            if (Constants.IS1 == cmsListProductVO.getFirstChoose() && Constants.highGross.contains(cmsListProductVO.getHighGross())) {
                tagName = "【首推优选】";
            } else if (StringUtils.isNotEmpty(groupBuyingInfoDto.getTopicPrefix())) {
                tagName = groupBuyingInfoDto.getTopicPrefix();
            }
            sbShowName.append(tagName).append(showName);
        }
        cmsListProductVO.setShowName(sbShowName.toString());
        /* 设置拼团活动信息 */
        cmsListProductVO.setActPt(groupBuyingActivityInfoDTO);
        return cmsListProductVO;
    }

    /**
     * 设置商品的批购包邮活动信息
     *
     * @param buyingInfoDto
     * @return
     */
    public static CmsListProductVO setActPgby(CmsListProductVO cmsListProductVO, GroupBuyingInfoDto buyingInfoDto) {
        if (Objects.isNull(cmsListProductVO) || Objects.isNull(buyingInfoDto)) {
            return cmsListProductVO;
        }
        /* 活动信息 */
        MarketingWholesaleActivityInfoDTO wholesaleActivityInfoDTO = MarketingWholesaleActivityInfoDTO.builder()
                .marketingId(buyingInfoDto.getMarketingId())
                .activityType(buyingInfoDto.getActivityType())
                .supportSuiXinPin(buyingInfoDto.isSupportSuiXinPin())
                .suiXinPinButtonText(buyingInfoDto.getSuiXinPinButtonText())
                .suiXinPinButtonBubbleText(buyingInfoDto.getSuiXinPinButtonBubbleText())
                .build();
        /* 活动的商品信息 */
        GroupBuyingSkuDto groupBuyingSkuDto = CollectionUtils.isNotEmpty(buyingInfoDto.getGroupBuyingSkuDtoList())
                ? buyingInfoDto.getGroupBuyingSkuDtoList().get(0) : null;
        if (Objects.nonNull(groupBuyingSkuDto)) {
            wholesaleActivityInfoDTO.setSkuStartNum(groupBuyingSkuDto.getSkuStartNum());
            wholesaleActivityInfoDTO.setAssemblePrice(groupBuyingSkuDto.getSkuPrice());
        }
        /* 调整商品的showName */
        String productUnit = Optional.ofNullable(cmsListProductVO.getProductUnit()).orElse("");
        String showName = Optional.ofNullable(cmsListProductVO.getShowName()).orElse("");
        StringBuilder sbShowName = new StringBuilder();
        if (wholesaleActivityInfoDTO.getSkuStartNum() != null) {
            sbShowName.append(wholesaleActivityInfoDTO.getSkuStartNum())
                    .append(productUnit).append("包邮")
                    .append(" ").append(showName);
        } else {
            sbShowName.append(showName);
        }
        // 追加规格显示
        if (StringUtils.isNotEmpty(cmsListProductVO.getSpec()) && !Objects.equals(cmsListProductVO.getSpec(), Constants.LINE)) {
            sbShowName.append("/").append(cmsListProductVO.getSpec());
        }
        cmsListProductVO.setShowName(sbShowName.toString());
        /* 设置批购包邮活动信息 */
        cmsListProductVO.setActPgby(wholesaleActivityInfoDTO);
        return cmsListProductVO;
    }

    /**
     * 创建
     *
     * @param listProduct
     * @param actSk
     * @return
     */
    public static CmsListProductVO create(ListProduct listProduct, MarketingSeckillActivityInfoDTO actSk) {
        if (listProduct == null) {
            return null;
        }
        CmsListProductVO productInfoVO = new CmsListProductVO();
        BeanUtils.copyProperties(listProduct, productInfoVO);
        if (StrUtil.isNotBlank(listProduct.getImageUrl())) {
            productInfoVO.setImageUrlPath(uploadPathUrl + imgPath + listProduct.getImageUrl());
        }
        if (StrUtil.isNotBlank(listProduct.getMarkerUrl())) {
            productInfoVO.setMarkerUrlPath(markerUrl + listProduct.getMarkerUrl());
        }
        Map<String, Object> tags = Maps.newHashMap();
        tags.put("productTags", listProduct.getProductTagList());
        tags.put("titleTags", listProduct.getTitleTagList());
        tags.put("markerTag", listProduct.getMarkerUrl());
        tags.put("activityTag", listProduct.getActivityTag());
        tags.put(TagTypeEnum.UNIT_PRICE_TAG.getName(), listProduct.getUnitPriceTag());
        tags.put("dataTags", listProduct.getExpressList());
        productInfoVO.setTags(tags);
        productInfoVO.setActPt(null);
        productInfoVO.setActSk(actSk);
        return productInfoVO;
    }


    /**
     * 合并展示标签
     *
     * @param productTagList 标签集合
     * @param expressList    快递标签集合
     * @param unitPriceTag   单价标签
     * @param firstTagList   追溯码
     * @return 合并后的标签集合
     */
    public static List<TagDTO> handlerTags(List<TagDTO> productTagList, List<TagDTO> expressList, TagDTO unitPriceTag, List<TagDTO> firstTagList) {
        List<TagDTO> tempTags = new ArrayList<>();
        // 添加单价标签  公斤价
        if (BeanUtil.isNotEmpty(unitPriceTag)) {
            tempTags.add(unitPriceTag);
        }
        if (CollUtil.isNotEmpty(productTagList)) {
            // 优先添加"次日达"
            Optional<TagDTO> nextDayDelivery = productTagList.stream()
                    .filter(tag -> "次日达".equals(tag.getName()) || "隔日达".equals(tag.getName()))
                    .findFirst();
            nextDayDelivery.ifPresent(tempTags::add);
        }
        if (CollUtil.isNotEmpty(firstTagList)) {
            //  第三位 添加 有追溯码
            Optional<TagDTO> traceCodeTag = firstTagList.stream()
                    .filter(tag -> traceCodeTagName.equals(tag.getName()))
                    .findFirst();
            traceCodeTag.ifPresent(tempTags::add);
        }

        if (CollUtil.isNotEmpty(productTagList) && tempTags.size() < 3) {
            // 添加剩余的productTagList元素（排除次日达/隔日达）
            productTagList.stream()
                    .filter(tag -> !"次日达".equals(tag.getName()) && !"隔日达".equals(tag.getName()))
                    .forEach(tag -> {
                        if (tempTags.size() < 3) {
                            tempTags.add(tag);
                        }
                    });
        }
        // 添加expressList元素直到达到最多三个
        if (CollUtil.isNotEmpty(expressList) && tempTags.size() < 3) {
            expressList.stream()
                    .limit(3 - tempTags.size())
                    .forEach(tempTags::add);
        }
        List<TagDTO> showProductTags = BeanUtil.copyToList(tempTags, TagDTO.class);
        showProductTags.forEach(tag -> {
            if (StrUtil.isNotBlank(tag.getAppIcon())) {
                tag.setAppIcon(uploadPathUrl + tag.getAppIcon());
            }
            if (StrUtil.isNotBlank(tag.getPcIcon())) {
                tag.setPcIcon(uploadPathUrl + tag.getPcIcon());
            }
        });
        return showProductTags;
    }

    /**
     * 创建
     *
     * @param listProduct
     * @return
     */
    public static CmsListProductVO create(ListProduct listProduct) {
        return create(listProduct, null);
    }

    /**
     * 创建
     *
     * @param listProducts
     * @return
     */
    public static List<CmsListProductVO> creates(List<ListProduct> listProducts) {
        if (CollectionUtils.isEmpty(listProducts)) {
            return Lists.newArrayList();
        }
        return listProducts.stream().map(CmsListProductVOHelper::create).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 创建
     *
     * @return
     */
    public static void setTagInfo(CmsListProductVO cmsListProductVO, Map<String, Object> dataTagInfo) {
        if (Objects.isNull(cmsListProductVO)) {
            return;
        }
        Map<String, Object> allTags = cmsListProductVO.getTags();
        if (Objects.isNull(allTags)) {
            return;
        }
        List<TagDTO> productTags = (List<TagDTO>) allTags.get("productTags");
        // 剔除uiType = 2 优惠券标签, 单独处理赋值 1: 临期, 2:券, 4: 自定义2, 3:其他 ,5:oem 6:三合
        List<TagDTO> couponTagList = Optional.ofNullable(productTags).orElse(Collections.emptyList())
                .stream()
                .filter(tagDTO -> tagDTO.getPromoType() != null
                        && tagDTO.getPromoType() == MarketingEnum.YOU_HUI_QUAN.getCode())
                .collect(Collectors.toList());
        // 取优惠券列表
        if (CollectionUtil.isNotEmpty(couponTagList)) {
            allTags.put("couponTags", couponTagList);
        }
        // 提取活动标签,不包含优惠券
        List<TagDTO> activityTags = Optional.ofNullable(productTags).orElse(Collections.emptyList())
                .stream()
                .filter(tagDTO -> Objects.nonNull(tagDTO.getPromoType())
                        && tagDTO.getPromoType() != MarketingEnum.YOU_HUI_QUAN.getCode())
                .collect(Collectors.toList());
        allTags.put("activityTags", activityTags);
        // 获取商品基本标签
        List<TagDTO> productTagList = Optional.ofNullable(productTags).orElse(Collections.emptyList())
                .stream()
                .filter(tagDTO -> Objects.isNull(tagDTO.getPromoType()))
                .collect(Collectors.toList());
        allTags.put("productTags", productTagList);
        Object object = allTags.get(TagTypeEnum.UNIT_PRICE_TAG.getName());
        if (object != null) {
            allTags.put(TagTypeEnum.UNIT_PRICE_TAG.getName(), object);
        }
        allTags.put("showProductTags", handlerTags(productTagList, (List<TagDTO>) allTags.get("dataTags"), (TagDTO) object, cmsListProductVO.getFirstTagList()));
        // 添加数据标签
        if (Objects.nonNull(dataTagInfo)) {
            allTags.putAll(dataTagInfo);
        }

        //重新赋值所有标签数据
        cmsListProductVO.setTags(allTags);
    }
}
