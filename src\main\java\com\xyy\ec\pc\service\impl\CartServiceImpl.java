package com.xyy.ec.pc.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.message.Transaction;
import com.xyy.cat.util.CatUtil;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.ecp.cart.EcpShoppingCartBusinessApi;
import com.xyy.ec.order.business.dto.CartVocherDto;
import com.xyy.ec.order.business.dto.ShoppingCartBusinessDto;
import com.xyy.ec.order.business.dto.shop.CartBusinessDto;
import com.xyy.ec.order.business.model.BaseResponseCode;
import com.xyy.ec.order.business.model.ServiceResponse;
import com.xyy.ec.order.core.dto.cart.Combined;
import com.xyy.ec.order.core.dto.cart.ShoppingCartDto;
import com.xyy.ec.order.core.util.BeanHelper;
import com.xyy.ec.order.dto.cart.ChangeCartDto;
import com.xyy.ec.order.dto.combined.CombinedDto;
import com.xyy.ec.order.dto.combined.CombinedRespDto;
import com.xyy.ec.order.enums.BizSourceEnum;
import com.xyy.ec.order.enums.PlatformEnum;
import com.xyy.ec.pc.rpc.OrderServerRpcService;
import com.xyy.ec.pc.service.CartService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Author: zhaoyun
 * @Date: 2018/8/27 16:52
 * @Description: 购物车服务类
 */
@Component
public class CartServiceImpl implements CartService {

    private static final Logger  logger = LoggerFactory.getLogger(CartServiceImpl.class);

    @Reference(version = "1.0.0",timeout = 60000)
    private EcpShoppingCartBusinessApi shoppingCartBusinessApi;
//    @Reference(version = "1.0.0",timeout = 60000)
//    private OrderBusinessApi orderBusinessApi;
    @Autowired
    private OrderServerRpcService orderServerRpcService;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    /**
     * 获取业务异常消息
     * @param merchantId
     * @return
     */
    @Override
    public String popErrorMsg(Long merchantId) {
        try{
            ServiceResponse<String> serviceResponse = shoppingCartBusinessApi.popErrorMsg(merchantId);
            if(serviceResponse.getCode() == BaseResponseCode.SUCCESS.getCode()) return serviceResponse.getResult();
        }catch (Exception e){
            logger.error("CartServiceImpl popErrorMsg error",e);
        }
        return null;
    }

    /**
     * 打开购物车
     * @param shoppingCartBusinessDto
     * @param branchCode
     * @return
     */
    @Deprecated
    @Override
    public CartBusinessDto getCartNew(ShoppingCartBusinessDto shoppingCartBusinessDto, String branchCode) {
        // try{
            ShoppingCartDto shoppingCart = BeanHelper.copyTo(shoppingCartBusinessDto,ShoppingCartDto.class);
            return shoppingCartBusinessApi.getCart(shoppingCart,branchCode);
        // }catch (Exception e){
        //     logger.error("getCartNew error",e);
        //     return null;
        // }

    }

    @Override
    public CombinedRespDto getCombined(Combined combined)  throws Exception {
        logger.info("9528-getCombined,combined:{}",JSON.toJSONString(combined));

        CombinedDto combinedDto = new CombinedDto();
        //终端机的类型
        combinedDto.setTerminalType(4);
        combinedDto.setAppVersion(1);
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        combinedDto.setMerchantId(merchant.getId());
        Map<String, Object> result = new HashMap<>();
        combinedDto.setBizSource(BizSourceEnum.B2B.getKey());
        combinedDto.setTerminalType(PlatformEnum.PC.getValue());
        combinedDto.setMerchantId(merchant.getId());
        combinedDto.setAccountRole(merchant.getAccountRole());
        combinedDto.setAccountId(merchant.getAccountId());
        combinedDto.setUseRedPacket(true);
        if (merchant.getAccountRole().equals(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId())) {
            combinedDto.setUseRedPacket(false);
            combinedDto.setUseOptimal(false);
        }
        if(StringUtils.isNotEmpty(combined.getCombinedList())) {
            List<com.xyy.ec.order.dto.combined.Combined> list = JSON.parseArray(combined.getCombinedList(), com.xyy.ec.order.dto.combined.Combined.class);
            combinedDto.setCombinedList(list);
        }
        logger.info("9528-getCombined,combinedDto:{}",JSON.toJSONString(combinedDto));
        return orderServerRpcService.getCombined(combinedDto);
    }
    /**
     * 修改购物车
     * @param shoppingCartBusinessDto
     * @return
     */
    @Deprecated
    @Override
    public Map<String, Object> changeCart(ShoppingCartBusinessDto shoppingCartBusinessDto) {
//        try {
            ShoppingCartDto shoppingCart = BeanHelper.copyTo(shoppingCartBusinessDto,ShoppingCartDto.class);
            return shoppingCartBusinessApi.changeCart(shoppingCart);
//        }catch (Exception e){
//            logger.error("changeCart error",e);
//        }
//        return null;
    }

    /**
     * 从采购单中删除单个商品
     * @param shoppingCartBusinessDto
     * @return
     */
    @Override
    public Map<String, Object> removeProductFromCart(ShoppingCartBusinessDto shoppingCartBusinessDto) {
        try{
            ShoppingCartDto shoppingCart = BeanHelper.copyTo(shoppingCartBusinessDto,ShoppingCartDto.class);
            return shoppingCartBusinessApi.removeProductFromCart(shoppingCart);
        }catch (Exception e){
            logger.error("removeProductFromCart error",e);
            return null;
        }
    }

    /**
     * 从采购单中批量删除商品
     * @param shoppingCartBusinessDto
     * @return
     */
    @Override
    public Map<String, Object> batchRemoveProductFromCart(ShoppingCartBusinessDto shoppingCartBusinessDto) {
        try{
            ShoppingCartDto shoppingCart = BeanHelper.copyTo(shoppingCartBusinessDto,ShoppingCartDto.class);
            return shoppingCartBusinessApi.batchRemoveProductFromCart(shoppingCart);
        }catch (Exception e){
            logger.error("batchRemoveProductFromCart error",e);
            return null;
        }
    }

    /**
     * 勾选商品
     * @param shoppingCartBusinessDto
     * @return
     */
    @Override
    public Map<String, Object> selectItem(ShoppingCartBusinessDto shoppingCartBusinessDto) {
        try{
            ShoppingCartDto shoppingCart = BeanHelper.copyTo(shoppingCartBusinessDto,ShoppingCartDto.class);
            return shoppingCartBusinessApi.selectItem(shoppingCart);
        }catch (Exception e){
            logger.error("selectItem error",e);
        }
        return null;
    }

    /**
     * 取消勾选商品
     * @param shoppingCartBusinessDto
     * @return
     */
    @Override
    public Map<String, Object> cancelItem(ShoppingCartBusinessDto shoppingCartBusinessDto) {
        try{
            ShoppingCartDto shoppingCart = BeanHelper.copyTo(shoppingCartBusinessDto,ShoppingCartDto.class);
            return shoppingCartBusinessApi.cancelItem(shoppingCart);
        }catch (Exception e){
            logger.error("cancelItem error",e);
        }
        return null;
    }

    /**
     * 全选
     * @param shoppingCartBusinessDto
     * @return
     */
    @Override
    public Map<String, Object> selectAllItem(ShoppingCartBusinessDto shoppingCartBusinessDto) {
        try{
            ShoppingCartDto shoppingCart = BeanHelper.copyTo(shoppingCartBusinessDto,ShoppingCartDto.class);
            return shoppingCartBusinessApi.selectAllItem(shoppingCart);
        }catch (Exception e){
            logger.error("selectAllItem error",e);
        }
        return null;
    }

    /**
     * 取消全选
     * @param shoppingCartBusinessDto
     * @return
     */
    @Override
    public Map<String, Object> cancelAllItem(ShoppingCartBusinessDto shoppingCartBusinessDto) {
        try{
            ShoppingCartDto shoppingCart = BeanHelper.copyTo(shoppingCartBusinessDto,ShoppingCartDto.class);
            return shoppingCartBusinessApi.cancelAllItem(shoppingCart);
        }catch (Exception e){
            logger.error("selectAllItem error",e);
        }
        return null;
    }

    /**
     * 获取购物车数量
     * @param merchantId
     * @return
     */
    @Override
    public int getCartNum(Long merchantId) {
        try{
            return shoppingCartBusinessApi.getCartNum(merchantId);
        }catch (Exception e){
            logger.error("getCartNum",e);
        }
        return 0;
    }

    /**
     * 批量移入收藏夹
     * @param shoppingCartBusinessDto
     */
    @Override
    public void addFavoriteForCart(ShoppingCartBusinessDto shoppingCartBusinessDto) {
        try{
            ShoppingCartDto shoppingCart = BeanHelper.copyTo(shoppingCartBusinessDto,ShoppingCartDto.class);
            shoppingCartBusinessApi.addFavoriteForCart(shoppingCart);
        }catch (Exception e){
            logger.error("addFavoriteForCart error",e);
        }
    }


    /**
     * 获取优惠券列表
     * @param shopCode
     * @param merchantId
     * @return
     */
    @Override
    public Map<String, List<CartVocherDto>> selectCartVoucher(Long merchantId,String shopCode) {
        Map<String, List<CartVocherDto>> map = new HashMap<>();
        try{
            ApiRPCResult<Map<String, List<CartVocherDto>>> apiRPCResult = shoppingCartBusinessApi.selectShopCoupons(shopCode, merchantId);
            if(apiRPCResult != null && CollectionUtil.isNotEmpty(apiRPCResult.getData())) {
                map = apiRPCResult.getData();
            }
            return map;
        }catch (Exception e){
            logger.error("queryVoucher error",e);
            return map;
        }
    }

    /**
     * 清空购物车
     * @param merchantId
     */
    @Override
    public void cleanCart(Long merchantId) {
        shoppingCartBusinessApi.clearCart(merchantId);
    }

    @Override
    public ServiceResponse<Boolean> checkMerchantQualifications(Long packageId, Long skuId, Long merchantId,String branchCode) {
        try {
            return shoppingCartBusinessApi.checkMerchantQualificationsAndOEMStatus(packageId, skuId, merchantId,branchCode);
        }catch (Exception e){
            e.printStackTrace();
        }
        ServiceResponse<Boolean> response=new ServiceResponse<>();
        response.setResult(false);
        response.setMsg("请求错误！");
        return  response;
    }

    @Override
    public Map<String, Object> changeCartForPromotion(ChangeCartDto changeCartDto) {
        changeCartDto.setBizSource(BizSourceEnum.GROUP_PURCHASE.getKey());
        changeCartDto.setRealIp(changeCartDto.getRealIp());
        Map<String, Object> result = orderServerRpcService.changeCart(changeCartDto);
        //TODO 这里可否去掉
//        resetGroupProductSubtotal(changeCartDto,changeCartVO);

        return result;
    }

}
