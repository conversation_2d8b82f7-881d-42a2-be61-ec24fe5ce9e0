package com.xyy.ec.pc.controller.promo;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ms.promotion.business.api.common.fixedprice.FixedPriceBusinessApi;
import com.xyy.ms.promotion.business.api.common.fixedprice.vo.FixedPriceACReq;
import com.xyy.ms.promotion.business.api.common.fixedprice.vo.FixedPriceACResq;
import com.xyy.ms.promotion.business.common.response.PromoResp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("/fixedPrice")
public class FixedPriceController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(FixedPriceController.class);

    @Reference(version = "1.0.0")
    private FixedPriceBusinessApi fixedPriceBusinessApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Reference(version = "1.0.0")
    MerchantBussinessApi merchantBussinessApi;

    @RequestMapping("/showFixedPriceActivity" )
    public Object  showFixedPrice(ModelMap modelMap, HttpServletRequest request){
        try {

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
//            MerchantPrincipal merchant = (MerchantPrincipal)  xyyIndentityValidator.currentPrincipal();
            modelMap.put("merchantId", merchant != null ? merchant.getId() : 01);
            modelMap.put("merchant", merchant);
            List<FixedPriceACResq> fixedPriceResqList = getFixedPriceResq(request, merchant);
            List<FixedPriceACResq> navigationList = new ArrayList<>();
            List<FixedPriceACResq> resList = new ArrayList<>();

            if(CollectionUtils.isNotEmpty(fixedPriceResqList)){
                for(FixedPriceACResq fixedPriceResq : fixedPriceResqList){
                    if(fixedPriceResq.getSkuDetailList() != null && CollectionUtils
                            .isNotEmpty(fixedPriceResq.getSkuDetailList().getSkuDtoList())){

                        FixedPriceACResq navigation = new FixedPriceACResq();
                        navigation.setFixedPriceId(fixedPriceResq.getFixedPriceId());
                        navigation.setName(fixedPriceResq.getName());
                        navigation.setDescription(fixedPriceResq.getDescription());
                        navigationList.add(navigation);

                        resList.add(fixedPriceResq);
                    }
                }
            }

            handleFixedPriceResq(resList);
            modelMap.put("fixedPriceList",resList);
            modelMap.put("navigationList",navigationList);
            return new ModelAndView("/fixedPrice/index.ftl", modelMap);

        } catch (Exception e) {
            logger.error("查询一口价活动错误：",e);
        }
        return  this.addError("查询一口价错误！");
    }

    /**
     * @Description 获取一口价活动
     * <AUTHOR>
     * @Data   2019/6/5 14:07
     * @Param  [request]
     * @return java.util.List<com.xyy.ms.promotion.business.api.common.fixedprice.vo.FixedPriceResq>
     **/
    private List<FixedPriceACResq> getFixedPriceResq(HttpServletRequest request,
                                                   MerchantBussinessDto merchant) throws Exception {

        FixedPriceACReq fixedPriceACReq = new FixedPriceACReq();
        if(merchant!=null){
            MerchantBussinessDto merchantBussinessDto = merchantBussinessApi
                    .findMerchantBaseInfoForApp(merchant.getId());
            fixedPriceACReq.setBranchCode(merchantBussinessDto.getRegisterCode());
            if(StringUtils.isNotEmpty(merchantBussinessDto.getCityCode())){
                fixedPriceACReq.setCityCode(merchantBussinessDto.getCityCode());
            }
            fixedPriceACReq.setMerchantId(merchant.getId());
        }else{
            fixedPriceACReq.setBranchCode(this.getBranchCodeByMerchantId(request,null));
            fixedPriceACReq.setMerchantId(0l);//默认0
        }

        // 查询一口活动
        PromoResp<List<FixedPriceACResq>> fixedPriceShowPage =
                fixedPriceBusinessApi.findFixedPriceShowPage(fixedPriceACReq);

        return fixedPriceShowPage.getData();
    }

    @RequestMapping("/showFixedPriceActivityOne")
    public Object  showFixedPriceOne(ModelMap modelMap, HttpServletRequest request){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            List<FixedPriceACResq> fixedPriceResqList = getFixedPriceResq(request, merchant);
            if(CollectionUtils.isNotEmpty(fixedPriceResqList)){
                modelMap.put("fixedPrice", fixedPriceResqList.get(0));
            }else{
                modelMap.put("fixedPrice", null);
            }

            return new ModelAndView("/", modelMap);
        } catch (Exception e) {
            logger.error("查询一口价活动错误：",e);
        }
        return  this.addError("查询一口价错误!");
    }

    public void handleFixedPriceResq(List<FixedPriceACResq> fixedPriceResqList){
        if(CollectionUtils.isNotEmpty(fixedPriceResqList)){
            for (FixedPriceACResq fixedPriceResq : fixedPriceResqList){
                String desc = fixedPriceResq.getDescription();
                if(StringUtils.isNotEmpty(desc)){
                    String[] descs = desc.split("；");
                    if(descs.length > 3){
                        StringBuilder sb = new StringBuilder();
                        for(int i=0; i< 3;i++){
                            if(i != 0){
                                sb.append(" ");
                            }
                            sb.append(descs[i]);
                        }
                        sb.append("...");
                        fixedPriceResq.setDescription(sb.toString().replace("，", ""));
                    }
                }
            }
        }
    }
}
