package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.merchant.bussiness.api.SuggestionBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.SuggestionBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by w_jing on 2018/1/29.
 * 意见反馈
 */
@Controller
@RequestMapping("/feedback")
public class FeedbackController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(FeedbackController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private XyyConfig.CdnConfig cdnConfig;

    @Reference(version = "1.0.0")
    private SuggestionBussinessApi suggestionBussinessApi;

    /**
     * 跳转意见反馈页面
     * @return
     */
    @RequestMapping("/indexFeedback.html")
    public ModelAndView indexFeedback(){
        Map<String,Object> model = new HashMap<String,Object>();
        String cdnPath = cdnConfig.getCdnHostname();
        model.put("cdnPath",cdnPath);
        return new ModelAndView("/feedback/feedback.ftl",model);
    }

    /**
     * 保存意见反馈信息
     * @param suggestion
     * @return
     */
    @RequestMapping("/saveFeedBack")
    @ResponseBody
    public Object saveFeedBack(SuggestionBussinessDto suggestion, HttpServletRequest request){
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            Long merchantId = 0L;
            if (merchant != null){
                merchantId = merchant.getId();
            }
            //用户所属域的域编码
            String branchCode = merchant.getRegisterCode();
            if(StringUtil.isNotEmpty(branchCode)) {
	            suggestion.setBranchCode(branchCode);
	            suggestion.setBranchName(BranchEnum.get(branchCode));
            }
            suggestionBussinessApi.insertSelective(suggestion);
        } catch (Exception e) {
            LOGGER.error("意见反馈信息保存异常,e="+e);
            return this.addError("意见反馈信息保存失败！");
        }
        return this.addResult();
    }
}
