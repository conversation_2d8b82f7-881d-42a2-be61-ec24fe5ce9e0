package com.xyy.ec.pc.cms.param;

import com.xyy.ec.search.engine.ecp.commons.constants.enums.EcpSearchSortStrategyEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Auther chenshaobo
 * @Date 2024/8/6
 * @Description 跨店、店铺、商品券凑单页入参
 * @Version V1.0
 **/
@Data
public class AppVoucherSupplementOrderQueryParam implements Serializable {

    /**
     * 优惠券ID
     */
    private Long voucherTemplateId;

    /**
     * 搜索关键词。原查询参数是keyword
     */
    private String queryWord;

    /**
     * 排序策略。原查询参数是property和direction
     *
     * @see EcpSearchSortStrategyEnum
     */
    private Integer sortStrategy;

    /**
     * 店铺编码列表。
     * 上限2000。
     */
    private String shopCodes;

    /**
     * 商品分类id列表。
     * 上限200。
     */
    private String categoryIdsStr;

    /**
     * 是否是有货
     * 1：是； <br/>
     * 0：否； <br/>
     * 不传或为空串则表示不限制。
     */
    private Integer hasStock;

    /**
     * 是否是支持京东快递。原查询参数是tagList=YBM_ST_SERV_LOG_JD
     * 1：是； <br/>
     * 0：否； <br/>
     * 不传或为空串则表示不限制。
     */
    private Integer isSupportJdExpress;

    /**
     * 是否是支持顺丰快递。原查询参数是tagList=YBM_ST_SERV_LOG_SF
     * 1：是； <br/>
     * 0：否； <br/>
     * 不传或为空串则表示不限制。
     */
    private Integer isSupportSfExpress;

    /**
     * 药品类型列表：1:甲类OTC 2:乙类OTC 3:处方药 4:其他。原查询参数是drugClassificationStr
     */
    private String drugClassificationsStr;

    /**
     * 价格区间：最小价格
     */
    private BigDecimal minPrice;

    /**
     * 价格区间：最大价格
     */
    private BigDecimal maxPrice;

    /**
     * 价格区间类型 1全部 2区间
     */
    private Integer priceRangeStatus;

    /**
     * 是否是第一次请求
     */
    private Boolean isFirstRequest = Boolean.FALSE;

    /**
     * 用户id
     */
    private Long merchantId;

    /**
     * 平台类型
     */
    private Integer terminalType;

    /**
     * 区域编码
     */
    private String branchCode;

    private Integer pageNum;

    private Integer pageSize;

    // 埋点相关数据
    /**
     * 埋点来源页类型
     */
    private String sptype;

    /**
     * 埋点来源页id
     */
    private String spid;

    /**
     * 埋点用户唯一标识
     */
    private String sid;

    /**
     * 发起列表页请求的页面url
     */
    private String pageurl;

    /**
     * 列表页请求的来源页入口信息
     * APP端页面传递，PC端在url获取，获取不到为空字符串
     */
    private String pageSource;

    /**
     * 列表页追踪唯一标识id（例：20201111-599-YBMNB-1）
     */
    private String nsid;

    private String nsptype;

    private String nspid;

    /**
     * 是否自营入仓品
     * 1-是 0-否
     */
    private Integer isWarehousing;

}
