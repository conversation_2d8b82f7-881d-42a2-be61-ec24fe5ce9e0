<!DOCTYPE HTML>
<html>
<head>
		<#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>我的授权列表</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="/static/css/user.css?t=${t_v}" />
    <link rel="stylesheet" href="/static/css/myplan.css?t=${t_v}" />
    <script type="text/javascript" src="/static/js/subsituteOrderAuthrize/index.js?t=${t_v}"></script>
    <script src="/static/js/ajaxfileupload.js"></script>
    <script type="text/javascript">
        var ctx="/static";
    </script>
</head>

<body>

<div class="container">
    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
				<#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->

    <!--主体部分开始-->
    <div class="main">
        <div class="myplan row">
            <!--面包屑-->
            <ul class="sui-breadcrumb">
                <li><a href="/">首页</a>></li>
                <li><a href="/merchant/center/index.htm">用户中心</a>></li>
                <li><a href="/merchant/center/sprout/order/index.htm">代下单专区</a>></li>
                <li class="active">授权清单</li>
            </ul>

            <div class="myqual-content clear">
                <div class="side-left fl">
							<#include "/common/merchantCenter/left.ftl" />
                </div>

                <div class="main-right fr">

                    <div class="agreement-state">
                        <div class="already"><a href="/merchant/center/sprout/order/index.htm">订单专区(${orderNum })</a><i></i></div>
                        <div class="unsigned"><a class="active" href="javascript:">授权专区(${authorizeNum })</a>
                        </div>
                    </div>
                    <div class="myneworder-search myneworder-search-none clear">
                        <div class="fl">
                            <label class="padding-left">状态：</label>
                            <select class="inp-sel " id="status" name="status">
                                <option value="-1"  selected="selected">全部</option>
                                <option value="1" <#if authorizeDto.status==1>selected="selected"</#if>>已授权</option>
                                <option value="0" <#if authorizeDto.status==0>selected="selected"</#if>>未授权</option>
                            </select>

                            <div class="sui-form form-horizontal fr">
                                <div data-toggle="datepicker" class="control-group input-daterange">
                                    <label class="padding-left">提交时间：</label>
                                    <div class="controls">
                                        <input type="text" id="startCreateTime" class="input-medium input-date" <#if authorizeDto.startDate??>value="${authorizeDto.startDate?string('yyyy-MM-dd') }"</#if>><span> 到 </span>
                                        <input type="text" id="endCreateTime" class="input-medium input-date"<#if authorizeDto.endDate??>value="${authorizeDto.endDate?string('yyyy-MM-dd') }"</#if>>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="myplan-buttons myplan-buttons-new clearfix">
                            <div class="plan-anniu fr">
                                <button class="plan-empty margin-none clear-btn fl">清空</button>
                                <button class="search-for query-btn fl">搜索</button>
                            </div>
                        </div>
                    </div>


                    <div class="user-myplan">
                    <table>
                        <thead>
                        <th class="myorder-row1">授权申请标题</th>
                        <th style="width: 160px">提交人</th>
                        <th style="width: 170px">提交时间</th>
                        <th style="width: 130px">状态</th>
                        <th class="myorder-row5" style="width: 130px">操作</th>
                        </thead>
                    <tbody>
                                <#if pager.rows??&&(pager.rows?size>0)>
										<#list pager.rows as authorize>
                                            <tr class="">
                                            <td name="id" hidden><input type="checkbox" name="id" hidden value="${authorize.id }" /></td>
                                            <td><span>${authorize.title}</span></td>
                                            <td><span>${authorize.applicant}</span></td>
                                            <td><span>${authorize.createTime}</span></td>
                                                <td>
                                                    <span class="spanColor">
                                                        <#if (authorize.status==1)>
                                                            已授权
                                                        <#elseif (authorize.status==0)>
                                                            未授权
                                                        </#if>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="/substitute/order/authorize/detail.htm?authorizeId=${authorize.id}" class="details">查看</a>
                                                </td>
                                            </tr>
										</#list>
									</tbody>
										</table>
									<#else>
								</table>

									<!--没有订单:   加样式和图片以及事件-->
									<div class="noplan">
                                        <img src="/static/images/user/noplan.png" alt="">
                                   
									<#if total == 0>
									<p>还没有授权列表</p>
									<#else>
										<p>暂无记录</p>
									</#if>
                                    </div>
									</#if>
                    </div>
                    <!--分页器-->
                    <div class="page">
									 <#import "/common/pager.ftl" as p>
				              		 <@p.pager currentPage=pager.currentPage limit=pager.limit total=pager.total pageCount=pager.pageCount toURL=pager.requestUrl method="get"/>
                    </div>


                </div>

            </div>
        </div>
    </div>
</div>
<!--主体部分结束-->
<!--底部导航区域开始-->
<div class="footer" id="footer">
				<#include "/common/footer.ftl" />
</div>
<!--底部导航区域结束-->
<!--客服入口开始-->
<div class="kefu-box">
    <a href="javaScript:callKf('','${merchant.id}');">
        <img src="/static/images/kefu-online.png" alt="">
    </a>
</div>
<!--客服入口结束-->
</div>

</body>
</html>