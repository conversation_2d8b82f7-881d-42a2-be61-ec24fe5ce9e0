package com.xyy.ec.pc.authentication.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.account.JgBuryingPointApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.enums.PlatformEnum;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.merchant.server.dto.AccountInfoDto;
import com.xyy.ec.merchant.server.dto.AccountMerchantRelDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRelStatusEnum;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.pc.authentication.consts.Constants;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.utils.PrincipalUtils;
import com.xyy.ec.pc.authentication.utils.ServletUtils;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.PasswordVerifier;
import com.xyy.ec.pc.base.Principal;
import com.xyy.ec.pc.enums.MerchantStatusEnum;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.exception.AuthenticationException;
import com.xyy.ec.pc.service.AuthenticationProvider;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/4/10 10:17
 * @File XyyJwtIdentityValidatorImpl.class
 * @Software IntelliJ IDEA
 * @Description JWT认证, 继承旧类, 用于兼容旧版, 灰度流量
 */
@Slf4j
@Primary
@Component
public class XyyJwtIdentityValidatorImpl implements XyyIndentityValidator {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private AuthenticationProvider authenticationProvider;

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;
    
    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    @Reference(version = "1.0.0")
    private JgBuryingPointApi jgBuryingPointApi;

    /** 登录 */
    @Override
    public Principal login(Verifier verifier) throws Exception {
        
        if (!(verifier instanceof PasswordVerifier)) {
            return null;
        }
        PasswordVerifier passwordVerifier = (PasswordVerifier) verifier;
        // 用户验证
        try {
            JwtPrincipal jwtPrincipal = loginService.login(passwordVerifier);
            jwtPrincipal.setLoginDeviceId(passwordVerifier.getDeviceId());
            // 创建Token
            String token = tokenService.createToken(jwtPrincipal);
            // 将token等信息写入cookie
            ServletUtils.writeLoginCookie(jwtPrincipal, passwordVerifier, token);
            // XXX 兼容旧版
            return jwtPrincipal;
        }
        catch (Exception e) {
            log.error(e.getMessage());
            if (e instanceof AuthenticationException) {
                throw e;
            }
            throw new AuthenticationException(e.getMessage());
        }
    }

    /** 登出 */
    @Override
    public void logout() {

        JwtPrincipal jwtPrincipal = tokenService.getPrincipal();
        if (ObjectUtil.isNotNull(jwtPrincipal)) {
            // 删除用户缓存记录
            tokenService.delLoginUser(jwtPrincipal.getToken());
            // 清除cookie
            ServletUtils.clearCookie();
        }
    }

    /** 选择店铺后设置MerchantID */
    @Override
    public MerchantPrincipal setPrincipalMerchant(Long merchantId, Long accountId) throws Exception {
        
        // XXX 兼容旧版在登录时自动匹配店铺及登录后选择店铺后设置MerchantID
        JwtPrincipal jwtPrincipal = tokenService.getPrincipal();
        if (jwtPrincipal == null) {
            jwtPrincipal = new JwtPrincipal();
            jwtPrincipal.setAccountId(jwtPrincipal.getAccountId());
            PrincipalUtils.initJwtPrincipal(jwtPrincipal);
        }
//        log.info("设置Merchant，jwtPrincipal -> {}", jwtPrincipal.toString());
        // 查询是该店铺是否为账号关联的有效店铺
        AccountMerchantRelDto accountMerchantRelDto = loginAccountApi.selectByAccountIdAndMerchantId(accountId, merchantId);
        if (accountMerchantRelDto == null || !AccountMerchantRelStatusEnum.PASS.getValue().equals(accountMerchantRelDto.getStatus())){
            return null;
        }
        // 设置账号角色
        jwtPrincipal.setAccountRole(accountMerchantRelDto.getRole());
        // 店长账号则查询店员账号
        if (Objects.equals(accountMerchantRelDto.getRole(), AccountMerchantRoleEnum.SHOP_MANAGER.getRoleId())){
            setSubAccountList(jwtPrincipal);
        }
        // 查询店铺信息
        MerchantBussinessDto merchantPrincipal = merchantBussinessApi.findMerchantById(Convert.toLong(merchantId));
        BeanUtils.copyProperties(merchantPrincipal, jwtPrincipal);
        // 兼容旧版
        jwtPrincipal.setMerchantId(merchantId);
        jwtPrincipal.setMechantId(merchantId);
        jwtPrincipal.setAccountId(accountId);
        // 删除之前token
        String oldToken = jwtPrincipal.getToken();
        // 重新创建token
        String newToken = tokenService.createToken(jwtPrincipal);
        // 写入cookie
        ServletUtils.writeLoginCookie(jwtPrincipal, new PasswordVerifier(null, null), newToken);
        // 记录QT用来上报埋点的session放入cookie
        Cookie qtSessionCookie = ServletUtils.createCookie(Constants.QT_SESSION_KET, StrUtil.format("{}_{}", RandomStringUtils.randomAlphanumeric(8), System.currentTimeMillis()), Constants.COOKIE_MAX_AGE);
        ServletUtils.writeCookie(qtSessionCookie);
        // 从redis删除旧token
        tokenService.delLoginUser(oldToken);
        // 上报店铺登录事件到极光
        if (merchantId != null) {
            try {
                jgBuryingPointApi.profileSetLoginOrRegister(merchantId, PlatformEnum.PC.getKey());
            }
            catch (Exception e) {
                log.error("登录埋点数据上报异常, merchantId -> {}", merchantId, e);
            }
        }
        return jwtPrincipal;
    }

    private void setSubAccountList(JwtPrincipal jwtPrincipal) {
   
        if (jwtPrincipal.getMechantId() == null){
            return;
        }
        try {
            ApiRPCResult<List<AccountInfoDto>> listApiRPCResult = loginAccountApi.querySubAccountList(jwtPrincipal.getMechantId());
            if (listApiRPCResult != null && listApiRPCResult.isSuccess() && listApiRPCResult.getData() != null){
                List<MerchantBussinessDto.SubAccountInfo> subAccountInfoList = listApiRPCResult.getData().stream().map(item->{
                    MerchantBussinessDto.SubAccountInfo subAccountInfo = new MerchantBussinessDto.SubAccountInfo();
                    subAccountInfo.setAccountId(item.getAccountId());
                    subAccountInfo.setAccountName(item.getAccountName());
                    subAccountInfo.setMobile(item.getMobile());
                    return subAccountInfo;
                }).collect(Collectors.toList());
                jwtPrincipal.setSubAccountInfoList(subAccountInfoList);
            }
        }
        catch (Exception e) {
            log.error("查询子账号列表失败",e);
        }
    }

    /** 获取当前登录用户，且是已选择店铺的用户 */
    @Override
    public Principal currentPrincipal() throws Exception {

        JwtPrincipal jwtPrincipal = tokenService.getPrincipal();
        if (jwtPrincipal != null && jwtPrincipal.getMechantId() != null) {
            // 查询是该店铺是否为账号关联的有效店铺
            JwtPrincipal principal = this.getPrincipal(jwtPrincipal.getAccountId(), jwtPrincipal.getMechantId());
            if (principal == null) {
                return null;
            }
            // 兼容旧版
            jwtPrincipal.setMerchantId(jwtPrincipal.getMerchantId());
            jwtPrincipal.setMechantId(jwtPrincipal.getMerchantId());
            jwtPrincipal.setAccountId(principal.getAccountId());
            jwtPrincipal.setAccountRole(principal.getAccountRole());
//            MerchantPrincipal merchantPrincipal = (MerchantPrincipal) jwtPrincipal; 
//            log.info("当前登录用户信息 jwtPrincipal -> {}, merchantPrincipal -> {}", JSONObject.toJSONString(jwtPrincipal), JSONObject.toJSONString(merchantPrincipal));
            return jwtPrincipal;
        }
        return null;
    }

    @Override
    public Principal currentPrincipalEaseEx() {
        try {
            JwtPrincipal jwtPrincipal = tokenService.getPrincipal();
            if (jwtPrincipal == null || jwtPrincipal.getMechantId() == null) {
                return null;
            }
            // 查询是该店铺是否为账号关联的有效店铺
            JwtPrincipal principal = this.getPrincipal(jwtPrincipal.getAccountId(), jwtPrincipal.getMechantId());
            if (principal == null) {
                return null;
            }
            // 兼容旧版
            jwtPrincipal.setMerchantId(jwtPrincipal.getMerchantId());
            jwtPrincipal.setMechantId(jwtPrincipal.getMerchantId());
            jwtPrincipal.setAccountId(principal.getAccountId());
            jwtPrincipal.setAccountRole(principal.getAccountRole());
            return jwtPrincipal;
        } catch (Exception e) {
            throw new AppException("请求登录信息失败", XyyJsonResultCodeEnum.FAIL);
        }
    }

    public JwtPrincipal getPrincipal(Long accountId, Long merchantId) throws Exception {

        JwtPrincipal principal = new JwtPrincipal();
        principal.setAccountId(accountId);
        if (merchantId != null && merchantId > 0) {
            AccountMerchantRelDto accountMerchantRelDto = loginAccountApi.selectByAccountIdAndMerchantId(accountId, merchantId);
            if (accountMerchantRelDto == null) {
                log.error("账号未关联该店铺, account:[{}], merchantId:[{}]", accountId, merchantId);
                return null;
            }
            if (!accountMerchantRelDto.getStatus().equals(AccountMerchantRelStatusEnum.PASS.getValue())) {
                log.error("账号非委托书审核通过状态, account:[{}], merchantId:[{}]", accountId, merchantId);
                return null;
            }
            MerchantBussinessDto merchant = merchantBussinessApi.findMerchantById(merchantId);
            if (merchant == null) {
                log.error("未查询到店铺信息, account:[{}], merchantId:[{}]", accountId, merchantId);
                return null;
            }
            if (merchant.getStatus() == MerchantStatusEnum.STATUS_IS_FROZEN.getId()) {
                log.error("店铺已冻结, account:[{}], merchantId:[{}]", accountId, merchantId);
                return null;
            }
            if (merchant.getStatus() == MerchantStatusEnum.STATUS_NON_ACTIVATED.getId()) {
                log.error("店铺未激活, account:[{}], merchantId:[{}]", accountId, merchantId);
                return null;
            }
            BeanUtils.copyProperties(merchant, principal);
            principal.setAccountId(accountId);
            principal.setAccountRole(accountMerchantRelDto.getRole());
            if (Objects.equals(accountMerchantRelDto.getRole(), AccountMerchantRoleEnum.SHOP_MANAGER.getRoleId())) {
                setSubAccountList(principal);
            }
        }
        return principal;
    }

    /** 获取当前登录用户，可以是没选择店铺的用户 */
    @Override
    public Principal currentAccountPrincipal() {

        return tokenService.getPrincipal();
    }

    // ########################################### 以下方法暂未使用 ########################################

    @Override
    public boolean isVisited() {

        return currentVisitor() != null;
    }

    @Override
    public String currentVisitor() {

        Object visitorObj = WebContext.currentRequest().getAttribute(Constants.VISITOR_COOKIE_NAME);
        if (visitorObj != null) return (String) visitorObj;
        try {
            Cookie cookie = CookieUtils.getCookie(Constants.VISITOR_COOKIE_NAME);
            if (cookie == null) return null;
            return URLDecoder.decode(cookie.getValue(), Constants.ENCODE);
        } catch (UnsupportedEncodingException e) {
            log.error(e.toString());
            return null;
        }
    }

    @Override
    public boolean isLogined() throws Exception {

        return currentPrincipal() != null;
    }

    @Override
    public void setAuthenticationProvider(AuthenticationProvider authenticationProvider) {

        this.authenticationProvider = authenticationProvider;
    }

    @Override
    public AuthenticationProvider getAuthenticationProvider() {

        return this.authenticationProvider;
    }

    @Override
    public String currentBranchId() {

        String branchId = (String) ServletUtils.getRequest().getAttribute(Constants.BRANCH_ID_COOKIE_NAME);
        if (StrUtil.isNotEmpty(branchId)) {
            return branchId;
        }
        return ServletUtils.getCookieValue(Constants.BRANCH_ID_COOKIE_NAME);
    }

    @Override
    public void setCurrentBranchId(String branchId) {

        ServletUtils.getRequest().setAttribute(Constants.BRANCH_ID_COOKIE_NAME, branchId);
        ServletUtils.writeCookie(ServletUtils.createCookie(Constants.BRANCH_ID_COOKIE_NAME, branchId, Constants.COOKIE_MAX_AGE));
    }

    @Override
    public String lastLoginUserName() {

        return ServletUtils.getCookieValue(Constants.VISITOR_COOKIE_NAME);
    }

    @Override
    public Long getBizLastLoginTime() {

        return Convert.toLong(ServletUtils.getCookieValue(Constants.BIZ_LAST_LOGIN_TIME));
    }

    /** 清除响应cookie */
    @Override
    public void newLogout() {
        HttpServletResponse response = WebContext.currentResponse();
        JwtPrincipal jwtPrincipal = tokenService.getLoginPrincipal(response);
        if (ObjectUtil.isNotNull(jwtPrincipal)) {
            // 删除用户缓存记录
            tokenService.delLoginUser(jwtPrincipal.getToken());
            // 清除响应头数据
            ServletUtils.deleteCookie();
        }
    }
}