package com.xyy.ec.pc.service.marketing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 营销拼团活动信息DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarketingGroupBuyingActivityInfoDTO implements Serializable {
    private static final long serialVersionUID = 8597989338442040244L;

    /**
     * 活动ID
     */
    private Long marketingId;

    /**
     * 活动类型
     **/
    private Integer activityType;

    /**
     * 拼团价格
     */
    private BigDecimal assemblePrice;

    /**
     * 拼团开始时间
     */
    private Long assembleStartTime;

    /**
     * 拼团结束时间
     */
    private Long assembleEndTime;

    /**
     * 拼团剩余时间
     */
    private Long surplusTime;

    /**
     * 拼团进度
     */
    private String percentage;

    /**
     * 起拼数量
     */
    private Integer skuStartNum;

    /**
     * 已拼数量
     */
    private Long orderNum;

    /**
     * 拼团状态：0-未开始 1-进行中 2-结束。表示前后端协议定的活动状态。
     * 真实的拼团活动状态为：1.未开始 ，2.拼团中，3.已结束 。
     */
    private Integer assembleStatus;

    /**
     * 拼团预热期是否展示价格字段,0-不展示，1-展示
     */
    private Integer preheatShowPrice;

    /**
     * 购买人数
     */
    private Long merchantCount;

    /**
     * 购买人数描述
     */
    private String merchantCountDesc;

    /**
     * 是否是多阶梯价拼团 1:是 2:否
     */
    private Integer stepPriceStatus;

    /**
     * 最低拼团价格。仅当是多阶梯价拼团时有效且有值。
     */
    private BigDecimal minSkuPrice;

    /**
     * 最高拼团价格。仅当是多阶梯价拼团时有效且有值。
     */
    private BigDecimal maxSkuPrice;

    /**
     * 起始价显示文本，如¥30.22起。仅当是多阶梯价拼团时有效且有值。
     */
    private String startingPriceShowText;

    /**
     * 价格区间显示文本，如¥2.38-¥6.88。仅当是多阶梯价拼团时有效且有值。
     */
    private String rangePriceShowText;

    /**
     * 多梯度价显示文本信息列表，如["满20盒，22.10元/盒","满40盒，19.10元/盒"]。仅当是多阶梯价拼团时有效且有值。
     */
    private List<String> stepPriceShowTexts;
    /**
     * 是否使用在列表页上弹出动态面板的操作方式
     */
    private Boolean isApplyListShowType;

    /**
     * 是否支持随心拼
     */
    private Boolean supportSuiXinPin;

    /**
     * 随心拼按钮文案（商品详情页）
     */
    private String suiXinPinButtonText;

    /**
     * 随心拼按钮上气泡文案
     */
    private String suiXinPinButtonBubbleText;
}
