package com.xyy.ec.pc.recommend.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.ec.api.service.ProductService;
import com.xyy.ec.marketing.common.constants.MarketingEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.GroupBuyingInfoDto;
import com.xyy.ec.pc.config.AppProperties;
import com.xyy.ec.pc.recommend.dto.RecommendCsuDataTagCsuDTO;
import com.xyy.ec.pc.recommend.helpers.PcRecommendCardVOHelper;
import com.xyy.ec.pc.recommend.helpers.PcRecommendProductVOHelper;
import com.xyy.ec.pc.recommend.helpers.RecommendCsuDataTagCsuDTOHelper;
import com.xyy.ec.pc.recommend.params.RecommendCsuDataTagQueryParam;
import com.xyy.ec.pc.recommend.service.EcpPcRecommendService;
import com.xyy.ec.pc.recommend.vo.PcRecommendCardVO;
import com.xyy.ec.pc.recommend.vo.PcRecommendProductVO;
import com.xyy.ec.pc.remote.ProductForPromotionRemoteService;
import com.xyy.ec.pc.remote.ProductForSearchRemoteService;
import com.xyy.ec.pc.remote.ShopQueryRemoteService;
import com.xyy.ec.pc.search.service.DataService;
import com.xyy.ec.pc.service.marketing.MarketingService;
import com.xyy.ec.pc.service.marketing.dto.MarketingSeckillActivityInfoDTO;
import com.xyy.ec.product.back.end.ecp.csu.dto.CsuDTO;
import com.xyy.ec.product.business.dto.ProductEnumDTO;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.product.business.ecp.csutag.dto.ProductActivityTagDTO;
import com.xyy.ec.shop.server.business.results.ShopInfoDTO;
import com.xyy.recommend.ecp.result.EcpRecommendBaseCardDTO;
import com.xyy.recommend.ecp.result.EcpRecommendProductCardDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EcpPcRecommendServiceImpl implements EcpPcRecommendService {

    @Autowired
    private ProductForSearchRemoteService productForSearchRemoteService;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private ShopQueryRemoteService shopQueryRemoteService;

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private DataService dataService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductForPromotionRemoteService productForPromotionRemoteService;

    @Override
    public List<PcRecommendCardVO> convertAndFillRecommendCards(Long merchantId, String branchCode, Boolean priceDisplayFlag, boolean userOneClickReplenishment, List<EcpRecommendBaseCardDTO> cardDTOS) {
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，cardDTOS：{}", merchantId, JSONArray.toJSONString(cardDTOS));
        }

        if (Objects.isNull(merchantId) || merchantId <= 0 || CollectionUtils.isEmpty(cardDTOS)) {
            return Lists.newArrayList();
        }

        Boolean isQueryShopDataTags = null;
        Boolean isShowSimilarGoodsJump = null;
        isQueryShopDataTags = true;
        // 强制不显示店铺同款
        isShowSimilarGoodsJump = false;
        // 获取商品id列表
        Map<Long, EcpRecommendProductCardDTO> cardMap = this.getCardCsuIdsAndSpuRecallStrategyName(cardDTOS);
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，cardMap：{}", merchantId, JSONArray.toJSONString(cardMap));
        }
        if (MapUtils.isEmpty(cardMap)) {
            return Lists.newArrayList();
        }
        List<Long> csuIds = new ArrayList<>(cardMap.keySet());
        // 查询商品信息
        List<ProductDTO> productDTOS = productForSearchRemoteService.fillProductInfo(csuIds, merchantId, branchCode);
        if (CollectionUtils.isEmpty(productDTOS)) {
            return Lists.newArrayList();
        }

        List<PcRecommendProductVO> productVOS = PcRecommendProductVOHelper.creates(productDTOS);
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，productVOS：{}", merchantId, JSONArray.toJSONString(productVOS));
        }

        // 填充店铺信息
        this.fillProductShopInfo(productVOS);

        // 填充活动信息
        List<Long> marketingActivityCsuIds = productVOS.stream()
                .filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.PROMOTION_SKU_TYPE.getId())
                        || Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.WHOLESALE_TYPE.getId()))
                .map(PcRecommendProductVO::getId).collect(Collectors.toList());
        Set<Long> gaoMaoSkuIdSet = productVOS.stream().filter(productDto -> {
            if (null != productDto && (appProperties.getApplyGaoMaoGroupSaleHighGrossSet().contains(productDto.getHighGross())
                    || appProperties.getFbpShopCodeSet().contains(productDto.getShopCode()))) {
                return true;
            }
            return false;
        }).map(PcRecommendProductVO::getId).collect(Collectors.toSet());

        Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoDtoMap = marketingService.getActCardInfoBySkuIdListForSearch(marketingActivityCsuIds, merchantId,
                Sets.newHashSet(MarketingEnum.PING_TUAN.getCode(), MarketingEnum.PI_GOU_BAO_YOU.getCode()), gaoMaoSkuIdSet);
        this.fillProductMarketingActivityInfo(productVOS, csuIdToGroupBuyingInfoDtoMap);
        List<Long> skIdList = productVOS.stream()
                .filter(productDTO -> Objects.equals(productDTO.getProductType(), ProductEnumDTO.ProductTypeEnum.SECKILL_SKU_TYPE.getId()))
                .map(PcRecommendProductVO::getId).collect(Collectors.toList());
        Map<Long, MarketingSeckillActivityInfoDTO> csuIdToSeckillActivityInfoDTOMap = marketingService.getShowingSeckillActivityInfoByCsuIdsForSearch(merchantId, skIdList);
        productVOS.stream().forEach(item -> item.setActSk(csuIdToSeckillActivityInfoDTOMap.get(item.getId())));

        // 填充数据标签信息
        RecommendCsuDataTagQueryParam queryParam = RecommendCsuDataTagQueryParam.builder()
                .merchantId(merchantId).csuList(RecommendCsuDataTagCsuDTOHelper.creates(productVOS))
                .isQueryShopDataTags(isQueryShopDataTags).build();
        fillRecommendQueryParam(cardMap, queryParam);

        Map<Long, Map<String, Object>> dataTagMaps = dataService.batchGetCsuDataTags(queryParam);

        productVOS.stream().forEach(product -> PcRecommendProductVOHelper.setTagInfo(product, dataTagMaps.get(product.getId())));
        // 填充库存信息
        productService.fillRecommendProductDTOActTotalSurplusQtyToAvailableQty(productVOS);
        // 不显示价格时隐藏价格
        if (BooleanUtils.isFalse(priceDisplayFlag)) {
            this.resetProductPrice(productVOS);
        }

        //显示券后价
        if (BooleanUtils.isTrue(userOneClickReplenishment)) {
            List<Long> skuIdList = productVOS.stream().map(PcRecommendProductVO::getId).collect(Collectors.toList());
            Map<Long, BigDecimal> saasPromotionProductDiscountPrice = marketingService.getSaasPromotionProductDiscountPrice(merchantId, skuIdList);
            productVOS.stream().forEach(item -> item.setCouponPrice(saasPromotionProductDiscountPrice.get(item.getId())));
        }

        // 转数据结构
        Map<Long, PcRecommendProductVO> csuIdToInfoMap = productVOS.stream().filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId()))
                .collect(Collectors.toMap(PcRecommendProductVO::getId, Function.identity(), (f, s) -> f));
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，csuIdToInfoMap：{}", merchantId, JSONObject.toJSONString(csuIdToInfoMap));
        }
        List<PcRecommendCardVO> recommendCardVOS = PcRecommendCardVOHelper.creates(cardDTOS, csuIdToInfoMap, isShowSimilarGoodsJump);
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，recommendCardVOS：{}", merchantId, JSONArray.toJSONString(recommendCardVOS));
        }
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，merchantId：{}，最终的recommendCardVOS：{}", merchantId, JSONArray.toJSONString(recommendCardVOS));
        }
        return recommendCardVOS;
    }

    @Override
    public List<PcRecommendCardVO> convertAndFillRecommendCards(String saasOrganSign, List<EcpRecommendBaseCardDTO> cardDTOS) {
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，saasOrganSign{},cardDTOS：{}", saasOrganSign, JSONArray.toJSONString(cardDTOS));
        }

        if (CollectionUtils.isEmpty(cardDTOS)) {
            return Lists.newArrayList();
        }

        // 获取商品id列表
        Map<Long, EcpRecommendProductCardDTO> cardMap = this.getCardCsuIdsAndSpuRecallStrategyName(cardDTOS);

        List<ProductDTO> csuIdList = productForPromotionRemoteService.queryCsuSimpleInfoCsuIdList(Lists.newArrayList(cardMap.keySet()));

        if (CollectionUtils.isEmpty(csuIdList)) {
            return Lists.newArrayList();
        }

        List<PcRecommendProductVO> pcRecommendProductVOS = PcRecommendProductVOHelper.createsNoSimple(csuIdList);
        if (CollectionUtils.isEmpty(pcRecommendProductVOS)) {
            return Lists.newArrayList();
        }
        // 填充店铺信息
        fillProductShopInfo(pcRecommendProductVOS);
        Map<Long, PcRecommendProductVO> skuIdToInfoMap = pcRecommendProductVOS.stream().collect(Collectors.toMap(PcRecommendProductVO::getId, c -> c, (f, s) -> f));
        List<PcRecommendCardVO> recommendCardVOS = PcRecommendCardVOHelper.creates(cardDTOS, skuIdToInfoMap, false);
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，saasOrganSign：{}，recommendCardVOS：{}", saasOrganSign, JSONArray.toJSONString(recommendCardVOS));
        }
        if (log.isDebugEnabled()) {
            log.debug("组装搜索卡片VO信息，saasOrganSign：{}，最终的recommendCardVOS：{}", saasOrganSign, JSONArray.toJSONString(recommendCardVOS));
        }
        return recommendCardVOS;
    }

    private void fillRecommendQueryParam(Map<Long, EcpRecommendProductCardDTO> cardMap, RecommendCsuDataTagQueryParam queryParam) {
        if (MapUtils.isEmpty(cardMap)) {
            return;
        }
        List<RecommendCsuDataTagCsuDTO> csuList = queryParam.getCsuList();
        if (CollectionUtils.isEmpty(csuList)) {
            return;
        }
        csuList.stream().filter(Objects::nonNull).forEach(item -> {
            EcpRecommendProductCardDTO ecpRecommendProductCardDTO = cardMap.get(item.getId());
            if (ecpRecommendProductCardDTO != null) {
                item.setSpuRecallStrategyName(ecpRecommendProductCardDTO.getSpuRecallStrategyName());
                if (ecpRecommendProductCardDTO.getProduct() != null) {
                    item.setSpuId(ecpRecommendProductCardDTO.getProduct().getSpuId());
                }
            }
        });
        queryParam.setCsuList(csuList);
    }


    public List<PcRecommendProductVO> resetProductPrice(List<PcRecommendProductVO> products) {
        if (CollectionUtils.isEmpty(products)) {
            return products;
        }
        products.stream().filter(Objects::nonNull).forEach(item -> {
            //商品标签
            ProductActivityTagDTO activityTag = new ProductActivityTagDTO();
            activityTag.setTagUrl("");
            activityTag.setTagNoteBackGroupUrl("");
            activityTag.setSkuTagNotes(new ArrayList<>());
            item.setFob(BigDecimal.ZERO);
            item.setUnitPrice(null);
            item.setActivityTag(activityTag);
            //毛利
            item.setGrossMargin("");
            //建议零售价
            item.setSuggestPrice(BigDecimal.ZERO);
            //控销零售价
            item.setUniformPrice(BigDecimal.ZERO);
            // 对比价
            item.setRetailPrice(BigDecimal.ZERO);
            // 拼团 批购包邮 秒杀
            if (Objects.nonNull(item.getActPt())) {
                item.getActPt().setAssemblePrice(BigDecimal.ZERO);
            }
            if (Objects.nonNull(item.getActPgby())) {
                item.getActPgby().setAssemblePrice(BigDecimal.ZERO);
            }
            if (Objects.nonNull(item.getActSk())) {
                item.getActSk().setSkPrice(BigDecimal.ZERO);
            }
        });
        return products;
    }


    public List<PcRecommendProductVO> fillProductShopInfo(List<PcRecommendProductVO> products) {
        if (CollectionUtils.isEmpty(products)) {
            return products;
        }
        // 填充商品的店铺信息
        Set<String> shopCodes = products.stream()
                .filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShopCode()))
                .map(PcRecommendProductVO::getShopCode).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(shopCodes)) {
            return products;
        }
        List<ShopInfoDTO> shopInfoDTOS = shopQueryRemoteService.queryShopInfosByShopCodes(Lists.newArrayList(shopCodes));
        if (CollectionUtils.isEmpty(shopInfoDTOS)) {
            return products;
        }
        Map<String, ShopInfoDTO> shopCodeToShopInfoDTOMap = shopInfoDTOS.stream().filter(item -> item != null && item.getShopCode() != null)
                .collect(Collectors.toMap(ShopInfoDTO::getShopCode, Function.identity(), (a, b) -> a));
        if (MapUtils.isEmpty(shopCodeToShopInfoDTOMap)) {
            return products;
        }
        products.stream().filter(item -> Objects.nonNull(item) && StringUtils.isNotEmpty(item.getShopCode()))
                .forEach(item -> {
                    ShopInfoDTO shopInfoDTO = shopCodeToShopInfoDTOMap.get(item.getShopCode());
                    if (Objects.nonNull(shopInfoDTO)) {
                        item.setShopName(shopInfoDTO.getShowName());
                        item.setShopUrl(shopInfoDTO.getPcLink());
                    }
                });
        return products;
    }

    public List<PcRecommendProductVO> fillProductMarketingActivityInfo(List<PcRecommendProductVO> products, Map<Long, GroupBuyingInfoDto> csuIdToGroupBuyingInfoMap) {
        if (CollectionUtils.isEmpty(products) || MapUtils.isEmpty(csuIdToGroupBuyingInfoMap)) {
            return products;
        }
        Long id;
        GroupBuyingInfoDto groupBuyingInfoDto;
        for (PcRecommendProductVO product : products) {
            id = product.getId();
            groupBuyingInfoDto = csuIdToGroupBuyingInfoMap.get(id);
            if (Objects.nonNull(groupBuyingInfoDto)) {
                if (Objects.equals(groupBuyingInfoDto.getActivityType(), MarketingEnum.PING_TUAN.getCode())) {
                    PcRecommendProductVOHelper.setActPt(product, groupBuyingInfoDto, appProperties.getIsApplyListShowType());
                } else if (Objects.equals(groupBuyingInfoDto.getActivityType(), MarketingEnum.PI_GOU_BAO_YOU.getCode())) {
                    PcRecommendProductVOHelper.setActPgby(product, groupBuyingInfoDto, appProperties.getIsApplyListShowType());
                }
                if (Objects.nonNull(product.getActPt())) {
                    product.setPrice(product.getActPt().getAssemblePrice());
                }
                if (Objects.nonNull(product.getActPgby())) {
                    product.setPrice(product.getActPgby().getAssemblePrice());
                }
            }
        }
        return products;
    }


    private Map<Long, EcpRecommendProductCardDTO> getCardCsuIdsAndSpuRecallStrategyName(List<EcpRecommendBaseCardDTO> cardDTOS) {
        Map<Long, EcpRecommendProductCardDTO> csuIdToSpuRecallStrategyNameMap = Maps.newHashMapWithExpectedSize(16);
        cardDTOS.stream().forEach(recommendBaseCardDTO -> {
            if (recommendBaseCardDTO instanceof EcpRecommendProductCardDTO) {
                EcpRecommendProductCardDTO ecpRecommendProductCardDTO = (EcpRecommendProductCardDTO) recommendBaseCardDTO;
                if (Objects.nonNull(ecpRecommendProductCardDTO.getProduct())) {
                    csuIdToSpuRecallStrategyNameMap.put(ecpRecommendProductCardDTO.getProduct().getId(), ecpRecommendProductCardDTO);
                }
            }
        });
        return csuIdToSpuRecallStrategyNameMap;
    }
}
