package com.xyy.ec.pc.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.hyperspace.api.MarketingRebateApi;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateWeChatBindingBaseResult;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateWeChatBindingParam;
import com.xyy.ec.merchant.bussiness.api.*;
import com.xyy.ec.merchant.bussiness.api.account.AccountMerchantWechatRelApi;
import com.xyy.ec.merchant.bussiness.api.account.LoginAccountApi;
import com.xyy.ec.merchant.bussiness.api.account.WechatLoginApi;
import com.xyy.ec.merchant.bussiness.api.admin.MerchantAdminBusinessApi;
import com.xyy.ec.merchant.bussiness.api.wxwork.WxWorkBusinessApi;
import com.xyy.ec.merchant.bussiness.base.PcRegisterFirstModel;
import com.xyy.ec.merchant.bussiness.base.ResultMessage;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantMdLogBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.MerchantPwdModifiLogBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.ShippingAddressBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.account.*;
import com.xyy.ec.merchant.bussiness.enums.ResultCodeEnum;
import com.xyy.ec.merchant.bussiness.enums.SiteEnum;
import com.xyy.ec.merchant.bussiness.utils.Pwdverifiers;
import com.xyy.ec.pc.authentication.domain.JwtPrincipal;
import com.xyy.ec.pc.authentication.service.TokenService;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.PasswordVerifier;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.constants.MerchantLicenseEnum;
import com.xyy.ec.pc.controller.vo.AccountRegisterVO;
import com.xyy.ec.pc.enums.MerchantStatusEnum;
import com.xyy.ec.pc.exception.AuthenticationException;
import com.xyy.ec.pc.interceptor.helper.DifferentPlacesLoginHelper;
import com.xyy.ec.pc.interceptor.helper.ForceUpdatePasswordHelper;
import com.xyy.ec.pc.interceptor.helper.SpiderHelper;
import com.xyy.ec.pc.model.Merchant;
import com.xyy.ec.pc.model.ShippingAddress;
import com.xyy.ec.pc.model.dto.CrawlerDto;
import com.xyy.ec.pc.remote.LoginAgreementBussinessRemoteService;
import com.xyy.ec.pc.rpc.MerchantServiceRpc;
import com.xyy.ec.pc.search.utils.SnowGroundUtil;
import com.xyy.ec.pc.service.*;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.pc.util.ipip.IPUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import com.xyy.ec.merchant.bussiness.api.UserWechatBindApi;

/**
 * @Description: 登录/注册控制器
 * @Author: WanKp
 * @Date: 2018/8/25 17:28
 **/
@Controller
@RequestMapping("/login")
public class LoginController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class);

    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;

    @Autowired
    private MerchantServiceRpc merchantServiceRpc;

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private AsyncMerchantInfoService asyncMerchantInfoService;

    @Autowired
    private Config config;

    @Autowired
    private BuryingPointService buryingPointService;

    @Reference(version = "1.0.0")
    private MerchantPwdModifiLogBussinessApi logBussinessApi;

    @Autowired
    private AsyncInfoForSaasService asyncInfoForSaasService;

    @Reference(version = "1.0.0")
    private MerchantMdLogBusinessApi merchantMdLogBusinessApi;

    @Reference(version = "1.0.0")
    private LicenseAuditBussinessApi licenseAuditBussinessApi;

    @Resource
    private CrawlerUtil crawlerUtil;

    @Autowired
    private LoginAccountService loginAccountService;

    @Reference(version = "1.0.0")
    private MerchantCancelFreezeLogBusinessApi merchantCancelFreezeLogBusinessApi;

    @Autowired
    private SnowGroundUtil snowGroundUtil;

    @Autowired
    private SpiderHelper spiderHelper;

    @Autowired
    private LoginAgreementBussinessRemoteService loginAgreementBussinessRemoteService;

    @Value("${switch.login.agreement:false}")
    private Boolean switchLoginAgreement;

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    @Reference(version = "1.0.0")
    private WechatLoginApi wechatLoginApi;

    @Autowired
    private DifferentPlacesLoginHelper differentPlacesLoginHelper;

    @Autowired
    private ForceUpdatePasswordHelper forceUpdatePasswordHelper;

    @Autowired
    private TokenService tokenService;

    @Reference(version = "1.0.0")
    private WxWorkBusinessApi wxWorkBusinessApi;

    @Reference(version = "1.0.0")
    private AccountMerchantWechatRelApi accountMerchantWechatRelApi;

    @Reference(version = "1.0.0")
    private MerchantAdminBusinessApi merchantAdminBusinessApi;

    @Value("${new.login.open:true}")
    private boolean newLoginOpen;

    @Reference(version = "1.0.0")
    private UserWechatBindApi userWechatBindApi;

    /**
     * 跳转到登录页
     *
     * @return
     */
    @RequestMapping(value = "/image.htm", method = RequestMethod.GET)
    public ModelAndView image() {
        return new ModelAndView("/image.ftl");

    }

    /**
     * 跳转到登录页
     *
     * @return
     */
    @RequestMapping(value = {"", "/login.htm"}, method = RequestMethod.GET)
    public ModelAndView login(String errorMsg, HttpServletRequest request, RedirectAttributes redirectAttributes) {

        // 启用新登录页
        if (newLoginOpen) {
            // 1. 将所有参数添加到 RedirectAttributes
            request.getParameterMap().forEach((key, values) -> {
                for (String value : values) {
                    redirectAttributes.addAttribute(key, value);
                }
            });
            String baseUrl = request.getHeader("x-forwarded-proto") + "://" + request.getHeader("host");
            return new ModelAndView("redirect:" + baseUrl + "/new/login");
        }
//        xyyIndentityValidator.logout();
        //退出登录
        Map<String, Object> model = new HashMap<String, Object>();
        String redirectUrl = request.getParameter("redirectUrl");
        if (redirectUrl == null || redirectUrl.indexOf("/login/forget_pass_last") != -1 || redirectUrl.indexOf("/login/register") != -1) {
            redirectUrl = "";
        }
        if (StringUtil.isNotEmpty(errorMsg)) {
            model.put("errorMsg", errorMsg);
        }
        model.put("level", null);
        model.put("redirectUrl", redirectUrl);
        MerchantPrincipal merchantBussinessDto = null;
        try {
            merchantBussinessDto = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
        } catch (Exception e) {
            LOGGER.error("登录出现异常,e="+e);
        }
        Long merchantId = 0L;
        if (merchantBussinessDto != null) {
            //补充数据
            return new ModelAndView(new RedirectView("/",true,false));
//            merchantId = merchantBussinessDto.getId();
        }
        model.put("merchantId", merchantId);
        if (StringUtils.isNotBlank(request.getParameter("registerSource")) && "7".equals(request.getParameter("registerSource"))){
            //雨诺跳转登录数据记录为登录
            MerchantMdLogBussinessDto merchantMdLogBussinessDto = new MerchantMdLogBussinessDto();
            merchantMdLogBussinessDto.setDataType("0");
            merchantMdLogBussinessDto.setOperateTime(new Date());
            merchantMdLogBussinessDto.setRegisteredSource((byte)0);
            merchantMdLogBusinessApi.asyncInsert(merchantMdLogBussinessDto);
        }
        model.put("registerSource",request.getParameter("registerSource"));
        return new ModelAndView("/login.ftl", model);

    }


    /**
     * 采购宝跳转登录
     * @param errorMsg
     * @param request
     * @return
     */
    @RequestMapping(value = "/purchase/login.htm", method = RequestMethod.GET)
    public ModelAndView purchaseLogin(String errorMsg, HttpServletRequest request) {
        Map<String, Object> model = new HashMap<String, Object>();
        String deviceId = request.getParameter("hid");
        try {
            String redirectUrl = request.getParameter("redirectUrl");
            if (redirectUrl == null || redirectUrl.indexOf("/login/forget_pass_last") != -1 || redirectUrl.indexOf("/login/register") != -1) {
                redirectUrl = "";
            }
            if (StringUtil.isNotEmpty(errorMsg)) {
                model.put("errorMsg", errorMsg);
            }
            model.put("redirectUrl", redirectUrl);
            MerchantPrincipal merchantBussinessDto = null;
            try {
                merchantBussinessDto = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            } catch (Exception e) {
                LOGGER.error("登录出现异常,e=" + e);
            }
            Long merchantId = 0L;
            if (merchantBussinessDto != null) {
                boolean result = true;
                boolean flag = licenseAuditBussinessApi.getAuditStatusByMerchantId(merchantBussinessDto.getId());
                //查询资质信息
                MerchantBussinessDto dto = merchantBussinessApi.selectByPrimaryKey(merchantBussinessDto.getId());
                if(null == dto){
                    return new ModelAndView("/comparativePrice/login.ftl", model);
                }
                LOGGER.info("采购宝登录页面,用户编号是{}，一审状态是{},资质状态是{}", merchantBussinessDto.getId(), flag, dto.getLicenseStatus());
                //一审不通过，资质状态为资质未提交,首营资质审核中 拦截登录
                if (flag == false && (dto.getLicenseStatus() == MerchantLicenseEnum.license_uncommitted.getId() || dto.getLicenseStatus() == MerchantLicenseEnum.license_camp_auditing.getId())) {
                    //拦截登录
                    result = false;
                }
                if (result) {
                    merchantBussinessApi.refreshMerchantSourceCache(merchantBussinessDto.getId(), 91);
                    //登录成功后跳转商品列表
                    redirectUrl = "/comparisonPrice/skuInfo.htm";
                    return new ModelAndView(new RedirectView(redirectUrl.replaceAll("%26", "&"),true,false));
                }
            }
            model.put("merchantId", merchantId);
            model.put("deviceId", deviceId);
            model.put("registerSource", 9);
        } catch (Exception e) {
            LOGGER.error("采购宝跳转登录有错误，error是{}", e);
        }
        return new ModelAndView("/comparativePrice/login.ftl", model);
    }

    /**
     * 采购宝登录
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/purchase/login.htm", method = RequestMethod.POST)
    public Object login_purchase(HttpServletRequest request) throws Exception {
        String loginName = request.getParameter("name");
        String loginPass = request.getParameter("password");
        String deviceId = request.getParameter("hid");
        LOGGER.info("采购宝登录,登录用户名是{}", loginName);
        String from = request.getParameter("redirectUrl");
        //错误数据封装
        Map<String, Object> model = new HashMap<>();
        model.put("registerSource", request.getParameter("registerSource"));
        model.put("errorMsg", "暂未开通权限，请联系对应的销售人员咨询");
        model.put("deviceId", deviceId);
        model.put("name", loginName);
        model.put("redirectUrl", from);
        model.put("loginName",loginName);
        try {
            PasswordVerifier verifier = new PasswordVerifier(loginName, loginPass, "1", deviceId);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.login(verifier);
            if (merchant != null) {
                //如果没有账号激活，则跳转到填写邀请码页面
                if (merchant.getStatus().equals(MerchantStatusEnum.STATUS_NON_ACTIVATED.getId())) {
                    LOGGER.info("采购宝踢出登录用户id:{}",merchant.getId());
                    xyyIndentityValidator.logout();
                    return new ModelAndView("/comparativePrice/login.ftl", model);
                }
                //埋点数据
//                buryingPointService.actionLoginAndExit(merchant.getId(), loginName, ConstUtil.BuryingPointName.ACTION_lOGIN);
                //采购宝登录 判断是否通过一审
                boolean flag = licenseAuditBussinessApi.getAuditStatusByMerchantId(merchant.getId());
                LOGGER.info("采购宝登录,登录用户名是{}，用户编号是{}，一审状态是{},资质状态是{}", loginName, merchant.getId(), flag, merchant.getLicenseStatus());
                //一审不通过，资质状态为资质未提交,首营资质审核中 拦截登录
                if (flag == false && (merchant.getLicenseStatus() == MerchantLicenseEnum.license_uncommitted.getId() || merchant.getLicenseStatus() == MerchantLicenseEnum.license_camp_auditing.getId())) {
                    //拦截登录
                    return new ModelAndView("/comparativePrice/login.ftl", model);
                }
                //登录成功后跳转商品列表
                merchantBussinessApi.refreshMerchantSourceCache(merchant.getId(), 91);
                String redirectUrl = "/comparisonPrice/skuInfo.htm";
                return new ModelAndView(new RedirectView(redirectUrl.replaceAll("%26", "&"),true,false));
            }
            return new ModelAndView("/comparativePrice/login.ftl", model);
        } catch (AuthenticationException e) {
            //未激活或者账号冻结
            if (e.getCode() == ResultCodeEnum.STATUS_NON_ACTIVATED.getCode() || e.getCode() == ResultCodeEnum.USER_FROZEN.getCode()) {
                return new ModelAndView("/comparativePrice/login.ftl", model);
            }
            //ka用户验证
            if (e.getCode() == ResultCodeEnum.TYPE_OF_KA_ONE.getCode() || e.getCode() == ResultCodeEnum.TYPE_OF_KA_TWO.getCode()){
                return new ModelAndView("/comparativePrice/login.ftl", model);
            }
            model.put("errorMsg", e.getMessage());
            return new ModelAndView("/comparativePrice/login.ftl", model);
        } catch (Exception e) {
            model.put("errorMsg", "登录失败");
            LOGGER.error("登录出错,e=" + e);
            return new ModelAndView("/comparativePrice/login.ftl", model);
        }
    }

    /**
     * 登录
     *
     * @param request
     * @return
     * @throws Exception
     */

    @RequestMapping(value = "/login.htm", method = RequestMethod.POST)
    public Object login_commit(HttpServletRequest request) throws Exception {

        String loginName = request.getParameter("name");
        String loginPass = request.getParameter("password");
        String redirectUrl = request.getParameter("redirectUrl");

        String checkTime = request.getParameter("checkTime");
        String agreements = request.getParameter("agreements");


        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("登录，loginName：{}，redirectUrl：{}", loginName, redirectUrl);
        }
        try {
            PasswordVerifier verifier = new PasswordVerifier(loginName, loginPass);
            JwtPrincipal merchant = (JwtPrincipal) xyyIndentityValidator.login(verifier);
            if (switchLoginAgreement){
                try {
                    String ip = IPUtils.getClientIP(request);
                    loginAgreementBussinessRemoteService.insertLoginAgreementLogForLogin(agreements,checkTime,loginName,merchant.getAccountId(),ip);
                }catch (Exception e){
                    LOGGER.error("登录日志插入失败，errorMsg：{}", e);
                }
            }
            if (merchant != null) {
                Map<String, Object> map = loginAccountService.loginBuildDate(merchant.getAccountId());
                //关联店铺数
                Integer shopCount = (Integer) map.get("shopCount");
                if (Objects.equals(shopCount, 1)) {
                    Long merchantId = (Long) map.get("merchantId");
                    Boolean isAudit = (Boolean) map.get("isAudit");
                    Integer status = (Integer) map.get("status");
                    if (BooleanUtils.isTrue(isAudit)) { //关联状态审核通过且店铺状态审核通过，跳转到首页
                        if (Objects.equals(status, 3) && Objects.nonNull(merchantId)) {
                            // 关联状态审核通过且店铺状态审核通过，跳转到首页时校验药店状态，若是冻结，则给出用户相应的提示
                            boolean result = merchantCancelFreezeLogBusinessApi.findIsCancellation(merchantId);
                            String errorMsg = "";
                            if (result) {
                                LOGGER.info("登录，accountId：{}，merchantId：{}，已注销", merchant.getAccountId(), merchantId);
                                errorMsg = "您的账号已成功注销";
                            } else {
                                LOGGER.info("登录，accountId：{}，merchantId：{}，已冻结", merchant.getAccountId(), merchantId);
                                errorMsg = "该店铺处于冻结状态，请联系客服解冻";
                            }
                            Map<String, Object> model = Maps.newHashMapWithExpectedSize(3);
                            model.put("name", loginName);
                            model.put("errorMsg", errorMsg);
                            model.put("redirectUrl", redirectUrl);
                            return new ModelAndView("/login.ftl", model);
                        }
                        JwtPrincipal jwtPrincipal = (JwtPrincipal) xyyIndentityValidator.setPrincipalMerchant(merchantId, merchant.getAccountId());
                        ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.selectLoginAccountById(merchant.getAccountId());
                        LoginAccountDto loginAccountDto = apiRPCResult.getData();
                        // 强制修改密码检查
                        if (forceUpdatePasswordHelper.check(loginAccountDto)) {
                            LOGGER.warn("强制修改密码, 账号[{}]", merchant.getAccountId());
                            // 刷新登录信息缓存
                            tokenService.refreshTokenOneHour(jwtPrincipal);
                            // 弹出提示需要修改密码弹窗
                            Map<String, Object> model = new HashMap<>();
                            model.put("loginName", loginName);
                            model.put("loginPass", loginPass);
                            model.put("redirectUrl", redirectUrl);
                            model.put("level", 2);
                            return new ModelAndView("/login.ftl", model);
                        }
                        // 爬虫校验
                        String realIP = IPUtils.getClientIP(request);
                        LOGGER.info("登录，保存登录凭证，loginName:{}, accountId:{}, merchantId:{}, realIP:{}", loginName, merchant.getAccountId(), merchantId, realIP);
                        if (spiderHelper.getNewSpiderInterceptionOpen()) {
                            if (spiderHelper.needSmsVerify(merchant.getAccountId().toString())) {
                                LOGGER.warn("爬虫账号[{}], 需要短信验证", merchant.getAccountId());
                                // 刷新登录信息缓存
                                tokenService.refreshTokenOneHour(jwtPrincipal);
                                // 弹出短信验证窗口
                                Map<String, Object> model = new HashMap<>();
                                model.put("loginName", loginName);
                                model.put("loginPass", loginPass);
                                model.put("redirectUrl", redirectUrl);
                                model.put("level", 0);
                                return new ModelAndView("/login.ftl", model);
                            }
                        }
                        // 异地登录检查
                        if (differentPlacesLoginHelper.isDifferentPlacesLogin(loginAccountDto)) {
                            LOGGER.warn("异地登录, 账号[{}]", merchant.getAccountId());
                            // 设置异地登录标识
                            merchant.setIsDifferentPlacesLogin(true);
                            // 刷新登录信息缓存
                            tokenService.refreshTokenOneHour(jwtPrincipal);
                            // 弹出短信验证窗口
                            Map<String, Object> model = new HashMap<>();
                            model.put("loginName", loginName);
                            model.put("loginPass", loginPass);
                            model.put("redirectUrl", redirectUrl);
                            model.put("level", 1);
                            return new ModelAndView("/login.ftl", model);
                        }
                        snowGroundUtil.uploadEventInfoForLoginSuccess(request, merchantId);
                        //组装数据
                        Map<String, Object> model = new HashMap<String, Object>();
                        //generateMdData(model, merchantId);
                        if (StringUtils.isEmpty(redirectUrl)) {
                            return new ModelAndView(new RedirectView("/", true, false), model);
                        } else {
                            return new ModelAndView(new RedirectView(redirectUrl, false, false), model);
                        }
                    } else {
                        if (StringUtils.isEmpty(redirectUrl)) {
                            return new ModelAndView(new RedirectView("/newstatic/#/register/selectLoginShop", false, false));
                        } else {
                            return new ModelAndView(new RedirectView("/newstatic/#/register/selectLoginShop?redirectUrl=" + URLEncoder.encode(redirectUrl, "UTF-8"), false, false));
                        }
                    }
                } else if (shopCount < Constants.IS1) {
                    return new ModelAndView(new RedirectView("/newstatic/#/register/connectPharmacy", false, false));
                } else if (shopCount > Constants.IS1) {
                    if (spiderHelper.getNewSpiderInterceptionOpen()) {
                        if (spiderHelper.needSmsVerify(merchant.getAccountId().toString())) {
                            LOGGER.warn("爬虫账号[{}], 需要短信验证", merchant.getAccountId());
                            // 刷新登录信息缓存
                            tokenService.refreshTokenOneHour(merchant);
                            // 弹出短信验证窗口
                            Map<String, Object> model = new HashMap<>();
                            model.put("loginName", loginName);
                            model.put("loginPass", loginPass);
                            model.put("redirectUrl", StringUtils.isEmpty(redirectUrl) ? "/newstatic/#/register/selectLoginShop" : "/newstatic/#/register/selectLoginShop?redirectUrl=" + URLEncoder.encode(redirectUrl, "UTF-8"));
                            model.put("level", 0);
                            return new ModelAndView("/login.ftl", model);
                        }
                    }
                    ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.selectLoginAccountById(merchant.getAccountId());
                    LoginAccountDto loginAccountDto = apiRPCResult.getData();
                    // 强制修改密码检查
                    if (forceUpdatePasswordHelper.check(loginAccountDto)) {
                        LOGGER.warn("强制修改密码, 账号[{}]", merchant.getAccountId());
                        // 刷新登录信息缓存
                        tokenService.refreshTokenOneHour(merchant);
                        // 弹出提示需要修改密码弹窗
                        Map<String, Object> model = new HashMap<>();
                        model.put("loginName", loginName);
                        model.put("loginPass", loginPass);
                        model.put("redirectUrl", redirectUrl);
                        model.put("level", 2);
                        return new ModelAndView("/login.ftl", model);
                    }
                    // 异地登录检查
                    if (differentPlacesLoginHelper.isDifferentPlacesLogin(loginAccountDto)) {
                        LOGGER.warn("异地登录, 账号[{}]", merchant.getAccountId());
                        // 设置异地登录标识
                        merchant.setIsDifferentPlacesLogin(true);
                        // 刷新登录信息缓存
                        tokenService.refreshTokenOneHour(merchant);
                        // 弹出短信验证窗口
                        Map<String, Object> model = new HashMap<>();
                        model.put("loginName", loginName);
                        model.put("loginPass", loginPass);
                        model.put("redirectUrl", StringUtils.isEmpty(redirectUrl) ? "/newstatic/#/register/selectLoginShop" : "/newstatic/#/register/selectLoginShop?redirectUrl=" + URLEncoder.encode(redirectUrl, "UTF-8"));
                        model.put("level", 1);
                        return new ModelAndView("/login.ftl", model);
                    }
                    if (StringUtils.isEmpty(redirectUrl)) {
                        return new ModelAndView(new RedirectView("/newstatic/#/register/selectLoginShop", false, false));
                    } else {
                        return new ModelAndView(new RedirectView("/newstatic/#/register/selectLoginShop?redirectUrl=" + URLEncoder.encode(redirectUrl, "UTF-8"), false, false));
                    }
                }
            } else {
                Map<String, Object> model = new HashMap<>();
                model.put("name", loginName);
                model.put("errorMsg", "登录失败");
                model.put("redirectUrl", redirectUrl);
                return new ModelAndView("/login.ftl", model);
            }
        }catch (AuthenticationException ae){
            Map<String, Object> model = new HashMap<>();
            model.put("name", loginName);
            model.put("errorMsg", ae.getMessage());
            model.put("redirectUrl", redirectUrl);
            LOGGER.error("登录出错,ae=", ae);
            return new ModelAndView("/login.ftl", model);
        }catch (Exception e) {
            Map<String, Object> model = new HashMap<>();
            model.put("name", loginName);
            model.put("errorMsg", "登录失败");
            model.put("redirectUrl", redirectUrl);
            LOGGER.error("登录出错,e=", e);
            return new ModelAndView("/login.ftl", model);
        }
        return new ModelAndView("/login.ftl");
    }

    /**
     * 超时弹窗登录
     *
     * @param mobileNumber 手机号
     * @param password     密码
     * @return
     */
    @RequestMapping(value = "/login.json", method = RequestMethod.POST)
    @ResponseBody
    public Object loginOutOfTime(@RequestParam("mobileNumber") String mobileNumber, @RequestParam("password") String password, HttpServletRequest request) {
        Long id = 0L;
        String mobile = "";
        try {
            PasswordVerifier verifier = new PasswordVerifier(mobileNumber, password);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.login(verifier);
            if (merchant != null) {
                Map<String, Object> map = loginAccountService.loginBuildDate(merchant.getAccountId());
                //关联店铺数
                Integer shopCount = (Integer) map.get("shopCount");
                if (Objects.equals(shopCount, 1)) {
                    Long merchantId = (Long) map.get("merchantId");
                    Boolean isAudit = (Boolean) map.get("isAudit");
                    Integer status = (Integer) map.get("status");
                    if (BooleanUtils.isTrue(isAudit)) { //关联状态审核通过且店铺状态审核通过，跳转到首页
                        if (Objects.equals(status, 3) && Objects.nonNull(merchantId)) {
                            // 关联状态审核通过且店铺状态审核通过，跳转到首页时校验药店状态，若是冻结，则给出用户相应的提示
                            boolean result = merchantCancelFreezeLogBusinessApi.findIsCancellation(merchantId);
                            String errorMsg = "";
                            if (result) {
                                LOGGER.info("超时弹窗登录，accountId：{}，merchantId：{}，已注销", merchant.getAccountId(), merchantId);
                                errorMsg = "您的账号已成功注销";
                            } else {
                                LOGGER.info("超时弹窗登录，accountId：{}，merchantId：{}，已冻结", merchant.getAccountId(), merchantId);
                                errorMsg = "该店铺处于冻结状态，请联系客服解冻";
                            }
                            return this.addError(errorMsg);
                        }
                        xyyIndentityValidator.setPrincipalMerchant(merchantId,merchant.getAccountId());
                    }
//                    else {
//                        return new ModelAndView(new RedirectView("/newstatic/#/register/selectLoginShop"));
//                    }
                }
//                else if (shopCount < Constants.IS1) {
//                    return new ModelAndView(new RedirectView("/newstatic/#/register/connectPharmacy"));
//                } else if (shopCount > Constants.IS1) {
//                    return new ModelAndView(new RedirectView("/newstatic/#/register/selectLoginShop"));
//                }
                Map<String, Object> result = this.addResult();
                result.put("data", map);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("超时弹窗登录，{}", JSONObject.toJSONString(result));
                }
                return result;

                /*//如果没有账号激活，则跳转到填写邀请码页面
                if (merchant.getStatus() == MerchantStatusEnum.STATUS_NON_ACTIVATED.getId()) {
                    id = merchant.getId();
                    mobile = merchant.getMobile();
                    LOGGER.info("超时踢出登录用户id:{}",id);
                    xyyIndentityValidator.logout();
                    return new ModelAndView(new RedirectView("/login/register_step.htm?merchantId=" + id + "&mobile=" + mobile + "&mobilepwd=" + password + "&sourceType=1",true,false));
                }*/
            }
            return this.addResult();
        } catch (Exception e) {
            LOGGER.error("登录失败,e="+e);
            /*if ("STATUS_NON_ACTIVATED".equals(e.getMessage())) {
                return new ModelAndView(new RedirectView("/login/register_step.htm?merchantId=" + id + "&mobile=" + mobileNumber + "&mobilepwd=" + password + "&sourceType=1",true,false));
            } else {
                return this.addError(e.getMessage());
            }*/
            return this.addError(e.getMessage());
        }
    }

    /**
     * 注册页面(第一步：填写基本资料)
     *
     * @return
     */
    @RequestMapping(value = "/register.htm", method = RequestMethod.GET)
    public ModelAndView register(HttpServletRequest request) {
        Map<String, Object> model = new HashMap<String, Object>();
        model.put("merchant", new MerchantBussinessDto());
        //注册来源5为sass引流过来的数据
        if ("registerSource".equals(request.getParameter("registerSource"))){
            model.put("registerSource",5);
        }else {
            model.put("registerSource",request.getParameter("registerSource"));
        }
        model.put("organSign",request.getParameter("organSign"));
        return new ModelAndView("/register.ftl", model);
    }

    @RequestMapping("/valition.json")
    @ResponseBody
    public Object valition(@RequestParam("mobileNumber") String mobileNumber) {
        MerchantBussinessDto dto = merchantBussinessApi.findMerchantByMobile(mobileNumber);
        if (dto != null) {
            return this.addError("账号已经存在，请去 <a href='/login/login.htm'>登录</a>");
        } else {
            return this.addResult("success");
        }
    }

    /**
     * 注册页面(提交数据，并且跳转到下个页面)
     *
     * @return
     */
    @RequestMapping(value = "/register_next", method = RequestMethod.POST)
    @ResponseBody
    public Object register_next(HttpServletRequest request, AccountRegisterVO accountRegisterVO) {
        try {
            if(StringUtils.isEmpty(accountRegisterVO.getContactName())){
                return this.addError("联系人姓名不能为空");
            }
            accountRegisterVO.setRegisterSource(4);
            LoginAccountDto loginAccountDto = new LoginAccountDto();
            BeanUtils.copyProperties(accountRegisterVO,loginAccountDto);
            ApiRPCResult res = loginAccountService.register(loginAccountDto,accountRegisterVO.getCode());
            if(!res.isSuccess()){
                return this.addError(res.getErrMsg());
            }
            //自动登录
            PasswordVerifier verifier = new PasswordVerifier(accountRegisterVO.getMobile(), MD5Util.getMD5Str(accountRegisterVO.getPassword()));
            xyyIndentityValidator.login(verifier);
            Map<String,Object> dataMap = new HashMap<>();
            dataMap.put(CODE, CODE_SUCCESS);
            return this.addResult("data", dataMap);
        } catch (Exception e) {
            LOGGER.error("注册失败,mobile:{}", accountRegisterVO.getMobile(), e);
            return this.addError("注册失败");
        }
    }

    @RequestMapping(value = "/register_step.htm", method = RequestMethod.GET)
    public ModelAndView register_step(HttpServletRequest request) {
        Map<String, Object> model = new HashMap<String, Object>();
        String merchantId = request.getParameter("merchantId");
        String mobile = request.getParameter("mobile");
        String mobilepwd = request.getParameter("mobilepwd");
        model.put("merchantId", merchantId);
        model.put("mobile", mobile);
        String sourceType = request.getParameter("sourceType");
        model.put("sourceType", sourceType);
        if(StringUtil.isEmpty(sourceType)){
            model.put("mobilepwd", MD5Util.getMD5Str(mobilepwd));
        }else{
            model.put("mobilepwd", mobilepwd);
        }
        model.put("registerSource",request.getParameter("registerSource"));
        return new ModelAndView("/register_next.ftl",model);
    }

    /**
     * 得到验证码
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/checkPhoCode.htm", method = RequestMethod.GET)
    @ResponseBody
    public String checkPhoCode(HttpServletRequest request, HttpServletResponse response)throws Exception {
        Map<String, Object> model = new HashMap<String, Object>();
        try{
            String photoCode = request.getParameter("photoCode");
            String mobile = request.getParameter("mobile");
            String vCode = merchantBussinessApi.getPCVcode(photoCode.toUpperCase());
            if(StringUtil.isEmpty(vCode)){
                model.put("status", false);
                model.put("msg", "验证码已经失效，请重新生成");
                model.put("mobile", mobile);
                return JSONObject.toJSONString(model);
            }else{
                if(!photoCode.toUpperCase().equals(String.valueOf(vCode).toUpperCase())){
                    model.put("status", false);
                    model.put("msg", "验证码输入错误，请重新输入");
                    model.put("mobile", mobile);
                    return JSONObject.toJSONString(model);
                }
            }
            model.put("status", true);
            model.put("mobile", mobile);
            return JSONObject.toJSONString(model);
        }catch (Exception e){
            LOGGER.error("获取验证码异常,e="+e);
        }
        return null;
    }


    /**
     * 得到验证码
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/getCode.htm", method = RequestMethod.GET)
    public ModelAndView getCode(HttpServletRequest request, HttpServletResponse response)throws Exception {
        String mechantId = request.getParameter("merchantId");
        response.setContentType("image/jpeg");
        VerifyCodeUtils vCode = new VerifyCodeUtils(100,30,4,5);
        vCode.write(response.getOutputStream());
        String code = vCode.getCode().toUpperCase();
        merchantBussinessApi.setPCVcode(code,code,300);
        return null;
    }

    /**
     * 注册：第二步（验证商户邀请码，完成注册）
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/register_last.htm")
    public ModelAndView register_last(HttpServletRequest request)throws Exception {
        String merchantId = request.getParameter("merchantId");
        String mobile = request.getParameter("mobile");
        String authCode = request.getParameter("authCode");
        String code = request.getParameter("code");
        String mobilepwd = request.getParameter("mobilepwd");

        Map<String, Object> model = new HashMap<String, Object>();
        model.put("registerSource",request.getParameter("registerSource"));
        if(request.getMethod().equals("GET")){
            model.put("merchantId", merchantId);
            model.put("mobile", mobile);
            return new ModelAndView("/register_next.ftl",model);
        }
        try{
            ResultMessage<MerchantBussinessDto> resultMessage = merchantBussinessApi.appRegisterSecond(mobile,authCode,4);
            if (resultMessage.getCode()==ResultCodeEnum.ERROR.getCode()){
                model.put("status", false);
                model.put("msg", resultMessage.getMsg());
                model.put("merchantId", merchantId);
                model.put("mobile", mobile);
                model.put("mobilepwd", mobilepwd);
                model.put("sourceType", 1);
                return new ModelAndView("/register_next.ftl",model);
            }
            //注册成功之后跳转都注册成功的页面
            PasswordVerifier verifier = new PasswordVerifier(mobile, mobilepwd);
            verifier.setFlag(false);
            //强制走主库
            xyyIndentityValidator.login(verifier);
            //同步到神农
            asyncMerchantInfoService.pushEcMerchantInfoToGodErp("SYNC" +resultMessage.getResult().getId());
            //添加埋点信息
            //补充数据
            generateMdData(model, merchantId == null ? 0 : Long.parseLong(merchantId));
//            buryingPointService.actionUserRegister(merchantId == null ? 0 : Long.parseLong(merchantId));
            //push 豆芽数据
            asyncMerchantInfoService.pushMerchantRegister(Long.parseLong(merchantId));
            //雨诺过来的数据且登录成功记为首页(埋点日志)
            if (StringUtils.isNotBlank(request.getParameter("registerSource")) && "7".equals(request.getParameter("registerSource"))){
                MerchantMdLogBussinessDto merchantMdLogBussinessDto = new MerchantMdLogBussinessDto();
                merchantMdLogBussinessDto.setDataType("0");
                merchantMdLogBussinessDto.setOperateTime(new Date());
                merchantMdLogBussinessDto.setRegisteredSource((byte)2);
                merchantMdLogBussinessDto.setMerchantId(Long.parseLong(merchantId));
                merchantMdLogBusinessApi.asyncInsert(merchantMdLogBussinessDto);
            }
            return new ModelAndView(new RedirectView("/login/register_complete.htm",true,false));
        }catch(Exception se){
            LOGGER.error("注册第二步验证商户邀请码异常,e="+ExceptionUtils.getStackTrace(se));
            model.put("status", false);
            model.put("msg", se.getMessage());
            model.put("merchantId", merchantId);
            model.put("mobile", mobile);
            model.put("mobilepwd", mobilepwd);
            model.put("sourceType", 1);
            if (StringUtils.isNotBlank(request.getParameter("registerSource")) && "9".equals(request.getParameter("registerSource"))) {
                model.put("registerSource", 9);
            }
            return new ModelAndView("/register_next.ftl",model);
        }
    }

    /**
     * 注册成功页面（最后一步）
     *
     * @return
     */
    @RequestMapping(value = "/register_complete.htm", method = RequestMethod.GET)
    public ModelAndView registerComplete() {
        return new ModelAndView("/register_complete.ftl");
    }


    /**
     * 退出
     * @return
     */
    @RequestMapping(value = "/logout.htm", method = RequestMethod.GET)
    public ModelAndView logOut() {
        try {
            MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if(null != merchantBussinessDto) {
                LOGGER.info("退出登录踢出登录用户id:{}",merchantBussinessDto.getId());
//                buryingPointService.actionLoginAndExit(merchantBussinessDto.getId(), merchantBussinessDto.getMobile(), ConstUtil.BuryingPointName.ACTION_EXIT);
                merchantBussinessApi.refreshMerchantSourceCache(merchantBussinessDto.getMerchantId(),0);
            }
        }catch (Exception e){
            LOGGER.error("退出登录获得基本信息出错,error is {}",e);
        }
        xyyIndentityValidator.logout();
        return new ModelAndView(new RedirectView("/login/login.htm",true,false));
    }

    /**
     * 采购宝退登
     * @return
     */
    @RequestMapping(value = "/purchase/logout.htm", method = RequestMethod.GET)
    public ModelAndView PurchaselogOut() {
        try {
            MerchantBussinessDto merchantBussinessDto = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if(null != merchantBussinessDto) {
                LOGGER.info("采购宝退登踢出登录用户id:{}",merchantBussinessDto.getId());
//                buryingPointService.actionLoginAndExit(merchantBussinessDto.getId(), merchantBussinessDto.getMobile(), ConstUtil.BuryingPointName.ACTION_EXIT);
                merchantBussinessApi.refreshMerchantSourceCache(merchantBussinessDto.getId(),0);
            }
        }catch (Exception e){
            LOGGER.error("采购宝退出登录获得基本信息出错,error is {}",e);
        }
        xyyIndentityValidator.logout();
        return new ModelAndView(new RedirectView("/login/purchase/login.htm",true,false));
    }


    /**
     * 忘记密码:第一步
     *
     * @return
     */
    @RequestMapping(value = "/forgetPassword.htm", method = RequestMethod.GET)
    public ModelAndView forgetPassword() {
        return new ModelAndView("/forgetPassword.ftl");
    }

    /**
     * 忘记密码:第二步
     * @return
     */
    @RequestMapping(value = "/forget_pass_next.htm")
    public ModelAndView forget_pass_next(HttpServletRequest request) {
        Map<String, Object> model = new HashMap<String, Object>();
        String mobile = request.getParameter("phone");
        String code = request.getParameter("yqcode");
        model.put("mobile", mobile);
        model.put("yqcode",code);
        try {

            if (StringUtils.isBlank(mobile)){
                model.put(mobile,null);
                model.put("msg","请输入手机号码");
                return new ModelAndView("/forgetPassword.ftl",model);
            }
            if (!MobileValidateUtil.isNumeric(mobile)){
                model.put(mobile,null);
                model.put("msg","手机号码不正确");
                return new ModelAndView("/forgetPassword.ftl",model);
            }
            if (mobile.length()>11){
                model.put(mobile,null);
                model.put("msg","手机号码不正确");
                return new ModelAndView("/forgetPassword.ftl",model);
            }
            if (!MobileValidateUtil.isPass(mobile)){
                model.put(mobile,null);
                model.put("msg","手机号码不正确");
                return new ModelAndView("/forgetPassword.ftl",model);
            }

            ResultMessage<?> resultMessage = merchantBussinessApi.checkVerificationCode(mobile, code);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if(resultCode!=ResultCodeEnum.SUCCESS.getCode()){
                model.put("msg",msg);
                return new ModelAndView("/forgetPassword.ftl",model);
            }
        } catch (Exception e) {
            return new ModelAndView("/forgetPassword.ftl",model);
        }
        return new ModelAndView("/forget_pass_next.ftl",model);
    }

    /**
     * 忘记密码:第三步
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/forget_pass_last.htm", method = RequestMethod.POST)
    public ModelAndView forget_pass_last(HttpServletRequest request) throws Exception {
        Map<String, Object> model = new HashMap<String, Object>();
        String mobile = request.getParameter("mobile");
        String password = request.getParameter("password");
        String code = request.getParameter("yqcode");
        model.put("mobile", mobile);
        try {
            String checkResult = Pwdverifiers.checkPwd(password);
            if (!checkResult.equals("OK")) {
                model.put("yqcode",code);
                model.put("msg", checkResult);
                return new ModelAndView("/forget_pass_next.ftl",model);
            }

            ResultMessage<?> resultMessage = merchantBussinessApi.checkVerificationCode(mobile, code);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if(resultCode!=ResultCodeEnum.SUCCESS.getCode()){
                model.put("yqcode",code);
                model.put("msg",msg);
                return new ModelAndView("/forget_pass_next.ftl",model);
            }
        }catch (Exception e){
            return new ModelAndView("/forget_pass_next.ftl",model);
        }

//        MerchantBussinessDto oldMerchant = merchantBussinessApi.findMerchantByMobile(mobile);
        com.xyy.ec.merchant.server.dto.LoginAccountDto loginAccountDto = loginAccountService.selectLoginAccountForLoginByMobile(mobile);
        if (loginAccountDto == null) {
            return new ModelAndView(new RedirectView("/login/forgetPassword.htm",true,false));
        }else{
            merchantBussinessApi.updatePassword(mobile, null, password);
            LoginAccountDto loginAccount = merchantServiceRpc.selectLoginAccountById(loginAccountDto.getId());
            String newPassword = MD5Util.getMD5Str(password);
            loginAccount.setPassword(newPassword);
            merchantServiceRpc.updateLoginAccount(loginAccount);
        }
        //todo insert 忘记密码修改密码记录
        MerchantPwdModifiLogBussinessDto logBussinessDto = new MerchantPwdModifiLogBussinessDto();
        logBussinessDto.setMerchantId(loginAccountDto.getId());
        logBussinessDto.setType(3);
        logBussinessApi.insert(logBussinessDto);
        // 关闭账号强制密码修改
        UpdateAccountPasswordVo updateAccountPasswordVo = UpdateAccountPasswordVo.builder()
                .accountId(loginAccountDto.getId())
                .userId(loginAccountDto.getId())
                .username(loginAccountDto.getAccountName())
                .reason("完成修改密码, 解除账号强制修改密码")
                .build();
        merchantAdminBusinessApi.liftForceUpdatePasswordById(updateAccountPasswordVo);
        // ******** 删除 accountId 所有会话
        loginAccountApi.clearAccountSessionId(loginAccountDto.getId());
        return new ModelAndView("/forget_pass_last.ftl",model);
    }

    @RequestMapping(value = "/agreement.htm", method = RequestMethod.GET)
    public ModelAndView agreement(HttpServletRequest request) {
        return new ModelAndView("/agreement.ftl");
    }

    @RequestMapping(value = "/agreement/getHtmlFile", method = RequestMethod.GET)
    public ModelAndView agreement(@RequestParam(value = "agrType") String agrType ,@RequestParam(value = "version", required = false) String version,@RequestParam(value = "lang", required = false) String lang, HttpServletRequest request) {
        if (StringUtils.isEmpty(lang) || StringUtils.isEmpty(version)){
            return new ModelAndView("/agreement.ftl");
        }
        return new ModelAndView("/helpCenter/agreements/agreement-"+agrType+"-"+version+"-"+lang+".ftl");
    }

    @RequestMapping("/remember.json")
    public ModelAndView remember(String loginName, String salt) {
        return new ModelAndView("/agreement.ftl");

    }

    @RequestMapping("/getIp")
    @ResponseBody
    public String getIp() {

        return IPUtils.getIp();
    }

    /**
     * 组装埋点数据
     * @param model
     * @param merchantId
     */
    private void generateMdData(Map<String, Object> model, Long merchantId) {
        try {
            if(null != model && merchantId > 0) {
                MerchantBussinessDto cust = merchantBussinessApi.findMerchantById(merchantId);
                if (null != cust) {
                    model.put("licenseStatus",cust.getLicenseStatus());
                    model.put("realName", cust.getRealName());
                    model.put("province", cust.getProvince());
                    model.put("provinceCode", cust.getProvinceCode());
                    model.put("city", cust.getCity());
                    model.put("cityCode", cust.getCityCode());
                    model.put("district", cust.getDistrict());
                    model.put("areaCode", cust.getAreaCode());
                    model.put("activeTime", cust.getActiveTime());
                    model.put("channelCode",cust.getChannelNames());
                }
            }
        }catch (Exception e){
            LOGGER.error("组装埋点数据失败,error is {}",e);
        }
    }


    /**
     * 判断用户是否需要登出做短信验证
     *
     * @param merchantId
     * @param ip
     * @return 0:不需要登出; 1:是爬虫; 2:是 KICK_OUT 账号
     */
    private int needSmsVerify(Long merchantId, String ip) {

        // IP白名单用户(北京、武汉办公室等)，不需要短信验证
        if (crawlerUtil.isWhiteIP(merchantId, ip)) return 0;

        // 如果是爬虫，需要短信验证
        if (crawlerUtil.isCrawler(merchantId, ip)) {
            LOGGER.info("发现爬虫账号，需要登出，做短信验证。merchantId:{}, ip:{}", merchantId, ip);
            return 1;
        }

        // 白名单用户不需要检查kick out
        if (crawlerUtil.isWhiteUser(merchantId, ip)) {
            return 0;
        }

        // 如果是KICK_OUT用户，需要登出，做短信验证
        if (crawlerUtil.isKickOut(merchantId, ip)) {
            LOGGER.info("发现KICK_OUT请求，需要登出，做短信验证。merchantId:{}, ip:{}", merchantId, ip);
            return 2;
        }
        return 0;
    }

    /** 发送微信绑定验证码 */
    @PostMapping("/wechat/sendBindValidCode")
    @ResponseBody
    public Object sendBindSmsCode(@RequestParam("phone") String phone) {

        // 1、手机号格式校验
        if (org.apache.commons.lang3.StringUtils.isBlank(phone) || !MobileValidateUtil.isPass(phone) || phone.length() != 11) {
            return this.addError("手机号格式有误，请确认后重新填写！");
        }
        // 2、判断手机号码是否已注册
        ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.getAccountInfoByMobile(phone);
        if (apiRPCResult.isFail()) {
            return this.addError(apiRPCResult.getErrMsg());
        }
        if (apiRPCResult.isSuccess() && apiRPCResult.getData() == null) {
            HashMap<String, Object> result = new HashMap<>();
            result.put("status", 0);
            result.put("msg", "该手机号码未注册");
            return this.addResultData(result);
        }
        try {
            // 3、发送验证码
            ResultMessage<?> resultMessage = merchantBussinessApi.sendVerificationCode(phone,2);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if (resultCode != ResultCodeEnum.SUCCESS.getCode()) {
                return this.addError(msg);
            }
            HashMap<String, Object> result = new HashMap<>();
            result.put("status", 1);
            result.put("msg", "发送验证码成功");
            return this.addResultData(result);
        } catch (Exception e) {
            LOGGER.error("微信绑定短信验证码发送失败", e);
            return this.addError("微信绑定短信验证码发送失败");
        }
    }

    /** 账号绑定微信 */
    @PostMapping("/wechat/bind")
    @ResponseBody
    public Object wechatBind(
            @RequestParam("phone") String phone,
            @RequestParam("code") String code,
            @RequestParam("openid") String openid,
            @RequestParam("accessToken") String accessToken
    ) {
        try {
            // 1、校验验证码是否正确
            ResultMessage<?> resultMessage = merchantBussinessApi.checkVerificationCode(phone, code);
            int resultCode = resultMessage.getCode();
            String msg = resultMessage.getMsg();
            if (resultCode != ResultCodeEnum.SUCCESS.getCode()) {
                return this.addError(msg);
            }
            // 2、查询微信用户信息
            ApiRPCResult<WechatUserDto> apiRPCResult1 = wechatLoginApi.getUserinfo(accessToken, openid);
            WechatUserDto wechatUserDto = apiRPCResult1.getData();
            if (apiRPCResult1.isFail()) {
                return this.addError("获取微信用户信息失败，请稍后再试");
            }
            // 3、查询该微信是否已绑定过账号
            ApiRPCResult<LoginAccountDto> apiRPCResult2 = loginAccountApi.selectAccountByUnionId(wechatUserDto.getUnionid());
            LoginAccountDto loginAccountDto1 = apiRPCResult2.getData();
            if (loginAccountDto1 != null) {
                return this.addError(StrUtil.format("该微信已绑定要帮忙其他账号[{}]，不可重复绑定", loginAccountDto1.getMobile()));
            }
            // 4、判断手机号码是否已注册
            ApiRPCResult<LoginAccountDto> apiRPCResult3 = loginAccountApi.getAccountInfoByMobile(phone);
            LoginAccountDto loginAccountDto2 = apiRPCResult3.getData();
            if (apiRPCResult3.isFail()) {
                return this.addError(apiRPCResult3.getErrMsg());
            }
            if (apiRPCResult3.isSuccess() && apiRPCResult3.getData() == null) {
                HashMap<String, Object> result = new HashMap<>();
                result.put("status", 0);
                result.put("msg", "该手机号码未注册");
                return this.addResultData(result);
            }
            // 5、进行账号绑定微信
            ApiRPCResult<Boolean> apiRPCResult4 = loginAccountApi.updateUnionIdByMobile(wechatUserDto.getOpenid(), wechatUserDto.getUnionid(), phone);
            if (apiRPCResult4.getData() == null || BooleanUtils.isFalse(apiRPCResult4.getData())) {
                return this.addError("绑定微信失败");
            }
            // 6、查询该微信是否已绑定过账号关联关系 调用接口修改企业微信客户备注及标签
            this.wechatBindCommon(loginAccountDto2.getId(),wechatUserDto.getUnionid());
            userWechatBindApi.bind(loginAccountDto2.getId());
            HashMap<String, Object> result = new HashMap<>();
            result.put("status", 1);
            result.put("msg", "绑定微信成功");
            return this.addResultData(result);
        } catch (Exception e) {
            LOGGER.error("绑定微信异常", e);
            return this.addError("绑定微信异常");
        }
    }


    /**
     * 账号绑定微信 - 注册
     */
    @PostMapping("/wechat/register/bind")
    @ResponseBody
    public Object wechatBindRegister(
            @RequestParam("openid") String openid,
            @RequestParam("accessToken") String accessToken
    ) {
        try {
            // 1、查询微信用户信息
            ApiRPCResult<WechatUserDto> apiRPCResult1 = wechatLoginApi.getUserinfo(accessToken, openid);
            WechatUserDto wechatUserDto = apiRPCResult1.getData();
            if (apiRPCResult1.isFail()) {
                return this.addError("获取微信用户信息失败，请稍后再试");
            }
            // 2、查询该微信是否已绑定过账号
            ApiRPCResult<LoginAccountDto> apiRPCResult2 = loginAccountApi.selectAccountByUnionId(wechatUserDto.getUnionid());
            LoginAccountDto loginAccountDto1 = apiRPCResult2.getData();
            if (loginAccountDto1 != null) {
                return this.addError(StrUtil.format("该微信已绑定要帮忙其他账号[{}]，不可重复绑定", loginAccountDto1.getMobile()));
            }
            // 3、进行账号绑定微信
            JwtPrincipal jwtPrincipal = tokenService.getPrincipal();
            ApiRPCResult<Boolean> apiRPCResult4 = loginAccountApi.updateUnionIdById(wechatUserDto.getOpenid(), wechatUserDto.getUnionid(), jwtPrincipal.getAccountId());
            if (apiRPCResult4.getData() == null || BooleanUtils.isFalse(apiRPCResult4.getData())) {
                return this.addError("绑定微信失败");
            }
            // 4、查询该微信是否已绑定过账号关联关系 调用接口修改企业微信客户备注及标签
            this.wechatBindCommon(jwtPrincipal.getAccountId(), wechatUserDto.getUnionid());
            userWechatBindApi.bind(jwtPrincipal.getAccountId());
            HashMap<String, Object> result = new HashMap<>();
            result.put("status", 1);
            result.put("msg", "绑定微信成功");
            return this.addResultData(result);
        } catch (Exception e) {
            LOGGER.error("绑定微信异常", e);
            return this.addError("绑定微信异常");
        }
    }

    private Object wechatBindCommon(Long accountId, String unionid) {
        // 2、查询该微信是否已绑定过账号关联关系 绑定其他账号时覆盖绑定关系
        ApiRPCResult<AccountMerchantWechatRelDto> apiRPCResult2 = accountMerchantWechatRelApi.selectByUnionId(unionid);
        if (apiRPCResult2.getData() != null) {
            AccountMerchantWechatRelDto existRel = apiRPCResult2.getData();
            if (accountId.equals(existRel.getAccountId())) {
                LOGGER.info("微信已绑定当前账号, accountId:{}, unionid:{}", accountId, unionid);
                return this.addResult("绑定成功");
            }

            LOGGER.info("删除旧微信绑定关系, accountId:{}, unionid:{}", existRel.getAccountId(), unionid);
            ApiRPCResult<Boolean> deleteResult = accountMerchantWechatRelApi.deleteByUnionId(unionid);
            if (!deleteResult.isSuccess() || !BooleanUtils.isTrue(deleteResult.getData())) {
                LOGGER.error("删除旧微信绑定关系失败, unionid:{}, error:{}", unionid, deleteResult.getErrMsg());
                return addError("绑定失败");
            }

//            ApiRPCResult<LoginAccountDto> apiRPCResult = loginAccountApi.selectLoginAccountById(accountMerchantWechatRelDto.getAccountId());
//            if (apiRPCResult.isSuccess() && apiRPCResult.getData() != null) {
//                LoginAccountDto loginAccountDto = apiRPCResult.getData();
//                return this.addError(StrUtil.format("该微信已绑定药帮忙账号[{}]，不可重复绑定", loginAccountDto.getMobile()));
//            }
//            return this.addError("该微信已绑定药帮忙账号，不可重复绑定");
        }
        return wechatBindBase(accountId, unionid);
    }


    private Object wechatBindBase(Long accountId, String unionid) {
        // 3、绑定账号、店铺及微信关联关系
        AccountMerchantWechatRelDto accountMerchantWechatRelDto = AccountMerchantWechatRelDto.builder()
                .accountId(accountId)
                .unionid(unionid).build();
        ApiRPCResult<Boolean> apiRPCResult3 = accountMerchantWechatRelApi.insert(accountMerchantWechatRelDto);
        if (apiRPCResult3.isSuccess() && BooleanUtils.isTrue(apiRPCResult3.getData())) {
            try {
                LOGGER.info("wxWorkBusinessApi.updateRemarkAndTagByUnionId, 账号[{}], unionId[{}]", accountId, unionid);
                ApiRPCResult<String> apiRPCResult = wxWorkBusinessApi.updateRemarkAndTagByUnionId(unionid);
                LOGGER.info("wxWorkBusinessApi.updateRemarkAndTagByUnionId, 账号[{}], unionId[{}] 结果[{}]", accountId, unionid, apiRPCResult.getData());
            } catch (Exception e) {
                LOGGER.error("wxWorkBusinessApi.updateRemarkAndTagByUnionId, 异常 账号[{}], unionId[{}]", accountId, unionid, e);
            }
        }
        return BooleanUtils.isTrue(apiRPCResult3.getData()) ? this.addResult("绑定成功") : this.addError("绑定失败");
    }

    /** 获取微信登录二维码链接 */
    @GetMapping("/wechat/getLoginUrl")
    @ResponseBody
    public Object getLoginUrl() {

        ApiRPCResult<String> apiRPCResult = wechatLoginApi.getLoginUrl();
        if (apiRPCResult.isFail()) {
            return this.addError("初始化微信登录二维码失败");
        }
        return this.addResultData(new HashMap<String, String>(){{put("url", apiRPCResult.getData());}});
    }

    /** 获取微信 accessToken */
    @PostMapping("/wechat/accessToken")
    @ResponseBody
    public Object wechatAccessToken(@RequestParam("code") String code) {

        LOGGER.info("当前微信code, {}", code);
        // 换取 access_token
        ApiRPCResult<WechatLoginUserDto> apiRPCResult = wechatLoginApi.getAccessToken(SiteEnum.PC.getValue(), code);
        if (apiRPCResult.isFail()) {
            return this.addError("获取微信令牌失败，请稍后再试");
        }
        return this.addResultData(apiRPCResult.getData());
    }

    /** 微信登录 */
    @PostMapping("/wechat/login")
    @ResponseBody
    public Object wechatLogin(
            @RequestParam("openid") String openid,
            @RequestParam("unionid") String uninid,
            @RequestParam("accessToken") String accessToken,
            @RequestParam("redirectUrl") String redirectUrl,
            @RequestParam("agreements") String agreements,
            @RequestParam("checkTime") String checkTime,
            HttpServletRequest request
    ) {
        // 1、获取微信用户信息
        ApiRPCResult<WechatUserDto> apiRPCResult1 = wechatLoginApi.getUserinfo(accessToken, openid);
        if (apiRPCResult1.isFail()) {
            return this.addError("获取微信用户信息失败，请稍后再试");
        }
        WechatUserDto wechatUserDto = apiRPCResult1.getData();
        String unionId = wechatUserDto.getUnionid();
        // 2、查询 unionId 绑定的账号
        ApiRPCResult<LoginAccountDto> apiRPCResult2 = loginAccountApi.selectAccountByUnionId(unionId);
        LoginAccountDto loginAccountDto = apiRPCResult2.getData();
        if (apiRPCResult2.getData() == null) {
            // 未绑定账号，弹窗进行账号绑定
            return this.addResultData(new HashMap<String, Object>(){{
                put("status", 0);
                put("isRedirect", true);
                put("redirectUrl", "");
            }});
        }
        // 3、进行模拟账密登录
        String loginName = loginAccountDto.getMobile();
        String loginPass = loginAccountDto.getPassword();
        try {
            PasswordVerifier verifier = new PasswordVerifier(loginName, loginPass);
            JwtPrincipal merchant = (JwtPrincipal) xyyIndentityValidator.login(verifier);
            if (switchLoginAgreement){
                try {
                    loginAgreementBussinessRemoteService.insertLoginAgreementLogForLogin(agreements,checkTime,loginName,merchant.getAccountId(), IPUtils.getClientIP(request));
                } catch (Exception e){
                    LOGGER.error("登录日志插入失败，errorMsg", e);
                }
            }
            if (merchant == null) {
                return this.addError("登录失败");
            }
            Map<String, Object> map = loginAccountService.loginBuildDate(merchant.getAccountId());
            Integer shopCount = (Integer) map.get("shopCount");
            Long merchantId = (Long) map.get("merchantId");
            Boolean isAudit = (Boolean) map.get("isAudit");
            Integer status = (Integer) map.get("status");
            if (Objects.equals(shopCount, 1)) {
                // 只关联一家店铺，自动进行店铺登录
                if (BooleanUtils.isFalse(isAudit)) {
                    // 关联状态不为审核通过 or 店铺状态不为审核通过，跳转到选择关联店铺页面
                    return this.addResultData(new HashMap<String, Object>() {{
                        put("status", 1);
                        put("account", loginName);
                        put("password", loginPass);
                        put("isRedirect", true);
                        put("redirectUrl", StrUtil.format("/newstatic/#/register/selectLoginShop{}", StringUtils.isEmpty(redirectUrl) ? "" : "?redirectUrl=" + URLEncoder.encode(redirectUrl, "UTF-8")));
                    }});
                }
                //关联状态审核通过且店铺状态审核通过，跳转到首页
                if (Objects.nonNull(merchantId) && Objects.equals(status, 3)) {
                    // 关联状态审核通过且店铺状态审核通过，跳转到首页时校验药店状态，若是冻结，则给出用户相应的提示
                    boolean result = merchantCancelFreezeLogBusinessApi.findIsCancellation(merchantId);
                    String errorMsg = result ? "您的账号已成功注销" : "该店铺处于冻结状态，请联系客服解冻";
                    LOGGER.info("登录，accountId：{}，merchantId：{}，{}", merchant.getAccountId(), merchantId, result ? "已注销" : "已冻结");
                    return this.addError(errorMsg);
                }
                LOGGER.info("登录，保存登录凭证，loginName:{}, accountId:{}, merchantId:{}, realIP:{}", loginName, merchant.getAccountId(), merchantId, IPUtils.getClientIP(request));
                snowGroundUtil.uploadEventInfoForLoginSuccess(request, merchantId);
                xyyIndentityValidator.setPrincipalMerchant(merchantId, merchant.getAccountId());
                // 跳转到首页 or 缓存页面
                return this.addResultData(new HashMap<String, Object>(){{
                    put("status", 1);
                    put("account", loginName);
                    put("password", loginPass);
                    put("isRedirect", true);
                    put("redirectUrl", StringUtils.isEmpty(redirectUrl) ? "/" : redirectUrl);
                }});
            }
            else if (shopCount < Constants.IS1) {
                // 没有关联店铺，跳转到选择关联店铺页面
                return this.addResultData(new HashMap<String, Object>(){{
                    put("status", 1);
                    put("account", loginName);
                    put("password", loginPass);
                    put("isRedirect", true);
                    put("redirectUrl", "/newstatic/#/register/connectPharmacy");
                }});
            }
            else  {
                // 关联多家店铺，跳转到选择登录店铺页面
                return this.addResultData(new HashMap<String, Object>(){{
                    put("status", 1);
                    put("account", loginName);
                    put("password", loginPass);
                    put("isRedirect", true);
                    put("redirectUrl", StrUtil.format("/newstatic/#/register/selectLoginShop{}", StringUtils.isEmpty(redirectUrl) ? "" : "?redirectUrl=" + URLEncoder.encode(redirectUrl, "UTF-8")));
                }});
            }
        }
        catch (AuthenticationException ae) {
            LOGGER.error("登录出错, ae=", ae);
            return this.addError(ae.getMessage());
        }
        catch (Exception e) {
            LOGGER.error("登录异常, e=", e);
            return this.addError("登录异常，请稍后重试");
        }
    }
}
