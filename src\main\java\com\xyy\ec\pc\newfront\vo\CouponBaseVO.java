package com.xyy.ec.pc.newfront.vo;

import com.xyy.ec.marketing.common.dto.InitiatorDto;
import com.xyy.ms.promotion.business.common.constants.VoucherEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class CouponBaseVO {
    /**
     * 券实例ID
     */
    private Long id;

    /**
     * 券模板ID
     */
    private Long templateId;

    /**
     * 店铺来源，1ec,2店铺
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingChannelTypeEnum
     */
    private Integer shopType;

    /**
     * 区域编码
     */
    private String branchCode;

    /**
     * 人群ID
     */
    private Long customerGroupId;

    /**
     * 店铺编码
     */
    private String shopCode;

    private String shopPatternCode;
    /**
     * 券模板名称
     */
    private String templateName;

    /**
     * 券层级(1:平台  2：店铺)
     */
    private Integer level;

    /**
     * 券模板类型(1-通用券，2-厂家券，3-折扣券，4-礼品券,5-新人券，6-叠加券, 7-平台券，8-跨店券)
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum
     */
    private Integer voucherType;

    /**
     * 跨店券枚举
     *
     * @see com.xyy.ec.marketing.client.common.enums.MakeFromEnum
     */
    private Integer makeFrom;

    /**
     * 券模板类型(1-通用券，2-厂家券，3-折扣券，4-礼品券,5-新人券，6-叠加券)说明
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherTypeEnum
     */
    private String voucherTypeDesc;

    /**
     * 券实例状态（1-未领取，2-已领取，3-已用完，4-失效，5-已刪除）te
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.MerchantVoucherStatusEnum
     */
    private Integer state;

    /**
     * 券的状态
     */
    private Integer couponState;

    /**
     * 折扣类型 默认0。 1为折扣类型。
     * PS：优惠额度为折扣额度时，此为1。此字段根据discount判定得出。
     */
    private Integer voucherState = 0;

    /**
     * 券面文案
     */
    private String voucherTitle;

    /**
     * 券包含金额
     */
    private BigDecimal moneyInVoucher;

    /**
     * 起用券最低消费金额
     */
    private BigDecimal minMoneyToEnable;

    /**
     * 优惠券最高抵扣金额
     */
    private BigDecimal maxMoneyInVoucher;

    /**
     * 生效时间
     */
    private Date validDate;
    /**
     * 失效时间
     */
    private Date expireDate;

    /**
     * 1:全部商品参与 2:指定商品参与 3:指定商品不参与
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.RelationTypeEnum
     */
    private Integer skuRelationType;

    /**
     * 商品券 相关商品ID
     */
    private List<Long> assignProductIds;

    /**
     * 商品券 相关店铺Code
     */
    private List<String> assignShopCodes;

    /**
     * 券使用方式 0满，1每满，2阶梯
     *
     * @see com.xyy.ec.marketing.hyperspace.api.common.constants.VoucherEnum.VoucherReduceTypeEnum
     */
    private Integer voucherUsageWay;
    /**
     * pop活动是否展示
     */
    private Integer display;

    /**
     * 折扣额度
     */
    private BigDecimal discount;
    private String discountRatio = "";

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 描述
     */
    private String voucherInstructions;

    /**
     * 使用说明对应数据库的voucher_instructions字段
     */
    private String useVoucherInstructions;

    /**
     * 排序
     */
    private Integer sortNo;

    /**
     * 创建人。
     * 仅当平台券时有效有值。
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后修改人。
     * 仅当平台券时有效有值。
     */
    private String updator;
    /**
     * 最后修改时间
     */
    private Date updateTime;

    //计算 开始 --------------------
    /**
     * 是否可用
     */
    private boolean isAvailable;

    /**
     * 未达到门槛，距离门槛金额
     */
    private BigDecimal ladderDiff;

    /**
     * 达到门槛，已优惠多少钱，包含阶梯，总优惠
     */
    private BigDecimal ladderDiscount;

    /**
     * 已购券中商品金额
     */
    private BigDecimal buyAmount;

    /**
     * 达到门槛，差距下一阶梯还差多少
     */
    private BigDecimal nextLadderDiff;

    /**
     * 达到门槛,满足下一阶梯可优惠多少
     */
    private BigDecimal nextLadderDiscount;

    /**
     * 实际优惠金额
     */
    private BigDecimal realTotalDiscount;

    //-------------------- 结束 计算

    /**
     * 是否被选中,true选中，false不选中
     */
    private Boolean isSelected;

    /**
     * 是否可以被选择
     */
    private Boolean canSelected;

    private Long manufacturerId;

    /**
     * app跳转url
     */
    private String appUrl;

    /**
     * 11.4 app跳转url
     */
    private String appDesignateShopUrl;
    /**
     * 11.9.5 app跳转url(跨店、店铺、商品)
     */
    private String appSupplementOrderUrl;
    /**
     * 是否指定店铺
     */
    private Boolean isDesignateShop = Boolean.FALSE;

    /**
     * pc跳转url
     */
    private String pcUrl;

    /**
     * 券展示图片
     */
    private String describeUrl;


    /**
     * 券最低使用描述
     */
    private String minMoneyToEnableDesc;

    /**
     * 优惠券最高抵扣金额描述
     */
    private String maxMoneyInVoucherDesc;

    /**
     * 仅当店铺券时有效有值。店铺优惠券类型，MarketingShopTypeEnum，1, "EC店铺"  2, "POP店铺"
     */
    private Integer shopCouponType;

    private Set<Integer> ownerBizCodes;

    /**
     * 多个发起方
     */
    private String initiatorJson;

    /**
     *多个发起方
     */
    private List<InitiatorDto> initiators;

    /**
     * 有效时间（单位：天）
     */
    private Integer validDays;

    /**
     * 有效期类型：1-有效天数，2-有效周期
     *
     * @see VoucherEnum.VoucherValidityTypeEnum
     */
    private Integer validityType;

    /**
     * 1:不限定，2：限定平台新人可用
     */
    private Integer platformNew;

    /**
     * 发行数量
     */
    private Integer totalLimitQty;

    /**
     * 11.9.14 九章用
     */
    private String tips;
    /**
     * 是否达到最高减
     * 11.9.14 九章
     */
    private Boolean maxLadderReached;
    /**
     * 是否是绑定商品组的优惠券
     */
    private Boolean exhibitionGroupVoucher;
    /**
     * 商品组id
     */
    private List<Long> exhibitionGroupIds;

    /**
     * 是否支持自营入仓商品
     * 0-不支持  1-支持
     */
    private Integer isSupportSelfFbpSku;

    private Integer receiveType;

    private Integer isShowCenter;

    /**
     * 是否为测试券：0-否(默认)，1-是
     */
    private Integer isTestVoucher;
}
