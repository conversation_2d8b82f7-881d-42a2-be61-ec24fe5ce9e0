package com.xyy.ec.api.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.api.rpc.gdd.GongDuoDuoRpcService;
import com.xyy.ec.api.rpc.product.ProductRpcService;
import com.xyy.ec.api.service.pager.RAMPager;
import com.xyy.ec.product.business.ecp.csu.dto.CsuQueryDTO;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.shop.server.business.params.ShopProductQueryParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/21 20:40
 * @Description:
 */
@Slf4j
@Service
public class IndustryService {

    private static final int SOLD_OUT_STATUS = 2;//已售罄
    @Autowired
    ProductService productService;
    @Autowired
    ProductRpcService productRpcService;
    @Autowired
    GongDuoDuoRpcService gongDuoDuoRpcService;

    public PageInfo<ProductDTO> pagingShopProducts(ShopProductQueryParam param , Integer realPageNum, Integer realPageSize,List<String> goodsCodeList) {
        CsuQueryDTO csuQueryDTO = new CsuQueryDTO();
        csuQueryDTO.setBranchCode(param.getBranchCode());
        csuQueryDTO.setIsSelfSupport(true);
        csuQueryDTO.setBarCodes(goodsCodeList);
        List<Long> shopCsuIds = productRpcService.findCsuIdListByParams(csuQueryDTO);
        if (CollectionUtils.isEmpty(shopCsuIds)){
            return new PageInfo<>();
        }
        //优化控销逻辑
        shopCsuIds =  productService.controlFilterCsuIdsForIsNotPurchase(param.getMerchantId(), param.getBranchCode(), shopCsuIds);

        //分页处理
        RAMPager<Long> pager = new RAMPager<>(shopCsuIds,realPageSize);
        List<Long> csuIdsForPage = pager.page(realPageNum);

        //查询商品信息
        List<ProductDTO> productDTOS = productService.fillProductInfo(csuIdsForPage, param.getMerchantId(), param.getBranchCode());
        /**
         * 1、已售罄置底
         * 2、ID升序排序
         */
        List<ProductDTO> realProducts = productDTOS.stream()
                .sorted(Comparator.comparing(ProductDTO::getStatus,(x, y)->{
                    if (y.equals(SOLD_OUT_STATUS)) return -1;
                    return x.compareTo(y);
                }).thenComparing(ProductDTO::getStatus))
                .collect(Collectors.toList());

        PageInfo<ProductDTO> pageInfo = new PageInfo(realProducts);
        pageInfo.setTotal(pager.getTotal());
        pageInfo.setPages(pager.getPageCount());
        pageInfo.setPageNum(realPageNum);
        pageInfo.setPageSize(realPageSize);
        return pageInfo;
    }
}
