package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.layout.buinese.dto.CmsDialogDto;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.service.FrameManagerNewService;
import com.xyy.ec.pc.rest.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@CustomizeCmsResponse
@RestController
@RequestMapping("/new-front/layout/frame")
public class FrameManagerNewController {

    @Resource
    private FrameManagerNewService frameManagerNewService;

    /**
     * cms弹窗
     */
    @GetMapping("/new-cms-dialog")
    public AjaxResult<CmsDialogDto> getNewCmsDialog(Integer sceneType) {
        try {
            return frameManagerNewService.getNewCmsDialog(sceneType);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }
}
