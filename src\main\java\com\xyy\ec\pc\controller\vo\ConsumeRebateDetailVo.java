package com.xyy.ec.pc.controller.vo;

import com.xyy.ec.marketing.hyperspace.api.common.enums.MarketingRebateConsumptionReturnLevelSceneEnum;
import com.xyy.ec.marketing.hyperspace.api.dto.activityRebate.MarketingRebateConsumptionReturnAmountDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ConsumeRebateDetailVo {
    /**
     * 距离结束时间的毫秒数
     */
    private long toEtimeSeconds;

    /**
     * 根据上月消费金额计算出的预计红包金额
     */
    private BigDecimal lastMonthExpectedAmount;

    /**
     * 下个阶梯比例
     */
    private BigDecimal nextLevelRate;

    /**
     * 下个阶梯剩余金额
     */
    private BigDecimal nextLevelShortAmount;

    /**
     * 下个阶梯返红包金额
     */
    private BigDecimal nextLevelRedPacketAmount;
    /**
     * 实际可返红包金额
     */
    private BigDecimal realAmount;

    /**
     * 最高返红包金额
     */
    private BigDecimal maxReturnRedPackageAmount;
    /**
     * 活动配置
     */
    private List<MarketingRebateConsumptionReturnAmountDTO> actResultList;

    /**
     * 场景
     * @see MarketingRebateConsumptionReturnLevelSceneEnum
     */
    private Integer levelScene;
}
