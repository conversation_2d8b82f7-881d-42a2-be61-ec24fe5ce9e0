package com.xyy.ec.pc.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public enum ElectronicPlanOrderSkuColumnEnum {


	CODE("69码","code"),

	PRODUCT_NAME("商品名称","productName"),
	SPEC("规格","spec"),
	MANUFACTURER("厂家","manufacturer"),
	REGISTER_NUM("登记数量","registerNum");
	
	
    private String key;
    private String value;
    

    ElectronicPlanOrderSkuColumnEnum(String key, String value){
        this.key = key;
        this.value = value;
    }
    
    private static Map<String, ElectronicPlanOrderSkuColumnEnum> eMaps = new HashMap<>();
    private static Map<String, String> valueMaps = new HashMap<>();
    public static Map<String,String> maps = new HashMap<>();
    static {
        for(ElectronicPlanOrderSkuColumnEnum e : ElectronicPlanOrderSkuColumnEnum.values()) {
        	eMaps.put(e.getKey(), e);
            maps.put(e.getKey(),e.getValue());
            valueMaps.put(e.getValue(), e.getKey());
        }
    }

    public static String get(String key) {
        return eMaps.get(key).getValue();
    }

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}



}
