package com.xyy.ec.pc.newfront.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.pc.newfront.service.ImDialogService;
import com.xyy.ec.pc.newfront.service.MessageCenterService;
import com.xyy.ec.system.business.api.MessageCenterBusinessApi;
import com.xyy.ec.system.business.dto.MessageCenterBusinessDto;
import com.xyy.ec.system.business.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class MessageCenterServiceImpl implements MessageCenterService {

    @Reference(version = "1.0.0")
    private MessageCenterBusinessApi messageCenterBusinessApi;

    @Resource
    private ImDialogService imDialogService;

    @Override
    public Integer findUnReadCount(MessageCenterBusinessDto messageCenter) {
        int unReadCount = 0;
        if (messageCenter != null && messageCenter.getMerchantId() != null && messageCenter.getMerchantId() != 0L) {
            messageCenter.setState(1);//未读
            messageCenter.setCustomerType(1);
            // 系统消息未读数
            unReadCount = messageCenterBusinessApi.selectCount(messageCenter);

            // 查自营会话未读消息
            MessageCenterBusinessDto businessDto = imDialogService.findLatestDialogByUid(messageCenter.getMerchantId());
            if (businessDto != null) {
                unReadCount += businessDto.getUnRead();
            }

            // 查询第三方会话未读消息
            messageCenter.setMessageType(MessageTypeEnum.DIALOG_MESSAGE.getCode());
            PageInfo<MessageCenterBusinessDto> pageInfo = messageCenterBusinessApi.searchMessageInfo(messageCenter, 0, 10);
            if (pageInfo == null) {
                return unReadCount;
            }
            Long total = pageInfo.getTotal();
            pageInfo = messageCenterBusinessApi.searchMessageInfo(messageCenter, 0, total.intValue());
            if (pageInfo == null) {
                return unReadCount;
            }
            List<MessageCenterBusinessDto> businessDtoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(businessDtoList)) {
                unReadCount += businessDtoList.stream()
                        .filter(dto -> Objects.nonNull(dto) && dto.getUnRead() > 0)
                        .mapToLong(MessageCenterBusinessDto::getUnRead).sum();
            }

        }
        return unReadCount;
    }
}
