<!DOCTYPE HTML>
<html>
<head>
    <#include "/common/common.ftl" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description"
          content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
    <meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
    <title>我的订单</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="/static/css/user.css?t=${t_v}"/>
    <script type="text/javascript" src="/static/js/order/index.js?t=${t_v}"></script>
    <link rel="stylesheet" type="text/css" href="/static/css/common.css?t=${t_v}"/>
    <script src="/static/js/jquery.fileDownload.js"></script>
    <script type="text/javascript" src="/static/js/download/jszip.min.js"></script>
    <script type="text/javascript" src="/static/js/download/FileSaver.min.js"></script>
    <script type="text/javascript" src="/static/js/setToast/toast.script.js"></script>
    <script type="text/javascript" src="/static/js/download/xlsx.full.min.js"></script>
    <script type="text/javascript" src="/static/js/qtShopDetail.js?t=${t_v}"></script>
    <script type="text/javascript">
        var ctx = "";
    </script>
    <style>
        .order-tab-title a {
            display: inline-block;
            height: 57px;
            color: #333333;
        }

        .order-tab-title .origan {
            color: #f39801;
            font-weight: 600;
            font-style: normal;
        }

        .order-tab-title .order-tab-title-item-cur a {
            color: #00C675;
            border-bottom: 2px solid #00C675;
        }

        .checkbox-pretty {
            top: -2px;
        }

        /*退款原因**/
        .reason-box {
            border: 1px solid #ddd;
            margin-top: 10px;
            color: #666;
            font-size: 14px;
            height: 28px;
            width: 180px;
            outline: none;
        }

        .reason-tip {
            color: #f00;
            font-size: 12px;
            position: relative;
            top: 4px;
            left: 10px;
            display: none;
        }

        .table-item-row-aftersales-line {
            display: flex;
            flex-direction: row;
        }

        .table-item-row-aftersales-img {
            display: flex;
            flex-direction: column;
            width: 140px;
            margin-bottom: 10px;
        }

        .table-item-row-aftersales-img-bottomtext {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            letter-spacing: 0.33px;
            text-align: center;
        }

        .table-item-row-aftersales-row2 {
            margin-top: 30px;
            width: 250px;
            font-weight: 400;
            font-size: 12px !important;
            color: #999999;
            letter-spacing: 0.33px;
            text-align: left;
        }

        .table-item-row-aftersales-row2-text {
            font-size: 12px;
            color: #999999;
        }

        .table-item-row-aftersales-row3 {
            margin-top: 40px;
            width: 240px;
        }

        .table-item-row-aftersales-row3-text {
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            text-align: center;
        }

        .table-item-row-aftersales-row3-content {
            display: flex;
            flex-direction: row;
            margin-top: 10px;
            justify-content: center;
        }

        .table-item-row-aftersales-row3-content-time-hint {
            width: 72px;
            height: 21px;
            background-image: linear-gradient(90deg, #FC7148 0%, #F62626 100%);
            border-radius: 2px 0 0 2px;
            font-weight: 400;
            font-size: 12px;
            color: #FFFFFF;
            letter-spacing: 0;
            text-align: center;
            padding-top: 3px;
        }

        .table-item-row-aftersales-row3-content-time {
            width: 123px;
            height: 19px;
            background: #FFF7F7;
            border: 1px solid #F62626;
            border-radius: 2px 0 0 2px;
            font-weight: 400;
            font-size: 12px;
            color: #F62827;
            letter-spacing: 0;
            text-align: center;
            padding-top: 3px;
        }

        .table-item-row-aftersales-row4 {
            padding: 0 20px;
            width: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .table-item-row-aftersales-row4-text {
            font-weight: 400;
            font-size: 12px;
            text-align: left;
            margin-left: 20px;
        }

        .table-item-row-aftersales-row5 {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .table-item-row-aftersales-row5-btn {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
        }

        .table-item-row-aftersales-row5-btn:hover {
            color: #00C675;
        }

        .top-button-mk {
            padding: 5px 20px;
            color: #fff;
            width: 100px;
            height: 30px;
            background: #00dc82;
            text-align: center;
            line-height: 30px;
            margin-left: 10px;
        }

        .top-button-mk-reset {
            padding: 5px 20px;
            width: 100px;
            height: 30px;
            text-align: center;
            line-height: 30px;
            margin-left: 10px;
            border: 1px solid #DDDDDD;
            border-radius: 2px;
            color: #666666;
        }

        .table-header-buttons {
            width: 100%;
            height: 44px;
            background: #F4F4F4;
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .table-header-buttons-items {
            height: 24px;
            padding: 0 20px;
            border-radius: 4px;
            margin-right: 20px;
            justify-content: center;
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .table-header-buttons-items-selected {
            color: white;
            background: #00B377;
        }

        .table-header-buttons-items-noselected {
            color: #666666;
            background: #FFFFFF;
        }

        .table-item-row-aftersales-row2-nocl {
            padding: 20px 20px;
        }

        .table-item-row-aftersales-nocl-text {
            width: 615px;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 12px;
            color: #FF6E02;
        }
        .newBuyTitle{
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
        }
        .product-card-top {
            display: flex;
            border-top: 1px solid #ccc;
            border-bottom: 1px solid #ccc;
            padding: 16px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .product-card-bottom {
            display: flex;
            border-bottom: 1px solid #ccc;
            padding: 16px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .product-image {
            width:70px;
            height:70px;
            border-radius: 8px;
        }
        .shixiao-image {
            width: 50px;
            height: 50px;
            top: 15%;
            left: 15%;
            position: absolute;
        }
        .product-name {
            font-family: PingFangSC-Medium;
            font-weight: 500;
            font-size: 14px;
            color: #777777;
            letter-spacing: 0;
            line-height: 14px;
        }
        .product-price {
            color: #777777;
            font-size: 14px;
            font-face: PingFangSC;
            font-weight: 500;
            line-height: 14px;
            letter-spacing: 0;
            text-align: left;
            margin: 0px 15px 0 0;
        }
        .img-boder{
            position: relative;
            width: 70px;
            height: 70px;
            margin-right: 10px;
            border: 1px solid #ccc;
        }
        .product-origin-price {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #777777;
        }
        .product-unit {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #777777;
        }
        .product-effect {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #777777;
        }
    </style>
</head>

<body>
<div class="container">

    <!--头部导航区域开始-->
    <div class="headerBox" id="headerBox">
        <#include "/common/header.ftl" />
    </div>
    <!--头部导航区域结束-->

    <!--主体部分开始-->
    <div class="main">
        <div class="myorder row" style="width:1360px;">
            <!--面包屑-->
            <ul class="sui-breadcrumb">
                <li><a href="/">首页</a>></li>
                <li><a href="/merchant/center/index.htm">用户中心</a>></li>
                <li class="active">我的订单</li>
            </ul>
            <div class="myqual-content clear">
                <div class="side-left fl">
                    <#include "/common/merchantCenter/left.ftl" />
                </div>
                <div class="main-right fr" style="width:1138px;">
                    <!--资质状态提示信息-->
                    <div class="sui-msg msg-large msg-tips" id="licenseMsg"
                         style="display:none;font-size: 14px;background:#fff3ce;padding:11px 20px;color: #ff8e29;font-weight:400;">
                        <span class="main-msg" style="margin-right: 20px;"></span><a id="updateLicense"
                                                                                     href="/merchant/center/licenseAudit/findLicenseCategoryInfo.htm"
                                                                                     style="color:#ff8e29;">点击去更新资质&nbsp;></a>
                    </div>
                    <div style="background:#ffffff;">
                        <div class="order-tab-title" id="order_status_menu"
                             style="margin-top:0;height:59px;border-bottom:1px solid #efefef;line-height:59px;width:1138px;">
									 <span <#if paramOrder.status??> class="order-tab-title-item" <#else>  class="order-tab-title-item order-tab-title-item-cur" </#if>>
										 <a href="javascript:;">全部订单</a>
									 </span>
                            <#--<a href="javascript:;"  <#if paramOrder.status??> class="order-tab-title-item" <#else>  class="order-tab-title-item order-tab-title-item-cur" </#if> >-->
                            <#--&lt;#&ndash;<span class="order-icon order-icon1"></span>&ndash;&gt;-->
                            <#--&lt;#&ndash;<span class="order-tab-title-name">全部订单</span>&ndash;&gt;-->
                            <#--全部订单-->
                            <#--</a>-->
                            <span status="10" <#if paramOrder.status==10 > class="order-tab-title-item order-tab-title-item-cur" <#else>  class="order-tab-title-item" </#if>>
										<a href="javascript:;">待付款(<i class="origan">${waitPayNum }</i>)</a>
									</span>
                            <#--<a  href="javascript:;" status="10" <#if paramOrder.status==10 > class="order-tab-title-item order-tab-title-item-cur" <#else>  class="order-tab-title-item" </#if> >-->
                            <#--&lt;#&ndash;<span class="order-icon order-icon2"></span>&ndash;&gt;-->
                            <#--<span class="order-tab-title-name">待付款(<i class="origan">${waitPayNum }</i>)</span>-->
                            <#--</a>-->

                            <span status="1" <#if paramOrder.status==1 > class="order-tab-title-item order-tab-title-item-cur" <#else>  class="order-tab-title-item" </#if>>
										<a href="javascript:;">待配送(<i class="origan">${waitShippingNum }</i>)</a>
									 </span>
                            <#--<a  href="javascript:;" status="1" <#if paramOrder.status==1 > class="order-tab-title-item order-tab-title-item-cur" <#else>  class="order-tab-title-item" </#if> >-->
                            <#--&lt;#&ndash;<span class="order-icon order-icon3"></span>&ndash;&gt;-->
                            <#--<span class="order-tab-title-name">待配送(<i class="origan">${waitShippingNum }</i>)</span>-->
                            <#--</a>-->

                            <span status="2" <#if paramOrder.status==2 > class="order-tab-title-item order-tab-title-item-cur" <#else>  class="order-tab-title-item" </#if>>
										<a href="javascript:;">待收货(<i class="origan">${shippingNum }</i>)</a>
									 </span>
                            <#--<a   href="javascript:;" status="2" <#if paramOrder.status==2 > class="order-tab-title-item order-tab-title-item-cur" <#else> class="order-tab-title-item" </#if> >-->
                            <#--&lt;#&ndash;<span class="order-icon order-icon4"></span>&ndash;&gt;-->
                            <#--<span class="order-tab-title-name">待收货(<i class="origan">${shippingNum }</i>)</span>-->
                            <#--</a>-->

                            <span status="90" <#if paramOrder.status==90 > class="order-tab-title-item order-tab-title-item-cur" <#else>  class="order-tab-title-item" </#if>>
										<a href="javascript:;">退款(<i class="origan">${refundNum }</i>)</a>
									 </span>
                            <#--<a  href="javascript:;" status="90"  <#if paramOrder.status==90 > class="order-tab-title-item order-tab-title-item-cur" <#else> class="order-tab-title-item" </#if> >-->
                            <#--&lt;#&ndash;<span class="order-icon order-icon5"></span>&ndash;&gt;-->
                            <#--<span class="order-tab-title-name">售后/退款(<i class="origan">${refundNum }</i>)</span>-->
                            <#--</a>-->

                            <span status="89" <#if paramOrder.status==89 > class="order-tab-title-item order-tab-title-item-cur" <#else>  class="order-tab-title-item" </#if>>
										<a href="javascript:;">售后(<i class="origan">${afterSalesCount }</i>)</a>
									 </span>


                            <span status="3" <#if paramOrder.status==3 > class="order-tab-title-item order-tab-title-item-cur" <#else>  class="order-tab-title-item" </#if>>
										<a href="javascript:;">交易完成(<i class="origan">${compareNum }</i>)</a>
									 </span>
                            <#--<a  href="javascript:;" status="3" <#if paramOrder.status==3> class="order-tab-title-item order-tab-title-item-cur" <#else> class="order-tab-title-item" </#if> >-->
                            <#--&lt;#&ndash;<span class="order-icon order-icon6"></span>&ndash;&gt;-->
                            <#--<span class="order-tab-title-name">交易完成(<i class="origan">${compareNum }</i>)</span>-->
                            <#--</a>-->
                        </div>
                        <div class="myneworder-search clear"
                             style="margin-top:13px;padding-left:15px;padding-bottom:17px;">
                            <#if paramOrder.status!=89 >
                                <div class="fl">
                                    <label>订单编号</label>
                                    <input class="inp-num" type="text" id="orderNo" placeholder="输入订单编号"
                                           value="${paramOrder.orderNo }">

                                    <label class="padding-left">订单来源</label>
                                    <select class="inp-sel" id="orderSource">
                                        <option value="" selected="selected">全部</option>
                                        <option value="-1" <#if paramOrder.orderSource=-1>selected="selected"</#if>>
                                            手机端
                                        </option>
                                        <option value="4" <#if paramOrder.orderSource=4>selected="selected"</#if>>PC端
                                        </option>
                                    </select>
                                </div>
                                <div class="sui-form form-horizontal" style="margin:0 0 5px;">
                                    <div data-toggle="datepicker" class="control-group input-daterange">
                                        <label class="padding-left">下单时间</label>
                                        <div class="controls">
                                            <input type="text" id="startCreateTime" class="input-medium input-date"
                                                   <#if paramOrder.startCreateTime??>value="${paramOrder.startCreateTime?string('yyyy-MM-dd') }"</#if>><span> 至 </span>
                                            <input type="text" id="endCreateTime" class="input-medium input-date"
                                                   <#if paramOrder.endCreateTime??>value="${paramOrder.endCreateTime?string('yyyy-MM-dd') }"</#if>>

                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label>药品名称</label>
                                    <input class="inp-num" type="text" id="drugName" placeholder="输入药品名称"
                                           value="${paramOrder.name }">

                                    <div style="display: inline-block" class="searchShopBox">
                                        <label class="padding-left">店铺名称</label>
                                        <input class="inp-num" style="width: 200px;" type="text" onblur="clearOrderId()"
                                               orgid="${paramOrder.orgId}" id="searchShopBox" placeholder="输入店铺名称"
                                               value="${paramOrder.companyName }">
                                        <!--搜索下拉弹窗-->
                                        <ul class="searchShopUl"
                                            style="max-height:300px;overflow-y:scroll;scrollbar-width:none;"
                                            id="searchShopUl"></ul>
                                    </div>

                                    <label class="padding-left">有无电子发票</label>
                                    <select class="inp-sel" id="invoiceState">
                                        <option value="" selected="selected">全部订单</option>
                                        <option value="1" <#if paramOrder.invoiceState=1>selected="selected"</#if>>
                                            有电子发票
                                        </option>
                                    </select>
                                    <#--<label class="padding-left">渠 道</label>-->
                                    <#--<select class="inp-sel"  id="channelCode">-->
                                    <#--<option value="" selected="selected">全部</option>-->
                                    <#--<option value="1" <#if paramOrder.channelCode??&&paramOrder.channelCode=="1">selected="selected"</#if>>药帮忙</option>-->
                                    <#--<option value="2" <#if paramOrder.channelCode??&&paramOrder.channelCode=="2">selected="selected"</#if>>宜块钱</option>-->
                                    <#--</select>-->
                                </div>
                                <div style="margin-top: 10px; clear: both;">
                                    <a href="#" class="query-btn fl">查询</a>
                                    <a href="#" class="export-btn fl">订单导出</a>
                                    <a href="#" class="detail-export-btn fl">订单明细导出</a>
                                    <a href="#" class="invoice-btn fl">电子发票下载</a>
                                </div>
                            <#else>
                                <div class="fl">
                                    <label>订单编号</label>
                                    <input class="inp-num" type="text" id="orderNo" placeholder="输入订单编号"
                                           value="${paramOrder.orderNo }">

                                    <div style="display: inline-block" class="searchShopBox">
                                        <label class="padding-left">店铺名称</label>
                                        <input class="inp-num" style="width: 200px;" type="text" onblur="clearOrderId()"
                                               orgid="${paramOrder.orgId}" id="searchShopBox" placeholder="输入店铺名称"
                                               value="${paramOrder.companyName }">
                                        <!--搜索下拉弹窗-->
                                        <ul class="searchShopUl"
                                            style="max-height:300px;overflow-y:scroll;scrollbar-width:none;"
                                            id="searchShopUl"></ul>
                                    </div>

                                    <a href="#" class="top-button-mk">查询</a>
                                    <a href="#" class="top-button-mk-reset">重置</a>
                                </div>
                            </#if>
                        </div>
                    </div>

                    <#if paramOrder.status==89>
                        <div class="table-header-buttons">

                            <a id="table-header-buttons-one" href="#"
                               class="table-header-buttons-items <#if afterSalesProcessState??&&afterSalesProcessState == -1>table-header-buttons-items-selected<#else>table-header-buttons-items-noselected</#if>"
                               onclick="headerButtonClick('')">全部</a>
                            <a id="table-header-buttons-two" href="#"
                               class="table-header-buttons-items <#if afterSalesProcessState??&&afterSalesProcessState == 6>table-header-buttons-items-selected<#else>table-header-buttons-items-noselected</#if>"
                               onclick="headerButtonClick(6)">待客户确认</a>
                            <a id="table-header-buttons-three" href="#"
                               class="table-header-buttons-items <#if afterSalesProcessState??&&afterSalesProcessState == 0>table-header-buttons-items-selected<#else>table-header-buttons-items-noselected</#if>"
                               onclick="headerButtonClick(0)">处理中</a>
                            <a id="table-header-buttons-four" href="#"
                               class="table-header-buttons-items <#if afterSalesProcessState??&&afterSalesProcessState == 1>table-header-buttons-items-selected<#else>table-header-buttons-items-noselected</#if>"
                               onclick="headerButtonClick(1)">已完成</a>
                        </div>
                    </#if>
                    <input type="hidden" id="productImageUrl" value="${productImageUrl}" />



                    <#--<div class="myneworder-search clear" style="text-align:right;">-->
                    <#--<a href="#" class="query-btn fl">查询</a>-->
                    <#--<a href="#" class="export-btn fl">订单导出</a>-->
                    <#--</div>-->

                    <#if (notice)?length gt 0>
                        <div class="sui-msg msg-notice orderwait">
                            <div class="msg-con">${notice}</div>
                            <s class="msg-icon"></s>
                        </div>
                    </#if>


                    <div class="tab-content myorder-tab-content" style="width:1138px;">
                        <#if pager.rows??&&(pager.rows?size>0)>
                        <div class="tab-cont" style="display: block;">
                            <#if paramOrder.status!=89>
                                <div class="table-head clear" style="height:40px;line-height: 50px;margin-bottom: 0;width:1138px;">
								    	<span class="table-head-row table-head-row1">
								    		<label class="checkbox-pretty check-all-btn inline">
                                                <input type="checkbox"><span>全选</span>
                                            </label>
								    	</span>
                                    <span class="table-head-row table-head-row2" style="width:420px;">订单号</span>
                                    <#--<span class="table-head-row table-head-row6">订单渠道</span>-->
                                    <span class="table-head-row table-head-row3">订单状态</span>
                                    <span class="table-head-row table-head-row4">金额</span>
                                    <span class="table-head-row table-head-row5">操作</span>
                                </div>
                            </#if>

                            <tbody>
                            <div class="table-body">
                                <#list pager.rows as order>
                                    <#if paramOrder.status==89>
    
                                        <div class="table-item" style="width:1138px;">
                                            <div class="cgd-qy" style="justify-content:space-between;">
                                                <div style="flex: 1">
                                                    <#if order.isParent == 0>
                                                        <#if order.isThirdCompany ==0>
                                                            <span class="ziying">自营</span>
                                                        <#else>
                                                            <img src="/static/images/pop-shop-icon.png" alt=""
                                                                style="margin-right: 0px;top: -1px;position: relative;width: 18px;">
                                                        </#if>
                                                        <span class="qy-title">
                                                            <#if order.isThirdCompany ==1>
                                                                <a target="_blank"
                                                                    href="/company/center/companyInfo/shopIndex.htm?orgId=${order.orgId}"
                                                                    style="color: #333333;">${order.origName}</a>
                                                            <#else>
                                                            <#--  /selfShop/center/${order.shopCode}.htm  -->
                                                                <a target="_blank" href="/selfShop/center/${order.shopCode}.htm"
                                                                    style="color: #333333;">${order.origName}</a>
                                                            </#if>
                                                        </span>
                                                        <#if order.isThirdCompany == 0>
                                                            <#if order.isVirtualSupplier?? && order.isVirtualSupplier !='1'>
                                                                <a href="javaScript:callKf('${order.orderNo}','${merchant.id}');">
                                                                    <img src="/static/images/order/im-icon.png" width="20"
                                                                        height="20">
                                                                </a>
                                                            </#if>
                                                        <#else>
                                                            <!--pop-->
                                                            <#if order.isVirtualSupplier?? && order.isVirtualSupplier !='1'>
                                                                <a href="javaScript:callKf('${order.orderNo}','${merchant.id}','${order.orgId}','${order.isThirdCompany}','${order.origName}');"
                                                                style="margin-left: 8px;font-size:12px; color: #00dc82;">
                                                                    <#-- ./我的订单_files/customer.png 这个是前面的那个客服图标  -->
                                                                    <img src="./我的订单_files/customer.png" alt=""
                                                                        style="width:16px;height:16px;">联系商家
                                                                </a>
                                                            </#if>
                                                        </#if>
                                                    <#else>
                                                        <span>药帮忙</span>    
                                                    </#if>
                                                </div>
												<#if order.status==10>
													<div id="${order.orderNo}" class="xLastPayTime" style="color:red;" data-source='{"id":"${order.orderNo}", "end":"${order.payExpireTime?string("yyyy-MM-dd HH:mm:ss")}"}'>
													</div>
												</#if>
                                            </div>   

                                            <#--  这里需要加判断看看是用哪个  -->
                                            <#--  这里是售后等待商家处理  -->
                                            <div class="table-item-body clear table-item-row-aftersales-line"
                                                 style="background: #fff;">

                                                <div class="table-item-row-aftersales-row2-nocl">
                                                    <div class="table-item-row-aftersales-row2-text">订单编号：<a
                                                                href="/merchant/center/order//queryDetail?orderNo=${order.orderNo}"
                                                                style="color: #00C675;">${order.orderNo}</a></div>
                                                    <div class="table-item-row-aftersales-row2-text">
                                                        下单时间：${order.createTime?string('yyyy-MM-dd HH:mm:ss') }</div>
                                                    <div class="table-item-row-aftersales-row2-text">
                                                        售后类型：${order.afterSalesTypeName}</div>
                                                </div>
                                                <div class="table-item-row-aftersales-nocl-text">
                                                    ${order.statusName}
                                                </div>
                                                <div class="table-item-row-aftersales-row5">
                                                    <a href="/app/afterSales/index?orderNo=${order.orderNo}&afterSalesNo=${order.afterSalesNo}&url=invoiceServiceDetails"
                                                       class="table-item-row-aftersales-row5-btn">查看详情 ></a>
                                                </div>
                                            </div>

                                            <#--  这里是售后待客户退回货物  -->
                                            <#--									<div class="table-item-body clear table-item-row-aftersales-line" style="background: #fff;">-->
                                            <#--										<div class="table-item-row-aftersales-img">-->
                                            <#--											<a href="/merchant/center/order/detail/3.htm" class="table-body-row table-body-row1">-->
                                            <#--												<img src="http://upload.test.ybm100.com/ybm/product/min/51078433-c412-4778-b7a1-19680a315a5b.jpg"></a>-->
                                            <#--											<span class="table-item-row-aftersales-img-bottomtext">订单内共有 1 件商品</span>-->
                                            <#--										</div>-->

                                            <#--										<div class="table-item-row-aftersales-row2">-->
                                            <#--											<div class="table-item-row-aftersales-row2-text">订单编号：xxx</div>-->
                                            <#--											<div class="table-item-row-aftersales-row2-text">下单时间：xxx</div>-->
                                            <#--											<div class="table-item-row-aftersales-row2-text">订单内共有 2 件商品</div>-->
                                            <#--										</div>-->

                                            <#--										<div class="table-item-row-aftersales-row3">-->
                                            <#--											<div class="table-item-row-aftersales-row3-text">待客户退回货物</div>-->
                                            <#--											<div class="table-item-row-aftersales-row3-content">-->
                                            <#--												<div class="table-item-row-aftersales-row3-content-time-hint">剩余时间</div>-->
                                            <#--												<div class="table-item-row-aftersales-row3-content-time">03天11小时45分钟</div>-->
                                            <#--											</div>-->

                                            <#--											<div class="table-item-row-aftersales-row4">-->
                                            <#--												<div class="table-item-row-aftersales-row4-text">-->
                                            <#--													<span style="font-weight: 400;font-size: 12px;color: #999999;">退款金额:</span>-->
                                            <#--													<span style="font-weight: 500;font-size: 12px;color: #FF0000;">￥221.80</span>-->
                                            <#--												</div>-->
                                            <#--												<div class="table-item-row-aftersales-row4-text">-->
                                            <#--													<span style="font-weight: 400;font-size: 12px;color: #999999;">退回购物金:</span>-->
                                            <#--													<span style="font-weight: 500;font-size: 12px;color: #FF0000;">￥121.80</span>-->
                                            <#--												</div>-->
                                            <#--											</div>-->

                                            <#--											<div class="table-item-row-aftersales-row5">-->
                                            <#--												<a href="/merchant/center/order/detail/3.htm" class="opt-btn">订单详情 ></a>-->
                                            <#--											</div>-->
                                            <#--										</div>-->
                                        </div>
                                    <#else>
                                        <div class="table-item" style="width:1138px;">
                                            <div class="cgd-qy" style="justify-content:space-between;">
                                                <div style="flex: 1">
                                                    <label <#if paramOrder.status==null || paramOrder.status==3> class="checkbox-pretty inline"</#if>>
                                                        <#if paramOrder.status==null || paramOrder.status==3>
                                                            <input type="checkbox" name="check_order_tr"
                                                                st="${order.status }" value="${order.id }"/>
                                                            <span></span>
                                                        </#if>
                                                    </label>
                                                    <#if order.isParent == 0>
                                                        <#if order.isThirdCompany ==0>
                                                            <span class="ziying">自营</span>
                                                        <#else>
                                                            <img src="/static/images/pop-shop-icon.png" alt=""
                                                                style="margin-right: 0px;top: -1px;position: relative;width: 18px;">
                                                        </#if>
                                                        <span class="qy-title">
                                                        <#if order.isThirdCompany ==1>
                                                            <a target="_blank"
                                                                href="/company/center/companyInfo/shopIndex.htm?orgId=${order.orgId}">${order.origName}</a>
                                                        <#else>
                                                        <#--  /selfShop/center/${order.shopCode}.htm  -->
                                                            <a target="_blank"
                                                                href="/selfShop/center/${order.shopCode}.htm">${order.origName}</a>
                                                        </#if>
                                                        </span>
                                                        <#if order.isThirdCompany == 0>
                                                            <#if order.isVirtualSupplier?? && order.isVirtualSupplier !='1'>
                                                                <a href="javaScript:callKf('${order.orderNo}','${merchant.id}');">
                                                                    <img src="/static/images/order/im-icon.png" width="20"
                                                                        height="20">
                                                                </a>
                                                            </#if>
                                                        <#else>
                                                            <!--pop-->
                                                            <#if order.isVirtualSupplier?? && order.isVirtualSupplier !='1'>
                                                                <a href="javaScript:callKf('${order.orderNo}','${merchant.id}','${order.orgId}','${order.isThirdCompany}','${order.origName}');"
                                                                style="margin-left: 8px;font-size:12px;">
                                                                    联系商家
                                                                </a>
                                                            </#if>
                                                        </#if>
                                                    <#else>
                                                        <span>药帮忙<span>    
                                                    </#if>
                                                </div>
                                                <div>
													<#if order.toBeConfirmedCount?? && order.toBeConfirmedCount gt 0>
														<div style="margin-right:10px;">
															有待确认的退款待处理，
															<a href="/merchant/center/order/findRefundOrderList/${order.id}.htm"
															class="opt-refund-btn" data-toggle="modal">去处理<span
																		class="white-circle"></span></a>
														</div>
													</#if>
													<#if order.status==10>
														<div id="${order.orderNo}" class="xLastPayTime" style="color:red;" data-source='{"id":"${order.orderNo}", "end":"${order.payExpireTime?string("yyyy-MM-dd HH:mm:ss")}"}'>
														</div>
													</#if>
												</div>
                                            </div>

                                            <#--<div class="table-item">-->
                                            <#--<div class="table-item-head">-->
                                            <#--<label <#if paramOrder.status==null || paramOrder.status==3> class="checkbox-pretty inline"</#if>>-->
                                            <#--<#if paramOrder.status==null || paramOrder.status==3>-->
                                            <#--<input type="checkbox" name="check_order_tr" st="${order.status }" value="${order.id }" />-->
                                            <#--</#if>-->
                                            <#--<span>${order.orderNo}-->
                                            <#--<#if (order.payType==null || order.payType==1)>-->
                                            <#--<#if (order.payTime??)>(已支付)-->
                                            <#--<#else>(未支付)-->
                                            <#--</#if>-->
                                            <#--<#elseif (order.payType==3)>-->
                                            <#--<#if (order.paymentTime??)>(已支付)-->
                                            <#--<#else>(未支付)</#if>-->
                                            <#--<#elseif (order.payType==2)>-->

                                            <#--</#if>-->
                                            <#--</span>-->
                                            <#--</label>-->
                                            <#--</div>-->
                                            <div class="table-item-body clear" style="background: #fff;">
                                                <a href="/merchant/center/order/detail/${order.id}.htm"
                                                   class="table-body-row table-body-row1"><img
                                                            src="${productImageUrl}/ybm/product/min/${order.imageUrl}"></a>
                                                <span class="table-body-row table-body-row2" style="width:380px;">
												    <div>订单编号：${order.orderNo}</div>
													<div>下单时间：${order.createTime?string('yyyy-MM-dd HH:mm:ss') }</div>
													<div>订单内共有 ${order.varietyNum} 件商品</div>
												</span>
                                                <#--<span class="table-body-row table-body-row7">-->
                                                <#--<div><#if order.isThirdCompany ==0><#if order.channelCode?? && order.channelCode=="2" >宜块钱 <#else> 药帮忙</#if></#if></div>-->
                                                <#--<div></div>-->
                                                <#--</span>-->
                                                <span class="table-body-row table-body-row3">
													<div>${order.statusName?default("--")}</div>
													<div></div>
												</span>
                                                <span class="table-body-row table-body-row4">
													<div>¥${order.money}</div>
													<div>${order.payTypeName}</div>
												</span>
                                                <span class="table-body-row table-body-row5">
													<a href="/merchant/center/order/detail/${order.id}.htm"
                                                       class="opt-btn">查看订单</a>
													<#if (order.reminderStatus !=0)>
                                                        <#if (order.reminderStatus == 1)>
                                                            <a href="javascript:void(0)" class="opt-btn" onclick="reminderShipment('${order.orderNo}','${merchant.id}','${order.orgId}','${order.isThirdCompany}','${order.origName}')">提醒发货</a>
                                                        </#if>
                                                        <#if (order.reminderStatus == 2)>
                                                            <a href="/merchant/center/order/reminder/detail/${order.id}.htm"class="opt-btn">提醒发货进度</a>
                                                        </#if>
                                                        
                                                    </#if>
                                                    <#if order.status!=10 && 3!=order.orderChannel && !(order.isVirtualSupplier?? && order.isVirtualSupplier == 1 && order.orderChannel == 7)>
                                                    <a href="javascript:void(0)" class="opt-btn"
                                                       onclick="action_sub_module_click_orderList(this,'${order_index}','${order.orderNo}','${order.statusName}');nowBuy('${order.id}','${order.orderNo}')">
                                                            再次购买</a></#if>
                                                    <#if (order.status==2 || order.status==3 || order.status==91 ) && merchant.accountRole!=3>
                                                        <#if order.isThirdCompany==0 || order.isFbp == 1>
                                                            <a href="javascript:void(0)" class="opt-refund-btn"
                                                               id="getSignaturesDownload" data-toggle="modal"
                                                               onclick="getSignaturesDownload('${order.orderNo}','${merchant.id}')">商品资质下载<span
                                                                        class="white-circle"></span></a>
                                                        <#elseif order.signaturesStatus == 1>
                                                            <a href="javascript:void(0)" class="opt-refund-btn"
                                                               id="getSignaturesDownloadPOP" data-toggle="modal"
                                                               onclick="getSignaturesDownloadPOP('${order.orderNo}','${order.orgId}','${merchant.id}','${order.isThirdCompany}','${order.origName}')">商品资质下载<span
                                                                        class="white-circle"></span></a>
                                                        </#if>
                                                    </#if>
                                                    <#if order.appraiseStatus==1&& merchant.accountRole!=3 ><a
                                                        href="${ctx }/merchant/center/order/evaluate/edit/${order.id}.htm"
                                                        class="opt-btn">评价</a></#if>
                                                    <#if order.appraiseStatus==2 && merchant.accountRole!=3><a
                                                        href="${ctx }/merchant/center/order/evaluate/detail/${order.orderNo}.htm?id=${order.id}"
                                                        class="opt-btn">查看评价</a></#if>

                                                    <#--	<#if order.status==2 || order.status==3>
                                                            <a href="javascript:void(0)" class="opt-refund-btn" data-toggle="modal" id="firstCampDownload" onclick="firstCampDownload('${order.orderNo}',null,'firstCamp')">首营资料下载<span class="white-circle"></span></a>
                                                        </#if>
                                                        -->
                                                    <#if order.outOrderShowFlag?? && order.outOrderShowFlag == 1 &&merchant.accountRole!=3>
                                                        <a href="javascript:void(0)"
                                                           onclick="getHandoverOrderUrl('${order.orderNo}','${order.branchCode}','1')">电子出库单</a>
                                                    </#if>
                                                    <#if order.goldCollectCertificate?? && order.goldCollectCertificate.showContract == true>
                                                        <a href="javascript:void(0)"
                                                           onclick="jumpShopingDescUrl('${order.goldCollectCertificate.contractUrl}');clickSubModule('${order.orderNo}','${order.statusName}','${order.goldCollectCertificate.fileType}','${order.goldCollectCertificate.fileId}','${order_index}')">购物金代收款说明</a>
                                                    </#if>
												</span>
                                                 <#if merchant.accountRole!=3>
                                                <span class="table-body-row table-body-row6">
                                                    <#if (order.refundCount > 0)><a
                                                        href="/merchant/center/order/findRefundOrderList/${order.id}.htm"
                                                        class="opt-refund-btn" data-toggle="modal" >查看退款<span
                                                                class="white-circle"></span></a></#if>
                                                    <#if paramOrder.status==90 && order.canPlatformIn == true>
                                                                <a
                                                        onclick="jumpPlatformHandle('','${order.orderNo}')"
                                                        class="opt-refund-btn" href="javascript:void(0)" data-toggle="modal" >平台介入<span
                                                                class="white-circle"></span></a></#if>
                                                    <#if ((order.status=1 || order.status=2) && order.payType=2 && order.orderChannel != 92) || order.status=10>
                                                    <a href="javascript:void(0)" class="opt-btn" data-toggle="modal"
                                                       onclick="cancelOrder(this,'${order.id}','${order.status}','${order.orderNo}')">
                                                            取消订单<span class="white-circle"></span></a></#if>
                                                    <#if order.isThirdCompany=0 && order.isKa != 1>
                                                        <#if ((((order.status=1 || order.status=2) && (order.payType=1 || order.payType=3)) || order.status=3 || order.status=7) && order.isShowRefund=0)>
                                                            <a href="javascript:void(0)" class="opt-refund-btn"
                                                               data-toggle="modal"
                                                               onclick="applyReturn(this,'${order.id}','${order.status}','${order.payType}','${order.balanceStatus}','${order.isThirdCompany}')">申请退款<span
                                                                        class="white-circle"></span></a>
                                                        </#if>
													<#elseif order.isThirdCompany=1 && order.isShowRefund=0>
                                                        <#if ((order.status=1 || order.status=2 || order.status=7) && (order.payType=1 || order.payType=3)) || order.status=3 || order.status=91>
                                                        <a href="javascript:void(0)" class="opt-refund-btn"
                                                           data-toggle="modal"
                                                           onclick="applyReturn(this,'${order.id}','${order.status}','${order.payType}','${order.balanceStatus}','${order.isThirdCompany}')">
                                                                申请退款<span class="white-circle"></span></a></#if>
                                                    </#if>
                                                    <#if order.status=10 && order.payType!=3><a
                                                        href="/merchant/center/order/confirmOrder.htm?id=${order.id}&useVirtualGold=true"
                                                        class="opt-btn" >立即支付<span class="white-circle"></span>
                                                        </a></#if>
                                                    <#if order.status=3 >
                                                        <#if order.balanceStatus=0>
                                                            <a href="javascript:void(0)"
                                                               onclick="selectOrderBalance('${order.id}','${order.balanceText}')"
                                                               class="rec-money-btn">领取余额<span
                                                                        class="red-circle"></span></a>
                                                        </#if>
														<#if order.balanceStatus=1>
                                                        <a href="javascript:void(0)" class="">已领取<span
                                                                    class="white-circle"></span></a>
                                                    </#if>
													<#elseif (order.status==2 || order.status==20)>
                                                        <#if order.canConfirmReceipt==1>
                                                            <a href="javascript:void(0)"
                                                               onclick="confirmOrderGoods('${order.id}')"
                                                               class="">确认收货<span class="white-circle"></span></a>
                                                        </#if>
                                                    </#if>
                                                    <#if order.status==2 || order.status==3>
                                                        <#if order.isThirdCompany==0 || order.isFbp == 1>
                                                            <a href="javascript:void(0)" class="opt-refund-btn"
                                                               id="drugReportDownload" data-toggle="modal"
                                                               onclick="drugReportDownload('${order.orderNo}',null,'drugReport')">药检报告下载<span
                                                                        class="white-circle"></span></a>
                                                        <#elseif order.signaturesStatus == 1>
                                                            <a href="javascript:void(0)" class="opt-refund-btn"
                                                               id="drugReportDownloadPOP" data-toggle="modal"
                                                               onclick="drugReportDownloadPOP('${order.orderNo}','${order.orgId}','${merchant.id}','${order.isThirdCompany}','${order.origName}')">药检报告下载<span
                                                                        class="white-circle"></span></a>
                                                        </#if>
                                                    </#if>
                                                    <#if order.showUploadEvidenceBtn>
                                                        <a target="_blank"
                                                           href="/merchant/center/order/offline_instruction.htm?id=${order.id}"
                                                           class="opt-btn" style="color: #6689ee;">上传电汇凭证</a>
                                                    </#if>
                                                    <#if !order.showUploadEvidenceBtn&&order.status=10 && order.payType=3><a target="_blank"
                                                                                               href="/merchant/center/order/offline_instruction.htm?id=${order.id}"
                                                                                               class="opt-btn"
                                                                                               style="color: #6689ee;" >
                                                            查看电汇凭证</a>
                                                    </#if>
                                                    <#if order.handoverOrderShowFlag?? && order.handoverOrderShowFlag == 1>
                                                        <a href="javascript:void(0)"
                                                           onclick="getHandoverOrderUrl('${order.orderNo}','${order.branchCode}','2')">仓库交接单</a>
                                                    </#if>
                                                    <#if order.status==2 || order.status==3>
										                 <a href="javascript:void(0)" class="fapiao"
                                                            onclick="showInvoince(${order.id },'${order.orderNo }',${order.billType}, 'orderList')">查看发票</a>
                                                    </#if>
                                                    <#if order.contract.showContract>
										                 <a href="javascript:void(0)" class="hetong"
                                                            onclick="showContract('${order.contract.contractName }','${order.contract.contractUrl }')">购销合同</a>
                                                    </#if>
                                                </span>
                                                 </#if>
                                            </div>
                                            <#if order.sellerRemark && (order.status==10 || order.status==1 || order.status==7)>
                                                <div style="color: #FF9500; background: #FFFAE0;padding: 10px 20px; overflow: hidden;text-overflow: ellipsis;white-space: nowrap;font-size: 12px;">${order.sellerRemark}</div>
                                            </#if>

                                            <#if order.hasOrderExceptionFlag == true>
                                                <div style="display: flex;justify-content: space-between;padding: 10px 10px 10px 30px;background: #fff;align-items: center;">
                                                    <div hastrack="false"
                                                         data-supplierException="${order.supplierException}"
                                                         data-sysException="${order.sysException}"
                                                         data-orderNo="${order.orderNo}"
                                                         data-exceptionFlag="${order.hasOrderExceptionFlag}"
                                                         data-source="order_list">
                                                        <#if order.sysException?? && order.sysException !="">
                                                            <div style="max-height:18px;overflow: hidden;position:relative;">
                                                                <#--  ${order.sysException}  -->
                                                                <span style="color: red">系统：</span><span>${order.sysException}</span>
                                                                <div
                                                                        style="position:absolute;top:-18px;left: 0;max-height: 36px;overflow:hidden;color: transparent;"
                                                                        data-orderNo="${order.orderNo}"
                                                                        data-source="order_list" data-checkMore="true"
                                                                        data-type="system" hastrack="false"
                                                                >
                                                                    系统：${order.sysException}
                                                                    <span class="checkMoreSysException"
                                                                          style="position:absolute;top: 18px;right: 0;cursor: pointer;background:#fff;color:#31cb96;"
                                                                          data-sysException="${order.sysException}"
                                                                          data-source="order_list"
                                                                          data-orderNo="${order.orderNo}">...查看</span>
                                                                </div>
                                                            </div>
                                                        </#if>
                                                        <#if order.supplierException?? && order.supplierException !="">
                                                            <div style="max-height:18px;overflow: hidden;position:relative;">
                                                                <#--  ${order.supplierException}  -->
                                                                <span style="color: red">商家：</span><span>${order.supplierException}</span>
                                                                <div
                                                                        style="position:absolute;top:-18px;left: 0;max-height: 36px;overflow:hidden;color: transparent;"
                                                                        data-orderNo="${order.orderNo}"
                                                                        data-source="order_list" data-checkMore="true"
                                                                        data-type="business" hastrack="false"
                                                                >
                                                                    商家：${order.supplierException}
                                                                    <span class="checkMoreSupplierException"
                                                                          style="position:absolute;top: 18px;right: 0;cursor: pointer;background:#fff;color:#31cb96;"
                                                                          data-supplierException="${order.supplierException}"
                                                                          data-source="order_list"
                                                                          data-orderNo="${order.orderNo}">...查看</span>
                                                                </div>
                                                            </div>
                                                        </#if>
                                                    </div>
                                                    <div style="flex-shrink: 0;padding-left: 5px;">
                                                        <button type="button"
                                                                class="sui-btn btn-primary btn-large handleJumpLicense"
                                                                data-source="order_list"
                                                                data-orderNo="${order.orderNo}">去更新
                                                        </button>
                                                    </div>
                                                </div>
                                            </#if>
                                        </div>
                                    </#if>
                                </#list>

                                <div class="table-bot">
                                    <label class="checkbox-pretty check-all-btn inline">
                                        <input type="checkbox"><span>全选</span>
                                    </label>
                                    <a href="javascript:void(0)" data-toggle="modal"
                                       class="oper-del-btn oper-btn">删除选择</a>
                                </div>
                                <!--分页器-->
                                <div class="page">
                                    <#import "/common/pager.ftl" as p>
                                    <@p.pager currentPage=pager.currentPage limit=pager.limit total=pager.total pageCount=pager.pageCount toURL=pager.requestUrl method="get"/>
                                    <input type="hidden" name="currentPage" id="currentPage" value="${pager.currentPage}"/>
                                </div>
                                <#else>
                                    <!--没有订单-->
                                    <div class="nowish">
                                        <img src="/static/images/user/noorder.png" alt="">
                                    </div>
                                </#if>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <!--主体部分结束-->
            <!--弹窗提示-->
            <div id="delModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                            <h4 id="myModalLabel" class="modal-title">提示</h4>
                        </div>
                        <div class="modal-body">
                            <div class="scbox"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large">确定</button>
                            <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="confirmOrderGoodsModal" tabindex="-1" role="dialog" data-hasfoot="false"
                 class="sui-modal hide fade">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                            <h4 id="myModalLabel" class="modal-title">提示</h4>
                        </div>
                        <div class="modal-body">
                            <div class="scbox">请收到商品后，再确认收货，否则您将可能钱货两空！</div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large">确定</button>
                            <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                        </div>
                    </div>
                </div>
            </div>

            <!--底部导航区域开始-->
            <div class="footer" id="footer">
                <#include "/common/footer.ftl" />
            </div>
            <!--底部导航区域结束-->
            <!--客服入口开始-->
            <div class="kefu-box">
                <a href="javaScript:callKf('','${merchant.id}');">
                    <img src="/static/images/kefu-online.png" alt="">
                </a>
            </div>
            <!--客服入口结束-->
        </div>

        <div id="successModel" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static"
             class="sui-modal hide fade">
            <b style="color: #31cb96" id="mol-money"></b>元余额已到账，请确认查收
        </div>

        <div id="progressModel" tabindex="-1" role="dialog" data-hasfoot="false" data-backdrop="static"
             class="sui-modal hide fade" height="32" style="border-radius: 10px;">
            <div style="background: #fff;padding:15px 10px;border-radius: 10px;">
                <p style="text-align: center;margin-bottom: 20px;">正在为您下载，请稍等…</p>
                <div class="sui-progress progress-success progress-striped active"
                     style="height:32px;border-radius: 5px;">
                    <div id="progress_rate" class="bar" style="border-radius: 5px;"></div>
                    <div class="bar-text" id="progress_text" style="height:32px;line-height: 32px;">0%</div>
                </div>
            </div>
        </div>

        <div id="J_addsuppliersDialog" tabindex="-1" role="dialog" class="sui-modal hide fade" data-width="800"
             data-height="400">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 id="myModalLabel" class="modal-title">温馨提示</h4>
                    </div>
                    <div class="modal-body sui-form form-horizontal">
                        <div class="sui-msg msg-block msg-default msg-tips">
                            <div class="msg-con">
                                以下部分商品暂未上传药检报告资料，“确定”则为您下载已上传的商品药检报告以及未找到药检报告的商品清单。如有疑问请您及时联系客服人员进行咨询，客服电话：400-0505-111。
                            </div>
                            <s class="msg-icon"></s>
                        </div>
                        <table class="sui-table table-bordered">
                            <thead>
                            <tr>
                                <th style="text-align: center;width: 50px;">序号</th>
                                <th style="text-align: center;">商品名称</th>
                                <th style="text-align: center;">商品规格</th>
                                <th style="text-align: center;">生产厂家</th>
                                <th style="text-align: center;">是否存在药检报告</th>
                            </tr>
                            </thead>
                            <tbody id="J_addsuppliersDialog_tbody">
                            </tbody>
                        </table>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                        <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large">确定</button>
                    </div>
                </div>
            </div>
        </div>
        <#--  提醒发货弹窗  -->
        <div id="reminderShipment" tabindex="-1" role="dialog" class="sui-modal hide fade" data-width="440"
             data-height="80">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" aria-hidden="true" class="sui-close closeOrderBox1">×</button>
                        <h4 id="myreminderShipmentLabel" class="modal-title" style="text-align: center;">提醒发货</h4>
                    </div>
                    <div class="modal-body">
                        <p id="reminderShipmentBody">商家将在2个工作日内发货（剔除周末等法定节假日），如超时未处理，将自动为您申请退款并赔付</p>
                    </div>
                    <div class="modal-footer" style="text-align: center;">
                        <button type="button" id="reminderShipmentB" class="sui-btn btn-default btn-large closeOrderBox1" style="color:black">
                        <a  id="reminderShipmentA" href="javascript:void(0);" style="color: black;">联系商家</a>
                        </button>
                        <button type="button" class="sui-btn btn-primary btn-large closeOrderBox1">我知道了</button>
                    </div>
                </div>
            </div>
        </div>
        <!--取消订单弹窗-->
        <div id="cancelOrderDialog" tabindex="-1" role="dialog" class="sui-modal hide fade" data-width="440"
             data-height="80">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" aria-hidden="true" class="sui-close closeOrderBox">×</button>
                        <h4 id="myModalLabel" class="modal-title">取消订单</h4>
                    </div>
                    <div class="modal-body">
                        <p>确认要取消订单吗?</p>
                        <select class="reason-box"></select>
                        <span class='reason-tip'>至少选择一个</span>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="sui-btn btn-default btn-large closeOrderBox">取消</button>
                        <button type="button" id="sure-btn" class="sui-btn btn-primary btn-large">确定</button>
                    </div>
                </div>
            </div>
        </div>
        <!--再次购买部分不可购买弹窗-->
        <div id="buyAganDialog" tabindex="-1" role="dialog" class="sui-modal hide fade" data-width="600" data-height="200">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" aria-hidden="true" class="sui-close closebuyBox">×</button>
                        <div  class="modal-title newBuyTitle">以下商品已不能购买，是否将其他商品加入购物车？</div>
                    </div>
                    <div class="modal-body">
                        <div id="buyAgan"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="sui-btn btn-default btn-large closebuyBox">再想想</button>
                        <button type="button" id="buy-sure-btn" class="sui-btn btn-primary btn-large">确定</button>
                    </div>
                </div>
            </div>
        </div>
        <#--  //平台介入弹窗  -->
        <div id="ptjrDialog" tabindex="-1" style="height:460px;width:600px;border-radius: 8px;" role="dialog" class="sui-modal hide fade" data-width="600" data-height="460">
            <div class="modal-dialog">
                <div class="modal-content">
                      <iframe id="ptjrDialogIframe" src="" width="100%" height="100vh" style=" border: none;height:500px;border-radius:8px"></iframe>
                </div>
            </div>
        </div>
        <!--发票须知弹窗-->
        <div id="fpxzTc" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 class="modal-title">发票信息</h4>
                    </div>
                    <div class="modal-body">
                        <div class="xzmain">
                            <div class="dbinfo">
                                <span>订单编号：</span><span id="showInvoiceOrder"></span>
                                <#--								<span class="jg">下单时间：</span><span>${order.createTime?string('yyyy-MM-dd HH:mm:ss')}</span>-->
                                <#--								<span class="jg">付款时间：</span><span>-->
                                <#--									<#if order.payType==1>-->
                                <#--										<#if order.payTime??>-->
                                <#--											${order.payTime?string('yyyy-MM-dd HH:mm:ss')}-->
                                <#--										</#if>-->
                                <#--									<#elseif order.payType==3>-->
                                <#--										<#if order.paymentTime??>-->
                                <#--											${order.paymentTime?string('yyyy-MM-dd HH:mm:ss')}-->
                                <#--										</#if>-->
                                <#--									</#if>-->
                                <#--								</span>-->
                            </div>
                            <div class="dbinfo" style="margin-top: 10px;">
                                <span>发票信息：<span id="invoice-down-btn"
                                                 style="border: 1px solid #28a3ef; color: #28a3ef; padding: 2px 4px;cursor: pointer;"
                                                 onclick="downloadSingleOrderInvoice()">下载全部电子发票</span></span>
                            </div>
                            <div id="fapiaoDiv"></div>
                            <div id="invoice-down-share" style="margin: 10px 0;">
                                <span>分享至邮箱：</span>
                                <input type="text" placeholder="请输入接收人的电子邮箱地址"
                                       style="width: 300px; height: 24px; border: 1px solid #E8E8E8" id="emailUrl">
                                <input type="hidden" id="orderId" value="">
                                <input type="hidden" id="invoiceOrderNo" value="">
                                <button onclick="sendEmail()">发送</button>
                            </div>
                            <div class="dbinfo" style="margin-top: 10px;"><span>*发票类型以最终开具的发票为准</span></div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <!--消费返入口-->
        <div id="rebatePage" class="consumer-rebate">
                <div class="rebate-head">
                    <div class="rebate-flex">
                        <img style="height: 20px;" src="/static/images/rebate-title.png" alt="">
                        <img class="red-packet-h" src="/static/images/red-packet-h.png" alt="">
                    </div>
                    <div id="rebateContent" class="rebate-content-box">
                    参与下单返活动，预估月均可返红包 <span>30元</span>
                    </div>
                    <div class="rebate-look-btn">
                        立即查看
                        <img class="rebate-btn-icon" src="/static/images/more_icon.png">
                    </div>
            </div>
        </div>

</body>
</html>
<script>
     function jumpPlatformHandle(orderId,orderNo){    
         $.ajax({
                type: "GET",
                url: "/merchant/center/order/getRefundOrderListForPlatformIn",
                data: {
                    orderNo:orderNo
                },
                contentType: "application/json",
                dataType: "json",
                success: function (data) {
                        if(data.status=="success"){
                         if(data.data&&data.data.length){
                            var uniqueParam = new Date().getTime();
                            $('#ptjrDialog iframe').prop('src', '');
                            var urlDz='/newstatic/#/ptjrDialog?orderNo=' + orderNo + '&_=' + uniqueParam
                            setTimeout(function(){
                                $('#ptjrDialog iframe').prop('src', urlDz);
                                $("#ptjrDialog").modal("show")
                            },200)
                         }else{
                            $.alert("亲，若卖家拒绝您的申请，或者超2个工作日未处理，您可以申请“平台介入”");
                         }
                       }else{
                         $.alert(data.errorMsg);
                       }
                   
                }
            });        
        }
        window.closePtjrDialog=function(e,data){
           if(e){
             $.ajax({
                type: "GET",
                url: "/intervention/getRedirectUrl",
                data: {
                    afsNo:data.refundOrderNo,
                    orderNo:data.orderNo
                },
                contentType: "application/json",
                dataType: "json",
                success: function (data) {
                       if(data.status=="success"){
                         window.open(data.data.data)  
                       }else{
                         $.alert(data.errorMsg);
                       }
                        
                         
                   
                }
            });
             
           }
       $("#ptjrDialog").modal("hide")    
        }
    //点击子模块QT上报
    function qtClick(data){
        aplus_queue.push({
            'action': 'aplus.record',
            'arguments': ['action_sub_module_click', 'CLK',data]
        });
    }
    window.addScmeV3 = function(type) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        //14位
        if(type==1){
            for (let i = 0; i < 14; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }
        //6位
        if(type==2){
            for (let i = 0; i < 6; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
           return result;
        }
        //8位
        if(type==3){
            for (let i = 0; i < 8; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
           return result;
        }
    }
    //qt埋点三期
	//PC订单列表-侧边栏点击
    try{
        window.tabindexClick=function(index,name){
            let tabs = tabSwitch(document.querySelector('.order-tab-title-item-cur a').text)
            let spm_cnt = "1_4."+'orderList_0-0_' + tabs +".merchantCenterMenu@5." + "btn@" + index + '.' +window.getSpmE(); //订单页面侧边栏按钮点击
            let scm_cnt = "pcFE.0.all_0." + "text-" + name + '.' + window.addScmeV3(1);
            qtClick({
                "spm_cnt": spm_cnt,
                "scm_cnt": scm_cnt
            })
        }
    }catch(err){
        console.log(err)
    }
    //qt埋点三期
	//PC订单列表-页面曝光
    $(document).ready(function(){
        try{
            sessionStorage.setItem("orderListClick", window.addScmeV3(3));
            let tabs = tabSwitch(document.querySelector('.order-tab-title-item-cur a').text)
            let spm_cnt="1_4."+'orderList_0-0_' + tabs +".0.0."+window.getSpmE(); //订单页面曝光
            aplus_queue.push({
                'action': 'aplus.record',
                'arguments': ['page_exposure', 'EXP', {
                "spm_cnt":spm_cnt,
                }]
            });
        }catch(err){
            console.log(err);
        }
    })
    function tabSwitch(tabName){
        if(tabName.includes('全部订单')) return 'QBDD';
        else if(tabName.includes('待付款')) return 'DFK';
        else if(tabName.includes('待配送')) return 'DPS';
        else if(tabName.includes('待收货')) return 'DSH';
        else if(tabName.includes('退款')) return 'TK';
        else if(tabName.includes('售后')) return 'SH';
    }
    //更新资质埋点
    $("#updateLicense").click(function () {
        var link = $(this);
        webSdk.track('QualificationUpdate_click', {
            'pageName': window.document.title
        }, function () {
            location.href = $(link).attr('href'); //继续跳转到目标页面
        });
        // return false;
    })
	//需要展示剩余支付时间的订单集合
	const orderIds = [];
	function timeLoop(){
		const els = $('.xLastPayTime');
		if (els.length == 0) {
			return
		}
		els.each(index => {
			orderIds.push(JSON.parse(els[index].getAttribute("data-source")));
		})
		const timer = setInterval(() => {
			orderIds.forEach((item) => {
				const deadLine = new Date(item.end).getTime();
				const now = Date.now();
				const residue = deadLine - now;
				if (residue <= 0) {
					$(`#${item.id}`).text('剩余支付时间：00天00时00分00秒')
				} else {
					let d = parseInt((residue / 60 / 60 / 24 / 1000)).toFixed(0);
					let h = parseInt((residue / 60 / 60 / 1000 % 24)).toFixed(0);
					let m = parseInt((residue / 60 / 1000 % 60)).toFixed(0);
					let s = parseInt((residue / 1000 % 60)).toFixed(0);
					d = d < 10 ? '0' + d : d;
					h = h < 10 ? '0' + h : h;
					m = m < 10 ? '0' + m : m;
					s = s < 10 ? '0' + s : s;
					const str = "剩余支付时间：" + d + "天" + h + "小时" + m + "分钟" + s + "秒";
					const id = '#' + item.id;
					$(id).text(str);
				}
			})
		}, 1000)

	}
	timeLoop()
    // 埋点五期
    // 购物金代收款说明 子模块点击
    function clickSubModule(orderNo,statusName,fileType,fileId,order_index) {
        try {
            const tabs = tabSwitch(document.querySelector('.order-tab-title-item-cur a').text)
            let index = (Number($('#currentPage').val()) - 1)*10 + Number(order_index) + 1
            aplus_queue.push({
                action: "aplus.record",
                arguments: ['action_sub_module_click','CLK', {
                    spm_cnt: '1_4.' + 'orderList_0-0_' + tabs + '.orderList@9.' + 'orderCard@' + index + '_btn@5.' + window.getSpmE(),
                    scm_cnt: 'order.' + '0.' + 'all_0.' + 'order-' + orderNo + '_orderStatus-' + statusName + '_text-购物金代收款说明_fileType-' + fileType + '_fileId-' + fileId + '.' + window.scmEShopDetail(14)
                }]
            })
        }catch (e) {
            console.log(e)
        }
    }
</script>
