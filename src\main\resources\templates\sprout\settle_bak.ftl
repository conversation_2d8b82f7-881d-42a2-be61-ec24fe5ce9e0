<!DOCTYPE HTML>
<html>
	<head>
		<#include "/common/common.ftl" />
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="description" content="药帮忙网上商城是武汉小药药医药科技有限公司旗下国家药监局批准的正规药品采购、批发、销售网上药品交易电子商务平台，主要经营中西成药、营养保健、医疗器械、全部针剂等医药品类。药帮忙是全国正规合法网上采购药品平台,以全新的互联网营销理念，满足客户多方面需求。以诚信服务于广大用户，优化医药供应链流程为经营理念。原供货源直销医药商品，质量保障，同品质药品价格比市场更优惠。 ">
		<meta name="keywords" content="网上药店, 网上买药,网上购药,网上药品交易平台,网上药品批发,药品网站,药品批发,药品,药品网,医药批发,医药批发市场, 药品交易">
		<title>结算页</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="renderer" content="webkit">

		<link rel="stylesheet" href="/static/css/sprout/settle.css?t=${t_v}" />

        <style>
            .main1{
                background: #F2F2F2;
            }
            .fd-warp .list-item span.spec {
                width: 240px;
            }

            .default_address{
                margin-top:15px;
                color: #999999;
            }
            .isdefaultSec.checkbox-pretty>span{
                position: relative;
            }
            .isdefaultSec.checkbox-pretty>span:before{
                content: "";
                width:15px;
                height:15px;
                display: block;
                border:1px solid #999999;
                border-radius: 2px;
                top:0px;
                left:-20px;
                font-size: 0px;
                margin:0px;
                margin-right:5px;
                position: absolute;
            }
            .isdefaultSec.checkbox-pretty.checked>span:before{
                content: "✓";
                width:17px;
                height:17px;
                display: block;
                border:0px solid black;
                border-radius: 2px;
                text-align: center;
                line-height: 17px;
                top:0px;
                left:-20px;
                background: #00dc82;
                font-size: 14px!important;
                font-weight: 600;
                color: #fff!important;
                margin:0px;
                margin-right:5px;
                position: absolute;
            }
            .sui-tooltip{
                border:1px solid #eee;
                border-radius:8px;
            }
            .sui-tooltip.bottom .tooltip-arrow, .tooltip-only-arrow.bottom .tooltip-arrow{
                border-bottom-color: #eee;
            }
            .tooltip-inner{
                line-height: 25px;
            }
            .sui-tooltip.default .tooltip-inner, .sui-tooltip.normal .tooltip-inner, .sui-tooltip.confirm .tooltip-inner{
                color:#333;
            }
        </style>

		<script type="text/javascript" src="/static/js/jquery.cityselect.js?t=${t_v}"></script>
		<script type="text/javascript" src="/static/js/sprout/settle20200326.js?t=${t_v}"></script>
		<script type="text/javascript">
            var ctx="${ctx}";
            var shoppingCartImgUUID = "${shoppingCartImgUUID}";
		</script>

	</head>

	<body>
		<div class="container">
            <input type="hidden" id="divmore" value="0">
            <input type="hidden" id="tranNo" value="${shoppingCartInfo.tranNo}">
            <input type="hidden" id="purchaseNo" value="${purchaseNo}">
			<!--头部导航区域开始-->
			<div class="headerBox" id="headerBox">
				<#include "/common/header.ftl" />
			</div>
			<!--头部导航区域结束-->

			<!--结算页面头部导航-->
            <div class="topbzbox">
                <div class="warp">
                    <div class="bzcol1"><a href="/"><img src="/static/images/logo_login.png" ></a></div>
                    <div class="bzcol2"></div>
                    <div class="bzcol3">结算</div>
                    <div class="bzcol4"><img src="/static/images/buzhoujiesuan.png" ></div>
                </div>
            </div>

			<!--主体部分开始-->
			<div class="main1">
                <!--提示文案-->
                <div class="titleTips" style=" width: 1200px; margin: 10px auto;">
                    <div class="sui-msg msg-default msg-notice" style="position: relative;">
                        <i class="sui-icon icon-touch-noti-circle" style="position: absolute;top:10px;left:10px;font-size:22px;"></i>
                        <div class="msg-con" style="padding:10px 40px;background: #FFFDF4;color: #FBA475">声明：为严格执行《药品管理法》及《药品经营质量管理规范》的相关规定，送货地址默认为《药品经营许可证》中的经营地址或仓库地址，并不能随意修改。若地址有误，请联系客服：400-0505-111</div>
                    </div>
                </div>
				<!--支付主体内容-->
                <div class="pay-content" style="margin: 0px auto;padding: 10px 25px 25px 25px;">
					<!--填写并核对订单信息-->
					<#--<div class="p-title">填写并核对订单信息：</div>-->
					<!--收货人信息-->
					<div class="recebox">
                        <div class="address-top-title">收货人信息</div>
						<!--地址列表-->
						<div class="default-show">
                        <#if (shippingAddressList?size>0)>
                            <#list shippingAddressList as shippingAddress>
                            <div class="gspadres clear">
                                <input type="hidden" hi="id" value="${shippingAddress.id }"/>
                                <input type="hidden" hi="remark" value="${shippingAddress.remark }"/>
                                <input type="hidden" hi="auditState" value="${shippingAddress.auditState }"/>
                                <input type="hidden" hi="isdefault" value="${shippingAddress.isdefault!"" }" class="isdefault"/>
                                <label class="adres-row adres-row-new radio-pretty inline <#if shippingAddress.isdefault>checked </#if>">
                                    <input type="radio" <#if shippingAddress.isdefault>checked="checked" </#if> name="radio"><span></span>
                                </label>
                                <span class="adres-row adres-row1 text-overflow" data-id="contactor">${shippingAddress.contactor }</span>
                                <span class="adres-row adres-row2">
									<p class="main-ades text-overflow" data-id="fullAddress" style="white-space: unset;">${shippingAddress.fullAddress }</p>
								</span>
                                <span class="adres-row adres-row3 text-overflow" data-id="mobile">
                                    <span class="mobile">${shippingAddress.mobile }</span>
                                    <#if shippingAddress.isdefault>
                                        <span class="def-biaoqian">
                                            <span class="default">默认</span>
                                        </span>
                                    </#if>
                                </span>
                                <span class="adres-row adres-row4">
									<a href="javascript:void(0)" class="res-btn" data-toggle="modal" onclick="editAddress(this)" data-keyboard="false" data-target="#editModal"><i class="sui-icon icon-touch-edit-rect"></i>编辑</a>
								</span>
                            </div>
                            </#list>
                        </#if>
						</div>
					</div>
					<!--支付方式-->
					<div class="zffs">
                        <div class="address-top-title">选择支付方式</div>
						<ul>
							<li class="cur" t="1">在线支付</li>
							<li t="3">线下转账</li>
							<#if shoppingCartInfo.isShow==1>
								<li t="2">货到付款</li>
							</#if>

						</ul>
						<#--<#if payTips!''>-->
                        	<#--<span style="float: left;">返利：<br /><span style="font-size: 12px;color: #31cb96;">619周年庆，6月1日起全场最高返利10%，更有疯狂免单，等你来抢！</span></span>-->
						<#--</#if>-->
						<#if shoppingCartInfo.isShow==1>
							<div class="hdfk-box">
								<span>${shoppingCartInfo.offlineMessage}</span>
							</div>
						</#if>
						<input type="hidden" id="payTips" value="${payTips}"/>
					</div>

					<!--发票类型-->
					<!--发票类型-->
                    <div class="fplx">
                        <div class="address-top-title">选择发票类型 <a href="javascript:void(0)" class="fplx-a" data-toggle="modal"  data-keyboard="false" data-target="#fpxzTc" ><i class="sui-icon icon-notification"></i></a></div>
                        <ul id="invoiceUL">
                            <li class="cur" t="${invoiceType}">${invoiceText}</li>
                        </ul>
                        <#if (invoiceType==1 && ptdzInvoincePeer==1) >
                            <div class="fptx">
                                <label class="checkbox-pretty inline" id="peerTypeLab" >
                                    <input type="checkbox" value="1" id="peerType" /><span>电子普通发票随货同行<i>勾选后，我们会将您的电子普通发票打印并加盖鲜章，随货为您寄出。</i></span>
                                </label>
                            </div>
                        </#if>
                    </div>

					<!--
						<div class="fplx">
						<div class="address-top-title">选择发票类型</div>
						<ul>
							<#if invoiceType==1>
							<li class="cur" t="1">普通发票</li>
							<#elseif invoiceType==2>
							<li class="cur" t="2">专用发票</li>
							</#if>
						</ul>
					</div>
					-->

                    <!--备注留言-->
                    <div class="bzly" style="border:none;padding-bottom: 0;">
                        <div class="address-top-title">备注留言</div>
                        <input type="text" id="remark" placeholder="填写您的备注留言">
                    </div>
				</div>

                <!--送货清单-->
                <div class="shqdtitle">送货清单</div>
                <div class="shqd">
                    <!--默认显示-->
                    <div class="defaultbox">
                        <!--列表模式-->
                        <div class="listmode">
                            <!--表头-->
                            <div class="headbox">
                                <ul>
                                    <li class="li1">
                                        <span>商品信息</span>
                                    </li>
                                    <li class="li_mingxi"><span class="head-tit">明细</span></li>
                                    <li class="li3">单价</li>
                                    <li class="li5">数量</li>
                                    <li class="li4">金额</li>
                                    <!--<li class="li6">操作</li>-->
                                </ul>
                            </div>


                            <!--表体-->
						<#assign showCartItemNum = 0 />
						<#list shoppingCartInfo.group as shoppingCartGroup>

                            <#if (shoppingCartGroup.isThirdCompany == 1)>
                                <div class="cgd-qy">
                                    <span class="qy-title">${shoppingCartGroup.companyName}</span>
                                </div>
                            <#-- <div class="taocanbox-onther">
                                 <span class="new">${shoppingCartGroup.companyName}</span>
                             </div>-->
                            <#elseif (shoppingCartGroup.isThirdCompany ==0) && (shoppingCartGroup_index = 0)>
                                <div class="cgd-qy">
                                    <span class="ziying">自营</span>
                                    <span class="qy-title">${shoppingCartGroup.companyName}</span>
                                </div>
                            <#elseif (shoppingCartGroup.type = 9) && (shoppingCartGroup_index > 0) && shoppingCartGroup.channelCode = '1'>
                                 <div class="taocanbox-onther">
                                     <span class="new">其它商品：</span>
                                 </div>
                            </#if>
                            <#if (shoppingCartGroup.channelCode == '2') && (shoppingCartGroup.isThirdCompany ==0)>
                            <!--宜块钱渠道-->
                            <div class="taocanbox-onther">
                                <span class="new" style="padding-left: 23px;"><img src="/static/images/order/channel-yi.png" alt="" style="margin-top:-3px;"></span>
                                <span class="new" style="padding-left:5px;padding-right:2px;">宜块钱渠道</span>
                                <a id="J_set" href="javascript:void(0)" data-placement="bottom" data-trigger="hover" data-toggle="tooltip" title="" data-original-title="<b style='font-size:14px;text-align:center;display:inline-block;width:100%;'>注意</b><br>亲，购买渠道【宜块钱】的商品时请注意：<br>该渠道商品不参与药帮忙促销活动，不支持返利<br>且不支持余额抵扣"><img src="/static/images/order/tip-icon.png" alt="" style="margin-top:-3px;cursor: pointer;"></a>
                            </div>
                            </#if>
                            <#if (shoppingCartGroup.channelCode == '1') && (shoppingCartGroup_index = 0) && (shoppingCartGroup.isThirdCompany ==0)>
                            <!--B2B渠道-->
                            <div class="taocanbox-onther">
                                <span class="new" style="padding-left: 23px;"><img src="/static/images/order/channel-yao.png" alt="" style="margin-top:-3px;"></span>
                                <span class="new" style="padding-left:5px;padding-right:2px;">药帮忙</span>
                            </div>
                            </#if>
                            <#if (shoppingCartGroup.type!=10 && shoppingCartGroup.type!=9)>
                            <div class="manjianbox" name="${shoppingCartGroup.id}" >
								<#if shoppingCartGroup.title??>
                                    <span class="title"><#if shoppingCartGroup.type==1>满减<#elseif shoppingCartGroup.type==2>满折<#elseif shoppingCartGroup.type==3>满赠<#elseif shoppingCartGroup.type==4>满减赠<#elseif shoppingCartGroup.type==6>一口价</#if></span>
                                    <span class="info">${shoppingCartGroup.title}</span>
                                </#if>
                                <#if shoppingCartGroup.type==5>
                                    <#--<label class="radio-pretty-giftId" style="padding-left: 27px;padding-right: 10px;">-->
                                        <input type="radio" name="giftId" style="margin-left: 17px;margin-right: 5px;" value="${shoppingCartGroup.id}" <#if shoppingCartGroup.selectStatus==1>checked="true" data-mutex-check="true"</#if> <#if shoppingCartGroup.selectStatus==0> data-mutex-check="false"</#if> >
                                    <#--</label><span></span>-->
                                    <span class="title_hui">物料心愿单礼包</span>
                                    <span class="info_hui" value="${shoppingCartGroup.id}">不需要</span>
                                    <input type="hidden" name="giftIds" value="${shoppingCartGroup.id}"/>
                                    <input type="hidden" name="giftTotalAmount" value="${shoppingCartGroup.totalAmount}"/>
                                </#if>
                            </div>
                            </#if>

							<#list shoppingCartGroup.sorted as shoppingCartItem>
								<#if (showCartItemNum = 5)>
									<#break/>
								</#if>
								<#assign showCartItemNum = showCartItemNum + 1 />

								<#if shoppingCartItem.itemType=3>
                                    <!--套餐商品-->
                                    <div class="taocanbox">
                                        <span class="new">套餐商品</span>
                                    </div>
                                        <div class="bodybox taocanspe">
                                            <!--列表-->
											<#list shoppingCartItem.subItemList as tcList>
                                                <ul>
                                                    <li class="lib1">
                                                        <div class="l-box fl">
                                                            <a href="/search/skuDetail/${tcList.id}.htm" target="_blank" title="${tcList.commonName }">
                                                                <img src="${productImageUrl}/ybm/product/min/${tcList.imageUrl }" alt="${tcList.commonName}" onerror="this.src='/static/images/default-big.png'"></a>
                                                            <!--标签-->
															<#if shoppingCartItem.item.valid=0>
                                                                <div class="bq-box">
                                                                    <img src="img/bq-qiangguang.png" alt="">
                                                                </div>
															</#if>
                                                            <#if tcList.blackSku=1>
                                                                <!--不参与返点提示-->
                                                            `   <#if (tcList.blackSkuText??) && (tcList.blackSkuText!="")>
                                                                    <div class="nofd">
                                                                        ${tcList.blackSkuText}
                                                                    </div>
                                                                </#if>
                                                            </#if>
                                                        </div>
                                                        <div class="r-box fr">
                                                            <div class="lib1-row1 text-overflow">
                                                                <a href="/search/skuDetail/${tcList.id}.htm" target="_blank" title="${tcList.commonName }">${tcList.sku.showName}</a>
                                                            </div>
                                                            <div class="lib1-row3">
                                                                <div class="row-biaoqian">
                                                                        <#if tcList.tagList ?? && (tcList.tagList?size >0) >
                                                                            <#list tcList.tagList as item >
                                                                                <#if (item_index < 3)>
                                                                                <span class="<#if item.uiType == 1>linqi</#if>
                                                                                <#if item.uiType == 2>quan</#if>
                                                                                <#if item.uiType == 3>manjian</#if>
                                                                                <#if item.uiType == 4>default</#if>
                                                                                <#if item.uiType == 5>yibao</#if>
                                                                                ">${item.name}</span>
                                                                                </#if>
                                                                            </#list>
                                                                        </#if>
                                                                </div>
                                                            </div>
                                                            <div class="lib1-row2 text-overflow">
                                                                <span class="title">规　　格：</span>
                                                                <span class="info">${tcList.sku.spec}</span>
                                                            </div>
														<#--<div class="lib1-row3">
                                                            <span class="title">批准文号：</span>
                                                            <span class="info">${tcList.sku.approvalNumber}</span>
                                                        </div>-->
                                                            <div class="lib1-row4 text-overflow">
                                                                <span class="title">生产厂家：</span>
                                                                <span class="info">${tcList.sku.manufacturer}</span>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li class="li_mingxi" id="${tcList.uniqueKey}">
                                                        <div class="mxrow1">
                                                            <span class="mxrow_tit">实付金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}realPayAmount">￥${tcList.realPayAmount}</span>
                                                            <span class="mxrow_tit">优惠金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}discountAmount">￥${tcList.discountAmount}</span>
                                                        </div>
                                                        <div class="mxrow2">
                                                            <span class="mxrow_tit">余额抵扣：</span><span class="mxrow_info" id="${tcList.uniqueKey}useBalanceAmount">￥${tcList.useBalanceAmount}</span>
                                                            <span class="mxrow_tit">返点金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}balanceAmount">￥${tcList.balanceAmount}</span>
                                                        </div>
                                                         <div class="mxrow2">
                                                             <span class="mxrow_tit">实付价：</span>
                                                             <span class="mxrow_info ${tcList.uniqueKey}purchasePriceK">￥<#if 1 == tcList.sku.isGive >${tcList.sku.fob?string('0.00')}<#elseif tcList.purchasePrice ?? >${tcList.purchasePrice?string('0.00')}<#else>0.00</#if></span>
                                                             <span class="mxrow_tit">成本价：</span><span class="mxrow_info  ${tcList.uniqueKey}costPriceK">￥<#if 1 == tcList.sku.isGive >${tcList.sku.fob?string('0.00')}<#elseif tcList.costPrice ?? >${tcList.costPrice?string('0.00')}<#else>0.00</#if></span>
                                                         </div>
                                                    </li>
                                                    <li class="lib3">
                                                        <div class="zkj">￥${tcList.sku.fob}</div>
                                                        <div class="sjj"><span>￥${tcList.sku.retailPrice}</span></div>

                                                        <!--价格登录可见样式-->
                                                        <!--<div class="loginshow">价格登录可见</div>-->
                                                        <!--暂无购买权限样式-->
                                                        <!--<div class="notbug">暂无购买权限</div>-->
                                                    </li>
                                                    <li class="lib5">
                                                        <span>x${tcList.amount}</span>
                                                    </li>
                                                    <li class="lib4"></li>
                                                </ul>
											</#list>
                                            <!--套餐价格-->
										<#--<div class="tcprice">￥${shoppingCartItem.item.subtotal}</div>-->
                                            <div class="taocanxj">
                                                <div class="zkj">￥${shoppingCartItem.item.subtotal}</div>
                                            </div>
                                        </div>
								</#if>

								<#if 	shoppingCartItem.itemType!=3>
                                    <div class="bodybox <#if shoppingCartGroup.type==5>bigGift${shoppingCartGroup.id} </#if>" >
                                        <!--列表-->
                                        <ul>
                                            <li class="lib1">
                                                <div class="l-box fl">
                                                    <a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm" target="_blank" title="${shoppingCartItem.item.sku.showName }">
                                                        <img src="${productImageUrl}/ybm/product/min/${shoppingCartItem.item.sku.imageUrl }" alt="${shoppingCartItem.item.sku.showName}" onerror="this.src='/static/images/default-big.png'">
                                                        <div class="bq-box">
															<#if shoppingCartItem.item.sku.status==2>
                                                                <img src="/static/images/product/bq-shouqing.png" alt="">
															</#if>
															<#if shoppingCartItem.item.sku.status==4  && shoppingCartItem.item.sku.isGive==0>
                                                                <img src="/static/images/product/bq-xiajia.png" alt="">
															</#if>
                                                        </div>
                                                        <#if shoppingCartItem.item.blackSku=1>
                                                            <!--不参与返点提示-->
                                                            <#if (shoppingCartItem.item.blackSkuText??) && (shoppingCartItem.item.blackSkuText!="")>
                                                                <div class="nofd">
                                                                    ${shoppingCartItem.item.blackSkuText}
                                                                </div>
                                                            </#if>
                                                        </#if>
                                                    </a>
                                                </div>
                                                <div class="r-box fr">
                                                    <div class="lib1-row1 text-overflow">
													<span>
														<a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm" target="_blank" title="${shoppingCartItem.item.sku.showName }">
                                                       <#if shoppingCartItem.item.isShow806 || shoppingCartItem.item.gift>
														    <div class="bq806">
														        <img src="/static/images/bq806.png" alt="">
														    </div>
                                                       </#if>
														<#if shoppingCartItem.item.agent == 1><span class="dujia">独家</span></#if>${shoppingCartItem.item.sku.showName }
														</a>
													</span>
                                                    </div>
                                                    <div class="lib1-row3">
                                                        <div class="row-biaoqian">
                                                            <#if shoppingCartItem.item.tagList ?? && (shoppingCartItem.item.tagList?size >0) >
                                                             <#list shoppingCartItem.item.tagList as item >
                                                                 <#if (item_index < 3)>
                                                                 <span class="<#if item.uiType == 1>linqi</#if>
                                                                 <#if item.uiType == 2>quan</#if>
                                                                 <#if item.uiType == 3>manjian</#if>
                                                                 <#if item.uiType == 4>default</#if>
                                                                 <#if item.uiType == 5>yibao</#if>
                                                                 ">${item.name}</span>
                                                                 </#if>
                                                             </#list>
                                                            </#if>
                                                        </div>
                                                    </div>
                                                    <div class="lib1-row5">
                                                        <div class="row-last">
                                                            <#if (shoppingCartItem.item.sku.suggestPrice ?? ) && (shoppingCartItem.item.sku.suggestPrice != '')>
                                                               <div class="kongxiao-box">
                                                                   <span class="s-kx">零售价</span><span class="jg">￥${shoppingCartItem.item.sku.suggestPrice}</span>
                                                               </div>
                                                            </#if>
                                                            <#if shoppingCartItem.item.sku.grossMargin ?? && shoppingCartItem.item.sku.grossMargin !=''>
                                                               <div class="maoli-box">
                                                                   <span class="s-ml">毛利</span><span class="jg">${shoppingCartItem.item.sku.grossMargin}</span>
                                                               </div>
                                                            </#if>
                                                        </div>
                                                    </div>
                                                    <div class="lib1-row2 text-overflow">
                                                        <span class="title">规　　格：</span>
                                                        <span class="info">${shoppingCartItem.item.sku.spec }</span>
                                                    </div>
                                                    <#--<div class="lib1-row3">
                                                        <span class="title">批准文号：</span>
                                                        <span class="info">${shoppingCartItem.item.sku.approvalNumber }</span>
                                                    </div>-->
                                                    <div class="lib1-row4 text-overflow">
                                                        <span class="title">生产厂家：</span>
                                                        <span class="info">${shoppingCartItem.item.sku.manufacturer }</span>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="li_mingxi">
                                                <div class="mxrow1">
                                                    <span class="mxrow_tit">实付金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}realPayAmount">￥${shoppingCartItem.item.realPayAmount}</span>
                                                    <span class="mxrow_tit">优惠金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}discountAmount">￥${shoppingCartItem.item.discountAmount}</span>
                                                </div>
                                                <div class="mxrow2">
                                                    <span class="mxrow_tit">余额抵扣：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}useBalanceAmount">￥${shoppingCartItem.item.useBalanceAmount}</span>
                                                    <span class="mxrow_tit">返点金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}balanceAmount">￥${shoppingCartItem.item.balanceAmount}</span>
                                                </div>
                                                <div class="mxrow2">
                                                    <span class="mxrow_tit">实付价：</span><span class="mxrow_info ${shoppingCartItem.item.uniqueKey}purchasePrice">￥<#if 1 == shoppingCartItem.item.sku.isGive >${shoppingCartItem.item.price?string('0.00')}<#elseif shoppingCartItem.item.purchasePrice ?? >${shoppingCartItem.item.purchasePrice?string('0.00')}<#else>0.00</#if></span>
                                                    <span class="mxrow_tit">成本价：</span><span class="mxrow_info  ${shoppingCartItem.item.uniqueKey}costPrice">￥<#if 1 == shoppingCartItem.item.sku.isGive >${shoppingCartItem.item.price?string('0.00')}<#elseif shoppingCartItem.item.costPrice ?? >${shoppingCartItem.item.costPrice?string('0.00')}<#else>0.00</#if></span>
                                                </div>
                                            </li>
                                            <li class="lib3">
                                                <div class="zkj">￥${shoppingCartItem.item.price }
                                                </div>
                                                <div class="sjj"><span>￥${shoppingCartItem.item.sku.retailPrice }</span></div>
                                                <!--价格登录可见样式-->
                                                <!--<div class="loginshow">价格登录可见</div>-->
                                                <!--暂无购买权限样式-->
                                                <!--<div class="notbug">暂无购买权限</div>-->
                                            </li>
                                            <li class="lib5">
                                                <span>x${shoppingCartItem.item.amount }</span>
                                            </li>
                                            <li class="lib4">
                                                <div class="zkj">￥${shoppingCartItem.item.subtotal }</div>
                                                <!-- <div class="sjj">
												<span>满减优惠￥</span><span>${shoppingCartItem.item.subtotal }</span>
											</div>
											<div class="sjj">
												<span>优惠券￥</span><span>${shoppingCartItem.item.subtotal }</span>
											</div> -->
                                            </li>
                                        </ul>
                                    </div>
								</#if>
                                <#if shoppingCartItem_has_next!="true" &&(shoppingCartGroup.isThirdCompany ==1||(shoppingCartGroup.isThirdCompany ==0) && (shoppingCartGroup.isThirdCompanyLastFlag = 1))>
                                    <!--总计-->
                                    <div class="totalbox clear">
                                        <div class="rbox-total fr">
                                            <div class="spzjbox">
                                                共<span class="zhongshu">${shoppingCartGroup.productVarietyNum }</span>种商品，总件数<span class="zongjianshu">${shoppingCartGroup.productTotalNum }</span>商品总计：￥<span id="total_price_span"  <#if shoppingCartGroup.isThirdCompany ==0 >name="directTotalAmount"</#if> >${shoppingCartGroup.totalAmount?string('0.00') }</span>
                                            </div>
                                            <div class="fd-warp">
                                                <!--中间竖线-->
                                            <#--<div class="mid-line"></div>-->
                                                <div class="right-fd-box">
                                                <#--<div class="list-item">
                                                        <span class="spec">运费:</span><span  class="red">+￥</span><span  class="red">${(shoppingCartGroup.freightAmount)!'0.00' }</span>
                                                    </div>
                                                    <#if shoppingCartGroup.isThirdCompany ==0>
                                                        <div class="list-item">
                                                            <span class="spec">满减:</span><span class="red">-￥</span><span class="red" id="wholeDiscountAmount">${(shoppingCartGroup.promoDiscountAmount)!'0.00' }</span>
                                                        </div>
                                                        <div class="list-item">
                                                            <span class="spec">优惠券:</span><span class="red">-￥</span><span class="red" id="wholeVoucherAmount">${(shoppingCartGroup.voucherDiscountAmount)!'0.00' }</span>
                                                        </div>
                                                       <div class="list-item">
                                                            <span class="spec">余额抵扣:</span><span class="red">-￥</span><span class="red " name="balancespan"  id="balance_span">${(shoppingCartInfo.balanceAmount)!'0.00' }</span>
                                                            <input id="hasBalanceAmount"  type="hidden" value="${(shoppingCartInfo.balanceAmount)!'0.00' }"/>
                                                        </div>
                                                    </#if>-->
                                                </div>
                                            </div>

                                        <#if (shoppingCartInfo.rebate>0)>
                                        <#--<div class="fanli">-->
                                        <#--<span class="spec">返利:</span><span>-￥</span><span id="rebate_span">${shoppingCartInfo.rebate?string('0.00') }</span>-->
                                        <#--</div>-->
                                        </#if>
                                        </div>
                                    </div>
                                </#if>
							</#list>
                            <#--<#if (shoppingCartGroup.type==10)>
                                <div class="taocanbox-onther">
                                    <span class="new"></span>
                                </div>
                            </#if>-->
						</#list>
                        </div>

                        <!--查看更多-->
					<#if (showCartItemNum = 5)>
                        <a href="javaScript:void(0); " class="more">点击展开 <i class="sui-icon icon-tb-unfold "></i> </a>
					</#if>
                    </div>
                    <!--全部显示-->
                    <div class="defaultbox-all">
                        <div class="listmode">
                            <!--表头-->
                            <div class="headbox">
                                <ul>
                                    <li class="li1">
                                        <span>商品信息</span>
                                    </li>
                                    <li class="li_mingxi"><span class="head-tit">明细</span></li>
                                    <!--<li class="li2">优惠</li>-->
                                    <li class="li3">单价</li>
                                    <li class="li5">数量</li>
                                    <li class="li4">金额</li>
                                    <!--<li class="li6">操作</li>-->
                                </ul>
                            </div>

                            <!--表体-->
						<#list shoppingCartInfo.group as shoppingCartGroup>
                            <#if (shoppingCartGroup.isThirdCompany == 1)>
                                    <div class="cgd-qy">
                                        <span class="qy-title">${shoppingCartGroup.companyName}</span>
                                    </div>
                            <#elseif (shoppingCartGroup.isThirdCompany ==0) && (shoppingCartGroup_index = 0)>
                                <div class="cgd-qy">
                                    <span class="ziying">自营</span>
                                    <span class="qy-title">${shoppingCartGroup.companyName}</span>
                                </div>
                            <#elseif (shoppingCartGroup.type = 9) && (shoppingCartGroup_index > 0) && (shoppingCartGroup.channelCode == '1')>
                                 <div class="taocanbox-onther">
                                     <span class="new">其它商品：</span>
                                 </div>
                            </#if>

                            <#if shoppingCartGroup.channelCode == '2'>
                                <!--宜块钱渠道-->
                                <div class="taocanbox-onther">
                                    <span class="new" style="padding-left: 23px;"><img src="/static/images/order/channel-yi.png" alt="" style="margin-top:-3px;"></span>
                                    <span class="new" style="padding-left:5px;padding-right:2px;">宜块钱渠道</span>
                                    <a id="J_set" href="javascript:void(0)" data-placement="bottom" data-trigger="hover" data-toggle="tooltip" title="" data-original-title="<b style='font-size:14px;text-align:center;display:inline-block;width:100%;'>注意</b><br>亲，购买渠道【宜块钱】的商品时请注意：<br>该渠道商品不参与药帮忙促销活动，不支持返利<br>且不支持余额抵扣"><img src="/static/images/order/tip-icon.png" alt="" style="margin-top:-3px;cursor: pointer;"></a>
                                </div>
                            </#if>
                            <#if (shoppingCartGroup.channelCode == '1') && (shoppingCartGroup_index = 0) && (shoppingCartGroup.isThirdCompany ==0)>
                                <!--B2B渠道-->
                                <div class="taocanbox-onther">
                                    <span class="new" style="padding-left: 23px;"><img src="/static/images/order/channel-yao.png" alt="" style="margin-top:-3px;"></span>
                                    <span class="new" style="padding-left:5px;padding-right:2px;">药帮忙</span>
                                </div>
                            </#if>

                            <#if (shoppingCartGroup.type!=10 && shoppingCartGroup.type!=9)>
                                <div class="manjianbox" name="${shoppingCartGroup.id}">
                                    <#if shoppingCartGroup.title??>
                                        <span class="title"><#if shoppingCartGroup.type==1>满减<#elseif shoppingCartGroup.type==2>满折<#elseif shoppingCartGroup.type==3>满赠<#elseif shoppingCartGroup.type==4>满减赠<#elseif shoppingCartGroup.type==6>一口价</#if></span>
                                        <span class="info">${shoppingCartGroup.title}</span>
                                    </#if>
                                    <#if shoppingCartGroup.type==5>
                                    <#--<label class="radio-pretty-giftId" style="padding-left: 27px;padding-right: 10px;">-->
                                        <input type="radio" name="giftIdmore" style="margin-left: 17px;margin-right: 5px;" value="${shoppingCartGroup.id}" <#if shoppingCartGroup.selectStatus==1>checked="checked" data-mutex-check="true"</#if> <#if shoppingCartGroup.selectStatus==0> data-mutex-check="false"</#if> >
                                    <#--</label><span></span>-->
                                        <span class="title_hui">会员大礼包</span>
                                        <span class="info_hui">不需要</span>
                                     </#if>
                                </div>
                            </#if>
							<#list shoppingCartGroup.sorted as shoppingCartItem>
								<#if shoppingCartItem.itemType=3>
                                    <!--套餐商品-->
                                    <div class="taocanbox">
                                        <span class="new">套餐商品</span>
                                    </div>
                                        <div class="bodybox taocanspe">
                                            <!--列表-->
											<#list shoppingCartItem.subItemList as tcList>
                                                <ul>
                                                    <li class="lib1">
                                                        <div class="l-box fl">
                                                            <a href="/search/skuDetail/${tcList.id}.htm" target="_blank" title="${tcList.commonName }">
                                                                <img src="${productImageUrl}/ybm/product/min/${tcList.imageUrl }" alt="${tcList.commonName}" onerror="this.src='/static/images/default-big.png'"></a>
                                                            <!--标签-->
															<#if shoppingCartItem.item.valid=0>
                                                                <div class="bq-box">
                                                                    <img src="img/bq-qiangguang.png" alt="">
                                                                </div>
															</#if>
                                                            <#if tcList.blackSku=1>
                                                                <!--不参与返点提示-->
                                                                <#if (tcList.blackSkuText??) && (tcList.blackSkuText!="")>
                                                                    <div class="nofd">
                                                                        ${tcList.blackSkuText}
                                                                    </div>
                                                                </#if>
                                                            </#if>
                                                        </div>
                                                        <div class="r-box fr">
                                                            <div class="lib1-row1 text-overflow">
                                                                <a href="/search/skuDetail/${tcList.id}.htm" target="_blank" title="${tcList.commonName }">${tcList.sku.showName}</a>
                                                            </div>
                                                            <div class="lib1-row3">
                                                                <div class="row-biaoqian">
                                                                        <#if tcList.tagList ?? && (tcList.tagList?size >0) >
                                                                            <#list tcList.tagList as item >
                                                                                <#if (item_index < 3)>
                                                                                <span class="<#if item.uiType == 1>linqi</#if>
                                                                                <#if item.uiType == 2>quan</#if>
                                                                                <#if item.uiType == 3>manjian</#if>
                                                                                <#if item.uiType == 4>default</#if>
                                                                                <#if item.uiType == 5>yibao</#if>
                                                                                ">${item.name}</span>
                                                                                </#if>
                                                                            </#list>
                                                                        </#if>
                                                                </div>
                                                            </div>
                                                            <div class="lib1-row2 text-overflow">
                                                                <span class="title">规　　格：</span>
                                                                <span class="info">${tcList.sku.spec}</span>
                                                            </div>
                                                            <#--<div class="lib1-row3">
                                                                <span class="title">批准文号：</span>
                                                                <span class="info">${tcList.sku.approvalNumber}</span>
                                                            </div>-->
                                                            <div class="lib1-row4 text-overflow">
                                                                <span class="title">生产厂家：</span>
                                                                <span class="info">${tcList.sku.manufacturer}</span>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li class="li_mingxi">
                                                        <div class="mxrow1">
                                                            <span class="mxrow_tit">实付金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}realPayAmountK">￥${tcList.realPayAmount}</span>
                                                            <span class="mxrow_tit">优惠金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}discountAmountK">￥${tcList.discountAmount}</span>
                                                        </div>
                                                        <div class="mxrow2">
                                                            <span class="mxrow_tit">余额抵扣：</span><span class="mxrow_info" id="${tcList.uniqueKey}useBalanceAmountK">￥${tcList.useBalanceAmount}</span>
                                                            <span class="mxrow_tit">返点金额：</span><span class="mxrow_info" id="${tcList.uniqueKey}balanceAmountK">￥${tcList.balanceAmount}</span>
                                                        </div>
                                                        <div class="mxrow2">
                                                            <span class="mxrow_tit">实付价：</span><span class="mxrow_info ${tcList.uniqueKey}purchasePriceK">￥<#if 1 == tcList.sku.isGive >${tcList.sku.fob?string('0.00')}<#elseif tcList.purchasePrice ?? >${tcList.purchasePrice?string('0.00')}<#else>0.00</#if></span>
                                                            <span class="mxrow_tit">成本价：</span><span class="mxrow_info  ${tcList.uniqueKey}costPriceK">￥<#if 1 == tcList.sku.isGive >${tcList.sku.fob?string('0.00')}<#elseif tcList.costPrice ?? >${tcList.costPrice?string('0.00')}<#else>0.00</#if></span>
                                                        </div>
                                                    </li>
                                                    <li class="lib3">
                                                        <div class="zkj">￥${tcList.sku.fob}</div>
                                                        <div class="sjj"><span>￥${tcList.sku.retailPrice}</span></div>

                                                        <!--价格登录可见样式-->
                                                        <!--<div class="loginshow">价格登录可见</div>-->
                                                        <!--暂无购买权限样式-->
                                                        <!--<div class="notbug">暂无购买权限</div>-->
                                                    </li>
                                                    <li class="lib5">
                                                        <span>x${tcList.amount}</span>
                                                    </li>
                                                    <li class="lib4"></li>
                                                </ul>
											</#list>
                                            <!--套餐价格-->
                                            <div class="taocanxj">
                                                <div class="zkj">￥${shoppingCartItem.item.subtotal}</div>
                                            </div>
                                        </div>
								</#if>

								<#if shoppingCartItem.itemType!=3>
                                    <div class="bodybox <#if shoppingCartGroup.type==5>bigGift${shoppingCartGroup.id} </#if>">
                                        <!--列表-->
                                        <ul>
                                            <li class="lib1">
                                                <div class="l-box fl">
                                                    <a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm" target="_blank" title="${shoppingCartItem.item.sku.showName }">
                                                        <img src="${productImageUrl}/ybm/product/min/${shoppingCartItem.item.sku.imageUrl }" alt="${shoppingCartItem.item.sku.showName}" onerror="this.src='/static/images/default-big.png'">
                                                        <div class="bq-box">
															<#if shoppingCartItem.item.sku.status==2>
                                                                <img src="/static/images/product/bq-shouqing.png" alt="">
															</#if>
															<#if shoppingCartItem.item.sku.status==4 && shoppingCartItem.item.sku.isGive==0>
                                                                <img src="/static/images/product/bq-xiajia.png" alt="">
															</#if>
                                                        </div>
                                                        <#if shoppingCartItem.item.blackSku=1>
                                                            <!--不参与返点提示-->
                                                            <#if (shoppingCartItem.item.blackSkuText??) && (shoppingCartItem.item.blackSkuText!="")>
                                                                <div class="nofd">
                                                                     ${shoppingCartItem.item.blackSkuText}
                                                                </div>
                                                            </#if>
                                                        </#if>
                                                    </a>
                                                </div>
                                                <div class="r-box fr">
                                                    <div class="lib1-row1 text-overflow">
													<span>
														<a href="/search/skuDetail/${shoppingCartItem.item.sku.id}.htm" target="_blank" title="${shoppingCartItem.item.sku.showName }">
														  <#if shoppingCartItem.item.isShow806 || shoppingCartItem.item.gift>
														    <div class="bq806">
														        <img src="/static/images/bq806.png" alt="">
														    </div>
                                                         </#if>
                                                         <#if shoppingCartItem.item.agent == 1><span class="dujia">独家</span></#if>${shoppingCartItem.item.sku.showName }
														</a>
													</span>
                                                    </div>
                                                    <div class="lib1-row3">
                                                        <div class="row-biaoqian">
                                                            <#if shoppingCartItem.item.tagList ?? && (shoppingCartItem.item.tagList?size >0) >
                                                             <#list shoppingCartItem.item.tagList as item >
                                                                 <#if (item_index < 3)>
                                                                 <span class="<#if item.uiType == 1>linqi</#if>
                                                                 <#if item.uiType == 2>quan</#if>
                                                                 <#if item.uiType == 3>manjian</#if>
                                                                 <#if item.uiType == 4>default</#if>
                                                                 <#if item.uiType == 5>yibao</#if>
                                                                 ">${item.name}</span>
                                                                 </#if>
                                                             </#list>
                                                            </#if>
                                                        </div>
                                                    </div>
                                                    <div class="lib1-row5">
                                                        <div class="row-last">
                                                            <#if (shoppingCartItem.item.sku.suggestPrice ?? ) && (shoppingCartItem.item.sku.suggestPrice != '')>
                                                               <div class="kongxiao-box">
                                                                   <span class="s-kx">零售价</span><span class="jg">￥${shoppingCartItem.item.sku.suggestPrice}</span>
                                                               </div>
                                                            </#if>
                                                            <#if shoppingCartItem.item.sku.grossMargin ?? && shoppingCartItem.item.sku.grossMargin !=''>
                                                               <div class="maoli-box">
                                                                   <span class="s-ml">毛利</span><span class="jg">${shoppingCartItem.item.sku.grossMargin}</span>
                                                               </div>
                                                            </#if>
                                                        </div>
                                                    </div>
                                                    <div class="lib1-row2 text-overflow">
                                                        <span class="title">规　　格：</span>
                                                        <span class="info">${shoppingCartItem.item.sku.spec }</span>
                                                    </div>
                                                    <#--<div class="lib1-row3">
                                                        <span class="title">批准文号：</span>
                                                        <span class="info">${shoppingCartItem.item.sku.approvalNumber }</span>
                                                    </div>-->
                                                    <div class="lib1-row4 text-overflow">
                                                        <span class="title">生产厂家：</span>
                                                        <span class="info">${shoppingCartItem.item.sku.manufacturer }</span>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="li_mingxi">
                                                <div class="mxrow1">
                                                    <span class="mxrow_tit">实付金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}realPayAmountK">￥${shoppingCartItem.item.realPayAmount}</span>
                                                    <span class="mxrow_tit">优惠金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}discountAmountK">￥${shoppingCartItem.item.discountAmount}</span>
                                                </div>
                                                <div class="mxrow2">
                                                    <span class="mxrow_tit">余额抵扣：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}useBalanceAmountK">￥${shoppingCartItem.item.useBalanceAmount}</span>
                                                    <span class="mxrow_tit">返点金额：</span><span class="mxrow_info" id="${shoppingCartItem.item.uniqueKey}balanceAmountK">￥${shoppingCartItem.item.balanceAmount}</span>
                                                </div>
                                                <div class="mxrow2">
                                                    <span class="mxrow_tit">实付价：</span><span class="mxrow_info ${shoppingCartItem.item.uniqueKey}purchasePrice">￥<#if 1 == shoppingCartItem.item.sku.isGive >${shoppingCartItem.item.price?string('0.00')}<#elseif shoppingCartItem.item.purchasePrice ?? >${shoppingCartItem.item.purchasePrice?string('0.00')}<#else>0.00</#if></span>
                                                    <span class="mxrow_tit">成本价：</span><span class="mxrow_info  ${shoppingCartItem.item.uniqueKey}costPrice">￥<#if 1 == shoppingCartItem.item.sku.isGive >${shoppingCartItem.item.price?string('0.00')}<#elseif shoppingCartItem.item.costPrice ?? >${shoppingCartItem.item.costPrice?string('0.00')}<#else>0.00</#if></span>
                                                </div>
                                            </li>
                                            <li class="lib3">
                                                <div class="zkj">￥${shoppingCartItem.item.price }
                                                </div>
                                                <div class="sjj"><span>￥${shoppingCartItem.item.sku.retailPrice }</span></div>
                                                <!--价格登录可见样式-->
                                                <!--<div class="loginshow">价格登录可见</div>-->
                                                <!--暂无购买权限样式-->
                                                <!--<div class="notbug">暂无购买权限</div>-->
                                            </li>
                                            <li class="lib5">
                                                <span>x${shoppingCartItem.item.amount }</span>
                                            </li>
                                            <li class="lib4">
                                                <div class="zkj">￥${shoppingCartItem.item.subtotal }</div>
                                                <!-- <div class="sjj">
												<span>满减优惠￥</span><span>${shoppingCartItem.item.subtotal }</span>
											</div>
											<div class="sjj">
												<span>优惠券￥</span><span>${shoppingCartItem.item.subtotal }</span>
											</div> -->
                                            </li>
                                        </ul>
                                    </div>
								</#if>
                                <#--<#if shoppingCartItem_has_next!="true" &&(shoppingCartGroup.isThirdCompany ==1||(shoppingCartGroup.isThirdCompany ==0) && (shoppingCartGroup.isThirdCompanyLastFlag = 1))>-->
                                    <#--<!--总计&ndash;&gt;-->
                                    <#--<div class="totalbox clear">-->
                                    <#--&lt;#&ndash;<div class="lbox-total fl">&ndash;&gt;-->
                                        <#--&lt;#&ndash;<span class="zj">总计</span>&ndash;&gt;-->
                                        <#--&lt;#&ndash;<span>商品种类</span><span>${shoppingCartInfo.varietyNum }</span><span>种</span>&ndash;&gt;-->
                                        <#--&lt;#&ndash;<span class="spzj">商品总计：</span><span>${shoppingCartInfo.totalNum }</span>&ndash;&gt;-->
                                    <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                        <#--<div class="rbox-total fr">-->
                                            <#--<div class="spzjbox">-->
                                                <#--共<span class="zhongshu">${shoppingCartGroup.productVarietyNum }</span>种商品，总件数<span class="zongjianshu">${shoppingCartGroup.productTotalNum }</span>商品总计：￥<span id="total_price_span" <#if shoppingCartGroup.isThirdCompany ==0 >name="directTotalAmount"</#if>>${shoppingCartGroup.totalAmount?string('0.00') }</span>-->
                                            <#--</div>-->
                                            <#--<div class="fd-warp">-->
                                                <#--<!--中间竖线&ndash;&gt;-->
                                            <#--&lt;#&ndash;<div class="mid-line"></div>&ndash;&gt;-->
                                                 <#--<#if hasThirdCompany >-->
                                                    <#--<div class="right-fd-box">-->
                                                        <#--<div class="list-item">-->
                                                            <#--<span class="spec">运费:</span><span  class="red">+￥</span><span  class="red">${(shoppingCartGroup.freightAmount)!'0.00' }</span>-->
                                                        <#--</div>-->
                                                        <#--<div class="list-item">-->
                                                            <#--<span class="spec">满减:</span><span class="red">-￥</span><span class="red" id="wholeDiscountAmount">${(shoppingCartGroup.promoDiscountAmount)!'0.00' }</span>-->
                                                        <#--</div>-->
                                                        <#--<div class="list-item">-->
                                                            <#--<span class="spec">优惠券:</span><span class="red">-￥</span><span class="red" id="wholeVoucherAmount">${(shoppingCartGroup.voucherDiscountAmount)!'0.00' }</span>-->
                                                        <#--</div>-->
                                                        <#--<#if shoppingCartGroup.isThirdCompany ==0>-->
                                                          <#--<div class="list-item">-->
                                                              <#--<span class="spec">余额抵扣:</span><span class="red">-￥</span><span class="red " name="balancespan" id="balance_span">${(shoppingCartInfo.balanceAmount)!'0.00' }</span>-->
                                                          <#--</div>-->
                                                        <#--</#if>-->
                                                    <#--</div>-->
                                                <#--</#if>-->
                                            <#--</div>-->

                                        <#--<#if (shoppingCartInfo.rebate>0)>-->
                                        <#--&lt;#&ndash;<div class="fanli">&ndash;&gt;-->
                                        <#--&lt;#&ndash;<span class="spec">返利:</span><span>-￥</span><span id="rebate_span">${shoppingCartInfo.rebate?string('0.00') }</span>&ndash;&gt;-->
                                        <#--&lt;#&ndash;</div>&ndash;&gt;-->
                                        <#--</#if>-->
                                        <#--</div>-->
                                    <#--</div>-->
                                <#--</#if>-->
							</#list>
						</#list>
                        </div>
                        <!--收起-->
                        <a href="javaScript:void(0); " class="no-more">点击收起 <i class="sui-icon icon-tb-fold"></i> </a>
                    </div>


                    <!--销售协议 加noshow隐藏-->
				<#--<#if (orderQty = 0)>
                    <div class="wrapbox ">
                        <label class="checkbox-pretty inline all checked">
                            <input type="checkbox" checked="checked"><span>我已阅读并同意签订</span>
                        </label>
                        <a href="purchases_contract.html" target="_blank" class="fr">《药帮忙网上购销合同》</a>
                    </div>
				</#if>-->
                    <!--提示领取余额-->
				<#--<#if returnBalanceTips!''>-->
				<#--<div class="ts-getyue">-->
				<#--<span id="returnbalanceTipsSpan">-->
				<#--${returnBalanceTips}-->
				<#--</span>， <a href="/activity/activityCustom.htm?title=619活动说明&type=events-619#first" target="_blank">查看活动详情</a>-->
				<#--</div>-->
				<#--</#if>-->
                    <!--提交订单-->
                <#-- <div class="applybox">
                     <div class="acol5">
                         <span class="tinfo">应付金额：</span>
                         <span class="money">￥</span><span class="money" id="payablePrice_span">${shoppingCartInfo.payablePrice?string("0.00") }</span>
                     </div>
                     <div class="acol6">
                     <#if (orderQty = 0)>
                         <div class="wrapbox ">
                             <label class="checkbox-pretty inline all checked">
                                 <input type="checkbox" checked="checked"><span>我已阅读并同意签订</span>
                             </label>
                             <a href="purchases_contract.html" target="_blank" class="fr">《药帮忙网上购销合同》</a>
                         </div>
                     </#if>
                         <a href="javascript:void(0);" class="tjbtn" id="settle_btn">提交订单</a>
                         <div class="tishiwa">${returnBalanceTips}</div>
                     </div>
                 </div>-->
                    <!--优惠券-->
                    <div class="youhuiquan">
                        <div class="address-top-title">
                            <div class="yhq-l-box">
                                <a href="javascript:;" class="ky-yhq cur">可用优惠券</a>
                                <a href="javascript:;" class="bky-yhq ">不可用优惠券</a>
                            </div>
                            <div class="yhq-r-box">
                                <a href="javascript:;" class="xiala">
                                    <img src="/static/images/xiala.png" alt=""><span>查看全部优惠券</span>
                                </a>
                                <a href="javascript:;" class="shouqi">
                                    <img src="/static/images/xiala-sq.png" alt=""><span>收起优惠券</span>
                                </a>
                            </div>

                        </div>
                        <!--可用优惠券-->
                        <#assign availItemNum = 0 />
                        <ul class="yhq-common weishiyong ky">
                            <#if (shoppingCartInfo?? && shoppingCartInfo.availDjVoucherList ?? && shoppingCartInfo.availDjVoucherList?size>0)>
                                <#assign availItemNum = availItemNum + 1 />
                                <li <#if (shoppingCartInfo ?? && shoppingCartInfo.selectDjVoucherList ?? && shoppingCartInfo.selectDjVoucherList?size>0)>class="cur"</#if>>
                                    <input type="hidden" name="voucherId" value="1"/>
                                    <input type="hidden" name="voucherMoney" value="${shoppingCartInfo.availDjAmount }"/>
                                    <div class="yhq-lb">
                                        <div class="yhq-lb-top">
                                            <span class="fuhao">￥</span><span class="price">${shoppingCartInfo.availDjAmount}</span>
                                        </div>
                                        <div class="yhq-lb-foot">
                                            <#--满200元使用-->无门槛
                                        </div>
                                    </div>
                                    <div class="yhq-rb">
                                        <div class="yhq-rb-top">
                                            <span class="quan quan-die">自营叠加券</span><span class="info" title="${voucherDemo}">${voucherDemo}</span>
                                        </div>
                                        <div style="height:30px;overflow:hidden;"></div>
                                        <div class="yhq-rb-foot">
                                            <span>可与其他优惠券叠加</span>
                                            <a href="javascript:;" class="ck" style="float:right;margin-right:10px" data-toggle="modal"  data-keyboard="false" data-target="#yhqModal">查看</a>
                                        </div>
                                    </div>
                                    <div class="yhq-checkb">
                                        <label class="checkbox-pretty inline <#if (shoppingCartInfo ?? && shoppingCartInfo.selectDjVoucherList ?? && shoppingCartInfo.selectDjVoucherList?size>0)>checked</#if>" type="6">
                                            <input type="checkbox" ><span></span>
                                        </label>
                                    </div>
                                </li>
                            <#--<li class="coupon-list first <#if (shoppingCartInfo.selectDjVoucherList?size>0)>cur</#if>">
                                <input type="hidden" name="voucherId" value="1"/>
                                <input type="hidden" name="voucherMoney" value="${shoppingCartInfo.availDjAmount }"/>
                                <a href="javascript:void(0);" class="coupon-item clear">
                                    <div class="coupon-item-top clear">
                                        <div class="coupon-item-top-left">
                                            <div class="coupon-name">${voucherTitle}</div>
                                            <div class="coupon-time">${voucherDemo}</div>
                                        </div>
                                        <div class="coupon-item-top-right">
                                            <span class="rmb-icon">¥</span>${shoppingCartInfo.availDjAmount}
                                        </div>
                                    </div>
                                    <div class="coupon-item-bot clear">
                                        <div class="coupon-item-bot-left">全场商品使用</div>
                                        <!--<div class="coupon-item-bot-right">无门槛</div>&ndash;&gt;
                                        <div class="first-spe">
                                            <span class="wmk">无门槛</span>
                                            <span class="chakan" data-toggle="modal"  data-keyboard="false" data-target="#yhqModal">查看</span>
                                        </div>
                                    </div>
                                </a>
                                <div class="yhq-checkb">
                                    <label class="checkbox-pretty inline <#if (shoppingCartInfo.selectDjVoucherList?size>0)>checked</#if>" type="6">
                                        <input type="checkbox" ><span></span>
                                    </label>
                                </div>
                            </li>-->
                            </#if>
                            <#list shoppingCartInfo.availVoucherList as voucher>
                                <#assign availItemNum = availItemNum + 1 />
                                <li class="<#if (voucher.isUse==1)>cur </#if><#if voucher.isUse==0>ygq </#if><#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">
                                    <input type="hidden" name="voucherId" value="${voucher.id }"/>
                                    <input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>
                                    <div class="yhq-lb">
                                        <div class="yhq-lb-top">
                                            <#if voucher.voucherState==1>
                                                <span class="price">${voucher.discountRatio }</span>
                                                <span class="fuhao">折</span>
                                            <#elseif voucher.voucherUsageWay==1>
                                                <span class="fuhao">￥</span><span class="price">${voucher.sourceMoneyInVoucher }</span>
                                            <#else>
                                                <span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>
                                            </#if>
                                        </div>
                                        <div class="yhq-lb-foot">
                                            ${voucher.minMoneyToEnableDesc }
                                        </div>
                                        <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >
                                            <div class="yhq-lb-foot">
                                                ${voucher.maxMoneyInVoucherDesc }
                                            </div>
                                        </#if>
                                    </div>
                                    <div class="yhq-rb">
                                        <div class="yhq-rb-top">
                                            <#if voucher.voucherType == 2>
                                                <span class="quan">${voucher.voucherTypeDesc}</span>
                                            </#if>
                                            <#if voucher.voucherType == 1>
                                                <span class="quan quan-tong">${voucher.voucherTypeDesc}</span>
                                            </#if>
                                            <#if voucher.voucherType == 6>
                                                <span class="quan quan-die">${voucher.voucherTypeDesc}</span>
                                            </#if>
                                            <#if voucher.voucherType == 5>
                                                <span class="quan quan-xin">${voucher.voucherTypeDesc}</span>
                                            </#if>
                                            <span class="info" title="${voucher.voucherDesc }">${voucher.voucherDesc }</span>
                                            <#if voucher.voucherType==7><span class="tip">?</span></#if>
                                        </div>
                                        <div style="height:30px;overflow:hidden;"></div>
                                        <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
                                        <#--<div style="position: absolute; bottom: 20px;left:0px;">-->
                                        <#--<span class="lastJian" style="font-size: 12px;font-family: PingFangSC;font-weight: 400;color: #F04134;line-height: 17px;">${voucher.maxMoneyInVoucherDesc }</span>-->
                                        <#--</div>-->
                                        <#--</#if>-->
                                        <div class="yhq-rb-foot">
                                            <span>${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }</span>
                                        </div>
                                    </div>
                                    <div class="yhq-checkb">
                                        <label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>"">
                                        <input type="checkbox" <#if voucher.isUse==0>disabled=""</#if> ><span></span>
                                        </label>
                                    </div>
                                </li>
                            <#--<li class="coupon-list <#if voucher.isUse==1>cur</#if> <#if voucher.isUse==0>gray</#if> <#if availItemNum%3==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">
                                <input type="hidden" name="voucherId" value="${voucher.id }"/>
                                <input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>
                                <a href="javascript:void(0);" class="coupon-item clear">
                                    <div class="coupon-item-top clear">
                                        <div class="coupon-item-top-left">
                                            <div class="coupon-name">${voucher.voucherTypeDesc}</div>
                                            <div class="coupon-time">${voucher.validDate?string('yyyy-MM-dd') }-${voucher.expireDate?string('yyyy-MM-dd') }</div>
                                        </div>
                                        <div class="coupon-item-top-right">
                                            <span class="rmb-icon">¥</span>${voucher.moneyInVoucher }
                                        </div>
                                    </div>
                                    <div class="coupon-item-bot clear">
                                        <div class="coupon-item-bot-left">${voucher.voucherDesc }</div>
                                        <div class="coupon-item-bot-right">${voucher.minMoneyToEnableDesc }</div>
                                    </div>
                                </a>
                                <div class="yhq-checkb">
                                    <label class="checkbox-pretty inline <#if voucher.isUse==1>checked</#if>">
                                        <input type="checkbox" <#if voucher.isUse==0>disabled=""</#if>><span></span>
                                    </label>
                                </div>
                            </li>-->
                            </#list>
                        </ul>
                        <!--不可用优惠券-->
                        <ul class="yhq-common weishiyong bky">
                            <#assign unavailItemNum = 0 />
                            <#if (shoppingCartInfo ?? && shoppingCartInfo.unavailDjVoucherList ?? &&shoppingCartInfo.unavailDjVoucherList?size>0)>
                                <#assign unavailItemNum = unavailItemNum + 1 />
                                <li>
                                    <div class="yhq-lb">
                                        <div class="yhq-lb-top">
                                            <span class="fuhao">￥</span><span class="price">${shoppingCartInfo.unavailDjAmount}</span>
                                        </div>
                                        <div class="yhq-lb-foot">
                                            <#--满200元使用-->无门槛
                                        </div>
                                    </div>
                                    <div class="yhq-rb">
                                        <div class="yhq-rb-top">
                                            <span class="quan quan-die">自营叠加券</span><span class="info"></span>
                                        </div>
                                        <div style="height:30px;overflow:hidden;"></div>
                                        <div class="yhq-rb-foot">
                                            <span>可与其他优惠券叠加</span>
                                            <a href="javascript:;" class="ck" style="float:right;margin-right:10px" data-toggle="modal"  data-keyboard="false" data-target="#unyhqModal">查看</a>
                                        </div>
                                    </div>
                                </li>
                            <#--<li class="coupon-list first">
                                <a href="javascript:void(0);" class="coupon-item clear">
                                    <div class="coupon-item-top clear">
                                        <div class="coupon-item-top-left">
                                            <div class="coupon-name">${voucherTitle}</div>
                                            <div class="coupon-time">${voucherDemo}</div>
                                        </div>
                                        <div class="coupon-item-top-right">
                                            <span class="rmb-icon">¥</span>${shoppingCartInfo.unavailDjAmount}
                                        </div>
                                    </div>
                                    <div class="coupon-item-bot clear">
                                        <div class="coupon-item-bot-left">全场商品使用</div>
                                        <!--<div class="coupon-item-bot-right">无门槛</div>&ndash;&gt;
                                        <div class="first-spe">
                                            <span class="wmk">无门槛</span>
                                            <span class="chakan" data-toggle="modal"  data-keyboard="false" data-target="#unyhqModal">查看</span>
                                        </div>
                                    </div>
                                </a>
                            </li>-->
                            </#if>
                            <#list shoppingCartInfo.unavailVoucherList as voucher>
                                <#assign unavailItemNum = unavailItemNum + 1 />
                                <li class="<#if availItemNum%2==0>three-3n</#if>" type="${voucher.voucherType}-${voucher.manufacturerId}">
                                    <input type="hidden" name="voucherId" value="${voucher.id }"/>
                                    <input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>
                                    <div class="yhq-lb">
                                        <div class="yhq-lb-top">
                                            <#if voucher.voucherState==1>
                                                <span class="price">${voucher.discountRatio }</span>
                                                <span class="fuhao">折</span>
                                            <#else>
                                                <span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>
                                            </#if>
                                        </div>
                                        <div class="yhq-lb-foot">
                                            ${voucher.minMoneyToEnableDesc }
                                        </div>
                                        <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >
                                            <div class="yhq-lb-foot">
                                                ${voucher.maxMoneyInVoucherDesc }
                                            </div>
                                        </#if>
                                    </div>
                                    <div class="yhq-rb">
                                        <div class="yhq-rb-top">
                                            <#if voucher.voucherType == 2>
                                                <span class="quan">${voucher.voucherTypeDesc}</span>
                                            </#if>
                                            <#if voucher.voucherType == 1>
                                                <span class="quan quan-tong">${voucher.voucherTypeDesc}</span>
                                            </#if>
                                            <#if voucher.voucherType == 6>
                                                <span class="quan quan-die">${voucher.voucherTypeDesc}</span>
                                            </#if>
                                            <#if voucher.voucherType == 5>
                                                <span class="quan quan-xin">${voucher.voucherTypeDesc}</span>
                                            </#if>
                                            <span class="info" title="${voucher.voucherDesc }">${voucher.voucherDesc }</span>
                                        </div>
                                        <div style="height:30px;overflow:hidden;"></div>
                                        <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="" ) >-->
                                        <#--<div style="position: absolute; bottom: 20px;left:0px;">-->
                                        <#--<span class="lastJian" style="font-size: 12px;font-family: PingFangSC;font-weight: 400;color: #F04134;line-height: 17px;">${voucher.maxMoneyInVoucherDesc }</span>-->
                                        <#--</div>-->
                                        <#--</#if>-->
                                        <div class="yhq-rb-foot">
                                            <span>${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }</span>
                                        </div>
                                    </div>
                                </li>
                            <#--<li class="coupon-list <#if unavailItemNum%3==0>three-3n</#if>">
                                <a href="javascript:void(0);" class="coupon-item clear">
                                    <div class="coupon-item-top clear">
                                        <div class="coupon-item-top-left">
                                            <div class="coupon-name">${voucher.voucherTypeDesc}</div>
                                            <div class="coupon-time">${voucher.validDate?string('yyyy-MM-dd') }-${voucher.expireDate?string('yyyy-MM-dd') }</div>
                                        </div>
                                        <div class="coupon-item-top-right">
                                            <span class="rmb-icon">¥</span>${voucher.moneyInVoucher }
                                        </div>
                                    </div>
                                    <div class="coupon-item-bot clear">
                                        <div class="coupon-item-bot-left">${voucher.voucherDesc }</div>
                                        <div class="coupon-item-bot-right">${voucher.minMoneyToEnableDesc }</div>
                                    </div>
                                </a>
                            </li>-->
                            </#list>
                        </ul>
                    </div>

                    <#if (shoppingCartInfo.totalBalanceAmt>0)>
                        <!--我的余额-->
                        <div class="my-balance">
                            <div class="address-top-title">我的余额</div>
                            <div class="infobox">
                                <div class="infobox-top">
                                    本次使用<input class="inputprice" type="text" id="useBalanceAmount" data-placement="top" name="balanceAmount" onkeyup="clearNoNum(this)" onblur="getTotalGoodsMoney(3)" value="${shoppingCartInfo.balanceAmount }"/>元
                                    <span class="dikou">抵扣</span>
                                    <span class="redcolor">￥</span><span class="redcolor resultprice" id="useBalanceAmountHtml">${shoppingCartInfo.balanceAmount }</span> 元
                                </div>
                                <div class="infobox-bot">
                                    账户可用余额共计为<span class="redcolor allprice" >${shoppingCartInfo.totalBalanceAmt?string('0.00')}</span>元

                                    <span class="tishi">(扣减余额后最低起运价仍为${startingPriceText}，因此本次最多可使用<span id="yuetishi">${shoppingCartInfo.availBalanceAmt}</span>元)</span>

                                    <a href="/merchant/center/balanceJournal/helpBalance.html" class="rightbox fr" target="_blank"><i class="sui-icon icon-tb-question"></i>了解什么是余额</a>
                                </div>
                            </div>
                        </div>
                    </#if>
                    <!--提交订单-->
                    <div class="applybox">
                        <div class="js-total ">
                            <div class="fd-warp right-fd-box fr">
                                <div class="list-item">
                                    <span class="spec">商品总金额:</span><span>￥</span><span name="price">${shoppingCartInfo.price?string('0.00') }</span>
                                </div>
                                <div class="list-item">
                                    <span class="spec">满减总额:</span><span class="red">-￥</span><span class="red" name="promoDiscountAmount">${(shoppingCartInfo.promoDiscountAmount)!'0.00' }</span>
                                </div>
                                <div class="list-item">
                                    <span class="spec">优惠券总额:</span><span class="red">-￥</span><span class="red" id="voucherDiscountAmount" name="voucherDiscountAmount">${(shoppingCartInfo.voucherDiscountAmount)!'0.00' }</span>
                                </div>
                                <div class="list-item">
                                    <span class="spec">运费总额:</span><span class="red">+￥</span><span class="red"  name="totalFreightAmount" >${(shoppingCartInfo.totalFreightAmount)!'0.00' }</span>
                                </div>
                                <div class="list-item">
                                    <span class="spec">余额抵扣:</span><span class="red">-￥</span><span class="red " name="balancespan"  id="balance_span">${(shoppingCartInfo.balanceAmount)!'0.00' }</span>
                                </div>

                                <#if shoppingCartInfo.fixedPriceAmount?? && shoppingCartInfo.fixedPriceAmount gt 0 >
                                 <div class="list-item">
                                     <span class="spec">一口价优惠:</span><span class="red">-￥</span><span class="red " name="fixedpricespan"  id="fixed_price_span">${(shoppingCartInfo.fixedPriceAmount)!'0.00' }</span>
                                 </div>
                                </#if>


                            </div>
                        </div>
                        <div class="js-order">
                            <div class="acol6">
                                <div class="wrapbox ">
                                    <p style="padding-top: 10px;">应付金额: <span class="money"  id="payablePrice_span">￥${shoppingCartInfo.payablePrice?string("0.00") }</span> </p>
                                    <p><span  name="rebate_span">${returnBalanceTips}</span></p>
                                    <p style="display: none;"><label class="isdefaultSec checkbox-pretty inline all checked">
                                        <input type="checkbox" checked="checked"><span>我已阅读并同意签订</span>
                                    </label><a href="purchases_contract.html"  target="_blank"  class="fr">《药帮忙网上购销合同》    </a>
                                    </p>
                                </div>
                                <a href="javascript:void(0);" class="turn-down" id="trunDownBtn" data-purchaseno=${purchaseNo}>驳回</a>
                                <a href="javascript:void(0);"  class="settle-btn-css" id="settle_btn">提交订单</a>
                            </div>
                        </div>
                    </div>
             </div>
         </div>
         <!--主体部分结束-->

			<!--底部导航区域开始-->
			<div class="footer" id="footer">
				<#include "/common/footer.ftl" />
			</div>
			<!--底部导航区域结束-->


		</div>

        <!--删除弹窗-->
        <div id="delModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 id="myModalLabel" class="modal-title">删除</h4>
                    </div>
                    <div class="modal-body">
                        <div class="scbox">
                            收货人地址将被删除，你确定吗？
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large" >确定</button>
                        <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                    </div>
                </div>
            </div>
        </div>

		<!-- 新增收货地址-->
		<#--<div id="addModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
						<h4 id="myModalLabel" class="modal-title">新增配货地址</h4>
					</div>
					<div class="modal-body">
						<div class="add-address-modal">
							<form class="sui-form form-horizontal sui-validate" onsubmit="return doAddAddress()">
								<div class="control-group">
									<label for="name" class="control-label"><span class="sui-text-danger">*</span>姓名：</label>
									<div class="controls">
										<input type="text" id="name" placeholder="" data-rules="required" name="contactor">
									</div>
								</div>
								<div class="control-group">
									<label for="mobile" class="control-label"><span class="sui-text-danger">*</span>手机号码：</label>
									<div class="controls">
										<input type="text" id="mobile" placeholder="" data-rules="required|mobile" name="mobile">
									</div>
								</div>
								<div class="control-group">
									<label for="mobile" class="control-label"><span class="sui-text-danger">*</span>收货地址：</label>
									<div class="controls">
										<div id="city_4">
											<select class="prov" name="province"></select>
											<select class="city" disabled="disabled" name="city"></select>
											<select class="dist" disabled="disabled" name="district"></select>
										</div>
										<div class="detailaddress">
											<input type="text" class="detailinp" name="address" placeholder="详细地址" data-rules="required|minlength=2|maxlength=100">
										</div>
									</div>
								</div>
								<div class="control-group">
									<label for="mobile" class="control-label"></label>
									<div class="controls">
										<label class="checkbox-pretty inline ">
											<input type="checkbox" name="isdefault"><span>设为默认地址</span>
										</label>
									</div>
								</div>
								<div class="modal-bot">
									<button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
									<button type="submit" class="sui-btn btn-primary btn-large">保存</button>
								</div>
							</form>
						</div>
					</div>

				</div>
			</div>
		</div>-->
		<!--编辑收货地址-->
		<div id="editModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
						<h4 id="myModalLabel" class="modal-title">修改配送信息</h4>
					</div>
					<div class="modal-body">
						<div class="add-address-modal">
							<form class="sui-form form-horizontal sui-validate" onsubmit="return doEditAddress()">
								<input type="hidden" name="id"/>
								<div class="control-group">
									<label for="contactor" class="control-label"><span class="sui-text-danger">*</span>姓名：</label>
									<div class="controls">
										<input type="text" id="contactor" placeholder="" data-rules="required|minlength_xm=2|maxlength_xm=8" maxlength="8" name="contactor">
									</div>
								</div>
								<div class="control-group">
									<label for="mobile" class="control-label"><span class="sui-text-danger">*</span>手机号码：</label>
									<div class="controls">
										<input type="text" id="mobile" placeholder="" data-rules="required|mobile" maxlength="11" name="mobile" />
									</div>
								</div>
                                <div class="control-group">
                                    <label for="mobile" class="control-label">收货地址：</label>
                                    <div class="controls">
                                        <div class="adres-text" name="fullAddress"></div>

                                    </div>
                                </div>

                                <#--<div class="control-group">-->
                                    <#--<label for="mobile" class="control-label">地址备注：</label>-->
                                    <#--<div class="controls">-->
                                        <#--<div class="detailaddress">-->
											<#--<#if (shippingAddress.auditState == 2 || shippingAddress.auditState == 3)>-->
                                                <#--${shippingAddress.remark}-->
											<#--</#if>-->
											<#--<#if (shippingAddress.auditState != 2 && shippingAddress.auditState != 3)>-->
                                                <#--<input type="text" maxlength="50" class="detailinp" name="remark" placeholder="若以上gsp地址不能被识别，请在此输入框中填写gsp地址的街道和门牌号">-->
											<#--</#if>-->
                                        <#--</div>-->
                                    <#--</div>-->
                                <#--</div>-->
                                <!--地址备注提示-->
                                <div class="control-group" style="margin-top: -22px;">
                                    <label for="mobile" class="control-label"></label>
                                    <div class="controls">
                                        <div class="addressTips"></div>
                                        <div class="is_default" style="margin-left: 23px;margin-top:15px;">
                                            <label id="isdefault" class="isdefaultSec checkbox-pretty inline ">
                                                <input type="checkbox" name="isdefault"><span>设置为默认收货地址</span>
                                            </label>
                                        </div>
                                        <div class="default_address">
                                                <span>默认收货地址</span>
                                        </div>
                                    </div>
                                </div>
								<#--<div class="control-group">
									<label for="mobile" class="control-label"><span class="sui-text-danger">*</span>收货地址：</label>
									<div class="controls">
										<div id="city_5">
											<select class="prov" name="province"></select>
											<select class="city" disabled="disabled" name="city"></select>
											<select class="dist" disabled="disabled" name="district"></select>
										</div>
										<div class="detailaddress">
											<input type="text" class="detailinp" name="address" placeholder="详细地址" data-rules="required|minlength=2|maxlength=100">
										</div>
									</div>
								</div>
								<div class="control-group">
									<label for="mobile" class="control-label"></label>
									<div class="controls">
										<label class="checkbox-pretty inline ">
											<input type="checkbox" name="isdefault"><span>设为默认地址</span>
										</label>
									</div>
								</div>-->
								<div class="modal-bot">
									<button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
									<button type="submit" class="sui-btn btn-primary btn-large">保存</button>
								</div>
							</form>
						</div>
					</div>

				</div>
			</div>
		</div>
		<!--设为默认地址弹窗-->
        <div id="editDefaultModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 id="myModalLabel" class="modal-title">设为默认地址</h4>
                    </div>
                    <div class="modal-body">
                        <div class="scbox">
                           	 您确认将该地址信息设为默认地址吗?
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-ok="modal" class="sui-btn btn-primary btn-large" >确定</button>
                        <button type="button" data-dismiss="modal" class="sui-btn btn-default btn-large">取消</button>
                    </div>
                </div>
            </div>
        </div>
        <!--查看优惠券弹窗-->
        <div id="yhqModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 id="myModalLabel" class="modal-title">叠加优惠券</h4>
                    </div>
                    <div class="modal-body">
                        <ul class="yhq-common weishiyong">
                            <#list shoppingCartInfo.availDjVoucherList as voucher>
                                <li>
                                    <input type="hidden" name="voucherId" value="${voucher.id }"/>
                                    <input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>
                                    <div class="yhq-lb">
                                        <div class="yhq-lb-top">
                                            <#if voucher.voucherState==1>
                                                <span class="price">${voucher.discountRatio }</span>
                                                <span class="fuhao">折</span>
                                            <#else>
                                                <span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>
                                            </#if>
                                        </div>
                                        <div class="yhq-lb-foot">
                                            ${voucher.minMoneyToEnableDesc }
                                        </div>
                                        <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                            <div class="yhq-lb-foot">
                                                ${voucher.maxMoneyInVoucherDesc }
                                            </div>
                                        </#if>
                                    </div>
                                    <div class="yhq-rb">
                                        <div class="yhq-rb-top">
                                            <span class="quan quan-die">${voucher.voucherTypeDesc}</span><span class="info">${voucher.voucherDesc }</span>
                                        </div>
                                        <div style="height:30px;overflow:hidden;"></div>
                                        <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucher?? && voucher.maxMoneyInVoucher!=null && voucher.maxMoneyInVoucher!="" && voucher.maxMoneyInVoucher>0 ) >-->
                                            <#--<div style="position: absolute; bottom: 20px;left:0px;">-->
                                                <#--<span class="lastJian" style="font-size: 12px;font-family: PingFangSC;font-weight: 400;color: #F04134;line-height: 17px;">${voucher.maxMoneyInVoucherDesc }</span>-->
                                            <#--</div>-->
                                        <#--</#if>-->
                                        <div class="yhq-rb-foot" style="position:absolute;bottom:10px;">
                                            <span>${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }</span>
                                        </div>
                                    </div>
                                </li>
                            <#--<li class="coupon-list">
                                <input type="hidden" name="voucherId" value="${voucher.id }"/>
                                <input type="hidden" name="voucherMoney" value="${voucher.moneyInVoucher }"/>
                                <a href="javascript:void(0);" class="coupon-item clear">
                                    <div class="coupon-item-top clear">
                                        <div class="coupon-item-top-left">
                                            <div class="coupon-name">${voucher.voucherTypeDesc}</div>
                                            <div class="coupon-time">${voucher.validDate?string('yyyy-MM-dd') }-${voucher.expireDate?string('yyyy-MM-dd') }</div>
                                        </div>
                                        <div class="coupon-item-top-right">
                                            <span class="rmb-icon">¥</span>${voucher.moneyInVoucher }
                                        </div>
                                    </div>
                                    <div class="coupon-item-bot clear">
                                        <div class="coupon-item-bot-left">${voucher.voucherDesc }</div>
                                        <div class="coupon-item-bot-right">${voucher.minMoneyToEnableDesc }</div>
                                    </div>
                                </a>
                            </li>-->
                            </#list>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div id="unyhqModal" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
                        <h4 id="myModalLabel" class="modal-title">叠加优惠券</h4>
                    </div>
                    <div class="modal-body">
                        <ul class="yhq-common weishiyong">
                            <#list shoppingCartInfo.unavailDjVoucherList as voucher>
                                <li>
                                    <div class="yhq-lb">
                                        <div class="yhq-lb-top">
                                            <#if voucher.voucherState==1>
                                                <span class="price">${voucher.discountRatio }</span>
                                                <span class="fuhao">折</span>
                                            <#else>
                                                <span class="fuhao">￥</span><span class="price">${voucher.moneyInVoucher }</span>
                                            </#if>
                                        </div>
                                        <div class="yhq-lb-foot">
                                            ${voucher.minMoneyToEnableDesc }
                                        </div>
                                        <#if (voucher.maxMoneyInVoucherDesc?? && voucher.maxMoneyInVoucherDesc!=null && voucher.maxMoneyInVoucherDesc!="") >
                                            <div class="yhq-lb-foot">
                                                ${voucher.maxMoneyInVoucherDesc }
                                            </div>
                                        </#if>
                                    </div>
                                    <div class="yhq-rb">
                                        <div class="yhq-rb-top">
                                            <span class="quan quan-die">${voucher.voucherTypeDesc}</span><span class="info">${voucher.voucherDesc }</span>
                                        </div>
                                        <div style="height:30px;overflow:hidden;"></div>
                                        <#--<#if (voucher.voucherUsageWay ==1 && voucher.maxMoneyInVoucher?? && voucher.maxMoneyInVoucher!=null && voucher.maxMoneyInVoucher!="" && voucher.maxMoneyInVoucher>0 ) >-->
                                            <#--<div style="position: absolute; bottom: 20px;left:0px;">-->
                                                <#--<span class="lastJian" style="font-size: 12px;font-family: PingFangSC;font-weight: 400;color: #F04134;line-height: 17px;">${voucher.maxMoneyInVoucherDesc }</span>-->
                                            <#--</div>-->
                                        <#--</#if>-->
                                        <div class="yhq-rb-foot" style="position:absolute;bottom:10px;">
                                            <span>${voucher.validDate?string('yyyy.MM.dd') }-${voucher.expireDate?string('yyyy.MM.dd') }</span>
                                        </div>
                                    </div>
                                </li>
                                <#--<li class="coupon-list">
                                    <a href="javascript:void(0);" class="coupon-item clear">
                                        <div class="coupon-item-top clear">
                                            <div class="coupon-item-top-left">
                                                <div class="coupon-name">${voucher.voucherTypeDesc}</div>
                                                <div class="coupon-time">${voucher.validDate?string('yyyy-MM-dd') }-${voucher.expireDate?string('yyyy-MM-dd') }</div>
                                            </div>
                                            <div class="coupon-item-top-right">
                                                <span class="rmb-icon">¥</span>${voucher.moneyInVoucher }
                                            </div>
                                        </div>
                                        <div class="coupon-item-bot clear">
                                            <div class="coupon-item-bot-left">${voucher.voucherDesc }</div>
                                            <div class="coupon-item-bot-right">${voucher.minMoneyToEnableDesc }</div>
                                        </div>
                                    </a>
                                </li>-->
                            </#list>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!--发票须知弹窗-->
		<div id="fpxzTc" tabindex="-1" role="dialog" data-hasfoot="false" class="sui-modal hide fade">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" data-dismiss="modal" aria-hidden="true" class="sui-close">×</button>
						<h4  class="modal-title">发票须知</h4>
					</div>
					<div class="modal-body">
						<div class="xzmain">
							<p class="xz-title">1.电子普通发票</p>
							<p class="xz-info">电子发票是税局认可的有效收付款凭证，其法律效力、基本用途及使用规范等同于纸质发票，可作为用户报销、维权的有效凭据。详见《国家税务总局公告2015年第84号》；</p>
							<p class="xz-info">如需纸质发票，可自行下载打印。</p>

							<p class="xz-title">2.增值税专用发票</p>
							<p class="xz-info">纸质增值税专用发票，包括纸质版和电子版，不仅具有商事凭证的作用，还可以进行进项抵扣。开具增值税专用发票需提供药店完整的开票信息，详询客服400-0505-111；</p>
							<p class="xz-info">我司依法开具发票，请您依法使用。</p>

							<p class="xz-title">3.发票金额</p>
							<p class="xz-info">发票金额为实付金额，不包括优惠金额。</p>

							<p class="xz-title">4. 电子普通发票开票时间</p>
							<p class="xz-info">电子普通发票：确认收货后24小时内生成，您可以在个人中心--我的订单--订单详情中，查看下载（APP端只支持查看，PC端支持查看、下载、打印）。</p>

							<p class="xz-title">5.已开具电子普通发票，申请退货，发票怎么办</p>
							<p class="xz-info">部分退货：商品退回后，我司会把原电子发票冲红，开具一张新的发票，请您留意订单详情中电子发票的变更；</p>
							<p class="xz-info">整单退货：商品退回后，我司会把原电子发票冲红，订单中的电子发票无法再次查看下载。</p>

							<p class="xz-title">6.已开具增值税专用发票，申请退货，发票怎么办</p>
							<p class="xz-info">部分退货或者整单退货，纸质发票都必须随商品一起退回。</p>
						</div>
					</div>

				</div>
			</div>
		</div>

        <div style="display: none" id="voucherUnavailableDialog">
            ${voucherText}
        </div>

		<#if shippingAddress.auditState==0>
		<script>
            $.alert({
                title: '温馨提示：',
                body: '为严格执行《药品管理法》及《药品经营质量管理规范》的相关规定，收货地址将默认为许可证注册地址或仓库地址，并不能随意修改。如该地址无法被快递员识别请在收货地址右方点击修改并在输入框内加以描述，如有其它疑问您还可以联系官方客服进行咨询！', //必填
                okBtn : '我知道啦'
            })
		</script>
		</#if>
        <div class="ti_box">
            <input type="hidden" value="" id="bigPackage">
            <input type="hidden" value="" id="bigPackageTotalAmount">
            <div class="ti_shi">
                <div class="shi_tou">
                    <i class="sui-icon icon-tb-infofill"></i>
                    <span>提示</span>
                    <i class="sui-icon icon-tb-close"></i>
                </div>
                <div class="shi_xia">
                    <p>确定不需要后，您将无法获取该礼包</p>
                </div>
                <div class="shi_que">
                    确 认
                </div>
                <div class="shi_qu">
                    取 消
                </div>
            </div>
        </div>

        <div class="ti_box_v">
            <div class="ti_shi_v">
                <div class="shi_tou_v">
                    <i class="sui-icon_v icon-tb-infofill_v"></i>
                    <span>提示</span>
                </div>
                <div class="shi_xia_v">
                    <p>您有优惠力度更高的优惠券，当前订单是否使用？</p>
                </div>
                <div class="shi_que_v">
                    确 认
                </div>
                <div class="shi_qu_v">
                    取 消
                </div>
            </div>
        </div>
	</body>
</html>
