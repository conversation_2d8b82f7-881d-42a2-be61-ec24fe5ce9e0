package com.xyy.ec.api.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.xyy.ec.base.exception.BusiCommonException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.product.business.ecp.csufilter.dto.FilterResultForSearchDTO;
import com.xyy.ec.product.business.ecp.out.search.api.ProductForSearchApi;
import com.xyy.ec.product.business.ecp.out.shop.ProductForShopApi;
import com.xyy.ec.product.business.ecp.out.shop.dto.FilterResultDTO;
import com.xyy.ec.search.engine.enums.CsuVisible;
import com.xyy.ec.shop.server.business.enums.ShopApiResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther:
 * @Date: 2020/4/21 19:56
 * @Description:copy的店铺的代码...
 */
@Slf4j
@Service
public class ProductService {
   @Reference(version = "1.0.0")
   private ProductForShopApi productForShopApi;
   @Reference(version = "1.0.0")
   private ProductForSearchApi productForSearchApi;
   public List<Long> controlFilterCsuIdsForIsNotPurchase(Long merchantId, String branchCode, List<Long> csuIds) {
      long start = System.currentTimeMillis();
      if (CollectionUtils.isEmpty(csuIds)) {
         return Lists.newArrayList();
      }
      List<Long> result;
      int controlFilterCsuIdsMaxSize = 200;
      // 分组
      List<List<Long>> parts = Lists.partition(csuIds, controlFilterCsuIdsMaxSize);
      if (parts.size() == 1) {
         // 仅仅1组，串行
         List<Long> sourceCsuIds = controlFilterCsuIdsForIsNotPurchaseSynchronously(merchantId, branchCode, csuIds);
         result = doSortCsuIds(csuIds, sourceCsuIds);
      } else {
         // 串行
         List<Long> allControlFilterCsuIds = Lists.newLinkedList();
         List<Long> partControlFilterCsuIds;
         for (List<Long> part : parts) {
            partControlFilterCsuIds = controlFilterCsuIdsForIsNotPurchaseSynchronously(merchantId, branchCode, part);
            allControlFilterCsuIds.addAll(partControlFilterCsuIds);
         }
         result = doSortCsuIds(csuIds, allControlFilterCsuIds);
      }
      return result;
   }
   public List<Long> findVisibleListForRecommend(List<Long> skuIdList, String branchCode, Long merchantId) {
      return filterListForRecommend(skuIdList, branchCode, merchantId, Arrays.asList(CsuVisible.ORDER.getValue()));
   }

   public List<Long> filterListForRecommend(List<Long> skuIdList, String branchCode, Long merchantId, List<Integer> visibleList) {
      if (CollectionUtil.isEmpty(skuIdList) || StringUtils.isEmpty(branchCode) || Objects.isNull(merchantId)) {
         log.error("调用推荐控销失败，无效的参数 branchCode : {}, merchantId : {}，skuIdList : {}", branchCode, merchantId, skuIdList);
         return skuIdList;
      }
      try {
         log.info("调用推荐控销过滤，merchantId : {}, branchCode : {}, visibleList : {}, skuIdList : {}", merchantId, branchCode, visibleList, skuIdList);
         //查询商品过滤状态
         ApiRPCResult apiRPCResult = productForSearchApi.filterSkuIdListForRecommend(skuIdList, branchCode, merchantId);
         if (apiRPCResult.isFail()) {
            log.error("调用推荐控销过滤接口异常,商品未能被过滤, branchCode : {}, merchantId : {}, skuIdList : {}, errMsg : {}", branchCode, merchantId, skuIdList, apiRPCResult.getErrMsg());
            return skuIdList;
         }
         List<FilterResultForSearchDTO> list = (List<FilterResultForSearchDTO>) apiRPCResult.getData();
         //转换成map
         Map<Long, CsuVisible> visibleMap = list.stream().collect(Collectors.toMap(FilterResultForSearchDTO::getCsuId, f -> CsuVisible.getByValue(f.getControlStatus())));

         //控销后商品集合
         List<Long> visibleSkuList = visibleMap.entrySet().stream().filter(map -> visibleList.contains(map.getValue().getValue())).map(map -> map.getKey()).collect(Collectors.toList());

         //保证顺序
         List<Long> orderVisibleSkuList = skuIdList.stream().filter(skuId -> visibleSkuList.contains(skuId)).collect(Collectors.toList());
         log.info("调用推荐控销，branchCode : {}, merchantId : {}, 控销数量 : {}, 控销后数量 : {}, skuIdList : {}, orderVisibleSkuList : {}", branchCode, merchantId, skuIdList.size(), visibleSkuList.size(), skuIdList, orderVisibleSkuList);
         return orderVisibleSkuList;
      } catch (Exception ex) {
         log.error("调用推荐控销过滤异常, branchCode : {}, merchantId : {}, visibleList : {}, skuIdList : {}, ", branchCode, merchantId, visibleList, skuIdList);
      }
      return skuIdList;
   }

   private List<ProductDTO> doSortProducts(List<Long> sortedCsuIds, List<ProductDTO> sourceProducts) {
      if (CollectionUtils.isEmpty(sortedCsuIds) || CollectionUtils.isEmpty(sourceProducts)) {
         return Lists.newArrayList();
      }
      Map<Long, ProductDTO> productMap = sourceProducts.stream().filter(item -> item != null)
              .collect(Collectors.toMap(ProductDTO::getId, item -> item));
      return sortedCsuIds.stream().filter(item -> item != null && productMap.containsKey(item))
              .map(item -> productMap.get(item)).collect(Collectors.toList());
   }
   private List<Long> doSortCsuIds(List<Long> sortedCsuIds, List<Long> sourceCsuIds) {
      if (CollectionUtils.isEmpty(sortedCsuIds) || CollectionUtils.isEmpty(sourceCsuIds)) {
         return Lists.newArrayList();
      }
      Set<Long> sourceCsuIdSet = new HashSet<>(sourceCsuIds);
      return sortedCsuIds.stream().filter(item -> item != null && sourceCsuIdSet.contains(item)).collect(Collectors.toList());
   }
   private List<Long> controlFilterCsuIdsForIsNotPurchaseSynchronously(Long merchantId, String branchCode, List<Long> csuIds) {
      long start = System.currentTimeMillis();
      ApiRPCResult<List<FilterResultDTO>> apiRPCResult = productForShopApi.filterSkuIdList(csuIds, branchCode, merchantId);
      if (!apiRPCResult.isSuccess()) {
         String message = MessageFormat.format("单次，单线程；merchantId：{0}，branchCode：{1}，csuIds：{2}",
                 merchantId, branchCode, JSONArray.toJSONString(csuIds));
         throw new BusiCommonException(ShopApiResultCodeEnum.CONSOLE_FILTER_CSU_ERROR.getCode(),message);
      }
      List<FilterResultDTO> filterResultDTOS = apiRPCResult.getData();
      if (CollectionUtils.isEmpty(filterResultDTOS)) {
         return Lists.newArrayList();
      }
      return controlFilterCsuIdsForIsNotPurchase(filterResultDTOS);
   }
   private List<Long> controlFilterCsuIdsForIsNotPurchase(List<FilterResultDTO> filterResultForSearchDTOS) {
      if (CollectionUtils.isEmpty(filterResultForSearchDTOS)) {
         return Lists.newArrayList();
      }
      return filterResultForSearchDTOS.stream().filter(item -> item != null && item.getControlStatus() != null && !item.getControlStatus().equals(2))
              .map(FilterResultDTO::getCsuId).collect(Collectors.toList());
   }

   public List<ProductDTO> fillProductInfo(List<Long> csuIds, Long merchantId, String branchCode) {
      long start = System.currentTimeMillis();
      if (CollectionUtils.isEmpty(csuIds)) {
         return Lists.newArrayList();
      }
      log.info("查询店铺商品信息】填充商品信息。参数信息，csuIds：{}, merchantId:{}, branchCode:{}", csuIds, merchantId, branchCode);
      ApiRPCResult<List<ProductDTO>> apiRPCResult = productForShopApi.findProductInfoBySkuIdList(csuIds, branchCode, merchantId);
      if (!apiRPCResult.isSuccess()) {
         String message = MessageFormat.format("请求入参，merchantId：{0}，branchCode：{1}，csuIds：{2}",
                 merchantId, branchCode, JSONArray.toJSONString(csuIds));
         throw new BusiCommonException(ShopApiResultCodeEnum.FILL_PRODUCT_INFO_ERROR.getCode(),message);
      }
      List<ProductDTO> sourceProducts = apiRPCResult.getData();
      List<ProductDTO> productDTOS = doSortProducts(csuIds, sourceProducts);
      return productDTOS;
   }
}
