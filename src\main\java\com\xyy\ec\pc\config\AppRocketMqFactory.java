package com.xyy.ec.pc.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.MQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * 应用RocketMQ核心配置。
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class AppRocketMqFactory {

    @Value("${spring.rocketmq.name-server}")
    private String nameServerAddresses;

    @Value("${rocketmq.app.shoppingTraceWatchByCrm.group}")
    private String shoppingTraceWatchByCrmGroup;

    @Value("${rocketmq.app.shoppingTraceWatchByCrm.topic}")
    private String shoppingTraceWatchByCrmTopic;

    @Value("${rocketmq.app.shoppingTraceWatchByCrm.tags}")
    private String shoppingTraceWatchByCrmTag;

    private MQProducer shoppingTraceWatchByCrmProducer;

    /* 搜素结果商品信息 */
    @Value("${spring.rocketmq.name-server-bj3-ec1}")
    private String nameServerAddressesBj3Ec1;

    @Value("${rocketmq.app.search-result-csu-info.topic}")
    private String searchResultCsuInfoTopic;

    @Value("${rocketmq.app.search-result-csu-info.producer-group}")
    private String searchResultCsuInfoProducerGroup;

    @Value("${rocketmq.app.search-result-csu-info.tag}")
    private String searchResultCsuInfoTag;

    private MQProducer searchResultCsuInfoProducer;

    @PostConstruct
    public void postConstruct() throws MQClientException {
        /* 商品组变化后通知CRM */
        // Instantiate with a producer group name.
        DefaultMQProducer shoppingTraceWatchByCrmProducer = new DefaultMQProducer(shoppingTraceWatchByCrmGroup);
        // Specify name server addresses.
        shoppingTraceWatchByCrmProducer.setNamesrvAddr(nameServerAddresses);
        // config
        shoppingTraceWatchByCrmProducer.setSendMsgTimeout(3000);
        //Launch the instance.
        shoppingTraceWatchByCrmProducer.start();
        log.info("create RocketMQ producer success: exhibitionProductChangeCrmMQProducer.");
        this.shoppingTraceWatchByCrmProducer = shoppingTraceWatchByCrmProducer;

        /* 搜索结果商品信息 */
        // Instantiate with a producer group name.
        DefaultMQProducer searchResultCsuInfoProducer = new DefaultMQProducer(searchResultCsuInfoProducerGroup);
        // Specify name server addresses.
        searchResultCsuInfoProducer.setNamesrvAddr(nameServerAddressesBj3Ec1);
        // config
        searchResultCsuInfoProducer.setSendMsgTimeout(3000);
        searchResultCsuInfoProducer.setInstanceName("search-result-csu-info");
        //Launch the instance.
        searchResultCsuInfoProducer.start();
        log.info("create RocketMQ producer success: searchResultCsuInfoProducer.");
        this.searchResultCsuInfoProducer = searchResultCsuInfoProducer;
    }


    public void sendMsgShoppingTraceWatchByCrm(JSONObject body){
        Message mqMessage = new Message();
        //延时发送
        //messageDelayLevel=1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
        // 相当于延迟1s
        mqMessage.setDelayTimeLevel(1);
        mqMessage.setTopic(shoppingTraceWatchByCrmTopic);
        mqMessage.setTags(shoppingTraceWatchByCrmTag);
        mqMessage.setBody(JSONObject.toJSONString(body).getBytes());
        try {
            SendResult send = shoppingTraceWatchByCrmProducer.send(mqMessage);
            log.info("sendGroupBuyingSycProductMQMsg success，消息：{},send={}", JSONObject.toJSONString(body), JSON.toJSONString(send));
        } catch (Exception e) {
            log.info("sendGroupBuyingSycProductMQMsg fail，消息：{}", JSONObject.toJSONString(body), e);
        }

    }

    /**
     * 发送搜索结果商品信息mq
     *
     * @param mqs
     */
    public void sendSearchResultCsuInfoMq(List<JSONObject> mqs) {
        if (CollectionUtils.isEmpty(mqs)) {
            return;
        }
        try {
            Message mqMessage = new Message();
            //延时发送
            //messageDelayLevel=1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
            // 相当于延迟1s
            mqMessage.setDelayTimeLevel(1);
            mqMessage.setTopic(searchResultCsuInfoTopic);
            mqMessage.setTags(searchResultCsuInfoTag);
            mqMessage.setBody(JSONArray.toJSONString(mqs).getBytes());
            SendResult sendResult = searchResultCsuInfoProducer.send(mqMessage);
            if (log.isDebugEnabled()) {
                log.debug("发送搜索结果商品信息mq，消息：{}，sendResult：{}", JSONArray.toJSONString(mqs), JSONObject.toJSONString(sendResult));
            }
        } catch (Exception e) {
            log.error("发送搜索结果商品信息mq失败，消息：{}，异常：", JSONArray.toJSONString(mqs), e);
        }
    }
}
