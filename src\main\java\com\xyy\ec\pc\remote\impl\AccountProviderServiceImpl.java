package com.xyy.ec.pc.remote.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.merchant.server.dto.AccountInfoDto;
import com.xyy.ec.merchant.server.dto.MerchantAndAccountDto;
import com.xyy.ec.pc.remote.AccountProviderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther chenshaobo
 * @Date 2024/7/16
 * @Description 子账号
 * @Version V1.0
 **/
@Slf4j
@Service
public class AccountProviderServiceImpl implements AccountProviderService {

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;

    @Override
    public List<AccountInfoDto> querySubAccountList(Long merchantId) {
        ApiRPCResult<List<AccountInfoDto>> result = null;
        try {
            result = loginAccountApi.querySubAccountList(merchantId);
            if (result.isFail()) {
                log.error("查询子账号信息业务异常 merchantId:{}, result:{}", merchantId, JSON.toJSONString(result));
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("查询子账号信息未知异常 merchantId:{}, result:{}, error_msg:{}", merchantId, JSON.toJSONString(result), e.getMessage(), e);
            return new ArrayList<>();
        }
        return result.getData();
    }

    @Override
    public MerchantAndAccountDto queryManagerAccountBySyncNo(String syncNo) {
        if (StringUtils.isEmpty(syncNo)) {
            return null;
        }
        ApiRPCResult<MerchantAndAccountDto> result = null;
        try {
            result = loginAccountApi.queryManagerAccountBySyncNo(syncNo);
            if (result.isFail()) {
                log.error("queryManagerAccountBySyncNo 查询主账号信息异常 syncNo:{}", syncNo);
                return new MerchantAndAccountDto();
            }
        } catch (Exception e) {
            log.error("queryManagerAccountBySyncNo 查询主账号信息异常 syncNo:{}, error_msg:{}", syncNo, e.getMessage(), e);
            return new MerchantAndAccountDto();
        }
        return result.getData();
    }

}
