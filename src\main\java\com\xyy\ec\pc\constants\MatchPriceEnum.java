package com.xyy.ec.pc.constants;

/**
 * <p>匹价枚举</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021年11月09日 上午10:20
 */
public enum MatchPriceEnum {
    /**
     * 折后价
     */
    ZHE_HOU_PRICE(2, "折后价"),
    /**
     * 连锁指导价
     */
    GUIDE_PRICE(1, "连锁指导价");

    private  int id;
    private  String value;

    MatchPriceEnum(int id, String value){
        this.id = id;
        this.value = value;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
