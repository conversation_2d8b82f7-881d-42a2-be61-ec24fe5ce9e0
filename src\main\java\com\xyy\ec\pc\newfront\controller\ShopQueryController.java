package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.service.ShopQueryService;
import com.xyy.ec.pc.newfront.vo.ShotInfoVO;
import com.xyy.ec.pc.newfront.vo.ShotQueryParam;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 店铺相关查询
 */
@CustomizeCmsResponse
@RequestMapping("/new-front/shop")
@RestController
@Slf4j
public class ShopQueryController extends BaseController {

    @Resource
    private XyyIndentityValidator xyyIndentityValidator;

    @Resource
    private ShopQueryService shopQueryService;

    /**
     * 店铺列表 首页
     */
    @PostMapping(value = "/list")
    public AjaxResult<List<ShotInfoVO>> queryShopList(@RequestBody ShotQueryParam param) {
        try {
            MerchantBussinessDto merchant = (MerchantPrincipal) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                log.error("用户未登录 , ");
                return AjaxResult.errResult("用户未登录");
            }
            //用户id
            Long merchantId = merchant.getId();
            List<ShotInfoVO> shopList = shopQueryService.queryShopActivityInfoByShopCodes(param, merchantId);
            return AjaxResult.successResult(shopList);
        } catch (Exception e) {
            log.error("shopList 异常：", e);
            return AjaxResult.errResult("系统异常");
        }
    }




}
