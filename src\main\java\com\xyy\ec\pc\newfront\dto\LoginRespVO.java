package com.xyy.ec.pc.newfront.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: TODO
 * @date: 2025/5/27 12:44
 * @author: <EMAIL>
 * @version: 1.0
 */

@Getter
@Setter
@ToString
public class LoginRespVO {

    String name;
    String errorMsg;
    String redirectUrl;


    Long merchantId;

    // level = 0 反爬账号  需要进行手机验证码验证后才可登录
    // level = 1 异地登陆验证  系统检测到您当前的账号正在进行异地登录，为保证账号的安全，请输入手机验证码完成安全验证
    // level = 2 弹出修改密码弹窗
    Integer level;
    /**
     * 跳转选择登录店铺
     */
    boolean selectLoginShop;
    /**
     * 跳转关联药房页面
     */
    boolean connectPharmacy;

    public LoginRespVO(String name, String errorMsg, String redirectUrl) {
        this.name = name;
        this.errorMsg = errorMsg;
        this.redirectUrl = redirectUrl;
    }

    public LoginRespVO(){

    }



}
