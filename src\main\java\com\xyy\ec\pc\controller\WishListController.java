package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.bussiness.api.WishOrderBussinessApi;
import com.xyy.ec.merchant.bussiness.base.SkuPOJO;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.WishOrderBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.WishOrderBussinessExtensionDto;
import com.xyy.ec.order.business.api.ShoppingCartBusinessApi;
import com.xyy.ec.order.business.model.ServiceResponse;
import com.xyy.ec.order.core.dto.cart.ShoppingCartDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.base.Sort;
import com.xyy.ec.pc.config.BranchEnum;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.model.WishOrder;
import com.xyy.ec.pc.model.WishOrderPO;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.FileUploadUtil;
import com.xyy.ec.pc.util.PcVersionUtils;
import com.xyy.ec.pc.util.StringUtil;
import com.xyy.ec.product.business.api.ProductBusinessApi;
import com.xyy.ec.product.business.dto.ProductDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 心愿单管理
 * @Author: WanKp
 * @Date: 2018/8/26 13:54
 **/

@Controller
@RequestMapping("/merchant/center/wish")
public class WishListController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WishListController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private WishOrderBussinessApi wishOrderBussinessApi;

    @Reference(version = "1.0.0")
    private ProductBusinessApi productBusinessApi;

    @Reference(version = "1.0.0")
    private ShoppingCartBusinessApi shoppingCartBusinessApi;

    @Autowired
    private XyyConfig.CdnConfig cdnConfig;

    @Autowired
    private Config config;

    @Autowired
    private PcVersionUtils pcVersionUtils;

    /**
     * 心愿单首页
     *
     * @param request,page,sort,wishList
     * @return ModelAndView
     * @throws
     * <AUTHOR>
     * @Date:2018年1月20日下午3:56:51
     */
    @RequestMapping("index.htm")
    public ModelAndView index(HttpServletRequest request, Page page, Sort sort,
                              WishOrderPO wishList,
                              @RequestParam(value = "tab", required = false) Integer tab) {
        Map<String, Object> model = new HashMap<>();
        MerchantBussinessDto merchant = null;
        try {
            merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            merchant = pcVersionUtils.getPriceDisplayFlagByMerchantId(merchant);
            if (merchant == null) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }
            model.put("merchant", merchant);
            if (tab == null) {
                tab = 1;
            }

            if (tab==1){
                model.put("center_menu", "wish");
                model.put("tab", tab);
                model.put("productImageUrl", config.getProductImagePathUrl());
                return new ModelAndView("/wish/wish.ftl", model);
            }

            wishList.setMerchantId(merchant.getId());
            if (page == null) {
                page=new Page();
            }

            WishOrderBussinessExtensionDto wishOrderBussinessDto = new WishOrderBussinessExtensionDto();
            BeanUtils.copyProperties(wishList, wishOrderBussinessDto);
            PageInfo<WishOrderBussinessExtensionDto> pageInfo = wishOrderBussinessApi.findWishOrderForClient(wishOrderBussinessDto, page.getOffset(), page.getLimit());

            List<WishOrderBussinessExtensionDto> list = new ArrayList<>();
            for (Object map : pageInfo.getList()) {
                String jsonString = JSONObject.toJSONString(map);
                if (StringUtil.isNotEmpty(jsonString)) {
                    WishOrderBussinessExtensionDto dto = JSONObject.parseObject(jsonString, WishOrderBussinessExtensionDto.class);
                    list.add(dto);
                }
            }

            // 查询心愿单是否已经关联上商品
            if (CollectionUtil.isNotEmpty(list)) {
                for (WishOrderBussinessExtensionDto wish : list) {
                    if (wish.getStatus()==2){
                        checkSku(wish, merchant);
                    }
                }
            }

            String requestUrl = this.getRequestUrl(request);
            Page pager = new Page();
            pager.setOffset(page.getOffset());
            pager.setLimit(page.getLimit());
            pager.setRows(list);
            pager.setTotal(pageInfo.getTotal());
            pager.setRequestUrl(requestUrl);
            model.put("pager", pager);
            model.put("center_menu", "wish");
            model.put("tab", tab);
            model.put("productImageUrl", config.getProductImagePathUrl());
            return new ModelAndView("/wish/wish.ftl", model);
        } catch (Exception e) {
            LOGGER.error("心愿单首页异常,e=" + e);
            String requestUrl = this.getRequestUrl(request);
            page.setRequestUrl(requestUrl);
            model.put("pager", page);
            model.put("merchant", merchant);
            model.put("center_menu", "wish");
            model.put("tab", tab);
            return new ModelAndView("/wish/wish.ftl", model);
        }
    }

    /**
     * 查询心愿单是否已经关联上商品
     * @param merchant
     * @param wish
     */
    private void checkSku(WishOrderBussinessExtensionDto wish, MerchantBussinessDto merchant) throws Exception {
        if (StringUtil.isNotEmpty(wish.getBarcode())) {
            ProductDto pro = productBusinessApi.getSkuInfoByBarcodeAndBranchCode(wish.getBarcode(), merchant.getRegisterCode());
            if (pro != null) {
                assemble(wish,merchant,pro);
            }
        }
    }

    /**
     * 将商品和购物车组装
     * @param wish
     * @param merchant
     * @param pro
     * @throws Exception
     */
    private void assemble(WishOrderBussinessExtensionDto wish,MerchantBussinessDto merchant,ProductDto pro) throws Exception{
        if (pro.getStatus() != 4) {
            // TODO 等待商品接口
            ProductDto product = productBusinessApi.findSkuDetail(pro.getId(), merchant.getId(),merchant.getRegisterCode());
            Long[] ids = new Long[]{pro.getId()};
            //TODO 等待购物车接口
            ServiceResponse<List<ShoppingCartDto>> listCartResponse = shoppingCartBusinessApi.getCartListByIds(ids, merchant.getId());
            List<ShoppingCartDto> listCart = listCartResponse.getResult();
            if (CollectionUtil.isNotEmpty(listCart)) {
                product.setCartProductNum(listCart.get(0)
                        .getAmount());
            }
            SkuPOJO skuPOJO = new SkuPOJO();
            BeanUtils.copyProperties(product, skuPOJO);
            wish.setProduct(skuPOJO);
        } else {
            //TODO 先传个空
            SkuPOJO skuPOJO = new SkuPOJO();
            BeanUtils.copyProperties(pro, skuPOJO);
            wish.setProduct(skuPOJO);
        }
    }


    /**
     * 保存心愿单信息
     *
     * @param wishList,targetFileName,request
     * @return ModelAndView
     * @throws
     * <AUTHOR>
     * @Date:2018年1月20日下午3:57:34
     */
    @RequestMapping(value = "saveWishList.htm", method = RequestMethod.POST)
    public ModelAndView saveWishList(
            WishOrder wishList,
            HttpServletRequest request,
            @RequestParam(value = "targetFileName", required = false) String targetFileName) {
        Map<String, Object> model = new HashMap<String, Object>();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();

            if ( null == merchant) {
                return new ModelAndView(new RedirectView("/login/login.htm",true,false));
            }

            // 心愿单上传图片
            String uploadPath = "/ybm/wish/" + merchant.getId() + "/";
            Map<String, Object> uploadResultMap = FileUploadUtil.fileUpload(request, uploadPath, cdnConfig, targetFileName, null);
            if (uploadResultMap != null) {
                Object uploadResult = uploadResultMap.get("fileName");
                if (uploadResult != null) {
                    List<String> fileNameList = (List<String>) uploadResult;
                    if (CollectionUtil.isNotEmpty(fileNameList)) {
                        String fileName = uploadPath + fileNameList.get(0);
                        wishList.setUrl(fileName);
                    }
                }
            }
            String branchCode = merchant.getRegisterCode();
            if(StringUtil.isNotEmpty(branchCode)) {
                wishList.setBranchCode(branchCode);
                wishList.setBranchName(BranchEnum.get(branchCode));
            }
            wishList.setMerchantId(merchant.getId());
            wishList.setUpdator(merchant.getId());
            WishOrderBussinessDto wishOrderBussinessDto = new WishOrderBussinessDto();
            BeanUtils.copyProperties(wishList,wishOrderBussinessDto);
            wishOrderBussinessApi.insertSelective(wishOrderBussinessDto);
            return new ModelAndView(new RedirectView("/merchant/center/wish/index.htm?tab=2",true,false));
        } catch (Exception e) {
            LOGGER.error("保存心愿单失败", e);
            model.put("msg", "保存心愿单失败");
            return new ModelAndView("/wish/wish.ftl", model);
        }
    }


}
