var int = null;
var count = 0;

$(function() {
	if(errorMsg){
		$.alert(errorMsg);
		return false;
	}
	var retryTimes = parseInt($('#retryTimes').val());
	var retryInterval = parseInt($('#retryInterval').val());
	invokeInterval(retryTimes,retryInterval);
});

function invokeInterval(retryTimes,retryInterval){
	queryOrderInt = setInterval(function(){
		if(count <=  retryTimes){
			 checkOrderGenerate();
			 count++;
		}
		else{
			clearInterval(queryOrderInt);
			count = 0; 
			$.alert({
				title: "温馨提示",
				body: 'Sorry! 由于下单人数较多，您可以执行以前操作',
				okBtn : '立即支付 ',
				cancelBtn : '稍后支付',
				okHidden: function(){invokeInterval(retryTimes,retryInterval);return true;},
				timeout: 1000* 60 * 10,    
				cancelHidden: function(){
					window.location.href = "/merchant/center/order/index.htm";
					return true;
					}
			});
			
		}
     }  ,retryInterval);
}


function checkOrderGenerate(){
	var orderNo = $('#orderNo').val();
	var jumpType = $('#jumpType').val();
	var token = $("#token").val();
	var tranNo = $("#tranNo").val();
	var useVirtualGold = $("#useVirtualGold").val()
	$.ajax({
        type: "POST",
        url: "/merchant/center/order/checkOrderGenerate.json",
        data: {"orderNo":orderNo},
        dataType: "json",
        success: function (data) {
            if (data.status === "success") {
            	clearInterval(queryOrderInt);
            	if (jumpType == 2) {
                    window.location.href = "/merchant/center/order/index.htm";
                    return;
				}
            	var id = data.id;
            	window.location.href = "/merchant/center/order/queryConfirmOrder.htm?id="+id + '&token=' + token + '&tranNo=' + tranNo + '&useVirtualGold=' + useVirtualGold;
            	//跳转
            }else{
            	if(data.errorMsg){
                    clearInterval(queryOrderInt);

                    $.alert({
                        title: "温馨提示",
                        body: data.errorMsg,
                        okBtn : '回采购单 ',
                        cancelBtn : '我的订单',
                        okHidden: function(){
                            window.location.href = "/merchant/center/cart/index.htm";
                            return true;
                        	},
                        timeout: 1000* 60 * 10,
                        cancelHidden: function(){
                            window.location.href = "/merchant/center/order/index.htm";
                            return true;
                        }
                    });
                    return;
                }
                else{
                    console.log(data.errorMsg);
                }

            }
          
        }
    });
}