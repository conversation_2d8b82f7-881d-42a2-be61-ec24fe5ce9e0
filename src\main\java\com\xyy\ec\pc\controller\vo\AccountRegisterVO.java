package com.xyy.ec.pc.controller.vo;

import java.io.Serializable;

public class AccountRegisterVO implements Serializable {
    //手机号
    private String mobile;
    //密码
    private String password;
    //图片验证码
    private String photoCode;
    //短信验证码
    private String code;

    private Integer registerSource;

    //联系人姓名
    private String contactName;

    public Integer getRegisterSource() {
        return registerSource;
    }

    public void setRegisterSource(Integer registerSource) {
        this.registerSource = registerSource;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhotoCode() {
        return photoCode;
    }

    public void setPhotoCode(String photoCode) {
        this.photoCode = photoCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }
}
