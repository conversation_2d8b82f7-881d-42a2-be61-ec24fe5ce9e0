package com.xyy.ec.pc.enums.marketing;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Auther chenshaobo
 * @Date 2024/9/14
 * @Description 据商品表中【high_gross】或【指定店铺】配置拼团商品“已拼数量/客户数”展示逻辑 枚举
 * @Version V1.0
 **/
@Getter
@AllArgsConstructor
public enum MarketingGroupBuyingSalesRuleEnum {

    DESIGN_SHOP(1, "店铺"),
    HIGH_GROSS(2, "聚合");

    private Integer type;

    private String name;

}
