package com.xyy.ec.pc.out.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.merchant.server.dto.LoginAccountDto;
import com.xyy.ec.merchant.server.enums.LoginAccountStatusEnum;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.PasswordVerifier;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.enums.MerchantStatusEnum;
import com.xyy.ec.pc.exception.AuthenticationException;
import com.xyy.ec.pc.model.MerchantToSass;
import com.xyy.ec.pc.service.AuthenticationProvider;
import com.xyy.ec.pc.service.LoginAccountService;
import com.xyy.ec.pc.service.MerchantService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录/注册控制器(Sass或者ERP使用)
 * <AUTHOR>
 */
@Controller
@RequestMapping("/out")
public class OutLoginController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OutLoginController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Autowired
    private AuthenticationProvider authenticationProvider;

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private LoginAccountService loginAccountService;

    /**
     * Sass 登录
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "/validate.json", method = RequestMethod.POST)
    public Object login_validate(HttpServletRequest request,
                                 @RequestParam("name") String loginName, @RequestParam("password") String loginPass) {
        if (StringUtils.isEmpty(loginName)) {
            return this.addError("用户名不能为空！");
        }

        if (StringUtils.isEmpty(loginPass)) {
            return this.addError("密码不能为空！");
        }
        loginPass = MD5Util.getMD5Str(loginPass);
        LOGGER.info(String.format("login_validate传递过来的参数为：loginName=%s,loginPass=%s", loginName, loginPass));
        MerchantBussinessDto merchant = new MerchantBussinessDto();
        try {

            PasswordVerifier verifier = new PasswordVerifier(loginName, loginPass);
            merchant = (MerchantBussinessDto) authenticationProvider.authenticate(verifier);
            if (merchant == null) {
                return super.addError("用户名或密码不正确！");
            }
            return super.addResult("token", merchant.getSyncNo());

        } catch (AuthenticationException e) {
            if (Constants.TYPE_IS_KA.equals(e.getCode())) {
                try {
                   merchant = merchantService.findMerchantByMobile(loginName);
                } catch (Exception e1) {
                    LOGGER.error("AuthenticationException验证失败,e=" + e.toString());
                    return super.addError(e.getMessage());
                }
                return super.addResult("token", merchant.getSyncNo());
            } else {
                LOGGER.error("AuthenticationException验证失败,e=" + e.toString());
                return super.addError(e.getMessage());
            }
        } catch (Exception e) {
            LOGGER.error("login_validate验证失败,e=" + e.toString());
            return super.addError(e.getMessage());
        }
    }

    /**
     * KA登录
     * @param request
     * @return
     * @throws Exception
     */

    @RequestMapping(value = "/login.htm", method = RequestMethod.POST)
    public Object login_commit(HttpServletRequest request,
                               @RequestParam("name") String loginName, @RequestParam("password") String loginPass) throws Exception {
        Map<String, Object> model = new HashMap<String, Object>();

        if (StringUtils.isEmpty(loginName)) {
            model.put("errorMsg", "用户名不能为空！");
            return new ModelAndView("/login.ftl", model);
        }

        if (StringUtils.isEmpty(loginPass)) {
            model.put("errorMsg", "密码不能为空！");
            return new ModelAndView("/login.ftl", model);
        }

        LOGGER.info(String.format("login_commit传递过来的参数为：loginName=%s,loginPass=%s", loginName, loginPass));

        String from = request.getParameter("redirectUrl");
        Long merchantId = request.getParameter("merchantId") == null ? 0L : Long.valueOf(request.getParameter("merchantId"));
        Long id = 0L;
        String mobile = "";
        try {
            PasswordVerifier verifier = new PasswordVerifier(loginName, loginPass);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.login(verifier);
            if (merchant != null) {
                //如果没有账号激活，则跳转到填写邀请码页面
                if (merchant.getStatus() == MerchantStatusEnum.STATUS_NON_ACTIVATED.getId()) {
                    id = merchant.getId();
                    mobile = merchant.getMobile();
                    LOGGER.info("KA踢出登录用户id:{}",id);
                    xyyIndentityValidator.logout();
                    return new ModelAndView(new RedirectView("/login/register_step.htm?merchantId=" + id + "&mobile=" + mobile + "&mobilepwd=" + loginPass + "&sourceType=1",true,false));
                }
            }
            String redirectUrl = "/";
            if (merchantId == 0L) {
                if (StringUtil.isNotEmpty(from)) {
                    if (from.indexOf("/merchant/center/order/detail") != -1) {
                        from = "/";
                    }
                    redirectUrl = from;
                }
                return new ModelAndView(new RedirectView(redirectUrl.replaceAll("%26", "&"),true,false));
            } else {
                if (merchant.getId() != merchantId) {
                    return new ModelAndView(new RedirectView(redirectUrl,true,false));
                } else {
                    if (StringUtil.isNotEmpty(from)) {
                        redirectUrl = from;
                    }
                    return new ModelAndView(new RedirectView(redirectUrl.replaceAll("%26", "&"),true,false));
                }
            }
        } catch (AuthenticationException e) {
            model = new HashMap<String, Object>();
            model.put("name", loginName);
            MerchantBussinessDto cust = merchantService.findMerchantByMobile(loginName);
            if (cust != null) {
                id = cust.getId();
            }
            if ("STATUS_NON_ACTIVATED".equals(e.getMessage())) {
                return new ModelAndView(new RedirectView("/login/register_step.htm?merchantId=" + id + "&mobile=" + loginName + "&mobilepwd=" + loginPass + "&sourceType=1",true,false));
            }

            /*
            if (Constants.TYPE_IS_KA.equals(e.getCode())) {
                String jsessionid = "loginName=" + loginName + "&loginPass=" + loginPass;
                jsessionid = EncodeUtil.base64UrlSafeEncode(AESUtil.encrypt(jsessionid, "pw2dOykmYsQ"));
//                return new ModelAndView(new RedirectView(PropertiesUtil.get("chain.shop.url") + "/login/login.htm?jsessionid=" + jsessionid));
                String redirectUrl = from;
                if (org.apache.commons.lang.StringUtils.isEmpty(redirectUrl)) {
                    redirectUrl = "/";
                }
                return new ModelAndView(new RedirectView( redirectUrl + "?jsessionid="+jsessionid));
            }
             */
            model.put("errorMsg", e.getMessage());
            model.put("redirectUrl", from);
            return new ModelAndView("/login.ftl", model);
        }

    }

    /**
     * SASS3.0 商户验证信息接口
     *
     * @param name
     * @param password
     * @return
     */
    @ResponseBody
    @RequestMapping("/sass/validate")
    public Object sassValidate(String name, String password) {

        if (StringUtils.isEmpty(name)) {
            return this.addError("用户名不能为空！");
        }

        if (StringUtils.isEmpty(password)) {
            return this.addError("密码不能为空！");
        }
        password = MD5Util.getMD5Str(password);
        LOGGER.info(String.format("sassValidate传递过来的参数为：loginName=%s,loginPass=%s", name, password));
        MerchantBussinessDto merchant = new MerchantBussinessDto();
        try {
            merchant = merchantService.findMerchantByMobile(name);
            if (merchant == null) {
                return this.addError("用户名或密码不正确！");
            }

            if (!merchant.getPassword().equalsIgnoreCase(password)) {
                return this.addError("用户名或密码不正确！");
            }

            if (merchant.getStatus() == MerchantStatusEnum.STATUS_IS_FROZEN.getId()) {
                return this.addError("账户被冻结！");
            }

            if (merchant.getStatus() == MerchantStatusEnum.STATUS_NON_ACTIVATED.getId()) {
                return this.addError("账户未激活！");
            }

            MerchantToSass merchantToSass = CreateMerchantToSass(merchant);
            return super.addResult("data", merchantToSass);
        } catch (Exception e) {
            LOGGER.error("sassValidate验证失败" + e.getMessage());
            return super.addError("验证失败！");
        }
    }
    /**
     * SASS3.0 商户验证信息接口
     *
     * @param name
     * @param password
     * @return
     */
    @ResponseBody
    @RequestMapping("/sass/v2/validate")
    public Object sassValidateV2(String name, String password) {

        if (StringUtils.isEmpty(name)) {
            return this.addError("用户名不能为空！");
        }
        if (StringUtils.isEmpty(password)) {
            return this.addError("密码不能为空！");
        }
        password = MD5Util.getMD5Str(password);
        LOGGER.info(String.format("sassValidate传递过来的参数为：loginName=%s,loginPass=%s", name, password));
        try {
            LoginAccountDto loginAccountDto = loginAccountService.selectLoginAccountForLoginByMobile(name);
            if (loginAccountDto == null) {
                return this.addError("用户名或密码不正确！");
            }

            if (!loginAccountDto.getPassword().equalsIgnoreCase(password)) {
                return this.addError("用户名或密码不正确！");
            }

            if (loginAccountDto.getStatus() == LoginAccountStatusEnum.Frozen.getValue()) {
                return this.addError("账户被冻结！");
            }

            if (loginAccountDto.getStatus() == LoginAccountStatusEnum.Logoff.getValue()) {
                return this.addError("账户已注销！");
            }
            List<MerchantToSass> list = new ArrayList<>();
            List<MerchantBussinessDto> merchantBussinessDtos = merchantService.findMerchantForSaasByAccountId(loginAccountDto.getId());
            if(CollectionUtil.isNotEmpty(merchantBussinessDtos)){
                merchantBussinessDtos.forEach(merchantBussinessDto -> {
                    list.add(CreateMerchantToSass(merchantBussinessDto));
                });
            }
            return super.addResult("data", list);
        } catch (Exception e) {
            LOGGER.error("sassValidate验证失败" + e.getMessage());
            return super.addError("验证失败！");
        }
    }

    private MerchantToSass CreateMerchantToSass(MerchantBussinessDto merchant) {
        MerchantToSass merchantToSass = new MerchantToSass();
        if (merchant.getMobile() != null) {
            merchantToSass.setMobile(merchant.getMobile());
        }
        if (merchant.getRealName() != null) {
            merchantToSass.setRealName(merchant.getRealName());
        }
        if (merchant.getBusinessType() != null) {
            merchantToSass.setBusinessType(merchant.getBusinessType());
        }
        if (merchant.getProvince() != null) {
            merchantToSass.setProvince(merchant.getProvince());
        }
        if (merchant.getCity() != null) {
            merchantToSass.setCity(merchant.getCity());
        }
        if (merchant.getDistrict() != null) {
            merchantToSass.setDistrict(merchant.getDistrict());
        }
        if (merchant.getAddress() != null) {
            merchantToSass.setAddress(merchant.getAddress());
        }
        if (merchant.getSyncNo() != null) {
            merchantToSass.setSyncNo(merchant.getSyncNo());
        }
        if (merchant.getNickname() != null){
            merchantToSass.setNickname(merchant.getNickname());
        }
        if (merchant.getRegisterCode() != null){
            merchantToSass.setRegisterCode(merchant.getRegisterCode());
        }
        return merchantToSass;
    }

}
