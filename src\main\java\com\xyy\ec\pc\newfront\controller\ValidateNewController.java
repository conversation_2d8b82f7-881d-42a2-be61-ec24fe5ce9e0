package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.merchant.bussiness.dto.licence.LicenseValidDto;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.service.ValidateNewService;
import com.xyy.ec.pc.newfront.vo.CheckCodeParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;




@CustomizeCmsResponse
@RequiredArgsConstructor
@RestController
@RequestMapping("/new-front/validate")
public class ValidateNewController {

    private final ValidateNewService validateNewService;


    /**
     * 爬虫用户发送短信验证码
     */
    @GetMapping("/send-crawler-code")
    public AjaxResult<Object> sendCrawlerCode(@RequestParam("phone") String phone) {
        try {
            return validateNewService.sendCrawlerCode(phone);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    /**
     * 爬虫用户短信验证码校验
     */
    @PostMapping("/check-crawler-code")
    public AjaxResult<Object> checkCrawlerCode(@RequestBody CheckCodeParamVO codeParamVO) {
        try {
            return validateNewService.checkCrawlerCode(codeParamVO);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }


    /**
     * 注册-获取验证码
     *
     * @param mobileNumber
     * @return
     */
    @GetMapping("/sendRegisterVerificationCode")
    public AjaxResult<LicenseValidDto> registerSendVerificationCode(@RequestParam("mobileNumber") String mobileNumber, @RequestParam("code") String code) {
        try {
            return validateNewService.sendRegisterMsgCode(mobileNumber, code);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }

    }
    /**
     * 获取验证码
     */
    @GetMapping("/send-code")
    public AjaxResult<Object> getValitionCode(@RequestParam("mobileNumber")String mobileNumber) {
        try {
            return validateNewService.getValitionCode(mobileNumber);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }

    /**
     * 检验验证码的有效性
     */
    @GetMapping("/check-validate")
    public AjaxResult<Object> checkValidate(@RequestParam("mobileNumber")String mobileNumber,@RequestParam("code")String code) {
        try {
            return validateNewService.checkCode(mobileNumber,code);
        } catch (Exception e) {
            return AjaxResult.errResult(e.getMessage());
        }
    }
}


