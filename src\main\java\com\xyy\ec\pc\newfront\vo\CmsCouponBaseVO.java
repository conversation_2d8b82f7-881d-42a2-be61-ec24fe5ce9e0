package com.xyy.ec.pc.newfront.vo;

import com.xyy.ec.marketing.hyperspace.api.dto.coupon.CouponBaseDto;
import com.xyy.track.dto.TrackDataEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class CmsCouponBaseVO extends CouponBaseDto {
    /**
     * 打折额度
     */
    private String discountStr;

    /**
     * 人群名称
     */
    private String customerGroupName;

    /**
     * 组件序号
     */
    private String componentPosition;
    /**
     * 组件名称
     */
    private String componentName;
    /**
     * 组件标题
     */
    private String componentTitle;
    /**
     * 子模块类型
     */
    private String subModuleType;
    /**
     * 子模块序号
     */
    private String subModulePosition;
    /**
     * 子模块名称
     */
    private String subModuleName;
    /**
     * 来源ID
     */
    private String jgspid;

    /**
     * 埋点
     */
    private TrackDataEntity trackData;

    private Integer receiveType;
    /**
     * 优惠券文案
     */
    private String couponText;

    /**
     * 优惠券状态
     */
    private Integer couponState;

    /**
     * 生效时间
     */
    private Date validDate;
    /**
     * 失效时间
     */
    private Date expireDate;

}
