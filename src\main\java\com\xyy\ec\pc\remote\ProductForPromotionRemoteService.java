package com.xyy.ec.pc.remote;

import com.xyy.ec.product.business.ecp.csufillattr.dto.ProductDTO;
import com.xyy.ec.product.business.ecp.out.promotion.api.ProductForPromotionApi;
import com.xyy.ec.product.business.ecp.out.promotion.dto.SkuSimpleDTO;

import java.util.List;

/**
 * {@link ProductForPromotionApi} RPC调用Service
 *
 * <AUTHOR>
 */
public interface ProductForPromotionRemoteService {

    /**
     * 根据商品ID获取商品简易信息。
     * PS：由于商品这个接口是基础且重要接口，故而若出错向上传递异常，由上游自行决定如何处理。
     *
     * @param skuIds
     * @return
     */
    List<SkuSimpleDTO> findProductSimpleInfoBySkuIdList(List<Long> skuIds);


    /**
     * 获取商品简易信息
     * @param skuIds
     * @return
     */
    List<ProductDTO> queryCsuSimpleInfoCsuIdList(List<Long> skuIds);
}
