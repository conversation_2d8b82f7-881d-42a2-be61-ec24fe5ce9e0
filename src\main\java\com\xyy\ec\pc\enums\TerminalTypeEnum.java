package com.xyy.ec.pc.enums;

/**
 * @ClassName: PlatformEnum
 * @Description: 平台枚举值
 * @author: denghp
 * @date: 2020/4/17
 */
public enum TerminalTypeEnum {

    ANDROID("Android", 1),
    IOS("iOS", 2),
    H5("H5", 3),
    PC("Pc", 4);

    private String name;
    private int value;

    public String getName() {
        return name;
    }

    public int getValue() {
        return value;
    }

    TerminalTypeEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }
}
