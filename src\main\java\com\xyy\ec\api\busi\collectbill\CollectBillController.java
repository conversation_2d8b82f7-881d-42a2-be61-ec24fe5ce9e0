package com.xyy.ec.api.busi.collectbill;

import com.ec.web.api.WebApiResp;
import com.xyy.ec.api.busi.base.WebBaseController;
import com.xyy.ec.api.rpc.order.OrderRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Auther: zhangjianhui
 * @Date: 2020/4/15 09:57
 * @Description:
 */
@RestController
@RequestMapping("/pc/bill")
@Slf4j
public class CollectBillController extends WebBaseController {
    @Autowired
    OrderRpcService orderRpcService;

    @RequestMapping("/collect")
    public WebApiResp collect(@Valid  CollectReq collectReq) {
        Long userId = super.getUserId();
        return new WebApiResp(orderRpcService.selectRturnVoucherInfo(collectReq.getShopCode(),userId,Long.valueOf(collectReq.getActId())));
    }
}
