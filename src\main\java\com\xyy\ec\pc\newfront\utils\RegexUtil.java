package com.xyy.ec.pc.newfront.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 校验营业执照号/医疗机构执业许可证编号
 */
public class RegexUtil {
    private static String CHINESE_CHAR_REGEX="[\\uFF00-\\uFFFF]|。";
    public static Boolean chineseChar(String text){
        if (StringUtils.isBlank(text)){
            return false;
        }
        Pattern pattern = Pattern.compile(CHINESE_CHAR_REGEX);
        Matcher matcher = pattern.matcher(text);
        return matcher.matches();
    }
}
