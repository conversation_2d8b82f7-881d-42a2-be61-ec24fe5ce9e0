(function (config) {
    console.log("新执行极光埋点", config);
    if (window.AnalysysAgent && window.AnalysysAgent.isInit) return
    window.AnalysysAgent = window.AnalysysAgent || {}
    var ans = ['identify', 'alias', 'reset', 'track', 'profileSet', 'profileSetOnce', 'profileIncrement', 'profileAppend', 'profileUnset', 'profileDelete', 'registerSuperProperty', 'registerSuperProperties', 'unRegisterSuperProperty', 'clearSuperProperties', 'getSuperProperty', 'getSuperProperties', 'pageView', 'getDistinctId']
    var a = window.AnalysysAgent
    a['param'] = []
    function factory (b) {
        return function () {
            a['param'].push({
                fn: b,
                arg: arguments
            })
        }
    }
   
    for (var i = 0; i < ans.length; i++) {
        a[ans[i]] = factory(ans[i])
    }

    if (config.name) {
        window[config.name] = a
    }

    var c = document.createElement('script'), n = document.getElementsByTagName('script')[0];
    var date = new Date();
    var time = new String(date.getFullYear()) + new String(date.getMonth() + 1) + new String(date.getDate());
    c.type = 'text/javascript';
    c.async = true;
    c.id = 'ARK_SDK';
    c.src = '/static/js/AnalysysAgent_JS_SDK.min.js' +'?v=' +time; //JS SDK存放地址
    c.onload = function () {
        window.AnalysysAgent.init(config)
    }
    n.parentNode.insertBefore(c, n);
    var cookie = this.getCookieValue("xyy_principal") || "";
    var accountId = "";
    var cookieMerchantId= "";
    var cookieArr = cookie.split("&");
    if (cookieArr.length > 2) {
        cookieMerchantId = cookieArr[2];
        accountId = cookieArr[0];
    }
    window.AnalysysAgent.registerSuperProperty("merchant_id", cookieMerchantId || "");
    window.AnalysysAgent.registerSuperProperty("account_id", accountId || "");
})({
    // appkey: 'e6f27194adb12753', //测试APPKEY
    appkey: 'a775ff9e4e84bc9a', //预发/线上APPKEY
    uploadURL: 'https://jgmd.ybm100.com',//上传数据的地址
    debugMode: 0,  // 1 开启调试模式且数据不入库  2 开启调试模式且数据入库 0 关闭调试模式
    autoPageViewDuration: true,
    // pageProperty: {
    //     '$url': location.href,
	// 	'$url_domain': location.origin,
    //     '$title': document.title,
    // }
})