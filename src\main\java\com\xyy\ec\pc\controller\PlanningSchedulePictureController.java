package com.xyy.ec.pc.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.ec.merchant.bussiness.api.PicElectronicPlanOrderBussinessApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.bussiness.dto.PicElectronicPlanOrderBussinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.Page;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.CollectionUtil;
import com.xyy.ec.pc.util.DateUtil;
import com.xyy.ec.pc.util.FileUploadUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/9/28
 * @description 电子计划单图片控制器
 */
@Controller
@RequestMapping("/merchant/center/planningSchedule/picture")
public class PlanningSchedulePictureController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(PlanningSchedulePictureController.class);

    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
//    @Reference(version = "1.0.0",timeout = 60000,url = "dubbo://localhost:22005/com.xyy.ec.merchant.bussiness.api.PicElectronicPlanOrderBussinessApi")
    private PicElectronicPlanOrderBussinessApi picElectronicPlanOrderBussinessApi;

    @Autowired
    private XyyConfig.CdnConfig cdnConfig;

    /**
     * 上传图片
     *
     * @param request
     * @param targetFileName
     * @return
     */
    @RequestMapping(value = "/uploadPicture", method = RequestMethod.POST)
    @ResponseBody
    public Object upLoadPicture(HttpServletRequest request, String targetFileName) {
        //todo 电子计划单下线
        if(true){
            return this.addResult("电子计划单已下线!");
        }
        try {
            //上传图片
            String localTempPath = System.getProperty("xyy-shop");
            String uploadPath = "/ybm/planningOrder/";
            Map<String, Object> uploadResultMap = FileUploadUtil.fileUpload(
                    request, uploadPath, cdnConfig, targetFileName,
                    localTempPath);
            if (uploadResultMap != null) {
                Object uploadResult = uploadResultMap.get("fileName");
                if (uploadResult != null) {
                    List<String> fileNameList = (List<String>) uploadResult;
                    if (CollectionUtil.isNotEmpty(fileNameList)) {
                        String fileName = uploadPath + fileNameList.get(0);
                        return this.addResult(fileName);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("上传图片采购单失败,e=" + e);
            return this.addError("上传图片采购单失败");
        }
        return this.addError("上传图片采购单失败");
    }


    /**
     * 批量保存上传的电子计划单图片
     *
     * @param order 图片采购单实体
     * @return
     */
    @RequestMapping(value = "/save.json", method = RequestMethod.POST)
    @ResponseBody
    public Object savePictureAjax(PicElectronicPlanOrderBussinessDto order) {
        //todo 电子计划单下线
        if(true){
            return this.addResult("电子计划单已下线!");
        }
        Map<String, Object> model = new HashMap<>();
        try {

            if (order == null) {
                model.put(this.RESULT_STATUS, this.CODE_ERROR);
                return  addError("请上传图片");
            }

            //采购单名不能为空
            if (order.getPurchaseName() == null || order.getPurchaseName().trim().equals("")) {
                model.put(this.RESULT_STATUS, this.CODE_ERROR);
                return  addError("采购单名不能为空");
            }

            //图片链接不能为空
            if (StringUtils.isEmpty(order.getPicUrls())) {
                model.put(this.RESULT_STATUS, this.CODE_ERROR);
                return  addError("请上传图片");
            }

            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return  addError("请先登录！");
            }

            //采购单名称不能重复
            order.setMerchantId(merchant.getId());
            boolean bl = picElectronicPlanOrderBussinessApi.chargePicElectronicPlanOrderExsits(order);
            if (!bl) {
                return  addError("图片采购单名称重复");
            }

            Date date = new Date();
            String dateString = DateUtil.date2String(date, DateUtil.PATTERN_TIMESTAMP);
            //保存
            order.setBranchCode(merchant.getRegisterCode());
            order.setPurchaseNo("CGD" + dateString);
            order.setRealName(merchant.getRealName());
            order.setSubTime(date);
            order.setMobile(merchant.getMobile());
            //图片数
            String[] urls = order.getPicUrls().split(";");
            order.setPicNum(urls.length);
            picElectronicPlanOrderBussinessApi.savePicElectronicPlanOrder(order);
        } catch (Exception e) {
            logger.error("批量保存图片采购单异常,e=" + e);
            model.put("msg", "提交失败");
            return  addError("提交失败");
        }
        //跳转到列表页
        return  addResult("保存成功");
    }

    /**
     * 跳转到图片采购单列表页
     *
     * @return
     */
    @RequestMapping("/index.htm")
    public ModelAndView getPictureList(PicElectronicPlanOrderBussinessDto order, Page page, HttpServletRequest request) {
        Map<String, Object> model = new HashMap<>();
        int oldOffset=page.getOffset();
        page.setOffset((oldOffset>0?(page.getOffset()-1)*10:0));
        String startTime = order.getStartTime();
        if (startTime!=null){
            order.setStartTime(startTime+" 00:00:00");
        }
        String endTime = order.getEndTime();
        if (endTime!=null){
            order.setEndTime(endTime+" 23:59:59");
        }
        PageInfo<PicElectronicPlanOrderBussinessDto> list = null;
        Page<PicElectronicPlanOrderBussinessDto> pageInfo = new Page<>();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            order.setMerchantId(merchant.getId());
            list = picElectronicPlanOrderBussinessApi.getListForPc(order, oldOffset,page.getLimit());
        } catch (Exception e) {
            logger.error("查下图片电子计划单异常",e);
            return new ModelAndView("/planningSchedule/listPic.ftl");
        }
        pageInfo.setRequestUrl(this.getRequestUrl(request));
        pageInfo.setOffset(oldOffset);
        pageInfo.setRows(list==null?null:list.getList());
        pageInfo.setTotal(list==null?0:list.getTotal());
        model.put("pager", pageInfo);
        order.setStartTime(startTime);
        order.setEndTime(endTime);
        model.put("paramOrder",order);
        return new ModelAndView("/planningSchedule/listPic.ftl", model);
    }

    /**
     * 获取商户图片采购单详情
     *
     * @param id
     * @return
     */
    @RequestMapping("/details")
    @ResponseBody
    public Object getPictureDetails(Long id) {
        Map<String, Object> model = new HashMap<>();
        try {
            if (id == null || id <= 0) {
                return this.addError("查询条件为空");
            }
            PicElectronicPlanOrderBussinessDto order = picElectronicPlanOrderBussinessApi.picElectronicPlanOrderDetails(id);
            model.put(this.RESULT_STATUS, this.CODE_SUCCESS);
            model.put("data", order);
            return model;
        } catch (Exception e) {
            logger.error("查询商户图片采购单详情异常,e" + e);
            return this.addError("查无数据");
        }
    }

    /**
     * 拼接页面参数
     *
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
//    protected String getRequestUrl(HttpServletRequest request){
//        String url = "";
//        String requestUri = request.getRequestURI();
//        String queryString = request.getQueryString();
//        String qs = StringUtil.removeParameter(queryString, "offset");
//        if(requestUri.contains("/xyy-shop/")){
//            requestUri = requestUri.replace("/xyy-shop/", "/");
//        }
//        if (StringUtil.isNotEmpty(qs)) {
//            url = requestUri + "?" + EncodeUtil.urlDecode(qs,"UTF-8");
//        } else {
//            url = requestUri;
//        }
//        return url;
//    }

}
