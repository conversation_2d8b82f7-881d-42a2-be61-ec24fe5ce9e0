package com.xyy.ec.pc.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.marketing.hyperspace.api.ShopCouponForMerchantQueryApi;
import com.xyy.ec.merchant.bussiness.api.BalanceBussinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantAuthenticationBusinessApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.SignInRecordBussinessApi;
import com.xyy.ec.merchant.bussiness.api.account.JgBuryingPointApi;
import com.xyy.ec.merchant.bussiness.api.license.MerchantLicenseApi;
import com.xyy.ec.merchant.bussiness.api.user.UserMiddleBusinessApi;
import com.xyy.ec.merchant.bussiness.api.wxwork.WxWorkBusinessApi;
import com.xyy.ec.merchant.bussiness.base.ResultMessage;
import com.xyy.ec.merchant.bussiness.dto.*;
import com.xyy.ec.merchant.bussiness.dto.licence.LicenseValidDto;
import com.xyy.ec.merchant.bussiness.dto.merchant.AddMerchantResultDto;
import com.xyy.ec.merchant.bussiness.dto.user.UserInfoDTO;
import com.xyy.ec.merchant.bussiness.enums.PlatformEnum;
import com.xyy.ec.merchant.bussiness.enums.SystemFlagEnum;
import com.xyy.ec.merchant.bussiness.params.*;
import com.xyy.ec.merchant.bussiness.result.MerchantRelateShopResult;
import com.xyy.ec.merchant.bussiness.utils.AesSecurityUtils;
import com.xyy.ec.merchant.server.api.AccountCardApi;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.merchant.server.api.MerchantApi;
import com.xyy.ec.merchant.server.dto.*;
import com.xyy.ec.merchant.server.enums.AccountMerchantRelStatusEnum;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.business.api.OrderBusinessApi;
import com.xyy.ec.order.business.api.OrderDetailBusinessApi;
import com.xyy.ec.order.business.api.OrderExtendBusinessApi;
import com.xyy.ec.order.business.config.OrderEnum;
import com.xyy.ec.order.business.dto.ImageOrderDetailBusinessDto;
import com.xyy.ec.order.business.dto.OrderBusinessDto;
import com.xyy.ec.order.business.dto.OrderExtendBusinessDto;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.base.MerchantPrincipal;
import com.xyy.ec.pc.base.PasswordVerifier;
import com.xyy.ec.pc.common.helpers.MerchantClerkLicenseAuditImageParamHelper;
import com.xyy.ec.pc.common.params.AppMerchantClerkLicenseAuditImageParam;
import com.xyy.ec.pc.constants.CodeMapConstants;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.controller.vo.merchant.*;
import com.xyy.ec.pc.enums.MerchantStatusEnum;
import com.xyy.ec.pc.enums.TerminalTypeEnum;
import com.xyy.ec.pc.enums.XyyJsonResultCodeEnum;
import com.xyy.ec.pc.exception.AppException;
import com.xyy.ec.pc.exception.AuthenticationException;
import com.xyy.ec.pc.interceptor.helper.SpiderHelper;
import com.xyy.ec.pc.model.AddMerchantDto;
import com.xyy.ec.pc.model.Merchant;
import com.xyy.ec.pc.model.dto.XyyJsonResult;
import com.xyy.ec.pc.rpc.CodeItemServiceRpc;
import com.xyy.ec.pc.service.MerchantService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.*;
import com.xyy.ec.pc.util.ipip.IPUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 个人中心
 * @Author: WanKp
 * @Date: 2018/8/25 22:03
 **/
@RequestMapping("/merchant/center")
@Controller
public class MerchantCenterController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MerchantCenterController.class);
    //支付密码密钥
    @Value("${pay_pwd_security:FmpHxGc9no95cvd4}")
    private String payPwdSecurity;
    @Reference(version = "1.0.0")
    private MerchantBussinessApi merchantBussinessApi;
    @Reference(version = "1.0.0")
    private AccountCardApi accountCardApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;

    @Reference(version = "1.0.0")
    private OrderBusinessApi orderBusinessApi;

    @Reference(version = "1.0.0")
    private ShopCouponForMerchantQueryApi shopCouponForMerchantQueryApi;

    @Reference(version = "1.0.0")
    private SignInRecordBussinessApi signInRecordBussinessApi;

    @Reference(version = "1.0.0")
    private BalanceBussinessApi balanceBussinessApi;

    @Reference(version = "1.0.0")
    private OrderExtendBusinessApi orderExtendBusinessApi;

    @Autowired
    private CodeItemServiceRpc codeItemServiceRpc;

    @Reference(version = "1.0.0")
    private OrderDetailBusinessApi orderDetailBusinessApi;

    @Reference(version = "1.0.0")
    private MerchantAuthenticationBusinessApi merchantAuthenticationBusinessApi;

    @Reference(version = "1.0.0")
    private MerchantLicenseApi merchantLicenseApi;

    @Value("${ka.business.type}")
    private String kaBusinessType;

    @Value("${ka.branch}")
    private String kaBranch;

    @Value("${ka.balance.switch}")
    private Boolean kaBalanceSwitch;

    @Autowired
    private MerchantService merchantService;

    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;
    @Reference(version = "1.0.0")
    private MerchantApi merchantApi;
    @Reference(version = "1.0.0")
    private WxWorkBusinessApi wxWorkBusinessApi;
    @Resource
    private CrawlerUtil crawlerUtil;

    @Autowired
    private RedisTemplate redisTemplate;

    private static final String LICENSE_INDEX_DIALOG = "LICENSE_INDEX_DIALOG_PC:";

    @Autowired
    private SpiderHelper spiderHelper;

    @Reference(version = "1.0.0")
    private JgBuryingPointApi jgBuryingPointApi;
    @Reference(version = "1.0.0")
    private UserMiddleBusinessApi userMiddleBusinessApi;
    /**
     * 个人中心首页入口
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/index.htm", method = RequestMethod.GET)
    public ModelAndView index() throws Exception {
        Map<String, Object> model = new HashMap<String, Object>();
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        if (merchant == null) {
            return new ModelAndView(new RedirectView("/login/login.htm",true,false));
        }

        //商户信息
        Merchant m = new Merchant();
        BeanUtils.copyProperties(merchant, m);
        model.put("merchant", m);
        model.put("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);

        try {
            //订单信息
            OrderBusinessDto orderBusinessDto = new OrderBusinessDto();
            orderBusinessDto.setMerchantId(merchant.getId());
            orderBusinessDto.setVisibled(OrderBusinessDto.STATUS_VISIBLED);
            orderBusinessDto.setAccountRole(merchant.getAccountRole());
            //如果账号角色是子账号
            if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
                orderBusinessDto.setAccountId(merchant.getAccountId());
            } else {
                orderBusinessDto.setAccountId(null);
            }
            Map<String, Integer> resultMap = orderBusinessApi.findNumGroupByStatus(orderBusinessDto);
            model.put("waitPayNum", resultMap.get("waitPayNum"));
            model.put("waitShippingNum", resultMap.get("waitShippingNum"));
            model.put("shippingNum", resultMap.get("waitReceiveNum"));
            model.put("refundNum", resultMap.get("refundNum"));

            //请求订单列表
            OrderBusinessDto order = new OrderBusinessDto();
            order.setMerchantId(merchant.getId());
            order.setVisibled(order.STATUS_VISIBLED);
            order.setNoQueryStatus(OrderEnum.OrderStatus.DELETE.getId());
            //如果账号角色是子账号
            if (Objects.nonNull(merchant.getAccountRole()) && AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId().equals(merchant.getAccountRole())) {
                order.setAccountId(merchant.getAccountId());
            } else {
                order.setAccountId(null);
            }
            PageInfo<OrderBusinessDto> pageDto = new PageInfo();
            pageDto.setPageNum(1);
            pageDto.setPageSize(3);
            PageInfo<OrderBusinessDto> pageInfo_order = orderBusinessApi.selectPage(pageDto,order);
            List<OrderBusinessDto> orderList = pageInfo_order.getList();
            if (CollectionUtil.isNotEmpty(orderList)){
                checkOrder(orderList,order,merchant.getRegisterCode());
            }

            model.put("orderList",orderList);
            // TODO 优惠券数量 替换新接口
            int voucherCount = 0;
            ApiRPCResult<Integer> resultDTO = shopCouponForMerchantQueryApi.getMerchantNoUseVoucherCount(merchant.getId());
            if (resultDTO.getCode() == 200) {
                voucherCount  = resultDTO.getData();
            }
            model.put("voucherNum", voucherCount);

            // 积分
            ResultMessage<SignCenterInfoBussinessDto> resultMessage = signInRecordBussinessApi.findSignCenterInfo(m.getId());
            SignCenterInfoBussinessDto dto = resultMessage.getResult();
            model.put("pointCount", dto.getRemainingPoints());

            // 余额
            if (kaBalanceSwitch && kaBusinessType.contains("," + merchant.getBusinessType() + ",") && kaBranch.contains(merchant.getRegisterCode())) {
                model.put("balancemoney", 0);
            }else {
                BalanceBussinessDto balancetmp = balanceBussinessApi.selectByMerchant(m.getId());
                double balanceMoney = 0;
                if (balancetmp != null) {
                    balanceMoney = balancetmp.getBalance() == null ? 0 : balancetmp.getBalance().doubleValue();
                }
                model.put("balancemoney", balanceMoney);
            }

            // 签到信息
            if (CollectionUtil.isNotEmpty(dto.getSignDayArray())) {
                model.put("signDayArray", JSONArray.toJSONString(dto.getSignDayArray()));
            } else {
                model.put("signDayArray", JSONArray.toJSONString(new ArrayList<Integer>()));
            }
            model.put("center_menu", "merchantcenter");
            MerchantAuthenticationBusinessDto merchantAuthenticationBusinessDto = merchantAuthenticationBusinessApi.getInfoByMerchantId(merchant.getId());
            if(merchantAuthenticationBusinessDto != null)   model.put("status",merchantAuthenticationBusinessDto.getStatus());

            return new ModelAndView("/merchantCenter/index.ftl", model);
        } catch (Exception e) {
            logger.error("进入用户中心出错,e"+e);
            model.put("waitPayNum", 0);
            model.put("waitShippingNum", 0);
            model.put("shippingNum", 0);
            model.put("refundNum", 0);
            model.put("orderList",new ArrayList<OrderBusinessDto>());
            model.put("voucherNum", 0);
            model.put("pointCount", 0);
            model.put("balancemoney", 0);
            model.put("signDayArray", JSONArray.toJSONString(new ArrayList<Integer>()));
            model.put("center_menu", "merchantcenter");
            model.put("status",0);
            return new ModelAndView("/merchantCenter/index.ftl", model);
        }
    }

    /**
     * 关联店铺管理
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/connectShopManagement.htm", method = RequestMethod.GET)
    public ModelAndView connectShopManagement(ModelAndView modelAndView) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
        if (merchant == null) {
            return new ModelAndView(new RedirectView("/login/login.htm", true, false));
        }
        modelAndView.addObject("subAccount", Objects.equals(merchant.getAccountRole(), AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId()) ? 1 : 0);
        modelAndView.addObject("center_menu", "connectShopManagement");
        modelAndView.setViewName("/merchantCenter/connectShopManagement.ftl");
        return modelAndView;
    }

    /**
     * 商户签到
     *
     * @param merchantId
     * @return
     */
    @RequestMapping("/signIn.json")
    @ResponseBody
    public Object signIn(@RequestParam("merchantId") Long merchantId) {
        try {
            //签到
            signInRecordBussinessApi.signIn(merchantId);
            //刷新积分
            ResultMessage<SignCenterInfoBussinessDto> resultMessage = signInRecordBussinessApi.findSignCenterInfo(merchantId);
            SignCenterInfoBussinessDto dto = resultMessage.getResult();
            String[] key = {"continuityDays", "signPoint", "remainingPoints"};
            Object[] value = {dto.getContinuityDays(), dto.getSignPoint(), dto.getRemainingPoints()};
            return this.addResult(key, value);
        } catch (Exception e) {
            logger.error("签到失败", e);
            return this.addError("签到失败");
        }
    }

    /**
     * 刷新当前jvm的指定商品活动
     *
     * @param refreshId
     * @return
     */
    @RequestMapping("/refreshSkuPromoMap")
    @ResponseBody
    public Object refreshSkuPromoMap(@RequestParam("refreshId") int refreshId) {
        try {
//            Map<String, Object> result = this.addResult("指定商品活动缓存刷新成功");
//            List<Branch> listBranch = branchService.getAllBranchs();
//            for(Branch branch : listBranch) {
//                result.put(branch.getBranchCode(), skuService.refreshSkuPromoDTO(branch.getBranchCode()).size());
//            }
//            return result;
            this.addError("指定商品活动缓存刷新功能开发中");
        } catch (Exception e) {
            logger.error("刷新当前jvm的指定商品活动失败,e : {}", e);
        }
        return this.addError("指定商品活动缓存刷新失败");

    }

    private void checkOrder(List<OrderBusinessDto> orderList,OrderBusinessDto order,String branchCode){
        Map<String, String> paytypeMap = codeItemServiceRpc.findCodeMap(CodeMapConstants.ORDER_PAY_TYPE,order.getBranchCode());
        Map<String, String> statusMap = codeItemServiceRpc.findCodeMap(CodeMapConstants.ORDER_STATUS,order.getBranchCode());
        List<String> orderNo = orderList.stream().map(orderBusinessDto -> orderBusinessDto.getOrderNo()).collect(Collectors.toList());
        List<ImageOrderDetailBusinessDto> imageOrderDetailBusinessDtos = orderDetailBusinessApi.selectOrderDetailfirstImageUrl(orderNo);
        Map<String, String> imageUrl = imageOrderDetailBusinessDtos.stream().collect(Collectors.toMap(ImageOrderDetailBusinessDto::getOrderNo, ImageOrderDetailBusinessDto::getImageUrl));
        for (OrderBusinessDto orderltmp:orderList) {
            orderltmp.setImageUrl(imageUrl.get(orderltmp.getOrderNo()));
            orderltmp.setStatusName(statusMap.get(orderltmp.getStatus() + ""));
            orderltmp.setPayTypeName(paytypeMap.get(orderltmp.getPayType()+""));
            if(orderltmp.getPayChannel()!=null&&orderltmp.getPayChannel()>0) {
                orderltmp.setPayChannelName(OrderEnum.OrderPayChannelType.get(orderltmp.getPayChannel()));
            }
            if (orderltmp.getStatus() != null && orderltmp.getStatus() ==  OrderEnum.OrderMiddleStatus.STATUS_HAS_BEEN_SHIPPED.getId()) {
                //设置优惠卷的状态。
                orderltmp.setBalanceStatus(100);
                OrderExtendBusinessDto orderExtend = orderExtendBusinessApi.selectByOrderNo(orderltmp.getOrderNo());
                if (orderExtend != null) {
                    //获取字典表
                    Map<String,String> codeMap=codeItemServiceRpc.findCodeMap("BALANCE_INFO",branchCode);
                    if (orderExtend.getBalanceStatus() == 0 || orderExtend.getBalanceStatus() == 1) {
                        orderltmp.setBalanceStatus(orderExtend.getBalanceStatus());
                        //领取订单时候的文案
                        String balanceText = codeMap.get("BALANCE_TEXT").replace("balance", new DecimalFormat("#.##").format(orderExtend.getBalance()));
                        order.setBalanceText(balanceText);
                    }
                }
            }
        }
    }

    /**
     * 查询专属销售人员电话
     * @Title: salesInfo
     * @return
     * Object
     */
    @RequestMapping("/salesInfo.json")
    @ResponseBody
    public Object salesInfo() {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant != null && merchant.getId() != null){
                SalesBusinessDto  salesBusinessDto = merchantBussinessApi.getSalesInfo(merchant.getId());
                if (salesBusinessDto != null && StringUtils.isNotBlank(salesBusinessDto.getSalesPhone())){
                    return this.addResult("phone", salesBusinessDto.getSalesPhone());
                }
            }
        } catch (Exception e) {
            logger.error("查询专属销售电话异常,e="+ExceptionUtils.getStackTrace(e));
            return this.addError("查询专属销售电话异常");
        }
        return null;
    }

    /**
     * 资质过期/临期/正常 接口
     * @return
     */
    @ResponseBody
    @RequestMapping("/license/validity")
    public Object validity(){
       final String validity = "validity";
       Integer status = 0;
       try {
           MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
           if (merchant == null) {
               return this.addDataResult(validity,status);
           }
           logger.info("资质过期或者临期判断标识, 用户:{}",merchant.getId());
           ApiRPCResult<Integer> rs = merchantLicenseApi.licenseValidity(merchant.getId());
           if (rs==null){
               return this.addDataResult(validity,status);
           }
           status = rs.getData();
       }catch (Exception e){
           logger.error("资质过期或者临期判断标识接口异常",e);
       }
       return this.addDataResult(validity,status);
    }


    /**
     * 获取资质提醒  type有值说明是主页弹窗，一天弹一次
     */
    @RequestMapping("/license/validity/remind")
    @ResponseBody
    public Object validityRemind(@RequestParam(name = "type", required = false) String type) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant == null) {
                return this.addResult("data", null);
            }
            ApiRPCResult<LicenseValidDto> mapApiRPCResult = StringUtils.isEmpty(type) ?
                    merchantLicenseApi.licenseValidityRemind(merchant.getId()) :
                    merchantLicenseApi.licenseValidityRemindV3(merchant.getId(),4);
            if (mapApiRPCResult.getCode() != ApiResultCodeEum.SUCCESS.getCode()) {
                return this.addError(mapApiRPCResult.getMsg());
            }
            return this.addResult("data", mapApiRPCResult.getData());
        } catch (Exception e) {
            logger.error("获取资质提醒失败,e=" + org.apache.commons.lang.exception.ExceptionUtils.getStackTrace(e));
            return this.addError("获取资质提醒失败");
        }
    }

    /**
     * 关联店铺
     *
     * @param poiIdStr
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/relShop")
    public XyyJsonResult relateShop(@RequestParam(name = "poiId", required = false) String poiIdStr, HttpServletRequest request) {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getMerchantId();

            logger.info("关联店铺，开始，accountId：{}，merchantId：{}，poiIdStr：{}", accountId, merchantId, poiIdStr);
            // 入参合法性校验
            Long poiId = null;
            if (StringUtils.isNotEmpty(poiIdStr)) {
                try {
                    poiId = Long.parseLong(poiIdStr);
                } catch (Exception e) {
                }
            }
            boolean poiIdStrIsLegal = Objects.nonNull(poiId) && poiId > 0;
            logger.info("关联店铺，参数校验结果，poiIdStrIsLegal：{}，accountId：{}，merchantId：{}，poiIdStr：{}",
                    poiIdStrIsLegal, accountId, merchantId, poiIdStr);
            if (!poiIdStrIsLegal) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.PARAMETER_ERROR)
                        .msg("请选择店铺");
            }
            MerchantRelateShopParam param = MerchantRelateShopParam.builder().poiId(poiId).accountId(accountId)
                    .terminalType(TerminalTypeEnum.PC.getValue()).build();
            MerchantRelateShopResult result = merchantService.relateShop(param);
            logger.info("关联店铺，结果，result：{}，accountId：{}，merchantId：{}，poiIdStr：{}",
                    JSONObject.toJSONString(result), accountId, merchantId, poiIdStr);

            {
                // 由于在关联店铺后，若角色是店长角色则FE逻辑会跳到资质管理页面（/merchant/center/licenseAudit/findLicenseCategoryInfo.htm），故而这里在关联成功后需要保存cookie。
                if (Objects.equals(result.getAccountRole(), 1)) {
                    xyyIndentityValidator.setPrincipalMerchant(result.getMerchantId(), accountId);
                    String realIP = IPUtils.getClientIP(request);
                    logger.info("关联店铺，当前账号为店长角色，保存登录凭证，accountId:{}, merchantId:{}, realIP:{}", accountId, result.getMerchantId(), realIP);
                    // 判断是否需要登出做短信验证
                }
            }

            return XyyJsonResult.createSuccess()
                    .addResult("merchantId", result.getMerchantId())
                    .addResult("role", result.getAccountRole())
                    .msg("关联成功");
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("关联店铺失败，accountId：{}，merchantId：{}，poiIdStr：{}，msg：{}，异常信息：",
                        accountId, merchantId, poiIdStr, e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            logger.error("关联店铺失败，merchantId：{}，poiIdStr：{}，accountIdStr：{}，异常信息：",
                    accountId, merchantId, poiIdStr, e);
            return XyyJsonResult.createFailure().msg(Constants.MSG_ERROR);
        }
    }

    /**
     * 取消店铺关联
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/delRelShop")
    public XyyJsonResult cancelShopRelation(@RequestParam(name = "merchantId", required = false) String targetMerchantIdStr) {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getMerchantId();

            logger.info("取消店铺关联，开始，accountId：{}，merchantId：{}，targetMerchantIdStr：{}", accountId, merchantId, targetMerchantIdStr);
            // 入参合法性校验
            Long targetMerchantId = null;
            if (StringUtils.isNotEmpty(targetMerchantIdStr)) {
                try {
                    targetMerchantId = Long.parseLong(targetMerchantIdStr);
                } catch (Exception e) {
                }
            }
            boolean targetMerchantIdStrIsLegal = Objects.nonNull(targetMerchantId) && targetMerchantId > 0L;
            logger.info("取消店铺关联，参数校验结果，targetMerchantIdStrIsLegal：{}，accountId：{}，merchantId：{}，targetMerchantIdStr：{}",
                    targetMerchantIdStrIsLegal, accountId, merchantId, targetMerchantIdStr);
            if (!targetMerchantIdStrIsLegal) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.PARAMETER_ERROR)
                        .msg("请选择店铺");
            }
            MerchantCancelShopRelationParam param = MerchantCancelShopRelationParam.builder()
                    .merchantId(targetMerchantId).accountId(accountId).operator("用户（店员）").build();
            merchantService.cancelShopRelation(param);
            logger.info("取消店铺关联成功，accountId：{}，merchantId：{}，targetMerchantIdStr：{}", accountId, merchantId, targetMerchantIdStr);
            return XyyJsonResult.createSuccess().msg("已取消关联");
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("取消店铺关联失败，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，msg：{}，异常信息：",
                        accountId, merchantId, targetMerchantIdStr, e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            logger.error("取消店铺关联失败，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，异常信息：",
                    accountId, merchantId, targetMerchantIdStr, e);
            return XyyJsonResult.createFailure().msg(Constants.MSG_ERROR);
        }
    }

    /**
     * 添加店员资质审核信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/uploadAuthorization")
    public XyyJsonResult addClerkLicenseAudit(@RequestParam(name = "merchantId", required = false) String targetMerchantIdStr,
                                              @RequestParam(name = "licenseAuditImgListStr", required = false) String licenseAuditImagesJsonStr) {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            merchantId = merchant.getMerchantId();
            accountId = merchant.getAccountId();

            logger.info("添加店员资质审核信息，开始，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，licenseAuditImagesJsonStr：{}",
                    accountId, merchantId, targetMerchantIdStr, licenseAuditImagesJsonStr);
            // 入参合法性校验
            Long targetMerchantId = null;
            List<AppMerchantClerkLicenseAuditImageParam> appImageParams = null;
            if (!org.springframework.util.StringUtils.isEmpty(targetMerchantIdStr)) {
                try {
                    targetMerchantId = Long.parseLong(targetMerchantIdStr);
                } catch (Exception e) {
                }
            }
            if (!org.springframework.util.StringUtils.isEmpty(licenseAuditImagesJsonStr)) {
                try {
                    appImageParams = JSONArray.parseArray(licenseAuditImagesJsonStr, AppMerchantClerkLicenseAuditImageParam.class);
                } catch (Exception e) {
                }
            }
            List<MerchantClerkLicenseAuditImageParam> imageParams = MerchantClerkLicenseAuditImageParamHelper.creates(appImageParams);
            boolean targetMerchantIdStrIsLegal = Objects.nonNull(targetMerchantId) && targetMerchantId > 0L;
            boolean licenseAuditImagesJsonStrIsLegal = CollectionUtils.isNotEmpty(imageParams);
            logger.info("添加店员资质审核信息，参数校验结果，targetMerchantIdStrIsLegal：{}，licenseAuditImagesJsonStrIsLegal：{}，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，licenseAuditImagesJsonStr：{}",
                    targetMerchantIdStrIsLegal, licenseAuditImagesJsonStrIsLegal, accountId, merchantId, targetMerchantIdStr, licenseAuditImagesJsonStr);
            if (!targetMerchantIdStrIsLegal) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.PARAMETER_ERROR)
                        .msg("请选择店铺");
            }
            if (!licenseAuditImagesJsonStrIsLegal) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.PARAMETER_ERROR)
                        .msg("请上传图片");
            }
            MerchantAddClerkLicenseAuditParam param = MerchantAddClerkLicenseAuditParam.builder()
                    .merchantId(targetMerchantId).accountId(accountId).imageParams(imageParams).build();
            merchantService.addClerkLicenseAudit(param);
            logger.info("添加店员资质审核信息成功，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，licenseAuditImagesJsonStr：{}",
                    accountId, merchantId, targetMerchantIdStr, licenseAuditImagesJsonStr);
            return XyyJsonResult.createSuccess().msg("添加店员资质审核信息成功");
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("添加店员资质审核信息失败，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，licenseAuditImagesJsonStr：{}，msg：{}，异常信息：",
                        accountId, merchantId, targetMerchantIdStr, licenseAuditImagesJsonStr, e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            logger.error("添加店员资质审核信息失败，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，licenseAuditImagesJsonStr：{}，异常信息：",
                    accountId, merchantId, targetMerchantIdStr, licenseAuditImagesJsonStr, e);
            return XyyJsonResult.createFailure().msg(Constants.MSG_ERROR);
        }
    }

    /**
     * 获取店员资质审核信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/getUploadAuthorization")
    public XyyJsonResult getClerkLicenseAuditInfo(@RequestParam(name = "merchantId", required = false) String targetMerchantIdStr) {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            merchantId = merchant.getMerchantId();
            accountId = merchant.getAccountId();
            if (logger.isDebugEnabled()) {
                logger.debug("获取店员资质审核信息，开始，accountId：{}，merchantId：{}，targetMerchantIdStr：{}",
                        accountId, merchantId, targetMerchantIdStr);
            }
            // 入参合法性校验
            Long targetMerchantId = null;
            List<AppMerchantClerkLicenseAuditImageParam> appImageParams = null;
            if (!org.springframework.util.StringUtils.isEmpty(targetMerchantIdStr)) {
                try {
                    targetMerchantId = Long.parseLong(targetMerchantIdStr);
                } catch (Exception e) {
                }
            }
            boolean targetMerchantIdStrIsLegal = Objects.nonNull(targetMerchantId) && targetMerchantId > 0L;
            if (logger.isDebugEnabled()) {
                logger.debug("获取店员资质审核信息，参数校验结果，targetMerchantIdStrIsLegal：{}，accountId：{}，merchantId：{}，targetMerchantIdStr：{}",
                        targetMerchantIdStrIsLegal, accountId, merchantId, targetMerchantIdStr);
            }
            if (!targetMerchantIdStrIsLegal) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.PARAMETER_ERROR)
                        .msg("请选择店铺");
            }
            MerchantGetClerkLicenseAuditInfoParam param = MerchantGetClerkLicenseAuditInfoParam.builder()
                    .merchantId(targetMerchantId).accountId(accountId).build();
            MerchantClerkLicenseAuditInfoVO clerkLicenseAuditInfo = merchantService.getClerkLicenseAuditInfo(param);
            if (logger.isDebugEnabled()) {
                logger.debug("获取店员资质审核信息成功，accountId：{}，merchantId：{}，targetMerchantIdStr：{}",
                        accountId, merchantId, targetMerchantIdStr);
            }
            return XyyJsonResult.createSuccess().addResult("list", clerkLicenseAuditInfo.getImages());
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("获取店员资质审核信息失败，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，msg：{}，异常信息：",
                        accountId, merchantId, targetMerchantIdStr, e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            logger.error("获取店员资质审核信息失败，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，异常信息：",
                    accountId, merchantId, targetMerchantIdStr, e);
            return XyyJsonResult.createFailure().msg(Constants.MSG_ERROR);
        }
    }

    /**
     * 获取当前登录的账号信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/getLoginAccountInfo")
    public XyyJsonResult getLoginAccountInfo() {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            merchantId = merchant.getMerchantId();
            accountId = merchant.getAccountId();
            if (logger.isDebugEnabled()) {
                logger.debug("获取当前登录的账号信息，开始，accountId：{}，merchantId：{}", accountId, merchantId);
            }
            LoginAccountDto loginAccountDto = loginAccountApi.selectLoginAccountById(accountId);
            String accountMobile = Objects.nonNull(loginAccountDto) ? loginAccountDto.getMobile() : null;
            MerchantAccountInfoVO merchantAccountInfoVO = MerchantAccountInfoVO.builder()
                    .accountId(accountId)
                    .merchantId(merchantId)
                    .role(merchant.getAccountRole())
                    .mobile(accountMobile).build();
            if (logger.isDebugEnabled()) {
                logger.debug("获取当前登录的账号信息成功，accountId：{}，merchantId：{}，merchantAccountInfoVO：{}",
                        accountId, merchantId, JSONObject.toJSONString(merchantAccountInfoVO));
            }
            return XyyJsonResult.createSuccess().addResult("info", merchantAccountInfoVO);
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("获取当前登录的账号信息失败，accountId：{}，merchantId：{}，msg：{}，异常信息：", accountId, merchantId, e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            logger.error("获取当前登录的账号信息失败，accountId：{}，merchantId：{}，异常信息：", accountId, merchantId, e);
            return XyyJsonResult.createFailure().msg(Constants.MSG_ERROR);
        }
    }

    /**
     * 分页查询账号关联的店铺
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/account/merchantList")
    public XyyJsonResult listAccountRelatedMerchants(@RequestParam(name = "isOnlyCanDirectLogin", required = false) String isOnlyCanDirectLoginStr,
                                                     @RequestParam(name = "pageNo", required = false) String pageNoStr,
                                                     @RequestParam(name = "pageSize", required = false) String pageSizeStr) {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getMerchantId();
            if (logger.isDebugEnabled()) {
                logger.debug("分页查询账号关联的店铺，开始，accountId：{}，merchantId：{}，isOnlyCanDirectLoginStr：{}，pageNoStr：{}，pageSizeStr：{}",
                        accountId, merchantId, isOnlyCanDirectLoginStr, pageNoStr, pageSizeStr);
            }
            // 入参合法性校验
            Boolean isOnlyCanDirectLogin = null;
            Integer pageNo = null;
            Integer pageSize = null;
            if (StringUtils.isNotEmpty(isOnlyCanDirectLoginStr)) {
                try {
                    isOnlyCanDirectLogin = Boolean.parseBoolean(isOnlyCanDirectLoginStr);
                } catch (Exception e) {
                }
            }
            if (StringUtils.isNotEmpty(pageNoStr)) {
                try {
                    pageNo = Integer.parseInt(pageNoStr);
                } catch (Exception e) {
                }
            }
            if (StringUtils.isNotEmpty(pageSizeStr)) {
                try {
                    pageSize = Integer.parseInt(pageSizeStr);
                } catch (Exception e) {
                }
            }
            boolean pageNoStrIsLegal = Objects.nonNull(pageNo) && pageNo > 0;
            boolean pageSizeStrIsLegal = Objects.nonNull(pageSize) && pageSize > 0;
            if (logger.isDebugEnabled()) {
                logger.debug("分页查询账号关联的店铺，参数校验结果，pageNoStrIsLegal：{}，pageSizeStrIsLegal：{}，accountId：{}，merchantId：{}，isOnlyCanDirectLoginStr：{}，pageNoStr：{}，pageSizeStr：{}",
                        pageNoStrIsLegal, pageSizeStrIsLegal, accountId, merchantId, isOnlyCanDirectLoginStr, pageNoStr, pageSizeStr);
            }
            if (!pageNoStrIsLegal) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.PARAMETER_ERROR)
                        .msg("翻页错误，请重新加载页面");
            }
            if (!pageSizeStrIsLegal) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.PARAMETER_ERROR)
                        .msg("翻页错误，请重新加载页面");
            }
            MerchantListAccountRelatedMerchantsQueryParam queryParam = MerchantListAccountRelatedMerchantsQueryParam.builder()
                    .accountId(accountId).isOnlyCanDirectLogin(isOnlyCanDirectLogin).pageNum(pageNo).pageSize(pageSize).build();
            PageInfo<MerchantAccountRelatedMerchantInfoVO> pageInfo = merchantService.listAccountRelatedMerchants(queryParam);
            if (logger.isDebugEnabled()) {
                logger.debug("分页查询账号关联的店铺，结果，pageInfo：{}，accountId：{}，merchantId：{}，isOnlyCanDirectLoginStr：{}，pageNoStr：{}，pageSizeStr：{}",
                        JSONObject.toJSONString(pageInfo), accountId, merchantId, isOnlyCanDirectLoginStr, pageNoStr, pageSizeStr);
            }
            return XyyJsonResult.createSuccess()
                    .addResult("list", pageInfo.getList())
                    .addResult("total", pageInfo.getTotal())
                    .addResult("pages", pageInfo.getPages());
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("分页查询账号关联的店铺失败，accountId：{}，merchantId：{}，isOnlyCanDirectLoginStr：{}，pageNoStr：{}，pageSizeStr：{}，msg：{}，异常信息：",
                        accountId, merchantId, isOnlyCanDirectLoginStr, pageNoStr, pageSizeStr, e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            logger.error("分页查询账号关联的店铺失败，accountId：{}，merchantId：{}，isOnlyCanDirectLoginStr：{}，pageNoStr：{}，pageSizeStr：{}，异常信息：",
                    accountId, merchantId, isOnlyCanDirectLoginStr, pageNoStr, pageSizeStr, e);
            return XyyJsonResult.createFailure().msg(Constants.MSG_ERROR);
        }
    }

    /**
     * 分页查询店铺的店员信息
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/clerk")
    public XyyJsonResult listMerchantClerks(@RequestParam(name = "merchantId", required = false) String targetMerchantIdStr,
                                            @RequestParam(name = "pageNo", required = false) String pageNoStr,
                                            @RequestParam(name = "pageSize", required = false) String pageSizeStr) {
        Long merchantId = null;
        Long accountId = null;
        try {
            // 从鉴权拦截器或上下文中获取当前登录账号ID和药店编号
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (Objects.isNull(merchant)) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            accountId = merchant.getAccountId();
            merchantId = merchant.getMerchantId();
            if (logger.isDebugEnabled()) {
                logger.debug("分页查询店铺的店员信息，开始，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，pageNoStr：{}，pageSizeStr：{}",
                        accountId, merchantId, targetMerchantIdStr, pageNoStr, pageSizeStr);
            }
            // 入参合法性校验
            Long targetMerchantId = null;
            Integer pageNo = null;
            Integer pageSize = null;
            if (StringUtils.isNotEmpty(targetMerchantIdStr)) {
                try {
                    targetMerchantId = Long.parseLong(targetMerchantIdStr);
                } catch (Exception e) {
                }
            }
            if (StringUtils.isNotEmpty(pageNoStr)) {
                try {
                    pageNo = Integer.parseInt(pageNoStr);
                } catch (Exception e) {
                }
            }
            if (StringUtils.isNotEmpty(pageSizeStr)) {
                try {
                    pageSize = Integer.parseInt(pageSizeStr);
                } catch (Exception e) {
                }
            }
            boolean targetMerchantIdStrIsLegal = Objects.nonNull(targetMerchantId) && targetMerchantId > 0L;
            boolean pageNoStrIsLegal = Objects.nonNull(pageNo) && pageNo > 0;
            boolean pageSizeStrIsLegal = Objects.nonNull(pageSize) && pageSize > 0;
            if (logger.isDebugEnabled()) {
                logger.debug("分页查询店铺的店员信息，参数校验结果，targetMerchantIdStrIsLegal：{}，pageNoStrIsLegal：{}，pageSizeStrIsLegal：{}，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，pageNoStr：{}，pageSizeStr：{}",
                        targetMerchantIdStrIsLegal, pageNoStrIsLegal, pageSizeStrIsLegal, accountId, merchantId, targetMerchantIdStr, pageNoStr, pageSizeStr);
            }
            if (!targetMerchantIdStrIsLegal) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.PARAMETER_ERROR)
                        .msg("请选择店铺");
            }
            if (!pageNoStrIsLegal) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.PARAMETER_ERROR)
                        .msg("翻页错误，请重新加载页面");
            }
            if (!pageSizeStrIsLegal) {
                return XyyJsonResult.createFailure(XyyJsonResultCodeEnum.PARAMETER_ERROR)
                        .msg("翻页错误，请重新加载页面");
            }
            MerchantListMerchantClerksQueryParam queryParam = MerchantListMerchantClerksQueryParam.builder()
                    .merchantId(targetMerchantId).accountId(accountId).pageNum(pageNo).pageSize(pageSize).build();
            PageInfo<MerchantClerkInfoVO> pageInfo = merchantService.listMerchantClerks(queryParam);
            if (logger.isDebugEnabled()) {
                logger.debug("分页查询店铺的店员信息，结果，pageInfo：{}，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，pageNoStr：{}，pageSizeStr：{}",
                        JSONObject.toJSONString(pageInfo), accountId, merchantId, targetMerchantIdStr, pageNoStr, pageSizeStr);
            }
            return XyyJsonResult.createSuccess()
                    .addResult("list", pageInfo.getList())
                    .addResult("total", pageInfo.getTotal())
                    .addResult("pages", pageInfo.getPages());
        } catch (AppException e) {
            if (e.isWarn()) {
                logger.error("分页查询店铺的店员信息失败，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，pageNoStr：{}，pageSizeStr：{}，msg：{}，异常信息：",
                        accountId, merchantId, targetMerchantIdStr, pageNoStr, pageSizeStr, e.getMsg(), e);
            }
            return XyyJsonResult.createFailure().appName(e.getAppName()).code(e.getCode()).msg(e.getMsg());
        } catch (Exception e) {
            logger.error("分页查询店铺的店员信息失败，accountId：{}，merchantId：{}，targetMerchantIdStr：{}，pageNoStr：{}，pageSizeStr：{}，异常信息：",
                    accountId, merchantId, targetMerchantIdStr, pageNoStr, pageSizeStr, e);
            return XyyJsonResult.createFailure().msg(Constants.MSG_ERROR);
        }
    }

    /**
     * 切换店铺
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/selectMerchant", method = RequestMethod.POST)
    @ResponseBody
    public Object selectMerchant(HttpServletRequest request,Long merchantId) throws Exception {
        MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
        if(merchant == null){
            return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
        }
        AccountMerchantRelDto accountMerchantRelDto = loginAccountApi.selectByAccountIdAndMerchantId(merchant.getAccountId(),merchantId);
        if(accountMerchantRelDto == null){
            return XyyJsonResult.createFailure().msg("非法入参");
        }
        if(accountMerchantRelDto.getStatus() != AccountMerchantRelStatusEnum.PASS.getValue()){
            return XyyJsonResult.createFailure().msg("店铺审核中或委托书未上传");
        }
        MerchantBussinessDto bussinessDto = merchantService.getMerchant(merchantId);
        if(bussinessDto == null){
            return XyyJsonResult.createFailure().msg("店铺非法");
        }
        if(bussinessDto.getStatus() != MerchantStatusEnum.STATUS_NORMAL.getId()){
            return XyyJsonResult.createFailure().msg("该店铺已冻结，请选择其他店铺");
        }

        String realIP = IPUtils.getClientIP(request);
        logger.info("登录用户id:{}", merchantId + "_" + realIP);
        //设置店铺信息到cookie
        xyyIndentityValidator.setPrincipalMerchant(merchantId, merchant.getAccountId());
        // 判断是否需要登出做短信验证
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("isCrawler", false);
        if (spiderHelper.getNewSpiderInterceptionOpen()) {
            if (spiderHelper.needSmsVerify(merchant.getAccountId().toString())) {
                LoginAccountDto loginAccountDto = loginAccountApi.selectLoginAccountById(merchant.getAccountId());
                if (loginAccountDto != null) {
                    dataMap.put("mobile", loginAccountDto.getMobile());
                }
                dataMap.put("isCrawler", true);
            }
        }
        XyyJsonResult xyyJsonResult = XyyJsonResult.createSuccess();
        xyyJsonResult.setData(dataMap);
        CookieUtils.setJgLogin();
        return xyyJsonResult;
    }

    /**
     * 判断用户是否需要登出做短信验证
     *
     * @param merchantId
     * @param ip
     * @return 0:不需要登出; 1:是爬虫; 2:是 KICK_OUT 账号
     */
    private int needSmsVerify(Long merchantId, String ip) {

        // IP白名单用户(北京、武汉办公室等)，不需要短信验证
        if (crawlerUtil.isWhiteIP(merchantId, ip)) return 0;

        // 如果是爬虫，需要短信验证
        if (crawlerUtil.isCrawler(merchantId, ip)) {
            logger.info("发现爬虫账号，需要登出，做短信验证。merchantId:{}, ip:{}", merchantId, ip);
            return 1;
        }

        // 白名单用户不需要检查kick out
        if (crawlerUtil.isWhiteUser(merchantId, ip)) {
            return 0;
        }

        // 如果是KICK_OUT用户，需要登出，做短信验证
        if (crawlerUtil.isKickOut(merchantId, ip)) {
            logger.info("发现KICK_OUT请求，需要登出，做短信验证。merchantId:{}, ip:{}", merchantId, ip);
            return 2;
        }
        return 0;
    }


    /**
     * 添加店铺
     *
     * @param addMerchantDto
     * @return
     */
    @RequestMapping("/add")
    @ResponseBody
    public Object addMerchant(HttpServletRequest request, AddMerchantDto addMerchantDto) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if(merchant == null){
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            Long accountId = merchant.getAccountId();
            MerchantBussinessDto merchantBussinessDto = new MerchantBussinessDto();
            BeanUtils.copyProperties(addMerchantDto,merchantBussinessDto);
            merchantBussinessDto.setAccountId(accountId);
            merchantBussinessDto.setRealName(addMerchantDto.getName());
            merchantBussinessDto.setNickname(addMerchantDto.getName());
            ApiRPCResult<AddMerchantResultDto> res = merchantBussinessApi.addMerchant(merchantBussinessDto);
            logger.info("addMerchant req:{}, resp:{}", JsonUtil.toJson(merchantBussinessDto), JsonUtil.toJson(res));
            if(!res.isSuccess()){
                return this.addError(res.getErrMsg());
            }
            AddMerchantResultDto addMerchantResultDto = res.getData();
            String json = JsonUtil.toJson(addMerchantResultDto);
            // 上报店铺注册事件到极光
            if (addMerchantResultDto.getMerchantId() != null) {
                try {
                    jgBuryingPointApi.profileSetLoginOrRegister(addMerchantResultDto.getMerchantId(), PlatformEnum.PC.getKey());
                }
                catch (Exception e) {
                    logger.error("注册埋点数据上报异常, merchantId -> {}", addMerchantResultDto.getMerchantId(), e);
                }
            }
            return this.addResult("data", JsonUtil.jsonToMap(json));
        } catch (Exception e) {
            logger.error("添加店铺失败,accountId:{}", addMerchantDto.getAccountId(), e);
            return this.addError("添加店铺失败");
        }
    }


    /**
     * 店铺信息查询
     * @param request
     * @return
     */
    @RequestMapping("/getSimpleMerchantInfo")
    @ResponseBody
    public Object getSimpleMerchantInfo(HttpServletRequest request, Long merchantId) {
        if(merchantId == null){
            return this.addError("merchantId不能为空");
        }
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if(merchant == null){
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            Long accountId = merchant.getAccountId();
            ApiRPCResult<SimpleMerchantDto> rpcResult = merchantApi.findMerchantInfo(accountId, merchantId);
            if(!rpcResult.isSuccess()){
                return this.addError(rpcResult.getErrMsg());
            }
            String json = JsonUtil.toJson(rpcResult.getData());
            return this.addResult("data", JsonUtil.jsonToMap(json));
        } catch (Exception e) {
            logger.error("店铺信息查询失败",  e);
            return this.addError("店铺信息查询失败");
        }
    }

    /**
     * 我的绑卡列表
     * @return
     */
    @RequestMapping("/getMyCards")
    @ResponseBody
    public Object getMyCards() {
        Long accountId = null;
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if(merchant == null){
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            accountId = merchant.getAccountId();
            final ApiRPCResult<List<BankCardDto>> result = accountCardApi.queryMyBankCards(merchant.getAccountId());
            if (result.isFail()) {
                return this.addError(result.getErrMsg());
            }
            return this.addResult("data", result.getData());
        } catch (Exception e) {
            logger.error("getMyCards accountId:{} e", accountId, e);
            return this.addError("我的绑卡列表查询失败,请稍后重试");
        }
    }
    /**
     * 验证支付密码
     *
     * @param
     * @return
     */
    @RequestMapping("/checkPayPwd")
    @ResponseBody
    public Object checkPayPwd(@RequestParam(value = "orderId", required = false) String orderId,
                              @RequestParam(value = "tranNo", required = false) String tranNo,
                              @RequestParam(value = "pwd", required = true) String pwd) {
        Long accountId = null;
        try {
            //支付密码解密
            pwd = AesSecurityUtils.AesEncrypt(payPwdSecurity, pwd);
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (merchant == null) {
                return XyyJsonResult.createFailure().msg("未登录，请重新登录！");
            }
            accountId = merchant.getAccountId();
            //如果是购物金 验证支付密码
            Integer type = StringUtils.isEmpty(orderId) && StringUtil.isNotEmpty(tranNo) ? 1 : 0;
            ApiRPCResult<CheckPayPwdReturnDto> result = loginAccountApi.checkPayPwdV3(accountId, pwd, StringUtil.isNotBlank(orderId) ? orderId : tranNo, null, null, type);
            if (result.isFail()) {
                return this.addError(result.getErrMsg());
            }
            return this.addResult("data", result.getData());
        } catch (Exception e) {
            logger.error("checkPayPwd accountId:{} e", accountId, e);
            return this.addError("验证支付密码异常");
        }
    }


    /**
     * 查询是否设置了支付密码
     * @return
     */
    @RequestMapping("/payPwd/queryState")
    @ResponseBody
    public Object queryState() {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (merchant == null) {
                return this.addError("设置支付密码异常");
            }
            ApiRPCResult<QueryStateReturnDto> result = loginAccountApi.queryState(merchant.getAccountId());
            if (result.isFail()) {
                return this.addError(result.getErrMsg());
            }
            return this.addResult("data", result.getData());
        } catch (Exception e) {
            return this.addError("查询是否设置支付密码异常");
        }
    }

    /**
     * 设置支付密码
     *
     * @return
     */
    @RequestMapping("/setPayPwd")
    @ResponseBody
    public Object setPayPwd(PaymentPasswordParamDto param) {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (merchant == null) {
                return this.addError("设置支付密码异常");
            }
            //支付密码解密
          //  param.setMobile(merchant.getMobile());
            param.setAccountId(merchant.getAccountId());
            param.setPwd(AesSecurityUtils.AesEncrypt(payPwdSecurity, param.getPwd()));
            ApiRPCResult result = loginAccountApi.setPayPwd(param);
            if (result.isFail()) {
                return this.addError(result.getErrMsg());
            }
            return this.addResult("data", result.getData());
        } catch (Exception e) {
            return this.addError("设置支付密码异常");
        }
    }


    /**
     * 设置支付密码
     * @return
     */
    @RequestMapping("/setPayPwd/index")
    @ResponseBody
    public Object setPwdIndex(){
        try {
            ModelMap modelMap = new ModelMap();
            return new ModelAndView("/password/payPassword.ftl", modelMap);
        } catch (Exception e) {
            logger.error("setPayPwd error", e);
            return new ModelAndView("/error/500.ftl");
        }
    }

    /**
     * 发送验证码
     * @return
     */
    @RequestMapping("/sendVerifyCode")
    @ResponseBody
    public Object sendVerifyCode() {
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentAccountPrincipal();
            if (merchant == null) {
                return this.addError("设置支付密码异常");
            }
            LoginAccountDto loginAccountDto = loginAccountApi.selectLoginAccountById(merchant.getAccountId());
            String accountMobile = Objects.nonNull(loginAccountDto) ? loginAccountDto.getMobile() : null;
            ApiRPCResult<PaymentPasswordReturnDto> result = loginAccountApi.sendVerifyCode(accountMobile);
            if (result.isFail()) {
                return this.addError(result.getErrMsg());
            }
            return this.addResult("data", result.getData());
        } catch (Exception e) {
            return this.addError("发送验证码异常");
        }
    }

    /**
     * 查询专属销售人员二维码
     * @Title: salesInfo
     * @return
     * Object
     */
    @RequestMapping("/salesInfo/qrCode")
    @ResponseBody
    public Object getSalesQrCode() {
        SalesInfoVo salesInfoVo = new SalesInfoVo();
        try {
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            if (merchant != null && merchant.getId() != null) {
                //1button 2二维码
                ApiRPCResult<BdContactWayDto> result = wxWorkBusinessApi.getOrAddBdContactWay(merchant.getId(), 2);
                if (result != null && result.isSuccess()) {


                    String qrCode = result.getData().getQrCode();
                    Long oaId = result.getData().getOaId();
                    salesInfoVo.setQrCode(qrCode);
                    List<UserInfoDTO> userInfoDTOList = userMiddleBusinessApi.queryMeuserByOaIds(Collections.singletonList(oaId), SystemFlagEnum.XYY_EC_APP.getSystemFlag());
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(userInfoDTOList)) {
                        UserInfoDTO userInfoDTO = userInfoDTOList.get(0);
                        salesInfoVo.setPhone(userInfoDTO.getMobile());
                        salesInfoVo.setName(userInfoDTO.getRealname());
                        salesInfoVo.setDesc("有事您找我");
                    }

                    return this.addResult("data", salesInfoVo);
                }
            }
        } catch (Exception e) {
            logger.error("查询专属销售二维码异常,e=" + ExceptionUtils.getStackTrace(e));
            return this.addError("查询专属销售二维码异常");
        }
        return null;
    }
}