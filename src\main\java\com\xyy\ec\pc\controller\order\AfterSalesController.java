package com.xyy.ec.pc.controller.order;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.ec.api.rpc.order.OrderRpcService;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.cs.api.order.CsOrderRefundApi;
import com.xyy.ec.merchant.bussiness.api.MerchantBussinessApi;
import com.xyy.ec.merchant.bussiness.api.crm.InvoiceBussinessCrmApi;
import com.xyy.ec.merchant.bussiness.dto.InvoiceTypeBussinessDto;
import com.xyy.ec.cs.api.order.CsOrderRefundApi;
import com.xyy.ec.merchant.bussiness.dto.MerchantBussinessDto;
import com.xyy.ec.merchant.server.api.LoginAccountApi;
import com.xyy.ec.merchant.server.dto.LoginAccountDto;
import com.xyy.ec.order.api.afterSales.AfterSalesApi;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesDetailVo;
import com.xyy.ec.order.business.dto.afterSales.AfterSalesQueryParam;
import com.xyy.ec.order.dto.afterSales.AfterSalesOperateParam;
import com.xyy.ec.order.dto.afterSales.AfterSalesProcessDto;
import com.xyy.ec.order.dto.afterSales.CredentialApplyParam;
import com.xyy.ec.order.dto.afterSales.InvoiceApplyParam;
import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.Config;
import com.xyy.ec.pc.config.XyyConfig;
import com.xyy.ec.pc.controller.vo.afterSales.ErpInvoiceVo;
import com.xyy.ec.pc.service.AfterSalesService;
import com.xyy.ec.pc.service.XyyIndentityValidator;
import com.xyy.ec.pc.util.DateUtil;
import com.xyy.ec.pc.util.FileUploadUtil;
import com.xyy.ec.pc.vo.order.AfterSalesRouteVo;
import com.xyy.ec.pop.server.api.shop.api.ShopNoticeApi;
import com.xyy.ec.pop.server.api.shop.dto.ReturnNoticeDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 售后
 */
@Controller
@RequestMapping("/app/afterSales/")
public class AfterSalesController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(AfterSalesController.class);

    @Value("${afterSalesInvoiceApplyType}")
    private String afterSalesInvoiceApplyType;
    @Value("${credentialType}")
    private String credentialType;

    @Value("${incorrectInvoiceType}")
    private String incorrectInvoiceType;

    @Autowired
    private XyyConfig.CdnConfig cdnConfig;

    @Autowired
    private Config config;

    @Value("${ossDomain:https://oss-ec-test.ybm100.com/}")
    public String ossDomain;

    @Resource
    private AfterSalesService afterSalesService;
    @Reference(version = "1.0.0")
    private AfterSalesApi afterSalesApi;
    @Reference(version = "1.0.0")
    private ShopNoticeApi shopNoticeApi;
    @Reference(version = "1.0.0")
    private LoginAccountApi loginAccountApi;
    @Autowired
    private XyyIndentityValidator xyyIndentityValidator;
    @Reference
    private MerchantBussinessApi merchantBussinessApi;
/*    @Autowired
    private PcCodeitemServiceConfig codeitemServiceConfig;*/
    @Autowired
    OrderRpcService orderRpcService;
    @Reference(version = "1.0.0")
    private InvoiceBussinessCrmApi invoiceBussinessCrmApi;

    @Reference(version = "1.0.0")
    private CsOrderRefundApi csOrderRefundApi;
    /**
     * 售后主页
     *
     * @return
     */
    @RequestMapping("/index")
    @ResponseBody
    public Object toIndex(AfterSalesRouteVo routeVo,HttpServletRequest request) {
        try {
            final StringBuffer requestURL = request.getRequestURL();
            log.error("toIndex requestURL {}", requestURL.toString());
            log.error("toIndex requestURL2 {}", request.getRequestURI());
            MerchantBussinessDto merchant = (MerchantBussinessDto) xyyIndentityValidator.currentPrincipal();
            ModelMap modelMap = new ModelMap();
            modelMap.put("accountId", merchant.getAccountId());
            modelMap.put("routeVo", routeVo);
            modelMap.put("redirectUrl", request.getRequestURI());
            return new ModelAndView("/order/afterSales.ftl", modelMap);
        } catch (Exception e) {
            log.error("toIndex error ", e);
            return new ModelAndView("/error/500.ftl");
        }
    }
    /**
     * 发票售后申请
     * @return
     */
    @RequestMapping(value = "/invoiceApply", method = RequestMethod.POST)
    @ResponseBody
    public Object invoiceApply(@RequestBody InvoiceApplyParam param) {
        try {
            log.info("invoiceApply {}", JSONObject.toJSONString(param));
            final String msg = param.checkParam();
            if (StringUtils.isNotEmpty(msg)) {
                return addError(msg);
            }
            param.setTerminalType(4);
            LoginAccountDto loginAccountDto = loginAccountApi.selectLoginAccountById(param.getAccountId());
            param.setOperator(loginAccountDto.getMobile());
            final ApiRPCResult apiRPCResult = afterSalesApi.invoiceAfterSalesApply(param);
            if (apiRPCResult.isFail()) {
                return this.addError(apiRPCResult.getErrMsg());
            }
            return this.addResult("data", null);
        } catch (Exception e) {
            log.error("invoiceApply {}", JSONObject.toJSONString(param),e);
            return this.addError("保存失败");
        }

    }
    /**
     * 发票售后申请
     * @return
     */
    @RequestMapping(value = "/saveOperate", method = RequestMethod.POST)
    @ResponseBody
    public Object saveOperate(@RequestBody AfterSalesOperateParam param) {
        try {
            log.info("invoiceApply {}", JSONObject.toJSONString(param));
//            final String msg = param.checkParam();
//            if (StringUtils.isNotEmpty(msg)) {
//                return addError(msg);
//            }
            param.setTerminalType(4);
            LoginAccountDto loginAccountDto = loginAccountApi.selectLoginAccountById(param.getAccountId());
            param.setOperator(loginAccountDto.getMobile());
            final ApiRPCResult apiRPCResult = afterSalesApi.saveOperate(param);
            if (apiRPCResult.isFail()) {
                return this.addError(apiRPCResult.getErrMsg());
            }
            if(2 == param.getOperateType()){
                // 取消退款单之后将工单取消
                csOrderRefundApi.expireWorkOrderByAfterSalesApply(param.getAfterSalesNo());
            }
            return this.addResult("data", null);
        } catch (Exception e) {
            log.error("invoiceApply {}", JSONObject.toJSONString(param),e);
            return this.addError("保存失败");
        }

    }
    /**
     * 售后须知
     * @return
     */
    @RequestMapping(value = "/guide", method = RequestMethod.GET)
    @ResponseBody
    public Object guide(String orgId) {
        try {
            ApiRPCResult<ReturnNoticeDto> apiRPCResult = shopNoticeApi.queryReturnNoticeDtoByOrgId(orgId);
            if (apiRPCResult.isFail()) {
                return this.addError("查询须知出错");
            }
            if (Objects.isNull(apiRPCResult.getData())) {
                return this.addResult("data", "暂无数据");
            }
            return this.addResult("data", apiRPCResult.getData().getContent());
        } catch (Exception e) {
            log.error("guide orgId:{}", orgId,e);
            return this.addError("查询须知出错");
        }
    }
    /**
     * 查询售后状态(售后详情页)
     * @return
     */
    @RequestMapping(value = "/queryProcessState", method = RequestMethod.GET)
    @ResponseBody
    public Object queryProcessState(String afterSalesNo) {
        try {
            final ApiRPCResult<AfterSalesProcessDto> result = afterSalesApi.queryProcessState(afterSalesNo);
            return this.addResult("data", result.getData());
        } catch (Exception e) {
            log.error("queryProcessState", e);
            return this.addError("查询出错");
        }
    }

    /**
     * 发票售后类型
     * @return
     */
    @RequestMapping(value = "/queryInvoiceType", method = RequestMethod.GET)
    @ResponseBody
    public Object queryInvoiceType() {
        try {
            return this.addResult("data", afterSalesInvoiceApplyType);
        } catch (Exception e) {
            log.error("queryInvoiceType",  e);
            return this.addError("查询出错");
        }
    }

    /**
     * 获取专票信息
     * @return
     */
    @RequestMapping(value = "/querySpecialInvoice", method = RequestMethod.GET)
    @ResponseBody
    public Object querySpecialInvoice(String orderNo,Long merchantId) {
        try {
            log.info("querySpecialInvoice orderNo:{}", orderNo);

            final ErpInvoiceVo erpInvoiceVo = afterSalesService.querySpecialInvoice(merchantId);
            return this.addResult("data", erpInvoiceVo);
        } catch (Exception e) {
            log.error("querySpecialInvoice orderNo:{}", orderNo, e);
            return this.addResult("data", null);
        }
    }
    /**
     * 获取错票分类
     * @return
     */
    @RequestMapping(value = "/queryIncorrectInvoiceType", method = RequestMethod.GET)
    @ResponseBody
    public Object queryIncorrectInvoiceType() {
        try {
            return this.addResult("data", JSONObject.parseArray(incorrectInvoiceType));
        } catch (Exception e) {
            log.error("queryIncorrectInvoiceType",  e);
            return this.addError("获取错票分类出错");
        }
    }
    /**
     * 获取企业资质选项类型
     * @return
     */
    @RequestMapping(value = "/queryCredentialType", method = RequestMethod.GET)
    @ResponseBody
    public Object queryCredentialType() {
        try {
            Map map = new HashMap<>();
            map.put("labelTitle","企业相关资质");
            map.put("items",JSONObject.parseArray(credentialType));
            return this.addResult("data", map);
        } catch (Exception e) {
            log.error("queryCredentialType",  e);
            return this.addError("获取错票分类出错");
        }
    }
    @RequestMapping(value = "/credentialApply", method = RequestMethod.POST)
    @ResponseBody
    public Object credentialApply(@RequestBody CredentialApplyParam param) {
        try {
            log.info("invoiceApply {}", JSONObject.toJSONString(param));
            final String msg = param.checkParam();
            if (StringUtils.isNotEmpty(msg)) {
                return addError(msg);
            }
            param.setTerminalType(4);
            LoginAccountDto loginAccountDto = loginAccountApi.selectLoginAccountById(param.getAccountId());
            param.setOperator(loginAccountDto.getMobile());
            afterSalesApi.credentialAfterSalesApply(param);
            return this.addResult("data", null);
        } catch (Exception e) {
            log.error("invoiceApply {}", JSONObject.toJSONString(param),e);
            return this.addResult("data", "保存失败");
        }
    }

    /**
     * 售后列表页
     * @param param
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public Object list(@RequestBody AfterSalesQueryParam param) {
        try {
            log.info("afterSales list {}", JSONObject.toJSONString(param));
//            final Page<AfterSalesListVo> result = afterSalesService.queryAfterSalesByPage(param);
            return this.addResult("data", null);
        } catch (Exception e) {
            log.error("list {}", JSONObject.toJSONString(param),e);
            return this.addResult("data", "查询失败");
        }
    }


    /**
     * 售后详情
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    public Object detail(@RequestBody AfterSalesQueryParam param) {
        try {
            AfterSalesDetailVo afterSalesDetailVo = afterSalesService.queryAfterSalesDetail(param);
            return this.addResult("data", afterSalesDetailVo);
        } catch (Exception e) {
            log.error("invoiceApply {}", JSONObject.toJSONString(param),e);
            return this.addResult("data", "查询失败");
        }
    }

    /**
     * 上传
     *
     * @param uploadPath 规范：前后带"/", 可以不同业务定义一个目录：如/ybm/evidences/
     * @param request    请求request
     * @return
     */
    @ResponseBody
    @RequestMapping("/upload")
    public Object uploadImage(@RequestParam("uploadPath") String uploadPath, HttpServletRequest request) {


        try {
            log.error("文件上传uploadImage：{}", uploadPath);
            String path = uploadPath+ DateUtil.date2String(new Date(), "yyyyMMdd")+"/";
            Map<String, Object> resultMap = FileUploadUtil.fileUpload(request, path, cdnConfig, null, null);
            log.error("文件上传resultMap：{}", resultMap);
            List<String> fileNameList = (List<String>) resultMap.get("fileName");
            Map result = new HashMap<>();
            List<String> newFileNameList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(fileNameList)) {
                fileNameList.forEach(fileName -> newFileNameList.add(path + fileName));
            }
            result.put("host",ossDomain);
            result.put("downloadPath",newFileNameList);
            return this.addResult("data",result);
        } catch (Exception e) {
            log.error("文件上传异常：", e);
            return this.addError("文件上传异常");
        }
    }
}
