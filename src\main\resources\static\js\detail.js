$(function() {
    // hoverClass();

	/* 控制促销的展开按钮 */
    showZhan();

    /*倒计时*/
    timer('timer');

    var merchantId = $("#merchantId").val();
    var categoryId = $("#categoryId").val();
    var rxNum = $("#rxNum").val();
    showRecommendedSku(merchantId,categoryId,rxNum);
    //点击换一批
    $("#rxNew").click(function(){
        var merchantId = $("#merchantId").val();
        var categoryId = $("#categoryId").val();
        var rxNum = $("#rxNum").val();
        showRecommendedSku(merchantId,categoryId,rxNum);
    });

    //控制可领取显示
    klqShow();
    //控制已领取显示
    ylqShow();


    /*点击收藏*/
    setTimeout(function(){ $(".w-collectZone_list").removeClass("initial");},1000);
    $(".w-collectZone_list").click(function(){
        if($(this).hasClass("hasCollect")){
            /*取消收藏*/
            $(this).removeClass("hasCollect").addClass("nopCollect");
            /*弹窗提示 2秒后消失*/
            showCollectTC();
        }else{
            /*添加收藏*/
            $(this).removeClass("nopCollect").addClass("hasCollect");
        }
    })


    /* 显示收藏*/
	$(".mrth li").hover(
		function() {
			$(this).find(".showorno").css("display", "block");
		},
		function() {
			$(this).find(".showorno").css("display", "none");
		}
	);



	/*减操作*/
	$(".sub_detail").click(function() {
		var step = 1;
		var me = $(this),
			txt = me.next(":text");
		var val = parseFloat(txt.val());
		var isSplit = txt.attr("isSplit");
		var middpacking = txt.attr("middpacking");
		if(isSplit == 0){
			step = parseFloat(middpacking);
		}
		var num = 0;
		if(!isNaN(val)){
			num = val;
		}
		if(num <= step) {
			txt.val(0);
		} else {
			txt.val(num - step);
		}
	});
	/*加操作*/
	$(".add_detail").click(function() {
		var step = 10;
		var me = $(this),
			txt = me.prev(":text");
		var val = parseFloat(txt.val());
		var isSplit = txt.attr("isSplit");
		var middpacking = txt.attr("middpacking");
		// if(isSplit == 0){
		//无论是否可拆零都加中包装数量
			step = parseFloat(middpacking);
		// }
		var num = 0;
		if(!isNaN(val)){
			num = val;
		}
		txt.val(num + step);
	});

	/*激活商品详情*/
	$(".spxqfn").click(function () {
		$(this).addClass("cur").siblings().removeClass("cur");
		$(".c-spxq").css("display","block");
		$(".c-ybm").css("display","none");
		$(".c-shwy").css("display","none");
	});

	/*激活关于药帮忙*/
	$(".ybmfn").click(function () {
		$(this).addClass("cur").siblings().removeClass("cur");
		$(".c-spxq").css("display","none");
		$(".c-ybm").css("display","block");
		$(".c-shwy").css("display","none");
	});
	/*激活售后无忧*/
	$(".shwyfn").click(function () {
		$(this).addClass("cur").siblings().removeClass("cur");
		$(".c-spxq").css("display","none");
		$(".c-ybm").css("display","none");
		$(".c-shwy").css("display","block");
	});

});

//==================图片详细页函数=====================
//鼠标经过预览图片函数
function preview(img){
	$("#preview .jqzoom img.sptp").attr("src",$(img).attr("src"));
	$("#preview .jqzoom img.sptp").attr("jqimg",$(img).attr("bimg"));
}

//图片放大镜效果
$(function(){
	$(".jqzoom").jqueryzoom({xzoom:380,yzoom:450});
});



//图片预览小图移动效果,页面加载时触发
$(function(){
	var tempLength = 0; //临时变量,当前移动的长度
	var viewNum = 4; //设置每次显示图片的个数量
	var moveNum = 1; //每次移动的数量
	var moveTime = 300; //移动速度,毫秒
	var scrollDiv = $(".spec-scroll .items ul"); //进行移动动画的容器
	var scrollItems = $(".spec-scroll .items ul li"); //移动容器里的集合
	var moveLength = scrollItems.eq(0).width() * moveNum; //计算每次移动的长度
	var countLength = (scrollItems.length - viewNum) * scrollItems.eq(0).width(); //计算总长度,总个数*单个长度

	$(".spec-scroll .prev").addClass("noclick");
	//下一张
	$(".spec-scroll .next").bind("click",function(){
		$(".spec-scroll .prev").removeClass("noclick");
		if(tempLength < countLength){
			if((countLength - tempLength) > moveLength){
				scrollDiv.animate({left:"-=" + moveLength + "px"}, moveTime);
				tempLength += moveLength;
			}else{
				scrollDiv.animate({left:"-=" + (countLength - tempLength) + "px"}, moveTime);
				tempLength += (countLength - tempLength);
				$(this).addClass("noclick");
			}
		}
	});
	//上一张
	$(".spec-scroll .prev").bind("click",function(){
		$(".spec-scroll .next").removeClass("noclick");
		if(tempLength > 0){
			if(tempLength > moveLength){
				scrollDiv.animate({left: "+=" + moveLength + "px"}, moveTime);
				tempLength -= moveLength;
			}else{
				scrollDiv.animate({left: "+=" + tempLength + "px"}, moveTime);
				tempLength = 0;
				$(this).addClass("noclick");
			}
		}
	});
});

//缩略图为空时隐藏
$(function(){
	var li_length=$(".spec-scroll ul").find("li").length;
	if(li_length<=0){
		$(".spec-scroll").addClass("noshow");
	}
	if(li_length>0 && li_length<=4){
		console.log(4);
		$(".spec-scroll .next,.spec-scroll .prev").unbind( "click" );
		$(".spec-scroll .next,.spec-scroll .prev").addClass("noclick");
	}
})
//==================图片详细页函数=====================

//点击链接跳转事件
function jumpUrl() {
	var isNewTab = $("#isNewTab").val();
	var descriptionPCUrl = $("#descriptionPCUrl").val();
	if (isNewTab == 1){
		window.open(descriptionPCUrl);
	}else{
		window.location.href=descriptionPCUrl;
	}
}

//==================图片详细页函数=====================

//时分秒倒计时方法
function timer(eleId) {
    var element = document.getElementById(eleId);
    if(element) {
        endTime = element.getAttribute('data-timer');
        var ts = endTime - new Date().getTime();
        if(ts > 0) {
            var dd = parseInt(ts / 1000 / 60 / 60 / 24, 10);
            var hh = parseInt(ts / 1000 / 60 / 60 % 24, 10);
            var mm = parseInt(ts / 1000 / 60 % 60, 10);
            var ss = parseInt(ts / 1000 % 60, 10);
            dd = dd<10?("0" + dd):dd;   //天
            hh = hh < 10 ? ("0" + hh) : hh; //时
            mm = mm < 10 ? ("0" + mm) : mm; //分
            ss = ss < 10 ? ("0" + ss) : ss; //秒
            document.getElementById("timer_d").innerHTML=dd;
            document.getElementById("timer_h").innerHTML = hh;
            document.getElementById("timer_m").innerHTML = mm;
            document.getElementById("timer_s").innerHTML = ss;
            setTimeout(function() {
                timer(eleId);
            }, 1000);
        } else {
            document.getElementById("timer_d").innerHTML=0;
            document.getElementById("timer_h").innerHTML = 0;
            document.getElementById("timer_m").innerHTML = 0;
            document.getElementById("timer_s").innerHTML = 0;
        }
    }
}

//优惠券弹窗
function test(){
    $("#fpxzTc").modal('show')
}

// 領取優惠券
function receiveTemplate(merchantId,receiveTemplateId){
    if(merchantId && merchantId > 0){
        $.ajax({
            url: "/merchant/center/voucher/receiveVoucher",
            type: "POST",
            dataType: "json",
            data: {
                merchantId: merchantId,
                voucherTemplateId: receiveTemplateId
            },
            success: function(result){
                if(result.status == "success"){
                    // $.alert({
                    //     title: '提示',
                    //     body: result.msg
                    // });
                    $('#v_'+receiveTemplateId).replaceWith('<img src="/static/images/yilinqu.png" class="pos-ysy">');
                }else {
                    $.alert({
                        title: '提示',
                        body: result.errorMsg
                    });
                }
            },
            error: function(){
                $.alert({
                    title: '提示',
                    body: '因为某些原因导致优惠券领取异常哟!'
                });
            }
        });
    }else{
        $.alert({
            title: '提示',
            body: '您还没有登录，请先登录!',
            okHidden : function(e){
                window.location.href="/login/login.htm?redirectUrl=/";
            }
        });
    }
}

function showZhan(){
	var liLength = $("#cxUl li").length;
	if(parseInt(liLength) == 2){
		var desch1 = $("#desc0").height();
        var desch2 = $("#desc1").height();
        if(desch1 > 24 || desch2 > 24){
            $('.cuxiao-ul li').addClass("text-overflow");
            hoverClass();
		}else{
            $(".zhankai").css("display","none");
		}
	}

	if(parseInt(liLength) > 2){
        hoverClass();
	}
}

function hoverClass(){
    /*展开收起*/
    $(".zhankai").hover(
        function() {
            $(".zhankai").css("display","none");
            $('.cuxiao-ul').css("max-height","100%");
            $('.cuxiao-ul li').removeClass("text-overflow");
            $('.cuxiao-ul li.sbzhan').css("display","none");
        }
    )
    $(".cuxiaobox").hover(
        function() {},
        function() {
            $(".zhankai").css("display","block");
            $('.cuxiao-ul').css("max-height","54px");
            $('.cuxiao-ul li').addClass("text-overflow");
            $('.cuxiao-ul li.sbzhan').css("display","block");
        }
    )
}

function showRecommendedSku(merchantId,categoryId,rxNum){
    $.ajax({
        url: "/search/findRecommendedSku",
        type: "POST",
        dataType: "html",
        traditional :true,
        data: {
            merchantId: merchantId,
            categoryId: categoryId,
            rxNum:rxNum
        },
        success: function(result){
            $("#rexiao").html(result);
            var newRxNum = parseInt(rxNum)+1;
            $("#rxNum").val(newRxNum);
        }
    });
}

function klqShow(){
    var liLength = $("#klq li").length;
    if(parseInt(liLength) == 0){
        $("#klq").addClass("noshow");
        $("#klqb").append('<div class="noyhq">暂无可领取的优惠券</div>');
    }
}

function ylqShow(){
    var liLength = $("#ylq li").length;
    if(parseInt(liLength) == 0){
        $("#ylqb").addClass("noshow");
    }
}

function priceNotify(skuId, fob) {
    $.alert({
        title: '降价通知',
        body:'<div style="background: #FFFBDB;height: 33px;width: 100%;padding: 0px 30px 0 0;text-indent: 16px;margin:-20px 0 20px -15px;line-height: 33px;font-size: 12px;color: rgba(255,0,0,0.75);">当该商品在<span>45</span>天内降价，您将收到推送消息</div>\
					<div style="font-size: 14px;color: rgba(0,0,0,0.75);margin-bottom: 15px;">当前价格：￥<span>'+fob+'</span></div>\
					<div style="font-size: 14px;color: rgba(0,0,0,0.75);position: relative;">您的渠道价格：<span style="position: absolute;top: 8px;left: 100px;">￥</span><input id="expectPrice" type="text" placeholder="低于该价格后会通知您" style="height: 30px;width: 243px;border: 1px solid #D9D9D9;border-radius: 4px;padding-left: 16px;"></div>\
					<div style="font-size: 12px;color: rgba(153,153,153,0.75);margin-top: 15px;">*确认后，同时将收藏该商品，您可以在我的收藏夹查看您订阅过的所有商品</div>',
        okBtn : '提交',
        okHidden : function(e){
            var expectPrice = $("#expectPrice").val();
            $.ajax({
                type: "POST",
                url: "/merchant/merchantBusiness/pricenotify/"+skuId+".json",
                data: {
                    expectPrice: expectPrice,
                    currentPrice: fob
                },
                dataType: "json",
                success: function(result){
                    if(result.status == "success"){
                        $.alert("订阅成功");

                    }else {
                        $.alert(result.errorMsg);
                    }
                }
            });
        }
    })
}