package com.xyy.ec.pc.recommend.params;

import com.xyy.ec.pc.recommend.dto.RecommendCsuDataTagCsuDTO;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Builder
@Data
public class RecommendCsuDataTagQueryParam implements Serializable {

    /**
     * 用户ID，必填
     */
    private Long merchantId;

    /**
     * 商品列表，必填
     */
    private List<RecommendCsuDataTagCsuDTO> csuList;

    /**
     * 是否查询店铺标签
     */
    private Boolean isQueryShopDataTags;

}
