package com.xyy.ec.pc.remote;

import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.merchant.server.common.Page;
import com.xyy.ec.merchant.server.dto.SubAccountDto;
import com.xyy.ec.merchant.server.dto.SubAccountModifyDto;
import com.xyy.ec.merchant.server.param.SubAccountQueryParam;
import com.xyy.ec.order.business.exception.ServiceException;

public interface SubAccountRemoteService {
    Page<SubAccountDto> subAccountList(SubAccountQueryParam queryParam) throws ServiceException;

    Boolean saveSubAccount(SubAccountDto subAccountDto) throws ServiceException;

    String checkMobileLegitimate(String mobile,Long merchantId) throws ServiceException;

    String handleSubAccountData(SubAccountModifyDto enabledDto) throws ServiceException;

    String checkSaveParam(SubAccountDto subAccountDto);
}
