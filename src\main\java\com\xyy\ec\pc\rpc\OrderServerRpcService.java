package com.xyy.ec.pc.rpc;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.base.framework.enumcode.ApiResultCodeEum;
import com.xyy.ec.base.framework.exception.promo.XyyEcPromotionBizNoneCheckRTException;
import com.xyy.ec.base.framework.rpc.ApiRPCResult;
import com.xyy.ec.ka.api.ka.OrderImportProductApi;
import com.xyy.ec.ka.vo.OrderImportProductDto;
import com.xyy.ec.merchant.server.enums.AccountMerchantRoleEnum;
import com.xyy.ec.order.api.cart.CartForYBMApi;
import com.xyy.ec.order.api.gray.RefactorCheckApi;
import com.xyy.ec.order.api.gray.RefactorGrayApi;
import com.xyy.ec.order.api.cart.GiftPoolForYBMApi;
import com.xyy.ec.order.api.limit.LimitQueryApi;
import com.xyy.ec.order.api.order.OrderForYBMApi;
import com.xyy.ec.order.api.settle.SettleForKaApi;
import com.xyy.ec.order.api.settle.SettleForYBMApi;
import com.xyy.ec.order.api.voucher.VoucherForYbmApi;
import com.xyy.ec.order.backend.order.dto.OrderShopHistoryDTO;
import com.xyy.ec.order.backend.order.dto.OrderShopHistoryQueryDTO;
import com.xyy.ec.order.backend.out.ka.OrderDetailForKaApi;
import com.xyy.ec.order.business.api.ecp.order.OrderSettleApi;
import com.xyy.ec.order.business.api.ecp.orderforpromotion.PromotionMsgBusinessApi;
import com.xyy.ec.order.business.common.ResultDTO;
import com.xyy.ec.order.business.dto.ecp.orderforpromotion.PromotionMsg;
import com.xyy.ec.order.dto.cart.*;
import com.xyy.ec.order.dto.combined.CombinedDto;
import com.xyy.ec.order.dto.combined.CombinedRespDto;
import com.xyy.ec.order.dto.giftPool.*;
import com.xyy.ec.order.dto.giftPool.vo.ChangeGiftPoolVo;
import com.xyy.ec.order.dto.giftPool.vo.ShoppingGiftPoolCacheVo;
import com.xyy.ec.order.dto.limit.PersonalBuySkuDto;
import com.xyy.ec.order.dto.limit.PersonalBuySkuParam;
import com.xyy.ec.order.dto.order.ConfirmOrderDto;
import com.xyy.ec.order.dto.settle.KaMatchPriceVO;
import com.xyy.ec.order.dto.settle.SettleDto;
import com.xyy.ec.order.dto.settle.SettleMatchPriceDto;
import com.xyy.ec.order.dto.settle.SettleVO;
import com.xyy.ec.order.dto.voucher.CartVoucherDto;
import com.xyy.ec.order.enums.BizSourceEnum;
import com.xyy.ec.order.enums.PlatformEnum;
import com.xyy.ec.order.vo.OrderVO;
import com.xyy.ec.pc.constants.Constants;
import com.xyy.ec.pc.controller.vo.MatchLineVO;
import com.xyy.ec.pc.controller.vo.MatchPriceResultDTO;
import com.xyy.ec.pc.model.sxp.SuiXinPinTopVo;
import com.xyy.ec.pc.service.CartService;
import com.xyy.ec.pc.service.order.MatchPriceService;
import com.xyy.ec.product.business.ecp.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.xyy.ec.pc.model.excel.PurchasePlanImportTemplate.Status.CANNOT_BUY;
import static com.xyy.ec.pc.model.excel.PurchasePlanImportTemplate.Status.CAN_BUY;

/**
 * <AUTHOR>
 * @version V1.0
 * @Package com.xyy.ec.app.remote
 * @date 2020/9/23 15:24
 */
@Slf4j
@Service
public class OrderServerRpcService {

    @Autowired
    private CartService cartService;
    @Reference(version = "1.0.0",timeout = 1000)
    private RefactorGrayApi refactorGrayApi;
    @Reference(version = "1.0.0", retries = -1, timeout = 10)
    private RefactorCheckApi refactorCheckApi;

    @Reference(version = "1.0.0",timeout = 60000)
    private CartForYBMApi cartForYBMApi;

    @Reference(version = "1.0.0",timeout = 60000)
    private SettleForYBMApi settleForYBMApi;

    @Reference(version = "1.0.0", timeout = 180000)
    private SettleForKaApi settleForKaApi;

    @Reference(version = "1.0.0")
    private OrderForYBMApi orderForYBMApi;

    @Reference(version = "1.0.0",timeout = 60000)
    private OrderSettleApi orderSettleApi;
    @Reference(version = "1.0.0",timeout = 60000)
    private VoucherForYbmApi voucherForYbmApi;

    @Value("${order.check.switch}")
    private String checkSwitch;

    @Reference(version = "1.0.0")
    LimitQueryApi limitQueryApi;

    @Reference(version = "1.0.0")
    private PromotionMsgBusinessApi promotionMsgBusinessApi;

    @Reference(version = "1.0.0")
    private OrderDetailForKaApi orderDetailForKaApi;

    @Reference(version = "1.0.0")
    private OrderImportProductApi orderImportProductApi;
    @Reference(version = "1.0.0",timeout = 5000)
    private GiftPoolForYBMApi giftPoolForYBMApi;

    public CartBusinessDto getCart(GetCartDto getCartDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-查询购物车入参:{}", JSON.toJSONString(getCartDto));
            }
            ApiRPCResult<CartBusinessDto> result = cartForYBMApi.getCart(getCartDto);
            if (result.isFail()) {
                log.error("九章迁移-查询购物车 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(getCartDto), result.getMsg());
                throw new RuntimeException("查询购物车业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-查询购物车返回:{}", JSON.toJSONString(result));
            }
            CartBusinessDto data=result.getData();
            data.setBizSource(getCartDto.getBizSource());
            return data;
        } catch (Exception e) {
            log.error("九章迁移-查询购物车 系统异常：请求参数：{}, msg:{}", JSON.toJSONString(getCartDto), e.getMessage(), e);
            throw e;
        }
    }
    public CombinedRespDto getCombined(CombinedDto combinedDto) {
        try {
            ApiRPCResult<CombinedRespDto> result=   cartForYBMApi.getCombined(combinedDto);
            if (result.isFail()) {
                log.error("新接口-查询组合 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(combinedDto), result.getMsg());
                throw new RuntimeException("查询组合信息业务异常");
            }
            log.info("新接口-查询组合 :请求参数：{},返回:{}", JSON.toJSONString(combinedDto),JSON.toJSONString(result));
            return result.getData();
        } catch (Exception e) {
            log.error("新接口-查询组合 系统异常：请求参数：{}, msg:{}", JSON.toJSONString(combinedDto), e.getMessage(), e);
            throw e;
        }
    }
    public Map<String, List<CartVoucherDto>> selectCartVoucher(Long merchantId, String shopCode, Integer voucherLevel){
        Integer termType = PlatformEnum.PC.getValue();
        try {
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-购物车查询优惠券入参:merchantId:{},shopCode:{},level:{},type:{}",merchantId,shopCode,voucherLevel,termType );
            }
            ApiRPCResult<Map<String, List<CartVoucherDto>>> result = voucherForYbmApi.selectCartVoucher(merchantId, shopCode, voucherLevel, termType);
            if (result.isFail()) {
                log.error("九章迁移-购物车查询优惠券入参:merchantId:{},shopCode:{},level:{},type:{}",merchantId,shopCode,voucherLevel,termType );
                throw new RuntimeException("购物车查询优惠券异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-购物车查询优惠券完成:merchantId:{},shopCode:{},level:{},type:{}",merchantId,shopCode,voucherLevel,termType );
            }
            return result.getData();
        } catch (Exception e) {
            log.info("九章迁移-购物车查询优惠券入参:merchantId:{},shopCode:{},level:{},type:{},msg:{}",merchantId,shopCode,voucherLevel,termType,e.getMessage(),e);
            throw e;
        }
    }


    public Map<String, Object> changeCart(ChangeCartDto changeCartDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-修改购物车入参:{}", JSON.toJSONString(changeCartDto));
            }
            ApiRPCResult<ChangeCartVO> result = cartForYBMApi.changeCart(changeCartDto);
            ChangeCartVO data = result.getData();
            if (result.isFail()) {
                log.error("九章迁移-修改购物车 业务异常!param:{},res:{}", changeCartDto, JSON.toJSONString(result));
                if (data == null) {
                    throw new RuntimeException("修改购物车失败!");
                }
                if(result.getCode() == 453){
                    throw new IllegalArgumentException(data.getMessage());
                }
            }
            Map<String, Object> dataMap = new HashMap<>();
            //设置商品ID
            dataMap.put("skuId", data.getSkuId());
            //设置返回消息
            dataMap.put("message", data.getMessage());
            if (data.getDialogStyle() != null) {
                dataMap.put("dialogStyle", data.getDialogStyle());
            }
            dataMap.put("qty", data.getQty());
            dataMap.put("totalAmount", data.getTotalAmount());
            dataMap.put("price", data.getPrice());
            dataMap.put("actPurchaseTip ", data.getActPurchaseTip());
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-修改购物车返回:{}", JSON.toJSONString(result));
            }
            return dataMap;
        } catch (Exception e) {
            log.error("九章迁移-修改购物车 系统异常：请求参数：{}, msg:{}", JSON.toJSONString(changeCartDto), e.getMessage(), e);
            throw e;
        }
    }

    public ResultDTO<String> voucherMonitor(ConfirmOrderDto confirmOrderDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-优惠券监控-入参:{}", JSON.toJSONString(confirmOrderDto));
            }
            ApiRPCResult<String> result = orderForYBMApi.voucherMonitor(confirmOrderDto);
            if (result.isFail()) {
                log.error("九章迁移-优惠券监控 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                return ResultDTO.fail(result.getData());
            }
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-优惠券监控-返回:{}", JSON.toJSONString(result));
            }
            return ResultDTO.success();
        } catch (Exception e) {
            log.error("九章迁移-优惠券监控- 系统异常：请求参数：{}, msg:{}", JSON.toJSONString(confirmOrderDto), e.getMessage(), e);
            return ResultDTO.fail("调用优惠券监测接口异常");
        }
    }

    public boolean commonIfaceGray(String iface){
        return true;
    }
    public ApiRPCResult<SettleVO> settle(SettleDto settleDto) {
        try {
            log.info("9528-ssm-九章迁移-结算入参:{}", JSON.toJSONString(settleDto));
            log.info("9528-ssm-九章迁移-结算入参,sxp:{}", JSON.toJSONString(settleDto.getSettleSxpSkuDtos()));

            ApiRPCResult<SettleVO> result;
            if (BizSourceEnum.GROUP_PURCHASE.getKey().equals(settleDto.getBizSource()) || BizSourceEnum.PIGOU.getKey().equals(settleDto.getBizSource())) {
                result = settleForYBMApi.settle4Pt(settleDto);
            } else {
                result = settleForYBMApi.settle(settleDto);
            }
            log.info("9528-ssm-九章迁移-结算返回:{}", JSON.toJSONString(result));

            return result;
        } catch (Exception e) {
            log.error("九章迁移-结算 系统异常：请求参数：{}, msg:{}", JSON.toJSONString(settleDto), e.getMessage(), e);
            throw e;
        }
    }
    public ApiRPCResult<SettleVO> matchPriceSettle(SettleDto settleDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("matchPriceSettle入参:merchantId:{}", JSON.toJSONString(settleDto));
            }
            ApiRPCResult<SettleVO> result = settleForYBMApi.settle(settleDto);
            if (log.isDebugEnabled()) {
                log.debug("matchPriceSettle返回:{}", JSON.toJSONString(result));
            }
            return result;
        } catch (Exception e) {
            log.error("matchPriceSettle系统异常：请求参数:{}, msg:{}", JSON.toJSONString(settleDto), e.getMessage(), e);
            throw e;
        }
    }
    public ApiRPCResult matchPricePreSettle(SettleDto settleDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-结算入参:{}", JSON.toJSONString(settleDto));
            }
            ApiRPCResult result = settleForYBMApi.matchPricePreSettle(settleDto);
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-结算返回:{}", JSON.toJSONString(result));
            }
            return result;
        } catch (Exception e) {
            log.error("九章迁移-结算 系统异常：请求参数：{}, msg:{}", JSON.toJSONString(settleDto), e.getMessage(), e);
            throw e;
        }
    }

    public OrderVO confirmOrder(ConfirmOrderDto confirmOrderDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-提单-入参:{}", JSON.toJSONString(confirmOrderDto));
            }
            ApiRPCResult<OrderVO> result = orderForYBMApi.confirmOrder(confirmOrderDto);
            if (log.isDebugEnabled()) {
                log.debug("九章迁移-提单-返回:{}", JSON.toJSONString(result));
            }
            if(result.isFail()){
                throw new RuntimeException(result.getErrMsg());
            }
            return result.getData();
        } catch (Exception e) {
            log.error("九章迁移-提单- 系统异常：请求参数：{}, msg:{}", JSON.toJSONString(confirmOrderDto), e.getMessage(), e);
            throw e;
        }
    }
    public Boolean checkShoppingCartImg(Long merchantId, String uuId){
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-校验购物车快照是否发生变更-入参 merchantId:{},uuId:{}", merchantId, uuId);
            }
            ApiRPCResult<Boolean> result = cartForYBMApi.checkShoppingCartImg(merchantId, uuId);
            if (result.isFail()) {
                log.error("新接口-校验购物车快照是否发生变更 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("校验购物车快照是否发生变更-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-校验购物车快照是否发生变更-返回:{}", JSON.toJSONString(result));
            }
            return result.getData();
        } catch (Exception e) {
            log.error("新接口-校验购物车快照是否发生变更- 系统异常：请求参数：merchantId:{},uuId:{}, msg:{}", merchantId, uuId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 查询个人限购期间购买数
     * @param merchantId
     * @param personalBuySkuParams
     * @return
     */
    public Map<Long,Integer> batchQueryPersonalBuySku(Long merchantId, List<PersonalBuySkuParam> personalBuySkuParams){
        try {
            if (log.isDebugEnabled()) {
                log.debug("调用订单模块limitQueryApi.batchQueryPersonalBuySku：输入参数：merchantId:{},personalBuySkuParams:{}",merchantId, JSON.toJSONString(personalBuySkuParams));
            }
            if(CollectionUtil.isEmpty(personalBuySkuParams)){
                return new HashMap<>(0);
            }
            ApiRPCResult<List<PersonalBuySkuDto>> personalBuySkuList = limitQueryApi.batchQueryPersonalBuySku(merchantId,personalBuySkuParams);
            if (log.isDebugEnabled()) {
                log.debug("调用订单模块limitQueryApi.batchQueryPersonalBuySku：输入参数：merchantId:{},personalBuySkuParams:{},result:{}",merchantId,JSON.toJSONString(personalBuySkuParams),JSON.toJSONString(personalBuySkuList));
            }
            if(personalBuySkuList.isSuccess() && CollectionUtil.isNotEmpty(personalBuySkuList.getData())){
                return personalBuySkuList.getData().stream().collect(Collectors.toMap(PersonalBuySkuDto::getSkuId, PersonalBuySkuDto::getQuantity, (key1, key2) -> key2));
            }
            return new HashMap<>(0);
        }catch (Exception e){
            log.error("limitQueryApi.batchQueryPersonalBuySku",e);
        }
        return Maps.newHashMap();
    }

    public Boolean createShoppingCartImg(Long merchantId){
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-创建购物车镜像-入参 merchantId:{}", merchantId);
            }
            ApiRPCResult<Boolean> result = cartForYBMApi.createShoppingCartImg(merchantId);
            if (result.isFail()) {
                log.error("新接口-创建购物车镜像 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("创建购物车镜像-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-创建购物车镜像-返回:{}", JSON.toJSONString(result));
            }
            return result.getData();
        } catch (Exception e) {
            log.error("创建购物车镜像- 系统异常：请求参数：merchantId:{}, msg:{}", merchantId, e.getMessage(), e);
            throw e;
        }
    }

    public void clearCart(Long merchantId){
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-根据用户ID清空购物车-入参:{}", merchantId);
            }
            ApiRPCResult result = cartForYBMApi.clearCart(merchantId);
            if (result.isFail()) {
                log.error("新接口-根据用户ID清空购物车 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("根据用户ID清空购物车-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-根据用户ID清空购物车-返回:{}", JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("新接口-根据用户ID清空购物车- 系统异常：请求参数：{}, msg:{}", merchantId, e.getMessage(), e);
            throw e;
        }
    }

    public void clearCartSubAccount(Long accountId,Long merchantId){
        try {
            if (log.isDebugEnabled()) {
                log.debug("子账号-新接口-根据用户ID清空购物车-入参:{}", accountId);
            }
            ApiRPCResult result = cartForYBMApi.clearCartSubAccount(accountId,merchantId);
            if (result.isFail()) {
                log.error("子账号-新接口-根据用户ID清空购物车 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("根据用户ID清空购物车-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("子账号-新接口-根据用户ID清空购物车-返回:{}", JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("子账号-新接口-根据用户ID清空购物车- 系统异常：请求参数：{}, msg:{}", accountId, e.getMessage(), e);
            throw e;
        }
    }

    public void batchRemoveProduct(DeleteCartDto deleteCartDto){
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-删除购物车指定商品-入参:{}", deleteCartDto);
            }
            ApiRPCResult result = cartForYBMApi.batchRemoveProduct(deleteCartDto);
            if (result.isFail()) {
                log.error("新接口-删除购物车指定商品 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("删除购物车指定商品-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-删除购物车指定商品-返回:{}", JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("新接口-删除购物车指定商品- 系统异常：请求参数：{}, msg:{}", deleteCartDto, e.getMessage(), e);
            throw e;
        }
    }

    public void batchSelectByGoupId(BatchSelectCartDto batchSelectCartDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-批量选中所有商品-入参:{}", batchSelectCartDto);
            }
            ApiRPCResult result = cartForYBMApi.batchSelectByGoupId(batchSelectCartDto);
            if (result.isFail()) {
                log.error("新接口-批量选中所有商品 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("批量选中所有商品-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-批量选中所有商品-返回:{}", JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("新接口-批量选中所有商品- 系统异常：请求参数：{}, msg:{}", batchSelectCartDto, e.getMessage(), e);
            throw e;
        }
    }

    public void batchUnSelectByGroupId(BatchSelectCartDto batchSelectCartDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-批量取消选中所有商品-入参:{}", batchSelectCartDto);
            }
            ApiRPCResult result = cartForYBMApi.batchUnSelectByGroupId(batchSelectCartDto);
            if (result.isFail()) {
                log.error("新接口-批量取消选中所有商品 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("批量取消选中所有商品-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-批量取消选中所有商品-返回:{}", JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("新接口-批量取消选中所有商品- 系统异常：请求参数：{}, msg:{}", batchSelectCartDto, e.getMessage(), e);
            throw e;
        }
    }

    public void selectItem(SelectCartDto selectCartDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-选中单个商品-入参:{}", selectCartDto);
            }
            ApiRPCResult result = cartForYBMApi.selectItem(selectCartDto);
            if (result.isFail()) {
                log.error("新接口-选中单个商品 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("选中单个商品-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-选中单个商品-返回:{}", JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("新接口-选中单个商品- 系统异常：请求参数：{}, msg:{}", selectCartDto, e.getMessage(), e);
            throw e;
        }
    }


    public void unSelectItem(SelectCartDto selectCartDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-取消选中单个商品-入参:{}", selectCartDto);
            }
            ApiRPCResult result = cartForYBMApi.unSelectItem(selectCartDto);
            if (result.isFail()) {
                log.error("新接口-取消选中单个商品 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("取消选中单个商品-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-取消选中单个商品-返回:{}", JSON.toJSONString(result));
            }
        } catch (Exception e) {
            log.error("新接口-取消选中单个商品- 系统异常：请求参数：{}, msg:{}", selectCartDto, e.getMessage(), e);
            throw e;
        }
    }

    public Integer getSkuVarietyNum(Long merchantId) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-获取购物车商品种类数量-入参:{}", merchantId);
            }
            ApiRPCResult<Integer> result = cartForYBMApi.getSkuVarietyNum(merchantId);
            if (result.isFail()) {
                log.error("新接口-获取购物车商品种类数量 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("获取购物车商品种类数量-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-获取购物车商品种类数量-返回:{}", JSON.toJSONString(result));
            }
            return result.getData();
        } catch (Exception e) {
            log.error("新接口-获取购物车商品种类数量- 系统异常：请求参数：{}, msg:{}", merchantId, e.getMessage(), e);
            throw e;
        }
    }

    public Integer getSkuVarietyNumSubAccount(Long accountId,Long merchantId) {
        CartDataQueryDto cartDataQueryDto = new CartDataQueryDto();
        try {
            cartDataQueryDto.setAccountId(accountId);
            cartDataQueryDto.setMerchantId(merchantId);
            cartDataQueryDto.setAccountRole(AccountMerchantRoleEnum.SUB_ACCOUNT.getRoleId());
            if (log.isDebugEnabled()) {
                log.debug("子账号-新接口-获取购物车商品种类数量-入参:{}", JSON.toJSONString(cartDataQueryDto));
            }
            ApiRPCResult<Integer> result = cartForYBMApi.getSkuVarietyNumSubAccount(cartDataQueryDto);
            if (result.isFail()) {
                log.error("子账号-新接口-获取购物车商品种类数量 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("获取购物车商品种类数量-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("子账号-新接口-获取购物车商品种类数量-返回:{}", JSON.toJSONString(result));
            }
            return result.getData();
        } catch (Exception e) {
            log.error("子账号-新接口-获取购物车商品种类数量- 系统异常：请求参数：{}, msg:{}", JSON.toJSONString(cartDataQueryDto), e.getMessage(), e);
            throw e;
        }
    }


    public Map<Long,Integer> batchQuerySkuNum(Long merchantId, List<Long> skuIdList) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-获取购物车商品种类数量-入参 merchantId:{}, skuIdList:{}", merchantId, JSON.toJSONString(skuIdList));
            }
            ApiRPCResult<Map<Long,Integer>> result = cartForYBMApi.batchQuerySkuNum(merchantId, skuIdList);
            if (result.isFail()) {
                log.error("新接口-获取购物车商品种类数量 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("获取购物车商品种类数量-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-获取购物车商品种类数量-返回:{}", JSON.toJSONString(result));
            }
            return result.getData();
        } catch (Exception e) {
            log.error("新接口-获取购物车商品种类数量- 系统异常：请求参数：merchantId:{}, skuIdList:{}, msg:{}", merchantId, JSON.toJSONString(skuIdList), e.getMessage(), e);
            throw e;
        }
    }


    public List<CartPackageNumDto> batchGetCartPackageNum(Long merchantId, List<Long> packageIdList) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("新接口-批量查询套餐数量-入参 merchantId:{},packageIdList:{}", merchantId, packageIdList);
            }
            ApiRPCResult<List<CartPackageNumDto>> result = cartForYBMApi.batchGetCartPackageNum(merchantId, packageIdList);
            if (result.isFail()) {
                log.error("新接口-批量查询套餐数量 业务异常：请求参数：{}, msg:{}", JSON.toJSONString(result), result.getErrMsg());
                throw new RuntimeException("批量查询套餐数量-业务异常");
            }
            if (log.isDebugEnabled()) {
                log.debug("新接口-批量查询套餐数量-返回:{}", JSON.toJSONString(result));
            }
            return result.getData();
        } catch (Exception e) {
            log.error("新接口-批量查询套餐数量- 系统异常：请求参数：merchantId:{},packageIdList:{}, msg:{}", merchantId, packageIdList, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取拼团信息（从hyperSpace迁移出来的）
     * @param param
     * @return
     */
    public List<PromotionMsg> queryPromotionListMsg(List<PromotionMsg> param) {
        ApiRPCResult<List<PromotionMsg>> resp = null;
        try {
            resp = promotionMsgBusinessApi.queryPromotionListMsg(param);
            if (!resp.isSuccess() || null == resp.getData()){
                log.error("queryPromotionListMsg from order resp is fail, resp:{}",resp);
                throw new XyyEcPromotionBizNoneCheckRTException(ApiResultCodeEum.QUERY_ERROR.getCode(), "拼团信息查询失败，请稍后重试", null);
            }
        } catch (Exception e) {
            log.error("queryPromotionListMsg from order exception,param:{}", JSON.toJSONString(param), e);
        }
        if (log.isDebugEnabled()) {
            log.debug("queryPromotionListMsg from order,param:{},result：{}",JSON.toJSONString(param),JSON.toJSONString(resp));
        }
        return resp.getData();
    }

    /**
     * 匹价计算
     * @param settleMatchPriceDto
     * @return
     */
    public ApiRPCResult<KaMatchPriceVO> matchPriceCalc(SettleMatchPriceDto settleMatchPriceDto) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("settleForKaApi.matchPriceCalc入参:{}", JSON.toJSONString(settleMatchPriceDto));
            }
            ApiRPCResult<KaMatchPriceVO> result =  settleForKaApi.matchPriceCalc(settleMatchPriceDto);
            if (log.isDebugEnabled()) {
                log.debug("settleForKaApi.matchPriceCalc返回:{}", JSON.toJSONString(result));
            }
            return result;
        } catch (Exception e) {
            log.error("settleForKaApi.matchPriceCalc系统异常：请求参数：{}, msg:{}", JSON.toJSONString(settleMatchPriceDto), e.getMessage(), e);
            throw e;
        }
    }

    public List<OrderShopHistoryDTO> getOrderShopHistory(OrderShopHistoryQueryDTO queryDTO) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("OrderServerRpcService.getOrderShopHistory#queryDTO:{}", JSON.toJSONString(queryDTO));
            }
            ApiRPCResult<List<OrderShopHistoryDTO>> rpcResult = orderDetailForKaApi.getOrderShopHistory(queryDTO);
            if (log.isDebugEnabled()) {
                log.debug("OrderServerRpcService.getOrderShopHistory#返回:{}", JSON.toJSONString(rpcResult));
            }
            if (rpcResult == null || rpcResult.isFail()) {
                return Lists.newArrayList();
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("OrderServerRpcService.getOrderShopHistory 系统异常：请求参数：{}, msg:{}", JSON.toJSONString(queryDTO), e.getMessage(), e);
            return Lists.newArrayList();
        }
    }

    public int updateImportStock(Long merchantId, Long excelId, Integer qty) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("OrderServerRpcService.updateImportStock#merchantId:{},excelId:{},qty:{}", merchantId, excelId, qty);
            }
            ApiRPCResult<Integer> rpcResult = orderImportProductApi.updateImportStock(merchantId, excelId, Constants.IMPORT_PLAN_USERNAME, qty);
            if (log.isDebugEnabled()) {
                log.debug("OrderServerRpcService.updateImportStock#返回:{}", JSON.toJSONString(rpcResult));
            }
            if (rpcResult == null || rpcResult.isFail()) {
                return -1;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("OrderServerRpcService.updateImportStock 系统异常 merchantId:{},excelId:{},qty:{}", merchantId, excelId, qty, e);
            return -1;
        }
    }
    public void saveOrderImportProduct(List<OrderImportProductDto> importProductDtoList, String username, Long merchantId) {
        this.saveOrderImportProduct(importProductDtoList, username, merchantId, true);
    }

    public void saveOrderImportProduct(List<OrderImportProductDto> importProductDtoList, String username, Long merchantId, boolean needDel) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("OrderServerRpcService.saveOrderImportProduct#username:{},merchantId:{},needDelHistory:{}", username, merchantId, needDel);
            }
            orderImportProductApi.saveOrderImportProduct(importProductDtoList, username, merchantId, needDel);
        } catch (Exception e) {
            log.error("OrderServerRpcService.saveOrderImportProduct 系统异常 importProductDtoList:{},username:{},merchantId:{}", JSON.toJSONString(importProductDtoList), username, merchantId, e);
            throw new RuntimeException("RPC导入采购单失败");
        }
    }

    public void updateImportProduct(Long merchantId, List<MatchLineVO> matchLines) {
        if (CollectionUtils.isEmpty(matchLines)) {
            return;
        }
        List<OrderImportProductDto> list = new ArrayList<>();
        for (MatchLineVO matchLine : matchLines) {
            OrderImportProductDto orderImportProductDto = new OrderImportProductDto();
            orderImportProductDto.setId(matchLine.getExcelId());
            orderImportProductDto.setCommonName(StringUtils.trim(matchLine.getExcelCommonName()));
            orderImportProductDto.setSpec(StringUtils.trim(matchLine.getExcelSpec()));
            orderImportProductDto.setManufacturer(StringUtils.trim(matchLine.getExcelManufacturer()));
            if (StringUtils.isNotBlank(orderImportProductDto.getCommonName()) && StringUtils.isNotBlank(orderImportProductDto.getSpec()) & StringUtils.isNotBlank(orderImportProductDto.getManufacturer())) {
                orderImportProductDto.setStatus(CAN_BUY.name());
                orderImportProductDto.setRemark("");
            }
            list.add(orderImportProductDto);
        }
        if (log.isDebugEnabled()) {
            log.debug("OrderServerRpcService.updateImportProduct#merchantId:{},list:{}", merchantId, JSON.toJSONString(list));
        }
        orderImportProductApi.updateImportProduct(merchantId, list);
        if (log.isDebugEnabled()) {
            log.debug("OrderServerRpcService.updateImportProduct#完成");
        }
    }

    public MatchPriceResultDTO selectImportDeficiencyProduct(Long merchantId) {
        MatchPriceResultDTO matchPriceResultDTO = new MatchPriceResultDTO();
        List<OrderImportProductDto> importProductDtoList = orderImportProductApi.selectImportDeficiencyProduct(merchantId);
        List<MatchLineVO> collect = importProductDtoList.stream().map(x -> {
            MatchLineVO vo = new MatchLineVO();
            vo.setExcelId(x.getId());
            vo.setExcelCommonName(x.getCommonName());
            vo.setExcelSpec(x.getSpec());
            vo.setExcelManufacturer(x.getManufacturer());
            vo.setNoMatchMsg(x.getRemark());
            return vo;
        }).collect(Collectors.toList());
        matchPriceResultDTO.setMatchSkuList(collect);
        return matchPriceResultDTO;
    }

    // v2
    public com.xyy.ec.match.dto.MatchPriceResultDTO selectImportDeficiencyProductV2(Long merchantId) {
        com.xyy.ec.match.dto.MatchPriceResultDTO matchPriceResultDTO = new com.xyy.ec.match.dto.MatchPriceResultDTO();
        List<OrderImportProductDto> importProductDtoList = orderImportProductApi.selectImportDeficiencyProduct(merchantId);
        List<com.xyy.ec.match.vo.MatchLineVO> collect = importProductDtoList.stream().map(x -> {
            com.xyy.ec.match.vo.MatchLineVO vo = new com.xyy.ec.match.vo.MatchLineVO();
            vo.setExcelId(x.getId());
            vo.setExcelCommonName(x.getCommonName());
            vo.setExcelSpec(x.getSpec());
            vo.setExcelManufacturer(x.getManufacturer());
            vo.setNoMatchMsg(x.getRemark());
            return vo;
        }).collect(Collectors.toList());
        matchPriceResultDTO.setMatchSkuList(collect);
        return matchPriceResultDTO;
    }



    /**
     * 根据用户信息查询购物车加购商品
     * @param merchantId 用户信息
     * @return 商品idList
     */
    public List<CartSelectedSkuIdDto> querySelectedSkuId(Long merchantId) {
        ApiRPCResult<List<CartSelectedSkuIdDto>> result;
        try {
            result = cartForYBMApi.querySelectedSkuId(merchantId);
            if (result.isFail()) {
                log.error("查询购物车已加购商品状态异常 merchantId:{}", merchantId);
                throw new RuntimeException("查询购物车已加购商品状态异常");

            }
            return result.getData();
        } catch (Exception e) {
            log.error("查询购物车已加购商品业务异常 merchantId:{}, errMsg:{}", merchantId, e.getMessage(), e);
            String message = MessageFormat.format("查询购物车已加购商品异常 merchantId:{}", merchantId);
            throw new RuntimeException("查询购物车已加购商品业务异常");
        }
    }


    /**
     * 修改赠品池
     * @param changeGiftPoolDto
     * @return
     */
    public ChangeGiftPoolVo changeGiftPool(ChangeGiftPoolDto changeGiftPoolDto) {
        ApiRPCResult<ChangeGiftPoolVo> result;
        try {
            result = giftPoolForYBMApi.changeGiftPool(changeGiftPoolDto);
            if (log.isDebugEnabled()) {
                log.debug("修改赠品池: changeGiftPool req:{},res:{}", JSON.toJSONString(changeGiftPoolDto), JSON.toJSONString(result));
            }
            if (result.isFail()) {
                log.error("修改赠品池状态异常 changeGiftPoolDto:{}", JSON.toJSONString(changeGiftPoolDto));
                throw new RuntimeException("查询购物车已加购商品状态异常");
            }
            return result.getData();
        } catch (Exception e) {
            log.error("修改赠品池业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(changeGiftPoolDto), e.getMessage(), e);
            String message = MessageFormat.format("修改赠品池业务异常 changeGiftPoolDto:{}", JSON.toJSONString(changeGiftPoolDto));
            throw new RuntimeException("查询购物车已加购商品状态异常");
        }
    }


    /**
     * 修改赠品池
     * @param commonGiftPoolDto
     * @return
     */
    public Integer getAllSelectedGiftPoolQty(CommonGiftPoolDto commonGiftPoolDto) {
        ApiRPCResult<Integer> result;
        try {
            result = giftPoolForYBMApi.getAllSelectedGiftPoolQty(commonGiftPoolDto);
            if (result.isFail()) {
                log.error("获取赠品池中被选中的赠品总数量状态异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
                throw new RuntimeException("查询购物车已加购商品状态异常");
            }
            return result.getData();
        } catch (Exception e) {
            log.error("获取赠品池中被选中的赠品总数量常业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(commonGiftPoolDto), e.getMessage(), e);
            String message = MessageFormat.format("获取赠品池中被选中的赠品总数量业务异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
            throw new RuntimeException("获取赠品池中被选中的赠品总数量常业务异常");
        }
    }


    /**
     * 查询赠品池
     * @param getGiftPoolDto
     * @return
     */
    public Map<String, ShoppingGiftPoolCacheVo> getGiftPool(CommonGiftPoolDto commonGiftPoolDto) {
        ApiRPCResult<Map<String, ShoppingGiftPoolCacheVo>> result;
        try {

            result = giftPoolForYBMApi.getGiftPool(commonGiftPoolDto);
            if (log.isDebugEnabled()) {
                log.debug("查询赠品池 req:{}，res:{}", JSON.toJSONString(commonGiftPoolDto),JSON.toJSONString(commonGiftPoolDto));
            }
            if (result.isFail()) {
                log.error("查询赠品池状态异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
                throw new RuntimeException("查询购物车已加购商品状态异常");
            }
            return result.getData();
        } catch (Exception e) {
            log.error("查询赠品池业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(commonGiftPoolDto), e.getMessage(), e);
            String message = MessageFormat.format("查询赠品池业务异常 changeGiftPoolDto:{0}", JSON.toJSONString(commonGiftPoolDto));
            throw new RuntimeException("查询赠品池状态异常");
        }
    }


    /**
     * 根据用户ID清空赠品池
     * @param commonGiftPoolDto
     * @return
     */
    public void clearGiftPool(CommonGiftPoolDto commonGiftPoolDto) {
        ApiRPCResult  result;
        try {
            result = giftPoolForYBMApi.clearGiftPool(commonGiftPoolDto);
            if (result.isFail()) {
                log.error("清空赠品池状态异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
                throw new RuntimeException("查询购物车已加购商品状态异常");
            }
            return ;
        } catch (Exception e) {
            log.error("清空赠品池业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(commonGiftPoolDto), e.getMessage(), e);
            String message = MessageFormat.format("清空赠品池业务异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
            throw new RuntimeException("清空赠品池业务异常");
        }
    }



    /**
     * 删除赠品池指定商品
     * @param deleteGiftPoolDto
     * @return
     */
    public void batchRemoveProduct(DeleteGiftPoolDto deleteGiftPoolDto) {
        ApiRPCResult result;
        try {
            result = giftPoolForYBMApi.batchRemoveProduct(deleteGiftPoolDto);
            if (result.isFail()) {
                log.error("删除赠品池指定商品状态异常 changeGiftPoolDto:{}", JSON.toJSONString(deleteGiftPoolDto));
                throw new RuntimeException("查询购物车已加购商品状态异常");
            }
            return ;
        } catch (Exception e) {
            log.error("删除赠品池指定商品业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(deleteGiftPoolDto), e.getMessage(), e);
            String message = MessageFormat.format("删除赠品池指定商品业务异常 changeGiftPoolDto:{}", JSON.toJSONString(deleteGiftPoolDto));
            throw new RuntimeException("删除赠品池指定商品业务异常");
        }
    }


    /**
     * 选中单个商品
     * @param selectGiftPoolDto
     * @return
     */
    public void selectItem(SelectGiftPoolDto selectGiftPoolDto) {
        ApiRPCResult result;
        try {
            result = giftPoolForYBMApi.selectItem(selectGiftPoolDto);
            if (result.isFail()) {
                log.error("选中单个商品状态异常 changeGiftPoolDto:{}", JSON.toJSONString(selectGiftPoolDto));
                throw new RuntimeException("查询购物车已加购商品状态异常");
            }
            return ;
        } catch (Exception e) {
            log.error("选中单个商品业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(selectGiftPoolDto), e.getMessage(), e);
            String message = MessageFormat.format("选中单个商品业务异常 changeGiftPoolDto:{}", JSON.toJSONString(selectGiftPoolDto));
            throw new RuntimeException("选中单个商品业务异常");
        }
    }

    /**
     * 取消选中单个商品
     * @param selectGiftPoolDto
     * @return
     */
    public void unSelectItem(SelectGiftPoolDto selectGiftPoolDto) {
        ApiRPCResult result;
        try {
            result = giftPoolForYBMApi.unSelectItem(selectGiftPoolDto);
            if (result.isFail()) {
                log.error("取消选中单个商品状态异常 changeGiftPoolDto:{}", JSON.toJSONString(selectGiftPoolDto));
                throw new RuntimeException("查询购物车已加购商品状态异常");
            }
            return ;
        } catch (Exception e) {
            log.error("取消选中单个商品业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(selectGiftPoolDto), e.getMessage(), e);
            String message = MessageFormat.format("取消选中单个商品业务异常 changeGiftPoolDto:{}", JSON.toJSONString(selectGiftPoolDto));
            throw new RuntimeException("取消选中单个商品状态异常");
        }
    }


    /**
     * 添加-放弃赠品池标志
     * @param commonGiftPoolDto
     * @return
     */
    public void addAutoGiveUpActFlag(CommonGiftPoolDto commonGiftPoolDto) {
        ApiRPCResult<ChangeGiftPoolVo> result;
        try {
            result = giftPoolForYBMApi.addAutoGiveUpActFlag(commonGiftPoolDto);
            if (result.isFail()) {
                log.error("添加-放弃赠品池标志状态异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
                throw new RuntimeException("查询购物车已加购商品状态异常");
            }
            return ;
        } catch (Exception e) {
            log.error("添加-放弃赠品池标志业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(commonGiftPoolDto), e.getMessage(), e);
            String message = MessageFormat.format("添加-放弃赠品池标志业务异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
            throw new RuntimeException("放弃赠品池标志状态异常");
        }
    }



//    /**
//     * 获取满赠类动文案提示
//     * @param commonGiftPoolDto
//     * @return
//     */
//    public String getActTitle(CommonGiftPoolDto commonGiftPoolDto) {
//        ApiRPCResult<String> result;
//        try {
//            result = giftPoolForYBMApi.getActTitle(commonGiftPoolDto);
//            if (result.isFail()) {
//                log.error("获取满赠类动文案提示状态异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
//                throw new AppException(XyyJsonResultCodeEnum.FAIL);
//            }
//            return result.getData();
//        } catch (Exception e) {
//            log.error("获取满赠类动文案提示业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(commonGiftPoolDto), e.getMessage(), e);
//            String message = MessageFormat.format("获取满赠类动文案提示业务异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
//            throw new AppException(message, XyyJsonResultCodeEnum.FAIL);
//        }
//    }


    /**
     * 删除-放弃赠品池标志
     * @param commonGiftPoolDto
     * @return
     */
    public void deleteAutoGiveUpActFlag(CommonGiftPoolDto commonGiftPoolDto) {
        ApiRPCResult result;
        try {
            result = giftPoolForYBMApi.deleteAutoGiveUpActFlag(commonGiftPoolDto);
            if (result.isFail()) {
                log.error("删除-放弃赠品池标志状态异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
                throw new RuntimeException("查询购物车已加购商品状态异常");
            }
            return ;
        } catch (Exception e) {
            log.error("删除-放弃赠品池标志业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(commonGiftPoolDto), e.getMessage(), e);
            String message = MessageFormat.format("删除-放弃赠品池标志标志业务异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
            throw new RuntimeException("放弃赠品池标志业务异常");
        }
    }



    /**
     *查询-放弃赠品池标志
     * @param commonGiftPoolDto
     * @return
     */
    public Boolean queryAutoGiveUpActFlag(CommonGiftPoolDto commonGiftPoolDto) {
        ApiRPCResult<Boolean> result;
        try {
            result = giftPoolForYBMApi.queryAutoGiveUpActFlag(commonGiftPoolDto);
            if (result.isFail()) {
                log.error("删除-放弃赠品池标志状态异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
                throw new RuntimeException("查询购物车已加购商品状态异常");
            }
            return result.getData();
        } catch (Exception e) {
            log.error("删除-放弃赠品池标志业务异常 changeGiftPoolDto:{}, errMsg:{}", JSON.toJSONString(commonGiftPoolDto), e.getMessage(), e);
            String message = MessageFormat.format("删除-放弃赠品池标志标志业务异常 changeGiftPoolDto:{}", JSON.toJSONString(commonGiftPoolDto));
            throw new RuntimeException("放弃赠品池标志业务异常");
        }
    }

}
