package com.xyy.ec.pc.newfront.controller;

import com.xyy.ec.pc.base.BaseController;
import com.xyy.ec.pc.config.RateFlowLimit;
import com.xyy.ec.pc.newfront.annotation.CustomizeCmsResponse;
import com.xyy.ec.pc.newfront.dto.SearchAgesRespVO;
import com.xyy.ec.pc.newfront.dto.SearchCategoryRespVO;
import com.xyy.ec.pc.newfront.dto.SearchRespVO;
import com.xyy.ec.pc.newfront.service.SearchNewService;
import com.xyy.ec.pc.newfront.vo.SearchParamVO;
import com.xyy.ec.pc.rest.AjaxResult;
import com.xyy.ec.pc.search.ecp.params.PcSearchQueryParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * @description: 搜索 相关modelView拆 ajax接口
 */
@CustomizeCmsResponse
@RestController
@RequiredArgsConstructor
@RequestMapping("/new-front/search")
public class SearchNewController extends BaseController {


    private final SearchNewService searchNewService;


    /**
     * 搜索商品
     */
    @PostMapping("/list-products")
    public AjaxResult<SearchRespVO> listProducts(@RequestBody PcSearchQueryParam searchParamVO, HttpServletRequest httpServletRequest) throws Exception {
            return  searchNewService.listProducts(searchParamVO,httpServletRequest);

    }


    /**
     * 搜索结果分类
     */
    @PostMapping("/search-categories")
    public AjaxResult<SearchAgesRespVO> searchCategory(@RequestBody PcSearchQueryParam searchQueryParam) throws Exception {
             return   searchNewService.getSearchCategory(searchQueryParam);

    }






}
