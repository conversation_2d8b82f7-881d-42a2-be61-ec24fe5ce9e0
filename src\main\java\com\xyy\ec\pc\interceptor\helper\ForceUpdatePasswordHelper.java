package com.xyy.ec.pc.interceptor.helper;

import com.xyy.ec.merchant.bussiness.Constants;
import com.xyy.ec.merchant.bussiness.dto.account.LoginAccountDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ForceUpdatePasswordHelper {

    @Value("${account.force.update.password.check.open:false}")
    private Boolean isCheck;

    public boolean check(LoginAccountDto loginAccountDto) {

        if (isCheck && loginAccountDto != null && Constants.IS1.equals(loginAccountDto.getUpdatePassword())) {
            return true;
        }
        return false;
    }
}