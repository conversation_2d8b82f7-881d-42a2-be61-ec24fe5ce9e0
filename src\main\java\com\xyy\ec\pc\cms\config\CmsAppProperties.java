package com.xyy.ec.pc.cms.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.ec.pc.cms.dto.CmsGroupBuyingSubjectHeadImageDailyPeriodDTO;
import com.xyy.ec.pc.cms.dto.GroupBuyingProductCategoryDTO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@Data
@NoArgsConstructor
public class CmsAppProperties {

    /**
     * 推荐商品总页数,默认5页
     */
    @Value("${xyy.ec.app.listProducts.recommend.maxPages:5}")
    private int recommendMaxPages;

    @Value("${cms.activity.assetsDomain}")
    private String cmsAssetsDomain;


    /**
     * 推荐商品提示语,默认空字符串页
     */
    @Value("${xyy.ec.app.listProducts.recommend.tip:提示语暂定}")
    private String recommendTip;

    /**
     * 拼团商品种类Map。逻辑商品分类ID - 拼团商品种类
     */
    private Map<Long, GroupBuyingProductCategoryDTO> logicCategoryIdToGroupBuyingProductCategoryMap = Maps.newHashMap();

    /**
     * 拼团商品种类列表
     */
    private List<GroupBuyingProductCategoryDTO> groupBuyingProductCategoryList = Lists.newArrayList();

    @Value("${xyy.ec.pc.groupBuyingProductCategories:[]}")
    public void setGroupBuyingProductCategories(String groupBuyingProductCategories) {
        Map<Long, GroupBuyingProductCategoryDTO> logicCategoryIdToGroupBuyingProductCategoryMap = Maps.newHashMap();
        List<GroupBuyingProductCategoryDTO> groupBuyingProductCategoryList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(groupBuyingProductCategories)) {
            groupBuyingProductCategoryList = JSONArray.parseArray(groupBuyingProductCategories, GroupBuyingProductCategoryDTO.class);
            Map<Long, GroupBuyingProductCategoryDTO> tempMap = groupBuyingProductCategoryList.stream()
                    .filter(item -> item != null && item.getId() != null)
                    .collect(Collectors.toMap(GroupBuyingProductCategoryDTO::getId, Function.identity(), (a, b) -> a));
            logicCategoryIdToGroupBuyingProductCategoryMap.putAll(tempMap);
        }
        this.logicCategoryIdToGroupBuyingProductCategoryMap = logicCategoryIdToGroupBuyingProductCategoryMap;
        this.groupBuyingProductCategoryList = groupBuyingProductCategoryList;
    }

    /**
     * CMS 拼团专题页的头图。
     * hashKey：星期几，1表示星期一。
     * hashValue：图片地址。
     */
    private Map<Integer, List<CmsGroupBuyingSubjectHeadImageDailyPeriodDTO>> cmsGroupBuyingSubjectHeadImageMap;

    @Value("${cms.groupBuyingSubjectHeadImageDailyPeriodMapJson:}")
    public void setCmsGroupBuyingSubjectHeadImageDailyPeriodMapJson(String cmsGroupBuyingSubjectHeadImageDailyPeriodMapJson) {
        Map<Integer, List<CmsGroupBuyingSubjectHeadImageDailyPeriodDTO>> cmsGroupBuyingSubjectHeadImageMap = Maps.newHashMapWithExpectedSize(16);
        if (StringUtils.isNotEmpty(cmsGroupBuyingSubjectHeadImageDailyPeriodMapJson)) {
            try {
                TypeReference<Map<Integer, List<CmsGroupBuyingSubjectHeadImageDailyPeriodDTO>>> typeReference
                        = new TypeReference<Map<Integer, List<CmsGroupBuyingSubjectHeadImageDailyPeriodDTO>>>() {};
                cmsGroupBuyingSubjectHeadImageMap = JSONObject.parseObject(cmsGroupBuyingSubjectHeadImageDailyPeriodMapJson, typeReference);
            } catch (Exception e) {
                log.error("解析CMS拼团专题页的头图每天时间段JSON异常，{}", cmsGroupBuyingSubjectHeadImageDailyPeriodMapJson, e);
                cmsGroupBuyingSubjectHeadImageMap = Maps.newHashMapWithExpectedSize(16);
            }
        }
        log.info("CMS拼团专题页的头图每天时间段：{}", JSONObject.toJSONString(cmsGroupBuyingSubjectHeadImageMap));
        this.cmsGroupBuyingSubjectHeadImageMap = cmsGroupBuyingSubjectHeadImageMap;
    }

    public String getCmsAssetsDomain() {
        return cmsAssetsDomain;
    }

    /**
     * 是否应用V2首页
     */
    @Value("${xyy.ec.cms.isApplyIndexV2:false}")
    private Boolean isApplyIndexV2;

    /**
     * V2首页地址
     */
    @Value("${xyy.ec.cms.indexV2.pageUrl:}")
    private String indexV2PageUrl;

    /**
     * 查询期望数量的商品列表的最大请求页数。
     */
    @Value("${xyy.ec.cms.listExpectProducts.maxPage:3}")
    private Integer listExpectProductsMaxPage;

    /**
     * 虚拟（实际不存在的）的商品组展示ID集合。
     */
    @Value("#{'${xyy.ec.app.virtualExhibitionIdStrs:}'.split(',')}")
    private Set<String> virtualExhibitionIdStrs;

    /**
     * 是否开启首页商品默认图片特性
     */
    @Value("${layout.isOpenIndexProductDefaultImageFeature:false}")
    private Boolean isOpenIndexProductDefaultImageFeature;

    /**
     * 商品默认图地址，相对路径
     */
    @Value("${layout.productDefaultImageUrl:defaultPhoto.jpg}")
    private String productDefaultImageUrl;

    /**
     * 未登录时逻辑用户ID。
     */
    @Value("${xyy.ec.app.noLoginLogicMerchantId:1}")
    private Long noLoginLogicMerchantId;

    @Value("${xyy.cms.rule.exhibition.use.switch:true}")
    private Boolean useRuleExhibitionSwitch;
}
