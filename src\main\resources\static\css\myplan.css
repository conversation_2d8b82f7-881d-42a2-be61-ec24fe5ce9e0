.myplan .user-myplan {
  width: 978px;
  margin-top: 10px;
  float: left;
  margin-bottom: 30px;
}
.myplan .user-myplan table {
  width: 978px;
}
.myplan .user-myplan table .plan-new td {
  text-align: left;
}
.myplan .user-myplan table .plan-new td span {
  display: none;
}
.myplan .user-myplan table .plan-new td input {
  width: 150px;
  height: 30px;
  border-radius: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  border: 1px solid #00DC82;
  padding-left: 8px;
  margin: 0 10px;
}
.myplan .user-myplan table .plan-new td button {
  width: 60px;
  height: 32px;
  text-align: center;
  line-height: 29px;
  font-size: 14px;
  outline: none;
  border: 0;
  border-radius: 4px;
}
.myplan .user-myplan table .plan-new td .plan-determine {
  background: #00C675;
  color: #FFFFFF;
}
.myplan .user-myplan table .plan-new td .plan-cancel,
.myplan .user-myplan table .plan-new td .plan-re-cancel {
  background: #FFFFFF;
  border: 1px solid #D9D9D9;
  color: #666666;
}
.myplan .user-myplan table .myorder-row1 {
  padding-left: 20px;
  text-align: left;
  width: 228px;
  border-radius: 4px 0 0 0;
  text-align: center;
}
.myplan .user-myplan table .myorder-row2 {
  width: 140px;
}
.myplan .user-myplan table .myorder-row3 {
  width: 110px;
}
.myplan .user-myplan table .myorder-row4 {
  width: 110px;
}
.myplan .user-myplan table .myorder-row5 {
  border-radius: 0 4px 0 0;
}
.myplan .user-myplan table thead th {
  height: 50px;
  background: #E3E3E3;
  padding: 0 10px;
  font-weight: 700;
  text-align: center;
}
.myplan .user-myplan table tbody {
  text-align: center;
}
.myplan .user-myplan table tbody tr:nth-of-type(2n-1) {
  background-color: #fff;
}
.myplan .user-myplan table tbody tr {
  height: 60px;
}
.myplan .user-myplan-sub table tbody tr {
  height: 60px;
  background: rgba(249,249,252,1);
}
.myplan .user-myplan table tbody .plan-small-navigation {
  display: none;
  position: absolute;
  top: -10px;
  left: 25px;
}
.myplan .user-myplan table tbody .plan-small-navigation .plan-sanjiao {
  width: 0;
  height: 0;
  border-width: 10px 10px 10px 0;
  border-style: solid;
  border-color: transparent #fff transparent transparent;
  position: absolute;
  top: 10px;
  left: -6px;
}
.myplan .user-myplan table tbody .plan-small-navigation ul {
  width: 100px;
  text-align: left;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #F5F5F5;
}
.myplan .user-myplan table tbody .plan-small-navigation ul li {
  border-bottom: 1px solid #f5f5f5;
  font-size: 14px;
  color: #666666;
}
.myplan .user-myplan table tbody .plan-small-navigation ul li:nth-last-child(1) {
  border-bottom: 0;
}
.myplan .user-myplan table tbody .plan-small-navigation ul li a {
  font-size: 14px;
  color: #666666;
  display: block;
  padding: 10px 18px;
}
.myplan .user-myplan table tbody .plan-ellipsis {
  position: relative;
  margin-left: 10px;
}
.myplan .user-myplan .noplan {
  margin-top: 50px;
  text-align: center;
}
.myplan .user-myplan .noplan img {
  margin-bottom: 20px;
}
.myplan .user-myplan .noplan p {
  font-size: 12px;
}
.myplan .user-myplan .noplan .electronic-Plan {
  margin-top: 15px;
  display: inline-block;
  width: 136px;
  height: 26px;
  line-height: 26px;
  background: #404C59;
  padding-left: 6px;
  box-sizing: border-box;
  border-radius: 100px;
  font-size: 14px;
  color: #FFFFFF;
  cursor: pointer;
}
.agreement-state {
  overflow: hidden;
  background: #fff;
  border-bottom: 1px solid rgba(244,244,244,1);
}

.agreement-state div {
  float: left;
  height: 59px;
  line-height: 58px;
}

.agreement-state a {
  font-size: 14px;
  color: #333;
  display: block;
  float: left;
  font-weight: 700;
}
.agreement-state a.active{
  color: #00C675;
  border-bottom: 2px #00c675 solid;
  float: left;
}

.already a{
  margin-left: 54px;
}
.spanColor{
  color: #00C675;
  float: initial;
}

.already i{
  display: inline-block;
  width: 1px;
  height: 45px;
  background: #F0F0F0;
  float: left;
  margin: 10px 54px 0 54px;
}
.myplan .myneworder-search {
  background: #FFFFFF;
  border-radius: 4px 4px 0 0;
  padding: 20px 0 0 10px;
  box-sizing: border-box;
  border-bottom: 1px solid #E7E7E7;
  margin-top: 0;
  height: 70px;
}
.myplan .myneworder-search-none{
  border-bottom: none;
  height: auto;
}
.myplan .myneworder-search .ybm-drop-down{
  margin-top: 3px;
}
.myplan .myneworder-search .ybm-drop-down .ybm-menu-title{
  font-size: 14px;
  color: #666666;
}
.myplan .myneworder-search .sui-form {
  margin-left: 15px;
}
.myplan .myneworder-search .inp-num {
  border-radius: 4px;
  width: 138px;
}
.myplan .sui-form .controls input.input-medium {
  width: 150px;
}
/* .myplan .sui-form .input-date {
  background-image: none;
} */
.myplan .myplan-buttons {
  background: #FFFFFF;
  border-radius: 0 0 4px 4px;
}
.myplan .myplan-buttons-new{
  display: inline-block;
  background: #FFFFFF;
  border-radius: 0 0 4px 4px;
  float: right;
}
.myplan .myplan-buttons .newplan {
  margin: 0 0 20px 30px;
}
.myplan .myplan-buttons .newplan .electronic-Plan {
  width: 145px;
  height: 32px;
  text-align: center;
  line-height: 30px;
  background: #F39800;
  border: 0;
  color: #fff;
  border-radius: 4px;
  outline: none;
}
.myplan .myplan-buttons .plan-anniu {
  margin-right: 30px;
}
.myplan .myplan-buttons .plan-anniu button {
  width: 83px;
  height: 32px;
  text-align: center;
  line-height: 29px;
  border: 0;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}
.myplan .myplan-buttons .plan-anniu .plan-empty {
  background: #FFFFFF;
  border: 1px solid #D9D9D9;
  color: #666666;
  margin-right: 20px;
}
.myplan .myplan-buttons .plan-anniu .margin-none{
  margin-right: 0;
}
.myplan .myplan-buttons .plan-anniu .search-for {
  background: #00C675;
  color: #FFFFFF;
}
.myplan tbody td>a{
  color: #666666;
}
.myplan tbody td>a:hover,.myplan tbody td>a:focus{
  color: #00C675;
}
.tfr{
  float: right;
}

.tfr .electronic-Plan {
  width: 145px !important;
  height: 32px;
  text-align: center;
  line-height: 30px;
  background: #F39800;
  border: 0;
  color: #fff;
  border-radius: 4px;
  outline: none;
}
.tclearfix{
  background: #f8f8f8 !important;
}
.crumbs{
  margin-bottom:0px;
  background: #ffffff;
  margin-top:12px;
}

.myneworder-search .padding-left{
  padding-left: 5px;
}

#startCreateTime{
  width:100px;
}

#endCreateTime{
  width:100px;
}
.page{
  width: 100% !important;
}
.inp-sel{
  border-radius: 4px !important;
}



.center_zhe{
  position: fixed;
  background:rgba(48, 48, 48, 0.5) ;
  display: block;
  width: 100%;
  height: 100%;
  z-index: 666;
  display: none;
}
.center_zhe .center_box{
  border-radius: 5px;
  width: 600px;
  height: 405px;
  background: #fff;
  margin: 200px auto;
  vertical-align: super;
  position: relative;
}
.center_ming{
  padding-left: 20px;
  padding-top: 50px;
  position: relative;
}
.shuru{
  position: absolute;
  top: 72px;
  left: 40px;
  width: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  height: 25px;
}
.center_tu{
  padding-left: 20px;
  padding-top: 50px;
}

.center_ti{
  margin-top: 22px;
  padding-left: 40px;
  position: absolute;
  top: 280px;
}
.center_ti1{
  margin-top: 22px;
  padding-left: 40px;
}
.center_shang{
  width: 60px;
  height: 30px;
  background: #00c675;
  color: #fff;
  line-height: 30px;
  text-align: center;
  border-radius: 2px;
  position: absolute;
  top: 356px;
  margin-left:275px;
  cursor: pointer;
}
.center_shang1{
  width: 60px;
  height: 30px;
  background: #00c675;
  color: #fff;
  line-height: 30px;
  text-align: center;
  border-radius: 2px;
  margin-top:20px;
  margin-left:275px;
  cursor: pointer;
}

imageDiv{ display: inline-block;  width: 66px;  height: 66px;  -webkit-box-sizing: border-box;  -moz-box-sizing:
        border-box;  box-sizing: border-box;  border: 1px dashed darkgray;  background: #f8f8f8; position: relative; overflow:
        hidden; } .cover{ position: absolute; z-index: 1;top: 0; left: 0; width: 66px; height: 66px; background-color:
        rgba(0,0,0,.3); display: none; line-height: 66px; text-align: center; cursor: pointer;} .cover>.delbtn{ color:
        red; font-size: 20px; } .imageDiv:hover .cover{ display: block; } .addImages{ display: inline-block;margin-top: 5px; width:
        66px; height: 66px; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; border:
        1px dashed darkgray; background: #f8f8f8; position: relative; overflow: hidden; } .text-detail{ margin-top: 20px;
                                                                                            text-align: center; } .text-detail>span{ font-size: 40px; } .file{ position: absolute; top: 0; left: 0;
                                                                                                                                                          width: 66px; height: 66px; opacity: 0; }


.center_zhe .center_cheng{
  display: none;
  border-radius: 5px;
  width: 600px;
  height: 350px;
  background: #fff;
  margin: 200px auto;
  vertical-align: super;
}
.logo{
  width: 200px;
  height: 200px;
  margin:auto;
  text-align: center;
  display: block;
}
.center_gong{
  text-align: center;
  font-size: 22px;
  display: block;
}

.guan{
  padding: 5px;
  font-size: 16px;
  position: fixed;
  padding-left:580px;
  z-index: 999;
}
.imageDiv{
  position: relative;
}
.covers{
  position: absolute;
  top: -3px;
  left: 54px;

}

.imageDiv{
  display: inline-block;
  margin-left:6px;
}

/*采购单详情*/
#detailModal .spebox .caigoudan{
  margin-bottom:5px;
}
#detailModal .spebox .caigoudan .title{
  color:#999; font-size:14px;width:120px; display:inline-block;
}
#detailModal .imgbox img{
  margin:5px; border:0px solid #999; border-radius:5px;width: 120px;
}