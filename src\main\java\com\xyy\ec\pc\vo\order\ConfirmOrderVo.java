package com.xyy.ec.pc.vo.order;

import com.alibaba.fastjson.annotation.JSONField;
import com.xyy.ec.order.core.dto.OrderAdjustDto;
import com.xyy.ec.order.core.dto.OrderDetailDto;
import com.xyy.ec.order.core.dto.OrderExtendDto;
import com.xyy.ec.order.core.dto.OrderZhuGDto;
import com.xyy.ec.order.core.dto.cart.ActivityPackageDto;
import com.xyy.ec.order.core.dto.cart.VoucherDto;
import com.xyy.ec.order.core.dto.promo.OrderPromoDetail;
import com.xyy.ec.order.core.dto.promo.OrderPromotion;
import com.xyy.ec.order.core.dto.promo.UseRedPacketDTO;
import com.xyy.ec.order.core.enums.CouponComputeAccountEnum;
import com.xyy.ec.order.dto.settle.SettleSkuDto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * @author: zhaoyun
 * @Date: 2018/8/21 16:59
 * @Description:
 */
public class ConfirmOrderVo implements Serializable {
    private static final long serialVersionUID = 1469135708701860103L;

    /** 订单信息 */
    private Long id;

    private Integer bizScene;
    /** 额外商品信息 */
    private String bizProducts;
    /**  */
    private String branchCode;
    /** 随心拼商品集合 [{"skuId":10,"quantity":20},{"skuId":11,"quantity":20}]*/
    private String suiXinPinSkus;
    /**  */
    private Integer companyProvinceCode;

    private String productCredential;

    private String corpCredential;

    /** 机构ID */
    private String orgId;

    /** 商户编号 */
    private Long merchantId;
    /**
     * 下单账号id
     */
    private Long accountId;
    private Integer accountRole;
    /** 订单编号 */
    private String orderNo;

    /** 外部订单唯一编码(如智鹿订单) */
    private String outsideOrderCode;

    /** 1-合单支付;0-不合单支付 */
    private Integer isPayMerge;

    /** 收货人 */
    private String contactor;

    /** 手机号 */
    private String mobile;

    /** 订单实付金额 */
    private BigDecimal money;

    /** 支付类型(1:在线支付 2:货到付款 3:线下转账） */
    private Integer payType;

    /** 付款人类型：1 自己付 2 他人代付 */
    private Integer payerType;

    /** 支付渠道(1-支付宝,2-微信,3-银联) */
    private Integer payChannel;

    /** 在线支付时间 */
    private Date payTime;

    /** 转账时间(争对线下转账) */
    private Date paymentTime;

    /** 发票信息 */
    private String billInfo;

    /** 发票类型 1:普通发票 2:专用发票 */
    private Integer billType;

    /** 订单状态：1-待配送，2-配送中，3-已配送，4-取消，5-已删除,6-已拆单,7-出库中,10-未支付,90-退款审核中,91-已退款,20-已送达,21-已拒签 */
    private Integer status;

    /** 审单流程状态：默认：0-尚未进入审单流程,1-审单中，2-开票中，3-发货中，9-审单流程结束 */
    private Integer checkStatus;

    /** 计划配送时间 */
    private Date deliveryTime;

    /** 实际配送时间 */
    private Date shipTime;

    /** 完成时间 */
    private Date finishTime;

    /** 原总金额(秒杀/直降/特价/拼团按原价计算)*/
    private BigDecimal originalTotalAmount = BigDecimal.ZERO;

    /** 原总优惠金额(包含秒杀/直降/特价/拼团优惠金额)*/
    private BigDecimal originalTotalDiscount = BigDecimal.ZERO;

    /** 总金额=实付金额+优惠金额 */
    private BigDecimal totalAmount;

    /** 优惠金额 */
    private BigDecimal discount;

    /** 总手动优惠金额(后台新增订单) */
    private BigDecimal customDiscountAmount;

    /** 后台新增订单优惠计算方式 ： 0 不参与系统优惠 1 参与系统优惠 */
    private Integer discountComputeType;
    /** 父订单ID */
    private Long parentId;

    /** 订单来源渠道:默认1，1-B2B(药帮忙); 2-宜块钱,3-豆芽代下单*/
    private Integer orderChannel;

    /** 是否可见 */
    private Integer visibled;
    public static final Integer STATUS_VISIBLED = 1;
    public static final Integer STATUS_NOTVISIBLED = 0;

    /** 紧急程度 0(默认:备注) 1:低  5:中  10:高 */
    private Integer urgencyDegree;

    /** 紧急程度描述 */
    private String urgencyInfo;

    /** 备注最后更新时间 */
    private Date urgencyUpdateTime;

    /** 订单原始支付金额(用于退款渠道和对账单作验证使用) */
    private BigDecimal sourcePayMoney;

    /** 省份编码 */
    private Integer provinceCode;

    /** 市区编码 */
    private Integer cityCode;

    /** 区域编码 */
    private Integer areaCode;

    /** 订单来源 0:手动添加 1:Android 2:IOS 3:H5 4:PC */
    private Integer orderSource;

    /**  */
    private Integer totalcount;

    /** 验证重复下单 */
    private String tranNo;

    /** 订单(签收/拒签)时间 */
    private Date deliveredTime;

    /** 申请退款次数 */
    private Integer refundCount;

    /** 销售工号 */
    private String salesJobNo;

    /** 销售姓名 */
    private String salesName;

    /** 销售员ID */
    private Long salesId;

    /** 配送方式: 0:未知 1:自营 2:第三方 3:其他 */
    private Integer shipType;

    /** 时空编号:SKPZxxxxxx */
    private String skCode;

    /** 集货区编码 */
    private String storeCode;

    /** ka门店地址ID */
    private Long storeAddressId;

    /** 箱数 */
    private Integer boxes;

    /** 是否分配 0:未分配 1:已分配 */
    private Integer haveSchedule;

    /** 过单时间 */
    private Date passTime;

    /** 地址id */
    private Long addressId;

    /** 商品总数量 */
    private Integer productNum;

    /** 药店名称 */
    private String merchantName;

    /** 不返点商品总金额 */
    private BigDecimal noRebateTotalAmount;

    /** 不返点商品满减金额 */
    private BigDecimal noRebateDiscountAmount;

    /** 不返点商品优惠券金额 */
    private BigDecimal noRebateVoucherAmount;

    /** 返点商品总金额 */
    private BigDecimal rebateTotalAmount;

    /** 返点商品满减金额 */
    private BigDecimal rebateDiscountAmount;

    /** 返点商品优惠券金额 */
    private BigDecimal rebateVoucherAmount;

    /** 全场满减金额 */
    private BigDecimal wholeDiscountAmount;

    /** 全场优惠券金额 */
    private BigDecimal wholeVoucherAmount;

    /** 商户类型标识,暂提供连锁药店使用(3) */
    private Integer businessType;

    /** 收货人地址 */
    private String address;

    /** 备注 */
    private String remark;

    /** 创建人 */
    private String creator;

    /** 创建时间 */
    private Date createTime;

    /** 修改人 */
    private String updator;

    /** 修改时间 */
    private Date updateTime;

    /**发票文案**/
    private String invoinceText;

    /**是否显示查看发票按钮**/
    private Integer isShowInvoinceButton;

    /** 订单状态名称 */
    private String statusName;

    /** 订单状态名称 */
    private String trackingNo;

    /** 不能去提单的公司（仓库） xyy0001,xyy002,xyy003 */
    private String notSubmitOrderOrgIds;



    /************************************************** 扩展字段 *************************************************/

    /** 是否可拆单还原:0 = 不可还原; 1 = 可还原 */
    private Integer separateFlag;

    /** 创建时间始 */
    private Date startCreateTime;

    /** 创建时间止 */
    private Date endCreateTime;

    /** 支付开始时间 */
    private Date startPayTime;

    /** 支付结束时间 */
    private Date endPayTime;

    /** 详细地址 */
    private String gspAddress;

    /** 地址状态: 0:默认无修改 1:用户已点击过弹窗 2:用户有修改过备注地址 3:用户备注地址已审过 4:用户备注地址没有审核通过,需要复审 */
    private Integer gspAuditState;

    /** 会员备注 */
    private String gspRemark;

    /** 订单扩展信息 */
    private OrderExtendDto orderExtend;

    /** 拆分订单明细 */
    private List<ConfirmOrderVo> separateOrderList;

    /** 订单明细 */
    private List<OrderDetailDto> detailList;


    /** 订单扩展明细 */
    private List<OrderDetailDto> detailDTOList;


    /** 活动返余额 */
    private BigDecimal rebateBalanceAmt;

    /**订单来源数组*/
    private Integer[] orderSourceArray;

    /** 支付方式名称 */
    private String payTypeName;

    /** 支付渠道名称 */
    private String payChannelName;

    //是否使用余额 默认为true
    private boolean useBalance = true;

    //是否有领取余额(0:未领取 1：已领取 2：不能领 )
    private Integer balanceStatus;


    private List<ActivityPackageDto> packageList; //套餐列表

    /** 订单支付倒计时 */
    private String payEndTime;

    /** IOS使用的倒计时支付毫秒数 */
    private Long countDownNewTime;

    /** 倒计时相应的时间戳 毫秒数*/
    private Long countDownTime;

    //领取余额文案
    private String balanceText;

    //使用的余额金额
    private BigDecimal balanceAmount;
    /** 使用的购物金账户余额 */
    private BigDecimal virtualGold;
    /** 是否使用购物金余额 默认为true */
    private boolean useVirtualGold = false;
    /** 是否使用红包默认为true */
    private Boolean useRedPacket = false;
    private String balanceRemark;		//余额备注  --已领取余额100.0元/订单完成后预计可领取余额300.00元

    //申请退款文案
    private String refundText;

    /** 可以确认收货按钮 */
    private Integer canConfirmReceipt;

    private BigDecimal fullDivPrice;		//订单满减金额
    private BigDecimal voucherDivPrice;		//订单优惠券减免金额
    private BigDecimal rebate;				//订单返点金额

    /** 订单起送价格 */
    private BigDecimal startingPrice;
    /** 使用的优惠券 */
    private String voucherId;
    /** 选中的多个优惠券ID，用逗号分隔 */
    private String voucherIds;
    /** 使用优惠券ID LSIT */
    private List<Long> useVoucherIdList;
    private VoucherDto voucher;
    //参与优惠计算明细总金额
    private BigDecimal takeDiscountTotalAmt;
    /**
     * app版本
     * todo 该字段不该被序列化
     */
    private Integer appVersion;
    //设备ID
    private String deviceId;
    private Integer[] statuses;
    //退款审核状态数组
    private Integer[] auditStateArray;
    //根据药品名查询
    private String name;
    /**
     * 是否首字母查询
     * todo 该字段不该被序列化
     */
    private Boolean isLettersQuery;

    // 区分查询订单详情列表时候的状态
    private Integer sceneType;


    private String imageUrl;
    /** 优惠券优惠总金额 */
    private BigDecimal voucherDiscountAmount;
    /** 促销活动优惠总金额 */
    private BigDecimal promoDiscountAmount;

    /**
     * 订单优惠记录
     * todo 该字段不该被序列化
     */
    private List<OrderAdjustDto> orderAdjustList;

    /**为了不对原订单编号造成污染，所以先开一个字段*/
    /**
     * 预生成订单号
     * todo 该字段不该被序列化
     */
    private String preOrderNo;


    /**
     * 聚合订单号
     */
    private String mainOrderNo;

    //优惠类型
    private Integer discountType;
    /**
     * 跳转类型 1 只有自营跳支付 2 包含第三方订单跳订单列表
     * todo 该字段不该被序列化
     */
    private Integer jumpType;
    private Integer callType;
    /** 是否第三方厂家（0：否；1：是） */
    private Integer isThirdCompany;
    /** 是否FBP（0：否；1：是） */
    private Integer isFbp;
    /** 有无退款文本 */
    private String hasRefundText;
    /** 查询条件 0 -无退款 1 - 有退款 */
    private Integer hasRefund;

    /**商户类型（业务类型）：1-个体药店，2-连锁药店（加盟），3-连锁药店（直营），4-诊所 */
    private Integer businessTypeSearch;

    /** 注册手机号 */
    private String loginMobile;

    //确认下单多项采购单ID拼接字符串
    private String cartIds;

    /** 商户类型 1:正式用户 2:测试用户 */
    private Integer merchantType;


    /** 支付有效截止时间 */
    private Date payExpireTime;

    //运费
    private BigDecimal freightAmount;
    //卖家名称
    private String companyName;

    /** 同步订单到ERP 0不同步 1同步到时空 2同步到神农  */
    private int needInteractionSk;

    /** 电子普通发票随货同行 0:未勾选 1:已勾选  */
    private Integer peerType;

    private String giftIds;

    private String uuid;

    /** 一口价优惠金额 */
    private BigDecimal fixedPriceAmount;

    /** ka添加字段 门店地址 && */
    private String storeName;
    /** ka添加字段 是否配送至门店 1 是 0 否*/
    private Integer storeOrder;

    /** 是否首单：0 不是 1 是 */
    private Integer firstOrderFlag;

    /** 是否是补单：0 不是 1 是 */
    private Integer replacementOrderFlag;
    /** 是否是补单文本 */
    private String hasReplacementOrderFlagText;
    /** 是否是首单文本 */
    private String hasFirstOrderFlagText;
    /** Saas导流订单来源路径编码 */
    private Integer  saasOrderSourcePath;
    /** 渠道编码：默认为 1; B2B 1; 2 宜块钱 */
    private String channelCode;
    /** 渠道文本 */
    private String channelCodeText;
    /** 传入1，表示该次刷新结算数据是从弹出框点击（则不清除优惠券弹出标记），而不是正常的购物车到结算（清除优惠券弹出标记）， */
    private String voucherMonitor;

    /** 是否包含控销商品 */
    private Integer isHaveControl;
    //采购单ID
    private String purchaseNo;

    private List<OrderZhuGDto> orderZhuGList;

    //促销优惠流水
    private String resSerialNumber;

    //是否是KA快速下单 1 是 0否
    private Integer isKaFastOrder;

    /** 是否为店铺订单 */
    private Boolean isShopOrder = Boolean.FALSE;;
    /** 用户勾选的店铺优惠券信息，key:shopCode,value:List<voucherIds>  */
    private Map<String, List<Long>> shopVoucherIds = new HashMap<>();
    /** 公司备注Map 格式：{"companyCode1":"remark1","companyCode2":"remark2"}*/
    private Map<String, String> companyRemarks;
    /**
     * 优惠券分摊操作枚举
     */
    private CouponComputeAccountEnum couponComputeAccountEnum;
    /**是否ka用户**/
    private Boolean isKaUser = Boolean.FALSE;

    /**拼团的商品id 从商品详情发起，只有一个skuId**/
    private Long skuId;

    public String getHasReplacementOrderFlagText() {
        return hasReplacementOrderFlagText;
    }

    /**用户类型list**/
    private List<Integer> businessTypeList;

    private List<OrderPromoDetail> orderPromoDetailList;

    private List<UseRedPacketDTO> useRedPacketList;

    private OrderPromotion orderPromotion;

    /** 店铺优惠券金额 */
    private BigDecimal shopVoucherAmount;

    /** 跨平台优惠券金额 */
    private BigDecimal crossPlatformVoucherAmount;


    /** qtdata */
    private String qtdata;

    public String getQtdata() {
        return qtdata;
    }

    public void setQtdata(String qtdata) {
        this.qtdata = qtdata;
    }

    public String getNotSubmitOrderOrgIds() {
        return notSubmitOrderOrgIds;
    }

    public void setNotSubmitOrderOrgIds(String notSubmitOrderOrgIds) {
        this.notSubmitOrderOrgIds = notSubmitOrderOrgIds;
    }

    /**
     * 订单毛利是否异常
     * true 异常,false:正常
     */
    private boolean isGrossProfitAbnormal = false;
    /**
     * 订单商品毛利是否异常(ka专用)
     * true 异常,false:正常
     */
    private boolean isSkuGrossProfitAbnormal = false;

    //订单扩展信息
    private String extraInfoJson;

    /**
     * 是否存在整单优惠(ka专用)
     * true 存在, false 不存在
     */
    private boolean isExistFullOrderDiscount = false;
    /**
     * 是否存在厂家优惠(ka专用)
     * true 存在, false 不存在
     */
    private boolean isExistFirmDiscount = false;
    /**
     * 是否存在公司优惠(ka专用)
     * true 存在, false 不存在
     */
    private boolean isExistComDiscount = false;

    /**
     * 创建人真实名称
     * 后台下单记录下单人真实姓名， 目前仅ka后台下单使用
     */
    private String creatorRealName;
    /**
     * 创建人id
     * 后台下单记录下单人id， 目前仅ka后台下单使用
     */
    private Long creatorId;

    /** 豆芽销售员ID */
    private Integer dySalesId;
    //是否KA：1是0否
    private Integer isKa;
    /** 现金实付 */
    private BigDecimal cashPayAmount;
    /** 品种数量 */
    private Integer varietyNum;
    /**
     * 是否随心拼商品, ture：是,false：否
     */
    private Boolean isSxp;
    /**
     * 埋点通用字段
     */
    private String mddata;
    public void setHasReplacementOrderFlagText(String hasReplacementOrderFlagText) {
        this.hasReplacementOrderFlagText = hasReplacementOrderFlagText;
    }

    public Integer getFirstOrderFlag() {
        return firstOrderFlag;
    }

    public void setFirstOrderFlag(Integer firstOrderFlag) {
        this.firstOrderFlag = firstOrderFlag;
    }

    public Integer getReplacementOrderFlag() {
        return replacementOrderFlag;
    }

    public void setReplacementOrderFlag(Integer replacementOrderFlag) {
        this.replacementOrderFlag = replacementOrderFlag;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getStoreOrder() {
        return storeOrder;
    }

    public Integer getIsPayMerge() {
        return isPayMerge;
    }

    public void setIsPayMerge(Integer isPayMerge) {
        this.isPayMerge = isPayMerge;
    }

    public void setStoreOrder(Integer storeOrder) {
        this.storeOrder = storeOrder;
    }

    public BigDecimal getFixedPriceAmount() {
        return fixedPriceAmount;
    }

    public void setFixedPriceAmount(BigDecimal fixedPriceAmount) {
        this.fixedPriceAmount = fixedPriceAmount;
    }

    //用户实际IP地址
    @JSONField(serialize = false)
    private String realIP;

    public String getRealIP() {
        return realIP;
    }

    public void setRealIP(String realIP) {
        this.realIP = realIP;
    }

    public Integer getPeerType() {
        return peerType;
    }

    public void setPeerType(Integer peerType) {
        this.peerType = peerType;
    }

    public int getNeedInteractionSk() {
        return needInteractionSk;
    }

    public void setNeedInteractionSk(int needInteractionSk) {
        this.needInteractionSk = needInteractionSk;
    }

    public Date getPayExpireTime() {
        return payExpireTime;
    }

    public void setPayExpireTime(Date payExpireTime) {
        this.payExpireTime = payExpireTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getVarietyNum() {
        return varietyNum;
    }

    public void setVarietyNum(Integer varietyNum) {
        this.varietyNum = varietyNum;
    }

    public String getContactor() {
        return contactor;
    }

    public void setContactor(String contactor) {
        this.contactor = contactor;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public Integer getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(Integer payChannel) {
        this.payChannel = payChannel;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getPaymentTime() {
        return paymentTime;
    }

    public void setPaymentTime(Date paymentTime) {
        this.paymentTime = paymentTime;
    }

    public String getBillInfo() {
        return billInfo;
    }

    public void setBillInfo(String billInfo) {
        this.billInfo = billInfo;
    }

    public Integer getBillType() {
        return billType;
    }

    public void setBillType(Integer billType) {
        this.billType = billType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Date getShipTime() {
        return shipTime;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public BigDecimal getCustomDiscountAmount() {
        return customDiscountAmount;
    }

    public void setCustomDiscountAmount(BigDecimal customDiscountAmount) {
        this.customDiscountAmount = customDiscountAmount;
    }

    public Integer getDiscountComputeType() {
        return discountComputeType;
    }

    public void setDiscountComputeType(Integer discountComputeType) {
        this.discountComputeType = discountComputeType;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getVisibled() {
        return visibled;
    }

    public void setVisibled(Integer visibled) {
        this.visibled = visibled;
    }

    public static Integer getStatusVisibled() {
        return STATUS_VISIBLED;
    }

    public static Integer getStatusNotvisibled() {
        return STATUS_NOTVISIBLED;
    }

    public Integer getUrgencyDegree() {
        return urgencyDegree;
    }

    public void setUrgencyDegree(Integer urgencyDegree) {
        this.urgencyDegree = urgencyDegree;
    }

    public String getUrgencyInfo() {
        return urgencyInfo;
    }

    public void setUrgencyInfo(String urgencyInfo) {
        this.urgencyInfo = urgencyInfo;
    }

    public Date getUrgencyUpdateTime() {
        return urgencyUpdateTime;
    }

    public void setUrgencyUpdateTime(Date urgencyUpdateTime) {
        this.urgencyUpdateTime = urgencyUpdateTime;
    }

    public BigDecimal getSourcePayMoney() {
        return sourcePayMoney;
    }

    public void setSourcePayMoney(BigDecimal sourcePayMoney) {
        this.sourcePayMoney = sourcePayMoney;
    }

    public Integer getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(Integer provinceCode) {
        this.provinceCode = provinceCode;
    }

    public Integer getCityCode() {
        return cityCode;
    }

    public void setCityCode(Integer cityCode) {
        this.cityCode = cityCode;
    }

    public Integer getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(Integer areaCode) {
        this.areaCode = areaCode;
    }

    public Integer getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(Integer orderSource) {
        this.orderSource = orderSource;
    }

    public Integer getTotalcount() {
        return totalcount;
    }

    public void setTotalcount(Integer totalcount) {
        this.totalcount = totalcount;
    }

    public String getTranNo() {
        return tranNo;
    }

    public void setTranNo(String tranNo) {
        this.tranNo = tranNo;
    }

    public Date getDeliveredTime() {
        return deliveredTime;
    }

    public void setDeliveredTime(Date deliveredTime) {
        this.deliveredTime = deliveredTime;
    }

    public Integer getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(Integer refundCount) {
        this.refundCount = refundCount;
    }

    public String getSalesJobNo() {
        return salesJobNo;
    }

    public void setSalesJobNo(String salesJobNo) {
        this.salesJobNo = salesJobNo;
    }

    public String getSalesName() {
        return salesName;
    }

    public void setSalesName(String salesName) {
        this.salesName = salesName;
    }

    public Long getSalesId() {
        return salesId;
    }

    public void setSalesId(Long salesId) {
        this.salesId = salesId;
    }

    public Integer getShipType() {
        return shipType;
    }

    public void setShipType(Integer shipType) {
        this.shipType = shipType;
    }

    public String getSkCode() {
        return skCode;
    }

    public void setSkCode(String skCode) {
        this.skCode = skCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public Integer getBoxes() {
        return boxes;
    }

    public void setBoxes(Integer boxes) {
        this.boxes = boxes;
    }

    public Integer getHaveSchedule() {
        return haveSchedule;
    }

    public void setHaveSchedule(Integer haveSchedule) {
        this.haveSchedule = haveSchedule;
    }

    public Date getPassTime() {
        return passTime;
    }

    public void setPassTime(Date passTime) {
        this.passTime = passTime;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public Integer getProductNum() {
        return productNum;
    }

    public void setProductNum(Integer productNum) {
        this.productNum = productNum;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public BigDecimal getNoRebateTotalAmount() {
        return noRebateTotalAmount;
    }

    public void setNoRebateTotalAmount(BigDecimal noRebateTotalAmount) {
        this.noRebateTotalAmount = noRebateTotalAmount;
    }

    public BigDecimal getNoRebateDiscountAmount() {
        return noRebateDiscountAmount;
    }

    public void setNoRebateDiscountAmount(BigDecimal noRebateDiscountAmount) {
        this.noRebateDiscountAmount = noRebateDiscountAmount;
    }

    public BigDecimal getNoRebateVoucherAmount() {
        return noRebateVoucherAmount;
    }

    public void setNoRebateVoucherAmount(BigDecimal noRebateVoucherAmount) {
        this.noRebateVoucherAmount = noRebateVoucherAmount;
    }

    public BigDecimal getRebateTotalAmount() {
        return rebateTotalAmount;
    }

    public void setRebateTotalAmount(BigDecimal rebateTotalAmount) {
        this.rebateTotalAmount = rebateTotalAmount;
    }

    public BigDecimal getRebateDiscountAmount() {
        return rebateDiscountAmount;
    }

    public void setRebateDiscountAmount(BigDecimal rebateDiscountAmount) {
        this.rebateDiscountAmount = rebateDiscountAmount;
    }

    public BigDecimal getRebateVoucherAmount() {
        return rebateVoucherAmount;
    }

    public void setRebateVoucherAmount(BigDecimal rebateVoucherAmount) {
        this.rebateVoucherAmount = rebateVoucherAmount;
    }

    public BigDecimal getWholeDiscountAmount() {
        return wholeDiscountAmount;
    }

    public void setWholeDiscountAmount(BigDecimal wholeDiscountAmount) {
        this.wholeDiscountAmount = wholeDiscountAmount;
    }

    public BigDecimal getWholeVoucherAmount() {
        return wholeVoucherAmount;
    }

    public void setWholeVoucherAmount(BigDecimal wholeVoucherAmount) {
        this.wholeVoucherAmount = wholeVoucherAmount;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getInvoinceText() {
        return invoinceText;
    }

    public void setInvoinceText(String invoinceText) {
        this.invoinceText = invoinceText;
    }

    public Integer getIsShowInvoinceButton() {
        return isShowInvoinceButton;
    }

    public void setIsShowInvoinceButton(Integer isShowInvoinceButton) {
        this.isShowInvoinceButton = isShowInvoinceButton;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getSuiXinPinSkus() {
        return suiXinPinSkus;
    }

    public void setSuiXinPinSkus(String suiXinPinSkus) {
        this.suiXinPinSkus = suiXinPinSkus;
    }

    public String getTrackingNo() {
        return trackingNo;
    }

    public void setTrackingNo(String trackingNo) {
        this.trackingNo = trackingNo;
    }

    public Integer getSeparateFlag() {
        return separateFlag;
    }

    public void setSeparateFlag(Integer separateFlag) {
        this.separateFlag = separateFlag;
    }

    public Date getStartCreateTime() {
        return startCreateTime;
    }

    public void setStartCreateTime(Date startCreateTime) {
        this.startCreateTime = startCreateTime;
    }

    public Date getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(Date endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Date getStartPayTime() {
        return startPayTime;
    }

    public void setStartPayTime(Date startPayTime) {
        this.startPayTime = startPayTime;
    }

    public Date getEndPayTime() {
        return endPayTime;
    }

    public void setEndPayTime(Date endPayTime) {
        this.endPayTime = endPayTime;
    }

    public String getGspAddress() {
        return gspAddress;
    }

    public void setGspAddress(String gspAddress) {
        this.gspAddress = gspAddress;
    }

    public Integer getGspAuditState() {
        return gspAuditState;
    }

    public void setGspAuditState(Integer gspAuditState) {
        this.gspAuditState = gspAuditState;
    }

    public String getGspRemark() {
        return gspRemark;
    }

    public void setGspRemark(String gspRemark) {
        this.gspRemark = gspRemark;
    }

    public OrderExtendDto getOrderExtend() {
        return orderExtend;
    }

    public void setOrderExtend(OrderExtendDto orderExtend) {
        this.orderExtend = orderExtend;
    }

    public List<ConfirmOrderVo> getSeparateOrderList() {
        return separateOrderList;
    }

    public void setSeparateOrderList(List<ConfirmOrderVo> separateOrderList) {
        this.separateOrderList = separateOrderList;
    }

    public List<OrderDetailDto> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<OrderDetailDto> detailList) {
        this.detailList = detailList;
    }

    public List<OrderDetailDto> getDetailDTOList() {
        return detailDTOList;
    }

    public void setDetailDTOList(List<OrderDetailDto> detailDTOList) {
        this.detailDTOList = detailDTOList;
    }

    public BigDecimal getRebateBalanceAmt() {
        return rebateBalanceAmt;
    }

    public void setRebateBalanceAmt(BigDecimal rebateBalanceAmt) {
        this.rebateBalanceAmt = rebateBalanceAmt;
    }

    public Integer[] getOrderSourceArray() {
        return orderSourceArray;
    }

    public void setOrderSourceArray(Integer[] orderSourceArray) {
        this.orderSourceArray = orderSourceArray;
    }

    public String getPayTypeName() {
        return payTypeName;
    }

    public void setPayTypeName(String payTypeName) {
        this.payTypeName = payTypeName;
    }

    public String getPayChannelName() {
        return payChannelName;
    }

    public void setPayChannelName(String payChannelName) {
        this.payChannelName = payChannelName;
    }

    public boolean isUseBalance() {
        return useBalance;
    }

    public void setUseBalance(boolean useBalance) {
        this.useBalance = useBalance;
    }

    public Integer getBalanceStatus() {
        return balanceStatus;
    }

    public void setBalanceStatus(Integer balanceStatus) {
        this.balanceStatus = balanceStatus;
    }

    public List<ActivityPackageDto> getPackageList() {
        return packageList;
    }

    public void setPackageList(List<ActivityPackageDto> packageList) {
        this.packageList = packageList;
    }

    public String getPayEndTime() {
        return payEndTime;
    }

    public void setPayEndTime(String payEndTime) {
        this.payEndTime = payEndTime;
    }

    public Long getCountDownNewTime() {
        return countDownNewTime;
    }

    public void setCountDownNewTime(Long countDownNewTime) {
        this.countDownNewTime = countDownNewTime;
    }

    public Long getCountDownTime() {
        return countDownTime;
    }

    public void setCountDownTime(Long countDownTime) {
        this.countDownTime = countDownTime;
    }

    public String getBalanceText() {
        return balanceText;
    }

    public void setBalanceText(String balanceText) {
        this.balanceText = balanceText;
    }

    public BigDecimal getBalanceAmount() {
        return balanceAmount;
    }

    public void setBalanceAmount(BigDecimal balanceAmount) {
        this.balanceAmount = balanceAmount;
    }

    public String getBalanceRemark() {
        return balanceRemark;
    }

    public void setBalanceRemark(String balanceRemark) {
        this.balanceRemark = balanceRemark;
    }

    public String getRefundText() {
        return refundText;
    }

    public void setRefundText(String refundText) {
        this.refundText = refundText;
    }

    public Integer getCanConfirmReceipt() {
        return canConfirmReceipt;
    }

    public void setCanConfirmReceipt(Integer canConfirmReceipt) {
        this.canConfirmReceipt = canConfirmReceipt;
    }

    public BigDecimal getFullDivPrice() {
        return fullDivPrice;
    }

    public void setFullDivPrice(BigDecimal fullDivPrice) {
        this.fullDivPrice = fullDivPrice;
    }

    public BigDecimal getVoucherDivPrice() {
        return voucherDivPrice;
    }

    public void setVoucherDivPrice(BigDecimal voucherDivPrice) {
        this.voucherDivPrice = voucherDivPrice;
    }

    public BigDecimal getRebate() {
        return rebate;
    }

    public void setRebate(BigDecimal rebate) {
        this.rebate = rebate;
    }

    public BigDecimal getStartingPrice() {
        return startingPrice;
    }

    public void setStartingPrice(BigDecimal startingPrice) {
        this.startingPrice = startingPrice;
    }

    public String getVoucherId() {
        return voucherId;
    }

    public void setVoucherId(String voucherId) {
        this.voucherId = voucherId;
    }

    public String getVoucherIds() {
        return voucherIds;
    }

    public void setVoucherIds(String voucherIds) {
        this.voucherIds = voucherIds;
    }

    public VoucherDto getVoucher() {
        return voucher;
    }

    public void setVoucher(VoucherDto voucher) {
        this.voucher = voucher;
    }

    public BigDecimal getTakeDiscountTotalAmt() {
        return takeDiscountTotalAmt;
    }

    public void setTakeDiscountTotalAmt(BigDecimal takeDiscountTotalAmt) {
        this.takeDiscountTotalAmt = takeDiscountTotalAmt;
    }

    public Integer getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(Integer appVersion) {
        this.appVersion = appVersion;
    }

    public Integer[] getStatuses() {
        return statuses;
    }

    public void setStatuses(Integer[] statuses) {
        this.statuses = statuses;
    }

    public Integer[] getAuditStateArray() {
        return auditStateArray;
    }

    public void setAuditStateArray(Integer[] auditStateArray) {
        this.auditStateArray = auditStateArray;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getLettersQuery() {
        return isLettersQuery;
    }

    public void setLettersQuery(Boolean lettersQuery) {
        isLettersQuery = lettersQuery;
    }

    public Integer getSceneType() {
        return sceneType;
    }

    public void setSceneType(Integer sceneType) {
        this.sceneType = sceneType;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public BigDecimal getVoucherDiscountAmount() {
        return voucherDiscountAmount;
    }

    public void setVoucherDiscountAmount(BigDecimal voucherDiscountAmount) {
        this.voucherDiscountAmount = voucherDiscountAmount;
    }

    public String getMainOrderNo() {
        return mainOrderNo;
    }

    public void setMainOrderNo(String mainOrderNo) {
        this.mainOrderNo = mainOrderNo;
    }

    public BigDecimal getPromoDiscountAmount() {
        return promoDiscountAmount;
    }

    public void setPromoDiscountAmount(BigDecimal promoDiscountAmount) {
        this.promoDiscountAmount = promoDiscountAmount;
    }

    public List<OrderAdjustDto> getOrderAdjustList() {
        return orderAdjustList;
    }

    public void setOrderAdjustList(List<OrderAdjustDto> orderAdjustList) {
        this.orderAdjustList = orderAdjustList;
    }

    public String getPreOrderNo() {
        return preOrderNo;
    }

    public void setPreOrderNo(String preOrderNo) {
        this.preOrderNo = preOrderNo;
    }

    public Integer getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Integer discountType) {
        this.discountType = discountType;
    }

    public Integer getJumpType() {
        return jumpType;
    }

    public void setJumpType(Integer jumpType) {
        this.jumpType = jumpType;
    }

    public Integer getIsThirdCompany() {
        return isThirdCompany;
    }

    public void setIsThirdCompany(Integer isThirdCompany) {
        this.isThirdCompany = isThirdCompany;
    }

    public String getHasRefundText() {
        return hasRefundText;
    }

    public void setHasRefundText(String hasRefundText) {
        this.hasRefundText = hasRefundText;
    }

    public Integer getHasRefund() {
        return hasRefund;
    }

    public void setHasRefund(Integer hasRefund) {
        this.hasRefund = hasRefund;
    }

    public Integer getBusinessTypeSearch() {
        return businessTypeSearch;
    }

    public void setBusinessTypeSearch(Integer businessTypeSearch) {
        this.businessTypeSearch = businessTypeSearch;
    }

    public String getLoginMobile() {
        return loginMobile;
    }

    public void setLoginMobile(String loginMobile) {
        this.loginMobile = loginMobile;
    }

    public String getCartIds() {
        return cartIds;
    }

    public void setCartIds(String cartIds) {
        this.cartIds = cartIds;
    }

    public Integer getMerchantType() {
        return merchantType;
    }

    public void setMerchantType(Integer merchantType) {
        this.merchantType = merchantType;
    }

    public List<Long> getUseVoucherIdList() {
        return useVoucherIdList;
    }

    public void setUseVoucherIdList(List<Long> useVoucherIdList) {
        this.useVoucherIdList = useVoucherIdList;
    }

    public BigDecimal getFreightAmount() {
        return freightAmount;
    }

    public void setFreightAmount(BigDecimal freightAmount) {
        this.freightAmount = freightAmount;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getVoucherMonitor() {
        return voucherMonitor;
    }

    public void setVoucherMonitor(String voucherMonitor) {
        this.voucherMonitor = voucherMonitor;
    }

    public Integer getSaasOrderSourcePath() {
        return saasOrderSourcePath;
    }

    public void setSaasOrderSourcePath(Integer saasOrderSourcePath) {
        this.saasOrderSourcePath = saasOrderSourcePath;
    }
    @Override
    public String toString() {
        return "Order{" +
                "id=" + id +
                ", branchCode='" + branchCode + '\'' +
                ", orgId='" + orgId + '\'' +
                ", merchantId=" + merchantId +
                ", orderNo='" + orderNo + '\'' +
                ", varietyNum=" + varietyNum +
                ", contactor='" + contactor + '\'' +
                ", mobile='" + mobile + '\'' +
                ", money=" + money +
                ", payType=" + payType +
                ", payChannel=" + payChannel +
                ", payTime=" + payTime +
                ", paymentTime=" + paymentTime +
                ", billInfo='" + billInfo + '\'' +
                ", billType=" + billType +
                ", status=" + status +
                ", checkStatus=" + checkStatus +
                ", deliveryTime=" + deliveryTime +
                ", shipTime=" + shipTime +
                ", finishTime=" + finishTime +
                ", originalTotalAmount=" + originalTotalAmount +
                ", originalTotalDiscount=" + originalTotalDiscount +
                ", totalAmount=" + totalAmount +
                ", discount=" + discount +
                ", parentId=" + parentId +
                ", visibled=" + visibled +
                ", urgencyDegree=" + urgencyDegree +
                ", urgencyInfo='" + urgencyInfo + '\'' +
                ", urgencyUpdateTime=" + urgencyUpdateTime +
                ", sourcePayMoney=" + sourcePayMoney +
                ", provinceCode=" + provinceCode +
                ", cityCode=" + cityCode +
                ", areaCode=" + areaCode +
                ", orderSource=" + orderSource +
                ", totalcount=" + totalcount +
                ", tranNo='" + tranNo + '\'' +
                ", deliveredTime=" + deliveredTime +
                ", refundCount=" + refundCount +
                ", salesJobNo='" + salesJobNo + '\'' +
                ", salesName='" + salesName + '\'' +
                ", salesId=" + salesId +
                ", shipType=" + shipType +
                ", skCode='" + skCode + '\'' +
                ", storeCode='" + storeCode + '\'' +
                ", boxes=" + boxes +
                ", haveSchedule=" + haveSchedule +
                ", passTime=" + passTime +
                ", addressId=" + addressId +
                ", productNum=" + productNum +
                ", merchantName='" + merchantName + '\'' +
                ", noRebateTotalAmount=" + noRebateTotalAmount +
                ", noRebateDiscountAmount=" + noRebateDiscountAmount +
                ", noRebateVoucherAmount=" + noRebateVoucherAmount +
                ", rebateTotalAmount=" + rebateTotalAmount +
                ", rebateDiscountAmount=" + rebateDiscountAmount +
                ", rebateVoucherAmount=" + rebateVoucherAmount +
                ", wholeDiscountAmount=" + wholeDiscountAmount +
                ", wholeVoucherAmount=" + wholeVoucherAmount +
                ", businessType=" + businessType +
                ", address='" + address + '\'' +
                ", remark='" + remark + '\'' +
                ", creator='" + creator + '\'' +
                ", createTime=" + createTime +
                ", updator='" + updator + '\'' +
                ", updateTime=" + updateTime +
                ", invoinceText='" + invoinceText + '\'' +
                ", isShowInvoinceButton=" + isShowInvoinceButton +
                ", statusName='" + statusName + '\'' +
                ", trackingNo='" + trackingNo + '\'' +
                ", separateFlag=" + separateFlag +
                ", startCreateTime=" + startCreateTime +
                ", endCreateTime=" + endCreateTime +
                ", startPayTime=" + startPayTime +
                ", endPayTime=" + endPayTime +
                ", gspAddress='" + gspAddress + '\'' +
                ", gspAuditState=" + gspAuditState +
                ", gspRemark='" + gspRemark + '\'' +
                ", orderExtend=" + orderExtend +
                ", separateOrderList=" + separateOrderList +
                ", detailList=" + detailList +
                ", detailDTOList=" + detailDTOList +
                ", rebateBalanceAmt=" + rebateBalanceAmt +
                ", orderSourceArray=" + Arrays.toString(orderSourceArray) +
                ", payTypeName='" + payTypeName + '\'' +
                ", payChannelName='" + payChannelName + '\'' +
                ", useBalance=" + useBalance +
                ", balanceStatus=" + balanceStatus +
                ", packageList=" + packageList +
                ", payEndTime='" + payEndTime + '\'' +
                ", countDownNewTime=" + countDownNewTime +
                ", countDownTime=" + countDownTime +
                ", balanceText='" + balanceText + '\'' +
                ", balanceAmount=" + balanceAmount +
                ", balanceRemark='" + balanceRemark + '\'' +
                ", refundText='" + refundText + '\'' +
                ", canConfirmReceipt=" + canConfirmReceipt +
                ", fullDivPrice=" + fullDivPrice +
                ", voucherDivPrice=" + voucherDivPrice +
                ", rebate=" + rebate +
                ", startingPrice=" + startingPrice +
                ", voucherId='" + voucherId + '\'' +
                ", voucherIds='" + voucherIds + '\'' +
                ", voucher=" + voucher +
                ", takeDiscountTotalAmt=" + takeDiscountTotalAmt +
                ", appVersion=" + appVersion +
                ", statuses=" + Arrays.toString(statuses) +
                ", auditStateArray=" + Arrays.toString(auditStateArray) +
                ", name='" + name + '\'' +
                ", isLettersQuery=" + isLettersQuery +
                ", sceneType=" + sceneType +
                ", imageUrl='" + imageUrl + '\'' +
                ", voucherDiscountAmount=" + voucherDiscountAmount +
                ", promoDiscountAmount=" + promoDiscountAmount +
                ", customDiscountAmount=" + customDiscountAmount +
                ", discountComputeType=" + discountComputeType +
                ", orderAdjustList=" + orderAdjustList +
                ", preOrderNo='" + preOrderNo + '\'' +
                ", discountType=" + discountType +
                ", jumpType=" + jumpType +
                ", isThirdCompany=" + isThirdCompany +
                ", hasRefundText='" + hasRefundText + '\'' +
                ", hasRefund=" + hasRefund +
                ", businessTypeSearch=" + businessTypeSearch +
                ", loginMobile='" + loginMobile + '\'' +
                ", cartIds='" + cartIds + '\'' +
                ", merchantType=" + merchantType +
                ", firstOrderFlag=" + firstOrderFlag +
                ", replacementOrderFlag=" + replacementOrderFlag +
                ", hasReplacementOrderFlagText=" + hasReplacementOrderFlagText +
                ", payExpireTime=" + payExpireTime +
                ", voucherMonitor=" + voucherMonitor +
                ", saasOrderSourcePath=" + saasOrderSourcePath +
                ", isHaveControl=" + isHaveControl +
                ", channelCode=" + channelCode +
                ", channelCodeText=" + channelCodeText +
                '}';
    }

    public String getGiftIds() {
        return giftIds;
    }

    public void setGiftIds(String giftIds) {
        this.giftIds = giftIds;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getHasFirstOrderFlagText() {
        return hasFirstOrderFlagText;
    }

    public void setHasFirstOrderFlagText(String hasFirstOrderFlagText) {
        this.hasFirstOrderFlagText = hasFirstOrderFlagText;
    }

    public Integer getIsHaveControl() {
        return isHaveControl;
    }

    public void setIsHaveControl(Integer isHaveControl) {
        this.isHaveControl = isHaveControl;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getBizProducts() {
        return bizProducts;
    }

    public void setBizProducts(String bizProducts) {
        this.bizProducts = bizProducts;
    }

    public String getChannelCodeText() {
        return channelCodeText;
    }

    public void setChannelCodeText(String channelCodeText) {
        this.channelCodeText = channelCodeText;
    }

    public List<OrderZhuGDto> getOrderZhuGList() {
        return orderZhuGList;
    }

    public void setOrderZhuGList(List<OrderZhuGDto> orderZhuGList) {
        this.orderZhuGList = orderZhuGList;
    }

    public Integer getOrderChannel() {
        return orderChannel;
    }

    public void setOrderChannel(Integer orderChannel) {
        this.orderChannel = orderChannel;
    }

    public String getPurchaseNo() {
        return purchaseNo;
    }

    public void setPurchaseNo(String purchaseNo) {
        this.purchaseNo = purchaseNo;
    }

    public String getResSerialNumber() {
        return resSerialNumber;
    }

    public void setResSerialNumber(String resSerialNumber) {
        this.resSerialNumber = resSerialNumber;
    }

    public Integer getIsKaFastOrder() {
        return isKaFastOrder;
    }

    public void setIsKaFastOrder(Integer isKaFastOrder) {
        this.isKaFastOrder = isKaFastOrder;
    }

    public Boolean getShopOrder() {
        if(isShopOrder == null){
            isShopOrder = false;
        }
        return isShopOrder;
    }

    public void setShopOrder(Boolean shopOrder) {
        isShopOrder = shopOrder;
    }

    public Map<String, List<Long>> getShopVoucherIds() {
        return shopVoucherIds;
    }

    public void setShopVoucherIds(Map<String, List<Long>> shopVoucherIds) {
        this.shopVoucherIds = shopVoucherIds;
    }

    public Map<String, String> getCompanyRemarks() {
        return companyRemarks;
    }

    public void setCompanyRemarks(Map<String, String> companyRemarks) {
        this.companyRemarks = companyRemarks;
    }

    public CouponComputeAccountEnum getCouponComputeAccountEnum() {
        return couponComputeAccountEnum;
    }

    public void setCouponComputeAccountEnum(CouponComputeAccountEnum couponComputeAccountEnum) {
        this.couponComputeAccountEnum = couponComputeAccountEnum;
    }

    public List<Integer> getBusinessTypeList() {
        return businessTypeList;
    }

    public void setBusinessTypeList(List<Integer> businessTypeList) {
        this.businessTypeList = businessTypeList;
    }

    public Boolean getKaUser() {
        return isKaUser;
    }

    public void setKaUser(Boolean kaUser) {
        isKaUser = kaUser;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public List<OrderPromoDetail> getOrderPromoDetailList() {
        return orderPromoDetailList;
    }

    public void setOrderPromoDetailList(List<OrderPromoDetail> orderPromoDetailList) {
        this.orderPromoDetailList = orderPromoDetailList;
    }

    public OrderPromotion getOrderPromotion() {
        return orderPromotion;
    }

    public void setOrderPromotion(OrderPromotion orderPromotion) {
        this.orderPromotion = orderPromotion;
    }

    public BigDecimal getOriginalTotalAmount() {
        return originalTotalAmount;
    }

    public void setOriginalTotalAmount(BigDecimal originalTotalAmount) {
        this.originalTotalAmount = originalTotalAmount;
    }

    public BigDecimal getOriginalTotalDiscount() {
        return originalTotalDiscount;
    }

    public void setOriginalTotalDiscount(BigDecimal originalTotalDiscount) {
        this.originalTotalDiscount = originalTotalDiscount;
    }

    public Long getStoreAddressId() {
        return storeAddressId;
    }

    public void setStoreAddressId(Long storeAddressId) {
        this.storeAddressId = storeAddressId;
    }

    public String getOutsideOrderCode() {
        return outsideOrderCode;
    }

    public void setOutsideOrderCode(String outsideOrderCode) {
        this.outsideOrderCode = outsideOrderCode;
    }

    public BigDecimal getShopVoucherAmount() {
        return shopVoucherAmount;
    }

    public void setShopVoucherAmount(BigDecimal shopVoucherAmount) {
        this.shopVoucherAmount = shopVoucherAmount;
    }

    public BigDecimal getCrossPlatformVoucherAmount() {
        return crossPlatformVoucherAmount;
    }

    public void setCrossPlatformVoucherAmount(BigDecimal crossPlatformVoucherAmount) {
        this.crossPlatformVoucherAmount = crossPlatformVoucherAmount;
    }

    public Integer getPayerType() {
        return payerType;
    }

    public void setPayerType(Integer payerType) {
        this.payerType = payerType;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public boolean getIsGrossProfitAbnormal() {
        return isGrossProfitAbnormal;
    }

    public void setIsGrossProfitAbnormal(boolean grossProfitAbnormal) {
        isGrossProfitAbnormal = grossProfitAbnormal;
    }

    public String getExtraInfoJson() {
        return extraInfoJson;
    }

    public void setExtraInfoJson(String extraInfoJson) {
        this.extraInfoJson = extraInfoJson;
    }

    public boolean getIsExistFullOrderDiscount() {
        return isExistFullOrderDiscount;
    }

    public void setIsExistFullOrderDiscount(boolean existFullOrderDiscount) {
        isExistFullOrderDiscount = existFullOrderDiscount;
    }

    public boolean getIsExistFirmDiscount() {
        return isExistFirmDiscount;
    }

    public void setIsExistFirmDiscount(boolean existFirmDiscount) {
        isExistFirmDiscount = existFirmDiscount;
    }

    public boolean getIsExistComDiscount() {
        return isExistComDiscount;
    }

    public void setIsExistComDiscount(boolean existComDiscount) {
        isExistComDiscount = existComDiscount;
    }

    public String getCreatorRealName() {
        return creatorRealName;
    }

    public void setCreatorRealName(String creatorRealName) {
        this.creatorRealName = creatorRealName;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public boolean getIsSkuGrossProfitAbnormal() {
        return isSkuGrossProfitAbnormal;
    }

    public void setIsSkuGrossProfitAbnormal(boolean skuGrossProfitAbnormal) {
        isSkuGrossProfitAbnormal = skuGrossProfitAbnormal;
    }

    public Integer getCompanyProvinceCode() {
        return companyProvinceCode;
    }

    public void setCompanyProvinceCode(Integer companyProvinceCode) {
        this.companyProvinceCode = companyProvinceCode;
    }

    public Integer getDySalesId() {
        return dySalesId;
    }

    public void setDySalesId(Integer dySalesId) {
        this.dySalesId = dySalesId;
    }

    public Integer getIsKa() {
        return isKa;
    }

    public void setIsKa(Integer isKa) {
        this.isKa = isKa;
    }

    public Integer getIsFbp() {
        return isFbp;
    }

    public void setIsFbp(Integer isFbp) {
        this.isFbp = isFbp;
    }

    public BigDecimal getVirtualGold() {
        return virtualGold;
    }

    public void setVirtualGold(BigDecimal virtualGold) {
        this.virtualGold = virtualGold;
    }

    public BigDecimal getCashPayAmount() {
        return cashPayAmount;
    }

    public void setCashPayAmount(BigDecimal cashPayAmount) {
        this.cashPayAmount = cashPayAmount;
    }

    public boolean getUseVirtualGold() {
        return useVirtualGold;
    }

    public void setUseVirtualGold(boolean useVirtualGold) {
        this.useVirtualGold = useVirtualGold;
    }

    public List<UseRedPacketDTO> getUseRedPacketList() {
        return useRedPacketList;
    }

    public void setUseRedPacketList(List<UseRedPacketDTO> useRedPacketList) {
        this.useRedPacketList = useRedPacketList;
    }

    public Boolean getUseRedPacket() {
        return useRedPacket;
    }

    public void setUseRedPacket(Boolean useRedPacket) {
        this.useRedPacket = useRedPacket;
    }

    public Integer getBizScene() {
        return bizScene;
    }

    public void setBizScene(Integer bizScene) {
        this.bizScene = bizScene;
    }

    public Boolean getIsSxp() {
        return isSxp;
    }

    public void setIsSxp(Boolean sxp) {
        isSxp = sxp;
    }

    public Integer getCallType() {
        return callType;
    }

    public void setCallType(Integer callType) {
        this.callType = callType;
    }

    public String getProductCredential() {
        return productCredential;
    }

    public void setProductCredential(String productCredential) {
        this.productCredential = productCredential;
    }

    public String getCorpCredential() {
        return corpCredential;
    }

    public void setCorpCredential(String corpCredential) {
        this.corpCredential = corpCredential;
    }

    public Integer getAccountRole() {
        return accountRole;
    }

    public void setAccountRole(Integer accountRole) {
        this.accountRole = accountRole;
    }

    public String getMddata() {
        return mddata;
    }

    public void setMddata(String mddata) {
        this.mddata = mddata;
    }
}
