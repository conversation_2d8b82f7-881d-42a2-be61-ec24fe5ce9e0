package com.xyy.ec.pc.newfront.service;

import com.github.pagehelper.PageInfo;
import com.xyy.ec.api.busi.place.register.PoiReq;
import com.xyy.ec.merchant.bussiness.dto.MerchantPoiBusinessDto;
import com.xyy.ec.pc.rest.AjaxResult;

/**
 * <AUTHOR>
 * @date 2025-07-11 10:27
 */
public interface RegisterNewService {

    /**
     * 店铺列表查询
     *
     * @param poiReq
     * @return
     */
    AjaxResult<PageInfo<MerchantPoiBusinessDto>> getPageMerchantInfo(PoiReq poiReq);
}
